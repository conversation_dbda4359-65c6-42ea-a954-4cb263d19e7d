-- 菜单 SQL
INSERT INTO FunctionItem (
    [Id], [Title], [Url], [ParentId], [OrderNumber],
    [IsPublic], [Parameter], [DisplayName], [module_ID], [app_url],
    [app_module], [app_type], [app_icon]
)
VALUES (
    '${permissionPrefix}', '${table.classComment}', '/${moduleNameVar}/${classNameVar}', '${functionItem}', 1,
    2, NULL, '${table.classComment}', ${table.moduleId}, NULL,
    NULL, NULL, NULL
);

-- 按钮父菜单ID
-- 暂时只支持 MySQL。如果你是 Oracle、PostgreSQL、SQLServer 的话，需要手动修改 @parentId 的部分的代码
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
#set ($functionNames = ['查询', '新增', '修改', '删除', '导出'])
#set ($functionOps = ['QX01', 'QX02', 'QX03', 'QX04', 'QX05'])
#foreach ($functionName in $functionNames)
#set ($index = $foreach.count - 1)
INSERT INTO Permission ([PermissionNo], [OperationModule], [Operation], [Subsystem], [PermissionKind])
VALUES ('${permissionPrefix}${functionOps.get($index)}', '${table.classComment}', '${functionName}', '${table.classComment}', '${table.moduleId}');
#end
