package ${basePackage}.service.impl;

import com.soft.framework.common.utils.bean.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import ${basePackage}.controller.${table.businessName}.vo.*;
import ${basePackage}.entity.${table.className};
import ${IPageClassName};

import ${basePackage}.service.*;
import ${basePackage}.mapper.${table.className}Mapper;

/**
 * ${table.classComment} Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ${table.className}ServiceImpl extends ServiceImpl<${table.className}Mapper, ${table.className}> implements ${table.className}Service {

    @Override
    public ${primaryColumn.javaType} create${simpleClassName}(${sceneEnum.prefixClass}${table.className}CreateReqVO createReqVO) {
        // 插入
        ${table.className} ${classNameVar} = new ${table.className}();
        BeanUtils.copyProperties(${classNameVar}, createReqVO);
        baseMapper.insert(${classNameVar});
        // 返回
        return ${classNameVar}.getId();
    }

    @Override
    public void update${simpleClassName}(${sceneEnum.prefixClass}${table.className}UpdateReqVO updateReqVO) throws RuntimeException {
        // 校验存在
        validate${simpleClassName}Exists(updateReqVO.getId());
        // 更新
        ${table.className} ${classNameVar} = new ${table.className}();
        BeanUtils.copyProperties(${classNameVar}, updateReqVO);
        baseMapper.updateById(${classNameVar});
    }

    @Override
    public void delete${simpleClassName}(${primaryColumn.javaType} id) throws RuntimeException {
        // 校验存在
        validate${simpleClassName}Exists(id);
        // 删除
        baseMapper.deleteById(id);
    }

    private void validate${simpleClassName}Exists(${primaryColumn.javaType} id) throws RuntimeException {
        if (baseMapper.selectById(id) == null) {
            throw new RuntimeException("${table.classComment}不存在");
        }
    }

    @Override
    public ${table.className} get${simpleClassName}(${primaryColumn.javaType} id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<${table.className}> get${simpleClassName}List(Collection<${primaryColumn.javaType}> ids) {
        return baseMapper.selectBatchIds(ids);
    }

    @Override
    public IPage<${table.className}> get${simpleClassName}Page(${sceneEnum.prefixClass}${table.className} pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<${table.className}> get${simpleClassName}List(${sceneEnum.prefixClass}${table.className} exportReqVO) {
        return baseMapper.selectList(exportReqVO);
    }

}
