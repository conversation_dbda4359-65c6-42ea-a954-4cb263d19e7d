package ${basePackage}.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.*;
import javax.validation.*;
import ${basePackage}.controller.${table.businessName}.vo.*;
import ${basePackage}.entity.${table.className};
import ${IPageClassName};

/**
 * ${table.classComment} Service 接口
 *
 * <AUTHOR>
 */
public interface ${table.className}Service extends IService<${table.className}> {

    /**
     * 创建${table.classComment}
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    ${primaryColumn.javaType} create${simpleClassName}(@Valid ${sceneEnum.prefixClass}${table.className}CreateReqVO createReqVO);

    /**
     * 更新${table.classComment}
     *
     * @param updateReqVO 更新信息
     */
    void update${simpleClassName}(@Valid ${sceneEnum.prefixClass}${table.className}UpdateReqVO updateReqVO) throws RuntimeException;

    /**
     * 删除${table.classComment}
     *
     * @param id 编号
     */
    void delete${simpleClassName}(${primaryColumn.javaType} id) throws RuntimeException;

    /**
     * 获得${table.classComment}
     *
     * @param id 编号
     * @return ${table.classComment}
     */
    ${table.className} get${simpleClassName}(${primaryColumn.javaType} id);

    /**
     * 获得${table.classComment}列表
     *
     * @param ids 编号
     * @return ${table.classComment}列表
     */
    List<${table.className}> get${simpleClassName}List(Collection<${primaryColumn.javaType}> ids);

    /**
     * 获得${table.classComment}分页
     *
     * @param pageReqVO 分页查询
     * @return ${table.classComment}分页
     */
    IPage<${table.className}> get${simpleClassName}Page(${sceneEnum.prefixClass}${table.className} pageReqVO);

    /**
     * 获得${table.classComment}列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return ${table.classComment}列表
     */
    List<${table.className}> get${simpleClassName}List(${sceneEnum.prefixClass}${table.className} exportReqVO);

}
