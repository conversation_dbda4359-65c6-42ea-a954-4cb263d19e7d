package ${basePackage}.mapper;

import java.util.*;

import ${IPageClassName};
import ${PageClassName};
import ${QueryWrapperClassName};
import ${BaseMapperClassName};
import ${basePackage}.entity.${table.className};
import org.apache.ibatis.annotations.Mapper;

## 字段模板
#macro(listCondition)
#foreach ($column in $columns)
#if (${column.listOperation})
#set ($JavaField = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})##首字母大写
#if (${column.listOperationCondition} == "=")##情况一，= 的时候
                .eqIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "!=")##情况二，!= 的时候
                .neIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == ">")##情况三，> 的时候
                .gtIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == ">=")##情况四，>= 的时候
                .geIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "<")##情况五，< 的时候
                .ltIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "<=")##情况五，<= 的时候
                .leIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "LIKE")##情况七，Like 的时候
                .likeIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}())
#end
#if (${column.listOperationCondition} == "BETWEEN")##情况八，Between 的时候
                .betweenIfPresent(${table.className}::get${JavaField}, reqVO.get${JavaField}Array())
#end
#end
#end
#end
/**
 * ${table.classComment} Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ${table.className}Mapper extends BaseMapper<${table.className}> {

    default IPage<${table.className}> selectPage(${sceneEnum.prefixClass}${table.className} reqVO) {
        return selectPage(new Page<>(reqVO.getPageNum() - 1, reqVO.getPageSize()), new LambdaQueryWrapperX<${table.className}>()
			#listCondition()
                .orderByDesc(${table.className}::getId));## 大多数情况下，id 倒序

    }

    default List<${table.className}> selectList(${sceneEnum.prefixClass}${table.className} reqVO) {
        return selectList(new LambdaQueryWrapperX<${table.className}>()
			#listCondition()
                .orderByDesc(${table.className}::getId));## 大多数情况下，id 倒序

    }

}
