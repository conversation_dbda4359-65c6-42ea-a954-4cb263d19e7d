package ${basePackage}.controller.${table.businessName};

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
#if ($sceneEnum.scene == 1)import org.springframework.security.access.prepost.PreAuthorize;#end

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.io.IOException;

import ${IPageClassName};
import ${AjaxResultClassName};
import static ${AjaxResultClassName}.success;

import ${ExcelUtilsClassName};

import ${basePackage}.controller.${table.businessName}.vo.*;
import ${basePackage}.entity.${table.className};
import ${basePackage}.service.${table.className}Service;

@RestController
##二级的 businessName 暂时不算在 HTTP 路径上，可以根据需要写
@RequestMapping("/${table.moduleName}/${simpleClassName_strikeCase}")
@Validated
public class ${sceneEnum.prefixClass}${table.className}Controller {

    @Resource
    private ${table.className}Service ${classNameVar}Service;

    @PostMapping("/create")
    @ApiOperation(value = "创建${table.classComment}")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX02')")#end

    public AjaxResult create${simpleClassName}(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}CreateReqVO createReqVO) {
        return success(${classNameVar}Service.create${simpleClassName}(createReqVO));
    }

    @PutMapping("/update")
    @ApiOperation(value = "更新${table.classComment}")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX03')")#end

    public AjaxResult update${simpleClassName}(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}UpdateReqVO updateReqVO) {
        ${classNameVar}Service.update${simpleClassName}(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除${table.classComment}")
    @ApiParam(name = "id", value = "编号", required = true)
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX04')")#end

    public AjaxResult delete${simpleClassName}(@RequestParam("id") ${primaryColumn.javaType} id) {
        ${classNameVar}Service.delete${simpleClassName}(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得${table.classComment}")
    @ApiParam(name = "id", value = "编号", required = true, example = "1024")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX01')")#end

    public AjaxResult get${simpleClassName}(@RequestParam("id") ${primaryColumn.javaType} id) {
        ${table.className} ${classNameVar} = ${classNameVar}Service.get${simpleClassName}(id);
        return success(${classNameVar});
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得${table.classComment}列表")
    @ApiParam(name = "ids", value = "编号列表", required = true, example = "1024,2048")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX01')")#end

    public AjaxResult get${simpleClassName}List(@RequestParam("ids") Collection<${primaryColumn.javaType}> ids) {
        List<${table.className}> list = ${classNameVar}Service.get${simpleClassName}List(ids);
        return success(list);
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得${table.classComment}分页")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX01')")#end

    public AjaxResult get${simpleClassName}Page(@Valid ${sceneEnum.prefixClass}${table.className} pageVO) {
        IPage<${table.className}> pageResult = ${classNameVar}Service.get${simpleClassName}Page(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @ApiOperation(value = "导出${table.classComment} Excel")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermi('${permissionPrefix}QX05')")#end

    public void export${simpleClassName}Excel(@Valid ${sceneEnum.prefixClass}${table.className} exportReqVO,
              HttpServletResponse response) throws IOException, InvocationTargetException, IllegalAccessException {
        List<${table.className}> list = ${classNameVar}Service.get${simpleClassName}List(exportReqVO);
        // 导出 Excel
        List<${sceneEnum.prefixClass}${table.className}ExcelVO> datas = new ArrayList<>();
        for (InfraTest test : list) {
            InfraTestExcelVO excelVO = new InfraTestExcelVO();
            BeanUtils.copyProperties(test, excelVO);
            datas.add(excelVO);
        }
        ExcelUtils.write(response, "${table.classComment}.xls", "数据", ${sceneEnum.prefixClass}${table.className}ExcelVO.class, datas);
    }

}
