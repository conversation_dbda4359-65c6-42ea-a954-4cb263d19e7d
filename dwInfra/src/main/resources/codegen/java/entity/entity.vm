package ${basePackage}.entity;

import lombok.*;
import java.util.*;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#end
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#end
#end
import com.baomidou.mybatisplus.annotation.*;
import ${DfdwBaseEntityClassName};

/**
 * ${table.classComment}
 *
 * <AUTHOR>
 */
@TableName("${table.tableName.toLowerCase()}")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ${table.className} extends DfdwBaseEntity {

#foreach ($column in $columns)
#if (!${DfdwBaseEntityFields.contains(${column.javaField})})##排除 DfdwBaseEntity 的字段
    /**
     * ${column.columnComment}
    #if ("$!column.dictType" != "")##处理枚举值
     *
     * 枚举 {@link TODO ${column.dictType} 对应的类}
    #end
     */
    #if (${column.primaryKey})##处理主键
    @TableId(value = "${column.javaField}", type = IdType.AUTO)
    #end
    #if (!${column.primaryKey})##处理非主键
    @TableField(value = "${column.columnName}")
    #end
    private ${column.javaType} ${column.javaField};

    #if (${column.listOperation})##查询操作
    #if (${column.listOperationCondition} == "BETWEEN")## 情况一，Between 的时候
    @TableField(exist = false)
    #if (${column.javaType} == "LocalDateTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    #end
    private ${column.javaType}[] ${column.javaField}Array;
    #end
    #end
#end
#end

}
