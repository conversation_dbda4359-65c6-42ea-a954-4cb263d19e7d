<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
#foreach($column in $columns)
#if ($column.listOperation)
    #set ($dictType=$column.dictType)
    #set ($javaField = $column.javaField)
    #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
    #set ($comment=$column.columnComment)
#if ($column.htmlType == "input")
        <span class="font-size14">${comment}：</span>
        <el-input v-model="queryParams.${javaField}" clearable placeholder="请输入${comment}" @change="handleChange"></el-input>
#elseif ($column.htmlType == "inputNumber")
        <span class="font-size14">${comment}：</span>
        <el-input-number v-model="queryParams.${javaField}" :controls="false" :precision="0" clearable placeholder="请输入${comment}" @change="handleChange"></el-input-number>
#elseif ($column.htmlType == "select" || $column.htmlType == "radio")
        <span class="font-size14">${comment}：</span>
        <el-select v-model="queryParams.${javaField}" clearable placeholder="请选择${comment}" @change="handleChange">
    #if ("" != $dictType)## 设置了 dictType 数据字典的情况
          <el-option
              v-for="item in this.getDictDatas('$dictType')"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
    #else## 未设置 dictType 数据字典的情况
          <el-option label="请选择字典生成" value="" />
    #end
        </el-select>
#elseif($column.htmlType == "datetime")
        <span class="font-size14">${comment}：</span>
    #if ($column.listOperationCondition == "BETWEEN")## 范围
        <el-date-picker v-model="queryParams.${javaField}Array" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']" clearable @change="handleChange"/>
    #else## 非范围
        <el-date-picker clearable v-model="queryParams.${javaField}" type="date" value-format="yyyy-MM-dd" placeholder="选择${comment}" />
    #end
#elseif($column.htmlType == "inputDouble")
        <span class="font-size14">${comment}：</span>
        <el-input-number v-model="queryParams.${javaField}" :controls="false" :precision="2" clearable placeholder="请输入${comment}" @change="handleChange"></el-input-number>
#end
#end
#end
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-button type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-plus" @click="openDialog('save')" v-has-permi="['${permissionPrefix}QX02']">新增</el-button>
        <el-button type="text" icon="el-icon-edit" @click="openDialog('edit')" v-has-permi="['${permissionPrefix}QX03']">修改</el-button>
        <el-button type="text" icon="el-icon-delete" @click="openDialog('delete')" v-has-permi="['${permissionPrefix}QX04']">删除</el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('download')" v-has-permi="['${permissionPrefix}QX05']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          height="96%"
          @getCurrentData="select"
          @row-dblclick="openDialog('edit')"
      >
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
#foreach($column in $columns)
#if ($column.listOperationResult)
  #set ($dictType=$column.dictType)
  #set ($javaField = $column.javaField)
  #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
  #set ($comment=$column.columnComment)
#if ($column.htmlType == "select" || $column.htmlType == "radio")
        <template v-slot:${javaField}="scope">
          {{ filter('$dictType', scope.row.${javaField}) }}
        </template>
#elseif($column.htmlType == "datetime")
        <template v-slot:${javaField}="scope">
          {{ showTime(scope.row.${javaField}) }}
        </template>
#end
#end
#end
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="900px" :before-close="cancelDialog" append-to-body>
      <el-form
          ref="dialogParam"
          :model="dialogParam"
          :rules="dialogRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
#foreach($column in $columns)
#if ($column.createOperation || $column.updateOperation)
  #set ($dictType = $column.dictType)
  #set ($javaField = $column.javaField)
  #set ($AttrName = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
  #set ($comment = $column.columnComment)
  #if ($column.htmlType == "input")
    #if (!$column.primaryKey)## 忽略主键，不用在表单里
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-input v-model="dialogParam.${javaField}" clearable placeholder="请输入${comment}"/>
            </el-form-item>
          </el-col>
    #end
  #elseif ($column.htmlType == "inputNumber")
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-input-number v-model="dialogParam.${javaField}" :controls="false" :precision="0" clearable placeholder="请输入${comment}" style="width: 100%"/>
            </el-form-item>
          </el-col>
  #elseif ($column.htmlType == "select")
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-select v-model="dialogParam.${javaField}" clearable multiple placeholder="请选择${comment}" style="width: 100%">
                <el-option
                    v-for="item in this.getDictDatas('$dictType')"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
  #elseif ($column.htmlType == "datetime")
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-date-picker v-model="dialogParam.${javaField}" value-format="yyyy-MM-dd HH:mm:ss" type="date" clearable style="width: 100%"/>
            </el-form-item>
          </el-col>
  #elseif ($column.htmlType == "inputDouble")
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-input-number v-model="dialogParam.${javaField}" :controls="false" :precision="2" clearable placeholder="请输入${comment}" style="width: 100%"/>
            </el-form-item>
          </el-col>
  #elseif ($column.htmlType == "textarea")
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-input v-model="dialogParam.${javaField}" type="textarea" clearable placeholder="请输入${comment}"/>
            </el-form-item>
          </el-col>
  #else
          <el-col :span="12">
            <el-form-item label="${comment}" prop="${javaField}">
              <el-input v-model="dialogParam.${javaField}" clearable placeholder="请输入${comment}"/>
            </el-form-item>
          </el-col>
  #end
#end
#end
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDialog">确 定</el-button>
        <el-button @click="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {create${simpleClassName}, update${simpleClassName}, delete${simpleClassName}, get${simpleClassName}, get${simpleClassName}Page, export${simpleClassName}Excel} from "api/${moduleNameVar}/${classNameVar}";
  import Table from '@/components/MainTable'
  import Pagination from '@/components/Pagination'
  import Dropdown from '@/components/ColumnDropdown'
  import {downLoad} from "@/utils/tool";

  export default {
    name: "test",
    components: {Table, Pagination, Dropdown},
    created () {
      this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    },
    computed: {
      watermark(){
        return this.$store.state.user.watermark
      }
    },
    mounted() {
      this.getList()
    },
    data () {
      return {
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          total: 0,
#foreach($column in $columns)
  #if ($column.listOperation)
    #set ($dictType = $column.dictType)
    #set ($javaField = $column.javaField)
    #set ($AttrName = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
    #set ($comment = $column.columnComment)
       #if($column.htmlType == "datetime")
       #if ($column.listOperationCondition == "BETWEEN")## 范围
          ${javaField}Array: [],
       #else
          ${javaField}: undefined,
       #end
       #else
          ${javaField}: undefined,
       #end
  #end
#end
        },
        // 数据列表
        List: [],
        // 真实列表
        realTableOptions:[],
        // 列表配置
        tableOptions: [
#foreach($column in $columns)
  #if ($column.listOperationResult)
    #set ($dictType=$column.dictType)
    #set ($javaField = $column.javaField)
    #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
    #set ($comment=$column.columnComment)
      #if ($column.htmlType == "select" || $column.htmlType == "radio" || $column.htmlType == "datetime")
          {label: '${comment}', prop: '${javaField}', slot: true},
      #else
          {label: '${comment}', prop: '${javaField}'},
      #end
  #end
#end
        ],
        // 遮罩层
        loading: false,
        // 拷贝初始化查询参数
        defaultForm: {},
        // 选择列表id
        selectID: 0,
        // 选择列表数据
        selectParams: {},
        // 弹窗类型
        dialogType: '',
        // 弹窗标题
        dialogTitle: '',
        // 弹窗显示
        dialogVisible: false,
        // 新增/修改参数
        dialogParam: {
#foreach($column in $columns)
  #if ($column.createOperation || $column.updateOperation)
    #set ($dictType=$column.dictType)
    #set ($javaField = $column.javaField)
    #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
    #set ($comment=$column.columnComment)
          ${javaField}: undefined,
  #end
#end
        },
        // 新增/修改校验
        dialogRules: {
#foreach($column in $columns)
  #if ($column.createOperation || $column.updateOperation)
    #set ($dictType=$column.dictType)
    #set ($javaField = $column.javaField)
    #set ($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
    #set ($comment=$column.columnComment)
          ${javaField}: [{required: true, message: "${comment}不能为空", trigger: "blur"}],
  #end
#end
        }
      }
    },
    methods: {
      /** 获取新表格列表 */
      getNewArr(newTableOptions) {
        this.realTableOptions = [...newTableOptions]
      },
      /** queryParam事件 */
      handleChange () {
        this.queryParams.pageNum = 1
        this.getList()
      },
      /** 查询插拔记录 */
      getList () {
        this.loading = true
        getTestPage(this.queryParams).then((res) => {
          this.List = res.data.records
          this.queryParams.total = res.data.total
        }).finally(() => {
          this.loading = false
        })
      },
      /** 分页查询 */
      handleCurrentChange (val) {
        this.queryParams = val
        this.getList()
      },
      /** 重置查询 */
      resetQuery () {
        this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
        this.getList()
      },
      /** 单击表事件 */
      select (row) {
        this.selectID = row.id
        this.selectParams = row
      },
      /** 表格字典数据筛选 */
      filter(dictType, value) {
        return this.getDictDataLabel(dictType, value)
      },
      /** 时间格式修改 */
      showTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
        return this.$dayjs(date).format(format)
      },
      /** 按钮点击事件 */
      openDialog(type) {
        switch (type) {
          case 'save':
            this.dialogType = type;
            this.dialogVisible = true;
            this.dialogTitle = '新增';
            this.#[[$]]#refs["dialogParam"].resetFields();
            break;
          case 'edit':
            if (this.selectID === 0) {
              this.$message.error('请选择行！')
            } else {
              getTest(this.selectID).then((res) => {
                this.dialogParam = res.data
                this.dialogType = type;
                this.dialogVisible = true;
                this.dialogTitle = '修改';
              })
            }
            break;
          case 'delete':
            if (this.selectID === 0) {
              this.$message.error('请选择行！')
            } else {
              this.$confirm('是否删除该条数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                deleteTest(this.selectID).then((res) => {
                  this.$message.success('删除成功！')
                  this.getList()
                })
              })
            }
            break;
          case 'download':
            exportTestExcel(this.queryParams).then((res) => {
              this.$message.success('导出成功！')
              downLoad(res, '${table.classComment}.xlsx')
            })
            break;
        }
      },
      /** 关闭弹窗 */
      cancelDialog() {
        this.dialogVisible = false
        this.getList()
      },
      /** 弹窗确认按钮 */
      submitDialog() {
        switch (this.dialogType) {
           case 'save':
             this.#[[$]]#refs["dialogParam"].validate(valid => {
                if (valid) {
                    createTest(this.dialogParam).then((res) => {
                     this.$message.success('新增成功！')
                     this.dialogVisible = false
                     this.getList()
                    })
                }
             })
             break;
           case 'edit':
             this.#[[$]]#refs["dialogParam"].validate(valid => {
                 if (valid) {
                     updateTest(this.dialogParam).then((res) => {
                         this.$message.success('修改成功！')
                         this.dialogVisible = false
                         this.getList()
                     })
                 }
           })
             break;
        }
      },
    }
  }
</script>

<style scoped lang="less">
  .search-container {
    width: 100%;
    height: 100%;
    .operate-pannel{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      width: 100%;
      justify-content: space-between;

      .el-date-editor{
        margin-right: 10px;
      }
      .el-input {
        width: 200px;
        margin-right: 10px;
      }
      .el-select{
        margin-right: 10px;
      }
      .el-input-number {
        width: 200px;
        margin-right: 10px;
      }
    }
    .table-box {
      height: calc(100% - 80px);

      ::v-deep .el-pagination {
        margin-top: 10px;
        text-align: center;
      }
    }
  }

  ::v-deep .el-dialog__body {
    height: calc(100% - 54px);

    .el-tabs {
      height: calc(100% - 30px);
      // overflow: auto;

      .el-tabs__content {
        //39px el-tabs header高度
        height: calc(100% - 39px);
        overflow: auto;

        .el-tab-pane {
          height: 100%;
        }
      }
    }

    .btn-box {
      margin-top: 10px;
      text-align: center;
    }
  }

  .el-row {
    margin-bottom: 20px;
  }
  .text {
    font-size: 14px;
  }

  .item {
    padding: 0;
  }
</style>