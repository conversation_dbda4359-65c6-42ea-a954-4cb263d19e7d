package com.soft.framework.helper;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.config.OssConfig;

import java.io.File;
import java.util.List;

public class OSSHelper {
    private String endpoint = "http://oss-cn-nb-yd-d01-a.ops.yongyao-cloud.com/";
    private String accessKeyId = "pAsA8syPLoZ9h03Q";
    private String accessKeySecret = "Jh0OM0DVPqY3AU2erlZyMCR7YUsHpv";
    private String bucketName = "shuziyongyao";
    private OSS client = null;

    public OSSHelper()
    {
        if (!OssConfig.getEndPoint().equals(""))
        {
            endpoint = OssConfig.getEndPoint();
        }
        if (!OssConfig.getAccessKeyId().equals(""))
        {
            accessKeyId = OssConfig.getAccessKeyId();
        }
        if (!OssConfig.getAccessKeySecret().equals(""))
        {
            accessKeySecret = OssConfig.getAccessKeySecret();
        }
        if (!OssConfig.getBucketName().equals(""))
        {
            bucketName = OssConfig.getBucketName();
        }
        return;
    }

    public Boolean InitClient()
    {
        try
        {
            if (client != null) {
                return true;
            }
            client = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            return true;
        }
        catch (Exception Ex)
        {
            client = null;
            return false;
        }
    }

    public Boolean DoesBucketExist()
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            Boolean exist = client.doesBucketExist(bucketName);
            return exist;
        }
        catch (Exception ex)
        {
            return false;
        }
    }


    public Boolean DoesObjectExist(String key)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            boolean exist = client.doesObjectExist(bucketName, key);
            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean DeleteFile(String fname)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            Boolean exist = client.doesObjectExist(bucketName, fname);
            if (exist)
            {
                client.deleteObject(bucketName, fname);
            }
            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean DeleteDir(String dname)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            ObjectListing listResult = client.listObjects(bucketName,dname);
            List<OSSObjectSummary> sums = listResult.getObjectSummaries();
            for (OSSObjectSummary s : sums) {
                client.deleteObject(bucketName, s.getKey());
            }

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean ListDir(String dname,List<String> _klist)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            ObjectListing listResult = client.listObjects(bucketName,dname);
            List<OSSObjectSummary> sums = listResult.getObjectSummaries();
            for (OSSObjectSummary s : sums) {
                _klist.add(s.getKey());
            }

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean ListDir(String dname, StringBuilder sb)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            ObjectListing listResult = client.listObjects(bucketName,dname);
            List<OSSObjectSummary> sums = listResult.getObjectSummaries();
            for (OSSObjectSummary s : sums) {
                sb.append(s.getKey()+"\n");
            }

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean SetDirMode(String dname,Boolean wap)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            ObjectListing listResult = client.listObjects(bucketName,dname);
            List<OSSObjectSummary> sums = listResult.getObjectSummaries();
            for (OSSObjectSummary s : sums)
            {
                if (wap)
                {
                    client.setObjectAcl(bucketName, s.getKey(), CannedAccessControlList.PublicRead);
                }
                else
                {
                    client.setObjectAcl(bucketName, s.getKey(), CannedAccessControlList.Private);
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean SetFileMode(String kname, Boolean wap)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            if (wap)
            {
                client.setObjectAcl(bucketName,kname, CannedAccessControlList.PublicRead);
            }
            else
            {
                client.setObjectAcl(bucketName,kname, CannedAccessControlList.Private);
            }
            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean UploadFileProx(String fname, File fullname,Boolean wap)
    {
        if (wap)
        {
            return UploadFile_WAP(fname, fullname);
        }
        else
        {
            return UploadFile(fname, fullname);
        }
    }

    public Boolean UploadFile(String fname, File fullname)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            if (!client.doesObjectExist(bucketName, fname))
            {
                client.putObject(bucketName, fname, fullname);
                //FileUtil.Delete(fullname.getName());
            }
            client.setObjectAcl(bucketName, fname, CannedAccessControlList.Private);

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean UploadFile_WAP(String fname, File fullname)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            if (!client.doesObjectExist(bucketName, fname))
            {
                client.putObject(bucketName, fname, fullname);
                //FileUtil.Delete(fullname.getName());
            }
            client.setObjectAcl(bucketName, fname, CannedAccessControlList.PublicRead);
            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean UploadDir(String dir, String mdir)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            if (!FileUtil.DirectoryExists(dir)) {
                return false;
            }

            File fdir=new File(dir);
            String[] flist = fdir.list();
            for (String fname: flist)
            {
                File fi=new File(fname);
                if(fi.isFile()) {
                    String ofilename = fi.getName();
                    String mname = fi.getPath().replace(mdir, "").replace("\\", "/");
                    if (!UploadFile(mname + "/" + ofilename, fi)) {
                        return false;
                    }
                }else if(fi.isDirectory()) {
                    String dname = fi.getName();
                    if (!UploadDir_WAP(dname,mdir))
                    {
                        return false;
                    }
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean UploadDir_WAP(String dir,String mdir)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            if (!FileUtil.DirectoryExists(dir)) {
                return false;
            }

            File fdir=new File(dir);
            String[] flist = fdir.list();
            for (String fname: flist)
            {
                File fi=new File(fname);
                if(fi.isFile()) {
                    String ofilename = fi.getName();
                    String mname = fi.getPath().replace(mdir, "").replace("\\", "/");
                    if (!UploadFile(mname + "/" + ofilename, fi)) {
                        return false;
                    }
                }else if(fi.isDirectory()) {
                    String dname = fi.getName();
                    if (!UploadDir_WAP(dname,mdir))
                    {
                        return false;
                    }
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public Boolean DownloadFile(String kname, String filename)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return false;
            }

            String fdir=FileUtil.ExtractFileDir(filename);
            if (!FileUtil.DirectoryExists(fdir))
            {
                FileUtil.CreateDir(fdir);
            }
            if(client.doesObjectExist(bucketName, kname))
            {
                client.getObject(new GetObjectRequest(bucketName,kname), new File(filename));
                return true;
            }else {
                return false;
            }
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    public String CreateSignUrl(String kname)
    {
        try
        {
            if (client == null) {
                InitClient();
            }
            if (client == null) {
                return "";
            }

            ObjectMetadata metadata = client.getObjectMetadata(bucketName, kname);
            String etag = metadata.getETag();

            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, kname, HttpMethod.GET);
            String uri = client.generatePresignedUrl(req).toString();

            return uri;
        }
        catch (Exception ex)
        {
            return "";
        }
    }
}
