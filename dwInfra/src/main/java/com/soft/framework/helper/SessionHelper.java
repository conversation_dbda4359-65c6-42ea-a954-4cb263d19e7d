package com.soft.framework.helper;

import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.ventity.PersonEntity;
import javax.servlet.http.HttpServletRequest;

public final class SessionHelper {
    public static HttpServletRequest getRequest() {
        return ServletUtils.getRequest();
    }

    public static String getSessionToken() { ;
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            String token = (String)request.getSession().getAttribute("CurrentToken");
            if(token!=null)
            {
                return token;
            }else
            {
                return null;
            }
        }else
        {
            return null;
        }
    }

    public static String getSessionId() {
        return getRequest().getSession().getId();
    }

    public static int getSessionUserID() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getId();
            }else
            {
                return -1;
            }
        }else
        {
            return -1;
        }
    }

    public static int getSessionTopGroupId() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getTopGroupId();
            }else
            {
                return -1;
            }
        }else
        {
            return -1;
        }
    }

    public static String getSessionUserName() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRealName();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionLoginName() { ;
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getLoginName();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static int getSessionDeptId() { ;
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getGroupId();
            }else
            {
                return -1;
            }
        }else
        {
            return -1;
        }
    }

    public static String getSessionDeptName() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getGroupName();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionRoleIdList() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRoleIdsString();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionRoleNameList() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRoleNamesString();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionPermissionList() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRolePermissionString();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static PersonEntity getSessionPerson() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity;
            }else
            {
                return null;
            }
        }else
        {
            return null;
        }
    }

    public static int getSessionUserPwf() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return loginUser.getPwf();
            }else
            {
                return 2;
            }
        }else
        {
            return 2;
        }
    }

    public static boolean Session_IsAdmin() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                String LoginName=personEntity.getLoginName();
                String RoleNameList=personEntity.getRoleNamesString();
                if(LoginName.equals("admin")||RoleNameList.indexOf("JAVA框架-平台管理角色")!=-1){
                    return true;
                }
                return false;
            }else
            {
                return false;
            }
        }else
        {
            return false;
        }
    }

    //和net返回值相反，
    public static Boolean SessionVaild(String token)
    {
        return WpServiceHelper.IsTokenValid();
    }
}
