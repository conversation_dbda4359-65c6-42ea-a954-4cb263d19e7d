package com.soft.framework.security.service;

import com.soft.framework.common.constant.Constants;
import com.soft.framework.common.utils.MessageUtils;
import com.soft.framework.helper.SecToolHelper;
import com.soft.framework.manager.AsyncManager;
import com.soft.framework.manager.factory.AsyncFactory;
import com.soft.framework.security.LoginUser;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashSet;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    /**
     * 登录验证
     *
     * @return 结果
     */
    public void loginPC(HttpServletRequest request, String token,String loginname, String password) {
        // 用户验证
        Authentication authentication = null;

        // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
        authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(loginname, password));

        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginname, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        int pwf = SecToolHelper.UserIsInNWWD(request);
        loginUser.setPwf(pwf);
        HashSet<String> plist=new HashSet<String>(loginUser.getUser().getPermissionList());
        loginUser.setPermissions(plist);

        String uuid=tokenService.ExtractTokenUUID(token);
        loginUser.setToken(uuid);
        tokenService.createLocalToken(loginUser);
        return;
    }


    /**
     * 登录验证
     *
     * @param loginname 用户名
     * @param password 密码
     * @return 结果
     */
    public void loginAPP(HttpServletRequest request,String token,String loginname, String password) {
        // 用户验证
        Authentication authentication = null;

        // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
        authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(loginname, password));

        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginname, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        int pwf = SecToolHelper.UserIsInNWWD(request);
        loginUser.setPwf(pwf);
        HashSet<String> plist=new HashSet<String>(loginUser.getUser().getPermissionList());
        loginUser.setPermissions(plist);

        String uuid=tokenService.ExtractTokenUUID(token);
        loginUser.setToken(uuid);
        tokenService.createLocalAppToken(loginUser);
        return;
    }

}
