package com.soft.framework.controller.Base;

import com.soft.framework.common.constant.Constants;
import com.soft.framework.common.exception.user.CaptchaException;
import com.soft.framework.common.exception.user.CaptchaExpireException;
import com.soft.framework.common.utils.security.Base64Util;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.SysLoginService;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value="/Service/Base/User" )
@Api(tags ="基本框架接口->登录基本接口")
public class LoginController {
    @Autowired(required = false)
    private SysLoginService loginService;

    @Autowired(required = false)
    private TokenService tokenService;

    @RequestMapping(value="/MLogin",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="MLogin",notes="单点登录接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @LoadBalanced
    public AjaxResult MLogin(HttpServletRequest request)
    {
        String token= "";
        AjaxResult ajax = null;
        try {
            TokenService tokenService= (TokenService) SpringUtil.getBean("tokenService");
            if(tokenService==null) {
                return AjaxResult.error("系统错误");
            }

            token=tokenService.getToken(request);
            if(StringUtils.isEmpty(token)){
                return AjaxResult.error("参数错误");
            }

            PersonEntity person = WpServiceHelper.GetCurrentPerson();
            if (person == null) {
                return AjaxResult.error("用户已经退出登录！");
            }

            String loginname = person.getLoginName().toString();
            String md5pwd = person.getPassword().toString();
            if (StringUtils.isEmpty(loginname) || StringUtils.isEmpty(md5pwd)) {
                return AjaxResult.error("参数错误");
            }

            //获取代码版本，控制各种静态页面自动刷新
            SqlHelper sqlhelper = new SqlHelper();
            String cpsversion = sqlhelper.ExecuteScalar("select vc_ver from cps_ver_ctrl");
            if (cpsversion.equals("")) {
                cpsversion = "0";
            }

            /*if (!CacheLoginUser.isStongPass(password)){
                return AjaxResult.error("请使用强密码！");
            }*/

            // 生成令牌
            loginService.loginPC(request,token,loginname,md5pwd);
            ajax = AjaxResult.success();
            ajax.put(Constants.TOKEN, token);
            ajax.put("cpsversion", cpsversion);
            String smenc = Base64Util.encodeData(ConfigHelper.getSystemMark());
            ajax.put("sysmark", smenc);
        }catch(CaptchaExpireException ex)
        {
            LogHelper.WriteSysLog("用户登录信息过期!");
            ajax=AjaxResult.error("用户登录信息过期！");
        }catch(CaptchaException ex)
        {
            LogHelper.WriteSysLog("系统异常，登录Token异常！");
            ajax=AjaxResult.error("系统异常，登录Token异常！");
        }catch(Exception ex)
        {
            LogHelper.WriteSysLog("系统异常:ex="+ex.getMessage());
            ajax=AjaxResult.error(ex.getMessage());
        }
        return ajax;
    }

    @RequestMapping(value="/GetCurrentPerson",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetCurrentPerson",notes="获取当前用户信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult GetCurrentPerson(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);

        AjaxResult ajax = null;
        if(loginUser!=null)
        {
            PersonEntity person=loginUser.getUser();
            if(person!=null)
            {
                ajax=AjaxResult.success("获取用户信息成功!",person);
            }else
            {
                ajax=AjaxResult.success("获取用户信息失败!");
            }
        }else
        {
            ajax=AjaxResult.success("获取用户信息失败!");
        }
        return ajax;
    }
}
