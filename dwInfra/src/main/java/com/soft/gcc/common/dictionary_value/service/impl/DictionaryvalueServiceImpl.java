package com.soft.gcc.common.dictionary_value.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dictionary_value.entity.Dictionaryvalue;
import com.soft.gcc.common.dictionary_value.mapper.DictionaryvalueMapper;
import com.soft.gcc.common.dictionary_value.service.DictionaryvalueService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DictionaryValue】的数据库操作Service实现
* @createDate 2023-02-24 09:09:29
*/
@Service
public class DictionaryvalueServiceImpl extends ServiceImpl<DictionaryvalueMapper, Dictionaryvalue>
    implements DictionaryvalueService{

}




