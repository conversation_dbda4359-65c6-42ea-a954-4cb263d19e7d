package com.soft.gcc.common.lc_workFlow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName Lc_workFlow
 */
@TableName(value ="Lc_workFlow")
@Data
public class LcWorkflow implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "lc_defineID")
    private Integer lcDefineid;

    /**
     * 
     */
    @TableField(value = "ywID")
    private Integer ywid;

    /**
     * 
     */
    @TableField(value = "lc_jdID")
    private Integer lcJdid;

    /**
     * 
     */
    @TableField(value = "lc_jdmc")
    private String lcJdmc;

    /**
     * 
     */
    @TableField(value = "groupID")
    private Integer groupid;

    /**
     * 
     */
    @TableField(value = "groupName")
    private String groupname;

    /**
     * 
     */
    @TableField(value = "personZgh")
    private String personzgh;

    /**
     * 
     */
    @TableField(value = "personName")
    private String personname;

    /**
     * 
     */
    @TableField(value = "transdate")
    private Date transdate;

    /**
     * 
     */
    @TableField(value = "feed")
    private String feed;

    /**
     * 
     */
    @TableField(value = "number")
    private Integer number;

    /**
     * 
     */
    @TableField(value = "BXType")
    private String bxtype;

    /**
     * 
     */
    @TableField(value = "PNO")
    private String pno;

    /**
     * 
     */
    @TableField(value = "startdate")
    private Date startdate;

    /**
     * 
     */
    @TableField(value = "LcByRole")
    private Integer lcbyrole;

    /**
     * 
     */
    @TableField(value = "isback")
    private Integer isback;

    /**
     * 
     */
    @TableField(value = "useback")
    private Integer useback;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LcWorkflow other = (LcWorkflow) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLcDefineid() == null ? other.getLcDefineid() == null : this.getLcDefineid().equals(other.getLcDefineid()))
            && (this.getYwid() == null ? other.getYwid() == null : this.getYwid().equals(other.getYwid()))
            && (this.getLcJdid() == null ? other.getLcJdid() == null : this.getLcJdid().equals(other.getLcJdid()))
            && (this.getLcJdmc() == null ? other.getLcJdmc() == null : this.getLcJdmc().equals(other.getLcJdmc()))
            && (this.getGroupid() == null ? other.getGroupid() == null : this.getGroupid().equals(other.getGroupid()))
            && (this.getGroupname() == null ? other.getGroupname() == null : this.getGroupname().equals(other.getGroupname()))
            && (this.getPersonzgh() == null ? other.getPersonzgh() == null : this.getPersonzgh().equals(other.getPersonzgh()))
            && (this.getPersonname() == null ? other.getPersonname() == null : this.getPersonname().equals(other.getPersonname()))
            && (this.getTransdate() == null ? other.getTransdate() == null : this.getTransdate().equals(other.getTransdate()))
            && (this.getFeed() == null ? other.getFeed() == null : this.getFeed().equals(other.getFeed()))
            && (this.getNumber() == null ? other.getNumber() == null : this.getNumber().equals(other.getNumber()))
            && (this.getBxtype() == null ? other.getBxtype() == null : this.getBxtype().equals(other.getBxtype()))
            && (this.getPno() == null ? other.getPno() == null : this.getPno().equals(other.getPno()))
            && (this.getStartdate() == null ? other.getStartdate() == null : this.getStartdate().equals(other.getStartdate()))
            && (this.getLcbyrole() == null ? other.getLcbyrole() == null : this.getLcbyrole().equals(other.getLcbyrole()))
            && (this.getIsback() == null ? other.getIsback() == null : this.getIsback().equals(other.getIsback()))
            && (this.getUseback() == null ? other.getUseback() == null : this.getUseback().equals(other.getUseback()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLcDefineid() == null) ? 0 : getLcDefineid().hashCode());
        result = prime * result + ((getYwid() == null) ? 0 : getYwid().hashCode());
        result = prime * result + ((getLcJdid() == null) ? 0 : getLcJdid().hashCode());
        result = prime * result + ((getLcJdmc() == null) ? 0 : getLcJdmc().hashCode());
        result = prime * result + ((getGroupid() == null) ? 0 : getGroupid().hashCode());
        result = prime * result + ((getGroupname() == null) ? 0 : getGroupname().hashCode());
        result = prime * result + ((getPersonzgh() == null) ? 0 : getPersonzgh().hashCode());
        result = prime * result + ((getPersonname() == null) ? 0 : getPersonname().hashCode());
        result = prime * result + ((getTransdate() == null) ? 0 : getTransdate().hashCode());
        result = prime * result + ((getFeed() == null) ? 0 : getFeed().hashCode());
        result = prime * result + ((getNumber() == null) ? 0 : getNumber().hashCode());
        result = prime * result + ((getBxtype() == null) ? 0 : getBxtype().hashCode());
        result = prime * result + ((getPno() == null) ? 0 : getPno().hashCode());
        result = prime * result + ((getStartdate() == null) ? 0 : getStartdate().hashCode());
        result = prime * result + ((getLcbyrole() == null) ? 0 : getLcbyrole().hashCode());
        result = prime * result + ((getIsback() == null) ? 0 : getIsback().hashCode());
        result = prime * result + ((getUseback() == null) ? 0 : getUseback().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", lcDefineid=").append(lcDefineid);
        sb.append(", ywid=").append(ywid);
        sb.append(", lcJdid=").append(lcJdid);
        sb.append(", lcJdmc=").append(lcJdmc);
        sb.append(", groupid=").append(groupid);
        sb.append(", groupname=").append(groupname);
        sb.append(", personzgh=").append(personzgh);
        sb.append(", personname=").append(personname);
        sb.append(", transdate=").append(transdate);
        sb.append(", feed=").append(feed);
        sb.append(", number=").append(number);
        sb.append(", bxtype=").append(bxtype);
        sb.append(", pno=").append(pno);
        sb.append(", startdate=").append(startdate);
        sb.append(", lcbyrole=").append(lcbyrole);
        sb.append(", isback=").append(isback);
        sb.append(", useback=").append(useback);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}