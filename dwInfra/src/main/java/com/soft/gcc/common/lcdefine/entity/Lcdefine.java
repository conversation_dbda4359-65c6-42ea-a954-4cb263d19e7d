package com.soft.gcc.common.lcdefine.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName Lcdefine
 */
@TableName(value ="Lcdefine")
@Data
public class Lcdefine implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "LcID")
    private Integer lcid;

    /**
     * 
     */
    @TableField(value = "lcName")
    private String lcname;

    /**
     * 
     */
    @TableField(value = "ywb")
    private String ywb;

    /**
     * 
     */
    @TableField(value = "ywUrl")
    private String ywurl;

    /**
     * 
     */
    @TableField(value = "xszd")
    private String xszd;

    /**
     * 
     */
    @TableField(value = "isUse")
    private Integer isuse;

    /**
     * 
     */
    @TableField(value = "app_url")
    private String appUrl;

    /**
     * 
     */
    @TableField(value = "app_ywb")
    private String appYwb;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Lcdefine other = (Lcdefine) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLcid() == null ? other.getLcid() == null : this.getLcid().equals(other.getLcid()))
            && (this.getLcname() == null ? other.getLcname() == null : this.getLcname().equals(other.getLcname()))
            && (this.getYwb() == null ? other.getYwb() == null : this.getYwb().equals(other.getYwb()))
            && (this.getYwurl() == null ? other.getYwurl() == null : this.getYwurl().equals(other.getYwurl()))
            && (this.getXszd() == null ? other.getXszd() == null : this.getXszd().equals(other.getXszd()))
            && (this.getIsuse() == null ? other.getIsuse() == null : this.getIsuse().equals(other.getIsuse()))
            && (this.getAppUrl() == null ? other.getAppUrl() == null : this.getAppUrl().equals(other.getAppUrl()))
            && (this.getAppYwb() == null ? other.getAppYwb() == null : this.getAppYwb().equals(other.getAppYwb()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLcid() == null) ? 0 : getLcid().hashCode());
        result = prime * result + ((getLcname() == null) ? 0 : getLcname().hashCode());
        result = prime * result + ((getYwb() == null) ? 0 : getYwb().hashCode());
        result = prime * result + ((getYwurl() == null) ? 0 : getYwurl().hashCode());
        result = prime * result + ((getXszd() == null) ? 0 : getXszd().hashCode());
        result = prime * result + ((getIsuse() == null) ? 0 : getIsuse().hashCode());
        result = prime * result + ((getAppUrl() == null) ? 0 : getAppUrl().hashCode());
        result = prime * result + ((getAppYwb() == null) ? 0 : getAppYwb().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", lcid=").append(lcid);
        sb.append(", lcname=").append(lcname);
        sb.append(", ywb=").append(ywb);
        sb.append(", ywurl=").append(ywurl);
        sb.append(", xszd=").append(xszd);
        sb.append(", isuse=").append(isuse);
        sb.append(", appUrl=").append(appUrl);
        sb.append(", appYwb=").append(appYwb);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}