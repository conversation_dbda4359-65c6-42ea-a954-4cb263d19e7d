package com.soft.gcc.common.dfdw_dict.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 东方多维-字典类型
 * <AUTHOR>
 * @TableName DFDW_DICT_TYPE
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="DFDW_DICT_TYPE")
@Data
public class DfdwTDictType extends DfdwBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字典名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 字典类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 删除时间
     */
    @TableField(value = "deleted_time")
    private Date deletedTime;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTimeArray;

}