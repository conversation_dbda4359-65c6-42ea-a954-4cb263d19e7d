package com.soft.gcc.xtbg.codegen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 代码生成表定义
 *
 * <AUTHOR>
 * @TableName infra_codegen_table
 */
@TableName(value = "infra_codegen_table")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InfraCodegenTable extends CodeBaseEntity {
    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据源配置的编号
     */
    @TableField(value = "data_source_config_id")
    private Long dataSourceConfigId;

    /**
     * 生成场景
     */
    @TableField(value = "scene")
    private Integer scene;

    // ========== 表相关字段 ==========

    /**
     * 表名称
     */
    @TableField(value = "table_name")
    private String tableName;

    /**
     * 表描述
     */
    @TableField(value = "table_comment")
    private String tableComment;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    // ========== 类相关字段 ==========

    /**
     * 模块名
     */
    @TableField(value = "module_name")
    private String moduleName;

    /**
     * 业务名
     */
    @TableField(value = "business_name")
    private String businessName;

    /**
     * 类名称
     */
    @TableField(value = "class_name")
    private String className;

    /**
     * 类描述
     */
    @TableField(value = "class_comment")
    private String classComment;

    /**
     * 作者
     */
    @TableField(value = "author")
    private String author;

    // ========== 生成相关字段 ==========

    /**
     * 模板类型
     */
    @TableField(value = "template_type")
    private Integer templateType;

    /**
     * 前端类型
     */
    @TableField(value = "front_type")
    private Integer frontType;

    /**
     * 父菜单编号
     */
    @TableField(value = "parent_menu_id")
    private Long parentMenuId;

    /**
     * 权限前缀
     */
    @TableField(value = "permission_prefix")
    private String permissionPrefix;

    /**
     * 模块id
     */
    @TableField(value = "module_id")
    private Integer moduleId;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTimeArray;

    @TableField(exist = false)
    private List<String> tableNames;
}