package com.soft.gcc.xtbg.base.util;

import java.lang.annotation.*;

/**
 * 用于判断是否需要合并以及合并的主键
 * 在需要合并单元格的属性上设置 @ExcelMerge 注解
 *
 */

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface  ExcelMerge {
    /**
     * 是否合并单元格
     *
     * @return true || false
     */
    boolean merge() default true;

    /**
     * 是否为主键（即该字段相同的行合并）
     *
     * @return true || false
     */
    boolean isPrimaryKey() default false;
}
