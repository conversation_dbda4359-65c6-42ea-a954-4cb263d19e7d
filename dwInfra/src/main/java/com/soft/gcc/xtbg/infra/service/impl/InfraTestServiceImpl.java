package com.soft.gcc.xtbg.infra.service.impl;

import com.soft.framework.common.utils.bean.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.soft.gcc.xtbg.infra.controller.infratest.vo.*;
import com.soft.gcc.xtbg.infra.entity.InfraTest;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.soft.gcc.xtbg.infra.service.*;
import com.soft.gcc.xtbg.infra.mapper.InfraTestMapper;

/**
 * 代码生成器测试 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InfraTestServiceImpl extends ServiceImpl<InfraTestMapper, InfraTest> implements InfraTestService {

    @Override
    public Integer createTest(InfraTestCreateReqVO createReqVO) {
        // 插入
        InfraTest test = new InfraTest();
        BeanUtils.copyProperties(createReqVO, test);
        baseMapper.insert(test);
        // 返回
        return test.getId();
    }

    @Override
    public void updateTest(InfraTestUpdateReqVO updateReqVO) throws RuntimeException {
        // 校验存在
        validateTestExists(updateReqVO.getId());
        // 更新
        InfraTest test = new InfraTest();
        BeanUtils.copyProperties(updateReqVO, test);
        baseMapper.updateById(test);
    }

    @Override
    public void deleteTest(Integer id) throws RuntimeException {
        // 校验存在
        validateTestExists(id);
        // 删除
        baseMapper.deleteById(id);
    }

    private void validateTestExists(Integer id) throws RuntimeException {
        if (baseMapper.selectById(id) == null) {
            throw new RuntimeException("代码生成器测试不存在");
        }
    }

    @Override
    public InfraTest getTest(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<InfraTest> getTestList(Collection<Integer> ids) {
        return baseMapper.selectBatchIds(ids);
    }

    @Override
    public IPage<InfraTest> getTestPage(InfraTest pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InfraTest> getTestList(InfraTest exportReqVO) {
        return baseMapper.selectList(exportReqVO);
    }

}
