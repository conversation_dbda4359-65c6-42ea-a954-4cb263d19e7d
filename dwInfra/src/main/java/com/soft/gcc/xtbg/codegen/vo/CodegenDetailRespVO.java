package com.soft.gcc.xtbg.codegen.vo;

import com.soft.gcc.xtbg.codegen.entity.InfraCodegenColumn;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenTable;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CodegenDetailRespVO {

    @Valid // 校验内嵌的字段
    @NotNull(message = "表定义不能为空")
    private InfraCodegenTable table;

    @Valid // 校验内嵌的字段
    @NotNull(message = "字段定义不能为空")
    private List<InfraCodegenColumn> columns;
}
