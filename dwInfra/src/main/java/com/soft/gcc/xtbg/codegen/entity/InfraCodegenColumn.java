package com.soft.gcc.xtbg.codegen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 代码生成表字段定义
 * <AUTHOR>
 * @TableName infra_codegen_column
 */
@TableName(value ="infra_codegen_column")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InfraCodegenColumn extends CodeBaseEntity {
    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 表编号
     */
    @TableField(value = "table_id")
    private Long tableId;

    /**
     * 字段名
     */
    @TableField(value = "column_name")
    private String columnName;

    /**
     * 字段类型
     */
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 字段描述
     */
    @TableField(value = "column_comment")
    private String columnComment;

    /**
     * 是否允许为空
     */
    @TableField(value = "nullable")
    private Boolean nullable;

    /**
     * 是否主键
     */
    @TableField(value = "primary_key")
    private Boolean primaryKey;

    /**
     * 是否自增
     */
    @TableField(value = "auto_increment")
    private Boolean autoIncrement;

    /**
     * 排序
     */
    @TableField(value = "ordinal_position")
    private Integer ordinalPosition;

    /**
     * Java 属性类型
     */
    @TableField(value = "java_type")
    private String javaType;

    /**
     * Java 属性名
     */
    @TableField(value = "java_field")
    private String javaField;

    /**
     * 字典类型
     */
    @TableField(value = "dict_type")
    private String dictType;

    /**
     * 数据示例
     */
    @TableField(value = "example")
    private String example;

    /**
     * 是否为 Create 创建操作的字段
     */
    @TableField(value = "create_operation")
    private Boolean createOperation;

    /**
     * 是否为 Update 更新操作的字段
     */
    @TableField(value = "update_operation")
    private Boolean updateOperation;

    /**
     * 是否为 List 查询操作的字段
     */
    @TableField(value = "list_operation")
    private Boolean listOperation;

    /**
     * List 查询操作的条件类型
     */
    @TableField(value = "list_operation_condition")
    private String listOperationCondition;

    /**
     * 是否为 List 查询操作的返回字段
     */
    @TableField(value = "list_operation_result")
    private Boolean listOperationResult;

    /**
     * 显示类型
     */
    @TableField(value = "html_type")
    private String htmlType;

}