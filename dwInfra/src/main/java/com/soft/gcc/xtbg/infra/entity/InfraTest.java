package com.soft.gcc.xtbg.infra.entity;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;

/**
 * 代码生成器测试
 *
 * <AUTHOR>
 */
@TableName("dfdw_infra_test")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InfraTest extends DfdwBaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字符串
     */
    @TableField(value = "str")
    private String str;

    /**
     * 整数
     */
    @TableField(value = "num")
    private Integer num;

    /**
     * 字典
     */
    @TableField(value = "dict")
    private String dict;

    /**
     * 日期
     */
    @TableField(value = "useDate")
    private Date useDate;

    @TableField(exist = false)
    private Date[] useDateArray;
    /**
     * 小数
     */
    @TableField(value = "dou")
    private Double dou;


}
