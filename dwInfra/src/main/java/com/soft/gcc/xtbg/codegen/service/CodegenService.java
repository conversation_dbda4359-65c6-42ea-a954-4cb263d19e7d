package com.soft.gcc.xtbg.codegen.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenTable;
import com.soft.gcc.xtbg.codegen.vo.CodegenDetailRespVO;

import java.util.List;
import java.util.Map;

/**
 * 代码生成 Service 接口
 *
 * <AUTHOR>
 */
public interface CodegenService {
    List<InfraCodegenTable> getDatabaseTableList(Long dataSourceConfigId, String name, String comment);

    IPage<InfraCodegenTable> getCodegenTablePage(InfraCodegenTable infraCodegenTable);

    CodegenDetailRespVO getCodegenDetail(Long tableId);

    List<Long> createCodegenList(InfraCodegenTable table);

    void updateCodegen(CodegenDetailRespVO updateReqVO);

    void syncCodegenFromDB(Long tableId);

    void deleteCodegen(Long tableId);

    Map<String, String> generationCodes(Long tableId);
}
