package com.soft.gcc.xtbg.codegen.service;

import com.soft.gcc.xtbg.codegen.entity.InfraDataSourceConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【infra_data_source_config(数据源配置表)】的数据库操作Service
* @createDate 2024-07-16 13:41:36
*/
public interface InfraDataSourceConfigService extends IService<InfraDataSourceConfig> {

    List<InfraDataSourceConfig> getDataSourceConfigList();
}
