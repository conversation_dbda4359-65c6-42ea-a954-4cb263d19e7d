package com.soft.gcc.xtbg.infra.controller.infratest.vo;

import com.soft.gcc.xtbg.base.annotation.DictFormat;
import com.soft.gcc.xtbg.base.convert.DictConvert;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 代码生成器测试 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class InfraTestExcelVO {

    @ExcelProperty("主键")
    private Integer id;

    @ExcelProperty("字符串")
    private String str;

    @ExcelProperty("整数")
    private Integer num;

    @ExcelProperty(value = "字典", converter = DictConvert.class)
    @DictFormat("infra_is_or_no")
    private String dict;

    @ExcelProperty("日期")
    private Date useDate;

    @ExcelProperty("小数")
    private Double dou;

    @ExcelProperty("创建时间")
    private Date createTime;

}
