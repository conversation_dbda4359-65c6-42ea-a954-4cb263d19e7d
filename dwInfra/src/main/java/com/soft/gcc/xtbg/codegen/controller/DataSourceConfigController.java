package com.soft.gcc.xtbg.codegen.controller;

import com.soft.framework.domain.AjaxResult;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.codegen.entity.InfraDataSourceConfig;
import com.soft.gcc.xtbg.codegen.service.InfraDataSourceConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/infra/data-source-config")
@Validated
public class DataSourceConfigController extends BaseController {

    @Resource
    private InfraDataSourceConfigService infraDataSourceConfigService;

    @GetMapping("/list")
    public AjaxResult getDataSourceConfigList() {
        List<InfraDataSourceConfig> list = infraDataSourceConfigService.getDataSourceConfigList();
        return AjaxResult.success(list);
    }
}
