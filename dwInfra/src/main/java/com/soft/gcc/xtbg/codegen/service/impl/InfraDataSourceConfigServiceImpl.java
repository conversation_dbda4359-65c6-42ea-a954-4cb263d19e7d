package com.soft.gcc.xtbg.codegen.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.codegen.entity.InfraDataSourceConfig;
import com.soft.gcc.xtbg.codegen.service.InfraDataSourceConfigService;
import com.soft.gcc.xtbg.codegen.mapper.InfraDataSourceConfigMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【infra_data_source_config(数据源配置表)】的数据库操作Service实现
* @createDate 2024-07-16 13:41:36
*/
@Service
public class InfraDataSourceConfigServiceImpl extends ServiceImpl<InfraDataSourceConfigMapper, InfraDataSourceConfig>
    implements InfraDataSourceConfigService{

    @Override
    public List<InfraDataSourceConfig> getDataSourceConfigList() {
        return this.list();
    }
}




