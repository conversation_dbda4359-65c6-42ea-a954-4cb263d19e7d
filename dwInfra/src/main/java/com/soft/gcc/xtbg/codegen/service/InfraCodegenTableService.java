package com.soft.gcc.xtbg.codegen.service;

import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenTable;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【infra_codegen_table(代码生成表定义)】的数据库操作Service
* @createDate 2024-07-16 13:41:36
*/
public interface InfraCodegenTableService extends IService<InfraCodegenTable> {

    /**
     * 获得表列表，基于表名称 + 表描述进行模糊匹配
     *
     * @param dataSourceConfigId 数据源配置的编号
     * @param nameLike 表名称，模糊匹配
     * @param commentLike 表描述，模糊匹配
     * @return 表列表
     */
    List<TableInfo> getTableList(Long dataSourceConfigId, String nameLike, String commentLike);

    /**
     * 获得指定表名
     *
     * @param dataSourceConfigId 数据源配置的编号
     * @param tableName 表名称
     * @return 表
     */
    TableInfo getTable(Long dataSourceConfigId, String tableName);
}
