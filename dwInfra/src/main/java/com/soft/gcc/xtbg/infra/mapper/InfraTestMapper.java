package com.soft.gcc.xtbg.infra.mapper;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.base.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.infra.entity.InfraTest;
import org.apache.ibatis.annotations.Mapper;

/**
 * 代码生成器测试 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraTestMapper extends BaseMapper<InfraTest> {

    default IPage<InfraTest> selectPage(InfraTest reqVO) {
        return selectPage(new Page<>(reqVO.getPageNum() - 1, reqVO.getPageSize()), new LambdaQueryWrapperX<InfraTest>()
                .eqIfPresent(InfraTest::getStr, reqVO.getStr())
                .eqIfPresent(InfraTest::getNum, reqVO.getNum())
                .eqIfPresent(InfraTest::getDict, reqVO.getDict())
                .betweenIfPresent(InfraTest::getUseDate, reqVO.getUseDateArray())
                .eqIfPresent(InfraTest::getDou, reqVO.getDou())
                .betweenIfPresent(InfraTest::getCreateTime, reqVO.getCreateTimeArray())
                .orderByDesc(InfraTest::getId));
    }

    default List<InfraTest> selectList(InfraTest reqVO) {
        return selectList(new LambdaQueryWrapperX<InfraTest>()
                .eqIfPresent(InfraTest::getStr, reqVO.getStr())
                .eqIfPresent(InfraTest::getNum, reqVO.getNum())
                .eqIfPresent(InfraTest::getDict, reqVO.getDict())
                .betweenIfPresent(InfraTest::getUseDate, reqVO.getUseDateArray())
                .eqIfPresent(InfraTest::getDou, reqVO.getDou())
                .betweenIfPresent(InfraTest::getCreateTime, reqVO.getCreateTimeArray())
                .orderByDesc(InfraTest::getId));
    }

}
