package com.soft.gcc.xtbg.codegen.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.enums.SqlLike;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.converts.SqlServerTypeConvert;
import com.baomidou.mybatisplus.generator.config.po.LikeTable;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.querys.SqlServerQuery;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.query.DefaultQuery;
import com.baomidou.mybatisplus.generator.query.SQLQuery;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenTable;
import com.soft.gcc.xtbg.codegen.entity.InfraDataSourceConfig;
import com.soft.gcc.xtbg.codegen.service.InfraCodegenTableService;
import com.soft.gcc.xtbg.codegen.mapper.InfraCodegenTableMapper;
import com.soft.gcc.xtbg.codegen.service.InfraDataSourceConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【infra_codegen_table(代码生成表定义)】的数据库操作Service实现
* @createDate 2024-07-16 13:41:36
*/
@Service
public class InfraCodegenTableServiceImpl extends ServiceImpl<InfraCodegenTableMapper, InfraCodegenTable>
    implements InfraCodegenTableService {
    @Resource
    private InfraDataSourceConfigService infraDataSourceConfigService;

    @Override
    public List<TableInfo> getTableList(Long dataSourceConfigId, String nameLike, String commentLike) {
        List<TableInfo> tables = getTableList0(dataSourceConfigId, null);
        return tables.stream().filter(tableInfo -> (StrUtil.isEmpty(nameLike) || tableInfo.getName().contains(nameLike))
                        && (StrUtil.isEmpty(commentLike) || tableInfo.getComment().contains(commentLike)))
                .collect(Collectors.toList());
    }

    @Override
    public TableInfo getTable(Long dataSourceConfigId, String name) {
        return CollUtil.getFirst(getTableList0(dataSourceConfigId, name));
    }

    private List<TableInfo> getTableList0(Long dataSourceConfigId, String name) {
        // 获得数据源配置
        InfraDataSourceConfig config = infraDataSourceConfigService.getById(dataSourceConfigId);
        Assert.notNull(config, "数据源({}) 不存在！", dataSourceConfigId);

        // 使用 MyBatis Plus Generator 解析表结构
        DataSourceConfig dataSourceConfig = new DataSourceConfig
                .Builder(config.getUrl(), config.getUsername(), config.getPassword())
                .databaseQueryClass(SQLQuery.class)
                .dbQuery(new SqlServerQuery())
                .typeConvert(new SqlServerTypeConvert())
                .build();
        StrategyConfig.Builder strategyConfig = new StrategyConfig.Builder();
        if (StrUtil.isNotEmpty(name)) {
            strategyConfig.addInclude(name);
        } else {
            LikeTable table = new LikeTable("DFDW_", SqlLike.RIGHT);
            strategyConfig.likeTable(table);
        }
        GlobalConfig globalConfig = new GlobalConfig.Builder().dateType(DateType.ONLY_DATE).build();
        ConfigBuilder builder = new ConfigBuilder(null, dataSourceConfig, strategyConfig.build(),
                null, globalConfig, null);
        // 按照名字排序
        List<TableInfo> tables = builder.getTableInfoList();
        tables.sort(Comparator.comparing(TableInfo::getName));
        return tables;
    }
}




