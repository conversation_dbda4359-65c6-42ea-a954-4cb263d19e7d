package com.soft.gcc.xtbg.infra.controller.infratest;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.io.IOException;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.framework.domain.AjaxResult;
import static com.soft.framework.domain.AjaxResult.success;

import com.soft.gcc.xtbg.base.util.ExcelUtils;

import com.soft.gcc.xtbg.infra.controller.infratest.vo.*;
import com.soft.gcc.xtbg.infra.entity.InfraTest;
import com.soft.gcc.xtbg.infra.service.InfraTestService;

@RestController
@RequestMapping("/Infra/test")
@Validated
public class InfraTestController {

    @Resource
    private InfraTestService testService;

    @PostMapping("/create")
    @ApiOperation(value = "创建代码生成器测试")
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX02')")
    public AjaxResult createTest(@Valid @RequestBody InfraTestCreateReqVO createReqVO) {
        return success(testService.createTest(createReqVO));
    }

    @PutMapping("/update")
    @ApiOperation(value = "更新代码生成器测试")
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX03')")
    public AjaxResult updateTest(@Valid @RequestBody InfraTestUpdateReqVO updateReqVO) {
        testService.updateTest(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除代码生成器测试")
    @ApiParam(name = "id", value = "编号", required = true)
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX04')")
    public AjaxResult deleteTest(@RequestParam("id") Integer id) {
        testService.deleteTest(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得代码生成器测试")
    @ApiParam(name = "id", value = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX01')")
    public AjaxResult getTest(@RequestParam("id") Integer id) {
        InfraTest test = testService.getTest(id);
        return success(test);
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得代码生成器测试列表")
    @ApiParam(name = "ids", value = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX01')")
    public AjaxResult getTestList(@RequestParam("ids") Collection<Integer> ids) {
        List<InfraTest> list = testService.getTestList(ids);
        return success(list);
    }

    @GetMapping("/page")
    @ApiOperation(value = "获得代码生成器测试分页")
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX01')")
    public AjaxResult getTestPage(@Valid InfraTest pageVO) {
        IPage<InfraTest> pageResult = testService.getTestPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @ApiOperation(value = "导出代码生成器测试 Excel")
    @PreAuthorize("@ss.hasPermi('JDWIN01TT01QX05')")
    public void exportTestExcel(@Valid InfraTest exportReqVO, HttpServletResponse response) throws IOException {
        List<InfraTest> list = testService.getTestList(exportReqVO);
        // 导出 Excel
        List<InfraTestExcelVO> datas = new ArrayList<>();
        for (InfraTest test : list) {
            InfraTestExcelVO excelVO = new InfraTestExcelVO();
            BeanUtils.copyProperties(test, excelVO);
            datas.add(excelVO);
        }
        ExcelUtils.write(response, "代码生成器测试.xls", "数据", InfraTestExcelVO.class, datas);
    }

}
