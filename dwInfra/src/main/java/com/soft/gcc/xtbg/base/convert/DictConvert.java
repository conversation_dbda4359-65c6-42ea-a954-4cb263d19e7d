package com.soft.gcc.xtbg.base.convert;

import cn.hutool.core.convert.Convert;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.xtbg.base.annotation.DictFormat;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel 数据字典转换器
 *
 * <AUTHOR>
 */
@Slf4j
public class DictConvert implements Converter<Object> {

    private DfdwTDictDataService dfdwTDictDataService;

    public DictConvert() {
        this.dfdwTDictDataService = SpringUtil.getBean(DfdwTDictDataService.class);
    }

    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public Object convertToJavaData(ReadCellData readCellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        // 使用字典解析
        String type = getType(contentProperty);
        String label = readCellData.getStringValue();
        String value = getDictDataByTypeAndLabel(type, label);
        if (value == null) {
            log.error("[convertToJavaData][type({}) 解析不掉 label({})]", type, label);
            return null;
        }
        // 将 String 的 value 转换成对应的属性
        Class<?> fieldClazz = contentProperty.getField().getType();
        return Convert.convert(fieldClazz, value);
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        // 空时，返回空
        if (object == null) {
            return new WriteCellData<>("");
        }

        // 使用字典格式化
        String type = getType(contentProperty);
        String value = String.valueOf(object);
        String label = "";
        if (value.contains(",")) {
            String[] values = value.split(",");
            for (String v : values) {
                label += getDictDataByTypeAndLabel(type, v) + " ";
            }
        }else {
            label = getDictDataByTypeAndLabel(type, value);
        }
        if (label == null || label.isEmpty()) {
            log.error("[convertToExcelData][type({}) 转换不了 label({})]", type, value);
            return new WriteCellData<>("");
        }
        // 生成 Excel 小表格
        return new WriteCellData<>(label);
    }

    private static String getType(ExcelContentProperty contentProperty) {
        return contentProperty.getField().getAnnotation(DictFormat.class).value();
    }

    private String getDictDataByTypeAndLabel(String type, String label) {
        DfdwTDictData dictData = dfdwTDictDataService.getDictDataByTypeAndLabel(type, label);
        if (dictData != null) {
            return dictData.getValue();
        }
        return null;
    }
}
