package com.soft.gcc.xtbg.codegen.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenColumn;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenTable;
import com.soft.gcc.xtbg.codegen.enums.CodegenSceneEnum;
import com.soft.gcc.xtbg.codegen.framework.CodegenProperties;
import com.soft.gcc.xtbg.codegen.service.inner.CodegenBuilder;
import com.soft.gcc.xtbg.codegen.service.inner.CodegenEngine;
import com.soft.gcc.xtbg.base.util.CollectionUtils;
import com.soft.gcc.xtbg.codegen.vo.CodegenDetailRespVO;
import com.soft.gcc.xtbg.codegen.service.CodegenService;
import com.soft.gcc.xtbg.codegen.service.InfraCodegenColumnService;
import com.soft.gcc.xtbg.codegen.service.InfraCodegenTableService;
import com.soft.gcc.xtbg.codegen.service.InfraDataSourceConfigService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

/**
 * 代码生成 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class CodegenServiceImpl implements CodegenService {

    @Resource
    private InfraCodegenColumnService infraCodegenColumnService;
    @Resource
    private InfraCodegenTableService infraCodegenTableService;
    @Resource
    private InfraDataSourceConfigService infraDataSourceConfigService;

    @Resource
    private CodegenBuilder codegenBuilder;
    @Resource
    private CodegenEngine codegenEngine;

    @Resource
    private CodegenProperties codegenProperties;

    public PersonEntity user() {
        PersonEntity personEntity = SessionHelper.getSessionPerson();
        if (personEntity != null) {
            return personEntity;
        } else {
            return null;
        }
    }

    @Override
    public List<InfraCodegenTable> getDatabaseTableList(Long dataSourceConfigId, String name, String comment) {
        List<TableInfo> tables = infraCodegenTableService.getTableList(dataSourceConfigId, name, comment);
        // 移除已经生成的表
        // 移除在 Codegen 中，已经存在的
        Set<String> existsTables = CollectionUtils.convertSet(
                infraCodegenTableService.list(new LambdaQueryWrapper<InfraCodegenTable>().eq(InfraCodegenTable::getDataSourceConfigId, dataSourceConfigId)),
                InfraCodegenTable::getTableName
        );
        tables.removeIf(table -> existsTables.contains(table.getName()));
        List<InfraCodegenTable> result = new ArrayList<>(tables.size());
        for (TableInfo table : tables) {
            InfraCodegenTable codegenTable = new InfraCodegenTable();
            codegenTable.setTableName(table.getName());
            codegenTable.setTableComment(table.getComment());
            result.add(codegenTable);
        }
        return result;
    }

    @Override
    public IPage<InfraCodegenTable> getCodegenTablePage(InfraCodegenTable infraCodegenTable) {
        LambdaQueryWrapper<InfraCodegenTable> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(infraCodegenTable.getTableName() != null, InfraCodegenTable::getTableName, infraCodegenTable.getTableName())
                .like(infraCodegenTable.getTableComment() != null, InfraCodegenTable::getTableComment, infraCodegenTable.getTableComment())
                .like(infraCodegenTable.getClassName() != null, InfraCodegenTable::getClassName, infraCodegenTable.getClassName());
        if (infraCodegenTable.getCreateTimeArray() != null) {
            wrapper.between(InfraCodegenTable::getCreateTime, infraCodegenTable.getCreateTimeArray()[0], infraCodegenTable.getCreateTimeArray()[1]);
        }
        wrapper.orderByAsc(InfraCodegenTable::getId);
        return infraCodegenTableService.page(
                new Page<>(infraCodegenTable.getPageNum() - 1, infraCodegenTable.getPageSize()),
                wrapper
        );
    }

    @Override
    public CodegenDetailRespVO getCodegenDetail(Long tableId) {
        CodegenDetailRespVO respVO = new CodegenDetailRespVO();
        respVO.setTable(infraCodegenTableService.getById(tableId));
        respVO.setColumns(infraCodegenColumnService.list(new LambdaQueryWrapper<InfraCodegenColumn>().eq(InfraCodegenColumn::getTableId, tableId)));
        return respVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> createCodegenList(InfraCodegenTable table) {
        PersonEntity person = user();
        List<Long> ids = new ArrayList<>(table.getTableNames().size());
        // 遍历添加。虽然效率会低一点，但是没必要做成完全批量，因为不会这么大量
        table.getTableNames().forEach(tableName -> ids.add(createCodegen(person.getId(), table.getDataSourceConfigId(), tableName)));
        return ids;
    }

    private Long createCodegen(Integer userId, Long dataSourceConfigId, String tableName) {
        // 从数据库中，获得数据库表结构
        TableInfo tableInfo = infraCodegenTableService.getTable(dataSourceConfigId, tableName);
        // 导入
        return createCodegen0(userId, dataSourceConfigId, tableInfo);
    }

    private Long createCodegen0(Integer userId, Long dataSourceConfigId, TableInfo tableInfo) {
        PersonEntity person = user();
        // 校验导入的表和字段非空
        validateTableInfo(tableInfo);
        // 校验是否已经存在
        if (infraCodegenTableService.getOne(new LambdaQueryWrapper<InfraCodegenTable>()
                .eq(InfraCodegenTable::getTableName, tableInfo.getName())
                .eq(InfraCodegenTable::getDataSourceConfigId, dataSourceConfigId)
        ) != null) {
            throw new ServiceException("表定义已经存在");
        }
        InfraCodegenTable table = codegenBuilder.buildTable(tableInfo);
        table.setDataSourceConfigId(dataSourceConfigId);
        table.setScene(CodegenSceneEnum.ADMIN.getScene()); // 默认配置下，使用管理后台的模板
        table.setFrontType(codegenProperties.getFrontType());
        table.setAuthor(person.getRealName());
        infraCodegenTableService.save(table);

        List<InfraCodegenColumn> columns = codegenBuilder.buildColumns(table.getId(), tableInfo.getFields());
        // 如果没有主键，则使用第一个字段作为主键
        if (!tableInfo.isHavePrimaryKey()) {
            columns.get(0).setPrimaryKey(true);
        }
        for (InfraCodegenColumn column : columns) {
            infraCodegenColumnService.save(column);
        }
        return table.getId();
    }

    private void validateTableInfo(TableInfo tableInfo) {
        if (tableInfo == null) {
            throw new ServiceException("导入的表不存在");
        }
        if (StrUtil.isEmpty(tableInfo.getComment())) {
            throw new ServiceException("数据库的表注释未填写");
        }
        if (CollUtil.isEmpty(tableInfo.getFields())) {
            throw new ServiceException("导入的字段不存在");
        }
        tableInfo.getFields().forEach(field -> {
            if (StrUtil.isEmpty(field.getComment())) {
                throw new ServiceException("数据库的表字段(" + field.getName() + ")注释未填写");
            }
        });
    }

    @Override
    public void updateCodegen(CodegenDetailRespVO updateReqVO) {
        // 校验是否已经存在
        if (infraCodegenTableService.getById(updateReqVO.getTable().getId()) == null) {
            throw new ServiceException("表定义不存在");
        }

        // 更新 table 表定义
        infraCodegenTableService.updateById(updateReqVO.getTable());
        // 更新 column 字段定义
        updateReqVO.getColumns().forEach(column -> infraCodegenColumnService.updateById(column));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCodegenFromDB(Long tableId) {
        // 校验是否已经存在
        InfraCodegenTable table = infraCodegenTableService.getById(tableId);
        if (table == null) {
            throw new ServiceException("表定义不存在");
        }
        // 从数据库中，获得数据库表结构
        TableInfo tableInfo = infraCodegenTableService.getTable(table.getDataSourceConfigId(), table.getTableName());
        // 执行同步
        syncCodegen0(tableId, tableInfo);
    }

    private void syncCodegen0(Long tableId, TableInfo tableInfo) {
        // 校验导入的表和字段非空
        validateTableInfo(tableInfo);
        List<TableField> tableFields = tableInfo.getFields();

        // 构建 CodegenColumnDO 数组，只同步新增的字段
        List<InfraCodegenColumn> codegenColumns = infraCodegenColumnService.list(new LambdaUpdateWrapper<InfraCodegenColumn>()
                .eq(InfraCodegenColumn::getTableId, tableId));
        Set<String> codegenColumnNames = CollectionUtils.convertSet(codegenColumns, InfraCodegenColumn::getColumnName);

        //计算需要修改的字段，插入时重新插入，删除时将原来的删除
        BiPredicate<TableField, InfraCodegenColumn> pr =
                (tableField, codegenColumn) -> tableField.getMetaInfo().getJdbcType().name().equals(codegenColumn.getDataType())
                        && tableField.getMetaInfo().isNullable() == codegenColumn.getNullable()
                        && tableField.isKeyFlag() == codegenColumn.getPrimaryKey()
                        && tableField.getComment().equals(codegenColumn.getColumnComment());
        Map<String, InfraCodegenColumn> codegenColumnDOMap = CollectionUtils.convertMap(codegenColumns, InfraCodegenColumn::getColumnName);
        //需要修改的字段
        Set<String> modifyFieldNames = tableFields.stream()
                .filter(tableField -> codegenColumnDOMap.get(tableField.getColumnName()) != null
                        && !pr.test(tableField, codegenColumnDOMap.get(tableField.getColumnName())))
                .map(TableField::getColumnName)
                .collect(Collectors.toSet());
        // 计算需要删除的字段
        Set<String> tableFieldNames = CollectionUtils.convertSet(tableFields, TableField::getName);
        Set<Long> deleteColumnIds = codegenColumns.stream()
                .filter(column -> (!tableFieldNames.contains(column.getColumnName())) || modifyFieldNames.contains(column.getColumnName()))
                .map(InfraCodegenColumn::getId).collect(Collectors.toSet());
        // 移除已经存在的字段
        tableFields.removeIf(column -> codegenColumnNames.contains(column.getColumnName()) && (!modifyFieldNames.contains(column.getColumnName())));
        if (CollUtil.isEmpty(tableFields) && CollUtil.isEmpty(deleteColumnIds)) {
            throw new ServiceException("同步失败，不存在改变");
        }

        // 插入新增的字段
        List<InfraCodegenColumn> columns = codegenBuilder.buildColumns(tableId, tableFields);
        for (InfraCodegenColumn column : columns) {
            infraCodegenColumnService.save(column);
        }
        // 删除不存在的字段
        if (CollUtil.isNotEmpty(deleteColumnIds)) {
            infraCodegenColumnService.removeByIds(deleteColumnIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCodegen(Long tableId) {
        // 校验是否已经存在
        if (infraCodegenTableService.getById(tableId) == null) {
            throw new ServiceException("表定义不存在");
        }

        // 删除 table 表定义
        infraCodegenTableService.removeById(tableId);
        // 删除 column 字段定义
        infraCodegenColumnService.remove(new LambdaQueryWrapper<InfraCodegenColumn>().eq(InfraCodegenColumn::getTableId, tableId));
    }

    @Override
    public Map<String, String> generationCodes(Long tableId) {
        // 校验是否已经存在
        InfraCodegenTable table = infraCodegenTableService.getById(tableId);
        if (table == null) {
            throw new ServiceException("表定义不存在");
        }
        List<InfraCodegenColumn> columns = infraCodegenColumnService.list(new LambdaQueryWrapper<InfraCodegenColumn>()
                .eq(InfraCodegenColumn::getTableId, tableId));
        if (CollUtil.isEmpty(columns)) {
            throw new ServiceException("字段义不存在");
        }

        // 执行生成
        return codegenEngine.execute(table, columns);
    }
}
