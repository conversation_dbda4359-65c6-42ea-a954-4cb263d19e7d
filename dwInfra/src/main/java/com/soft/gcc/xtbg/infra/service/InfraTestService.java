package com.soft.gcc.xtbg.infra.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.*;
import javax.validation.*;
import com.soft.gcc.xtbg.infra.controller.infratest.vo.*;
import com.soft.gcc.xtbg.infra.entity.InfraTest;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 代码生成器测试 Service 接口
 *
 * <AUTHOR>
 */
public interface InfraTestService extends IService<InfraTest> {

    /**
     * 创建代码生成器测试
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createTest(@Valid InfraTestCreateReqVO createReqVO);

    /**
     * 更新代码生成器测试
     *
     * @param updateReqVO 更新信息
     */
    void updateTest(@Valid InfraTestUpdateReqVO updateReqVO) throws RuntimeException;

    /**
     * 删除代码生成器测试
     *
     * @param id 编号
     */
    void deleteTest(Integer id) throws RuntimeException;

    /**
     * 获得代码生成器测试
     *
     * @param id 编号
     * @return 代码生成器测试
     */
    InfraTest getTest(Integer id);

    /**
     * 获得代码生成器测试列表
     *
     * @param ids 编号
     * @return 代码生成器测试列表
     */
    List<InfraTest> getTestList(Collection<Integer> ids);

    /**
     * 获得代码生成器测试分页
     *
     * @param pageReqVO 分页查询
     * @return 代码生成器测试分页
     */
    IPage<InfraTest> getTestPage(InfraTest pageReqVO);

    /**
     * 获得代码生成器测试列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 代码生成器测试列表
     */
    List<InfraTest> getTestList(InfraTest exportReqVO);

}
