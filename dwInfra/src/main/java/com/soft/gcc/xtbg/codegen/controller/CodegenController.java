package com.soft.gcc.xtbg.codegen.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ZipUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenTable;
import com.soft.gcc.xtbg.codegen.service.CodegenService;
import com.soft.gcc.xtbg.codegen.vo.CodegenDetailRespVO;
import com.soft.gcc.xtbg.codegen.vo.CodegenPreviewRespVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/infra/codegen")
@Validated
public class CodegenController extends BaseController {

    @Resource
    private CodegenService codegenService;

    @ApiOperation(value = "获得数据库自带的表定义列表 会过滤掉已经导入 Codegen 的表")
    @GetMapping("/db/table/list")
    public AjaxResult getDatabaseTableList(
            @RequestParam(value = "dataSourceConfigId") Long dataSourceConfigId,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "comment", required = false) String comment) {
        return AjaxResult.success(codegenService.getDatabaseTableList(dataSourceConfigId, name, comment));
    }

    @ApiOperation(value = "获得表定义分页")
    @GetMapping("/table/page")
    public AjaxResult getCodeGenTablePage(InfraCodegenTable infraCodegenTable) {
        return AjaxResult.success(codegenService.getCodegenTablePage(infraCodegenTable));
    }

    @ApiOperation(value = "获得表和字段的明细")
    @GetMapping("/detail")
    public AjaxResult getCodegenDetail(@RequestParam("tableId") Long tableId) {
        return AjaxResult.success(codegenService.getCodegenDetail(tableId));
    }

    @ApiOperation(value = "基于数据库的表结构，创建代码生成器的表和字段定义")
    @PostMapping("/create-list")
    public AjaxResult createCodegenList(@RequestBody InfraCodegenTable table) {
        return AjaxResult.success(codegenService.createCodegenList(table));
    }

    @ApiOperation(value = "更新数据库的表和字段定义")
    @PutMapping("/update")
    public AjaxResult updateCodegen(@Valid @RequestBody CodegenDetailRespVO updateReqVO) {
        codegenService.updateCodegen(updateReqVO);
        return AjaxResult.success();
    }

    @ApiOperation(value = "基于数据库的表结构，同步数据库的表和字段定义")
    @PutMapping("/sync-from-db")
    public AjaxResult syncCodegenFromDB(@RequestParam("tableId") Long tableId) {
        codegenService.syncCodegenFromDB(tableId);
        return AjaxResult.success();
    }

    @ApiOperation(value = "删除数据库的表和字段定义")
    @ApiParam(value = "表编号", required = true, example = "1")
    @DeleteMapping("/delete")
    public AjaxResult deleteCodegen(@RequestParam("tableId") Long tableId) {
        codegenService.deleteCodegen(tableId);
        return AjaxResult.success();
    }

    @ApiOperation(value = "预览生成代码")
    @GetMapping("/preview")
    public AjaxResult previewCodegen(@RequestParam("tableId") Long tableId) {
        Map<String, String> codes = codegenService.generationCodes(tableId);
        List<CodegenPreviewRespVO> list = codes.entrySet().stream().map(entry -> {
            CodegenPreviewRespVO respVO = new CodegenPreviewRespVO();
            respVO.setFilePath(entry.getKey());
            respVO.setCode(entry.getValue());
            return respVO;
        }).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    @ApiOperation(value = "下载生成代码")
    @GetMapping("/download")
    public void downloadCodegen(@RequestParam("tableId") Long tableId, HttpServletResponse response) throws IOException {
        // 生成代码
        Map<String, String> codes = codegenService.generationCodes(tableId);
        // 构建 zip 包
        String[] paths = codes.keySet().toArray(new String[0]);
        ByteArrayInputStream[] ins = codes.values().stream().map(IoUtil::toUtf8Stream).toArray(ByteArrayInputStream[]::new);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipUtil.zip(outputStream, paths, ins);
        // 输出
        // 设置 header 和 contentType
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("codegen.zip", "UTF-8"));
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        // 输出附件
        IoUtil.write(response.getOutputStream(), false, outputStream.toByteArray());
    }
}
