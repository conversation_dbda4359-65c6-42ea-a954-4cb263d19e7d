package com.soft.gcc.xtbg.codegen.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.codegen.entity.InfraCodegenColumn;
import com.soft.gcc.xtbg.codegen.service.InfraCodegenColumnService;
import com.soft.gcc.xtbg.codegen.mapper.InfraCodegenColumnMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【infra_codegen_column(代码生成表字段定义)】的数据库操作Service实现
* @createDate 2024-07-16 13:41:36
*/
@Service
public class InfraCodegenColumnServiceImpl extends ServiceImpl<InfraCodegenColumnMapper, InfraCodegenColumn>
    implements InfraCodegenColumnService {

}




