<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.clwx.mapper.DfdwClwxRepairFactoryMapper">
    <select id="getList" resultType="com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory">
        select rf.*, gi.groupname deptName from DFDW_CLWX_REPAIR_FACTORY rf
        left join GroupItem gi on rf.deptId = gi.id
        <where>
            rf.isDelete = 0
            <if test="repairFactory.repairFactoryName != null and repairFactory.repairFactoryName != ''">
                and rf.repairFactoryName like concat('%',#{repairFactory.repairFactoryName},'%')
            </if>
            <if test="repairFactory.deptId != null and repairFactory.deptId > -1">
                and rf.deptId = #{repairFactory.deptId}
            </if>
        </where>
        order by gi.XH, rf.repairFactoryName
    </select>
    <select id="getPage" resultType="com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory">
        select rf.*, gi.groupname deptName from DFDW_CLWX_REPAIR_FACTORY rf
        left join GroupItem gi on rf.deptId = gi.id
        <where>
            rf.isDelete = 0
            <if test="repairFactory.repairFactoryName != null and repairFactory.repairFactoryName != ''">
                and rf.repairFactoryName like concat('%',#{repairFactory.repairFactoryName},'%')
            </if>
            <if test="repairFactory.deptId != null and repairFactory.deptId > -1">
                and rf.deptId = #{repairFactory.deptId}
            </if>
        </where>
        order by gi.XH, rf.repairFactoryName
    </select>
</mapper>
