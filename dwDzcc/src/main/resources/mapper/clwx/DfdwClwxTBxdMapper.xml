<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.clwx.mapper.DfdwClwxTBxdMapper">
    <select id="getPage" resultType="com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd">
        select bxd.*,bxd.confirmed_status as confirmedStatus , car.LicencePlate as licencePlate, car.CarType as carType, car.CarMold as carMold, rf.repairFactoryName from DFDW_CLWX_T_BXD bxd
        left join LZCL_T_Car car on bxd.carId = car.id
        left join DFDW_CLWX_REPAIR_FACTORY rf on bxd.repairFactoryId = rf.id
        where bxd.isDelete=0
        <if test="bxd.applyNo != null and bxd.applyNo != ''">
            and bxd.applyNo like concat('%',#{bxd.applyNo},'%')
        </if>
        <if test="bxd.confirmedStatus != null ">
            and bxd.confirmed_status  =#{bxd.confirmedStatus}
        </if>
        <if test="bxd.applyTopDeptId != null and bxd.applyTopDeptId > -1">
            and bxd.applyTopDeptId = #{bxd.applyTopDeptId}
        </if>
        <if test="bxd.licencePlate != null and bxd.licencePlate != ''">
            and car.LicencePlate like concat('%',#{bxd.licencePlate},'%')
        </if>
        <if test="bxd.approveState != null">
            and bxd.approveState = #{bxd.approveState}
        </if>
        <if test="bxd.repairDateArray.length == 2">
            and bxd.repairDate between #{bxd.repairDateArray[0]} and #{bxd.repairDateArray[1]}
        </if>
        <if test="bxd.idList != null and bxd.idList.length > 0">
            and bxd.id in <foreach item="item" index="index" collection="bxd.idList" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="bxd.applyUserName != null and bxd.applyUserName != ''">
            and bxd.applyUserName like concat('%',#{bxd.applyUserName},'%')
        </if>
        order by bxd.applyNo desc
    </select>
    <select id="getList" resultType="com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd">
        select bxd.*, car.LicencePlate as licencePlate, car.CarType as carType, car.CarMold as carMold, rf.repairFactoryName from DFDW_CLWX_T_BXD bxd
        left join LZCL_T_Car car on bxd.carId = car.id
        left join DFDW_CLWX_REPAIR_FACTORY rf on bxd.repairFactoryId = rf.id
        where bxd.isDelete=0
        <if test="bxd.applyNo != null and bxd.applyNo != ''">
            and bxd.applyNo like concat('%',#{bxd.applyNo},'%')
        </if>
        <if test="bxd.applyTopDeptId != null and bxd.applyTopDeptId > -1">
            and bxd.applyTopDeptId = #{bxd.applyTopDeptId}
        </if>
        <if test="bxd.licencePlate != null and bxd.licencePlate != ''">
            and car.LicencePlate like concat('%',#{bxd.licencePlate},'%')
        </if>
        <if test="bxd.approveState != null">
            and bxd.approveState = #{bxd.approveState}
        </if>
        <if test="bxd.repairDateArray.length == 2">
            and bxd.repairDate between #{bxd.repairDateArray[0]} and #{bxd.repairDateArray[1]}
        </if>
        <if test="bxd.idList != null and bxd.idList.length > 0">
            and bxd.id in <foreach item="item" index="index" collection="bxd.idList" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="bxd.applyUserName != null and bxd.applyUserName != ''">
            and bxd.applyUserName like concat('%',#{bxd.applyUserName},'%')
        </if>
        order by bxd.applyNo desc
    </select>
</mapper>
