<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.LzclTCarStatusMapper">

    <sql id="Base_Column_List">
        Id,DeviceId,Mileage,
        RunTime,AllMileage,Status,
        OnLineStartTime,UpdateTime,CreateTime,
        UniqueId,StartMoveTime,StartMoveLongitude,
        StartMoveLatitude,MoveMileage,EndMoveTime,
        EndMoveLongitude,EndMoveLatitude,MoveTime,
        LicencePlate,PointCount,ActualMileage
    </sql>
    <select id="selectListByCarIdAndDate" resultType="com.soft.gcc.xtbg.dzcc.domain.LzclTCarStatus">
        SELECT cs.* FROM LZCL_T_Car_Status cs
        LEFT JOIN (
        SELECT
        car.GPSNum,
        IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate) startDate,
        IIF(min(detail.startDate) is null or min(detail.startDate) >= #{endDate}, EOMONTH(#{startDate}), DATEADD(DAY, -1, min(detail.startDate))) endDate
        FROM
        -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
        ( SELECT LicencePlate, GroupId, StartDate, IIF ( EndDate IS NULL, EOMONTH ( StartDate ), EndDate ) EndDate FROM LZCL_T_Car_Move_Child ) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
        -- 关联查询部门
        LEFT JOIN GroupItem gi on gi.id = mc.GroupId
        -- 关联查询司机
        LEFT JOIN (
        -- 查询当月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
        UNION
        -- 查询往月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        -- 关联查询结束时间
        left join DFDW_DZCC_T_CLGLDetail detail on detail.carId = car.Id AND detail.groupId = mc.GroupId and detail.startDate > IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate)
        WHERE car.Id = #{carId} AND clgl.driverId = #{driveId} AND gi.id = #{topGroupId}
        GROUP BY car.GPSNum, clgl.startDate) a ON cs.DeviceId = a.GPSNum AND CONVERT(date, cs.CreateTime) between a.startDate and a.endDate
        WHERE a.GPSNum IS NOT NULL
    </select>
</mapper>
