<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.LzclTCarMoreMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.LzclTCarMore">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="licencePlate" column="LicencePlate" jdbcType="VARCHAR"/>
            <result property="startDate" column="StartDate" jdbcType="DATE"/>
            <result property="endDate" column="EndDate" jdbcType="DATE"/>
            <result property="groupId" column="GroupId" jdbcType="INTEGER"/>
            <result property="updateDate" column="UpdateDate" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,LicencePlate,StartDate,
        EndDate,GroupId,UpdateDate
    </sql>
</mapper>
