<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccHoildayMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccHoilday">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="startDate" column="startDate" jdbcType="DATE"/>
            <result property="endDate" column="endDate" jdbcType="DATE"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createUserId" column="createUserId" jdbcType="INTEGER"/>
            <result property="createUserName" column="createUserName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,startDate,endDate,
        createTime,createUserId,createUserName
    </sql>
</mapper>
