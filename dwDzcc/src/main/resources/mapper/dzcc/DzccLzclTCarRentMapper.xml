<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccLzclTCarRentMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccLzclTCarRent">
        <id property="id" column="Id" jdbcType="INTEGER"/>
        <result property="carId" column="CarId" jdbcType="INTEGER"/>
        <result property="driverId" column="DriverId" jdbcType="INTEGER"/>
        <result property="useDeptId" column="UseDeptId" jdbcType="INTEGER"/>
        <result property="applyId" column="ApplyId" jdbcType="INTEGER"/>
        <result property="contractId" column="ContractId" jdbcType="INTEGER"/>
        <result property="contractStartDate" column="ContractStartDate" jdbcType="DATE"/>
        <result property="contractEndDate" column="ContractEndDate" jdbcType="DATE"/>
        <result property="realReturnDate" column="RealReturnDate" jdbcType="DATE"/>
        <result property="contractState" column="ContractState" jdbcType="VARCHAR"/>
        <result property="createDate" column="CreateDate" jdbcType="TIMESTAMP"/>
        <result property="carReturnType" column="CarReturnType" jdbcType="INTEGER"/>
        <result property="topDeptId" column="TopDeptId" jdbcType="INTEGER"/>
        <result property="batchId" column="BatchId" jdbcType="VARCHAR"/>
        <result property="contractBh" column="ContractBh" jdbcType="VARCHAR"/>
        <result property="contractAmount" column="ContractAmount" jdbcType="DECIMAL"/>
        <result property="contractTopDepId" column="ContractTopDepId" jdbcType="INTEGER"/>
        <result property="contractTopDepName" column="ContractTopDepName" jdbcType="VARCHAR"/>
        <result property="boundTime" column="BoundTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
                Id, CarId, DriverId,
                UseDeptId, ApplyId, ContractId,
                ContractStartDate, ContractEndDate, RealReturnDate,
                ContractState, CreateDate, CarReturnType,
                TopDeptId, BatchId, ContractBh,
                ContractAmount, ContractTopDepId, ContractTopDepName,
                BoundTime
    </sql>
    <select id="getLocusList" resultType="com.soft.gcc.xtbg.dzcc.entity.LocusDto">
        SELECT a.Id AS carId,
        a.LicencePlate AS licensePlate,
        gi.id AS groupId,
        gi.groupname AS groupName,
        d.driverId,
        d.driverName,
        (CASE WHEN m.hourT IS NOT NULL AND m.hourT &lt;= 24 THEN 1 ELSE 0 END) xinHao,
        a.CarMold as carMold
        FROM (
            select * from LZCL_T_Car
            where groupid is not null and groupid != ''
             <if test="locusDto.licensePlate == null or locusDto.licensePlate == ''">
                 and IsEnable = 1
             </if>
            AND NOT (CarTag = 2 AND LEN(GPSNum) != 12)
            AND IsDelete = 0
        ) a
        LEFT JOIN GroupItem gi ON gi.id = a.groupid
        LEFT JOIN (
            select *,row_number() OVER ( partition BY carId ORDER BY startDate DESC ) AS row from DFDW_DZCC_T_CLGLDetail
            where startDate &lt;= getdate()
        ) d ON d.carId = a.Id and d.groupId = a.groupid and d.row = 1
        left join ( SELECT row_number ( ) OVER ( partition BY CarId ORDER BY arriveTime DESC ) AS row , DATEDIFF(hh, arriveTime , GETDATE() ) hourT ,CarId,arriveTime FROM DFDW_DZCC_T_CARMOVE ) m on m.CarId = a.Id and m.row = 1
        where 1 = 1
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and a.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="locusDto.licensePlate != null and locusDto.licensePlate != ''">
            and a.LicencePlate like concat('%', #{locusDto.licensePlate}, '%')
        </if>
        <if test="locusDto.driverName != null and locusDto.driverName != ''">
            and d.driverName like concat('%', #{locusDto.driverName}, '%')
        </if>
        <if test="locusDto.groupId > 0">
            and gi.id = #{locusDto.groupId}
        </if>
        order by gi.XH, a.LicencePlate
    </select>
    <select id="getNotLocusListPage" resultType="com.soft.gcc.xtbg.dzcc.entity.LocusDto">
        select * from (
        SELECT
        gi.groupname AS groupName,
        car.CarMold AS carMold,
        mc.LicencePlate as licensePlate,
        gi.XH
        FROM
        -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
        (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate AND NOT (car.CarTag = 2 AND LEN(car.GPSNum) != 12)
        -- 关联查询部门
        LEFT JOIN GroupItem gi on gi.id = mc.GroupId
        -- 关联车辆状态表
        LEFT JOIN LZCL_T_Car_Status cs ON cs.LicencePlate = car.LicencePlate AND cs.CreateTime >= #{locusDto.beginDate} AND datediff(day ,cs.CreateTime, #{locusDto.overDate}) >= 0
        -- 关联车辆白名单
        LEFT JOIN LZCL_T_Car_White_List cwl ON cwl.CarId = car.Id
        WHERE
            cs.CreateTime IS NULL
            and car.Id IS NOT NULL
            AND car.CarState = 1
            AND cwl.Id IS NULL
            AND ((mc.StartDate &lt;= #{locusDto.beginDate} AND mc.EndDate >= #{locusDto.overDate}) OR (mc.StartDate &lt;= #{locusDto.overDate} AND mc.StartDate >= #{locusDto.beginDate}) OR (mc.EndDate &lt;= #{locusDto.overDate} AND mc.EndDate >= #{locusDto.beginDate}))
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="locusDto.licensePlate != null and locusDto.licensePlate != ''">
            and mc.LicencePlate like concat('%', #{locusDto.licensePlate}, '%')
        </if>
        <if test="locusDto.groupId > 0">
            and mc.GroupId = #{locusDto.groupId}
        </if>
        GROUP BY gi.groupname, car.CarMold, mc.LicencePlate, gi.XH
        ) a
        order by a.XH, a.licensePlate
    </select>
    <select id="getNotLocusList" resultType="com.soft.gcc.xtbg.dzcc.entity.LocusDto">
        select * from (
        SELECT
        gi.groupname AS groupName,
        car.CarMold AS carMold,
        mc.LicencePlate as licensePlate,
        gi.XH
        FROM
        -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
        (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate AND NOT (car.CarTag = 2 AND LEN(car.GPSNum) != 12)
        -- 关联查询部门
        LEFT JOIN GroupItem gi on gi.id = mc.GroupId
        -- 关联车辆状态表
        LEFT JOIN LZCL_T_Car_Status cs ON cs.LicencePlate = car.LicencePlate AND cs.CreateTime >= #{locusDto.beginDate} AND datediff(day ,cs.CreateTime, #{locusDto.overDate}) >= 0
        -- 关联车辆白名单
        LEFT JOIN LZCL_T_Car_White_List cwl ON cwl.CarId = car.Id
        WHERE
        cs.CreateTime IS NULL
        and car.Id IS NOT NULL
        AND car.CarState = 1
        AND cwl.Id IS NULL
        AND ((mc.StartDate &lt;= #{locusDto.beginDate} AND mc.EndDate >= #{locusDto.overDate}) OR (mc.StartDate &lt;= #{locusDto.overDate} AND mc.StartDate >= #{locusDto.beginDate}) OR (mc.EndDate &lt;= #{locusDto.overDate} AND mc.EndDate >= #{locusDto.beginDate}))
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="locusDto.licensePlate != null and locusDto.licensePlate != ''">
            and mc.LicencePlate like concat('%', #{locusDto.licensePlate}, '%')
        </if>
        <if test="locusDto.groupId > 0">
            and mc.GroupId = #{locusDto.groupId}
        </if>
        GROUP BY gi.groupname, car.CarMold, mc.LicencePlate, gi.XH
        ) a
        order by a.XH, a.licensePlate
    </select>
</mapper>
