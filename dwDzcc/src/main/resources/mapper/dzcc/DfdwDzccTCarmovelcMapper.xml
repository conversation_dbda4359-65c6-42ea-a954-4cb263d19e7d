<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarmovelcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmovelc">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="currDate" column="currDate" jdbcType="DATE"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="approveNote" column="approveNote" jdbcType="VARCHAR"/>
            <result property="approveTime" column="approveTime" jdbcType="TIMESTAMP"/>
            <result property="approveUserId" column="approveUserId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,currDate,
        approveState,approveNote,approveTime,
        approveUserId,createTime
    </sql>
    <select id="getList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmovelc">
        select clc.*,car.CarMold from DFDW_DZCC_T_CARMOVELC clc
        left join LZCL_T_Car car on clc.carId = car.Id
        where car.Id is not null
        <if test="ent.type > 0">
            and clc.type = #{ent.type}
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                clc.topGroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (clc.topGroupId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and clc.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="ent.licensePlate != null and ent.licensePlate != ''">
            and clc.licensePlate like concat('%', concat(#{ent.licensePlate}, '%'))
        </if>
        <if test="ent.driveName != null and ent.driveName != ''">
            and clc.driveName like concat('%', concat(#{ent.driveName}, '%'))
        </if>
        <if test="ent.groupId > -1">
            and clc.topGroupId = #{ent.groupId}
        </if>
        <if test="ent.startMoveTime != null">
            and clc.createTime >= #{ent.startMoveTime}
        </if>
        <if test="ent.endMoveTime != null">
            and clc.createTime &lt; #{ent.endMoveTime}
        </if>
        <if test="ent.approveState > -1">
            and clc.approveState = #{ent.approveState}
        </if>
        order by clc.createTime desc
    </select>
    <select id="getYccllcPage" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmovelc">
        select clc.*,car.CarMold from DFDW_DZCC_T_CARMOVELC clc
        left join LZCL_T_Car car on clc.carId = car.Id
        where car.Id is not null
        <if test="ent.type > 0">
            and clc.type = #{ent.type}
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                clc.topGroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (clc.topGroupId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and clc.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="ent.licensePlate != null and ent.licensePlate != ''">
            and clc.licensePlate like concat('%', concat(#{ent.licensePlate}, '%'))
        </if>
        <if test="ent.driveName != null and ent.driveName != ''">
            and clc.driveName like concat('%', concat(#{ent.driveName}, '%'))
        </if>
        <if test="ent.groupId > -1">
            and clc.topGroupId = #{ent.groupId}
        </if>
        <if test="ent.startMoveTime != null">
            and clc.createTime >= #{ent.startMoveTime}
        </if>
        <if test="ent.endMoveTime != null">
            and clc.createTime &lt; #{ent.endMoveTime}
        </if>
        <if test="ent.approveState > -1">
            and clc.approveState = #{ent.approveState}
        </if>
        order by clc.createTime desc
    </select>
</mapper>
