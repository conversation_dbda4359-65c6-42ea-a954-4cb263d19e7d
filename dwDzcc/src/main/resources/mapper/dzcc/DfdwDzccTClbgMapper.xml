<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTClbgMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="groupId" column="groupId" jdbcType="INTEGER"/>
        <result property="unitGPS" column="unitGPS" jdbcType="VARCHAR"/>
        <result property="unitName" column="unitName" jdbcType="VARCHAR"/>
        <result property="userId" column="userId" jdbcType="INTEGER"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="longitude" column="longitude" jdbcType="DECIMAL"/>
        <result property="latitude" column="latitude" jdbcType="DECIMAL"/>
        <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
                id, groupId, unitGPS,
                unitName, userId, createTime,
                updateTime, longitude, latitude,
                groupName
    </sql>
    <select id="selectFrist" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg">
        select *
        from (select *, row_number() OVER ( partition BY groupId ORDER BY createTime ) AS row
              from DFDW_DZCC_T_CLBG) a
        where a.row = 1
    </select>
    <select id="GetBGDList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg">
        select a.* from (
        select clbg.*, GI.XH from DFDW_DZCC_T_CLBG clbg
        left join GroupItem GI on clbg.groupId = GI.id
        <if test="dzccQx == 2">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{id} and cdzgl.deptId = clbg.groupId and (cdzgl.type = 1 or cdzgl.type = 2)
        </if>
        <if test="dzccQx == 4">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{id} and cdzgl.deptId = clbg.groupId and cdzgl.type = 2
        </if>
        where 1 = 1
        <if test="unitName != ''">
            and clbg.unitName like concat('%', #{unitName}, '%')
        </if>
        <if test="groupId > 0">
            and clbg.groupId = #{groupId}
        </if>
        <if test="dzccQx == 3">
            and clbg.groupId = #{topGroupId}
        </if>
        ) a
        order by a.XH, a.createTime desc
    </select>
    <select id="GetBgdListForBackHaul" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg">
        select a.id,a.unitName from (
        select clbg.*, GI.XH from DFDW_DZCC_T_CLBG clbg
        left join GroupItem GI on clbg.groupId = GI.id
        <if test="dzccQx == 2">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{id} and cdzgl.deptId = clbg.groupId and (cdzgl.type = 1 or cdzgl.type = 2)
        </if>
        <if test="dzccQx == 4">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{id} and cdzgl.deptId = clbg.groupId and cdzgl.type = 2
        </if>
        where 1 = 1
        <if test="dzccQx == 3">
            and clbg.groupId = #{topGroupId}
        </if>
        ) a
        order by a.XH, a.createTime desc
    </select>
</mapper>
