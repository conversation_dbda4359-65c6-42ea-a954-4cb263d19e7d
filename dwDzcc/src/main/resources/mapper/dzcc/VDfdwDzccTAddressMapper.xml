<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTAddressMapper">

<!--    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTAddress">-->
<!--            <result property="primaryId" column="primaryId" jdbcType="INTEGER"/>-->
<!--            <result property="ccOpenTime" column="ccOpenTime" jdbcType="TIMESTAMP"/>-->
<!--            <result property="executeTime" column="executeTime" jdbcType="TIMESTAMP"/>-->
<!--            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>-->
<!--            <result property="driveId" column="driveId" jdbcType="INTEGER"/>-->
<!--            <result property="carId" column="carId" jdbcType="INTEGER"/>-->
<!--            <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>-->
<!--            <result property="applyTopDeptId" column="applyTopDeptId" jdbcType="INTEGER"/>-->
<!--            <result property="applyTopDeptName" column="applyTopDeptName" jdbcType="VARCHAR"/>-->
<!--            <result property="id" column="id" jdbcType="INTEGER"/>-->
<!--            <result property="lcId" column="lcId" jdbcType="INTEGER"/>-->
<!--            <result property="provinces" column="provinces" jdbcType="INTEGER"/>-->
<!--            <result property="city" column="city" jdbcType="INTEGER"/>-->
<!--            <result property="area" column="area" jdbcType="INTEGER"/>-->
<!--            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>-->
<!--            <result property="areaText" column="areaText" jdbcType="VARCHAR"/>-->
<!--            <result property="cityText" column="cityText" jdbcType="VARCHAR"/>-->
<!--            <result property="addType" column="addType" jdbcType="INTEGER"/>-->
<!--            <result property="approveState" column="approveState" jdbcType="INTEGER"/>-->
<!--            <result property="arriveState" column="arriveState" jdbcType="INTEGER"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        primaryId,ccOpenTime,executeTime,-->
<!--        driverName,driveId,carId,-->
<!--        licensePlate,applyTopDeptId,applyTopDeptName,-->
<!--        id,lcId,provinces,-->
<!--        city,area,createTime,-->
<!--        areaText,cityText,addType,-->
<!--        approveState,arriveState-->
<!--    </sql>-->
</mapper>
