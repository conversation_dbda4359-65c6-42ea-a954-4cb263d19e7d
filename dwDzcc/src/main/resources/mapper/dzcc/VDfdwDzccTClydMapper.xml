<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTClydMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="deviceId" column="DeviceId" jdbcType="VARCHAR"/>
            <result property="mileage" column="Mileage" jdbcType="BIGINT"/>
            <result property="runTime" column="RunTime" jdbcType="INTEGER"/>
            <result property="allMileage" column="AllMileage" jdbcType="INTEGER"/>
            <result property="status" column="Status" jdbcType="INTEGER"/>
            <result property="onLineStartTime" column="OnLineStartTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UpdateTime" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="uniqueId" column="UniqueId" jdbcType="VARCHAR"/>
            <result property="startMoveTime" column="StartMoveTime" jdbcType="TIMESTAMP"/>
            <result property="startMoveLongitude" column="StartMoveLongitude" jdbcType="DECIMAL"/>
            <result property="startMoveLatitude" column="StartMoveLatitude" jdbcType="DECIMAL"/>
            <result property="moveMileage" column="MoveMileage" jdbcType="BIGINT"/>
            <result property="endMoveTime" column="EndMoveTime" jdbcType="TIMESTAMP"/>
            <result property="endMoveLongitude" column="EndMoveLongitude" jdbcType="DECIMAL"/>
            <result property="endMoveLatitude" column="EndMoveLatitude" jdbcType="DECIMAL"/>
            <result property="moveTime" column="MoveTime" jdbcType="TIMESTAMP"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>
            <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>
            <result property="driverId" column="driverId" jdbcType="INTEGER"/>
            <result property="diff" column="diff" jdbcType="INTEGER"/>
            <result property="row" column="row" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,DeviceId,Mileage,
        RunTime,AllMileage,Status,
        OnLineStartTime,UpdateTime,CreateTime,
        UniqueId,StartMoveTime,StartMoveLongitude,
        StartMoveLatitude,MoveMileage,EndMoveTime,
        EndMoveLongitude,EndMoveLatitude,MoveTime,
        carId,licensePlate,groupName,
        groupId,driverName,driverId,
        diff,row
    </sql>
    <select id="getClydList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd">
        SELECT
        car.Id as carId,
        car.CarMold,
        car.LicencePlate as LicensePlate,
        mc.GroupId as groupId,
        gi.groupname as groupName,
        cs.StartMoveTime,
        cs.EndMoveTime,
        clgl.driverId,
        clgl.driverName
        FROM
        ( SELECT LicencePlate, GroupId, StartDate, IIF ( EndDate IS NULL, EOMONTH ( StartDate ), EndDate ) EndDate FROM LZCL_T_Car_Move_Child ) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car ON mc.LicencePlate = car.LicencePlate AND NOT (car.CarTag = 2 AND LEN(car.GPSNum) != 12)
        -- 关联查询部门
        LEFT JOIN GroupItem gi ON mc.GroupId = gi.id
        -- 关联查询司机
        LEFT JOIN (
        -- 查询当前时间内设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startTime} AND startDate &lt; #{endTime})
        UNION
        -- 查询之前设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startTime} ) a WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        -- 关联查询车辆状态
        LEFT JOIN (select * from LZCL_T_Car_Status where CreateTime >= #{startTime} AND CreateTime &lt;= #{endTime}) cs ON car.LicencePlate = cs.LicencePlate
        WHERE car.Id is not null and
        (( mc.StartDate >= #{startTime} AND mc.StartDate &lt; #{endTime} ) OR ( mc.EndDate >= #{startTime} AND mc.EndDate &lt; #{endTime} ) OR ( mc.StartDate &lt;= #{startTime} AND mc.EndDate >= DATEADD( DAY, - 1, #{endTime} ) ))
        <if test="clyd.licensePlate != null and clyd.licensePlate != ''">
            and car.licencePlate like concat('%', #{clyd.licensePlate}, '%')
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        GROUP BY car.Id, car.CarMold, car.LicencePlate, mc.GroupId, gi.groupname, cs.StartMoveTime, cs.EndMoveTime, clgl.driverId, clgl.driverName, gi.XH
        order by gi.XH, IIF(clgl.driverName is null, 1, 0), car.LicencePlate, clgl.driverName, cs.StartMoveTime
    </select>
    <select id="getClydListPage" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd">
        select * from (
        SELECT
        car.Id as carId,
        car.CarMold,
        car.LicencePlate as LicensePlate,
        mc.GroupId as groupId,
        gi.groupname as groupName,
        cs.StartMoveTime,
        cs.StartMoveLongitude,
        cs.StartMoveLatitude,
        cs.EndMoveTime,
        cs.EndMoveLongitude,
        cs.EndMoveLatitude,
        clgl.driverId,
        clgl.driverName,
        gi.XH
        FROM
        ( SELECT LicencePlate, GroupId, StartDate, IIF ( EndDate IS NULL, EOMONTH ( StartDate ), EndDate ) EndDate FROM
        LZCL_T_Car_Move_Child ) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car ON mc.LicencePlate = car.LicencePlate AND NOT (car.CarTag = 2 AND LEN(car.GPSNum) != 12)
        -- 关联查询部门
        LEFT JOIN GroupItem gi ON mc.GroupId = gi.id
        -- 关联查询司机
        LEFT JOIN (
        -- 查询当前时间内设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startTime}
        AND startDate &lt; #{endTime})
        UNION
        -- 查询之前设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startTime} ) a WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        -- 关联查询车辆状态
        LEFT JOIN (select * from LZCL_T_Car_Status where CreateTime >= #{startTime} AND CreateTime &lt;= #{endTime}) cs ON car.LicencePlate = cs.LicencePlate
        WHERE car.Id is not null and
        (( mc.StartDate >= #{startTime} AND mc.StartDate &lt; #{endTime} ) OR ( mc.EndDate >= #{startTime} AND
        mc.EndDate &lt; #{endTime} ) OR ( mc.StartDate &lt;= #{startTime} AND mc.EndDate >= DATEADD( DAY, - 1,
        #{endTime} ) ))
        <if test="clyd.licensePlate != null and clyd.licensePlate != ''">
            and car.licencePlate like concat('%', #{clyd.licensePlate}, '%')
        </if>
        <if test="clyd.groupId > -1">
            and gi.id = #{clyd.groupId}
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>
            )
        </if>
        GROUP BY car.Id, car.CarMold, car.LicencePlate, mc.GroupId, gi.groupname, cs.StartMoveLongitude,
        cs.StartMoveLatitude, cs.StartMoveTime, cs.EndMoveTime, cs.EndMoveLongitude, cs.EndMoveLatitude, clgl.driverId,
        clgl.driverName, gi.XH
        ) a
        order by a.XH, IIF(a.driverName is null, 1, 0), a.licensePlate, a.driverName, a.StartMoveTime
    </select>
</mapper>
