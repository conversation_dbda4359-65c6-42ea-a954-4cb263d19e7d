<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTWeekMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="driveName" column="driveName" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="week" column="week" jdbcType="INTEGER"/>
            <result property="startTime" column="startTime" jdbcType="DATE"/>
            <result property="endTime" column="endTime" jdbcType="DATE"/>
            <result property="weekDays" column="weekDays" jdbcType="INTEGER"/>
            <result property="appendDelayHour" column="appendDelayHour" jdbcType="DECIMAL"/>
            <result property="applyUserId" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyUserName" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="applyTime" column="applyTime" jdbcType="TIMESTAMP"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,driveId,driveName,
        year,month,week,
        startTime,endTime,weekDays,
        appendDelayHour,applyUserId,applyUserName,
        approveState,remark,createTime,
        applyTime,lcDefineID,lcName,
        ywID,sendPerson,sendPersonZgh,
        AllPersonZgh,isMany,lcJdmc,
        lcJdid,lcIsback,lcTojdid,
        number,BXType
    </sql>



    <select id="sumOringOvertimeHoursAndappendDelayHour"
            resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek">
        select sum(appendDelayHour) as appendDelayHour ,sum(oringOvertimeHours) as oringOvertimeHours  from V_DFDW_DZCC_T_WEEK
        where year = #{year} and month = #{month} and week = #{week}  and  oringOvertimeHours > 0
    </select>
</mapper>
