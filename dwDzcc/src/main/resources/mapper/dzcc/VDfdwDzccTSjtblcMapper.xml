<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTSjtblcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="driverId" column="driverId" jdbcType="INTEGER"/>
            <result property="date" column="date" jdbcType="DATE"/>
            <result property="mileage" column="mileage" jdbcType="INTEGER"/>
            <result property="imgFileId" column="imgFileId" jdbcType="INTEGER"/>
            <result property="isSubstitute" column="isSubstitute" jdbcType="INTEGER"/>
            <result property="substituteBeginDate" column="substituteBeginDate" jdbcType="DATE"/>
            <result property="substituteEndDate" column="substituteEndDate" jdbcType="DATE"/>
            <result property="createUserId" column="createUserId" jdbcType="INTEGER"/>
            <result property="approvingState" column="approvingState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="licencePlate" column="licencePlate" jdbcType="VARCHAR"/>
            <result property="createUserName" column="createUserName" jdbcType="VARCHAR"/>
            <result property="groupid" column="groupid" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,driverId,date,
        mileage,imgFileId,isSubstitute,
        substituteBeginDate,substituteEndDate,createUserId,
        approvingState,createTime,driverName,
        carId,licencePlate,createUserName,groupid
    </sql>
    <select id="getSjtblcListPage" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc">
        SELECT a.*, c.GroupId as groupid, car.CarMold FROM DFDW_DZCC_T_CLLC a
        LEFT JOIN (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) c
        ON c.LicencePlate = a.licencePlate and c.StartDate &lt;= a.date and c.EndDate >= a.date
        LEFT JOIN LZCL_T_Car car on car.Id = a.carId
        LEFT JOIN GroupItem gi on gi.id = c.GroupId
        where a.isSubstitute = 0 and car.Id is not null
        <if test="groupIds != null">
            AND c.groupid in <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="sjtblc.beginDate != null">
            and a.date >= #{sjtblc.beginDate}
        </if>
        <if test="sjtblc.overDate != null">
            and a.date &lt;= #{sjtblc.overDate}
        </if>
        <if test="sjtblc.licencePlate != null and sjtblc.licencePlate != ''">
            and a.licencePlate like concat('%', #{sjtblc.licencePlate}, '%')
        </if>
        <if test="sjtblc.driverName != null and sjtblc.driverName != ''">
            and a.driverName like concat('%', #{sjtblc.driverName}, '%')
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        order by gi.XH, c.LicencePlate, a.driverId, a.date desc
    </select>
    <select id="getSjtblcList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc">
        SELECT a.*, c.GroupId as groupid, car.CarMold FROM DFDW_DZCC_T_CLLC a
        LEFT JOIN (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) c
            ON c.LicencePlate = a.licencePlate and c.StartDate &lt;= a.date and c.EndDate >= a.date
        LEFT JOIN LZCL_T_Car car on car.Id = a.carId
        LEFT JOIN GroupItem gi on gi.id = c.GroupId
        where a.isSubstitute = 0 and car.Id is not null
        <if test="groupIds != null">
            AND c.groupid in <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="sjtblc.beginDate != null">
            and a.date >= #{sjtblc.beginDate}
        </if>
        <if test="sjtblc.overDate != null">
            and a.date &lt;= #{sjtblc.overDate}
        </if>
        <if test="sjtblc.licencePlate != null and sjtblc.licencePlate != ''">
            and a.licencePlate like concat('%', #{sjtblc.licencePlate}, '%')
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        order by gi.XH, c.LicencePlate, a.driverId, a.date desc
    </select>
</mapper>
