<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTDriveremployMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="personId" column="personId" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="startDate" column="startDate" jdbcType="DATE"/>
            <result property="endDate" column="endDate" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,personId,groupId,
        startDate,endDate
    </sql>
    <select id="GetEmployList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy">
        select de.*, gi.groupname as groupName
        from DFDW_DZCC_T_DRIVEREMPLOY de
        left join GroupItem gi on de.groupId = gi.id
        where de.personId = #{personId}
        order by de.startDate desc
    </select>
    <select id="getListInTime" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy">
        select de.*
        from (select id, personId, groupId, startDate, IIF(endDate IS NULL, '2199-01-01', endDate) endDate FROM DFDW_DZCC_T_DRIVEREMPLOY) de
        WHERE de.personId = #{personId}
        AND (
                (de.startDate >= #{startDate} AND de.endDate &lt;= #{endDate})
                OR (de.startDate &lt;= #{startDate} AND de.endDate >= #{startDate})
                OR (de.startDate &lt;= #{endDate} AND de.endDate >= #{endDate})
            )
        AND id != #{id}
    </select>
</mapper>
