<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCclcdetailMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCclcdetail">
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="moveDate" column="moveDate" jdbcType="DATE"/>
            <result property="startTime" column="startTime" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="endTime" jdbcType="TIMESTAMP"/>
            <result property="overtimeHoursAm" column="overtimeHoursAm" jdbcType="DECIMAL"/>
            <result property="overtimeHoursPm" column="overtimeHoursPm" jdbcType="DECIMAL"/>
            <result property="subsidyState" column="subsidyState" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="dayType" column="dayType" jdbcType="INTEGER"/>
            <result property="bgdId" column="bgdId" jdbcType="INTEGER"/>
            <result property="overtimeHoursAll" column="overtimeHoursAll" jdbcType="DECIMAL"/>
            <result property="overtimeHoursNoon" column="overtimeHoursNoon" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        carId,driveId,moveDate,
        startTime,endTime,overtimeHoursAm,
        overtimeHoursPm,subsidyState,groupId,
        dayType,bgdId,overtimeHoursAll,
        overtimeHoursNoon
    </sql>
    <select id="getCCLCDetailList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCclcdetail">
        select de.*,car.LicencePlate,CONVERT(varchar(100), de.moveDate, 120) as dateTemp from V_DFDW_DZCC_T_CCLCDetail  de
        left join LZCL_T_Car  car on carId = car.Id
        where driveId = #{driveId}  and #{start} &lt;= moveDate  and moveDate &lt;= #{end}
        <if test="type==1">
            and  dayType in (0,2)
        </if>
        <if test="type==2">
            and (dayType = 1 )
        </if>
        <if test="type==3">
            and subsidyState = 1
        </if>
        <if test="type==4">
            and travelAllowance !=0
        </if>
        order by moveDate asc
    </select>

    <select id="getSjqx" resultType="com.soft.gcc.xtbg.dzcc.entity.JtsjDto">
        select p.Id as driverId,p.realName as driverName ,car.LicencePlate ,de.moveDate ,res.ConcatenatedAreaText ,
        ISNULL((select sum(overtimeHoursAll) from V_DFDW_DZCC_T_CCLCDetail where moveDate =de.moveDate and driveId = de.driveId and dayType  =1 ), 0) as overtimeHoursAll,
        (CASE DATEPART(weekday,de.moveDate)
            WHEN 1 THEN '周日' WHEN 2 THEN '周一' WHEN 3 THEN '周二'  WHEN 4 THEN '周三'  WHEN 5 THEN '周四' WHEN 6 THEN '周五' WHEN 7 THEN '周六'
            END
        ) week
        from DFDW_DZCC_T_CCLCDetail de
        left join DFDW_DZCC_T_CCLC lc on de.ywId = lc.Id
        left join LZCL_T_Car car on de.carId = car.Id
        left join Person p on de.driveId = p.Id
        left join (
        SELECT lcId,
        STUFF((SELECT N'、 ' + areaText FROM DFDW_DZCC_T_ADDRESS a2 WHERE a2.arriveState = 1 AND a2.lcId = a1.lcId
        FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'),1,2,'') AS ConcatenatedAreaText
        FROM (SELECT DISTINCT lcId FROM DFDW_DZCC_T_ADDRESS WHERE arriveState = 1) a1
        ) res on lc.Id = res.lcId
        where de.groupId = 373 and p.Id is not null
        and de.moveDate &gt;= #{jtsjDto.startDate} and de.moveDate &lt; #{jtsjDto.endDate}
        <if test="jtsjDto.driverId != null ">
            and de.driveId =  #{jtsjDto.driverId}
        </if>
        <if test="jtsjDto.driverName != null and jtsjDto.driverName != '' ">
            and p.realName like '%'+  #{jtsjDto.driverName} + '%'
        </if>
        <if test="jtsjDto.driverIds != null and jtsjDto.driverIds.size() > 0">
            AND de.driveId in <foreach item="item" index="index" collection="jtsjDto.driverIds" open="(" separator="," close=")">#{item}</foreach>
        </if>
        order by p.realName, car.LicencePlate, de.moveDate
    </select>
    <select id="getSjqdForMonth" resultType="com.soft.gcc.xtbg.dzcc.entity.JtsjDto">
        SELECT * FROM (
            SELECT
            car.Id AS carId,
            clgl.driverId,
            clgl.driverName,
            mc.groupId,
            IIF(clgl.startDate &lt; #{jtsjDto.startDate} OR clgl.startDate IS NULL, #{jtsjDto.startDate}, clgl.startDate) as startDate,
            mc.LicencePlate as licencePlate,
            IIF(min(detail.startDate) is null or min(detail.startDate) >= #{jtsjDto.startDate}, DATEADD(day, -1,#{jtsjDto.endDate}), min(detail.startDate)) AS endDate
            FROM
            -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
            (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
            -- 关联查询车辆
            LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
            -- 关联查询司机
            LEFT JOIN (
            -- 查询当月设置的司机
            SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{jtsjDto.startDate} AND startDate &lt; #{jtsjDto.endDate})
            UNION
            -- 查询往月设置的司机
            SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{jtsjDto.startDate} ) a
            WHERE row = 1
            ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
            -- 关联查询结束时间
            left join DFDW_DZCC_T_CLGLDetail detail on detail.carId = car.Id AND detail.groupId = mc.GroupId and detail.startDate > IIF(clgl.startDate &lt; #{jtsjDto.startDate}, #{jtsjDto.startDate}, clgl.startDate)
            WHERE car.Id IS NOT NULL AND mc.GroupId = 373
            GROUP BY car.Id, clgl.driverId, clgl.driverName, mc.groupId, clgl.startDate, mc.LicencePlate
        ) a
        WHERE ((a.startDate >= #{jtsjDto.startDate} AND a.startDate &lt;= DATEADD(day, -1, #{jtsjDto.endDate})) OR 
               (a.endDate >= #{jtsjDto.startDate} AND a.endDate &lt;= DATEADD(day, -1, #{jtsjDto.endDate})) OR
               (a.startDate &lt;= #{jtsjDto.startDate} AND a.endDate >= DATEADD(day, -1, #{jtsjDto.endDate}))
              )
        and a.driverId is not null
        <if test="jtsjDto.driverId != null ">
            AND a.driverId = #{jtsjDto.driverId}
        </if>
        <if test="jtsjDto.driverIds != null and jtsjDto.driverIds.size() > 0">
            AND a.driverId in
            <foreach item="item" index="index" collection="jtsjDto.driverIds" open="(" separator="," close=")">#{item}</foreach>
        </if>
    </select>
    <select id="GetDriverList" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccVPerson">
        select vP.Id,vp.LoginName,vp.RealName,vp.TopGroupId,p.Sphone
        from vPerson vP
        inner join (
        select rP.* from RolePerson rP
        inner join Role r on r.Id = rP.RoleId and r.RoleName = N'协同办公-电子出车-司机'
        ) rP on rP.PersonId = vP.Id
        left join Person p on vP.Id = p.Id
        left join (
        select driverId from DFDW_DZCC_T_CLGLDetail where groupId = 373 group by driverId
        ) dri on dri.driverId = vP.Id
        where vP.TopGroupId = 373
        <if test="jtsjDto.driverName != null and jtsjDto.driverName != '' ">
            and vP.realName like '%'+  #{jtsjDto.driverName} + '%'
        </if>
        <if test="jtsjDto.driverId != null ">
            and vP.Id =  #{jtsjDto.driverId}
        </if>
        <if test="jtsjDto.driverIds != null and jtsjDto.driverIds.size() > 0">
            AND vP.Id in <foreach item="item" index="index" collection="jtsjDto.driverIds" open="(" separator="," close=")">#{item}</foreach>
        </if>
        and dri.driverId  is not null

    </select>
</mapper>
