<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccTFileMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccTFile">
            <id property="ID" column="ID" jdbcType="INTEGER"/>
            <result property="fileName" column="FileName" jdbcType="VARCHAR"/>
            <result property="filePath" column="FilePath" jdbcType="VARCHAR"/>
            <result property="projectID" column="ProjectID" jdbcType="INTEGER"/>
            <result property="functionID" column="FunctionID" jdbcType="INTEGER"/>
            <result property="type" column="Type" jdbcType="VARCHAR"/>
            <result property="hjID" column="hjID" jdbcType="VARCHAR"/>
            <result property="uploadDate" column="UploadDate" jdbcType="TIMESTAMP"/>
            <result property="isSecret" column="IsSecret" jdbcType="INTEGER"/>
            <result property="personName" column="PersonName" jdbcType="VARCHAR"/>
            <result property="personZgh" column="PersonZgh" jdbcType="VARCHAR"/>
            <result property="subTName" column="SubTName" jdbcType="VARCHAR"/>
            <result property="subTID" column="SubTID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,FileName,FilePath,
        ProjectID,FunctionID,Type,
        hjID,UploadDate,IsSecret,
        PersonName,PersonZgh,SubTName,
        SubTID
    </sql>
</mapper>
