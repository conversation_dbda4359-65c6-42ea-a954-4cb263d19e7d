<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTZbbMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTZbb">
        <result property="YEAR" column="YEAR" jdbcType="INTEGER"/>
        <result property="MONTH" column="MONTH" jdbcType="INTEGER"/>
        <result property="driveId" column="driveId" jdbcType="INTEGER"/>
        <result property="carId" column="carId" jdbcType="INTEGER"/>
        <result property="actualValue" column="actualValue" jdbcType="INTEGER"/>
        <result property="realName" column="RealName" jdbcType="VARCHAR"/>
        <result property="telephone" column="Telephone" jdbcType="VARCHAR"/>
        <result property="topGroupId" column="TopGroupId" jdbcType="INTEGER"/>
        <result property="topGroupName" column="TopGroupName" jdbcType="VARCHAR"/>
        <result property="licencePlate" column="LicencePlate" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
                YEAR, MONTH, driveId,
                carId, actualValue, RealName,
                Telephone, TopGroupId, TopGroupName,
                LicencePlate
    </sql>
    <select id="GetZbbList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTZbb">
        -- 里程报表 6月集团 车 部门 司机 开始时间
        select
            a.CarId,
            a.CarMold,
            a.CarTag,
            a.LicencePlate,
            a.TopGroupId,
            a.TopGroupName,
            a.driveId,
            a.RealName,
            a.actualValue,
            a.YEAR,
            a.MONTH,
            a.startrow,
            a.endrow,
            a.XH,
            a.actualMileage
        from (
            SELECT
                a.CarId,
                a.CarMold,
                a.CarTag,
                a.LicencePlate,
                a.GroupId TopGroupId,
                a.GroupName TopGroupName,
                IIF(a.driverId is null, 0, a.driverId) driveId,
                IIF(a.driverName is null, '--', a.driverName) RealName,
                IIF(a.mileage IS NULL, 0, a.mileage) actualValue,
                YEAR(#{startDate}) YEAR,
                MONTH(#{startDate}) MONTH,
                a.startrow,
                a.endrow,
                a.XH,
                a.actualMileage
            FROM (
                -- 取出初始里程
                SELECT
                    car.Id CarId,
                    car.CarMold,
                    car.CarTag,
                    mc.LicencePlate,
                    mc.GroupId,
                    gi.groupname GroupName,
                    clgl.driverId,
                    clgl.driverName,
                    IIF(cllc.mileage IS NULL, 0, cllc.mileage) mileage,
                    row_number() OVER ( partition BY mc.GroupId, car.Id, clgl.driverId, IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate) ORDER BY abs(datediff(day, IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate), cllc.date)) ) AS startrow,
                    row_number() OVER ( partition BY mc.GroupId, car.Id, clgl.driverId, IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate) ORDER BY abs(datediff(day, IIF(min(detail.startDate) is null or min(detail.startDate) >= #{endDate}, EOMONTH(#{startDate}), min(detail.startDate)), cllc.date)) ) AS endrow,
                    gi.XH,
                    sum(cs.ActualMileage) actualMileage
                FROM
                -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
                (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
                -- 关联查询车辆
                LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
                -- 关联查询部门
                LEFT JOIN GroupItem gi on gi.id = mc.GroupId
                -- 关联查询司机
                LEFT JOIN (
                    -- 查询当月设置的司机
                    SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
                    UNION
                    -- 查询往月设置的司机
                    SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a
                    WHERE row = 1
                ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
                -- 关联查询结束时间
                left join DFDW_DZCC_T_CLGLDetail detail on detail.carId = car.Id AND detail.groupId = mc.GroupId and detail.startDate > IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate)
                -- 查询里程
                left join DFDW_DZCC_T_CLLC cllc ON car.Id = cllc.carId and cllc.isSubstitute = 0 and cllc.date >= DATEADD(month, -1, #{startDate}) and cllc.date &lt;= DATEADD(month, 1, #{endDate})
                -- 查询实际里程
                LEFT JOIN LZCL_T_Car_Status cs ON car.GPSNum = cs.DeviceId AND CONVERT(date, cs.CreateTime) >= mc.StartDate AND CONVERT(date, cs.CreateTime) &lt;= mc.EndDate
                WHERE car.Id IS NOT NULL AND mc.StartDate >= #{startDate} AND mc.EndDate &lt; #{endDate}
                    <if test="groupIds.size() > 0">
                      AND mc.GroupId in <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                GROUP BY car .Id, car.CarMold, car.CarTag, mc.LicencePlate, mc.GroupId, gi.groupname, clgl.driverId, clgl.driverName, clgl.startDate, cllc.mileage, cllc.date, gi.XH
            ) a WHERE a.startrow = 1 or a.endrow = 1
        ) a
        <where>
            <if test="zbb.LicencePlate != null and zbb.LicencePlate != ''">
                AND a.LicencePlate LIKE concat('%', #{zbb.LicencePlate}, '%')
            </if>
            <if test="zbb.RealName != null and zbb.RealName != ''">
                AND a.RealName LIKE concat('%', #{zbb.RealName}, '%')
            </if>
            <if test="!person.qxs.contains(1)">
                and (
                <if test="person.qxs.contains(2)">
                    a.TopGroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
                </if>
                <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                    <if test="person.qxs.contains(2)">or</if>
                    (a.TopGroupId in (
                    <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                    </if>
                    <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                    </if>
                    <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                    </if>
                    ) and a.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
                </if>)
            </if>
            <if test="zbb.carTag != null and zbb.CarTag > -1">
                AND a.CarTag = #{zbb.CarTag}
            </if>
        </where>
        order by a.XH, a.CarId, a.driveId, a.startrow
    </select>
    <select id="GetZbbListForSJ" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTZbb">
        -- 里程报表 6月集团 车 部门 司机 开始时间
        select
            a.CarId,
            a.CarMold,
            a.LicencePlate,
            a.TopGroupId,
            a.TopGroupName,
            a.driveId,
            a.RealName,
            a.actualValue,
            a.YEAR,
            a.MONTH,
            a.startrow,
            a.endrow,
            a.XH
        from (
            SELECT
                a.CarId,
                a.CarMold,
                a.LicencePlate,
                a.GroupId TopGroupId,
                a.GroupName TopGroupName,
                IIF(a.driverId is null, 0, a.driverId) driveId,
                IIF(a.driverName is null, '--', a.driverName) RealName,
                IIF(a.mileage IS NULL, 0, a.mileage) actualValue,
                YEAR(#{startDate}) YEAR,
                MONTH(#{startDate}) MONTH,
                a.startrow,
                a.endrow,
                a.XH
            FROM (
                -- 取出初始里程
                SELECT
                    car.Id CarId,
                    car.CarMold,
                    car.LicencePlate,
                    gi.id as GroupId,
                    gi.groupname GroupName,
                    clgl.driverId,
                    clgl.driverName,
                    IIF(cllc.mileage IS NULL, 0, cllc.mileage) mileage,
                    row_number() OVER ( partition BY gi.id, car.Id, clgl.driverId, IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate) ORDER BY abs(datediff(day, IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate), cllc.date)) ) AS startrow,
                    row_number() OVER ( partition BY gi.id, car.Id, clgl.driverId, IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate) ORDER BY abs(datediff(day, IIF(min(detail.startDate) is null or min(detail.startDate) >= #{endDate}, EOMONTH(#{startDate}), min(detail.startDate)), cllc.date)) ) AS endrow,
                    gi.XH
                FROM
                -- 查询人
                vPerson vp
                -- 关联查询部门
                LEFT JOIN GroupItem gi on gi.id = vp.TopGroupId
                -- 关联车辆管理查询司机
                LEFT JOIN (
                    -- 查询当月设置的司机
                    SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
                    UNION
                    -- 查询往月设置的司机
                    SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a
                    WHERE row = 1
                ) clgl on clgl.driverId = vp.Id AND clgl.groupId = vp.TopGroupId
                -- 关联查询车辆
                LEFT JOIN LZCL_T_Car car on clgl.carId = car.Id
                -- 关联查询结束时间
                left join DFDW_DZCC_T_CLGLDetail detail on detail.carId = car.Id AND detail.groupId = gi.id and detail.startDate > IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate)
                -- 查询里程
                left join DFDW_DZCC_T_CLLC cllc ON car.Id = cllc.carId and cllc.isSubstitute = 0
                WHERE car.Id IS NOT NULL and vp.Id = #{id}
                GROUP BY car.Id, car.CarMold, car.LicencePlate, gi.id, gi.groupname, clgl.driverId, clgl.driverName, clgl.startDate, cllc.mileage, cllc.date, gi.XH
            ) a WHERE a.startrow = 1 or a.endrow = 1
            union
            SELECT
                a.CarId,
                a.CarMold,
                a.LicencePlate,
                a.GroupId TopGroupId,
                a.GroupName TopGroupName,
                a.driverId driveId,
                IIF(a.driverName is null, '--', a.driverName) RealName,
                IIF(a.mileage IS NULL, 0, a.mileage) actualValue,
                YEAR(#{startDate}) YEAR,
                MONTH(#{startDate}) MONTH,
                a.startrow,
                a.endrow,
                a.XH
            FROM (
                -- 取出初始里程
                SELECT
                    car.Id CarId,
                    car.CarMold,
                    car.LicencePlate,
                    gi.id as GroupId,
                    gi.groupname GroupName,
                    vp.Id as driverId,
                    vp.RealName as driverName,
                    IIF(cllc.mileage IS NULL, 0, cllc.mileage) mileage,
                    row_number() OVER ( partition BY gi.id, car.Id, cclc.driveId ORDER BY abs(datediff(day, #{startDate}, cllc.date)) ) AS startrow,
                    row_number() OVER ( partition BY gi.id, car.Id, cclc.driveId ORDER BY abs(datediff(day, #{endDate}, cllc.date)) ) AS endrow,
                    gi.XH
                FROM
                    -- 查询人
                    vPerson vp
                    -- 关联查询部门
                    LEFT JOIN GroupItem gi on gi.id = vp.TopGroupId
                    -- 关联出车单司机
                    LEFT JOIN DFDW_DZCC_T_CCLC cclc on cclc.driveId = vp.Id AND cclc.applyTopDeptId = vp.TopGroupId and cclc.ccOpenTime >= #{startDate} and cclc.ccOpenTime &lt;= #{endDate}
                    -- 排除关联车辆管理查询司机
                    LEFT JOIN (
                        -- 查询当月设置的司机
                        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
                        UNION
                        -- 查询往月设置的司机
                        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a
                        WHERE row = 1
                    ) clgl on clgl.driverId = vp.Id AND clgl.groupId = vp.TopGroupId
                    -- 关联查询车辆
                    LEFT JOIN LZCL_T_Car car on cclc.carId = car.Id
                    -- 查询里程
                    left join DFDW_DZCC_T_CLLC cllc ON car.Id = cllc.carId and cllc.isSubstitute = 0 and cllc.date >= DATEDIFF(month, 1, #{startDate}) and cllc.date &lt;= DATEADD(month, 1, #{endDate})
                WHERE car.Id IS NOT NULL and vp.Id = #{id} and clgl.driverId is null
                GROUP BY car.Id, car.CarMold, car.LicencePlate, gi.id, gi.groupname, vp.Id, vp.RealName, cllc.mileage, cllc.date, gi.XH
            ) a WHERE a.startrow = 1 or a.endrow = 1
        ) a
    </select>
</mapper>
