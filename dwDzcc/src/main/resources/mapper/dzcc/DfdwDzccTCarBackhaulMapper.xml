<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarBackhaulMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarBackhaul">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="bgdId" column="bgdId" jdbcType="INTEGER"/>
            <result property="backStartTime" column="backStartTime" jdbcType="VARCHAR"/>
            <result property="backEndTime" column="backEndTime" jdbcType="VARCHAR"/>
            <result property="continueMinute" column="continueMinute" jdbcType="INTEGER"/>
            <result property="radius" column="radius" jdbcType="VARCHAR"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="workStartTime" column="workStartTime" jdbcType="VARCHAR"/>
            <result property="workEndTime" column="workEndTime" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,bgdId,
        backStartTime,backEndTime,continueMinute,
        radius,groupId,workStartTime,
        workEndTime
    </sql>
    <select id="GetBackHaulList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarBackhaul">
        select cb.*,car.LicencePlate,clbg.unitName,gi.groupName from DFDW_DZCC_T_CAR_BACKHAUL cb
        left join groupItem gi on cb.groupId = gi.id
        left join DFDW_DZCC_T_CLBG clbg on cb.bgdId = clbg.id
        left join LZCL_T_Car car on cb.carId = car.id
        where 1=1
        <if test="groupId > 0">
            and cb.groupId = #{groupId}
        </if>
        <if test="unitName != null and unitName != ''">
            and clbg.unitName like concat('%', #{unitName}, '%')
        </if>
        <if test="licencePlate != null and licencePlate != ''">
            and car.LicencePlate like concat('%', #{licencePlate}, '%')
        </if>
        <if test="type == 0">
            and cb.groupId is not null
        </if>
        <if test="type == 1">
            and cb.carId is not null
        </if>
        order by cb.id asc
    </select>

</mapper>
