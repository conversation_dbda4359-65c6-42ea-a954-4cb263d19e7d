<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccCarMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccCar">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="carType" column="CarType" jdbcType="INTEGER"/>
            <result property="licencePlate" column="LicencePlate" jdbcType="VARCHAR"/>
            <result property="brand" column="Brand" jdbcType="VARCHAR"/>
            <result property="model" column="Model" jdbcType="VARCHAR"/>
            <result property="licenseCheckDate" column="LicenseCheckDate" jdbcType="TIMESTAMP"/>
            <result property="carDeptName" column="CarDeptName" jdbcType="VARCHAR"/>
            <result property="GPSNum" column="GPSNum" jdbcType="VARCHAR"/>
            <result property="driverId" column="DriverId" jdbcType="INTEGER"/>
            <result property="borrowDate" column="BorrowDate" jdbcType="DATE"/>
            <result property="isEnable" column="IsEnable" jdbcType="INTEGER"/>
            <result property="createDate" column="CreateDate" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="IsDelete" jdbcType="INTEGER"/>
            <result property="isConfirm" column="IsConfirm" jdbcType="INTEGER"/>
            <result property="confirmUserId" column="ConfirmUserId" jdbcType="INTEGER"/>
            <result property="confirmTime" column="ConfirmTime" jdbcType="DATE"/>
            <result property="carTag" column="CarTag" jdbcType="INTEGER"/>
            <result property="GPSEnableDate" column="GPSEnableDate" jdbcType="DATE"/>
            <result property="businessRisksEndDate" column="BusinessRisksEndDate" jdbcType="DATE"/>
            <result property="groupid" column="groupid" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,CarType,LicencePlate,
        Brand,Model,LicenseCheckDate,
        CarDeptName,GPSNum,DriverId,
        BorrowDate,IsEnable,CreateDate,
        IsDelete,IsConfirm,ConfirmUserId,
        ConfirmTime,CarTag,GPSEnableDate,
        InsuranceEndDate,BusinessRisksEndDate,groupid
    </sql>
    <select id="SelectCarPerson" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccCar">
        select car.* from LZCL_T_Car car
        <if test="dzccQx != 1">
            inner join DFDW_DZCC_T_CDZGL on deptId = car.groupid and cdzId = #{id}
        </if>
        <if test="dzccQx == 4">
            inner join DFDW_DZCC_T_CARPERSON on personId = #{id} and carId = #{carId}
        </if>
        where car.id = #{carId}
    </select>
    <select id="GetClsyListForMonth" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccCar">
        select a.CarMold,a.year,a.month,sum ( a.num ) AS number  from (
            SELECT
            case when c.CarMold IS NULL OR c.CarMold = '' then N'其它' else c.CarMold end CarMold,
            datepart(yyyy,l.StartDate) as year,
            datepart(mm ,l.StartDate) as month,
            round(((datediff(day, l.StartDate,l.EndDate) + 1) / (day(cast(EOMONTH(l.StartDate) as datetime)) + 0.0)),0) num
            FROM
            LZCL_T_Car c
            LEFT JOIN LZCL_T_Car_More m ON c.LicencePlate = m.LicencePlate
            LEFT JOIN (SELECT YwId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) l ON l.YwId = m.Id
            where
            (l.StartDate >= #{beginDate}) and (l.StartDate &lt; #{overDate})
            AND (l.EndDate &lt; #{overDate})
            AND c.CarTag != 2
            <if test="carMold != null and carMold != ''">
                AND c.carMold like concat('%', #{carMold}, '%')
            </if>
            <if test="groupIds.size() > 0">
                AND m.groupId in <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">#{item}</foreach>
            </if>
            <if test="!person.qxs.contains(1)">
                and (
                <if test="person.qxs.contains(2)">
                    m.groupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
                </if>
                <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                    <if test="person.qxs.contains(2)">or</if>
                    (m.groupId in (
                    <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                    </if>
                    <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                    </if>
                    <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                    </if>
                    ) and c.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
                </if>)
            </if>
        ) a
        group by a.CarMold,a.year,a.month
    </select>
    <select id="getClxxList" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccCar">
        SELECT
            a.*,
            gi.id as groupid,
            gi.groupname,
            d.driverId,
            d.driverName,
            p.RealName as confirmUserName
        FROM LZCL_T_Car a
        LEFT JOIN (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
            ON mc.LicencePlate = a.LicencePlate and datediff(day , mc.StartDate, getdate()) >= 0 and datediff(day , mc.EndDate, getdate()) &lt;= 0
        LEFT JOIN GroupItem gi on mc.GroupId = gi.id
        LEFT JOIN (select *, row_number() OVER ( partition BY carId ORDER BY startDate DESC ) AS row from DFDW_DZCC_T_CLGLDetail where startDate &lt;= getdate()
         ) d on d.carId = a.Id and mc.GroupId = d.groupId and d.row = 1
        LEFT JOIN Person p ON p.Id = a.ConfirmUserId
        WHERE a.IsEnable = 1 and a.IsDelete != 1
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                mc.GroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (mc.GroupId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and a.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="car.carType != null and car.carType != ''">
            AND a.CarType = #{car.carType}
        </if>
        <if test="car.brand != null and car.brand != ''">
            AND a.Brand LIKE concat('%', #{car.brand}, '%')
        </if>
        <if test="car.licencePlate != null and car.licencePlate != ''">
            AND a.LicencePlate LIKE concat('%', #{car.licencePlate}, '%')
        </if>
        <if test="car.carDeptName != null and car.carDeptName != ''">
            AND a.CarDeptName LIKE concat('%', #{car.carDeptName}, '%')
        </if>
        <if test="car.GPSNum != null and car.GPSNum != ''">
            AND a.GPSNum LIKE concat('%', #{car.GPSNum}, '%')
        </if>
        <if test="car.isConfirm > -1">
            AND a.IsConfirm = #{car.isConfirm}
        </if>
        <if test="car.groupid > -1">
            AND mc.GroupId = #{car.groupid}
        </if>
        <if test="car.CarMold != null and car.CarMold != ''">
            AND a.CarMold LIKE concat('%', #{car.CarMold}, '%')
        </if>
        <if test="car.carTag != null and car.carTag > -1">
            AND a.CarTag = #{car.carTag}
        </if>
        Order by gi.XH,a.LicencePlate
    </select>
</mapper>
