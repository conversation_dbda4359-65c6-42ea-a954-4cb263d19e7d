<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTUnitlogMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTUnitlog">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="unitName" column="unitName" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="DECIMAL"/>
            <result property="latitude" column="latitude" jdbcType="DECIMAL"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="useDateTime" column="useDateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,groupId,
        unitName,longitude,latitude,
        type,useDateTime
    </sql>
    <insert id="addUnit">
        INSERT INTO DFDW_DZCC_T_UnitLog ( carId, groupId, unitName, longitude, latitude, type, useDateTime ) SELECT
        clgl.carId,
        clgl.groupId,
        clbg.unitName,
        clbg.longitude,
        clbg.latitude,
        '0' AS type,
        GETDATE( ) AS useDateTime
        FROM
        DFDW_DZCC_T_CLGL clgl
        LEFT JOIN DFDW_DZCC_T_CLBG clbg ON clgl.unitId = clbg.id
        WHERE
        clgl.id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </insert>
    <insert id="addUnitForGroup">
        INSERT INTO DFDW_DZCC_T_UnitLog (carId, groupId, unitName, longitude, latitude, type, useDateTime)
        SELECT clgl.carId,
               clgl.groupId,
               clbg.unitName,
               clbg.longitude,
               clbg.latitude,
               '0'       AS type,
               GETDATE() AS useDateTime
        FROM DFDW_DZCC_T_CLGL clgl
                 LEFT JOIN DFDW_DZCC_T_CLBG clbg ON clgl.unitId = clbg.id
        WHERE clgl.groupId = #{id}
    </insert>
    <insert id="addStop">
        INSERT INTO DFDW_DZCC_T_UnitLog ( carId, groupId, unitName, longitude, latitude, type, useDateTime ) SELECT
        clgl.carId,
        clgl.groupId,
        clgl.stopName AS unitName,
        clgl.longitude,
        clgl.latitude,
        '1' AS type,
        GETDATE( ) AS useDateTime
        FROM
        DFDW_DZCC_T_CLGL clgl
        WHERE
        clgl.id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </insert>
</mapper>
