<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccOverTimeMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTOverTime">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="deptId" column="deptId" jdbcType="INTEGER"/>
            <result property="startTimeType" column="startTimeType" jdbcType="INTEGER"/>
            <result property="isComputeNoon" column="IsComputeNoon" jdbcType="INTEGER"/>
            <result property="travelAllowanceType" column="travelAllowanceType" jdbcType="INTEGER"/>
            <result property="overtimeHoursAllType" column="overtimeHoursAllType" jdbcType="INTEGER"/>
            <result property="overtimeHoursMonthType" column="overtimeHoursMonthtype" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,deptId,startTimeType,IsComputeNoon
        travelAllowanceType,overtimeHoursAllType,overtimeHoursMonthtype,
    </sql>

    <select id="selectOverTimePage" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTOverTime">
        select
            t1.Id,t1.deptId,t1.startTimeType,t1.IsComputeNoon,t1.travelAllowanceType,
            t1.overtimeHoursAllType,t1.overtimeHoursMonthtype,t2.groupname,t1.sxrDelayHourMonthType,t1.jjrDelayHourMonthType
        from DFDW_DZCC_T_OVERTIME_SET t1
        left join GroupItem t2 on t1.deptId = t2.id
        <where>
            1=1
            <if test="overTime.deptId > 0 ">
                and t1.deptId = #{overTime.deptId}
            </if>
        </where>
        order by t1.id DESC
    </select>

</mapper>
