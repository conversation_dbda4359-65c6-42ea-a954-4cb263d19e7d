<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCbjlMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCbjl">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="deviceId" column="DeviceId" jdbcType="VARCHAR"/>
            <result property="type" column="Type" jdbcType="INTEGER"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="startTime" column="StartTime" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="EndTime" jdbcType="TIMESTAMP"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>
            <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,DeviceId,Type,
        CreateTime,StartTime,EndTime,
        carId,licensePlate,groupName,
        groupId
    </sql>
</mapper>
