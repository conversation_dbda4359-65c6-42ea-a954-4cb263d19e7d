<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTYcclydMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTYcclyd">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="deviceId" column="DeviceId" jdbcType="VARCHAR"/>
            <result property="mileage" column="Mileage" jdbcType="BIGINT"/>
            <result property="runTime" column="RunTime" jdbcType="INTEGER"/>
            <result property="allMileage" column="AllMileage" jdbcType="INTEGER"/>
            <result property="status" column="Status" jdbcType="INTEGER"/>
            <result property="onLineStartTime" column="OnLineStartTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UpdateTime" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="uniqueId" column="UniqueId" jdbcType="VARCHAR"/>
            <result property="startMoveTime" column="StartMoveTime" jdbcType="TIMESTAMP"/>
            <result property="startMoveLongitude" column="StartMoveLongitude" jdbcType="DECIMAL"/>
            <result property="startMoveLatitude" column="StartMoveLatitude" jdbcType="DECIMAL"/>
            <result property="moveMileage" column="MoveMileage" jdbcType="BIGINT"/>
            <result property="endMoveTime" column="EndMoveTime" jdbcType="TIMESTAMP"/>
            <result property="endMoveLongitude" column="EndMoveLongitude" jdbcType="DECIMAL"/>
            <result property="endMoveLatitude" column="EndMoveLatitude" jdbcType="DECIMAL"/>
            <result property="moveTime" column="MoveTime" jdbcType="TIMESTAMP"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>
            <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,DeviceId,Mileage,
        RunTime,AllMileage,Status,
        OnLineStartTime,UpdateTime,CreateTime,
        UniqueId,StartMoveTime,StartMoveLongitude,
        StartMoveLatitude,MoveMileage,EndMoveTime,
        EndMoveLongitude,EndMoveLatitude,MoveTime,
        carId,licensePlate,groupName,
        groupId
    </sql>
</mapper>
