<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccVGroupItemMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccVGroupItem">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="groupname" column="groupname" jdbcType="VARCHAR"/>
            <result property="parentid" column="parentid" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="shortpinyin" column="shortpinyin" jdbcType="VARCHAR"/>
            <result property="dydj" column="dydj" jdbcType="VARCHAR"/>
            <result property="XH" column="XH" jdbcType="INTEGER"/>
            <result property="isShow" column="IsShow" jdbcType="INTEGER"/>
            <result property="category" column="Category" jdbcType="INTEGER"/>
            <result property="UComapanyQC" column="UComapanyQC" jdbcType="VARCHAR"/>
            <result property="groupDesc" column="GroupDesc" jdbcType="VARCHAR"/>
            <result property="parentName" column="ParentName" jdbcType="VARCHAR"/>
            <result property="topGroupId" column="TopGroupId" jdbcType="INTEGER"/>
            <result property="plevel" column="plevel" jdbcType="INTEGER"/>
            <result property="topGroupName" column="TopGroupName" jdbcType="VARCHAR"/>
            <result property="parentPath" column="ParentPath" jdbcType="VARCHAR"/>
            <result property="parentTPath" column="ParentTPath" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,groupname,parentid,
        type,shortpinyin,dydj,
        XH,IsShow,Category,
        UComapanyQC,GroupDesc,ParentName,
        TopGroupId,plevel,TopGroupName,
        ParentPath,ParentTPath
    </sql>
</mapper>
