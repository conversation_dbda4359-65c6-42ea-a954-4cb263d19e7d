<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTClglMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="carId" column="carId" jdbcType="INTEGER"/>
        <result property="groupId" column="groupId" jdbcType="INTEGER"/>
        <result property="unitId" column="unitId" jdbcType="INTEGER"/>
        <result property="stopName" column="stopName" jdbcType="VARCHAR"/>
        <result property="stopGPS" column="stopGPS" jdbcType="VARCHAR"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="editUserId" column="editUserId" jdbcType="INTEGER"/>
        <result property="longitude" column="longitude" jdbcType="DECIMAL"/>
        <result property="latitude" column="latitude" jdbcType="DECIMAL"/>
        <result property="unitName" column="unitName" jdbcType="VARCHAR"/>
        <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
        <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
                id, carId, groupId,
                unitId, stopName, stopGPS,
                createTime, updateTime, editUserId,
                longitude, latitude, unitName,
                groupName, licensePlate
    </sql>
    <insert id="saveBatch1">
        insert into DFDW_DZCC_T_CLGL (carId, groupId, unitId, stopName, stopGPS, editUserId, longitude, latitude,
        unitName, groupName, licensePlate)
        values
        <foreach collection="clglList" item="item" index="index" separator=",">
            (#{item.carId}, #{item.groupId},#{item.unitId}, #{item.stopName},#{item.stopGPS},
            #{item.editUserId},#{item.longitude}, #{item.latitude},#{item.unitName}, #{item.groupName},
            #{item.licensePlate})
        </foreach>
    </insert>
    <select id="selectCarRent" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl">
        select * from (
            SELECT
                a.Id AS carId,
                a.LicencePlate AS licensePlate,
                gi.id AS groupId,
                gi.groupname AS groupName,
                b.unitName,
                g.id,
                g.stopName,
                g.stopGPS,
                g.unitId,
                g.longitude,
                g.latitude,
                d.driverId,
                d.driverName,
                ROW_NUMBER() OVER(Order by gi.XH,a.LicencePlate ) AS RowId
            FROM LZCL_T_Car a
                LEFT JOIN LZCL_T_Car_More m ON a.LicencePlate = m.LicencePlate
                LEFT JOIN (SELECT YwId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) l ON l.YwId = m.Id
                LEFT JOIN GroupItem gi ON gi.id = m.groupid
                LEFT JOIN DFDW_DZCC_T_CLGL g ON g.carId = a.Id AND g.groupId = gi.id
                LEFT JOIN DFDW_DZCC_T_CLBG b ON b.id = g.unitId
                LEFT JOIN (select *, row_number() OVER ( partition BY carId, groupId ORDER BY startDate DESC ) AS row
                    from DFDW_DZCC_T_CLGLDetail
                    where startDate &lt;= getdate()
                ) d on g.id = d.cid and d.row = 1
                WHERE (l.StartDate &lt;= getdate())
                AND (l.EndDate >= convert(varchar(10),getdate(),120)) AND a.IsEnable = 1 and a.IsDelete != 1
                <if test="!person.qxs.contains(1)">
                    and (
                    <if test="person.qxs.contains(2)">
                        gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
                    </if>
                    <if test="person.qxs.contains(4)">
                        <if test="person.qxs.contains(2)">or</if>
                        (gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2)
                        and a.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
                    </if>)
                </if>
                <if test="clgl.groupId > 0">
                    AND gi.id = #{clgl.groupId}
                </if>
                <if test="clgl.licensePlate != null and clgl.licensePlate != ''">
                    AND a.LicencePlate LIKE concat('%',#{clgl.licensePlate},'%')
                </if>
                <if test="clgl.driverName != null and clgl.driverName != ''">
                    AND d.driverName LIKE concat('%',#{clgl.driverName},'%')
                </if>
                group by a.Id,a.LicencePlate,gi.id,gi.groupname,b.unitName,g.id,g.stopName,g.stopGPS,g.unitId,g.longitude,g.latitude,d.driverId,d.driverName,gi.XH
        ) as b
        where RowId between #{startNum} and #{endNum}
    </select>
    <select id="countCarRent" resultType="java.lang.Long">
        select count(1) from (
        SELECT
        a.Id AS carId,
        a.LicencePlate AS licensePlate,
        gi.id AS groupId,
        gi.groupname AS groupName,
        b.unitName,
        g.id,
        g.stopName,
        g.stopGPS,
        g.unitId,
        g.longitude,
        g.latitude,
        d.driverId,
        d.driverName,
        ROW_NUMBER() OVER(Order by gi.XH,a.LicencePlate ) AS RowId
        FROM LZCL_T_Car a
        LEFT JOIN LZCL_T_Car_More m ON a.LicencePlate = m.LicencePlate
        LEFT JOIN (SELECT YwId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) l ON l.YwId = m.Id
        LEFT JOIN GroupItem gi ON gi.id = m.groupid
        LEFT JOIN DFDW_DZCC_T_CLGL g ON g.carId = a.Id AND g.groupId = gi.id
        LEFT JOIN DFDW_DZCC_T_CLBG b ON b.id = g.unitId
        LEFT JOIN (select *, row_number() OVER ( partition BY carId, groupId ORDER BY startDate DESC ) AS row
        from DFDW_DZCC_T_CLGLDetail
        where startDate &lt;= getdate()
        ) d on g.id = d.cid and d.row = 1
        WHERE (l.StartDate &lt;= getdate())
        AND (l.EndDate >= convert(varchar(10),getdate(),120)) AND a.IsEnable = 1 and a.IsDelete != 1
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2)
                and a.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="clgl.groupId > 0">
            AND gi.id = #{clgl.groupId}
        </if>
        <if test="clgl.licensePlate != null and clgl.licensePlate != ''">
            AND a.LicencePlate LIKE concat('%',#{clgl.licensePlate},'%')
        </if>
        <if test="clgl.driverName != null and clgl.driverName != ''">
            AND d.driverName LIKE concat('%',#{clgl.driverName},'%')
        </if>
        group by a.Id,a.LicencePlate,gi.id,gi.groupname,b.unitName,g.id,g.stopName,g.stopGPS,g.unitId,g.longitude,g.latitude,d.driverId,d.driverName,gi.XH
        ) as b
    </select>
    <select id="selectCarRentNew" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl">
        SELECT
            a.Id AS carId,
            a.LicencePlate AS licensePlate,
            gi.id AS groupId,
            gi.groupname AS groupName
        FROM LZCL_T_Car a
        LEFT JOIN LZCL_T_Car_More m ON a.LicencePlate = m.LicencePlate
        LEFT JOIN (SELECT Id, YwId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) l ON l.YwId = m.Id
        LEFT JOIN GroupItem gi ON gi.id = m.groupid
        LEFT JOIN DFDW_DZCC_T_CLGL g ON g.carId = a.Id AND g.groupId = gi.id
        WHERE (l.StartDate &lt;= getdate())
        AND (l.EndDate >= convert(varchar(10),getdate(),120)) AND a.IsEnable = 1 and a.IsDelete != 1
        and g.id is null
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2)
                and a.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        group by a.Id,a.LicencePlate,gi.id,gi.groupname,gi.XH
        order by gi.XH,a.LicencePlate
    </select>
    <select id="getRepeatCar" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl">
        SELECT
            a.Id,
            a.LicencePlate
        FROM LZCL_T_Car a
        LEFT JOIN LZCL_T_Car_More m ON a.LicencePlate = m.LicencePlate
        LEFT JOIN ( SELECT YwId, StartDate, IIF ( EndDate IS NULL, EOMONTH ( StartDate ), EndDate ) EndDate FROM LZCL_T_Car_Move_Child ) l ON l.YwId = m.Id
        LEFT JOIN GroupItem gi ON gi.id = m.groupid
        LEFT JOIN DFDW_DZCC_T_CLGL g ON g.carId = a.Id AND g.groupId = gi.id
        LEFT JOIN DFDW_DZCC_T_CLBG b ON b.id = g.unitId
        LEFT JOIN (
            SELECT
                *,
                row_number ( ) OVER ( partition BY carId, groupId ORDER BY startDate DESC ) AS row
            FROM DFDW_DZCC_T_CLGLDetail
            WHERE startDate &lt;= getdate( )
        ) d ON g.id = d.cid AND d.row = 1
        WHERE ( l.StartDate &lt;= getdate( ) )
          AND ( l.EndDate >= CONVERT ( VARCHAR ( 10 ), getdate( ), 120 ) )
          AND a.IsEnable = 1
          AND a.IsDelete != 1
        GROUP BY a.Id, a.LicencePlate
        HAVING COUNT(a.Id) > 1
    </select>
</mapper>
