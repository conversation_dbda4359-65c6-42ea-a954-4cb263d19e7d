<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccPersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccPerson">
        <id property="id" column="Id" jdbcType="INTEGER"/>
        <result property="loginName" column="LoginName" jdbcType="VARCHAR"/>
        <result property="realName" column="RealName" jdbcType="VARCHAR"/>
        <result property="password" column="Password" jdbcType="VARCHAR"/>
        <result property="groupID" column="GroupID" jdbcType="INTEGER"/>
        <result property="roleId" column="RoleId" jdbcType="INTEGER"/>
        <result property="telephone" column="Telephone" jdbcType="VARCHAR"/>
        <result property="msgType" column="MsgType" jdbcType="VARCHAR"/>
        <result property="OA" column="OA" jdbcType="VARCHAR"/>
        <result property="WZ_UserID" column="WZ_UserID" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="sphone" column="Sphone" jdbcType="VARCHAR"/>
        <result property="ZJ_CS" column="ZJ_CS" jdbcType="INTEGER"/>
        <result property="phName" column="PhName" jdbcType="INTEGER"/>
        <result property="certificateID" column="CertificateID" jdbcType="VARCHAR"/>
        <result property="officePhone" column="OfficePhone" jdbcType="VARCHAR"/>
        <result property="BFLoginName" column="BFLoginName" jdbcType="VARCHAR"/>
        <result property="p_XH" column="P_XH" jdbcType="INTEGER"/>
        <result property="loginName2" column="LoginName2" jdbcType="VARCHAR"/>
        <result property="oldID" column="oldID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
                Id, LoginName, RealName,
                Password, GroupID, RoleId,
                Telephone, MsgType, OA,
                WZ_UserID, type, Sphone,
                ZJ_CS, PhName, CertificateID,
                OfficePhone, BFLoginName, P_XH,
                LoginName2, oldID
    </sql>
    <insert id="insertSjRole">
        insert RolePerson (roleid, personid)
        SELECT r.Id, #{id} from Role r
            left join RolePerson rp on rp.RoleId = r.Id and rp.PersonId = #{id}
        where ( RoleName = N'外协' or RoleName = N'协同办公-电子出车-司机' ) and rp.Id is null
    </insert>
    <delete id="deleteSjRole">
        delete from RolePerson where PersonId = #{id} and RoleId = (select id from Role where RoleName = N'协同办公-电子出车-司机')
    </delete>
    <select id="GetDriverList" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccPerson">
        SELECT
            p.*
        FROM
            vPerson p,
            RolePerson rp,
            Role r
        WHERE
            p.Id = rp.PersonId and
            rp.RoleId = r.Id AND
            r.RoleName = N'协同办公-电子出车-司机' AND
            p.TopGroupId = #{groupId}
        order by p.RealName
    </select>
    <select id="GetPersonRoleById" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccRole">
        select r.*
        from Role r
                 right join RolePerson p on p.RoleId = r.Id and p.PersonId = #{id}
        where r.RoleName like N'%协同办公-电子出车%'
    </select>
    <select id="GetPersonByID" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccVPerson">
        select p.Id, p.RealName, p.CertificateID, p.Telephone, vP.TopGroupId from Person p
        left join vPerson vP on p.GroupID = vP.GroupId
        where p.CertificateID = #{certificateID}
    </select>
</mapper>
