<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTWeekMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="driveid" column="driveId" jdbcType="INTEGER"/>
            <result property="drivename" column="driveName" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="week" column="week" jdbcType="INTEGER"/>
            <result property="oringOvertimeHours" column="oringOvertimeHours" jdbcType="DECIMAL"/>
            <result property="starttime" column="startTime" jdbcType="DATE"/>
            <result property="endtime" column="endTime" jdbcType="DATE"/>
            <result property="weekdays" column="weekDays" jdbcType="INTEGER"/>
            <result property="appenddelayhour" column="appendDelayHour" jdbcType="DECIMAL"/>
            <result property="applyuserid" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyusername" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="approvestate" column="approveState" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createtime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="applytime" column="applyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,driveId,driveName,
        year,month,week,
        startTime,endTime,weekDays,
        appendDelayHour,applyUserId,applyUserName,
        approveState,remark,createTime,
        applyTime,oringOvertimeHours
    </sql>

    <sql id="BaseSelect">
        SELECT t.*
        FROM V_DFDW_DZCC_T_WEEK t
        <where>
            <if test="query.year != null">
                AND t.year = #{query.year}
            </if>
            <if test="query.month != null">
                AND t.month = #{query.month}
            </if>
            <if test="query.week != null">
                AND t.week = #{query.week}
            </if>
            <if test="query.weekMainId != null">
                 AND t.weekMainId = #{query.weekMainId}
            </if>
            <if test="query.driveName != null and query.driveName != ''">
                AND t.driveName LIKE CONCAT('%', #{query.driveName}, '%')
            </if>
            <if test="query.approveState != null and query.approveState != -1 ">
                AND t.approveState = #{query.approveState}
            </if>
        </where>
        ORDER BY t.year DESC
    </sql>
    <select id="listPage" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek">
     <include refid="BaseSelect"></include>
    </select>
    <select id="getList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek">
        <include refid="BaseSelect"></include>
    </select>

    <select id="hasRole" resultType="java.lang.Integer">
        select count(1) from  RolePerson rp LEFT JOIN Role r on r.Id = rp.RoleId where r.RoleName = #{roleName}
          and rp.PersonId = #{personId}
    </select>
    <select id="getDriverList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek">
        select *, IIF(approveState = 2, oringOvertimeHours + appendDelayHour, oringOvertimeHours) as overtimeHoursAll from V_DFDW_DZCC_T_WEEK_CAR
        <where>
            <if test="query.driveName != null and query.driveName != ''">
                AND driveName LIKE CONCAT('%', #{query.driveName}, '%')
            </if>
            <if test="query.year != null">
                AND year = #{query.year}
            </if>
            <if test="query.month != null">
                AND month = #{query.month}
            </if>
            <if test="query.weekList.size() > 0">
                AND week IN
                <foreach collection="query.weekList" item="week" separator="," open="(" close=")">
                    #{week}
                </foreach>
            </if>
        </where>
        ORDER BY driveName, week
    </select>


    <select id="getCurrentMonthDriverId" resultType="java.lang.Integer">
        select driveId from DFDW_DZCC_T_WEEK
          where year = #{year} and month = #{month}


    </select>
    <select id="getWeeks" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain">
        select
            wm.*,
            sum(w.appendDelayHour) as sumAppendDelayHour
        from
            DFDW_DZCC_T_WEEK_MAIN wm
        left join DFDW_DZCC_T_WEEK w on w.weekMainId = wm.id
        where wm.year = #{year} and wm.month = #{month}
        group by wm.id, wm.year, wm.month, wm.week, wm.startTime, wm.endTime, wm.weekDays, wm.createTime, wm.applyUserId, wm.applyUserName, wm.approveState, wm.applyTime
    </select>

</mapper>
