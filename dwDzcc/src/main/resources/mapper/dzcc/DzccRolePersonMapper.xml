<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccRolePersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccRolePerson">
        <result property="id" column="Id" jdbcType="INTEGER"/>
        <result property="roleId" column="RoleId" jdbcType="INTEGER"/>
        <result property="personId" column="PersonId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id, RoleId, PersonId
    </sql>
    <insert id="saveBatch1">
        insert into <PERSON><PERSON><PERSON> (RoleId, PersonId)
        values
        <foreach collection="roleList" item="item" index="index" separator=",">
            (#{item.RoleId}, #{item.PersonId})
        </foreach>
    </insert>
</mapper>
