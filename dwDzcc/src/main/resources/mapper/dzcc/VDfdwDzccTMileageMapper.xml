<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTMileageMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTMileage">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="mileage" column="mileage" jdbcType="INTEGER"/>
            <result property="currDate" column="currDate" jdbcType="DATE"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="licencePlate" column="LicencePlate" jdbcType="VARCHAR"/>
            <result property="realName" column="RealName" jdbcType="VARCHAR"/>
            <result property="groupname" column="groupname" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,driveId,
        mileage,currDate,createTime,
        groupId,LicencePlate,RealName,
        groupname
    </sql>
</mapper>
