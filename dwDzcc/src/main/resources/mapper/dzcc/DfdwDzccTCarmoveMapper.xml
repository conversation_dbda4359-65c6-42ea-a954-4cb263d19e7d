<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarmoveMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmove">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="provinces" column="provinces" jdbcType="INTEGER"/>
            <result property="city" column="city" jdbcType="INTEGER"/>
            <result property="area" column="area" jdbcType="INTEGER"/>
            <result property="arriveTime" column="arriveTime" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="curDate" column="curDate" jdbcType="DATE"/>
            <result property="adCode" column="adCode" jdbcType="VARCHAR"/>
            <result property="provincesName" column="provincesName" jdbcType="VARCHAR"/>
            <result property="cityName" column="cityName" jdbcType="VARCHAR"/>
            <result property="areaName" column="areaName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,driveId,
        provinces,city,area,
        arriveTime,createTime,curDate,
        adCode,provincesName,cityName,
        areaName
    </sql>
</mapper>
