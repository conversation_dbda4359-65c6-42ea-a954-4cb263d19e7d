<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="groupname" column="groupname" jdbcType="VARCHAR"/>
        <result property="parentid" column="parentid" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="shortpinyin" column="shortpinyin" jdbcType="VARCHAR"/>
        <result property="dydj" column="dydj" jdbcType="VARCHAR"/>
        <result property="uComapanyBH" column="uComapanyBH" jdbcType="VARCHAR"/>
        <result property="uComapanyJC" column="uComapanyJC" jdbcType="VARCHAR"/>
        <result property="XH" column="XH" jdbcType="INTEGER"/>
        <result property="isShow" column="IsShow" jdbcType="INTEGER"/>
        <result property="category" column="Category" jdbcType="INTEGER"/>
        <result property="parentidRz" column="parentidRz" jdbcType="INTEGER"/>
        <result property="uComapanyQC" column="uComapanyQC" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
                id, groupname, parentid,
                type, shortpinyin, dydj,
                uComapanyBH, uComapanyJC, XH,
                IsShow, Category, parentidRz,
                uComapanyQC
    </sql>
    <select id="GetGroupListByRole" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem">
        select g.id, g.groupname
        from GroupItem g
        <if test="!isAll and (person.dzccQx == 2 or person.dzccQx == 4)">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{person.id} and cdzgl.deptId = g.id and (
            cdzgl.type = 0
            <if test="person.qxs.contains(2)">
                or cdzgl.type = 1
            </if>
            <if test="person.qxs.contains(4)">
                or cdzgl.type = 2
            </if>
            <if test="person.qxs.contains(6)">
                or cdzgl.type = 3
            </if>
            )
        </if>
        where g.Category = 1
        <if test="person.dzccQx == 3">
            and g.id = #{person.topGroupId}
        </if>
        and g.parentid = #{parentId}
        group by g.id, g.groupname, g.XH
        order by g.XH
    </select>
    <select id="getCDZGroup" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem">
        select g.id,g.groupname
        from GroupItem g
        <if test="isShowAll == 0 and dzccQx == 1">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{personId} and cdzgl.deptId = g.id
        </if>
        <if test="isShowAll == 0 and dzccQx == 2">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{id} and cdzgl.deptId = g.id and cdzgl.type = 1
        </if>
        where g.Category = 1
        group by g.id,g.groupname, g.XH
        order by g.XH
    </select>
    <select id="GetGroupListByRole2" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem">
        select g.id, g.groupname
        from GroupItem g
        <if test="!isAll and (isZGL == 2)">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{id} and cdzgl.deptId = g.id and cdzgl.type = 1
        </if>
        where g.Category = 1
        and g.parentid = #{parentId}
        <if test="isZGL == 3">
            and g.id = #{topGroupId}
        </if>
        group by g.id, g.groupname, g.XH
        order by g.XH
    </select>
    <select id="GetGroupListByRole12" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem">
        select g.id, g.groupname
        from GroupItem g
        <if test="!isAll and (person.dzccQx == 2 or person.dzccQx == 4)">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{person.id} and cdzgl.deptId = g.id and (
            cdzgl.type = 0
            <if test="person.qxs.contains(2)">
                or cdzgl.type = 1
            </if>
            <if test="person.qxs.contains(4)">
                or cdzgl.type = 2
            </if>
            )
        </if>
        where g.Category = 1
        and g.parentid = #{parentId}
        <if test="person.dzccQx == 3">
            and g.id = #{person.topGroupId}
        </if>
        group by g.id, g.groupname, g.XH
        order by g.XH
    </select>
</mapper>
