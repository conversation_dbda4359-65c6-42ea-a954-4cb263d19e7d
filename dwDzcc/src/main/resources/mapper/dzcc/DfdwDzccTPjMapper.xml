<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTPjMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="lcId" column="lcId" jdbcType="INTEGER"/>
            <result property="rate" column="rate" jdbcType="INTEGER"/>
            <result property="zjState" column="zjState" jdbcType="INTEGER"/>
            <result property="aqjsState" column="aqjsState" jdbcType="INTEGER"/>
            <result property="pdkzState" column="pdkzState" jdbcType="INTEGER"/>
            <result property="tsState" column="tsState" jdbcType="INTEGER"/>
            <result property="tsText" column="tsText" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,lcId,rate,
        zjState,aqjsState,pdkzState,
        tsState,tsText,createTime
    </sql>
    <select id="getPage" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj">
        select * from (
            select
                pj.*,
                cclc.applyNo,
                cclc.ccOpenTime,
                car.LicencePlate,
                sj.realName as driveName,
                cclc.ycrName,
                cclc.applyTopDeptId,
                cclc.applyTopDeptName,
                cclc.carId,
                sj.realName,
                ROW_NUMBER() OVER(partition by cclc.applyNo order by pj.id) AS RowId
            from DFDW_DZCC_T_PJ pj
            left join DFDW_DZCC_T_CCLC cclc on  pj.lcId = cclc.id
            left join LZCL_T_Car car on car.Id = cclc.carId
            left join Person sj on sj.id = cclc.driveId
            where cclc.id is not null
        ) a
        <where>
            a.RowId = 1
            <if test="!person.qxs.contains(1)">
                and (
                <if test="person.qxs.contains(2)">
                    a.applyTopDeptId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
                </if>
                <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                    <if test="person.qxs.contains(2)">or</if>
                    (a.applyTopDeptId in (
                    <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                    </if>
                    <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                    </if>
                    <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                    </if>
                    ) and a.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
                </if>
                )
            </if>
            <if test="dfdwDzccTPj.beginDate != null">
                and a.ccOpenTime >= #{dfdwDzccTPj.beginDate} and a.ccOpenTime &lt; #{dfdwDzccTPj.overDate}
            </if>
            <if test="dfdwDzccTPj.licencePlate != null and dfdwDzccTPj.licencePlate != ''">
                and a.LicencePlate like concat('%', #{dfdwDzccTPj.licencePlate}, '%')
            </if>
            <if test="dfdwDzccTPj.driveName != null and dfdwDzccTPj.driveName != ''">
                and a.realName like concat('%', #{dfdwDzccTPj.driveName}, '%')
            </if>
            <if test="dfdwDzccTPj.ycrName != null and dfdwDzccTPj.ycrName != ''">
                and a.ycrName like concat('%', #{dfdwDzccTPj.ycrName}, '%')
            </if>
            <if test="dfdwDzccTPj.applyTopGroupId > -1">
                and a.applyTopDeptId = #{dfdwDzccTPj.applyTopGroupId}
            </if>
            <if test="dfdwDzccTPj.hasTs">
                and (
                    (a.createTime >= '2025-01-23 00:00:00' and (a.rate != 5 or a.zjState = 0 or a.aqjsState = 0 or a.pdkzState = 0 or a.tsState = 0))
                 or (a.createTime &lt; '2025-01-23 00:00:00' and ((a.rate != 3 and a.rate != 5) or a.zjState = 0 or a.aqjsState = 0 or a.pdkzState = 0 or a.tsState = 1))
                )
            </if>
        </where>
        order by a.applyNo desc
    </select>
    <select id="getList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj">
        select * from (
        select
        pj.*,
        cclc.applyNo,
        cclc.ccOpenTime,
        car.LicencePlate,
        sj.realName as driveName,
        cclc.ycrName,
        cclc.applyTopDeptId,
        cclc.applyTopDeptName,
        cclc.carId,
        sj.realName,
        ROW_NUMBER() OVER(partition by cclc.applyNo order by pj.id) AS RowId
        from DFDW_DZCC_T_PJ pj
        left join DFDW_DZCC_T_CCLC cclc on  pj.lcId = cclc.id
        left join LZCL_T_Car car on car.Id = cclc.carId
        left join Person sj on sj.id = cclc.driveId
        where cclc.id is not null
        ) a
        <where>
            a.RowId = 1
            <if test="!person.qxs.contains(1)">
                and (
                <if test="person.qxs.contains(2)">
                    a.applyTopDeptId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
                </if>
                <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                    <if test="person.qxs.contains(2)">or</if>
                    (a.applyTopDeptId in (
                    <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                    </if>
                    <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                    </if>
                    <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                        select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                    </if>
                    ) and a.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
                </if>
                )
            </if>
            <if test="dfdwDzccTPj.beginDate != null">
                and a.ccOpenTime >= #{dfdwDzccTPj.beginDate} and a.ccOpenTime &lt; #{dfdwDzccTPj.overDate}
            </if>
            <if test="dfdwDzccTPj.licencePlate != null and dfdwDzccTPj.licencePlate != ''">
                and a.LicencePlate like concat('%', #{dfdwDzccTPj.licencePlate}, '%')
            </if>
            <if test="dfdwDzccTPj.driveName != null and dfdwDzccTPj.driveName != ''">
                and a.realName like concat('%', #{dfdwDzccTPj.driveName}, '%')
            </if>
            <if test="dfdwDzccTPj.ycrName != null and dfdwDzccTPj.ycrName != ''">
                and a.ycrName like concat('%', #{dfdwDzccTPj.ycrName}, '%')
            </if>
            <if test="dfdwDzccTPj.applyTopGroupId > -1">
                and a.applyTopDeptId = #{dfdwDzccTPj.applyTopGroupId}
            </if>
            <if test="dfdwDzccTPj.hasTs">
                and (
                (a.createTime >= '2025-01-23 00:00:00' and (a.rate != 5 or a.zjState = 0 or a.aqjsState = 0 or a.pdkzState = 0 or a.tsState = 0))
                or (a.createTime &lt; '2025-01-23 00:00:00' and ((a.rate != 3 and a.rate != 5) or a.zjState = 0 or a.aqjsState = 0 or a.pdkzState = 0 or a.tsState = 1))
                )
            </if>
        </where>
        order by a.applyNo desc
    </select>
</mapper>
