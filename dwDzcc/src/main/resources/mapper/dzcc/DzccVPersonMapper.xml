<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccVPersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccVPerson">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="loginName" column="LoginName" jdbcType="VARCHAR"/>
            <result property="realName" column="RealName" jdbcType="VARCHAR"/>
            <result property="password" column="Password" jdbcType="VARCHAR"/>
            <result property="roleId" column="RoleId" jdbcType="INTEGER"/>
            <result property="telephone" column="Telephone" jdbcType="VARCHAR"/>
            <result property="msgType" column="MsgType" jdbcType="VARCHAR"/>
            <result property="OA" column="OA" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="p_XH" column="P_XH" jdbcType="INTEGER"/>
            <result property="groupId" column="GroupId" jdbcType="INTEGER"/>
            <result property="parentId" column="ParentId" jdbcType="INTEGER"/>
            <result property="parentName" column="ParentName" jdbcType="VARCHAR"/>
            <result property="groupType" column="GroupType" jdbcType="INTEGER"/>
            <result property="groupName" column="GroupName" jdbcType="VARCHAR"/>
            <result property="groupDesc" column="GroupDesc" jdbcType="VARCHAR"/>
            <result property="topGroupId" column="TopGroupId" jdbcType="INTEGER"/>
            <result property="topGroupName" column="TopGroupName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,LoginName,RealName,
        Password,RoleId,Telephone,
        MsgType,OA,type,
        P_XH,GroupId,ParentId,
        ParentName,GroupType,GroupName,
        GroupDesc,TopGroupId,TopGroupName
    </sql>
    <select id="GetDriverList" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccVPerson">
        select vP.*,p.Sphone
        from vPerson vP
        inner join (
        select rP.* from RolePerson rP
        inner join Role r on r.Id = rP.RoleId and r.RoleName = N'协同办公-电子出车-司机'
        ) rP on rP.PersonId = vP.Id
        left join Person p on vP.Id = p.Id
        where 1 = 1
        <if test="topGroupId > 0">
            and vP.TopGroupId = #{topGroupId,jdbcType=INTEGER}
        </if>
        <if test="realName != null and realName != ''">
            AND vP.RealName LIKE concat('%', #{realName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="isZGL == 2 || isZGL == 4">
            AND vP.TopGroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{personId,jdbcType=INTEGER})
        </if>
        <if test="isZGL == 3">
            AND vP.TopGroupId = #{groupId,jdbcType=INTEGER}
        </if>
        order by vP.TopGroupId, vP.realName
    </select>
    <select id="GetCDZListPage" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccVPerson">
        select vP.*, #{personTypeId} as personType from vPerson vP
        <if test="personTypeId == 1">
        inner join (
            select rP.* from RolePerson rP
            inner join Role r on r.Id = rP.RoleId and r.RoleName = N'协同办公-电子出车-车辆管理人员'
        ) rP on rP.PersonId = vP.Id
        </if>
        <if test="personTypeId == 2">
        inner join (
            select rP.* from RolePerson rP
            inner join Role r on r.Id = rP.RoleId and r.RoleName = N'协同办公-电子出车-车辆调度人员'
        ) rP on rP.PersonId = vP.Id
        </if>
        <if test="personTypeId == 3">
        inner join (
            select rP.* from RolePerson rP
            inner join Role r on r.Id = rP.RoleId and r.RoleName = N'协同办公-电子出车-车辆运监人员'
        ) rP on rP.PersonId = vP.Id
        </if>
        <if test="manageGroupId > 0">
            inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.deptId = #{manageGroupId} and cdzgl.cdzId = vP.Id
        </if>
        left join GroupItem gi on gi.Id = vP.TopGroupId
        where 1 = 1
        <if test="name != null and name != ''">
            and vP.RealName like concat(concat('%', #{name}), '%')
        </if>
        <if test="deptId > 0">
            and vP.TopGroupId = #{deptId}
        </if>
        order by gi.XH, vP.RealName
    </select>
    <select id="getList" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccVPerson">
        select vp.* from vPerson vp
        left join GroupItem gi on vp.TopGroupId = gi.id
        where vp.type = 1
        <if test="groupId > 0">
            and vp.TopGroupId = #{groupId}
        </if>
        <if test="realName != null || realName != ''">
            and vp.RealName like concat('%', #{realName}, '%')
        </if>
        <if test="groupIds != null">
            AND vp.TopGroupId in<foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">#{item}</foreach>
        </if>
        order by gi.XH, vp.realName
    </select>
</mapper>
