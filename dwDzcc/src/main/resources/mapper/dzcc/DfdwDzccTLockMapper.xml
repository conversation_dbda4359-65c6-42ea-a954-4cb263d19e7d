<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTLockMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTLock">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="date" column="date" jdbcType="DATE"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="editId" column="editId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,groupId,year,
        month,date,createTime,
        updateTime,editId
    </sql>
</mapper>
