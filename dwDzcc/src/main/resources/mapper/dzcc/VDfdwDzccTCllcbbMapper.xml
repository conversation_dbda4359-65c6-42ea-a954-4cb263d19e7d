<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCllcbbMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCllcbb">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="driverId" column="driverId" jdbcType="INTEGER"/>
            <result property="date" column="date" jdbcType="DATE"/>
            <result property="mileage" column="mileage" jdbcType="INTEGER"/>
            <result property="imgFileId" column="imgFileId" jdbcType="INTEGER"/>
            <result property="isSubstitute" column="isSubstitute" jdbcType="INTEGER"/>
            <result property="substituteBeginDate" column="substituteBeginDate" jdbcType="DATE"/>
            <result property="substituteEndDate" column="substituteEndDate" jdbcType="DATE"/>
            <result property="createUserId" column="createUserId" jdbcType="INTEGER"/>
            <result property="approvingState" column="approvingState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="licencePlate" column="licencePlate" jdbcType="VARCHAR"/>
            <result property="createUserName" column="createUserName" jdbcType="VARCHAR"/>
            <result property="topGroupId" column="TopGroupId" jdbcType="INTEGER"/>
            <result property="topGroupName" column="TopGroupName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,driverId,date,
        mileage,imgFileId,isSubstitute,
        substituteBeginDate,substituteEndDate,createUserId,
        approvingState,createTime,driverName,
        carId,licencePlate,createUserName,
        TopGroupId,TopGroupName
    </sql>
</mapper>
