<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarstatusdailyMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarstatusdaily">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="personId" column="personId" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="curDate" column="curDate" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,personId,
        groupId,status,createTime,
        curDate
    </sql>
    <insert id="insertBatch">
        INSERT INTO DFDW_DZCC_T_CarStatusDaily (carId, personId, groupId, status, curDate)
        VALUES
        <foreach collection="users" index="" item="user" separator=",">
            (#{user.carId},
            #{user.personId},
            #{user.groupId},
            #{user.status},
            #{user.curDate})
        </foreach>
    </insert>
    <select id="getList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarstatusdaily">
        select
            car.Id as carId,
            clgl.driverId as personId,
            mc.GroupId as groupId
        from LZCL_T_Car_Move_Child mc
                 -- 关联查询车
                 left join LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
            -- 关联查询司机
                 LEFT JOIN (
            -- 查询设置的司机
            SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{date} ) a
            WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        where mc.StartDate &lt;= #{date} and (mc.EndDate is null or (mc.EndDate is not null and mc.EndDate >= #{date}))
    </select>
</mapper>
