<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCclcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclc">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="applyUserId" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyUserName" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="applyPhone" column="applyPhone" jdbcType="VARCHAR"/>
            <result property="applyDeptId" column="applyDeptId" jdbcType="INTEGER"/>
            <result property="applyDeptName" column="applyDeptName" jdbcType="VARCHAR"/>
            <result property="applyNum" column="applyNum" jdbcType="INTEGER"/>
            <result property="ccType" column="ccType" jdbcType="INTEGER"/>
            <result property="ccOpenTime" column="ccOpenTime" jdbcType="TIMESTAMP"/>
            <result property="note" column="note" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="executeState" column="executeState" jdbcType="INTEGER"/>
            <result property="executeTime" column="executeTime" jdbcType="TIMESTAMP"/>
            <result property="mainId" column="mainId" jdbcType="INTEGER"/>
            <result property="ccDays" column="ccDays" jdbcType="INTEGER"/>
            <result property="addressInfo" column="addressInfo" jdbcType="VARCHAR"/>
            <result property="scdText" column="scdText" jdbcType="VARCHAR"/>
            <result property="applyTopDeptId" column="applyTopDeptId" jdbcType="INTEGER"/>
            <result property="applyTopDeptName" column="applyTopDeptName" jdbcType="VARCHAR"/>
            <result property="ccEndTime" column="ccEndTime" jdbcType="TIMESTAMP"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="cdzPersonId" column="cdzPersonId" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="ycrId" column="ycrId" jdbcType="INTEGER"/>
            <result property="ycrName" column="ycrName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,applyNo,applyUserId,
        applyUserName,applyPhone,applyDeptId,
        applyDeptName,applyNum,ccType,
        ccOpenTime,note,createTime,
        approveState,executeState,executeTime,
        mainId,ccDays,addressInfo,
        scdText,applyTopDeptId,applyTopDeptName,
        ccEndTime,carId,driveId,
        cdzPersonId,remark,ycrId,
        ycrName
    </sql>

    <select id="getCcdByDriveIdAndTime" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclc">
        select lc.* ,car.LicencePlate,p.realName as driverName,p.Telephone as driverPhone,
               (case when vc.startTime is null then lc.ccOpenTime else vc.startTime end) as actualStartTime ,
               (case when vc.endTime is null then lc.executeTime else vc.endTime end) as actualEndTime,
               sd.status
        from DFDW_DZCC_T_CCLC lc
        left join V_DFDW_DZCC_T_CCDTIME vc on lc.Id = vc.ywId
        left join LZCL_T_Car car on lc.carId = car.Id
        left join Person p on lc.driveId = p.Id
        left join DFDW_DZCC_T_CCLC_STATUS_DAILY sd on lc.id = sd.ywId and sd.moveDate = #{date}
        where  lc.driveId = #{driveId} and lc.approveState = 2 AND lc.executeState = 2 and lc.carId !=0
        AND CONVERT(varchar(100), ccOpenTime , 23)  &lt;=  #{date} and #{date}  &lt;=  CONVERT(varchar(100), lc.executeTime , 23)
    </select>
    <select id="getListCcdXq" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclc">
        select cclc.*, car.LicencePlate, car.CarMold, p.RealName as driverName, p.Telephone as driverPhone from DFDW_DZCC_T_CCLC cclc
        left join LZCL_T_Car car on  cclc.carId = car.Id
        left join Person p on  cclc.driveId = p.Id
        where cclc.id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>
</mapper>
