<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCcdMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="applyUserId" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyUserName" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="applyPhone" column="applyPhone" jdbcType="VARCHAR"/>
            <result property="applyDeptId" column="applyDeptId" jdbcType="INTEGER"/>
            <result property="applyDeptName" column="applyDeptName" jdbcType="VARCHAR"/>
            <result property="applyNum" column="applyNum" jdbcType="INTEGER"/>
            <result property="ccType" column="ccType" jdbcType="INTEGER"/>
            <result property="ccOpenTime" column="ccOpenTime" jdbcType="TIMESTAMP"/>
            <result property="note" column="note" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="executeState" column="executeState" jdbcType="INTEGER"/>
            <result property="executeTime" column="executeTime" jdbcType="TIMESTAMP"/>
            <result property="mainId" column="mainId" jdbcType="INTEGER"/>
            <result property="ccDays" column="ccDays" jdbcType="INTEGER"/>
            <result property="addressInfo" column="addressInfo" jdbcType="VARCHAR"/>
            <result property="scdText" column="scdText" jdbcType="VARCHAR"/>
            <result property="applyTopDeptId" column="applyTopDeptId" jdbcType="INTEGER"/>
            <result property="applyTopDeptName" column="applyTopDeptName" jdbcType="VARCHAR"/>
            <result property="ccEndTime" column="ccEndTime" jdbcType="TIMESTAMP"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="cdzPersonId" column="cdzPersonId" jdbcType="INTEGER"/>
            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>
            <result property="driverPhone" column="driverPhone" jdbcType="VARCHAR"/>
            <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="ycrId" column="ycrId" jdbcType="INTEGER"/>
            <result property="ycrName" column="ycrName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,applyNo,applyUserId,
        applyUserName,applyPhone,applyDeptId,
        applyDeptName,applyNum,ccType,
        ccOpenTime,note,createTime,
        approveState,executeState,executeTime,
        mainId,ccDays,addressInfo,
        scdText,applyTopDeptId,applyTopDeptName,
        ccEndTime,carId,driveId,
        cdzPersonId,driverName,driverPhone,
        licensePlate,remark,ycrId,
        ycrName
    </sql>
    <select id="getCcdBBList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd">
        -- 查询行程报表
        SELECT * FROM (
            SELECT a.carId, a.CarMold, a.CarTag, a.licensePlate, a.applyTopDeptId, a.applyTopDeptName, a.driveId, a.driverName, COUNT(ccd.applyNo) ValidDays FROM (
                SELECT
                    car.Id carId,
                    car.CarMold,
                    car.CarTag,
                    mc.LicencePlate licensePlate,
                    mc.GroupId applyTopDeptId,
                    gi.groupname applyTopDeptName,
                    IIF(clgl.driverId IS NULL, 0, clgl.driverId) driveId,
                    IIF(clgl.driverName IS NULL, '--', clgl.driverName) driverName,
                    IIF(clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate) startDate,
                    IIF((min(detail.startDate) is null or min(detail.startDate) >= #{endDate}) and clgl.startDate IS NOT NULL, #{endDate}, DATEADD(DAY, 1, min(detail.startDate))) as endDate
                FROM
                -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
                ( SELECT LicencePlate, GroupId, StartDate, IIF ( EndDate IS NULL, EOMONTH ( StartDate ), EndDate ) EndDate FROM LZCL_T_Car_Move_Child ) mc
                -- 关联查询车辆
                LEFT JOIN LZCL_T_Car car ON mc.LicencePlate = car.LicencePlate and car.IsEnable = 1
                -- 关联查询部门
                LEFT JOIN GroupItem gi ON gi.id = mc.GroupId
                -- 关联查询司机
                LEFT JOIN (
                    -- 查询当月设置的司机
                    SELECT carId, driverId, driverName, groupId, startDate FROM DFDW_DZCC_T_CLGLDetail WHERE ( startDate >= #{startDate} AND startDate &lt; #{endDate} )
                    UNION
                    -- 查询往月设置的司机
                    SELECT carId, driverId, driverName, groupId, startDate FROM ( SELECT *, row_number ( ) OVER ( PARTITION BY carId, groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a WHERE row = 1
                ) clgl ON clgl.carId = car.Id AND clgl.groupId = mc.GroupId
                -- 关联查询结束时间
                LEFT JOIN DFDW_DZCC_T_CLGLDetail detail ON detail.carId = car.Id AND detail.groupId = mc.GroupId AND detail.startDate > IIF ( clgl.startDate &lt; #{startDate}, #{startDate}, clgl.startDate )
                WHERE ( ( mc.EndDate >= #{endDate} AND mc.StartDate &lt;= #{startDate} )  OR ( mc.StartDate >= #{startDate} AND mc.StartDate &lt; #{endDate} )  OR ( mc.EndDate >= #{startDate} AND mc.EndDate &lt; #{endDate} ) )
                and car.Id IS NOT NULL
                GROUP BY car.Id, car.CarMold, car.CarTag, mc.LicencePlate, mc.GroupId, gi.groupname, clgl.driverId, clgl.driverName, clgl.startDate
            ) a
            -- 关联出车单表
            LEFT JOIN DFDW_DZCC_T_CCLC ccd ON ccd.carId = a.carId AND ccd.driveId = a.driveId AND ccd.ApplyTopDeptId = a.applyTopDeptId AND ccd.mainId = 0 AND ccd.approveState = 2 AND ccd.executeState = 2 AND ( ( ccd.executeTime >= a.endDate AND ccd.ccOpenTime &lt;= a.startDate ) OR ( ccd.ccOpenTime >= a.startDate AND ccd.ccOpenTime &lt; a.endDate ) OR ( ccd.executeTime >= a.startDate AND ccd.executeTime &lt; a.endDate ) )
            GROUP BY a.carId, a.CarMold, a.CarTag, a.licensePlate, a.applyTopDeptId, a.applyTopDeptName, a.driveId, a.driverName
            <if test="ccd.isQtcl">
                UNION
                SELECT
                ccd.carId carId,
                '' CarMold,
                N'其他车辆' CarTag,
                N'其他车辆' licensePlate,
                ccd.applyTopDeptId,
                ccd.applyTopDeptName,
                -2 driveId,
                N'其他人员' driverName,
                count(ccd.applyNo) ValidDays
                FROM DFDW_DZCC_T_CCLC ccd
                WHERE ccd.mainId = 0 AND ccd.approveState = 2 AND ccd.executeState = 2 AND ( ( ccd.executeTime >= #{endDate} AND ccd.ccOpenTime &lt;= #{startDate} ) OR ( ccd.ccOpenTime >= #{startDate} AND ccd.ccOpenTime &lt; #{endDate} ) OR ( ccd.executeTime >= #{startDate} AND ccd.executeTime &lt; #{endDate} ) ) AND ccd.carId = -2
                GROUP BY ccd.carId, ccd.applyTopDeptId, ccd.applyTopDeptName
            </if>
        ) a
        left join GroupItem gi ON gi.id = a.applyTopDeptId
        where 1 = 1
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                a.applyTopDeptId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (a.applyTopDeptId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and a.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="ccd.licensePlate != null and ccd.licensePlate != ''">
            and a.licensePlate like concat('%', #{ccd.licensePlate}, '%')
        </if>
        <if test="ccd.driverName != null and ccd.driverName != ''">
            and a.driverName like concat('%', #{ccd.driverName}, '%')
        </if>
        <if test="ccd.applyTopDeptId > 0">
            and a.applyTopDeptId = #{ccd.applyTopDeptId}
        </if>
        <if test="ccd.carTag != null and ccd.carTag > -1">
            and a.CarTag = #{ccd.carTag}
        </if>
        <if test="ccd.carOrPerson == 0">
            ORDER BY gi.XH, case a.driveId WHEN -2 THEN 1 WHEN 0 THEN 2 ELSE 0 END, a.licensePlate
        </if>
        <if test="ccd.carOrPerson == 1">
            ORDER BY gi.XH, case a.driveId WHEN -2 THEN 1 WHEN 0 THEN 2 ELSE 0 END, a.driverName
        </if>
    </select>
    <select id="GetCcdListPage" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd">
        select * ,(case when vt.startTime is null then ccd.ccOpenTime else vt.startTime end) as actualStartTime
        ,(case when vt.endTime is null then ccd.executeTime else vt.endTime end) as actualEndTime from (
            SELECT
                ccd.*,
                IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他人员', p.RealName) driverName,
                p.Telephone as driverPhone,
                IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他车辆', car.LicencePlate) licensePlate,
                car.CarMold as carMold,
                car.CarTag as carTag
            FROM DFDW_DZCC_T_CCLC ccd
            LEFT JOIN vPerson p on p.Id = ccd.driveId
            LEFT JOIN LZCL_T_Car car on car.Id = ccd.CarId
        ) ccd
        left join V_DFDW_DZCC_T_CCDTIME vt on ccd.Id = vt.YwId
        WHERE ccd.mainId = 0 and ccd.applyTopDeptId != 0
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                ccd.applyTopDeptId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (ccd.applyTopDeptId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and ccd.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="ccd.applyTopDeptId > 0">
            and ccd.applyTopDeptId = #{ccd.applyTopDeptId}
        </if>
        <if test="ccd.driverName != null and ccd.driverName != ''">
            AND ccd.driverName LIKE concat('%',#{ccd.driverName},'%')
        </if>
        <if test="ccd.licensePlate != null and ccd.licensePlate != '' and ccd.licensePlate != '-2'">
            AND ccd.licensePlate LIKE concat('%',#{ccd.licensePlate},'%')
        </if>
        <if test="ccd.licensePlate == '-2'">
            AND ccd.carId = -2
        </if>
        <if test="ccd.approveState > -1">
            and ccd.approveState = #{ccd.approveState}
        </if>
        <if test="ccd.executeState > -1">
            and ccd.executeState = #{ccd.executeState}
        </if>
        <if test="ccd.ccOpenTime != null and ccd.executeTime != null">
            and (
                (ccd.executeTime >= #{ccd.executeTime} and ccd.ccOpenTime &lt;= #{ccd.ccOpenTime})
                or (ccd.ccOpenTime >= #{ccd.ccOpenTime} and ccd.ccOpenTime &lt;= #{ccd.executeTime})
                or (ccd.executeTime >= #{ccd.ccOpenTime} and ccd.executeTime &lt;= #{ccd.executeTime})
            )
        </if>
        <if test="ccd.applyUserName != null and ccd.applyUserName != ''">
            AND ccd.applyUserName LIKE concat('%',#{ccd.applyUserName},'%')
        </if>
        <if test="ccd.ycrName != null and ccd.ycrName != ''">
            AND ccd.ycrName LIKE concat('%',#{ccd.ycrName},'%')
        </if>
        <if test="ccd.carTag != null and ccd.carTag > -1">
            AND ccd.carTag = #{ccd.carTag}
        </if>
        order by ccd.ccOpenTime desc, ccd.executeTime desc
    </select>
    <select id="getCcdList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd">
        select * ,(case when vt.startTime is null then ccd.ccOpenTime else vt.startTime end) as actualStartTime
        ,(case when vt.endTime is null then ccd.executeTime else vt.endTime end) as actualEndTime from (
            SELECT
            ccd.*,
            IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他人员', p.RealName) driverName,
            p.Telephone as driverPhone,
            IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他车辆', car.LicencePlate) licensePlate,
            car.CarMold as carMold,
            car.CarTag as carTag
            FROM DFDW_DZCC_T_CCLC ccd
            LEFT JOIN vPerson p on p.Id = ccd.driveId
            LEFT JOIN LZCL_T_Car car on car.Id = ccd.CarId
        ) ccd
        left join V_DFDW_DZCC_T_CCDTIME vt on ccd.Id = vt.YwId
        WHERE ccd.mainId = 0 and ccd.applyTopDeptId != 0
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                ccd.applyTopDeptId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (ccd.applyTopDeptId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and ccd.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="ccd.applyTopDeptId > 0">
            and ccd.applyTopDeptId = #{ccd.applyTopDeptId}
        </if>
        <if test="ccd.driverName != null and ccd.driverName != ''">
            AND ccd.driverName LIKE concat('%',#{ccd.driverName},'%')
        </if>
        <if test="ccd.licensePlate != null and ccd.licensePlate != '' and ccd.licensePlate != '-2'">
            AND ccd.licensePlate LIKE concat('%',#{ccd.licensePlate},'%')
        </if>
        <if test="ccd.licensePlate == '-2'">
            AND ccd.carId = -2
        </if>
        <if test="ccd.approveState > -1">
            and ccd.approveState = #{ccd.approveState}
        </if>
        <if test="ccd.executeState > -1">
            and ccd.executeState = #{ccd.executeState}
        </if>
        <if test="ccd.ccOpenTime != null and ccd.executeTime != null">
            and (
            (ccd.executeTime >= #{ccd.executeTime} and ccd.ccOpenTime &lt;= #{ccd.ccOpenTime})
            or (ccd.ccOpenTime >= #{ccd.ccOpenTime} and ccd.ccOpenTime &lt;= #{ccd.executeTime})
            or (ccd.executeTime >= #{ccd.ccOpenTime} and ccd.executeTime &lt;= #{ccd.executeTime})
            )
        </if>
        <if test="ccd.applyUserName != null and ccd.applyUserName != ''">
            AND ccd.applyUserName LIKE concat('%',#{ccd.applyUserName},'%')
        </if>
        <if test="ccd.ycrName != null and ccd.ycrName != ''">
            AND ccd.ycrName LIKE concat('%',#{ccd.ycrName},'%')
        </if>
        <if test="ccd.carTag != null and ccd.carTag > -1">
            AND ccd.carTag = #{ccd.carTag}
        </if>
        order by ccd.ccOpenTime desc, ccd.executeTime desc
    </select>
    <select id="GetCcdListPageNew" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd">
        select ccd.*,
        CAST(CONVERT(varchar, ccd.ccOpenTime, 120) AS DATETIME) + CAST(ccd.openTime AS DATETIME) AS ccOpenTimeHms,
        IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他人员', p.RealName) driverName,
        p.Telephone as driverPhone,
        IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他车辆', car.LicencePlate) licensePlate,
        car.CarMold as carMold,
        car.CarTag as carTag
        FROM DFDW_DZCC_T_CCLC ccd
        LEFT JOIN vPerson p on p.Id = ccd.driveId
        LEFT JOIN LZCL_T_Car car on car.Id = ccd.CarId

        WHERE ccd.mainId = 0 and ccd.applyTopDeptId != 0

        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                ccd.applyTopDeptId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (ccd.applyTopDeptId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and ccd.carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="ccd.applyTopDeptId > 0">
            and ccd.applyTopDeptId = #{ccd.applyTopDeptId}
        </if>
        <if test="ccd.driverName != null and ccd.driverName != ''">
            AND ccd.driverName LIKE concat('%',#{ccd.driverName},'%')
        </if>
        <if test="ccd.licensePlate != null and ccd.licensePlate != '' and ccd.licensePlate != '-2'">
            AND ccd.licensePlate LIKE concat('%',#{ccd.licensePlate},'%')
        </if>
        <if test="ccd.licensePlate == '-2'">
            AND ccd.carId = -2
        </if>
        <if test="ccd.approveState > -1">
            and ccd.approveState = #{ccd.approveState}
        </if>
        <if test="ccd.executeState > -1">
            and ccd.executeState = #{ccd.executeState}
        </if>
        <if test="ccd.ccOpenTime != null and ccd.executeTime != null">
            and (
            (ccd.executeTime >= #{ccd.executeTime} and ccd.ccOpenTime &lt;= #{ccd.ccOpenTime})
            or (ccd.ccOpenTime >= #{ccd.ccOpenTime} and ccd.ccOpenTime &lt;= #{ccd.executeTime})
            or (ccd.executeTime >= #{ccd.ccOpenTime} and ccd.executeTime &lt;= #{ccd.executeTime})
            )
        </if>
        <if test="ccd.applyUserName != null and ccd.applyUserName != ''">
            AND ccd.applyUserName LIKE concat('%',#{ccd.applyUserName},'%')
        </if>
        <if test="ccd.ycrName != null and ccd.ycrName != ''">
            AND ccd.ycrName LIKE concat('%',#{ccd.ycrName},'%')
        </if>
        <if test="ccd.carTag != null and ccd.carTag > -1">
            AND ccd.carTag = #{ccd.carTag}
        </if>
        order by ccd.ccOpenTime desc, ccd.executeTime desc
    </select>
    <select id="getCcdListById" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd">
        select * ,
               (case when vt.startTime is null then ccd.ccOpenTime else vt.startTime end) as actualStartTime,
               (case when vt.endTime is null then ccd.executeTime else vt.endTime end) as actualEndTime
        from (
            SELECT
                ccd.*,
                IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他人员', p.RealName) driverName,
                p.Telephone as driverPhone,
                IIF(ccd.carId = -2 AND ccd.approveState = 2, N'其他车辆', car.LicencePlate) licensePlate,
                car.CarMold as carMold,
                car.CarTag as carTag
            FROM DFDW_DZCC_T_CCLC ccd
            LEFT JOIN vPerson p on p.Id = ccd.driveId
            LEFT JOIN LZCL_T_Car car on car.Id = ccd.CarId
        ) ccd
        left join V_DFDW_DZCC_T_CCDTIME vt on ccd.Id = vt.YwId
        WHERE ccd.mainId = 0 and ccd.applyTopDeptId != 0 and ccd.Id = #{id}
    </select>
</mapper>
