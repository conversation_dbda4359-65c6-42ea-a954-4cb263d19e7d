<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTClgldetailMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgldetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="cid" column="cid" jdbcType="INTEGER"/>
            <result property="driverId" column="driverId" jdbcType="INTEGER"/>
            <result property="startDate" column="startDate" jdbcType="DATE"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="editId" column="editId" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cid,driverId,
        startDate,createTime,updateTime,
        editId,carId,groupId,
        driverName
    </sql>
</mapper>
