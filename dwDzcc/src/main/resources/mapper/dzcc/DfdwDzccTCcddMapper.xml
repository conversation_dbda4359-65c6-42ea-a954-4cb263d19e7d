<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCcddMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCcdd">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="lcid" column="lcId" jdbcType="INTEGER"/>
            <result property="provinces" column="provinces" jdbcType="INTEGER"/>
            <result property="city" column="city" jdbcType="INTEGER"/>
            <result property="area" column="area" jdbcType="INTEGER"/>
            <result property="areatext" column="areaText" jdbcType="VARCHAR"/>
            <result property="applyuserid" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyusername" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createtime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,lcId,provinces,
        city,area,areaText,
        applyUserId,applyUserName,remark,
        createTime
    </sql>
</mapper>
