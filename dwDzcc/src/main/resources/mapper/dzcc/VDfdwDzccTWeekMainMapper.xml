<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTWeekMainMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="week" column="week" jdbcType="INTEGER"/>
            <result property="startTime" column="startTime" jdbcType="DATE"/>
            <result property="endTime" column="endTime" jdbcType="DATE"/>
            <result property="weekDays" column="weekDays" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="oringDelay" column="oringDelay" jdbcType="DECIMAL"/>
            <result property="appendDelay" column="appendDelay" jdbcType="DECIMAL"/>
            <result property="appendDelayYsp" column="appendDelayYsp" jdbcType="DECIMAL"/>
            <result property="lcDefineID" column="lcDefineID" jdbcType="INTEGER"/>
            <result property="lcName" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="isMany" column="isMany" jdbcType="INTEGER"/>
            <result property="lcJdmc" column="lcJdmc" jdbcType="VARCHAR"/>
            <result property="lcJdid" column="lcJdid" jdbcType="INTEGER"/>
            <result property="lcIsback" column="lcIsback" jdbcType="INTEGER"/>
            <result property="lcTojdid" column="lcTojdid" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,month,
        week,startTime,endTime,
        weekDays,createTime,oringDelay,
        appendDelay,appendDelayYsp,lcDefineID,
        lcName,ywID,sendPerson,
        sendPersonZgh,AllPersonZgh,isMany,
        lcJdmc,lcJdid,lcIsback,
        lcTojdid,number,BXType
    </sql>


    <select id="listPage" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain">
        SELECT t.*
        FROM V_DFDW_DZCC_T_WEEK_MAIN t
        <where>
            <if test="query.year != null">
                AND t.year = #{query.year}
            </if>
            <if test="query.month != null">
                AND t.month = #{query.month}
            </if>
            <if test="query.week != null">
                AND t.week = #{query.week}
            </if>
            <if test="query.dzccPersonEntity != null and !query.checked">
                AND t.sendPersonZgh = CONCAT( #{query.dzccPersonEntity.loginName}, '~')
            </if>
            <if test="query.dzccPersonEntity != null and query.checked">
                AND t.AllPersonZgh LIKE CONCAT('%', #{query.dzccPersonEntity.loginName} ,'%')
            </if>
            <if test="query.applyUserName != null and query.applyUserName !='' ">
                AND t.applyUserName LIKE CONCAT('%', #{query.applyUserName}, '%')
            </if>
            <if test="query.driveName != null and query.driveName != ''">
                AND t.drive_name LIKE CONCAT('%', #{query.driveName}, '%')
            </if>
            <if test="query.approveState != null and query.approveState != -1 ">
                AND t.approveState = #{query.approveState}
            </if>
        </where>
        ORDER BY t.year DESC

    </select>
</mapper>
