<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DzccRoleMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DzccRole">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="roleName" column="RoleName" jdbcType="VARCHAR"/>
            <result property="adminGroupIds" column="AdminGroupIds" jdbcType="VARCHAR"/>
            <result property="isHide" column="IsHide" jdbcType="BIT"/>
            <result property="roleKind" column="RoleKind" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        I<PERSON>,<PERSON><PERSON><PERSON>,<PERSON>min<PERSON><PERSON>I<PERSON>,
        IsH<PERSON>,<PERSON><PERSON><PERSON>
    </sql>
</mapper>
