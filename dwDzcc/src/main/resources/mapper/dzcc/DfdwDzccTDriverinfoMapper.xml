<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTDriverinfoMapper">

    <select id="GetList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriverinfo">
        select * from (
        SELECT
        info.*,
        gi.id as topGroupId,
        gi.groupname,
        gi.XH
        FROM DFDW_DZCC_T_DRIVERINFO info
        left join (select id, personId, groupId, startDate, IIF(endDate IS NULL, '2199-01-01', endDate) endDate FROM
        DFDW_DZCC_T_DRIVEREMPLOY) em on em.personId = info.id
        LEFT JOIN GroupItem gi ON em.groupId = gi.id
        <if test="!isInfoPerson and (dzccQx == 2 || dzccQx == 4)">
            inner join DFDW_DZCC_T_CDZGL cdzgl on gi.id = cdzgl.deptId and cdzgl.cdzId = #{id}
        </if>
        WHERE gi.id is not null and info.type like '%2%'
        <if test="info.startDate != null">
            and em.startDate &lt;= #{info.startDate} and em.endDate >= #{info.startDate}
        </if>
        <if test="isInfoPerson || dzccQx == 1">union all
            SELECT
            info.*,
            gi.id as topGroupId,
            gi.groupname,
            gi.XH
            FROM DFDW_DZCC_T_DRIVERINFO info
            left join (select id, personId, groupId, startDate, IIF(endDate IS NULL, '2199-01-01', endDate) endDate FROM
            DFDW_DZCC_T_DRIVEREMPLOY) em on em.personId = info.id
            LEFT JOIN GroupItem gi ON em.groupId = gi.id
            where gi.id is null and info.type like '%2%'
        </if>) a
        where 1 = 1
        <if test="info.groupId > 0">
            and a.topGroupId = #{info.groupId}
        </if>
        <if test="info.name != null and info.name != ''">
            and a.name like concat('%', #{info.name}, '%')
        </if>
        <if test="info.certificateID != null and info.certificateID != ''">
            and a.certificateID like concat('%', #{info.certificateID}, '%')
        </if>
        ORDER BY a.XH, a.name
    </select>
    <select id="GetJsyxxList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriverinfo">
        select * from (
            SELECT
                info.*,
                gi.groupname,
                gi.XH
            FROM DFDW_DZCC_T_DRIVERINFO info
            left join vPerson vp on vp.Id = info.personId
            LEFT JOIN GroupItem gi ON vp.TopGroupId = gi.id
            <if test="!isInfoPerson and (dzccQx == 2 || dzccQx == 4)">
                inner join DFDW_DZCC_T_CDZGL cdzgl on gi.id = cdzgl.deptId and cdzgl.cdzId = #{id}
            </if>
            WHERE gi.id is not null and info.type like '%1%' and info.personId is not null
        ) a
        where 1 = 1
        <if test="info.groupId > 0">
            and a.groupId = #{info.groupId}
        </if>
        <if test="info.name != null and info.name != ''">
            and a.name like concat('%', #{info.name}, '%')
        </if>
        ORDER BY a.XH, a.name
    </select>
    <select id="GetJsyxxPage" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriverinfo">
        select * from (
            SELECT
                info.*,
                gi.groupname,
                gi.XH
            FROM DFDW_DZCC_T_DRIVERINFO info
            left join vPerson vp on vp.Id = info.personId
            LEFT JOIN GroupItem gi ON vp.TopGroupId = gi.id
            <if test="!isInfoPerson and (dzccQx == 2 || dzccQx == 4)">
                inner join DFDW_DZCC_T_CDZGL cdzgl on gi.id = cdzgl.deptId and cdzgl.cdzId = #{id}
            </if>
            WHERE gi.id is not null and info.type like '%1%' and info.personId is not null
        ) a
        where 1 = 1
        <if test="info.groupId > 0">
            and a.groupId = #{info.groupId}
        </if>
        <if test="info.name != null and info.name != ''">
            and a.name like concat('%', #{info.name}, '%')
        </if>
        ORDER BY a.XH, a.name
    </select>
</mapper>
