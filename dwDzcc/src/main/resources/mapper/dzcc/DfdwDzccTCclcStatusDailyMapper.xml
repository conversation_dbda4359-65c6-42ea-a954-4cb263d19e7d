<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCclcStatusDailyMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcStatusDaily">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="ywid" column="ywId" jdbcType="INTEGER"/>
            <result property="carid" column="carId" jdbcType="INTEGER"/>
            <result property="driveid" column="driveId" jdbcType="INTEGER"/>
            <result property="movedate" column="moveDate" jdbcType="DATE"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="createtime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updatetime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="istoyz" column="isToYZ" jdbcType="BIT"/>
            <result property="createuserid" column="createUserId" jdbcType="INTEGER"/>
            <result property="updateuserid" column="updateUserId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ywId,carId,
        driveId,moveDate,status,
        createTime,updateTime,isToYZ,
        createUserId,updateUserId
    </sql>
</mapper>
