<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCarmoveMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove">
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="carMold" column="carMold" jdbcType="VARCHAR"/>
            <result property="licensePlate" column="licensePlate" jdbcType="VARCHAR"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="driverName" column="driverName" jdbcType="VARCHAR"/>
            <result property="applyTopDeptId" column="applyTopDeptId" jdbcType="INTEGER"/>
            <result property="applyDeptName" column="applyDeptName" jdbcType="VARCHAR"/>
            <result property="curDate" column="curDate" jdbcType="DATE"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        carId,carMold,licensePlate,
        driveId,driverName,applyTopDeptId,
        applyDeptName,curDate,status
    </sql>
    <select id="getXcglListPage" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove">
        select * from (
        select
        row_number() over (order by gi.XH, car.LicencePlate, csd.curDate desc) row,
        car.Id as carId,
        car.CarMold as carMold,
        car.LicencePlate as licensePlate,
        car.LicencePlate as LicencePlate,
        clgl.driverId as driveId,
        clgl.driverName,
        gi.id as applyTopDeptId,
        gi.groupname as applyDeptName,
        csd.curDate,
        csd.status,
        gi.XH
        from
        -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
        (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
        -- 关联查询部门
        LEFT JOIN GroupItem gi on gi.id = mc.GroupId
        -- 关联查询司机
        LEFT JOIN (
        -- 查询当月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
        UNION
        -- 查询往月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a
        WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        left join DFDW_DZCC_T_CarStatusDaily csd on csd.carId = car.Id and csd.groupId = gi.id and csd.personId = clgl.driverId AND csd.curDate >= mc.StartDate AND csd.curDate &lt;= mc.EndDate
        WHERE car.Id IS NOT NULL AND csd.curDate >= #{startDate} AND csd.curDate &lt; #{endDate} and (csd.status = N'出大市' or csd.status = N'跨区域')
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="carmove.licensePlate != null and carmove.licensePlate != ''">
            AND car.LicencePlate LIKE concat('%', #{carmove.licensePlate}, '%')
        </if>
        <if test="carmove.driverName != null and carmove.driverName != ''">
            AND clgl.driverName LIKE concat('%', #{carmove.driverName}, '%')
        </if>
        <if test="carmove.applyTopDeptId > 0">
            AND gi.id = #{carmove.applyTopDeptId}
        </if>
        <if test="carmove.alarmType != null and carmove.alarmType != '' and carmove.alarmType != '全部'">
            AND csd.status = #{carmove.alarmType}
        </if>
        ) a
        where row > #{carmove.pageSize} * (#{carmove.pageNum} - 1) and row &lt;= #{carmove.pageSize} * #{carmove.pageNum}
    </select>
    <select id="getXcglList" resultType="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove">
        select
        car.Id as carId,
        car.CarMold as carMold,
        car.LicencePlate as licensePlate,
        car.LicencePlate as LicencePlate,
        clgl.driverId as driveId,
        clgl.driverName,
        gi.id as applyTopDeptId,
        gi.groupname as applyDeptName,
        csd.curDate,
        csd.status,
        gi.XH
        from
        -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
        (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
        -- 关联查询部门
        LEFT JOIN GroupItem gi on gi.id = mc.GroupId
        -- 关联查询司机
        LEFT JOIN (
        -- 查询当月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
        UNION
        -- 查询往月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a
        WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        left join DFDW_DZCC_T_CarStatusDaily csd on csd.carId = car.Id and csd.groupId = gi.id and csd.personId = clgl.driverId AND csd.curDate >= mc.StartDate AND csd.curDate &lt;= mc.EndDate
        WHERE car.Id IS NOT NULL AND csd.curDate >= #{startDate} AND csd.curDate &lt; #{endDate} and (csd.status = N'出大市' or csd.status = N'跨区域')
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="carmove.licensePlate != null and carmove.licensePlate != ''">
            AND car.LicencePlate LIKE concat('%', #{carmove.licensePlate}, '%')
        </if>
        <if test="carmove.driverName != null and carmove.driverName != ''">
            AND clgl.driverName LIKE concat('%', #{carmove.driverName}, '%')
        </if>
        <if test="carmove.applyTopDeptId > 0">
            AND gi.id = #{carmove.applyTopDeptId}
        </if>
        <if test="carmove.alarmType != null and carmove.alarmType != '' and carmove.alarmType != '全部'">
            AND csd.status = #{carmove.alarmType}
        </if>
        order by gi.XH, car.LicencePlate, csd.curDate desc
    </select>
    <select id="getXcglListCount" resultType="java.lang.Long">
        select
        count (*)
        from
        -- 查询LZCL_T_Car_Move_Child,EndDate为空时自动补上当月最后一天
        (SELECT LicencePlate, GroupId, StartDate, IIF(EndDate IS NULL, EOMONTH(StartDate), EndDate) EndDate FROM LZCL_T_Car_Move_Child) mc
        -- 关联查询车辆
        LEFT JOIN LZCL_T_Car car on mc.LicencePlate = car.LicencePlate
        -- 关联查询部门
        LEFT JOIN GroupItem gi on gi.id = mc.GroupId
        -- 关联查询司机
        LEFT JOIN (
        -- 查询当月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM DFDW_DZCC_T_CLGLDetail WHERE (startDate >= #{startDate} AND startDate &lt; #{endDate})
        UNION
        -- 查询往月设置的司机
        SELECT carId,driverId,driverName,groupId,startDate FROM ( SELECT *,row_number() over ( PARTITION BY carId,groupId ORDER BY startDate DESC ) AS row FROM DFDW_DZCC_T_CLGLDetail WHERE startDate &lt;= #{startDate} ) a
        WHERE row = 1
        ) clgl on clgl.carId = car.Id AND clgl.groupId = mc.GroupId
        left join DFDW_DZCC_T_CarStatusDaily csd on csd.carId = car.Id and csd.groupId = gi.id and csd.personId = clgl.driverId AND csd.curDate >= mc.StartDate AND csd.curDate &lt;= mc.EndDate
        WHERE car.Id IS NOT NULL AND csd.curDate >= #{startDate} AND csd.curDate &lt; #{endDate} and (csd.status = N'出大市' or csd.status = N'跨区域')
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                gi.id in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (gi.id in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) and car.id in (select carId from DFDW_DZCC_T_CARPERSON where personId = #{person.id}))
            </if>)
        </if>
        <if test="carmove.licensePlate != null and carmove.licensePlate != ''">
            AND car.LicencePlate LIKE concat('%', #{carmove.licensePlate}, '%')
        </if>
        <if test="carmove.driverName != null and carmove.driverName != ''">
            AND clgl.driverName LIKE concat('%', #{carmove.driverName}, '%')
        </if>
        <if test="carmove.applyTopDeptId > 0">
            AND gi.id = #{carmove.applyTopDeptId}
        </if>
        <if test="carmove.alarmType != null and carmove.alarmType != '' and carmove.alarmType != '全部'">
            AND csd.status = #{carmove.alarmType}
        </if>
    </select>
</mapper>
