<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCarmovelcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmovelc">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="currDate" column="currDate" jdbcType="DATE"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="approveNote" column="approveNote" jdbcType="VARCHAR"/>
            <result property="approveTime" column="approveTime" jdbcType="TIMESTAMP"/>
            <result property="approveUserId" column="approveUserId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="lcDefineID" column="lcDefineID" jdbcType="INTEGER"/>
            <result property="lcName" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="isMany" column="isMany" jdbcType="INTEGER"/>
            <result property="lcJdmc" column="lcJdmc" jdbcType="VARCHAR"/>
            <result property="lcJdid" column="lcJdid" jdbcType="INTEGER"/>
            <result property="lcIsback" column="lcIsback" jdbcType="INTEGER"/>
            <result property="lcTojdid" column="lcTojdid" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,currDate,
        approveState,approveNote,approveTime,
        approveUserId,createTime,lcDefineID,
        lcName,ywID,sendPerson,
        sendPersonZgh,AllPersonZgh,isMany,
        lcJdmc,lcJdid,lcIsback,
        lcTojdid,number,BXType
    </sql>
</mapper>
