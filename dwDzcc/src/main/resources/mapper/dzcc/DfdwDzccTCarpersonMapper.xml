<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarpersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarperson">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="personId" column="personId" jdbcType="INTEGER"/>
            <result property="realReturnDate" column="realReturnDate" jdbcType="DATE"/>
            <result property="editTime" column="editTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,carId,personId,
        realReturnDate,editTime
    </sql>
    <select id="GetCarList" resultType="com.soft.gcc.xtbg.dzcc.domain.DzccCar">
        select car.id, car.LicencePlate, car.groupid, g.groupname from LZCL_T_Car car
        inner join DFDW_DZCC_T_CDZGL cdzgl on cdzgl.cdzId = #{personId} and cdzgl.deptId = car.groupid
        <if test="qx == 2">
            inner join DFDW_DZCC_T_CDZGL cdz on cdz.cdzId = #{id} and cdz.deptId = car.groupid
        </if>
        LEFT JOIN GroupItem g ON car.groupid = g.id
        where car.IsEnable = 1
          and car.IsDelete = 0
        <if test="groupId > 0">
              and car.groupid = #{groupId}
        </if>
        group by car.id, car.LicencePlate, car.groupid, g.groupname
    </select>
</mapper>
