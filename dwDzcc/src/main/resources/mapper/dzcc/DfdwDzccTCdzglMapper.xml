<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCdzglMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCdzgl">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="cdzId" column="cdzId" jdbcType="INTEGER"/>
            <result property="cdzName" column="cdzName" jdbcType="VARCHAR"/>
            <result property="zglId" column="zglId" jdbcType="INTEGER"/>
            <result property="zglName" column="zglName" jdbcType="VARCHAR"/>
            <result property="deptId" column="deptId" jdbcType="INTEGER"/>
            <result property="deptName" column="deptName" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="certificateId" column="certificateId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cdzId,cdzName,
        zglId,zglName,deptId,
        deptName,createTime,updateTime,
        certificateId
    </sql>
    <insert id="saveBatch">
        INSERT INTO DFDW_DZCC_T_CDZGL (deptId, deptName, cdzId, cdzName, type)
        VALUES
        <foreach collection ="addList" item="add" separator =",">
            (#{add.deptId}, #{add.deptName}, #{add.cdzId}, #{add.cdzName}, #{add.type})
        </foreach>
    </insert>
    <select id="GetCDZPZList" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCdzgl">
        SELECT
            a.cdzId,
            a.cdzName,
            a.Telephone,
            deptName = STUFF(
                    (
                        SELECT
                            ',' + cdz.deptName
                        FROM
                            (
                                SELECT
                                    cdz.cdzId,
                                    cdz.deptName
                                FROM
                                    DFDW_DZCC_T_CDZGL cdz
                                    INNER JOIN RolePerson rp ON cdz.cdzId = rp.PersonId
                                <if test="personTypeId == 2">
                                    INNER JOIN Role r ON r.Id = rp.RoleId AND r.RoleName = N'协同办公-电子出车-车辆调度人员'
                                </if>
                                <if test="personTypeId == 3">
                                    INNER JOIN Role r ON r.Id = rp.RoleId AND r.RoleName = N'协同办公-电子出车-车辆运监人员'
                                </if>
                                <if test="qx == 2">
                                    INNER JOIN DFDW_DZCC_T_CDZGL cdzgl ON cdzgl.cdzId = #{id} AND cdzgl.deptId = cdz.deptId and cdzgl.type = 1
                                </if>
                                WHERE
                                       1 = 1
                                    <if test="deptId > 0">
                                        AND cdz.deptId = #{deptId}
                                    </if>
                                    <if test="name != null || name != ''">
                                        AND cdz.cdzName like concat(concat('%', #{name}), '%')
                                    </if>
                                GROUP BY cdz.cdzId, cdz.deptName
                            ) cdz
                        WHERE
                            cdz.cdzId = a.cdzId FOR xml path ( '' )
                    ),
                    1,
                    1,
                    ''
                ),
            #{personTypeId} as personType
        FROM
            (
                SELECT
                    cdz.cdzId,
                    vp.RealName cdzName,
                    vp.Telephone
                FROM
                    DFDW_DZCC_T_CDZGL cdz
                    left join vPerson vp on cdz.cdzId = vp.Id
                    INNER JOIN RolePerson rp ON cdz.cdzId = rp.PersonId
                <if test="personTypeId == 2">
                    INNER JOIN Role r ON r.Id = rp.RoleId AND r.RoleName = N'协同办公-电子出车-车辆调度人员'
                </if>
                <if test="personTypeId == 3">
                    INNER JOIN Role r ON r.Id = rp.RoleId AND r.RoleName = N'协同办公-电子出车-车辆运监人员'
                </if>
                <if test="qx == 2">
                    INNER JOIN DFDW_DZCC_T_CDZGL cdzgl ON cdzgl.cdzId = #{id} AND cdzgl.deptId = cdz.deptId and cdzgl.type = 1
                </if>
                WHERE
                    1 = 1
                <if test="deptId > 0">
                    AND cdz.deptId = #{deptId}
                </if>
                <if test="name != null || name != ''">
                    AND vp.RealName like concat(concat('%', #{name}), '%')
                </if>
                GROUP BY cdz.cdzId, vp.RealName, vp.Telephone
            ) a
        order by a.cdzName
    </select>
</mapper>
