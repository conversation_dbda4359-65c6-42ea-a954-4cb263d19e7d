<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTWeekMainMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="week" column="week" jdbcType="INTEGER"/>
            <result property="startTime" column="startTime" jdbcType="DATE"/>
            <result property="endTime" column="endTime" jdbcType="DATE"/>
            <result property="weekDays" column="weekDays" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,month,
        week,startTime,endTime,
        weekDays,createTime
    </sql>
</mapper>
