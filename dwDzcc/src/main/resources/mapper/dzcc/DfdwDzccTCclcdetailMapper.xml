<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCclcdetailMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="ywId" column="ywId" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="moveDate" column="moveDate" jdbcType="DATE"/>
            <result property="startTime" column="startTime" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="endTime" jdbcType="TIMESTAMP"/>
            <result property="overtimeHoursAm" column="overtimeHoursAm" jdbcType="DECIMAL"/>
            <result property="overtimeHoursPm" column="overtimeHoursPm" jdbcType="DECIMAL"/>
            <result property="subsidyState" column="subsidyState" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="dayType" column="dayType" jdbcType="INTEGER"/>
            <result property="bgdId" column="bgdId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="overtimeHoursAll" column="overtimeHoursAll" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ywId,carId,
        driveId,moveDate,startTime,
        endTime,overtimeHoursAm,overtimeHoursPm,
        subsidyState,groupId,dayType,
        bgdId,createTime,updateTime,
        overtimeHoursAll
    </sql>
</mapper>
