<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTNoticeMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTNotice">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="groupId" column="groupId" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="editId" column="editId" jdbcType="INTEGER"/>
            <result property="startTime" column="startTime" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="endTime" jdbcType="TIMESTAMP"/>
            <result property="isOpen" column="isOpen" jdbcType="INTEGER"/>
            <result property="noticeType" column="noticeType" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="titleImage" column="titleImage" jdbcType="VARCHAR"/>
            <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
            <result property="titlefileid" column="titlefileid" jdbcType="INTEGER"/>
            <result property="videofileid" column="videofileid" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,groupId,remark,
        createTime,updateTime,editId,
        startTime,endTime,isOpen,
        noticeType,title,titleImage,
        groupName,titlefileid,videofileid
    </sql>
</mapper>
