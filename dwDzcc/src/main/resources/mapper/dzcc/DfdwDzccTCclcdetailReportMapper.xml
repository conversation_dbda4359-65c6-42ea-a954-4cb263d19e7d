<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCclcdetailReportMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="subsidyStandardOvertime" column="subsidyStandardOvertime" jdbcType="VARCHAR"/>
            <result property="subsidyStandardDelay" column="subsidyStandardDelay" jdbcType="VARCHAR"/>
            <result property="subsidyStandardNight" column="subsidyStandardNight" jdbcType="VARCHAR"/>
            <result property="sxrOvertimeDays" column="sxrOvertimeDays" jdbcType="INTEGER"/>
            <result property="jjrOvertimeDays" column="jjrOvertimeDays" jdbcType="INTEGER"/>
            <result property="overtimeCost" column="overtimeCost" jdbcType="DECIMAL"/>
            <result property="gzrDelayHour" column="gzrDelayHour" jdbcType="DECIMAL"/>
            <result property="sxrDelayHour" column="sxrDelayHour" jdbcType="DECIMAL"/>
            <result property="jjrDelayHour" column="jjrDelayHour" jdbcType="DECIMAL"/>
            <result property="delayCost" column="delayCost" jdbcType="DECIMAL"/>
            <result property="nightShiftDays" column="nightShiftDays" jdbcType="INTEGER"/>
            <result property="nightShiftCost" column="nightShiftCost" jdbcType="DECIMAL"/>
            <result property="travelAllowance" column="travelAllowance" jdbcType="DECIMAL"/>
            <result property="allCost" column="allCost" jdbcType="DECIMAL"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,driveId,year,
        month,subsidyStandardOvertime,subsidyStandardDelay,
        subsidyStandardNight,sxrOvertimeDays,jjrOvertimeDays,
        overtimeCost,gzrDelayHour,sxrDelayHour,
        jjrDelayHour,delayCost,nightShiftDays,
        nightShiftCost,travelAllowance,allCost,
        createTime,updateTime
    </sql>


    <select id="getListByMonth" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport">
        select r.*,vp.topGroupId,vp.topGroupName,vp.RealName from DFDW_DZCC_T_CCLCDetail_Report r
        left join vPerson vp on r.driveId = vp.Id
        where year = #{year} and month = #{month} and vp.topGroupId  is not null
        <if test="topGroupId !=-1">
            and vp.topGroupId = #{topGroupId}
        </if>
        <if test="realName !=null and realName !='' ">
            and vp.RealName like concat('%', #{realName}, '%')
        </if>
        <if test="topGroupId !=-1">
           and vp.topGroupId = #{topGroupId}
        </if>
        <if test="realName !=null and realName !='' ">
            and vp.RealName like concat('%', #{realName}, '%')
        </if>
        <if test="!person.qxs.contains(1)">
            and (
            <if test="person.qxs.contains(2)">
                vp.TopGroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)
            </if>
            <if test="person.qxs.contains(4) || person.qxs.contains(6)">
                <if test="person.qxs.contains(2)">or</if>
                (vp.TopGroupId in (
                <if test="person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1
                </if>
                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2
                </if>
                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">
                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3
                </if>
                ) )
            </if>)
        </if>
     </select>

<!--    <select id="getListByMonth" resultType="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport">-->
<!--        select r.*,vp.topGroupId,vp.topGroupName,vp.RealName from DFDW_DZCC_T_CCLCDetail_Report r-->
<!--        left join vPerson vp on r.driveId = vp.Id-->
<!--        where year = #{year} and month = #{month} and vp.topGroupId  is not null-->
<!--        <if test="topGroupId !=-1">-->
<!--            and vp.topGroupId = #{topGroupId}-->
<!--        </if>-->
<!--        <if test="realName !=null and realName !='' ">-->
<!--            and vp.RealName like concat('%', #{realName}, '%')-->
<!--        </if>-->
<!--        <if test="!person.qxs.contains(1)">-->
<!--            and (-->
<!--            <if test="person.qxs.contains(2)">-->
<!--                vp.TopGroupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 1)-->
<!--            </if>-->
<!--            <if test="person.qxs.contains(4) || person.qxs.contains(6)">-->
<!--                <if test="person.qxs.contains(2)">or</if>-->
<!--                (vp.TopGroupId in (-->
<!--                <if test="person.qxs.contains(4) and person.qxs.contains(6)">-->
<!--                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type != 1-->
<!--                </if>-->
<!--                <if test="person.qxs.contains(4) and !person.qxs.contains(6)">-->
<!--                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 2-->
<!--                </if>-->
<!--                <if test="!person.qxs.contains(4) and person.qxs.contains(6)">-->
<!--                    select deptId from DFDW_DZCC_T_CDZGL where cdzId = #{person.id} and type = 3-->
<!--                </if>-->
<!--                ) )-->
<!--            </if>)-->
<!--        </if>-->
<!--    </select>-->
</mapper>
