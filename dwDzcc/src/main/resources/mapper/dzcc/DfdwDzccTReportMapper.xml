<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTReportMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTReport">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="driveId" column="driveId" jdbcType="INTEGER"/>
            <result property="carId" column="carId" jdbcType="INTEGER"/>
            <result property="startDate" column="startDate" jdbcType="DATE"/>
            <result property="endDate" column="endDate" jdbcType="DATE"/>
            <result property="mileage" column="mileage" jdbcType="INTEGER"/>
            <result property="actualValue" column="actualValue" jdbcType="INTEGER"/>
            <result property="cerateTime" column="cerateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,month,
        driveId,carId,startDate,
        endDate,mileage,actualValue,
        cerateTime
    </sql>
</mapper>
