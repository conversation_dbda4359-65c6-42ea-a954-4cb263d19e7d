package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTClydMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTClydService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 针对表【V_DFDW_DZCC_T_CLYD】的数据库操作Service实现
 * @createDate 2022-11-07 09:48:07
 */
@Service
public class VDfdwDzccTClydServiceImpl extends ServiceImpl<VDfdwDzccTClydMapper, VDfdwDzccTClyd>
        implements IVDfdwDzccTClydService {
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    @Override
    public Result<Object> GetClydList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTClyd clyd) {
        try {
            IPage<VDfdwDzccTClyd> list = new Page<>();
            list.setCurrent(clyd.getPageNum());
            list.setSize(clyd.getPageSize());
            LocalDate startDate = DateUtil.asLocalDate(clyd.getStartMoveTime());
            LocalDate endDate = DateUtil.asLocalDate(clyd.getEndMoveTime()).plusDays(1);

            list = baseMapper.getClydListPage(
                    list,
                    clyd,
                    startDate,
                    endDate,
                    dzccPersonEntity
            );
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




