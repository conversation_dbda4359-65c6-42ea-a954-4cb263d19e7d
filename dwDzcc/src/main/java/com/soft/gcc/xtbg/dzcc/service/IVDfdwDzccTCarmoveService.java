package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CARMOVE】的数据库操作Service
* @createDate 2022-11-11 15:26:25
*/
public interface IVDfdwDzccTCarmoveService extends IService<VDfdwDzccTCarmove> {

    List<VDfdwDzccTCarmove> getXcglListPage(DzccPersonEntity person, VDfdwDzccTCarmove carmove, LocalDate startDate, LocalDate endDate);

    List<VDfdwDzccTCarmove> getXcglList(DzccPersonEntity person, VDfdwDzccTCarmove carmove, LocalDate startDate, LocalDate endDate);

    Long getXcglListCount(DzccPersonEntity person, VDfdwDzccTCarmove carmove, LocalDate startDate, LocalDate endDate);
}
