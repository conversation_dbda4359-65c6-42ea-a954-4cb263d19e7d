package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_WEEK_MAIN】的数据库操作Mapper
* @createDate 2024-12-23 16:58:44
* @Entity com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain
*/
public interface VDfdwDzccTWeekMainMapper extends BaseMapper<VDfdwDzccTWeekMain> {
    IPage<VDfdwDzccTWeekMain> listPage(@Param("page") Page<VDfdwDzccTWeekMain> page, @Param("query") VDfdwDzccTWeek vdfdwDzccTWeek);

}




