package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_CCD
*/
@TableName(value ="V_DFDW_DZCC_T_CCD", autoResultMap = true)
@Data
public class VDfdwDzccTCcd extends DzccBaseEntity implements Serializable {


    /**
    *
    */
    @TableField(value = "id")
    @JSONField(name = "id")

    private Integer id;
    /**
    *
    */
    @TableField(value = "applyNo")
    @JSONField(name = "applyNo")

    private String applyNo;
    /**
    *
    */
    @TableField(value = "applyUserId")
    @JSONField(name = "applyUserId")

    private Integer applyUserId;
    /**
    *
    */
    @TableField(value = "applyUserName")
    @JSONField(name = "applyUserName")

    private String applyUserName;
    /**
    *
    */
    @TableField(value = "applyPhone")
    @JSONField(name = "applyPhone")

    private String applyPhone;
    /**
    *
    */
    @TableField(value = "applyDeptId")
    @JSONField(name = "applyDeptId")

    private Integer applyDeptId;
    /**
    *
    */
    @TableField(value = "applyDeptName")
    @JSONField(name = "applyDeptName")

    private String applyDeptName;
    /**
    *
    */
    @TableField(value = "applyNum")
    @JSONField(name = "applyNum")

    private Integer applyNum;
    /**
    *
    */
    @TableField(value = "ccType")
    @JSONField(name = "ccType")

    private Integer ccType;
    /**
    *
    */
    @TableField(value = "ccOpenTime")
    @JSONField(name = "ccOpenTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ccOpenTime;
    /**
    *
    */
    @TableField(value = "note")
    @JSONField(name = "note")

    private String note;
    /**
    *
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    *
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")

    private Integer approveState;
    /**
    *
    */
    @TableField(value = "executeState")
    @JSONField(name = "executeState")

    private Integer executeState;
    /**
    *
    */
    @TableField(value = "executeTime")
    @JSONField(name = "executeTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime executeTime;
    /**
    *
    */
    @TableField(value = "mainId")
    @JSONField(name = "mainId")

    private Integer mainId;
    /**
    *
    */
    @TableField(value = "ccDays")
    @JSONField(name = "ccDays")

    private Integer ccDays;
    /**
    *
    */
    @TableField(value = "addressInfo")
    @JSONField(name = "addressInfo")

    private String addressInfo;
    /**
    *
    */
    @TableField(value = "scdText")
    @JSONField(name = "scdText")

    private String scdText;
    /**
    *
    */
    @TableField(value = "applyTopDeptId")
    @JSONField(name = "applyTopDeptId")

    private Integer applyTopDeptId;
    /**
    *
    */
    @TableField(value = "applyTopDeptName")
    @JSONField(name = "applyTopDeptName")

    private String applyTopDeptName;
    /**
    *
    */
    @TableField(value = "ccEndTime")
    @JSONField(name = "ccEndTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime ccEndTime;
    /**
    *
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    *
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
    *
    */
    @TableField(value = "cdzPersonId")
    @JSONField(name = "cdzPersonId")

    private Integer cdzPersonId;
    /**
    *
    */
    @TableField(value = "remark")
    @JSONField(name = "remark")

    private String remark;
    /**
    *
    */
    @TableField(value = "ycrId")
    @JSONField(name = "ycrId")

    private Integer ycrId;
    /**
    *
    */
    @TableField(value = "ycrName")
    @JSONField(name = "ycrName")

    private String ycrName;
    /**
     * 开始时间（0上午1下午）
     */
    @TableField(value = "beginType")
    @JSONField(name = "beginType")
    private Integer beginType;
    /**
     * 开始时间
     */
    @TableField(value = "openTime")
    @JSONField(name = "openTime")
    private String openTime;

    /**
     * 结束时间
     */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    private String endTime;
    /**
    *
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")

    private String driverName;
    /**
    *
    */
    @TableField(value = "driverPhone")
    @JSONField(name = "driverPhone")

    private String driverPhone;
    /**
    *
    */
    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")

    private String licensePlate;
    /**
     *车型
     */
    @TableField(value = "carMold")
    @JSONField(name = "carMold")
    private String carMold;


    /**
     * 用户实际点击到达实际
     */
    @TableField(value = "arriveTime")
    @JSONField(name = "arriveTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arriveTime;

    @TableField(exist = false)
    private Boolean isAvail;

    /**
     *有效天数
     */
    @TableField(exist = false)
    private Integer validDays;
    /**
     *有效总天数
     */
    @TableField(exist = false)
    private Integer validDaySum;
    /**
     *合并数量
     */
    @TableField(exist = false)
    private Integer count;
    /**
     *0 车 1 人
     */
    @TableField(exist = false)
    private Integer carOrPerson;
    /**
     *其他车辆
     */
    @TableField(exist = false)
    private Boolean isQtcl;

    //实际开始时间
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date actualStartTime;

    //实际结束实际
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date actualEndTime;

    /**
     * 车辆标识
     */
    @TableField(exist = false)
    private Integer carTag;

    /**
     * 自定义 时间 拼接 y-m-d H:m:s
     */
    @TableField(exist = false)
    private Date ccOpenTimeHms;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
