package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCbjl;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCbjlMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCbjlService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【V_DFDW_DZCC_T_CBJL】的数据库操作Service实现
 * @createDate 2022-10-26 11:14:10
 */
@Service
public class VDfdwDzccTCbjlServiceImpl extends ServiceImpl<VDfdwDzccTCbjlMapper, VDfdwDzccTCbjl>
        implements IVDfdwDzccTCbjlService {

    @Override
    public Result<Object> GetCbjlList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCbjl cbjl) {
        try {
            IPage<VDfdwDzccTCbjl> list = new Page<>();
            list.setCurrent(cbjl.getPageNum());
            list.setSize(cbjl.getPageSize());
            String sql = "";
            if (dzccPersonEntity.getDzccQx() == 1) {
                sql = "";
            } else {
                sql = "(";
                if (dzccPersonEntity.getQxs().contains(2)) {
                    sql += "groupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 1) ";
                }
                if (dzccPersonEntity.getQxs().contains(4) || dzccPersonEntity.getQxs().contains(6)) {
                    if (dzccPersonEntity.getQxs().contains(2)) {
                        sql += " or ";
                    }
                    sql += "(groupId in (";
                    if (dzccPersonEntity.getQxs().contains(4) && dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type != 1";
                    } else if (dzccPersonEntity.getQxs().contains(4) && !dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 2";
                    } else if (!dzccPersonEntity.getQxs().contains(4) && dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 3";
                    }
                    sql += ") and carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = " + dzccPersonEntity.getId() + "))";
                }
                sql += ")";
            }
            if (cbjl.getEndTime() != null) {
                cbjl.setEndTime(new Date(cbjl.getEndTime().getTime() + 86400000));
            }
            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                list = this.page(list, new LambdaQueryWrapper<VDfdwDzccTCbjl>()
                        .like(!"".equals(cbjl.getLicensePlate()), VDfdwDzccTCbjl::getLicensePlate, cbjl.getLicensePlate())
                        .ge(cbjl.getStartTime() != null, VDfdwDzccTCbjl::getStartTime, cbjl.getStartTime())
                        .le(cbjl.getEndTime() != null, VDfdwDzccTCbjl::getStartTime, cbjl.getEndTime())
                        .apply(!"".equals(sql), sql)
                        .eq(cbjl.getGroupId() > -1, VDfdwDzccTCbjl::getGroupId, cbjl.getGroupId())
                        .eq(cbjl.getType() > 0, VDfdwDzccTCbjl::getType, cbjl.getType())
                        .orderByDesc(VDfdwDzccTCbjl::getCreateTime)
                );
            }
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




