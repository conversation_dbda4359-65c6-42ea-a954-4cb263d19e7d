package com.soft.gcc.xtbg.dzcc.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.VO.DateRange;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.entity.JtsjDto;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTWeekMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCclcdetailMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTWeekMapper;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekMainService;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekService;
import com.soft.gcc.xtbg.dzcc.service.VDfdwDzccTWeekService;
import org.docx4j.wml.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR> @description 针对表【DFDW_DZCC_T_WEEK(电子出车-周期（延时申请）)】的数据库操作Service实现
* @createDate 2024-12-16 10:47:16
*/
@Service
public class DfdwDzccTWeekServiceImpl extends ServiceImpl<DfdwDzccTWeekMapper, DfdwDzccTWeek>
    implements DfdwDzccTWeekService {

    @Autowired
    private VDfdwDzccTCclcdetailMapper vDfdwDzccTCclcdetailMapper;

    @Resource
    DfdwTDictDataService dfdwTDictDataService;
    @Resource
    VDfdwDzccTWeekMapper vDfdwDzccTWeekMapper;
    @Resource
    DfdwDzccTWeekMainService dfdwDzccTWeekMainService;

    @Override
    public IPage<DfdwDzccTWeek> listPage(DfdwDzccTWeek dfdwDzccTWeek){
        Page<DfdwDzccTWeek> page = new Page<>(dfdwDzccTWeek.getPageNum(),dfdwDzccTWeek.getPageSize());
        return this.baseMapper.listPage(page,dfdwDzccTWeek);
    }

    @Override
    public IPage<DfdwDzccTWeek> getWorkSummaryList(DfdwDzccTWeek dfdwDzccTWeek) throws Exception {
        Integer year = dfdwDzccTWeek.getYear();
        Integer month = dfdwDzccTWeek.getMonth();
        Integer week = dfdwDzccTWeek.getWeek();


        // 根据周数计算开始和结束日期
        LocalDate startDate;
        LocalDate endDate;

        List<DfdwDzccTWeekMain> weekMainList = dfdwDzccTWeekMainService.list(new QueryWrapper<DfdwDzccTWeekMain>().lambda()
                .eq(DfdwDzccTWeekMain::getYear, year)
                .eq(DfdwDzccTWeekMain::getMonth, month)
                .eq(DfdwDzccTWeekMain::getWeek, week));
        if(CollectionUtil.isNotEmpty(weekMainList)){
            DfdwDzccTWeekMain weekMain = weekMainList.get(0);
             startDate = weekMain.getStartTime();
             endDate = weekMain.getEndTime().plusDays(1);//加一天给下面的getSjqdForMonth 查sql语句
        }else{
            throw new Exception("没有对应的周信息");
        }

        Page<DfdwDzccTWeek> page = new Page<>(dfdwDzccTWeek.getPageNum(),dfdwDzccTWeek.getPageSize());
        //week表清单

        List<DfdwDzccTWeek> BaseList = this.baseMapper.getList(dfdwDzccTWeek);


        List<DfdwDzccTWeek> addList=new ArrayList<>();
        //有初始延时的司机ID列表
        List<Integer> HasHoursDriverIds= new ArrayList<>();
        //如果有初始延时，直接week表取车牌
        BaseList.stream().forEach(s->{
                                if(s.getOringOvertimeHours()!=null && !s.getOringOvertimeHours().equals(BigDecimal.ZERO)){
                                    String licencePlates = s.getLicencePlates();
                                    if(StrUtil.isNotEmpty(licencePlates)){
                                        HasHoursDriverIds.add(s.getDriveId());
                                        String[] split = licencePlates.split("、");
                                        List<String> strList = Arrays.asList(split);
                                        s.setLicencePlate(strList.get(0));
                                        if(strList.size()>1){
                                            for (int i = 1; i < strList.size(); i++) {
                                                DfdwDzccTWeek newR = new DfdwDzccTWeek();
                                                BeanUtil.copyProperties(s, newR);
                            newR.setLicencePlate(strList.get(i));
                            addList.add(newR);
                        }
                    }
                }
            }
        });
        //如果没有初始延时
        JtsjDto driverParam = new JtsjDto();
        driverParam.setStartDate(startDate);
        driverParam.setEndDate(endDate.plusDays(1));
        List<Integer> driverIds = BaseList.stream().map(s -> s.getDriveId()).collect(Collectors.toList());
        driverIds.removeAll(HasHoursDriverIds);
        driverParam.setDriverIds(driverIds);
        if(driverIds!=null && driverIds.size()>0){
            List<JtsjDto> driverList = vDfdwDzccTCclcdetailMapper.getSjqdForMonth(driverParam);
            Map<Integer, List<String>> driverPlateMap = driverList.stream()
                    .collect(Collectors.groupingBy(JtsjDto::getDriverId,
                            Collectors.mapping(JtsjDto::getLicencePlate, Collectors.toList())));

            for (DfdwDzccTWeek record : BaseList) {
                List<String> plateList = driverPlateMap.getOrDefault(record.getDriveId(), new ArrayList<>());
                if(CollectionUtil.isNotEmpty(plateList)){
                    record.setLicencePlate(plateList.get(0));
                    int size = plateList.size();
                    if(size>1){
                        for (int i = 1; i < size; i++) {
                            DfdwDzccTWeek newR=new DfdwDzccTWeek();
                            BeanUtil.copyProperties(record,newR);
                            newR.setLicencePlate(plateList.get(i));
                            addList.add(newR);
                        }
                    }
                }
            }
        }
        BaseList.addAll(addList);
        //重复车牌号不要
        BaseList= new ArrayList<>(new HashSet<>(BaseList));
        //将records按照driverId排序
        BaseList.sort(Comparator.comparing(DfdwDzccTWeek::getDriveId));
        List<DfdwDzccTWeek> list = new ArrayList<>();
        if (dfdwDzccTWeek.getPageSize() == -1) {
            list = BaseList;
        } else {
            list = BaseList.subList(
                    (dfdwDzccTWeek.getPageNum() - 1) * dfdwDzccTWeek.getPageSize(),
                    Math.min(
                            (dfdwDzccTWeek.getPageNum() - 1) * dfdwDzccTWeek.getPageSize() + dfdwDzccTWeek.getPageSize(),
                            BaseList.size()
                    ));
        }
        //如果不是审批通过的，增加延时数不显示
        list.stream().forEach(s->{
            Integer approveState = s.getApproveState();
            if(approveState==null || approveState!=2){
                s.setAppendDelayHour(BigDecimal.ZERO);
            }
        });
        IPage<DfdwDzccTWeek> iPage=new Page<>();
        iPage.setRecords(list);
        iPage.setSize(list.size());
        iPage.setTotal(BaseList.size());
        iPage.setCurrent(dfdwDzccTWeek.getPageNum());
        return iPage;
    }

    @Override
    public List<DfdwDzccTWeekMain> getWeeks(DfdwDzccTWeek dfdwDzccTWeek) {
        List<DfdwDzccTWeekMain> weekMainList = baseMapper.getWeeks(dfdwDzccTWeek);
        return weekMainList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> editAppendDelayHour(DfdwDzccTWeek dfdwDzccTWeek) {

        //协同办公-电子出车-特殊信息-审核
        //协同办公-电子出车-特殊信息-申请
        Integer applyCount = this.baseMapper.hasRole("协同办公-电子出车-特殊信息-申请",dfdwDzccTWeek.getDzccPersonEntity().getId());
        Integer reviewerCount = this.baseMapper.hasRole("协同办公-电子出车-特殊信息-审核",dfdwDzccTWeek.getDzccPersonEntity().getId());
        if(applyCount == 0 && reviewerCount == 0){
           return Result.error("没有操作权限！");
        }
        List<DfdwTDictData> list = dfdwTDictDataService.getDictDataByType("dzcc-jtsjqd-delayHour");
        DfdwTDictData applyDictData = list.stream().filter(e->"申请角色".equals(e.getLabel())).findFirst().orElse(null);
        DfdwTDictData reviewerDictData = list.stream().filter(e->"审核角色".equals(e.getLabel())).findFirst().orElse(null);
        BigDecimal warnPre = null;
        if (applyCount > 0 && applyDictData != null ){
            warnPre = new BigDecimal(applyDictData.getValue());
        }
        if (reviewerCount > 0 && reviewerDictData != null ){
            warnPre = new BigDecimal(reviewerDictData.getValue());
        }
        //获取修改之前的数据
        DfdwDzccTWeek afterData = this.baseMapper.selectById(dfdwDzccTWeek.getId());
        BigDecimal afterAppendDelayHour = afterData.getAppendDelayHour() == null ? BigDecimal.ZERO : afterData.getAppendDelayHour();

        //计算当前年月 周期 下面的所有增加延时总数 和初始延时总数
        VDfdwDzccTWeek dfdwDzccTWeek1 = vDfdwDzccTWeekMapper.sumOringOvertimeHoursAndappendDelayHour(dfdwDzccTWeek.getYear(),dfdwDzccTWeek.getMonth(),dfdwDzccTWeek.getWeek());
        BigDecimal sumOringOvertimeHours = dfdwDzccTWeek1.getOringOvertimeHours(); // 数据库 初始延时数总和
        BigDecimal sumAppend = dfdwDzccTWeek1.getAppendDelayHour()== null ? BigDecimal.ZERO : dfdwDzccTWeek1.getAppendDelayHour() ; // 数据库 已添加延时数总和
        //本次添加延时数和数据库添加延时数总和 相加，比较是否超出设置阈值
        BigDecimal endAppend = dfdwDzccTWeek.getAppendDelayHour().add(sumAppend).subtract(afterAppendDelayHour);
        //计算最多能添加多少 延时数
        BigDecimal maxAddValue = null;
        if (warnPre != null) {
            maxAddValue = warnPre.divide(new BigDecimal("100")).multiply(sumOringOvertimeHours);
            if(endAppend.compareTo(maxAddValue) > 0 ){
                return Result.error("超出与预设值！最多还能添加"+maxAddValue.subtract(sumAppend));
            }
        }
        this.baseMapper.updateById(dfdwDzccTWeek);
        return Result.ok();
    }


    @Override
    public void operaDriverToWeekTab(DfdwDzccTWeek param) {
        //获取当月所有已有的司机数据
        List<Integer> ids = this.baseMapper.getCurrentMonthDriverId(param.getYear(),param.getMonth());
        List<DzccVPerson> list = vDfdwDzccTCclcdetailMapper.GetDriverList( new JtsjDto());
        List<DfdwTDictData> dictList = dfdwTDictDataService.getDictDataByType("dzcc-jtsjqd-setup");
        String value = "";
        if (!dictList.isEmpty()){
            value = dictList.get(0).getValue();
        }
        boolean saveDateRanges = false;
        List<DfdwDzccTWeekMain> dateRanges;
        //判断主表是否有 如果有直接读取主表数据
        dateRanges = dfdwDzccTWeekMainService.list(new LambdaQueryWrapper<DfdwDzccTWeekMain>().eq(DfdwDzccTWeekMain::getYear,param.getYear()).eq(DfdwDzccTWeekMain::getMonth,param.getMonth()));

        LocalDate currentDate = LocalDate.of(param.getYear(), param.getMonth(), 1);
        if(dateRanges.isEmpty()){
            saveDateRanges = true;
            //根据设置的周期数进行拆分
            dateRanges = getSplitDates(currentDate, Integer.parseInt(value));
        }
        //保存好数据
        if (saveDateRanges){
            for (DfdwDzccTWeekMain dateRange : dateRanges){
                dfdwDzccTWeekMainService.save(dateRange);
            }
        }

        int index;
        //查询出所有的司机
        for (DzccVPerson item : list) {
            index = 1;
            if( ! ids.isEmpty() && ids.contains(item.getId()) ){
                continue;
            }

            //拆分
            for (DfdwDzccTWeekMain dateRange : dateRanges){
                //有指定生成周期的话优先判断指定周期
                if (param.getWeek() != null && index != param.getWeek()){
                    continue;
                }
                DfdwDzccTWeek dfdwDzccTWeek = new DfdwDzccTWeek();
                dfdwDzccTWeek.setDriveId(item.getId());
                dfdwDzccTWeek.setDriveName(item.getRealName());
                dfdwDzccTWeek.setYear(currentDate.getYear());
                dfdwDzccTWeek.setMonth(currentDate.getMonthValue());
                dfdwDzccTWeek.setWeek(index);
                dfdwDzccTWeek.setStartTime(dateRange.getStartTime());
                dfdwDzccTWeek.setEndTime(dateRange.getEndTime());
                dfdwDzccTWeek.setWeekMainId(dateRange.getId());
                //计算两个日期差异
//                dfdwDzccTWeek.setWeekDays((int) ChronoUnit.DAYS.between(dateRange.getStartTime(), dateRange.getEndTime()));
                dfdwDzccTWeek.setWeekDays(dateRange.getWeekDays());
                dfdwDzccTWeek.setApproveState(0);
                this.baseMapper.insert(dfdwDzccTWeek);
                index ++;
            }

        }




    }

    /**
     *
     * @param currentDate 开始日期
     * @param splitValue 间隔天数 字典取值
     * @return
     */
    public static List<DfdwDzccTWeekMain> getSplitDates(LocalDate currentDate, int splitValue) {
        List<DfdwDzccTWeekMain> dateRanges = new ArrayList<>();

        // 获取当前月份的第一天和最后一天
        LocalDate startOfMonth = currentDate.withDayOfMonth(1);
        LocalDate endOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());

        LocalDate current = startOfMonth;
        int _index = 1;
        while (!current.isAfter(endOfMonth)) {
            // 计算当前区间的结束日期
            LocalDate endOfRange = current.plusDays(splitValue - 1);
            if (endOfRange.isAfter(endOfMonth)) {
                endOfRange = endOfMonth; // 如果超出本月最后一天，则调整
            }

            // 将当前区间添加到列表中
            DfdwDzccTWeekMain addData = new DfdwDzccTWeekMain();
            addData.setYear(current.getYear());
            addData.setMonth(current.getMonthValue());
            addData.setWeek(_index);
            addData.setStartTime(current);
            addData.setEndTime(endOfRange);
            addData.setWeekDays(splitValue);
            addData.setCreateTime(new Date());
            dateRanges.add(addData);
            // 移动到下一个区间的开始日期
            current = endOfRange.plusDays(1);
            _index ++;
        }

        return dateRanges;
    }

    @Override
    public List<VDfdwDzccTWeek> getDriverList(DfdwDzccTWeek dfdwDzccTWeek) {
        return vDfdwDzccTWeekMapper.selectList(new LambdaQueryWrapper<VDfdwDzccTWeek>()
                .eq(dfdwDzccTWeek.getYear() != null ,VDfdwDzccTWeek::getYear,dfdwDzccTWeek.getYear())
                .eq(dfdwDzccTWeek.getMonth() != null,VDfdwDzccTWeek::getMonth,dfdwDzccTWeek.getMonth())
                .eq(dfdwDzccTWeek.getWeek() != null ,VDfdwDzccTWeek::getWeek,dfdwDzccTWeek.getWeek())
                .eq(dfdwDzccTWeek.getWeekMainId() != null,VDfdwDzccTWeek::getWeekMainId,dfdwDzccTWeek.getWeekMainId())
                .eq(StringUtils.checkValNotNull(dfdwDzccTWeek.getDriveName()),VDfdwDzccTWeek::getDriveName,dfdwDzccTWeek.getDriveName())
                .eq((dfdwDzccTWeek.getApproveState() != null && dfdwDzccTWeek.getApproveState() != -1),VDfdwDzccTWeek::getApproveState,dfdwDzccTWeek.getApproveState())
        );
    }
}




