package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCcdd;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTCcddService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCcddMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCDD(电子出车-后续新增的出车地点日志)】的数据库操作Service实现
* @createDate 2024-12-17 15:13:37
*/
@Service
public class DfdwDzccTCcddServiceImpl extends ServiceImpl<DfdwDzccTCcddMapper, DfdwDzccTCcdd>
    implements DfdwDzccTCcddService{

}




