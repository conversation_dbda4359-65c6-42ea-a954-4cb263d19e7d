package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclc;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCLC(电子出车单LC)】的数据库操作Mapper
* @createDate 2022-10-25 14:16:03
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTCclc
*/
public interface DfdwDzccTCclcMapper extends BaseMapper<DfdwDzccTCclc> {

    List<DfdwDzccTCclc> getCcdByDriveIdAndTime(@Param("driveId") Integer driveId, @Param("date") String date);

    List<DfdwDzccTCclc> getListCcdXq(List<Integer> integerList);
}




