package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子出车-后续新增的出车地点日志
 * @TableName DFDW_DZCC_T_CCDD
 */
@TableName(value ="DFDW_DZCC_T_CCDD")
@Data
public class DfdwDzccTCcdd implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * cclc主键ID
     */
    private Integer lcid;

    /**
     * 省份
     */
    private Integer provinces;

    /**
     * 市
     */
    private Integer city;

    /**
     * 区
     */
    private Integer area;

    /**
     * 区域中文
     */
    private String areatext;

    /**
     * 申请人Id
     */
    private Integer applyuserid;

    /**
     * 申请人名称
     */
    private String applyusername;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间（申请时间）
     */
    private Date createtime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DfdwDzccTCcdd other = (DfdwDzccTCcdd) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLcid() == null ? other.getLcid() == null : this.getLcid().equals(other.getLcid()))
            && (this.getProvinces() == null ? other.getProvinces() == null : this.getProvinces().equals(other.getProvinces()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getArea() == null ? other.getArea() == null : this.getArea().equals(other.getArea()))
            && (this.getAreatext() == null ? other.getAreatext() == null : this.getAreatext().equals(other.getAreatext()))
            && (this.getApplyuserid() == null ? other.getApplyuserid() == null : this.getApplyuserid().equals(other.getApplyuserid()))
            && (this.getApplyusername() == null ? other.getApplyusername() == null : this.getApplyusername().equals(other.getApplyusername()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getCreatetime() == null ? other.getCreatetime() == null : this.getCreatetime().equals(other.getCreatetime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLcid() == null) ? 0 : getLcid().hashCode());
        result = prime * result + ((getProvinces() == null) ? 0 : getProvinces().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getArea() == null) ? 0 : getArea().hashCode());
        result = prime * result + ((getAreatext() == null) ? 0 : getAreatext().hashCode());
        result = prime * result + ((getApplyuserid() == null) ? 0 : getApplyuserid().hashCode());
        result = prime * result + ((getApplyusername() == null) ? 0 : getApplyusername().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getCreatetime() == null) ? 0 : getCreatetime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", lcid=").append(lcid);
        sb.append(", provinces=").append(provinces);
        sb.append(", city=").append(city);
        sb.append(", area=").append(area);
        sb.append(", areatext=").append(areatext);
        sb.append(", applyuserid=").append(applyuserid);
        sb.append(", applyusername=").append(applyusername);
        sb.append(", remark=").append(remark);
        sb.append(", createtime=").append(createtime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}