package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.util.AliyunOSSUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.UUID;

/**
 * 电子出车通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class DzccCommonController {

    /**
     * 电子出车-PC-通用-文件上传
     */
    @PostMapping("/dzccCommon/upload")
    public Result<Object> uploadFile(MultipartFile file, String fileType)
    {
        try{
            LocalDate localDate = LocalDate.now();
            String YEAR_MONTH = localDate.getYear() + "/" + localDate.getMonthValue() + "/";
            String path ="szyy/dzcc"  + (fileType==null?"/":"/"+fileType+"/") + YEAR_MONTH;
            String hz = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
            String fileName = UUID.randomUUID() + "." + hz;
            String url = path +  AliyunOSSUtils.uploadFile(path, fileName, file);
            Result<Object> ajax = Result.ok();
            ajax.setResult(url);
            return ajax;
        }catch (Exception e){
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    /**
     * 电子出车-PC-通用-成功
     */
    @PostMapping("/dzccCommon/ok")
    public Result<Object> ok()
    {
        return Result.ok();
    }
}
