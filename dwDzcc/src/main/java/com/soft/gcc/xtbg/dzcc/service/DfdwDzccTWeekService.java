package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;

import java.util.List;

/**
* <AUTHOR> @description 针对表【DFDW_DZCC_T_WEEK(电子出车-周期（延时申请）)】的数据库操作Service
* @createDate 2024-12-16 10:47:16
*/
public interface DfdwDzccTWeekService extends IService<DfdwDzccTWeek> {
    IPage<DfdwDzccTWeek> listPage(DfdwDzccTWeek dfdwDzccTWeek);
    IPage<DfdwDzccTWeek> getWorkSummaryList(DfdwDzccTWeek dfdwDzccTWeek) throws Exception;

    /**
     * 获取周期下拉框 （根据年份+月份获取
     * @param dfdwDzccTWeek
     * @return
     */
    List<DfdwDzccTWeekMain> getWeeks(DfdwDzccTWeek dfdwDzccTWeek);

    List<VDfdwDzccTWeek> getDriverList(DfdwDzccTWeek dfdwDzccTWeek);

    Result<Object> editAppendDelayHour(DfdwDzccTWeek dfdwDzccTWeek);

    /**
     * 给 DFDW_DZCC_T_WEEK 表同步数据
     */
    void operaDriverToWeekTab(DfdwDzccTWeek param);
}
