package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCdzgl;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CDZGL(电子出车-车队长分配表)】的数据库操作Mapper
* @createDate 2022-10-17 16:02:59
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTCdzgl
*/
public interface DfdwDzccTCdzglMapper extends BaseMapper<DfdwDzccTCdzgl> {

    List<DfdwDzccTCdzgl> GetCDZPZList(
            @Param("deptId") int deptId,
            @Param("name") String name,
            @Param("qx") Integer dzccQx,
            @Param("id") Integer id,
            @Param("personTypeId") Integer personTypeId
    );

    void saveBatch(@Param("addList") List<DfdwDzccTCdzgl> addList);
}




