package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 
* @TableName V_DFDW_DZCC_T_CCDTIME
*/
@TableName(value ="V_DFDW_DZCC_T_CCDTIME")
@Data
public class VDfdwDzccTCcdtime implements Serializable {


    /**
    * 
    */
    @TableField(value = "ywId")
    @JSONField(name = "ywId")
    
    private Integer ywId;
    /**
    * 
    */
    @TableField(value = "startTime")
    @JSONField(name = "startTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
    * 
    */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
