package com.soft.gcc.xtbg.dzcc.mapper;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCclcdetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.entity.JtsjDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CCLCDetail】的数据库操作Mapper
* @createDate 2023-11-16 09:43:56
* @Entity com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCclcdetail
*/
public interface VDfdwDzccTCclcdetailMapper extends BaseMapper<VDfdwDzccTCclcdetail> {

    List<VDfdwDzccTCclcdetail> getCCLCDetailList(
            @Param(value = "type") Integer type, @Param(value = "driveId") Integer driveId,
            @Param(value = "start") String start,@Param(value = "end") String end);

    List<JtsjDto> getSjqx(@Param(value = "jtsjDto") JtsjDto jtsjDto);

    List<JtsjDto> getSjqdForMonth(@Param(value = "jtsjDto") JtsjDto jtsjDto);

    List<DzccVPerson> GetDriverList(@Param(value = "jtsjDto") JtsjDto jtsjDto );
}




