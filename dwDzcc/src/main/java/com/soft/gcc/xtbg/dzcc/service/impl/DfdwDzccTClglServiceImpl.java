package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DzccPerson;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.*;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import com.soft.gcc.xtbg.base.controller.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CLGL(电子出车-车辆管理)】的数据库操作Service实现
 * @createDate 2022-10-19 14:11:29
 */
@Service
public class DfdwDzccTClglServiceImpl extends ServiceImpl<DfdwDzccTClglMapper, DfdwDzccTClgl>
        implements IDfdwDzccTClglService {
    @Autowired
    IDfdwDzccTCdzglService cdzglService;
    @Autowired
    IDzccLzclTCarRentService lzclTCarRentService;
    @Autowired
    DzccCarService dzccCarService;
    @Autowired
    IDfdwDzccTClgldetailService clgldetailService;
    @Autowired
    DzccPersonMapper personMapper;
    @Autowired
    DfdwDzccTClbgMapper clbgMapper;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;
    @Autowired
    DfdwDzccTUnitlogMapper unitlogMapper;
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Result<Object> GetCLGLList(DzccPersonEntity dzccPersonEntity, DfdwDzccTClgl clgl) {
        try {
            IPage<DfdwDzccTClgl> list = new Page<>();
            list.setCurrent(clgl.getPageNum());
            list.setSize(clgl.getPageSize());
            int startNum = (clgl.getPageNum() - 1) * clgl.getPageSize() + 1;
            int endNum = clgl.getPageNum() * clgl.getPageSize();

            // 车辆数据
            List<DfdwDzccTClgl> carRents = baseMapper.selectCarRentNew(dzccPersonEntity);
            List<DfdwDzccTClgl> saveList = new ArrayList<>();
            // 得到办公点
            List<DfdwDzccTClbg> clbgs = clbgMapper.selectFrist();
            for (DfdwDzccTClgl car : carRents) {
                if (car.getId() == null) {
                    if (car.getGroupId() != null) {
                        for (DfdwDzccTClbg clbg : clbgs) {
                            if (car.getGroupId().equals(clbg.getGroupId())) {
                                car.setUnitId(clbg.getId());
                                car.setUnitName(clbg.getUnitName());
                                break;
                            }
                        }
                    }
                    car.setEditUserId(dzccPersonEntity.getId());
                    car.setCreateTime(new Date());
                    saveList.add(car);
                }
            }
            // 数据量过大，分批插入
            if (saveList.size() > 0) {
                int a = 0;
                int b = 160;
                while (saveList.size() >= a) {
                    if (saveList.size() < b) {
                        b = saveList.size();
                    }
                    List<DfdwDzccTClgl> saves = saveList.subList(a, b);
                    baseMapper.saveBatch1(saves);
                    a += 160;
                    b += 160;
                }
            }
            carRents = baseMapper.selectCarRent(dzccPersonEntity, clgl, startNum, endNum);
            list.setTotal(baseMapper.countCarRent(dzccPersonEntity, clgl));
            list.setRecords(carRents);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetRentList(DfdwDzccTClgl clgl) {
        try {
            LocalDate nowDate = LocalDate.now();
            List<DzccLzclTCarRent> carRents = lzclTCarRentService.list(new LambdaQueryWrapper<DzccLzclTCarRent>()
                    .eq(DzccLzclTCarRent::getCarId, clgl.getCarId())
                    .eq(DzccLzclTCarRent::getUseDeptId, clgl.getGroupId())
                    .ge(DzccLzclTCarRent::getRealReturnDate, nowDate)
                    .orderByAsc(DzccLzclTCarRent::getRealReturnDate));
            return Result.ok(carRents);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetDetailList(Map<String, String> map) {
        try {
            Integer id = ParseUtil.tryParseInt(map.get("id"));
            List<DfdwDzccTClgldetail> details = clgldetailService.list(new LambdaQueryWrapper<DfdwDzccTClgldetail>()
                    .eq(DfdwDzccTClgldetail::getCid, id)
                    .orderByDesc(DfdwDzccTClgldetail::getStartDate)
            );
            return Result.ok(details);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetDriverList(Map<String, String> map) {
        try {
            Integer groupId = ParseUtil.tryParseInt(map.get("groupId"));
            List<DzccPerson> persons = personMapper.GetDriverList(groupId);
            return Result.ok(persons);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> AddClglDetail(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        try {
            DfdwDzccTClgldetail clgldetail = new DfdwDzccTClgldetail();
            clgldetail.setCarId(ParseUtil.tryParseInt(map.get("carId")));
            clgldetail.setCid(ParseUtil.tryParseInt(map.get("cid")));
            clgldetail.setDriverId(ParseUtil.tryParseInt(map.get("driverId")));
            clgldetail.setDriverName(ParseUtil.tryParseString(map.get("driverName")));
            clgldetail.setGroupId(ParseUtil.tryParseInt(map.get("groupId")));
            String startDate = ParseUtil.tryParseString(map.get("startDate"));
            if ("".equals(startDate)) {
                return Result.error("请选择生效日期！");
            }
            clgldetail.setStartDate(formatter.parse(startDate));
//            List<DzccLzclTCarRent> carRents = lzclTCarRentService.list(new LambdaQueryWrapper<DzccLzclTCarRent>()
//                    .eq(DzccLzclTCarRent::getCarId, clgldetail.getCarId())
//                    .eq(DzccLzclTCarRent::getUseDeptId, clgldetail.getGroupId())
//                    .le(DzccLzclTCarRent::getContractStartDate, clgldetail.getStartDate())
//                    .ge(DzccLzclTCarRent::getRealReturnDate, clgldetail.getStartDate())
//            );
//            List<DzccCar> cars = baseMapper.getCQCL(clgldetail.getCarId(), clgldetail.getGroupId());
//            if (carRents.size() == 0 && cars.size() == 0) {
//                return Result.error("该车不是产权车或生效时间不在该车在此部门的租赁时间内！");
//            }
            if (clgldetail.getDriverId() == null) {
                return Result.error("请选择驾驶员！");
            }
            List<DfdwDzccTClgldetail> list = clgldetailService.list(new LambdaQueryWrapper<DfdwDzccTClgldetail>()
                    .eq(DfdwDzccTClgldetail::getCarId, clgldetail.getCarId())
                    .eq(DfdwDzccTClgldetail::getStartDate, clgldetail.getStartDate())
            );
            if (list.size() > 0) {
                return Result.error("当天该车只能有一条记录！");
            } else {
                clgldetail.setEditId(dzccPersonEntity.getId());
                clgldetail.setCreateTime(new Date());
                clgldetailService.save(clgldetail);
                return Result.ok("添加成功！");
            }
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> EditClglDetail(DzccPersonEntity person, Map<String, String> map) {
        try {
            DfdwDzccTClgldetail clgldetail = new DfdwDzccTClgldetail();
            clgldetail.setId(ParseUtil.tryParseInt(map.get("id")));
            clgldetail.setCarId(ParseUtil.tryParseInt(map.get("carId")));
            clgldetail.setCid(ParseUtil.tryParseInt(map.get("cid")));
            clgldetail.setDriverId(ParseUtil.tryParseInt(map.get("driverId")));
            clgldetail.setDriverName(ParseUtil.tryParseString(map.get("driverName")));
            clgldetail.setGroupId(ParseUtil.tryParseInt(map.get("groupId")));
            clgldetail.setCreateTime(formatter.parse(ParseUtil.tryParseString(map.get("createTime"))));
            String startDate = ParseUtil.tryParseString(map.get("startDate"));
            if ("".equals(startDate)) {
                return Result.error("请选择生效日期！");
            }
            clgldetail.setStartDate(formatter.parse(startDate));
//            List<DzccLzclTCarRent> carRents = lzclTCarRentService.list(new LambdaQueryWrapper<DzccLzclTCarRent>()
//                    .eq(DzccLzclTCarRent::getCarId, clgldetail.getCarId())
//                    .eq(DzccLzclTCarRent::getUseDeptId, clgldetail.getGroupId())
//                    .le(DzccLzclTCarRent::getContractStartDate, clgldetail.getStartDate())
//                    .ge(DzccLzclTCarRent::getRealReturnDate, clgldetail.getStartDate())
//            );
//            List<DzccCar> cars = baseMapper.getCQCL(clgldetail.getCarId(), clgldetail.getGroupId());
//            if (carRents.size() == 0 && cars.size() == 0) {
//                return Result.error("该车不是产权车或生效时间不在该车在此部门的租赁时间内！");
//            }
            if (clgldetail.getDriverId() == null) {
                return Result.error("请选择驾驶员！");
            }
            List<DfdwDzccTClgldetail> list = clgldetailService.list(new LambdaQueryWrapper<DfdwDzccTClgldetail>()
                            .eq(DfdwDzccTClgldetail::getCarId, clgldetail.getCarId())
                            .eq(DfdwDzccTClgldetail::getStartDate, clgldetail.getStartDate())
            );
            if (list.size() > 0) {
                return Result.error("当天该车只能有一条记录！");
            } else {
                clgldetail.setEditId(person.getId());
                clgldetail.setUpdateTime(new Date());
                clgldetailService.updateById(clgldetail);
                return Result.ok("修改成功！");
            }
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> DeleteClglDetail(int id) {
        try {
            DfdwDzccTClgldetail clgldetail = clgldetailService.getById(id);
            if (clgldetail != null) {
                if (clgldetail.getStartDate().before(new Date())) {
                    return Result.error("往期数据不能删除！");
                }
            }
            clgldetailService.removeById(id);
            return Result.ok("删除成功");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> EditUnit(Map<String, String> map) {
        try {
            String ids = ParseUtil.tryParseString(map.get("ids"));
            List<Integer> idList = Arrays.stream(ids.split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            int unitId = ParseUtil.tryParseInt(map.get("unitId"));
            if (unitId == 0) {
                return Result.error("请选择办公地点！");
            }
            String unitName = ParseUtil.tryParseString(map.get("unitName"));
            UpdateWrapper<DfdwDzccTClgl> wrapper = Wrappers.update();
            wrapper.lambda()
                    .set(DfdwDzccTClgl::getUnitId, unitId)
                    .set(DfdwDzccTClgl::getUnitName, unitName)
                    .in(DfdwDzccTClgl::getId, idList);
            this.update(null, wrapper);
            if (idList.size() > 0) {
                unitlogMapper.addStop(idList);
            }
            return Result.ok("修改成功");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> EditStop(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        try {
            String ids = ParseUtil.tryParseString(map.get("ids"));
            List<Integer> idList = Arrays.stream(ids.split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            String stopName = ParseUtil.tryParseString(map.get("stopName"));
            if ("".equals(stopName)) {
                return Result.error("停车点名称不能为空！");
            }
            String stopGPS = ParseUtil.tryParseString(map.get("stopGPS"));
            if ("".equals(stopGPS)) {
                return Result.error("GPS不能为空！");
            }
            String[] gps = stopGPS.split(",");
            BigDecimal longitude = new BigDecimal(gps[0]);
            BigDecimal latitude = new BigDecimal(gps[1]);
            UpdateWrapper<DfdwDzccTClgl> wrapper = Wrappers.update();
            wrapper.lambda()
                    .set(DfdwDzccTClgl::getStopName, stopName)
                    .set(DfdwDzccTClgl::getStopGPS, stopGPS)
                    .set(DfdwDzccTClgl::getLongitude, longitude)
                    .set(DfdwDzccTClgl::getLatitude, latitude)
                    .set(DfdwDzccTClgl::getUpdateTime, new Date())
                    .set(DfdwDzccTClgl::getEditUserId, dzccPersonEntity.getId())
                    .in(DfdwDzccTClgl::getId, idList);
            this.update(null, wrapper);
            if (idList.size() > 0) {
                unitlogMapper.addStop(idList);
            }
            return Result.ok("修改成功");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> SelectRepeatCar(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        List<DfdwDzccTClgl> list = this.list(new LambdaQueryWrapper<DfdwDzccTClgl>()
                .select(DfdwDzccTClgl::getCarId, DfdwDzccTClgl::getGroupId)
                .groupBy(DfdwDzccTClgl::getCarId, DfdwDzccTClgl::getGroupId)
                .having("count(1) > 1")
        );
        List<DfdwDzccTClgl> clglList = baseMapper.getRepeatCar();
        Map<String, Object> result = new HashMap<>();
        result.put("list", list);
        result.put("clgl", clglList);
        return Result.ok(result);
    }

    @Override
    public void DeleteClgl(Integer id) {
        baseMapper.deleteById(id);
    }
}




