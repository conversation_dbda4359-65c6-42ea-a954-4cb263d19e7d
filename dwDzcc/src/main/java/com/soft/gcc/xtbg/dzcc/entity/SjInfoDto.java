package com.soft.gcc.xtbg.dzcc.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 司机详情
 * @date 2024-12-12 17:24:41
 */
@Data
public class SjInfoDto {
    private String week;
    private String driverName;
    private String licencePlate;
    private BigDecimal overtimeHoursAll;
    /**
     * 初始延时
     */
    private BigDecimal overtimeHours;

    /**
     * 审批延时
     */
    private BigDecimal overtimeHoursApp;

    private List<JtsjDto> jtsjDtos;
}
