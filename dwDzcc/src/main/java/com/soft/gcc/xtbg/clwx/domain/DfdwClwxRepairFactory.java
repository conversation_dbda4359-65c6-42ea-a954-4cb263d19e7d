package com.soft.gcc.xtbg.clwx.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.soft.gcc.xtbg.dzcc.domain.DzccBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 车辆维修（新）-维修厂家
 * <AUTHOR>
 * @TableName DFDW_CLWX_REPAIR_FACTORY
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="DFDW_CLWX_REPAIR_FACTORY")
@Data
public class DfdwClwxRepairFactory extends DzccBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 送修厂家
     */
    @TableField(value = "repairFactoryName")
    private String repairFactoryName;

    /**
     * 单位Id
     */
    @TableField(value = "deptId")
    private Integer deptId;

    /**
     * 单位Id
     */
    @TableField(value = "topDeptId")
    private Integer topDeptId;

    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "updateId")
    private Integer updateId;

    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    private Date updateTime;

    /**
     * 启用状态 1启用 0 未启用
     */
    @TableField(value = "status")
    private Integer status;

    @TableField(exist = false)
    private List<Integer> ids;

    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}