package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 
* @TableName DFDW_DZCC_T_CARPERSON
*/
@TableName(value ="DFDW_DZCC_T_CARPERSON", autoResultMap = true)
@Data
public class DfdwDzccTCarperson implements Serializable {

    /**
    * 主键：
    */
    
    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 车辆id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")
    
    private Integer carId;
    /**
    * 人员id
    */
    @TableField(value = "personId")
    @JSONField(name = "personId")
    
    private Integer personId;
    /**
    * 实际还车时间
    */
    @TableField(value = "realReturnDate")
    @JSONField(name = "realReturnDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realReturnDate;
    /**
    * 
    */
    @TableField(value = "editTime")
    @JSONField(name = "editTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date editTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
