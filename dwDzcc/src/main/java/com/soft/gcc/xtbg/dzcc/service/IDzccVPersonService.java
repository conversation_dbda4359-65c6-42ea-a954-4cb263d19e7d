package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccPerson;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【vPerson(vPerson)】的数据库操作Service
* @createDate 2022-10-21 11:00:43
*/
public interface IDzccVPersonService extends IService<DzccVPerson> {
    Result<Object> GetDriverList(DzccPersonEntity person, DzccVPerson vPerson);

    Result<Object> EditDriverPhone(DzccVPerson vPerson);

    Result<Object> InsertDriver(DzccPerson person);

    Result<Object> GetCDZList(IPage<DzccVPerson> page, int personTypeId, String name, int manageGroupId, int deptId);

    Result<Object> GetPersonList(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetGroupList(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetPersonRoleById(Map<String, String> map);

    Result<Object> GetRoles();

    Result<Object> EditRoles(Map<String, String> map);
}
