package com.soft.gcc.xtbg.dzcc.mapper;

import com.soft.gcc.xtbg.dzcc.domain.LzclTCarStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Status(零租车辆-车辆状态表)】的数据库操作Mapper
* @createDate 2024-10-17 09:49:40
* @Entity com.soft.gcc.xtbg.dzcc.domain.LzclTCarStatus
*/
public interface LzclTCarStatusMapper extends BaseMapper<LzclTCarStatus> {

    List<LzclTCarStatus> selectListByCarIdAndDate(
            @Param("carId") Integer carId,
            @Param("driveId") Integer driveId,
            @Param("topGroupId") Integer topGroupId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
}




