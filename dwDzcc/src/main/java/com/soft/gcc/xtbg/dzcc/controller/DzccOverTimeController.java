package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTOverTime;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccOverTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


@RequestMapping("/dzcc/overTime")
@RestController
public class DzccOverTimeController extends DzccBaseController {

    @Autowired
    DfdwDzccOverTimeService dfdwDzccOverTimeService;



    /**
     * 新增
     * @return
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermi('NDWCC01JBF03QX02')")
    public Result<Object> createOverTime(@RequestBody DfdwDzccTOverTime dfdwDzccTOverTime) {
        int id =  dfdwDzccOverTimeService.createOverTime(dfdwDzccTOverTime);
        return Result.ok(id) ;
    }


    /**
     * 删除
     * @return
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('NDWCC01JBF03QX03')")
    public Result<Object> deleteOverTime(@RequestParam("id") Long id) {
        dfdwDzccOverTimeService.deleteOverTime(id);
        return Result.ok();
    }

    /**
     * 修改
     * @return
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('NDWCC01JBF03QX04')")
    public Result<Object> updateOverTime(@RequestBody DfdwDzccTOverTime dfdwDzccTOverTime) {
        dfdwDzccOverTimeService.updateOverTime(dfdwDzccTOverTime);
        return Result.ok();
    }


    /**
     * 获取加班计算规则page
     * @return
     */
    @PostMapping("/getListPage")
    @PreAuthorize("@ss.hasPermi('NDWCC01JBF03QX01')")
    public Result<Object> getListPage(@RequestBody DfdwDzccTOverTime dfdwDzccTOverTime) {
        IPage<DfdwDzccTOverTime> overTimeIPage =  dfdwDzccOverTimeService.getListPage(dfdwDzccTOverTime);
        return Result.ok(overTimeIPage) ;
    }
}
