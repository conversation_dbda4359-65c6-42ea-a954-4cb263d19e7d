package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCdzgl;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CDZGL(电子出车-车队长分配表)】的数据库操作Service
* @createDate 2022-10-17 16:02:59
*/
public interface IDfdwDzccTCdzglService extends IService<DfdwDzccTCdzgl> {
    Result<Object> EditCdz(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetCDZGroupList(DzccPersonEntity person, Map<String, String> map);
}
