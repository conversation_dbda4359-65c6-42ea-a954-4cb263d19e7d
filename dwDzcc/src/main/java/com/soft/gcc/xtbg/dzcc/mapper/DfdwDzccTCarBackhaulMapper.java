package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarBackhaul;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;
import org.apache.ibatis.annotations.Param;

/**
 * @Entity com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarBackhaul
 */
public interface DfdwDzccTCarBackhaulMapper extends BaseMapper<DfdwDzccTCarBackhaul> {
    IPage<DfdwDzccTCarBackhaul> GetBackHaulList(
            Page<DfdwDzccTCarBackhaul> page,
            @Param("groupId") Integer groupId,
            @Param("unitName") String unitName,
            @Param("licencePlate") String licencePlate,
            @Param("type") Integer type
    );
}




