package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriverinfo;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTDriveremployService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTDriverinfoService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.service.IDzccGroupitemService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 司机资质信息
 */
@RequestMapping("/dzcc/employInfo")
@RestController
public class DzccEmployInfoController extends DzccBaseController {
    private static final Logger log = LoggerFactory.getLogger(DzccEmployInfoController.class);
    @Autowired
    IDzccGroupitemService groupitemService;
    @Autowired
    IDfdwDzccTDriverinfoService driverinfoService;
    @Autowired
    IDfdwDzccTDriveremployService driveremployService;
    @Autowired
    TFileService tFileService;

    @RequestMapping("/GetGroupList")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX01')")
    public Result<Object> GetGroupList(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        boolean isInfoPerson = dzccPersonEntity.getQxs().contains(5);
        return this.groupitemService.GetGroupList(dzccPersonEntity, map, isInfoPerson);
    }

    @RequestMapping("/GetList")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX01')")
    public Result<Object> GetList(@RequestBody DfdwDzccTDriveremploy employ) {
        DzccPersonEntity person = getDzccPerson();
        return driverinfoService.GetList(person, employ,false);
    }

    @RequestMapping("/GetDict")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX01')")
    public Result<Object> GetDict() {
        return driverinfoService.GetDict();
    }

    @RequestMapping("/EditInfo")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX02')")
    public Result<Object> EditInfo(@RequestBody DfdwDzccTDriverinfo info) {
        try {
            List<DfdwDzccTDriverinfo> driverinfos = driverinfoService.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                    .eq(DfdwDzccTDriverinfo::getCertificateID, info.getCertificateID())
                    .like(DfdwDzccTDriverinfo::getType, "2")
            );
            if (driverinfos.size() > 0) {
                if (info.getId() == null) {
                    return Result.error("该身份证已存在！");
                }
                for (DfdwDzccTDriverinfo driverinfo : driverinfos) {
                    if (!info.getId().equals(driverinfo.getId())) {
                        return Result.error("该身份证已存在！");
                    }
                }
            }
            driverinfos = driverinfoService.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                    .eq(DfdwDzccTDriverinfo::getCertificateID, info.getCertificateID())
            );
            if (!driverinfos.isEmpty()) {
                info.setId(driverinfos.get(0).getId());
                if (!driverinfos.get(0).getType().contains("2")) {
                    info.setType(driverinfos.get(0).getType() + ",2");
                }
            } else {
                info.setId(null);
                info.setType("2");
            }
            if (info.getFileId1() != null) {
                info.setIdCardFrontFileId(Integer.parseInt(info.getFileId1()));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .eq(TFile::getId, Integer.parseInt(info.getFileId1()))
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 1)
                );
            }
            if (info.getFileId2() != null) {
                info.setIdCardReserveFileId(Integer.parseInt(info.getFileId2()));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .eq(TFile::getId, Integer.parseInt(info.getFileId2()))
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 2)
                );
            }
            if (info.getFileId3() != null) {
                info.setLicenseFrontFileId(Integer.parseInt(info.getFileId3()));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .eq(TFile::getId, Integer.parseInt(info.getFileId3()))
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 3)
                );
            }
            if (info.getFileId4() != null) {
                info.setLicenseReserveFileId(Integer.parseInt(info.getFileId4()));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .eq(TFile::getId, Integer.parseInt(info.getFileId4()))
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 4)
                );
            }
            if (info.getFileId5() != null) {
                info.setFacePhotoId(Integer.parseInt(info.getFileId5()));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .eq(TFile::getId, Integer.parseInt(info.getFileId5()))
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 5)
                );
            }
            if (StringUtils.isNotEmpty(info.getAccidentInsurancePolicyFile())) {
                List<String> filepath = Arrays.asList(info.getAccidentInsurancePolicyFile().split(","));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .in(TFile::getFilepath, filepath)
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 6)
                );
            }
            if (StringUtils.isNotEmpty(info.getLaborContractFile())) {
                List<String> filepath = Arrays.asList(info.getLaborContractFile().split(","));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .in(TFile::getFilepath, filepath)
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "zzxx")
                        .eq(TFile::getHjid, 7)
                );
            }
            driverinfoService.saveOrUpdate(info);
            return Result.ok(info.getId());
        } catch (Exception e) {
            log.error("电子出车-信息填报-聘用信息-信息修改报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/DeleteInfo")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX02')")
    public Result<Object> DeleteInfo(@RequestBody DfdwDzccTDriverinfo info) {
        if ("2".equals(info.getType())) {
            driverinfoService.removeById(info.getId());
            driveremployService.remove(new LambdaQueryWrapper<DfdwDzccTDriveremploy>().eq(DfdwDzccTDriveremploy::getPersonId, info.getId()));
        } else {
            driverinfoService.update(null, new LambdaUpdateWrapper<DfdwDzccTDriverinfo>()
                    .set(DfdwDzccTDriverinfo::getType, "1")
                    .eq(DfdwDzccTDriverinfo::getId, info.getId())
            );
            driveremployService.remove(new LambdaQueryWrapper<DfdwDzccTDriveremploy>().eq(DfdwDzccTDriveremploy::getPersonId, info.getId()));
        }

        return Result.ok();
    }

    @RequestMapping("/AddEmployPerson")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX03')")
    public Result<Object> AddEmployPerson(@RequestBody DfdwDzccTDriveremploy emoloy) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driveremployService.AddEmployPerson(dzccPersonEntity, emoloy);
    }

    @RequestMapping("/EditEmployPerson")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX03')")
    public Result<Object> EditEmployPerson(@RequestBody DfdwDzccTDriveremploy emoloy) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driveremployService.EditEmployPerson(dzccPersonEntity, emoloy);
    }

    @RequestMapping("/GetEmployList")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX03')")
    public Result<Object> GetEmployList(@RequestBody DfdwDzccTDriveremploy emoloy) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driveremployService.GetEmployList(dzccPersonEntity, emoloy);
    }

    @RequestMapping("/DeleteEmployPerson")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ03QX03')")
    public Result<Object> DeleteEmployPerson(@RequestBody DfdwDzccTDriveremploy emoloy) {
        driveremployService.removeById(emoloy.getId());
        return Result.ok();
    }

    @RequestMapping("/GetJsyxxList")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC02QX01')")
    public Result<Object> GetJsyxxList(@RequestBody DfdwDzccTDriverinfo info) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driverinfoService.GetJsyxxList(dzccPersonEntity, info);
    }

    @RequestMapping("/AddJsyxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC02QX02')")
    public Result<Object> AddJsyxx(@RequestBody DfdwDzccTDriverinfo info) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driverinfoService.AddJsyxx(dzccPersonEntity, info);
    }

    @RequestMapping("/EditJsyxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC02QX03')")
    public Result<Object> EditJsyxx(@RequestBody DfdwDzccTDriverinfo info) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driverinfoService.EditJsyxx(dzccPersonEntity, info);
    }

    @RequestMapping("/DeleteJsyxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC02QX04')")
    public Result<Object> DeleteJsyxx(@RequestBody DfdwDzccTDriverinfo info) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driverinfoService.DeleteJsyxx(dzccPersonEntity, info);
    }

    @RequestMapping("/UploadLoadJsyxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC02QX06')")
    public Result<Object> UploadLoadJsyxx(MultipartFile file) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driverinfoService.UploadLoadJsyxx(dzccPersonEntity, file);
    }

    @RequestMapping("/DownLoadJsyxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC02QX06')")
    public Result<Object> DownLoadJsyxx(@RequestBody DfdwDzccTDriverinfo info, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return driverinfoService.DownLoadJsyxx(dzccPersonEntity, info, response);
    }

    @RequestMapping("/DownLoadZzxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD05QX02')")
    public Result<Object> DownLoadZzxx(@RequestBody DfdwDzccTDriveremploy employ, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        XSSFWorkbook workbook = null;
        try {
            Result<Object> result = driverinfoService.GetList(person, employ, true);
            IPage<DfdwDzccTDriverinfo>  list0= (IPage<DfdwDzccTDriverinfo>) result.getResult();
            List<DfdwDzccTDriverinfo> list=list0.getRecords();
            list.stream().forEach(s->{
                Date driverExpiryDate = s.getDriverExpiryDate();
                SimpleDateFormat  sdf = new SimpleDateFormat("yyyy-MM-dd");
                String format = sdf.format(driverExpiryDate);
                s.setDriverExpiryDateStr(format);
            });
            int index = 0;

            workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue( "资质信息报表");
            sheet.setDefaultColumnWidth(23);
            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("部门");
            row.createCell(1).setCellValue("名字");
            row.createCell(2).setCellValue("联系电话");
            row.createCell(3).setCellValue("驾驶证有效期");
            row.createCell(4).setCellValue("驾驶证类型");
            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getGroupName());
                row.createCell(1).setCellValue(list.get(i).getName());
                row.createCell(2).setCellValue(list.get(i).getPhoto());
                row.createCell(3).setCellValue(list.get(i).getDriverExpiryDateStr());
                row.createCell(4).setCellValue(list.get(i).getDriverCarType());
                row.setRowStyle(cellStyle);
            }
            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("有效天数报表".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
