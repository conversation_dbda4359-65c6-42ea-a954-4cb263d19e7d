package com.soft.gcc.xtbg.dzcc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.LzclTCarStatusMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTZbbMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【V_DFDW_DZCC_T_ZBB(主报表（查询当月信息）)】的数据库操作Service实现
 * @createDate 2022-10-31 09:46:24
 */
@Service
public class VDfdwDzccTZbbServiceImpl extends ServiceImpl<VDfdwDzccTZbbMapper, VDfdwDzccTZbb>
        implements IVDfdwDzccTZbbService {
    @Autowired
    IDfdwDzccTLockService lockService;
    @Autowired
    IVDfdwDzccTCarmoveService vCarmoveService;
    @Autowired
    IVDfdwDzccTCcdService ccdService;
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;
    @Autowired
    IDfdwDzccTCarstatusdailyService carstatusdailyService;
    @Autowired
    private IDfdwDzccTCclcdetailReportService reportService;

    @Autowired
    private IDfdwDzccTCclcService cclcService;
    @Autowired
    private DzccCarService carService;
    @Autowired
    private IDfdwDzccTCclcStatusDailyService cclcStatusDailyService;
    @Autowired
    private LzclTCarStatusMapper carStatusMapper;
    @Autowired
    private DfdwDzccTWeekService dfdwDzccTWeekService;
    @Autowired
    private DfdwDzccOverTimeService dfdwDzccOverTimeService;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


    /**
     * 里程报表
     *
     * @param dzccPersonEntity
     * @param zbb
     * @return
     */
    @Override
    public Result<Object> GetZbbList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTZbb zbb) {
        try {
            IPage<VDfdwDzccTZbb> list = new Page<>();
            list.setCurrent(zbb.getPageNum());
            list.setSize(zbb.getPageSize());

            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);

            List<Integer> groupIds = new ArrayList<>();
            if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                groupIds.add(zbb.getTopGroupId());
            } else {
                List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            }
            List<VDfdwDzccTZbb> zbbs = queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, dzccPersonEntity);

            int index = 0;
            for (int i = 0; i < zbbs.size(); i++) {
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    if (zbb.getCarOrPerson() == 0) {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getCarId().equals(zbbs.get(index).getCarId())) {
                            zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    } else {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getDriveId().equals(zbbs.get(index).getDriveId())) {
                            zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    }
                }
            }
            for (int i = 0; i < zbbs.size(); i++) {
                if (zbbs.get(i).getCount() != 0) {
                    zbbs.get(i).setAllValue(0);
                }
                int value = 0;
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    value += zbbs.get(j).getWorkValue();
                }
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(j).setAllValue(value);
                }
            }
            list.setTotal(zbbs.size());
            // 分页
            List<VDfdwDzccTZbb> zbbList = new ArrayList<>();
            if (zbbs.size() > 0) {
                int fromIndex = (zbb.getPageNum() - 1) * zbb.getPageSize();
                int toIndex = zbb.getPageNum() * zbb.getPageSize();
                if (toIndex > zbbs.size()) {
                    toIndex = zbbs.size();
                }
//                if (zbbs.get(toIndex - 1).getCount() == 0) {
//                    for (int i = toIndex; i < zbbs.size(); i++) {
//                        if (zbbs.get(i).getCount() == 0) {
//                            toIndex++;
//                        } else {
//                            break;
//                        }
//                    }
//                }
//                if (zbbs.get(fromIndex).getCount() == 0) {
//                    for (int i = fromIndex; i < zbbs.size(); i++) {
//                        if (zbbs.get(i).getCount() == 0) {
//                            fromIndex++;
//                        } else {
//                            break;
//                        }
//                    }
//                }
                zbbList = zbbs.subList(fromIndex, toIndex);
            }
            list.setRecords(zbbList);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    /**
     * 加班费报表统计
     *
     * @param dzccPersonEntity
     * @param zbb
     * @return
     */
    @Override
    public Result<Object> GetZbbListJbf(DzccPersonEntity dzccPersonEntity, VDfdwDzccTZbb zbb) {
        try {
            IPage<VDfdwDzccTZbb> list = new Page<>();
            list.setCurrent(zbb.getPageNum());
            list.setSize(zbb.getPageSize());

            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);

            List<Integer> groupIds = new ArrayList<>();
            if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                groupIds.add(zbb.getTopGroupId());
            } else {
                List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            }
            List<VDfdwDzccTZbb> zbbsTemp = queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, dzccPersonEntity);

            List<VDfdwDzccTZbb> zbbs = new ArrayList<>();
            //查找本月加班数据
            List<DfdwDzccTCclcdetailReport> reportList = reportService.list(new LambdaQueryWrapper<DfdwDzccTCclcdetailReport>()
                    .eq(DfdwDzccTCclcdetailReport::getYear, nowDate.getYear()).eq(DfdwDzccTCclcdetailReport::getMonth, nowDate.getMonthValue()));
            List<Integer> driveList = reportList.stream().map(DfdwDzccTCclcdetailReport::getDriveId).collect(Collectors.toList());
            zbbs = zbbsTemp.stream().filter(p -> driveList.contains(p.getDriveId())).collect(Collectors.toList());

            //求不包含的数据，特殊处理


            int index = 0;
            for (int i = 0; i < zbbs.size(); i++) {
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并【按人排序】
                    if (zbbs.get(i).getDriveId().equals(zbbs.get(index).getDriveId())) {
                        zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                        zbbs.get(i).setCount(0);
                    } else {
                        index = i;
                        // 如果当前行和上一行其值不相等
                        zbbs.get(i).setCount(1);
                    }
                }


            }
            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setAllValue(0);
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(i).setAllValue(zbbs.get(i).getAllValue() + zbbs.get(j).getWorkValue());
                }

                VDfdwDzccTZbb vDfdwDzccTZbb = zbbs.get(i);
                //计算公里津贴
                if (vDfdwDzccTZbb.getAllValue() <= 1000) {
                    BigDecimal mileageCost = new BigDecimal("0.2").multiply(new BigDecimal(vDfdwDzccTZbb.getAllValue()));
                    vDfdwDzccTZbb.setMileageCost(mileageCost);
                } else {
                    BigDecimal mileageCost = new BigDecimal("0.3").multiply(new BigDecimal(vDfdwDzccTZbb.getAllValue()));
                    vDfdwDzccTZbb.setMileageCost(mileageCost);
                }
                List<DfdwDzccTCclcdetailReport> report = reportList.stream().filter(p -> p.getDriveId().equals(vDfdwDzccTZbb.getDriveId())).collect(Collectors.toList());
                //解析加班时长数据
                // jbfJx(vDfdwDzccTZbb, cclcdetailList, gzrYsbt, sxrYsbt, jjrYsbt, sxrJbfbt, jjrJbfbt, gyBt);
                JbfJxNew(vDfdwDzccTZbb, report);
            }
            list.setTotal(zbbs.size());
            // 分页
            List<VDfdwDzccTZbb> zbbList = new ArrayList<>();
            if (zbbs.size() > 0) {
                int fromIndex = (zbb.getPageNum() - 1) * zbb.getPageSize();
                int toIndex = zbb.getPageNum() * zbb.getPageSize();
                if (toIndex > zbbs.size()) {
                    toIndex = zbbs.size();
                }
                if (zbbs.get(toIndex - 1).getCount() == 0) {
                    for (int i = toIndex; i < zbbs.size(); i++) {
                        if (zbbs.get(i).getCount() == 0) {
                            toIndex++;
                        } else {
                            break;
                        }
                    }
                }
                if (zbbs.get(fromIndex).getCount() == 0) {
                    for (int i = fromIndex; i < zbbs.size(); i++) {
                        if (zbbs.get(i).getCount() == 0) {
                            fromIndex++;
                        } else {
                            break;
                        }
                    }
                }
                zbbList = zbbs.subList(fromIndex, toIndex);
            }
            list.setRecords(zbbList);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    /**
     * 加班费报表统计New
     *
     * @param dzccPersonEntity
     * @param zbb
     * @return
     */
    @Override
    public Result<Object> GetZbbListJbfNew(DzccPersonEntity dzccPersonEntity, VDfdwDzccTZbb zbb) {
        try {
            Integer type = 0;
            if(zbb.getType() !=null ){
                type = zbb.getType();
            }
            IPage<VDfdwDzccTZbb> list = new Page<>();
            list.setCurrent(zbb.getPageNum());
            list.setSize(zbb.getPageSize());

            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);

            List<Integer> groupIds = new ArrayList<>();
            if(type == 1){
                if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                    groupIds.add(zbb.getTopGroupId());
                } else {
                    groupIds.add(373);
                }
            }else{
                if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                    groupIds.add(zbb.getTopGroupId());
                } else {
                    List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                    groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
                }
            }


            List<VDfdwDzccTZbb> zbbsTemp = queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, dzccPersonEntity);

            List<VDfdwDzccTZbb> zbbs = new ArrayList<>();
            //查找本月加班数据
            Integer year = nowDate.getYear();
            Integer month = nowDate.getMonthValue();
            List<DfdwDzccTCclcdetailReport> reportList = reportService.getListByMonth(year, month, zbb.getTopGroupId(), zbb.getRealName(), dzccPersonEntity, nowDate.format(df), nextDate.format(df));
            reportList = reportList.stream().distinct().collect(Collectors.toList());
            //获取所有车
            List<DzccCar> carList = carService.list();

            //查找所有已审批通过的延时添加数
            List<DfdwDzccTWeek> weekList = dfdwDzccTWeekService.list(new LambdaQueryWrapper<DfdwDzccTWeek>()
                    .eq(DfdwDzccTWeek::getYear, year).eq(DfdwDzccTWeek::getMonth, month)
                    .eq(DfdwDzccTWeek::getApproveState, 2));
            //获取配置信息
            List<DfdwDzccTOverTime> overtimeSetList = dfdwDzccOverTimeService.list(new LambdaQueryWrapper<DfdwDzccTOverTime>());

            for (DfdwDzccTCclcdetailReport report : reportList) {
                VDfdwDzccTZbb data = new VDfdwDzccTZbb();
                if (report.getId() == -1) {
                    report.setSubsidyStandardOvertime("157.5|210|314.5");
                    report.setSubsidyStandardDelay("19.70|26.20|39.30");
                    report.setSubsidyStandardNight("15");
                    report.setSxrOvertimeDays(0);
                    report.setJjrOvertimeDays(0);
                    report.setOvertimeCost(new BigDecimal("0"));
                    report.setGzrDelayHour(new BigDecimal("0"));
                    report.setSxrDelayHour(new BigDecimal("0"));
                    report.setJjrDelayHour(new BigDecimal("0"));
                    report.setDelayCost(new BigDecimal("0"));
                    report.setNightShiftDays(0);
                    report.setNightShiftCost(new BigDecimal("0"));
                    report.setTravelAllowance(new BigDecimal("0"));
                    report.setAllCost(new BigDecimal("0"));
                }
                //基础数据
                data.setTopGroupId(report.getTopGroupId());
                data.setTopGroupName(report.getTopGroupName());
                data.setDriveId(report.getDriveId());
                data.setRealName(report.getRealName());
                data.setYEAR(year);
                data.setMONTH(month);

                //加班费数据
                data.setSxrOvertimeDays(report.getSxrOvertimeDays());
                data.setJjrOvertimeDays(report.getJjrOvertimeDays());
                data.setOvertimeCost(report.getOvertimeCost());
                //延时
                data.setGzrDelayHour(report.getGzrDelayHour());
                data.setSxrDelayHour(report.getSxrDelayHour());
                data.setJjrDelayHour(report.getJjrDelayHour());
                data.setDelayCost(report.getDelayCost());


                //查找已审批通过的添加延时数
                BigDecimal addGzrDelayCost = new BigDecimal("0");
                if(type == 1){
                    List<DfdwDzccTWeek> weekListDriver = weekList.stream().filter(p -> Objects.equals(p.getDriveId(), report.getDriveId())).collect(Collectors.toList());
                    if (weekListDriver.size() > 0) {
                        DfdwDzccTOverTime overtimeSet = getOneOverTimeSet(overtimeSetList, report.getTopGroupId());
                        //工作日延时
                        Integer scale = 0;
                        if (overtimeSet.getOvertimeHoursMonthType() == 2) {
                            scale = 2;
                        }
                        //得到添加的延时小时数
                        BigDecimal addGzrDelayHour = weekListDriver.stream().map(DfdwDzccTWeek::getAppendDelayHour).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //在原有的小时数上增加
                        BigDecimal newGzrDelayHour = report.getGzrDelayHour().add(addGzrDelayHour).setScale(scale, BigDecimal.ROUND_HALF_UP);
                        data.setGzrDelayHour(newGzrDelayHour);

                        //添加的延时数费用
                        int index = report.getSubsidyStandardDelay().indexOf('|'); // 查找第一个 | 的位置
                        String firstPart = report.getSubsidyStandardDelay().substring(0, index);
                        BigDecimal gzrYsbt = new BigDecimal(firstPart);
                        addGzrDelayCost = gzrYsbt.multiply(addGzrDelayHour).setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal delayCostNew = report.getDelayCost().add(addGzrDelayCost);

                        data.setDelayCost(delayCostNew);
                    }
                }


                //夜餐
                data.setNightShiftDays(report.getNightShiftDays());
                data.setNightShiftCost(report.getNightShiftCost());

                //出差补贴
                data.setTravelAllowance(report.getTravelAllowance());

                //总合计
                BigDecimal allCost = report.getAllCost().add(addGzrDelayCost);
                data.setAllCost(allCost);

                //公里数
                List<VDfdwDzccTZbb> zbb1 = zbbsTemp.stream().filter(p -> p.getDriveId().equals(report.getDriveId())).collect(Collectors.toList());
                if (zbb1.size() > 0) {
                    //循环，存在多辆车则进入多次
//                    for (VDfdwDzccTZbb temp :zbb1) {
//                        if(StringUtils.isNotEmpty(zbb.getLicencePlate())){
//                            if(!temp.getLicencePlate().contains(zbb.getLicencePlate())){
//                                continue;
//                            }
//                        }
//                        //车辆相关数据
//                        data.setCarId(temp.getCarId());
//                        data.setCarMold(temp.getCarMold());
//                        data.setLicencePlate(temp.getLicencePlate());
//                        data.setActualValue(temp.getActualValue());
//                        data.setWorkValue(temp.getWorkValue());
//                        zbbs.add(data);
//                    }
                    for (int i = 0; i < zbb1.size(); i++) {
                        VDfdwDzccTZbb temp = zbb1.get(i);
                        if (StringUtils.isNotEmpty(zbb.getLicencePlate())) {
                            if (!temp.getLicencePlate().contains(zbb.getLicencePlate())) {
                                continue;
                            }
                        }
                        if (i == 0) {
                            //车辆相关数据
                            data.setCarId(temp.getCarId());
                            data.setCarMold(temp.getCarMold());
                            data.setLicencePlate(temp.getLicencePlate());
                            data.setActualValue(temp.getActualValue());
                            data.setWorkValue(temp.getWorkValue());
                            zbbs.add(data);
                        } else {
                            VDfdwDzccTZbb dataNew = JSONObject.parseObject(JSONObject.toJSONString(data), VDfdwDzccTZbb.class);
                            dataNew.setCarId(temp.getCarId());
                            dataNew.setCarMold(temp.getCarMold());
                            dataNew.setLicencePlate(temp.getLicencePlate());
                            dataNew.setActualValue(temp.getActualValue());
                            dataNew.setWorkValue(temp.getWorkValue());
                            zbbs.add(dataNew);
                        }

                    }
                } else {
                    //用户输入的车牌查询,那么车牌为空的需要不展示
                    if (StringUtils.isNotEmpty(zbb.getLicencePlate())) {
                        continue;
                    }
                    data.setCarId(null);
                    data.setCarMold(null);
                    data.setLicencePlate(null);
                    data.setActualValue(0);
                    data.setWorkValue(0);
                    zbbs.add(data);
                }
            }


            int index = 0;
            for (int i = 0; i < zbbs.size(); i++) {
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并【按人排序】
                    if (zbbs.get(i).getDriveId().equals(zbbs.get(index).getDriveId())) {
                        zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                        zbbs.get(i).setCount(0);
                    } else {
                        index = i;
                        // 如果当前行和上一行其值不相等
                        zbbs.get(i).setCount(1);
                    }
                }


            }
            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setAllValue(0);
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(i).setAllValue(zbbs.get(i).getAllValue() + zbbs.get(j).getWorkValue());
                }

                VDfdwDzccTZbb vDfdwDzccTZbb = zbbs.get(i);
                List<DzccCar> cars = carList.stream().filter(p -> p.getId().equals(vDfdwDzccTZbb.getCarId())).collect(Collectors.toList());
                Integer CarGenre = 0;
                if (cars.size() > 0) {
                    CarGenre = cars.get(0).getCarGenre();
                }
                //计算公里津贴
                if (CarGenre == 1) {
                    //大车
                    BigDecimal mileageCost = new BigDecimal("0.3").multiply(new BigDecimal(vDfdwDzccTZbb.getAllValue()));
                    vDfdwDzccTZbb.setMileageCost(mileageCost);
                } else {
                    //小车
                    if (vDfdwDzccTZbb.getAllValue() <= 1000) {
                        BigDecimal mileageCost = new BigDecimal("0.2").multiply(new BigDecimal(vDfdwDzccTZbb.getAllValue()));
                        vDfdwDzccTZbb.setMileageCost(mileageCost);
                    } else {
                        Integer a = vDfdwDzccTZbb.getAllValue() - 1000;
                        BigDecimal cost = new BigDecimal("0.3").multiply(new BigDecimal(a));
                        BigDecimal mileageCost = cost.add(new BigDecimal("200"));
                        vDfdwDzccTZbb.setMileageCost(mileageCost);
                    }
                }

                if (vDfdwDzccTZbb.getMileageCost() != null) {
                    BigDecimal allCost = vDfdwDzccTZbb.getAllCost().add(vDfdwDzccTZbb.getMileageCost());
                    vDfdwDzccTZbb.setAllCost(allCost);
                }
            }
            list.setTotal(zbbs.size());
            // 分页
            List<VDfdwDzccTZbb> zbbList = new ArrayList<>();
            if (zbbs.size() > 0) {
                int fromIndex = (zbb.getPageNum() - 1) * zbb.getPageSize();
                int toIndex = zbb.getPageNum() * zbb.getPageSize();
                if (toIndex > zbbs.size()) {
                    toIndex = zbbs.size();
                }
                if (zbbs.get(toIndex - 1).getCount() == 0) {
                    for (int i = toIndex; i < zbbs.size(); i++) {
                        if (zbbs.get(i).getCount() == 0) {
                            toIndex++;
                        } else {
                            break;
                        }
                    }
                }
                if (zbbs.get(fromIndex).getCount() == 0) {
                    for (int i = fromIndex; i < zbbs.size(); i++) {
                        if (zbbs.get(i).getCount() == 0) {
                            fromIndex++;
                        } else {
                            break;
                        }
                    }
                }
                zbbList = zbbs.subList(fromIndex, toIndex);
            }

            // 计算出车单区域情况
            for (VDfdwDzccTZbb item : zbbList) {
                if (item.getCount() != 0) {
                    List<DfdwDzccTCclcStatusDaily> dailyList = cclcStatusDailyService.list(new QueryWrapper<DfdwDzccTCclcStatusDaily>()
                            .eq("YEAR(moveDate)", year)
                            .eq("MONTH(moveDate)", month)
                            .lambda()
                            .eq(DfdwDzccTCclcStatusDaily::getDriveid, item.getDriveId())
                    );
                    item.setCdsNum(dailyList.stream().filter(p -> "出大市".equals(p.getStatus())).count());
                    item.setKqyNum(dailyList.stream().filter(p -> "跨区域".equals(p.getStatus())).count());
                    item.setBqyNum(dailyList.stream().filter(p -> "本区域".equals(p.getStatus())).count());
                    item.setYzNum(dailyList.stream().filter(DfdwDzccTCclcStatusDaily::getIstoyz).count());
                } else {
                    item.setCdsNum(0L);
                    item.setKqyNum(0L);
                    item.setBqyNum(0L);
                    item.setYzNum(0L);
                }
            }

            list.setRecords(zbbList);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    @Override
    public DfdwDzccTOverTime getOneOverTimeSet(List<DfdwDzccTOverTime> overtimeSetList, Integer groupId) {
        List<DfdwDzccTOverTime> overtimeSetTemp = overtimeSetList.stream().filter(p -> p.getDeptId().equals(groupId)).collect(Collectors.toList());
        DfdwDzccTOverTime overtimeSet = null;
        if (overtimeSetTemp.size() > 0) {
            overtimeSet = overtimeSetTemp.get(0);
        } else {
            overtimeSet = new DfdwDzccTOverTime();
            overtimeSet.setDeptId(groupId);
            overtimeSet.setStartTimeType(0);
            overtimeSet.setIsComputeNoon(1);
            overtimeSet.setTravelAllowanceType(1);
            overtimeSet.setOvertimeHoursAllType(1);
            overtimeSet.setOvertimeHoursMonthType(1);
            overtimeSet.setSxrDelayHourMonthType(1);
            overtimeSet.setJjrDelayHourMonthType(1);
        }
        return overtimeSet;
    }


    /**
     * 加班费用计算
     *
     * @param data           数据
     * @param cclcdetailList 本月加班时长数据
     * @param gzrYsbt        工作日补贴标准
     * @param sxrYsbt        双休日补贴标准
     * @param jjrYsbt        节假日补贴标准
     * @param sxrJbfbt       双休日加班费标准
     * @param jjrJbfbt       节假日加班费标准
     * @param gybt           过夜补贴
     */
    @Override
    public void jbfJx(VDfdwDzccTZbb data, List<VDfdwDzccTCclcdetail> cclcdetailList, BigDecimal gzrYsbt, BigDecimal sxrYsbt, BigDecimal jjrYsbt, BigDecimal sxrJbfbt, BigDecimal jjrJbfbt, BigDecimal gybt) {
        List<VDfdwDzccTCclcdetail> ccdDetail = cclcdetailList.stream().filter(p -> p.getDriveId().equals(data.getDriveId())).collect(Collectors.toList());
        if (ccdDetail.size() > 0) {
            //双休日加班天数 给加班费全天
            Integer sxrOvertimeDays = ccdDetail.stream().filter(p -> p.getDayType() == 0).collect(Collectors.toList()).size();
            BigDecimal sxrOvertimeAmount = sxrJbfbt.multiply(new BigDecimal(sxrOvertimeDays)).setScale(2, BigDecimal.ROUND_HALF_UP);
            //节假日加班天数
            Integer jjrOvertimeDays = ccdDetail.stream().filter(p -> p.getDayType() == 2).collect(Collectors.toList()).size();
            BigDecimal jjrOvertimeAmount = jjrJbfbt.multiply(new BigDecimal(jjrOvertimeDays)).setScale(2, BigDecimal.ROUND_HALF_UP);
            //加班费合计
            BigDecimal overtimeCost = sxrOvertimeAmount.add(jjrOvertimeAmount);
            data.setSxrOvertimeDays(sxrOvertimeDays);
            data.setJjrOvertimeDays(jjrOvertimeDays);
            data.setOvertimeCost(overtimeCost);

            //工作日延时
            BigDecimal gzrDelayHour = ccdDetail.stream().filter(p -> p.getDayType() == 1).map(VDfdwDzccTCclcdetail::getOvertimeHoursAll).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
            BigDecimal gzrDelayHourAmount = gzrYsbt.multiply(gzrDelayHour).setScale(2, BigDecimal.ROUND_HALF_UP);
            //双休日延时 4小时内不给延时，超过4小时给延时费用
            BigDecimal sxrDelayHour = ccdDetail.stream().filter(p -> p.getDayType() == 0 && p.getOvertimeHoursAll().compareTo(new BigDecimal("4")) >= 0).map(VDfdwDzccTCclcdetail::getOvertimeHoursAll).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
            BigDecimal sxrDelayHourAmount = sxrYsbt.multiply(sxrDelayHour).setScale(2, BigDecimal.ROUND_HALF_UP);
            //节假日延时
            BigDecimal jjrDelayHour = ccdDetail.stream().filter(p -> p.getDayType() == 2 && p.getOvertimeHoursAll().compareTo(new BigDecimal("4")) >= 0).map(VDfdwDzccTCclcdetail::getOvertimeHoursAll).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(0, BigDecimal.ROUND_HALF_UP);
            BigDecimal jjrDelayHourAmount = jjrYsbt.multiply(jjrDelayHour).setScale(2, BigDecimal.ROUND_HALF_UP);
            //延时津贴合计
            BigDecimal delayCost = gzrDelayHourAmount.add(sxrDelayHourAmount).add(jjrDelayHourAmount);
            data.setGzrDelayHour(gzrDelayHour);
            data.setSxrDelayHour(sxrDelayHour);
            data.setJjrDelayHour(jjrDelayHour);
            data.setDelayCost(delayCost);

            //夜餐台班数
            Integer nightShiftDays = ccdDetail.stream().filter(p -> p.getSubsidyState() == 1).collect(Collectors.toList()).size();
            BigDecimal nightShiftCost = gybt.multiply(new BigDecimal(nightShiftDays)).setScale(2, BigDecimal.ROUND_HALF_UP);
            data.setNightShiftDays(nightShiftDays);
            data.setNightShiftCost(nightShiftCost);

            //出差补贴
            BigDecimal travelAllowance = ccdDetail.stream().filter(p -> p.getTravelAllowance() != null).map(VDfdwDzccTCclcdetail::getTravelAllowance).reduce(BigDecimal.ZERO, BigDecimal::add);
            data.setTravelAllowance(travelAllowance);

            //总合计
            BigDecimal allCost = overtimeCost.add(delayCost).add(nightShiftCost).add(travelAllowance);
            if (data.getMileageCost() != null) {
                allCost = allCost.add(data.getMileageCost());
            }

            data.setAllCost(allCost);

        } else {
            //代表本月没有数据存在,则所有值为0
            data.setSxrOvertimeDays(0);
            data.setJjrOvertimeDays(0);
            data.setOvertimeCost(new BigDecimal("0"));
            data.setGzrDelayHour(new BigDecimal("0"));
            data.setSxrDelayHour(new BigDecimal("0"));
            data.setJjrDelayHour(new BigDecimal("0"));
            data.setDelayCost(new BigDecimal("0"));
            data.setNightShiftDays(0);
            data.setNightShiftCost(new BigDecimal("0"));
            //出差补贴
            data.setTravelAllowance(new BigDecimal("0"));
            //总合计
            BigDecimal allCost = new BigDecimal("0");
            if (data.getMileageCost() != null) {
                allCost = allCost.add(data.getMileageCost());
            }

            data.setAllCost(allCost);
        }
    }

    /**
     * 加班费解析New
     *
     * @param data 数据
     */
    @Override
    public void JbfJxNew(VDfdwDzccTZbb data, List<DfdwDzccTCclcdetailReport> reportList) {
        if (reportList.size() > 0) {
            DfdwDzccTCclcdetailReport report = reportList.get(0);
            data.setSxrOvertimeDays(report.getSxrOvertimeDays());
            data.setJjrOvertimeDays(report.getJjrOvertimeDays());
            data.setOvertimeCost(report.getOvertimeCost());
            data.setGzrDelayHour(report.getGzrDelayHour());
            data.setSxrDelayHour(report.getSxrDelayHour());
            data.setJjrDelayHour(report.getJjrDelayHour());
            data.setDelayCost(report.getDelayCost());
            data.setNightShiftDays(report.getNightShiftDays());
            data.setNightShiftCost(report.getNightShiftCost());

            //出差补贴
            data.setTravelAllowance(report.getTravelAllowance());

            //总合计
            BigDecimal allCost = report.getAllCost();
            if (data.getMileageCost() != null) {
                allCost = allCost.add(data.getMileageCost());
            }

            data.setAllCost(allCost);
        } else {
            //代表本月没有数据存在,则所有值为0
            data.setSxrOvertimeDays(0);
            data.setJjrOvertimeDays(0);
            data.setOvertimeCost(new BigDecimal("0"));
            data.setGzrDelayHour(new BigDecimal("0"));
            data.setSxrDelayHour(new BigDecimal("0"));
            data.setJjrDelayHour(new BigDecimal("0"));
            data.setDelayCost(new BigDecimal("0"));
            data.setNightShiftDays(0);
            data.setNightShiftCost(new BigDecimal("0"));
            //总合计
            BigDecimal allCost = new BigDecimal("0");
            if (data.getMileageCost() != null) {
                allCost = allCost.add(data.getMileageCost());
            }

            data.setAllCost(allCost);
        }
    }


    @Override
    public List<VDfdwDzccTZbb> queryZbbList(String format, String format1, List<Integer> groupIds, VDfdwDzccTZbb zbb, DzccPersonEntity dzccPersonEntity) {
        List<VDfdwDzccTZbb> zbbs = baseMapper.GetZbbList(format, format1, groupIds, zbb, dzccPersonEntity);
        List<VDfdwDzccTZbb> zbbList = new ArrayList<>();
        for (int i = 0; i < zbbs.size(); i++) {
            int carId = zbbs.get(i).getCarId();
            int driverId = zbbs.get(i).getDriveId();
            int groupId = zbbs.get(i).getTopGroupId();
            VDfdwDzccTZbb b = new VDfdwDzccTZbb();
            BeanUtils.copyProperties(zbbs.get(i), b);
            b.setActualValue(0);
            b.setActualMileage(0L);
            int a = i;
            for (int j = i; j < zbbs.size(); j++) {
                if (zbbs.get(j).getCarId() == carId && zbbs.get(j).getDriveId() == driverId && zbbs.get(j).getTopGroupId() == groupId) {
                    a = j;
                    if (zbbs.get(j).getStartrow() == 1) {
                        b.setActualValue(b.getActualValue() + zbbs.get(j).getActualValue());
                        if (zbbs.get(j).getActualMileage() != null) {
                            b.setActualMileage(b.getActualMileage() + zbbs.get(j).getActualMileage());
                        }
                    }
                    if (zbbs.get(j).getEndrow() == 1) {
                        b.setActualValue(b.getActualValue() - zbbs.get(j).getActualValue());
                    }
                } else {
                    break;
                }
            }
            i = a;
            b.setActualValue(b.getActualValue() * -1);
            b.setWorkValue(b.getActualValue());
            zbbList.add(b);
        }
        if (zbb.getCarOrPerson() == 0) {
            zbbList = zbbList.stream().sorted(Comparator.comparing(VDfdwDzccTZbb::getXH).thenComparing(VDfdwDzccTZbb::getLicencePlate).thenComparing(VDfdwDzccTZbb::getRealName)).collect(Collectors.toList());
        } else {
            zbbList = zbbList.stream().sorted(Comparator.comparing(VDfdwDzccTZbb::getXH).thenComparing(VDfdwDzccTZbb::getRealName).thenComparing(VDfdwDzccTZbb::getLicencePlate)).collect(Collectors.toList());
        }
        return zbbList;
    }

    @Override
    public Result<Object> LockMonth(DzccPersonEntity dzccPersonEntity, DfdwDzccTLock lock) {
        try {
            List<DfdwDzccTLock> locks = lockService.list(new LambdaQueryWrapper<DfdwDzccTLock>()
                    .eq(DfdwDzccTLock::getGroupId, lock.getGroupId())
                    .eq(DfdwDzccTLock::getDate, lock.getDate()));
            if (locks.size() > 0) {
                return Result.error("该部门当月数据已被锁定，请勿重复锁定！");
            }
            Instant instant = lock.getDate().toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDate date = instant.atZone(zoneId).toLocalDate();
            lock.setYear(date.getYear());
            lock.setMonth(date.getMonth().getValue());
            lock.setEditId(dzccPersonEntity.getId());
            lockService.save(lock);
            return Result.ok("锁定成功！");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> UnLockMonth(DfdwDzccTLock lock) {
        try {
            lockService.removeById(lock);
            return Result.ok("解锁成功！");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> ShowLockMonth(DfdwDzccTLock lock) {
        try {
            List<DfdwDzccTLock> locks = lockService.list(new LambdaQueryWrapper<DfdwDzccTLock>().eq(DfdwDzccTLock::getGroupId, lock.getGroupId()).orderByDesc(DfdwDzccTLock::getDate));
            return Result.ok(locks);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetSbbList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTZbb zbb) {
        try {
            LocalDate startDate = DateUtil.asLocalDate(zbb.getStartDate());
            LocalDate endDate1 = DateUtil.asLocalDate(zbb.getEndDate());
            LocalDate endDate = endDate1.plusDays(-1);

            List<DfdwDzccTCarmove> fbbInfoList = new ArrayList<>();
            List<DfdwDzccTCarstatusdaily> list = carstatusdailyService.list(new LambdaQueryWrapper<DfdwDzccTCarstatusdaily>()
                    .ge(DfdwDzccTCarstatusdaily::getCurDate, startDate)
                    .le(DfdwDzccTCarstatusdaily::getCurDate, endDate)
                    .eq(DfdwDzccTCarstatusdaily::getPersonId, zbb.getDriveId())
                    .eq(DfdwDzccTCarstatusdaily::getCarId, zbb.getCarId())
                    .eq(DfdwDzccTCarstatusdaily::getGroupId, zbb.getTopGroupId())
            );
            List<LzclTCarStatus> statusList = carStatusMapper.selectListByCarIdAndDate(zbb.getCarId(), zbb.getDriveId(), zbb.getTopGroupId(), startDate, endDate1);
            LocalDate finalStartDate = startDate;
            List<DfdwDzccTCclc> cclcList = cclcService.list(new QueryWrapper<DfdwDzccTCclc>()
                    .and(t -> t
                            .or(w -> w.le("CONVERT(date, ccOpenTime)", finalStartDate).ge("CONVERT(date, executeTime)", finalStartDate))
                            .or(w -> w.le("CONVERT(date, ccOpenTime)", endDate).ge("CONVERT(date, executeTime)", endDate))
                            .or(w -> w.ge("CONVERT(date, ccOpenTime)", finalStartDate).le("CONVERT(date, executeTime)", endDate))
                    )
                    .lambda()
                    .eq(DfdwDzccTCclc::getApproveState, 2)
                    .eq(DfdwDzccTCclc::getExecuteState, 2)
                    .eq(DfdwDzccTCclc::getCarId, zbb.getCarId())
                    .eq(DfdwDzccTCclc::getDriveId, zbb.getDriveId())
                    .eq(DfdwDzccTCclc::getApplyTopDeptId, zbb.getTopGroupId())
            );
            //当开始时间不大于结束时间，循环执行
            while (!startDate.isAfter(endDate)) {

                //所以日期转化为String，
                DfdwDzccTCarmove info = new DfdwDzccTCarmove();
                info.setDriveId(zbb.getDriveId());
                info.setCarId(zbb.getCarId());
                info.setCurDate(Date.from(startDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                LocalDate finalStartDate1 = startDate;
                DfdwDzccTCarstatusdaily daily = list.stream().filter(s -> s.getCurDate().isEqual(finalStartDate1)).findFirst().orElse(null);
                if (daily != null) {
                    info.setDayInfo(daily.getStatus());
                } else {
                    info.setDayInfo("本区域");
                }
                LzclTCarStatus status = statusList.stream().filter(s -> {
                    LocalDate date = DateUtil.asLocalDate(s.getCreateTime());
                    return date.isEqual(finalStartDate1);
                }).findFirst().orElse(null);
                if (status != null) {
                    info.setActualMileage(status.getActualMileage());
                } else {
                    info.setActualMileage(0L);
                }
                List<DfdwDzccTCclc> cclcs = cclcList.stream().filter(s -> {
                    LocalDate sDate = DateUtil.asLocalDate(s.getCcOpenTime());
                    LocalDate eDate = DateUtil.asLocalDate(s.getExecuteTime());
                    return !sDate.isAfter(finalStartDate1) && !eDate.isBefore(finalStartDate1);
                }).collect(Collectors.toList());
                info.setCcdNum(cclcs.size());
                fbbInfoList.add(info);
                //日期+1，继续执行
                startDate = startDate.plusDays(1);
            }

            fbbInfoList = fbbInfoList.stream().sorted(Comparator.comparing(DfdwDzccTCarmove::getCurDate)).collect(Collectors.toList());
            return Result.ok(fbbInfoList);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GettbbList(DzccPersonEntity dzccPersonEntity, DfdwDzccTCarmove carmove) {
        try {
            Instant instant = carmove.getCurDate().toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDate localDate = instant.atZone(zoneId).toLocalDate();
            LocalDate endDate = localDate.plusDays(1);
            List<VDfdwDzccTCcd> ccdList = ccdService.list(new LambdaQueryWrapper<VDfdwDzccTCcd>()
                    .eq(VDfdwDzccTCcd::getCarId, carmove.getCarId())
                    .eq(VDfdwDzccTCcd::getApproveState, 2)
                    .eq(VDfdwDzccTCcd::getExecuteState, 2)
                    .ge(VDfdwDzccTCcd::getExecuteTime, localDate)
                    .le(VDfdwDzccTCcd::getCcOpenTime, endDate)
            );
            return Result.ok(ccdList);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetLcgjList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTZbb zbb) {
        try {
            IPage<VDfdwDzccTZbb> list = new Page<>();
            list.setCurrent(zbb.getPageNum());
            list.setSize(zbb.getPageSize());
            List<VDfdwDzccTZbb> zbbs = new ArrayList<>();
            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);

            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                List<Integer> groupIds = new ArrayList<>();
                if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                    groupIds.add(zbb.getTopGroupId());
                } else {
                    List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                    groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
                }
                zbbs = queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, dzccPersonEntity);
            }
            List<DzccDictionaryvalue> dictionaryvalues = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .eq(DzccDictionaryvalue::getTitleID, 970221)
                    .like(DzccDictionaryvalue::getContent, "每日里程指标"));
            for (int i = 0; i < zbbs.size(); i++) {
                for (DzccDictionaryvalue value : dictionaryvalues) {
                    if ("司机每日里程指标".equals(value.getContent())) {
                        zbbs.get(i).setDriverIndex(Integer.valueOf(value.getParameter()));
                    } else if ("车辆每日里程指标".equals(value.getContent())) {
                        zbbs.get(i).setCarIndex(Integer.valueOf(value.getParameter()));
                    }
                }
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    if (zbb.getCarOrPerson() == 0) {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getCarId().equals(zbbs.get(i - 1).getCarId())) {
                            zbbs.get(i - 1).setCount(zbbs.get(i - 1).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    } else {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getDriveId().equals(zbbs.get(i - 1).getDriveId())) {
                            zbbs.get(i - 1).setCount(zbbs.get(i - 1).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    }
                }
            }
            for (int i = 0; i < zbbs.size(); i++) {
                if (zbbs.get(i).getCount() != 0) {
                    zbbs.get(i).setAllValue(0);
                }
                int value = 0;
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    value += zbbs.get(j).getWorkValue();
                }
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(j).setAllValue(value);
                }
            }
            zbbs = zbbs.stream().filter(a -> a.getCount() != 0).collect(Collectors.toList());
            list.setTotal(zbbs.size());
            // 分页
            if (zbbs.size() > 0) {
                int fromIndex = (zbb.getPageNum() - 1) * zbb.getPageSize();
                int toIndex = zbb.getPageNum() * zbb.getPageSize();
                if (toIndex > zbbs.size()) {
                    toIndex = zbbs.size();
                }
                zbbs = zbbs.subList(fromIndex, toIndex);
            }
            list.setRecords(zbbs);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetXcgjList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCarmove carmove) {
        try {
            IPage<VDfdwDzccTCarmove> list = new Page<>();
            list.setCurrent(carmove.getPageNum());
            list.setSize(carmove.getPageSize());
            LocalDate startDate = carmove.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate endDate = carmove.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                list.setTotal(vCarmoveService.getXcglListCount(dzccPersonEntity, carmove, startDate, endDate));
                list.setRecords(vCarmoveService.getXcglListPage(dzccPersonEntity, carmove, startDate, endDate));
            }
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}





