package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 办公和停车地点修改日志
 * @TableName DFDW_DZCC_T_UnitLog
 */
@TableName(value ="DFDW_DZCC_T_UnitLog")
@Data
public class DfdwDzccTUnitlog implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车辆id
     */
    @TableField(value = "carId")
    private Integer carId;

    /**
     * 部门id
     */
    @TableField(value = "groupId")
    private Integer groupId;

    /**
     * 地点名称
     */
    @TableField(value = "unitName")
    private String unitName;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @TableField(value = "latitude")
    private BigDecimal latitude;

    /**
     * 地点类型(0 办公 1停车)
     */
    @TableField(value = "type")
    private String type;

    /**
     * 开始使用时间
     */
    @TableField(value = "useDateTime")
    private LocalDateTime useDateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}