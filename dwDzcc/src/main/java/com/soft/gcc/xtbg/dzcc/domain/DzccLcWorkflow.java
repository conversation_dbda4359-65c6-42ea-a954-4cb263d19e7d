package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 
* @TableName Lc_workFlow
*/
@TableName(value ="Lc_workFlow", autoResultMap = true)
@Data
public class DzccLcWorkflow implements Serializable {

    /**
    * 主键：
    */
    
    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name = "ID")
    private Integer ID;

    /**
    * 
    */
    @TableField(value = "lc_defineID")
    @JSONField(name = "lc_defineID")
    
    private Integer lc_defineID;
    /**
    * 
    */
    @TableField(value = "ywID")
    @JSONField(name = "ywID")
    
    private Integer ywID;
    /**
    * 
    */
    @TableField(value = "lc_jdID")
    @JSONField(name = "lc_jdID")
    
    private Integer lc_jdID;
    /**
    * 
    */
    @TableField(value = "lc_jdmc")
    @JSONField(name = "lc_jdmc")
    
    private String lc_jdmc;
    /**
    * 
    */
    @TableField(value = "groupID")
    @JSONField(name = "groupID")
    
    private Integer groupID;
    /**
    * 
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")
    
    private String groupName;
    /**
    * 
    */
    @TableField(value = "personZgh")
    @JSONField(name = "personZgh")
    
    private String personZgh;
    /**
    * 
    */
    @TableField(value = "personName")
    @JSONField(name = "personName")
    
    private String personName;
    /**
    * 
    */
    @TableField(value = "transdate")
    @JSONField(name = "transdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transdate;
    /**
    * 
    */
    @TableField(value = "feed")
    @JSONField(name = "feed")
    
    private String feed;
    /**
    * 
    */
    @TableField(value = "number")
    @JSONField(name = "number")
    
    private Integer number;
    /**
    * 
    */
    @TableField(value = "BXType")
    @JSONField(name = "BXType")
    
    private String BXType;
    /**
    * 
    */
    @TableField(value = "PNO")
    @JSONField(name = "PNO")
    
    private String PNO;
    /**
    * 
    */
    @TableField(value = "startdate")
    @JSONField(name = "startdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startdate;
    /**
    * 
    */
    @TableField(value = "LcByRole")
    @JSONField(name = "LcByRole")
    
    private Integer LcByRole;
    /**
    * 
    */
    @TableField(value = "isback")
    @JSONField(name = "isback")
    
    private Integer isback;
    /**
    * 
    */
    @TableField(value = "useback")
    @JSONField(name = "useback")
    
    private Integer useback;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
