package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.domain.LzclTCarMore;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd;
import com.soft.gcc.xtbg.dzcc.service.DzccCarService;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 车辆信息
 */
@RequestMapping("/dzcc/clxx")
@RestController
public class DzccClxxController extends DzccBaseController {
    @Autowired
    DzccCarService dzccCarService;

    /**
     * 电子出车-PC-车辆信息-车辆办公点查询
     * */
    @RequestMapping("/GetClxxList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01ZC01QX01')")
    public Result<Object> GetClxxList(@RequestBody DzccCar car) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.GetClxxList(dzccPersonEntity, car);
    }

    /**
     * 电子出车-PC-车辆信息-车辆类型查询
     * */
    @RequestMapping("/GetMoldDict")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01ZC01QX01')")
    public Result<Object> GetMoldDict() {
        return this.dzccCarService.GetMoldDict();
    }

    /**
     * 电子出车-PC-车辆信息-添加车辆信息
     * */
    @RequestMapping("/AddClxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX02')")
    public Result<Object> AddClxx(@RequestBody DzccCar car) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.AddClxx(dzccPersonEntity, car);
    }

    /**
     * 电子出车-PC-车辆信息-修改车辆信息
     * */
    @RequestMapping("/EditClxx")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX03')")
    public Result<Object> EditClxx(@RequestBody DzccCar car) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.EditClxx(dzccPersonEntity, car);
    }

    /**
     * 电子出车-PC-车辆信息-根据id删除车辆信息
     * */
    @RequestMapping("/DeleteClxxById")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX04')")
    public Result<Object> DeleteClxxById(@RequestParam Integer id) {
        return this.dzccCarService.DeleteClxxById(id);
    }

    /**
     * 电子出车-PC-车辆信息-导出
     * */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX05')")
    public Result<Object> DownLoad(@RequestBody DzccCar car, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.DownLoad(dzccPersonEntity, car, response);
    }

    /**
     * 电子出车-PC-车辆信息-部门配置查询
     * */
    @RequestMapping("/GetCarMoveList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01ZC01QX01')")
    public Result<Object> GetCarMoveList(@RequestBody LzclTCarMore carMore) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.GetCarMoveList(dzccPersonEntity, carMore);
    }

    /**
     * 电子出车-PC-车辆信息-部门配置查询
     * */
    @RequestMapping("/GetCarMoveChildList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01ZC01QX01')")
    public Result<Object> GetCarMoveChildList(@RequestBody LzclTCarMore carMore) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.GetCarMoveChildList(dzccPersonEntity, carMore);
    }

    /**
     * 电子出车-PC-车辆信息-添加部门配置
     * */
    @RequestMapping("/AddCarMove")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX02')")
    public Result<Object> AddCarMove(@RequestBody LzclTCarMore carMore) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.AddCarMove(dzccPersonEntity, carMore);
    }

    /**
     * 电子出车-PC-车辆信息-修改部门配置
     * */
    @RequestMapping("/EditCarMove")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX03')")
    public Result<Object> EditCarMove(@RequestBody LzclTCarMore carMore) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.dzccCarService.EditCarMove(dzccPersonEntity, carMore);
    }

    /**
     * 电子出车-PC-车辆信息-根据id删除部门配置
     * */
    @RequestMapping("/DeleteCarMoveById")
    @PreAuthorize("@ss.hasPermi('NDWCC01ZC01QX04')")
    public Result<Object> DeleteCarMoveById(@RequestParam Integer id) {
        return this.dzccCarService.DeleteCarMoveById(id);
    }
}
