package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarBackhaul;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTCarBackhaulService;
import com.soft.gcc.xtbg.dzcc.service.DzccCarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("/dzcc/clxxpz")
@RestController
public class DfdwDzccTCarBackhaulController extends BaseController {
    @Autowired
    private DfdwDzccTCarBackhaulService dfdwDzccTCarBackhaulService;
    @Autowired
    private DzccCarService dzccCarService;


    @RequestMapping("/GetBackHaulList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01CLPZ01QX04')")
    public Result<Object> GetBackHaulList(@RequestBody DfdwDzccTCarBackhaul backhaul) {
        IPage<DfdwDzccTCarBackhaul> dfdwDzccTClbgIPage = this.dfdwDzccTCarBackhaulService.GetBackHaulList(backhaul);
        return Result.ok(dfdwDzccTClbgIPage);
    }
    /**
     * 电子出车-PC-车辆办公点-添加车辆办公点
     * */
    @RequestMapping("/AddBackHaul")
    @PreAuthorize("@ss.hasPermi('NDWCC01CLPZ01QX01')")
    public Result<Object> AddBackHaul(@RequestBody DfdwDzccTCarBackhaul backhaul)
    {
        backHaulVOInit(backhaul);
        if(backhaul.getType()!=null && backhaul.getType()==1){
            backhaul.setGroupId(null);
        }
        dfdwDzccTCarBackhaulService.save(backhaul);
        return Result.ok();
    }



    /**
     * 电子出车-PC-车辆办公点-修改车辆办公点
     * */
    @RequestMapping("/EditBackHaul")
    @PreAuthorize("@ss.hasPermi('NDWCC01CLPZ01QX02')")
    public Result<Object> EditBackHaul(@RequestBody DfdwDzccTCarBackhaul backhaul){
        dfdwDzccTCarBackhaulService.updateById(backhaul);
        return Result.ok();
    }

    /**
     * 电子出车-PC-车辆办公点-根据id删除车辆办公点
     * */
    @RequestMapping("/DeleteBackHaul")
    @PreAuthorize("@ss.hasPermi('NDWCC01CLPZ01QX03')")
    public Result<Object> DeleteBackHaul(@RequestParam(value = "id", required = true) String id){
        dfdwDzccTCarBackhaulService.removeById(id);
        return Result.ok();
    }

     public String splitTime(String resource){
         String replace = resource.replace("[", "").replace("]", "").replace("\"", "");
         String[] split = replace.split(",");
         StringBuilder strb = new StringBuilder();
         strb.append(split[0]).append("|").append(split[1]);
         return strb.toString();
     }
    private void backHaulVOInit(DfdwDzccTCarBackhaul backhaul) {
        backhaul.setBackEndTime(splitTime(backhaul.getBackEndTime()));
        backhaul.setBackStartTime(splitTime(backhaul.getBackStartTime()));
        backhaul.setWorkEndTime(splitTime(backhaul.getWorkEndTime()));
        backhaul.setWorkStartTime(splitTime(backhaul.getWorkStartTime()));
    }


    @RequestMapping("/getCarList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BG01QX01,NDWCC01BG01QX02,NDWCC01BG01QX03')")
    public Result<Object> getCarList(@RequestParam("id")Integer groupId) {
        QueryWrapper<DzccCar> wp=new QueryWrapper<>();
        wp.select("id,LicencePlate");
        wp.eq("IsEnable",1);
        wp.eq("IsDelete",0);
        if(groupId!=null&&!groupId.equals("")&& groupId!=-1){
            wp.eq("groupId",groupId);
        }
        List<DzccCar> list = dzccCarService.list(wp);
        return Result.ok(list);
    }
}
