package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccLcWorkflow;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.service.IDzccLcWorkflowService;
import com.soft.gcc.xtbg.dzcc.service.VDfdwDzccTWeekMainService;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTWeekMainMapper;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_WEEK_MAIN】的数据库操作Service实现
* @createDate 2024-12-23 16:58:44
*/
@Service
public class VDfdwDzccTWeekMainServiceImpl extends ServiceImpl<VDfdwDzccTWeekMainMapper, VDfdwDzccTWeekMain>
    implements VDfdwDzccTWeekMainService{

    @Resource
    IDzccLcWorkflowService lcWorkflowService;



    @Override
    public IPage<VDfdwDzccTWeekMain> listPage(VDfdwDzccTWeek vDfdwDzccTWeek) {
        Page<VDfdwDzccTWeekMain> page = new Page<>(vDfdwDzccTWeek.getPageNum(), vDfdwDzccTWeek.getPageSize());
        return this.baseMapper.listPage(page,vDfdwDzccTWeek);
    }



    @Override
    public Result<Object> GetLcListById(VDfdwDzccTWeek vDfdwDzccTWeek) {
        try {
            //.apply("ywID in (select id from DFDW_DZCC_T_CCLC where mainId = " + id + " or id = " + id + ")")
            List<DzccLcWorkflow> list = lcWorkflowService.list(new LambdaQueryWrapper<DzccLcWorkflow>()
                    .eq(DzccLcWorkflow::getYwID,vDfdwDzccTWeek.getId())
                    .eq(DzccLcWorkflow::getLc_defineID, 20032)
                    .orderByAsc(DzccLcWorkflow::getYwID, DzccLcWorkflow::getStartdate)
            );
            Map<Integer, List<DzccLcWorkflow>> collect = list.stream().collect(Collectors.groupingBy(DzccLcWorkflow::getYwID));
            return Result.ok(collect);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




