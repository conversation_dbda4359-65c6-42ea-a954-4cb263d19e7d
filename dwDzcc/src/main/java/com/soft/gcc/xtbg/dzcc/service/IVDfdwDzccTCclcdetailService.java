package com.soft.gcc.xtbg.dzcc.service;

import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCclcdetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CCLCDetail】的数据库操作Service
* @createDate 2023-11-16 09:43:56
*/
public interface IVDfdwDzccTCclcdetailService extends IService<VDfdwDzccTCclcdetail> {
    List<VDfdwDzccTCclcdetail> getCCLCDetailList(Integer selectType, Integer driveId,Integer year,Integer month,Integer type);

}
