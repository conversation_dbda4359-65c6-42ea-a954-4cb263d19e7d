package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 每日出车情况
 * @TableName DFDW_DZCC_T_CarStatusDaily
 */
@TableName(value ="DFDW_DZCC_T_CarStatusDaily")
@Data
public class DfdwDzccTCarstatusdaily implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车辆id
     */
    @TableField(value = "carId")
    private Integer carId;

    /**
     * 驾驶员id
     */
    @TableField(value = "personId")
    private Integer personId;

    /**
     * 部门id
     */
    @TableField(value = "groupId")
    private Integer groupId;

    /**
     * 出车情况
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private LocalDateTime createTime;

    /**
     * 出车时间
     */
    @TableField(value = "curDate")
    private LocalDate curDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}