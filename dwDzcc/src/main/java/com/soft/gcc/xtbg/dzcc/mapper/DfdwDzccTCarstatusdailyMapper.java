package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarstatusdaily;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CarStatusDaily(每日出车情况)】的数据库操作Mapper
* @createDate 2023-08-08 15:17:41
* @Entity com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarstatusdaily
*/
public interface DfdwDzccTCarstatusdailyMapper extends BaseMapper<DfdwDzccTCarstatusdaily> {

    List<DfdwDzccTCarstatusdaily> getList(@Param("date") LocalDate date);

    void insertBatch(@Param("users") List<DfdwDzccTCarstatusdaily> users);
}




