package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriverinfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_DRIVERINFO(司机准假资质信息)】的数据库操作Mapper
* @createDate 2023-07-27 09:36:19
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTDriverinfo
*/
public interface DfdwDzccTDriverinfoMapper extends BaseMapper<DfdwDzccTDriverinfo> {

    IPage<DfdwDzccTDriverinfo> GetList(
            IPage<DfdwDzccTDriverinfo> page,
            @Param("dzccQx") Integer dzccQx,
            @Param("id") Integer id,
            @Param("info") DfdwDzccTDriveremploy info,
            @Param("isInfoPerson") boolean isInfoPerson
    );

    IPage<DfdwDzccTDriverinfo> GetJsyxxPage(
            IPage<DfdwDzccTDriverinfo> page,
            @Param("dzccQx") Integer dzccQx,
            @Param("id") Integer id,
            @Param("info") DfdwDzccTDriverinfo info,
            @Param("isInfoPerson") boolean isInfoPerson
    );

    List<DfdwDzccTDriverinfo> GetJsyxxList(
            @Param("dzccQx") Integer dzccQx,
            @Param("id") Integer id,
            @Param("info") DfdwDzccTDriverinfo info,
            @Param("isInfoPerson") boolean isInfoPerson
    );
}




