package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 主报表（查询当月信息）
 * @TableName V_DFDW_DZCC_T_ZBB
 */
@TableName(value = "V_DFDW_DZCC_T_ZBB", autoResultMap = true)
@Data
public class VDfdwDzccTZbb extends DzccBaseEntity implements Serializable {


    /**
     *
     */
    @TableField(value = "YEAR")
    @JSONField(name = "YEAR")

    private Integer YEAR;
    /**
     *
     */
    @TableField(value = "MONTH")
    @JSONField(name = "MONTH")

    private Integer MONTH;
    /**
     *
     */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
     *
     */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
     *
     */
    @TableField(value = "actualValue")
    @JSONField(name = "actualValue")

    private Integer actualValue;
    /**
     *
     */
    @TableField(value = "RealName")
    @JSONField(name = "RealName")

    private String RealName;
    /**
     *
     */
    @TableField(value = "Telephone")
    @JSONField(name = "Telephone")

    private String Telephone;
    /**
     *
     */
    @TableField(value = "TopGroupId")
    @JSONField(name = "TopGroupId")

    private Integer TopGroupId;
    /**
     *
     */
    @TableField(value = "TopGroupName")
    @JSONField(name = "TopGroupName")

    private String TopGroupName;
    /**
     *
     */
    @TableField(value = "LicencePlate")
    @JSONField(name = "LicencePlate")

    private String LicencePlate;
    /**
     * 年月
     */
    @TableField(exist = false)
    private String yearMonth;
    /**
     * 工作历程
     */
    @TableField(exist = false)
    private Integer workValue;
    /**
     * 总里程
     */
    @TableField(exist = false)
    private Integer allValue;
    /**
     * 合并数量
     */
    @TableField(exist = false)
    private Integer count;
    /**
     * 0 车 1 人
     */
    @TableField(exist = false)
    private Integer carOrPerson;
    /**
     * 开始时间
     */
    @TableField(exist = false)
    @JSONField(name = "startDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    /**
     * 结束时间
     */
    @TableField(exist = false)
    @JSONField(name = "endDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    /**
     * 司机指标
     */
    @TableField(exist = false)
    private Integer driverIndex;
    /**
     * 车辆指标
     */
    @TableField(exist = false)
    private Integer carIndex;

    @TableField(exist = false)
    private String date;

    @TableField(exist = false)
    private String CarMold;

    @TableField(exist = false)
    private Integer startrow;

    @TableField(exist = false)
    private Integer endrow;

    @TableField(exist = false)
    private Integer XH;

    @TableField(exist = false)
    private Integer CarTag;

    /**
     * 类型 0 普通加班费 1 集团加班费
     */
    @TableField(exist = false)
    private Integer type;

    //公里津贴
    @TableField(exist = false)
    private BigDecimal mileageCost;
    //双休日加班天数
    @TableField(exist = false)
    private Integer sxrOvertimeDays;
    //节假日加班天数
    @TableField(exist = false)
    private Integer jjrOvertimeDays;
    //加班费合计
    @TableField(exist = false)
    private BigDecimal overtimeCost;
    //工作日延时小时
    @TableField(exist = false)
    private BigDecimal gzrDelayHour;
    //双休日延时小时
    @TableField(exist = false)
    private BigDecimal sxrDelayHour;
    //节假日延时小时
    @TableField(exist = false)
    private BigDecimal jjrDelayHour;
    //延时总津贴
    @TableField(exist = false)
    private BigDecimal delayCost;
    //夜班天数
    @TableField(exist = false)
    private Integer nightShiftDays;
    //夜班津贴
    @TableField(exist = false)
    private BigDecimal nightShiftCost;
    //出差补贴
    private BigDecimal travelAllowance;
    //总费用合计
    private BigDecimal allCost;

    //出大市单数
    @TableField(exist = false)
    private Long cdsNum;
    //跨区域单数
    @TableField(exist = false)
    private Long kqyNum;
    //本区域单数
    @TableField(exist = false)
    private Long bqyNum;
    //鄞州单数
    @TableField(exist = false)
    private Long yzNum;
    //实际里程 米
    @TableField(exist = false)
    private Long actualMileage;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Integer selectType;
}
