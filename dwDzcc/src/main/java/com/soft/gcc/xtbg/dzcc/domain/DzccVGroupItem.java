package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* vGroupItem
* @TableName vGroupItem
*/
@TableName(value ="vGroupItem", autoResultMap = true)
@Data
public class DzccVGroupItem implements Serializable {


    /**
    *
    */
    @TableField(value = "id")
    @JSONField(name = "id")

    private Integer id;
    /**
    *
    */
    @TableField(value = "groupname")
    @JSONField(name = "groupname")

    private String groupname;
    /**
    *
    */
    @TableField(value = "parentid")
    @JSONField(name = "parentid")

    private Integer parentid;
    /**
    *
    */
    @TableField(value = "type")
    @JSONField(name = "type")

    private Integer type;
    /**
    *
    */
    @TableField(value = "shortpinyin")
    @JSONField(name = "shortpinyin")

    private String shortpinyin;
    /**
    *
    */
    @TableField(value = "dydj")
    @JSONField(name = "dydj")

    private String dydj;
    /**
    *
    */
    @TableField(value = "XH")
    @JSONField(name = "XH")

    private Integer XH;
    /**
    *
    */
    @TableField(value = "IsShow")
    @JSONField(name = "IsShow")

    private Integer IsShow;
    /**
    *
    */
    @TableField(value = "Category")
    @JSONField(name = "Category")

    private Integer Category;
    /**
    *
    */
    @TableField(value = "UComapanyQC")
    @JSONField(name = "UComapanyQC")

    private String UComapanyQC;
    /**
    *
    */
    @TableField(value = "GroupDesc")
    @JSONField(name = "GroupDesc")

    private String GroupDesc;
    /**
    *
    */
    @TableField(value = "ParentName")
    @JSONField(name = "ParentName")

    private String ParentName;
    /**
    *
    */
    @TableField(value = "TopGroupId")
    @JSONField(name = "TopGroupId")

    private Integer TopGroupId;
    /**
    *
    */
    @TableField(value = "plevel")
    @JSONField(name = "plevel")

    private Integer plevel;
    /**
    *
    */
    @TableField(value = "TopGroupName")
    @JSONField(name = "TopGroupName")

    private String TopGroupName;
    /**
    *
    */
    @TableField(value = "ParentPath")
    @JSONField(name = "ParentPath")

    private String ParentPath;
    /**
    *
    */
    @TableField(value = "ParentTPath")
    @JSONField(name = "ParentTPath")

    private String ParentTPath;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
