package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_DRIVEREMPLOY(司机聘用表)】的数据库操作Mapper
* @createDate 2023-07-27 10:32:23
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTDriveremploy
*/
public interface DfdwDzccTDriveremployMapper extends BaseMapper<DfdwDzccTDriveremploy> {

    List<DfdwDzccTDriveremploy> GetEmployList(@Param("personId") Integer personId);

    List<DfdwDzccTDriveremploy> getListInTime(
            @Param("id") Integer id,
            @Param("personId") Integer personId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}




