package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCdzglService;
import com.soft.gcc.xtbg.dzcc.service.IDzccGroupitemService;
import com.soft.gcc.xtbg.dzcc.service.IDzccVPersonService;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 部门管理
 */
@RequestMapping("/dzcc/cdzgl")
@RestController
public class DzccCdzglController extends DzccBaseController {
    @Autowired
    IDzccGroupitemService groupitemService;
    @Autowired
    IDfdwDzccTCdzglService cdzglService;
    @Autowired
    IDzccVPersonService personService;

    /**
     * 电子出车-PC-车队长管理-车队长查询
     * */
    @RequestMapping("/GetCDZList")
    @PreAuthorize("@ss.hasRole('协同办公-电子出车-车辆总管理')")
    public Result<Object> GetCDZList(@RequestParam Map<String, String> map) {
        try {
            String name = ParseUtil.tryParseString(map.get("cdzname"));
            int deptId = ParseUtil.tryParseInt(map.get("dept"));
            int manageGroupId = ParseUtil.tryParseInt(map.get("manageGroupId"));
            int pageNum = ParseUtil.tryParseInt(map.get("pageNum"));
            int pageSize = ParseUtil.tryParseInt(map.get("pageSize"), 1);
            int personTypeId = ParseUtil.tryParseInt(map.get("personTypeId"));
            return this.personService.GetCDZList(new Page<>(pageNum, pageSize), personTypeId, name, manageGroupId, deptId);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    /**
     * 电子出车-PC-车队长管理-选择车队长下拉
     * */
    @RequestMapping("/GetCDZGroupList")
    @PreAuthorize("@ss.hasRole('协同办公-电子出车-车辆总管理')")
    public Result<Object> GetCDZGroupList(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.cdzglService.GetCDZGroupList(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车队长管理-编辑车队长管理
     * */
    @RequestMapping("/EditCdz")
    @PreAuthorize("@ss.hasRole('协同办公-电子出车-车辆总管理')")
    public Result<Object> EditCdz(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.cdzglService.EditCdz(dzccPersonEntity, map);
    }
}
