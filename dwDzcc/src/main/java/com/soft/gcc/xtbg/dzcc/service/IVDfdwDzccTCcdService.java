package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CCD】的数据库操作Service
* @createDate 2022-10-25 16:50:19
*/
public interface IVDfdwDzccTCcdService extends IService<VDfdwDzccTCcd> {

    Result<Object> GetCcdsBBList(DzccPersonEntity person, VDfdwDzccTCcd ccd);
}
