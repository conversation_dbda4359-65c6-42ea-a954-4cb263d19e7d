package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 出车单-目的地
* @TableName DFDW_DZCC_T_ADDRESS
*/
@TableName(value ="DFDW_DZCC_T_ADDRESS", autoResultMap = true)
@Data
public class DfdwDzccTAddress implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * cclc主键ID
    */
    @TableField(value = "lcId")
    @JSONField(name = "lcId")

    private Integer lcId;
    /**
    * 省份
    */
    @TableField(value = "provinces")
    @JSONField(name = "provinces")

    private Integer provinces;
    /**
    * 市
    */
    @TableField(value = "city")
    @JSONField(name = "city")

    private Integer city;
    /**
    * 区
    */
    @TableField(value = "area")
    @JSONField(name = "area")

    private Integer area;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 区域中文
    */
    @TableField(value = "areaText")
    @JSONField(name = "areaText")

    private String areaText;
    /**
    * 市中文冗余字段
    */
    @TableField(value = "cityText")
    @JSONField(name = "cityText")

    private String cityText;
    /**
    * 添加类型（0用户新增，1车队长新增 2申请人后面补充添加）
    */
    @TableField(value = "addType")
    @JSONField(name = "addType")

    private Integer addType;
    /**
    * 用户添加新的目的地审批状态（0待审批，1审批中， 2审批通过）
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")

    private Integer approveState;
    /**
     * 到达状态（0未到达，1已到达）
     */
    @TableField(value = "arriveState")
    @JSONField(name = "arriveState")

    private Integer arriveState;

    /**
     * 地址信息
     */
    @TableField(value = "addressInfo")
    @JSONField(name = "addressInfo")

    private String addressInfo;

    @TableField(exist = false)
    private Integer driveId;
    @TableField(exist = false)
    private Date ccOpenTime;
    @TableField(exist = false)
    private Date executeTime;

    @TableField(exist = false)
    private DzccPersonEntity dzccPersonEntity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
