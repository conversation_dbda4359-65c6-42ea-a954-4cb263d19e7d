package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_WEEK_MAIN】的数据库操作Service
* @createDate 2024-12-23 16:58:44
*/
public interface VDfdwDzccTWeekMainService extends IService<VDfdwDzccTWeekMain> {

    IPage<VDfdwDzccTWeekMain> listPage(VDfdwDzccTWeek vDfdwDzccTWeek);

    Result<Object> GetLcListById(VDfdwDzccTWeek vDfdwDzccTWeek);
}
