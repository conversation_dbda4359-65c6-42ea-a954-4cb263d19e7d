package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTNotice;
import com.soft.gcc.xtbg.dzcc.domain.DzccDictionaryvalue;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTNotice;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTNoticeService;
import com.soft.gcc.xtbg.dzcc.service.IDzccDictionaryvalueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description 司机公告
 */
@RequestMapping("/dzcc/notice")
@RestController
public class DzccNoticeController extends DzccBaseController{
    @Autowired
    IDfdwDzccTNoticeService noticeService;
    @Autowired
    TFileService tFileService;
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;

    /**
     * 电子出车-PC-通告-通知查询
     * */
    @RequestMapping("/GetNoticeList")
    @PreAuthorize("@ss.hasPermi('NDWCC01TG01QX01')")
    public Result<Object> GetNoticeList(@RequestBody VDfdwDzccTNotice notice) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return noticeService.GetNoticeList(dzccPersonEntity, notice);
    }

    /**
     * 电子出车-PC-通告-新增上传文件
     * */
    @RequestMapping("/upload")
    public Result<Object> upload(MultipartFile file, HttpServletRequest request, String type, Integer joinId) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return noticeService.upload(dzccPersonEntity, file, request, type, joinId);
    }

    /**
     * 电子出车-PC-通告-新增司机通告
     * */
    @RequestMapping("/addSjtg")
    @PreAuthorize("@ss.hasPermi('NDWCC01TG01QX02')")
    public Result<Object> addSjtg(@RequestBody DfdwDzccTNotice notice) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return noticeService.addSjtg(dzccPersonEntity, notice);
    }

    /**
     * 修改时获取图片预览
     * @param fileId 文件id
     * @return 文件
     */
    @RequestMapping("/image")
    public Result<Object> image(Integer fileId){
        TFile result = this.tFileService.getById(fileId);
        return Result.ok(result);
    }

    /**
     * 电子出车-PC-通告-修改司机通告
     * */
    @RequestMapping("/editSjtg")
    @PreAuthorize("@ss.hasPermi('NDWCC01TG01QX03')")
    public Result<Object> editSjtg(@RequestBody DfdwDzccTNotice notice) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return noticeService.editSjtg(dzccPersonEntity, notice);
    }

    /**
     * 电子出车-PC-通告-删除司机通告
     * */
    @RequestMapping("/deleteSjtg")
    @PreAuthorize("@ss.hasPermi('NDWCC01TG01QX04')")
    public Result<Object> deleteSjtg(Integer noticeId) {
        return noticeService.deleteSjtg(noticeId);
    }

    /**
     * 电子出车-PC-通告-文件限制
     * */
    @RequestMapping("/fileLimit")
    public Result<Object> fileLimit() {
        List<DzccDictionaryvalue> values = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>().eq(DzccDictionaryvalue::getTitleID, 970222));
        return Result.ok(values);
    }
}
