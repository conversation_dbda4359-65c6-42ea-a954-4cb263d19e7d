package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.entity.CLGLDetailForMonth;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTZbbMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 里程告警
 */
@RequestMapping("/dzcc/lcgj")
@RestController
public class DzccLcgjController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTZbbService zbbService;
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    /**
     * 电子出车-PC-里程告警-里程告警查询
     * */
    @RequestMapping("/GetLcgjList")
    @PreAuthorize("@ss.hasPermi('NDWCC01LC01QX01')")
    public Result<Object> GetLcgjList(@RequestBody VDfdwDzccTZbb zbb) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.GetLcgjList(dzccPersonEntity, zbb);
    }

    /**
     * 电子出车-PC-里程告警-导出
     * */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01LC01QX02')")
    public Result<Object> DownLoad(@RequestBody VDfdwDzccTZbb zbb, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<VDfdwDzccTZbb> list = new ArrayList<>();
            List<VDfdwDzccTZbb> zbbs = new ArrayList<>();
            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);
            if (person.getDzccQx() != 3 && person.getDzccQx() > 0) {
                List<Integer> groupIds = new ArrayList<>();
                if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                    groupIds.add(zbb.getTopGroupId());
                } else {
                    List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(person, 1, false);
                    groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
                }
                zbbs = zbbService.queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, person);
            }
            List<DzccDictionaryvalue> dictionaryvalues = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .eq(DzccDictionaryvalue::getTitleID, 970221)
                    .like(DzccDictionaryvalue::getContent, "每日里程指标"));
            for (int i = 0; i < zbbs.size(); i++) {
                for (DzccDictionaryvalue value : dictionaryvalues) {
                    if ("司机每日里程指标".equals(value.getContent())) {
                        zbbs.get(i).setDriverIndex(Integer.valueOf(value.getParameter()));
                    } else if ("车辆每日里程指标".equals(value.getContent())) {
                        zbbs.get(i).setCarIndex(Integer.valueOf(value.getParameter()));
                    }
                }
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    if (zbb.getCarOrPerson() == 0) {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getCarId().equals(zbbs.get(i - 1).getCarId())) {
                            zbbs.get(i - 1).setCount(zbbs.get(i - 1).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    } else {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getDriveId().equals(zbbs.get(i - 1).getDriveId())) {
                            zbbs.get(i - 1).setCount(zbbs.get(i - 1).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    }
                }
            }
            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setAllValue(0);
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(i).setAllValue(zbbs.get(i).getAllValue() + zbbs.get(j).getWorkValue());
                    if (j != i) {
                        zbbs.remove(j);
                    }
                }
                if (zbbs.get(i).getCount() == 0) {
                    zbbs.get(i).setAllValue(zbbs.get(i).getAllValue());
                }
            }
            zbbs = zbbs.stream().filter(a -> a.getCount() != 0).collect(Collectors.toList());
            list = zbbs;

            XSSFSheet sheet = workbook.createSheet("里程告警");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(zbb.getTopGroupName() + "里程告警(" + date.getYear() + "年" + date.getMonth() + "月)");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("月份");
            row.createCell(1).setCellValue("申请单位");
            row.createCell(2).setCellValue("车型");
            if (zbb.getCarOrPerson() == 0) {
                row.createCell(3).setCellValue("车牌号");
                row.createCell(4).setCellValue("车辆里程");
            } else {
                row.createCell(3).setCellValue("司机");
                row.createCell(4).setCellValue("工作里程");
            }
            row.createCell(5).setCellValue("里程指标");
            row.createCell(6).setCellValue("车辆标识");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.setRowStyle(cellStyle);

                String yearMonth = "";
                if (list.get(i).getMONTH() < 10) {
                    yearMonth = list.get(i).getYEAR() + "-0" + list.get(i).getMONTH();
                } else {
                    yearMonth = list.get(i).getYEAR() + "-" + list.get(i).getMONTH();
                }
                row.createCell(0).setCellValue(yearMonth);
                row.createCell(1).setCellValue(list.get(i).getTopGroupName());
                row.createCell(2).setCellValue(list.get(i).getCarMold());
                String carTag = "";
                if (list.get(i).getCarTag() == 0) {
                    carTag = "临租车辆";
                } else if (list.get(i).getCarTag() == 1) {
                    carTag = "特殊车辆";
                } else if (list.get(i).getCarTag() == 2) {
                    carTag = "产权车辆";
                }
                if (zbb.getCarOrPerson() == 0) {
                    row.createCell(3).setCellValue(list.get(i).getLicencePlate());
                    row.createCell(4).setCellValue(list.get(i).getAllValue());
                    row.createCell(5).setCellValue(list.get(i).getCarIndex());
                    row.createCell(6).setCellValue(carTag);
                    CellStyle redCellStyle = null;
                    if (list.get(i).getAllValue() < list.get(i).getCarIndex()) {
                        for (int j = 0; j < 7; j++) {
                            Cell cell = sheet.getRow(2 + i).getCell(j);
                            if (redCellStyle == null) {
                                redCellStyle = workbook.createCellStyle();
                                // 重点：从现有样式克隆style，只修改Font，其它style不变
                                redCellStyle.cloneStyleFrom(cell.getCellStyle());
                                // 获取原有字体
                                Font oldFont = workbook.getFontAt(redCellStyle.getFontIndexAsInt());
                                // 创建新字体
                                Font redFont = workbook.createFont();
                                // 重点：保留原字体样式
                                redFont.setFontName(oldFont.getFontName()); // 保留原字体
                                redFont.setFontHeightInPoints(oldFont.getFontHeightInPoints()); // 保留原字体高度
                                redFont.setBold(true); // 加粗
                                redFont.setColor(IndexedColors.RED.getIndex());  // 字体颜色：红色
                                // 设置红色字体
                                redCellStyle.setFont(redFont);
                            }
                            // 设置样式
                            cell.setCellStyle(redCellStyle);
                        }
                    }
                } else {
                    row.createCell(3).setCellValue(list.get(i).getRealName());
                    row.createCell(4).setCellValue(list.get(i).getAllValue());
                    row.createCell(5).setCellValue(list.get(i).getDriverIndex());
                    row.createCell(6).setCellValue(carTag);
                    CellStyle redCellStyle = null;
                    if (list.get(i).getAllValue() < list.get(i).getDriverIndex()) {
                        for (int j = 0; j < 7; j++) {
                            Cell cell = sheet.getRow(2 + i).getCell(j);
                            if (redCellStyle == null) {
                                redCellStyle = workbook.createCellStyle();
                                // 重点：从现有样式克隆style，只修改Font，其它style不变
                                redCellStyle.cloneStyleFrom(cell.getCellStyle());
                                // 获取原有字体
                                Font oldFont = workbook.getFontAt(redCellStyle.getFontIndexAsInt());
                                // 创建新字体
                                Font redFont = workbook.createFont();
                                // 重点：保留原字体样式
                                redFont.setFontName(oldFont.getFontName()); // 保留原字体
                                redFont.setFontHeightInPoints(oldFont.getFontHeightInPoints()); // 保留原字体高度
                                redFont.setBold(true); // 加粗
                                redFont.setColor(IndexedColors.RED.getIndex());  // 字体颜色：红色
                                // 设置红色字体
                                redCellStyle.setFont(redFont);
                            }
                            // 设置样式
                            cell.setCellStyle(redCellStyle);
                        }
                    }
                }
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + zbb.getTopGroupName() + "里程告警(" + date.getYear() + "年" + date.getMonth() + "月)" + ".xlsx");

            workbook.write(response.getOutputStream());

            return Result.ok();

        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
