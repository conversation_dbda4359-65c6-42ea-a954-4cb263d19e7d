package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DzccLzclTCarRent;
import com.soft.gcc.xtbg.dzcc.entity.LocusDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Rent(零租车辆--车辆借用记录)】的数据库操作Mapper
* @createDate 2022-10-19 17:04:48
* @Entity com.yykj.app.dzcc.domain.DzccLzclTCarRent
*/
public interface DzccLzclTCarRentMapper extends BaseMapper<DzccLzclTCarRent> {

    List<LocusDto> getLocusList(
            @Param("person") DzccPersonEntity person,
            @Param("locusDto") LocusDto locusDto
    );

    IPage<LocusDto> getNotLocusListPage(IPage<LocusDto> list,@Param("locusDto") LocusDto locusDto,@Param("person") DzccPersonEntity person);

    List<LocusDto> getNotLocusList(@Param("locusDto") LocusDto locusDto,@Param("person") DzccPersonEntity person);
}




