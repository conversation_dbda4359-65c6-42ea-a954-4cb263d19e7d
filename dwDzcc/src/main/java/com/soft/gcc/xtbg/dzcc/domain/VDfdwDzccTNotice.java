package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_NOTICE
*/
@TableName(value ="V_DFDW_DZCC_T_NOTICE", autoResultMap = true)
@Data
public class VDfdwDzccTNotice extends DzccBaseEntity implements Serializable {


    /**
    *
    */
    @TableField(value = "id")
    @JSONField(name = "id")

    private Integer id;
    /**
    *
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    *
    */
    @TableField(value = "remark")
    @JSONField(name = "remark")

    private String remark;
    /**
    *
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    *
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    *
    */
    @TableField(value = "editId")
    @JSONField(name = "editId")

    private Integer editId;
    /**
    *
    */
    @TableField(value = "startTime")
    @JSONField(name = "startTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    /**
    *
    */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
    *
    */
    @TableField(value = "isOpen")
    @JSONField(name = "isOpen")

    private Integer isOpen;
    /**
    *
    */
    @TableField(value = "noticeType")
    @JSONField(name = "noticeType")

    private Integer noticeType;
    /**
    *
    */
    @TableField(value = "title")
    @JSONField(name = "title")

    private String title;
    /**
    *
    */
    @TableField(value = "titleImage")
    @JSONField(name = "titleImage")

    private String titleImage;
    /**
    *
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")

    private String groupName;
    /**
    *
    */
    @TableField(value = "titlefileid")
    @JSONField(name = "titlefileid")

    private Integer titlefileid;
    /**
    *
    */
    @TableField(value = "videofileid")
    @JSONField(name = "videofileid")

    private Integer videofileid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
