package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 
* @TableName V_DFDW_DZCC_T_ADDRESS
*/
@TableName(value ="V_DFDW_DZCC_T_ADDRESS", autoResultMap = true)
@Data
public class VDfdwDzccTAddress implements Serializable {


    /**
    * 
    */
    @TableField(value = "primaryId")
    @JSONField(name = "primaryId")
    
    private Integer primaryId;
    /**
    * 
    */
    @TableField(value = "ccOpenTime")
    @JSONField(name = "ccOpenTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ccOpenTime;
    /**
    * 
    */
    @TableField(value = "executeTime")
    @JSONField(name = "executeTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeTime;
    /**
    * 
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")
    
    private String driverName;
    /**
    * 
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")
    
    private Integer driveId;
    /**
    * 
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")
    
    private Integer carId;
    /**
    * 
    */
    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")
    
    private String licensePlate;
    /**
    * 
    */
    @TableField(value = "applyTopDeptId")
    @JSONField(name = "applyTopDeptId")
    
    private Integer applyTopDeptId;
    /**
    * 
    */
    @TableField(value = "applyTopDeptName")
    @JSONField(name = "applyTopDeptName")
    
    private String applyTopDeptName;
    /**
    * 
    */
    @TableField(value = "id")
    @JSONField(name = "id")
    
    private Integer id;
    /**
    * 
    */
    @TableField(value = "lcId")
    @JSONField(name = "lcId")
    
    private Integer lcId;
    /**
    * 
    */
    @TableField(value = "provinces")
    @JSONField(name = "provinces")
    
    private Integer provinces;
    /**
    * 
    */
    @TableField(value = "city")
    @JSONField(name = "city")
    
    private Integer city;
    /**
    * 
    */
    @TableField(value = "area")
    @JSONField(name = "area")
    
    private Integer area;
    /**
    * 
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 
    */
    @TableField(value = "areaText")
    @JSONField(name = "areaText")
    
    private String areaText;
    /**
    * 
    */
    @TableField(value = "cityText")
    @JSONField(name = "cityText")
    
    private String cityText;
    /**
    * 
    */
    @TableField(value = "addType")
    @JSONField(name = "addType")
    
    private Integer addType;
    /**
    * 
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")
    
    private Integer approveState;
    /**
    * 
    */
    @TableField(value = "arriveState")
    @JSONField(name = "arriveState")
    
    private Integer arriveState;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
