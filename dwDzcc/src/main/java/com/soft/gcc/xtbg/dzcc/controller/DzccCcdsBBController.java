package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd;
import com.soft.gcc.xtbg.dzcc.entity.CLGLDetailForMonth;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCcdMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTZbbMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCcdService;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 行程报表
 */
@RequestMapping("/dzcc/ccdsbb")
@RestController
public class DzccCcdsBBController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTCcdService ccdService;
    @Autowired
    VDfdwDzccTZbbMapper zbbMapper;
    @Autowired
    VDfdwDzccTCcdMapper ccdMapper;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    /**
     * 电子出车-PC-有效天数报表-主报表查询
     * */
    @RequestMapping("/GetCcdsBBList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD05QX01')")
    public Result<Object> GetCcdsBBList(@RequestBody VDfdwDzccTCcd ccd) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return ccdService.GetCcdsBBList(dzccPersonEntity, ccd);
    }

    /**
     * 电子出车-PC-有效天数报表-主报表导出
     * */
    @RequestMapping("/DownLoadCcdsBB")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD05QX02')")
    public Result<Object> DownLoadCcdsBB(@RequestBody VDfdwDzccTCcd ccd, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            IPage<VDfdwDzccTCcd> list = new Page<>();
            list.setCurrent(ccd.getPageNum());
            list.setSize(ccd.getPageSize());

            LocalDateTime startDate = ccd.getCcOpenTime();
            LocalDateTime endDate = ccd.getExecuteTime().plusDays(1);
            List<VDfdwDzccTCcd> ccdList = ccdMapper.getCcdBBList(
                    dzccPersonEntity,
                    startDate,
                    endDate,
                    ccd
            );

            int index = 0;
            for (int i = 0; i < ccdList.size(); i++) {
                // index == 0表示数据为第一行
                if (i == 0) {
                    ccdList.get(i).setCount(1);
                } else {
                    if (ccd.getCarOrPerson() == 0) {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (ccdList.get(i).getCarId().equals(ccdList.get(index).getCarId())) {
                            ccdList.get(index).setCount(ccdList.get(index).getCount() + 1);
                            ccdList.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            ccdList.get(i).setCount(1);
                        }
                    } else {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (ccdList.get(i).getDriveId().equals(ccdList.get(index).getDriveId())) {
                            ccdList.get(index).setCount(ccdList.get(index).getCount() + 1);
                            ccdList.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            ccdList.get(i).setCount(1);
                        }
                    }
                }
            }

            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(ccd.getApplyTopDeptName() + "有效天数报表（" + startDate + "-" + endDate.plusDays(-1) + ")");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            if (ccd.getCarOrPerson() == 0) {
                row.createCell(0).setCellValue("申请单位");
                row.createCell(1).setCellValue("车型");
                row.createCell(2).setCellValue("车牌");
                row.createCell(3).setCellValue("司机");
                row.createCell(4).setCellValue("有效天数");
                row.createCell(5).setCellValue("有效总天数");
                row.createCell(6).setCellValue("车辆标识");
            } else {
                row.createCell(0).setCellValue("申请单位");
                row.createCell(1).setCellValue("车型");
                row.createCell(2).setCellValue("司机");
                row.createCell(3).setCellValue("车牌");
                row.createCell(4).setCellValue("有效天数");
                row.createCell(5).setCellValue("有效总天数");
                row.createCell(6).setCellValue("车辆标识");
            }

            for (int i = 0; i < ccdList.size(); i++) {
                ccdList.get(i).setValidDaySum(0);
                for (int j = i; j < i + ccdList.get(i).getCount(); j++) {
                    ccdList.get(i).setValidDaySum(ccdList.get(i).getValidDaySum() + ccdList.get(j).getValidDays());
                }
                // 列合并
                if (ccdList.get(i).getCount() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + ccdList.get(i).getCount(), 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + ccdList.get(i).getCount(), 5, 5));
                }

                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(ccdList.get(i).getApplyTopDeptName());
                row.createCell(1).setCellValue(ccdList.get(i).getCarMold());
                if (ccd.getCarOrPerson() == 0) {
                    row.createCell(2).setCellValue(ccdList.get(i).getLicensePlate());
                    row.createCell(3).setCellValue(ccdList.get(i).getDriverName());
                } else {
                    row.createCell(2).setCellValue(ccdList.get(i).getDriverName());
                    row.createCell(3).setCellValue(ccdList.get(i).getLicensePlate());
                }
                row.createCell(4).setCellValue(ccdList.get(i).getValidDays());
                row.createCell(5).setCellValue(ccdList.get(i).getValidDaySum());
                if (ccdList.get(i).getCarTag() == 0) {
                    row.createCell(6).setCellValue("临租车辆");
                } else if (ccdList.get(i).getCarTag() == 1) {
                    row.createCell(6).setCellValue("特殊车辆");
                } else if (ccdList.get(i).getCarTag() == 2) {
                    row.createCell(6).setCellValue("产权车辆");
                }
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("有效天数报表".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;

        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
