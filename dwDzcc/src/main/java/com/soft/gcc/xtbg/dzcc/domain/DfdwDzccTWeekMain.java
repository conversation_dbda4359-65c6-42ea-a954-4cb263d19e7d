package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 周期-主表
 * @TableName DFDW_DZCC_T_WEEK_MAIN
 */
@TableName(value ="DFDW_DZCC_T_WEEK_MAIN")
@Data
//@NoArgsConstructor // 无参构造函数
//@AllArgsConstructor // 有参构造函数
public class DfdwDzccTWeekMain implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年份
     */
    @TableField(value = "year")
    private Integer year;

    /**
     * 月份
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 周期
     */
    @TableField(value = "week")
    private Integer week;

    /**
     * 开始日期
     */
    @TableField(value = "startTime")
    private LocalDate startTime;

    /**
     * 结束日期
     */
    @TableField(value = "endTime")
    private LocalDate endTime;

    /**
     * 周期天数 字典值
     */
    @TableField(value = "weekDays")
    private Integer weekDays;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 申请人Id（发起延时申请用户）
     */
    @TableField(value = "applyUserId")
    private Integer applyUserId;

    /**
     * 申请人名称（发起延时申请用户）
     */
    @TableField(value = "applyUserName")
    private String applyUserName;

    /**
     * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(value = "approveState")
    private Integer approveState;

    /**
     * 流程申请时间
     */
    @TableField(value = "applyTime")
    private LocalDateTime applyTime;

    @TableField(exist = false)
    private BigDecimal sumAppendDelayHour;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
//    public DfdwDzccTWeekMain(){}
//
//    public DfdwDzccTWeekMain(int year, int month, int week, LocalDate startTime, LocalDate endTime, int weekDays, Date createTime) {
//        this.year = year;
//        this.month = month;
//        this.week = week;
//        this.startTime = startTime;
//        this.endTime = endTime;
//        this.weekDays = weekDays;
//        this.createTime = createTime;
//    }
}
