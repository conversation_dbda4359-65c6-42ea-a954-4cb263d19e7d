package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName V_DFDW_DZCC_T_WEEK
 */
@TableName(value ="V_DFDW_DZCC_T_WEEK")
@Data
public class VDfdwDzccTWeek extends DzccBaseEntity implements Serializable {
    /**
     * 
     */
    @TableField( value = "id")
    private Integer id;

    /**
     * 
     */
    @TableField( value = "driveId")
    private Integer driveId;

    /**
     * 
     */
    @TableField( value = "driveName")
    private String driveName;

    /**
     * 
     */
    @TableField(value = "year")
    private Integer year;

    /**
     * 
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 
     */
    @TableField(value = "week")
    private Integer week;

    /**
     * 
     */
    @TableField( value = "startTime")
    private Date startTime;

    /**
     * 
     */
    @TableField( value = "endTime")
    private Date endTime;

    /**
     * 
     */
    @TableField( value = "weekDays")
    private Integer weekDays;

    /**
     * 
     */
    @TableField( value = "appendDelayHour")
    private BigDecimal appendDelayHour;

    /**
     * 
     */
    @TableField( value = "applyUserId")
    private Integer applyUserId;

    /**
     * 
     */
    @TableField( value = "applyUserName")
    private String applyUserName;

    /**
     * 
     */
    @TableField( value = "approveState")
    private Integer approveState;

    /**
     * 
     */
    @TableField( value = "remark")
    private String remark;

    /**
     * 
     */
    @TableField( value = "createTime")
    private Date createTime;

    /**
     * 
     */
    @TableField( value = "applyTime")
    private Date applyTime;

    @TableField( value = "weekMainId")
    private Integer weekMainId;

    /**
     *
     */
    @TableField( value = "oringOvertimeHours")
    private BigDecimal oringOvertimeHours;

    /**
     *
     */
    @TableField( value = "carIds")
    private String carIds;

    /**
     *
     */
    @TableField(value = "LicencePlates")
    private String licencePlates;


    @TableField( exist = false)
    private Boolean selectAll;

    /**
     * 前端使用，是否显示我已审批记录
     */
    @TableField( exist = false)
    private Boolean checked;

    @TableField( exist = false)
    private static final long serialVersionUID = 1L;


}