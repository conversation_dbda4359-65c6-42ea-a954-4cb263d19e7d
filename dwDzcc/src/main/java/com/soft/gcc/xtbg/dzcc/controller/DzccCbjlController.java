package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCbjl;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCbjlService;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description GPS异常告警
 */
@RequestMapping("/dzcc/cbjl")
@RestController
public class DzccCbjlController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTCbjlService cbjlService;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping("/GetCbjlList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CB01QX01')")
    public Result<Object> GetCbjlList(@RequestBody VDfdwDzccTCbjl cbjl) {
        DzccPersonEntity person = getDzccPerson();
        return this.cbjlService.GetCbjlList(person, cbjl);
    }

    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01CB01QX02')")
    public Result<Object> DownLoad(@RequestBody VDfdwDzccTCbjl cbjl, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<VDfdwDzccTCbjl> list = new ArrayList<>();
            String sql = "";
            if (person.getDzccQx() == 1) {
                sql = "";
            } else {
                sql = "(";
                if (person.getQxs().contains(2)) {
                    sql += "groupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + person.getId() + " and type = 1) ";
                }
                if (person.getQxs().contains(4) || person.getQxs().contains(6)) {
                    if (person.getQxs().contains(2)) {
                        sql += " or ";
                    }
                    sql += "(groupId in (";
                    if (person.getQxs().contains(4) && person.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + person.getId() + " and type != 1";
                    } else if (person.getQxs().contains(4) && !person.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + person.getId() + " and type = 2";
                    } else if (!person.getQxs().contains(4) && person.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + person.getId() + " and type = 3";
                    }
                    sql += ") and carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = " + person.getId() + "))";
                }
                sql += ")";
            }
            if (cbjl.getEndTime() != null) {
                cbjl.setEndTime(new Date(cbjl.getEndTime().getTime() + 86400000));
            }
            if (person.getDzccQx() != 3 && person.getDzccQx() > 0) {
                list = cbjlService.list(new LambdaQueryWrapper<VDfdwDzccTCbjl>()
                        .like(!"".equals(cbjl.getLicensePlate()), VDfdwDzccTCbjl::getLicensePlate, cbjl.getLicensePlate())
                        .ge(cbjl.getStartTime() != null, VDfdwDzccTCbjl::getStartTime, cbjl.getStartTime())
                        .le(cbjl.getEndTime() != null, VDfdwDzccTCbjl::getStartTime, cbjl.getEndTime())
                        .apply(!"".equals(sql), sql)
                        .eq(cbjl.getGroupId() > -1, VDfdwDzccTCbjl::getGroupId, cbjl.getGroupId())
                        .eq(cbjl.getType() > 0, VDfdwDzccTCbjl::getType, cbjl.getType())
                        .orderByAsc(VDfdwDzccTCbjl::getType)
                        .orderByDesc(VDfdwDzccTCbjl::getCreateTime)
                );
            }

            XSSFSheet sheet = workbook.createSheet("GPS异常记录");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(cbjl.getGroupName() + "加班车辆信息");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("车型");
            row.createCell(1).setCellValue("车牌");
            row.createCell(2).setCellValue("部门");
            row.createCell(3).setCellValue("拔掉时间");
            row.createCell(4).setCellValue("插入时间");
            row.createCell(5).setCellValue("告警类型");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getCarMold());
                row.createCell(1).setCellValue(list.get(i).getLicensePlate());
                row.createCell(2).setCellValue(list.get(i).getGroupName());
                if (list.get(i).getStartTime() != null) {
                    row.createCell(3).setCellValue(dateFormat.format(list.get(i).getStartTime()));
                }
                if (list.get(i).getEndTime() != null) {
                    row.createCell(4).setCellValue(dateFormat.format(list.get(i).getEndTime()));
                }
                if (list.get(i).getType() == 1) {
                    row.createCell(5).setCellValue("超速");
                } else if (list.get(i).getType() == 2) {
                    row.createCell(5).setCellValue("被拔");
                } else if (list.get(i).getType() == 3) {
                    row.createCell(5).setCellValue("超时");
                } else if (list.get(i).getType() == 4) {
                    row.createCell(5).setCellValue("设备异常");
                }
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================

            workbook.write(response.getOutputStream());

            return Result.ok();

        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
