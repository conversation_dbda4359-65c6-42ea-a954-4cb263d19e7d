package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
*
* @TableName RolePerson
*/
@TableName(value ="RolePerson", autoResultMap = true)
@Data
public class DzccRole<PERSON>erson implements Serializable {


    /**
    *
    */
    @TableField(value = "Id")
    @JSONField(name = "Id")

    private Integer Id;
    /**
    *
    */
    @TableField(value = "RoleId")
    @JSONField(name = "RoleId")

    private Integer RoleId;
    /**
    *
    */
    @TableField(value = "PersonId")
    @JSONField(name = "PersonId")

    private Integer PersonId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
