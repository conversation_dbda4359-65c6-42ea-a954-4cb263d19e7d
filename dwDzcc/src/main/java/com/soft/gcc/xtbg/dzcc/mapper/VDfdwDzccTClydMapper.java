package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CLYD】的数据库操作Mapper
* @createDate 2022-11-07 11:17:40
* @Entity com.yykj.app.dzcc.domain.VDfdwDzccTClyd
*/
public interface VDfdwDzccTClydMapper extends BaseMapper<VDfdwDzccTClyd> {

    List<VDfdwDzccTClyd> getClydList(
            @Param("clyd") VDfdwDzccTClyd clyd,
            @Param("startTime") LocalDate startTime,
            @Param("endTime") LocalDate endTime,
            @Param("person") DzccPersonEntity person
    );

    IPage<VDfdwDzccTClyd> getClydListPage(
            IPage<VDfdwDzccTClyd> list,
            @Param("clyd") VDfdwDzccTClyd clyd,
            @Param("startTime") LocalDate startTime,
            @Param("endTime") LocalDate endTime,
            @Param("person") DzccPersonEntity person
    );
}




