package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【GroupItem】的数据库操作Mapper
 * @createDate 2022-10-17 16:39:57
 * @Entity com.yykj.app.dzcc.domain.DzccGroupitem
 */
public interface DzccGroupitemMapper extends BaseMapper<DzccGroupitem> {

    List<DzccGroupitem> GetGroupListByRole(
            @Param(value = "person") DzccPersonEntity person,
            @Param(value = "parentId") int parentId,
            @Param(value = "isAll") Boolean isAll
    );

    List<DzccGroupitem> getCDZGroup(
            @Param(value = "personId") Integer personId,
            @Param(value = "isShowAll") Integer isShowAll,
            @Param(value = "dzccQx") Integer dzccQx,
            @Param(value = "id") Integer id);

    List<DzccGroupitem> GetGroupListByRole2(
            @Param(value = "isZGL") Integer isZGL,
            @Param(value = "id") Integer id,
            @Param(value = "topGroupId") Integer topGroupId,
            @Param(value = "parentId") int parentId,
            @Param(value = "isAll") Boolean isAll
    );

    List<DzccGroupitem> GetGroupListByRole12(
            @Param(value = "person") DzccPersonEntity person,
            @Param(value = "parentId") int parentId,
            @Param(value = "isAll") Boolean isAll
    );
}




