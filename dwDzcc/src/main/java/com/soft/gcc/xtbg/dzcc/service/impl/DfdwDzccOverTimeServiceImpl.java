package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTOverTime;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccOverTimeMapper;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccOverTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description 针对表【DFDW_DZCC_T_OVERTIME_SET(加班费计算规则配置表)】的数据库操作Service
*/
@Service
public class DfdwDzccOverTimeServiceImpl extends ServiceImpl<DfdwDzccOverTimeMapper, DfdwDzccTOverTime>
    implements DfdwDzccOverTimeService {

    @Autowired
    DfdwDzccOverTimeMapper dfdwDzccOverTimeMapper;
    @Autowired
    DfdwTDictDataService dfdwTDictDataService;


    /**
     * 新增
     * @param dfdwDzccTOverTime
     * @return
     */
    @Override
    public int createOverTime(DfdwDzccTOverTime dfdwDzccTOverTime) {
        int row = this.dfdwDzccOverTimeMapper.insert(dfdwDzccTOverTime);
        return row;
    }

    /**
     * 删除
     * @return
     */
    @Override
    public void deleteOverTime(Long id) {
         this.dfdwDzccOverTimeMapper.deleteById(id);
    }

    /**
     * 修改
     * @return
     */
    @Override
    public void updateOverTime(DfdwDzccTOverTime dfdwDzccTOverTime) {
        this.dfdwDzccOverTimeMapper.updateById(dfdwDzccTOverTime);
    }

    /**
     * 获取加班计算规则page
     * @return
     */
    @Override
    public IPage<DfdwDzccTOverTime> getListPage(DfdwDzccTOverTime dfdwDzccTOverTime) {
        //分页参数
        Page<DfdwDzccTOverTime> pageParam = new Page<>(dfdwDzccTOverTime.getPageNum(), dfdwDzccTOverTime.getPageSize());
        //分页查询
        IPage<DfdwDzccTOverTime> list = this.baseMapper.selectOverTimePage(pageParam,dfdwDzccTOverTime);
        return list;
    }
}




