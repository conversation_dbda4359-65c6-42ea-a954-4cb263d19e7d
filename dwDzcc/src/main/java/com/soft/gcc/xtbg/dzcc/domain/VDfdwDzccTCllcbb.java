package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 
* @TableName V_DFDW_DZCC_T_CLLCBB
*/
@TableName(value ="V_DFDW_DZCC_T_CLLCBB", autoResultMap = true)
@Data
public class VDfdwDzccTCllcbb implements Serializable {


    /**
    * 
    */
    @TableField(value = "id")
    @JSONField(name = "id")
    
    private Integer id;
    /**
    * 
    */
    @TableField(value = "driverId")
    @JSONField(name = "driverId")
    
    private Integer driverId;
    /**
    * 
    */
    @TableField(value = "date")
    @JSONField(name = "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;
    /**
    * 
    */
    @TableField(value = "mileage")
    @JSONField(name = "mileage")
    
    private Integer mileage;
    /**
    * 
    */
    @TableField(value = "imgFileId")
    @JSONField(name = "imgFileId")
    
    private Integer imgFileId;
    /**
    * 
    */
    @TableField(value = "isSubstitute")
    @JSONField(name = "isSubstitute")
    
    private Integer isSubstitute;
    /**
    * 
    */
    @TableField(value = "substituteBeginDate")
    @JSONField(name = "substituteBeginDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date substituteBeginDate;
    /**
    * 
    */
    @TableField(value = "substituteEndDate")
    @JSONField(name = "substituteEndDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date substituteEndDate;
    /**
    * 
    */
    @TableField(value = "createUserId")
    @JSONField(name = "createUserId")
    
    private Integer createUserId;
    /**
    * 
    */
    @TableField(value = "approvingState")
    @JSONField(name = "approvingState")
    
    private Integer approvingState;
    /**
    * 
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")
    
    private String driverName;
    /**
    * 
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")
    
    private Integer carId;
    /**
    * 
    */
    @TableField(value = "licencePlate")
    @JSONField(name = "licencePlate")
    
    private String licencePlate;
    /**
    * 
    */
    @TableField(value = "createUserName")
    @JSONField(name = "createUserName")
    
    private String createUserName;
    /**
    * 
    */
    @TableField(value = "TopGroupId")
    @JSONField(name = "TopGroupId")
    
    private Integer TopGroupId;
    /**
    * 
    */
    @TableField(value = "TopGroupName")
    @JSONField(name = "TopGroupName")
    
    private String TopGroupName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
