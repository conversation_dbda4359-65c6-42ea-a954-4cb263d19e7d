package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.*;
import com.soft.gcc.xtbg.dzcc.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CCLC_STATUS_DAILY(电子出车-出车单-每日出车情况)】的数据库操作Service实现
 * @createDate 2024-10-15 15:43:10
 */
@Service
public class IDfdwDzccTCclcStatusDailyServiceImpl extends ServiceImpl<DfdwDzccTCclcStatusDailyMapper, DfdwDzccTCclcStatusDaily>
        implements IDfdwDzccTCclcStatusDailyService {

    @Autowired
    private IDzccPersonService dzccPersonService;
    @Autowired
    private IDfdwDzccTCclcService cclcService;
    @Autowired
    private IDzccDictionaryvalueService dictionaryValueService;
    @Autowired
    private DfdwDzccTAddressMapper addressMapper;
    @Resource
    private DfdwDzccTCarmoveMapper carmoveMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private DfdwDzccTClglMapper clglMapper;
    @Resource
    private DfdwDzccTClbgMapper clbgMapper;
    @Resource
    private IDfdwDzccTCarstatusdailyService carstatusdailyService;


    @Override
    public List<DfdwDzccTCclcStatusDaily> getCcdStatusDaily(DfdwDzccTCclcStatusDaily cclcStatusDaily) {
        List<DfdwDzccTCclcStatusDaily> cclcStatusDailyList = this.list(new QueryWrapper<DfdwDzccTCclcStatusDaily>()
                .eq("YEAR(moveDate)", cclcStatusDaily.getYear())
                .eq("MONTH(moveDate)", cclcStatusDaily.getMonth())
                .lambda()
                .eq(cclcStatusDaily.getDriveid() != null, DfdwDzccTCclcStatusDaily::getDriveid, cclcStatusDaily.getDriveid())
                .orderByAsc(DfdwDzccTCclcStatusDaily::getMovedate)
        );
        List<LocalDate> dateList = cclcStatusDailyList.stream().map(DfdwDzccTCclcStatusDaily::getMovedate).distinct().collect(Collectors.toList());
        List<DfdwDzccTCclcStatusDaily> resultList = new ArrayList<>();
        for (LocalDate date : dateList) {
            List<DfdwDzccTCclcStatusDaily> list = cclcStatusDailyList.stream().filter(cclc -> cclc.getMovedate().equals(date)).collect(Collectors.toList());
            if (!list.isEmpty()) {
                DfdwDzccTCclcStatusDaily item = new DfdwDzccTCclcStatusDaily();
                item.setMovedate(date);
                item.setDriveid(cclcStatusDaily.getDriveid());
                item.setCdsNum(list.stream().filter(p -> "出大市".equals(p.getStatus())).map(DfdwDzccTCclcStatusDaily::getYwid).distinct().count());
                item.setKqyNum(list.stream().filter(p -> "跨区域".equals(p.getStatus())).map(DfdwDzccTCclcStatusDaily::getYwid).distinct().count());
                item.setBqyNum(list.stream().filter(p -> "本区域".equals(p.getStatus())).map(DfdwDzccTCclcStatusDaily::getYwid).distinct().count());
                item.setYzNum(list.stream().filter(DfdwDzccTCclcStatusDaily::getIstoyz).map(DfdwDzccTCclcStatusDaily::getYwid).distinct().count());
                resultList.add(item);
            }
        }
        return resultList;
    }

    @Override
    public void calcStatusDaily(Integer lcId) {
        try {


            //高德AK
            List<DzccDictionaryvalue> dictionaryValueList = dictionaryValueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .eq(DzccDictionaryvalue::getTitleID, 970221).eq(DzccDictionaryvalue::getContent, "高德地图AK"));
            String AK = "";
            if (dictionaryValueList.size() > 0) {
                AK = dictionaryValueList.get(0).getParameter();
            }
            DfdwDzccTCclc cclc = cclcService.getById(lcId);

            DzccPerson user = dzccPersonService.getById(cclc.getApplyUserId());

            //获取子记录 已审批通过的
            List<DfdwDzccTCclc> cclcList = cclcService.list(new LambdaQueryWrapper<DfdwDzccTCclc>().eq(DfdwDzccTCclc::getMainId, lcId).eq(DfdwDzccTCclc::getApproveState, 2));
            List<Integer> ids = cclcList.stream().map(DfdwDzccTCclc::getId).collect(Collectors.toList());
            ids.add(lcId);
            //获取所有目的地且已审批通过
            List<DfdwDzccTAddress> addressList = addressMapper.selectList(new LambdaQueryWrapper<DfdwDzccTAddress>()
                    .in(DfdwDzccTAddress::getLcId, ids).eq(DfdwDzccTAddress::getArriveState, 1).orderByAsc(DfdwDzccTAddress::getLcId));
            LocalDate startDate = cclc.getCcOpenTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate endDate = cclc.getExecuteTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            List<DfdwDzccTCarmove> carmoveList = carmoveMapper.selectList(new LambdaQueryWrapper<DfdwDzccTCarmove>()
                    .eq(DfdwDzccTCarmove::getCarId, cclc.getCarId())
                    .eq(DfdwDzccTCarmove::getDriveId, cclc.getDriveId())
                    .between(DfdwDzccTCarmove::getCurDate, startDate, endDate)
            );
            for (LocalDate currDate = startDate; !currDate.isAfter(endDate); currDate = currDate.plusDays(1)) {
                List<DfdwDzccTCclcStatusDaily> dailys = this.list(new LambdaQueryWrapper<DfdwDzccTCclcStatusDaily>()
                        .eq(DfdwDzccTCclcStatusDaily::getYwid, lcId)
                        .eq(DfdwDzccTCclcStatusDaily::getCarid, cclc.getCarId())
                        .eq(DfdwDzccTCclcStatusDaily::getDriveid, cclc.getDriveId())
                        .eq(DfdwDzccTCclcStatusDaily::getMovedate, Date.from(currDate.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                );
                DfdwDzccTCclcStatusDaily daily = null;
                if (!dailys.isEmpty()) {
                    daily = dailys.get(0);
                }
                if (daily == null) {
                    daily = new DfdwDzccTCclcStatusDaily();
                    daily.setYwid(lcId);
                    daily.setCarid(cclc.getCarId());
                    daily.setDriveid(cclc.getDriveId());
                    daily.setMovedate(currDate);
                    daily.setIstoyz(false);
                    daily.setCreateuserid(user.getId());
                    daily.setUpdateuserid(user.getId());
                    daily.setCreatetime(LocalDateTime.now());
                    daily.setUpdatetime(LocalDateTime.now());
                } else {
                    daily.setUpdateuserid(user.getId());
                    daily.setUpdatetime(LocalDateTime.now());
                }
                LocalDate finalCurrDate = currDate;
                List<DfdwDzccTCarmove> currList = carmoveList.stream().filter(item -> item.getCurDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().equals(finalCurrDate)).collect(Collectors.toList());
                List<DfdwDzccTAddress> currAddressList = addressList.stream().filter(item -> {
                    boolean isTrue = false;
                    for (DfdwDzccTCarmove carMove : currList) {
                        if (item.getProvinces().equals(carMove.getProvinces()) && item.getCity().equals(carMove.getCity())) {
                            if (item.getCity() != 123) {
                                isTrue = true;
                                break;
                            } else if (item.getArea().equals(carMove.getArea())) {
                                isTrue = true;
                                break;
                            }
                        }
                    }
                    return isTrue;
                }).collect(Collectors.toList());
                if (currAddressList.stream().anyMatch(item -> item.getCity() != 123)) {
                    daily.setStatus("出大市");
                } else {
                    List<Integer> nbAreaList = currAddressList.stream().filter(item -> item.getCity() == 123).map(DfdwDzccTAddress::getArea).distinct().collect(Collectors.toList());
                    //从redis中获取部门所在区域（停车点，办公点），防止多次读取
                    String key = "DZCC_CAR_GROUP_AREA::" + cclc.getCarId() + "_" + cclc.getApplyTopDeptId();
                    String carGroupArea = redisTemplate.opsForValue().get(key);
                    //不存在重新取值赋值
                    if (carGroupArea == null) {
                        //查询车辆停车地点和办公地点gps
                        List<DfdwDzccTClgl> clglList = clglMapper.selectList(new LambdaQueryWrapper<DfdwDzccTClgl>()
                                .eq(DfdwDzccTClgl::getCarId, cclc.getCarId())
                                .eq(DfdwDzccTClgl::getGroupId, cclc.getApplyTopDeptId())
                        );
                        if (!clglList.isEmpty()) {
                            //停车点区域Id
                            Integer tcdQyId = 0;
                            //办公点区域Id
                            Integer bgdQyId = 0;
                            if (clglList.get(0).getLongitude() != null && clglList.get(0).getLatitude() != null) {
                                //根据经纬度反解析 省市区
                                tcdQyId = carstatusdailyService.getAreaId(AK, clglList.get(0).getLatitude(), clglList.get(0).getLongitude());
                            }
                            //办公点
                            DfdwDzccTClbg clbg = clbgMapper.selectById(clglList.get(0).getUnitId());
                            if (clbg != null) {
                                //根据经纬度反解析 省市区
                                bgdQyId = carstatusdailyService.getAreaId(AK, clbg.getLatitude(), clbg.getLongitude());
                            }
                            carGroupArea = tcdQyId + "," + bgdQyId;
                            redisTemplate.opsForValue().set(key, carGroupArea, 30, TimeUnit.MINUTES);
                        }
                    }
                    //白名单区域
                    List<Integer> groupAreas = new ArrayList<>();
                    if (carGroupArea != null) {
                        String[] carGroupAreaSplit = carGroupArea.split(",");
                        groupAreas.add(Integer.valueOf(carGroupAreaSplit[0]));
                        groupAreas.add(Integer.valueOf(carGroupAreaSplit[1]));

                        //海曙江北之间就是本区域,所以设置白名单，有海曙添加江北
                        if (carGroupArea.contains("1196")) {
                            groupAreas.add(1197);
                        } else if (carGroupArea.contains("1197")) {
                            groupAreas.add(1196);
                        }
                    }

                    //判断实际到达的区域除了白名单所在点是否还去了其他地方
                    List<Integer> integerList = nbAreaList.stream().filter(p -> !groupAreas.contains(p)).collect(Collectors.toList());
                    if (!integerList.isEmpty()) {
                        daily.setStatus("跨区域");
                        if (integerList.contains(1200)) {
                            daily.setIstoyz(true);
                        }
                    } else {
                        daily.setStatus("本区域");
                    }
                }
                this.saveOrUpdate(daily);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}




