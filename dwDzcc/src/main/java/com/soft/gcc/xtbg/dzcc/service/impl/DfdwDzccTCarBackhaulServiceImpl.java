package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarBackhaul;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTCarBackhaulService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarBackhaulMapper;
import com.soft.gcc.xtbg.dzcc.service.DzccCarService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTClbgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class DfdwDzccTCarBackhaulServiceImpl extends ServiceImpl<DfdwDzccTCarBackhaulMapper, DfdwDzccTCarBackhaul>
    implements DfdwDzccTCarBackhaulService{
    @Autowired
    GroupitemService groupitemService;
    @Autowired
    DzccCarService dzccCarService;
    @Autowired
    IDfdwDzccTClbgService clbgService;
    @Override
    public IPage<DfdwDzccTCarBackhaul> GetBackHaulList(DfdwDzccTCarBackhaul backhaul) {
        Page<DfdwDzccTCarBackhaul> page = new Page<>(backhaul.getPageNum(), backhaul.getPageSize());
        IPage<DfdwDzccTCarBackhaul> list = baseMapper.GetBackHaulList(page,backhaul.getGroupId(),backhaul.getUnitName(),
                backhaul.getLicencePlate(),backhaul.getType());
//        list.getRecords().stream().forEach(s->{
//            Integer groupId = s.getGroupId();
//            Groupitem groupitem = groupitemService.getById(groupId);
//            if(groupitem!=null){
//                s.setGroupName(groupitem.getGroupname());
//            }
//            Integer carId = s.getCarId();
//            DzccCar dzccCar = dzccCarService.getById(carId);
//            if(dzccCar!=null){
//                s.setLicencePlate(dzccCar.getLicencePlate());
//            }
//            DfdwDzccTClbg dzccTClbg = clbgService.getById(s.getBgdId());
//            if(dzccTClbg!=null){
//                s.setUnitName(dzccTClbg.getUnitName());
//            }
//        });
        return list;
    }
}




