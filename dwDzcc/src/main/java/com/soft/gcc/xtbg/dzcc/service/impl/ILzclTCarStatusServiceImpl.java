package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.LzclTCarStatus;
import com.soft.gcc.xtbg.dzcc.service.ILzclTCarStatusService;
import com.soft.gcc.xtbg.dzcc.mapper.LzclTCarStatusMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Status(零租车辆-车辆状态表)】的数据库操作Service实现
* @createDate 2024-10-17 09:49:40
*/
@Service
public class ILzclTCarStatusServiceImpl extends ServiceImpl<LzclTCarStatusMapper, LzclTCarStatus>
    implements ILzclTCarStatusService {

}




