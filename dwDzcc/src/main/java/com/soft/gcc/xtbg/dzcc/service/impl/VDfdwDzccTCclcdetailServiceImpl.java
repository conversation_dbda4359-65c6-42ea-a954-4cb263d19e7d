package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCclcdetail;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekService;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCclcdetailService;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCclcdetailMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.logging.SimpleFormatter;

/**
 * <AUTHOR>
 * @description 针对表【V_DFDW_DZCC_T_CCLCDetail】的数据库操作Service实现
 * @createDate 2023-11-16 09:43:56
 */
@Service
public class VDfdwDzccTCclcdetailServiceImpl extends ServiceImpl<VDfdwDzccTCclcdetailMapper, VDfdwDzccTCclcdetail>
        implements IVDfdwDzccTCclcdetailService {

    @Autowired
    private DfdwDzccTWeekService dfdwDzccTWeekService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public List<VDfdwDzccTCclcdetail> getCCLCDetailList(Integer selectType, Integer driveId, Integer year, Integer month,Integer type) {


        //查询指定月份指定表中数据是否存在，不存在新增，存在修改
        Calendar calendar1 = Calendar.getInstance();
        // 设置当月第一天
        calendar1.set(year, month - 1, 1);
        Date firstDayOfMonth = calendar1.getTime();
        // 设置当月最后一天
        calendar1.set(Calendar.DAY_OF_MONTH, calendar1.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastDayOfMonth = calendar1.getTime();
        String startDate = sdf.format(firstDayOfMonth);
        String endDate = sdf.format(lastDayOfMonth);


        List<VDfdwDzccTCclcdetail> list = baseMapper.getCCLCDetailList(selectType, driveId, startDate, endDate);

        if (selectType == 2 && type == 1) {
            //追加 增加延时数 数据
            List<DfdwDzccTWeek> weekList = dfdwDzccTWeekService.list(new LambdaQueryWrapper<DfdwDzccTWeek>()
                    .eq(DfdwDzccTWeek::getYear, year).eq(DfdwDzccTWeek::getMonth, month)
                    .eq(DfdwDzccTWeek::getApproveState, 2).eq(DfdwDzccTWeek::getDriveId, driveId));
            for (int i = 0; i < weekList.size(); i++) {
                VDfdwDzccTCclcdetail cclcdetail = new VDfdwDzccTCclcdetail();
                String dateTemp = weekList.get(i).getStartTime() + "至" + weekList.get(i).getEndTime();
                cclcdetail.setDateTemp(dateTemp);
                cclcdetail.setDriveId(driveId);
                cclcdetail.setOvertimeHoursAll(weekList.get(i).getAppendDelayHour());
                cclcdetail.setDayType(1);
                cclcdetail.setOvertimeHoursAm(new BigDecimal("0"));
                cclcdetail.setOvertimeHoursPm(new BigDecimal("0"));
                cclcdetail.setOvertimeHoursNoon(new BigDecimal("0"));
                list.add(cclcdetail);
            }



        }


        return list;
    }
}




