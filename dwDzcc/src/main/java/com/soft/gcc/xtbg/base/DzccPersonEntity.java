package com.soft.gcc.xtbg.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yyszc.wpbase.ventity.PersonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DzccPersonEntity extends PersonEntity implements Serializable{

    /**
     * 电子出车角色（1车辆总管理 2车辆管理人员 3其他人员 4车队长）
     */
    @TableField(exist = false)
    private Integer dzccQx;
    /**
     * 电子出车角色
     */
    @TableField(exist = false)
    private List<Integer> qxs;
}
