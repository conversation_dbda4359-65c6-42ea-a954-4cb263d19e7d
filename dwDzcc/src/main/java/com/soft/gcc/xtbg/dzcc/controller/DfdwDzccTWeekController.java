package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekService;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dzcc/t_week")
public class DfdwDzccTWeekController extends DzccBaseController {

    @Resource
    public DfdwDzccTWeekService dfdwDzccTWeekService;

    @RequestMapping("/list")
    //@PreAuthorize("@ss.hasAnyPermi('NDWCCO1YS01QX01')")
    public Result<Object> list(@RequestBody DfdwDzccTWeek dfdwDzccTWeek) {
        IPage<DfdwDzccTWeek> res = this.dfdwDzccTWeekService.listPage(dfdwDzccTWeek);
        return Result.ok(res);
    }

    //集团属地化管理-车队工作量-获取列表
    @RequestMapping("/getWorkSummaryList")
    //@PreAuthorize("@ss.hasAnyPermi('NDWCCO1YS01QX01')")
    public Result<Object> getWorkSummaryList(@RequestBody DfdwDzccTWeek dfdwDzccTWeek) throws Exception {
        IPage<DfdwDzccTWeek> res = this.dfdwDzccTWeekService.getWorkSummaryList(dfdwDzccTWeek);
        return Result.ok(res);
    }

    //集团属地化管理-车队工作量-导出列表
    @RequestMapping("/exportWorkSummary")
    public Result<Object> exportWorkSummary(@RequestBody DfdwDzccTWeek dfdwDzccTWeek, HttpServletResponse response) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // 获取数据
            dfdwDzccTWeek.setPageSize(-1);
            IPage<DfdwDzccTWeek> res = this.dfdwDzccTWeekService.getWorkSummaryList(dfdwDzccTWeek);
            List<DfdwDzccTWeek> list = res.getRecords();

            // 计算需要合并的行
            int index = 0;
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    list.get(i).setMergeCount(1);
                } else {
                    // 判断当前行是否与上一行司机相同
                    if (list.get(i).getDriveId().equals(list.get(index).getDriveId())) {
                        list.get(index).setMergeCount(list.get(index).getMergeCount() + 1);
                        list.get(i).setMergeCount(0);
                    } else {
                        index = i;
                        list.get(i).setMergeCount(1);
                    }
                }
            }

            // 创建工作表
            XSSFSheet sheet = workbook.createSheet("工作汇总表");
            // 创建居中样式
            CellStyle centerStyle = workbook.createCellStyle();
            centerStyle.setAlignment(HorizontalAlignment.CENTER);
            centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建标题行
            XSSFRow titleRow = sheet.createRow(0);
            XSSFCell titleCell = titleRow.createCell(0);
            String title = String.format("集团本部车队量汇总表(%s年%s月第%s周)",
                    list.get(0).getYear(), list.get(0).getMonth(), list.get(0).getWeek());
            titleRow.createCell(0).setCellValue(title);
            titleCell.setCellValue(title);
            titleCell.setCellStyle(centerStyle);
            // 合并标题行
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

            // 设置列宽
            sheet.setColumnWidth(0, 10 * 256); // 序号
            sheet.setColumnWidth(1, 15 * 256); // 车牌
            sheet.setColumnWidth(2, 15 * 256); // 驾驶员
            sheet.setColumnWidth(3, 15 * 256); // 延时
            sheet.setColumnWidth(4, 15 * 256); // 增加延时
            sheet.setColumnWidth(5, 15 * 256); // 车队长确认
            sheet.setColumnWidth(6, 20 * 256); // 备注

            // 创建表头行
            XSSFRow headerRow = sheet.createRow(1);
            String[] headers = {"序号", "车牌", "驾驶员", "延时", "增加延时", "车队长确认", "备注"};
            for (int i = 0; i < headers.length; i++) {
                headerRow.createCell(i).setCellValue(headers[i]);
                XSSFCell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(centerStyle);
            }

            // 填充数据并合并单元格
            int rowNum = 2;
            for (int i = 0; i < list.size(); i++) {
                DfdwDzccTWeek item = list.get(i);
                XSSFRow row = sheet.createRow(rowNum + i);

                // 填充数据
                row.createCell(0).setCellValue(rowNum + i - 1); // 序号
                row.createCell(1).setCellValue(item.getLicencePlate());
                row.createCell(2).setCellValue(item.getDriveName());
                row.createCell(3).setCellValue(item.getOringOvertimeHours() != null ? item.getOringOvertimeHours().toString() : "");
                row.createCell(4).setCellValue(item.getAppendDelayHour() != null ? item.getAppendDelayHour().toString() : "");
                row.createCell(5).setCellValue(""); // 车队长确认
                row.createCell(6).setCellValue("");

                // 合并单元格
                if (item.getMergeCount() > 1) {
                    // 合并驾驶员列
                    sheet.addMergedRegion(new CellRangeAddress(
                            rowNum + i,
                            rowNum + i + item.getMergeCount() - 1,
                            2, 2
                    ));
                    // 合并延时列
                    sheet.addMergedRegion(new CellRangeAddress(
                            rowNum + i,
                            rowNum + i + item.getMergeCount() - 1,
                            3, 3
                    ));
                    // 合并增加延时列
                    sheet.addMergedRegion(new CellRangeAddress(
                            rowNum + i,
                            rowNum + i + item.getMergeCount() - 1,
                            4, 4
                    ));
                    sheet.addMergedRegion(new CellRangeAddress(
                            rowNum + i,
                            rowNum + i + item.getMergeCount() - 1,
                            5, 5
                    ));
                    sheet.addMergedRegion(new CellRangeAddress(
                            rowNum + i,
                            rowNum + i + item.getMergeCount() - 1,
                            6, 6
                    ));
                }
            }
            // 获取最后一行的行号
            int lastDataRow = rowNum + list.size();



            // 添加合计行
            XSSFRow totalRow = sheet.createRow(lastDataRow);
            XSSFCell totalCell = totalRow.createCell(0);
            totalCell.setCellValue("合计");
            totalCell.setCellStyle(centerStyle);

            // 合并前三个单元格
            sheet.addMergedRegion(new CellRangeAddress(lastDataRow, lastDataRow, 0, 2));
            //合计计算
            // 计算延时总和
            BigDecimal totalOvertime =BigDecimal.ZERO;
            BigDecimal totalAppendDelay = BigDecimal.ZERO;
            Set<Integer> calculatedDrivers = new HashSet<>();

            for (DfdwDzccTWeek item : list) {
                // 如果该驾驶员已经计算过，则跳过
                if (calculatedDrivers.contains(item.getDriveId())) {
                    continue;
                }

                if (item.getOringOvertimeHours() != null) {
                    totalOvertime = totalOvertime.add(item.getOringOvertimeHours());
                }
                if (item.getAppendDelayHour() != null) {
                    totalAppendDelay = totalAppendDelay.add(item.getAppendDelayHour());
                }

                // 将已计算的驾驶员ID添加到Set中
                calculatedDrivers.add(item.getDriveId());
            }

            // 设置延时合计
            totalRow.createCell(3).setCellValue(String.format("%.1f", totalOvertime));
            // 设置增加延时合计
            totalRow.createCell(4).setCellValue(String.format("%.1f", totalAppendDelay));


            // 2. 添加审核和车队长行
            XSSFRow auditRow = sheet.createRow(lastDataRow + 4);
            auditRow.createCell(2).setCellValue("审核:");
            auditRow.createCell(5).setCellValue("车队长:");

            // 3. 添加日期行
            XSSFRow dateRow = sheet.createRow(lastDataRow + 6);
            dateRow.createCell(5).setCellValue("日期:");

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    new String("工作汇总表.xlsx".getBytes("gb2312"), "ISO8859-1"));

            // 输出文件
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @PostMapping("getWeeks")
    public Result<Object> getWeeks(@RequestBody DfdwDzccTWeek dfdwDzccTWeek) {
        List<DfdwDzccTWeekMain> res = this.dfdwDzccTWeekService.getWeeks(dfdwDzccTWeek);
        return Result.ok(res);
    }


    @PostMapping("editAppendDelayHour")
    public Result<Object> editAppendDelayHour(@RequestBody DfdwDzccTWeek dfdwDzccTWeek) {
        dfdwDzccTWeek.setDzccPersonEntity(getDzccPerson());
        return this.dfdwDzccTWeekService.editAppendDelayHour(dfdwDzccTWeek);
    }

    @PostMapping("syncDriver")
    public Result<Object> syncDriver(@RequestBody DfdwDzccTWeek dfdwDzccTWeek) {
        this.dfdwDzccTWeekService.operaDriverToWeekTab(dfdwDzccTWeek);
        return Result.ok();
    }

    @PostMapping("getDriveList")
    public Result<Object> getDriverList(@RequestBody DfdwDzccTWeek dfdwDzccTWeek) {

        return Result.ok(this.dfdwDzccTWeekService.getDriverList(dfdwDzccTWeek));
    }

}
