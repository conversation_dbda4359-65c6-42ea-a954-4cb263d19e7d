package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccLzclTCarRent;
import com.soft.gcc.xtbg.dzcc.entity.LocusDto;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Rent(零租车辆--车辆借用记录)】的数据库操作Service
* @createDate 2022-10-19 17:04:48
*/
public interface IDzccLzclTCarRentService extends IService<DzccLzclTCarRent> {

    Result<Object> getLocusList(DzccPersonEntity person, LocusDto locusDto);

    Result<Object> getNotLocusList(DzccPersonEntity person, LocusDto locusDto);
}
