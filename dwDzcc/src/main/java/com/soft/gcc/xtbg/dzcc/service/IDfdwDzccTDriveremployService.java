package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy;


/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_DRIVEREMPLOY(司机聘用表)】的数据库操作Service
* @createDate 2023-07-27 10:32:23
*/
public interface IDfdwDzccTDriveremployService extends IService<DfdwDzccTDriveremploy> {

    Result<Object> AddEmployPerson(DzccPersonEntity person, DfdwDzccTDriveremploy emoloy);

    Result<Object> GetEmployList(DzccPersonEntity person, DfdwDzccTDriveremploy emoloy);

    Result<Object> EditEmployPerson(DzccPersonEntity person, DfdwDzccTDriveremploy emoloy);
}
