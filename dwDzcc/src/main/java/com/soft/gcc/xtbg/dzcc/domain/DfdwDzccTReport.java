package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车-里程统计
* @TableName DFDW_DZCC_T_REPORT
*/
@TableName(value ="DFDW_DZCC_T_REPORT", autoResultMap = true)
@Data
public class DfdwDzccTReport extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 年
    */
    @TableField(value = "year")
    @JSONField(name = "year")

    private Integer year;
    /**
    * 月份
    */
    @TableField(value = "month")
    @JSONField(name = "month")

    private Integer month;
    /**
    * 司机Id
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
    * 车Id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 开始日期
    */
    @TableField(value = "startDate")
    @JSONField(name = "startDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    /**
    * 结束日期
    */
    @TableField(value = "endDate")
    @JSONField(name = "endDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    /**
    * 上传里程
    */
    @TableField(value = "mileage")
    @JSONField(name = "mileage")

    private Integer mileage;
    /**
    * 实际值(想减之后的值)
    */
    @TableField(value = "actualValue")
    @JSONField(name = "actualValue")

    private Integer actualValue;
    /**
    * 创建时间
    */
    @TableField(value = "cerateTime")
    @JSONField(name = "cerateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cerateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
