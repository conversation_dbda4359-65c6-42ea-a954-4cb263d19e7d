package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.LzclTCarStatusMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 里程报表
 */
@RequestMapping("/dzcc/bb")
@RestController
public class DzccBbController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTZbbService zbbService;
    @Autowired
    IVDfdwDzccTCcdService ccdService;
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;
    @Autowired
    IDfdwDzccTCarstatusdailyService carstatusdailyService;
    @Autowired
    private IDfdwDzccTCclcService cclcService;
    @Autowired
    private LzclTCarStatusMapper carStatusMapper;

    @RequestMapping("/GetZbbList")
    @PreAuthorize("@ss.hasPermi('NDWCC01BB01QX01')")
    public Result<Object> GetZbbList(@RequestBody VDfdwDzccTZbb zbb) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.GetZbbList(dzccPersonEntity, zbb);
    }

    @RequestMapping("/LockMonth")
    @PreAuthorize("@ss.hasPermi('NDWCC01BB01QX03')")
    public Result<Object> LockMonth(@RequestBody DfdwDzccTLock lock) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.LockMonth(dzccPersonEntity, lock);
    }

    @RequestMapping("/UnLockMonth")
    @PreAuthorize("@ss.hasPermi('NDWCC01BB01QX04')")
    public Result<Object> UnLockMonth(@RequestBody DfdwDzccTLock lock) {
        return zbbService.UnLockMonth(lock);
    }

    @RequestMapping("/ShowLockMonth")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BB01QX03,NDWCC01BB01QX04')")
    public Result<Object> ShowLockMonth(@RequestBody DfdwDzccTLock lock) {
        return zbbService.ShowLockMonth(lock);
    }

    @RequestMapping("/GetSbbList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BB01QX01,NDWCC01CD05QX03')")
    public Result<Object> GetSbbList(@RequestBody VDfdwDzccTZbb zbb) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.GetSbbList(dzccPersonEntity, zbb);
    }

    @RequestMapping("/GettbbList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BB01QX01,NDWCC01CD05QX03')")
    public Result<Object> GettbbList(@RequestBody DfdwDzccTCarmove carmove) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.GettbbList(dzccPersonEntity, carmove);
    }

    @RequestMapping("/DownLoadZbb")
    @PreAuthorize("@ss.hasPermi('NDWCC01BB01QX02')")
    public Result<Object> DownLoadZbb(@RequestBody VDfdwDzccTZbb zbb, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);
            List<Integer> groupIds = new ArrayList<>();
            if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                groupIds.add(zbb.getTopGroupId());
            } else {
                List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(person, 1, false);
                groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            }
            List<VDfdwDzccTZbb> zbbs = zbbService.queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, person);

            int index = 0;
            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setYearMonth(zbb.getYearMonth().substring(0, 7));
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    if (zbb.getCarOrPerson() == 0) {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getCarId().equals(zbbs.get(index).getCarId())) {
                            zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    } else {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (zbbs.get(i).getDriveId().equals(zbbs.get(index).getDriveId())) {
                            zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                            zbbs.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            zbbs.get(i).setCount(1);
                        }
                    }
                }
            }

            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(zbb.getTopGroupName() + "电子出车报表");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            if (zbb.getCarOrPerson() == 0) {
                row.createCell(0).setCellValue("月份");
                row.createCell(1).setCellValue("申请单位");
                row.createCell(2).setCellValue("车型");
                row.createCell(3).setCellValue("车牌");
                row.createCell(4).setCellValue("司机");
                row.createCell(5).setCellValue("工作里程");
                row.createCell(6).setCellValue("车辆里程");
                row.createCell(7).setCellValue("车辆标识");
                row.createCell(8).setCellValue("实际里程");
            } else {
                row.createCell(0).setCellValue("月份");
                row.createCell(1).setCellValue("申请单位");
                row.createCell(2).setCellValue("车型");
                row.createCell(3).setCellValue("司机");
                row.createCell(4).setCellValue("车牌");
                row.createCell(5).setCellValue("车辆里程");
                row.createCell(6).setCellValue("工作里程");
                row.createCell(7).setCellValue("车辆标识");
                row.createCell(8).setCellValue("实际里程");
            }

            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setAllValue(0);
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(i).setAllValue(zbbs.get(i).getAllValue() + zbbs.get(j).getWorkValue());
                }
                // 列合并
                if (zbbs.get(i).getCount() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 6, 6));
                }

                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(zbbs.get(i).getYearMonth());
                row.createCell(1).setCellValue(zbbs.get(i).getTopGroupName());
                row.createCell(2).setCellValue(zbbs.get(i).getCarMold());
                if (zbb.getCarOrPerson() == 0) {
                    row.createCell(3).setCellValue(zbbs.get(i).getLicencePlate());
                    row.createCell(4).setCellValue(zbbs.get(i).getRealName());
                } else {
                    row.createCell(3).setCellValue(zbbs.get(i).getRealName());
                    row.createCell(4).setCellValue(zbbs.get(i).getLicencePlate());
                }
                row.createCell(5).setCellValue(zbbs.get(i).getWorkValue());
                row.createCell(6).setCellValue(zbbs.get(i).getAllValue());
                if (zbbs.get(i).getCarTag() == 0) {
                    row.createCell(7).setCellValue("临租车辆");
                } else if (zbbs.get(i).getCarTag() == 1) {
                    row.createCell(7).setCellValue("特殊车辆");
                } else if (zbbs.get(i).getCarTag() == 2) {
                    row.createCell(7).setCellValue("产权车辆");
                }
                row.createCell(8).setCellValue((double) zbbs.get(i).getActualMileage() / 1000);
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("主报表".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @RequestMapping("/DownLoadSbb")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BB01QX02,NDWCC01CD05QX03')")
    public Result<Object> DownLoadSbb(@RequestBody VDfdwDzccTZbb zbb, HttpServletResponse response) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {

            LocalDate startDate = DateUtil.asLocalDate(zbb.getStartDate());
            LocalDate endDate1 = DateUtil.asLocalDate(zbb.getEndDate());
            LocalDate endDate = endDate1.plusDays(-1);

            List<DfdwDzccTCarmove> fbbInfoList = new ArrayList<>();
            List<DfdwDzccTCarstatusdaily> list = carstatusdailyService.list(new LambdaQueryWrapper<DfdwDzccTCarstatusdaily>()
                    .ge(DfdwDzccTCarstatusdaily::getCurDate, startDate)
                    .le(DfdwDzccTCarstatusdaily::getCurDate, endDate)
                    .eq(DfdwDzccTCarstatusdaily::getPersonId, zbb.getDriveId())
                    .eq(DfdwDzccTCarstatusdaily::getCarId, zbb.getCarId())
                    .eq(DfdwDzccTCarstatusdaily::getGroupId, zbb.getTopGroupId())
            );
            List<LzclTCarStatus> statusList = carStatusMapper.selectListByCarIdAndDate(zbb.getCarId(), zbb.getDriveId(), zbb.getTopGroupId(), startDate, endDate1);
            LocalDate finalStartDate = startDate;
            List<DfdwDzccTCclc> cclcList = cclcService.list(new QueryWrapper<DfdwDzccTCclc>()
                    .and(t -> t
                            .or(w -> w.le("CONVERT(date, ccOpenTime)", finalStartDate).ge("CONVERT(date, executeTime)", finalStartDate))
                            .or(w -> w.le("CONVERT(date, ccOpenTime)", endDate).ge("CONVERT(date, executeTime)", endDate))
                            .or(w -> w.ge("CONVERT(date, ccOpenTime)", finalStartDate).le("CONVERT(date, executeTime)", endDate))
                    )
                    .lambda()
                    .eq(DfdwDzccTCclc::getApproveState, 2)
                    .eq(DfdwDzccTCclc::getExecuteState, 2)
                    .eq(DfdwDzccTCclc::getCarId, zbb.getCarId())
                    .eq(DfdwDzccTCclc::getDriveId, zbb.getDriveId())
                    .eq(DfdwDzccTCclc::getApplyTopDeptId, zbb.getTopGroupId())
            );
            //当开始时间不大于结束时间，循环执行
            while (!startDate.isAfter(endDate)) {

                //所以日期转化为String，
                DfdwDzccTCarmove info = new DfdwDzccTCarmove();
                info.setDriveId(zbb.getDriveId());
                info.setCarId(zbb.getCarId());
                info.setCurDate(Date.from(startDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                LocalDate finalStartDate1 = startDate;
                DfdwDzccTCarstatusdaily daily = list.stream().filter(s -> s.getCurDate().isEqual(finalStartDate1)).findFirst().orElse(null);
                if (daily != null) {
                    info.setDayInfo(daily.getStatus());
                } else {
                    info.setDayInfo("本区域");
                }
                LzclTCarStatus status = statusList.stream().filter(s -> {
                    LocalDate date = DateUtil.asLocalDate(s.getCreateTime());
                    return date.isEqual(finalStartDate1);
                }).findFirst().orElse(null);
                if (status != null) {
                    info.setActualMileage(status.getActualMileage());
                } else {
                    info.setActualMileage(0L);
                }
                List<DfdwDzccTCclc> cclcs = cclcList.stream().filter(s -> {
                    LocalDate sDate = DateUtil.asLocalDate(s.getCcOpenTime());
                    LocalDate eDate = DateUtil.asLocalDate(s.getExecuteTime());
                    return !sDate.isAfter(finalStartDate1) && !eDate.isBefore(finalStartDate1);
                }).collect(Collectors.toList());
                info.setCcdNum(cclcs.size());
                fbbInfoList.add(info);
                //日期+1，继续执行
                startDate = startDate.plusDays(1);
            }

            fbbInfoList = fbbInfoList.stream().sorted(Comparator.comparing(DfdwDzccTCarmove::getCurDate)).collect(Collectors.toList());

            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(zbb.getLicencePlate() + "-" + zbb.getRealName() + "出车情况");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("日期");
            row.createCell(1).setCellValue("申请单位");
            row.createCell(2).setCellValue("车牌");
            row.createCell(3).setCellValue("出市情况");
            row.createCell(4).setCellValue("实际里程");
            row.createCell(5).setCellValue("出车单数");

            for (int i = 0; i < fbbInfoList.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(fbbInfoList.get(i).getCurDate()));
                row.createCell(1).setCellValue(zbb.getRealName());
                row.createCell(2).setCellValue(zbb.getLicencePlate());
                row.createCell(3).setCellValue(fbbInfoList.get(i).getDayInfo());
                if (fbbInfoList.get(i).getActualMileage() != null) {
                    row.createCell(4).setCellValue((double) fbbInfoList.get(i).getActualMileage() / 1000);
                } else {
                    row.createCell(4).setCellValue(0);
                }
                if (fbbInfoList.get(i).getCcdNum() != null) {
                    row.createCell(5).setCellValue(fbbInfoList.get(i).getCcdNum());
                } else {
                    row.createCell(5).setCellValue(0);
                }
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("主报表".getBytes("gb2312"), "ISO8859-1") + ".pdf");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @RequestMapping("/DownLoadTbb")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BB01QX02,NDWCC01CD05QX03')")
    public Result<Object> DownLoadTbb(@RequestBody DfdwDzccTCarmove carmove, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Instant instant = carmove.getCurDate().toInstant();
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDate localDate = instant.atZone(zoneId).toLocalDate();
            LocalDate endDate = localDate.plusDays(1);
            List<VDfdwDzccTCcd> ccdList = ccdService.list(new LambdaQueryWrapper<VDfdwDzccTCcd>()
                    .eq(VDfdwDzccTCcd::getCarId, carmove.getCarId())
                    .eq(VDfdwDzccTCcd::getApproveState, 2)
                    .eq(VDfdwDzccTCcd::getExecuteState, 2)
                    .ge(VDfdwDzccTCcd::getExecuteTime, localDate)
                    .le(VDfdwDzccTCcd::getCcOpenTime, endDate)
            );

            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 7));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(carmove.getLicencePlate() + "-" + carmove.getRealName() + new SimpleDateFormat("yyyy-MM-dd").format(carmove.getCurDate()) + "出车单");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("出车单");
            row.createCell(1).setCellValue("申请人");
            row.createCell(2).setCellValue("申请单位");
            row.createCell(3).setCellValue("出车时间");
            row.createCell(4).setCellValue("目的地");
            row.createCell(5).setCellValue("人数");
            row.createCell(6).setCellValue("天数");
            row.createCell(6).setCellValue("事由");

            for (int i = 0; i < ccdList.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(ccdList.get(i).getApplyNo());
                row.createCell(1).setCellValue(ccdList.get(i).getApplyUserName());
                row.createCell(2).setCellValue(ccdList.get(i).getApplyUserName());
                row.createCell(3).setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(ccdList.get(i).getCcOpenTime()));
                row.createCell(4).setCellValue(ccdList.get(i).getAddressInfo());
                row.createCell(5).setCellValue(ccdList.get(i).getApplyNum());
                row.createCell(6).setCellValue(ccdList.get(i).getCcDays());
                row.createCell(7).setCellValue(ccdList.get(i).getNote());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("主报表".getBytes("gb2312"), "ISO8859-1") + ".pdf");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

}
