package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCarmoveService;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCarmoveMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CARMOVE】的数据库操作Service实现
* @createDate 2022-11-11 15:26:25
*/
@Service
public class VDfdwDzccTCarmoveServiceImpl extends ServiceImpl<VDfdwDzccTCarmoveMapper, VDfdwDzccTCarmove>
    implements IVDfdwDzccTCarmoveService{

    @Override
    public List<VDfdwDzccTCarmove> getXcglListPage(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCarmove carmove, LocalDate startDate, LocalDate endDate) {
        return baseMapper.getXcglListPage(dzccPersonEntity, carmove, startDate, endDate);
    }

    @Override
    public List<VDfdwDzccTCarmove> getXcglList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCarmove carmove, LocalDate startDate, LocalDate endDate) {
        return baseMapper.getXcglList(dzccPersonEntity, carmove, startDate, endDate);
    }

    @Override
    public Long getXcglListCount(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCarmove carmove, LocalDate startDate, LocalDate endDate) {
        return baseMapper.getXcglListCount(dzccPersonEntity, carmove, startDate, endDate);
    }
}




