package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 光伏项目-地区表
 * @TableName GFXM_T_City
 */
@TableName(value ="GFXM_T_City")
@Data
public class GfxmTCity implements Serializable {
    /**
     * 
     */
    @TableId(value = "Id")
    private Integer id;

    /**
     * 父级
     */
    @TableField(value = "Pid")
    private Integer pid;

    /**
     * 
     */
    @TableField(value = "CityName")
    private String cityName;

    /**
     * 1省份 2市 3区
     */
    @TableField(value = "Type")
    private Integer type;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<GfxmTCity> children;

    public void addChild(GfxmTCity child) {
        this.children.add(child);
    }
    public GfxmTCity(Integer id, Integer pid, String cityName, Integer type) {
        this.id = id;
        this.pid = pid;
        this.cityName = cityName;
        this.type = type;
        this.children = new ArrayList<>();
    }
}