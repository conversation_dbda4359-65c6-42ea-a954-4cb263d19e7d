package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_SJTBLC】的数据库操作Service
* @createDate 2023-05-26 11:22:18
*/
public interface IVDfdwDzccTSjtblcService extends IService<VDfdwDzccTSjtblc> {

    Result<Object> GetSjtblcList(DzccPersonEntity person, VDfdwDzccTSjtblc sjtblc);
}
