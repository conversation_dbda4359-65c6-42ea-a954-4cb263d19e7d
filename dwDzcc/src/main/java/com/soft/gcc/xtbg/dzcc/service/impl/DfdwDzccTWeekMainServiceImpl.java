package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekMainService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTWeekMainMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_WEEK_MAIN(周期-主表)】的数据库操作Service实现
* @createDate 2024-12-19 14:03:46
*/
@Service
public class DfdwDzccTWeekMainServiceImpl extends ServiceImpl<DfdwDzccTWeekMainMapper, DfdwDzccTWeekMain>
    implements DfdwDzccTWeekMainService{

}




