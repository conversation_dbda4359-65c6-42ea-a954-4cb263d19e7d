package com.soft.gcc.xtbg.clwx.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_CLWX_T_BXD(车辆维修（新）-维修申请主表)】的数据库操作Mapper
* @createDate 2024-10-17 16:11:09
* @Entity com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd
*/
public interface DfdwClwxTBxdMapper extends BaseMapper<DfdwClwxTBxd> {

    IPage<DfdwClwxTBxd> getPage(Page<Object> objectPage, @Param("bxd") DfdwClwxTBxd bxd, @Param("user") DzccPersonEntity user);

    List<DfdwClwxTBxd> getList(@Param("bxd") DfdwClwxTBxd bxd, @Param("user") DzccPersonEntity user);
}




