package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccLzclTCarRent;
import com.soft.gcc.xtbg.dzcc.entity.LocusDto;
import com.soft.gcc.xtbg.dzcc.mapper.DzccLzclTCarRentMapper;
import com.soft.gcc.xtbg.dzcc.service.IDzccLzclTCarRentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Rent(零租车辆--车辆借用记录)】的数据库操作Service实现
* @createDate 2022-10-19 17:04:48
*/
@Service
public class DzccLzclTCarRentServiceImpl extends ServiceImpl<DzccLzclTCarRentMapper, DzccLzclTCarRent>
    implements IDzccLzclTCarRentService {

    @Override
    public Result<Object> getLocusList(DzccPersonEntity dzccPersonEntity, LocusDto locusDto) {
        try {
            IPage<LocusDto> locusDtos = new Page<>();
            List<LocusDto> list = baseMapper.getLocusList(dzccPersonEntity, locusDto);
            locusDtos.setCurrent(locusDto.getPageNum());
            locusDtos.setSize(locusDto.getPageSize());
            locusDtos.setTotal(list.size());
            if (list.size() > 0) {
                int fromIndex = (locusDto.getPageNum() - 1) * locusDto.getPageSize();
                int toIndex = locusDto.getPageNum() * locusDto.getPageSize();
                if (toIndex > list.size()) {
                    toIndex = list.size();
                }
                list = list.subList(fromIndex, toIndex);
            }
            locusDtos.setRecords(list);
            return Result.ok(locusDtos);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> getNotLocusList(DzccPersonEntity dzccPersonEntity, LocusDto locusDto) {
        try {
            IPage<LocusDto> list = new Page<>();
            list.setCurrent(locusDto.getPageNum());
            list.setSize(locusDto.getPageSize());
            IPage<LocusDto> locusDtos = new Page<>();
            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                locusDtos = baseMapper.getNotLocusListPage(list, locusDto, dzccPersonEntity);
            }
            return Result.ok(locusDtos);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




