package com.soft.gcc.xtbg.dzcc.util;


import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-12-07 15:26
 * @Version 1.0
 * @Description <p>备注：封装pdf工具类</p>
 */
public class PdfUtil {
    /**
     * pdf中文字体设置
     *
     * @return Font
     * @throws Exception
     */
    public static Font getPdfChineseFont(Integer fontSize, Integer style) throws Exception {
        if (fontSize == null) {
            //默认11
            fontSize = 11;
        }
        //字体资源路径
        InputStream inputStream = new ClassPathResource("\\fonts\\simsun.ttf").getInputStream();
        BaseFont baseFont = BaseFont.createFont("simsun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, true, IOUtils.toByteArray(inputStream), null);
        Font fontChinese = new Font(baseFont, 12, style);
        fontChinese.setColor(BaseColor.BLACK);
        fontChinese.setSize(fontSize);
        inputStream.close();
        return fontChinese;
    }

    /**
     * pdf单元格样式
     *
     */
    public static void setTableStyle(PdfPTable table, PdfPCell cell) {
        // 设置表格样式
        table.setLockedWidth(true);
        table.setTotalWidth(500);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);
        // 设置单元格样式
        cell.setMinimumHeight(35);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setBackgroundColor(BaseColor.WHITE);
        cell.setBorder(0);
        cell.setBorderWidthTop(0.1f);
        cell.setBorderWidthBottom(0.1f);
        cell.setBorderWidthLeft(0.1f);
        cell.setBorderWidthRight(0.1f);
        cell.setBorderColorBottom(BaseColor.BLACK);
        cell.setBorderColorLeft(BaseColor.BLACK);
        cell.setBorderColorRight(BaseColor.BLACK);
        cell.setBorderColorTop(BaseColor.BLACK);
        cell.setPadding(3);
    }

    /**
     * 给PDF添加水印
     * @param inputFilePath 原文件路径+名称,例如D:\\pdf\\test.pdf
     * @param outputFilePath 添加水印后输出文件保存的路径+名称
     * @param waterMarkContent 添加水印的内容
     */
    public static void pdfAddWaterMark(String inputFilePath, String outputFilePath, List<String> waterMarkContent) {
        try {
            // 水印的高和宽
            int waterMarkHeight = 30;
            int watermarkWeight = 60;

            // 水印间隔距离
            int waterMarkInterval = 150;

            // 读取PDF文件流
            PdfReader pdfReader = new PdfReader(inputFilePath);

            // 创建PDF文件的模板，可以对模板的内容修改，重新生成新PDF文件
            PdfStamper pdfStamper = new PdfStamper(pdfReader, Files.newOutputStream(Paths.get(outputFilePath)));

            // 设置水印字体
            InputStream inputStream = new ClassPathResource("\\fonts\\simsun.ttf").getInputStream();
            BaseFont baseFont = BaseFont.createFont("simsun.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED, true, IOUtils.toByteArray(inputStream), null);

            // 设置PDF内容的Graphic State 图形状态
            PdfGState pdfGraPhicState = new PdfGState();
            // 填充透明度
            pdfGraPhicState.setFillOpacity(0.2f);
            // 轮廓不透明度
            pdfGraPhicState.setStrokeOpacity(0.4f);

            // PDF页数
            int pdfPageNum = pdfReader.getNumberOfPages() + 1;

            // PDF文件内容字节
            PdfContentByte pdfContent;

            // PDF页面矩形区域
            Rectangle pageRectangle;

            for (int i = 1; i < pdfPageNum; i++) {
                // 获取当前页面矩形区域
                pageRectangle = pdfReader.getPageSizeWithRotation(i);
                // 获取当前页内容，getOverContent表示之后会在页面内容的上方加水印
                pdfContent = pdfStamper.getOverContent(i);

                // 获取当前页内容，getOverContent表示之后会在页面内容的下方加水印
                // pdfContent = pdfStamper.getUnderContent(i);

                pdfContent.saveState();
                // 设置水印透明度
                pdfContent.setGState(pdfGraPhicState);

                // 开启写入文本
                pdfContent.beginText();
                // 设置字体
                pdfContent.setFontAndSize(baseFont, 20);

                // 在高度和宽度维度每隔waterMarkInterval距离添加一个水印
                for (int height = waterMarkHeight; height < pageRectangle.getHeight(); height = height + waterMarkInterval) {
                    for (int width = watermarkWeight; width < pageRectangle.getWidth() + watermarkWeight;
                         width = width + waterMarkInterval) {
                        // 添加水印文字并旋转30度角
                        for (int j = 0; j < waterMarkContent.size(); j++) {
                            pdfContent.showTextAligned(Element.ALIGN_LEFT, waterMarkContent.get(j), width - watermarkWeight + 40 - (15 * j), height - waterMarkHeight - 10 + (20 * j), 30);
                        }
                    }
                }
                // 停止写入文本
                pdfContent.endText();
            }
            pdfStamper.close();
            pdfReader.close();
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String numberToChinese(int number) {
        String[] chineseNumbers = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        String[] units = {"", "十", "百", "千"};
        String[] sections = {"", "万", "亿", "万亿"};

        if (number == 0) {
            return chineseNumbers[0];
        }

        StringBuilder result = new StringBuilder();
        int sectionIndex = 0; // 用于跟踪万、亿等大数的节
        boolean isZero = true; // 用于跟踪是否需要添加“零”

        while (number > 0) {
            int current = number % 10000; // 每次取4位数进行处理
            if (current == 0) {
                if (!isZero) {
                    result.insert(0, sections[sectionIndex]);
                    result.insert(0, "零");
                }
                sectionIndex++;
                number /= 10000;
                continue;
            }

            isZero = false;
            int unitIndex = 0;
            while (current > 0) {
                int digit = current % 10;
                if (digit == 0) {
                    if (!isZero) {
                        isZero = true;
                        result.insert(0, chineseNumbers[digit]);
                    }
                } else {
                    isZero = false;
                    result.insert(0, units[unitIndex]);
                    result.insert(0, chineseNumbers[digit]);
                }
                current /= 10;
                unitIndex++;
            }
            result.insert(0, sections[sectionIndex]);
            sectionIndex++;
            number /= 10000;
        }

        return result.toString();
    }

    public static String weekToChinese(int number) {
        String[] chineseWeeks = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return chineseWeeks[number - 1];
    }

    public static void main(String[] args) throws IOException {
        //  测试addPdfWatermark方法
        String inputPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\yykj\\dzcc\\downloads\\246991701933769512.pdf";
        String outputPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\yykj\\dzcc\\downloads\\246991701933769512.pdf";
        List<String> watermark = Arrays.asList("陈翰", "科技公司", "2023-12-07");
        pdfAddWaterMark(inputPath, outputPath, watermark);
    }
}
