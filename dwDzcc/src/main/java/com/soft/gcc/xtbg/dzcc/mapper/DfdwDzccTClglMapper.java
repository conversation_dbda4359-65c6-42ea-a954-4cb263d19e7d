package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CLGL(电子出车-车辆管理)】的数据库操作Mapper
 * @createDate 2022-10-19 14:11:29
 * @Entity com.yykj.app.dzcc.domain.DfdwDzccTClgl
 */
public interface DfdwDzccTClglMapper extends BaseMapper<DfdwDzccTClgl> {

    List<DfdwDzccTClgl> selectCarRent(
            @Param(value = "person") DzccPersonEntity person,
            @Param(value = "clgl") DfdwDzccTClgl clgl,
            @Param(value = "startNum") Integer startNum,
            @Param(value = "endNum") Integer endNum
    );

    void saveBatch1(@Param("clglList") List<DfdwDzccTClgl> saveList);

    long countCarRent(
            @Param(value = "person") DzccPersonEntity person,
            @Param(value = "clgl") DfdwDzccTClgl clgl
    );

    List<DfdwDzccTClgl> selectCarRentNew(
            @Param(value = "person") DzccPersonEntity person
    );

    List<DfdwDzccTClgl> getRepeatCar();
}




