package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 电子出车-周期（延时申请）
 * @TableName DFDW_DZCC_T_WEEK
 */
@TableName(value ="DFDW_DZCC_T_WEEK")
@Data
public class DfdwDzccTWeek extends DzccBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 司机Id
     */
    @TableField(value = "driveId")
    private Integer driveId;

    /**
     * 司机姓名
     */
    @TableField(value = "driveName")
    private String driveName;

    /**
     * 年份
     */
    @TableField(value = "year")
    private Integer year;

    /**
     * 月份
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 周期
     */
    @TableField(value = "week")
    private Integer week;

    /**
     * 开始日期
     */
    @TableField(value = "startTime")
    private LocalDate startTime;

    /**
     * 结束日期
     */
    @TableField(value = "endTime")
    private LocalDate endTime;

    /**
     * 周期天数
     */
    @TableField(value = "weekDays")
    private Integer weekDays;

    /**
     * 添加延时数
     */
    @TableField(value = "appendDelayHour")
    private BigDecimal appendDelayHour;

    /**
     * 延时数
     */
    @TableField(exist = false)
    private BigDecimal delayHour;


    /**
     * 申请人Id（发起延时申请用户）
     */
    @TableField(value = "applyUserId")
    private Integer applyUserId;

    /**
     * 申请人名称（发起延时申请用户）
     */
    @TableField(value = "applyUserName")
    private String applyUserName;

    /**
     * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(value = "approveState")
    private Integer approveState;

    /**
     * 备注(冗余字段)
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 主表id
     */
    @TableField(value = "weekMainId")
    private Integer weekMainId;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private LocalDateTime createTime;

    /**
     * 初始延时
     */
    @TableField(value = "oringOvertimeHours",exist = false)
    private BigDecimal oringOvertimeHours;

    /**
     * 流程申请时间
     */
    @TableField(value = "applyTime")
    private LocalDateTime applyTime;

    @TableField(exist = false)
    private List<String> licencePlateList;
    @TableField(exist = false)
    private Integer mergeCount;

    @TableField(exist = false)
    private String licencePlate;

    @TableField(exist = false)
    private List<Integer> weekList;

    @TableField(exist = false)
    private BigDecimal overtimeHoursAll;

    //v_week 里面的字段licencePlates
    @TableField(exist = false)
    private String licencePlates;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
