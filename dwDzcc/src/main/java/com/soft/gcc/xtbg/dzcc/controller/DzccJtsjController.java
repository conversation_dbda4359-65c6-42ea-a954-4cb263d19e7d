package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTZbb;
import com.soft.gcc.xtbg.dzcc.entity.JtsjDto;
import com.soft.gcc.xtbg.dzcc.entity.SjInfoDto;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccJtsjService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 集团司机数据
 * @date 2024-12-12 10:35:21
 */
@RequestMapping("/dzcc/jtsj")
@RestController
public class DzccJtsjController  extends DzccBaseController {
    @Autowired
    private IDfdwDzccJtsjService jtsjService;


    /**
     * 集团司机-PC-集团司机清单列表数据
     * @param jtsjDto
     * @return
     */
    @RequestMapping("/GetDriverList")
    @PreAuthorize("@ss.hasPermi('NDWCC01JTSJ01QX01')")
    public Result<Object> GetDriverList(@RequestBody JtsjDto jtsjDto) {
        Result<Object> result =  jtsjService.GetDriverList(jtsjDto);
        return result;
    }

    /**
     * 集团司机-PC-集团司机清单-查询详情
     * */
    @RequestMapping("/getSjqdx")
    @PreAuthorize("@ss.hasPermi('NDWCC01JTSJ01QX01')")
    public Result<Object> GetSjqdx(@RequestBody JtsjDto jtsjDto) {
        jtsjDto.setEndDate(jtsjDto.getEndDate().plusDays(1));
        return jtsjService.getSjqx(jtsjDto);
    }

    /**
     * 集团司机-PC-集团司机清单-驾驶员工作量清单导出
     * */
    @RequestMapping("/downJsyqdByIds")
    @PreAuthorize("@ss.hasPermi('NDWCC01JTSJ01QX02')")
    @ResponseBody
    public Result<Object> downJsyqdByIds(@RequestBody JtsjDto jtsjDto, HttpServletResponse response) {
        DzccPersonEntity user = getDzccPerson();
        jtsjDto.setEndDate(jtsjDto.getEndDate().plusDays(1));
        return jtsjService.downJsyqdByIds(jtsjDto, user, response);
    }
}
