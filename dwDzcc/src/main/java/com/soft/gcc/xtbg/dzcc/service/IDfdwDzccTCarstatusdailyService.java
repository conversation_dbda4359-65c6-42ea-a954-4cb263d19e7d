package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarstatusdaily;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CarStatusDaily(每日出车情况)】的数据库操作Service
* @createDate 2023-08-08 15:17:41
*/
public interface IDfdwDzccTCarstatusdailyService extends IService<DfdwDzccTCarstatusdaily> {

    Result addCarStatusDaily(LocalDate date);

    String getQyInfo(String currDate, String AK, Integer driveId, Integer carId);

    void getQyInfoNew(Integer lcId);

    void saveOrUpdate(Integer carId, Integer driveId, Integer groupId, String status, String time) ;

    Integer getAreaId(String AK, BigDecimal Latitude, BigDecimal Longitude);
}
