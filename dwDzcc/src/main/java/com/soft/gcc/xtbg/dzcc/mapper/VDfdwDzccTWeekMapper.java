package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_WEEK】的数据库操作Mapper
* @createDate 2024-12-16 14:43:09
* @Entity com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek
*/
public interface VDfdwDzccTWeekMapper extends BaseMapper<VDfdwDzccTWeek> {

    VDfdwDzccTWeek sumOringOvertimeHoursAndappendDelayHour(@Param("year") Integer year, @Param("month") Integer month, @Param("week") Integer week);

}




