package com.soft.gcc.xtbg.clwx.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory;
import com.soft.gcc.xtbg.clwx.service.DfdwClwxRepairFactoryService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@RequestMapping("/clwx/repair-factory")
@RestController
public class RepairFactoryController extends BaseController {
    @Autowired
    private DfdwClwxRepairFactoryService repairFactoryService;

    /**
     * 车辆维修-PC-厂家配置-分页
     * */
    @RequestMapping("/page")
    @PreAuthorize("@ss.hasPermi('JDWWX01PZ01QX01')")
    public Result<Object> page(@RequestBody DfdwClwxRepairFactory repairFactory) {
        PersonEntity person = user();
        return repairFactoryService.getPage(repairFactory, person);
    }

    /**
     * 车辆维修-PC-厂家配置-列表
     * */
    @RequestMapping("/list")
    @PreAuthorize("@ss.hasPermi('JDWWX01PZ01QX01')")
    public Result<Object> list(@RequestBody DfdwClwxRepairFactory repairFactory) {
        PersonEntity person = user();
        return repairFactoryService.getList(repairFactory, person);
    }

    /**
     * 车辆维修-PC-厂家配置-新增
     * */
    @RequestMapping("/add")
    @PreAuthorize("@ss.hasPermi('JDWWX01PZ01QX02')")
    public Result<Object> add(@RequestBody DfdwClwxRepairFactory repairFactory) {
        PersonEntity person = user();
        return repairFactoryService.add(repairFactory, person);
    }

    /**
     * 车辆维修-PC-厂家配置-修改
     * */
    @RequestMapping("/edit")
    @PreAuthorize("@ss.hasPermi('JDWWX01PZ01QX03')")
    public Result<Object> edit(@RequestBody DfdwClwxRepairFactory repairFactory) {
        PersonEntity person = user();
        return repairFactoryService.edit(repairFactory, person);
    }

    /**
     * 车辆维修-PC-厂家配置-删除
     * */
    @RequestMapping("/deleteByIds")
    @PreAuthorize("@ss.hasPermi('JDWWX01PZ01QX04')")
    public Result<Object> delete(@RequestBody DfdwClwxRepairFactory repairFactory) {
        PersonEntity person = user();
        return repairFactoryService.deleteByIds(repairFactory, person);
    }
}
