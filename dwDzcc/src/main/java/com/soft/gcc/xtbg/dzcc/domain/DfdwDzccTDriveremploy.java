package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 司机聘用表
 * @TableName DFDW_DZCC_T_DRIVEREMPLOY
 */
@TableName(value ="DFDW_DZCC_T_DRIVEREMPLOY", autoResultMap = true)
@Data
public class DfdwDzccTDriveremploy extends DzccBaseEntity implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 司机id
     */
    @TableField(value = "personId")
    private Integer personId;

    /**
     * 部门id
     */
    @TableField(value = "groupId")
    private Integer groupId;

    /**
     * 开始日期
     */
    @TableField(value = "startDate")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @TableField(value = "endDate")
    private LocalDate endDate;

    /**
     * 姓名
     */
    @TableField(exist = false)
    private String name;

    /**
     * 部门名
     */
    @TableField(exist = false)
    private String groupName;

    /**
     * 身份证
     */
    @TableField(exist = false)
    private String certificateID;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
