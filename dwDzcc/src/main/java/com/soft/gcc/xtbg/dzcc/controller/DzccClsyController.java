package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.mapper.DzccCarMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.service.DzccCarService;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 车辆使用报表
 */
@RequestMapping("/dzcc/clsy")
@RestController
public class DzccClsyController extends DzccBaseController{
    @Autowired
    DzccCarService carService;
    @Autowired
    DzccCarMapper baseMapper;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    /**
     * 电子出车-PC-使用报表-车辆使用查询
     * */
    @RequestMapping("/GetClsyList")
    @PreAuthorize("@ss.hasPermi('NDWCC01SY01QX01')")
    public Result<Object> GetClsyList(@RequestBody DzccCar car) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.carService.GetClsyList(dzccPersonEntity, car);
    }

    /**
     * 电子出车-PC-使用报表-车辆使用导出
     * */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01SY01QX02')")
    public Result<Object> DownLoad(@RequestBody DzccCar car, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        try(XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<Integer> groupIds = new ArrayList<>();
            if (car.getGroupid() != null && car.getGroupid() > 0) {
                groupIds.add(car.getGroupid());
            } else {
                List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            }
            List<DzccCar> clsyList = baseMapper.GetClsyListForMonth(
                    groupIds,
                    car.getCarMold(),
                    dzccPersonEntity,
                    car.getBeginDate(),
                    car.getOverDate().plusMonths(1)
            );
            List<DzccCar> cars = carService.list(new LambdaQueryWrapper<DzccCar>()
                    .like(car.getCarMold() != null, DzccCar::getCarMold, car.getCarMold())
                    .in(DzccCar::getGroupid, groupIds)
//                    .not(wrapper -> wrapper
//                            .eq(DzccCar::getCarTag, 2)
//                            .apply("LEN(GPSNum) <> 12")
//                    )// 针对产权车辆并且设备号的长度不等于12位的需要过滤
                    .ne(DzccCar::getCarTag, 2) // 排除产权车辆
                    .eq(DzccCar::getIsEnable, 1) // 已启用
                    .ne(DzccCar::getIsDelete, 1) // 排除删除
                    .orderByAsc(DzccCar::getCarMold)
            );
            for (DzccCar dzccCar : cars) {
                boolean istrue = true;
                for (DzccCar clsyCar : clsyList) {
                    if (dzccCar.getCarMold().equals(clsyCar.getCarMold())) {
                        istrue = false;
                        break;
                    }
                }
                // 不存在则添加
                if (istrue) {
                    DzccCar clsyCar = new DzccCar();
                    clsyCar.setCarMold(dzccCar.getCarMold());
                    clsyCar.setYear(car.getBeginDate().getYear());
                    clsyCar.setMonth(car.getBeginDate().getMonthValue());
                    clsyCar.setNumber(0);
                    clsyList.add(clsyCar);
                }
            }
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Map<String, Object>> map = new HashMap<>();
            List<String> titleList = new ArrayList<>();
            Map<String, Integer> sumMap = new HashMap<>();
            for (DzccCar dzccCar : clsyList) {
                if (map.containsKey(dzccCar.getCarMold())) {
                    Map<String, Object> obj = map.get(dzccCar.getCarMold());
                    if (dzccCar.getMonth() < 10) {
                        obj.put(dzccCar.getYear() + "年0" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                    } else {
                        obj.put(dzccCar.getYear() + "年" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                    }
                } else {
                    Map<String, Object> obj = new HashMap<>();
                    obj.put("carMold", dzccCar.getCarMold());
                    for (LocalDate date = car.getBeginDate(); !date.isAfter(car.getOverDate()); date = date.plusMonths(1)) {
                        if (date.getMonthValue() < 10) {
                            obj.put(date.getYear() + "年0" + date.getMonthValue() + "月", 0);
                        } else {
                            obj.put(date.getYear() + "年" + date.getMonthValue() + "月", 0);
                        }
                    }
                    if (dzccCar.getMonth() < 10) {
                        obj.put(dzccCar.getYear() + "年0" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                    } else {
                        obj.put(dzccCar.getYear() + "年" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                    }
                    map.put(dzccCar.getCarMold(), obj);
                }
            }
            for (Map.Entry<String, Map<String, Object>> entry : map.entrySet()) {
                list.add(entry.getValue());
            }
            if (!list.isEmpty()) {
                list.sort((o1, o2) -> {
                    String str1 = o1.get("carMold").toString();
                    String str2 = o2.get("carMold").toString();
                    //str1在前，str2在后，默认升序，这里Integer类型的也可以
                    return str1.compareTo(str2);
                });
                // 把name为haha的学生放在最后面
                Map<String, Object> temp = new HashMap<>();
                boolean flag = false;

                for(int i = 0; i < list.size(); i++){
                    Map<String, Object> mold = list.get(i);
                    if ("其它".equals(mold.get("carMold"))) {
                        temp = mold;
                        flag = true;
                        list.remove(i);
                        break;
                    }
                }

                if (flag) {
                    list.add(temp);
                }
                Map<String, Object> titleMap = list.get(0);
                for (Map.Entry<String, Object> entry : titleMap.entrySet()) {
                    String entryKey = entry.getKey();
                    if (!"carMold".equals(entryKey)) {
                        titleList.add(entryKey);
                        sumMap.put(entryKey, 0);
                    }
                }
                Collections.sort(titleList);
            }

            XSSFSheet sheet = workbook.createSheet("车辆使用情况");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 1, 2));
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 1, 2));
            sheet.setColumnWidth(0, 10*256);

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("车辆使用情况");
            sheet.setDefaultColumnWidth(23);

            //设置一个样式
            XSSFCellStyle textStyle = workbook.createCellStyle();
            //垂直居中
            textStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            textStyle.setAlignment(HorizontalAlignment.CENTER);
            //第二行
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("单位：");
            row.createCell(1).setCellValue(car.getGroupname());
            row.getCell(1).setCellStyle(textStyle);

            //第三行
            row = sheet.createRow(2);
            row.createCell(0).setCellValue("时间：");
            row.createCell(1).setCellValue(car.getBeginDate().format(DateTimeFormatter.ofPattern("yyyy年MM月")) + "至" + car.getOverDate().format(DateTimeFormatter.ofPattern("yyyy年MM月")));
            row.getCell(1).setCellStyle(textStyle);

            //第四行数据
            //设置一个样式
            XSSFCellStyle cellTitleStyle = workbook.createCellStyle();
            //垂直居中
            cellTitleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            cellTitleStyle.setAlignment(HorizontalAlignment.CENTER);
            cellTitleStyle.setBorderBottom(BorderStyle.THIN);
            cellTitleStyle.setBorderLeft(BorderStyle.THIN);
            cellTitleStyle.setBorderRight(BorderStyle.THIN);
            cellTitleStyle.setBorderTop(BorderStyle.THIN);
            //添加前景色,内容看的清楚
            cellTitleStyle.setFillForegroundColor((short) 11);
            XSSFFont cellTitleFont = workbook.createFont();
            //字体加粗
            cellTitleFont.setBold(true);
            cellTitleFont.setColor((short) 255);
            cellTitleStyle.setFont(cellTitleFont);
            row = sheet.createRow(5);
            row.createCell(0).setCellValue("序号");
            row.getCell(0).setCellStyle(cellTitleStyle);
//            row.createCell(1).setCellValue("车辆");
//            row.getCell(1).setCellStyle(cellTitleStyle);
//            row.createCell(2).setCellValue("车型");
//            row.getCell(2).setCellStyle(cellTitleStyle);
//            row.createCell(3).setCellValue("使用天数");
//            row.getCell(3).setCellStyle(cellTitleStyle);
            row.createCell(1).setCellValue("车型");
            row.getCell(1).setCellStyle(cellTitleStyle);
            for (int i = 0; i < titleList.size(); i++) {
                row.createCell(2 + i).setCellValue(titleList.get(i) + "车辆总数");
                row.getCell(2 + i).setCellStyle(cellTitleStyle);
            }

            //设置一个样式
            XSSFCellStyle cellStyle = workbook.createCellStyle();
            //垂直居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
            //添加前景色,内容看的清楚
            cellStyle.setFillForegroundColor((short) 11);
            for (int i = 0; i< list.size(); i++) {
                //第5+i行数据
                row = sheet.createRow(6 + i);

                XSSFCell cell = row.createCell(0);
                cell.setCellValue(i + 1);
                cell.setCellStyle(cellStyle);
//                cell = row.createCell(1);
//                cell.setCellValue("车" + (i + 1));
//                cell.setCellStyle(cellStyle);
//                cell = row.createCell(2);
//                cell.setCellValue(list.get(i).getCarMold());
//                cell.setCellStyle(cellStyle);
//                cell = row.createCell(3);
//                cell.setCellValue(list.get(i).getNumber());
//                cell.setCellStyle(cellStyle);
                cell = row.createCell(1);
                cell.setCellValue(list.get(i).get("carMold").toString());
                cell.setCellStyle(cellStyle);
                for (int j = 0; j < titleList.size(); j++) {
                    cell = row.createCell(2 + j);
                    cell.setCellValue((int)list.get(i).get(titleList.get(j)));
                    cell.setCellStyle(cellStyle);
                    sumMap.put(titleList.get(j), sumMap.get(titleList.get(j)) + (int)list.get(i).get(titleList.get(j)));
                }
            }
            //第5+list.size()行数据
            sheet.addMergedRegion(new CellRangeAddress(6+list.size(), 6+list.size(), 0, 1));
            row = sheet.createRow(6+list.size());
            row.createCell(0).setCellValue("合计：");
            row.getCell(0).setCellStyle(cellStyle);
            row.createCell(1);
            row.getCell(1).setCellStyle(cellStyle);
            for (int i = 0; i < titleList.size(); i++) {
                row.createCell(2 + i).setCellValue(sumMap.get(titleList.get(i)));
                row.getCell(2 + i).setCellStyle(cellStyle);
            }
            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("车辆移动情况".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
