package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车通知
* @TableName DFDW_DZCC_T_NOTICE
*/
@TableName(value ="DFDW_DZCC_T_NOTICE", autoResultMap = true)
@Data
public class DfdwDzccTNotice extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 部门id
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    * 公告内容
    */
    @TableField(value = "remark")
    @JSONField(name = "remark")

    private String remark;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 修改时间
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 编辑人id
    */
    @TableField(value = "editId")
    @JSONField(name = "editId")

    private Integer editId;
    /**
    * 开始时间
    */
    @TableField(value = "startTime")
    @JSONField(name = "startTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    /**
    * 结束时间
    */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
    * 是否开启通知（0未开始，1开启）
    */
    @TableField(value = "isOpen")
    @JSONField(name = "isOpen")

    private Integer isOpen;
    /**
    * 通知类别（0部门，1全部）
    */
    @TableField(value = "noticeType")
    @JSONField(name = "noticeType")

    private Integer noticeType;
    /**
    * 标题
    */
    @TableField(value = "title")
    @JSONField(name = "title")

    private String title;
    /**
    * 标题图片image
    */
    @TableField(value = "titleImage")
    @JSONField(name = "titleImage")

    private String titleImage;
    /**
    * 部门名
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")

    private String groupName;

    @TableField(exist = false)
    private Integer titlefileid;
    @TableField(exist = false)
    private Integer videofileid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
