package com.soft.gcc.xtbg.dzcc.controller;

import com.alibaba.fastjson.JSONObject;
import com.soft.gcc.xtbg.base.controller.Result;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/18 15:23:27
 */
@RequestMapping("/login")
@RestController
public class DzccLoginController {
    @Autowired
    CacheManager cacheManager;

    /**
     * 电子出车-PC-登录-登录校验
     * */
    @RequestMapping("/getUser")
    public Result<Object> getUser(HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, Cookie> cookieMap = ReadCookieMap(request);

            // 发布版本
//            Cookie cookie = (Cookie) cookieMap.get("jtoken");
//            String token = cookie.getValue();
//            HttpGet post = new HttpGet("http://************:9537/sso/getUser");
//            post.addHeader("x-access-token", token);
//            CloseableHttpClient client = HttpClients.createDefault();
//            HttpResponse res = client.execute(post);
//            String result = EntityUtils.toString(res.getEntity());
//            JSONObject jsonObject = JSONObject.parseObject(result);
//
//            if (200 ==Integer.parseInt(jsonObject.get("code").toString())) {
//                response.addHeader("x-access-token", token);
//                return Result.ok(token);
//            } else {
//                return Result.error("");
//            }

            // 本地打包版本
            Cookie cookie = cookieMap.get("loginName");
            String m = cookie.getValue();
            HttpGet post = new HttpGet("http://127.0.0.1:9595/sys/test/" + m);
            CloseableHttpClient client = HttpClients.createDefault();
            HttpResponse res = client.execute(post);
            String token = res.getHeaders("x-access-token")[0].getValue();
            String result = EntityUtils.toString(res.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(result);

            if (200 ==Integer.parseInt(jsonObject.get("code").toString())) {
                response.addHeader("x-access-token", token);
                return Result.ok(token);
            } else {
                return Result.error("");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    private Map<String, Cookie> ReadCookieMap(HttpServletRequest request) {
        Map<String, Cookie> cookieMap = new HashMap<String, Cookie>();

        Cookie[] cookies = request.getCookies();

        if (null != cookies) {
            for (Cookie cookie : cookies) {

                cookieMap.put(cookie.getName(), cookie);
            }
        }
        return cookieMap;

    }
}
