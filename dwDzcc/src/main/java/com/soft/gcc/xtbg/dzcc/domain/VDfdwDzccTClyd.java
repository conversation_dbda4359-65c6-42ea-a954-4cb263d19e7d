package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_CLYD
*/
@TableName(value ="V_DFDW_DZCC_T_CLYD", autoResultMap = true)
@Data
public class VDfdwDzccTClyd extends DzccBaseEntity implements Serializable {


    /**
    *
    */
    @TableField(value = "Id")
    @JSONField(name = "Id")

    private Integer Id;
    /**
    *
    */
    @TableField(value = "DeviceId")
    @JSONField(name = "DeviceId")

    private String DeviceId;
    /**
    *
    */
    @TableField(value = "Mileage")
    @JSONField(name = "Mileage")

    private Long Mileage;
    /**
    *
    */
    @TableField(value = "RunTime")
    @JSONField(name = "RunTime")

    private Integer RunTime;
    /**
    *
    */
    @TableField(value = "AllMileage")
    @JSONField(name = "AllMileage")

    private Integer AllMileage;
    /**
    *
    */
    @TableField(value = "Status")
    @JSONField(name = "Status")

    private Integer Status;
    /**
    *
    */
    @TableField(value = "OnLineStartTime")
    @JSONField(name = "OnLineStartTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date OnLineStartTime;
    /**
    *
    */
    @TableField(value = "UpdateTime")
    @JSONField(name = "UpdateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date UpdateTime;
    /**
    *
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    *
    */
    @TableField(value = "UniqueId")
    @JSONField(name = "UniqueId")

    private String UniqueId;
    /**
    *
    */
    @TableField(value = "StartMoveTime")
    @JSONField(name = "StartMoveTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date StartMoveTime;
    /**
    *
    */
    @TableField(value = "StartMoveLongitude")
    @JSONField(name = "StartMoveLongitude")

    private BigDecimal StartMoveLongitude;
    /**
    *
    */
    @TableField(value = "StartMoveLatitude")
    @JSONField(name = "StartMoveLatitude")

    private BigDecimal StartMoveLatitude;
    /**
    *
    */
    @TableField(value = "MoveMileage")
    @JSONField(name = "MoveMileage")

    private Long MoveMileage;
    /**
    *
    */
    @TableField(value = "EndMoveTime")
    @JSONField(name = "EndMoveTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date EndMoveTime;
    /**
    *
    */
    @TableField(value = "EndMoveLongitude")
    @JSONField(name = "EndMoveLongitude")

    private BigDecimal EndMoveLongitude;
    /**
    *
    */
    @TableField(value = "EndMoveLatitude")
    @JSONField(name = "EndMoveLatitude")

    private BigDecimal EndMoveLatitude;
    /**
    *
    */
    @TableField(value = "MoveTime")
    @JSONField(name = "MoveTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date MoveTime;
    /**
    *
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    *
    */
    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")

    private String licensePlate;
    /**
    *
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")

    private String groupName;
    /**
    *
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    *
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")

    private String driverName;
    /**
    *
    */
    @TableField(value = "driverId")
    @JSONField(name = "driverId")

    private Integer driverId;
    /**
    *
    */
    @TableField(value = "diff")
    @JSONField(name = "diff")

    private Integer diff;
    /**
    *
    */
    @TableField(value = "row")
    @JSONField(name = "row")

    private Long row;

    /**
     * 车型
     */
    @TableField(exist = false)
    private String carMold;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
