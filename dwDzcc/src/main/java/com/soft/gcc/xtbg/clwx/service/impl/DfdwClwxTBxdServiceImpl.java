package com.soft.gcc.xtbg.clwx.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd;
import com.soft.gcc.xtbg.clwx.service.DfdwClwxTBxdService;
import com.soft.gcc.xtbg.clwx.mapper.DfdwClwxTBxdMapper;
import com.soft.gcc.xtbg.dzcc.domain.DzccLcWorkflow;
import com.soft.gcc.xtbg.dzcc.service.IDzccLcWorkflowService;
import com.soft.gcc.xtbg.dzcc.service.impl.DfdwDzccTCclcServiceImpl;
import com.soft.gcc.xtbg.dzcc.util.AliyunOSSUtils;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import com.soft.gcc.xtbg.dzcc.util.PdfUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
* <AUTHOR>
* @description 针对表【DFDW_CLWX_T_BXD(车辆维修（新）-维修申请主表)】的数据库操作Service实现
* @createDate 2024-10-17 16:11:09
*/
@Service
public class DfdwClwxTBxdServiceImpl extends ServiceImpl<DfdwClwxTBxdMapper, DfdwClwxTBxd>
    implements DfdwClwxTBxdService{
    private static final Logger log = LoggerFactory.getLogger(DfdwClwxTBxdServiceImpl.class);
    @Autowired
    IDzccLcWorkflowService lcWorkflowService;
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Resource
    TFileService tFileService;




    @Override
    public Result<Object> getPage(DfdwClwxTBxd bxd, DzccPersonEntity user) {
        boolean isGroupAdmin = false;
        for (String roleVO : user.getRoleList()) {
            if ("协同办公-车辆维修-总管理".equals(roleVO)) {
                break;
            }
            if ("协同办公-车辆维修-部门管理".equals(roleVO)) {
                isGroupAdmin = true;
            }
        }
        if (isGroupAdmin) {
            bxd.setApplyTopDeptId(user.getTopGroupId());
        }
        IPage<DfdwClwxTBxd> page = baseMapper.getPage(new Page<>(bxd.getPageNum(), bxd.getPageSize()), bxd, user);
        return Result.ok(page);
    }

    @Override
    public Result<Object> GetLcListById(DfdwClwxTBxd bxd) {
        // 流程定义ID
        Integer lcDefineID = 20031;
        List<DzccLcWorkflow> list = lcWorkflowService.list(new LambdaQueryWrapper<DzccLcWorkflow>()
                .eq(DzccLcWorkflow::getYwID, bxd.getId())
                .eq(DzccLcWorkflow::getLc_defineID, lcDefineID)
                .orderByAsc(DzccLcWorkflow::getYwID, DzccLcWorkflow::getStartdate)
        );
        return Result.ok(list);
    }

    @Override
    public Result downWxdXqByIds(DfdwClwxTBxd bxd, DzccPersonEntity user, HttpServletResponse response) {
        //生成并上传pdf
        String localPath = System.getProperty("java.io.tmpdir");
        //临时文件目录
        List<String> pathList = new ArrayList<>();
        try (ServletOutputStream servletOutputStream = response.getOutputStream()) {
            File file = new File(localPath + "/yykj/clwx/downloads/");
            if (!file.exists()) {
                file.mkdirs();
            }
            file = new File(localPath + "/yykj/clwx/downloads/water/");
            if (!file.exists()) {
                file.mkdirs();
            }
            List<DfdwClwxTBxd> bxdList = baseMapper.getList(bxd, user);
            // 流程定义ID
            Integer lcDefineID = 20031;
            List<DzccLcWorkflow> lcWorkflowList = lcWorkflowService.list(new LambdaQueryWrapper<DzccLcWorkflow>()
                    .in(DzccLcWorkflow::getYwID, Arrays.asList(bxd.getIdList()))
                    .eq(DzccLcWorkflow::getLc_defineID, lcDefineID)
                    .ne(DzccLcWorkflow::getLc_jdID, 200311)
                    .orderByAsc(DzccLcWorkflow::getStartdate)
            );
            long time = System.currentTimeMillis();
            for (DfdwClwxTBxd bxd1 : bxdList) {
                String inputPath = localPath + "/yykj/clwx/downloads/" + bxd1.getId() + time + ".pdf";
                String outputPath = localPath + "/yykj/clwx/downloads/water/" + bxd1.getId() + time + ".pdf";
                try (FileOutputStream fos = new FileOutputStream(inputPath)) {
                    //1.打开文档并设置基本属性
                    Document document = new Document();

                    //writer
                    PdfWriter writer = PdfWriter.getInstance(document, fos);
                    writer.setViewerPreferences(PdfWriter.PageModeUseThumbs);
                    writer.setPageSize(PageSize.A4);
                    document.open();

                    //标题一
                    Paragraph title = new Paragraph(bxd.getApplyNo(), PdfUtil.getPdfChineseFont(10, Font.BOLD));
                    //标题样式
                    title.setPaddingTop(0.1f);
                    title.setAlignment(Element.ALIGN_RIGHT);
                    document.add(title);
                    //标题二
                    title = new Paragraph("附表.车辆报修单", PdfUtil.getPdfChineseFont(20, Font.BOLD));
                    //标题样式
                    title.setPaddingTop(0.1f);
                    title.setAlignment(Element.ALIGN_CENTER);
                    document.add(title);

                    //第一表格列宽
                    float[] firtWidths = {200, 200, 200, 200};
                    PdfPTable table = new PdfPTable(firtWidths);
                    table.setTotalWidth(800);
                    table.setHorizontalAlignment(Element.ALIGN_CENTER);
                    table.setSpacingBefore(10f); // 前间距
                    table.setSpacingAfter(10f); // 后间距

                    String nature = "";
                    if ("0".equals(bxd.getNature())) {
                        nature = "临租车辆";
                    } else if ("1".equals(bxd.getNature())) {
                        nature = "特殊车辆";
                    } else if ("2".equals(bxd.getNature())) {
                        nature = "产权车辆";
                    }
                    String feed = "";
                    List<DzccLcWorkflow> lcWorkList = lcWorkflowList.stream().filter(lc -> lc.getYwID().equals(bxd1.getId())).collect(Collectors.toList());
                    if (bxd1.getApproveState() == 2) {
                        feed = "同意";
                    }
                    String approvePerson = "审批人：";
                    List<String> personList = new ArrayList<>();
                    for (DzccLcWorkflow lc : lcWorkList) {
                        if (!personList.contains(lc.getPersonName())) {
                            personList.add(lc.getPersonName());
                        }
                    }
                    approvePerson += String.join(",", personList);

                    //表格数据
                    //mock数据
                    Object[][] datas = {
                            {"车辆单位：", bxd1.getApplyTopDeptName(), "产权性质：", nature},
                            {"车牌号：",  bxd1.getLicencePlate(), "车  型：",  bxd1.getCarMold()},
                            {"送修厂家：", bxd1.getRepairFactoryName(), "送修日期：", sdf.format(bxd1.getRepairDate())},
                            {"报    修    项    目"},
                            {bxd1.getRepairItem()},
                            {"保修原因：", bxd1.getRepairReason(), "预计维修保养金额（元）：", bxd1.getEstimatedAmount()},
                            {"实际维修金额：", bxd1.getActualRepairAmount(), "实际材料费（元）：", bxd1.getActualMaterialCost()},
                            {"车辆所在单位审核意见：", feed + "\r\n\r\n" + approvePerson + "\r\n\r\n                                       签名："},
                            {"实    际    维    修    项    目（附维修清单）"},
                            {null},
                            {"实际维修保养金额（元）：", bxd1.getActualRepairAmount()},
                            {"车队意见：", " \r\n\r\n\r\n\r\n\r\n                                       签名："},
                            {"车辆所在单位意见：", " \r\n\r\n\r\n\r\n\r\n                                       签名："},
                    };

                    //table数据填充
                    for (int i = 0; i < datas.length; i++) {
                        for (int j = 0; j < datas[i].length; j++) {
                            if (datas[i][j] == null) {
                                datas[i][j] = "";
                            }
                            //选好cell处理数据
                            PdfPCell pdfCell = new PdfPCell();
                            PdfUtil.setTableStyle(table, pdfCell);
                            Paragraph paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.BOLD));
                            if (j == 1 || j == 3) {
                                paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.NORMAL));
                            }
                            // 单元格合并和美化操作
                            if (i == 3 || i == 7) {
                                pdfCell.setRowspan(1);
                                pdfCell.setColspan(4);
                            } else if (i == 4) {
                                pdfCell.setRowspan(1);
                                pdfCell.setColspan(4);
                                pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                                paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.NORMAL));
                            } else if (i == 6 && j == 0) {
                                // 左边距
                                pdfCell.setPaddingLeft(22f);
                                // 右边距
                                pdfCell.setPaddingRight(22f);
                            } else if ((i == 6 || i == 10 || i == 11) && j == 1) {
                                pdfCell.setRowspan(1);
                                pdfCell.setColspan(3);
                                pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                            } else if (i == 8) {
                                pdfCell.setRowspan(1);
                                pdfCell.setColspan(4);
                                pdfCell.setFixedHeight(150f);
                            } else if (i == 9 && j == 1) {
                                pdfCell.setRowspan(1);
                                pdfCell.setColspan(3);
                            }
                            pdfCell.setPhrase(paragraph);
                            table.addCell(pdfCell);
                        }
                    }
                    document.add(table);
                    document.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("导出异常,用户id：" + user.getId() + ", 出车单编号：" + bxd1.getApplyNo() + ",异常原因：" + e.getMessage());
                }
                // 添加水印
                List<String> watermark = Arrays.asList(user.getRealName(), user.getGroupName(), sdf.format(new Date()));
                PdfUtil.pdfAddWaterMark(inputPath, outputPath, watermark);
                pathList.add(outputPath);
                file = new File(inputPath);
                if (file.exists()) {
                    file.delete();
                }
            }
            //=================生成word到设置浏览默认下载地址=================
            if ("pdf".equals(bxd.getDownloadType())) {
                byte[] buffer = new byte[1024];
                file = new File(pathList.get(0));
                if (!file.exists()) {
                    log.info("文件不存在：" + pathList.get(0));
                }
                try (FileInputStream fis = new FileInputStream(file); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    int i = bis.read(buffer);
                    while (i != -1) {
                        servletOutputStream.write(buffer, 0, i);
                        i = bis.read(buffer);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("车辆维修保修单详情单文件下载失败：" + e.getMessage());
                }

            } else {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(servletOutputStream)) {
                    for (int i = 0; i < bxdList.size(); i++) {
                        String applyNo = bxdList.get(i).getApplyNo();
                        String filePath = pathList.get(i);
                        // 创建ZIP条目
                        ZipEntry zipEntry = new ZipEntry(applyNo + ".pdf");
                        zipOutputStream.putNextEntry(zipEntry);

                        // 读取文件并写入到ZIP输出流
                        byte[] pdfFile = FileUtil.readBytes(filePath);
                        zipOutputStream.write(pdfFile);
                        zipOutputStream.closeEntry();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("车辆维修保修单详情多文件压缩失败：" + e.getMessage());
                }
            }
            return Result.ok();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        } finally {
            for (String path : pathList) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
        }
    }


    @Override
    public Result downloadFile(DfdwClwxTBxd bxd, DzccPersonEntity user, HttpServletResponse response) {
        List<DfdwClwxTBxd> list = null;
        List<String> pdfPathList = new ArrayList<>();

        try {
            // 获取要下载的记录列表
              list = baseMapper.getList(bxd, user);

//            list = this.baseMapper.selectList(new LambdaQueryWrapper<DfdwClwxTBxd>()
//                    .in(DfdwClwxTBxd::getId, Arrays.asList(bxd.getIdList()))
//            );

            if (list.isEmpty()) {
                throw new RuntimeException("未找到要下载的记录");
            }

            // 设置响应头
//            response.setContentType("application/zip");
//            response.setHeader("Content-Disposition", "attachment; filename=\"files_" + System.currentTimeMillis() + ".zip\"");

            // 生成PDF文件
            pdfPathList = generatePdfFiles(list, user);

            try (ServletOutputStream servletOutputStream = response.getOutputStream();
                 ZipOutputStream zipOutputStream = new ZipOutputStream(servletOutputStream)) {

                // 遍历每个记录，每个记录代表一个文件夹
                for (int i = 0; i < list.size(); i++) {
                    DfdwClwxTBxd item = list.get(i);

                    // 获取文件夹名称，使用申请编号
                    String folderName = item.getApplyNo();
                    if (folderName == null || folderName.trim().isEmpty()) {
                        folderName = "folder_" + item.getId();
                    }

                    // 1. 首先添加PDF文件到文件夹中
                    if (i < pdfPathList.size()) {
                        String pdfPath = pdfPathList.get(i);
                        try {
                            String pdfFileName = item.getApplyNo() + ".pdf";
                            String zipEntryPath = folderName + "/" + pdfFileName;
                            ZipEntry pdfEntry = new ZipEntry(zipEntryPath);
                            zipOutputStream.putNextEntry(pdfEntry);

                            // 读取PDF文件内容
                            byte[] pdfContent = FileUtil.readBytes(pdfPath);
                            if (pdfContent != null && pdfContent.length > 0) {
                                zipOutputStream.write(pdfContent);
                            }
                            zipOutputStream.closeEntry();

                        } catch (Exception e) {
                            log.error("处理PDF文件时发生异常: {}", e.getMessage(), e);
                        }
                    }

                    // 2. 然后添加附件文件到文件夹中
                    if (item.getAnnexIds() != null && !item.getAnnexIds().trim().isEmpty()) {
                        String[] annexIdArray = item.getAnnexIds().split(",");
                        List<String> annexIdList = Arrays.stream(annexIdArray)
                                .map(String::trim)
                                .filter(id -> !id.isEmpty())
                                .collect(Collectors.toList());

                        if (!annexIdList.isEmpty()) {
                            List<TFile> files = tFileService.list(new LambdaQueryWrapper<TFile>()
                                    .in(TFile::getId, annexIdList));

                            // 处理文件夹下的每个附件文件
                            for (TFile file : files) {
                                boolean entryOpened = false;
                                try {
                                    // 获取文件名
                                    String fileName = file.getFilename();
                                    if (fileName == null || fileName.trim().isEmpty()) {
                                        fileName = "file_" + file.getId();
                                    }

                                    // 创建ZIP条目，路径为：文件夹名/文件名
                                    String zipEntryPath = folderName + "/" + fileName;
                                    ZipEntry zipEntry = new ZipEntry(zipEntryPath);
                                    zipOutputStream.putNextEntry(zipEntry);
                                    entryOpened = true; // 标记条目已打开

                                    // 从阿里云OSS下载文件内容
                                    byte[] fileContent = AliyunOSSUtils.downloadFileStream(file.getFilepath());
                                    if (fileContent != null && fileContent.length > 0) {
                                        zipOutputStream.write(fileContent);
                                    } else {
                                        log.warn("文件 {} 下载失败或内容为空", file.getFilename());
                                    }

                                    zipOutputStream.closeEntry();
                                    entryOpened = false; // 标记条目已关闭

                                } catch (Exception e) {
                                    log.error("处理附件文件 {} 时发生异常: {}", file.getFilename(), e.getMessage(), e);
                                    // 如果ZIP条目已打开但未关闭，则尝试关闭它
                                    if (entryOpened) {
                                        try {
                                            zipOutputStream.closeEntry();
                                        } catch (Exception closeException) {
                                            log.error("关闭ZIP条目时发生异常: {}", closeException.getMessage());
                                        }
                                    }
                                    // 继续处理下一个文件，不中断整个流程
                                }
                            }
                        }
                    }
                }

                zipOutputStream.finish();
                zipOutputStream.flush();

            } catch (Exception e) {
                log.error("创建ZIP文件时发生异常: {}", e.getMessage(), e);
                throw new RuntimeException("文件打包失败: " + e.getMessage(), e);
            }

            return Result.ok("文件下载成功");

        } catch (Exception ex) {
            log.error("下载文件时发生异常: {}", ex.getMessage(), ex);
            throw new RuntimeException("文件下载失败: " + ex.getMessage(), ex);
        } finally {
            // 清理临时PDF文件
            for (String pdfPath : pdfPathList) {
                try {
                    File tempPdfFile = new File(pdfPath);
                    if (tempPdfFile.exists()) {
                        tempPdfFile.delete();
                    }
                } catch (Exception e) {
                    log.warn("删除临时PDF文件失败: {}", pdfPath, e);
                }
            }
        }
    }

    /**
     * 生成PDF文件
     * @param bxdList 车辆维修记录列表
     * @param user 用户信息
     * @return PDF文件路径列表
     */
    private List<String> generatePdfFiles(List<DfdwClwxTBxd> bxdList, DzccPersonEntity user) {
        String localPath = System.getProperty("java.io.tmpdir");
        List<String> pathList = new ArrayList<>();

        try {
            // 创建临时目录
            File downloadDir = new File(localPath + "/yykj/clwx/downloads/");
            if (!downloadDir.exists()) {
                downloadDir.mkdirs();
            }
            File waterDir = new File(localPath + "/yykj/clwx/downloads/water/");
            if (!waterDir.exists()) {
                waterDir.mkdirs();
            }

            // 获取流程信息
            Integer lcDefineID = 20031;
            List<Integer> idList = bxdList.stream().map(DfdwClwxTBxd::getId).collect(Collectors.toList());
            List<DzccLcWorkflow> lcWorkflowList = lcWorkflowService.list(new LambdaQueryWrapper<DzccLcWorkflow>()
                    .in(DzccLcWorkflow::getYwID, idList)
                    .eq(DzccLcWorkflow::getLc_defineID, lcDefineID)
                    .ne(DzccLcWorkflow::getLc_jdID, 200311)
                    .orderByAsc(DzccLcWorkflow::getStartdate)
            );

            long time = System.currentTimeMillis();

            for (DfdwClwxTBxd bxd1 : bxdList) {
                String inputPath = localPath + "/yykj/clwx/downloads/" + bxd1.getId() + time + ".pdf";
                String outputPath = localPath + "/yykj/clwx/downloads/water/" + bxd1.getId() + time + ".pdf";

                try (FileOutputStream fos = new FileOutputStream(inputPath)) {
                    // 创建PDF文档
                    Document document = new Document();
                    PdfWriter writer = PdfWriter.getInstance(document, fos);
                    writer.setViewerPreferences(PdfWriter.PageModeUseThumbs);
                    writer.setPageSize(PageSize.A4);
                    document.open();

                    // 添加标题
                    Paragraph title = new Paragraph(bxd1.getApplyNo(), PdfUtil.getPdfChineseFont(10, Font.BOLD));
                    title.setPaddingTop(0.1f);
                    title.setAlignment(Element.ALIGN_RIGHT);
                    document.add(title);

                    title = new Paragraph("附表.车辆报修单", PdfUtil.getPdfChineseFont(20, Font.BOLD));
                    title.setPaddingTop(0.1f);
                    title.setAlignment(Element.ALIGN_CENTER);
                    document.add(title);

                    // 创建表格
                    float[] firstWidths = {200, 200, 200, 200};
                    PdfPTable table = new PdfPTable(firstWidths);
                    table.setTotalWidth(800);
                    table.setHorizontalAlignment(Element.ALIGN_CENTER);
                    table.setSpacingBefore(10f);
                    table.setSpacingAfter(10f);

                    // 处理产权性质
                    String nature = "";
                    if ("0".equals(bxd1.getNature())) {
                        nature = "临租车辆";
                    } else if ("1".equals(bxd1.getNature())) {
                        nature = "特殊车辆";
                    } else if ("2".equals(bxd1.getNature())) {
                        nature = "产权车辆";
                    }

                    // 处理审批信息
                    String feed = "";
                    List<DzccLcWorkflow> lcWorkList = lcWorkflowList.stream()
                            .filter(lc -> lc.getYwID().equals(bxd1.getId()))
                            .collect(Collectors.toList());
                    if (bxd1.getApproveState() == 2) {
                        feed = "同意";
                    }

                    String approvePerson = "审批人：";
                    List<String> personList = new ArrayList<>();
                    for (DzccLcWorkflow lc : lcWorkList) {
                        if (!personList.contains(lc.getPersonName())) {
                            personList.add(lc.getPersonName());
                        }
                    }
                    approvePerson += String.join(",", personList);

                    // 表格数据
                    Object[][] datas = {
                            {"车辆单位：", bxd1.getApplyTopDeptName(), "产权性质：", nature},
                            {"车牌号：", bxd1.getLicencePlate(), "车  型：", bxd1.getCarMold()},
                            {"送修厂家：", bxd1.getRepairFactoryName(), "送修日期：", sdf.format(bxd1.getRepairDate())},
                            {"报    修    项    目"},
                            {bxd1.getRepairItem()},
                            {"保修原因：", bxd1.getRepairReason(), "预计维修保养金额（元）：", bxd1.getEstimatedAmount()},
//                            {"实际维修金额：", bxd1.getActualRepairAmount(), "实际材料费（元）：", bxd1.getActualMaterialCost()},
                            {"车辆所在单位审核意见：", feed + "\r\n\r\n" + approvePerson + "\r\n\r\n                                       签名："},
                            {"实    际    维    修    项    目（附维修清单）"},
                            {null},
                            {"实际维修保养金额（元）：", bxd1.getActualRepairAmount()},
                            {"车队意见：", " \r\n\r\n\r\n\r\n\r\n                                       签名："},
                            {"车辆所在单位意见：", " \r\n\r\n\r\n\r\n\r\n                                       签名："},
                    };

                    // 填充表格数据
                    fillPdfTable(table, datas);

                    document.add(table);
                    document.close();

                } catch (Exception e) {
                    log.error("生成PDF异常,用户id：{}, 出车单编号：{}, 异常原因：{}", user.getId(), bxd1.getApplyNo(), e.getMessage(), e);
                    throw new RuntimeException("导出异常,用户id：" + user.getId() + ", 出车单编号：" + bxd1.getApplyNo() + ",异常原因：" + e.getMessage());
                }

                // 添加水印
                List<String> watermark = Arrays.asList(user.getRealName(), user.getGroupName(), sdf.format(new Date()));
                PdfUtil.pdfAddWaterMark(inputPath, outputPath, watermark);
                pathList.add(outputPath);

                // 删除临时文件
                File tempFile = new File(inputPath);
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("生成PDF文件失败", e);
            throw new RuntimeException("生成PDF文件失败: " + e.getMessage());
        }

        return pathList;
    }

    /**
     * 填充PDF表格数据
     */
    private void fillPdfTable(PdfPTable table, Object[][] datas) throws Exception {
        for (int i = 0; i < datas.length; i++) {
            for (int j = 0; j < datas[i].length; j++) {
                if (datas[i][j] == null) {
                    datas[i][j] = "";
                }
                //选好cell处理数据
                PdfPCell pdfCell = new PdfPCell();
                PdfUtil.setTableStyle(table, pdfCell);
                Paragraph paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.BOLD));
                if (j == 1 || j == 3) {
                    paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.NORMAL));
                }
                // 单元格合并和美化操作
                if (i == 3 || i == 7) {
                    pdfCell.setRowspan(1);
                    pdfCell.setColspan(4);
                } else if (i == 4) {
                    pdfCell.setRowspan(1);
                    pdfCell.setColspan(4);
                    pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.NORMAL));
                } else if (i == 6 && j == 0) {
                    // 左边距
                    pdfCell.setPaddingLeft(22f);
                    // 右边距
                    pdfCell.setPaddingRight(22f);
                } else if ((i == 6 || i == 10 || i == 11) && j == 1) {
                    pdfCell.setRowspan(1);
                    pdfCell.setColspan(3);
                    pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                } else if (i == 8) {
                    pdfCell.setRowspan(1);
                    pdfCell.setColspan(4);
                    pdfCell.setFixedHeight(150f);
                } else if (i == 9 && j == 1) {
                    pdfCell.setRowspan(1);
                    pdfCell.setColspan(3);
                }
                pdfCell.setPhrase(paragraph);
                table.addCell(pdfCell);
            }
        }
//        for (int i = 0; i < datas.length; i++) {
//            for (int j = 0; j < datas[i].length; j++) {
//                if (datas[i][j] == null) {
//                    datas[i][j] = "";
//                }
//
//                // 创建单元格
//                PdfPCell pdfCell = new PdfPCell();
//                PdfUtil.setTableStyle(table, pdfCell);
//                Paragraph paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.BOLD));
//
//                if (j == 1 || j == 3) {
//                    paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.NORMAL));
//                }
//
//                // 单元格合并和美化操作
//                if (i == 3 || i == 8) {
//                    pdfCell.setRowspan(1);
//                    pdfCell.setColspan(4);
//                } else if (i == 4) {
//                    pdfCell.setRowspan(1);
//                    pdfCell.setColspan(4);
//                    pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
//                    paragraph = new Paragraph(String.valueOf(datas[i][j]), PdfUtil.getPdfChineseFont(12, Font.NORMAL));
//                } else if (i == 7 && j == 0) {
//                    pdfCell.setPaddingLeft(22f);
//                    pdfCell.setPaddingRight(22f);
//                } else if ((i == 7 || i == 11 || i == 12) && j == 1) {
//                    pdfCell.setRowspan(1);
//                    pdfCell.setColspan(3);
//                    pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
//                } else if (i == 9) {
//                    pdfCell.setRowspan(1);
//                    pdfCell.setColspan(4);
//                    pdfCell.setFixedHeight(150f);
//                } else if (i == 10 && j == 1) {
//                    pdfCell.setRowspan(1);
//                    pdfCell.setColspan(3);
//                }
//
//                pdfCell.setPhrase(paragraph);
//                table.addCell(pdfCell);
//            }
//        }
    }
}
