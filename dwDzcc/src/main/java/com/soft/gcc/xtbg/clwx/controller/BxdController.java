package com.soft.gcc.xtbg.clwx.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd;
import com.soft.gcc.xtbg.clwx.service.DfdwClwxTBxdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@RequestMapping("/clwx/bxd")
@RestController
public class BxdController extends BaseController {
    @Autowired
    private DfdwClwxTBxdService bxdService;

    /**
     * 车辆维修-PC-保修单-分页
     * */
    @RequestMapping("/page")
    @PreAuthorize("@ss.hasPermi('JDWWX01DJ01QX01')")
    public Result<Object> page(@RequestBody DfdwClwxTBxd bxd) {
        return bxdService.getPage(bxd, user());
    }

    /**
     * 车辆维修-PC-保修单-根据id获取流程
     * */
    @RequestMapping("/lcListById")
    @PreAuthorize("@ss.hasPermi('JDWWX01DJ01QX01')")
    public Result<Object> lcListById(@RequestBody DfdwClwxTBxd bxd) {
        return bxdService.GetLcListById(bxd);
    }

    /**
     * 车辆维修-PC-保修单-保修单详情导出
     * */
    @RequestMapping("/downWxdXqByIds")
    @PreAuthorize("@ss.hasPermi('JDWWX01DJ01QX05')")
    @ResponseBody
    public Result downWxdXqByIds(@RequestBody DfdwClwxTBxd bxd, HttpServletResponse response) {
        return bxdService.downWxdXqByIds(bxd, user(), response);
    }


    /**
     * 批量下载附件
     */
    @RequestMapping("/downloadFile")
    @PreAuthorize("@ss.hasPermi('JDWWX01DJ01QX06')")
    @ResponseBody
    public Result downloadFile(@RequestBody DfdwClwxTBxd bxd, HttpServletResponse response) {
        return bxdService.downloadFile(bxd,user(), response);
    }


}
