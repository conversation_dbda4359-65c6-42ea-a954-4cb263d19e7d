package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTSjtblcMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTSjtblcService;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 司机填报里程
 */
@RequestMapping("/dzcc/sjtblc")
@RestController
public class DzccSjtblcController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTSjtblcService ivDfdwDzccTSjtblcService;
    @Autowired
    VDfdwDzccTSjtblcMapper vDfdwDzccTSjtblcMapper;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    /**
     * 电子出车-PC-司机填报-司机填报里程
     */
    @RequestMapping("/GetSjtblcList")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ02QX01')")
    public Result<Object> GetSjtblcList(@RequestBody VDfdwDzccTSjtblc sjtblc) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return ivDfdwDzccTSjtblcService.GetSjtblcList(dzccPersonEntity, sjtblc);
    }

    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ02QX02')")
    public Result<Object> DownLoad(@RequestBody VDfdwDzccTSjtblc sjtblc, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<Integer> groupIds = new ArrayList<>();
            if (sjtblc.getGroupid() != null && sjtblc.getGroupid() > 0) {
                groupIds.add(sjtblc.getGroupid());
            } else {
                List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(person, 1, false);
                groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            }
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            List<VDfdwDzccTSjtblc> list = vDfdwDzccTSjtblcMapper.getSjtblcList(
                    sjtblc,
                    person,
                    groupIds
            );

            XSSFSheet sheet = workbook.createSheet("司机填报里程");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(sjtblc.getGroupName() + "司机填报里程");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("车型");
            row.createCell(1).setCellValue("车牌号");
            row.createCell(2).setCellValue("填报人");
            row.createCell(3).setCellValue("里程数");
            row.createCell(4).setCellValue("填报时间");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getCarMold());
                row.createCell(1).setCellValue(list.get(i).getLicencePlate());
                row.createCell(2).setCellValue(list.get(i).getDriverName());
                row.createCell(3).setCellValue(list.get(i).getMileage());
                row.createCell(4).setCellValue(new SimpleDateFormat("yyyy-MM-dd").format(list.get(i).getDate()));
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("司机填报历程".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
