package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.service.IDzccGroupitemService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【GroupItem】的数据库操作Service实现
 * @createDate 2022-10-17 16:39:57
 */
@Service
public class DzccGroupitemServiceImpl extends ServiceImpl<DzccGroupitemMapper, DzccGroupitem>
        implements IDzccGroupitemService {
    @Override
    public Result<Object> GetGroupList(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        return GetGroupList(dzccPersonEntity, map, false);
    }

    @Override
    public Result<Object> GetGroupList(DzccPersonEntity dzccPersonEntity, Map<String, String> map, Boolean isAll) {
        int isShowAll = ParseUtil.tryParseInt(map.get("isShowAll"));
        int parentId = ParseUtil.tryParseInt(map.get("parentId"));
        List<DzccGroupitem> list = new ArrayList<>();
        try {
            if (isShowAll != 0) {
                DzccGroupitem group = new DzccGroupitem();
                group.setId(-1);
                group.setGroupname("全部");
                list.add(group);
            }
            List<DzccGroupitem> groupitems = baseMapper.GetGroupListByRole(dzccPersonEntity, parentId, isAll);
            list.addAll(groupitems);
            return Result.ok(list);
        } catch (Exception Ex) {
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetGroupList2(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        int isShowAll = ParseUtil.tryParseInt(map.get("isShowAll"));
        int parentId = ParseUtil.tryParseInt(map.get("parentId"));
        List<DzccGroupitem> list = new ArrayList<>();
        try {
            if (isShowAll != 0) {
                DzccGroupitem group = new DzccGroupitem();
                group.setId(-1);
                group.setGroupname("全部");
                list.add(group);
            }
            List<DzccGroupitem> groupitems = baseMapper.GetGroupListByRole2(dzccPersonEntity.getDzccQx(), dzccPersonEntity.getId(), dzccPersonEntity.getTopGroupId(), parentId, false);
            list.addAll(groupitems);
            return Result.ok(list);
        } catch (Exception Ex) {
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetGroupList12(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        int isShowAll = ParseUtil.tryParseInt(map.get("isShowAll"));
        int parentId = ParseUtil.tryParseInt(map.get("parentId"));
        List<DzccGroupitem> list = new ArrayList<>();
        try {
            if (isShowAll != 0) {
                DzccGroupitem group = new DzccGroupitem();
                group.setId(-1);
                group.setGroupname("全部");
                list.add(group);
            }
            List<DzccGroupitem> groupitems = baseMapper.GetGroupListByRole12(dzccPersonEntity, parentId, false);
            list.addAll(groupitems);
            return Result.ok(list);
        } catch (Exception Ex) {
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




