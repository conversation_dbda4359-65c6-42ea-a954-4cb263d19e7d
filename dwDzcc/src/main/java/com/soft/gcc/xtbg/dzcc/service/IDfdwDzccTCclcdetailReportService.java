package com.soft.gcc.xtbg.dzcc.service;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCLCDetail_Report(月份加班数据)】的数据库操作Service
* @createDate 2023-11-20 09:14:23
*/
public interface IDfdwDzccTCclcdetailReportService extends IService<DfdwDzccTCclcdetailReport> {

    List<DfdwDzccTCclcdetailReport> getListByMonth(int year, int monthValue, Integer topGroupId, String realName, DzccPersonEntity person,String currDate,String nextDate);
}
