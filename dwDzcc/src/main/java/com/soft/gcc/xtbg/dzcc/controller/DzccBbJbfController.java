package com.soft.gcc.xtbg.dzcc.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.entity.CclcDetailsDto;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 加班费报表
 * @date 2023-11-13 16:28:25
 */
@RequestMapping("/dzcc/jbfbb")
@RestController
public class DzccBbJbfController extends DzccBaseController {

    @Autowired
    IVDfdwDzccTZbbService zbbService;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;

    @Autowired
    private IDfdwDzccTCclcdetailReportService reportService;

    @Autowired
    private IDfdwDzccTCclcService cclcService;

    @Autowired
    private IVDfdwDzccTCclcdetailService ivDfdwDzccTCclcdetailService;

    @Autowired
    private IDzccPersonService personService;
    @Autowired
    private DzccCarService carService;
    @Autowired
    private IDfdwDzccTCclcStatusDailyService cclcStatusDailyService;
    @Autowired
    private DfdwDzccTWeekService dfdwDzccTWeekService;
    @Autowired
    private DfdwDzccOverTimeService dfdwDzccOverTimeService;

    @Autowired
    private IDfdwDzccTCclcdetailService dfdwDzccTCclcdetailService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @RequestMapping("/GetZbbList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF02QX01')")
    public Result<Object> GetZbbList(@RequestBody VDfdwDzccTZbb zbb) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.GetZbbListJbfNew(dzccPersonEntity, zbb);
    }


    @RequestMapping("/DownLoadZbb")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BB01QX02')")
    public Result<Object> DownLoadZbb(@RequestBody VDfdwDzccTZbb zbb, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        Integer type = 0;
        if(zbb.getType() !=null ){
            type = zbb.getType();
        }
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            LocalDate date = LocalDate.parse(zbb.getYearMonth());
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate nowDate = LocalDate.of(date.getYear(), date.getMonth(), 1);
            LocalDate nextDate = nowDate.plusMonths(1);
            List<Integer> groupIds = new ArrayList<>();
            if(type == 1){
                if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                    groupIds.add(zbb.getTopGroupId());
                } else {
                    groupIds.add(373);
                }
            }else{
                if (zbb.getTopGroupId() != null && zbb.getTopGroupId() > 0) {
                    groupIds.add(zbb.getTopGroupId());
                } else {
                    List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(person, 1, false);
                    groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
                }
            }

            List<VDfdwDzccTZbb> zbbsTemp = zbbService.queryZbbList(nowDate.format(df), nextDate.format(df), groupIds, zbb, person);

            List<VDfdwDzccTZbb> zbbs = new ArrayList<>();
            //查找本月加班数据
            Integer year = nowDate.getYear();
            Integer month = nowDate.getMonthValue();
            List<DfdwDzccTCclcdetailReport> reportList = reportService.getListByMonth(year, month, zbb.getTopGroupId(), zbb.getRealName(), person, nowDate.format(df), nextDate.format(df));
            reportList = reportList.stream().distinct().collect(Collectors.toList());

            //查找所有已审批通过的延时添加数
            List<DfdwDzccTWeek> weekList = dfdwDzccTWeekService.list(new LambdaQueryWrapper<DfdwDzccTWeek>()
                    .eq(DfdwDzccTWeek::getYear, year).eq(DfdwDzccTWeek::getMonth, month)
                    .eq(DfdwDzccTWeek::getApproveState, 2));
            //获取配置信息
            List<DfdwDzccTOverTime> overtimeSetList = dfdwDzccOverTimeService.list(new LambdaQueryWrapper<DfdwDzccTOverTime>());


            //重新处理结果
            for (DfdwDzccTCclcdetailReport report : reportList) {
                VDfdwDzccTZbb data = new VDfdwDzccTZbb();
                if (report.getId() == -1) {
                    report.setSubsidyStandardOvertime("157.5|210|314.5");
                    report.setSubsidyStandardDelay("19.70|26.20|39.30");
                    report.setSubsidyStandardNight("15");
                    report.setSxrOvertimeDays(0);
                    report.setJjrOvertimeDays(0);
                    report.setOvertimeCost(new BigDecimal("0"));
                    report.setGzrDelayHour(new BigDecimal("0"));
                    report.setSxrDelayHour(new BigDecimal("0"));
                    report.setJjrDelayHour(new BigDecimal("0"));
                    report.setDelayCost(new BigDecimal("0"));
                    report.setNightShiftDays(0);
                    report.setNightShiftCost(new BigDecimal("0"));
                    report.setTravelAllowance(new BigDecimal("0"));
                    report.setAllCost(new BigDecimal("0"));
                }
                //基础数据
                data.setTopGroupId(report.getTopGroupId());
                data.setTopGroupName(report.getTopGroupName());
                data.setDriveId(report.getDriveId());
                data.setRealName(report.getRealName());
                data.setYEAR(year);
                data.setMONTH(month);

                //加班费数据
                data.setSxrOvertimeDays(report.getSxrOvertimeDays());
                data.setJjrOvertimeDays(report.getJjrOvertimeDays());
                data.setOvertimeCost(report.getOvertimeCost());
                //延时
                data.setGzrDelayHour(report.getGzrDelayHour());
                data.setSxrDelayHour(report.getSxrDelayHour());
                data.setJjrDelayHour(report.getJjrDelayHour());
                data.setDelayCost(report.getDelayCost());


                //查找已审批通过的添加延时数
                BigDecimal addGzrDelayCost = new BigDecimal("0");
                if (type == 1) {
                    List<DfdwDzccTWeek> weekListDriver = weekList.stream().filter(p -> Objects.equals(p.getDriveId(), report.getDriveId())).collect(Collectors.toList());
                    if (weekListDriver.size() > 0) {
                        DfdwDzccTOverTime overtimeSet = zbbService.getOneOverTimeSet(overtimeSetList, report.getTopGroupId());
                        //工作日延时
                        Integer scale = 0;
                        if (overtimeSet.getOvertimeHoursMonthType() == 2) {
                            scale = 2;
                        }
                        //得到添加的延时小时数
                        BigDecimal addGzrDelayHour = weekListDriver.stream().map(DfdwDzccTWeek::getAppendDelayHour).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //在原有的小时数上增加
                        BigDecimal newGzrDelayHour = report.getGzrDelayHour().add(addGzrDelayHour).setScale(scale, BigDecimal.ROUND_HALF_UP);
                        data.setGzrDelayHour(newGzrDelayHour);

                        //添加的延时数费用
                        int index = report.getSubsidyStandardDelay().indexOf('|'); // 查找第一个 | 的位置
                        String firstPart = report.getSubsidyStandardDelay().substring(0, index);
                        BigDecimal gzrYsbt = new BigDecimal(firstPart);
                        addGzrDelayCost = gzrYsbt.multiply(addGzrDelayHour).setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal delayCostNew = report.getDelayCost().add(addGzrDelayCost);

                        data.setDelayCost(delayCostNew);
                    }
                }


                //夜餐
                data.setNightShiftDays(report.getNightShiftDays());
                data.setNightShiftCost(report.getNightShiftCost());

                //出差补贴
                data.setTravelAllowance(report.getTravelAllowance());

                //总合计
                BigDecimal allCost = report.getAllCost().add(addGzrDelayCost);
                data.setAllCost(allCost);

                //公里数
                List<VDfdwDzccTZbb> zbb1 = zbbsTemp.stream().filter(p -> p.getDriveId().equals(report.getDriveId())).collect(Collectors.toList());
                if (zbb1.size() > 0) {
                    //循环，存在多辆车则进入多次
//                    for (VDfdwDzccTZbb temp : zbb1) {
//                        if(StringUtils.isNotEmpty(zbb.getLicencePlate())){
//                            if(!temp.getLicencePlate().contains(zbb.getLicencePlate())){
//                                continue;
//                            }
//                        }
//
//                        //车辆相关数据
//                        data.setCarId(temp.getCarId());
//                        data.setCarMold(temp.getCarMold());
//                        data.setLicencePlate(temp.getLicencePlate());
//                        data.setActualValue(temp.getActualValue());
//                        data.setWorkValue(temp.getWorkValue());
//                        zbbs.add(data);
//                    }

                    for (int i = 0; i < zbb1.size(); i++) {
                        VDfdwDzccTZbb temp = zbb1.get(i);
                        if (StringUtils.isNotEmpty(zbb.getLicencePlate())) {
                            if (!temp.getLicencePlate().contains(zbb.getLicencePlate())) {
                                continue;
                            }
                        }
                        if (i == 0) {
                            //车辆相关数据
                            data.setCarId(temp.getCarId());
                            data.setCarMold(temp.getCarMold());
                            data.setLicencePlate(temp.getLicencePlate());
                            data.setActualValue(temp.getActualValue());
                            data.setWorkValue(temp.getWorkValue());
                            zbbs.add(data);
                        } else {
                            VDfdwDzccTZbb dataNew = JSONObject.parseObject(JSONObject.toJSONString(data), VDfdwDzccTZbb.class);
                            dataNew.setCarId(temp.getCarId());
                            dataNew.setCarMold(temp.getCarMold());
                            dataNew.setLicencePlate(temp.getLicencePlate());
                            dataNew.setActualValue(temp.getActualValue());
                            dataNew.setWorkValue(temp.getWorkValue());
                            zbbs.add(dataNew);
                        }

                    }
                } else {
                    //用户输入的车牌查询,那么车牌为空的需要不展示
                    if (StringUtils.isNotEmpty(zbb.getLicencePlate())) {
                        continue;
                    }
                    data.setCarId(null);
                    data.setCarMold(null);
                    data.setLicencePlate(null);
                    data.setActualValue(0);
                    data.setWorkValue(0);
                    zbbs.add(data);
                }
            }


            int index = 0;
            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setYearMonth(zbb.getYearMonth().substring(0, 7));
                // index == 0表示数据为第一行
                if (i == 0) {
                    zbbs.get(i).setCount(1);
                } else {
                    // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                    if (zbbs.get(i).getDriveId().equals(zbbs.get(index).getDriveId())) {
                        zbbs.get(index).setCount(zbbs.get(index).getCount() + 1);
                        zbbs.get(i).setCount(0);
                    } else {
                        index = i;
                        // 如果当前行和上一行其值不相等
                        zbbs.get(i).setCount(1);
                    }

                }
            }

            // 计算出车单区域情况
            for (VDfdwDzccTZbb item : zbbs) {
                if (item.getCount() != 0) {
                    List<DfdwDzccTCclcStatusDaily> dailyList = cclcStatusDailyService.list(new QueryWrapper<DfdwDzccTCclcStatusDaily>()
                            .eq("YEAR(moveDate)", year)
                            .eq("MONTH(moveDate)", month)
                            .lambda()
                            .eq(DfdwDzccTCclcStatusDaily::getDriveid, item.getDriveId())
                    );
                    item.setCdsNum(dailyList.stream().filter(p -> "出大市".equals(p.getStatus())).count());
                    item.setKqyNum(dailyList.stream().filter(p -> "跨区域".equals(p.getStatus())).count());
                    item.setBqyNum(dailyList.stream().filter(p -> "本区域".equals(p.getStatus())).count());
                    item.setYzNum(dailyList.stream().filter(DfdwDzccTCclcStatusDaily::getIstoyz).count());
                } else {
                    item.setCdsNum(0L);
                    item.setKqyNum(0L);
                    item.setBqyNum(0L);
                    item.setYzNum(0L);
                }
            }

            XSSFSheet sheet = workbook.createSheet("加班费信息");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 22));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(zbb.getTopGroupName() + "加班费信息");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);

            row.createCell(0).setCellValue("月份");
            row.createCell(1).setCellValue("申请单位");
            row.createCell(2).setCellValue("车型");
            row.createCell(3).setCellValue("司机");
            row.createCell(4).setCellValue("车牌");
            row.createCell(5).setCellValue("车辆里程");
            row.createCell(6).setCellValue("工作里程");
            row.createCell(7).setCellValue("公里津贴");
            row.createCell(8).setCellValue("加班天数(双休日)");
            row.createCell(9).setCellValue("加班天数（节假日）");
            row.createCell(10).setCellValue("加班费合计");
            row.createCell(11).setCellValue("延时小时(工作日)");
            row.createCell(12).setCellValue("延时小时(双休日)");
            row.createCell(13).setCellValue("延时小时(节假日)");
            row.createCell(14).setCellValue("延时津贴");
            row.createCell(15).setCellValue("夜餐台班数");
            row.createCell(16).setCellValue("夜餐金额");
            row.createCell(17).setCellValue("出差补贴");
            row.createCell(18).setCellValue("合计");
            row.createCell(19).setCellValue("出大市");
            row.createCell(20).setCellValue("跨区域");
            row.createCell(21).setCellValue("本区域");
            row.createCell(22).setCellValue("鄞州");

            //获取字典加班费配置信息
//            List<DzccDictionaryvalue> dictionaryvalues = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
//                    .eq(DzccDictionaryvalue::getTitleID, 970221)
//                    .like(DzccDictionaryvalue::getContent, "补贴标准"));
//
//            BigDecimal gzrYsbt = new BigDecimal("19.70"); //工作日延时
//            BigDecimal sxrYsbt = new BigDecimal("26.20"); //双休日延时
//            BigDecimal jjrYsbt = new BigDecimal("39.30"); //节假日延时
//            BigDecimal sxrJbfbt = new BigDecimal("210"); //双休日加班费补贴
//            BigDecimal jjrJbfbt = new BigDecimal("314.5"); //节假日加班费补贴
//            BigDecimal gyBt = new BigDecimal("15"); //节假日加班费补贴
//            for (DzccDictionaryvalue dict : dictionaryvalues) {
//                if ("工作日延时工作补贴标准(元/小时)".equals(dict.getContent())) {
//                    gzrYsbt = new BigDecimal(dict.getParameter());
//                } else if ("双休日延时工作补贴标准(元/小时)".equals(dict.getContent())) {
//                    sxrYsbt = new BigDecimal(dict.getParameter());
//                } else if ("工作日延时工作补贴标准(元/小时)".equals(dict.getContent())) {
//                    jjrYsbt = new BigDecimal(dict.getParameter());
//                } else if ("双休日加班费补贴标准(元/天)".equals(dict.getContent())) {
//                    sxrJbfbt = new BigDecimal(dict.getParameter());
//                } else if ("节假日加班费补贴标准(元/天)".equals(dict.getContent())) {
//                    jjrJbfbt = new BigDecimal(dict.getParameter());
//                } else if ("夜餐金额补贴标准(元/天)".equals(dict.getContent())) {
//                    gyBt = new BigDecimal(dict.getParameter());
//                }
//            }
            //获取指定月份加班时长数据
            //  List<VDfdwDzccTCclcdetail> cclcdetailList = ivDfdwDzccTCclcdetailService.list(new LambdaQueryWrapper<VDfdwDzccTCclcdetail>().ge(VDfdwDzccTCclcdetail::getMoveDate, nowDate.format(df)).lt(VDfdwDzccTCclcdetail::getMoveDate, nextDate.format(df)));

            //获取所有车
            List<DzccCar> carList = carService.list();
            for (int i = 0; i < zbbs.size(); i++) {
                zbbs.get(i).setAllValue(0);
                for (int j = i; j < i + zbbs.get(i).getCount(); j++) {
                    zbbs.get(i).setAllValue(zbbs.get(i).getAllValue() + zbbs.get(j).getWorkValue());
                }

                VDfdwDzccTZbb vDfdwDzccTZbb = zbbs.get(i);
                List<DzccCar> cars = carList.stream().filter(p -> p.getId().equals(vDfdwDzccTZbb.getCarId())).collect(Collectors.toList());
                Integer CarGenre = 0;
                if (cars.size() > 0) {
                    CarGenre = cars.get(0).getCarGenre();
                }
                //计算公里津贴
                if (CarGenre == 1) {
                    //大车
                    BigDecimal mileageCost = new BigDecimal("0.3").multiply(new BigDecimal(vDfdwDzccTZbb.getAllValue()));
                    vDfdwDzccTZbb.setMileageCost(mileageCost);
                } else {
                    //小车
                    if (vDfdwDzccTZbb.getAllValue() <= 1000) {
                        BigDecimal mileageCost = new BigDecimal("0.2").multiply(new BigDecimal(vDfdwDzccTZbb.getAllValue()));
                        vDfdwDzccTZbb.setMileageCost(mileageCost);
                    } else {
                        Integer a = vDfdwDzccTZbb.getAllValue() - 1000;
                        BigDecimal cost = new BigDecimal("0.3").multiply(new BigDecimal(a));
                        BigDecimal mileageCost = cost.add(new BigDecimal("200"));
                        vDfdwDzccTZbb.setMileageCost(mileageCost);
                    }
                }


                // ivDfdwDzccTZbbServicel.jbfJx(vDfdwDzccTZbb, cclcdetailList, gzrYsbt, sxrYsbt, jjrYsbt, sxrJbfbt, jjrJbfbt, gyBt);
//                List<DfdwDzccTCclcdetailReport> report = reportList.stream().filter(p -> p.getDriveId().equals(vDfdwDzccTZbb.getDriveId())).collect(Collectors.toList());
//                ivDfdwDzccTZbbServicel.JbfJxNew(vDfdwDzccTZbb,report);
                if (vDfdwDzccTZbb.getMileageCost() != null) {
                    BigDecimal allCost = vDfdwDzccTZbb.getAllCost().add(vDfdwDzccTZbb.getMileageCost());
                    vDfdwDzccTZbb.setAllCost(allCost);
                }
                // 列合并
                if (zbbs.get(i).getCount() > 1) {
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 6, 6));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 7, 7));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 8, 8));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 9, 9));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 10, 10));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 11, 11));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 12, 12));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 13, 13));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 14, 14));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 15, 15));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 16, 16));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 17, 17));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 18, 18));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 19, 19));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 20, 20));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 21, 21));
                    sheet.addMergedRegion(new CellRangeAddress(2 + i, 1 + i + zbbs.get(i).getCount(), 22, 22));
                }
                //解析加班时长数据


                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(zbbs.get(i).getYearMonth());
                row.createCell(1).setCellValue(zbbs.get(i).getTopGroupName());
                row.createCell(2).setCellValue(zbbs.get(i).getCarMold());

                row.createCell(3).setCellValue(zbbs.get(i).getRealName());
                row.createCell(4).setCellValue(zbbs.get(i).getLicencePlate());

                row.createCell(5).setCellValue(zbbs.get(i).getWorkValue());
                row.createCell(6).setCellValue(zbbs.get(i).getAllValue());

                row.createCell(7).setCellValue(zbbs.get(i).getMileageCost().doubleValue());
                row.createCell(8).setCellValue(zbbs.get(i).getSxrOvertimeDays());
                row.createCell(9).setCellValue(zbbs.get(i).getJjrOvertimeDays());
                row.createCell(10).setCellValue(zbbs.get(i).getOvertimeCost().doubleValue());
                row.createCell(11).setCellValue(zbbs.get(i).getGzrDelayHour().doubleValue());
                row.createCell(12).setCellValue(zbbs.get(i).getSxrDelayHour().doubleValue());
                row.createCell(13).setCellValue(zbbs.get(i).getJjrDelayHour().doubleValue());
                row.createCell(14).setCellValue(zbbs.get(i).getDelayCost().doubleValue());
                row.createCell(15).setCellValue(zbbs.get(i).getNightShiftDays());
                row.createCell(16).setCellValue(zbbs.get(i).getNightShiftCost().doubleValue());
                if (zbbs.get(i).getTravelAllowance() == null) {
                    row.createCell(17).setCellValue(0);
                } else {
                    row.createCell(17).setCellValue(zbbs.get(i).getTravelAllowance().doubleValue());
                }
                row.createCell(18).setCellValue(zbbs.get(i).getAllCost().doubleValue());
                row.createCell(19).setCellValue(zbbs.get(i).getCdsNum());
                row.createCell(20).setCellValue(zbbs.get(i).getKqyNum());
                row.createCell(21).setCellValue(zbbs.get(i).getBqyNum());
                row.createCell(22).setCellValue(zbbs.get(i).getYzNum());


                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("加班费报表".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    /**
     * 二级报表
     *
     * @param params
     * @return
     */
    @RequestMapping("/GetCCLCDetailList")
    //@PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF01QX02,NDWCC01JBF02QX01')")
    public Result<Object> getCCLCDetailList(@RequestBody Map<String, Object> params) {
        Integer selectType = (Integer) params.get("selectType");
        Integer driveId = (Integer) params.get("driveId");
        Integer year = (Integer) params.get("year");
        Integer month = (Integer) params.get("month");
        Integer type = 0;
        if (params.get("type") != null) {
            type = (Integer) params.get("type");
        }
        List<VDfdwDzccTCclcdetail> cclcDetailList = ivDfdwDzccTCclcdetailService.getCCLCDetailList(selectType, driveId, year, month, type);
        return Result.ok(cclcDetailList);
    }


    /**
     * 二级报表导出
     *
     * @param params
     * @return
     */
    @RequestMapping("/DownLoadlevel2")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF02QX01')")
    public Result<Object> DownLoadlevel2(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        Integer selectType = (Integer) params.get("selectType");
        Integer driveId = (Integer) params.get("driveId");
        Integer year = (Integer) params.get("year");
        Integer month = (Integer) params.get("month");
        Integer type = 0;
        if (params.get("type") != null) {
            type = (Integer) params.get("type");
        }

        DzccPerson person = personService.getById(driveId);
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<VDfdwDzccTCclcdetail> cclcDetailList = ivDfdwDzccTCclcdetailService.getCCLCDetailList(selectType, driveId, year, month, type);


            String title = "";
            Integer laseCol = 0;
            if (selectType == 1) {
                title = person.getRealName() + "(" + year + "年" + month + "月)" + "加班费";
                laseCol = 3;
            } else if (selectType == 2) {
                title = person.getRealName() + "(" + year + "年" + month + "月)" + "延时津贴";
                laseCol = 5;
            } else if (selectType == 3) {
                title = person.getRealName() + "(" + year + "年" + month + "月)" + "夜餐台班数";
                laseCol = 3;
            } else if (selectType == 4) {
                title = person.getRealName() + "(" + year + "年" + month + "月)" + "出差补贴";
                laseCol = 3;
            }
            XSSFSheet sheet = workbook.createSheet(title);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, laseCol));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(title);
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);

            row.createCell(0).setCellValue("日期");
            row.createCell(1).setCellValue("天数类型");
            row.createCell(2).setCellValue("车牌");
            if (selectType == 2) {
                row.createCell(3).setCellValue("总加班时长");
                row.createCell(4).setCellValue("上午加班时长");
                row.createCell(5).setCellValue("下午加班时长");
            }
            if (selectType == 4) {
                row.createCell(3).setCellValue("补贴金额");
            }
            if (selectType == 1 || selectType == 3) {
                row.createCell(3).setCellValue("天数");
            }
            for (int i = 0; i < cclcDetailList.size(); i++) {

                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(cclcDetailList.get(i).getDateTemp());
                String dayType = cclcDetailList.get(i).getDayType() == 0 ? "休息日" : cclcDetailList.get(i).getDayType() == 1 ? "工作日" : "节假日";
                row.createCell(1).setCellValue(dayType);
                row.createCell(2).setCellValue(cclcDetailList.get(i).getLicencePlate());
                if (selectType == 2) {
                    row.createCell(3).setCellValue(cclcDetailList.get(i).getOvertimeHoursAll().toString());
                    row.createCell(4).setCellValue(cclcDetailList.get(i).getOvertimeHoursAm().toString());
                    row.createCell(5).setCellValue(cclcDetailList.get(i).getOvertimeHoursPm().toString());
                }
                if (selectType == 4) {
                    row.createCell(3).setCellValue(cclcDetailList.get(i).getTravelAllowance().toString());
                }
                if (selectType == 1 || selectType == 3) {
                    row.createCell(3).setCellValue("1");
                }

                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String(title.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }


    }


    /**
     * 三级报表
     * 根据司机id和时间查找对应出车单信息
     *
     * @param params
     * @return
     */
    @RequestMapping("/getCcdByDriveIdAndTime")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF02QX01')")
    public Result<Object> getCcdByDriveIdAndTime(@RequestBody Map<String, Object> params) {
        Integer driveId = (Integer) params.get("driveId");
        String date = (String) params.get("date");
        List<DfdwDzccTCclc> list = cclcService.getCcdByDriveIdAndTime(driveId, date);
        return Result.ok(list);
    }

    /**
     * 三级报表导出
     * 根据司机id和时间查找对应出车单信息
     *
     * @param params
     * @return
     */
    @RequestMapping("/DownLoadlevel3")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF02QX01')")
    public Result<Object> DownLoadlevel3(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        Integer driveId = (Integer) params.get("driveId");
        String date = (String) params.get("date");

        DzccPerson person = personService.getById(driveId);
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<DfdwDzccTCclc> list = cclcService.getCcdByDriveIdAndTime(driveId, date);
            String title = person.getRealName() + "(" + date + ")" + "出车单";
            Integer laseCol = 7;

            XSSFSheet sheet = workbook.createSheet(title);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, laseCol));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(title);
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);

            row.createCell(0).setCellValue("出车单编号");
            row.createCell(1).setCellValue("车牌");
            row.createCell(2).setCellValue("实际开始日期");
            row.createCell(3).setCellValue("实际结束日期");
            row.createCell(4).setCellValue("申请人");
            row.createCell(5).setCellValue("用车人");
            row.createCell(6).setCellValue("目的地");
            row.createCell(7).setCellValue("出车情况");


            for (int i = 0; i < list.size(); i++) {

                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getApplyNo());
                row.createCell(1).setCellValue(list.get(i).getLicencePlate());
                row.createCell(2).setCellValue(sdf2.format(list.get(i).getActualStartTime()));
                row.createCell(3).setCellValue(sdf2.format(list.get(i).getActualEndTime()));
                row.createCell(4).setCellValue(list.get(i).getApplyUserName());
                row.createCell(5).setCellValue(list.get(i).getYcrName());
                row.createCell(6).setCellValue(list.get(i).getAddressInfo());
                row.createCell(7).setCellValue(list.get(i).getStatus());

                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String(title.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }

    }

    /**
     * 每天出车单报表
     * 根据司机id和车辆id查找每天的单数
     *
     * @param cclcStatusDaily
     * @return
     */
    @RequestMapping("/getCcdStatusDaily")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF02QX01')")
    public Result<Object> getCcdStatusDaily(@RequestBody DfdwDzccTCclcStatusDaily cclcStatusDaily) {
        return Result.ok(cclcStatusDailyService.getCcdStatusDaily(cclcStatusDaily));
    }

    /**
     * 每天出车单报表导出
     * 根据司机id和车辆id查找每天的单数
     *
     * @param cclcStatusDaily
     * @return
     */
    @RequestMapping("/downCcdStatusDaily")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JBF01QX01,NDWCC01JBF02QX01')")
    public Result<Object> downCcdStatusDaily(@RequestBody DfdwDzccTCclcStatusDaily cclcStatusDaily, HttpServletResponse response) {
        DzccPerson person = personService.getById(cclcStatusDaily.getDriveid());
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<DfdwDzccTCclcStatusDaily> list = cclcStatusDailyService.getCcdStatusDaily(cclcStatusDaily);
            String title = person.getRealName() + "(" + cclcStatusDaily.getYear() + "-" + cclcStatusDaily.getMonth() + ")" + "出车单每日出车情况";
            Integer laseCol = 4;

            XSSFSheet sheet = workbook.createSheet(title);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, laseCol));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(title);
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);

            row.createCell(0).setCellValue("日期");
            row.createCell(1).setCellValue("出大市");
            row.createCell(2).setCellValue("跨区域");
            row.createCell(3).setCellValue("本区域");
            row.createCell(4).setCellValue("鄞州");


            for (int i = 0; i < list.size(); i++) {

                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getMovedate().toString());
                row.createCell(1).setCellValue(list.get(i).getCdsNum());
                row.createCell(2).setCellValue(list.get(i).getKqyNum());
                row.createCell(3).setCellValue(list.get(i).getBqyNum());
                row.createCell(4).setCellValue(list.get(i).getYzNum());

                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String(title.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    /**
     * 测试接口 -- 加班费每日详情
     * @param cclcDetailsDto
     * @return
     */
    @RequestMapping("/getDzccCclcDetails")
    public Result<Object> getDzccCclcDetails(@RequestBody CclcDetailsDto cclcDetailsDto){
     List<DfdwDzccTCclcdetail>   list =  dfdwDzccTCclcdetailService.list(new LambdaQueryWrapper<DfdwDzccTCclcdetail>()
                .eq(cclcDetailsDto.getYwId() !=null,DfdwDzccTCclcdetail::getYwId,cclcDetailsDto.getYwId())
                .eq(cclcDetailsDto.getCarId() !=null,DfdwDzccTCclcdetail::getCarId,cclcDetailsDto.getCarId())
                .eq(cclcDetailsDto.getDriveId() !=null,DfdwDzccTCclcdetail::getDriveId,cclcDetailsDto.getDriveId())
                .ge(cclcDetailsDto.getStartTime() !=null,DfdwDzccTCclcdetail::getMoveDate,cclcDetailsDto.getStartTime())
                .le(cclcDetailsDto.getEndTime() !=null,DfdwDzccTCclcdetail::getMoveDate,cclcDetailsDto.getEndTime())
        );
     return Result.ok(list);
    }


    /**
     * 测试接口 -- 修改加班费明细信息
     * @param cclcDetailsDto
     * @return
     */
    @RequestMapping("/dzccDeatilOvertimeHoursNoon")
    public Result<Object> dzccDeatilOvertimeHoursNoon(@RequestBody DfdwDzccTCclcdetail cclcDetailsDto){
        DfdwDzccTCclcdetail cclcdetail = dfdwDzccTCclcdetailService.getById(cclcDetailsDto.getId());
        if(cclcdetail !=null){
            if(cclcDetailsDto.getOvertimeHoursNoon() !=null){
                //判断是否为0
                if(cclcDetailsDto.getOvertimeHoursNoon() ==BigDecimal.ZERO){
                    cclcdetail.setOvertimeHoursNoon(null);
                }else{
                    cclcdetail.setOvertimeHoursNoon(cclcDetailsDto.getOvertimeHoursNoon());
                }
            }
            if(cclcDetailsDto.getOvertimeHoursAll() !=null){
                cclcdetail.setOvertimeHoursAll(cclcDetailsDto.getOvertimeHoursAll());
            }
            if(cclcDetailsDto.getOvertimeHoursAm() !=null){
                cclcdetail.setOvertimeHoursAm(cclcDetailsDto.getOvertimeHoursAm());
            }
            if(cclcDetailsDto.getOvertimeHoursPm() !=null){
                cclcdetail.setOvertimeHoursPm(cclcDetailsDto.getOvertimeHoursPm());
            }
        }
        return Result.ok(cclcdetail);
    }





}
