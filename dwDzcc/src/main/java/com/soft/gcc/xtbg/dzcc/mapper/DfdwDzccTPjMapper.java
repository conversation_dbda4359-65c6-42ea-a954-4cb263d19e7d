package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_PJ(电子出车单-评价)】的数据库操作Mapper
* @createDate 2025-01-21 14:33:25
* @Entity com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj
*/
public interface DfdwDzccTPjMapper extends BaseMapper<DfdwDzccTPj> {

    IPage<DfdwDzccTPj> getPage(Page<Object> objectPage, @Param("dfdwDzccTPj") DfdwDzccTPj dfdwDzccTPj, @Param("person") DzccPersonEntity person);

    List<DfdwDzccTPj> getList(@Param("dfdwDzccTPj") DfdwDzccTPj dfdwDzccTPj, @Param("person") DzccPersonEntity person);
}




