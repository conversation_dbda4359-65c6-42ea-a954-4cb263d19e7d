package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 电子出车-车辆回程通知设置
 * @TableName DFDW_DZCC_T_CAR_BACKHAUL
 */
@TableName(value ="DFDW_DZCC_T_CAR_BACKHAUL")
@Data
public class DfdwDzccTCarBackhaul extends DzccBaseEntity implements Serializable {
    /**
     * 主键Id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 车Id
     */
    private Integer carId;

    /**
     * 车辆办公点Id
     */
    private Integer bgdId;

    /**
     * 上午检测时间
     */
    private String backStartTime;

    /**
     * 下午检测时间
     */
    private String backEndTime;

    /**
     * 持续时间（分钟）
     */
    private Integer continueMinute;

    /**
     * 检测范围（米）
     */
    private String radius;

    /**
     * 单位Id
     */
    private Integer groupId;

    /**
     * 工作上班上午检测时间
     */
    private String workStartTime;

    /**
     * 工作上班下午检测时间
     */
    private String workEndTime;

    /**
     * 计算类型（0有工单计算 1无工单计算）
     */
    private Integer calculateType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String groupName;
    @TableField(exist = false)
    private String licencePlate;

    @TableField(exist = false)
    private String unitName;

    @TableField(exist = false)
    private Integer type;

}