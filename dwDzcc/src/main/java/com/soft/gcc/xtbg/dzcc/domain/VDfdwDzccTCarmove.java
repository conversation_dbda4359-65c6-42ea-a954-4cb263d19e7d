package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_CARMOVE
*/
@TableName(value ="V_DFDW_DZCC_T_CARMOVE", autoResultMap = true)
@Data
public class VDfdwDzccTCarmove extends DzccBaseEntity implements Serializable {
    /**
    *
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    *
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
    *
    */
    @TableField(value = "curDate")
    @JSONField(name = "curDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
    /**
    *
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")

    private String driverName;
    /**
    *
    */
    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")

    private String licensePlate;
    /**
    *
    */
    @TableField(value = "applyTopDeptId")
    @JSONField(name = "applyTopDeptId")

    private Integer applyTopDeptId;
    /**
    *
    */
    @TableField(value = "applyDeptName")
    @JSONField(name = "applyDeptName")

    private String applyDeptName;
    /**
     *
     */
    @TableField(value = "carMold")
    @JSONField(name = "carMold")

    private String carMold;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    @TableField(exist = false)
    private String alarmType;
    @TableField(exist = false)
    private String status;
    @TableField(exist = false)
    @JSONField(name = "LicencePlate")
    private String LicencePlate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
