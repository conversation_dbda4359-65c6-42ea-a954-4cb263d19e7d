package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTAddress;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_ADDRESS(出车单-目的地)】的数据库操作Service
* @createDate 2022-10-21 17:06:33
*/
public interface IDfdwDzccTAddressService extends IService<DfdwDzccTAddress> {

    /**
     * 添加出车单 目的地
     * @param dfdwDzccTAddress
     * @return
     */
    Result<Object> addCcdAddess(DfdwDzccTAddress dfdwDzccTAddress);

}
