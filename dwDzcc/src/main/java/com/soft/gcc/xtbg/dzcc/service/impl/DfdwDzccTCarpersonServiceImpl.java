package com.soft.gcc.xtbg.dzcc.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.entity.Cascader;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarpersonMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCdzglMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CARPERSON】的数据库操作Service实现
 * @createDate 2023-01-13 10:41:12
 */
@Service
public class DfdwDzccTCarpersonServiceImpl extends ServiceImpl<DfdwDzccTCarpersonMapper, DfdwDzccTCarperson>
        implements IDfdwDzccTCarpersonService {
    @Autowired
    DzccGroupitemMapper groupitemMapper;
    @Autowired
    DfdwDzccTCdzglMapper cdzglMapper;

    @Override
    public Result<Object> GetCDZPZList(DzccPersonEntity person, Map<String, String> map) {
        try {
            String name = ParseUtil.tryParseString(map.get("cdzname"));
            int deptId = ParseUtil.tryParseInt(map.get("dept"));
            int pageNum = ParseUtil.tryParseInt(map.get("pageNum"));
            int pageSize = ParseUtil.tryParseInt(map.get("pageSize"), 1);
            int personTypeId = ParseUtil.tryParseInt(map.get("personTypeId"));
            IPage<DfdwDzccTCdzgl> list = new Page<>();
            list.setCurrent(pageNum);
            list.setSize(pageSize);
            if (person.getDzccQx() == 1 || person.getDzccQx() == 2) {
                List<DfdwDzccTCdzgl> personList = cdzglMapper.GetCDZPZList(deptId, name, person.getDzccQx(), person.getId(), personTypeId);
                list.setTotal(personList.size());
                if (personList.size() > 0) {
                    int fromIndex = (pageNum - 1) * pageSize;
                    int toIndex = pageNum * pageSize;
                    if (toIndex > personList.size()) {
                        toIndex = personList.size();
                    }
                    personList = personList.subList(fromIndex, toIndex);
                }
                list.setRecords(personList);
            }
            return Result.ok(list);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }

    @Override
    public Result<Object> GetCarList(DzccPersonEntity person, Map<String, String> map) {
        try {
            Integer groupId = ParseUtil.tryParseInt(map.get("groupId"));
            Integer personId = ParseUtil.tryParseInt(map.get("personId"));
            List<DzccCar> carList = baseMapper.GetCarList(groupId, person.getDzccQx(), person.getId(), personId);
            List<DfdwDzccTCarperson> carpersonList = this.list(new LambdaQueryWrapper<DfdwDzccTCarperson>().eq(DfdwDzccTCarperson::getPersonId, personId));
            List<Integer> carIds = carpersonList.stream().map(DfdwDzccTCarperson::getCarId).collect(Collectors.toList());
            Set<Cascader> cascaderList = new HashSet<>();
            for (DzccCar car : carList) {
                Cascader cascader = new Cascader();
                cascader.setValue(car.getId().toString());
                cascader.setLabel(car.getLicencePlate());
                cascader.setChange(carIds.contains(car.getId()));
                cascaderList.add(cascader);
            }
            return Result.ok(cascaderList);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }

    @Override
    public Result<Object> EditCDZPZ(DzccPersonEntity person, Map<String, String> map) {
        try {
            Integer groupId = ParseUtil.tryParseInt(map.get("groupId"));
            Integer personId = ParseUtil.tryParseInt(map.get("personId"));
            String ids = ParseUtil.tryParseString(map.get("carIds"));
            List<Integer> selectCarIds = new ArrayList<>();
            if (StringUtils.isNotEmpty(ids)) {
                String[] number = ids.split(",");
                Integer[] numbers = Convert.toIntArray(number);
                selectCarIds = Arrays.asList(numbers);
            }
            List<DzccCar> carList = baseMapper.GetCarList(groupId, person.getDzccQx(), person.getId(), personId);
            List<Integer> carIds = carList.stream().map(DzccCar::getId).collect(Collectors.toList());
            List<DfdwDzccTCarperson> carpersonList = this.list(new LambdaQueryWrapper<DfdwDzccTCarperson>().eq(DfdwDzccTCarperson::getPersonId, personId));
            List<Integer> carpersonForCarIds = carpersonList.stream().map(DfdwDzccTCarperson::getCarId).collect(Collectors.toList());

            // 取可选车辆id和carperson表中车辆id的交集的补集
            List<Integer> idList = (List<Integer>) CollectionUtils.intersection(carIds, carpersonForCarIds);
            if (idList.size() > 0) {
                this.remove(new LambdaQueryWrapper<DfdwDzccTCarperson>()
                        .in(DfdwDzccTCarperson::getCarId, idList)
                        .eq(DfdwDzccTCarperson::getPersonId, personId)
                );
            }
            for (Integer id : selectCarIds) {
                DfdwDzccTCarperson carperson = new DfdwDzccTCarperson();
                carperson.setCarId(id);
                carperson.setPersonId(personId);
                this.save(carperson);
            }
            return Result.ok();
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }

    @Override
    public Result<Object> GetCDZGroup(DzccPersonEntity person, Map<String, String> map) {
        try {
            Integer personId = ParseUtil.tryParseInt(map.get("personId"));
            Integer isShowAll =  ParseUtil.tryParseInt(map.get("isShowAll"));
            List<DzccGroupitem> groupitemList = groupitemMapper.getCDZGroup(personId, isShowAll, person.getDzccQx(), person.getId());
            return Result.ok(groupitemList);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }

    @Override
    public Result<Object> GetCarListForCdz(DzccPersonEntity person, Map<String, String> map) {
        try {
            Integer groupId = ParseUtil.tryParseInt(map.get("groupId"));
            Integer personId = ParseUtil.tryParseInt(map.get("personId"));
            List<DzccCar> carList = baseMapper.GetCarList(groupId, person.getDzccQx(), person.getId(), personId);
            List<DfdwDzccTCarperson> carpersonList = this.list(new LambdaQueryWrapper<DfdwDzccTCarperson>().eq(DfdwDzccTCarperson::getPersonId, personId));
            List<Integer> carIds = carpersonList.stream().map(DfdwDzccTCarperson::getCarId).collect(Collectors.toList());
            carList = carList.stream().filter(t->carIds.contains(t.getId())).collect(Collectors.toList());
            return Result.ok(carList);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }
}




