package com.soft.gcc.xtbg.dzcc.controller;

import com.aspose.words.Document;
import com.aspose.words.PdfSaveOptions;
import com.aspose.words.SaveFormat;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType0Font;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

public class DzccBaseController extends BaseController {
    /**
     * doc、docx文件转PDF
     */
    public void  wordToPdf(String inPath, String outPath) {
        FileOutputStream os =null;
        try {
            // 加载 Word 文档
            Document doc = new Document(inPath);
            // 创建 PdfSaveOptions 对象并设置字体
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setEmbedFullFonts(true); // 将字体嵌入到 PDF 中
            // 新建输出文件
            File file = new File(outPath);
            os = new FileOutputStream(file);
            // 将 Word 文档保存为 PDF
            doc.save(os, pdfSaveOptions);
            // 关闭输出流
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("word转换PDF出错");
        }finally{
            if(os!=null){
                try {
                    os.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public DzccPersonEntity getDzccPerson() {
        DzccPersonEntity dzccPersonEntity = user();
        dzccPersonEntity.setDzccQx(0);
        List<Integer> list = new ArrayList<>();
        for (String roleVO : dzccPersonEntity.getRoleList()) {
            if ("协同办公-电子出车-车辆总管理".equals(roleVO)) {
                dzccPersonEntity.setDzccQx(1);
                list.add(1);
            } else if ("协同办公-电子出车-车辆管理人员".equals(roleVO)) {
                if (dzccPersonEntity.getDzccQx() != 1) {
                    dzccPersonEntity.setDzccQx(2);
                }
                list.add(2);
            } else if ("协同办公-电子出车-车辆调度人员".equals(roleVO) || "协同办公-电子出车-车辆运监人员".equals(roleVO)) {
                if (dzccPersonEntity.getDzccQx() != 1 && dzccPersonEntity.getDzccQx() != 2) {
                    dzccPersonEntity.setDzccQx(4);
                }
                if ("协同办公-电子出车-车辆调度人员".equals(roleVO)) {
                    list.add(4);
                } else {
                    list.add(6);
                }
            } else if ("协同办公-电子出车-司机".equals(roleVO) || "协同办公-电子出车-出车单申请".equals(roleVO) || "协同办公-电子出车-领导审核".equals(roleVO)) {
                if (dzccPersonEntity.getDzccQx() != 1 && dzccPersonEntity.getDzccQx() != 2 && dzccPersonEntity.getDzccQx() != 4) {
                    dzccPersonEntity.setDzccQx(3);
                }
                list.add(3);
            } else if ("协同办公-电子出车-信息填报人员".equals(roleVO) || "协同办公-电子出车-东耀管理员".equals(roleVO)) {
                list.add(5);
            }

        }
        dzccPersonEntity.setQxs(list);
        return dzccPersonEntity;
    }
}
