package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DzccDictionaryvalue;
import com.soft.gcc.xtbg.dzcc.service.IDzccDictionaryvalueService;
import com.soft.gcc.xtbg.dzcc.mapper.DzccDictionaryvalueMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DictionaryValue】的数据库操作Service实现
* @createDate 2022-11-10 14:57:36
*/
@Service
public class DzccDictionaryvalueServiceImpl extends ServiceImpl<DzccDictionaryvalueMapper, DzccDictionaryvalue>
    implements IDzccDictionaryvalueService{

}




