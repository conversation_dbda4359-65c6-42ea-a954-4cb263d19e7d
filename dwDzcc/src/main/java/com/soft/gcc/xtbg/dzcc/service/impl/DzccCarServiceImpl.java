package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dictionary_value.entity.Dictionaryvalue;
import com.soft.gcc.common.dictionary_value.mapper.DictionaryvalueMapper;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.*;
import com.soft.gcc.xtbg.dzcc.service.DzccCarService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.service.ILzclTCarMoveChildService;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car(零租车辆--车辆管理表)】的数据库操作Service实现
* @createDate 2023-01-17 09:52:31
*/
@Service
public class DzccCarServiceImpl extends ServiceImpl<DzccCarMapper, DzccCar>
    implements DzccCarService{
    @Resource
    DzccGroupitemMapper dzccGroupitemMapper;
    @Resource
    DfdwDzccTClgldetailMapper clgldetailMapper;
    @Resource
    DictionaryvalueMapper dictionaryvalueMapper;
    @Resource
    LzclTCarMoreMapper lzclTCarMoreMapper;
    @Resource
    private ILzclTCarMoveChildService lzclTCarMoveChildService;

    @Override
    public Result<Object> GetClsyList(DzccPersonEntity dzccPersonEntity, DzccCar car) {
        try {
            List<DzccCar> clsyList;
            List<Map<String, Object>> list = new ArrayList<>();
            if (dzccPersonEntity.getDzccQx() == 1 ||  dzccPersonEntity.getDzccQx() == 2 ||  dzccPersonEntity.getDzccQx() == 4) {
                List<Integer> groupIds = new ArrayList<>();
                if (car.getGroupid() != null && car.getGroupid() > 0) {
                    groupIds.add(car.getGroupid());
                } else {
                    List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                    groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
                }
                clsyList = baseMapper.GetClsyListForMonth(
                        groupIds,
                        car.getCarMold(),
                        dzccPersonEntity,
                        car.getBeginDate(),
                        car.getOverDate().plusMonths(1)
                );
                List<DzccCar> cars = this.list(new LambdaQueryWrapper<DzccCar>()
                        .like(car.getCarMold() != null, DzccCar::getCarMold, car.getCarMold())
                        .in(DzccCar::getGroupid, groupIds)
//                        .not(wrapper -> wrapper
//                                .eq(DzccCar::getCarTag, 2)
//                                .apply("LEN(GPSNum) <> 12")
//                        )// 针对产权车辆并且设备号的长度不等于12位的需要过滤
                        .ne(DzccCar::getCarTag, 2) // 排除产权车辆
                        .eq(DzccCar::getIsEnable, 1) // 已启用
                        .ne(DzccCar::getIsDelete, 1) // 排除删除
                        .orderByAsc(DzccCar::getCarMold)
                );
                for (DzccCar dzccCar : cars) {
                    if (dzccCar.getCarMold() == null || dzccCar.getCarMold().isEmpty()) {
                        dzccCar.setCarMold("其他");
                    }
                    boolean istrue = true;
                    for (DzccCar clsyCar : clsyList) {
                        if (dzccCar.getCarMold().equals(clsyCar.getCarMold())) {
                            istrue = false;
                            break;
                        }
                    }
                    // 不存在则添加
                    if (istrue) {
                        DzccCar clsyCar = new DzccCar();
                        clsyCar.setCarMold(dzccCar.getCarMold());
                        clsyCar.setYear(car.getBeginDate().getYear());
                        clsyCar.setMonth(car.getBeginDate().getMonthValue());
                        clsyCar.setNumber(0);
                        clsyList.add(clsyCar);
                    }
                }
                Map<String, Map<String, Object>> map = new HashMap<>();
                for (DzccCar dzccCar : clsyList) {
                   if (map.containsKey(dzccCar.getCarMold())) {
                       Map<String, Object> obj = map.get(dzccCar.getCarMold());
                       if (dzccCar.getMonth() < 10) {
                           obj.put(dzccCar.getYear() + "年0" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                       } else {
                           obj.put(dzccCar.getYear() + "年" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                       }
                   } else {
                       Map<String, Object> obj = new HashMap<>();
                       obj.put("carMold", dzccCar.getCarMold());
                       for (LocalDate date = car.getBeginDate(); !date.isAfter(car.getOverDate()); date = date.plusMonths(1)) {
                           if (date.getMonthValue() < 10) {
                               obj.put(date.getYear() + "年0" + date.getMonthValue() + "月", 0);
                           } else {
                               obj.put(date.getYear() + "年" + date.getMonthValue() + "月", 0);
                           }
                       }
                       if (dzccCar.getMonth() < 10) {
                           obj.put(dzccCar.getYear() + "年0" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                       } else {
                           obj.put(dzccCar.getYear() + "年" + dzccCar.getMonth() + "月", dzccCar.getNumber());
                       }
                       map.put(dzccCar.getCarMold(), obj);
                   }
                }

                for (Map.Entry<String, Map<String, Object>> entry : map.entrySet()) {
                    list.add(entry.getValue());
                }
                if (!list.isEmpty()) {
                    list.sort((o1, o2) -> {
                        String str1 = o1.get("carMold").toString();
                        String str2 = o2.get("carMold").toString();
                        //str1在前，str2在后，默认升序，这里Integer类型的也可以
                        return str1.compareTo(str2);
                    });
                    // 把name为haha的学生放在最后面
                    Map<String, Object> temp = new HashMap<>();
                    boolean flag = false;

                    for(int i = 0; i < list.size(); i++){
                        Map<String, Object> mold = list.get(i);
                        if ("其它".equals(mold.get("carMold"))) {
                            temp = mold;
                            flag = true;
                            list.remove(i);
                            break;
                        }
                    }

                    if (flag) {
                        list.add(temp);
                    }
                }
            }
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetClxxList(DzccPersonEntity dzccPersonEntity, DzccCar car) {
        try {
            IPage<DzccCar> page = new Page<>(car.getPageNum(), car.getPageSize());
            List<DzccCar> list = baseMapper.getClxxList(dzccPersonEntity, car);
            // 分页
            List<DzccCar> carList = new ArrayList<>();
            if (!list.isEmpty()) {
                int fromIndex = (car.getPageNum() - 1) * car.getPageSize();
                int toIndex = car.getPageNum() * car.getPageSize();
                if (toIndex > list.size()) {
                    toIndex = list.size();
                }
                carList = list.subList(fromIndex, toIndex);
            }
            page.setTotal(list.size());
            page.setRecords(carList);
            return Result.ok(page);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> AddClxx(DzccPersonEntity dzccPersonEntity, DzccCar car) {
        try {
            List<DzccCar> carList = this.list(new LambdaQueryWrapper<DzccCar>()
                    .eq(DzccCar::getLicencePlate, car.getLicencePlate())
                    .or()
                    .eq(DzccCar::getGPSNum, car.getGPSNum())
            );
            if (!carList.isEmpty()) {
                return Result.error("车牌或GPS识别号已经存在请重新输入！");
            }
            car.setIsDelete(0);
            car.setIsConfirm(0);
            this.save(car);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> EditClxx(DzccPersonEntity dzccPersonEntity, DzccCar car) {
        try {
            List<DzccCar> carList = this.list(new LambdaQueryWrapper<DzccCar>()
                    .ne(DzccCar::getId, car.getId())
                    .and(t -> t.eq(DzccCar::getLicencePlate, car.getLicencePlate())
                            .or()
                            .eq(DzccCar::getGPSNum, car.getGPSNum()))
            );
            if (!carList.isEmpty()) {
                return Result.error("车牌或GPS识别号已经存在请重新输入！");
            }
            this.updateById(car);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> DeleteClxxById(Integer id) {
        try {
            DzccCar car = this.getById(id);
            if (car == null) {
                return Result.error("删除数据不存在！");
            }
            List<LzclTCarMore> carMores = lzclTCarMoreMapper.selectList(new LambdaQueryWrapper<LzclTCarMore>()
                    .eq(LzclTCarMore::getLicencePlate, car.getLicencePlate())
                    .and(t -> t
                            .isNull(LzclTCarMore::getEndDate)
                            .or(s -> s
                                    .ge(LzclTCarMore::getStartDate, LocalDate.now())
                            )
                    )
            );
            if (!carMores.isEmpty()) {
                return Result.error("该车正在使用或以后存在使用单位，不能删除！");
            }
            this.update(null, new LambdaUpdateWrapper<DzccCar>()
                    .set(DzccCar::getIsDelete, 1)
                    .eq(DzccCar::getId, id)
            );
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> DownLoad(DzccPersonEntity dzccPersonEntity, DzccCar car, HttpServletResponse response) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<DzccCar> list = baseMapper.getClxxList(dzccPersonEntity, car);

            List<Dictionaryvalue> dictionaryvalueList = dictionaryvalueMapper.selectList(new LambdaQueryWrapper<Dictionaryvalue>()
                    .eq(Dictionaryvalue::getTitleid, 970211)
                    .likeRight(Dictionaryvalue::getParameter, "|")
            );

            XSSFSheet sheet = workbook.createSheet("车辆信息");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 13));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("车辆信息");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("车辆类型");
            row.createCell(1).setCellValue("车牌");
            row.createCell(2).setCellValue("车辆品牌");
            row.createCell(3).setCellValue("车辆品牌型号");
            row.createCell(4).setCellValue("行驶证登记时间");
            row.createCell(5).setCellValue("产权单位");
            row.createCell(6).setCellValue("GPS识别号");
            row.createCell(7).setCellValue("驾驶人");
            row.createCell(8).setCellValue("是否启用");
            row.createCell(9).setCellValue("确认人");
            row.createCell(10).setCellValue("确认时间");
            row.createCell(11).setCellValue("车辆标识");
            row.createCell(12).setCellValue("设备启用时间");
            row.createCell(13).setCellValue("配置单位");
            row.createCell(14).setCellValue("车型");

            for (int i = 0; i< list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);

                int finalId = list.get(i).getCarType();
                List<Dictionaryvalue> dicts = dictionaryvalueList.stream().filter(t -> finalId == t.getId()).collect(Collectors.toList());
                if (!dicts.isEmpty()) {
                    row.createCell(0).setCellValue(dicts.get(0).getContent());
                }
                row.createCell(1).setCellValue(list.get(i).getLicencePlate());
                row.createCell(2).setCellValue(list.get(i).getBrand());
                row.createCell(3).setCellValue(list.get(i).getModel());
                if (list.get(i).getLicenseCheckDate() != null) {
                    row.createCell(4).setCellValue(list.get(i).getLicenseCheckDate().format(fmt));
                }
                row.createCell(5).setCellValue(list.get(i).getCarDeptName());
                row.createCell(6).setCellValue(list.get(i).getGPSNum());
                if (list.get(i).getDriverName() != null) {
                    row.createCell(7).setCellValue(list.get(i).getDriverName());
                }
                row.createCell(8).setCellValue(list.get(i).getIsEnable() == 1? "是" : "否");
                if (list.get(i).getConfirmUserName() != null) {
                    row.createCell(9).setCellValue(list.get(i).getConfirmUserName());
                }
                if (list.get(i).getConfirmTime() != null) {
                    row.createCell(10).setCellValue(sdf.format(list.get(i).getConfirmTime()));
                }
                if (list.get(i).getCarTag() == 0) {
                    row.createCell(11).setCellValue("临租车辆");
                } else if (list.get(i).getCarTag() == 1) {
                    row.createCell(11).setCellValue("特殊车辆");
                } else if (list.get(i).getCarTag() == 2) {
                    row.createCell(11).setCellValue("产权车辆");
                }
                if (list.get(i).getGPSEnableDate() != null) {
                    row.createCell(12).setCellValue(sdf.format(list.get(i).getGPSEnableDate()));
                }
                row.createCell(13).setCellValue(list.get(i).getGroupname());
                if (list.get(i).getCarMold() != null) {
                    row.createCell(14).setCellValue(list.get(i).getCarMold());
                }
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("主报表".getBytes("gb2312"), "ISO8859-1") + ".pdf");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetMoldDict() {
        List<Dictionaryvalue> list = dictionaryvalueMapper.selectList(new LambdaQueryWrapper<Dictionaryvalue>()
                .eq(Dictionaryvalue::getTitleid, 970211)
                .likeRight(Dictionaryvalue::getParameter, "|")
        );
        return Result.ok(list);
    }

    @Override
    public Result<Object> GetCarMoveList(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore) {
        try {
            List<LzclTCarMore> list = lzclTCarMoreMapper.selectList(new LambdaQueryWrapper<LzclTCarMore>()
                    .eq(LzclTCarMore::getLicencePlate, carMore.getLicencePlate())
                    .orderByDesc(LzclTCarMore::getStartDate)
            );
            return Result.ok(list);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }



    @Override
    public Result<Object> GetCarMoveChildList(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore) {
        try {
            List<LzclTCarMoveChild> list = lzclTCarMoveChildService.list(new LambdaQueryWrapper<LzclTCarMoveChild>()
                    .eq(LzclTCarMoveChild::getLicencePlate, carMore.getLicencePlate())
                    .orderByDesc(LzclTCarMoveChild::getStartDate)
            );
            return Result.ok(list);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> AddCarMove(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore) {
        try {
            // 如果结束时间为空
            if (carMore.getEndDate() != null) {
                if (carMore.getEndDate().compareTo(carMore.getStartDate()) <=0) {
                    return Result.error("结束时间需比开始时间大！");
                }
            }
            LambdaQueryWrapper<LzclTCarMore> wapper = new LambdaQueryWrapper<LzclTCarMore>()
                    .eq(LzclTCarMore::getLicencePlate, carMore.getLicencePlate());

            if (carMore.getEndDate() == null) {
                // 如果结束时间为空
                wapper = wapper.and(s -> s
                        // 开始时间小于输入开始时间，结束时间小于输入开始时间
                        .and(t -> t
                                .le(LzclTCarMore::getStartDate, carMore.getStartDate())
                                .ge(LzclTCarMore::getEndDate, carMore.getStartDate())
                        )
                        .or(t -> t
                                .ge(LzclTCarMore::getStartDate, carMore.getStartDate())
                        )
                );
            } else {
                // 如果结束时间不为空
                wapper = wapper.and(s -> s
                        // 判断开始时间是否在时间段内
                        .and(t -> t
                                .le(LzclTCarMore::getStartDate, carMore.getStartDate())
                                .ge(LzclTCarMore::getEndDate, carMore.getStartDate())
                        )
                        // 判断结束时间是否在时间段内
                        .or(t -> t
                                .le(LzclTCarMore::getStartDate, carMore.getEndDate())
                                .ge(LzclTCarMore::getEndDate, carMore.getEndDate())
                        )
                        // 判断开始-结束时间内是否存在时间段内
                        .or(t -> t
                                .ge(LzclTCarMore::getStartDate, carMore.getStartDate())
                                .le(LzclTCarMore::getEndDate, carMore.getEndDate())
                        )
                        // 结束时间为空
                        .or(t -> t
                                .isNull(LzclTCarMore::getEndDate)
                                .and(v -> v
                                        .and(k -> k
                                                .ge(LzclTCarMore::getStartDate, carMore.getStartDate())
                                                .le(LzclTCarMore::getEndDate, carMore.getEndDate())
                                        )
                                        .or()
                                        .le(LzclTCarMore::getStartDate, carMore.getStartDate())
                                )
                        )
                );
            }
            List<LzclTCarMore> list = lzclTCarMoreMapper.selectList(wapper);
            if (!list.isEmpty()) {
                return Result.error("选择时间段与以往重合，请重新输入！");
            }
            lzclTCarMoreMapper.insert(carMore);
//            if (carMore.getStartDate().before(new Date())) {
//                if (carMore.getEndDate() != null && carMore.getEndDate().before(new Date())) {
//                    this.update(null, );
//                }
//            }
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> EditCarMove(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore) {
        try {
            LzclTCarMore lzclTCarMore =  lzclTCarMoreMapper.selectById(carMore.getId());
            if (lzclTCarMore != null) {
                if (lzclTCarMore.getEndDate() != null) {
                    if (lzclTCarMore.getEndDate().compareTo(new Date()) < 0) {
                        return Result.error("结束时间已经过了当日时间不可修改");
                    }
                }
            } else {
                return Result.error("数据为空不可修改！");
            }
            // 如果结束时间为空
            if (carMore.getEndDate() != null) {
                if (carMore.getEndDate().compareTo(carMore.getStartDate()) <=0) {
                    return Result.error("结束时间需比开始时间大！");
                }
            }
            LambdaQueryWrapper<LzclTCarMore> wapper = new LambdaQueryWrapper<LzclTCarMore>()
                    .ne(LzclTCarMore::getId, carMore.getId())
                    .eq(LzclTCarMore::getLicencePlate, carMore.getLicencePlate());
            if (carMore.getEndDate() == null) {
                // 如果结束时间为空
                wapper = wapper.and(s -> s
                        // 开始时间小于输入开始时间，结束时间小于输入开始时间
                        .and(t -> t
                                .le(LzclTCarMore::getStartDate, carMore.getStartDate())
                                .ge(LzclTCarMore::getEndDate, carMore.getStartDate())
                        )
                        .or()
                        .ge(LzclTCarMore::getStartDate, carMore.getStartDate())
                );
            } else {
                // 如果结束时间不为空
                wapper = wapper.and(s -> s
                        // 判断开始时间是否在时间段内
                        .and(t -> t
                                .le(LzclTCarMore::getStartDate, carMore.getStartDate())
                                .ge(LzclTCarMore::getEndDate, carMore.getStartDate())
                        )
                        // 判断结束时间是否在时间段内
                        .or(t -> t
                                .le(LzclTCarMore::getStartDate, carMore.getEndDate())
                                .ge(LzclTCarMore::getEndDate, carMore.getEndDate())
                        )
                        // 判断开始-结束时间内是否存在时间段内
                        .or(t -> t
                                .ge(LzclTCarMore::getStartDate, carMore.getStartDate())
                                .le(LzclTCarMore::getEndDate, carMore.getEndDate())
                        )
                        // 结束时间为空
                        .or(t -> t
                                .isNull(LzclTCarMore::getEndDate)
                                .and(v -> v
                                        .and(k -> k
                                                .ge(LzclTCarMore::getStartDate, carMore.getStartDate())
                                                .le(LzclTCarMore::getEndDate, carMore.getEndDate())
                                        )
                                        .or()
                                        .le(LzclTCarMore::getStartDate, carMore.getStartDate())
                                )
                        )
                );
            }
            List<LzclTCarMore> list = lzclTCarMoreMapper.selectList(wapper);
            if (!list.isEmpty()) {
                return Result.error("选择时间段与以往重合，请重新输入！");
            }
            lzclTCarMoreMapper.updateById(carMore);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> DeleteCarMoveById(Integer id) {
        try {
            LzclTCarMore carMore =  lzclTCarMoreMapper.selectById(id);
            if (carMore != null) {
                if (carMore.getStartDate().compareTo(new Date()) <= 0) {
                    return Result.error("该记录已被使用不能删除");
                }
            }
            lzclTCarMoreMapper.deleteById(id);
            return Result.ok();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}




