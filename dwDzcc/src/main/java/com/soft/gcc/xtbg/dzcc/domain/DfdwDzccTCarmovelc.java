package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 节假日或者平时9点以后车辆移动Lc
* @TableName DFDW_DZCC_T_CARMOVELC
*/
@TableName(value ="DFDW_DZCC_T_CARMOVELC")
@Data
public class DfdwDzccTCarmovelc extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 车Id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 日期
    */
    @TableField(value = "currDate")
    @JSONField(name = "currDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currDate;
    /**
    * 审批状态（0审批中，1同意，2不同意）
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")
    private Integer approveState;
    /**
    * 审批意见
    */
    @TableField(value = "approveNote")
    @JSONField(name = "approveNote")
    private String approveNote;
    /**
    * 审批时间
    */
    @TableField(value = "approveTime")
    @JSONField(name = "approveTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date approveTime;
    /**
    * 车队长审批Id
    */
    @TableField(value = "approveUserId")
    @JSONField(name = "approveUserId")
    private Integer approveUserId;

    @TableField(value = "approveUserName")
    @JSONField(name = "approveUserName")
    private String approveUserName;

    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @TableField(value = "driveId")
    @JSONField(name = "driveId")
    private Integer driveId;

    @TableField(value = "driveName")
    @JSONField(name = "driveName")
    private String driveName;

    @TableField(value = "groupId")
    @JSONField(name = "groupId")
    private Integer groupId;

    @TableField(value = "groupName")
    @JSONField(name = "groupName")
    private String groupName;

    @TableField(value = "topGroupId")
    @JSONField(name = "topGroupId")
    private Integer topGroupId;

    @TableField(value = "topGroupName")
    @JSONField(name = "topGroupName")
    private String topGroupName;

    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")
    private String  licensePlate;

    @TableField(value = "type")
    @JSONField(name = "type")
    private Integer type;

    @TableField(exist = false)
    @JSONField(name = "startMoveTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startMoveTime;

    @TableField(exist = false)
    @JSONField(name = "endMoveTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endMoveTime;

    @TableField(exist = false)
    @JSONField(name = "carMold")
    private String carMold;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
