package com.soft.gcc.xtbg.dzcc.util;

import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.deepoove.poi.policy.MiniTableRenderPolicy;
import com.deepoove.poi.util.TableTools;

import com.soft.gcc.xtbg.dzcc.entity.DetailData;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import java.util.List;
import java.util.Map;

public class DetailTablePolicyClyd extends DynamicTableRenderPolicy {
    // 填充数据所在行数
    int listsStartRow = 1;

    @Override
    public void render(XWPFTable table, Object data) {
        if (null == data) {
            return;
        }
        DetailData detailData = (DetailData) data;

        // 商品订单详情列表数据 循环渲染
        List<RowRenderData> lists = detailData.getPlists();
        // 二级分类分组统计商品个数数据
        List<Map<String, Object>> tlists = detailData.getTlists();

        if (null != lists) {
            table.removeRow(listsStartRow);
            // 循环插入行
            for (int i = lists.size() - 1; i >= 0; i--) {
                XWPFTableRow insertNewTableRow = table.insertNewTableRow(listsStartRow);
                for (int j = 0; j < 5; j++) {
                    insertNewTableRow.createCell();
                }
                // 渲染单行商品订单详情数据
                MiniTableRenderPolicy.Helper.renderRow(table, listsStartRow, lists.get(i));
            }
            //处理合并
            for (int i = 0; i < lists.size(); i++) {
                String type_name = lists.get(i).getCells().get(2).getCellText().getText();
                for (int j = 0; j < tlists.size(); j++) {
                    String typeName = String.valueOf(tlists.get(j).get("typeName"));
                    Integer listSize = Integer.parseInt(String.valueOf(tlists.get(j).get("listSize")));
                    if (type_name.equals(typeName)) {
                        // 合并第1列的第i+1行到第i+listSize行的单元格
//                        Integer start = listsStartRow + lists.size() - i - listSize;
//                        Integer end = listsStartRow + lists.size() - 1 - i;
                        Integer start = i +  1;
                        Integer end = i +  listSize;
                        if (start < end) {
                            TableTools.mergeCellsVertically(table, 2, start, end);
                            TableTools.mergeCellsVertically(table, 5, start, end);
                        }
                        //处理垂直居中
                        for (int y = 1; y < 6; y++) {
                            XWPFTableCell cell = table.getRow(i + 1).getCell(y);
                            //垂直居中
                            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                        }
                        tlists.remove(j);
                        break;
                    }
                }
            }
        }
    }
}
