package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccPerson;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;
import com.soft.gcc.xtbg.dzcc.service.IDzccVPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 司机配置
 */
@RequestMapping("/dzcc/sjqx")
@RestController
public class DzccSjqxController extends DzccBaseController{
    @Autowired
    IDzccVPersonService vPersonService;

    /**
     * 电子出车-PC-司机权限-司机查询
     * */
    @RequestMapping("/GetDriverList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01SJ01QX01,NDWCC01SJ01QX02')")
    public Result<Object> GetDriverList(@RequestBody DzccVPerson vPerson) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.vPersonService.GetDriverList(dzccPersonEntity, vPerson);
    }

    /**
     * 电子出车-PC-司机权限-编辑司机手机号
     * */
    @RequestMapping("/EditDriverPhone")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ01QX02')")
    public Result<Object> EditDriverPhone(@RequestBody DzccVPerson vPerson) {
        return this.vPersonService.EditDriverPhone(vPerson);
    }

    /**
     * 电子出车-PC-司机权限-新增司机
     * */
    @RequestMapping("/InsertDriver")
    @PreAuthorize("@ss.hasPermi('NDWCC01SJ01QX01')")
    public Result<Object> InsertDriver(@RequestBody DzccPerson Person) {
        return this.vPersonService.InsertDriver(Person);
    }
}
