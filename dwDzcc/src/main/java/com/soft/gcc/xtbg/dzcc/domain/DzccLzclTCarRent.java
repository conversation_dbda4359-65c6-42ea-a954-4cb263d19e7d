package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
* 零租车辆--车辆借用记录
* @TableName LZCL_T_Car_Rent
*/
@TableName(value ="LZCL_T_Car_Rent", autoResultMap = true)
@Data
public class DzccLzclTCarRent implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 车辆表关联Id
    */
    @TableField(value = "CarId")
    @JSONField(name = "CarId")

    private Integer CarId;
    /**
    * 驾驶员Id,驾驶员Id，为Null表示不需要驾驶员
    */
    @TableField(value = "DriverId")
    @JSONField(name = "DriverId")

    private Integer DriverId;
    /**
    * 使用部门
    */
    @TableField(value = "UseDeptId")
    @JSONField(name = "UseDeptId")

    private Integer UseDeptId;
    /**
    * 申请单关联ID
    */
    @TableField(value = "ApplyId")
    @JSONField(name = "ApplyId")

    private Integer ApplyId;
    /**
    * 合同关联Id
    */
    @TableField(value = "ContractId")
    @JSONField(name = "ContractId")

    private Integer ContractId;
    /**
    * 合同签订时间
    */
    @TableField(value = "ContractStartDate")
    @JSONField(name = "ContractStartDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ContractStartDate;
    /**
    * 合同到期时间
    */
    @TableField(value = "ContractEndDate")
    @JSONField(name = "ContractEndDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ContractEndDate;
    /**
    * 实际还车日期
    */
    @TableField(value = "RealReturnDate")
    @JSONField(name = "RealReturnDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date RealReturnDate;
    /**
    * 合同状态( 草稿、删除、审核、生效、作废)
    */
    @TableField(value = "ContractState")
    @JSONField(name = "ContractState")

    private String ContractState;
    /**
    * 创建日期
    */
    @TableField(value = "CreateDate")
    @JSONField(name = "CreateDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateDate;
    /**
    * 还车状态 0未申请还车 1申请还车中 2还车完成 3还车审批通过
    */
    @TableField(value = "CarReturnType")
    @JSONField(name = "CarReturnType")

    private Integer CarReturnType;
    /**
    * 使用单位
    */
    @TableField(value = "TopDeptId")
    @JSONField(name = "TopDeptId")

    private Integer TopDeptId;
    /**
    * 批次ID
    */
    @TableField(value = "BatchId")
    @JSONField(name = "BatchId")

    private String BatchId;
    /**
    * 合同编号
    */
    @TableField(value = "ContractBh")
    @JSONField(name = "ContractBh")

    private String ContractBh;
    /**
    * 合同金额
    */
    @TableField(value = "ContractAmount")
    @JSONField(name = "ContractAmount")

    private BigDecimal ContractAmount;
    /**
    * 甲方单位Id
    */
    @TableField(value = "ContractTopDepId")
    @JSONField(name = "ContractTopDepId")

    private Integer ContractTopDepId;
    /**
    * 甲方单位名称
    */
    @TableField(value = "ContractTopDepName")
    @JSONField(name = "ContractTopDepName")

    private String ContractTopDepName;
    /**
    * 合同绑定日期
    */
    @TableField(value = "BoundTime")
    @JSONField(name = "BoundTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date BoundTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
