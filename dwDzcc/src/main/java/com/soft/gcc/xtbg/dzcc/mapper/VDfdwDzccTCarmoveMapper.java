package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CARMOVE】的数据库操作Mapper
* @createDate 2022-11-11 15:26:25
* @Entity com.yykj.app.dzcc.domain.VDfdwDzccTCarmove
*/
public interface VDfdwDzccTCarmoveMapper extends BaseMapper<VDfdwDzccTCarmove> {

    List<VDfdwDzccTCarmove> getXcglListPage(
            @Param("person") DzccPersonEntity person,
            @Param("carmove") VDfdwDzccTCarmove carmove,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    Long getXcglListCount(
            @Param("person") DzccPersonEntity person,
            @Param("carmove") VDfdwDzccTCarmove carmove,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    List<VDfdwDzccTCarmove> getXcglList(
            @Param("person") DzccPersonEntity person,
            @Param("carmove") VDfdwDzccTCarmove carmove,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
}




