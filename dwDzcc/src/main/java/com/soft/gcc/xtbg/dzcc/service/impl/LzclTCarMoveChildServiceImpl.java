package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.LzclTCarMoveChild;
import com.soft.gcc.xtbg.dzcc.service.ILzclTCarMoveChildService;
import com.soft.gcc.xtbg.dzcc.mapper.LzclTCarMoveChildMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Move_Child(车辆情况补充-子表)】的数据库操作Service实现
* @createDate 2024-01-09 09:40:33
*/
@Service
public class LzclTCarMoveChildServiceImpl extends ServiceImpl<LzclTCarMoveChildMapper, LzclTCarMoveChild>
    implements ILzclTCarMoveChildService{

}




