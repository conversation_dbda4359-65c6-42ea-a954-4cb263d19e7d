package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTNotice;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTNotice;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_NOTICE(电子出车通知)】的数据库操作Service
* @createDate 2022-11-15 09:43:56
*/
public interface IDfdwDzccTNoticeService extends IService<DfdwDzccTNotice> {

    Result<Object> GetNoticeList(DzccPersonEntity person, VDfdwDzccTNotice notice);

    Result<Object> upload(DzccPersonEntity person, MultipartFile file, HttpServletRequest request, String type, Integer joinId);

    Result<Object> addSjtg(DzccPersonEntity person, DfdwDzccTNotice notice);

    Result<Object> editSjtg(DzccPersonEntity person, DfdwDzccTNotice notice);

    Result<Object> deleteSjtg(Integer noticeId);
}
