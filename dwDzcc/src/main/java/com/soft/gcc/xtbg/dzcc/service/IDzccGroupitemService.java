package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【GroupItem】的数据库操作Service
* @createDate 2022-10-17 16:39:57
*/
public interface IDzccGroupitemService extends IService<DzccGroupitem> {
    Result<Object> GetGroupList(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetGroupList(DzccPersonEntity person, Map<String, String> map, Boolean isInfoPerson);

    Result<Object> GetGroupList2(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetGroupList12(DzccPersonEntity person, Map<String, String> map);
}
