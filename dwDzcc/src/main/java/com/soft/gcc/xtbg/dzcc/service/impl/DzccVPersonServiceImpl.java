package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccPersonMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccRolePersonMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccVPersonMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【vPerson(vPerson)】的数据库操作Service实现
 * @createDate 2022-10-21 11:00:43
 */
@Service
public class DzccVPersonServiceImpl extends ServiceImpl<DzccVPersonMapper, DzccVPerson>
        implements IDzccVPersonService {
    @Autowired
    IDzccPersonService personService;
    @Autowired
    DzccPersonMapper personMapper;
    @Autowired
    IDzccRoleService roleService;
    @Autowired
    DzccRolePersonMapper rolePersonMapper;
    @Autowired
    DzccGroupitemMapper groupitemMapper;
    @Autowired
    IDzccVGroupItemService vGroupItemService;
    @Autowired
    IDzccRolePersonService rolePersonService;
    @Autowired
    IDfdwDzccTCdzglService cdzglService;
    @Autowired
    IDfdwDzccTCarpersonService carpersonService;
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;

    @Override
    public Result<Object> GetDriverList(DzccPersonEntity dzccPersonEntity, DzccVPerson vPerson) {
        try {
            IPage<DzccVPerson> list = new Page<>();
            if (!"".equals(vPerson.getRealName())) {
                vPerson.setRealName("%" + vPerson.getRealName() + "%");
            }
            if (dzccPersonEntity.getDzccQx() == 0 || dzccPersonEntity.getDzccQx() == 3) {
                return Result.ok(list);
            }
            List<DzccVPerson> vPersonList = baseMapper.GetDriverList(dzccPersonEntity.getDzccQx(), dzccPersonEntity.getId(), dzccPersonEntity.getTopGroupId(), vPerson.getTopGroupId(), vPerson.getRealName());
            list.setCurrent(vPerson.getPageNum());
            list.setSize(vPerson.getPageSize());
            list.setTotal(vPersonList.size());
            if (vPersonList.size() > 0) {
                int fromIndex = (vPerson.getPageNum() - 1) * vPerson.getPageSize();
                int toIndex = vPerson.getPageNum() * vPerson.getPageSize();
                if (toIndex > vPersonList.size()) {
                    toIndex = vPersonList.size();
                }
                vPersonList = vPersonList.subList(fromIndex, toIndex);
            }
            list.setRecords(vPersonList);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> EditDriverPhone(DzccVPerson vPerson) {
        try {
            List<DzccPerson> telePersonList = personService.list(new LambdaQueryWrapper<DzccPerson>()
                    .ne(DzccPerson::getId, vPerson.getId())
                    .eq(DzccPerson::getTelephone, vPerson.getTelephone())
            );
            if (!telePersonList.isEmpty()) {
                return Result.error("该手机号已被注册！");
            }
            UpdateWrapper<DzccPerson> wrapper = Wrappers.update();
            wrapper.lambda()
                    .set(DzccPerson::getTelephone, vPerson.getTelephone())
                    .set(DzccPerson::getSphone, vPerson.getSphone())
                    .eq(DzccPerson::getId, vPerson.getId());
            personService.update(null, wrapper);
            return Result.ok();
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> InsertDriver(DzccPerson person) {
        try {
            List<DzccPerson> list = personService.list(new LambdaQueryWrapper<DzccPerson>().eq(DzccPerson::getCertificateID, person.getCertificateID()));
            if (list.size() > 0) {
                return Result.error("该身份证已被注册！");
            }
            List<DzccPerson> telePersonList = personService.list(new LambdaQueryWrapper<DzccPerson>().eq(DzccPerson::getTelephone, person.getTelephone()));
            if (!telePersonList.isEmpty()) {
                return Result.error("该手机号已被注册！");
            }
            person.setLoginName(person.getCertificateID());
            person.setPassword("Tlx7PTkm8G7oFs5dbAwJ+g==");
            person.setRoleId(0);
            person.setType(1);
            person.setZJ_CS(0);
            person.setPhName(0);
            person.setP_XH(999);
            personService.save(person);
            List<DzccRole> roles = roleService.list(new LambdaQueryWrapper<DzccRole>()
                    .eq(DzccRole::getRoleName, "协同办公-电子出车-司机")
                    .or()
                    .eq(DzccRole::getRoleName, "外协")
            );
            List<DzccRolePerson> rolePersonList = new ArrayList<>();
            for (DzccRole role: roles) {
                DzccRolePerson rolePerson = new DzccRolePerson();
                rolePerson.setRoleId(Math.toIntExact(role.getId()));
                rolePerson.setPersonId(person.getId());
                rolePersonList.add(rolePerson);
            }
            rolePersonMapper.saveBatch1(rolePersonList);
            return Result.ok();
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetCDZList(IPage<DzccVPerson> page, int personTypeId, String name, int manageGroupId, int deptId) {
        try {
            page = baseMapper.GetCDZListPage(page, personTypeId, name, manageGroupId, deptId);
            return Result.ok(page);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetPersonList(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        IPage<DzccVPerson> page = new Page<>();
        Integer GroupId = ParseUtil.tryParseInt(map.get("GroupId"));
        String RealName = ParseUtil.tryParseString(map.get("RealName"));
        Integer pageNum = ParseUtil.tryParseInt(map.get("pageNum"));
        Integer pageSize = ParseUtil.tryParseInt(map.get("pageSize"), 1);
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        try {
            List<DzccGroupitem> groupitems = groupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
            List<Integer> ids = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            IPage<DzccVPerson> list = baseMapper.getList(page, GroupId, RealName, ids);
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetGroupList(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        int isShowAll = ParseUtil.tryParseInt(map.get("isShowAll"));
        int parentId = ParseUtil.tryParseInt(map.get("parentId"));
        List<DzccVGroupItem> list = new ArrayList<>();
        try {
            if (isShowAll != 0) {
                DzccVGroupItem group = new DzccVGroupItem();
                group.setId(-1);
                group.setGroupname("全部");
                list.add(group);
            }
            if (dzccPersonEntity.getDzccQx() == 1 || dzccPersonEntity.getDzccQx() == 2) {
                List<DzccGroupitem> groupitems1 = groupitemMapper.GetGroupListByRole(dzccPersonEntity, parentId, false);
                List<Integer> ids = groupitems1.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
                if (ids.size() == 0) {
                    return Result.error("未配置管理，请联系总管理进行部门配置！");
                }
                List<DzccVGroupItem> vGroupItems = vGroupItemService.list(new LambdaQueryWrapper<DzccVGroupItem>().in(DzccVGroupItem::getId, ids).orderByAsc(DzccVGroupItem::getXH));
                list.addAll(vGroupItems);
            }
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetPersonRoleById(Map<String, String> map) {
        try {
            Integer id = ParseUtil.tryParseInt(map.get("id"));
            List<DzccRole> roles = personMapper.GetPersonRoleById(id);
            return Result.ok(roles);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetRoles() {
        try {
            List<DzccDictionaryvalue> dicts = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .apply("TitleID IN (SELECT ID FROM DictionaryDefine WHERE Title = '电子出车PC人员权限角色黑名单')")
            );
            List<String> stringList = dicts.stream().map(DzccDictionaryvalue::getParameter).collect(Collectors.toList());
            List<DzccRole> roles = roleService.list(new LambdaQueryWrapper<DzccRole>()
                    .like(DzccRole::getRoleName, "协同办公-电子出车")
                    .notIn(DzccRole::getRoleName, stringList)
                    .orderByAsc(DzccRole::getRoleName)
            );
            return Result.ok(roles);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> EditRoles(Map<String, String> map) {
        try {
            Integer personId = ParseUtil.tryParseInt(map.get("personId"));
            String ids = ParseUtil.tryParseString(map.get("ids"));
            List<DzccDictionaryvalue> dicts = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .apply("TitleID IN (SELECT ID FROM DictionaryDefine WHERE Title = '电子出车PC人员权限角色黑名单')")
            );
            List<String> stringList = dicts.stream().map(DzccDictionaryvalue::getParameter).collect(Collectors.toList());
            List<DzccRole> dzccRoleList = roleService.list(new LambdaQueryWrapper<DzccRole>()
                    .like(DzccRole::getRoleName, "协同办公-电子出车")
                    .notIn(DzccRole::getRoleName, stringList)
            );
            List<Long> dzccRoleLists = dzccRoleList.stream().map(DzccRole::getId).collect(Collectors.toList());
            rolePersonService.remove(new LambdaQueryWrapper<DzccRolePerson>().eq(DzccRolePerson::getPersonId, personId).in(DzccRolePerson::getRoleId, dzccRoleLists));
            if (!"".equals(ids)) {
                List<DzccRole> dzccRole = roleService.list(new LambdaQueryWrapper<DzccRole>()
                        .eq(DzccRole::getRoleName, "协同办公-电子出车-车辆管理人员")
                        .or()
                        .eq(DzccRole::getRoleName, "协同办公-电子出车-车辆调度人员")
                        .or()
                        .eq(DzccRole::getRoleName, "协同办公-电子出车-车辆运监人员")
                );
                DzccRole ddryRole = dzccRole.stream().filter(t -> "协同办公-电子出车-车辆调度人员".equals(t.getRoleName())).findFirst().orElse(null);
                DzccRole yjryRole = dzccRole.stream().filter(t -> "协同办公-电子出车-车辆运监人员".equals(t.getRoleName())).findFirst().orElse(null);
                boolean isCDZ = false;
                boolean isZGL = true;
                List<Long> roles = dzccRole.stream().map(DzccRole::getId).collect(Collectors.toList());

                ids = ParseUtil.trim(ids, ',');
                String[] idsList = ids.split(",");
                List<DzccRolePerson> roleList = new ArrayList<>();
                for (String s : idsList) {
                    DzccRolePerson role = new DzccRolePerson();
                    int roleId = ParseUtil.tryParseInt(s);
                    role.setRoleId(roleId);
                    role.setPersonId(personId);
                    roleList.add(role);
                    // 判断是否是车队长
                    if (roleId == ddryRole.getId() || roleId == yjryRole.getId()) {
                        isCDZ = true;
                    }
                    // 判断是否需要部门管理
                    if (roles.contains(roleId)) {
                        isZGL = false;
                    }
                }
                rolePersonMapper.saveBatch1(roleList);
                // 不是车队长删除车队长配置表信息
                if (!isCDZ) {
                    carpersonService.remove(new LambdaQueryWrapper<DfdwDzccTCarperson>().eq(DfdwDzccTCarperson::getPersonId, personId));
                }
                // 不需要部门管理删除部门配置表
                if (!isZGL) {
                    cdzglService.remove(new LambdaQueryWrapper<DfdwDzccTCdzgl>().eq(DfdwDzccTCdzgl::getCdzId, personId));
                }
            }
            return Result.ok("编辑成功");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




