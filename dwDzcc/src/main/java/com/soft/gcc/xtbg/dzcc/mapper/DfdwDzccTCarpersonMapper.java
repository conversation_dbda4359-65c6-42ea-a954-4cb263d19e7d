package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarperson;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CARPERSON】的数据库操作Mapper
* @createDate 2023-01-13 10:41:12
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTCarperson
*/
public interface DfdwDzccTCarpersonMapper extends BaseMapper<DfdwDzccTCarperson> {

    List<DzccCar> GetCarList(
            @Param("groupId") Integer groupId,
            @Param("qx") Integer qx,
            @Param("id") Integer id,
            @Param("personId") Integer personId);
}




