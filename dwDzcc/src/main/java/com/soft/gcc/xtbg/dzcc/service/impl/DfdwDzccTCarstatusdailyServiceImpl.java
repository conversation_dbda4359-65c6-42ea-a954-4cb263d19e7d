package com.soft.gcc.xtbg.dzcc.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarstatusdailyMapper;
import com.soft.gcc.xtbg.dzcc.util.GPSCoordinateTranslation;
import com.soft.gcc.xtbg.dzcc.util.HttpClientUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.soft.gcc.xtbg.dzcc.domain.DzccDictionaryvalue;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CarStatusDaily(每日出车情况)】的数据库操作Service实现
 * @createDate 2023-08-08 15:17:41
 */
@Service
public class DfdwDzccTCarstatusdailyServiceImpl extends ServiceImpl<DfdwDzccTCarstatusdailyMapper, DfdwDzccTCarstatusdaily>
        implements IDfdwDzccTCarstatusdailyService {
    @Autowired
    private IDzccDictionaryvalueService dictionaryValueService;
    @Autowired
    IDfdwDzccTCarmoveService carmoveService;
    @Autowired
    DzccCarService dzccCarService;
    @Autowired
    IDzccLzclCarMoveChildService carMoveChildService;
    @Autowired
    RedisTemplate<String, String> redisTemplate;
    @Autowired
    IDfdwDzccTClglService clglService;
    @Autowired
    IDfdwDzccTClbgService clbgService;
    @Autowired
    GfxmTCityService gfxmTCityService;
    @Autowired
    private IDfdwDzccTCclcService cclcService;
    @Autowired
    private IDfdwDzccTAddressService addressService;
    private DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public Result addCarStatusDaily(LocalDate date) {
        try {
            this.remove(new LambdaQueryWrapper<DfdwDzccTCarstatusdaily>().eq(DfdwDzccTCarstatusdaily::getCurDate, date));
            List<DfdwDzccTCarstatusdaily> carList = baseMapper.getList(date);
            //高德AK
            List<DzccDictionaryvalue> dictionaryValueList = dictionaryValueService.list(new QueryWrapper<DzccDictionaryvalue>().eq("TitleID", 970221).eq("Content", "高德地图AK"));
            String AK = "";
            if (dictionaryValueList.size() > 0) {
                AK = dictionaryValueList.get(0).getParameter();
            }
            for (DfdwDzccTCarstatusdaily d : carList) {
                d.setStatus(getQyInfo(df.format(date), AK, d.getPersonId(), d.getCarId()));
                d.setCurDate(date);
            }
            List<List<DfdwDzccTCarstatusdaily>> partition = ListUtils.partition(carList, 100);
            for (List<DfdwDzccTCarstatusdaily> users : partition) {
                baseMapper.insertBatch(users);
            }
            return Result.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    /**
     * 计算区域
     * 2023.08.03新需求：以车辆停放地点为准，超出区域的为跨区域，出宁波大市就是出大市，没超过区域的就是本区域，海曙江北之间就是本区域
     *
     * @param currDate
     * @return
     */
    @Override
    public String getQyInfo(String currDate, String AK, Integer driveId, Integer carId) {
        DfdwDzccTCarmove fbb = new DfdwDzccTCarmove();
        fbb.setCarId(carId);
        fbb.setDriveId(driveId);
        try {
            fbb.setCurDate(sdf.parse(currDate));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        String quInfo = "本区域";

        //获取实际去过的区域
        List<DfdwDzccTCarmove> moveList = carmoveService.list(new LambdaQueryWrapper<DfdwDzccTCarmove>()
                .eq(DfdwDzccTCarmove::getDriveId, driveId).eq(DfdwDzccTCarmove::getCarId, carId).eq(DfdwDzccTCarmove::getCurDate, currDate));
        List<Integer> carList = moveList.stream().map(DfdwDzccTCarmove::getCarId).collect(Collectors.toList());
        if (carList.size() > 0) {
            carId = moveList.get(0).getCarId();
        } else {
            //当日没有车辆移动，则是本区域
            quInfo = "本区域";
            fbb.setDayInfo(quInfo);
            return quInfo;
        }

        //判断是否出现不在宁波的移动范围
        List<Integer> cityList = moveList.stream().filter(p -> p.getCity() != 123).map(p -> p.getCity()).distinct().collect(Collectors.toList()); //市
        if (cityList.size() > 0) {
            quInfo = "出大市";
            fbb.setDayInfo(quInfo);
            return quInfo;
        }

        //计算当天车辆所在部门
        DzccCar car = dzccCarService.getById(carId);
        List<DzccLzclCarMoveChild> carMoveChildList = carMoveChildService.list(new LambdaQueryWrapper<DzccLzclCarMoveChild>()
                .and(v -> v
                        .and(t -> t
                                .le(DzccLzclCarMoveChild::getStartDate, currDate)
                                .isNull(DzccLzclCarMoveChild::getEndDate)
                        )
                        .or(t -> t
                                .le(DzccLzclCarMoveChild::getStartDate, currDate)
                                .ge(DzccLzclCarMoveChild::getEndDate, currDate)
                                .isNotNull(DzccLzclCarMoveChild::getEndDate))
                )
                .eq(DzccLzclCarMoveChild::getLicencePlate, car.getLicencePlate())
        );
        if (carMoveChildList.size() > 0) {
            Integer groupId = carMoveChildList.get(0).getGroupId();


            //从redis中获取部门所在区域（停车点，办公点），防止多次读取
            String key = "DZCC_CAR_GROUP_AREA::" + carId + "_" + groupId;
            String carGroupArea = redisTemplate.opsForValue().get(key);
            //不存在重新取值赋值
            if (carGroupArea == null) {
                //查询车辆停车地点和办公地点gps
                List<DfdwDzccTClgl> clglList = clglService.list(new LambdaQueryWrapper<DfdwDzccTClgl>().eq(DfdwDzccTClgl::getCarId, carId).eq(DfdwDzccTClgl::getGroupId, groupId));
                if (clglList.size() > 0) {
                    //停车点
                    Integer tcdQyId = 0;  //停车点区域Id
                    Integer bgdQyId = 0;  //办公点区域Id
                    if (clglList.get(0).getLongitude() != null && clglList.get(0).getLatitude() != null) {
                        //根据经纬度反解析 省市区
                        tcdQyId = getAreaId(AK, clglList.get(0).getLatitude(), clglList.get(0).getLongitude());
                    }
                    //办公点
                    DfdwDzccTClbg clbg = clbgService.getById(clglList.get(0).getUnitId());
                    if (clbg != null) {
                        //根据经纬度反解析 省市区
                        bgdQyId = getAreaId(AK, clbg.getLatitude(), clbg.getLongitude());
                    }
                    carGroupArea = tcdQyId + "," + bgdQyId;
                    redisTemplate.opsForValue().set(key, carGroupArea, 30, TimeUnit.MINUTES);
                }
            }

            //白名单区域
            String carGroupAreaSplit[] = carGroupArea.split(",");
            List<Integer> groupAreas = new ArrayList<>();
            groupAreas.add(Integer.valueOf(carGroupAreaSplit[0]));
            groupAreas.add(Integer.valueOf(carGroupAreaSplit[1]));

            //海曙江北之间就是本区域,所以设置白名单，有海曙添加江北
            if (carGroupArea.contains("1196")) {
                groupAreas.add(Integer.valueOf(1197));
            } else if (carGroupArea.contains("1197")) {
                groupAreas.add(Integer.valueOf(1196));
            }


            //判断实际到达的区域除了白名单所在点是否还去了其他地方
            List<Integer> areaList = moveList.stream().map(DfdwDzccTCarmove::getArea).collect(Collectors.toList());
            List<Integer> integerList = areaList.stream().filter(p -> !groupAreas.contains(p)).collect(Collectors.toList());
            if (integerList.size() > 0) {
                //跨区域，除了白名单中的地方，还去了其他地点则是跨区域
                quInfo = "跨区域";
            } else {
                //没超过区域的就是本区域
                quInfo = "本区域";
            }
        }
        fbb.setDayInfo(quInfo);
        return quInfo;
    }


    /**
     * 计算区域
     * 2023.08.28新需求：根据出车单的实际目的地来计算，目的地到达的区域判断车辆移动表 哪几天去过这些区域判断出市情况
     * 市区逻辑：以车辆停放地点为准，超出区域的为跨区域，出宁波大市就是出大市，没超过区域的就是本区域，海曙江北之间就是本区域
     *
     * @param lcId 流程id
     * @return
     */
    @Override
    public void getQyInfoNew(Integer lcId) {
        try {
            //高德AK
            List<DzccDictionaryvalue> dictionaryValueList = dictionaryValueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .eq(DzccDictionaryvalue::getTitleID, 970221).eq(DzccDictionaryvalue::getContent, "高德地图AK"));
            String AK = "";
            if (dictionaryValueList.size() > 0) {
                AK = dictionaryValueList.get(0).getParameter();
            }


            DfdwDzccTCclc lc = cclcService.getById(lcId);
            String openTime = sdf.format(lc.getCcOpenTime());
            String endTime = sdf.format(lc.getExecuteTime());
            Integer carId = lc.getCarId();
            //我已到达
            //获取所有目的地且已审批通过
            List<DfdwDzccTAddress> addressList = getAllAddress(lcId).stream().filter(p -> p.getArriveState() == 1).collect(Collectors.toList());
            if (addressList.size() > 0) {

                //判断实际出车单中是否有出大市
                List<Integer> cityList = addressList.stream().filter(p -> p.getCity() != 123).map(p -> p.getCity()).distinct().collect(Collectors.toList()); //市
                if (cityList.size() > 0) {
                    //从车辆移动表中判断，哪天是出大市
                    List<DfdwDzccTCarmove> careMoveList = carmoveService.list(new LambdaQueryWrapper<DfdwDzccTCarmove>()
                            .eq(DfdwDzccTCarmove::getDriveId, lc.getDriveId()).eq(DfdwDzccTCarmove::getCarId, lc.getCarId())
                            .ge(DfdwDzccTCarmove::getCurDate, openTime).le(DfdwDzccTCarmove::getCurDate, endTime)
                            .in(DfdwDzccTCarmove::getCity, cityList)
                            .ne(DfdwDzccTCarmove::getCity, 123)
                    );
                    List<Date> dateList = careMoveList.stream().map(DfdwDzccTCarmove::getCurDate).collect(Collectors.toList());
                    for (Date date : dateList) {
                        this.saveOrUpdate(lc.getCarId(), lc.getDriveId(), lc.getApplyDeptId(), "出大市", sdf.format(date));
                    }
                }


                //跨区域、本区域逻辑处理
                List<Integer> nbAreaList = addressList.stream().filter(p -> p.getCity() == 123).map(p -> p.getArea()).distinct().collect(Collectors.toList()); //宁波市


                Calendar startCalendar = Calendar.getInstance();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                Date a = sdf.parse(openTime);
                startCalendar.setTime(a); // 设置起始日期

                Calendar endCalendar = Calendar.getInstance();
                Date b = sdf.parse(endTime);
                endCalendar.setTime(b); // 设置结束日期

                while (startCalendar.compareTo(endCalendar) <= 0) {
                    String currDate = sdf.format(startCalendar.getTime());

                    List<DzccLzclCarMoveChild> carMoveChildList = new ArrayList<>();
                    //计算当天车辆所在部门
                    if (carId.equals("286") || carId == 286) {
                        DzccLzclCarMoveChild carMoveChild = new DzccLzclCarMoveChild();
                        carMoveChild.setGroupId(471);

                        carMoveChildList.add(carMoveChild);
                    } else {
                        DzccCar car = dzccCarService.getById(carId);
                        carMoveChildList = carMoveChildService.list(new LambdaQueryWrapper<DzccLzclCarMoveChild>()
                                .and(v -> v
                                        .and(t -> t
                                                .le(DzccLzclCarMoveChild::getStartDate, currDate)
                                                .isNull(DzccLzclCarMoveChild::getEndDate)
                                        )
                                        .or(t -> t
                                                .le(DzccLzclCarMoveChild::getStartDate, currDate)
                                                .ge(DzccLzclCarMoveChild::getEndDate, currDate)
                                                .isNotNull(DzccLzclCarMoveChild::getEndDate))
                                )
                                .eq(DzccLzclCarMoveChild::getLicencePlate, car.getLicencePlate())
                        );
                    }


                    if (carMoveChildList.size() > 0) {
                        Integer groupId = carMoveChildList.get(0).getGroupId();


                        //从redis中获取部门所在区域（停车点，办公点），防止多次读取
                        String key = "DZCC_CAR_GROUP_AREA::" + carId + "_" + groupId;
                        String carGroupArea = redisTemplate.opsForValue().get(key);
                        //不存在重新取值赋值
                        if (carGroupArea == null) {
                            //查询车辆停车地点和办公地点gps
                            List<DfdwDzccTClgl> clglList = clglService.list(new LambdaQueryWrapper<DfdwDzccTClgl>().eq(DfdwDzccTClgl::getCarId, carId).eq(DfdwDzccTClgl::getGroupId, groupId));
                            if (clglList.size() > 0) {
                                //停车点
                                Integer tcdQyId = 0;  //停车点区域Id
                                Integer bgdQyId = 0;  //办公点区域Id
                                if (clglList.get(0).getLongitude() != null && clglList.get(0).getLatitude() != null) {
                                    //根据经纬度反解析 省市区
                                    tcdQyId = getAreaId(AK, clglList.get(0).getLatitude(), clglList.get(0).getLongitude());
                                }
                                //办公点
                                DfdwDzccTClbg clbg = clbgService.getById(clglList.get(0).getUnitId());
                                if (clbg != null) {
                                    //根据经纬度反解析 省市区
                                    bgdQyId = getAreaId(AK, clbg.getLatitude(), clbg.getLongitude());
                                }
                                carGroupArea = tcdQyId + "," + bgdQyId;
                                redisTemplate.opsForValue().set(key, carGroupArea, 30, TimeUnit.MINUTES);
                            }
                        }

                        //白名单区域
                        String carGroupAreaSplit[] = carGroupArea.split(",");
                        List<Integer> groupAreas = new ArrayList<>();
                        groupAreas.add(Integer.valueOf(carGroupAreaSplit[0]));
                        groupAreas.add(Integer.valueOf(carGroupAreaSplit[1]));

                        //海曙江北之间就是本区域,所以设置白名单，有海曙添加江北
                        if (carGroupArea.contains("1196")) {
                            groupAreas.add(Integer.valueOf(1197));
                        } else if (carGroupArea.contains("1197")) {
                            groupAreas.add(Integer.valueOf(1196));
                        }


                        //判断实际到达的区域除了白名单所在点是否还去了其他地方
                        List<Integer> integerList = nbAreaList.stream().filter(p -> !groupAreas.contains(p)).collect(Collectors.toList());
                        if (integerList.size() > 0) {
                            //跨区域，除了白名单中的地方，还去了其他地点则是跨区域
                            //判断当天的值是否已经不是本区域了，是本区域则修改
                            List<DfdwDzccTCarstatusdaily> carstatusdailies = this.list(new LambdaQueryWrapper<DfdwDzccTCarstatusdaily>().eq(DfdwDzccTCarstatusdaily::getCarId, carId)
                                    .eq(DfdwDzccTCarstatusdaily::getPersonId, lc.getDriveId()).eq(DfdwDzccTCarstatusdaily::getCurDate, currDate));
                            if (carstatusdailies.size() > 0 && "本区域".equals(carstatusdailies.get(0).getStatus())) {
                                List<DfdwDzccTCarmove> careMoveList = carmoveService.list(new LambdaQueryWrapper<DfdwDzccTCarmove>()
                                        .eq(DfdwDzccTCarmove::getDriveId, lc.getDriveId()).eq(DfdwDzccTCarmove::getCarId, lc.getCarId())
                                        .eq(DfdwDzccTCarmove::getCurDate, currDate).in(DfdwDzccTCarmove::getArea, integerList)
                                );
                                if (careMoveList.size() > 0) {
                                    //如果车辆移动表中包含跨区域的这个目的地，则当天属于跨区域
                                    this.saveOrUpdate(lc.getCarId(), lc.getDriveId(), lc.getApplyDeptId(), "跨区域", currDate);
                                }
                            }
                        } else {
                            //没超过区域的就是本区域,则不用修改
                        }
                    }


                    startCalendar.add(Calendar.DAY_OF_MONTH, 1); // 将日期加1
                }

            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public void saveOrUpdate(Integer carId, Integer driveId, Integer groupId, String status, String time) {
        List<DfdwDzccTCarstatusdaily> carstatusdailies = baseMapper.selectList(new LambdaQueryWrapper<DfdwDzccTCarstatusdaily>()
                .eq(DfdwDzccTCarstatusdaily::getCarId, carId)
                .eq(DfdwDzccTCarstatusdaily::getPersonId, driveId)
                .eq(DfdwDzccTCarstatusdaily::getCurDate, time)
        );
        if (carstatusdailies.size() > 0) {
            for (DfdwDzccTCarstatusdaily carStatus : carstatusdailies) {
                carStatus.setStatus(status);
                baseMapper.updateById(carStatus);
            }
        } else {
            DfdwDzccTCarstatusdaily carstatusdaily = new DfdwDzccTCarstatusdaily();
            carstatusdaily.setCarId(carId);
            carstatusdaily.setPersonId(driveId);
            carstatusdaily.setGroupId(groupId);
            carstatusdaily.setStatus(status);
            carstatusdaily.setCreateTime(LocalDateTime.now());
            LocalDate date = LocalDate.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            carstatusdaily.setCurDate(date);
            baseMapper.insert(carstatusdaily);
        }
    }


    public List<DfdwDzccTAddress> getAllAddress(Integer id) {
        //获取子记录 已审批通过的
        List<DfdwDzccTCclc> cclcList = cclcService.list(new LambdaQueryWrapper<DfdwDzccTCclc>().eq(DfdwDzccTCclc::getMainId, id).eq(DfdwDzccTCclc::getApproveState, 2));
        List<Integer> ids = cclcList.stream().map(p -> p.getId()).collect(Collectors.toList());
        ids.add(id);
        //获取目的地
        List<DfdwDzccTAddress> addressList = addressService.list(new LambdaQueryWrapper<DfdwDzccTAddress>()
                .in(DfdwDzccTAddress::getLcId, ids).orderByAsc(DfdwDzccTAddress::getLcId));
        return addressList;
    }


    /**
     * 反解析区域
     *
     * @param AK
     * @param Latitude
     * @param Longitude
     * @return
     */
    @Override
    public Integer getAreaId(String AK, BigDecimal Latitude, BigDecimal Longitude) {
        try {
            //根据经纬度反解析 省市区
            HashMap<String, String> amapParam = new HashMap<String, String>();
            double[] latLon = GPSCoordinateTranslation.gps84_To_Gcj02(Double.parseDouble(Latitude.toString()), Double.parseDouble(Longitude.toString()));
            amapParam.put("location", latLon[1] + "," + latLon[0]);
            amapParam.put("key", AK);
            String result = HttpClientUtils.doGet("https://restapi.amap.com/v3/geocode/regeo", amapParam);
            Map<String, Object> addressComponent = ((Map) ((Map) JSON.parseObject(result).get("regeocode")).get("addressComponent"));

            List<GfxmTCity> GfxmCityList = gfxmTCityService.list(new QueryWrapper<GfxmTCity>()
                    .eq("CityName", addressComponent.get("province"))
                    .or().eq("CityName", addressComponent.get("city"))
                    .or().eq("CityName", addressComponent.get("district")));
            GfxmTCity provinceGfxmCity = GfxmCityList.stream().filter(s -> s.getType() == 1).findFirst().get();
            GfxmTCity cityGfxmCity = GfxmCityList.stream().filter(s -> (s.getType() == 2 && provinceGfxmCity.getId().equals(s.getPid()))).findFirst().get();
            GfxmTCity districtGfxmCity = GfxmCityList.stream().filter(s -> (s.getType() == 3 && cityGfxmCity.getId().equals(s.getPid()))).findFirst().get();
            return districtGfxmCity.getId();
        } catch (Exception ex) {
            return 0;
        }
    }
}




