package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd;
import com.soft.gcc.xtbg.dzcc.entity.CLGLDetailForMonth;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCcdMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTZbbMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCcdService;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import com.soft.gcc.xtbg.base.controller.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【V_DFDW_DZCC_T_CCD】的数据库操作Service实现
 * @createDate 2022-10-25 16:50:19
 */
@Service
public class VDfdwDzccTCcdServiceImpl extends ServiceImpl<VDfdwDzccTCcdMapper, VDfdwDzccTCcd>
        implements IVDfdwDzccTCcdService {
    @Autowired
    VDfdwDzccTCcdMapper ccdMapper;
    @Autowired
    VDfdwDzccTZbbMapper zbbMapper;
    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    @Override
    public Result<Object> GetCcdsBBList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd ccd) {
        try {
            IPage<VDfdwDzccTCcd> list = new Page<>();
            list.setCurrent(ccd.getPageNum());
            list.setSize(ccd.getPageSize());

            LocalDateTime startDate = ccd.getCcOpenTime();
            LocalDateTime endDate = ccd.getExecuteTime().plusDays(1);
            List<VDfdwDzccTCcd> ccdList = ccdMapper.getCcdBBList(
                    dzccPersonEntity,
                    startDate,
                    endDate,
                    ccd
            );

            int index = 0;
            for (int i = 0; i < ccdList.size(); i++) {
                // index == 0表示数据为第一行
                if (i == 0) {
                    ccdList.get(i).setCount(1);
                } else {
                    if (ccd.getCarOrPerson() == 0) {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (ccdList.get(i).getCarId().equals(ccdList.get(index).getCarId())) {
                            ccdList.get(index).setCount(ccdList.get(index).getCount() + 1);
                            ccdList.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            ccdList.get(i).setCount(1);
                        }
                    } else {
                        // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并
                        if (ccdList.get(i).getDriveId().equals(ccdList.get(index).getDriveId())) {
                            ccdList.get(index).setCount(ccdList.get(index).getCount() + 1);
                            ccdList.get(i).setCount(0);
                        } else {
                            index = i;
                            // 如果当前行和上一行其值不相等
                            ccdList.get(i).setCount(1);
                        }
                    }
                }
            }
            for (int i = 0; i < ccdList.size(); i++) {
                ccdList.get(i).setValidDaySum(0);
                for (int j = i; j < i + ccdList.get(i).getCount(); j++) {
                    ccdList.get(i).setValidDaySum(ccdList.get(i).getValidDaySum() + ccdList.get(j).getValidDays());
                }
            }
            list.setTotal(ccdList.size());
            // 分页
            List<VDfdwDzccTCcd> nowCcbList = new ArrayList<>();
            if (ccdList.size() > 0) {
                int fromIndex = (ccd.getPageNum() - 1) * ccd.getPageSize();
                int toIndex = ccd.getPageNum() * ccd.getPageSize();
                if (toIndex > ccdList.size()) {
                    toIndex = ccdList.size();
                }
                if (ccdList.get(toIndex - 1).getCount() == 0) {
                    for (int i = toIndex; i < ccdList.size(); i++) {
                        if (ccdList.get(i).getCount() == 0) {
                            toIndex++;
                        } else {
                            break;
                        }
                    }
                }
                if (ccdList.get(fromIndex).getCount() == 0) {
                    for (int i = fromIndex; i < ccdList.size(); i++) {
                        if (ccdList.get(i).getCount() == 0) {
                            fromIndex++;
                        } else {
                            break;
                        }
                    }
                }
                nowCcbList = ccdList.subList(fromIndex, toIndex);
            }
            list.setRecords(nowCcbList);

            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




