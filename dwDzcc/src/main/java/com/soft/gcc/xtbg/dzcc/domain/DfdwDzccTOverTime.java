package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* 加班费计算规则配置表
* @TableName DFDW_DZCC_T_OVERTIME_SET
*/
@TableName(value ="DFDW_DZCC_T_OVERTIME_SET", autoResultMap = true)
@Data
public class DfdwDzccTOverTime extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */
    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;


    /**
    * 部门id
    */
    @TableField(value = "deptId")
    @JSONField(name = "deptId")
    private Integer deptId;

    /**
     * 开始时间取值
     */
    @TableField(value = "startTimeType")
    @JSONField(name = "startTimeType")
    private Integer startTimeType;

    /**
     * 是否统计午休
     */
    @TableField(value = "IsComputeNoon")
    @JSONField(name = "isComputeNoon")
    private Integer isComputeNoon;

    /**
     * 出差补贴计算标准
     */
    @TableField(value = "travelAllowanceType")
    @JSONField(name = "travelAllowanceType")
    private Integer travelAllowanceType;

    /**
     * 加班小时计算标准
     */
    @TableField(value = "overtimeHoursAllType")
    @JSONField(name = "overtimeHoursAllType")
    private Integer overtimeHoursAllType;

    /**
     * 每月延时小时计算标准
     */
    @TableField(value = "overtimeHoursMonthtype")
    @JSONField(name = "overtimeHoursMonthType")
    private Integer overtimeHoursMonthType;

    /**
     * 双休日延时小时计算（1工作时间大于4小时  ，2不计算  ，3 延时小时大于4小时 ）
     */
    @TableField(value = "sxrDelayHourMonthType")
    @JSONField(name = "sxrDelayHourMonthType")
    private Integer sxrDelayHourMonthType;

    /**
     * 节假日延时小时计算（1工作时间大于4小时  ，2不计算  ，3 延时小时大于4小时 ）
     */
    @TableField(value = "jjrDelayHourMonthType")
    @JSONField(name = "jjrDelayHourMonthType")
    private Integer jjrDelayHourMonthType;

    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String groupName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
