package com.soft.gcc.xtbg.clwx.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.soft.gcc.xtbg.dzcc.domain.DzccBaseEntity;
import lombok.Data;

/**
 * 车辆维修（新）-维修申请主表
 * @TableName DFDW_CLWX_T_BXD
 */
@TableName(value ="DFDW_CLWX_T_BXD")
@Data
public class DfdwClwxTBxd extends DzccBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 维修编号
     */
    @TableField(value = "applyNo")
    private String applyNo;

    /**
     * 车牌Id
     */
    @TableField(value = "carId")
    private Integer carId;

    /**
     * 产权性质（字典）
     */
    @TableField(value = "nature")
    private String nature;

    /**
     * 送修厂家
     */
    @TableField(value = "repairFactoryId")
    private Integer repairFactoryId;

    /**
     * 报修日期
     */
    @TableField(value = "repairDate")
    private Date repairDate;

    /**
     * 报修项目
     */
    @TableField(value = "repairItem")
    private String repairItem;

    /**
     * 报修原因
     */
    @TableField(value = "repairReason")
    private String repairReason;

    /**
     * 预估报修金额
     */
    @TableField(value = "estimatedAmount")
    private BigDecimal estimatedAmount;

    /**
     * 申请人Id
     */
    @TableField(value = "applyUserId")
    private Integer applyUserId;

    /**
     * 申请人名称
     */
    @TableField(value = "applyUserName")
    private String applyUserName;

    /**
     * 申请人部门Id
     */
    @TableField(value = "applyDeptId")
    private Integer applyDeptId;

    /**
     * 申请人部门名称
     */
    @TableField(value = "applyDeptName")
    private String applyDeptName;

    /**
     * 申请人单位Id
     */
    @TableField(value = "applyTopDeptId")
    private Integer applyTopDeptId;

    /**
     * 申请人单位名称
     */
    @TableField(value = "applyTopDeptName")
    private String applyTopDeptName;

    /**
     * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(value = "approveState")
    private Integer approveState;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;


    /**
     * 预估材料费
     */
    @TableField("estimatedMaterialCosts")
    private BigDecimal estimatedMaterialCosts;
    /**
     * 实际维修金额
     */
    @TableField("actualRepairAmount")
    private BigDecimal actualRepairAmount;
    /**
     * 实际材料费
     */
    @TableField("actualMaterialCost")
    private BigDecimal actualMaterialCost;
    /**
     * 候补审批-- 状态 (0未提交，1审批中，2已审批，3已驳回，4需要审批标识，5不需要审批)
     */
    @TableField("appendApproveState")
    private Integer appendApproveState;

    @TableField("remarks")
    private String remarks;
    /**
     * 候补审批-- 审批人
     */
    @TableField("appendApproveId")
    private Integer appendApproveId;

    /**
     * 候补审批-- 审批人名称
     */
    @TableField("appendApproveName")
    private String appendApproveName;

    /**
     * 确认状态 0 未确认  1 已确认
     */
    @TableField("confirmed_status")
    private Integer confirmedStatus;

    /**
     * 附件ids
     */
    @TableField("annexIds")
    private String annexIds;

    /**
     * 创建时间
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDelete;

    @TableField(exist = false)
    private String licencePlate;
    @TableField(exist = false)
    private Integer carType;
    @TableField(exist = false)
    private String carMold;
    @TableField(exist = false)
    private String repairFactoryName;
    @TableField(exist = false)
    private Date[] repairDateArray;
    @TableField(exist = false)
    private Integer[] idList;
    @TableField(exist = false)
    private String downloadType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}