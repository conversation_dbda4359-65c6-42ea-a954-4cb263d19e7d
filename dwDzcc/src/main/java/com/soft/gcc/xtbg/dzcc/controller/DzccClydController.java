package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.microsoft.schemas.office.office.CTLock;
import com.microsoft.schemas.office.office.STConnectType;
import com.microsoft.schemas.vml.*;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmove;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTClydMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCarmoveService;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTClydService;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPicture;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @description 车辆移动
 */
@RequestMapping("/dzcc/clyd")
@RestController
public class DzccClydController extends DzccBaseController{
    @Autowired
    IVDfdwDzccTClydService clydService;
    @Autowired
    VDfdwDzccTClydMapper clydMapper;
    @Autowired
    IDfdwDzccTCarmoveService dfdwDzccTCarmoveService;

    @RequestMapping("/GetClydList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY02QX01')")
    public Result<Object> GetClydList(@RequestBody VDfdwDzccTClyd clyd) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clydService.GetClydList(dzccPersonEntity, clyd);
    }

    /**
     * 电子出车-PC-移动报表-导出
     * */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY02QX02')")
    public Result<Object> DownLoad(@RequestBody VDfdwDzccTClyd clyd, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<VDfdwDzccTClyd> list;
            LocalDate startDate = DateUtil.asLocalDate(clyd.getStartMoveTime());
            LocalDate endDate = DateUtil.asLocalDate(clyd.getEndMoveTime()).plusDays(1);

            list = clydMapper.getClydList(
                    clyd,
                    startDate,
                    endDate,
                    dzccPersonEntity
            );

            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(clyd.getGroupName() + "车辆移动情况");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("申请单位");
            row.createCell(1).setCellValue("车型");
            row.createCell(2).setCellValue("车牌号");
            row.createCell(3).setCellValue("司机");
            row.createCell(4).setCellValue("开始移动时间");
            row.createCell(5).setCellValue("结束移动时间");

            for (int i = 0; i< list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getGroupName());
                row.createCell(1).setCellValue(list.get(i).getCarMold());
                row.createCell(2).setCellValue(list.get(i).getLicensePlate());
                if (list.get(i).getDriverName() != null) {
                    row.createCell(3).setCellValue(list.get(i).getDriverName());
                }
                if (list.get(i).getStartMoveTime() != null) {
                    row.createCell(4).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(list.get(i).getStartMoveTime()));
                }
                if (list.get(i).getEndMoveTime() != null) {
                    row.createCell(5).setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(list.get(i).getEndMoveTime()));
                }
                row.setRowStyle(cellStyle);
            }

//            BBData datas = new BBData();
//            TableStyle rowStyle = new TableStyle();
//            rowStyle.setAlign(STJc.CENTER);
//            DetailData detailTable = new DetailData();
//            List<RowRenderData> plists = new ArrayList<RowRenderData>();
//            List<Map<String, Object>> tlists = new ArrayList<Map<String, Object>>();
//
//            list.stream().forEach(zb -> {
//                RowRenderData plist;
//                plist = RowRenderData.build(zb.getGroupName(), zb.getLicensePlate(), zb.getDriverName(), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(zb.getStartMoveTime()), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(zb.getEndMoveTime()));
//                plist.setRowStyle(rowStyle);
//                plists.add(plist);
//            });

//            detailTable.setPlists(plists);
//            detailTable.setTlists(tlists);
//            datas.setDetailTable(detailTable);
//            datas.setDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//            datas.setGroupName(clyd.getGroupName());
//
//            Configure config = Configure.newBuilder().bind("detail_table", new DetailTablePolicyClyd()).build();
//
//            XWPFTemplate template = XWPFTemplate.compile(filePath, config).render(datas);
//            //获取页面页脚
//            XWPFHeaderFooterPolicy headerFooterPolicy = template.getXWPFDocument().getHeaderFooterPolicy();
//            //创建文本段落
//            XWPFParagraph paragraph = template.getXWPFDocument().createParagraph();
//            SimpleDateFormat simple1 = new SimpleDateFormat("yyyy-MM-dd");
//            //设置水印显示的内容
//            String text = person.getRealName() + "\n\r" + person.getGroupName() + "\n\r" + simple1.format(Calendar.getInstance().getTimeInMillis());
//            headerFooterPolicy.createWatermark(text);
//            XWPFHeader header = headerFooterPolicy.getHeader(XWPFHeaderFooterPolicy.DEFAULT);
//            paragraph = header.getParagraphArray(0);
//            CTR r = paragraph.getCTP().getRArray(0);
//            // 6个水印（本身有一个）
//            //上中
//            setCTR(r, text, 1, "position:absolute;left:0pt;margin-left:100pt;margin-top:50pt;mso-position-horizontal-relative:margin;mso-position-vertical-relative:margin;rotation:-2949120f;height:60pt;width:180pt;mso-width-relative:page;mso-height-relative:page");
//            //右上角
//            setCTR(r, text, 1, "position:absolute;left:0pt;margin-left:320pt;margin-top:50pt;mso-position-horizontal-relative:margin;mso-position-vertical-relative:margin;rotation:-2949120f;height:60pt;width:180pt;mso-width-relative:page;mso-height-relative:page");
//            //左下角
//            setCTR(r, text, 1, "position:absolute;left:0pt;margin-left:-120pt;margin-top:495pt;mso-position-horizontal-relative:margin;mso-position-vertical-relative:margin;rotation:-2949120f;height:60pt;width:180pt;mso-width-relative:page;mso-height-relative:page");
//            //上中
//            setCTR(r, text, 1, "position:absolute;left:0pt;margin-left:100pt;margin-top:495pt;mso-position-horizontal-relative:margin;mso-position-vertical-relative:margin;rotation:-2949120f;height:60pt;width:180pt;mso-width-relative:page;mso-height-relative:page");
//            //右下角
//            setCTR(r, text, 1, "position:absolute;left:0pt;margin-left:320pt;margin-top:495pt;mso-position-horizontal-relative:margin;mso-position-vertical-relative:margin;rotation:-2949120f;height:60pt;width:180pt;mso-width-relative:page;mso-height-relative:page");
//
//            XmlObject[] xmlobjects = r.getPictArray(0).selectChildren(
//                    new QName("urn:schemas-microsoft-com:vml", "shape"));
//            if (xmlobjects.length > 0) {
//                CTShape ctshape = (CTShape) xmlobjects[0];
//                //设置水印的颜色
//
//                ctshape.setFillcolor("#D3D3D3");
//                //修改水印样式
//                //左上角
//                ctshape.setStyle("position:absolute;left:0pt;margin-left:-120pt;margin-top:50pt;mso-position-horizontal-relative:margin;mso-position-vertical-relative:margin;rotation:-2949120f;height:60pt;width:180pt;mso-width-relative:page;mso-height-relative:page");
//            }
//
//            String YEAR_MONTH = Calendar.getInstance().get(Calendar.YEAR) + "/" + Calendar.getInstance().get(Calendar.MONTH) + "/";
//            targetPath = localPath + "/szyy/dzcc/ccd/" + YEAR_MONTH;
//            File targetFile = new File(targetPath);
//            if (!targetFile.exists()) {
//                if (targetFile.mkdirs()) {
//                    out.println(targetPath + "创建成功");
//                } else {
//                    out.println(targetPath + "创建失败");
//                }
//            }//输出的文件
//            fileName = "出车单详情" + Calendar.getInstance().getTimeInMillis();
//            path = targetPath + fileName + ".docx";
//            outPut = new FileOutputStream(path);
//            template.write(outPut);
//            template.close();
//            //word转pdf
//            pdf = fileName + ".pdf";
//            InputStream docIs = Files.newInputStream(Paths.get(path));
//            MultipartFile pdfFile = wordToPdf(docIs, targetPath, pdf);
            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("主报表".getBytes("gb2312"), "ISO8859-1") + ".pdf");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
//            servletOutputStream = response.getOutputStream();
//            byte[] buffer = new byte[1024];
//            File file = new File(targetPath + pdf);
//            fis = new FileInputStream(file);
//            bis = new BufferedInputStream(fis);
//            int i = bis.read(buffer);
//            while (i != -1) {
//                servletOutputStream.write(buffer, 0, i);
//                i = bis.read(buffer);
//            }
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        } finally {
//            if (servletOutputStream != null) {
//                servletOutputStream.flush();
//                servletOutputStream.close();
//            }
//            if (outPut != null) {
//                outPut.flush();
//                outPut.close();
//            }
//            if (bis != null) {
//                bis.close();
//            }
//            if (fis != null) {
//                fis.close();
//            }
//            if (path != null) {
//                File del = new File(path);
//                if (del.delete()) {
//                    // 删除word
//                    out.println("删除word成功");
//                } else {
//                    out.println("删除word失败");
//                }
//            }
//            if (targetPath != null && pdf != null) {
//                File del = new File(targetPath + pdf);
//                if (del.delete()) {
//                    // 删除pdf
//                    out.println("删除pdf成功");
//                } else {
//                    out.println("删除pdf失败");
//                }
//            }
        }
    }

    /**
     * 电子出车-PC-移动报表-移动信息
     * */
    @RequestMapping("/carMove")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY02QX04')")
    public Result<Object> carMove(@RequestBody VDfdwDzccTClyd clyd) {
        LocalDate startTime = DateUtil.asLocalDate(clyd.getStartMoveTime());
        LocalDate endTime = DateUtil.asLocalDate(clyd.getEndMoveTime()).plusDays(1);
        List<DfdwDzccTCarmove> list = dfdwDzccTCarmoveService.list(new LambdaQueryWrapper<DfdwDzccTCarmove>()
                .eq(DfdwDzccTCarmove::getCarId, clyd.getCarId())
                .eq(DfdwDzccTCarmove::getDriveId, clyd.getDriverId())
                .ge(DfdwDzccTCarmove::getCurDate, startTime)
                .lt(DfdwDzccTCarmove::getCurDate, endTime)
                .orderByAsc(DfdwDzccTCarmove::getArriveTime)
        );
        return Result.ok(list);
    }
}
