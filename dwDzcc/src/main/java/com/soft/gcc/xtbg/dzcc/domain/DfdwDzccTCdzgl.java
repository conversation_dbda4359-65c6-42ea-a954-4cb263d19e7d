package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车-车队长分配表
* @TableName DFDW_DZCC_T_CDZGL
*/
@TableName(value ="DFDW_DZCC_T_CDZGL", autoResultMap = true)
@Data
public class DfdwDzccTCdzgl extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 车队长id
    */
    @TableField(value = "cdzId")
    @JSONField(name = "cdzId")

    private Integer cdzId;
    /**
    * 车队长名称
    */
    @TableField(value = "cdzName")
    @JSONField(name = "cdzName")

    private String cdzName;
    /**
    * 总管理人员id
    */
    @TableField(value = "zglId")
    @JSONField(name = "zglId")

    private Integer zglId;
    /**
    * 管理人员姓名
    */
    @TableField(value = "zglName")
    @JSONField(name = "zglName")

    private String zglName;
    /**
    * 管理单位id
    */
    @TableField(value = "deptId")
    @JSONField(name = "deptId")

    private Integer deptId;
    /**
    * 单位名称
    */
    @TableField(value = "deptName")
    @JSONField(name = "deptName")

    private String deptName;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 修改时间
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 车队长身份证
    */
    @TableField(value = "certificateId")
    @JSONField(name = "certificateId")

    private String certificateId;
    /**
     * (1车辆管理人员，2调度人员-车队长，3运监人员)
     */
    @TableField(value = "type")
    @JSONField(name = "type")

    private Integer type;

    /**
     * 人员类别 1管理2调度3运监
     */
    @TableField(exist = false)
    private Integer personType;

    @TableField(exist = false)
    private String telephone;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
