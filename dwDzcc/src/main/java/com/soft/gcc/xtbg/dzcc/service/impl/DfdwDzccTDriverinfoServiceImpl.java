package com.soft.gcc.xtbg.dzcc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTDriverinfoMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccPersonMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccRoleMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTDriverinfoService;
import com.soft.gcc.xtbg.dzcc.service.IDzccDictionaryvalueService;
import com.soft.gcc.xtbg.dzcc.util.ReadPatientExcelUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_DRIVERINFO(司机准假资质信息)】的数据库操作Service实现
 * @createDate 2023-07-27 09:36:19
 */
@Service
public class DfdwDzccTDriverinfoServiceImpl extends ServiceImpl<DfdwDzccTDriverinfoMapper, DfdwDzccTDriverinfo>
        implements IDfdwDzccTDriverinfoService {
    private static final Logger log = LoggerFactory.getLogger(DfdwDzccTDriverinfoServiceImpl.class);
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;
    @Autowired
    DzccRoleMapper roleMapper;
    @Autowired
    DzccPersonMapper personMapper;
    @Autowired
    TFileService tFileService;

    @Override
    public Result<Object> GetList(DzccPersonEntity dzccPersonEntity, DfdwDzccTDriveremploy info,Boolean exportFlag) {
        try {
            IPage<DfdwDzccTDriverinfo> page = new Page<>();
            page.setCurrent(info.getPageNum());
            page.setSize(info.getPageSize());
            boolean isInfoPerson = dzccPersonEntity.getQxs().contains(5);
            if(exportFlag==true){
                page.setCurrent(-1);
                page.setSize(-1);
                page = baseMapper.GetList(page, dzccPersonEntity.getDzccQx(), dzccPersonEntity.getId(), info, isInfoPerson);
            }else{
                page = baseMapper.GetList(page, dzccPersonEntity.getDzccQx(), dzccPersonEntity.getId(), info, isInfoPerson);
            }
            return Result.ok(page);
        } catch (Exception e) {
            log.error("电子出车-信息填报-聘用信息-查询报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> GetDict() {
        try {
            List<Integer> titles = Arrays.asList(970235, 970236, 970237, 970238, 970239, 970240, 970241);
            List<DzccDictionaryvalue> dicts = dictionaryvalueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                    .in(DzccDictionaryvalue::getTitleID, titles));
            Map<Integer, List<DzccDictionaryvalue>> map = dicts.stream().collect(Collectors.groupingBy(DzccDictionaryvalue::getTitleID));
            JSONObject object = JSONObject.parseObject(JSONObject.toJSONString(map));
            return Result.ok(object);
        } catch (Exception e) {
            log.error("电子出车-信息填报-聘用信息-字典查询报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> GetJsyxxList(DzccPersonEntity person, DfdwDzccTDriverinfo info) {
        try {
            IPage<DfdwDzccTDriverinfo> page = new Page<>();
            page.setCurrent(info.getPageNum());
            page.setSize(info.getPageSize());
            boolean isInfoPerson = person.getQxs().contains(5);
            page = baseMapper.GetJsyxxPage(page, person.getDzccQx(), person.getId(), info, isInfoPerson);
            return Result.ok(page);
        } catch (Exception e) {
            log.error("电子出车-信息填报-驾驶员信息-查询报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> AddJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info) {
        try {
            List<DfdwDzccTDriverinfo> driverinfos = this.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                    .eq(DfdwDzccTDriverinfo::getCertificateID, info.getCertificateID())
                    .like(DfdwDzccTDriverinfo::getType, "1")
            );
            if (driverinfos.size() > 0) {
                return Result.error("该身份证已存在！");
            }
            driverinfos = this.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                    .eq(DfdwDzccTDriverinfo::getCertificateID, info.getCertificateID())
            );
            if (driverinfos.size() > 0) {
                info.setId(driverinfos.get(0).getId());
                info.setType(driverinfos.get(0).getType() + ",1");
            } else {
                info.setType("1");
            }
            List<DzccVPerson> personList = personMapper.GetPersonByID(info.getCertificateID());
            if (personList.size() == 0) {
                // person表中没有
                DzccPerson dzccPerson = new DzccPerson();
                dzccPerson.setLoginName(info.getCertificateID());
                dzccPerson.setCertificateID(info.getCertificateID());
                dzccPerson.setRealName(info.getName());
                if (info.getGroupId() == null) {
                    dzccPerson.setGroupID(467);// 东耀
                } else {
                    dzccPerson.setGroupID(info.getGroupId());
                }
                dzccPerson.setTelephone(info.getPhoto());
                dzccPerson.setPassword("Tlx7PTkm8G7oFs5dbAwJ+g==");
                dzccPerson.setRoleId(0);
                dzccPerson.setType(1);
                dzccPerson.setZJ_CS(0);
                dzccPerson.setPhName(0);
                dzccPerson.setP_XH(999);
                dzccPerson.setState(1);
                personMapper.insert(dzccPerson);
                info.setGroupId(dzccPerson.getGroupID());
                info.setPersonId(dzccPerson.getId());
            } else {
                // person表中有
                DzccVPerson dzccPerson = personList.get(0);
                info.setGroupId(dzccPerson.getTopGroupId());
                info.setPersonId(dzccPerson.getId());
            }
            // 新增外协+司机角色
            personMapper.insertSjRole(info.getPersonId());
            this.save(info);

            if (StringUtils.isNotEmpty(info.getAccidentInsurancePolicyFile())) {
                List<String> filepath = Arrays.asList(info.getAccidentInsurancePolicyFile().split(","));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .in(TFile::getFilepath, filepath)
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "jsyxx")
                        .eq(TFile::getHjid, 1)
                );
            }
            if (StringUtils.isNotEmpty(info.getLaborContractFile())) {
                List<String> filepath = Arrays.asList(info.getLaborContractFile().split(","));
                tFileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, info.getId())
                        .in(TFile::getFilepath, filepath)
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getType, "jsyxx")
                        .eq(TFile::getHjid, 2)
                );
            }
            return Result.ok();
        } catch (Exception e) {
            log.error("电子出车-信息填报-驾驶员信息-新增报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> EditJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info) {
        try {
            List<DfdwDzccTDriverinfo> driverinfos = this.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                    .eq(DfdwDzccTDriverinfo::getCertificateID, info.getCertificateID())
                    .like(DfdwDzccTDriverinfo::getType, "1")
            );
            if (driverinfos.size() > 0) {
                for (DfdwDzccTDriverinfo driverinfo : driverinfos) {
                    if (!info.getId().equals(driverinfo.getId())) {
                        return Result.error("该身份证已存在！");
                    }
                }
            }
            List<DzccVPerson> personList = personMapper.GetPersonByID(info.getCertificateID());
            if (personList.size() > 0) {
                // person表中有
                personMapper.update(null, new LambdaUpdateWrapper<DzccPerson>()
                        .set(DzccPerson::getGroupID, info.getGroupId())
                        .set(DzccPerson::getTelephone, info.getPhoto())
                        .eq(DzccPerson::getId, info.getPersonId())
                );
            }
            this.updateById(info);

            List<String> filepath = Arrays.asList(info.getAccidentInsurancePolicyFile().split(","));
            tFileService.update(new LambdaUpdateWrapper<TFile>()
                    .set(TFile::getProjectid, info.getId())
                    .in(TFile::getFilepath, filepath)
                    .eq(TFile::getFunctionid, 20017)
                    .eq(TFile::getType, "jsyxx")
                    .eq(TFile::getHjid, 1)
            );
            return Result.ok();
        } catch (Exception e) {
            log.error("电子出车-信息填报-驾驶员信息-修改报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> DeleteJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info) {
        try {
            personMapper.deleteSjRole(info.getPersonId());
            if ("1".equals(info.getType())) {
                this.removeById(info.getId());
            } else {
                this.update(null, new LambdaUpdateWrapper<DfdwDzccTDriverinfo>()
                        .set(DfdwDzccTDriverinfo::getType, "2")
                        .eq(DfdwDzccTDriverinfo::getId, info.getId())
                );
            }
            return Result.ok();
        } catch (Exception e) {
            log.error("电子出车-信息填报-驾驶员信息-修改报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> UploadLoadJsyxx(DzccPersonEntity dzccPersonEntity, MultipartFile file) {
        String fileName = file.getOriginalFilename();//获取文件名
        try {
            if (!ReadPatientExcelUtil.validateExcel(fileName)) {// 验证文件名是否合格
                return null;
            }
            boolean isExcel2003 = true;// 根据文件名判断文件是2003版本还是2007版本
            if (ReadPatientExcelUtil.isExcel2007(fileName)) {
                isExcel2003 = false;
            }
            Workbook wb = null;
            InputStream is = file.getInputStream();
            if (isExcel2003) {// 当excel是2003时,创建excel2003
                wb = new HSSFWorkbook(is);
            } else {// 当excel是2007时,创建excel2007
                wb = new XSSFWorkbook(is);
            }
            List<DfdwDzccTDriverinfo> userList = new ArrayList<>();
            // 读取Excel里面客户的信息
            //默认会跳过第一行标题
            // 得到第一个shell
            Sheet sheet = wb.getSheetAt(0);
            // 得到Excel的行数
            int totalRows = sheet.getPhysicalNumberOfRows();
            int totalCells = 0;
            // 得到Excel的列数(前提是有行数)
            if (totalRows > 1 && sheet.getRow(0) != null) {
                totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
            }
            // 循环Excel行数
            for (int r = 1; r < totalRows; r++) {
                Row row = sheet.getRow(r);
                if (row == null) {
                    continue;
                }
                DfdwDzccTDriverinfo user = new DfdwDzccTDriverinfo();
                // 循环Excel的列
                for (int c = 0; c < totalCells; c++) {
                    Cell cell = row.getCell(c);
                    if (null != cell) {
                        if (c == 0) {
                            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                                cell.setCellType(CellType.STRING);
                            }
                            user.setName(cell.getStringCellValue());
                        } else if (c == 1) {
                            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                                cell.setCellType(CellType.STRING);
                            }
                            String stringCellValue = cell.getStringCellValue();
                            user.setCertificateID(stringCellValue);
                        } else if (c == 2) {
                            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                                cell.setCellType(CellType.STRING);
                            }
                            user.setPhoto(String.valueOf(cell.getStringCellValue()));
                        }
                    }
                }
                // 添加到list
                List<DfdwDzccTDriverinfo> driverinfos = this.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                        .eq(DfdwDzccTDriverinfo::getCertificateID, user.getCertificateID())
                        .like(DfdwDzccTDriverinfo::getType, "1")
                );
                if (driverinfos.size() > 0) {
                    user.setId(driverinfos.get(0).getId());
                    // 有则修改
                    List<DzccVPerson> personList = personMapper.GetPersonByID(user.getCertificateID());
                    if (personList.size() > 0) {
                        // person表中有
                        personMapper.update(null, new LambdaUpdateWrapper<DzccPerson>()
                                .set(DzccPerson::getTelephone, user.getPhoto())
                                .eq(DzccPerson::getId, user.getPersonId())
                        );
                    }
                    this.updateById(user);
                } else {
                    // 无则新增
                    driverinfos = this.list(new LambdaQueryWrapper<DfdwDzccTDriverinfo>()
                            .eq(DfdwDzccTDriverinfo::getCertificateID, user.getCertificateID())
                    );
                    if (driverinfos.size() > 0) {
                        user.setId(driverinfos.get(0).getId());
                        user.setType(driverinfos.get(0).getType() + ",1");
                    } else {
                        user.setType("1");
                    }
                    List<DzccVPerson> personList = personMapper.GetPersonByID(user.getCertificateID());
                    if (personList.size() == 0) {
                        // person表中没有
                        DzccPerson dzccPerson = new DzccPerson();
                        dzccPerson.setLoginName(user.getCertificateID());
                        dzccPerson.setCertificateID(user.getCertificateID());
                        dzccPerson.setRealName(user.getName());
                        if (user.getGroupId() == null) {
                            dzccPerson.setGroupID(467);// 东耀
                        } else {
                            dzccPerson.setGroupID(user.getGroupId());
                        }
                        dzccPerson.setTelephone(user.getPhoto());
                        dzccPerson.setPassword("Tlx7PTkm8G7oFs5dbAwJ+g==");
                        dzccPerson.setRoleId(0);
                        dzccPerson.setType(1);
                        dzccPerson.setZJ_CS(0);
                        dzccPerson.setPhName(0);
                        dzccPerson.setP_XH(999);
                        dzccPerson.setState(1);
                        personMapper.insert(dzccPerson);
                        user.setGroupId(dzccPerson.getGroupID());
                        user.setPersonId(dzccPerson.getId());
                    } else {
                        // person表中有
                        DzccVPerson dzccPerson = personList.get(0);
                        user.setGroupId(dzccPerson.getTopGroupId());
                        user.setPersonId(dzccPerson.getId());
                    }
                    // 新增外协+司机角色
                    personMapper.insertSjRole(user.getPersonId());
                    this.save(user);
                }
            }
            return Result.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> DownLoadJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info, HttpServletResponse response) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            boolean isInfoPerson = person.getQxs().contains(5);
            List<DfdwDzccTDriverinfo> list = baseMapper.GetJsyxxList(person.getDzccQx(), person.getId(), info, isInfoPerson);
            XSSFSheet sheet = workbook.createSheet("出车报表");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("驾驶员信息");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("部门");
            row.createCell(1).setCellValue("姓名");
            row.createCell(2).setCellValue("身份证");
            row.createCell(3).setCellValue("手机号");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getGroupName());
                row.createCell(1).setCellValue(list.get(i).getName());
                row.createCell(2).setCellValue(list.get(i).getCertificateID());
                row.createCell(3).setCellValue(list.get(i).getPhoto());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("驾驶员信息".getBytes("gb2312"), "ISO8859-1") + ".pdf");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




