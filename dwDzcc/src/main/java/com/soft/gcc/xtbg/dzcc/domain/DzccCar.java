package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 零租车辆--车辆管理表
 * @TableName LZCL_T_Car
 */
@TableName(value ="LZCL_T_Car")
@Data
public class DzccCar extends DzccBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车辆类型
     */
    @TableField(value = "CarType")
    private Integer carType;

    /**
     * 车牌
     */
    @TableField(value = "LicencePlate")
    private String licencePlate;

    /**
     * 车辆品牌
     */
    @TableField(value = "Brand")
    private String brand;

    /**
     * 车辆品牌型号
     */
    @TableField(value = "Model")
    private String model;

    /**
     * 车辆行驶证登记时间
     */
    @TableField(value = "LicenseCheckDate")
    private LocalDateTime licenseCheckDate;

    /**
     * 车辆产权单位
     */
    @TableField(value = "CarDeptName")
    private String carDeptName;

    /**
     * GPS识别号
     */
    @TableField(value = "GPSNum")
    private String GPSNum;

    /**
     * 本车驾驶员Id，为Null表示没有
     */
    @TableField(value = "DriverId")
    private Integer driverId;

    /**
     * NULL值,表示该车未被租用；如果是个具体日期,则表示这个日期以后可以被租用。
     */
    @TableField(value = "BorrowDate")
    private Date borrowDate;

    /**
     * 是否启用(0不启用，1启用)
     */
    @TableField(value = "IsEnable")
    private Integer isEnable;

    /**
     * 创建日期
     */
    @TableField(value = "CreateDate")
    private Date createDate;

    /**
     * 是否删除 0否 1是
     */
    @TableField(value = "IsDelete")
    private Integer isDelete;

    /**
     * 是否已确认 0否 1是
     */
    @TableField(value = "IsConfirm")
    private Integer isConfirm;

    /**
     * 确认人Id
     */
    @TableField(value = "ConfirmUserId")
    private Integer confirmUserId;

    /**
     * 确认时间
     */
    @TableField(value = "ConfirmTime")
    private Date confirmTime;

    /**
     * 车辆标识 0临租车辆 1特殊车辆
     */
    @TableField(value = "CarTag")
    private Integer carTag;

    /**
     * GPS设备启用时间
     */
    @TableField(value = "GPSEnableDate")
    private Date GPSEnableDate;

    /**
     * 商业险截止日期
     */
    @TableField(value = "BusinessRisksEndDate")
    private Date businessRisksEndDate;

    /**
     *
     */
    @TableField(value = "groupid")
    private Integer groupid;

    @TableField(value = "CarState")
    private Integer carState;

    @TableField(value = "CarMold")
    private String CarMold;
    @TableField(value = "CarGenre")
    private Integer carGenre;

    //当前车辆状态
    @TableField(exist=false)
    private String status;

    //司机名称
    @TableField(exist=false)
    private String driverName;

    //是否配置
    @TableField(exist=false)
    private Integer isPz;

    //redis key
    @TableField(exist=false)
    private String carKeyName;

    //部门名
    @TableField(exist=false)
    private String groupname;

    //数量
    @TableField(exist=false)
    private Integer number;

    //年份
    @TableField(exist=false)
    private Integer year;

    //月份
    @TableField(exist=false)
    private Integer month;

    //确认人姓名
    @TableField(exist=false)
    private String confirmUserName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
