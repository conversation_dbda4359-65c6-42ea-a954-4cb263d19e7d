package com.soft.gcc.xtbg.clwx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory;
import com.soft.gcc.xtbg.clwx.service.DfdwClwxRepairFactoryService;
import com.soft.gcc.xtbg.clwx.mapper.DfdwClwxRepairFactoryMapper;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_CLWX_REPAIR_FACTORY(车辆维修（新）-维修厂家)】的数据库操作Service实现
* @createDate 2024-10-17 16:11:09
*/
@Service
public class DfdwClwxRepairFactoryServiceImpl extends ServiceImpl<DfdwClwxRepairFactoryMapper, DfdwClwxRepairFactory>
    implements DfdwClwxRepairFactoryService{

    @Override
    public Result<Object> getPage(DfdwClwxRepairFactory repairFactory, PersonEntity person) {
        boolean isGroupAdmin = false;
        for (String roleVO : person.getRoleList()) {
            if ("协同办公-车辆维修-总管理".equals(roleVO)) {
                break;
            }
            if ("协同办公-车辆维修-部门管理".equals(roleVO)) {
                isGroupAdmin = true;
            }
        }
        if (isGroupAdmin) {
            repairFactory.setDeptId(person.getTopGroupId());
        }
        IPage<DfdwClwxRepairFactory> page = baseMapper.getPage(new Page<>(repairFactory.getPageNum(), repairFactory.getPageSize()), repairFactory, person);
        return Result.ok(page);
    }

    @Override
    public Result<Object> getList(DfdwClwxRepairFactory repairFactory, PersonEntity person) {
        boolean isGroupAdmin = false;
        for (String roleVO : person.getRoleList()) {
            if ("协同办公-车辆维修-总管理".equals(roleVO)) {
                break;
            }
            if ("协同办公-车辆维修-部门管理".equals(roleVO)) {
                isGroupAdmin = true;
            }
        }
        if (isGroupAdmin) {
            repairFactory.setDeptId(person.getTopGroupId());
        }
        List<DfdwClwxRepairFactory> list = baseMapper.getList(repairFactory, person);
        return Result.ok(list);
    }

    @Override
    public Result<Object> add(DfdwClwxRepairFactory repairFactory, PersonEntity person) {
        repairFactory.setCreateId(person.getId());
        repairFactory.setCreateTime(new Date());
        repairFactory.setUpdateId(person.getId());
        repairFactory.setUpdateTime(new Date());
        baseMapper.insert(repairFactory);
        return Result.ok();
    }

    @Override
    public Result<Object> edit(DfdwClwxRepairFactory repairFactory, PersonEntity person) {
        repairFactory.setUpdateId(person.getId());
        repairFactory.setUpdateTime(new Date());
        baseMapper.updateById(repairFactory);
        return Result.ok();
    }

    @Override
    public Result<Object> deleteByIds(DfdwClwxRepairFactory repairFactory, PersonEntity person) {
        baseMapper.deleteBatchIds(repairFactory.getIds());
        return Result.ok();
    }
}




