package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_CCLCDetail
*/
@TableName(value ="V_DFDW_DZCC_T_CCLCDetail")
@Data
public class VDfdwDzccTCclcdetail implements Serializable {


    /**
    *
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    *
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
    *
    */
    @TableField(value = "moveDate")
    @JSONField(name = "moveDate")
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date moveDate;
    /**
    *
    */
    @TableField(value = "startTime")
    @JSONField(name = "startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
    *
    */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
    *
    */
    @TableField(value = "overtimeHoursAm")
    @JSONField(name = "overtimeHoursAm")

    private BigDecimal overtimeHoursAm;
    /**
    *
    */
    @TableField(value = "overtimeHoursPm")
    @JSONField(name = "overtimeHoursPm")

    private BigDecimal overtimeHoursPm;
    /**
    *
    */
    @TableField(value = "subsidyState")
    @JSONField(name = "subsidyState")

    private Integer subsidyState;
    /**
    *
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    *  天数类型（0休息日 1工作日 2节假日）
    */
    @TableField(value = "dayType")
    @JSONField(name = "dayType")

    private Integer dayType;
    /**
    *
    */
    @TableField(value = "bgdId")
    @JSONField(name = "bgdId")

    private Integer bgdId;
    /**
    *
    */
    @TableField(value = "overtimeHoursAll")
    @JSONField(name = "overtimeHoursAll")

    private BigDecimal overtimeHoursAll;
    /**
    *
    */
    @TableField(value = "overtimeHoursNoon")
    @JSONField(name = "overtimeHoursNoon")

    private BigDecimal overtimeHoursNoon;

    //出差补贴
    @TableField(value = "travelAllowance")
    @JSONField(name = "travelAllowance")
    private BigDecimal travelAllowance;



    @TableField(exist = false)
    private String licencePlate;

    @TableField(exist = false)
    private String dateTemp ;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
