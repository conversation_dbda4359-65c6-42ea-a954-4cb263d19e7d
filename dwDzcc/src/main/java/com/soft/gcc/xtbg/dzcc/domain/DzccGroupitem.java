package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
*
* @TableName GroupItem
*/
@TableName(value ="GroupItem", autoResultMap = true)
@Data
public class DzccGroupitem extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    *
    */
    @TableField(value = "groupname")
    @JSONField(name = "groupname")

    private String groupname;
    /**
    *
    */
    @TableField(value = "parentid")
    @JSONField(name = "parentid")

    private Integer parentid;
    /**
    *
    */
    @TableField(value = "type")
    @JSONField(name = "type")

    private Integer type;
    /**
    *
    */
    @TableField(value = "shortpinyin")
    @JSONField(name = "shortpinyin")

    private String shortpinyin;
    /**
    *
    */
    @TableField(value = "dydj")
    @JSONField(name = "dydj")

    private String dydj;
    /**
    * 单位编号
    */
    @TableField(value = "uComapanyBH")
    @JSONField(name = "uComapanyBH")

    private String uComapanyBH;
    /**
    * 单位简称
    */
    @TableField(value = "uComapanyJC")
    @JSONField(name = "uComapanyJC")

    private String uComapanyJC;
    /**
    * 序号
    */
    @TableField(value = "XH")
    @JSONField(name = "XH")

    private Integer XH;
    /**
    * 是否（分包）
    */
    @TableField(value = "IsShow")
    @JSONField(name = "IsShow")

    private Integer IsShow;
    /**
    * 类型（1：单位；2：部门；3：班组；4：其它）
    */
    @TableField(value = "Category")
    @JSONField(name = "Category")

    private Integer Category;
    /**
    *
    */
    @TableField(value = "parentidRz")
    @JSONField(name = "parentidRz")

    private Integer parentidRz;
    /**
    *
    */
    @TableField(value = "uComapanyQC")
    @JSONField(name = "uComapanyQC")

    private String uComapanyQC;

    @TableField(exist = false)
    @JSONField(name = "childList")
    private List<DzccGroupitem> childList;

    @TableField(exist = false)
    @JSONField(name = "leaf")
    private Boolean leaf;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
