package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车-车辆管理
* @TableName DFDW_DZCC_T_CLGL
*/
@TableName(value ="DFDW_DZCC_T_CLGL", autoResultMap = true)
@Data
public class DfdwDzccTClgl extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 车辆id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 车辆部门id
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    * 办公地点id
    */
    @TableField(value = "unitId")
    @JSONField(name = "unitId")

    private Integer unitId;
    /**
    * 停车地点名称
    */
    @TableField(value = "stopName")
    @JSONField(name = "stopName")

    private String stopName;
    /**
    * 停车地点GPS
    */
    @TableField(value = "stopGPS")
    @JSONField(name = "stopGPS")

    private String stopGPS;
    /**
    *
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    *
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 编辑人员id
    */
    @TableField(value = "editUserId")
    @JSONField(name = "editUserId")

    private Integer editUserId;
    /**
    * 经度
    */
    @TableField(value = "longitude")
    @JSONField(name = "longitude")

    private BigDecimal longitude;
    /**
    * 纬度
    */
    @TableField(value = "latitude")
    @JSONField(name = "latitude")

    private BigDecimal latitude;
    /**
    * 办公地点名称
    */
    @TableField(value = "unitName")
    @JSONField(name = "unitName")

    private String unitName;
    /**
    * 所属单位名称
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")

    private String groupName;
    /**
    * 车牌
    */
    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")

    private String licensePlate;

    /**
     * 驾驶员id
     */
    @TableField(exist = false)
    private String driverId;

    /**
     * 驾驶员姓名
     */
    @TableField(exist = false)
    private String driverName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
