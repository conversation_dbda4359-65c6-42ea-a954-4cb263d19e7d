package com.soft.gcc.xtbg.dzcc.mapper;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.swagger.annotations.Api;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCLCDetail_Report(月份加班数据)】的数据库操作Mapper
* @createDate 2023-11-20 09:14:23
* @Entity com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport
*/
public interface DfdwDzccTCclcdetailReportMapper extends BaseMapper<DfdwDzccTCclcdetailReport> {

    List<DfdwDzccTCclcdetailReport> getListByMonth(@Param("year") int year,@Param("month") int monthValue,
                                                   @Param("topGroupId") Integer topGroupId,@Param("realName") String realName,
                                                   @Param("person") DzccPersonEntity person,
                                                   @Param("currDate") String currDate,  @Param("nextDate")String nextDate);
}




