package com.soft.gcc.xtbg.clwx.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_CLWX_REPAIR_FACTORY(车辆维修（新）-维修厂家)】的数据库操作Mapper
* @createDate 2024-10-17 16:11:09
* @Entity com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory
*/
public interface DfdwClwxRepairFactoryMapper extends BaseMapper<DfdwClwxRepairFactory> {

    List<DfdwClwxRepairFactory> getList(@Param("repairFactory") DfdwClwxRepairFactory repairFactory, @Param("person") PersonEntity person);

    IPage<DfdwClwxRepairFactory> getPage(
            IPage<DfdwClwxRepairFactory> page,
            @Param("repairFactory") DfdwClwxRepairFactory repairFactory,
            @Param("person") PersonEntity person);
}




