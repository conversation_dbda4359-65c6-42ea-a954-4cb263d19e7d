package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.common.t_smssend.service.TSmssendService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.domain.DzccPerson;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.entity.SubmitApproveDto;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekMainService;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekService;
import com.soft.gcc.xtbg.dzcc.service.IDzccPersonService;
import com.soft.gcc.xtbg.dzcc.service.VDfdwDzccTWeekMainService;
import com.soft.gcc.xtbg.dzcc.service.impl.DfdwDzccTWeekMainServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 电子出车自己业务审批
 * @date 2024-12-25 09:21:59
 */
@RequestMapping("/dzcc/dzccFlow")
@RestController
public class DzccFlowController extends DzccBaseController {

    @Autowired
    private DfdwDzccTWeekMainService weekMainService;
    @Autowired
    private DfdwDzccTWeekService weekService;
    @Autowired
    private IDzccPersonService personService;
    @Autowired
    private TSmssendService smssendService;
    @Autowired
    private VDfdwDzccTWeekMainService vDfdwDzccTWeekMainService;








    /**
     * 提交前置校验
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/checkSubmit")
    @ResponseBody
    public Result<Object> checkSubmit(@RequestBody SubmitApproveDto submitApproveDto) {
        Integer ywId = submitApproveDto.getYwId();
        VDfdwDzccTWeekMain weekMain = vDfdwDzccTWeekMainService.getById(ywId);
        if (weekMain == null) {
            return Result.error("获取当前业务数据失败，请刷新后重试");
        }
        if(weekMain.getApproveState() == 2){
            return Result.error("当前流程已审批，无法再次提交");
        }
        DzccPersonEntity user = getDzccPerson();
        //如果是审批中
        if(weekMain.getApproveState() == 1){
            if(submitApproveDto.getLcJdId() == 200321){
                return Result.error("当前流程审批中，无法提交");
            }
        }
        //如果是已驳回
        if(weekMain.getApproveState() == 3){
            //判断驳回重新提交的用户，是否为本人
            if(!weekMain.getSendPersonZgh().equals(user.getLoginName()+"~")){
                return Result.error("当前审批用户非本人");
            }
        }
        if(weekMain.getApproveState() == 4){
            return Result.error("流程已终止，无法再次提交");
        }

        return Result.ok();
    }


    /**
     * 提交
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/submitLc")
    @ResponseBody
    public Result<Object> submitLc(@RequestBody SubmitApproveDto submitApproveDto) {
        try {
            DzccPersonEntity user = getDzccPerson();
            Integer ywId = submitApproveDto.getYwId();
            //当前流程节点Id
            Integer lcJdId = submitApproveDto.getLcJdId();
            DfdwDzccTWeekMain weekMain = weekMainService.getById(ywId);
            if (weekMain == null) {
                return Result.error("获取当前业务数据失败，请刷新后重试");
            }
            if (lcJdId == 200321) {
                weekMain.setApproveState(1);
                weekMain.setApplyUserId(user.getId());
                weekMain.setApplyUserName(user.getRealName());
                weekMain.setApplyTime(LocalDateTime.now());
                weekMainService.updateById(weekMain);
                List<DfdwDzccTWeek> weekList = weekService.list(new LambdaQueryWrapper<DfdwDzccTWeek>().eq(DfdwDzccTWeek::getWeekMainId, ywId));
                for (DfdwDzccTWeek week : weekList) {
                    week.setApproveState(1);
                    week.setApplyUserId(user.getId());
                    week.setApplyUserName(user.getRealName());
                    week.setApplyTime(LocalDateTime.now());
                    weekService.updateById(week);
                }
            }
            if (submitApproveDto.getSendPersonId() == -1 && submitApproveDto.getSendPersonName().equals("完成")) {
                //修改审批状态
                weekMain.setApproveState(2);
                weekMainService.updateById(weekMain);
                List<DfdwDzccTWeek> weekList = weekService.list(new LambdaQueryWrapper<DfdwDzccTWeek>().eq(DfdwDzccTWeek::getWeekMainId, ywId));
                for (DfdwDzccTWeek week : weekList) {
                    if(week.getAppendDelayHour() == null){
                        week.setAppendDelayHour(new BigDecimal("0"));
                    }
                    week.setApproveState(2);
                    weekService.updateById(week);
                }

                //审批完结-发送短信通知申请人
                DzccPerson person = personService.getById(weekMain.getApplyUserId());
                String msg = "您发起电子出车-延时申请已审批完成，详情请查看app。";
                smssendService.sendSms(person.getTelephone(), msg);
            } else {
                //审批过程中-发送短信提醒审批人
                DzccPerson person = personService.getById(submitApproveDto.getSendPersonId());
                String msg = weekMain.getApplyUserName() + "向您发起电子出车-延时申请审批，请给予办理。";
                smssendService.sendSms(person.getTelephone(), msg);
            }


        } catch (Exception ex) {
            return Result.error("提交审批失败：" + ex.getMessage());
        }
        return Result.ok("提交审批成功");
    }


    /**
     * 驳回-退回到申报
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/rollBackForApply")
    @ResponseBody
    public Result rollBackForApply(@RequestBody SubmitApproveDto submitApproveDto) {
        try {
            Integer ywId = submitApproveDto.getYwId();
            DfdwDzccTWeekMain weekMain = weekMainService.getById(ywId);
            if (weekMain == null) {
                return Result.error("获取当前业务数据失败，请刷新后重试");
            }
            weekMain.setApproveState(3);
            weekMainService.updateById(weekMain);
            List<DfdwDzccTWeek> weekList = weekService.list(new LambdaQueryWrapper<DfdwDzccTWeek>().eq(DfdwDzccTWeek::getWeekMainId, ywId));
            for (DfdwDzccTWeek week : weekList) {
                week.setApproveState(3);
                weekService.updateById(week);
            }

            //审批完结-发送短信通知申请人
            DzccPerson person = personService.getById(weekMain.getApplyUserId());
            String msg = "您发起电子出车-延时申请已被驳回，请及时查看。";
            smssendService.sendSms(person.getTelephone(), msg);


        } catch (Exception ex) {
            return Result.error("提交审批失败：" + ex.getMessage());
        }
        return Result.ok("提交审批成功");
    }


}
