package com.soft.gcc.xtbg.clwx.service;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxRepairFactory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yyszc.wpbase.ventity.PersonEntity;

/**
* <AUTHOR>
* @description 针对表【DFDW_CLWX_REPAIR_FACTORY(车辆维修（新）-维修厂家)】的数据库操作Service
* @createDate 2024-10-17 16:11:09
*/
public interface DfdwClwxRepairFactoryService extends IService<DfdwClwxRepairFactory> {

    Result<Object> getPage(DfdwClwxRepairFactory repairFactory, PersonEntity person);

    Result<Object> getList(DfdwClwxRepairFactory repairFactory, PersonEntity person);

    Result<Object> add(DfdwClwxRepairFactory repairFactory, PersonEntity person);

    Result<Object> edit(DfdwClwxRepairFactory repairFactory, PersonEntity person);

    Result<Object> deleteByIds(DfdwClwxRepairFactory repairFactory, PersonEntity person);
}
