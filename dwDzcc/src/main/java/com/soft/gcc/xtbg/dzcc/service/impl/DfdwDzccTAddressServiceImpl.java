package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.soft.gcc.common.dictionary_value.entity.Dictionaryvalue;
import com.soft.gcc.common.dictionary_value.service.DictionaryvalueService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTAddressMapper;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.yyszc.wpbase.entity.DictionaryValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_ADDRESS(出车单-目的地)】的数据库操作Service实现
 * @createDate 2022-10-21 17:06:33
 */
@Service
public class DfdwDzccTAddressServiceImpl extends ServiceImpl<DfdwDzccTAddressMapper, DfdwDzccTAddress>
        implements IDfdwDzccTAddressService {
    @Resource
    public GfxmTCityService gfxmTCityService;

    @Resource
    public DfdwDzccTCcddService dfdwDzccTCcddService;
    @Resource
    private IDfdwDzccTCarmoveService carmoveService;
    @Resource
    private IDfdwDzccTCclcService cclcService;
    @Resource
    private IDzccDictionaryvalueService dictionaryValueService;
    @Resource
    private IDfdwDzccTCarstatusdailyService carstatusdailyService;
    @Resource
    private IDfdwDzccTCclcStatusDailyService cclcStatusDailyService;
    @Resource
    private IDfdwDzccTCclcdetailService cclcdetailService;
    @Resource
    private DfdwDzccOverTimeService dfdwDzccOverTimeService;
    @Resource
    private IVDfdwDzccTZbbService vDfdwDzccTZbbService;
    @Autowired
    private IVDfdwDzccTCclcdetailService ivDfdwDzccTCclcdetailService;
    @Resource
    private IDfdwDzccTCclcdetailReportService reportService;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public Result<Object> addCcdAddess(DfdwDzccTAddress address) {
        //判断目的地是否已经存在
        Integer count = this.baseMapper.selectCount(new LambdaQueryWrapper<DfdwDzccTAddress>().eq(DfdwDzccTAddress::getLcId, address.getLcId()).eq(DfdwDzccTAddress::getArea, address.getArea()).eq(DfdwDzccTAddress::getCity, address.getCity()).eq(DfdwDzccTAddress::getProvinces, address.getProvinces()));
        if (count > 0) {
            return Result.error("目的地已存在！");
        }
        List<Integer> ids = new ArrayList<>();
        ids.add(address.getCity());
        if (address.getArea() > 0) {
            ids.add(address.getArea());
        }
        //根据type 升序的话 查询出来的顺序就是 省 市 区
        List<GfxmTCity> citys = gfxmTCityService.list(new LambdaQueryWrapper<GfxmTCity>().in(GfxmTCity::getId, ids).orderByAsc(GfxmTCity::getType));
        String cityName = citys.get(0).getCityName(); //市 名
        //不在宁波市区内的都显示市辖区
        String areaName = address.getArea() > 0 ? citys.get(1).getCityName() : "市辖区";
        address.setCityText(cityName);
        address.setAreaText(cityName + areaName);
        address.setCreateTime(new Date());
        address.setAddType(3);
        address.setApproveState(2);


        //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓判断用户是否到达过添加的目的地↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
        DfdwDzccTCclc cclc = cclcService.getById(address.getLcId());
        Integer carId = cclc.getCarId();
        String startDate = sdf.format(cclc.getCcOpenTime());
        String endDate = sdf.format(cclc.getExecuteTime());
        //获取出车单周期范围内 实际到达区域信息
        List<DfdwDzccTCarmove> moveList = carmoveService.list(new LambdaQueryWrapper<DfdwDzccTCarmove>().eq(DfdwDzccTCarmove::getCarId, carId)
                .ge(DfdwDzccTCarmove::getCurDate, startDate).le(DfdwDzccTCarmove::getCurDate, endDate));

        List<DfdwDzccTCarmove> temp;
        if (address.getCity() == 123) {
            //宁波市内
            temp = moveList.stream().filter(p -> p.getCity() == 123 && Objects.equals(p.getArea(), address.getArea())).collect(Collectors.toList());
        } else {
            temp = moveList.stream().filter(p -> p.getCity() != 123 && Objects.equals(p.getCity(), address.getCity())).collect(Collectors.toList());
        }
        Integer arriveState = 0;
        if (temp.size() > 0) {
            arriveState = 1;
        }
        //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑判断用户是否到达过添加的目的地↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

        address.setArriveState(arriveState);
        this.save(address);

        DfdwDzccTCcdd dzccTCcdd = new DfdwDzccTCcdd();
        dzccTCcdd.setLcid(address.getLcId());
        dzccTCcdd.setProvinces(address.getProvinces());
        dzccTCcdd.setCity(address.getCity());
        dzccTCcdd.setArea(address.getArea());
        dzccTCcdd.setAreatext(address.getAreaText());
        dzccTCcdd.setApplyuserid(address.getDzccPersonEntity().getId());
        dzccTCcdd.setApplyusername(address.getDzccPersonEntity().getLoginName());
        dzccTCcdd.setCreatetime(new Date());
        dfdwDzccTCcddService.save(dzccTCcdd);


        //修改lc主表目的地相关信息
        List<DfdwDzccTAddress> addressesList = baseMapper.selectList(new LambdaQueryWrapper<DfdwDzccTAddress>().eq(DfdwDzccTAddress::getLcId, address.getLcId()));
        if (addressesList.size() > 1) {
            cclc.setAddressInfo(addressesList.get(0).getAreaText() + "等" + addressesList.size() + "地");
        } else {
            cclc.setAddressInfo(addressesList.get(0).getAreaText());
        }
        cclcService.updateById(cclc);

        if (arriveState == 1) {
            //解析区域情况
            carstatusdailyService.getQyInfoNew(address.getLcId());
            //解析出车单区域情况
            cclcStatusDailyService.calcStatusDaily(address.getLcId());

            //重新加班费出差补贴金额
            travelAllowance(cclc);
        }

        return Result.ok();
    }

    public void travelAllowance(DfdwDzccTCclc cclc) {
        Integer lcId = cclc.getId();
        Integer carId = cclc.getCarId();
        Integer driverId = cclc.getDriveId();
        Integer topDeptId = cclc.getApplyTopDeptId();

        //获取配置信息
        List<DfdwDzccTOverTime> overtimeSetList = dfdwDzccOverTimeService.list(new LambdaQueryWrapper<DfdwDzccTOverTime>());
        DfdwDzccTOverTime overtimeSet = vDfdwDzccTZbbService.getOneOverTimeSet(overtimeSetList, topDeptId);

        //去字典出差补贴配置
        List<DzccDictionaryvalue> dictList = dictionaryValueService.list(new LambdaQueryWrapper<DzccDictionaryvalue>()
                .eq(DzccDictionaryvalue::getTitleID, 970221).eq(DzccDictionaryvalue::getContent, "出差补贴"));

        BigDecimal bqy = new BigDecimal("0");
        BigDecimal kqy = new BigDecimal("50");
        BigDecimal cds = new BigDecimal("140");
        for (DzccDictionaryvalue dict : dictList) {
            if ("出差补贴-本区域".equals(dict.getContent())) {
                bqy = new BigDecimal(dict.getParameter());
            }
            if ("出差补贴-跨区域".equals(dict.getContent())) {
                kqy = new BigDecimal(dict.getParameter());
            }
            if ("出差补贴-出大市".equals(dict.getContent())) {
                cds = new BigDecimal(dict.getParameter());
            }
        }

        //重新计算每日加班费数据
        List<DfdwDzccTCclcdetail> cclcdetailList = cclcdetailService.list(new LambdaQueryWrapper<DfdwDzccTCclcdetail>().eq(DfdwDzccTCclcdetail::getYwId, lcId));
        for (int i = 0; i < cclcdetailList.size(); i++) {
            DfdwDzccTCclcdetail entity = cclcdetailList.get(i);
            Date moveDate = entity.getMoveDate();

            List<DfdwDzccTCarstatusdaily> carstatusTemp = carstatusdailyService.list(new LambdaQueryWrapper<DfdwDzccTCarstatusdaily>().eq(DfdwDzccTCarstatusdaily::getCurDate, moveDate)
                    .eq(DfdwDzccTCarstatusdaily::getCarId, carId).eq(DfdwDzccTCarstatusdaily::getPersonId, driverId));
            if (carstatusTemp.size() > 0) {
                if ("本区域".equals(carstatusTemp.get(0).getStatus())) {
                    if (overtimeSet.getTravelAllowanceType() == 2) {
                        //2本区域=跨区域【动了就是50】
                        entity.setTravelAllowance(kqy);
                    } else {
                        //1正常计算【本区域0跨区域50出大市100】
                        entity.setTravelAllowance(bqy);
                    }

                } else if ("跨区域".equals(carstatusTemp.get(0).getStatus())) {
                    entity.setTravelAllowance(kqy);
                } else if ("出大市".equals(carstatusTemp.get(0).getStatus())) {
                    entity.setTravelAllowance(cds);
                }
            } else {
                //当天肯定有移动记录的 carStatusJt
                if (overtimeSet.getTravelAllowanceType() == 2) {
                    //2本区域=跨区域【动了就是50】
                    entity.setTravelAllowance(kqy);
                } else {
                    //1正常计算【本区域0跨区域50出大市100】
                    entity.setTravelAllowance(bqy);
                }
            }
            cclcdetailService.updateById(entity);
        }


        //重新计算每月加班费数据
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(cclc.getCcOpenTime());
        Integer year1 = calendar1.get(Calendar.YEAR);
        Integer month1 = calendar1.get(Calendar.MONTH) + 1;

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(cclc.getExecuteTime());
        Integer year2 = calendar2.get(Calendar.YEAR);
        Integer month2 = calendar2.get(Calendar.MONTH) + 1;
        List<DfdwDzccTCclcdetailReport> reportList1 = reportService.list(new LambdaQueryWrapper<DfdwDzccTCclcdetailReport>().eq(DfdwDzccTCclcdetailReport::getDriveId, driverId)
                .eq(DfdwDzccTCclcdetailReport::getYear, year1).eq(DfdwDzccTCclcdetailReport::getMonth, month1));
        if (reportList1.size() > 0) {
            DfdwDzccTCclcdetailReport report = reportList1.get(0);

            Calendar calendar = Calendar.getInstance();
            // 设置当月第一天
            calendar.set(year1, month1 - 1, 1);
            Date firstDayOfMonth = calendar.getTime();
            // 设置当月最后一天
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date lastDayOfMonth = calendar.getTime();
            String startDate = sdf.format(firstDayOfMonth);
            String endDate = sdf.format(lastDayOfMonth);


            //获取当月每日数据之和
            List<VDfdwDzccTCclcdetail> detailList = ivDfdwDzccTCclcdetailService.list(new LambdaQueryWrapper<VDfdwDzccTCclcdetail>()
                    .eq(VDfdwDzccTCclcdetail::getDriveId, driverId)
                    .ge(VDfdwDzccTCclcdetail::getMoveDate, startDate).le(VDfdwDzccTCclcdetail::getMoveDate, endDate));
            //出差补贴
            BigDecimal travelAllowance = detailList.stream().filter(p -> p.getTravelAllowance() != null).map(VDfdwDzccTCclcdetail::getTravelAllowance).reduce(BigDecimal.ZERO, BigDecimal::add);
            report.setTravelAllowance(travelAllowance);

            //总合计
            BigDecimal allCost = report.getOvertimeCost().add(report.getDelayCost()).add(report.getNightShiftCost()).add(travelAllowance);
            report.setAllCost(allCost);

            reportService.updateById(report);
        }

        //如果开始时间和结束时间不是同一月,需要重新更新结束日期所在月份数据
        if (year1 != year2 || month1 != month2) {
            List<DfdwDzccTCclcdetailReport> reportList2 = reportService.list(new LambdaQueryWrapper<DfdwDzccTCclcdetailReport>().eq(DfdwDzccTCclcdetailReport::getDriveId, driverId)
                    .eq(DfdwDzccTCclcdetailReport::getYear, year2).eq(DfdwDzccTCclcdetailReport::getMonth, month2));
            if (reportList2.size() > 0) {
                DfdwDzccTCclcdetailReport report = reportList2.get(0);

                Calendar calendar = Calendar.getInstance();
                // 设置当月第一天
                calendar.set(year2, month2 - 1, 1);
                Date firstDayOfMonth = calendar.getTime();
                // 设置当月最后一天
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                Date lastDayOfMonth = calendar.getTime();
                String startDate = sdf.format(firstDayOfMonth);
                String endDate = sdf.format(lastDayOfMonth);


                //获取当月每日数据之和
                List<VDfdwDzccTCclcdetail> detailList = ivDfdwDzccTCclcdetailService.list(new LambdaQueryWrapper<VDfdwDzccTCclcdetail>()
                        .eq(VDfdwDzccTCclcdetail::getDriveId, driverId)
                        .ge(VDfdwDzccTCclcdetail::getMoveDate, startDate).le(VDfdwDzccTCclcdetail::getMoveDate, endDate));
                //出差补贴
                BigDecimal travelAllowance = detailList.stream().filter(p -> p.getTravelAllowance() != null).map(VDfdwDzccTCclcdetail::getTravelAllowance).reduce(BigDecimal.ZERO, BigDecimal::add);
                report.setTravelAllowance(travelAllowance);

                //总合计
                BigDecimal allCost = report.getOvertimeCost().add(report.getDelayCost()).add(report.getNightShiftCost()).add(travelAllowance);
                report.setAllCost(allCost);

                reportService.updateById(report);

            }
        }
    }

}




