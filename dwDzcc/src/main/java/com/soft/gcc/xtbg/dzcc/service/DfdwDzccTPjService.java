package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_PJ(电子出车单-评价)】的数据库操作Service
* @createDate 2025-01-21 14:33:25
*/
public interface DfdwDzccTPjService extends IService<DfdwDzccTPj> {

    /**
     * 创建电子出车单-评价
     *
     * @param dfdwDzccTPj 创建信息
     * @return 编号
     */
    Integer createDzccTPj(DfdwDzccTPj dfdwDzccTPj);

    /**
     * 更新电子出车单-评价
     *
     * @param dfdwDzccTPj 更新信息
     */
    void updateDzccTPj(DfdwDzccTPj dfdwDzccTPj) throws RuntimeException;

    /**
     * 删除电子出车单-评价
     *
     * @param ids 编号
     */
    void deleteDzccTPj(Collection<Integer> ids) throws RuntimeException;

    /**
     * 获得电子出车单-评价
     *
     * @param id 编号
     * @return 电子出车单-评价
     */
    DfdwDzccTPj getDzccTPj(Integer id);

    /**
     * 获得电子出车单-评价列表
     *
     * @param ids 编号
     * @return 电子出车单-评价列表
     */
    List<DfdwDzccTPj> getDzccTPjList(Collection<Integer> ids);

    /**
     * 获得电子出车单-评价分页
     *
     * @param dfdwDzccTPj 分页查询
     * @param person 当前用户
     * @return 电子出车单-评价分页
     */
    IPage<DfdwDzccTPj> getDzccTPjPage(DfdwDzccTPj dfdwDzccTPj, DzccPersonEntity person);

    /**
     * 获得电子出车单-评价列表, 用于 Excel 导出
     *
     * @param dfdwDzccTPj 查询条件
     * @param person 当前用户
     * @return 电子出车单-评价列表
     */
    List<DfdwDzccTPj> getDzccTPjList(DfdwDzccTPj dfdwDzccTPj, DzccPersonEntity person);

    /**
     * 根据lcId获得电子出车单-评价
     *
     * @param lcId lc主键Id
     * @return 电子出车单-评价
     */
    DfdwDzccTPj getDzccTPjByLcId(Integer lcId);
}
