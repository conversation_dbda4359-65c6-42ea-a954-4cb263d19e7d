package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆情况补充
 * @TableName LZCL_T_Car_More
 */
@TableName(value ="LZCL_T_Car_More")
@Data
public class LzclTCarMore implements Serializable {
    /**
     *
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车牌
     */
    @TableField(value = "LicencePlate")
    private String licencePlate;

    /**
     * 开始日期
     */
    @TableField(value = "StartDate")
    private Date startDate;

    /**
     * 结束日期
     */
    @TableField(value = "EndDate")
    private Date endDate;

    /**
     * 单位id
     */
    @TableField(value = "GroupId")
    private Integer groupId;

    /**
     * 更新日期
     */
    @TableField(value = "UpdateDate")
    private Date updateDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
