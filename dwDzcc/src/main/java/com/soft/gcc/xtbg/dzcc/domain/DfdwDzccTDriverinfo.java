package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 司机准假资质信息
 * @TableName DFDW_DZCC_T_DRIVERINFO
 */
@TableName(value ="DFDW_DZCC_T_DRIVERINFO", autoResultMap = true)
@Data
public class DfdwDzccTDriverinfo extends DzccBaseEntity implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
     * 姓名
     */
    @TableField(value = "name")
    @JSONField(name = "name")
    private String name;

    /**
     * 部门id
     */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")
    private Integer groupId;

    /**
     * 人员id
     */
    @TableField(value = "personId")
    @JSONField(name = "personId")
    private Integer personId;

    /**
     * 身份证
     */
    @TableField(value = "certificateID")
    @JSONField(name = "certificateID")
    private String certificateID;

    /**
     * 性别（1男 2女）
     */
    @TableField(value = "sex")
    @JSONField(name = "sex")
    private Integer sex;

    /**
     * 生日
     */
    @TableField(value = "brithday")
    @JSONField(name = "brithday")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date brithday;

    /**
     * 民族
     */
    @TableField(value = "nation")
    @JSONField(name = "nation")
    private String nation;

    /**
     * 政治面貌
     */
    @TableField(value = "politicalOutlook")
    @JSONField(name = "politicalOutlook")
    private String politicalOutlook;

    /**
     * 健康状况
     */
    @TableField(value = "health")
    @JSONField(name = "health")
    private String health;

    /**
     * 电话
     */
    @TableField(value = "photo")
    @JSONField(name = "photo")
    private String photo;

    /**
     * 家庭住址
     */
    @TableField(value = "address")
    @JSONField(name = "address")
    private String address;

    /**
     * 劳务合同单位
     */
    @TableField(value = "laborContractUnit")
    @JSONField(name = "laborContractUnit")
    private String laborContractUnit;

    /**
     * 驾驶证
     */
    @TableField(value = "driverLicense")
    @JSONField(name = "driverLicense")
    private String driverLicense;

    /**
     * 初次领证时间
     */
    @TableField(value = "driverCollectionTime")
    @JSONField(name = "driverCollectionTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date driverCollectionTime;

    /**
     * 驾驶车型
     */
    @TableField(value = "driverCarType")
    @JSONField(name = "driverCarType")
    private String driverCarType;

    /**
     * 发证机关
     */
    @TableField(value = "licenceIssuingAuthority")
    @JSONField(name = "licenceIssuingAuthority")
    private String licenceIssuingAuthority;

    /**
     * 驾驶证有效期
     */
    @TableField(value = "driverExpiryDate")
    @JSONField(name = "driverExpiryDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date driverExpiryDate;





    /**
     * 准驾证
     */
    @TableField(value = "permitLicense")
    @JSONField(name = "permitLicense")
    private String permitLicense;

    /**
     * 准假类型
     */
    @TableField(value = "permitType")
    @JSONField(name = "permitType")
    private String permitType;

    /**
     * 初次申领时间
     */
    @TableField(value = "permitApplicationTime")
    @JSONField(name = "permitApplicationTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date permitApplicationTime;

    /**
     * 准驾车型
     */
    @TableField(value = "permitCarType")
    @JSONField(name = "permitCarType")
    private String permitCarType;

    /**
     * 准驾车辆归属
     */
    @TableField(value = "permitCarUnit")
    @JSONField(name = "permitCarUnit")
    private String permitCarUnit;

    /**
     * 准驾证有效期
     */
    @TableField(value = "permitExpiryDate")
    @JSONField(name = "permitExpiryDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date permitExpiryDate;

    /**
     * 交通安全理论成绩
     */
    @TableField(value = "trafficSafetyTheory")
    @JSONField(name = "trafficSafetyTheory")
    private Integer trafficSafetyTheory;

    /**
     * 驾驶技能考核成绩
     */
    @TableField(value = "drivingSkillResults")
    @JSONField(name = "drivingSkillResults")
    private Integer drivingSkillResults;

    /**
     * 背景审查
     */
    @TableField(value = "backgroundCheck")
    @JSONField(name = "backgroundCheck")
    private String backgroundCheck;

    /**
     * 近三年伤人交通事故
     */
    @TableField(value = "trafficAccident")
    @JSONField(name = "trafficAccident")
    private String trafficAccident;

    /**
     * 身份证正面图片T_File id
     */
    @TableField(value = "idCardFrontFileId")
    @JSONField(name = "idCardFrontFileId")
    private Integer idCardFrontFileId;

    /**
     * 身份证反面图片T_File id
     */
    @TableField(value = "idCardReserveFileId")
    @JSONField(name = "idCardReserveFileId")
    private Integer idCardReserveFileId;

    /**
     * 驾驶证正面图片T_File id
     */
    @TableField(value = "licenseFrontFileId")
    @JSONField(name = "licenseFrontFileId")
    private Integer licenseFrontFileId;

    /**
     * 驾驶证反面图片T_File id
     */
    @TableField(value = "licenseReserveFileId")
    @JSONField(name = "licenseReserveFileId")
    private Integer licenseReserveFileId;

    /**
     * 司机正面照图片T_File id
     */
    @TableField(value = "facePhotoId")
    @JSONField(name = "facePhotoId")
    private Integer facePhotoId;

    /**
     * 类型 1 驾驶员信息（汇丰一） 2 资质信息（汇丰二）
     */
    @TableField(value = "type")
    @JSONField(name = "type")
    private String type;

    /**
     * 意外保险单T_File filePath（多个由逗号隔开）
     */
    @TableField(value = "accidentInsurancePolicyFile", updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "accidentInsurancePolicyFile")
    private String accidentInsurancePolicyFile;

    /**
     * 意外保险单T_File filePath（多个由逗号隔开）
     */
    @TableField(value = "laborContractFile", updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "laborContractFile")
    private String laborContractFile;

    @TableField(exist = false)
    private String groupName;

    @TableField(exist = false)
    private String FileId1;

    @TableField(exist = false)
    private String FileId2;

    @TableField(exist = false)
    private String FileId3;

    @TableField(exist = false)
    private String FileId4;

    @TableField(exist = false)
    private String FileId5;


    /**
     * 驾驶证有效期
     */
    @TableField(exist = false)
    private String driverExpiryDateStr;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
