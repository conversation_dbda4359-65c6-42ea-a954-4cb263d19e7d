package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcdetailReport;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCclcdetailReportService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCclcdetailReportMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCLCDetail_Report(月份加班数据)】的数据库操作Service实现
* @createDate 2023-11-20 09:14:23
*/
@Service
public class DfdwDzccTCclcdetailReportServiceImpl extends ServiceImpl<DfdwDzccTCclcdetailReportMapper, DfdwDzccTCclcdetailReport>
    implements IDfdwDzccTCclcdetailReportService{

    /**
     *
     * @param year
     * @param monthValue
     * @param topGroupId
     * @param realName
     * @param person
     * @param currDate 2024-10-01
     * @param nextDate 2024-11-01
     * @return
     */
    @Override
    public List<DfdwDzccTCclcdetailReport> getListByMonth(int year, int monthValue,Integer topGroupId,String realName, DzccPersonEntity person,String currDate,String nextDate) {
        return baseMapper.getListByMonth(year,monthValue,topGroupId,realName,person,currDate,nextDate);
    }
}




