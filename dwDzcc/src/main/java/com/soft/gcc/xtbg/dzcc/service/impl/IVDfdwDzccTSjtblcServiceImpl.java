package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccGroupitem;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc;
import com.soft.gcc.xtbg.dzcc.mapper.DzccGroupitemMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTSjtblcMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTSjtblcService;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_SJTBLC】的数据库操作Service实现
* @createDate 2023-05-26 11:22:18
*/
@Service
public class IVDfdwDzccTSjtblcServiceImpl extends ServiceImpl<VDfdwDzccTSjtblcMapper, VDfdwDzccTSjtblc>
    implements IVDfdwDzccTSjtblcService {

    @Autowired
    DzccGroupitemMapper dzccGroupitemMapper;

    @Override
    public Result<Object> GetSjtblcList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTSjtblc sjtblc) {
        try {
            IPage<VDfdwDzccTSjtblc> list = new Page<>(sjtblc.getPageNum(), sjtblc.getPageSize());
            List<Integer> groupIds = new ArrayList<>();
            if (sjtblc.getGroupid() != null && sjtblc.getGroupid() > 0) {
                groupIds.add(sjtblc.getGroupid());
            } else {
                List<DzccGroupitem> groupitems = dzccGroupitemMapper.GetGroupListByRole(dzccPersonEntity, 1, false);
                groupIds = groupitems.stream().map(DzccGroupitem::getId).collect(Collectors.toList());
            }
            list = baseMapper.getSjtblcListPage(
                    list,
                    sjtblc,
                    dzccPersonEntity,
                    groupIds
            );
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




