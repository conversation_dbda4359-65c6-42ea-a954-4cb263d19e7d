package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 
* @TableName T_File
*/
@TableName(value ="T_File", autoResultMap = true)
@Data
public class DzccTFile implements Serializable {

    /**
    * 主键：
    */
    
    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name = "ID")
    private Integer ID;

    /**
    * 
    */
    @TableField(value = "FileName")
    @JSONField(name = "FileName")
    
    private String FileName;
    /**
    * 
    */
    @TableField(value = "FilePath")
    @JSONField(name = "FilePath")
    
    private String FilePath;
    /**
    * 
    */
    @TableField(value = "ProjectID")
    @JSONField(name = "ProjectID")
    
    private Integer ProjectID;
    /**
    * 
    */
    @TableField(value = "FunctionID")
    @JSONField(name = "FunctionID")
    
    private Integer FunctionID;
    /**
    * 
    */
    @TableField(value = "Type")
    @JSONField(name = "Type")
    
    private String Type;
    /**
    * 
    */
    @TableField(value = "hjID")
    @JSONField(name = "hjID")
    
    private String hjID;
    /**
    * 
    */
    @TableField(value = "UploadDate")
    @JSONField(name = "UploadDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date UploadDate;
    /**
    * 
    */
    @TableField(value = "IsSecret")
    @JSONField(name = "IsSecret")
    
    private Integer IsSecret;
    /**
    * 
    */
    @TableField(value = "PersonName")
    @JSONField(name = "PersonName")
    
    private String PersonName;
    /**
    * 
    */
    @TableField(value = "PersonZgh")
    @JSONField(name = "PersonZgh")
    
    private String PersonZgh;
    /**
    * 
    */
    @TableField(value = "SubTName")
    @JSONField(name = "SubTName")
    
    private String SubTName;
    /**
    * 
    */
    @TableField(value = "SubTID")
    @JSONField(name = "SubTID")
    
    private Integer SubTID;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
