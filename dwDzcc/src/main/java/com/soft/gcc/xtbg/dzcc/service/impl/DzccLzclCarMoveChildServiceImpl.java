package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.soft.gcc.xtbg.dzcc.domain.DzccLzclCarMoveChild;
import com.soft.gcc.xtbg.dzcc.mapper.DzccLzclCarMoveChildMapper;
import com.soft.gcc.xtbg.dzcc.service.IDzccLzclCarMoveChildService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car_Move_Child(车辆情况补充-子表)】的数据库操作Service实现
* @createDate 2023-08-03 16:45:56
*/
@Service
public class DzccLzclCarMoveChildServiceImpl extends ServiceImpl<DzccLzclCarMoveChildMapper, DzccLzclCarMoveChild>
    implements IDzccLzclCarMoveChildService {

}




