package com.soft.gcc.xtbg.dzcc.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarperson;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCdzgl;
import com.soft.gcc.xtbg.dzcc.domain.DzccVGroupItem;
import com.soft.gcc.xtbg.dzcc.entity.Cascader;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCdzglMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCarpersonService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCdzglService;
import com.soft.gcc.xtbg.dzcc.service.IDzccVGroupItemService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CDZGL(电子出车-车队长分配表)】的数据库操作Service实现
 * @createDate 2022-10-17 16:02:59
 */
@Service
public class DfdwDzccTCdzglServiceImpl extends ServiceImpl<DfdwDzccTCdzglMapper, DfdwDzccTCdzgl>
        implements IDfdwDzccTCdzglService {
    @Autowired
    IDzccVGroupItemService groupitemService;
    @Autowired
    IDfdwDzccTCarpersonService carpersonService;

    @Override
    public Result<Object> EditCdz(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        try {
            Integer personId = ParseUtil.tryParseInt(map.get("personId"));
            String personName = ParseUtil.tryParseString(map.get("personName"));
            String ids = ParseUtil.tryParseString(map.get("groupIds"));
            Integer type = ParseUtil.tryParseInt(map.get("personType"));
            List<Integer> selectGroupIds = new ArrayList<>();
            if (StringUtils.isNotEmpty(ids)) {
                String[] number = ids.split(",");
                Integer[] numbers = Convert.toIntArray(number);
                selectGroupIds = Arrays.asList(numbers);
            }
            List<DzccVGroupItem> groupItems = groupitemService.list(new LambdaQueryWrapper<DzccVGroupItem>()
                    .eq(DzccVGroupItem::getParentid, 1)
                    .eq(DzccVGroupItem::getCategory, 1)
            );
            List<Integer> groupIds = groupItems.stream().map(DzccVGroupItem::getId).collect(Collectors.toList());
            List<DfdwDzccTCdzgl> cdzglList = this.list(new LambdaQueryWrapper<DfdwDzccTCdzgl>()
                    .eq(DfdwDzccTCdzgl::getCdzId, personId)
                    .eq(DfdwDzccTCdzgl::getType, type)
            );
            List<Integer> deptIds = cdzglList.stream().map(DfdwDzccTCdzgl::getDeptId).collect(Collectors.toList());
            // 取可选车辆id和carperson表中车辆id的交集的补集
            List<Integer> idList = (List<Integer>) CollectionUtils.intersection(groupIds, deptIds);
            if (idList.size() > 0) {
                this.remove(new LambdaQueryWrapper<DfdwDzccTCdzgl>()
                        .in(DfdwDzccTCdzgl::getDeptId, idList)
                        .eq(DfdwDzccTCdzgl::getCdzId, personId)
                        .eq(DfdwDzccTCdzgl::getType, type)
                );
            }
            List<DfdwDzccTCdzgl> addList = new ArrayList<>();
            for (Integer id : selectGroupIds) {
                DfdwDzccTCdzgl cdzgl = new DfdwDzccTCdzgl();
                DzccVGroupItem groupItem = groupItems.stream().filter(t -> t.getId().equals(id)).findFirst().orElse(null);
                cdzgl.setDeptId(id);
                if (groupItem != null) {
                    cdzgl.setDeptName(groupItem.getTopGroupName());
                }
                cdzgl.setCdzId(personId);
                cdzgl.setCdzName(personName);
                cdzgl.setType(type);
                addList.add(cdzgl);
            }
            if (addList.size() > 0) {
                baseMapper.saveBatch(addList);
            }
            // 去除部门时把车队长或运监的车也删除（管理人员默认管理所有车不用加判断）
            carpersonService.remove(new LambdaQueryWrapper<DfdwDzccTCarperson>()
                    .eq(DfdwDzccTCarperson::getPersonId, personId)
                    .apply("carId NOT IN (SELECT Id FROM LZCL_T_Car WHERE groupid IN (SELECT deptId FROM DFDW_DZCC_T_CDZGL WHERE cdzId = " + personId + "))"));
            return Result.ok();
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetCDZGroupList(DzccPersonEntity dzccPersonEntity, Map<String, String> map) {
        try {
            int personId = ParseUtil.tryParseInt(map.get("personId"));
            int personType = ParseUtil.tryParseInt(map.get("personType"));
            List<DzccVGroupItem> groupItems = groupitemService.list(new LambdaQueryWrapper<DzccVGroupItem>()
                    .eq(DzccVGroupItem::getParentid, 1)
                    .eq(DzccVGroupItem::getCategory, 1)
                    .orderByAsc(DzccVGroupItem::getXH)
            );
            List<DfdwDzccTCdzgl> cdzglList = this.list(new LambdaQueryWrapper<DfdwDzccTCdzgl>()
                    .eq(DfdwDzccTCdzgl::getCdzId, personId)
                    .eq(DfdwDzccTCdzgl::getType, personType)
            );
            List<Integer> deptIds = cdzglList.stream().map(DfdwDzccTCdzgl::getDeptId).collect(Collectors.toList());
            List<Cascader> cascaderList = new ArrayList<>();
            for (DzccVGroupItem groupItem : groupItems) {
                Cascader cascader = new Cascader();
                cascader.setValue(groupItem.getId().toString());
                cascader.setLabel(groupItem.getGroupname());
                cascader.setChange(deptIds.contains(groupItem.getId()));
                cascaderList.add(cascader);
            }
            return Result.ok(cascaderList);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




