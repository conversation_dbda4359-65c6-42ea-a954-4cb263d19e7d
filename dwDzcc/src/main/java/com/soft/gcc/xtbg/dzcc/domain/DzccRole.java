package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
*
* @TableName Role
*/
@TableName(value ="Role", autoResultMap = true)
@Data
public class DzccRole implements Serializable {


    /**
    *
    */
    @TableId(value = "Id", type = IdType.ID_WORKER_STR)
    @JSONField(name = "Id")

    private Long Id;
    /**
    *
    */
    @TableField(value = "RoleName")
    @JSONField(name = "RoleName")

    private String RoleName;
    /**
    *
    */
    @TableField(value = "AdminGroupIds")
    @JSONField(name = "AdminGroupIds")

    private String AdminGroupIds;
    /**
    *
    */
    @TableField(value = "IsHide")
    @JSONField(name = "IsHide")

    private Boolean IsHide;
    /**
    *
    */
    @TableField(value = "RoleKind")
    @JSONField(name = "RoleKind")

    private String RoleKind;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
