package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCarpersonService;
import com.soft.gcc.xtbg.dzcc.service.IDzccGroupitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 人员车辆配置
 */
@RequestMapping("/dzcc/cdzpz")
@RestController
public class DzccCdzpzController extends DzccBaseController {
    @Autowired
    IDzccGroupitemService groupitemService;
    @Autowired
    IDfdwDzccTCarpersonService carpersonService;

    @RequestMapping("/GetGroupList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD04QX01')")
    public Result<Object> GetGroupList(@RequestParam Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.groupitemService.GetGroupList2(dzccPersonEntity, map);
    }

    @RequestMapping("/GetCDZPZList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD04QX01')")
    public Result<Object> GetCDZPZList(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.carpersonService.GetCDZPZList(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车队长配置-得到部门车辆列表
     * */
    @RequestMapping("/GetCarList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD04QX02')")
    public Result<Object> GetCarList(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.carpersonService.GetCarList(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车队长配置-编辑车队长配置
     * */
    @RequestMapping("/EditCDZPZ")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD04QX02')")
    public Result<Object> EditCDZPZ(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.carpersonService.EditCDZPZ(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车队长配置-得到部门列表
     * */
    @RequestMapping("/GetCDZGroup")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01CD04QX02,NDWCC01CD04QX03')")
    public Result<Object> GetCDZGroup(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.carpersonService.GetCDZGroup(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车队长配置-得到部门分配车辆列表
     * */
    @RequestMapping("/GetCarListForCdz")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01CD04QX02,NDWCC01CD04QX03')")
    public Result<Object> GetCarListForCdz(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.carpersonService.GetCarListForCdz(dzccPersonEntity, map);
    }
}
