package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

import lombok.Data;

/**
 * <AUTHOR>
 * 月份加班数据
 * @TableName DFDW_DZCC_T_CCLCDetail_Report
 */
@TableName(value = "DFDW_DZCC_T_CCLCDetail_Report")
@Data
public class DfdwDzccTCclcdetailReport implements Serializable {

    /**
     * 主键：
     */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
     * 司机Id
     */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
     * 年份
     */
    @TableField(value = "year")
    @JSONField(name = "year")

    private Integer year;
    /**
     * 月份
     */
    @TableField(value = "month")
    @JSONField(name = "month")

    private Integer month;
    /**
     * 补贴标准加班费(工作日|双休日|节假日)
     */
    @TableField(value = "subsidyStandardOvertime")
    @JSONField(name = "subsidyStandardOvertime")

    private String subsidyStandardOvertime;
    /**
     * 补贴标准延时(工作日|双休日|节假日)
     */
    @TableField(value = "subsidyStandardDelay")
    @JSONField(name = "subsidyStandardDelay")

    private String subsidyStandardDelay;
    /**
     * 夜餐金额补贴标准
     */
    @TableField(value = "subsidyStandardNight")
    @JSONField(name = "subsidyStandardNight")

    private String subsidyStandardNight;
    /**
     * 双休日加班天数
     */
    @TableField(value = "sxrOvertimeDays")
    @JSONField(name = "sxrOvertimeDays")

    private Integer sxrOvertimeDays;
    /**
     * 节假日加班天数
     */
    @TableField(value = "jjrOvertimeDays")
    @JSONField(name = "jjrOvertimeDays")

    private Integer jjrOvertimeDays;
    /**
     * 加班费合计
     */
    @TableField(value = "overtimeCost")
    @JSONField(name = "overtimeCost")

    private BigDecimal overtimeCost;
    /**
     * 工作日延时小时
     */
    @TableField(value = "gzrDelayHour")
    @JSONField(name = "gzrDelayHour")

    private BigDecimal gzrDelayHour;
    /**
     * 双休日延时小时
     */
    @TableField(value = "sxrDelayHour")
    @JSONField(name = "sxrDelayHour")

    private BigDecimal sxrDelayHour;
    /**
     * 节假日延时小时
     */
    @TableField(value = "jjrDelayHour")
    @JSONField(name = "jjrDelayHour")

    private BigDecimal jjrDelayHour;
    /**
     * 延时总津贴
     */
    @TableField(value = "delayCost")
    @JSONField(name = "delayCost")

    private BigDecimal delayCost;
    /**
     * 夜班天数
     */
    @TableField(value = "nightShiftDays")
    @JSONField(name = "nightShiftDays")

    private Integer nightShiftDays;
    /**
     * 夜班津贴
     */
    @TableField(value = "nightShiftCost")
    @JSONField(name = "nightShiftCost")

    private BigDecimal nightShiftCost;
    /**
     * 出差补贴
     */
    @TableField(value = "travelAllowance")
    @JSONField(name = "travelAllowance")

    private BigDecimal travelAllowance;
    /**
     * 合计
     */
    @TableField(value = "allCost")
    @JSONField(name = "allCost")

    private BigDecimal allCost;
    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private Integer topGroupId;

    @TableField(exist = false)
    private String topGroupName;
    @TableField(exist = false)
    private String realName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
