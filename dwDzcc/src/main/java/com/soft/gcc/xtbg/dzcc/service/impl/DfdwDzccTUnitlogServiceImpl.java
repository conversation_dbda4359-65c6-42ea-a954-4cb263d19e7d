package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTUnitlog;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTUnitlogService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTUnitlogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_UnitLog(办公和停车地点修改日志)】的数据库操作Service实现
* @createDate 2023-08-18 14:01:38
*/
@Service
public class DfdwDzccTUnitlogServiceImpl extends ServiceImpl<DfdwDzccTUnitlogMapper, DfdwDzccTUnitlog>
    implements IDfdwDzccTUnitlogService {

}




