package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 
* @TableName V_DFDW_DZCC_T_MILEAGE
*/
@TableName(value ="V_DFDW_DZCC_T_MILEAGE", autoResultMap = true)
@Data
public class VDfdwDzccTMileage implements Serializable {


    /**
    * 
    */
    @TableField(value = "id")
    @JSONField(name = "id")
    
    private Integer id;
    /**
    * 
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")
    
    private Integer carId;
    /**
    * 
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")
    
    private Integer driveId;
    /**
    * 
    */
    @TableField(value = "mileage")
    @JSONField(name = "mileage")
    
    private Integer mileage;
    /**
    * 
    */
    @TableField(value = "currDate")
    @JSONField(name = "currDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currDate;
    /**
    * 
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")
    
    private Integer groupId;
    /**
    * 
    */
    @TableField(value = "LicencePlate")
    @JSONField(name = "LicencePlate")
    
    private String LicencePlate;
    /**
    * 
    */
    @TableField(value = "RealName")
    @JSONField(name = "RealName")
    
    private String RealName;
    /**
    * 
    */
    @TableField(value = "groupname")
    @JSONField(name = "groupname")
    
    private String groupname;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
