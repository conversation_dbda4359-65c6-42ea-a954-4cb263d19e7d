package com.soft.gcc.xtbg.dzcc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.soft.gcc.xtbg.dzcc.domain.DzccBaseEntity;
import lombok.Data;

@Data
public class LocusDto extends DzccBaseEntity {
    @TableField(exist = false)
    private Integer carId;
    @TableField(exist = false)
    private String licensePlate;
    @TableField(exist = false)
    private Integer groupId;
    @TableField(exist = false)
    private String groupName;
    @TableField(exist = false)
    private Integer driverId;
    @TableField(exist = false)
    private String driverName;
    @TableField(exist = false)
    private Integer xinHao;
    @TableField(exist = false)
    private String carMold;

}
