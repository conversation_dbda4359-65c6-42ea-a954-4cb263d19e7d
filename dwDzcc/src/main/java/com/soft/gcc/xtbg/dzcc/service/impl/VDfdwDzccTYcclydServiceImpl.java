package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTYcclyd;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTYcclydMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTYcclydService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_YCCLYD】的数据库操作Service实现
* @createDate 2022-10-26 15:22:22
*/
@Service
public class VDfdwDzccTYcclydServiceImpl extends ServiceImpl<VDfdwDzccTYcclydMapper, VDfdwDzccTYcclyd>
    implements IVDfdwDzccTYcclydService{

    @Override
    public Result<Object> GetYccbydList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTYcclyd clyd) {
        try {
            IPage<VDfdwDzccTYcclyd> list = new Page<>();
            list.setCurrent(clyd.getPageNum());
            list.setSize(clyd.getPageSize());
            String sql = "";
            // 1总管理2车队长3其他
            if (dzccPersonEntity.getDzccQx() == 1) {
                sql = "";
            } else {
                sql = "(";
                if (dzccPersonEntity.getQxs().contains(2)) {
                    sql += "groupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 1) ";
                }
                if (dzccPersonEntity.getQxs().contains(4) || dzccPersonEntity.getQxs().contains(6)) {
                    if (dzccPersonEntity.getQxs().contains(2)) {
                        sql += " or ";
                    }
                    sql += "(groupId in (";
                    if (dzccPersonEntity.getQxs().contains(4) && dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type != 1";
                    } else if (dzccPersonEntity.getQxs().contains(4) && !dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 2";
                    } else if (!dzccPersonEntity.getQxs().contains(4) && dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 3";
                    }
                    sql += ") and carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = " + dzccPersonEntity.getId() + "))";
                }
                sql += ")";
            }
            if (clyd.getEndMoveTime() != null) {
                clyd.setEndMoveTime(new Date(clyd.getEndMoveTime().getTime() + 86400000));
            }
            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                list = this.page(list, new LambdaQueryWrapper<VDfdwDzccTYcclyd>()
                        .like(!"".equals(clyd.getLicensePlate()), VDfdwDzccTYcclyd::getLicensePlate, clyd.getLicensePlate())
                        .ge(clyd.getStartMoveTime() != null, VDfdwDzccTYcclyd::getCreateTime, clyd.getStartMoveTime())
                        .le(clyd.getEndMoveTime() != null, VDfdwDzccTYcclyd::getCreateTime, clyd.getEndMoveTime())
                        .apply(!"".equals(sql), sql)
                        .eq(clyd.getGroupId() > -1, VDfdwDzccTYcclyd::getGroupId, clyd.getGroupId())
                        .orderByDesc(VDfdwDzccTYcclyd::getCreateTime)
                );
            }
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




