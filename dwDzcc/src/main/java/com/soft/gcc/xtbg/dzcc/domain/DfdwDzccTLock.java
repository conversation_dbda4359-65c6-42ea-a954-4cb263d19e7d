package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 报表月份锁定
* @TableName DFDW_DZCC_T_LOCK
*/
@TableName(value ="DFDW_DZCC_T_LOCK", autoResultMap = true)
@Data
public class DfdwDzccTLock implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 部门id
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    * 年份
    */
    @TableField(value = "year")
    @JSONField(name = "year")

    private Integer year;
    /**
    * 月份
    */
    @TableField(value = "month")
    @JSONField(name = "month")

    private Integer month;
    /**
    * 日期
    */
    @TableField(value = "date")
    @JSONField(name = "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 修改时间
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 最后修改人id
    */
    @TableField(value = "editId")
    @JSONField(name = "editId")

    private Integer editId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
