package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTYcclyd;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTYcclydService;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 加班车辆信息
 */
@RequestMapping("/dzcc/ycclyd")
@RestController
public class DzccYcclydController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTYcclydService ycclydService;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 电子出车-PC-车辆移动-异常车辆移动查询
     * */
    @RequestMapping("/GetYccbydList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY01QX01')")
    public Result<Object> GetYccbydList(@RequestBody VDfdwDzccTYcclyd clyd) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.ycclydService.GetYccbydList(dzccPersonEntity, clyd);
    }

    /**
     * 电子出车-PC-车辆移动-导出
     * */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY01QX02')")
    public Result<Object> DownLoad(@RequestBody VDfdwDzccTYcclyd clyd, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<VDfdwDzccTYcclyd> list = new ArrayList<>();
            String sql = "";
            // 1总管理2车队长3其他
            if (dzccPersonEntity.getDzccQx() == 1) {
                sql = "";
            } else {
                sql = "(";
                if (dzccPersonEntity.getQxs().contains(2)) {
                    sql += "groupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 1) ";
                }
                if (dzccPersonEntity.getQxs().contains(4) || dzccPersonEntity.getQxs().contains(6)) {
                    if (dzccPersonEntity.getQxs().contains(2)) {
                        sql += " or ";
                    }
                    sql += "(groupId in (";
                    if (dzccPersonEntity.getQxs().contains(4) && dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type != 1";
                    } else if (dzccPersonEntity.getQxs().contains(4) && !dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 2";
                    } else if (!dzccPersonEntity.getQxs().contains(4) && dzccPersonEntity.getQxs().contains(6)) {
                        sql += "select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + " and type = 3";
                    }
                    sql += ") and carId in (select carId from DFDW_DZCC_T_CARPERSON where personId = " + dzccPersonEntity.getId() + "))";
                }
                sql += ")";
            }
            if (clyd.getEndMoveTime() != null) {
                clyd.setEndMoveTime(new Date(clyd.getEndMoveTime().getTime() + 86400000));
            }
            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                list = ycclydService.list(new LambdaQueryWrapper<VDfdwDzccTYcclyd>()
                        .like(!"".equals(clyd.getLicensePlate()), VDfdwDzccTYcclyd::getLicensePlate, clyd.getLicensePlate())
                        .ge(clyd.getStartMoveTime() != null, VDfdwDzccTYcclyd::getCreateTime, clyd.getStartMoveTime())
                        .le(clyd.getEndMoveTime() != null, VDfdwDzccTYcclyd::getCreateTime, clyd.getEndMoveTime())
                        .apply(!"".equals(sql), sql)
                        .eq(clyd.getGroupId() > -1, VDfdwDzccTYcclyd::getGroupId, clyd.getGroupId())
                        .orderByAsc(VDfdwDzccTYcclyd::getCreateTime)
                );
            }

            XSSFSheet sheet = workbook.createSheet("加班车辆信息");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(clyd.getGroupName() + "加班车辆信息");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("车型");
            row.createCell(1).setCellValue("车牌");
            row.createCell(2).setCellValue("部门");
            row.createCell(3).setCellValue("开始移动时间");
            row.createCell(4).setCellValue("最后移动时间");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getCarMold());
                row.createCell(1).setCellValue(list.get(i).getLicensePlate());
                row.createCell(2).setCellValue(list.get(i).getGroupName());
                row.createCell(3).setCellValue(dateFormat.format(list.get(i).getStartMoveTime()));
                row.createCell(4).setCellValue(dateFormat.format(list.get(i).getEndMoveTime()));
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + "加班车辆信息.xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return Result.ok();

        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
