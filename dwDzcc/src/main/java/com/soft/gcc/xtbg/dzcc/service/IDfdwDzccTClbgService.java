package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CLBG】的数据库操作Service
* @createDate 2022-10-19 14:44:13
*/
public interface IDfdwDzccTClbgService extends IService<DfdwDzccTClbg> {

    Result<Object> AddClbg(DzccPersonEntity person, DfdwDzccTClbg clbg);

    Result<Object> EditClbg(DzccPersonEntity person, DfdwDzccTClbg clbg);

    Result<Object> DeleteClbgById(Map<String, String> map);

    Result<Object> GetBGDList(DzccPersonEntity person, DfdwDzccTClbg clbg);

    Result<Object> GetUnitList(Map<String, String> map);

    List<DfdwDzccTClbg> GetBgdListForBackHaul(DzccPersonEntity person);

}
