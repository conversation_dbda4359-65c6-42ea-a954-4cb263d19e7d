package com.soft.gcc.xtbg.dzcc.service;

import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclcStatusDaily;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCLC_STATUS_DAILY(电子出车-出车单-每日出车情况)】的数据库操作Service
* @createDate 2024-10-15 15:43:10
*/
public interface IDfdwDzccTCclcStatusDailyService extends IService<DfdwDzccTCclcStatusDaily> {

    List<DfdwDzccTCclcStatusDaily> getCcdStatusDaily(DfdwDzccTCclcStatusDaily cclcStatusDaily);

    void calcStatusDaily(Integer lcId);
}
