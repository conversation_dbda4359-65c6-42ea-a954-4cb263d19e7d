package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriverinfo;
import com.soft.gcc.xtbg.base.controller.Result;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_DRIVERINFO(司机准假资质信息)】的数据库操作Service
* @createDate 2023-07-27 09:36:19
*/
public interface IDfdwDzccTDriverinfoService extends IService<DfdwDzccTDriverinfo> {

    Result<Object> GetList(DzccPersonEntity person, DfdwDzccTDriveremploy employ,Boolean exportFlag);

    Result<Object> GetDict();

    Result<Object> GetJsyxxList(DzccPersonEntity person, DfdwDzccTDriverinfo info);

    Result<Object> AddJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info);

    Result<Object> EditJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info);

    Result<Object> DeleteJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info);

    Result<Object> UploadLoadJsyxx(DzccPersonEntity dzccPersonEntity, MultipartFile file);

    Result<Object> DownLoadJsyxx(DzccPersonEntity person, DfdwDzccTDriverinfo info, HttpServletResponse response);
}
