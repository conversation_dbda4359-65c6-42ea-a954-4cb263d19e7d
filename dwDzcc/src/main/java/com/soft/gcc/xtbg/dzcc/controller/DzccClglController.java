package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTClbgService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTClglService;
import com.soft.gcc.xtbg.dzcc.service.IDzccGroupitemService;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("/dzcc/clgl")
@RestController
public class DzccClglController extends DzccBaseController {
    @Autowired
    IDzccGroupitemService groupitemService;
    @Autowired
    IDfdwDzccTClglService clglService;
    @Autowired
    IDfdwDzccTClbgService clbgService;

    /**
     * 电子出车-PC-车辆管理-车辆管理查询
     * */
    @RequestMapping("/GetGroupList")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-电子出车-车辆总管理,协同办公-电子出车-车辆管理人员,协同办公-电子出车-车辆调度人员,协同办公-电子出车-车辆运监人员')")
    public Result<Object> GetGroupList(@RequestParam Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.groupitemService.GetGroupList(dzccPersonEntity, map);
    }

    @RequestMapping("/GetGroupList12")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-电子出车-车辆总管理,协同办公-电子出车-车辆管理人员,协同办公-电子出车-车辆调度人员,协同办公-电子出车-车辆运监人员')")
    public Result<Object> GetGroupList12(@RequestParam Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.groupitemService.GetGroupList12(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车辆管理-车辆管理查询
     * */
    @RequestMapping("/GetCLGLList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX04')")
    public Result<Object> GetCLGLList(@RequestBody DfdwDzccTClgl clgl) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clglService.GetCLGLList(dzccPersonEntity, clgl);
    }

    /**
     * 电子出车-PC-车辆管理-租赁时间查询
     * */
    @RequestMapping("/GetRentList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX04')")
    public Result<Object> GetRentList(@RequestBody DfdwDzccTClgl clgl) {
        return this.clglService.GetRentList(clgl);
    }

    /**
     * 电子出车-PC-车辆管理-车辆管理细节表查询
     * */
    @RequestMapping("/GetDetailList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX04')")
    public Result<Object> GetDetailList(@RequestBody Map<String, String> map) {
        return this.clglService.GetDetailList(map);
    }

    /**
     * 电子出车-PC-车辆管理-驾驶员查询
     * */
    @RequestMapping("/GetDriverList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX04')")
    public Result<Object> GetDriverList(@RequestBody Map<String, String> map) {
        return this.clglService.GetDriverList(map);
    }

    /**
     * 电子出车-PC-车辆管理-添加车辆驾驶员
     * */
    @RequestMapping("/AddClglDetail")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX01')")
    public Result<Object> AddClglDetail(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clglService.AddClglDetail(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车辆管理-修改车辆驾驶员
     * */
    @RequestMapping("/EditClglDetail")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX02')")
    public Result<Object> EditClglDetail(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clglService.EditClglDetail(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车辆管理-删除车辆驾驶员
     * */
    @RequestMapping("/DeleteClglDetail")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX03')")
    public Result<Object> DeleteClglDetail(@RequestParam Map<String, String> map) {
        int id = ParseUtil.tryParseInt(map.get("id"));
        return this.clglService.DeleteClglDetail(id);
    }

    /**
     * 电子出车-PC-车辆管理-办公地点查询
     * */
    @RequestMapping("/GetUnitList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX04')")
    public Result<Object> GetUnitList(@RequestBody Map<String, String> map) {
        return this.clbgService.GetUnitList(map);
    }

    /**
     * 电子出车-PC-车辆管理-修改办公地点
     * */
    @RequestMapping("/EditUnit")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX05')")
    public Result<Object> EditUnit(@RequestBody Map<String, String> map) {
        return this.clglService.EditUnit(map);
    }

    /**
     * 电子出车-PC-车辆管理-修改停车地点
     * */
    @RequestMapping("/EditStop")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL01QX06')")
    public Result<Object> EditStop(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clglService.EditStop(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车辆管理-查询重复车辆
     * */
    @RequestMapping("/SelectRepeatCar")
    @PreAuthorize("@ss.hasRole('协同办公-电子出车-车辆总管理')")
    public Result<Object> selectRepeatCar(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clglService.SelectRepeatCar(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-车辆管理-删除
     * */
    @RequestMapping("/DeleteClgl")
    @PreAuthorize("@ss.hasRole('协同办公-电子出车-车辆总管理')")
    public Result<Object> deleteClgl(@RequestBody Map<String, String> map) {
        Integer id = ParseUtil.tryParseInt(map.get("id"));
        this.clglService.DeleteClgl(id);
        return Result.ok();
    }
}
