package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.entity.JtsjDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024-12-12 10:48:31
 */
public interface IDfdwDzccJtsjService extends IService<DfdwDzccTWeek> {

    /**
     * 获取司机列表
     * @return
     */
    Result<Object> GetDriverList(JtsjDto jtsjDto);

    /**
     * 获取司机工作量清单
     * @param jtsjDto
     * @return
     */
    Result<Object> getSjqx(JtsjDto jtsjDto);

    Result<Object> downJsyqdByIds(JtsjDto jtsjDto, DzccPersonEntity user, HttpServletResponse response);
}
