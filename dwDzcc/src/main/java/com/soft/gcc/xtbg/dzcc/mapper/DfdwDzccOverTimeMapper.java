package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTOverTime;
import org.apache.ibatis.annotations.Param;

/**
* @description 针对表【DFDW_DZCC_T_OVERTIME_SET(加班费计算规则配置表】的数据库操作Mapper
*/
public interface DfdwDzccOverTimeMapper extends BaseMapper<DfdwDzccTOverTime> {

    IPage<DfdwDzccTOverTime> selectOverTimePage(Page<DfdwDzccTOverTime> pageParam,@Param("overTime") DfdwDzccTOverTime overTime);
}




