package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CLGL(电子出车-车辆管理)】的数据库操作Service
* @createDate 2022-10-19 14:11:29
*/
public interface IDfdwDzccTClglService extends IService<DfdwDzccTClgl> {
    Result<Object> GetCLGLList(DzccPersonEntity person, DfdwDzccTClgl clgl);

    Result<Object> GetRentList(DfdwDzccTClgl clgl);

    Result<Object> GetDetailList(Map<String, String> map);

    Result<Object> GetDriverList(Map<String, String> map);

    Result<Object> AddClglDetail(DzccPersonEntity person, Map<String, String> map);

    Result<Object> EditClglDetail(DzccPersonEntity person, Map<String, String> map);

    Result<Object> DeleteClglDetail(int id);

    Result<Object> EditUnit(Map<String, String> map);

    Result<Object> EditStop(DzccPersonEntity dzccPersonEntity, Map<String, String> map);

    Result<Object> SelectRepeatCar(DzccPersonEntity dzccPersonEntity, Map<String, String> map);

    void DeleteClgl(Integer id);
}
