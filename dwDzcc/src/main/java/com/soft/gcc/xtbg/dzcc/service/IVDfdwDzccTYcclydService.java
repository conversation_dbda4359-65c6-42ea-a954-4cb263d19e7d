package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTYcclyd;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_YCCLYD】的数据库操作Service
* @createDate 2022-10-26 15:22:22
*/
public interface IVDfdwDzccTYcclydService extends IService<VDfdwDzccTYcclyd> {

    Result<Object> GetYccbydList(DzccPersonEntity person, VDfdwDzccTYcclyd clyd);
}
