package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DzccPerson;
import com.soft.gcc.xtbg.dzcc.domain.DzccRole;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Person】的数据库操作Mapper
 * @createDate 2022-10-20 09:30:31
 * @Entity com.yykj.app.dzcc.domain.DzccPerson
 */
public interface DzccPersonMapper extends BaseMapper<DzccPerson> {

    List<DzccPerson> GetDriverList(@Param("groupId") Integer groupId);

    List<DzccRole> GetPersonRoleById(@Param("id") Integer id);

    List<DzccVPerson> GetPersonByID(@Param("certificateID") String certificateID);

    void insertSjRole(@Param("id") Integer id);

    void deleteSjRole(@Param("id") Integer id);
}




