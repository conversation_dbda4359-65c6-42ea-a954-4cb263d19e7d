package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.entity.JtsjDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_WEEK(电子出车-周期（延时申请）)】的数据库操作Mapper
* @createDate 2024-12-13 17:02:36
* @Entity com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek
*/
public interface DfdwDzccTWeekMapper extends BaseMapper<DfdwDzccTWeek> {

    IPage<DfdwDzccTWeek> listPage(@Param("page") Page<DfdwDzccTWeek> page, @Param("query") DfdwDzccTWeek dfdwDzccTWeek);
    List<DfdwDzccTWeek> getList(@Param("query") DfdwDzccTWeek dfdwDzccTWeek);

    /**
     * 查询是否具有某个角色
     */
    Integer hasRole(@Param("roleName") String roleName ,@Param("personId") Integer personId);

    IPage<DfdwDzccTWeek> getDriverList(@Param("page") IPage<DfdwDzccTWeek> page, @Param("query") DfdwDzccTWeek dfdwDzccTWeek);

    /**
     *  获取当月的司机id
     * @return
     */
    List<Integer> getCurrentMonthDriverId(@Param("year") int year,@Param("month") int month);

    /**
     * 获取周期下拉框 （根据年份+月份获取
     * @param dfdwDzccTWeek 搜索条件
     * @return List<DfdwDzccTWeekMain>
     */
    List<DfdwDzccTWeekMain> getWeeks(DfdwDzccTWeek dfdwDzccTWeek);
}




