package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 电子出车单-评价
 * @TableName DFDW_DZCC_T_PJ
 */
@TableName(value ="DFDW_DZCC_T_PJ")
@Data
public class DfdwDzccTPj extends DzccBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * lc主键Id
     */
    @TableField(value = "lcId")
    private Integer lcId;

    /**
     * 星级评价
     */
    @TableField(value = "rate")
    private Integer rate;

    /**
     * 是否整洁（0否 1是）
     */
    @TableField(value = "zjState")
    private Integer zjState;

    /**
     * 是否安全驾驶（0否 1是）
     */
    @TableField(value = "aqjsState")
    private Integer aqjsState;

    /**
     * 是否准时到达（0否 1是）
     */
    @TableField(value = "pdkzState")
    private Integer pdkzState;

    /**
     * 服务是否满意（0否 1是）
     */
    @TableField(value = "tsState")
    private Integer tsState;

    /**
     * 投诉内容
     */
    @TableField(value = "tsText")
    private String tsText;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    @TableField(exist = false)
    private String applyNo;

    @TableField(exist = false)
    private LocalDateTime ccOpenTime;

    @TableField(exist = false)
    private String licencePlate;

    @TableField(exist = false)
    private String driveName;

    @TableField(exist = false)
    private String ycrName;

    @TableField(exist = false)
    private Integer applyTopGroupId;

    @TableField(exist = false)
    private String applyTopDeptName;

    @TableField(exist = false)
    private Boolean hasTs;

    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private List<LocalDate> time;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}