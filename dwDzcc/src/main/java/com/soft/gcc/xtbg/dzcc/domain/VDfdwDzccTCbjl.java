package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_CBJL
*/
@TableName(value ="V_DFDW_DZCC_T_CBJL", autoResultMap = true)
@Data
public class VDfdwDzccTCbjl extends DzccBaseEntity implements Serializable {


    /**
    *
    */
    @TableField(value = "Id")
    @JSONField(name = "Id")

    private Integer Id;
    /**
    *
    */
    @TableField(value = "DeviceId")
    @JSONField(name = "DeviceId")

    private String DeviceId;
    /**
    *
    */
    @TableField(value = "Type")
    @JSONField(name = "Type")

    private Integer Type;
    /**
    *
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    *
    */
    @TableField(value = "StartTime")
    @JSONField(name = "StartTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date StartTime;
    /**
    *
    */
    @TableField(value = "EndTime")
    @JSONField(name = "EndTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date EndTime;
    /**
    *
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    *
    */
    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")

    private String licensePlate;
    /**
    *
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")

    private String groupName;
    /**
    *
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
     *
     */
    @TableField(value = "carMold")
    @JSONField(name = "carMold")

    private String carMold;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
