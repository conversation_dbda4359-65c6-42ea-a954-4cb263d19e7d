package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTZbb;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_ZBB(主报表（查询当月信息）)】的数据库操作Mapper
* @createDate 2022-11-02 13:28:33
* @Entity com.yykj.app.dzcc.domain.VDfdwDzccTZbb
*/
public interface VDfdwDzccTZbbMapper extends BaseMapper<VDfdwDzccTZbb> {

    List<VDfdwDzccTZbb> GetZbbList(
            @Param("startDate") String format,
            @Param("endDate") String format1,
            @Param("groupIds") List<Integer> topGroupId,
            @Param("zbb") VDfdwDzccTZbb zbb,
            @Param("person") DzccPersonEntity person);
}




