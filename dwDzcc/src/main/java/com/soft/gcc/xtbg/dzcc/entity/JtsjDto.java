package com.soft.gcc.xtbg.dzcc.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.dzcc.domain.DzccBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 集团司机信息 -出车明细
 * @date 2024-12-12 10:50:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JtsjDto extends DzccBaseEntity implements Serializable  {

    /**
     * 司机Id
     */
    private Integer driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 车Id
     */
    private Integer carId;

    /**
     * 车牌
     */
    private String licencePlate;

    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate moveDate;

    /**
     * 星期
     */
    private String week;


    /**
     * 出车地点
     */
    private String concatenatedAreaText;

    /**
     * 延时
     */
    private BigDecimal overtimeHoursAll;

    /**
     * 开始日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * Id列表
     */
    private List<Integer> ids;

    /**
     * 司机Id列表
     */
    private List<Integer> driverIds;

    /**
     * 月周期列表
     */
    private List<Integer> monthlyCycles;

    /**
     * 下载类型
     */
    private String downloadType;
}
