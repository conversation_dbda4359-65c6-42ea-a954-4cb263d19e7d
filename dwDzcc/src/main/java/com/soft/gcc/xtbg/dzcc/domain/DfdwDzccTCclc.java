package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车单LC
* @TableName DFDW_DZCC_T_CCLC
*/
@TableName(value ="DFDW_DZCC_T_CCLC", autoResultMap = true)
@Data
public class DfdwDzccTCclc extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 项目编号
    */
    @TableField(value = "applyNo")
    @JSONField(name = "applyNo")

    private String applyNo;
    /**
    * 申请人Id
    */
    @TableField(value = "applyUserId")
    @JSONField(name = "applyUserId")

    private Integer applyUserId;
    /**
    * 申请人名称
    */
    @TableField(value = "applyUserName")
    @JSONField(name = "applyUserName")

    private String applyUserName;
    /**
    * 申请人电话
    */
    @TableField(value = "applyPhone")
    @JSONField(name = "applyPhone")

    private String applyPhone;
    /**
    * 申请人单位Id
    */
    @TableField(value = "applyDeptId")
    @JSONField(name = "applyDeptId")

    private Integer applyDeptId;
    /**
    * 申请人单位名称
    */
    @TableField(value = "applyDeptName")
    @JSONField(name = "applyDeptName")

    private String applyDeptName;
    /**
    * 人数
    */
    @TableField(value = "applyNum")
    @JSONField(name = "applyNum")

    private Integer applyNum;
    /**
    * 出车类型 （0现在 1时间点 ）
    */
    @TableField(value = "ccType")
    @JSONField(name = "ccType")

    private Integer ccType;
    /**
    * 出车时间
    */
    @TableField(value = "ccOpenTime")
    @JSONField(name = "ccOpenTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ccOpenTime;
    /**
    * 出行是由
    */
    @TableField(value = "note")
    @JSONField(name = "note")

    private String note;
    /**
    * 创建时间（申请时间）
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")

    private Integer approveState;
    /**
    * 执行状态（0未派车 1已派车，2已到达，3派车撤回,4已取消，5车辆异常）
    */
    @TableField(value = "executeState")
    @JSONField(name = "executeState")

    private Integer executeState;
    /**
    * 执行已到达时间
    */
    @TableField(value = "executeTime")
    @JSONField(name = "executeTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeTime;
    /**
    * 主流程ID,用作用户增加目的地重新走流程
    */
    @TableField(value = "mainId")
    @JSONField(name = "mainId")

    private Integer mainId;
    /**
    * 出车天数
    */
    @TableField(value = "ccDays")
    @JSONField(name = "ccDays")

    private Integer ccDays;
    /**
    * 目的地冗余字段
    */
    @TableField(value = "addressInfo")
    @JSONField(name = "addressInfo")

    private String addressInfo;
    /**
    * 上车点
    */
    @TableField(value = "scdText")
    @JSONField(name = "scdText")

    private String scdText;
    /**
    * 申请人TopdepId
    */
    @TableField(value = "applyTopDeptId")
    @JSONField(name = "applyTopDeptId")

    private Integer applyTopDeptId;
    /**
    * 申请人TopdepName
    */
    @TableField(value = "applyTopDeptName")
    @JSONField(name = "applyTopDeptName")

    private String applyTopDeptName;
    /**
    * 出车结束时间
    */
    @TableField(value = "ccEndTime")
    @JSONField(name = "ccEndTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ccEndTime;
    /**
    * 派车Id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 司机Id
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
    * 车队长id-审批人
    */
    @TableField(value = "cdzPersonId")
    @JSONField(name = "cdzPersonId")

    private Integer cdzPersonId;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @JSONField(name = "remark")
    private String remark;

    /**
     * 用车人Id
     */
    @TableField(value = "ycrId")
    @JSONField(name = "ycrId")
    private Integer ycrId;

    /**
     * 用车人名称
     */
    @TableField(value = "ycrName")
    @JSONField(name = "ycrName")
    private String ycrName;

    /**
     * 开始时间（0上午1下午）
     */
    @TableField(value = "beginType")
    @JSONField(name = "beginType")
    private Integer beginType;

    /**
     * 开始时间
     */
    @TableField(value = "openTime")
    @JSONField(name = "openTime")
    private String openTime;

    /**
     * 结束时间
     */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    private String endTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    //实际开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date actualStartTime;

    //实际结束实际
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    private Date actualEndTime;


    @TableField(exist = false)
    private String licencePlate;

    @TableField(exist = false)
    private String driverName;
    @TableField(exist = false)
    private String driverPhone;
    //车辆类型
    @TableField(exist = false)
    private String carMold;
    //出车情况
    @TableField(exist = false)
    private String status;
}
