package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTDriveremploy;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTDriveremployMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTDriveremployService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_DRIVEREMPLOY(司机聘用表)】的数据库操作Service实现
* @createDate 2023-07-27 10:32:23
*/
@Service
public class DfdwDzccTDriveremployServiceImpl extends ServiceImpl<DfdwDzccTDriveremployMapper, DfdwDzccTDriveremploy>
    implements IDfdwDzccTDriveremployService {
    private static final Logger log = LoggerFactory.getLogger(DfdwDzccTDriverinfoServiceImpl.class);
    @Override
    public Result<Object> AddEmployPerson(DzccPersonEntity dzccPersonEntity, DfdwDzccTDriveremploy emoloy) {
        try {
            // 结束时间为空，默认一个很大的年份，后面在赋值回空
            LocalDate date = LocalDate.of(2199,1,1);
            if (emoloy.getEndDate() == null) {
                emoloy.setEndDate(date);
            }
            List<DfdwDzccTDriveremploy> list = baseMapper.getListInTime(0, emoloy.getPersonId(), emoloy.getStartDate(), emoloy.getEndDate());
            if (list.size() > 0) {
                return Result.error("人员该时间段内已被聘用！");
            }
            if (emoloy.getEndDate() == date) {
                emoloy.setEndDate(null);
                List<DfdwDzccTDriveremploy> employs = this.list(new LambdaQueryWrapper<DfdwDzccTDriveremploy>()
                        .eq(DfdwDzccTDriveremploy::getPersonId, emoloy.getPersonId())
                        .lt(DfdwDzccTDriveremploy::getStartDate, emoloy.getStartDate())
                        .isNull(DfdwDzccTDriveremploy::getEndDate));
                for (DfdwDzccTDriveremploy employee : employs) {
                    LocalDate endDate = employee.getEndDate().plusDays(-1);
                    employee.setEndDate(endDate);
                    this.updateById(employee);
                }
            }
            this.save(emoloy);
            return Result.ok();
        } catch (Exception e) {
            log.error("电子出车-信息填报-聘用信息-聘用新增报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> GetEmployList(DzccPersonEntity dzccPersonEntity, DfdwDzccTDriveremploy emoloy) {
        try {
            List<DfdwDzccTDriveremploy> list = baseMapper.GetEmployList(emoloy.getPersonId());
            return Result.ok(list);
        } catch (Exception e) {
            log.error("电子出车-信息填报-聘用信息-聘用查看：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> EditEmployPerson(DzccPersonEntity dzccPersonEntity, DfdwDzccTDriveremploy emoloy) {
        try {
            LocalDate date = LocalDate.of(2199,1,1);
            if (emoloy.getEndDate() == null) {
                emoloy.setEndDate(date);
            }
            List<DfdwDzccTDriveremploy> list = baseMapper.getListInTime(emoloy.getId(), emoloy.getPersonId(), emoloy.getStartDate(), emoloy.getEndDate());
            if (list.size() > 0) {
                return Result.error("人员该时间段内已被聘用！");
            }
            if (emoloy.getEndDate() == date) {
                emoloy.setEndDate(null);
            }
            this.updateById(emoloy);
            return Result.ok();
        } catch (Exception e) {
            log.error("电子出车-信息填报-聘用信息-聘用修改报错：{}", e.getMessage());
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }
}




