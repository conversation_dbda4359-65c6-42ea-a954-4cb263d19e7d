package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmovelc;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CARMOVELC(节假日或者平时9点以后车辆移动Lc)】的数据库操作Mapper
* @createDate 2022-11-14 09:46:29
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTCarmovelc
*/
public interface DfdwDzccTCarmovelcMapper extends BaseMapper<DfdwDzccTCarmovelc> {

    List<DfdwDzccTCarmovelc> getList(
            @Param("person") DzccPersonEntity person,
            @Param("ent") DfdwDzccTCarmovelc ent);

    IPage<DfdwDzccTCarmovelc> getYccllcPage(
            IPage<DfdwDzccTCarmovelc> list,
            @Param("person") DzccPersonEntity person,
            @Param("ent") DfdwDzccTCarmovelc ent);
}




