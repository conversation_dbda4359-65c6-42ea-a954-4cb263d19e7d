package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* 
* @TableName DictionaryValue
*/
@TableName(value ="DictionaryValue", autoResultMap = true)
@Data
public class DzccDictionaryvalue implements Serializable {


    /**
    * 
    */
    @TableField(value = "ID")
    @JSONField(name = "ID")
    
    private Integer ID;
    /**
    * 
    */
    @TableField(value = "TitleID")
    @JSONField(name = "TitleID")
    
    private Integer TitleID;
    /**
    * 
    */
    @TableField(value = "Content")
    @JSONField(name = "Content")
    
    private String Content;
    /**
    * 
    */
    @TableField(value = "Parameter")
    @JSONField(name = "Parameter")
    
    private String Parameter;
    /**
    * 
    */
    @TableField(value = "IsUsed")
    @JSONField(name = "IsUsed")
    
    private Integer IsUsed;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
