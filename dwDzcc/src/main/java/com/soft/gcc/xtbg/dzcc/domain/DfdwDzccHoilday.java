package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 节假日
* @TableName DFDW_DZCC_HOILDAY
*/
@TableName(value ="DFDW_DZCC_HOILDAY")
@Data
public class DfdwDzccHoilday extends DzccBaseEntity  implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 开始时间
    */
    @TableField(value = "startDate")
    @JSONField(name = "startDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date startDate;
    /**
    * 结束时间
    */
    @TableField(value = "endDate")
    @JSONField(name = "endDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 创建人Id
    */
    @TableField(value = "createUserId")
    @JSONField(name = "createUserId")

    private Integer createUserId;
    /**
    * 创建人名称
    */
    @TableField(value = "createUserName")
    @JSONField(name = "createUserName")

    private String createUserName;

    @JSONField(name = "time")
    @TableField(exist = false)
    private String time;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
