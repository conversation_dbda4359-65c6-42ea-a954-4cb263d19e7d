package com.soft.gcc.xtbg.dzcc.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;
import com.soft.gcc.xtbg.dzcc.entity.JtsjDto;
import com.soft.gcc.xtbg.dzcc.entity.SjInfoDto;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTWeekMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCclcdetailMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccJtsjService;
import com.soft.gcc.xtbg.dzcc.util.PdfUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @description 集团司机清单
 * @date 2024-12-12 10:48:48
 */
@Service
public class DfdwDzccJtsjServiceImpl extends ServiceImpl<DfdwDzccTWeekMapper, DfdwDzccTWeek> implements IDfdwDzccJtsjService {
    private static final Logger log = LoggerFactory.getLogger(DfdwDzccJtsjServiceImpl.class);
    @Autowired
    private VDfdwDzccTCclcdetailMapper vDfdwDzccTCclcdetailMapper;
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月");
    private final DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
    private final DateTimeFormatter formatter3 = DateTimeFormatter.ofPattern("MM月dd日");

    /**
     * 获取司机列表
     * @return
     */
    @Override
    public Result<Object> GetDriverList(JtsjDto jtsjDto) {
        IPage<DfdwDzccTWeek> dfdwDzccTWeekList = new Page<>();
        dfdwDzccTWeekList.setCurrent(jtsjDto.getPageNum());
        dfdwDzccTWeekList.setSize(jtsjDto.getPageSize());
        DfdwDzccTWeek dfdwDzccTWeek = new DfdwDzccTWeek();
        dfdwDzccTWeek.setDriveName(jtsjDto.getDriverName());
        dfdwDzccTWeek.setYear(jtsjDto.getStartDate().getYear());
        dfdwDzccTWeek.setMonth(jtsjDto.getStartDate().getMonthValue());
        dfdwDzccTWeek.setWeekList(jtsjDto.getMonthlyCycles());
        dfdwDzccTWeekList = baseMapper.getDriverList(dfdwDzccTWeekList, dfdwDzccTWeek);
        return Result.ok(dfdwDzccTWeekList);
    }


    @Override
    public Result<Object> getSjqx(JtsjDto jtsjDto) {
        List<JtsjDto> list = vDfdwDzccTCclcdetailMapper.getSjqx(jtsjDto);
        List<DzccVPerson> vPersonList = vDfdwDzccTCclcdetailMapper.GetDriverList(jtsjDto);
        List<DfdwDzccTWeek> dfdwDzccTWeekList = this.list(new LambdaQueryWrapper<DfdwDzccTWeek>()
                .eq(DfdwDzccTWeek::getYear, jtsjDto.getStartDate().getYear())
                .eq(DfdwDzccTWeek::getMonth, jtsjDto.getStartDate().getMonthValue())
                .in(DfdwDzccTWeek::getWeek, jtsjDto.getMonthlyCycles())
                .eq(DfdwDzccTWeek::getDriveId, jtsjDto.getDriverId())
        );
        LocalDate startDate;
        LocalDate endDate;
        if (!dfdwDzccTWeekList.isEmpty()) {
            startDate = dfdwDzccTWeekList.get(0).getStartTime();
            endDate = dfdwDzccTWeekList.get(0).getEndTime();
        } else {
            startDate = jtsjDto.getStartDate();
            endDate = jtsjDto.getEndDate();
        }
        list = list.stream().filter(jtsj -> !jtsj.getMoveDate().isBefore(startDate) && !jtsj.getMoveDate().isAfter(endDate)).collect(Collectors.toList());

        JtsjDto driverParam = new JtsjDto();
        driverParam.setStartDate(startDate);
        driverParam.setEndDate(endDate.plusDays(1));
        driverParam.setDriverId(jtsjDto.getDriverId());
        List<JtsjDto> driverList = vDfdwDzccTCclcdetailMapper.getSjqdForMonth(driverParam);

        jtsjDto.setStartDate(startDate);
        jtsjDto.setEndDate(endDate);
        String licencePlate = "";
        if (!list.isEmpty()) {
            licencePlate = String.join("、", list.stream().map(JtsjDto::getLicencePlate).collect(Collectors.toSet()));
        } else if (!driverList.isEmpty()) {
            licencePlate = String.join("、", driverList.stream()
                    .filter(jtsj -> !jtsj.getStartDate().isAfter(startDate))
                    .map(JtsjDto::getLicencePlate)
                    .collect(Collectors.toSet()));
        }

        SjInfoDto sjInfoDto = new SjInfoDto();
        sjInfoDto.setDriverName(vPersonList.get(0).getRealName());
        sjInfoDto.setWeek(startDate.format(formatter2) + "-" +endDate.format(formatter2));
        sjInfoDto.setLicencePlate(licencePlate);
        sjInfoDto.setOvertimeHours(BigDecimal.ZERO);

        jtsjDto.setStartDate(startDate);
        jtsjDto.setEndDate(endDate);

        List<JtsjDto> result = new ArrayList<>();
        BigDecimal overtimeHoursAllSum = BigDecimal.ZERO;
        for (LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusDays(1)) {
            LocalDate finalDate = date;
            List<JtsjDto> jtsjList = list.stream().filter(jtsj ->jtsj.getMoveDate().equals(finalDate)).collect(Collectors.toList());
            String concatenatedAreaText = jtsjList.stream().map(JtsjDto::getConcatenatedAreaText).collect(Collectors.joining(",")).replace(" ", "");
            BigDecimal overtimeHoursAll = jtsjList.stream().map(JtsjDto::getOvertimeHoursAll).findFirst().orElse(null);
            if (overtimeHoursAll != null) {
                overtimeHoursAllSum = overtimeHoursAllSum.add(overtimeHoursAll);
            }

            JtsjDto jtsjDto1 = new JtsjDto();
            jtsjDto1.setMoveDate(date);
            jtsjDto1.setConcatenatedAreaText(concatenatedAreaText);
            jtsjDto1.setOvertimeHoursAll(overtimeHoursAll);
            jtsjDto1.setWeek(PdfUtil.weekToChinese(date.getDayOfWeek().getValue()));
            result.add(jtsjDto1);
        }
        sjInfoDto.setOvertimeHours(overtimeHoursAllSum);
        if (!dfdwDzccTWeekList.isEmpty()) {
            if (dfdwDzccTWeekList.get(0).getApproveState() == 2 && dfdwDzccTWeekList.get(0).getAppendDelayHour() != null) {
                overtimeHoursAllSum = overtimeHoursAllSum.add(dfdwDzccTWeekList.get(0).getAppendDelayHour());
                sjInfoDto.setOvertimeHoursApp(dfdwDzccTWeekList.get(0).getAppendDelayHour());
            }
        }
        sjInfoDto.setOvertimeHoursAll(overtimeHoursAllSum);
        sjInfoDto.setJtsjDtos(result);
        return Result.ok(sjInfoDto);
    }

    @Override
    public Result<Object> downJsyqdByIds(JtsjDto jtsjDto, DzccPersonEntity user, HttpServletResponse response) {
        log.info("用户{}开始生成pdf, id{}", user.getRealName(), user.getId());
        //生成并上传pdf
        String localPath = System.getProperty("java.io.tmpdir");
        //临时文件目录
        List<String> pathList = new ArrayList<>();
        try (ServletOutputStream servletOutputStream = response.getOutputStream()) {
            // 文件路径
            File file = new File(localPath + "/yykj/dzcc/downloads/jtsjqx/");
            if (!file.exists()) {
                file.mkdirs();
            }
            // 水印文件路径
            file = new File(localPath + "/yykj/dzcc/downloads/water/jtsjqx/");
            if (!file.exists()) {
                file.mkdirs();
            }
            // log.info("用户{}, id{}, 文件路径检查完毕", user.getRealName(), user.getId());
            List<JtsjDto> list = vDfdwDzccTCclcdetailMapper.getSjqx(jtsjDto);
            List<DzccVPerson> vPersonList = vDfdwDzccTCclcdetailMapper.GetDriverList(jtsjDto);
            List<DfdwDzccTWeek> weekList = this.list(new LambdaQueryWrapper<DfdwDzccTWeek>()
                    .in("pdf".equals(jtsjDto.getDownloadType()), DfdwDzccTWeek::getDriveId, jtsjDto.getDriverIds())
                    .in("pdf".equals(jtsjDto.getDownloadType()), DfdwDzccTWeek::getWeek, jtsjDto.getMonthlyCycles())
                    .in("zip".equals(jtsjDto.getDownloadType()), DfdwDzccTWeek::getId, jtsjDto.getIds())
                    .ge(DfdwDzccTWeek::getStartTime, jtsjDto.getStartDate())
                    .lt(DfdwDzccTWeek::getEndTime, jtsjDto.getEndDate())
            );
            log.info("用户{}, id{}, week表数据已取出开始循环生成pdf", user.getRealName(), user.getId());
            // 预先创建常用的字体对象
            Font normalFont = PdfUtil.getPdfChineseFont(16, Font.NORMAL);
            Font boldFont = PdfUtil.getPdfChineseFont(16, Font.BOLD);
            Font boldFont20 = PdfUtil.getPdfChineseFont(20, Font.BOLD);
            for (DfdwDzccTWeek week: weekList) {
                DzccVPerson person = vPersonList.stream().filter(p -> p.getId().equals(week.getDriveId())).findFirst().orElse(null);
                if (person == null) {
                    continue;
                }
                Integer monthlyCycle = week.getWeek();
                LocalDate startDate = week.getStartTime();
                LocalDate endDate = week.getEndTime();

                JtsjDto driverParam = new JtsjDto();
                driverParam.setStartDate(startDate);
                driverParam.setEndDate(endDate.plusDays(1));
                driverParam.setDriverId(person.getId());
                List<JtsjDto> driverList = vDfdwDzccTCclcdetailMapper.getSjqdForMonth(driverParam);
                long time = System.currentTimeMillis();
                String inputPath = localPath + "/yykj/dzcc/downloads/jtsjqx/" + person.getId() + time + ".pdf";
                String outputPath = localPath + "/yykj/dzcc/downloads/water/jtsjqx/" + person.getId() + time + ".pdf";
                try (FileOutputStream fos = new FileOutputStream(inputPath)) {
                    //1.打开文档并设置基本属性
                    Document document = new Document();

                    //writer
                    PdfWriter writer = PdfWriter.getInstance(document, fos);
                    writer.setViewerPreferences(PdfWriter.PageModeUseThumbs);
                    writer.setPageSize(PageSize.A4);
                    document.open();
                    //标题一
                    Paragraph title = new Paragraph("集团本部驾驶员工作量清单（" + jtsjDto.getStartDate().format(formatter) + "第" + PdfUtil.numberToChinese(monthlyCycle) + "周）", boldFont20);
                    //标题样式
                    title.setAlignment(Element.ALIGN_CENTER);
                    document.add(title);

                    //第一表格列宽
                    float[] firtWidths = {100, 140, 210, 140, 140, 140};
                    PdfPTable table = new PdfPTable(firtWidths);
                    table.setTotalWidth(870);
                    table.setHorizontalAlignment(Element.ALIGN_CENTER);
                    table.setSpacingBefore(10f); // 前间距
                    table.setSpacingAfter(10f); // 后间距

                    List<JtsjDto> jtsjs = list.stream().filter(jtsj ->
                            jtsj.getDriverId().equals(person.getId()) &&
                                    !jtsj.getMoveDate().isBefore(startDate) &&
                                    !jtsj.getMoveDate().isAfter(endDate)
                    ).collect(Collectors.toList());
                    String licencePlate = "";
                    if (!jtsjs.isEmpty()) {
                        licencePlate = String.join("\r\n", jtsjs.stream().map(JtsjDto::getLicencePlate).collect(Collectors.toSet()));
                    } else if (!driverList.isEmpty()) {
                        licencePlate = String.join("\r\n", driverList.stream()
                                .filter(jtsj -> !jtsj.getStartDate().isAfter(startDate))
                                .map(JtsjDto::getLicencePlate)
                                .collect(Collectors.toSet()));
                    }
                    //表格数据
                    //mock数据
                    Object[][] datas = new Object[(int) (6 + ChronoUnit.DAYS.between(startDate, endDate))][];
                    datas[0] = new Object[]{"周期: ", startDate.format(formatter2) + "-" + endDate.format(formatter2)};
                    datas[1] = new Object[]{"车牌: ", licencePlate, "驾驶员", person.getRealName()};
                    datas[2] = new Object[]{"电子出车系统", "个人补充并说明"};
                    datas[3] = new Object[]{"出车日期", "出车地点", "延时", "出车地点", "延时"};
                    int datai = 4;
                    BigDecimal overtimeHoursAllSum = BigDecimal.ZERO;
                    for (LocalDate date = startDate; date.isBefore(endDate.plusDays(1)); date = date.plusDays(1)) {
                        LocalDate finalDate = date;
                        List<JtsjDto> jtsjList = list.stream().filter(jtsj ->
                                jtsj.getDriverId().equals(person.getId()) && jtsj.getMoveDate().equals(finalDate)
                        ).collect(Collectors.toList());
                        String concatenatedAreaText = jtsjList.stream().map(JtsjDto::getConcatenatedAreaText).collect(Collectors.joining("\r\n\r\n")).replace(" ", "");
                        BigDecimal overtimeHoursAll = jtsjList.stream().map(JtsjDto::getOvertimeHoursAll).findFirst().orElse(null);
                        if (overtimeHoursAll != null) {
                            overtimeHoursAllSum = overtimeHoursAllSum.add(overtimeHoursAll);
                        }
                        datas[datai] = new Object[]{PdfUtil.weekToChinese(date.getDayOfWeek().getValue()), date.format(formatter3), concatenatedAreaText, overtimeHoursAll, "", ""};
                        datai++;
                    }
                    datas[datai] = new Object[]{"合计", overtimeHoursAllSum, "", ""};
                    if (week.getApproveState() == 2 && week.getAppendDelayHour() != null) {
                        datas[datai] = new Object[]{"合计", overtimeHoursAllSum, "", week.getAppendDelayHour()};
                    }
                    //table数据填充

                    // 预先创建基础单元格样式
                    PdfPCell baseCell = new PdfPCell();
                    PdfUtil.setTableStyle(table, baseCell);

                    for (int i = 0; i < datas.length; i++) {
                        for (int j = 0; j < datas[i].length; j++) {
                            // 复用基础单元格
                            PdfPCell pdfCell = new PdfPCell(baseCell);

                            // 设置内容
                            String content = datas[i][j] == null ? "" : String.valueOf(datas[i][j]);
                            Font currentFont = normalFont;  // 默认使用普通字体

                            // 根据位置设置样式
                            if ((i == 0 && j == 0) ||
                                (i == 1 && (j == 0 || j == 2)) ||
                                (i == 3)) {
                                currentFont = boldFont;
                            }

                            Paragraph paragraph = new Paragraph(content, currentFont);
                            pdfCell.setPhrase(paragraph);

                            // 设置单元格合并
                            switch (i) {
                                case 0:
                                    if (j == 1) {
                                        pdfCell.setColspan(5);
                                    }
                                    break;
                                case 1:
                                    if (j == 1 || j == 3) {
                                        pdfCell.setColspan(2);
                                    }
                                    break;
                                case 2:
                                    if (j == 0) {
                                        pdfCell.setColspan(4);
                                    } else if (j == 1) {
                                        pdfCell.setColspan(2);
                                    }
                                    break;
                                case 3:
                                    if (j == 0) {
                                        pdfCell.setColspan(2);
                                    }
                                    break;
                                default:
                                    if (i > 3 && i < (int) (5 + ChronoUnit.DAYS.between(startDate, endDate))) {
                                        if (j == 2) {
                                            pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                                        }
                                    } else if (i == (int) (5 + ChronoUnit.DAYS.between(startDate, endDate)) && j == 0) {
                                        pdfCell.setColspan(3);
                                    }
                            }

                            table.addCell(pdfCell);
                        }
                    }
                    document.add(table);
                    title = new Paragraph("①出车地点变更，须提供出车申请单，并由车辆使用人签字确认。", boldFont);
                    title.setAlignment(Element.ALIGN_LEFT);
                    document.add(title);
                    title = new Paragraph("②延时原则不作更改。因故需更改，应由车队长确认。\r\n\r\n", boldFont);
                    title.setAlignment(Element.ALIGN_LEFT);
                    document.add(title);
                    title = new Paragraph("车队长签字：            驾驶员签字：            日期：", boldFont);
                    title.setAlignment(Element.ALIGN_LEFT);
                    document.add(title);
                    document.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("驾驶员工作量清单导出异常,用户id：" + person.getId() + ", 周期：" + monthlyCycle + ",异常原因：" + e.getMessage());
                }
                // 添加水印
                List<String> watermark = Arrays.asList(user.getRealName(), user.getGroupName(), sdf.format(new Date()));
                PdfUtil.pdfAddWaterMark(inputPath, outputPath, watermark);
                pathList.add(outputPath);
                file = new File(inputPath);
                if (file.exists()) {
                    file.delete();
                }
            }
            //=================生成word到设置浏览默认下载地址=================
            if ("pdf".equals(jtsjDto.getDownloadType())) {
                byte[] buffer = new byte[1024];
                file = new File(pathList.get(0));
                if (!file.exists()) {
                    log.info("文件不存在：{}", pathList.get(0));
                }
                try (FileInputStream fis = new FileInputStream(file); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    int i = bis.read(buffer);
                    while (i != -1) {
                        servletOutputStream.write(buffer, 0, i);
                        i = bis.read(buffer);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("集团本部驾驶员工作量清单文件下载失败：" + e.getMessage());
                }
            } else {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(servletOutputStream)) {
                    int i = 0;
                    for (DfdwDzccTWeek week: weekList) {
                        // log.info("用户{}, id{}, {}第{}周zip开始", user.getRealName(), user.getId(), week.getDriveName(), week.getWeek());
                        String fileName = week.getDriveName() + jtsjDto.getStartDate().format(formatter) + "第" + PdfUtil.numberToChinese(week.getWeek()) + "周";
                        String filePath = pathList.get(i);
                        // 创建ZIP条目
                        ZipEntry zipEntry = new ZipEntry(fileName + ".pdf");
                        zipOutputStream.putNextEntry(zipEntry);

                        // 读取文件并写入到ZIP输出流
                        byte[] pdfFile = FileUtil.readBytes(filePath);
                        zipOutputStream.write(pdfFile);
                        zipOutputStream.closeEntry();

                        i++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("集团本部驾驶员工作量清单多文件压缩失败：" + e.getMessage());
                }
            }
            return Result.ok();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        } finally {
            for (String path : pathList) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
            log.info("用户{}, id{}, 接口结束", user.getRealName(), user.getId());
        }
    }
}
