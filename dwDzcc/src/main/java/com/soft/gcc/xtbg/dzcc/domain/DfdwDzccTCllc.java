package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车-车辆里程
* @TableName DFDW_DZCC_T_CLLC
*/
@TableName(value ="DFDW_DZCC_T_CLLC", autoResultMap = true)
@Data
public class DfdwDzccTCllc extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 驾驶员id
    */
    @TableField(value = "driverId")
    @JSONField(name = "driverId")

    private Integer driverId;
    /**
    * 出车日期
    */
    @TableField(value = "date")
    @JSONField(name = "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;
    /**
    * 里程数
    */
    @TableField(value = "mileage")
    @JSONField(name = "mileage")

    private Integer mileage;
    /**
    * 图片上传关联t_file表id
    */
    @TableField(value = "imgFileId")
    @JSONField(name = "imgFileId")

    private Integer imgFileId;
    /**
    * 是否是代班（0否【车辆里程】、1是【工作里程】）
    */
    @TableField(value = "isSubstitute")
    @JSONField(name = "isSubstitute")

    private Integer isSubstitute;
    /**
    * 代班开始时间
    */
    @TableField(value = "substituteBeginDate")
    @JSONField(name = "substituteBeginDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date substituteBeginDate;
    /**
    * 代班结束时间
    */
    @TableField(value = "substituteEndDate")
    @JSONField(name = "substituteEndDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date substituteEndDate;
    /**
    * 创建人id
    */
    @TableField(value = "createUserId")
    @JSONField(name = "createUserId")

    private Integer createUserId;
    /**
    * 审核状态（0 未提交、1 审核中、2 已审核、3已驳回）
    */
    @TableField(value = "approvingState")
    @JSONField(name = "approvingState")

    private Integer approvingState;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 驾驶员姓名
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")

    private String driverName;
    /**
    * 车辆表关联id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 车牌号
    */
    @TableField(value = "licencePlate")
    @JSONField(name = "licencePlate")

    private String licencePlate;
    /**
    * 创建人姓名
    */
    @TableField(value = "createUserName")
    @JSONField(name = "createUserName")

    private String createUserName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
