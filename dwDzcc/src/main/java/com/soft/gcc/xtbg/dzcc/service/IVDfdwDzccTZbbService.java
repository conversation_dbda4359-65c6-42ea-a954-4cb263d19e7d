package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_ZBB(主报表（查询当月信息）)】的数据库操作Service
* @createDate 2022-10-31 09:46:24
*/
public interface IVDfdwDzccTZbbService extends IService<VDfdwDzccTZbb> {

    Result<Object> GetZbbList(DzccPersonEntity person, VDfdwDzccTZbb zbb);


    Result<Object> GetZbbListJbf(DzccPersonEntity person, VDfdwDzccTZbb zbb);
    Result<Object> GetZbbListJbfNew(DzccPersonEntity person, VDfdwDzccTZbb zbb);
     DfdwDzccTOverTime getOneOverTimeSet(List<DfdwDzccTOverTime> overtimeSetList, Integer groupId) ;
    void jbfJx(VDfdwDzccTZbb data, List<VDfdwDzccTCclcdetail> cclcdetailList, BigDecimal gzrYsbt, BigDecimal sxrYsbt, BigDecimal jjrYsbt, BigDecimal sxrJbfbt, BigDecimal jjrJbfbt, BigDecimal gybt);
    void JbfJxNew(VDfdwDzccTZbb data, List<DfdwDzccTCclcdetailReport> reportList);

    List<VDfdwDzccTZbb> queryZbbList(String format, String format1, List<Integer> groupIds, VDfdwDzccTZbb zbb, DzccPersonEntity person);

    Result<Object> LockMonth(DzccPersonEntity person, DfdwDzccTLock lock);

    Result<Object> UnLockMonth(DfdwDzccTLock lock);

    Result<Object> ShowLockMonth(DfdwDzccTLock lock);

    Result<Object> GetSbbList(DzccPersonEntity person, VDfdwDzccTZbb zbb);

    Result<Object> GettbbList(DzccPersonEntity person, DfdwDzccTCarmove carmove);

    Result<Object> GetLcgjList(DzccPersonEntity person, VDfdwDzccTZbb zbb);

    Result<Object> GetXcgjList(DzccPersonEntity person, VDfdwDzccTCarmove carmove);
}
