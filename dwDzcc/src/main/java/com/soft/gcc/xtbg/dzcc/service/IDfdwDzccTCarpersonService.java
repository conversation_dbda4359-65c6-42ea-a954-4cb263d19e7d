package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarperson;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CARPERSON】的数据库操作Service
* @createDate 2023-01-13 10:41:12
*/
public interface IDfdwDzccTCarpersonService extends IService<DfdwDzccTCarperson> {

    Result<Object> GetCDZPZList(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetCarList(DzccPersonEntity person, Map<String, String> map);

    Result<Object> EditCDZPZ(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetCDZGroup(DzccPersonEntity person, Map<String, String> map);

    Result<Object> GetCarListForCdz(DzccPersonEntity person, Map<String, String> map);
}
