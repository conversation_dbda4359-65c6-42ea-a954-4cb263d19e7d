package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclc;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd;


import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CCLC(电子出车单LC)】的数据库操作Service
* @createDate 2022-10-21 14:17:15
*/
public interface IDfdwDzccTCclcService extends IService<DfdwDzccTCclc> {

    Result<Object> GetCcdList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd cclc);

    Result<Object> GetAddressListById(Map<String, String> map);

    Result<Object> GetLcListById(Map<String, String> map);

    List<VDfdwDzccTCcd> getCcdDownLoadList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd cclc);

    List<DfdwDzccTCclc> getCcdByDriveIdAndTime(Integer driveId, String date);

    Result DownCcdXqById(String type, List<String> idList, DzccPersonEntity person, HttpServletResponse response);

    IPage<VDfdwDzccTCcd> getCCDPageList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd cclc);

    Result<Object> GetCcdListById(Integer id);
}
