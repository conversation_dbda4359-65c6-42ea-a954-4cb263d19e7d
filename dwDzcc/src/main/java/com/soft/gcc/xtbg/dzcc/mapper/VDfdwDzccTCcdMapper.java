package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CCD】的数据库操作Mapper
* @createDate 2022-10-25 16:50:19
* @Entity com.yykj.app.dzcc.domain.VDfdwDzccTCcd
*/
public interface VDfdwDzccTCcdMapper extends BaseMapper<VDfdwDzccTCcd> {

    List<VDfdwDzccTCcd> getCcdBBList(
            @Param("person") DzccPersonEntity person,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("ccd") VDfdwDzccTCcd ccd
    );

    IPage<VDfdwDzccTCcd> GetCcdListPage(
            IPage<VDfdwDzccTCcd> list,
            @Param("person") DzccPersonEntity person,
            @Param("ccd") VDfdwDzccTCcd ccd
    );

    IPage<VDfdwDzccTCcd> GetCcdListPageNew(
            IPage<VDfdwDzccTCcd> list,
            @Param("person") DzccPersonEntity person,
            @Param("ccd") VDfdwDzccTCcd ccd
    );

    List<VDfdwDzccTCcd> getCcdList(
            @Param("person") DzccPersonEntity person,
            @Param("ccd") VDfdwDzccTCcd ccd
    );

    VDfdwDzccTCcd getCcdListById(Integer id);
}




