package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 电子出车-车辆移动信息
* @TableName DFDW_DZCC_T_CARMOVE
*/
@TableName(value ="DFDW_DZCC_T_CARMOVE", autoResultMap = true)
@Data
public class DfdwDzccTCarmove extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 车Id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 司机Id
    */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
    * 省份
    */
    @TableField(value = "provinces")
    @JSONField(name = "provinces")

    private Integer provinces;
    /**
    * 市
    */
    @TableField(value = "city")
    @JSONField(name = "city")

    private Integer city;
    /**
    * 区
    */
    @TableField(value = "area")
    @JSONField(name = "area")

    private Integer area;
    /**
    * 最后一次到达时间
    */
    @TableField(value = "arriveTime")
    @JSONField(name = "arriveTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arriveTime;
    /**
    * 创建时间（即第一次进入时间）
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 当天日期
    */
    @TableField(value = "curDate")
    @JSONField(name = "curDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date curDate;
    /**
    * 区域编码
    */
    @TableField(value = "adCode")
    @JSONField(name = "adCode")

    private String adCode;
    /**
    * 省名
    */
    @TableField(value = "provincesName")
    @JSONField(name = "provincesName")

    private String provincesName;
    /**
    * 市名
    */
    @TableField(value = "cityName")
    @JSONField(name = "cityName")

    private String cityName;
    /**
    * 区名
    */
    @TableField(value = "areaName")
    @JSONField(name = "areaName")

    private String areaName;

    @TableField(exist = false)
    private String dayInfo;
    @TableField(exist = false)
    private String groupName;
    @TableField(exist = false)
    private String licencePlate;
    @TableField(exist = false)
    private String realName;
    @TableField(exist = false)
    private String carMold;
    //实际里程 米
    @TableField(exist = false)
    private Long actualMileage;
    //出车单数
    @TableField(exist = false)
    private Integer ccdNum;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
