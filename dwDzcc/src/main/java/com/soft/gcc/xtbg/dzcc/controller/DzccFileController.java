package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.util.AliyunOSSUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("/dzcc/file")
@RestController
public class DzccFileController extends DzccBaseController {
    private static final Logger log = LoggerFactory.getLogger(DzccFileController.class);
    @Autowired
    TFileService tFileService;

    @RequestMapping("/upload")
    public Result<Object> upload(MultipartFile file, HttpServletRequest request, String type, String hjID) {
        DzccPersonEntity person = getDzccPerson();
        try {
            if (null == file) {
                file = ((StandardMultipartHttpServletRequest) request).getMultiFileMap().get("uploadFile").get(0);
            }
            LocalDate localDate = LocalDate.now();
            String YEAR_MONTH = localDate.getYear() + "/" + localDate.getMonthValue() + "/";
            String path = "Upload/FileManage/Dzcc/" + type + "/" + YEAR_MONTH;
            String hz = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
            String fileName = UUID.randomUUID() + "." + hz;
            String url = path + AliyunOSSUtils.uploadFile(path, fileName, file);
            String name = file.getOriginalFilename();
            TFile sf = new TFile();
            //type
            sf.setFilename(name);
            sf.setFilepath(url);
            sf.setUploaddate(new Date());
            sf.setType(type);
            sf.setPersonname(person.getRealName());
            sf.setPersonzgh(person.getLoginName());
            //lcDefine.LcID
            sf.setFunctionid(20017);
            //lcjd.lcjdID
            sf.setHjid(hjID);
            //1 2
            sf.setSubtname(type);
            if (tFileService.save(sf)) {
                return Result.ok(sf, "上传文件成功！");
            } else {
                return Result.error("上传文件失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/downloadImportExample")
    public Result<Object> upload(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
        OutputStream outputStream = null;
        try (BufferedInputStream bis = new BufferedInputStream(new ClassPathResource("\\static\\dzcc\\" + map.get("name").toString()).getInputStream())) {
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int i = bis.read(buffer);
            while (i != -1) {
                outputStream.write(buffer, 0, i);
                i = bis.read(buffer);
            }
            return Result.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        } finally {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        }
    }

    @GetMapping("/getFileName")
    public Result<Object> getFileName(String filePath) {
        List<TFile> list = tFileService.list(new LambdaQueryWrapper<TFile>().eq(TFile::getFilepath, filePath));
        if (!list.isEmpty()) {
            return Result.ok(list.get(0).getFilename());
        } else {
            return Result.error("文件不存在！");
        }
    }

    @GetMapping("/download")
    public Result<Object> download(String filePath, HttpServletResponse response) {
        if ( AliyunOSSUtils.existsByFileName(filePath)) {
            byte[] result = AliyunOSSUtils.downloadFileStream(filePath);
            if(result == null){
                return Result.error("查询数据失败!");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=" + FilenameUtils.getName(filePath));
            try (OutputStream outputStream = response.getOutputStream()) {
                // 写入数据
                outputStream.write(result);
                outputStream.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return Result.ok();
        }else{
            return Result.error("查询数据失败");
        }
    }


    @RequestMapping("/getFileList")
    public Result<Object> getFileList(@RequestBody Map<String, Object> map){
        try {
            List<Integer> ids = Arrays.asList(map.get("ids").toString().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
            List<TFile> list = tFileService.list(new LambdaQueryWrapper<TFile>()
                    .in(TFile::getId, ids)
            );
            return Result.ok(list);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }





    /**
     * 支持流式传输的文件下载接口，用于在线预览视频等大文件
     * 支持HTTP Range请求，实现断点续传和流式播放
     */
    @GetMapping("/download2")
    public void download2(String filePath, HttpServletRequest request, HttpServletResponse response) {
        OutputStream outputStream = null;
        try {
            // 检查文件是否存在
            if (!AliyunOSSUtils.existsByFileName(filePath)) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.setContentType("text/plain;charset=UTF-8");
                outputStream = response.getOutputStream();
                outputStream.write("File not found".getBytes("UTF-8"));
                outputStream.flush();
                return;
            }

            // 获取文件信息
            String fileName = FilenameUtils.getName(filePath);
            String contentType = getContentType(fileName);

            // 获取文件大小（这里需要从OSS获取文件大小）
            long fileSize = getFileSize(filePath);
            if (fileSize <= 0) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.setContentType("text/plain;charset=UTF-8");
                outputStream = response.getOutputStream();
                outputStream.write("Unable to get file size".getBytes("UTF-8"));
                outputStream.flush();
                return;
            }

            // 解析Range请求头
            String rangeHeader = request.getHeader("Range");
            long start = 0;
            long end = fileSize - 1;

            if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
                String[] ranges = parseRangeHeader(rangeHeader, fileSize);
                if (ranges != null) {
                    start = Long.parseLong(ranges[0]);
                    end = Long.parseLong(ranges[1]);
                }
            }

            // 设置响应头
            response.setContentType(contentType);
            response.setHeader("Accept-Ranges", "bytes");
            response.setHeader("Content-Length", String.valueOf(end - start + 1));

            // 如果是Range请求，设置206状态码和Content-Range头
            if (rangeHeader != null) {
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                response.setHeader("Content-Range", String.format("bytes %d-%d/%d", start, end, fileSize));
            } else {
                response.setStatus(HttpServletResponse.SC_OK);
            }

            // 设置缓存控制头，优化视频播放体验
            response.setHeader("Cache-Control", "public, max-age=3600");
            response.setHeader("ETag", "\"" + filePath.hashCode() + "\"");

            // 获取输出流并进行流式传输
            outputStream = response.getOutputStream();
            streamFileContent(filePath, start, end, outputStream);
            outputStream.flush();

        } catch (Exception e) {
            log.error("流式下载文件失败: {}", e.getMessage(), e);
            try {
                if (outputStream == null && !response.isCommitted()) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("text/plain;charset=UTF-8");
                    outputStream = response.getOutputStream();
                    outputStream.write(("Internal server error: " + e.getMessage()).getBytes("UTF-8"));
                    outputStream.flush();
                }
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        } finally {
            // 确保输出流被正确关闭
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭输出流失败", e);
                }
            }
        }
    }

    /**
     * 解析Range请求头
     */
    private String[] parseRangeHeader(String rangeHeader, long fileSize) {
        try {
            Pattern pattern = Pattern.compile("bytes=(\\d*)-(\\d*)");
            Matcher matcher = pattern.matcher(rangeHeader);

            if (matcher.find()) {
                String startStr = matcher.group(1);
                String endStr = matcher.group(2);

                long start = startStr.isEmpty() ? 0 : Long.parseLong(startStr);
                long end = endStr.isEmpty() ? fileSize - 1 : Long.parseLong(endStr);

                // 确保范围有效
                start = Math.max(0, start);
                end = Math.min(fileSize - 1, end);

                if (start <= end) {
                    return new String[]{String.valueOf(start), String.valueOf(end)};
                }
            }
        } catch (Exception e) {
            log.warn("解析Range头失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据文件扩展名获取Content-Type
     */
    private String getContentType(String fileName) {
        String extension = FilenameUtils.getExtension(fileName).toLowerCase();
        switch (extension) {
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/x-msvideo";
            case "mov":
                return "video/quicktime";
            case "wmv":
                return "video/x-ms-wmv";
            case "flv":
                return "video/x-flv";
            case "webm":
                return "video/webm";
            case "mkv":
                return "video/x-matroska";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "pdf":
                return "application/pdf";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 获取文件大小
     * 注意：这里需要根据实际的OSS实现来获取文件大小
     */
    private long getFileSize(String filePath) {
        try {
            // 这里需要调用OSS的API来获取文件大小
            // 由于当前的AliyunOSSUtils没有提供获取文件大小的方法，
            // 我们需要添加这个功能或者使用其他方式
            return AliyunOSSUtils.getFileSize(filePath);
        } catch (Exception e) {
            log.error("获取文件大小失败: {}", e.getMessage());
            return -1;
        }
    }

    /**
     * 流式传输文件内容
     */
    private void streamFileContent(String filePath, long start, long end, OutputStream outputStream) {
        try {
            // 验证Range参数
            if (start > end) {
                log.warn("无效的Range参数: start={}, end={}, 使用完整文件下载", start, end);
                // 如果Range无效，下载完整文件
                byte[] fileContent = AliyunOSSUtils.downloadFileStream(filePath);
                if (fileContent != null && fileContent.length > 0) {
                    outputStream.write(fileContent);
                }
                return;
            }

            // 使用OSS的Range下载功能
            // 注意：AliyunOSSUtils.downloadFileStream(start, end) 内部会调用 withRange(start, end-1)
            // 所以我们需要传入 end+1 来得到正确的范围
            byte[] fileContent = AliyunOSSUtils.downloadFileStream(filePath, start, end + 1);
            if (fileContent != null && fileContent.length > 0) {
                outputStream.write(fileContent);
            }
        } catch (Exception e) {
            log.error("流式传输文件内容失败: start={}, end={}, 错误: {}", start, end, e.getMessage(), e);
            //throw new RuntimeException("流式传输失败", e);
        }
    }
}
