package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTClbgService;
import com.soft.gcc.xtbg.dzcc.service.IDzccGroupitemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 车辆办公点
 */
@RequestMapping("/dzcc/clbg")
@RestController
public class DzccClbgController extends DzccBaseController {
    @Autowired
    IDzccGroupitemService groupitemService;
    @Autowired
    IDfdwDzccTClbgService clbgService;

    /**
     * 电子出车-PC-车辆办公点-车辆办公点查询
     * */
    @RequestMapping("/GetBGDList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BG01QX01,NDWCC01BG01QX02,NDWCC01BG01QX03')")
    public Result<Object> GetBGDList(@RequestBody DfdwDzccTClbg clbg) {
        DzccPersonEntity person = getDzccPerson();
        return this.clbgService.GetBGDList(person, clbg);
    }

    /**
     * 电子出车-PC-车辆办公点-添加车辆办公点
     * */
    @RequestMapping("/AddClbg")
    @PreAuthorize("@ss.hasPermi('NDWCC01BG01QX01')")
    public Result<Object> AddClbg(@RequestBody DfdwDzccTClbg clbg) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clbgService.AddClbg(dzccPersonEntity, clbg);
    }

    /**
     * 电子出车-PC-车辆办公点-修改车辆办公点
     * */
    @RequestMapping("/EditClbg")
    @PreAuthorize("@ss.hasPermi('NDWCC01BG01QX02')")
    public Result<Object> EditClbg(@RequestBody DfdwDzccTClbg clbg) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.clbgService.EditClbg(dzccPersonEntity, clbg);
    }

    /**
     * 电子出车-PC-车辆办公点-根据id删除车辆办公点
     * */
    @RequestMapping("/DeleteClbgById")
    @PreAuthorize("@ss.hasPermi('NDWCC01BG01QX03')")
    public Result<Object> DeleteClbgById(@RequestParam Map<String, String> map) {
        return this.clbgService.DeleteClbgById(map);
    }

    /**
     * 电子出车-PC-车辆办公点-车辆办公点查询
     * */
    @RequestMapping("/GetBgdListForBackHaul")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01BG01QX01,NDWCC01BG01QX02,NDWCC01BG01QX03')")
    public Result<Object> GetBgdListForBackHaul() {
        DzccPersonEntity person = getDzccPerson();
        List<DfdwDzccTClbg> list = this.clbgService.GetBgdListForBackHaul(person);
        return Result.ok(list);
    }
}
