package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName DFDW_DZCC_T_CLBG
*/
@TableName(value ="DFDW_DZCC_T_CLBG", autoResultMap = true)
@Data
public class DfdwDzccTClbg extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 所属单位id
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    * 办公地点GPS
    */
    @TableField(value = "unitGPS")
    @JSONField(name = "unitGPS")

    private String unitGPS;
    /**
    * 办公地点名称
    */
    @TableField(value = "unitName")
    @JSONField(name = "unitName")

    private String unitName;
    /**
    * 创建修改人id
    */
    @TableField(value = "userId")
    @JSONField(name = "userId")

    private Integer userId;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 修改时间
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 经度
    */
    @TableField(value = "longitude")
    @JSONField(name = "longitude")

    private BigDecimal longitude;
    /**
    * 纬度
    */
    @TableField(value = "latitude")
    @JSONField(name = "latitude")

    private BigDecimal latitude;
    /**
    * 所属单位名称
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")

    private String groupName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
