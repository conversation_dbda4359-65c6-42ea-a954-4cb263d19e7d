package com.soft.gcc.xtbg.dzcc.entity;

import com.deepoove.poi.el.Name;
import lombok.Data;

@Data
public class BBData {
    @Name("detail_table")
    private DetailData detailTable;
    @Name("date")
    private String date;
    @Name("carOrPerson")
    private String carOrPerson;
    @Name("groupName")
    private String groupName;
    @Name("name")
    private String name;
    private String licensePlate;
    private String dayInfo;


    private String applyNo;
    private Integer num;
    private String note;
    private String addressInfo;
}
