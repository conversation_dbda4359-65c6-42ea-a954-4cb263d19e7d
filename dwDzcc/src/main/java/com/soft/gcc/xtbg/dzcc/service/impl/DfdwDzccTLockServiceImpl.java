package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTLock;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTLockService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTLockMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_LOCK(报表月份锁定)】的数据库操作Service实现
* @createDate 2022-10-31 15:43:55
*/
@Service
public class DfdwDzccTLockServiceImpl extends ServiceImpl<DfdwDzccTLockMapper, DfdwDzccTLock>
    implements IDfdwDzccTLockService{

}




