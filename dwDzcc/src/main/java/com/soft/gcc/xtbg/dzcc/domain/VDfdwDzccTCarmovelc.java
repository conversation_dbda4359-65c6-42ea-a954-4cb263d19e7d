package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName V_DFDW_DZCC_T_CARMOVELC
*/
@TableName(value ="V_DFDW_DZCC_T_CARMOVELC")
@Data
public class VDfdwDzccTCarmovelc implements Serializable {


    /**
    *
    */
    @TableField(value = "id")
    @JSONField(name = "id")
    private Integer id;
    /**
    *
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")
    private Integer carId;
    /**
    *
    */
    @TableField(value = "currDate")
    @JSONField(name = "currDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currDate;
    /**
    *
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")
    private Integer approveState;
    /**
    *
    */
    @TableField(value = "approveNote")
    @JSONField(name = "approveNote")
    private String approveNote;
    /**
    *
    */
    @TableField(value = "approveTime")
    @JSONField(name = "approveTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;
    /**
    *
    */
    @TableField(value = "approveUserId")
    @JSONField(name = "approveUserId")
    private Integer approveUserId;
    /**
    *
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(value = "driveId")
    @JSONField(name = "driveId")
    private Integer driveId;

    @TableField(value = "driveName")
    @JSONField(name = "driveName")
    private String driveName;

    @TableField(value = "groupId")
    @JSONField(name = "groupId")
    private Integer groupId;

    @TableField(value = "groupName")
    @JSONField(name = "groupName")
    private String groupName;

    @TableField(value = "topGroupId")
    @JSONField(name = "topGroupId")
    private Integer topGroupId;

    @TableField(value = "topGroupName")
    @JSONField(name = "topGroupName")
    private String topGroupName;


    @TableField(value = "licensePlate")
    @JSONField(name = "licensePlate")
    private String  licensePlate;

    @TableField(value = "approveUserName")
    @JSONField(name = "approveUserName")
    private String approveUserName;

    @TableField(value = "type")
    @JSONField(name = "type")
    private Integer type;

    /**
    *
    */
    @TableField(value = "lcDefineID")
    @JSONField(name = "lcDefineID")

    private Integer lcDefineID;
    /**
    *
    */
    @TableField(value = "lcName")
    @JSONField(name = "lcName")

    private String lcName;
    /**
    *
    */
    @TableField(value = "ywID")
    @JSONField(name = "ywID")

    private Integer ywID;
    /**
    *
    */
    @TableField(value = "sendPerson")
    @JSONField(name = "sendPerson")

    private String sendPerson;
    /**
    *
    */
    @TableField(value = "sendPersonZgh")
    @JSONField(name = "sendPersonZgh")

    private String sendPersonZgh;
    /**
    *
    */
    @TableField(value = "AllPersonZgh")
    @JSONField(name = "AllPersonZgh")

    private String AllPersonZgh;
    /**
    *
    */
    @TableField(value = "isMany")
    @JSONField(name = "isMany")

    private Integer isMany;
    /**
    *
    */
    @TableField(value = "lcJdmc")
    @JSONField(name = "lcJdmc")

    private String lcJdmc;
    /**
    *
    */
    @TableField(value = "lcJdid")
    @JSONField(name = "lcJdid")

    private Integer lcJdid;
    /**
    *
    */
    @TableField(value = "lcIsback")
    @JSONField(name = "lcIsback")

    private Integer lcIsback;
    /**
    *
    */
    @TableField(value = "lcTojdid")
    @JSONField(name = "lcTojdid")

    private String lcTojdid;
    /**
    *
    */
    @TableField(value = "number")
    @JSONField(name = "number")

    private Integer number;
    /**
    *
    */
    @TableField(value = "BXType")
    @JSONField(name = "BXType")

    private String BXType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
