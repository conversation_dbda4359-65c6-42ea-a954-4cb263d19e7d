package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.domain.LzclTCarMore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car(零租车辆--车辆管理表)】的数据库操作Service
* @createDate 2023-01-17 09:52:31
*/
public interface DzccCarService extends IService<DzccCar> {

    Result<Object> GetClsyList(DzccPersonEntity person, DzccCar car);

    Result<Object> GetClxxList(DzccPersonEntity dzccPersonEntity, DzccCar car);

    Result<Object> AddClxx(DzccPersonEntity dzccPersonEntity, DzccCar car);

    Result<Object> EditClxx(DzccPersonEntity dzccPersonEntity, DzccCar car);

    Result<Object> DeleteClxxById(Integer id);

    Result<Object> DownLoad(DzccPersonEntity dzccPersonEntity, DzccCar car, HttpServletResponse response);

    Result<Object> GetMoldDict();

    Result<Object> GetCarMoveList(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore);

    Result<Object> GetCarMoveChildList(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore);

    Result<Object> AddCarMove(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore);

    Result<Object> EditCarMove(DzccPersonEntity dzccPersonEntity, LzclTCarMore carMore);

    Result<Object> DeleteCarMoveById(Integer id);


}
