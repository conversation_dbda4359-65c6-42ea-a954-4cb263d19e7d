package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccPerson;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【Person】的数据库操作Service
* @createDate 2022-10-20 09:30:31
*/
public interface IDzccPersonService extends IService<DzccPerson> {

}
