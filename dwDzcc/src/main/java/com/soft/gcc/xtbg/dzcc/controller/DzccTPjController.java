package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTPjService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
@RequestMapping("/dzcc/pj")
@RestController
public class DzccTPjController extends DzccBaseController {
    @Autowired
    DfdwDzccTPjService dfdwDzccTPjService;

    private static final Logger log = LoggerFactory.getLogger(DzccTPjController.class);

    @PostMapping("/create")
    @ApiOperation(value = "创建电子出车单-评价")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX02')")
    public Result<Object> createDzccTPj(@RequestBody DfdwDzccTPj dfdwDzccTPj) {
        return Result.ok(dfdwDzccTPjService.createDzccTPj(dfdwDzccTPj));
    }

    @PutMapping("/update")
    @ApiOperation(value = "更新电子出车单-评价")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX03')")
    public Result<Object> updateDzccTPj(@RequestBody DfdwDzccTPj dfdwDzccTPj) {
        dfdwDzccTPjService.updateDzccTPj(dfdwDzccTPj);
        return Result.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除电子出车单-评价")
    @ApiParam(name = "id", value = "编号", required = true)
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX04')")
    public Result<Object> deleteDzccTPj(@RequestParam("ids") Collection<Integer> ids) {
        dfdwDzccTPjService.deleteDzccTPj(ids);
        return Result.ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得电子出车单-评价")
    @ApiParam(name = "id", value = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX01')")
    public Result<Object> getDzccTPj(@RequestParam("id") Integer id) {
        DfdwDzccTPj dzccTPj = dfdwDzccTPjService.getDzccTPj(id);
        return Result.ok(dzccTPj);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得电子出车单-评价列表")
    @ApiParam(name = "ids", value = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX01')")
    public Result<Object> getDzccTPjList(@RequestParam("ids") Collection<Integer> ids) {
        List<DfdwDzccTPj> list = dfdwDzccTPjService.getDzccTPjList(ids);
        return Result.ok(list);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得电子出车单-评价分页")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX01')")
    public Result<Object> getDzccTPjPage(@RequestBody @Valid DfdwDzccTPj pageVO) {
        DzccPersonEntity person = getDzccPerson();
        if (pageVO.getTime().isEmpty()) {
            pageVO.setBeginDate(null);
            pageVO.setOverDate(null);
        } else {
            pageVO.setBeginDate(pageVO.getTime().get(0));
            pageVO.setOverDate(pageVO.getTime().get(1).plusDays(1));
        }
        IPage<DfdwDzccTPj> pageResult = dfdwDzccTPjService.getDzccTPjPage(pageVO, person);
        return Result.ok(pageResult);
    }

    @PostMapping("/export-excel")
    @ApiOperation(value = "导出电子出车单-评价 Excel")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX05')")
    public Result<Object> exportDzccTPjExcel(@RequestBody @Valid DfdwDzccTPj exportReqVO, HttpServletResponse response) throws Exception {
        DzccPersonEntity person = getDzccPerson();
        if (exportReqVO.getTime().isEmpty()) {
            exportReqVO.setBeginDate(null);
            exportReqVO.setOverDate(null);
        } else {
            exportReqVO.setBeginDate(exportReqVO.getTime().get(0));
            exportReqVO.setOverDate(exportReqVO.getTime().get(1).plusDays(1));
        }
        List<DfdwDzccTPj> list = dfdwDzccTPjService.getDzccTPjList(exportReqVO, person);
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet("出车单评价");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("出车单评价");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("出车单编号");
            row.createCell(1).setCellValue("出车时间");
            row.createCell(2).setCellValue("车牌");
            row.createCell(3).setCellValue("司机");
            row.createCell(4).setCellValue("用车人");
            row.createCell(5).setCellValue("星级评价");
            row.createCell(6).setCellValue("是否整洁");
            row.createCell(7).setCellValue("是否安全驾驶");
            row.createCell(8).setCellValue("是否准时到达");
            row.createCell(9).setCellValue("服务是否满意");

            for (int i = 0; i< list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getApplyNo());
                row.createCell(1).setCellValue(list.get(i).getCcOpenTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                row.createCell(2).setCellValue(list.get(i).getLicencePlate());
                row.createCell(3).setCellValue(list.get(i).getDriveName());
                row.createCell(4).setCellValue(list.get(i).getYcrName());
                row.createCell(5).setCellValue(list.get(i).getRate());
                row.createCell(6).setCellValue(list.get(i).getZjState() == 0 ? "否" : "是");
                row.createCell(7).setCellValue(list.get(i).getAqjsState() == 0 ? "否" : "是");
                row.createCell(8).setCellValue(list.get(i).getPdkzState() == 0 ? "否" : "是");
                row.createCell(9).setCellValue(list.get(i).getTsState() == 0 ? "否" : "是");
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("出车单评价".getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            log.error("导出Excel异常", Ex);
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @GetMapping("/getByLcId")
    @ApiOperation(value = "根据lcId获得电子出车单-评价")
    @PreAuthorize("@ss.hasPermi('NDWCC01PJ01QX01')")
    public Result<Object> getByLcId(@RequestParam("lcId") Integer lcId) {
        DfdwDzccTPj dzccTPj = dfdwDzccTPjService.getDzccTPjByLcId(lcId);
        return Result.ok(dzccTPj);
    }
}
