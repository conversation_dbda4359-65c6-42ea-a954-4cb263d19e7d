package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCarmove;
import com.soft.gcc.xtbg.dzcc.service.IDzccDictionaryvalueService;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTAddressService;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTCarmoveService;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTZbbService;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 每日行程告警
 */
@RequestMapping("/dzcc/csgj")
@RestController
public class DzccXcgjController extends DzccBaseController {
    @Autowired
    IVDfdwDzccTZbbService zbbService;
    @Autowired
    IVDfdwDzccTCarmoveService vCarmoveService;
    @Autowired
    IVDfdwDzccTAddressService vAddressService;
    @Autowired
    IDzccDictionaryvalueService dictionaryvalueService;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 电子出车-PC-告警-行程告警查询
     * */
    @RequestMapping("/GetXcgjList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL02QX01')")
    public Result<Object> GetXcgjList(@RequestBody VDfdwDzccTCarmove carmove) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return zbbService.GetXcgjList(dzccPersonEntity, carmove);
    }

    /**
     * 电子出车-PC-告警-导出
     * */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01CL02QX02')")
    public Result<Object> DownLoad(@RequestBody VDfdwDzccTCarmove carmove, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<VDfdwDzccTCarmove> list = new ArrayList<>();
            LocalDate startDate = carmove.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate endDate = carmove.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
            if (dzccPersonEntity.getDzccQx() != 3 && dzccPersonEntity.getDzccQx() > 0) {
                list = vCarmoveService.getXcglList(dzccPersonEntity, carmove, startDate, endDate);
            }

            XSSFSheet sheet = workbook.createSheet("每日行程告警");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(carmove.getApplyDeptName() + "每日行程告警(" + dateFormat.format(carmove.getStartDate()) + "至" + dateFormat.format(carmove.getEndDate()) + ")");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("日期");
            row.createCell(1).setCellValue("申请单位");
            row.createCell(2).setCellValue("车型");
            row.createCell(3).setCellValue("车牌号");
            row.createCell(4).setCellValue("驾驶员");
            row.createCell(5).setCellValue("告警类别");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(dateFormat.format(list.get(i).getCurDate()));
                row.createCell(1).setCellValue(list.get(i).getApplyDeptName());
                row.createCell(2).setCellValue(list.get(i).getCarMold());
                row.createCell(3).setCellValue(list.get(i).getLicensePlate());
                row.createCell(4).setCellValue(list.get(i).getDriverName());
                row.createCell(5).setCellValue(list.get(i).getStatus());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + carmove.getApplyDeptName() + "每日行程告警(" + dateFormat.format(carmove.getStartDate()) + "至" + dateFormat.format(carmove.getEndDate()) + ")" + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return Result.ok();

        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}
