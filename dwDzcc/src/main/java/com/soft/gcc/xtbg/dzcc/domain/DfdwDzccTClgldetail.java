package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 车辆管理detail
* @TableName DFDW_DZCC_T_CLGLDetail
*/
@TableName(value ="DFDW_DZCC_T_CLGLDetail", autoResultMap = true)
@Data
public class DfdwDzccTClgldetail extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    *
    */
    @TableField(value = "cid")
    @JSONField(name = "cid")

    private Integer cid;
    /**
    * 驾驶员id
    */
    @TableField(value = "driverId")
    @JSONField(name = "driverId")

    private Integer driverId;
    /**
    * 生效时间
    */
    @TableField(value = "startDate")
    @JSONField(name = "startDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    /**
    * 创建时间
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 修改时间
    */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
    * 编辑人id
    */
    @TableField(value = "editId")
    @JSONField(name = "editId")

    private Integer editId;
    /**
    * lzcl车辆表关联id
    */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
    * 所属部门id
    */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")

    private Integer groupId;
    /**
    * 司机姓名
    */
    @TableField(value = "driverName")
    @JSONField(name = "driverName")

    private String driverName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
