package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmovelc;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarmovelcMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCarmovelcService;
import com.soft.gcc.xtbg.dzcc.util.DateUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 异常车辆流程
 * @date 2022/11/14 11:14:44
 */
@RequestMapping("/dzcc/carMoveLc")
@RestController
public class DzccCarMoveLcController extends DzccBaseController {
    @Autowired
    private DfdwDzccTCarmovelcMapper carmovelcMapper;
    @Autowired
    private IDfdwDzccTCarmovelcService carmovelcService;

    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping("/getList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY03QX01')")
    public Result<Object> getList(@RequestBody DfdwDzccTCarmovelc ent) {
        DzccPersonEntity person = getDzccPerson();
        IPage<DfdwDzccTCarmovelc> list = new Page<>();
        list.setCurrent(ent.getPageNum());
        list.setSize(ent.getPageSize());
        if (ent.getEndMoveTime() != null) {
            ent.setEndMoveTime(DateUtil.asDate(DateUtil.asLocalDate(ent.getEndMoveTime()).plusDays(1)));
        }
        list = carmovelcMapper.getYccllcPage(
                list,
                person,
                ent
        );
        return Result.ok(list);
    }

    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasPermi('NDWCC01CY03QX02')")
    public Result<Object> DownLoad(@RequestBody DfdwDzccTCarmovelc ent, HttpServletResponse response) throws IOException {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<DfdwDzccTCarmovelc> list = new ArrayList<>();
            if (ent.getEndMoveTime() != null) {
                ent.setEndMoveTime(DateUtil.asDate(DateUtil.asLocalDate(ent.getEndMoveTime()).plusDays(1)));
            }
            if (person.getDzccQx() != 3 && person.getDzccQx() > 0) {
                list = carmovelcMapper.getList(
                        person,
                        ent
                );
            }

            XSSFSheet sheet = workbook.createSheet("异常车辆流程");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("异常车辆流程");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("部门");
            row.createCell(1).setCellValue("车型");
            row.createCell(2).setCellValue("车牌");
            row.createCell(3).setCellValue("司机");
            row.createCell(4).setCellValue("车队长审批");
            row.createCell(5).setCellValue("审批意见");
            row.createCell(6).setCellValue("审批时间");
            row.createCell(7).setCellValue("审批状态");
            row.createCell(8).setCellValue("创建时间");
            row.createCell(9).setCellValue("类别");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                String yearMonth = "";
                row.createCell(0).setCellValue(list.get(i).getGroupName());
                row.createCell(1).setCellValue(list.get(i).getCarMold());
                row.createCell(2).setCellValue(list.get(i).getLicensePlate());
                row.createCell(3).setCellValue(list.get(i).getDriveName());
                if (list.get(i).getApproveUserName() != null) {
                    row.createCell(4).setCellValue(list.get(i).getApproveUserName());
                }
                if (list.get(i).getApproveNote() != null) {
                    row.createCell(5).setCellValue(list.get(i).getApproveNote());
                }
                if (list.get(i).getApproveTime() != null) {
                    row.createCell(6).setCellValue(dateFormat.format(list.get(i).getApproveTime()));
                }
                if (list.get(i).getApproveState() == 0) {
                    row.createCell(7).setCellValue("审批中");
                } else if (list.get(i).getApproveState() == 1) {
                    row.createCell(7).setCellValue("同意");
                } else if (list.get(i).getApproveState() == 2) {
                    row.createCell(7).setCellValue("驳回");
                }
                row.createCell(8).setCellValue(dateFormat.format(list.get(i).getCreateTime()));
                if (list.get(i).getType() == 1) {
                    row.createCell(9).setCellValue("非规定时间出行");
                } else if (list.get(i).getType() == 2) {
                    row.createCell(9).setCellValue("未带工单出行车辆");
                } else if (list.get(i).getType() == 3) {
                    row.createCell(9).setCellValue("七日内无行驶轨迹");
                } else if (list.get(i).getType() == 4) {
                    row.createCell(9).setCellValue("超时未归场");
                } else if (list.get(i).getType() == 5) {
                    row.createCell(9).setCellValue("跨界");
                } else if (list.get(i).getType() == 6) {
                    row.createCell(9).setCellValue("节假日动车");
                } else if (list.get(i).getType() == 7) {
                    row.createCell(9).setCellValue("被拔");
                } else if (list.get(i).getType() == 8) {
                    row.createCell(9).setCellValue("车辆异常");
                }
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================

            workbook.write(response.getOutputStream());

            return Result.ok(list);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }
}
