package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 出车单子表
* @TableName DFDW_DZCC_T_CCLCDetail
*/
@TableName(value ="DFDW_DZCC_T_CCLCDetail")
@Data
public class DfdwDzccTCclcdetail implements Serializable {


    /**
     * 主键：
     */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
     * 主表业务Id
     */
    @TableField(value = "ywId")
    @JSONField(name = "ywId")

    private Integer ywId;
    /**
     * 车Id
     */
    @TableField(value = "carId")
    @JSONField(name = "carId")

    private Integer carId;
    /**
     * 司机Id
     */
    @TableField(value = "driveId")
    @JSONField(name = "driveId")

    private Integer driveId;
    /**
     * 移动日期
     */
    @TableField(value = "moveDate")
    @JSONField(name = "moveDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date moveDate;
    /**
     * 当天实际开始时间
     */
    @TableField(value = "startTime")
    @JSONField(name = "startTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 当天实际结束时间
     */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 上午加班时长
     */
    @TableField(value = "overtimeHoursAm",updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "overtimeHoursAm")
    private BigDecimal overtimeHoursAm;

    /**
     * 下午加班时长
     */
    @TableField(value = "overtimeHoursPm",updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "overtimeHoursPm")
    private BigDecimal overtimeHoursPm;



    /**
     * 总加班时长
     */
    @TableField(value = "overtimeHoursAll",updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "overtimeHoursAll")
    private BigDecimal overtimeHoursAll;


    /**
     * 是否有过夜津贴（0否 1是）正常超过22点算
     */
    @TableField(value = "subsidyState")
    @JSONField(name = "subsidyState")
    private Integer subsidyState;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    @JSONField(name = "updateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 部门
     */
    @TableField(value = "groupId")
    @JSONField(name = "groupId")
    private Integer groupId;


    /**
     * 天数类型（0休息日 1工作日 2节假日）
     */
    @TableField(value = "dayType")
    @JSONField(name = "dayType")
    private Integer dayType;

    /**
     * 车辆办公点Id
     */
    @TableField(value = "bgdId")
    @JSONField(name = "bgdId")
    private Integer bgdId;

    /**
     * 中午加班时长
     */
    @TableField(value = "overtimeHoursNoon",updateStrategy = FieldStrategy.IGNORED)
    @JSONField(name = "overtimeHoursNoon")
    private BigDecimal overtimeHoursNoon;


    /**
     * 工作上班上午检测时间
     */
    @TableField(value = "workStartTime")
    @JSONField(name = "workStartTime")
    private String workStartTime;

    /**
     * 工作上班下午检测时间
     */
    @TableField(value = "workEndTime")
    @JSONField(name = "workEndTime")
    private String workEndTime;

    /**
     * 出差补贴
     */
    @TableField(value = "travelAllowance")
    @JSONField(name = "travelAllowance")
    private BigDecimal travelAllowance;


    /**
     * 总工作时长
     */
    @TableField(value = "worktimeAll")
    @JSONField(name = "worktimeAll")
    private BigDecimal worktimeAll;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
