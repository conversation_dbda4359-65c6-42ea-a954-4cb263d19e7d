package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LZCL_T_Car(零租车辆--车辆管理表)】的数据库操作Mapper
* @createDate 2023-01-17 09:52:31
* @Entity com.yykj.app.dzcc.domain.DzccCar
*/
public interface DzccCarMapper extends BaseMapper<DzccCar> {

    List<DzccCar> SelectCarPerson(
            @Param("id") Integer id,
            @Param("dzccQx") Integer dzccQx,
            @Param("carId") Integer carId
    );

    List<DzccCar> GetClsyListForMonth(
            @Param("groupIds") List<Integer> groupIds,
            @Param("carMold") String carMold,
            @Param("person") DzccPersonEntity person,
            @Param("beginDate") LocalDate beginDate,
            @Param("overDate") LocalDate overDate
    );

    List<DzccCar> getClxxList(
            @Param("person") DzccPersonEntity person,
            @Param("car") DzccCar car
    );
}




