package com.soft.gcc.xtbg.dzcc.task;

import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTWeekService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;

@Component("syncDriveTask")
@Slf4j
@EnableScheduling
public class SyncDriveTask {

    @Resource
    DfdwDzccTWeekService dfdwDzccTWeekService;

    @Scheduled(cron = "0 0 1 * * ? ") //一分钟执行一次
    public void syncDrive()
    {
        log.info("开始执行同步司机定时任务=====");
        try {
            DfdwDzccTWeek dfdwDzccTWeek = new DfdwDzccTWeek();
            dfdwDzccTWeek.setYear(LocalDate.now().getYear());
            dfdwDzccTWeek.setMonth(LocalDate.now().getMonthValue());
            dfdwDzccTWeekService.operaDriverToWeekTab(dfdwDzccTWeek);
            log.info("执行完毕同步司机定时任务=====");
        }catch (Exception e){
            log.info("执行同步司机定时任务出错"+e.getMessage());
        }

    }
}
