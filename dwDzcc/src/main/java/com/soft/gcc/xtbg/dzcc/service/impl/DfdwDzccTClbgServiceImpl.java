package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClgl;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTClbgMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTUnitlogMapper;
import com.soft.gcc.xtbg.dzcc.service.DzccCarService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTClbgService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTClglService;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import com.soft.gcc.xtbg.base.controller.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CLBG】的数据库操作Service实现
 * @createDate 2022-10-18 15:36:20
 */
@Service
public class DfdwDzccTClbgServiceImpl extends ServiceImpl<DfdwDzccTClbgMapper, DfdwDzccTClbg>
        implements IDfdwDzccTClbgService {
    @Autowired
    IDfdwDzccTClglService clglService;
    @Autowired
    DfdwDzccTUnitlogMapper unitlogMapper;
    @Autowired
    DzccCarService carService;

    @Override
    public Result<Object> AddClbg(DzccPersonEntity dzccPersonEntity, DfdwDzccTClbg clbg) {
        try {
            if (clbg.getGroupId() == null) {
                return Result.error("请选择所属部门！");
            }
            if (clbg.getUnitName() == null) {
                return Result.error("请选择办公地点！");
            }
            List<DfdwDzccTClbg> list = this.list(new LambdaQueryWrapper<DfdwDzccTClbg>()
                    .eq(DfdwDzccTClbg::getGroupId, clbg.getGroupId())
                    .eq(DfdwDzccTClbg::getUnitGPS, clbg.getUnitGPS()));
            if (list.size() > 0) {
                return Result.error("所属部门也有此办公地点！");
            } else {
                clbg.setUserId(dzccPersonEntity.getId());
                clbg.setCreateTime(new Date());
                this.save(clbg);
                DfdwDzccTClgl clgl = new DfdwDzccTClgl();
                clgl.setUnitId(clbg.getId());
                clglService.update(clgl, new LambdaQueryWrapper<DfdwDzccTClgl>()
                        .apply("id in (select id from DFDW_DZCC_T_CLGL where groupId = " + clbg.getGroupId() + " and (unitId is null or unitId = 0))"));
                unitlogMapper.addUnitForGroup(clbg.getGroupId());
                return Result.ok("添加成功！");
            }
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> EditClbg(DzccPersonEntity dzccPersonEntity, DfdwDzccTClbg clbg) {
        try {
            if (clbg.getGroupId() == null) {
                return Result.error("请选择所属部门！");
            }
            if (clbg.getUnitName() == null) {
                return Result.error("请选择办公地点！");
            }
            List<DfdwDzccTClbg> list = this.list(new LambdaQueryWrapper<DfdwDzccTClbg>()
                    .eq(DfdwDzccTClbg::getGroupId, clbg.getGroupId())
                    .eq(DfdwDzccTClbg::getUnitName, clbg.getUnitName())
                    .eq(DfdwDzccTClbg::getUnitGPS, clbg.getUnitGPS()));
            if (list.size() > 0) {
                return Result.error("所属部门也有此办公地点！");
            } else {
                clbg.setUserId(dzccPersonEntity.getId());
                clbg.setUpdateTime(new Date());
                this.updateById(clbg);
                unitlogMapper.addUnitForGroup(clbg.getGroupId());
                return Result.ok("添加成功！");
            }
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> DeleteClbgById(Map<String, String> map) {
        int id = ParseUtil.tryParseInt(map.get("id"));
        int groupId = ParseUtil.tryParseInt(map.get("groupId"));
        try {
            List<DfdwDzccTClbg> clbgs = this.list(new LambdaQueryWrapper<DfdwDzccTClbg>().eq(DfdwDzccTClbg::getGroupId, groupId));
            if (clbgs.size() == 1) {
                DfdwDzccTClgl clgl = new DfdwDzccTClgl();
                clgl.setUnitId(0);
                clglService.update(clgl, new LambdaQueryWrapper<DfdwDzccTClgl>()
                        .apply("id in (select id from DFDW_DZCC_T_CLGL where groupId = " + groupId + " and (unitId is null or unitId != 0))"));
            } else {
                if (clbgs.get(0).getId() == id) {
                    DfdwDzccTClgl clgl = new DfdwDzccTClgl();
                    clgl.setUnitId(clbgs.get(1).getId());
                    clglService.update(clgl, new LambdaQueryWrapper<DfdwDzccTClgl>()
                            .apply("id in (select id from DFDW_DZCC_T_CLGL where groupId = " + groupId + " and unitId = " + id + ")"));
                }
            }
            this.removeById(id);
            unitlogMapper.addUnitForGroup(groupId);
            return Result.ok("删除成功");
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetBGDList(DzccPersonEntity dzccPersonEntity, DfdwDzccTClbg clbg) {
        IPage<DfdwDzccTClbg> list;
        try {
            Page<DfdwDzccTClbg> page = new Page<>(clbg.getPageNum(), clbg.getPageSize());
            list = baseMapper.GetBGDList(page, clbg.getUnitName(), clbg.getGroupId(), dzccPersonEntity.getDzccQx(), dzccPersonEntity.getId(), dzccPersonEntity.getTopGroupId());
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetUnitList(Map<String, String> map) {
        try {
            int groupId = ParseUtil.tryParseInt(map.get("groupId"));
            List<DfdwDzccTClbg> list = this.list(new LambdaQueryWrapper<DfdwDzccTClbg>().eq(DfdwDzccTClbg::getGroupId, groupId));
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public   List<DfdwDzccTClbg> GetBgdListForBackHaul(DzccPersonEntity dzccPersonEntity) {
        List<DfdwDzccTClbg> list=new ArrayList<>();
        list = baseMapper.GetBgdListForBackHaul(dzccPersonEntity.getDzccQx(), dzccPersonEntity.getId(), dzccPersonEntity.getTopGroupId());
        return list;
    }

}




