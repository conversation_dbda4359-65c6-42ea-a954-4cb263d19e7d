package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 零租车辆-车辆状态表
 * @TableName LZCL_T_Car_Status
 */
@TableName(value ="LZCL_T_Car_Status")
@Data
public class LzclTCarStatus implements Serializable {
    /**
     * 
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备id
     */
    @TableField(value = "DeviceId")
    private String deviceId;

    /**
     * 行驶里程 单位M
     */
    @TableField(value = "Mileage")
    private Long mileage;

    /**
     * 行驶时间 秒
     */
    @TableField(value = "RunTime")
    private Integer runTime;

    /**
     * 设备上传总里程 拔掉后会清零 单位M
     */
    @TableField(value = "AllMileage")
    private Integer allMileage;

    /**
     * 0:离线 1：在线
     */
    @TableField(value = "Status")
    private Integer status;

    /**
     * 在线开始时间
     */
    @TableField(value = "OnLineStartTime")
    private Date onLinestartTime;

    /**
     * 
     */
    @TableField(value = "UpdateTime")
    private Date updateTime;

    /**
     * 
     */
    @TableField(value = "CreateTime")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "UniqueId")
    private String uniqueId;

    /**
     * 开始移动时间
     */
    @TableField(value = "StartMoveTime")
    private Date startMoveTime;

    /**
     * 开始移动经度
     */
    @TableField(value = "StartMoveLongitude")
    private BigDecimal startMoveLongitude;

    /**
     * 开始移动纬度
     */
    @TableField(value = "StartMoveLatitude")
    private BigDecimal startMoveLatitude;

    /**
     * 移动里程相位 单位M
     */
    @TableField(value = "MoveMileage")
    private Long moveMileage;

    /**
     * 最后移动时间
     */
    @TableField(value = "EndMoveTime")
    private Date endMoveTime;

    /**
     * 最后移动经度
     */
    @TableField(value = "EndMoveLongitude")
    private BigDecimal endMoveLongitude;

    /**
     * 最后移动纬度
     */
    @TableField(value = "EndMoveLatitude")
    private BigDecimal endMoveLatitude;

    /**
     * 移动时间相位
     */
    @TableField(value = "MoveTime")
    private Date moveTime;

    /**
     * 车牌
     */
    @TableField(value = "LicencePlate")
    private String licencePlate;

    /**
     * 点位数量
     */
    @TableField(value = "PointCount")
    private Integer pointCount;

    /**
     * 实际行驶里程 单位M
     */
    @TableField(value = "ActualMileage")
    private Long actualMileage;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}