package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTOverTime;

/**
* @description 针对表【DFDW_DZCC_T_OVERTIME_SET(加班费计算规则配置表)】的数据库操作Service
*/
public interface DfdwDzccOverTimeService extends IService<DfdwDzccTOverTime> {


    /**
     * 新增
     * @return
     */
    int createOverTime(DfdwDzccTOverTime dfdwDzccTOverTime);

    /**
     * 删除
     * @return
     */
    void deleteOverTime(Long id);

    /**
     * 修改
     * @return
     */
    void updateOverTime(DfdwDzccTOverTime dfdwDzccTOverTime);

    /**
     * 获取加班计算规则page
     * @return
     */
    IPage<DfdwDzccTOverTime> getListPage(DfdwDzccTOverTime dfdwDzccTOverTime);
}
