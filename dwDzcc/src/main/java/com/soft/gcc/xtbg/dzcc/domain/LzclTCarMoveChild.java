package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 车辆情况补充-子表
* @TableName LZCL_T_Car_Move_Child
*/
@TableName(value ="LZCL_T_Car_Move_Child")
@Data
public class LzclTCarMoveChild implements Serializable {


    /**
    * 
    */
    @TableField(value = "Id")
    @JSONField(name = "Id")
    
    private Integer Id;
    /**
    * 父id
    */
    @TableField(value = "YwId")
    @JSONField(name = "YwId")
    
    private Integer YwId;
    /**
    * 车牌
    */
    @TableField(value = "LicencePlate")
    @JSONField(name = "LicencePlate")
    
    private String LicencePlate;
    /**
    * 单位id
    */
    @TableField(value = "GroupId")
    @JSONField(name = "GroupId")
    
    private Integer GroupId;
    /**
    * 月份
    */
    @TableField(value = "StartDate")
    @JSONField(name = "StartDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date StartDate;
    /**
    * 年份
    */
    @TableField(value = "EndDate")
    @JSONField(name = "EndDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date EndDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
