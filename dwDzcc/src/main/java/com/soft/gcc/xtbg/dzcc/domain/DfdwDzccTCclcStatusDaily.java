package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 电子出车-出车单-每日出车情况
 * @TableName DFDW_DZCC_T_CCLC_STATUS_DAILY
 */
@TableName(value ="DFDW_DZCC_T_CCLC_STATUS_DAILY")
@Data
public class DfdwDzccTCclcStatusDaily implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主表业务Id
     */
    @TableField(value = "ywId")
    private Integer ywid;

    /**
     * 车Id
     */
    @TableField(value = "carId")
    private Integer carid;

    /**
     * 司机Id
     */
    @TableField(value = "driveId")
    private Integer driveid;

    /**
     * 移动日期
     */
    @TableField(value = "moveDate")
    private LocalDate movedate;

    /**
     * 出车情况
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    private LocalDateTime updatetime;

    /**
     * 是否去过鄞州
     */
    @TableField(value = "isToYZ")
    private Boolean istoyz;

    /**
     * 创建人
     */
    @TableField(value = "createUserId")
    private Integer createuserid;

    /**
     * 修改人
     */
    @TableField(value = "updateUserId")
    private Integer updateuserid;

    @TableField(exist = false)
    private Integer year;

    @TableField(exist = false)
    private Integer month;

    @TableField(exist = false)
    private Long cdsNum;
    @TableField(exist = false)
    private Long kqyNum;
    @TableField(exist = false)
    private Long bqyNum;
    @TableField(exist = false)
    private Long yzNum;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}