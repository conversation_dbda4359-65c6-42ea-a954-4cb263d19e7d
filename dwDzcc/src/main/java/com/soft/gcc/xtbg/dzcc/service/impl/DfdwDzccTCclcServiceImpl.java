package com.soft.gcc.xtbg.dzcc.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTAddress;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCclc;
import com.soft.gcc.xtbg.dzcc.domain.DzccLcWorkflow;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTCcd;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCclcMapper;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTCcdMapper;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTAddressService;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCclcService;
import com.soft.gcc.xtbg.dzcc.service.IDzccLcWorkflowService;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.util.PdfUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_CCLC(电子出车单LC)】的数据库操作Service实现
 * @createDate 2022-10-21 14:17:15
 */
@Service
public class DfdwDzccTCclcServiceImpl extends ServiceImpl<DfdwDzccTCclcMapper, DfdwDzccTCclc>
        implements IDfdwDzccTCclcService {
    private static final Logger log = LoggerFactory.getLogger(DfdwDzccTCclcServiceImpl.class);
    @Autowired
    VDfdwDzccTCcdMapper ccdMapper;
    @Autowired
    IDfdwDzccTAddressService addressService;
    @Autowired
    IDzccLcWorkflowService lcWorkflowService;
    private final SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public Result<Object> GetCcdList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd cclc) {
        try {
            IPage<VDfdwDzccTCcd> list = new Page<>();
            list.setCurrent(cclc.getPageNum());
            list.setSize(cclc.getPageSize());
            if (cclc.getExecuteTime() != null) {
                LocalDateTime endDate = cclc.getExecuteTime().plusDays(1);
                cclc.setExecuteTime(endDate);
            }
            list = ccdMapper.GetCcdListPage(list, dzccPersonEntity, cclc);
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (VDfdwDzccTCcd ccd : list.getRecords()) {
                LocalDateTime time = ccd.getCcOpenTime();
                String localTime = df.format(time);
                time = LocalDateTime.parse(localTime.substring(0, 11) + ccd.getOpenTime(), df);
                ccd.setCcOpenTime(time);

                if (ccd.getCarId() == -2) {
                    ccd.setLicensePlate("其他车辆");
                    ccd.setDriverName("其他人员");
                }
            }
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    public static void main(String[] args) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String localTime = "2023-11-14 08:30:00.000";
        String aa = localTime.substring(0, 11);
        String BB = aa + "08:30:00";
        LocalDateTime time = LocalDateTime.parse(BB, df);
        System.out.println(time);
    }

    @Override
    public Result<Object> GetAddressListById(Map<String, String> map) {
        try {
            Integer id = ParseUtil.tryParseInt(map.get("id"));
            List<DfdwDzccTAddress> list = addressService.list(new LambdaQueryWrapper<DfdwDzccTAddress>()
                    .select(DfdwDzccTAddress::getId, DfdwDzccTAddress::getProvinces, DfdwDzccTAddress::getCity, DfdwDzccTAddress::getArea, DfdwDzccTAddress::getAreaText, DfdwDzccTAddress::getArriveState, DfdwDzccTAddress::getAddressInfo)
                    .eq(DfdwDzccTAddress::getApproveState, 2)
                    .apply("lcId in (select id from DFDW_DZCC_T_CCLC where mainId = " + id + " or id = " + id + ")")
                    .groupBy(DfdwDzccTAddress::getId, DfdwDzccTAddress::getProvinces, DfdwDzccTAddress::getCity, DfdwDzccTAddress::getArea, DfdwDzccTAddress::getAreaText, DfdwDzccTAddress::getArriveState, DfdwDzccTAddress::getAddressInfo)
                    .orderByAsc(DfdwDzccTAddress::getId,DfdwDzccTAddress::getProvinces, DfdwDzccTAddress::getCity, DfdwDzccTAddress::getArea)
            );
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> GetLcListById(Map<String, String> map) {
        try {
            Integer id = ParseUtil.tryParseInt(map.get("id"));
            List<DzccLcWorkflow> list = lcWorkflowService.list(new LambdaQueryWrapper<DzccLcWorkflow>()
                    .apply("ywID in (select id from DFDW_DZCC_T_CCLC where mainId = " + id + " or id = " + id + ")")
                    .eq(DzccLcWorkflow::getLc_defineID, 20015)
                    .orderByAsc(DzccLcWorkflow::getYwID, DzccLcWorkflow::getStartdate)
            );
            Map<Integer, List<DzccLcWorkflow>> collect = list.stream().collect(Collectors.groupingBy(DzccLcWorkflow::getYwID));
            return Result.ok(collect);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    /**
     * 获取出车单那导出列表
     *
     * @param dzccPersonEntity
     * @param cclc
     * @return
     */
    @Override
    public List<VDfdwDzccTCcd> getCcdDownLoadList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd cclc) {
        if (cclc.getExecuteTime() != null) {
            LocalDateTime endDate = cclc.getExecuteTime().plusDays(1);
            cclc.setExecuteTime(endDate);
        }
        List<VDfdwDzccTCcd> list = ccdMapper.getCcdList(dzccPersonEntity, cclc);
        return list;
    }

    @Override
    public List<DfdwDzccTCclc> getCcdByDriveIdAndTime(Integer driveId, String date) {
        return baseMapper.getCcdByDriveIdAndTime(driveId, date);
    }

    @Override
    public Result DownCcdXqById(String type, List<String> idList, DzccPersonEntity person, HttpServletResponse response) {
        //生成并上传pdf
        String localPath = System.getProperty("java.io.tmpdir");
        //临时文件目录
        List<String> pathList = new ArrayList<>();
        try (ServletOutputStream servletOutputStream = response.getOutputStream()) {
            File file = new File(localPath + "/yykj/dzcc/downloads/ccdxq/");
            if (!file.exists()) {
                file.mkdirs();
            }
            file = new File(localPath + "/yykj/dzcc/downloads/water/ccdxq/");
            if (!file.exists()) {
                file.mkdirs();
            }
            List<Integer> integerList = idList.stream().map(Integer::parseInt).collect(Collectors.toList());
            List<DfdwDzccTCclc> cclcList = baseMapper.getListCcdXq(integerList);
            long time = System.currentTimeMillis();
            Font boldFont10 = PdfUtil.getPdfChineseFont(10, Font.BOLD);
            Font boldFont20 = PdfUtil.getPdfChineseFont(20, Font.BOLD);
            // 预先创建字体对象
            Font boldFont = PdfUtil.getPdfChineseFont(12, Font.BOLD);
            Font normalFont = PdfUtil.getPdfChineseFont(12, Font.NORMAL);
            for (DfdwDzccTCclc cclc : cclcList) {
                String inputPath = localPath + "/yykj/dzcc/downloads/ccdxq/" + cclc.getId() + time + ".pdf";
                String outputPath = localPath + "/yykj/dzcc/downloads/water/ccdxq/" + cclc.getId() + time + ".pdf";
                try (FileOutputStream fos = new FileOutputStream(inputPath)) {
                    // 取目的地
                    List<DfdwDzccTAddress> addressList = addressService.list(
                            new LambdaQueryWrapper<DfdwDzccTAddress>()
                                    .apply("lcId in (select id from DFDW_DZCC_T_CCLC where mainId = " + cclc.getId() + " or id = " + cclc.getId() + ")")
                                    .orderByAsc(DfdwDzccTAddress::getCreateTime)
                    );
                    // 取流程表
                    List<DzccLcWorkflow> workflowList = lcWorkflowService.list(new LambdaQueryWrapper<DzccLcWorkflow>()
                            .apply("ywID in (select id from DFDW_DZCC_T_CCLC where mainId = " + cclc.getId() + " or id = " + cclc.getId() + ")")
                            .eq(DzccLcWorkflow::getLc_defineID, 20015)
                            .orderByAsc(DzccLcWorkflow::getYwID, DzccLcWorkflow::getStartdate)
                    );
                    //主表添加成功
                    if (cclc.getId() > 0) {
                        //1.打开文档并设置基本属性
                        Document document = new Document();

                        //writer
                        PdfWriter writer = PdfWriter.getInstance(document, fos);
                        writer.setViewerPreferences(PdfWriter.PageModeUseThumbs);
                        writer.setPageSize(PageSize.A4);
                        document.open();

                        //标题一
                        Paragraph title = new Paragraph(cclc.getApplyNo(), boldFont10);
                        //标题样式
                        title.setPaddingTop(0.1f);
                        title.setAlignment(Element.ALIGN_RIGHT);
                        document.add(title);
                        //标题二
                        title = new Paragraph("宁 波 永 耀 集 团 有 限 公 司", boldFont20);
                        //标题样式
                        title.setPaddingTop(0.1f);
                        title.setAlignment(Element.ALIGN_CENTER);
                        document.add(title);
                        //标题三
                        title = new Paragraph("电 子 出 车 单", boldFont20);
                        //标题样式
                        title.setPaddingTop(0.1f);
                        title.setAlignment(Element.ALIGN_CENTER);
                        document.add(title);

                        //第一表格列宽
                        float[] firtWidths = {200, 200, 200, 200};
                        PdfPTable table = new PdfPTable(firtWidths);
                        table.setTotalWidth(800);
                        table.setHorizontalAlignment(Element.ALIGN_CENTER);
                        table.setSpacingBefore(10f); // 前间距
                        table.setSpacingAfter(10f); // 后间距


                        //表格数据
                        //mock数据
                        Object[][] datas = {
                                {"用车人：", cclc.getYcrName(), "申请人：", cclc.getApplyUserName()},
                                {"申请单位：", cclc.getApplyDeptName(), "申请人电话：", cclc.getApplyPhone()},
                                {"出车时间：", sdf2.format(cclc.getCcOpenTime()), "结束时间：", sdf2.format(cclc.getExecuteTime())},
                                {"出车天数：", cclc.getCcDays(), "人数：", cclc.getApplyNum()},
                                {"上车地点：", cclc.getScdText(), "出行事由：", cclc.getNote()},
                                {"车辆类型：", cclc.getCarMold(), "车牌：", cclc.getLicencePlate()},
                                {"驾驶员：", cclc.getDriverName(), "驾驶员电话：", cclc.getDriverPhone()},
                                {"备注", cclc.getRemark()},
                                {"目的地", "备注xxx"},
                                {"意见", "备注xxx"},
                                {"本出车单原则上无需申请人或用车人签字确认，如出车时间、结束时间、上车地点目的地有误，需用车人或申请人签字确认", "备注xxx"}
                        };

                        //table数据填充

                        // 预先创建基础单元格样式
                        PdfPCell baseCell = new PdfPCell();
                        PdfUtil.setTableStyle(table, baseCell);

                        // 预处理目的地和意见内容
                        Paragraph destinationParagraph = null;
                        if (!addressList.isEmpty()) {
                            destinationParagraph = new Paragraph();
                            destinationParagraph.setFont(normalFont);
                            destinationParagraph.setLeading(2 * normalFont.getSize());
                            StringBuilder sb = new StringBuilder();
                            for (int k = 0; k < addressList.size(); k++) {
                                sb.append(k + 1).append("、").append(addressList.get(k).getAreaText());
                                if (addressList.get(k).getArriveState() == 1) {
                                    sb.append("√");
                                }
                                sb.append("\n");
                            }
                            destinationParagraph.add(sb.toString());
                        }

                        Paragraph workflowParagraph = null;
                        if (!workflowList.isEmpty()) {
                            workflowParagraph = new Paragraph();
                            workflowParagraph.setFont(normalFont);
                            workflowParagraph.setLeading(2 * normalFont.getSize());
                            StringBuilder sb = new StringBuilder();
                            for (int k = 0; k < workflowList.size(); k++) {
                                DzccLcWorkflow workflow = workflowList.get(k);
                                sb.append(k + 1).append("、")
                                .append(workflow.getLc_jdmc()).append(":")
                                .append(workflow.getFeed()).append("\n      ")
                                .append(workflow.getPersonName()).append(" 提交于 ")
                                .append(sdf.format(workflow.getStartdate())).append("\n");
                            }
                            workflowParagraph.add(sb.toString());
                        }

                        // 第10行自定义确认内容
                        String confirmString = "1.实际出车时间\n\n\n\n2.实际结束时间\n\n\n\n3.实际上车地点\n\n\n\n4.实际目的地\n\n\n\n                                           签名：\n\n";
                        Paragraph confirmParagraph = new Paragraph();
                        confirmParagraph.setFont(normalFont);
                        confirmParagraph.setLeading(2 * normalFont.getSize());
                        confirmParagraph.add(confirmString);

                        for (int i = 0; i < datas.length; i++) {
                            for (int j = 0; j < datas[i].length; j++) {
                                // 复用基础单元格
                                PdfPCell pdfCell = new PdfPCell(baseCell);
                                
                                // 设置内容和样式
                                String content = datas[i][j] == null ? "" : String.valueOf(datas[i][j]);
                                Paragraph paragraph;
                                
                                if (i > 6 && j == 1) {
                                    pdfCell.setRowspan(1);
                                    pdfCell.setColspan(3);
                                    
                                    if (i == 8 && destinationParagraph != null) {
                                        // 目的地
                                        paragraph = destinationParagraph;
                                    } else if (i == 9 && workflowParagraph != null) {
                                        // 意见
                                        pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                                        paragraph = workflowParagraph;
                                    } else if (i == 10) {
                                        // 意见
                                        pdfCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                                        paragraph = confirmParagraph;
                                    } else {
                                        paragraph = new Paragraph(content, normalFont);
                                    }
                                } else {
                                    // 普通单元格
                                    Font currentFont = (j == 1 || j == 3) ? normalFont : boldFont;
                                    paragraph = new Paragraph(content, currentFont);
                                }
                                
                                pdfCell.setPhrase(paragraph);
                                table.addCell(pdfCell);
                            }
                        }
                        document.add(table);
                        document.close();
                    }
                } catch (Exception e) {
                    throw new RuntimeException("导出异常,用户id：" + person.getId() + ", 出车单编号：" + cclc.getApplyNo() + ",异常原因：" + e.getMessage());
                }
                // 添加水印
                List<String> watermark = Arrays.asList(person.getRealName(), person.getGroupName(), sdf.format(new Date()));
                PdfUtil.pdfAddWaterMark(inputPath, outputPath, watermark);
                pathList.add(outputPath);
                file = new File(inputPath);
                if (file.exists()) {
                    file.delete();
                }
            }
            //=================生成word到设置浏览默认下载地址=================
            if ("pdf".equals(type)) {
                byte[] buffer = new byte[1024];
                file = new File(pathList.get(0));
                if (!file.exists()) {
                    log.info("文件不存在：" + pathList.get(0));
                }
                try (FileInputStream fis = new FileInputStream(file); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    int i = bis.read(buffer);
                    while (i != -1) {
                        servletOutputStream.write(buffer, 0, i);
                        i = bis.read(buffer);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("电子出车详情单文件下载失败：" + e.getMessage());
                }

            } else {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(servletOutputStream)) {
                    for (int i = 0; i < cclcList.size(); i++) {
                        String applyNo = cclcList.get(i).getApplyNo();
                        String filePath = pathList.get(i);
                        // 创建ZIP条目
                        ZipEntry zipEntry = new ZipEntry(applyNo + ".pdf");
                        zipOutputStream.putNextEntry(zipEntry);

                        // 读取文件并写入到ZIP输出流
                        byte[] pdfFile = FileUtil.readBytes(filePath);
                        zipOutputStream.write(pdfFile);
                        zipOutputStream.closeEntry();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("电子出车详情多文件压缩失败：" + e.getMessage());
                }
            }
            return Result.ok();
        } catch (Exception ex) {
            ex.printStackTrace();
            String s = "程序产生异常:" + ex.getMessage() + "!";
            log.error("出车单pdf下载失败，{}", s);
            return Result.error(s);
        } finally {
            for (String path : pathList) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
        }
    }


    @Override
    public IPage<VDfdwDzccTCcd> getCCDPageList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTCcd cclc) {
        IPage<VDfdwDzccTCcd> list = new Page<>();
        list.setCurrent(cclc.getPageNum());
        list.setSize(cclc.getPageSize());
        if (cclc.getExecuteTime() != null) {
            LocalDateTime endDate = cclc.getExecuteTime().plusDays(1);
            cclc.setExecuteTime(endDate);
        }
        list = ccdMapper.GetCcdListPage(list, dzccPersonEntity, cclc);
        return list;
    }

    @Override
    public Result<Object> GetCcdListById(Integer id) {
        VDfdwDzccTCcd ccd = ccdMapper.getCcdListById(id);
        if (ccd == null) {
            return Result.error("未查询到相关数据");
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = ccd.getCcOpenTime();
        String localTime = df.format(time);
        time = LocalDateTime.parse(localTime.substring(0, 11) + ccd.getOpenTime(), df);
        ccd.setCcOpenTime(time);
        if (ccd.getCarId() == -2) {
            ccd.setLicensePlate("其他车辆");
            ccd.setDriverName("其他人员");
        }
        return Result.ok(ccd);
    }
}




