package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTClbg;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CLBG】的数据库操作Mapper
* @createDate 2022-10-19 14:44:13
* @Entity com.yykj.app.dzcc.domain.DfdwDzccTClbg
*/
public interface DfdwDzccTClbgMapper extends BaseMapper<DfdwDzccTClbg> {

    List<DfdwDzccTClbg> selectFrist();

    IPage<DfdwDzccTClbg> GetBGDList(
            Page<DfdwDzccTClbg> page,
            @Param("unitName") String unitName,
            @Param("groupId") Integer groupId,
            @Param("dzccQx") Integer dzccQx,
            @Param("id") Integer id,
            @Param("topGroupId") Integer topGroupId
    );
    List<DfdwDzccTClbg> GetBgdListForBackHaul(
            @Param("dzccQx") Integer dzccQx,
            @Param("id") Integer id,
            @Param("topGroupId") Integer topGroupId);


}




