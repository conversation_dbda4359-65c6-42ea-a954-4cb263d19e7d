package com.soft.gcc.xtbg.dzcc.controller;


import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCarstatusdailyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description 测试接口
 * @date 2023/8/9 10:17:18
 */
@Controller
@RequestMapping({"/DzccTest"})
public class DzccTestController {
    private static final Logger log = LoggerFactory.getLogger(DzccTestController.class);
    @Autowired
    private IDfdwDzccTCarstatusdailyService carstatusdailyService;

    @RequestMapping("/carStatusDailyJob")
    @ResponseBody
    public Result carStatusDailyJob(@RequestParam("date") String s) {
        log.info("进入每日车辆出车情况");
        LocalDate date = LocalDate.parse(s, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Result result = carstatusdailyService.addCarStatusDaily(date);
        if(result.getCode()!=200){
            log.error("异常进入每日车辆出车情况 : "+result.getMessage());
        }
        return result;
    }
}
