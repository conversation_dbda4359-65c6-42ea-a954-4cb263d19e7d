package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTCarmovelc;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTCarmovelcService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTCarmovelcMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_CARMOVELC(节假日或者平时9点以后车辆移动Lc)】的数据库操作Service实现
* @createDate 2022-11-14 09:46:29
*/
@Service
public class DfdwDzccTCarmovelcServiceImpl extends ServiceImpl<DfdwDzccTCarmovelcMapper, DfdwDzccTCarmovelc>
    implements IDfdwDzccTCarmovelcService{

}




