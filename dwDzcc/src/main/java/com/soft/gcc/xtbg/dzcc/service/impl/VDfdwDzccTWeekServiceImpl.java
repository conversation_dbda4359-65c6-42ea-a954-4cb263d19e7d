package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.mapper.VDfdwDzccTWeekMapper;
import com.soft.gcc.xtbg.dzcc.service.VDfdwDzccTWeekService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_WEEK】的数据库操作Service实现
* @createDate 2024-12-16 14:43:09
*/
@Service
public class VDfdwDzccTWeekServiceImpl extends ServiceImpl<VDfdwDzccTWeekMapper, VDfdwDzccTWeek>
    implements VDfdwDzccTWeekService {

}




