package com.soft.gcc.xtbg.dzcc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTClyd;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_CLYD】的数据库操作Service
* @createDate 2022-11-07 09:48:07
*/
public interface IVDfdwDzccTClydService extends IService<VDfdwDzccTClyd> {
    Result<Object> GetClydList(DzccPersonEntity person, VDfdwDzccTClyd clyd);
}
