package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTSjtblc;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_DZCC_T_SJTBLC】的数据库操作Mapper
* @createDate 2023-05-26 11:22:17
* @Entity com.yykj.app.dzcc.domain.VDfdwDzccTSjtblc
*/
public interface VDfdwDzccTSjtblcMapper extends BaseMapper<VDfdwDzccTSjtblc> {

    List<VDfdwDzccTSjtblc> getSjtblcList(
            @Param("sjtblc") VDfdwDzccTSjtblc sjtblc,
            @Param("person") DzccPersonEntity person,
            @Param("groupIds") List<Integer> groupIds
    );

    IPage<VDfdwDzccTSjtblc> getSjtblcListPage(
            IPage<VDfdwDzccTSjtblc> list,
            @Param("sjtblc") VDfdwDzccTSjtblc sjtblc,
            @Param("person") DzccPersonEntity person,
            @Param("groupIds") List<Integer> groupIds
    );
}




