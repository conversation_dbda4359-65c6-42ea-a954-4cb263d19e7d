package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeek;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTWeekMain;
import com.soft.gcc.xtbg.dzcc.service.GfxmTCityService;
import com.soft.gcc.xtbg.dzcc.service.VDfdwDzccTWeekMainService;
import com.soft.gcc.xtbg.dzcc.service.VDfdwDzccTWeekService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

@RestController
@RequestMapping("/dzcc/v_t_week")
public class VDfdwDzccTWeekMainController extends DzccBaseController {

    @Resource
    public VDfdwDzccTWeekService vDfdwDzccTWeekService;
    @Resource
    public VDfdwDzccTWeekMainService  vDfdwDzccTWeekMainService;
    @Resource
    public GfxmTCityService cityService;

    @RequestMapping("/list")
    //@PreAuthorize("@ss.hasAnyPermi('NDWCCO1YSSP01QX01')")
    public Result<Object> list(@RequestBody VDfdwDzccTWeek vdfdwDzccTWeek) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        vdfdwDzccTWeek.setDzccPersonEntity(dzccPersonEntity);
        IPage<VDfdwDzccTWeekMain> res = vDfdwDzccTWeekMainService.listPage(vdfdwDzccTWeek);
        return Result.ok(res);
    }

    @PostMapping("/getLcListById")
    Result<Object> getLcListById(@RequestBody VDfdwDzccTWeek vdfdwDzccTWeek) {
        return vDfdwDzccTWeekMainService.GetLcListById(vdfdwDzccTWeek);
    }

}
