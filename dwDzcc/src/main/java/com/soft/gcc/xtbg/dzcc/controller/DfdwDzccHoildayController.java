package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccHoilday;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccHoildayService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 节假日配置
 * @date 2022/11/16 15:00:33
 */
@RequestMapping("/dzcc/hoilday")
@RestController
public class DfdwDzccHoildayController extends BaseController {
    @Autowired
    private IDfdwDzccHoildayService hoildayService;

    /**
     * 电子出车-PC-节假日配置列表
     * */
    @RequestMapping("/getList")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01JJR01QX01,NDWCC01JJR01QX02,NDWCC01JJR01QX03')")
    public Result<Object> getList(@RequestBody DfdwDzccHoilday ent) {
        IPage<DfdwDzccHoilday> list = new Page<>();
        list.setCurrent(ent.getPageNum());
        list.setSize(ent.getPageSize());

        list = hoildayService.page(list, new LambdaQueryWrapper<DfdwDzccHoilday>()
                .le(StringUtils.isNotEmpty(ent.getTime()),DfdwDzccHoilday::getStartDate,ent.getTime())
                .ge(StringUtils.isNotEmpty(ent.getTime()),DfdwDzccHoilday::getEndDate,ent.getTime())
                .orderByAsc(DfdwDzccHoilday::getCreateTime));
        return Result.ok(list);
    }

    @RequestMapping("/InsertHoilday")
    @PreAuthorize("@ss.hasPermi('NDWCC01JJR01QX01')")
    public Result<Object> InsertHoilday(@RequestBody DfdwDzccHoilday ent) {
        PersonEntity person = user();
        ent.setCreateTime(new Date());
        ent.setCreateUserId(person.getId());
        ent.setCreateUserName(person.getRealName());
        hoildayService.save(ent);
        return Result.ok(ent);
    }

    /**
     * 电子出车-PC-节假日配置修改
     * */
    @RequestMapping("/EditHoilday")
    @PreAuthorize("@ss.hasPermi('NDWCC01JJR01QX02')")
    public Result<Object> EditHoilday(@RequestBody DfdwDzccHoilday ent) {
        hoildayService.updateById(ent);
        return Result.ok(ent);
    }

    /**
     * 电子出车-PC-节假日配置删除
     * */
    @RequestMapping("/DelHoilday")
    @PreAuthorize("@ss.hasPermi('NDWCC01JJR01QX03')")
    public Result<Object> DelHoilday(@RequestBody DfdwDzccHoilday ent) {
        hoildayService.removeById(ent);
        return Result.ok(ent);
    }

}
