package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
*
* @TableName Person
*/
@TableName(value ="Person", autoResultMap = true)
@Data
public class DzccPerson extends DzccBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    *
    */
    @TableField(value = "LoginName")
    @JSONField(name = "LoginName")

    private String LoginName;
    /**
    *
    */
    @TableField(value = "RealName")
    @JSONField(name = "RealName")

    private String RealName;
    /**
    *
    */
    @TableField(value = "Password")
    @JSONField(name = "Password")

    private String Password;
    /**
    *
    */
    @TableField(value = "GroupID")
    @JSONField(name = "GroupID")

    private Integer GroupID;
    /**
    *
    */
    @TableField(value = "RoleId")
    @JSONField(name = "RoleId")

    private Integer RoleId;
    /**
    *
    */
    @TableField(value = "Telephone")
    @JSONField(name = "Telephone")

    private String Telephone;
    /**
    *
    */
    @TableField(value = "MsgType")
    @JSONField(name = "MsgType")

    private String MsgType;
    /**
    *
    */
    @TableField(value = "OA")
    @JSONField(name = "OA")

    private String OA;
    /**
    * 物资系统对应用户ID
    */
    @TableField(value = "WZ_UserID")
    @JSONField(name = "WZ_UserID")

    private Integer WZ_UserID;
    /**
    * 用于判断是集团企业还是施工单位(管理员1，其他是2)
    */
    @TableField(value = "type")
    @JSONField(name = "type")

    private Integer type;
    /**
    * 短号
    */
    @TableField(value = "Sphone")
    @JSONField(name = "Sphone")

    private String Sphone;
    /**
    * 专家次数
    */
    @TableField(value = "ZJ_CS")
    @JSONField(name = "ZJ_CS")

    private Integer ZJ_CS;
    /**
    * 是否有签名照
    */
    @TableField(value = "PhName")
    @JSONField(name = "PhName")

    private Integer PhName;
    /**
    * 身份证号码
    */
    @TableField(value = "CertificateID")
    @JSONField(name = "CertificateID")

    private String CertificateID;
    /**
    * 办公室电话
    */
    @TableField(value = "OfficePhone")
    @JSONField(name = "OfficePhone")

    private String OfficePhone;
    /**
    *
    */
    @TableField(value = "BFLoginName")
    @JSONField(name = "BFLoginName")

    private String BFLoginName;
    /**
    *
    */
    @TableField(value = "P_XH")
    @JSONField(name = "P_XH")

    private Integer P_XH;
    /**
    *
    */
    @TableField(value = "LoginName2")
    @JSONField(name = "LoginName2")

    private String LoginName2;
    /**
    *
    */
    @TableField(value = "oldID")
    @JSONField(name = "oldID")

    private Integer oldID;
    /**
     *
     */
    @TableField(value = "state")
    @JSONField(name = "state")

    private Integer state;

    @TableField(exist = false)
    @JSONField(name = "GroupName")
    private String GroupName;

    @TableField(exist = false)
    @JSONField(name = "RoleName")
    private String RoleName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
