package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTNotice;
import com.soft.gcc.xtbg.dzcc.domain.VDfdwDzccTNotice;
import com.soft.gcc.xtbg.dzcc.service.IDfdwDzccTNoticeService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTNoticeMapper;
import com.soft.gcc.xtbg.dzcc.service.IVDfdwDzccTNoticeService;

import com.soft.gcc.xtbg.base.controller.Result;

import com.soft.gcc.xtbg.dzcc.util.AliyunOSSUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_DZCC_T_NOTICE(电子出车通知)】的数据库操作Service实现
 * @createDate 2022-11-15 09:43:56
 */
@Service
public class DfdwDzccTNoticeServiceImpl extends ServiceImpl<DfdwDzccTNoticeMapper, DfdwDzccTNotice>
        implements IDfdwDzccTNoticeService {
    @Autowired
    TFileService tFileService;
    @Autowired
    IVDfdwDzccTNoticeService vNoticeService;

    @Override
    public Result<Object> GetNoticeList(DzccPersonEntity dzccPersonEntity, VDfdwDzccTNotice notice) {
        try {
            IPage<VDfdwDzccTNotice> list = new Page<>();
            list.setCurrent(notice.getPageNum());
            list.setSize(notice.getPageSize());
            String sql = "";
            // 1总管理2车队长3其他
            if (dzccPersonEntity.getDzccQx() == 1) {
                sql = "";
            } else if (dzccPersonEntity.getDzccQx() == 2 || dzccPersonEntity.getDzccQx() == 4) {
                sql = "groupId in (select deptId from DFDW_DZCC_T_CDZGL where cdzId = " + dzccPersonEntity.getId() + ")";
            } else if (dzccPersonEntity.getDzccQx() == 3) {
                sql = "groupId = " + dzccPersonEntity.getTopGroupId();
            }
            if (dzccPersonEntity.getDzccQx() == 0 || dzccPersonEntity.getDzccQx() == 3) {
                return Result.ok(list);
            }
            LocalDate startDate = LocalDate.now();
            if (notice.getStartTime() != null) {
                Instant instant = notice.getStartTime().toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                startDate = instant.atZone(zoneId).toLocalDate();
            }
            LocalDate endDate = LocalDate.now();
            if (notice.getEndTime() != null) {
                Instant instant = notice.getEndTime().toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                endDate = instant.atZone(zoneId).toLocalDate();
                endDate = endDate.plusDays(1);
            }

            LocalDate finalStartDate = startDate;
            LocalDate finalEndDate = endDate;
            list = this.vNoticeService.page(list, new LambdaQueryWrapper<VDfdwDzccTNotice>()
                    .and(notice.getGroupId() > 0, t -> t
                            .eq(VDfdwDzccTNotice::getGroupId, notice.getGroupId())
                            .or()
                            .eq(VDfdwDzccTNotice::getNoticeType, 1))
                    .ge(notice.getStartTime() != null, VDfdwDzccTNotice::getStartTime, notice.getStartTime())
                    .eq(notice.getIsOpen() > -1, VDfdwDzccTNotice::getIsOpen, notice.getIsOpen())
                    .and(notice.getStartTime() != null && notice.getEndTime() != null, t -> t
                            .and(s -> s.ge(VDfdwDzccTNotice::getEndTime, finalEndDate).le(VDfdwDzccTNotice::getStartTime, finalStartDate))
                            .or(s -> s.ge(VDfdwDzccTNotice::getStartTime, finalStartDate).lt(VDfdwDzccTNotice::getStartTime, finalEndDate))
                            .or(s -> s.ge(VDfdwDzccTNotice::getEndTime, finalStartDate).lt(VDfdwDzccTNotice::getEndTime, finalEndDate))
                    )
                    .isNotNull(dzccPersonEntity.getDzccQx() != 1, VDfdwDzccTNotice::getGroupId)
                    .apply(!"".equals(sql), sql)
                    .like(!"".equals(notice.getTitle()), VDfdwDzccTNotice::getTitle, notice.getTitle())
                    .orderByDesc(VDfdwDzccTNotice::getStartTime)
            );
            return Result.ok(list);
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> upload(DzccPersonEntity dzccPersonEntity, MultipartFile file, HttpServletRequest request, String type, Integer joinId) {
        try {
            if (null == file) {
                file = ((StandardMultipartHttpServletRequest) request).getMultiFileMap().get("uploadFile").get(0);
            }
            LocalDate localDate = LocalDate.now();
            String YEAR_MONTH = localDate.getYear() + "/" + localDate.getMonthValue() + "/";
            String path = "Upload/FileManage/Dzcc/sjtg/" + YEAR_MONTH;
            String hz = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
            String fileName = UUID.randomUUID() + "." + hz;
            String url = path + AliyunOSSUtils.uploadFile(path, fileName, file);
            String name = "1".equals(type) ? "标题图" : "视频";
            TFile sf = new TFile();
            //type
            sf.setFilename(name + "." + hz);
            sf.setFilepath(url);
            sf.setUploaddate(new Date());
            sf.setType("sjtg");
            sf.setPersonname(dzccPersonEntity.getRealName());
            sf.setPersonzgh(dzccPersonEntity.getLoginName());
            //lcDefine.LcID
            sf.setFunctionid(20017);
            //lcjd.lcjdID
            sf.setHjid(type);
            //1 2
            sf.setSubtname(type);
            if (tFileService.save(sf)) {
                return Result.ok(sf, "上传文件成功！");
            } else {
                return Result.error("上传文件失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> addSjtg(DzccPersonEntity dzccPersonEntity, DfdwDzccTNotice notice) {
        try {
            notice.setEditId(dzccPersonEntity.getId());
            Date date = new Date();
            notice.setCreateTime(date);
            //主表添加标题图base64
            TFile video = this.tFileService.getById(notice.getTitlefileid());
            String base64 = "data:image/png;base64,";
            if (video.getId() > 0) {
                base64 += Base64.encodeBase64String(AliyunOSSUtils.downloadFileStream(video.getFilepath()));
            }
            notice.setTitleImage(base64);
            this.save(notice);
            //主表添加成功
            if (notice.getId() > 0) {
                //修改T_file表的projectID
                List<Integer> fileIds = new ArrayList<>();
                fileIds.add(notice.getTitlefileid());
                fileIds.add(notice.getVideofileid());
                TFile file = new TFile();
                file.setProjectid(notice.getId());
                //文件关联联合业务主表
                this.tFileService.update(file, new LambdaQueryWrapper<TFile>().in(TFile::getId, fileIds));
            } else {
                return Result.error("新增失败!");
            }
            return Result.ok("新增成功!");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> editSjtg(DzccPersonEntity dzccPersonEntity, DfdwDzccTNotice notice) {
        try {
            notice.setEditId(dzccPersonEntity.getId());
            Date date = new Date();
            notice.setUpdateTime(date);
            //主表添加标题图base64
            TFile video = this.tFileService.getById(notice.getTitlefileid());
            String base64 = "data:image/png;base64,";
            if (video.getId() > 0) {
                base64 += Base64.encodeBase64String(AliyunOSSUtils.downloadFileStream(video.getFilepath()));
            }
            notice.setTitleImage(base64);
            //主表添加成功
            if (this.updateById(notice)) {
                List<TFile> files = this.tFileService.list(new LambdaQueryWrapper<TFile>()
                        .eq(TFile::getFunctionid, 20017)
                        .eq(TFile::getProjectid, notice.getId())
                        .eq(TFile::getType, "sjtg"));
                //修改T_file表的projectID
                List<Integer> fileIds = new ArrayList<>();
                fileIds.add(notice.getTitlefileid());
                fileIds.add(notice.getVideofileid());
                TFile file1 = new TFile();
                file1.setProjectid(notice.getId());
                //文件关联联合业务主表
                this.tFileService.update(file1, new LambdaQueryWrapper<TFile>().in(TFile::getId, fileIds));
                //需要删除的T_file表
                List<Integer> removefileIds = new ArrayList<>();
                for (TFile file : files) {
                    if ("1".equals(file.getSubtname()) && !file.getId().equals(notice.getTitlefileid())) {
                        removefileIds.add(file.getId());
                        AliyunOSSUtils.deleteFile(file.getFilepath());
                    } else if ("2".equals(file.getSubtname()) && !file.getId().equals(notice.getVideofileid())) {
                        removefileIds.add(file.getId());
                        AliyunOSSUtils.deleteFile(file.getFilepath());
                    }
                }
                if (removefileIds.size() > 0) {
                    //文件关联联合业务主表
                    this.tFileService.remove(new LambdaQueryWrapper<TFile>().in(TFile::getId, removefileIds).eq(TFile::getFunctionid, 20017).eq(TFile::getProjectid, notice.getId()).eq(TFile::getType, "sjtg"));
                }
            } else {
                return Result.error("修改失败!");
            }
            return Result.ok("修改成功!");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result<Object> deleteSjtg(Integer noticeId) {
        try {
            this.removeById(noticeId);
            List<TFile> files = this.tFileService.list(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getFunctionid, 20017)
                    .eq(TFile::getProjectid, noticeId)
                    .eq(TFile::getType, "sjtg")
            );
            for (TFile file : files) {
                AliyunOSSUtils.deleteFile(file.getFilepath());
            }
            this.tFileService.remove(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getFunctionid, 20017)
                    .eq(TFile::getProjectid, noticeId)
                    .eq(TFile::getType, "sjtg"));
            return Result.ok("删除成功!");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }
}




