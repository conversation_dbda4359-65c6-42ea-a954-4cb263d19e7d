package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DzccLcWorkflow;
import com.soft.gcc.xtbg.dzcc.service.IDzccLcWorkflowService;
import com.soft.gcc.xtbg.dzcc.mapper.DzccLcWorkflowMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【Lc_workFlow】的数据库操作Service实现
* @createDate 2022-10-21 17:07:10
*/
@Service
public class DzccLcWorkflowServiceImpl extends ServiceImpl<DzccLcWorkflowMapper, DzccLcWorkflow>
    implements IDzccLcWorkflowService{

}




