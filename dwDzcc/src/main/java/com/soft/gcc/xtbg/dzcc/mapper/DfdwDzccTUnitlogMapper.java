package com.soft.gcc.xtbg.dzcc.mapper;

import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTUnitlog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_UnitLog(办公和停车地点修改日志)】的数据库操作Mapper
* @createDate 2023-08-18 14:01:38
* @Entity com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTUnitlog
*/
public interface DfdwDzccTUnitlogMapper extends BaseMapper<DfdwDzccTUnitlog> {
    void addUnit(@Param("ids") List<Integer> ids);

    void addUnitForGroup(@Param("id") Integer id);

    void addStop(@Param("ids") List<Integer> ids);
}




