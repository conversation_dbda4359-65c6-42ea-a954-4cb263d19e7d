package com.soft.gcc.xtbg.clwx.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.DzccVGroupItem;
import com.soft.gcc.xtbg.dzcc.service.IDzccVGroupItemService;
import com.yyszc.wpbase.entity.GroupItem;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/21
 */
@RequestMapping("/clwx/common")
@RestController
public class CommonController extends BaseController {
    @Autowired
    IDzccVGroupItemService groupitemService;

    /**
     * 车辆维修-PC-通用-部门查询
     * */
    @RequestMapping("/groupList")
    public Result<Object> page() {
        PersonEntity person = user();
        if (person == null) {
            throw new RuntimeException("登录异常");
        }
        boolean isGroupAdmin = false;
        for (String roleVO : person.getRoleList()) {
            if ("协同办公-车辆维修-总管理".equals(roleVO)) {
                break;
            }
            if ("协同办公-车辆维修-部门管理".equals(roleVO)) {
                isGroupAdmin = true;
            }
        }
        List<DzccVGroupItem> list = groupitemService.list(new LambdaQueryWrapper<DzccVGroupItem>()
                .eq(DzccVGroupItem::getParentid, 1)
                .eq(DzccVGroupItem::getCategory, 1)
                .eq(isGroupAdmin, DzccVGroupItem::getId, person.getTopGroupId())
                .orderByAsc(DzccVGroupItem::getXH)
        );
        DzccVGroupItem group = new DzccVGroupItem();
        group.setId(-1);
        group.setGroupname("全部");
        list.add(0, group);
        return Result.ok(list);
    }
}
