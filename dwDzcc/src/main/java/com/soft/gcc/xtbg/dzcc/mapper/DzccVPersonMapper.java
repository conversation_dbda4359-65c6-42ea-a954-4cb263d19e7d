package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.dzcc.domain.DzccVPerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【vPerson(vPerson)】的数据库操作Mapper
* @createDate 2022-10-21 11:00:43
* @Entity com.yykj.app.dzcc.domain.DzccVPerson
*/
public interface DzccVPersonMapper extends BaseMapper<DzccVPerson> {

    List<DzccVPerson> GetDriverList(
            @Param(value = "isZGL") int isZGL,
            @Param(value = "personId") int personId,
            @Param("groupId") Integer groupId,
            @Param("topGroupId") Integer topGroupId,
            @Param("realName") String realName);

    IPage<DzccVPerson> GetCDZListPage(
            IPage<DzccVPerson> page,
            @Param(value = "personTypeId") int personTypeId,
            @Param(value = "name") String name,
            @Param(value = "manageGroupId") int manageGroupId,
            @Param(value = "deptId") int deptId);

    IPage<DzccVPerson> getList(
            IPage<DzccVPerson> page,
            @Param(value = "groupId") Integer groupId,
            @Param(value = "realName") String realName,
            @Param(value = "groupIds") List<Integer> groupIds);
}




