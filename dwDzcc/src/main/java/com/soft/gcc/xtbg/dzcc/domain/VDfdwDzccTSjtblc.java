package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName V_DFDW_DZCC_T_SJTBLC
 */
@TableName(value ="V_DFDW_DZCC_T_SJTBLC")
@Data
public class VDfdwDzccTSjtblc extends DzccBaseEntity implements Serializable {
    /**
     *
     */
    @TableField(value = "id")
    private Integer id;

    /**
     *
     */
    @TableField(value = "driverId")
    private Integer driverId;

    /**
     *
     */
    @TableField(value = "date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     *
     */
    @TableField(value = "mileage")
    private Integer mileage;

    /**
     *
     */
    @TableField(value = "imgFileId")
    private Integer imgFileId;

    /**
     *
     */
    @TableField(value = "isSubstitute")
    private Integer isSubstitute;

    /**
     *
     */
    @TableField(value = "substituteBeginDate")
    private Date substituteBeginDate;

    /**
     *
     */
    @TableField(value = "substituteEndDate")
    private Date substituteEndDate;

    /**
     *
     */
    @TableField(value = "createUserId")
    private Integer createUserId;

    /**
     *
     */
    @TableField(value = "approvingState")
    private Integer approvingState;

    /**
     *
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "driverName")
    private String driverName;

    /**
     *
     */
    @TableField(value = "carId")
    private Integer carId;

    /**
     *
     */
    @TableField(value = "licencePlate")
    private String licencePlate;

    /**
     *
     */
    @TableField(value = "createUserName")
    private String createUserName;

    /**
     *
     */
    @TableField(value = "groupid")
    private Integer groupid;

    @TableField(exist = false)
    private String groupName;

    @TableField(exist = false)
    private String carMold;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
