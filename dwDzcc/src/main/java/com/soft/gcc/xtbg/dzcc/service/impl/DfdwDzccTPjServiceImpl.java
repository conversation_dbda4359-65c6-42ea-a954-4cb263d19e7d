package com.soft.gcc.xtbg.dzcc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.dzcc.domain.DfdwDzccTPj;
import com.soft.gcc.xtbg.dzcc.service.DfdwDzccTPjService;
import com.soft.gcc.xtbg.dzcc.mapper.DfdwDzccTPjMapper;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_DZCC_T_PJ(电子出车单-评价)】的数据库操作Service实现
* @createDate 2025-01-21 14:33:25
*/
@Service
public class DfdwDzccTPjServiceImpl extends ServiceImpl<DfdwDzccTPjMapper, DfdwDzccTPj>
    implements DfdwDzccTPjService{

    @Override
    public Integer createDzccTPj(DfdwDzccTPj dfdwDzccTPj) {
        // 插入
        baseMapper.insert(dfdwDzccTPj);
        // 返回
        return dfdwDzccTPj.getId();
    }

    @Override
    public void updateDzccTPj(DfdwDzccTPj dfdwDzccTPj) throws RuntimeException {
        // 校验存在
        validateDzccTPjExists(dfdwDzccTPj.getId());
        // 更新
        baseMapper.updateById(dfdwDzccTPj);
    }

    @Override
    public void deleteDzccTPj(Collection<Integer> ids) throws RuntimeException {
        // 删除
        baseMapper.deleteBatchIds(ids);
    }

    private void validateDzccTPjExists(Integer id) throws RuntimeException {
        if (baseMapper.selectById(id) == null) {
            throw new RuntimeException("电子出车单-评价不存在");
        }
    }

    @Override
    public DfdwDzccTPj getDzccTPj(Integer id) {
        DfdwDzccTPj dzccTPj = baseMapper.selectById(id);
        // 20250123前的评论 评价星级修改 是否投诉变成服务是否满意（0 -> 1）修改
        if (dzccTPj.getCreateTime().compareTo(Date.valueOf(LocalDate.of(2025, 1, 23))) <= 0) {
            if (dzccTPj.getRate() == 3) {
                dzccTPj.setRate(5);
            }
            if (dzccTPj.getTsState() == 0) {
                dzccTPj.setTsState(1);
            } else if (dzccTPj.getTsState() == 1) {
                dzccTPj.setTsState(0);
            }
        }
        return dzccTPj;
    }

    @Override
    public List<DfdwDzccTPj> getDzccTPjList(Collection<Integer> ids) {
        List<DfdwDzccTPj> list = baseMapper.selectBatchIds(ids);
        for (DfdwDzccTPj dzccTPj : list) {
            // 20250123前的评论 评价星级修改 是否投诉变成服务是否满意（0 -> 1）修改
            if (dzccTPj.getCreateTime().compareTo(Date.valueOf(LocalDate.of(2025, 1, 23))) <= 0) {
                if (dzccTPj.getRate() == 3) {
                    dzccTPj.setRate(5);
                }
                if (dzccTPj.getTsState() == 0) {
                    dzccTPj.setTsState(1);
                } else if (dzccTPj.getTsState() == 1) {
                    dzccTPj.setTsState(0);
                }
            }
        }
        return list;
    }

    @Override
    public IPage<DfdwDzccTPj> getDzccTPjPage(DfdwDzccTPj dfdwDzccTPj, DzccPersonEntity person) {
        IPage<DfdwDzccTPj> page = baseMapper.getPage(new Page<>(dfdwDzccTPj.getPageNum(), dfdwDzccTPj.getPageSize()), dfdwDzccTPj, person);
        for (DfdwDzccTPj dzccTPj : page.getRecords()) {
            // 20250123前的评论 评价星级修改 是否投诉变成服务是否满意（0 -> 1）修改
            if (dzccTPj.getCreateTime().compareTo(Date.valueOf(LocalDate.of(2025, 1, 23))) <= 0) {
                if (dzccTPj.getRate() == 3) {
                    dzccTPj.setRate(5);
                }
                if (dzccTPj.getTsState() == 0) {
                    dzccTPj.setTsState(1);
                } else if (dzccTPj.getTsState() == 1) {
                    dzccTPj.setTsState(0);
                }
            }
        }
        return page;
    }

    @Override
    public List<DfdwDzccTPj> getDzccTPjList(DfdwDzccTPj dfdwDzccTPj, DzccPersonEntity person) {
        List<DfdwDzccTPj> list = baseMapper.getList(dfdwDzccTPj, person);
        for (DfdwDzccTPj dzccTPj : list) {
            // 20250123前的评论 评价星级修改 是否投诉变成服务是否满意（0 -> 1）修改
            if (dzccTPj.getCreateTime().compareTo(Date.valueOf(LocalDate.of(2025, 1, 23))) <= 0) {
                if (dzccTPj.getRate() == 3) {
                    dzccTPj.setRate(5);
                }
                if (dzccTPj.getTsState() == 0) {
                    dzccTPj.setTsState(1);
                } else if (dzccTPj.getTsState() == 1) {
                    dzccTPj.setTsState(0);
                }
            }
        }
        return list;
    }

    @Override
    public DfdwDzccTPj getDzccTPjByLcId(Integer lcId) {
       List<DfdwDzccTPj> list =  baseMapper.selectList(new LambdaQueryWrapper<DfdwDzccTPj>().eq(DfdwDzccTPj::getLcId,lcId).orderByDesc(DfdwDzccTPj::getCreateTime));
       if(list.size() == 0){
           return null;
       }
       DfdwDzccTPj dzccTPj = list.get(0);
        // 20250123前的评论 评价星级修改 是否投诉变成服务是否满意（0 -> 1）修改
        if (dzccTPj.getCreateTime().compareTo(Date.valueOf(LocalDate.of(2025, 1, 23))) <= 0) {
            if (dzccTPj.getRate() == 3) {
                dzccTPj.setRate(5);
            }
            if (dzccTPj.getTsState() == 0) {
                dzccTPj.setTsState(1);
            } else if (dzccTPj.getTsState() == 1) {
                dzccTPj.setTsState(0);
            }
        }
        return dzccTPj;
    }
}




