package com.soft.gcc.xtbg.dzcc.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* vPerson
* @TableName vPerson
*/
@TableName(value ="vPerson", autoResultMap = true)
@Data
public class DzccVPerson extends DzccBaseEntity implements Serializable {


    /**
    *
    */
    @TableField(value = "Id")
    @JSONField(name = "Id")

    private Integer Id;
    /**
    *
    */
    @TableField(value = "LoginName")
    @JSONField(name = "LoginName")

    private String LoginName;
    /**
    *
    */
    @TableField(value = "RealName")
    @JSONField(name = "RealName")

    private String RealName;
    /**
    *
    */
    @TableField(value = "Password")
    @JSONField(name = "Password")

    private String Password;
    /**
    *
    */
    @TableField(value = "RoleId")
    @JSONField(name = "RoleId")

    private Integer RoleId;
    /**
    *
    */
    @TableField(value = "Telephone")
    @JSONField(name = "Telephone")

    private String Telephone;
    /**
    *
    */
    @TableField(value = "MsgType")
    @JSONField(name = "MsgType")

    private String MsgType;
    /**
    *
    */
    @TableField(value = "OA")
    @JSONField(name = "OA")

    private String OA;
    /**
    *
    */
    @TableField(value = "type")
    @JSONField(name = "type")

    private Integer type;
    /**
    *
    */
    @TableField(value = "P_XH")
    @JSONField(name = "P_XH")

    private Integer P_XH;
    /**
    *
    */
    @TableField(value = "GroupId")
    @JSONField(name = "GroupId")

    private Integer GroupId;
    /**
    *
    */
    @TableField(value = "ParentId")
    @JSONField(name = "ParentId")

    private Integer ParentId;
    /**
    *
    */
    @TableField(value = "ParentName")
    @JSONField(name = "ParentName")

    private String ParentName;
    /**
    *
    */
    @TableField(value = "GroupType")
    @JSONField(name = "GroupType")

    private Integer GroupType;
    /**
    *
    */
    @TableField(value = "GroupName")
    @JSONField(name = "GroupName")

    private String GroupName;
    /**
    *
    */
    @TableField(value = "GroupDesc")
    @JSONField(name = "GroupDesc")

    private String GroupDesc;
    /**
    *
    */
    @TableField(value = "TopGroupId")
    @JSONField(name = "TopGroupId")

    private Integer TopGroupId;
    /**
    *
    */
    @TableField(value = "TopGroupName")
    @JSONField(name = "TopGroupName")

    private String TopGroupName;

    @TableField(exist = false)
    private String Sphone;

    @TableField(exist = false)
    private Integer personType;

    @TableField(exist = false)
    private String CertificateID;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
