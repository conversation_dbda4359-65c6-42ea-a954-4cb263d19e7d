package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.domain.HealthCheck;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.constants.Constant;
import com.soft.gcc.xtbg.base.constants.HttpConstant;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.service.impl.DfdwClwxTBxdServiceImpl;
import com.soft.gcc.xtbg.dzcc.domain.DzccCar;
import com.soft.gcc.xtbg.dzcc.entity.LocusDto;
import com.soft.gcc.xtbg.dzcc.mapper.DzccCarMapper;
import com.soft.gcc.xtbg.dzcc.mapper.DzccLzclTCarRentMapper;
import com.soft.gcc.xtbg.dzcc.service.IDzccLzclTCarRentService;
import com.soft.gcc.xtbg.dzcc.util.HttpClientUtils;
import com.soft.gcc.xtbg.dzcc.util.InfluxDBUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@RequestMapping("/dzcc/locus")
@RestController
public class DzccLocusController extends DzccBaseController{
    @Autowired
    IDzccLzclTCarRentService carRentService;
    @Autowired
    DzccCarMapper dzccCarMapper;
    @Autowired
    DzccLzclTCarRentMapper carRentMapper;
    private static final Logger log = LoggerFactory.getLogger(DzccLocusController.class);

    /**
     * 得到省份信息电子出车单
     * */
    @RequestMapping("/getLocusList")
    @ResponseBody
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01GJ01QX01,NDWCC01CD02QX03,NDWCC01CY02QX03')")
    public Result<Object> getLocusList(@RequestBody LocusDto locusDto) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        if (dzccPersonEntity == null) {
            return Result.error(HttpConstant.NOAUTH_CODE, Constant.EMPTY);
        }
        return this.carRentService.getLocusList(dzccPersonEntity, locusDto);
    }

    /**
     * 电子出车-PC-车辆管理-车辆轨迹查询
     * */
    @RequestMapping("/getPolylinePoints")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01GJ01QX01,NDWCC01CD02QX03,NDWCC01CY02QX03')")
    public Result<Object> getPolylinePoints(String carId, String startDate, String endDate, HttpServletRequest request) throws ParseException {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        if (dzccPersonEntity.getDzccQx() == 0 || dzccPersonEntity.getDzccQx() == 3) {
            return Result.error(10001, "你没有权限查看此页面");
        }
        List<DzccCar> dzccCarList = dzccCarMapper.SelectCarPerson(dzccPersonEntity.getId(), dzccPersonEntity.getDzccQx(), Integer.valueOf(carId));
        if(dzccCarList ==null || dzccCarList.isEmpty()) {
            return Result.error(10001, "车辆信息不存在或当前车辆未在管理范围内");
        }
        int length = 16;
        if (startDate.length() == length) {
            startDate += ":00";
        }
        if (endDate.length() == length) {
            endDate += ":00";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd");
        Date startDateValue = dateFormat.parse(startDate);
        Date endDateValue = dateFormat.parse(endDate);
        if ((endDateValue.getTime() - startDateValue.getTime()) > 604800000) {
            return Result.error(10001, "查询时间跨度不能超过七天");
        } else if (startDateValue.compareTo(endDateValue) > 0) {
            return Result.error(10001, "开始时间不能超过结束时间");
        } else if (dzccCarList.get(0).getGPSEnableDate() != null && startDateValue.compareTo(dzccCarList.get(0).getGPSEnableDate()) < 0) {
            return Result.error(10001, "请选择设备启用后的时间：" + dateFormat2.format(dzccCarList.get(0).getGPSEnableDate()));
        }

        SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
        startDate = zdateFormat.format(startDateValue);
        endDate = zdateFormat.format(endDateValue);
//        String query = String.format(
//                "import \"influxdata/influxdb/schema\" "
//                        + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
//                        + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
//                        + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
//                        + " and r[\"deviceId\"] == \"%s\")"
//                        + " |> schema.fieldsAsCols() "
//                        + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"speed\"])"
//                , InfluxDBUtil.bucket, startDate, endDate, "vehicle_position", dzccCarList.get(0).getGPSNum());
//
//        List<FluxTable> tables;
//        try {
//            InfluxDBClient influxDBClient = InfluxDBUtil.getInfluxDBClient();
//            HealthCheck health = influxDBClient.health();
//            if (health.getStatus()  == HealthCheck.StatusEnum.PASS) {
//                log.info(" 连接成功 ✅ | 服务状态: {}", health.getMessage());
//            } else {
//                log.error(" 连接异常 ❌ | 错误信息: {}", health.getMessage());
//            }
//            if (influxDBClient == null) {
//                log.error("时序库连接为空：{}, {}, {}, {}, {}", InfluxDBUtil.ipAddress, InfluxDBUtil.token, InfluxDBUtil.bucket, InfluxDBUtil.measurement, InfluxDBUtil.org);
//            }
//            log.info("时序库连接信息：{}, {}, {}, {}, {}", InfluxDBUtil.ipAddress, InfluxDBUtil.token, InfluxDBUtil.bucket, InfluxDBUtil.measurement, InfluxDBUtil.org);
//            log.info("时序库influxDBClient：{}", influxDBClient);
//            log.info("时序库influxDBClient.getQueryApi()：{}", influxDBClient.getQueryApi());
//            log.info("查询轨迹数据：{}, {}", query, InfluxDBUtil.org);
//            tables = influxDBClient.getQueryApi().query(query, InfluxDBUtil.org);
//            log.info("查询轨迹数据：{}", tables);
//        } catch (Exception e) {
//            InfluxDBUtil.reloadInfluxDBClinet();
//            log.error("查询轨迹数据异常", e);
//            return Result.error("获取轨迹数据出错" + e.getMessage());
//        }
//
//        ArrayList<HashMap<String, Object>> carLocationList = new ArrayList<>();
//        for (FluxTable table : tables) {
//            for (FluxRecord record : table.getRecords()) {
//                HashMap<String, Object> map = new HashMap<>();
//                map.put("speed", record.getValues().get("speed"));
//                map.put("longitude", record.getValues().get("longitude"));
//                map.put("latitude", record.getValues().get("latitude"));
////                map.put("loc_time",record.getValues().get("_time").toString());
////                map.put("loc_time",zdateFormat.parse(record.getValues().get("_time").toString().substring(0,19)+"Z").getTime()/1000);
//                map.put("loc_time", zdateFormat.parse(record.getValues().get("_time").toString().substring(0, 19) + "Z").getTime());
////                map.put("coord_type_input","bd09ll");
//                carLocationList.add(map);
//            }
//        }
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("carLocationList", carLocationList);

        HashMap<String, String> headers = new HashMap<>();
        headers.put("Authorization", request.getHeader("Authorization"));
        String cookie = request.getHeader("Cookie");
        String url = "/dwKqgl/kqtj/getDzccPoints";
        Map<String, Object> map = new HashMap<>();
        map.put("GPSNum", dzccCarList.get(0).getGPSNum());
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        Result result = HttpClientUtils.doGetKqgl(url, map, headers, cookie);

        return result;
    }

    /**
     * 校验车辆id与车牌号
     * */
    @RequestMapping("/checkCarPerson")
    @ResponseBody
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01GJ01QX01,NDWCC01CD02QX03,NDWCC01CY02QX03')")
    public Result<Object> checkCarPerson(@RequestBody LocusDto locusDto) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        List<DzccCar> dzccCarList = dzccCarMapper.SelectCarPerson(dzccPersonEntity.getId(), dzccPersonEntity.getDzccQx(), locusDto.getCarId());
        if(dzccCarList == null || dzccCarList.isEmpty()) {
            return Result.error("车辆信息不存在或当前车辆未在管理范围内");
        } else if (!dzccCarList.get(0).getLicencePlate().equals(locusDto.getLicensePlate())) {
            return Result.error("车牌与车辆id对应不上");
        }
        return Result.ok();
    }

    @RequestMapping("/getNotLocusList")
    @ResponseBody
    @PreAuthorize("@ss.hasPermi('NDWCC01GJ02QX01')")
    public Result<Object> getNotLocusList(@RequestBody LocusDto locusDto) {
        DzccPersonEntity person = getDzccPerson();
        return this.carRentService.getNotLocusList(person, locusDto);
    }

    @RequestMapping("/getNotLocusDownload")
    @ResponseBody
    @PreAuthorize("@ss.hasPermi('NDWCC01GJ02QX02')")
    public Result<Object> getNotLocusDownload(@RequestBody LocusDto locusDto, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<LocusDto> list = new ArrayList<>();
            if (person.getDzccQx() != 3 && person.getDzccQx() > 0) {
                list = carRentMapper.getNotLocusList(locusDto, person);
            }

            XSSFSheet sheet = workbook.createSheet("无轨迹告警");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(locusDto.getGroupName() + "无轨迹告警");
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("部门");
            row.createCell(1).setCellValue("车型");
            row.createCell(2).setCellValue("车牌");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getGroupName());
                row.createCell(1).setCellValue(list.get(i).getLicensePlate());
                row.createCell(2).setCellValue(list.get(i).getCarMold());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + "GPS异常记录.xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return Result.ok();
        } catch (Exception Ex) {
            log.error("getNotLocusDownload Error:", Ex);
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }


    /**
     * 车辆轨迹导出
     * @param locusDto
     * @param response
     * @return
     */
    @RequestMapping("/DownLoad")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01GJ01QX01,NDWCC01CD02QX03,NDWCC01CY02QX03')")
    public Result<Object> downLoad(@RequestBody LocusDto locusDto, HttpServletResponse response) {
        DzccPersonEntity person = getDzccPerson();

        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            List<LocusDto> list = new ArrayList<>();
            if (person.getDzccQx() != 3 && person.getDzccQx() > 0) {
                list = carRentMapper.getLocusList(person, locusDto);
            }

            XSSFSheet sheet = workbook.createSheet("车辆轨迹列表信息");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            if(locusDto.getGroupId() == -1){
                titleCell.setCellValue("全部" + "车辆轨迹列表信息");
            }else{
                titleCell.setCellValue(locusDto.getGroupName() + "车辆轨迹列表信息");
            }

            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("部门");
            row.createCell(1).setCellValue("车牌");
            row.createCell(2).setCellValue("司机");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getGroupName());
                row.createCell(1).setCellValue(list.get(i).getLicensePlate());
                row.createCell(2).setCellValue(list.get(i).getDriverName());
                row.setRowStyle(cellStyle);
            }


            //=================生成excel到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + "车辆轨迹列表信息.xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

            return Result.ok();
        } catch (Exception Ex) {
            log.error("DownLoad Error:", Ex);
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    /**
     * 获取车辆轨迹列表
     * @return
     */
    @RequestMapping("/getPolylinePointsTest")
    public Result getPolylinePoints(String deviceId, String startDate, String endDate, Integer tag) throws ParseException {
        if (startDate.length() == 16) startDate += ":00";
        if (endDate.length() == 16) endDate += ":00";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date startDateValue = dateFormat.parse(startDate);
        Date endDateValue = dateFormat.parse(endDate);
        if ((endDateValue.getTime() - startDateValue.getTime()) > 604800000) {
            return Result.error(10001, "查询时间跨度不能超过七天");
        } else if (startDate.compareTo(endDate) > 0) {
            return Result.error(10001, "开始时间不能超过结束时间");
        }

        SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
        startDate = zdateFormat.format(startDateValue);
        endDate = zdateFormat.format(endDateValue);
        String query = String.format(
                "import \"influxdata/influxdb/schema\" "
                        + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                        + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                        + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                        + " and r[\"deviceId\"] == \"%s\")"
                        + " |> schema.fieldsAsCols() "
                        + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"speed\"])"
                , InfluxDBUtil.bucket, startDate, endDate, InfluxDBUtil.measurement, deviceId);

        List<FluxTable> tables = null;
        try {
            tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
        } catch (Exception e) {
            InfluxDBUtil.reloadInfluxDBClinet();
        }

        ArrayList<Map> carLocationList = new ArrayList();
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                HashMap map = new HashMap();
                map.put("speed", record.getValues().get("speed"));
                map.put("longitude", record.getValues().get("longitude"));
                map.put("latitude", record.getValues().get("latitude"));
//                                map.put("loc_time",record.getValues().get("_time").toString());
//                map.put("loc_time",zdateFormat.parse(record.getValues().get("_time").toString().substring(0,19)+"Z").getTime()/1000);
                map.put("loc_time", zdateFormat.parse(record.getValues().get("_time").toString().substring(0, 19) + "Z").getTime());
//                map.put("coord_type_input","bd09ll");
                carLocationList.add(map);
            }
        }

        HashMap hashMap = new HashMap();
        hashMap.put("carLocationList", carLocationList);
        return Result.ok(hashMap);
    }
}
