package com.soft.gcc.xtbg.clwx.service;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.clwx.domain.DfdwClwxTBxd;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @description 针对表【DFDW_CLWX_T_BXD(车辆维修（新）-维修申请主表)】的数据库操作Service
* @createDate 2024-10-17 16:11:09
*/
public interface DfdwClwxTBxdService extends IService<DfdwClwxTBxd> {

    Result<Object> getPage(DfdwClwxTBxd bxd, DzccPersonEntity user);

    Result<Object> GetLcListById(DfdwClwxTBxd bxd);

    Result downWxdXqByIds(DfdwClwxTBxd bxd, DzccPersonEntity user, HttpServletResponse response);

    Result downloadFile(DfdwClwxTBxd bxd, DzccPersonEntity user, HttpServletResponse response);
}
