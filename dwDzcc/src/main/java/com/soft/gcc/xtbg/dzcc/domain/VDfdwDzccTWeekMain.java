package com.soft.gcc.xtbg.dzcc.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName V_DFDW_DZCC_T_WEEK_MAIN
 */
@TableName(value ="V_DFDW_DZCC_T_WEEK_MAIN")
@Data
public class VDfdwDzccTWeekMain implements Serializable {
    /**
     *
     */
    @TableField(value = "id")
    private Integer id;

    /**
     *
     */
    @TableField(value = "year")
    private Integer year;

    /**
     *
     */
    @TableField(value = "month")
    private Integer month;

    /**
     *
     */
    @TableField(value = "week")
    private Integer week;

    /**
     *
     */
    @TableField(value = "startTime")
    private Date startTime;

    /**
     *
     */
    @TableField(value = "endTime")
    private Date endTime;

    /**
     *
     */
    @TableField(value = "weekDays")
    private Integer weekDays;

    /**
     *
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "oringDelay")
    private BigDecimal oringDelay;

    /**
     *
     */
    @TableField(value = "appendDelay")
    private BigDecimal appendDelay;

    /**
     *
     */
    @TableField(value = "appendDelayYsp")
    private BigDecimal appendDelayYsp;

    /**
     *
     */
    @TableField(value = "lcDefineID")
    private Integer lcDefineID;

    /**
     *
     */
    @TableField(value = "lcName")
    private String lcName;

    /**
     *
     */
    @TableField(value = "ywID")
    private Integer ywID;

    /**
     *
     */
    @TableField(value = "sendPerson")
    private String sendPerson;

    /**
     *
     */
    @TableField(value = "sendPersonZgh")
    private String sendPersonZgh;

    /**
     *
     */
    @TableField(value = "AllPersonZgh")
    private String allPersonZgh;

    /**
     *
     */
    @TableField(value = "isMany")
    private Integer isMany;

    /**
     *
     */
    @TableField(value = "lcJdmc")
    private String lcJdmc;

    /**
     *
     */
    @TableField(value = "lcJdid")
    private Integer lcJdid;

    /**
     *
     */
    @TableField(value = "lcIsback")
    private Integer lcIsback;

    /**
     *
     */
    @TableField(value = "lcTojdid")
    private String lcTojdid;

    /**
     *
     */
    @TableField(value = "number")
    private Integer number;

    /**
     *
     */
    @TableField(value = "BXType")
    private String BXType;

    @TableField(value = "applyUserName")
    private String applyUserName;

    @TableField(value = "applyUserId")
    private String applyUserId;
    @TableField(value = "approveState")
    private Integer approveState;
    @TableField(value = "applyTime")
    private Date applyTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
