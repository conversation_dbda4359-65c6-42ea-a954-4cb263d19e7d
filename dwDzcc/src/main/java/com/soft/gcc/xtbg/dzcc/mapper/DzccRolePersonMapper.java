package com.soft.gcc.xtbg.dzcc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dzcc.domain.DzccRolePerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【RolePerson】的数据库操作Mapper
* @createDate 2022-10-21 13:53:34
* @Entity com.yykj.app.dzcc.domain.DzccRolePerson
*/
public interface DzccRolePersonMapper extends BaseMapper<DzccRolePerson> {

    void saveBatch1(
            @Param("roleList") List<DzccRolePerson> roleList);
}




