package com.soft.gcc.xtbg.dzcc.controller;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.service.IDzccPersonService;
import com.soft.gcc.xtbg.dzcc.service.IDzccVPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 人员权限
 */
@RequestMapping("/dzcc/ryqx")
@RestController
public class DzccRyqxController extends DzccBaseController{
    @Autowired
    IDzccVPersonService vPersonService;

    /**
     * 电子出车-PC-人员权限-部门查询
     * */
    @RequestMapping("/GetGroupList")
    @PreAuthorize("@ss.hasPermi('NDWCC01RY01QX01')")
    public Result<Object> GetGroupList(@RequestParam Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.vPersonService.GetGroupList(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-人员权限-人员查询
     * */
    @RequestMapping("/GetPersonList")
    @PreAuthorize("@ss.hasPermi('NDWCC01RY01QX01')")
    public Result<Object> GetPersonList(@RequestBody Map<String, String> map) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return this.vPersonService.GetPersonList(dzccPersonEntity, map);
    }

    /**
     * 电子出车-PC-人员权限-根据id查询角色
     * */
    @RequestMapping("/GetPersonRoleById")
    @PreAuthorize("@ss.hasPermi('NDWCC01RY01QX01')")
    public Result<Object> GetPersonRoleById(@RequestBody Map<String, String> map) {
        return this.vPersonService.GetPersonRoleById(map);
    }

    /**
     * 电子出车-PC-人员权限-角色查询
     * */
    @RequestMapping("/GetRoles")
    @PreAuthorize("@ss.hasPermi('NDWCC01RY01QX01')")
    public Result<Object> GetRoles() {
        return this.vPersonService.GetRoles();
    }

    /**
     * 电子出车-PC-人员权限-编辑权限
     * */
    @RequestMapping("/EditRoles")
    @PreAuthorize("@ss.hasPermi('NDWCC01RY01QX01')")
    public Result<Object> EditRoles(@RequestBody Map<String, String> map) {
        return this.vPersonService.EditRoles(map);
    }
}
