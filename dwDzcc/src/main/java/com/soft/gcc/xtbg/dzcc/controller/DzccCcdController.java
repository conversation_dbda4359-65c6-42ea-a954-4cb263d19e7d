package com.soft.gcc.xtbg.dzcc.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.domain.*;
import com.soft.gcc.xtbg.dzcc.entity.PcDto;
import com.soft.gcc.xtbg.dzcc.service.*;
import com.soft.gcc.xtbg.dzcc.util.ParseUtil;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.docx4j.wml.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @description 出车单
 */
@RequestMapping("/dzcc/ccd")
@RestController
public class DzccCcdController extends DzccBaseController {

    private static final Logger log = LoggerFactory.getLogger(DzccCcdController.class);
    @Autowired
    IDfdwDzccTCclcService cclcService;
    @Autowired
    IVDfdwDzccTCcdService ivDfdwDzccTCcdService;
    @Autowired
    IDfdwDzccTAddressService addressService;
    @Autowired
    IDzccLcWorkflowService lcWorkflowService;
    @Autowired
    private IVDfdwDzccTCcdtimeService ccdtimeService;
    @Resource
    private GfxmTCityService gfxmTCityService;
    @Resource
    private DfdwDzccTCcddService dfdwDzccTCcddService;
    @Autowired
    private IDfdwDzccTCclcdetailService dfdwDzccTCclcdetailService;
    private final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @RequestMapping("/GetCcdList")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD02QX01')")
    public Result<Object> GetCcdList(@RequestBody VDfdwDzccTCcd cclc) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        return cclcService.GetCcdList(dzccPersonEntity, cclc);
    }

    @RequestMapping("/GetAddressListById")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD02QX01')")
    public Result<Object> GetAddressListById(@RequestBody Map<String, String> map) {
        return cclcService.GetAddressListById(map);
    }

    @RequestMapping("/GetLcListById")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD02QX01')")
    public Result<Object> GetLcListById(@RequestBody Map<String, String> map) {
        return cclcService.GetLcListById(map);
    }


    /**
     * 每日出车时间
     * @param map
     * @return
     */
    @RequestMapping("/getCcdTimeByLcId")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD02QX01')")
    public Result<Object> getCcdTimeByLcId(@RequestBody Map<String, String> map) {
        Integer id = ParseUtil.tryParseInt(map.get("id"));
        DfdwDzccTCclc cclc =  cclcService.getById(id);

        List<DfdwDzccTCclcdetail> list = new ArrayList<>();
        //已审批 已到达
        if(cclc!=null && cclc.getApproveState()==2 && cclc.getExecuteState() == 2 ){
            list = dfdwDzccTCclcdetailService.list(new LambdaQueryWrapper<DfdwDzccTCclcdetail>()
                    .eq(DfdwDzccTCclcdetail::getYwId, id).orderByAsc(DfdwDzccTCclcdetail::getStartTime));
        }
        return Result.ok(list);
    }

    @RequestMapping("/DownCcdXqById")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD02QX02')")
    @ResponseBody
    public Result DownCcdXqById(@RequestBody PcDto pcDto, HttpServletResponse response) throws IOException {
        DzccPersonEntity person = getDzccPerson();
        List<String> idList = Arrays.asList(pcDto.getContent().split(","));
        String type = pcDto.getType();
        if (idList.size() == 0) {
            return Result.ok();
        }
        return cclcService.DownCcdXqById(type, idList, person, response);
    }

    /**
     * 出车单导出
     *
     * @param response
     * @return
     */
    @RequestMapping("/DownLoadCcd")
    @PreAuthorize("@ss.hasPermi('NDWCC01CD02QX02')")
    public Result<Object> DownLoadCcd(@RequestBody VDfdwDzccTCcd cclc, HttpServletResponse response) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            List<VDfdwDzccTCcd> list = cclcService.getCcdDownLoadList(dzccPersonEntity, cclc);

            XSSFSheet sheet = workbook.createSheet("出车单信息");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("出车单信息");
            sheet.setDefaultColumnWidth(25);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("出车单编号");
            row.createCell(1).setCellValue("出车时间");
            row.createCell(2).setCellValue("结束时间");
            row.createCell(3).setCellValue("目的地");
            row.createCell(4).setCellValue("人数");
            row.createCell(5).setCellValue("上车地点");
            row.createCell(6).setCellValue("用车是由");
            row.createCell(7).setCellValue("申请人");
            row.createCell(8).setCellValue("用车人");
            row.createCell(9).setCellValue("申请单位");
            row.createCell(10).setCellValue("车辆类型");
            row.createCell(11).setCellValue("车牌号");
            row.createCell(12).setCellValue("驾驶员");
            row.createCell(13).setCellValue("审核状态");
            row.createCell(14).setCellValue("执行状态");
            row.createCell(15).setCellValue("实际开始时间");
            row.createCell(16).setCellValue("实际结束时间");
            row.createCell(17).setCellValue("车辆标识");

            String approveStateText = "";
            String executeStateText = "";
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getCarId() == -2) {
                    list.get(i).setLicensePlate("其他车辆");
                    list.get(i).setDriverName("其他人员");
                }
                //审批状态
                Integer approveState = list.get(i).getApproveState();
                approveStateText = approveState == 0 ? "未提交" : approveState == 1 ? "审批中" : approveState == 2 ? "已审批" : approveState == 3 ? "已驳回" : approveState == 4 ? "流程终止" : "";
                //执行状态
                Integer executeState = list.get(i).getExecuteState();
                executeStateText = executeState == 0 ? "未派车" : executeState == 1 ? "已派车" : executeState == 2 ? "已到达" : executeState == 3 ? "派车撤回" : executeState == 4 ? "已取消" : executeState == 5 ? "车辆异常" : "";

                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getApplyNo());
                row.createCell(1).setCellValue(df.format(list.get(i).getCcOpenTime()));
                row.createCell(2).setCellValue(df.format(list.get(i).getExecuteTime()));
                row.createCell(3).setCellValue(list.get(i).getAddressInfo());
                row.createCell(4).setCellValue(list.get(i).getApplyNum());
                row.createCell(5).setCellValue(list.get(i).getScdText());
                row.createCell(6).setCellValue(list.get(i).getNote());
                row.createCell(7).setCellValue(list.get(i).getApplyUserName());
                row.createCell(8).setCellValue(list.get(i).getYcrName());
                row.createCell(9).setCellValue(list.get(i).getApplyTopDeptName());
                row.createCell(10).setCellValue(list.get(i).getCarMold());
                row.createCell(11).setCellValue(list.get(i).getLicensePlate());
                row.createCell(12).setCellValue(list.get(i).getDriverName());
                row.createCell(13).setCellValue(approveStateText);
                row.createCell(14).setCellValue(executeStateText);
                if( list.get(i).getActualStartTime() !=null){
                    row.createCell(15).setCellValue(sdf.format(list.get(i).getActualStartTime()));
                    row.createCell(16).setCellValue(sdf.format(list.get(i).getActualEndTime()));
                }else{
                    row.createCell(15).setCellValue("");
                    row.createCell(16).setCellValue("");
                }
                if (list.get(i).getCarTag() != null) {
                    if (list.get(i).getCarTag() == 0) {
                        row.createCell(17).setCellValue("临租车辆");
                    } else if (list.get(i).getCarTag() == 1) {
                        row.createCell(17).setCellValue("特殊车辆");
                    } else if (list.get(i).getCarTag() == 2) {
                        row.createCell(17).setCellValue("产权车辆");
                    }
                }
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + "出车单信息.xlsx");

            workbook.write(response.getOutputStream());

            return Result.ok();

        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            log.error("电子出车出车单导出异常：", Ex);
            return Result.error(s);
        }
    }


    /***
     * 集团属地化管理 获取出车单列表
     * @param cclc
     * @return
     */
    @RequestMapping("/getCCDPageList")
    //@PreAuthorize("@ss.hasPermi('NDWCC01CD02QX01')")
    public Result<Object> getCCDPageList(@RequestBody VDfdwDzccTCcd cclc) {
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        cclc.setApproveState(2);
        cclc.setExecuteState(2);
        return Result.ok(cclcService.getCCDPageList(dzccPersonEntity, cclc));
    }


    /**
     * 新增出车地点
     */
    @PostMapping("addCcd")
    public Result<Object> addCcd(@RequestBody DfdwDzccTAddress address){
        DzccPersonEntity dzccPersonEntity = getDzccPerson();
        address.setDzccPersonEntity(dzccPersonEntity);
        return addressService.addCcdAddess(address);
    }

    /**
     * 根据id查询出车单
     */
    @RequestMapping("/GetCcdListById")
    @PreAuthorize("@ss.hasAnyPermi('NDWCC01CD02QX01,NDWCC01PJ01QX01')")
    public Result<Object> GetCcdListById(@RequestParam("id") Integer id) {
        return cclcService.GetCcdListById(id);
    }

}
