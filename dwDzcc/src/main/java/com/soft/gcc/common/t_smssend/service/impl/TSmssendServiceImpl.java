package com.soft.gcc.common.t_smssend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.t_smssend.entity.TSmssend;
import com.soft.gcc.common.t_smssend.mapper.TSmssendMapper;
import com.soft.gcc.common.t_smssend.service.TSmssendService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【T_SMSSend】的数据库操作Service实现
* @createDate 2024-12-25 15:30:15
*/
@Service
public class TSmssendServiceImpl extends ServiceImpl<TSmssendMapper, TSmssend>
    implements TSmssendService {

    @Override
    public Integer sendSms(String phone, String content) {
        return baseMapper.sendSms(phone, content);
    }
}




