package com.soft.gcc.common.dfdw_dict.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictType;
import com.soft.gcc.xtbg.base.controller.Result;

/**
* <AUTHOR>
* @description 数据库操作Service
* @createDate 2024-06-14 11:02:58
*/
public interface DfdwTDictTypeService extends IService<DfdwTDictType> {


    /**
     * 新增字典类型
     * @param dfdwTDictType
     * @return
     */
    Result<Object> createDictType(DfdwTDictType dfdwTDictType) ;

    /**
     * 删除字典类型
     * @return
     */
    Result<Object> deleteDictType(Integer id);

    /**
     * 修改字典类型
     * @return
     */
    Result<Object> updateDictType(DfdwTDictType dfdwTDictType)  ;

    /**
     * 字典类型分页
     * @param dfdwTDictType
     * @return
     */
    IPage<DfdwTDictType> getList(DfdwTDictType dfdwTDictType);
}
