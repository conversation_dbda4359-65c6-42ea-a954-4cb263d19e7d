package com.soft.gcc.common.flow.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dzcc.util.HttpClientUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
@RequestMapping("/flow/approve")
@RestController
public class DfdwApproveController extends BaseController {

    @RequestMapping("/toPost")
    @ResponseBody
    public Result toPost(@RequestBody Map<String, Object> map, HttpServletRequest request) {
        try {
            HashMap<String, String> headers = new HashMap<>();
            headers.put("Authorization", request.getHeader("Authorization"));
            String cookie = request.getHeader("Cookie");
            String url = map.get("url").toString();
            map.remove("url");
            return HttpClientUtils.doPostFlow(url, map, headers, cookie);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/toGet")
    @ResponseBody
    public Result toGet(@RequestBody Map<String, Object> map, HttpServletRequest request) {
        try {
            HashMap<String, String> headers = new HashMap<>();
            headers.put("Authorization", request.getHeader("Authorization"));
            String cookie = request.getHeader("Cookie");
            String url = map.get("url").toString();
            map.remove("url");
            Result result = HttpClientUtils.doGetFlow(url, map, headers, cookie);
            return result;
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
