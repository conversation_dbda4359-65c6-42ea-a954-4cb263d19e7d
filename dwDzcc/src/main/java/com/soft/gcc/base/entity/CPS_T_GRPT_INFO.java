package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 报表定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CPS_T_GRPT_INFO")
@ApiModel(value="CPS_T_GRPT_INFO对象", description="报表定义表")
public class CPS_T_GRPT_INFO extends Model<CPS_T_GRPT_INFO> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报表ID")
    @TableId(value = "GRPT_ID", type = IdType.AUTO)
    @JSONField(name="GRPT_ID")
    private Integer GRPT_ID;

    @ApiModelProperty(value = "报表类型ID")
    @JSONField(name="GRPTT_ID")
    private Integer GRPTT_ID;

    @ApiModelProperty(value = "报表定义文件")
    @JSONField(name="GRPT_SFILE")
    private String GRPT_SFILE;

    @ApiModelProperty(value = "报表定义描述")
    @JSONField(name="GRPT_SMARK")
    private String GRPT_SMARK;

    @ApiModelProperty(value = "是否默认")
    @JSONField(name="GRPT_DEF")
    private Integer GRPT_DEF;


    @Override
    protected Serializable pkVal() {
        return this.GRPT_ID;
    }

}
