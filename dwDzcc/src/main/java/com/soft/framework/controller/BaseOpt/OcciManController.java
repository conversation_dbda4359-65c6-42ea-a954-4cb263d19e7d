package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.soft.framework.common.utils.date.DateUtils;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.MetaHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/OcciMan")
@Api(tags = "基本框架接口->OCCI接口调用监控统计模块接口")
public class OcciManController {

    @Data
    public class OCCI_STATE {
        @JSONField(name = "OCCI_FILE")
        public String OCCI_FILE;

        @JSONField(name = "OCCI_FUNC")
        public String OCCI_FUNC;

        @JSONField(name = "OCCI_START")
        public String OCCI_START;

        @JSONField(name = "OCCI_END")
        public String OCCI_END;

        @JSONField(name = "OCCI_SEC")
        public double OCCI_SEC;
    }

    private OCCI_STATE SplitOCCIString(String ls) {
        try {
            String[] larr = ls.split("\\|");
            if (larr.length >= 3) {
                OCCI_STATE occi = new OCCI_STATE();
                occi.OCCI_FILE = larr[0];
                occi.OCCI_FUNC = larr[1];
                occi.OCCI_START = larr[2];
                occi.OCCI_END = larr[3];
                occi.OCCI_SEC = Double.parseDouble(larr[4]);
                return occi;
            } else {
                return null;
            }
        } catch (Exception Ex) {
            return null;
        }
    }

    @RequestMapping(value = "/GetOCCIList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetOCCIList", notes = "获取OCCI监控记录接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetOCCIList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        String qdate = request.getParameter("qdate");
        String qsec = request.getParameter("qsec");
        if (!qdate.equals("")) {
            qdate = MetaHelper.LongDateToShort(qdate.substring(0, 10));
        }
        if (qdate.equals("")) {
            qdate = DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate());
        }

        String LogPath = ConfigHelper.getOcciLogPath();
        String fname = LogPath + qdate + ".log";

        try {
            List<String> msglist = new ArrayList<String>();
            ToolHelper.ReadFileToStr(fname, msglist);

            List<OCCI_STATE> list = new ArrayList<OCCI_STATE>();
            for (int i = 0; i < msglist.size(); i++) {
                String ls = msglist.get(i);
                OCCI_STATE occi = SplitOCCIString(ls);
                if (occi != null) {
                    list.add(occi);
                }
            }
            if (StringUtil.isNotEmpty(qsec)) {
                list = list.stream().filter(ss -> ss.getOCCI_SEC() > Long.parseLong(qsec)).collect(Collectors.toList());
            }

            List<OCCI_STATE> rlist = new ArrayList<OCCI_STATE>();
            int pageCount = (list.size() / limit) + 1;
            int currpage = start / limit + 1;
            if (currpage < pageCount) {
                if (list.size() >= start + limit) {
                    rlist.addAll(list.subList(start, limit));
                }
            } else {
                if (list.size() - start > 0) {
                    rlist.addAll(list.subList(start, list.size() - start));
                }
            }

            ajaxResult = AjaxResult.extgrid(OCCI_STATE.class, rlist.size(), rlist);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出OCCI监控记录接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //数据转换
        String tmpstr = "";
        tmpstr = request.getParameter("start");
        int start = StringUtil.IsNullOrEmpty(tmpstr) ? 0 : Integer.parseInt(tmpstr);
        tmpstr = request.getParameter("limit");
        int limit = StringUtil.IsNullOrEmpty(tmpstr) ? 0 : Integer.parseInt(tmpstr);
        String qdate = request.getParameter("qdate");
        String qsec = request.getParameter("qsec");
        if (!qdate.equals("")) {
            qdate = MetaHelper.LongDateToShort(qdate.substring(0, 10));
        }
        if (qdate.equals("")) {
            qdate = DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate());
        }

        String LogPath = ConfigHelper.getOcciLogPath();
        String fname = LogPath + ConfigHelper.getfSepChar() + qdate + ".log";

        try {
            List<String> msglist = new ArrayList<String>();
            ToolHelper.ReadFileToStr(fname, msglist);

            List<OCCI_STATE> list = new ArrayList<OCCI_STATE>();
            for (int i = 0; i < msglist.size(); i++) {
                String ls = msglist.get(i);
                OCCI_STATE occi = SplitOCCIString(ls);
                if (occi != null) {
                    list.add(occi);
                }
            }
            if (StringUtil.IsNullOrEmpty(qsec)) {
                list = list.stream().filter(ss -> ss.OCCI_SEC > Long.parseLong(qsec)).collect(Collectors.toList());
            }

            StringBuilder efname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("OCCI_FILE", "接口文件", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("OCCI_FUNC", "接口函数", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("OCCI_START", "调用开始时间", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("OCCI_END", "调用结束时间", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("OCCI_SEC", "用时(毫秒)", 20));

            boolean retb = ToolHelper.ExportList2XlsFile(list, "接口调用日志", cmlist, retstr, efname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + efname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
