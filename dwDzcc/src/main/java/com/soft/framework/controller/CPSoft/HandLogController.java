package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.SqlHelper;
import com.soft.gcc.base.entity.CPS_T_HAND_LOG;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/HandLog" )
public class HandLogController {

    @RequestMapping("/GatherHandLog")
    @ResponseBody
    public AjaxResult GatherSysLog(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();
        String sMark = request.getParameter("sMark");

        String strsql = "";
        try
        {
            strsql = "select * from CPS_T_HAND_LOG where 1=1 ";
            if (StringUtil.IsNullOrEmpty(sMark)) {
                strsql+=" and HL_MARK='"+sMark+"'";
            }
            strsql+=" order by HL_RATE asc";

            StringBuilder sb=new StringBuilder();
            List<CPS_T_HAND_LOG> list = sqlhelper.GetObjectList(CPS_T_HAND_LOG.class,strsql);
            for(CPS_T_HAND_LOG obj:list)
            {
                sb.append(obj.getHL_DATE()+":"+obj.getHL_INFO()+"\n");
            }

            ajaxResult=AjaxResult.success(sb.toString());
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
