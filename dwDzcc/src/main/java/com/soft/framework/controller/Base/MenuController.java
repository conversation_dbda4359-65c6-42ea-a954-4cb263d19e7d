package com.soft.framework.controller.Base;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.gcc.base.ventity.vMenuItem;
import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.vFunctionItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@Configuration
@RequestMapping(value="/Service/Base/Menu" )
@Api(tags ="基本框架接口->菜单基本接口")
public class MenuController {
    private String strImgPath = "../../Image/Menus/";
    private String strPath="../../Page/";

    @RequestMapping(value="/RefreshMenu",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="RefreshMenu",notes="获取子菜单接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String RefreshMenu(HttpServletRequest request){
        String pmid= request.getParameter("mid");
        String pshowimg= request.getParameter("showimg");
        String ptshow= request.getParameter("tshow");
        String mid= ConfigHelper.getModuleId();
        if(ConfigHelper.getRunMode().equals("dev")&&!StringUtil.IsNullOrEmpty(pmid))
        {
            mid=pmid;
        }
        if(StringUtil.IsNullOrEmpty(pshowimg))
        {
            pshowimg="false";
        }
        if(StringUtil.IsNullOrEmpty(ptshow))
        {
            ptshow="false";
        }

        int userID= SessionHelper.getSessionUserID();
        int pwf= SessionHelper.getSessionUserPwf();

        String menustr = "";
        String UrlStr="";
        String defpImg = "Page.gif",defnImg = "folder.png";
        String gResName = "", gResId = "", ResName = "", ResId = "",cResId="",cResName = "";
        try
        {
            if (userID!= -1)
            {
                List<vFunctionItem> flist = DBHelper.GetUserFunctionList(mid,userID,pwf);
                if(flist==null||flist.size()==0)
                {
                    return "";
                }

                DataTable dtable = DBHelper.GetUserNavTable(mid,userID,pwf);
                int gcount=dtable.getTotalCount();
                for(int iter=0;iter<gcount;iter++) {
                    DataRow drData = dtable.getRow(iter);
                    gResId = drData.getColValue(0).toString();
                    gResName = drData.getColValue(1).toString();
                    final String ffuncid=gResId;

                    List<vFunctionItem> _fll=flist.stream().filter(pp->pp.getParentId().equals(ffuncid)).collect(Collectors.toList());
                    if(_fll!=null&&_fll.size()>0) {
                        if(ptshow.equals("true")) {
                            String imgdef="b"+gResId+".png";
                            String imgstr="<img class='headimage' src='"+ strImgPath+imgdef+"' onerror=\"this.src='"+strImgPath+defnImg+"'\" border=0>";
                            //String imgstr="<img class='headimage' src='"+ strImgPath+imgdef+"' border=0>";
                            menustr += "<li cid='" + gResId + "' cname='"+gResName+"'><div cid='" + gResId + "' cname='"+gResName+"'><center><table class='def-menudrager-outtertable'><tr><td align='center'>"+imgstr+"</td><td class='firstTitle' align='left'>" + gResName + "</td><td align='center' style='width:1.5em;'><img class='levelimage' id='" + gResId + "_image' src='" + strImgPath + "arrow_down.gif' border=0/></td></tr></table></center></div></li>";
                        }else {
                            String spanstr="<span class='headimage'>&nbsp;</span>";
                            menustr += "<li cid='" + gResId + "' cname='"+gResName+"'><div cid='" + gResId + "' cname='"+gResName+"'><center><table class='def-menudrager-outtertable'><tr><td align='center'>"+spanstr+"</td><td class='firstTitle' align='left'>" + gResName + "</td><td align='center' style='width:1.5em;'><img class='levelimage' id='" + gResId + "_image' src='" + strImgPath + "arrow_down.gif' border=0/></td></tr></table></center></div></li>";
                        }
                        menustr += "<ul cid='" + gResId + "'>";

                        for (vFunctionItem fobj : _fll) {
                            ResId = fobj.getId().toString();
                            ResName = fobj.getTitle();
                            UrlStr = fobj.getUrl();
                            if (!UrlStr.equals("")) {
                                if (UrlStr.indexOf("javascript::") == -1) {
                                    UrlStr = "javascript:NavFunction('" + strPath + UrlStr + "','" + ResId + "','" + ResName + "');";
                                } else {
                                    UrlStr = UrlStr.replace("javascript::", "javascript:");
                                }
                            }

                            final String sfuncid = ResId;
                            List<vFunctionItem> _sll = flist.stream().filter(pp -> pp.getParentId().equals(sfuncid)).collect(Collectors.toList());
                            if (_sll != null && _sll.size() > 0) {
                                if (pshowimg.equals("true")) {
                                    String imgdef = "b" + ResId + ".png";
                                    String imgstr = "<img class='headimage' src='" + strImgPath + imgdef + "' border=0>";
                                    menustr += "<li cid='" + ResId + "' cname='"+ResName+"'><div cid='" + ResId + "' ><center><table class='def-menudrager-innertable'><tr><td align='center'>" + imgstr + "</td><td class='secondTitle' align='left'>" + ResName + "</td><td align='center' style='width:1.5em;'><img class='levelimage' id='" + ResId + "_image' src='" + strImgPath + "arrow_down.gif' border=0/></td></tr></table></center></div></li>";
                                } else {
                                    String spanstr="<span class='headimage'>&nbsp;</span>";
                                    menustr += "<li cid='" + ResId + "' cname='"+ResName+"'><div cid='" + ResId + "'><center><table class='def-menudrager-innertable'><tr><td align='center'>"+spanstr+"</td><td class='secondTitle' align='left'>" + ResName + "</td><td align='center' style='width:1.5em;'><img class='levelimage' id='" + ResId + "_image' src='" + strImgPath + "arrow_down.gif' border=0/></td></tr></table></center></div></li>";
                                }
                                menustr += "<ul cid='" + ResId + "'>";
                                for (vFunctionItem sobj : _sll) {
                                    cResId = sobj.getId().toString();
                                    cResName = sobj.getTitle();
                                    UrlStr = sobj.getUrl();
                                    if (!UrlStr.equals("")) {
                                        if (UrlStr.indexOf("javascript::") == -1) {
                                            UrlStr = "javascript:NavFunction('" + strPath + UrlStr + "','" + cResId + "','" + cResName + "');";
                                        } else {
                                            UrlStr = UrlStr.replace("javascript::", "javascript:");
                                        }
                                    }

                                    final String tfuncid = cResId;
                                    List<vFunctionItem> _tll = flist.stream().filter(pp -> pp.getParentId().equals(tfuncid)).collect(Collectors.toList());
                                    if (_tll != null && _tll.size() > 0) {
                                        String tvstr = LoadThreeChild(_tll, pshowimg, UrlStr, cResName, cResId);
                                        menustr += tvstr;
                                    } else {
                                        if (pshowimg.equals("true")) {
                                            String imgdef = "b" + cResId + ".png";
                                            String imgstr = "<img class='headimage' src='" + strImgPath + imgdef + "' border=0>";
                                            menustr += "<li cid='" + cResId + "' cname='"+cResName+"'><div><a href='javascript:void(0);' onclick=\"" + UrlStr + "\"><center><table class='def-menudrager-innertable'><tr><td align='center'>" + imgstr + "</td><td class='thirdtitle' align='left'>" + cResName + "</td></tr></table></center></div></li>";
                                        } else {
                                            String spanstr="<span class='headimage'>&nbsp;</span>";
                                            menustr += "<li cid='" + cResId + "' cname='"+cResName+"'><div><a href='javascript:void(0);' onclick=\"" + UrlStr + "\"><center><table class='def-menudrager-innertable'><tr><td align='center'>"+spanstr+"</td><td class='thirdtitle' align='left'>" + cResName + "</td></tr></table></center></div></li>";
                                        }
                                    }
                                } //end foreach (DataRow drData2 in dtData2.Rows)
                                menustr += "</ul>";
                            } else {
                                if (pshowimg.equals("true")) {
                                    String imgdef = "b" + ResId + ".png";
                                    String imgstr = "<img class='headimage' src='" + strImgPath + imgdef + "' border=0>";
                                    menustr += "<li cid='" + ResId + "' cname='"+ResName+"'><div><a href='javascript:void(0);' onclick=\"" + UrlStr + "\"><center><table class='def-menudrager-innertable'><tr><td align='center'>" + imgstr + "</td><td class='secondTitle' align='left'>" + ResName + "</td></tr></table></center></a></div></li>";
                                } else {
                                    String spanstr="<span class='headimage'>&nbsp;</span>";
                                    menustr += "<li cid='" + ResId + "' cname='"+ResName+"'><div><a href='javascript:void(0);' onclick=\"" + UrlStr + "\"><center><table class='def-menudrager-innertable'><tr><td align='center'>"+spanstr+"</td><td class='secondTitle' align='left'>" + ResName + "</td></tr></table></center></a></div></li>";
                                }
                            }
                            menustr += "</li>";
                        } //end foreach (DataRow drData1 in dtData1.Rows)
                    }

                    menustr += "</ul>";
                }
            }

            return menustr;
        }
        catch (Exception Ex)
        {
            LogHelper.WriteSysLog("系统产生异常：" + Ex.getMessage() + "!");
            return "";
        }
    }

    private String LoadThreeChild(List<vFunctionItem> _tll,String showimg,String parurl,String parname,String parid)
    {
        String itemstr="";
        try
        {
            String UrlStr = "", strImg = "", ResId = "", ResName = "";
            if(showimg.equals("true")) {
                String imgdef = "b" + parid + ".png";
                String imgstr = "<img class='headimage' src='" + strImgPath + imgdef + "' border=0>";
                itemstr += "<li cid='" + parid + "' cname='"+parname+"'><div cid='" + parid + "'><a href='javascript:void(0);' onclick=\"" + parurl + "\" ><table class='def-menudrager-innertable'><tr><td align='center'>"+imgstr+"</td>><td class='thirdtitle' align='left'>" + parname + "</td><td align='center' style='width:1.5em;'><img class='levelimage' id='" + parid + "_image' src='" + strImgPath + "arrow_down.gif' border=0/></td</tr></table></a></div>";
            }else{
                String spanstr="<span class='headimage'>&nbsp;</span>";
                itemstr += "<li cid='" + parid + "' cname='"+parname+"'><div cid='" + parid + "'><a href='javascript:void(0);' onclick=\"" + parurl + "\" ><table class='def-menudrager-innertable'><tr><td align='center'>"+spanstr+"</td>><td class='thirdtitle' align='left'>" + parname + "</td><td align='center' style='width:1.5em;'><img class='levelimage' id='" + parid + "_image' src='" + strImgPath + "arrow_down.gif' border=0/></td</tr></table></a></div>";
            }
            itemstr += "<ul cid='" + parid + "'>";
            for(vFunctionItem tobj:_tll)
            {
                ResId = tobj.getId().toString();
                ResName = tobj.getTitle();
                UrlStr = tobj.getUrl();
                if (!UrlStr.equals(""))
                {
                    if (UrlStr.indexOf("javascript::") == -1)
                    {
                        UrlStr = "javascript:NavFunction('" + strPath + UrlStr + "','" + ResId + "','" + ResName + "');";
                    }
                    else
                    {
                        UrlStr = UrlStr.replace("javascript::", "javascript:");
                    }
                }
                if(showimg.equals("true")) {
                    String imgdef = "b" + ResId + ".png";
                    String imgstr = "<img class='headimage' src='" + strImgPath + imgdef + "' border=0>";
                    itemstr += "<li cid='" + ResId + "' cname='"+ResName+"'><div cid='" + ResId + "'><a href='javascript:void(0);' onclick=\"" + UrlStr + "\" ><table class='def-menudrager-innertable'><tr><td align='center'>"+imgstr+"</td>><td class='thirdtitle' align='left'>" + ResName + "</td></tr></table></a></div>";
                }else{
                    String spanstr="<span class='headimage'>&nbsp;</span>";
                    itemstr += "<li cid='" + ResId + "' cname='"+ResName+"'><div cid='" + ResId + "'><a href='javascript:void(0);' onclick=\"" + UrlStr + "\" ><table class='def-menudrager-innertable'><tr><td align='center'>"+spanstr+"</td>><td class='thirdtitle' align='left'>" + ResName + "</td></tr></table></a></div>";
                }
            } //end foreach (DataRow drData3 in dtData3.Rows)
            itemstr += "</ul></li>";
            return itemstr;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    @RequestMapping(value="/GetMenuGroup",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetMenuGroup",notes="获取顶部菜单分组接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetMenuGroup(HttpServletRequest request){
        String mid= ConfigHelper.getModuleId();

        SqlHelper sqlhelper = new SqlHelper();
        int userID= SessionHelper.getSessionUserID();
        int pwf= SessionHelper.getSessionUserPwf();

        String tmpstr = "[";
        String groupName = "", groupId = "";
        try
        {
            DataTable dtable = DBHelper.GetUserNavTable(mid,userID,pwf);
            int gcount=dtable.getTotalCount();
            for(int iter=0;iter<gcount;iter++) {
                DataRow drData1 = dtable.getRow(iter);
                groupId = drData1.getColValue(0).toString();
                groupName = drData1.getColValue(1).toString();

                StringBuilder sb=new StringBuilder();
                sb.append("{");
                sb.append("\"id\":\"");
                sb.append(groupId);
                sb.append("\",\"text\":\"");
                sb.append(groupName);
                sb.append("\",\"DisplayName\":\"");
                sb.append(groupName);
                sb.append("\",\"image_url\":\"");
                sb.append("b"+groupId+".png");
                sb.append("\"}");

                if(iter<gcount-1)
                {
                    tmpstr+=sb.toString()+",";
                }else
                {
                    tmpstr+=sb.toString();
                }
            }
            tmpstr+="]";

            return tmpstr;
        }
        catch (Exception Ex)
        {
            return "";
        }
    }

    @RequestMapping(value="/GetMenuJson",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetMenuJson",notes="获取子菜单接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetMenuJson(HttpServletRequest request){
        AjaxResult ajax = null;

        String pmid= request.getParameter("mid");
        String pshowimg= request.getParameter("showimg");
        String ptshow= request.getParameter("tshow");
        String mid= ConfigHelper.getModuleId();
        if(ConfigHelper.getRunMode().equals("dev")&&!StringUtil.IsNullOrEmpty(pmid))
        {
            mid=pmid;
        }
        if(StringUtil.IsNullOrEmpty(pshowimg))
        {
            pshowimg="false";
        }
        if(StringUtil.IsNullOrEmpty(ptshow))
        {
            ptshow="false";
        }

        int userID= SessionHelper.getSessionUserID();
        int pwf= SessionHelper.getSessionUserPwf();

        String UrlStr="";
        String gResName = "", gResId = "", ResName = "", ResId = "",cResId="",cResName = "",zcResId="",zcResName = "";
        try
        {
            if (userID!= -1)
            {
                List<vFunctionItem> flist = DBHelper.GetUserFunctionList(mid,userID,pwf);
                if(flist==null||flist.size()==0)
                {
                    ajax=AjaxResult.error("用户未授予任何权限！");
                    return ajax;
                }

                List<vMenuItem> _mlist=new ArrayList<vMenuItem>();

                DataTable dtable = DBHelper.GetUserNavTable(mid,userID,pwf);
                int gcount=dtable.getTotalCount();
                for(int iter=0;iter<gcount;iter++) {
                    DataRow drData = dtable.getRow(iter);
                    gResId = drData.getColValue(0).toString();
                    gResName = drData.getColValue(1).toString();
                    final String ffuncid=gResId;

                    //顶栏菜单
                    vMenuItem gmenu=new vMenuItem();
                    _mlist.add(gmenu);
                    gmenu.setId(gResId);
                    gmenu.setTitle(gResName);
                    if(ptshow.equals("true")) {
                        String imgdef="b"+gResId+".png";
                        gmenu.setImageUrl(imgdef);
                    }else {
                        gmenu.setImageUrl("");
                    }

                    List<vFunctionItem> _fll=flist.stream().filter(pp->pp.getParentId().equals(ffuncid) && !pp.getTitle().contains("APP")).collect(Collectors.toList());
                    if(_fll!=null&&_fll.size()>0) {
                        List<vMenuItem> _flm=new ArrayList<vMenuItem>();
                        gmenu.setChildList(_flm);

                        for (vFunctionItem fobj : _fll) {
                            ResId = fobj.getId().toString();
                            ResName = fobj.getTitle();
                            UrlStr = fobj.getUrl();

                            //一级菜单
                            vMenuItem flmenu=new vMenuItem();
                            _flm.add(flmenu);
                            flmenu.setId(ResId);
                            flmenu.setTitle(ResName);
                            flmenu.setUrl(UrlStr);
                            if (pshowimg.equals("true")) {
                                String imgdef="b"+gResId+".png";
                                flmenu.setImageUrl(imgdef);
                            } else {
                                flmenu.setImageUrl("");
                            }

                            final String sfuncid = ResId;
                            List<vFunctionItem> _sll = flist.stream().filter(pp -> pp.getParentId().equals(sfuncid)).collect(Collectors.toList());
                            if (_sll != null && _sll.size() > 0) {
                                List<vMenuItem> _slm=new ArrayList<vMenuItem>();
                                flmenu.setChildList(_slm);

                                for (vFunctionItem sobj : _sll) {
                                    cResId = sobj.getId().toString();
                                    cResName = sobj.getTitle();
                                    UrlStr = sobj.getUrl();

                                    //二级菜单
                                    vMenuItem slmenu=new vMenuItem();
                                    _slm.add(slmenu);
                                    slmenu.setId(cResId);
                                    slmenu.setTitle(cResName);
                                    slmenu.setUrl(UrlStr);
                                    if (pshowimg.equals("true")) {
                                        String imgdef="b"+gResId+".png";
                                        slmenu.setImageUrl(imgdef);
                                    } else {
                                        slmenu.setImageUrl("");
                                    }

                                    final String tfuncid = cResId;
                                    List<vFunctionItem> _tll = flist.stream().filter(pp -> pp.getParentId().equals(tfuncid)).collect(Collectors.toList());
                                    if (_tll != null && _tll.size() > 0) {
                                        List<vMenuItem> _tlm=new ArrayList<vMenuItem>();
                                        slmenu.setChildList(_tlm);

                                        for (vFunctionItem tobj : _tll) {
                                            zcResId = tobj.getId().toString();
                                            zcResName = tobj.getTitle();
                                            UrlStr = tobj.getUrl();

                                            //三级菜单
                                            vMenuItem tlmenu = new vMenuItem();
                                            _tlm.add(tlmenu);
                                            tlmenu.setId(zcResId);
                                            tlmenu.setTitle(zcResName);
                                            tlmenu.setUrl(UrlStr);
                                            if (pshowimg.equals("true")) {
                                                String imgdef = "b" + gResId + ".png";
                                                tlmenu.setImageUrl(imgdef);
                                            } else {
                                                tlmenu.setImageUrl("");
                                            }
                                        }
                                    }
                                } //end foreach (DataRow drData2 in dtData2.Rows)
                            }
                        } //end foreach (DataRow drData1 in dtData1.Rows)
                    }
                }
                ajax= AjaxResult.success(_mlist);
                return ajax;
            }else
            {
                ajax=AjaxResult.error("未授权用户信息！");
                return ajax;
            }
        }
        catch (Exception Ex)
        {
            LogHelper.WriteSysLog("系统产生异常：" + Ex.getMessage() + "!");
            ajax=AjaxResult.error(Ex.getMessage());
            return ajax;
        }
    }
}
