package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.gcc.base.entity.CPS_T_GRPT_TYPE;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Controller
@Configuration
@RequestMapping(value="/Service/CpSoft/GrptT" )
@Api(tags ="基本框架接口->Japer报表类型接口")
public class GrpttController {

    @RequestMapping(value="/AddGRPTT",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="AddGRPTT",notes="新增Japer报表类型定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddGRPTT(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            CPS_T_GRPT_TYPE entity = new CPS_T_GRPT_TYPE();

            //数据转换
            String GRPTT_ID=request.getParameter("GRPTT_ID");
            if(!StringUtil.IsNullOrEmpty(GRPTT_ID))
            {
                entity.setGRPTT_ID(Integer.parseInt(GRPTT_ID));
            }
            entity.setGRPTT_NAME(request.getParameter("GRPTT_NAME"));
            entity.setGRPTT_CODE(request.getParameter("GRPTT_CODE"));
            entity.setGRPTT_REMARK(request.getParameter("GRPTT_REMARK"));

            //提交数据库
            String strsql = DBHelper.GetInsertSQL(entity,"CPS_T_GRPT_TYPE", new ArrayList<String>() {});
            if (StringUtil.IsNullOrEmpty(strsql))
            {
                ajaxResult=AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteInsertWithObtainId(strsql);

            ajaxResult=AjaxResult.success(entity.getGRPTT_ID().toString());
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ModifyGRPTT",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ModifyGRPTT",notes="修改Japer报表类型定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyGRPTT(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            //数据转换
            String ID=request.getParameter("ID");
            CPS_T_GRPT_TYPE entity=sqlhelper.GetObject(CPS_T_GRPT_TYPE.class,"select * from CPS_T_GRPT_TYPE where GRPTT_ID="+ID);
            if(entity==null)
            {
                ajaxResult=AjaxResult.error("此记录在数据库中已经被删除，请刷新主界面，重新加载数据！");
                return ajaxResult;
            }
            entity.setGRPTT_NAME(request.getParameter("GRPTT_NAME"));
            entity.setGRPTT_CODE(request.getParameter("GRPTT_CODE"));
            entity.setGRPTT_REMARK(request.getParameter("GRPTT_REMARK"));

            //提交数据库
            String strsql = DBHelper.GetUpdateSQL(entity,"CPS_T_GRPT_TYPE", Arrays.asList("GRPTT_ID"), Arrays.asList(ID.toString()));
            if (StringUtil.IsNullOrEmpty(strsql))
            {
                ajaxResult=AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult=AjaxResult.success(entity.getGRPTT_ID().toString());
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/DeleteGRPTT",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="DeleteGRPTT",notes="删除Japer报表类型定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteGRPTT(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            //数据转换
            String ID=request.getParameter("ID");

            //提交数据库
            String qnum = sqlhelper.ExecuteScalar("select count(1) from CPS_T_GRPT_INFO where GRPTT_ID="+ID);
            if (qnum == "0")
            {
                sqlhelper.ExecuteNoQuery("delete from CPS_T_GRPT_TYPE where grptt_id="+ID);
                ajaxResult=AjaxResult.success("删除成功！");
                return ajaxResult;
            }
            else
            {
                ajaxResult=AjaxResult.error("删除失败，报表类型被多次引用，不能删除！");
                return ajaxResult;
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    private boolean GetUIQueryString(HttpServletRequest request,StringBuilder strsql,StringBuilder orderstr,StringBuilder msgstr)
    {
        try
        {
            String seachtj=request.getParameter("seachtj");

            strsql.append("select * from CPS_T_GRPT_TYPE where 1=1 ");
            if (!StringUtil.IsNullOrEmpty(seachtj)) {
                strsql.append(" and GRPTT_NAME like '%" + seachtj + "%'");
            }

            if(BaseConfig.getExtVersion().equals("3.2"))
            {
                String sortf=request.getParameter("sort");
                String sortd=request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf)&& !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            }else
            {
                String jsons=request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons,orderstr);
            }

            return true;
        }
        catch (Exception Ex)
        {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value="/GetGRPTTList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetGRPTTList",notes="获取Japer报表类型定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGRPTTList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        //参数获取
        String tmpstr="";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts)? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits)? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr  = new StringBuilder();
        StringBuilder msgstr  = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request,basesql, orderstr, msgstr))
        {
            ajaxResult=AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try
        {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount/limit) + 1;
            int currpage = start/limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<CPS_T_GRPT_TYPE> list = sqlhelper.GetObjectList(CPS_T_GRPT_TYPE.class,strsql);
            ajaxResult=AjaxResult.extgrid(CPS_T_GRPT_TYPE.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetGRPTTList2",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetGRPTTList2",notes="获取Japer报表类型定义列表2接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGRPTTList2(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        try
        {
            List<CPS_T_GRPT_TYPE> list = sqlhelper.GetObjectList(CPS_T_GRPT_TYPE.class,"select * from CPS_T_GRPT_TYPE order by GRPTT_ID");
            ajaxResult=AjaxResult.extgrid(CPS_T_GRPT_TYPE.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetGRPTTById",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetGRPTTById",notes="获取指定IDJaper报表类型定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGRPTTById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();
        String ID=request.getParameter("ID");

        try
        {
            CPS_T_GRPT_TYPE obj= sqlhelper.GetObject(CPS_T_GRPT_TYPE.class,"select * from CPS_T_GRPT_TYPE where GRPTT_ID="+ID);
            ajaxResult=AjaxResult.extform(CPS_T_GRPT_TYPE.class,"获取信息成功！",obj);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExportExcel", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ExportExcel",notes="导出Japer报表类型定义信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr  = new StringBuilder();
        StringBuilder msgstr  = new StringBuilder();
        if (!GetUIQueryString(request,basesql, orderstr, msgstr))
        {
            ajaxResult=AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try
        {
            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            DataTable dt = sqlhelper.GetDataTable(basesql.toString()+" "+orderstr.toString());
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("GRPTT_ID", "报表类型ID", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GRPTT_NAME", "报表类型名称", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GRPTT_REMARK", "报表类型备注", 30));
            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "报表类型信息", cmlist, retstr, fname);
            if (retb)
            {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname=fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath()+ ConfigHelper.getfSepChar()+fname.toString());
                cfs.data2 = null;

                String jsonstr= JSON.toJSON(cfs).toString();
                return jsonstr;
            }
            else
            {
                ajaxResult=AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
