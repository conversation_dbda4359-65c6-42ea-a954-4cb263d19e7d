package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.date.DateUtils;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SessionHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.util.JRLoader;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.sql.Connection;
import java.util.HashMap;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/GrptS" )
@Api(tags ="基本框架接口->生成Japer报表的服务接口")
public class GrptsController {

    @RequestMapping(value="/GenerateReport", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GenerateReport",notes="生成Japer报表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GenerateReport(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String report_path = ConfigHelper.getReportPath();
        String cache_path = ConfigHelper.getCachePath();

        String rpttid=request.getParameter("rpttid");
        String billid=request.getParameter("billid");

        boolean initsuccess = false;
        String strsql="";

        try
        {
            PersonEntity person = SessionHelper.getSessionPerson();

            SqlHelper sqlhelper=new SqlHelper();

            StringBuilder grptt_code =new StringBuilder();
            StringBuilder grpt_sfile =new StringBuilder();

            strsql="select grptt_code,grpt_sfile from cps_v_grpt_ulink where grptt_id=" + rpttid + " and user_acc='" + person.getLoginName()+ "' limit 1";
            sqlhelper.ExecuteScalarPair(strsql,grptt_code,grpt_sfile);
            if (grpt_sfile.toString().equals(""))
            {
                strsql="select grptt_code,grpt_sfile from cps_v_grpt_info where grptt_id=" + rpttid+ " and grpt_def=1 limit 1";
                sqlhelper.ExecuteScalarPair(strsql,grptt_code, grpt_sfile);
            }
            if (grpt_sfile.toString().equals("")) {
                ajaxResult=AjaxResult.error("报表模板缺失");
                return JSON.toJSON(ajaxResult).toString();
            }

            HashMap<String ,Object> paramMap = new HashMap<String ,Object>();
            paramMap.put("billid",billid);

            //billid外其他参数获取
            StringBuilder grpt_params=new StringBuilder();
            StringBuilder grpt_pqrys=new StringBuilder();
            strsql="select grpt_params,grpt_pqrys from cps_v_grpt_info where grptt_id=" + rpttid+ " and grpt_def=1 limit 1";
            sqlhelper.ExecuteScalarPair(strsql,grpt_params, grpt_pqrys);
            if(!grpt_params.toString().equals("")&&!grpt_pqrys.toString().equals(""))
            {
                String [] params=grpt_params.toString().split("\\|");
                strsql=grpt_pqrys.toString().replace(":id",billid);
                DataTable dt=sqlhelper.GetDataTable(strsql);
                if(dt.getTotalCount()==1)//仅返回一条记录，多条记录说明语句设置有问题
                {
                    for(String param:params)
                    {
                        paramMap.put(param,dt.getRow(0).getColValue(param));
                    }
                }
            }

            String jrxmlf=report_path+grpt_sfile;
            String japerf=jrxmlf.replace(".jrxml",".jasper");
            File sfile=new File(jrxmlf);
            if(!sfile.exists())
            {
                ajaxResult=AjaxResult.error("报表模板文件不存在!");
                return JSON.toJSON(ajaxResult).toString();
            }
            Connection conn=sqlhelper.getConnection();
            JasperCompileManager.compileReportToFile(jrxmlf,japerf);
            JasperReport jasperReport = (JasperReport) JRLoader.loadObjectFromFile(japerf);
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport,paramMap,conn);

            String tmpf = grptt_code + "_" + DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate()) + ".pdf";
            String fullfile=cache_path+tmpf;
            if(FileUtil.FileExists(fullfile)) {
                FileUtil.Delete(fullfile);
            }
            JasperExportManager.exportReportToPdfFile(jasperPrint,fullfile);

            ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
            cfs.success = true;
            cfs.text = "生成文件成功！";
            cfs.fname=tmpf;
            cfs.data1 = ToolHelper.File2Bytes(fullfile);
            cfs.data2 = null;

            String jsonstr=JSON.toJSON(cfs).toString();
            return jsonstr;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
