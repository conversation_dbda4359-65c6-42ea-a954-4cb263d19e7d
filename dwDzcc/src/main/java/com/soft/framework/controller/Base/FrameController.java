package com.soft.framework.controller.Base;

import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SessionHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value="/Service/Base/Frame" )
@Api(tags ="基本框架接口->基本零星接口")
public class FrameController {
    @RequestMapping(value="/GetTopOption",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetTopOption",notes ="获取页面顶端头部标注信息")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetTopOption(HttpServletRequest request){
        AjaxResult ajaxResult=null;
        try
        {
            String titlestr = "数字平台";
            String welcomestr="";
            welcomestr= "您好,"+ SessionHelper.getSessionUserName()+ "("+SessionHelper.getSessionDeptName()+"),欢迎回来....";

            ajaxResult=AjaxResult.success("获取信息成功!");
            ajaxResult.put("title",titlestr);
            ajaxResult.put("welcome",welcomestr);
            return ajaxResult;
        }catch(Exception Ex)
        {
            ajaxResult = AjaxResult.error("获取信息失败！");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetServerFilePath",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetServerFilePath",notes ="获取服务器附件存放位置注信息")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetServerFilePath(HttpServletRequest request){
        AjaxResult ajaxResult=null;
        try
        {
            ajaxResult=AjaxResult.success(ConfigHelper.getOssLocalPath());
            return ajaxResult;
        }catch(Exception Ex)
        {
            ajaxResult = AjaxResult.error("获取信息失败！");
            return ajaxResult;
        }
    }
}
