package com.soft.framework.redis;

import java.util.List;
import java.util.Map;
 
public interface IRedisService {
  
  void addValue(String key, Object value, Long timeout) throws Exception;
  
  Object getValue(String key) throws Exception;
  
  void addList(String key, List<Object> list, Long timeout) throws Exception;
 
  List<Object> getList(String key) throws Exception;
  
  void addMap(String key, Map<String, Object> map, Long timeout) throws Exception;
 
  Map<String,Object> getMap(String key) throws Exception;
  
  void addValueSecond(String key, Object value, Long timeout) throws Exception;
  
  Long getExpire(String key);
	
  boolean hasKey(String key);
}