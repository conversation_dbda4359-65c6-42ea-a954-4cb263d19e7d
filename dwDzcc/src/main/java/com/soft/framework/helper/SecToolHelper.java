package com.soft.framework.helper;

import com.yyszc.wpbase.entity.NFT_NWIPD;
import com.yyszc.wpbase.entity.NFT_SQL_IDSTR;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

public class SecToolHelper {
    public static boolean CheckWordHasSQLKQ(String fvalue) {
        if (fvalue == "" || fvalue == null) {
            return false;
        }
        fvalue = fvalue.toLowerCase();

        try {
            List<NFT_SQL_IDSTR> sqlidsist = DBHelper.GetPreventSqlList();

            if(sqlidsist!=null) {
                for (NFT_SQL_IDSTR idx : sqlidsist) {
                    String[] sqlarr = idx.getSIDS().split("\\|");
                    for (int i = 0; i < sqlarr.length; i++) {
                        String sastr = sqlarr[i].toLowerCase();
                        if (idx.getSIDT() == 0) {
                            if (IsSqlBaseKey(sastr)) {
                                continue;
                            }
                            if (fvalue.contains(" " + sastr + " ")) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        } catch (Exception Ex) {
            return false;
        }
    }

    private static boolean CheckWordHasXN(String fvalue, List<NFT_SQL_IDSTR> plist)
    {
        try
        {
            if (fvalue == "" || fvalue == null) {
                return false;
            }

            fvalue = fvalue.toLowerCase();
            if(plist!=null) {
                for (NFT_SQL_IDSTR idx : plist) {
                    String[] sqlarr = idx.getSIDS().split("\\|");
                    for (int i = 0; i < sqlarr.length; i++) {
                        String sastr = sqlarr[i].toLowerCase();
                        if (idx.getSIDT() == 0) {
                            if (fvalue.contains(" " + sastr + " ")) {
                                return true;
                            }
                        } else if (idx.getSIDT() == 1) {
                            if (!sastr.equals("script") && fvalue.contains(sastr)) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }
        catch (Exception Ex)
        {
            return true;
        }
    }

    private static boolean CheckWordHasXF(String fvalue, List<NFT_SQL_IDSTR> plist)
    {
        try
        {
            if (fvalue == "" || fvalue == null) {
                return false;
            }
            fvalue = fvalue.toLowerCase();

            if(plist!=null) {
                for (NFT_SQL_IDSTR idx : plist) {
                    if (idx.getSIDT() == 2) {
                        String[] sqlarr = idx.getSIDS().split("\\|");
                        for (int i = 0; i < sqlarr.length; i++) {
                            String sastr = sqlarr[i].toLowerCase();
                            if (fvalue.contains(sastr)) {
                                return false;
                            }
                        }
                    }
                }
            }
            return false;
        }
        catch (Exception Ex)
        {
            return true;
        }
    }

    private static boolean IsSqlBaseKey(String kystr)
    {
        String lkystr = kystr.toLowerCase();
        if(lkystr == "select" || lkystr == "insert" || lkystr == "delete" || lkystr == "update" || lkystr == "from" || lkystr == "and" || lkystr == "or"
                || lkystr == "table" || lkystr == "view" || lkystr == "function" || lkystr == "procedure" || lkystr == "drop" || lkystr == "create"
                || lkystr == "drop table" || lkystr == "truncate" || lkystr == "declare" || lkystr == "set"||lkystr==";")
        {
            return true;
        }
        return false;
    }

    private static boolean CheckWordHasXNQ(String fkey,String fvalue, List<NFT_SQL_IDSTR> plist)
    {
        try {
            if (fvalue == "" || fvalue == null) {
                return false;
            }

            if(plist!=null) {
                fvalue = fvalue.toLowerCase();
                for (NFT_SQL_IDSTR idx : plist) {
                    String[] sqlarr = idx.getSIDS().split("\\|");
                    for (int i = 0; i < sqlarr.length; i++) {
                        String sastr = sqlarr[i].toLowerCase();
                        if (idx.getSIDT() == 0) {
                            if (IsSqlBaseKey(sastr)) {
                                continue;
                            }
                            if (fvalue.contains(" " + sastr + " ")) {
                                return true;
                            }
                        } else if (idx.getSIDT() == 1) {
                            if (!sastr.equals("script") && fvalue.contains(sastr)) {
                                return true;
                            }
                        } else if (fkey.equals("FileType") && idx.getSIDT() == 2) {
                            if (fvalue.contains(sastr)) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        } catch(Exception Ex) {
            return true;
        }
    }

    //检测参数是否含有注入字符
    @SuppressWarnings("unchecked")
    public static boolean SQL_Injection_Detection(HttpServletRequest request, String fkey, String value)
    {
        ServletContext application= request.getServletContext();
        List<NFT_SQL_IDSTR> sqlidsist=null;
        if(ConfigHelper.isLoadDBConf()||application.getAttribute("SQL_IDSTR_LIST")==null)
        {
            sqlidsist=DBHelper.GetPreventSqlList();
            application.setAttribute("SQL_IDSTR_LIST",sqlidsist);
        }else
        {
            sqlidsist=(List<NFT_SQL_IDSTR>)application.getAttribute("SQL_IDSTR_LIST");
        }

        if(sqlidsist==null||sqlidsist.size()==0)
        {
            return false;
        }

        String[] values = value.split(" ");
        if(sqlidsist!=null&&sqlidsist.size()>0)
        {
            String fvalue = "";
            for (int j=0;j<values.length;j++){
                fvalue=values[j];
                if (fvalue == "") {
                    continue;
                }
                if(CheckWordHasXN(fvalue,sqlidsist)) {
                    return true;
                }
            }
        }

        return false;
    }

    //检测参数是否含有注入字符
    @SuppressWarnings("unchecked")
    public static boolean FILE_Injection_Detection(HttpServletRequest request, String fnamestr)
    {
        ServletContext application= request.getServletContext();
        List<NFT_SQL_IDSTR> sqlidsist=null;
        if(ConfigHelper.isLoadDBConf()||application.getAttribute("SQL_IDSTR_LIST")==null)
        {
            sqlidsist=DBHelper.GetPreventSqlList();
            application.setAttribute("SQL_IDSTR_LIST",sqlidsist);
        }else
        {
            sqlidsist=(List<NFT_SQL_IDSTR>)application.getAttribute("SQL_IDSTR_LIST");
        }

        if(sqlidsist==null||sqlidsist.size()==0)
        {
            return false;
        }

        String[] values = fnamestr.split(" ");
        if(sqlidsist!=null&&sqlidsist.size()>0)
        {
            String fvalue = "";
            for (int j=0;j<values.length;j++){
                fvalue=values[j];
                if (fvalue == "") {
                    continue;
                }
                if(CheckWordHasXF(fvalue,sqlidsist)) {
                    return true;
                }
            }
        }

        return false;
    }

    //检测参数是否含有注入字符
    @SuppressWarnings("unchecked")
    public static boolean SQL_Injection_DetectionQ(HttpServletRequest request,String fkey,String value)
    {
        ServletContext application= request.getServletContext();
        List<NFT_SQL_IDSTR> sqlidsist=null;
        if(ConfigHelper.isLoadDBConf()||application.getAttribute("SQL_IDSTR_LIST")==null)
        {
            sqlidsist=DBHelper.GetPreventSqlList();
            application.setAttribute("SQL_IDSTR_LIST",sqlidsist);
        }else
        {
            sqlidsist=(List<NFT_SQL_IDSTR>)application.getAttribute("SQL_IDSTR_LIST");
        }

        if(sqlidsist==null||sqlidsist.size()==0)
        {
            return false;
        }

        String[] values = value.split(" ");
        if(sqlidsist!=null&&sqlidsist.size()>0)
        {
            String fvalue = "";
            for (int j=0;j<values.length;j++){
                fvalue=values[j];
                if (fvalue == "") {
                    continue;
                }
                if(CheckWordHasXNQ(fkey,fvalue,sqlidsist)) {
                    return true;
                }
            }
        }

        return false;
    }


    //返回值内外网标识，1：内网，2外网；
    public static int UserIsInNWWD(HttpServletRequest request)
    {
        String rip0 = request.getHeader("host");
        int port=request.getServerPort();
        if(port!=80)
        {
            rip0=rip0.replace(":"+port,"");
        }

        try
        {
            ServletContext application= request.getServletContext();
            List<NFT_NWIPD> wlist=null;
            if(ConfigHelper.isLoadDBConf()||application.getAttribute("NWIPD_LIST")==null)
            {
                wlist=DBHelper.GetNwIpdList();
                application.setAttribute("NWIPD_LIST",wlist);
            }else
            {
                wlist=(List<NFT_NWIPD>)application.getAttribute("NWIPD_LIST");
            }

            if (wlist!=null&&wlist.size() > 0)
            {
                boolean mflag = false;
                for (NFT_NWIPD tmpt:wlist)
                {
                    String rval=tmpt.getIPW();
                    if (rip0.equals(rval))
                    {
                        mflag = true;
                        break;
                    }
                }

                if (mflag == true)
                {
                    LogHelper.WriteSysLog("源地址:" + rip0 + ",比对内网设置，确认链接来自内网!");
                }
                else
                {
                    LogHelper.WriteSysLog("源地址:" + rip0 + ",比对内网设置，确认链接来自外网!");
                }
                return (mflag==true)?1:2;
            }
            else
            {
                LogHelper.WriteSysLog("源地址:" + rip0 + ",未设置内网相关设置,为把影响降到最低，确认链接来自外网!");
                return 2;
            }
        }
        catch (Exception Ex)
        {
            LogHelper.WriteSysLog("源地址:" + rip0 + ",验证访问来源出错,为把影响降到最低，确认链接来自外网!");
            return 2;
        }
    }
}
