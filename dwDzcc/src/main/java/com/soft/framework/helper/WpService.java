package com.soft.framework.helper;

import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.*;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/*
 * 远程调用 product 服务
 *
 * */
@FeignClient(name = "wpframe", url = "${feign.client.config.wpframe.url}")
public interface WpService {
    //---------------------info工具集---------------------------------------------------
    @RequestMapping(value = "/WpService/Info/Hello", method = {RequestMethod.GET})
    @ApiOperation(value = "Hello", notes = "测试交互接口")
    @ApiImplicitParam(name = "name", value = "String", required = true)
    String Hello(@RequestParam("name") String name);

    @RequestMapping(value = "/WpService/Info/TestDBEntity", method = {RequestMethod.GET})
    @ApiOperation(value = "TestDBEntity", notes = "测试基本实体对象交互接口")
    @ApiImplicitParam(name = "name", value = "String", required = true)
    Person TestDBEntity(@RequestParam("name") String name, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetCurrentPerson", method = {RequestMethod.GET})
    @ApiOperation(value = "GetCurrentPerson", notes = "获取当前用户信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    PersonEntity GetCurrentPerson(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetCurrentPerson_S", method = {RequestMethod.GET})
    @ApiOperation(value = "GetCurrentPerson_S", notes = "获取当前用户信息接口(精简接口)")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    PersonEntity GetCurrentPerson_S(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/IsTokenValid", method = {RequestMethod.GET})
    @ApiOperation(value = "IsTokenValid", notes = "获取当前token的有效性接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    boolean IsTokenValid(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/VerifyToken", method = {RequestMethod.GET})
    @ApiOperation(value = "VerifyToken", notes = "刷新中心服务token信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    Boolean VerifyToken(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetPreventSqlList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPreventSqlList", notes = "获取平台sql注入配置信息")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    List<NFT_SQL_IDSTR> GetPreventSqlList(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetNwIpdList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNwIpdList", notes = "获取平台内网认定配置信息")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    List<NFT_NWIPD> GetNwIpdList(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetNftWhiteList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNftWhiteList", notes = "获取平台内网认定配置信息")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    List<NFT_WhiteList> GetNftWhiteList(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetUserNavTable", method = {RequestMethod.GET})
    @ApiOperation(value = "GetUserNavTable", notes = "获取用户菜单顶栏菜单栏")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    DataTable GetUserNavTable(@RequestParam("mid") String mid, @RequestParam("userid") int userid, @RequestParam("pwf") int pwf, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetUserFunctionList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetUserFunctionList", notes = "获取用户菜单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<vFunctionItem> GetUserFunctionList(@RequestParam("mid") String mid, @RequestParam("userid") int userid, @RequestParam("pwf") int pwf, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetUserNavMenuGroup", method = {RequestMethod.GET})
    @ApiOperation(value = "GetUserNavMenuGroup", notes = "获取指定模块顶部菜单栏中默认选择项，一般时第一个导航项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    String GetUserNavMenuGroup(@RequestParam("mid") String mid, @RequestParam("userid") int userid, @RequestParam("pwf") int pwf, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetRolePersonList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetRolePersonList", notes = "获取含有指定角色的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<PersonEntity> GetRolePersonList(@RequestParam("roleid") int roleid, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetCompRolePersonList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetCompRolePersonList", notes = "获取指定单位含有指定角色的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleid", value = "int", required = true),
            @ApiImplicitParam(name = "topGroupId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<PersonEntity> GetCompRolePersonList(@RequestParam("roleid") int roleid, @RequestParam("topGroupId") int topGroupId, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetPermissionPersonList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPermissionPersonList", notes = "获取拥有指定权限的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permission", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<PersonEntity> GetPermissionPersonList(@RequestParam("permission") String permission, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetCompPermissionPersonList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetCompPermissionPersonList", notes = "获取指定单位拥有指定权限的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permission", value = "String", required = true),
            @ApiImplicitParam(name = "topGroupId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<PersonEntity> GetCompPermissionPersonList(@RequestParam("permission") String permission, @RequestParam("topGroupId") int topGroupId, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetAllTopGroup", method = {RequestMethod.GET})
    @ApiOperation(value = "GetAllTopGroup", notes = "获取指定模块操作单位（TopGroupId）列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<vComp> GetAllTopGroup(@RequestParam("mid") int mid, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetPersonByLoginName", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonByLoginName", notes = "据LogiinName获取用户基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    PersonEntity GetPersonByLoginName(@RequestParam("loginName") String loginName, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetPersonByUserId", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonByUserId", notes = "据userId获取用户基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    PersonEntity GetPersonByUserId(@RequestParam("userid") int userid, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetVPersonByLoginName_S", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVPersonByLoginName_S", notes = "据LogiinName获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vPerson GetVPersonByLoginName_S(@RequestParam("loginName") String loginName, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetPersonByLoginName_S", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonByLoginName_S", notes = "据LogiinName获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Person GetPersonByLoginName_S(@RequestParam("loginName") String loginName, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetVPersonByUserId_S", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVPersonByUserId_S", notes = "据userId获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public vPerson GetVPersonByUserId_S(@RequestParam("userid") int userid, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetPersonByUserId_S", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonByUserId_S", notes = "据userId获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public Person GetPersonByUserId_S(@RequestParam("userid") int userid, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Info/GetChildFuntionItemByParent", method = {RequestMethod.GET})
    @ApiOperation(value = "GetChildFuntionItemByParent", notes = "据LoginName获取用户邮件信息信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "gid", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    DataTable GetChildFuntionItemByParent(@RequestParam("userid") int userid, @RequestParam("pwf") int pwf, @RequestParam("gid") String gid, @RequestHeader(value = "token", required = true) String token);
    //-----------------------Info信息----------------------------------------------

    //--------------------Tool工具集---------------------------------------------------
    @RequestMapping(value = "/WpService/Tool/SendSms", method = {RequestMethod.GET})
    @ApiOperation(value = "SendSms", notes = "发送短消息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "String", required = true),
            @ApiImplicitParam(name = "phone", value = "String", required = true),
            @ApiImplicitParam(name = "content", value = "String", required = true)
    })
    boolean SendSms(@RequestParam("phone") String phone, @RequestParam("content") String content, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Tool/AddApplicationLog", method = {RequestMethod.GET})
    @ApiOperation(value = "AddApplicationLog", notes = "通用登记AppliactionLog接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uname", value = "String", required = true),
            @ApiImplicitParam(name = "func", value = "String", required = true),
            @ApiImplicitParam(name = "module", value = "String", required = true),
            @ApiImplicitParam(name = "detail", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean AddApplicationLog(@RequestParam("uname") String uname, @RequestParam("func") String func,
                              @RequestParam("module") String module, @RequestParam("detail") String detail,
                              @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Tool/AddApplicationLogYW", method = {RequestMethod.GET})
    @ApiOperation(value = "AddApplicationLogYW", notes = "通用登记AppliactionLog接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uname", value = "String", required = true),
            @ApiImplicitParam(name = "func", value = "String", required = true),
            @ApiImplicitParam(name = "module", value = "String", required = true),
            @ApiImplicitParam(name = "detail", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean AddApplicationLogYW(@RequestParam("uname") String uname, @RequestParam("func") String func,
                                @RequestParam("module") String module, @RequestParam("detail") String detail,
                                @RequestHeader(value = "token", required = true) String token);
    //---------------------Tool工具集----------------------------------------------------

    //---------------------lcdb工具集----------------------------------------------------
    @RequestMapping(value = "/WpService/lcdb/GetWorkFlowList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetWorkFlowList", notes = "获取流程待办列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<NFV_WorkFlow> GetWorkFlowList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------lcdb工具集----------------------------------------------------

    //---------------------Person表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Person/GetPersonById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonById", notes = "获取用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Person GetPersonById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/GetVPersonById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVPersonById", notes = "获取用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vPerson GetVPersonById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/GetPersonBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonBySql", notes = "获取用户列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Person GetPersonBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/GetVPersonBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVPersonBySql", notes = "获取用户列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vPerson GetVPersonBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/GetPersonList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPersonList", notes = "获取用户列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "strsql", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Person> GetPersonList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/GetVPersonList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVPersonList", notes = "获取用户列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "strsql", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<vPerson> GetVPersonList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Person表操作接口------------------------------------------------

    //---------------------Role表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Role/GetRoleById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetRoleById", notes = "获取角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Role GetRoleById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Role/GetRoleList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetRoleList", notes = "获取角色列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Role> GetRoleList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Role表操作接口------------------------------------------------

    //---------------------Permission表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Permission/GetPermissionById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPermissionById", notes = "获取权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Permission GetPermissionById(@RequestParam("PermissionNo") String Id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Permission/GetPermissionList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetPermissionList", notes = "获取权限列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Permission> GetPermissionList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Permission表操作接口------------------------------------------------

    //---------------------FunctionItem表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/FunctionItem/GetFunctionItemById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetFunctionItemById", notes = "获取资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    FunctionItem GetFunctionItemById(@RequestParam("id") String id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/FunctionItem/GetFunctionItemBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetFunctionItemBySql", notes = "获取资源列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    FunctionItem GetFunctionItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/FunctionItem/GetVFunctionItemBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVFunctionItemBySql", notes = "获取资源列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vFunctionItem GetVFunctionItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/FunctionItem/GetFunctionItemList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetFunctionItemList", notes = "获取资源列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<FunctionItem> GetFunctionItemList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/FunctionItem/GetFunctionItemByMark", method = {RequestMethod.GET})
    @ApiOperation(value = "GetFunctionItemByMark", notes = "获取含有指定属性的资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "title", value = "String", required = true),
            @ApiImplicitParam(name = "parent", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vFunctionItem GetFunctionItemByMark(@RequestParam("title") String title, @RequestParam("parent") String parent, @RequestHeader(value = "token", required = true) String token);
    //---------------------FunctionItem表操作接口------------------------------------------------


    //---------------------Module表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Module/GetModuleList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetModuleList", notes = "获取模块信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    List<Module> GetModuleList(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Module/GetModuleListInMode", method = {RequestMethod.GET})
    @ApiOperation(value = "GetModuleListInMode", notes = "获取指定模组下辖模块信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mlist", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Module> GetModuleListInMode(@RequestParam("mlist") String mlist, @RequestHeader(value = "token", required = true) String token);
    //---------------------Module表操作接口------------------------------------------------

    //---------------------GroupItem表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/GroupItem/GetGroupItemById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetGroupItemById", notes = "获取单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    GroupItem GetGroupItemById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetGroupItemBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetGroupItemBySql", notes = "获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    GroupItem GetGroupItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetVGroupItemById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVGroupItemById", notes = "获取单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vGroupItem GetVGroupItemById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetVGroupItemBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVGroupItemBySql", notes = "获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vGroupItem GetVGroupItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetGroupItemList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetGroupItemList", notes = "获取单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<GroupItem> GetGroupItemList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetVGroupItemList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVGroupItemList", notes = "获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<vGroupItem> GetVGroupItemList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetGroupList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetGroupList", notes = "获取单位精简列表信息接口(多用于列表框显示数据)")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    List<vGroupItem> GetGroupList(@RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetGroupTreeExtJs", produces = {"text/plain;charset=UTF-8"})
    @ApiOperation(value = "GetGroupTreeExtJs", notes = "获取树形单位机构列表接口(专用于Extjs)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "group", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    String GetGroupTreeExtJs(@RequestParam(value = "group", required = true) int group, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/GetGroupTree", produces = {"text/plain;charset=UTF-8"})
    @ApiOperation(value = "GetGroupTree", notes = "获取树形单位机构列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "group", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    String GetGroupTree(@RequestParam(value = "group", required = true) int group, @RequestHeader(value = "token", required = true) String token);
    //---------------------GroupItem表操作接口------------------------------------------------

    //---------------------vComp视图操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/vComp/GetCompList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetCompList", notes = "获取集体单位列表信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    List<vComp> GetCompList(@RequestHeader(value = "token", required = true) String token);
    //---------------------vComp视图操作接口--------------------------------------------


    //---------------------Lcdefine表操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/Lcdefine/GetLcdefineById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcdefineById", notes = "获取流程定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lcdefine GetLcdefineById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcdefine/GetLcdefineList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcdefineList", notes = "获取流程定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Lcdefine> GetLcdefineList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcdefine/GetLcdefineByLcId", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcdefineByLcId", notes = "获取流程定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lcId", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lcdefine GetLcdefineByLcId(@RequestParam("lcId") String lcId, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lcdefine表操作接口--------------------------------------------

    //---------------------Lcjd表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/Lcjd/GetLcjdById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcjdById", notes = "获取流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lcjd GetLcjdById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcjd/GetLcjdList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcjdList", notes = "获取流程节点定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Lcjd> GetLcjdList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcjd/GetLcjdListByLc", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcjdListByLc", notes = "获取指定流程所有节点定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lcId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Lcjd> GetLcjdListByLc(@RequestParam("lcId") int lcId, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcjd/GetLcjdByLcjdId", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcjdByLcId", notes = "获取流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "LcjdId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lcjd GetLcjdByLcId(@RequestParam("LcjdId") int LcjdId, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcjd/GetLcjdBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLcjdBySql", notes = "获取指定sql相关的流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lcjd GetLcjdBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lcjd表操作接口--------------------------------------------

    //---------------------Lc_currentState表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/CurrentState/GetLc_currentStateById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLc_currentStateById", notes = "获取流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lc_currentState GetLc_currentStateById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/CurrentState/GetLc_currentStateBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLc_currentStateBySql", notes = "获取流程状态列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lc_currentState GetLc_currentStateBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/CurrentState/GetLc_currentStateList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLc_currentStateList", notes = "获取流程状态列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Lc_currentState> GetLc_currentStateList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lc_currentState表操作接口--------------------------------------------

    //---------------------Lc_workFlow表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/WorkFlow/GetLc_workFlowById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLc_workFlowById", notes = "获取流程流转过程信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lc_workFlow GetLc_workFlowById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/WorkFlow/GetLc_workFlowBySql", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLc_workFlowBySql", notes = "获取流程流转过程列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Lc_workFlow GetLc_workFlowBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/WorkFlow/GetLc_workFlowList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetLc_workFlowList", notes = "获取流程流转过程列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<Lc_workFlow> GetLc_workFlowList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lc_workFlow表操作接口--------------------------------------------

    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/MAMLink/GetNFT_ModuleAMLinkById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_ModuleAMLinkById", notes = "获取模块自启动定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    NFT_ModuleAMLink GetNFT_ModuleAMLinkById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MAMLink/GetNFT_ModuleAMLinkListByMId", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_ModuleAMLinkListByMId", notes = "据模块ID获取自启动定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<NFT_ModuleAMLink> GetNFT_ModuleAMLinkListByMId(@RequestParam("mid") int mid, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MAMLink/GetNFT_ModuleAMLinkList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_ModuleAMLinkList", notes = "获取模块自启动定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<NFT_ModuleAMLink> GetNFT_ModuleAMLinkList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------

    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/MLCLink/GetNFT_ModuleLCLinkById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_ModuleLCLinkById", notes = "获取模块待办关联定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    NFT_ModuleLCLink GetNFT_ModuleLCLinkById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MLCLink/GetNFV_ModuleLCLinkById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFV_ModuleLCLinkById", notes = "获取模块关联待办定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    NFV_ModuleLCLink GetNFV_ModuleLCLinkById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MLCLink/GetNFT_ModuleLCLinkList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_ModuleLCLinkList", notes = "获取模块待办关联定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<NFT_ModuleLCLink> GetNFT_ModuleLCLinkList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MLCLink/GetNFV_ModuleLCLinkList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFV_ModuleLCLinkList", notes = "获取角色列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<NFV_ModuleLCLink> GetNFV_ModuleLCLinkList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MLCLink/GetNFT_ModuleLCLinkByLc", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_ModuleLCLinkByLc", notes = "获取角色列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lcid", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    NFT_ModuleLCLink GetNFT_ModuleLCLinkByLc(@RequestParam("lcid") String lcid, @RequestHeader(value = "token", required = true) String token);
    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------

    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/SIDS/GetNFT_SQL_IDSTRById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_SQL_IDSTRById", notes = "获取SQL注入检测信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    NFT_SQL_IDSTR GetNFT_SQL_IDSTRById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/SIDS/GetNFT_SQL_IDSTRList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetNFT_SQL_IDSTRList", notes = "获取SQL注入检测列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<NFT_SQL_IDSTR> GetNFT_SQL_IDSTRList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------

    //---------------------DictionaryDefine表操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/DictionaryDefine/GetDictionaryDefineById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetDictionaryDefineById", notes = "获取字典类型定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    DictionaryDefine GetDictionaryDefineById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryDefine/GetDictionaryDefineList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetDictionaryDefineList", notes = "获取字典类型定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<DictionaryDefine> GetDictionaryDefineList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------DictionaryDefine表操作接口--------------------------------------------


    //---------------------DictionaryValue表操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/DictionaryValue/GetDictionaryValueById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetDictionaryValueById", notes = "获取字典定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    DictionaryValue GetDictionaryValueById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryValue/GetVDictionaryValueById", method = {RequestMethod.GET})
    @ApiOperation(value = "GetVDictionaryValueById", notes = "获取字典定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    vDictionaryValue GetVDictionaryValueById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryValue/GetDictionaryValueList", method = {RequestMethod.GET})
    @ApiOperation(value = "GetDictionaryValueList", notes = "获取字典定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<vDictionaryValue> GetDictionaryValueList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryValue/GetDictionaryValueListByType", method = {RequestMethod.GET})
    @ApiOperation(value = "GetDictionaryValueListByType", notes = "获取字典定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    List<DictionaryValue> GetDictionaryValueListByType(@RequestParam("type") String type, @RequestHeader(value = "token", required = true) String token);
    //---------------------DictionaryValue表操作接口--------------------------------------------


    //--------------------操作更新工具集-------------------------------------------------------
    //--------------------Tool工具集---------------------------------------------------
    @RequestMapping(value = "/WpService/Tool/GetDataTable", method = {RequestMethod.GET})
    @ApiOperation(value = "GetDataTable", notes = "获取基本表格数据接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    DataTable GetDataTable(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Tool/RecordExists", method = {RequestMethod.GET})
    @ApiOperation(value = "RecordExists", notes = "通用记录存在确认接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean RecordExists(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Tool/GetRecordCount", method = {RequestMethod.GET})
    @ApiOperation(value = "GetRecordCount", notes = "通用获取记录条目接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    String GetRecordCount(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Tool/ExecuteScalar", method = {RequestMethod.GET})
    @ApiOperation(value = "ExecuteScalar", notes = "通用获取记录条目接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    String ExecuteScalar(@RequestParam("strsql") String strsql, @RequestHeader(value = "token", required = true) String token);
    //---------------------Tool工具集----------------------------------------------------

    //---------------------Person表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Person/AddPerson", method = {RequestMethod.POST})
    @ApiOperation(value = "AddPerson", notes = "新增用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "person", value = "Person", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddPerson(@RequestBody Person entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/UpdatePerson", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdatePerson", notes = "修改用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "person", value = "Person", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdatePerson(@RequestBody Person entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/DeletePersonById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeletePersonById", notes = "删除用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeletePersonById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/ResetPassword", method = {RequestMethod.GET})
    @ApiOperation(value = "ResetPassword", notes = "重置用户密码信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "defpwd", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean ResetPassword(@RequestParam("id") int id, @RequestParam("defpwd") String defpwd, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Person/UpdatePassword", method = {RequestMethod.GET})
    @ApiOperation(value = "UpdatePassword", notes = "修改用户密码信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "newpwd", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdatePassword(@RequestParam("id") int id, @RequestParam("newpwd") String newpwd, @RequestHeader(value = "token", required = true) String token);
    //---------------------Person表操作接口------------------------------------------------

    //---------------------Role表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Role/AddRole", method = {RequestMethod.POST})
    @ApiOperation(value = "AddRole", notes = "新增角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Role", value = "Role", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddRole(@RequestBody Role entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Role/UpdateRole", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateRole", notes = "修改角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Role", value = "Role", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateRole(@RequestBody Role entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Role/DeleteRoleById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteRoleById", notes = "删除角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteRoleById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);
    //---------------------Role表操作接口------------------------------------------------

    //---------------------Permission表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/Permission/AddPermission", method = {RequestMethod.POST})
    @ApiOperation(value = "AddPermission", notes = "新增权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Permission", value = "Permission", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    String AddPermission(@RequestBody Permission entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Permission/UpdatePermission", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdatePermission", notes = "修改权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Permission", value = "Permission", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdatePermission(@RequestBody Permission entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Permission/DeletePermissionById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeletePermissionById", notes = "删除权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeletePermissionById(@RequestParam("Id") String Id, @RequestHeader(value = "token", required = true) String token);
    //---------------------Permission表操作接口------------------------------------------------

    //---------------------FunctionItem表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/FunctionItem/AddFunctionItem", method = {RequestMethod.POST})
    @ApiOperation(value = "AddFunctionItem", notes = "新增资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "FunctionItem", value = "FunctionItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    String AddFunctionItem(@RequestBody FunctionItem entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/FunctionItem/UpdateFunctionItem", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateFunctionItem", notes = "修改资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "FunctionItem", value = "FunctionItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateFunctionItem(@RequestBody FunctionItem entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/FunctionItem/DeleteFunctionItemById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteFunctionItemById", notes = "删除资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteFunctionItemById(@RequestParam("id") String id, @RequestHeader(value = "token", required = true) String token);

    //---------------------FunctionItem表操作接口------------------------------------------------


    //---------------------GroupItem表操作接口------------------------------------------------
    @RequestMapping(value = "/WpService/GroupItem/AddGroupItem", method = {RequestMethod.POST})
    @ApiOperation(value = "AddGroupItem", notes = "新增单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "GroupItem", value = "GroupItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddGroupItem(@RequestBody GroupItem entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/UpdateGroupItem", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateGroupItem", notes = "修改单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "GroupItem", value = "GroupItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateGroupItem(@RequestBody GroupItem entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/GroupItem/DeleteGroupItemById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteGroupItemById", notes = "删除单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteGroupItemById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    //---------------------GroupItem表操作接口------------------------------------------------


    //---------------------RolePermission表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/RolePermission/DeleteRolePermission", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteRolePermission", notes = "删除角色权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PermissionNo", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteRolePermission(@RequestParam("RoleId") int RoleId, @RequestParam("PermissionNo") String PermissionNo, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/RolePermission/AddRolePermission", method = {RequestMethod.GET})
    @ApiOperation(value = "AddRolePermission", notes = "新增角色权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PermissionNo", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean AddRolePermission(@RequestParam("RoleId") int RoleId, @RequestParam("PermissionNo") String PermissionNo, @RequestHeader(value = "token", required = true) String token);
    //---------------------RolePermission表操作接口--------------------------------------------

    //---------------------RolePerson表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/RolePerson/DeleteRolePerson", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteRolePerson", notes = "删除用户角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PersonId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteRolePerson(@RequestParam("RoleId") int RoleId, @RequestParam("PersonId") int PersonId, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/RolePerson/AddRolePerson", method = {RequestMethod.GET})
    @ApiOperation(value = "AddRolePerson", notes = "新增用户角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PersonId", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean AddRolePerson(@RequestParam("RoleId") int RoleId, @RequestParam("PersonId") int PersonId, @RequestHeader(value = "token", required = true) String token);
    //---------------------RolePerson表操作接口--------------------------------------------


    //---------------------Lcdefine表操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/Lcdefine/AddLcdefine", method = {RequestMethod.POST})
    @ApiOperation(value = "AddLcdefine", notes = "新增流程定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lcdefine", value = "Lcdefine", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddLcdefine(@RequestBody Lcdefine entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcdefine/UpdateLcdefine", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateLcdefine", notes = "修改流程定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lcdefine", value = "Lcdefine", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateLcdefine(@RequestBody Lcdefine entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcdefine/DeleteLcdefineById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteLcdefineById", notes = "删除流程定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteLcdefineById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lcdefine表操作接口--------------------------------------------

    //---------------------Lcjd表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/Lcjd/AddLcjd", method = {RequestMethod.POST})
    @ApiOperation(value = "AddLcjd", notes = "新增流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lcjd", value = "Lcjd", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddLcjd(@RequestBody Lcjd entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcjd/UpdateLcjd", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateLcjd", notes = "修改流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lcjd", value = "Lcjd", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateLcjd(@RequestBody Lcjd entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/Lcjd/DeleteLcjdById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteLcjdById", notes = "删除流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteLcjdById(@RequestParam("id") int Id, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lcjd表操作接口--------------------------------------------

    //---------------------Lc_currentState表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/CurrentState/AddLc_currentState", method = {RequestMethod.POST})
    @ApiOperation(value = "AddLc_currentState", notes = "新增流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lc_currentState", value = "Lc_currentState", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddLc_currentState(@RequestBody Lc_currentState entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/CurrentState/UpdateLc_currentState", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateLc_currentState", notes = "修改流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lc_currentState", value = "Lc_currentState", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateLc_currentState(@RequestBody Lc_currentState entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/CurrentState/DeleteLc_currentStateById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteLc_currentStateById", notes = "删除流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteLc_currentStateById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lc_currentState表操作接口--------------------------------------------


    //---------------------Lc_workFlow表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/WorkFlow/AddLc_workFlow", method = {RequestMethod.POST})
    @ApiOperation(value = "AddLc_workFlow", notes = "新增流程流转过程信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lc_workFlow", value = "Lc_workFlow", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddLc_workFlow(@RequestBody Lc_workFlow entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/WorkFlow/UpdateLc_workFlow", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateLc_workFlow", notes = "修改流程流转过程信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lc_workFlow", value = "Lc_workFlow", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateLc_workFlow(@RequestBody Lc_workFlow entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/WorkFlow/DeleteLc_workFlowById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteLc_workFlowById", notes = "删除流程流转过程信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteLc_workFlowById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);
    //---------------------Lc_workFlow表操作接口--------------------------------------------


    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/MAMLink/AddNFT_ModuleAMLink", method = {RequestMethod.POST})
    @ApiOperation(value = "AddNFT_ModuleAMLink", notes = "新增模块自启动定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_ModuleAMLink", value = "NFT_ModuleAMLink", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddNFT_ModuleAMLink(@RequestBody NFT_ModuleAMLink entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MAMLink/UpdateNFT_ModuleAMLink", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateNFT_ModuleAMLink", notes = "修改模块自启动定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_ModuleAMLink", value = "NFT_ModuleAMLink", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateNFT_ModuleAMLink(@RequestBody NFT_ModuleAMLink entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MAMLink/DeleteNFT_ModuleAMLinkById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteNFT_ModuleAMLinkById", notes = "删除模块自启动定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteNFT_ModuleAMLinkById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);
    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------

    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/MLCLink/AddNFT_ModuleLCLink", method = {RequestMethod.POST})
    @ApiOperation(value = "AddNFT_ModuleLCLink", notes = "新增模块待办关联定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_ModuleLCLink", value = "NFT_ModuleLCLink", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddNFT_ModuleLCLink(@RequestBody NFT_ModuleLCLink entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MLCLink/UpdateNFT_ModuleLCLink", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateNFT_ModuleLCLink", notes = "修改模块待办关联定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_ModuleLCLink", value = "NFT_ModuleLCLink", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateNFT_ModuleLCLink(@RequestBody NFT_ModuleLCLink entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/MLCLink/DeleteNFT_ModuleLCLinkById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteNFT_ModuleLCLinkById", notes = "删除模块待办关联定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteNFT_ModuleLCLinkById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------

    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------
    @RequestMapping(value = "/WpService/SIDS/AddNFT_SQL_IDSTR", method = {RequestMethod.POST})
    @ApiOperation(value = "AddNFT_SQL_IDSTR", notes = "新增SQL注入检测定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_SQL_IDSTR", value = "NFT_SQL_IDSTR", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddNFT_SQL_IDSTR(@RequestBody NFT_SQL_IDSTR entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/SIDS/UpdateNFT_SQL_IDSTR", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateNFT_SQL_IDSTR", notes = "修改SQL注入检测定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_SQL_IDSTR", value = "NFT_SQL_IDSTR", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateNFT_SQL_IDSTR(@RequestBody NFT_SQL_IDSTR entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/SIDS/DeleteNFT_SQL_IDSTRById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteNFT_SQL_IDSTRById", notes = "删除SQL注入检测信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteNFT_SQL_IDSTRById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------

    //---------------------DictionaryDefine表操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/DictionaryDefine/AddDictionaryDefine", method = {RequestMethod.POST})
    @ApiOperation(value = "AddDictionaryDefine", notes = "新增字典类型定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DictionaryDefine", value = "DictionaryDefine", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddDictionaryDefine(@RequestBody DictionaryDefine entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryDefine/UpdateDictionaryDefine", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateDictionaryDefine", notes = "修改字典类型定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DictionaryDefine", value = "DictionaryDefine", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateDictionaryDefine(@RequestBody DictionaryDefine entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryDefine/DeleteDictionaryDefineById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteDictionaryDefineById", notes = "删除字典类型定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteDictionaryDefineById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);

    //---------------------DictionaryDefine表操作接口--------------------------------------------


    //---------------------DictionaryValue表操作接口-------------------------------------------
    @RequestMapping(value = "/WpService/DictionaryValue/AddDictionaryValue", method = {RequestMethod.POST})
    @ApiOperation(value = "AddDictionaryValue", notes = "新增字典定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DictionaryValue", value = "DictionaryValue", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Integer AddDictionaryValue(@RequestBody DictionaryValue entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryValue/UpdateDictionaryValue", method = {RequestMethod.POST})
    @ApiOperation(value = "UpdateDictionaryValue", notes = "修改字典定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DictionaryValue", value = "DictionaryValue", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean UpdateDictionaryValue(@RequestBody DictionaryValue entity, @RequestHeader(value = "token", required = true) String token);

    @RequestMapping(value = "/WpService/DictionaryValue/DeleteDictionaryValueById", method = {RequestMethod.GET})
    @ApiOperation(value = "DeleteDictionaryValueById", notes = "删除字典定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    Boolean DeleteDictionaryValueById(@RequestParam("id") int id, @RequestHeader(value = "token", required = true) String token);
    //---------------------DictionaryValue表操作接口--------------------------------------------
}
