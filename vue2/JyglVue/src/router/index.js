import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

export const constantRoutes = [
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/redirect/:path(.*)',
                component: () => import('@/views/redirect')
            }
        ]
    },
    {
        path: '/404',
        component: () => import('@/views/error/404'),
        hidden: true
    },
    {
        path: '/401',
        component: () => import('@/views/error/401'),
        hidden: true
    },
    {
        path: "*",
        name: "NotFound",
        component: () => import("@/views/error/404"),
    },
    //协同办公路由
    {
        path: '/jygl',
        component: Layout,
        // redirect: 'index',
        children: [
            {
                path: 'index.html',
                name: 'index.html',
                component: () => import('@/views/index'),
            },
            {
                path: 'debtManagementList',
                name: 'debtManagementList',
                component: () => import('@/views/debtManagement/list'),
            },
            {
                path: 'debtManagementSummary',
                name: 'debtManagementSummary',
                component: () => import('@/views/debtManagement/summary'),
            },
            {
                path: 'accountsReceivableList',
                name: 'accountsReceivableList',
                component: () => import('@/views/accountsReceivable/list'),
            },
            {
                path: 'accountsReceivableSummary',
                name: 'accountsReceivableSummary',
                component: () => import('@/views/accountsReceivable/summary'),
            },
            {
                path: 'collectionList',
                name: 'collectionList',
                component: () => import('@/views/collection'),
            },
            {
                path: 'associated',
                name: 'associated',
                component: () => import('@/views/associated/list'),
            },
        ]
    },
]

const router = new VueRouter({
    mode: 'history',
    scrollBehavior: () => ({ y: 0 }),
    base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
    routes: constantRoutes
})

export default router
