<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="getSummaryData()"
              v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ02QX01') > -1"
            >
              刷新
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="exportExcel()"
              :loading="exportLoading"
              v-if="summaryList.length > 0"
            >
              导出Excel
            </el-button>
          </div>
        </div>
        <div class="table-box">
          <el-table
            ref="summaryTable"
            :key="tableKey"
            :data="summaryList"
            v-loading="summaryLoading"
            border
            style="width: 100%"
            height="100%"
            @row-dblclick="handleRowClick"
            row-key="unit"
            :row-class-name="tableRowClassName"
            :show-summary="summaryList.length > 0"
            :summary-method="getSummaries"
            sum-text="总计"
            :table-layout="'fixed'"
          >
            <!-- 单位名称列 -->
            <el-table-column
              prop="unit"
              label="单位名称"
              width="120"
              fixed="left"
            />
            
            <!-- 账龄6个月以内 -->
            <el-table-column label="账龄6个月以内" align="center">
              <el-table-column prop="sixMonCount" label="个数" width="80" align="center" />
              <el-table-column prop="sixMonAmount" label="金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.sixMonAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="sixMonLedger" label="台账数" width="80" align="center" />
            </el-table-column>
            
            <!-- 账龄1年以内 -->
            <el-table-column label="账龄1年以内" align="center">
              <el-table-column prop="oneYearCount" label="个数" width="80" align="center" />
              <el-table-column prop="oneYearAmount" label="金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.oneYearAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="oneYearLedger" label="台账数" width="80" align="center" />
            </el-table-column>
            
            <!-- 账龄1-2年 -->
            <el-table-column label="账龄1-2年" align="center">
              <el-table-column prop="oneToTwoCount" label="个数" width="80" align="center" />
              <el-table-column prop="oneToTwoAmount" label="金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.oneToTwoAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="oneToTwoLedger" label="台账数" width="80" align="center" />
            </el-table-column>
            
            <!-- 账龄2-3年 -->
            <el-table-column label="账龄2-3年" align="center">
              <el-table-column prop="twoToThreeCount" label="个数" width="80" align="center" />
              <el-table-column prop="twoToThreeAmount" label="金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.twoToThreeAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="twoToThreeLedger" label="台账数" width="80" align="center" />
            </el-table-column>
            
            <!-- 账龄3年以上 -->
            <el-table-column label="账龄3年以上" align="center">
              <el-table-column prop="threeAboveCount" label="个数" width="80" align="center" />
              <el-table-column prop="threeAboveAmount" label="金额" min-width="120" align="center">
                <template slot-scope="scope">
                  {{ formatAmount(scope.row.threeAboveAmount) }}
                </template>
              </el-table-column>
              <el-table-column prop="threeAboveLedger" label="台账数" width="80" align="center" />
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSummary } from "api/jygl/accountsReceivable";
import { exportAccountsReceivableSummary } from "@/utils/excel";

export default {
  name: 'AccountsReceivableSummary',
  data() {
    return {
      summaryLoading: false,
      summaryList: [],
      tableKey: 0, // 用于强制重新渲染表格
      exportLoading: false // 导出加载状态
    };
  },
  mounted() {
    this.getSummaryData();
  },
  methods: {
    // 获取汇总数据
    getSummaryData() {
      this.summaryLoading = true;
      getSummary().then((res) => {
        this.summaryList = res.result || [];

        // 数据加载完成后，强制表格重新渲染
        this.$nextTick(() => {
          this.tableKey += 1; // 改变key值，强制重新渲染整个表格
        });
      }).catch((error) => {
        console.error('获取汇总数据失败:', error);
        this.$message.error('获取汇总数据失败');
      }).finally(() => {
        this.summaryLoading = false;
      });
    },

    // 自定义汇总方法
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计';
          return;
        }

        // 需要汇总的数值字段
        const sumFields = [
          'sixMonCount', 'sixMonAmount', 'sixMonLedger',
          'oneYearCount', 'oneYearAmount', 'oneYearLedger',
          'oneToTwoCount', 'oneToTwoAmount', 'oneToTwoLedger',
          'twoToThreeCount', 'twoToThreeAmount', 'twoToThreeLedger',
          'threeAboveCount', 'threeAboveAmount', 'threeAboveLedger'
        ];

        if (sumFields.includes(column.property)) {
          // 参考官网写法：获取所有值并转换为数字
          const values = data.map(item => Number(item[column.property]));

          // 检查是否都是NaN
          if (!values.every(value => isNaN(value))) {
            const sum = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);

            // 如果是金额字段，格式化显示
            if (column.property && column.property.includes('Amount')) {
              sums[index] = this.formatAmount(sum);
            } else {
              sums[index] = sum.toString();
            }
          } else {
            sums[index] = 'N/A';
          }
        } else {
          sums[index] = '';
        }
      });

      return sums;
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (!amount) return '0.00';
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // 表格行样式
    tableRowClassName({row, rowIndex}) {

      if (row.unit === '总计') {
        return 'total-row';
      }
      return '';
    },

    // 行点击事件 - 跳转到列表页面并传递单位名称
    handleRowClick(row) {
      // 总计行不允许点击跳转
      if (row.unit === '总计') {
        return;
      }

      // 跳转到列表页面，并传递单位名称作为查询条件
      this.$router.push({
        path: '/jygl/accountsReceivableList',
        query: {
          companyName: row.unit === '其他'?row.unit:row.companyName,
          isSummary: 1
        }
      });
    },

    // 导出Excel
    async exportExcel() {
      if (!this.summaryList || this.summaryList.length === 0) {
        this.$message.warning('暂无数据可导出');
        return;
      }

      this.exportLoading = true;

      try {
        // 获取汇总数据
        const summaryData = this.calculateSummaryData();

        // 调用导出函数
        await exportAccountsReceivableSummary(this.summaryList, summaryData);

        this.$message.success('Excel导出成功');
      } catch (error) {
        console.error('导出Excel失败:', error);
        this.$message.error('导出Excel失败');
      } finally {
        this.exportLoading = false;
      }
    },

    // 计算汇总数据
    calculateSummaryData() {
      const summaryData = {};

      // 需要汇总的数值字段
      const sumFields = [
        'sixMonCount', 'sixMonAmount', 'sixMonLedger',
        'oneYearCount', 'oneYearAmount', 'oneYearLedger',
        'oneToTwoCount', 'oneToTwoAmount', 'oneToTwoLedger',
        'twoToThreeCount', 'twoToThreeAmount', 'twoToThreeLedger',
        'threeAboveCount', 'threeAboveAmount', 'threeAboveLedger'
      ];

      sumFields.forEach(field => {
        const values = this.summaryList.map(item => Number(item[field]) || 0);
        summaryData[field] = values.reduce((prev, curr) => prev + curr, 0);
      });

      return summaryData;
    }
  }
};
</script>

<style scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.process-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;
  }

  .table-box {
    height: calc(100% - 38px);
    flex: 1;
    overflow: hidden;

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}
/* 总计行样式 */
::v-deep .total-row {
  background-color: #f0f9ff !important;
  font-weight: bold;
  color: #1890ff;
}

::v-deep .total-row:hover {
  background-color: #f0f9ff !important;
  cursor: default;
}

::v-deep .total-row td {
  background-color: #f0f9ff !important;
}

/* Element UI 汇总行样式 - 复用总计行样式 */
::v-deep .el-table__footer-wrapper {
  background-color: #f0f9ff;
}

::v-deep .el-table__footer .el-table__cell {
  background-color: #f0f9ff !important;
  font-weight: bold;
  color: #1890ff;
}

::v-deep .el-table__footer tr td:first-child {
  color: #1890ff;
  font-size: 14px;
  font-weight: bold;
}

::v-deep .el-table__footer tr:hover td {
  background-color: #f0f9ff !important;
  cursor: default;
}
</style>
