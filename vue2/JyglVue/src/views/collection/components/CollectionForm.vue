<template>
  <div class="collection-form" v-watermark="{label: watermark}">
    <el-form
        ref="form"
        :model="form"
        :rules="formRules"
        label-width="100px"
        size="small"
    >
      <el-row :gutter="20">

        <!-- 催收日期 -->
        <el-col :span="12">
          <el-form-item label="催收日期" prop="collectionDate">
            <el-date-picker
                v-model="form.collectionDate"
                type="date"
                placeholder="请选择催收日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <!-- 项目名称 -->
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectName">
            <el-input
                v-model="form.projectName"
                placeholder="请点击选择项目"
                readonly
                @click.native="openDialog"
                style="cursor: pointer;"
            >
              <i slot="suffix" class="el-icon-search" style="cursor: pointer;"></i>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-show="form.projectName">
        <!-- 公司名称 -->
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companyName">
            <el-input
                v-model="form.companyName"
                placeholder="请点击选择项目"
                readonly
            />
          </el-form-item>
        </el-col>

        <!-- 款项发生时间 -->
        <el-col :span="12">
          <el-form-item label="款项发生时间" prop="transferTime">
            <el-date-picker
                v-model="form.transferTime"
                type="datetime"
                placeholder="请选择款项发生时间"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                :disabled="form.id != null"
            />
          </el-form-item>
        </el-col>


      </el-row>


      <el-row :gutter="20" v-show="form.projectName">
        <!-- 往来户单位 -->
        <el-col :span="12">
          <el-form-item label="往来户单位" prop="unit">
            <el-input
                v-model="form.unit"
                placeholder="请点击选择单位"
                readonly
                @click.native="openDialog"
                style="cursor: pointer;"
            >
            </el-input>
          </el-form-item>
        </el-col>

        <!-- 催收状态 -->
        <el-col :span="12">
          <el-form-item label="催收状态" prop="state">
            <el-select
                v-model="form.state"
                placeholder="请选择催收状态"
                style="width: 100%"
                :disabled ="true"
            >
              <el-option label="未催收" :value="0"></el-option>
              <el-option label="已催收" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="form.id">
        <!-- 确认状态 -->
        <el-col :span="12">
          <el-form-item label="确认状态" prop="confirmationStatus">
            <el-select
                v-model="form.confirmationStatus"
                placeholder="请选择确认状态"
                style="width: 100%"
                :disabled="true"
            >
              <el-option label="未确认" :value="0"></el-option>
              <el-option label="已确认" :value="1"></el-option>
              <el-option label="待确认" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


      <!-- 催收文件 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="催收文件" prop="annexIds">
            <FileUpload
                v-model="form.annexId"
                :data="{ type: 'jygl', hjID: 1, functionId: 20017 }"
                :isShowTip="false"
                :limit="5"
                :type="2"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 佐证材料 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="佐证材料" prop="supportingDocuments">
            <FileUpload
                v-model="form.supportDocsIds"
                :data="{ type: 'jygl', hjID: 5, functionId: 20017 }"
                :isShowTip="false"
                :limit="5"
                :type="2"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 选择应收账款对话框 -->
    <AccountsReceivableSelector
      :visible.sync="addCollectionRecordDialog"
      @confirm="handleAccountSelect"
    />
  </div>

</template>

<script>
import AccountsReceivableSelector from './AccountsReceivableSelector.vue';
import FileUpload from  '../../../components/DFDW/FileUpload1.vue';
export default {
  name: 'CollectionForm',
  components: {
    AccountsReceivableSelector,FileUpload
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          id: null,
          numberOfDunningPeriods: '',
          collectionDate: '',
          companyName: '',
          projectName: '',
          transferTime: '',
          lastCollectionDate: '',
          unit: '',
          state: 0,
          confirmationStatus: 0,
          annexIds: '',
          supportingDocuments: '',
          remark: '',
          annexId:'',
        }
      }
    },
    // 催收记录id
    collectionID:{
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      watermark: this.$store.state.user.watermark,
      addCollectionRecordDialog:false,
      submitLoading: false,
      form: {
        id: null,
        numberOfDunningPeriods: '',
        collectionDate: '',
        companyName: '',
        projectName: '',
        transferTime: '',
        lastCollectionDate: '',
        unit: '',
        state: 0,
        confirmationStatus: 0,
        annexIds: '',
        supportingDocuments: '',
        remark: '',
        annexId:'',
      },
      formRules: {

        collectionDate: [
          { required: true, message: '请选择催收日期', trigger: 'change' }
        ],
        companyName: [
          { required: true, message: '请选择项目', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '请选择项目', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入往来户单位', trigger: 'blur' }
        ],
        state: [
          { required: true, message: '请选择催收状态', trigger: 'change' }
        ],
        confirmationStatus: [
          { required: true, message: '请选择确认状态', trigger: 'change' }
        ]
      },
      collectionFileList: [],
      supportingFileList: [],
    };
  },
  computed: {

  },
  mounted() {

  },
  watch: {
    value: {
      handler(val) {
        console.log("===========",val)
        this.form = val;
      },
      immediate: true,
      deep: true
    },
    form: {
      handler(val) {
        this.$emit("input", val);
      },
      immediate: true,
      deep: true
    },

  },
  methods: {


    // 表单验证
    validate(callback) {
      // 防御性校验：确保表单引用存在
      if (!this.$refs.form)  {
        console.error('Form ref not found');
        return typeof callback === 'function' ? callback(false) : Promise.resolve(false);
      }

      // 统一返回 Promise 以实现链式调用
      return new Promise((resolve) => {
        this.$refs.form.validate((valid)  => {
          // 处理回调函数
          if (typeof callback === 'function') {
            callback(valid);
          }
          // 无论是否有回调，都通过 Promise 返回结果
          resolve(valid);
        }).catch((error) => {
          console.error('Validation error:', error);
          resolve(false); // 验证失败时统一返回 false
        });
      });
    },


    // 打开对话框
    openDialog() {
      if(this.form.id){
        return
      }
      this.addCollectionRecordDialog = true;
    },

    // 处理账款选择确认
    handleAccountSelect(selectedAccount) {
      // 将选中的账款信息填入表单
      this.form.companyName = selectedAccount.companyName;
      this.form.projectName = selectedAccount.projectName;
      this.form.unit = selectedAccount.unit;
      this.form.transferTime = selectedAccount.transferTime;
      this.form.remark = selectedAccount.remark;
      this.form.businessDataId = selectedAccount.id;

      this.$message.success('已选择应收账款信息');
    },


  }

};
</script>

<style scoped>
.collection-form {
  padding: 20px;
}

.form-footer {
  text-align: right;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.form-footer .el-button {
  margin: 0 10px;
  min-width: 80px;
}


::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

::v-deep .el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}





/* 输入框只读状态的样式 */
::v-deep .el-input.is-disabled .el-input__inner,
::v-deep .el-input__inner[readonly] {
  background-color: #fff !important;
  border-color: #dcdfe6;
  color: #606266;
  cursor: pointer;
}

::v-deep .el-input__inner[readonly]:hover {
  border-color: #c0c4cc;
}

::v-deep .el-input__inner[readonly]:focus {
  border-color: #409eff;
}


</style>
