<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <span class="font-size14">单位名称：</span>
            <el-input v-model="queryParams.unit" clearable placeholder="请输入单位名称"
                      @change="handleChange" style="width: 300px;"></el-input>
            <span class="font-size14">公司名称：</span>
            <el-input v-model="queryParams.companyName" clearable placeholder="请输入单位名称"
                      @change="handleChange" style="width: 300px;"></el-input>
            <span class="font-size14">项目编码：</span>
            <el-input v-model="queryParams.projectCode" clearable placeholder="请输入项目编码"
                      @change="handleChange" style="width: 300px;"></el-input>
            <span class="font-size14">项目名称：</span>
            <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称"
                      @change="handleChange"></el-input>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-search"
                @click="query()"
                v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ02QX01') > -1"
            >
              查询
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="openDialog('add')"
            >
              新增
            </el-button>
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="openDialog('edit')"
                v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ02QX01') > -1"
            >
              编辑
            </el-button>
            <el-button
                type="text"
                size="mini"
                icon="el-icon-upload2"
                v-if="this.$store.getters.permissions.indexOf('JDWJY01YSZK01QX02') > -1"
                @click="()=>{importDialogVisible = true}"
            >
              导入Excel
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
          </div>
        </div>
        <div class="table-box">
          <Table
              :tableData="accountsReceivableList"
              :tableOptions="realTableOptions"
              :loading="debtManagementLoading"
              @getCurrentData="select"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <template slot="confirmationStatus" slot-scope="scope">
              <span v-if=" scope.row.confirmationStatus === 0">
                 未确认
              </span>
              <span v-if="scope.row.confirmationStatus === 1">
                已确认
              </span>
            </template>
            <template slot="state" slot-scope="scope">
              <span v-if="scope.row.state === 0">
                未催收
              </span>
              <span v-if="scope.row.state === 1">
                已催收
              </span>
            </template>
            <!--     催收文件       -->
            <template slot="annexIds" slot-scope="scope">
              <el-button type="text" @click="openFileDialog(scope.row.annexId,'催收文件')">查看</el-button>
            </template>
            <!--     佐证材料       -->
            <template slot="supportingDocuments" slot-scope="scope">
              <el-button type="text" @click="openFileDialog(scope.row.supportDocsIds,'佐证材料')">查看</el-button>
            </template>
            <!--     操作      -->
            <template slot="operation" slot-scope="scope">
              <el-button type="text" @click="confirm(scope.row)" >确认</el-button>
              <el-button type="text" @click="sendEmail(scope.row)">发送邮件</el-button>
            </template>


          </Table>
          <Pagination
              @handleRefresh="handleCurrentChange"
              :queryParam="queryParams"
              layout="total, sizes, prev, pager, next, jumper"
              :total="queryParams.total"
          />

        </div>
      </div>

      <el-dialog title="应收账款数据导入" :visible.sync="importDialogVisible" width="410px" :close-on-click-modal="false" append-to-body center custom-class="import-dialog">
        <div class="import-dialog-content">
          <FileImport
              uploadUrl="/accountsReceivable/importExcel"
              downloadName="应收账款数据导入模板"
              :limit="1"
              :fileSize="100"
              :apiFunctions="apiFunctions"
              @uploadSuccessData="handleUploadSuccess"
              @uploadErrorData="handleUploadError"
          />
        </div>
      </el-dialog>

      <el-dialog :title="dialogTitle" :visible.sync="addCollectionRecordDialog" width="60%" :close-on-click-modal="false" append-to-body>
        <CollectionForm
            ref="form"
            v-model="collectionFormData"
            :key="componentKey"
            @cancel="addCollectionRecordDialog = false"
        />
        <!-- 操作按钮 -->
        <div align="right">
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ collectionFormData.id ? '更 新' : '保 存' }}
          </el-button>
          <el-button @click="()=>{addCollectionRecordDialog = false}">取 消</el-button>
        </div>
      </el-dialog>

      <el-dialog :title="fileDialogTitle+'文件预览'" :visible.sync="fileDialog" width="60%" :close-on-click-modal="false" append-to-body>
        <FileUpload ref="fileupload" v-model="fileList" :limit="5" :type="2" :isShowTip="false" :disabled="true" />
      </el-dialog>

    </div>
  </div>
</template>
<script>
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import FileImport from 'components/UploadFile/FileImport.vue'

import {
  getList,
  importExcel,
  exportExcelTemplate,
  updateCollection,
  addCollection,
  getInfo
} from "api/jygl/collection";
import CollectionForm from "./components/CollectionForm.vue";
import FileUpload from  '@/components/DFDW/FileUpload1.vue';

export default {
  name: 'index',
  components: { CollectionForm , Table, Pagination, Dropdown,FileImport,FileUpload},
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyName: '',
        unit: '',
        projectCode: '',
        projectName: ''
      },
      importDialogVisible: false,
      addCollectionRecordDialog: false,
      apiFunctions:{
        exportExcelTemplate:exportExcelTemplate,
        importExcel: importExcel

      },
      realTableOptions: [],
      tableOptions: [
        {label: '催收期数', prop: 'numberOfDunningPeriods'},
        {label: '催收日期', prop: 'collectionDate'},
        {label: '公司名称', prop: 'companyName'},
        {label: '项目名称' , prop: 'projectName'},
        {label: '款项发生时间' , prop: 'transferTime'},
        {label: '最后催收日期' , prop: 'transferTime'},
        {label: '往来户单位', prop: 'unit'},
        {label: '催收状态', prop: 'state',slot:true},
        {label: '确认状态', prop: 'confirmationStatus',slot:true},
        {label: '催收文件', prop: 'annexIds',slot:true},
        {label: '佐证材料', prop: 'supportingDocuments',slot:true},
        {label: '操作', prop: 'operation',slot:true},

      ],
      debtManagementLoading: false,
      importLoading: false,
      accountsReceivableList: [],
      selectID: 0,
      collectionID: 0,
      isEdit: false,
      collectionFormData: {
        id: 0,
        numberOfDunningPeriods: '',
        collectionDate: '',
        companyName: '',
        projectName: '',
        transferTime: '',
        lastCollectionDate: '',
        unit: '',
        state: 0,
        confirmationStatus: 0,
        annexIds: '',
        supportingDocuments: '',
        remark: ''
      },
      componentKey: 0,
      dialogTitle:'',
      fileDialog:false,
      fileList:'',
      fileDialogTitle:'',
      submitLoading:false
    };
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.query()
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.query()
    },

    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
    },

    // 查询方法
    query(){
      this.debtManagementLoading = true
      getList(this.queryParams).then((res) => {
        this.accountsReceivableList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.debtManagementLoading = false
      })
    },

    // 编辑提交
    handleSubmit() {

      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          if (this.isEdit){
            updateCollection(this.collectionFormData).then(res=>{
              if (res.code === 200){
                this.$message.success( '更新催收记录成功');
                this.addCollectionRecordDialog = false;
                this.submitLoading = false;
                this.query();
              }
            }).finally(() => {
              this.submitLoading = false;
            })
          }else {
            addCollection(this.collectionFormData).then(res=>{
              if (res.code === 200){
                this.$message.success( '添加催收记录成功');
                this.addCollectionRecordDialog = false;
                this.submitLoading = false;
                this.query();
              }
            }).finally(() => {
              this.submitLoading = false;
            })
          }

        }
      })


    },
    /** 清空表单 */
    clearForm() {
      this.collectionFormData = {
        id: null,
        numberOfDunningPeriods: '',
        collectionDate: '',
        companyName: '',
        projectName: '',
        transferTime: '',
        lastCollectionDate: '',
        unit: '',
        state: 0,
        confirmationStatus: 0,
        annexIds: '',
        supportingDocuments: '',
        remark: '',
        annexId:'',
      }
    },

    openDialog(type){
      // 首先重置表单数据
      this.clearForm()
      switch (type) {
        case 'add':
          this.isEdit = false;
          this.dialogTitle = '新增'
          //获取当前登录用户
          if (!this.$store.state.user.name) {
            this.$message.error('请先登录')
            return
          }
          // 重置组件 key 强制组件渲染
          this.componentKey += 1
          this.addCollectionRecordDialog = true;
          this.$nextTick(() => {
            this.collectionFormData = {
              id: null,
              numberOfDunningPeriods: '',
              collectionDate: '',
              companyName: '',
              projectName: '',
              transferTime: '',
              lastCollectionDate: '',
              unit: '',
              state: 0,
              confirmationStatus: 0,
              annexIds: '',
              supportingDocuments: '',
              remark: '',
              annexId:'',
            }
            this.$forceUpdate()
          })

          break;
        case 'edit':
          if (this.selectID === 0){
            this.$message.warning('请先选择一条数据!')
            return;
          }
          this.isEdit = true;
          this.dialogTitle = '编辑'
          // 重置组件 key 强制组件渲染
          this.componentKey += 1
          getInfo({id:this.selectID}).then(res=>{
            this.collectionFormData = res.result;
          })
          this.addCollectionRecordDialog = true;
          break;

        default:
          break;
      }
    },

    openFileDialog(val,title){
      this.fileList = ''
      this.fileDialogTitle = ''
      this.fileList = val
      this.fileDialogTitle = title
      this.fileDialog = true
    },
    /**
     * 确认
     * @param row
     */
    confirm(row){

    },
    /**
     * 发送邮件
     * @param row
     */
    sendEmail(row){

    },



    //导入excel
    beforeUpload(file){
      // 获取上传excel文件的信息
      const fileContent = file.raw;
      // 获取文件类型
      const types = file.name.split(".")[1];
      const fileType = ["xlsx", "xls"].some(
          (item) => item === types
      );
      if (!fileContent) {
        if (!fileType) {
          alert("格式错误！请重新选择");
          return;
        }
      }
    },

    // 导入excel处理
    uploadHttpRequest(item) {
      this.importLoading = true
      const form = new FormData()
      form.append('file', item.file)
      importExcel(form).then(res => {
        if (res.code==200){
          this.$message.success(res.message)
          this.query();
        }else {
          this.$message.error(res.message)
        }
        // 安全地清除文件
        if (this.$refs.fileupload && typeof this.$refs.fileupload.clearFiles === 'function') {
          this.$refs.fileupload.clearFiles();
        }
      }).catch(() => {
        // 安全地清除文件
        if (this.$refs.fileupload && typeof this.$refs.fileupload.clearFiles === 'function') {
          this.$refs.fileupload.clearFiles();
        }
      }).finally(() => {
        this.importLoading = false
      })
    },

    // 处理文件上传成功
    handleUploadSuccess(message) {
      this.$message.success(message);
      this.importDialogVisible = false; // 关闭对话框
      this.query();
    },

    // 处理文件上传失败
    handleUploadError(message) {
      this.$message.error(message || '导入失败');
      this.importDialogVisible = false; // 关闭对话框
    },


  },

  created() {
    // 初始化表格列配置
    this.realTableOptions = [...this.tableOptions]

    // 检查是否有从汇总页面传递过来的查询参数
    if (this.$route.query.unit) {
      this.queryParams.unit = this.$route.query.unit
    }

    this.query()
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}
.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;
  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;
    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

</style>
