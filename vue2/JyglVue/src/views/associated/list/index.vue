<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <span class="font-size14">单位名称：</span>
            <el-input v-model="queryParams.companyName" clearable placeholder="请输入单位名称" @change="handleChange"
                      style="width: 300px;"></el-input>
            <el-button size="mini" type="text" icon="el-icon-search" @click="query()"
                       v-if="this.$store.getters.permissions.indexOf('JDWJY01WLH01QX01') > -1">
              查询
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd()"
                       v-if="this.$store.getters.permissions.indexOf('JDWJY01WLH01QX02') > -1">
              新增
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit()"
                       v-if="this.$store.getters.permissions.indexOf('JDWJY01WLH01QX03') > -1">
              修改
            </el-button>
            <el-button style="color: red" size="mini" type="text" icon="el-icon-delete" @click="handleDelete()"
                       v-if="this.$store.getters.permissions.indexOf('JDWJY01WLH01QX04') > -1">
              删除
            </el-button>

            <el-button type="text" size="mini" icon="el-icon-upload2"
                       v-if="this.$store.getters.permissions.indexOf('JDWJY01WLH01QX05') > -1"
                       @click="() => { importDialogVisible = true }">
              导入Excel(含模板)
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
          </div>
        </div>
        <div class="table-box">
          <Table :tableData="debtManagementList" :tableOptions="realTableOptions" :loading="debtManagementLoading"
                 @getCurrentData="select" @rowdblclick="handleRowDoubleClick">
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
          </Table>
          <Pagination @handleRefresh="handleCurrentChange" :queryParam="queryParams"
                      layout="total, sizes, prev, pager, next, jumper" :total="queryParams.total" />
        </div>
      </div>

      <!-- 新增/修改弹窗 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false"
                 append-to-body center @close="handleDialogClose">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px">
          <el-form-item label="往来户单位名称" prop="associatedUnits">
            <el-input v-model="formData.associatedUnits" placeholder="请输入往来户单位名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="统一社会信用代码" prop="uscc">
            <el-input v-model="formData.uscc" placeholder="请输入统一社会信用代码" clearable></el-input>
          </el-form-item>
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人" clearable></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="contactPersonNumber">
            <el-input v-model="formData.contactPersonNumber" placeholder="请输入联系电话" clearable></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱" clearable></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
        </div>
      </el-dialog>

      <el-dialog title="往来户单位数据导入" :visible.sync="importDialogVisible" width="410px" :close-on-click-modal="false"
                 append-to-body center custom-class="import-dialog">
        <div class="import-dialog-content">
          <FileImport uploadUrl="/debtManagement/importExcel" downloadName="往来户单位数据导入模板" :limit="1" :fileSize="100"
                      :apiFunctions="apiFunctions" @uploadSuccessData="handleUploadSuccess"
                      @uploadErrorData="handleUploadError"/>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import FileImport from 'components/UploadFile/FileImport.vue'

import {
  getList,
  importExcel,
  exportExcelTemplate,
  addData,
  updateData,
  deleteData,
  getDataById
} from "api/jygl/associated";

export default {
  name: 'index',
  components: { Table, Pagination, Dropdown, FileImport },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyName: '',
      },
      importDialogVisible: false,
      apiFunctions: {
        exportExcelTemplate: exportExcelTemplate,
        importExcel: importExcel
      },
      realTableOptions: [],
      tableOptions: [
        { label: '往来户单位名称', prop: 'associatedUnits' },
        { label: '统一社会信用代码', prop: 'uscc' },
        { label: '联系人', prop: 'contactPerson' },
        { label: '联系电话', prop: 'contactPersonNumber' },
        { label: '邮箱', prop: 'email' },
      ],
      debtManagementLoading: false,
      importLoading: false,
      debtManagementList: [],
      selectID: 0,
      selectedRow: null, // 当前选中的行数据

      // 弹窗相关数据
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false, // 是否为编辑模式
      submitLoading: false,
      formData: {
        id: null,
        associatedUnits: '',
        uscc: '',
        contactPerson: '',
        contactPersonNumber: '',
        email: ''
      },
      formRules: {
        associatedUnits: [
          { required: true, message: '请输入往来户单位名称', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.query()
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.query()
    },

    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.selectedRow = row // 保存选中的行数据
    },

    /** 双击表格行事件 - 打开修改弹窗 */
    handleRowDoubleClick(row) {
      // 检查是否有修改权限
      if (this.$store.getters.permissions.indexOf('JDWJY01WLH01QX03') === -1) {
        this.$message.warning('您没有修改权限');
        return;
      }

      // 设置选中的行数据
      this.selectedRow = row;
      this.selectID = row.id;

      // 打开修改弹窗
      this.isEdit = true;
      this.dialogTitle = '修改往来户单位';
      this.loadDataById(row.id);
    },

    // 查询方法
    query() {
      this.debtManagementLoading = true
      getList(this.queryParams).then((res) => {
        this.debtManagementList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.debtManagementLoading = false
      })
    },

    // 打开文件上传
    openFileUpload() {
      this.$refs.fileupload.$el.querySelector('.uploac-button').click();
    },

    //导入excel
    beforeUpload(file) {
      // 获取上传excel文件的信息
      const fileContent = file.raw;
      // 获取文件类型
      const types = file.name.split(".")[1];
      const fileType = ["xlsx", "xls"].some(
          (item) => item === types
      );
      if (!fileContent) {
        if (!fileType) {
          alert("格式错误！请重新选择");
          return;
        }
      }
    },

    // 导入excel处理
    uploadHttpRequest(item) {
      this.importLoading = true
      const form = new FormData()
      form.append('file', item.file)
      importExcel(form).then(res => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.query();
        } else {
          this.$message.error(res.message)
        }
        this.$refs.fileupload.clearFiles();
      }).catch(() => {
        this.$refs.fileupload.clearFiles();
      }).finally(() => {
        this.importLoading = false
      })
    },

    // 处理文件上传成功
    handleUploadSuccess(message) {
      this.$message.success(message);
      this.importDialogVisible = false; // 关闭对话框
      this.query();
    },

    // 处理文件上传失败
    handleUploadError(message) {
      this.$message.error(message || '导入失败');
      this.importDialogVisible = false; // 关闭对话框
    },

    // 新增功能
    handleAdd() {
      this.isEdit = false;
      this.dialogTitle = '新增往来户单位';
      this.resetFormData();
      this.dialogVisible = true;
    },

    // 修改功能
    handleEdit() {
      if (!this.selectedRow) {
        this.$message.warning('请先选择要修改的数据行');
        return;
      }
      this.isEdit = true;
      this.dialogTitle = '修改往来户单位';
      this.loadDataById(this.selectedRow.id);
    },

    // 删除功能
    handleDelete() {
      if (!this.selectedRow) {
        this.$message.warning('请先选择要删除的数据行');
        return;
      }

      const unitName = this.selectedRow.associatedUnits;
      this.$confirm(`是否删除${unitName}的数据？`, '确认删除', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        cancelButtonClass: 'el-button--info'
      }).then(() => {
        // 调用删除接口
        deleteData(this.selectedRow.id).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.query(); // 重新查询数据
            this.selectedRow = null; // 清空选中状态
          } else {
            this.$message.error(res.message || '删除失败');
          }
        }).catch(error => {
          this.$message.error('删除失败');
          console.error('删除错误:', error);
        });
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 根据ID加载数据
    loadDataById(id) {
      getDataById(id).then(res => {
        if (res.code === 200) {
          this.formData = res.result;
          this.dialogVisible = true;
        } else {
          this.$message.error(res.message || '获取数据失败');
        }
      }).catch(error => {
        this.$message.error('获取数据失败：' + error);
      });
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        id: null,
        associatedUnits: '',
        uscc: '',
        contactPerson: '',
        contactPersonNumber: '',
        email: ''
      };
      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    // 关闭弹窗
    handleDialogClose() {
      this.resetFormData();
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          const apiMethod = this.isEdit ? updateData : addData;
          const submitData = { ...this.formData };

          // 如果是新增，删除id字段
          if (!this.isEdit) {
            delete submitData.id;
          }

          apiMethod(submitData).then(res => {
            if (res.code === 200) {
              this.$message.success(this.isEdit ? '修改成功' : '新增成功');
              this.dialogVisible = false;
              this.query(); // 重新查询数据
              this.selectedRow = null; // 清空选中状态
            } else {
              this.$message.error(res.message || (this.isEdit ? '修改失败' : '新增失败'));
            }
          }).catch(error => {
            this.$message.error(this.isEdit ? '修改失败' : '新增失败');
            console.error('提交错误:', error);
          }).finally(() => {
            this.submitLoading = false;
          });
        }
      });
    },


  },

  created() {
    // 初始化表格列配置
    this.realTableOptions = [...this.tableOptions]

    // 检查是否有从汇总页面传递过来的查询参数
    if (this.$route.query.companyName) {
      this.queryParams.companyName = this.$route.query.companyName
    }

    this.query()
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;

    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}
</style>
