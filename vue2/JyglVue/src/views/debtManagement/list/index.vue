<template>
  <div class="main" ref="main">
    <div class="process-info" style="width: 100%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <span class="font-size14">单位名称：</span>
            <el-input v-model="queryParams.companyName" clearable placeholder="请输入单位名称" @change="handleChange"
              style="width: 300px;"></el-input>
            <span class="font-size14">项目编码：</span>
            <el-input v-model="queryParams.projectCode" clearable placeholder="请输入项目编码" @change="handleChange"
              style="width: 300px;"></el-input>
            <span class="font-size14">项目名称：</span>
            <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称"
              @change="handleChange"></el-input>
            <el-button size="mini" type="text" icon="el-icon-search" @click="clickQuery()"
              v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ01QX01') > -1">
              查询
            </el-button>
            <el-button size="mini" type="text" icon="el-icon-download" @click="exportExcel()"
                       v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ01QX01') > -1">
              导出
            </el-button>
            <el-button type="text" size="mini" icon="el-icon-upload2"
              v-if="this.$store.getters.permissions.indexOf('JDWJY01YXZJ01QX02') > -1"
              @click="() => { importDialogVisible = true }">
              导入Excel
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
          </div>
        </div>
        <div class="table-box">
          <Table :tableData="debtManagementList" :tableOptions="realTableOptions" :loading="debtManagementLoading"
            @getCurrentData="select">
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <!-- 项目完工时间 -->
            <template slot="projectCompletedDate" slot-scope="scope">
              <!-- 只保留年月日 -->
              {{ scope.row.projectCompletedDate ? scope.row.projectCompletedDate.split(' ')[0] : '' }}
            </template>
            <!-- 送审时间 -->
            <template slot="forReviewTime" slot-scope="scope">
              <!-- 只保留年月日 -->
              {{ scope.row.forReviewTime ? scope.row.forReviewTime.split(' ')[0] : '' }}
            </template>
            <!-- 审定时间 -->
            <template slot="forReviewTime" slot-scope="scope">
              <!-- 只保留年月日 -->
              {{ scope.row.reviewTime ? scope.row.reviewTime.split(' ')[0] : '' }}
            </template>
          </Table>
          <Pagination @handleRefresh="handleCurrentChange" :queryParam="queryParams"
            layout="total, sizes, prev, pager, next, jumper" :total="queryParams.total" />
        </div>
      </div>

      <el-dialog title="隐性债权数据导入" :visible.sync="importDialogVisible" width="410px" :close-on-click-modal="false"
        append-to-body center custom-class="import-dialog">
        <div class="import-dialog-content">
          <FileImport uploadUrl="/debtManagement/importExcel" downloadName="隐性债权数据导入模板" :limit="1" :fileSize="100"
            :apiFunctions="apiFunctions" @uploadSuccessData="handleUploadSuccess"
            @uploadErrorData="handleUploadError" :showDownload="false"/>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import FileImport from 'components/UploadFile/FileImport.vue'

import {
  getList,
  importExcel, exportExcel
} from "api/jygl/debtManagement";
import {downLoad} from "@/utils/tool";

export default {
  name: 'index',
  components: { Table, Pagination, Dropdown, FileImport },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        companyName: '',
        projectCode: '',
        projectName: ''
      },
      importDialogVisible: false,
      apiFunctions: {
        importExcel: importExcel
      },
      realTableOptions: [],
      tableOptions: [
        { label: '单位名称', prop: 'companyName' },
        { label: '项目编码', prop: 'projectCode' },
        { label: '项目名称', prop: 'projectName' },
        { label: '项目状态', prop: 'projectStatus' },
        { label: '客户名称', prop: 'customerName' },
        { label: '客户性质', prop: 'customerNature' },
        { label: '项目完工时间', prop: 'projectCompletedDate', slot: true },
        { label: '项目完工时间分类-按年', prop: 'projectCompletedTypeByYear' },
        { label: '清理状态', prop: 'cleanType' },
        { label: '合同金额（元）', prop: 'contractAmount' },
        { label: '送审时间', prop: 'forReviewTime', slot: true },
        { label: '送审金额（元）', prop: 'forReviewAmount' },
        { label: '审定时间', prop: 'reviewTime', slot: true },
        { label: '审定金额（元）', prop: 'reviewAmount' },
        { label: '累计开票金额（元）', prop: 'cumulativeInvoicingAmount' },
        { label: '隐性债权待清理金额（元）', prop: 'toBeCleanedAmount' },
        { label: '上月隐性债权清理金额（元）', prop: 'lastMonthCleanUpAmount' },
        { label: '本月隐性债权清理金额（元）', prop: 'thisMonthCleanUpAmount' },
        { label: '今年隐性债权清理金额（元）', prop: 'thisYearCleanUpAmount' },
      ],
      debtManagementLoading: false,
      importLoading: false,
      debtManagementList: [],
      selectID: 0
    };
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.query()
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.query()
    },

    /** 单击表事件 */
    select(row) {
      this.selectID = row.Id
    },

    clickQuery() {
      this.queryParams.isSummary = false
      this.query()
    },
    // 查询方法
    query() {
      this.debtManagementLoading = true
      getList(this.queryParams).then((res) => {
        this.debtManagementList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.debtManagementLoading = false
      })
    },
    exportExcel() {
      // 显示导出遮罩层
      const loading = this.$loading({
        lock: true,
        text: '正在导出中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      exportExcel(this.queryParams).then((res) => {
        let fileName ='应收账款数据'  + new Date().getTime() + '.xlsx'
        downLoad(res, fileName)
      }).catch((error) => {
        console.error('导出失败:', error);
        this.$message.error('导出失败，请重试');
      }).finally(() => {
        // 关闭遮罩层
        loading.close();
      });
    },
    // 处理文件上传成功
    handleUploadSuccess(message) {
      this.$message.success(message);
      this.importDialogVisible = false; // 关闭对话框
      this.query();
    },

    // 处理文件上传失败
    handleUploadError(message) {
      this.$message.error(message || '导入失败');
      this.importDialogVisible = false; // 关闭对话框
    },


  },

  created() {
    // 初始化表格列配置
    this.realTableOptions = [...this.tableOptions]

    // 检查是否有从汇总页面传递过来的查询参数
    if (this.$route.query.companyName && this.$route.query.isSummary) {
      this.queryParams.companyName = this.$route.query.companyName
      this.queryParams.isSummary = this.$route.query.isSummary
    }

    this.query()
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;

  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;

    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}
</style>
