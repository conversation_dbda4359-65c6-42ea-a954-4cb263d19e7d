<template>
  <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParam.pageNum"
      :page-sizes="[5,10,20,30]"
      :page-size="queryParam.pageSize"
      :layout="layout"
      :total="total">
  </el-pagination>
</template>

<script>
export default {
  name:'index',
  props:{
    queryParam:{
      type: Object,
      default:()=>{
        return {
          pageNum: 1,
          pageSize: 10
        }
      }
    },
    total:{
      type:Number,
      default: 0
    },
    layout:{
      type:String,
      default: 'total, sizes, prev, pager, next, jumper'
    }
  },
  methods:{
    handleSizeChange(val){
      this.queryParam.pageSize = val
      this.$emit('handleRefresh',this.queryParam)
    },
    handleCurrentChange(val){
      this.queryParam.pageNum = val
      this.$emit('handleRefresh',this.queryParam)
    },
  }
}
</script>

<style>

</style>