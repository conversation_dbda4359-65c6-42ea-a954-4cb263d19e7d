<template>
  <div>
    <div v-if="showDownload">
      为确保上传数据与列表内容匹配，请先下载
      <el-button
          size="mini"
          type="text"
          @click="download(downloadName)"
      >导入模板
      </el-button>
    </div>
    <el-upload
        class="upload-demo"
        :disabled="disabled"
        drag
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :action="baseUrl + uploadUrl"
        :http-request="customUpload"
        name="file"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :headers="headers"
        :show-file-list="false"
        accept=".xlsx"
        :file-list="fileList"
        :class="{width: '100%'}"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text"><em>{{myTips}}</em>将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">只能上传{{fileType.toString()}}文件，且不超过{{fileSize}}M</div>
    </el-upload>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getToken} from "@/utils/auth";
import {downLoad} from "@/utils/tool";

export default {
  name: "FIleUpload",
  props: {
    // 图片数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['xlsx']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 上传接口
    uploadUrl: {
      type: String,
      default: ''
    },
    // 导入示例文件名
    downloadName: {
      type: String,
      default: ''
    },
    // API函数对象
    apiFunctions: {
      type: Object,
      default: () => ({})
    },
    //其他参数
    otherParams: {
      type: Object,
      default: () => ({})
    },
    disabled:{
      type:Boolean,
      default:false,
    },
    myTips:{
      type: String,
      default: ''
    },
    //是否展示下载模板
    showDownload: {
      typeof: Boolean,
      default: true
    }
    
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API+'/jfcheck',
      appUrl: '/app',
      jtoken: Cookies.get('jfcheck-Token'),
      headers: {'Authorization': getToken()},
      fileList: []
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  methods: {
    // 自定义上传方法，使用传入的importExcel函数
    customUpload(options) {
      const formData = new FormData();
      formData.append('file', options.file);
      // 遍历this.otherParams对象，将其键值对放入formData
      if (this.otherParams) {
        Object.keys(this.otherParams).forEach(key => {
          // 判断值是否为null或undefined，避免无效值
          if (this.otherParams[key] != null) {
            formData.append(key, this.otherParams[key]);
          }
        });
      }

      this.loading = this.$loading({
        lock: true,
        text: '上传中',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      if (this.apiFunctions && this.apiFunctions.importExcel) {
        this.apiFunctions.importExcel(formData)
          .then(res => {
            this.handleUploadSuccess(res);
          })
          .catch(error => {
            this.handleUploadError(error);
          });
      } else {
        this.$message.error('导入功能未配置');
        this.loading.close();
      }
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      // 返回的参数是对象
      console.log(res)
      if (res.code === 200) {
        this.$emit('uploadSuccessData', res.message || "上传成功")
      } else {
        this.$emit('uploadErrorData', res.message)
      }
      this.loading.close()
    },
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          return !!(fileExtension && fileExtension.indexOf(type) > -1)
        })
      } else {
        isImg = file.type.indexOf('image') > -1
      }

      if (!isImg) {
        this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError(error) {
      console.error('上传失败:', error);
      this.$message({
        type: 'error',
        message: '上传失败'
      })
      if (this.loading) {
        this.loading.close()
      }
    },
    // 下载导入示例，使用传入的exportExcelTemplate函数
    download(name) {

      if (this.apiFunctions && this.apiFunctions.exportExcelTemplate) {
        this.apiFunctions.exportExcelTemplate({name: name}).then((res) => {

          const fileName =  name + new Date().getTime() + '.xlsx'
          downLoad(res, fileName)
        })
      } else {
        this.$message.error('导出功能未配置');
      }
    },
  }
}
</script>

<style scoped>
.upload-demo {
  width: 100%;
}
</style>
