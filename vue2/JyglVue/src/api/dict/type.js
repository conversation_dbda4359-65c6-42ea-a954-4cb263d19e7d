import request from '@/utils/request'

// 查询字典类型列表
export function listType(query) {
    return request({
        url: '/dwJygl/dfdw/dictType/typePage',
        method: 'post',
        data: query
    })
}

// 新增字典类型
export function addType(data) {
    return request({
        url: '/dwJygl/dfdw/dictType/create',
        method: 'post',
        data: data
    })
}

// 修改字典类型
export function updateType(data) {
    return request({
        url: '/dwJygl/dfdw/dictType/update',
        method: 'put',
        data: data
    })
}

// 删除字典类型
export function delType(dictId) {
    return request({
        url: '/dwJygl/dfdw/dictType/delete?id=' + dictId,
        method: 'delete'
    })
}
