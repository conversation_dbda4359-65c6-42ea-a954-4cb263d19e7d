import request from '@/utils/request'

/**
 * 下载示例
 * @returns
 */
export function downloadImportExample(params){
    return request({
        url: '/dwJygl/jygl/file/downloadImportExample',
        method:'post',
        data: params,
        responseType: 'blob'
    })
}

/**
 * 读取文件名
 * @returns
 */
export function getFileName(params) {
    return request({
        url: '/dwJygl/jygl/file/getFileName',
        method: 'get',
        params: params
    })
}

export function getFileList(params) {
    return request({
        url: '/dwJygl/jygl/file/getFileList',
        method: 'post',
        data: params
    })
}


/**
 * 下载
 * @returns
 */
export function download(params) {
    return request({
        url: '/dwJygl/jygl/file/download',
        method: 'get',
        params: params,
        responseType: 'blob'
    })
}
