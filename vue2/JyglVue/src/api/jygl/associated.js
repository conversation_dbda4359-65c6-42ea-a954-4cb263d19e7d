import request from '@/utils/request'

/** 往来户单位列表 */
export function getList (params) {
    return request({
        url: '/dwJygl/associated/getList',
        method: 'post',
        data: params
    })
}

/**
 * 新增往来户单位数据
 */
export function addData(params){
    return request({
        url: '/dwJygl/associated/add',
        method: 'post',
        data: params
    })
}

/**
 * 修改往来户单位数据
 */
export function updateData(params){
    return request({
        url: '/dwJygl/associated/update',
        method: 'post',
        data: params
    })
}

/**
 * 根据ID查询往来户单位详情
 */
export function getDataById(id){
    return request({
        url: `/dwJygl/associated/getById/${id}`,
        method: 'get'
    })
}

/**
 * 删除往来户单位数据
 */
export function deleteData(id){
    return request({
        url: `/dwJygl/associated/delete/${id}`,
        method: 'delete'
    })
}

/**
 * 导入excel
 */
export function importExcel(params){
    return request({
        url: '/dwJygl/associated/importExcel',
        method: 'post',
        data: params,
        headers: { "Content-Type": "multipart/form-data" }
    })
}

/**
 * 导出Excel模板
 */
export function exportExcelTemplate(){
    return request({
        url: '/dwJygl/associated/exportExcelTemplate',
        method: 'get',
        responseType: 'blob'
    })
}
