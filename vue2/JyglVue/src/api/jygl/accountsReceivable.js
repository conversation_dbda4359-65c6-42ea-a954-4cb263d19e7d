import request from '@/utils/request'

/** 隐性债券列表 */
export function getList (params) {
    return request({
        url: '/dwJygl/accountsReceivable/getList',
        method: 'post',
        data: params
    })
}

/**
 * 导入excel
 */
export function importExcel(params){
    return request({
        url: '/dwJygl/accountsReceivable/importExcel',
        method: 'post',
        data: params,
        headers: { "Content-Type": "multipart/form-data" }
    })
}

/**
 * 导出excel
 */
export function downLoadExcel(params){
    return request({
        url: '/dwJygl/accountsReceivable/exportExcel',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

/** 导出excel模板 */
export function exportExcelTemplate () {
    return request({
        url: '/dwJygl/accountsReceivable/exportExcelTemplate',
        method: 'post',
        responseType: 'blob'
    })
}


/**
 * 获取汇总数据
 */
export function getSummary(){
    return request({
        url: '/dwJygl/accountsReceivable/getSummary',
        method: 'get'
    })
}

export function getProjectNameAndCode(){
    return request({
        url: '/dwJygl/accountsReceivable/getProjectNameAndCode',
        method: 'get'
    })
}
