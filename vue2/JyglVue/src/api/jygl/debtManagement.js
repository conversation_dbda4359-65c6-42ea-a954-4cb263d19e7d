import request from '@/utils/request'

/** 隐性债券列表 */
export function getList (params) {
    return request({
        url: '/dwJygl/debtManagement/getList',
        method: 'post',
        data: params
    })
}

/**
 * 导入excel
 */
export function importExcel(params){
    return request({
        url: '/dwJygl/debtManagement/importExcel',
        method: 'post',
        data: params,
        headers: { "Content-Type": "multipart/form-data" }
    })
}

/**
 * 获取汇总数据
 */
export function getSummary(){
    return request({
        url: '/dwJygl/debtManagement/getSummary',
        method: 'get'
    })
}

/**
 * 导出excel
 */
export function exportExcel(params){
    return request({
        url: '/dwJygl/debtManagement/exportExcel',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

