import {listDataAll} from "api/dict/data";

const state = {
    /**
     * 数据字典 MAP
     * key：数据字典大类枚举值 dictType
     * dictValue：数据字典小类数值 {dictValue: '', dictLabel: ''} 的数组
     */
    dictDatas: {}
}

const mutations = {
    SET_DICT_DATAS: (state, dictDatas) => {
        state.dictDatas = dictDatas
    }
}

const actions = {
    GetDictDatas({ commit }) {
        return new Promise((resolve, reject)=> {
            listDataAll({type: 'jygl-'}).then(res => {
                if(res.code == 200) {
                    // 设置数据
                    const dictDataMap = {}
                    res.result.forEach(dictData => {
                        // 获得 dictType 层级
                        const enumValueObj = dictDataMap[dictData.dictType]
                        if (!enumValueObj) {
                            dictDataMap[dictData.dictType] = []
                        }
                        // 处理 dictValue 层级
                        dictDataMap[dictData.dictType].push({
                            value: dictData.value,
                            label: dictData.label,
                        })
                    })
                    // 存储到 Store 中
                    commit('SET_DICT_DATAS', dictDataMap)
                    resolve()
                } else {
                    reject()
                }
            }).catch(error => {
                reject(error)
            })
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}