<template>
  <div>
    <logo :collapse="false"/>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="$route.path"
          router
          :unique-opened="true"
          class="el-menu-vertical-demo"
          :active-Title-color="theme">
        <component
            v-for="route in menuList"
            :key="route.Id"
            :index="route.Title"
            :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'">
          <template slot="title">
            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
            <span slot="title" class="font-size14">{{route.Title}}</span>
          </template>
          <template v-if="route.childList && route.childList.length > 0">
            <el-menu-item
                v-for="routeChild in route.childList"
                :key="routeChild.Id"
                :index="routeChild.Url"
                :route="{path: routeChild.Url}"
                :disabled="!routeChild.HasPermi">
              <span slot="title">{{routeChild.Title}}</span>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin} from "api/login";
import {getModule} from "api/common";

export default {
  components: {Logo},
  data() {
    return {
      urlList: [],
      menuList: []
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  created() {
    MLogin().then(res=>{
      if(res.success){
        this.getMenuList()
        this.$store.dispatch('dict/GetDictDatas')
      }else{
        this.$router.push('/login')
      }
    })
  },
  methods:{
    async getMenuList(){
      await getModule().then(res=>{
        const functionList = (res.data || []).filter(item => item.Id === 'NDWCC01' || item.Id === 'JDWWX01')
        this.urlList = functionList.flatMap(item => item.childList.map(child => child.Url));
        this.menuList = [
          {
            Id: 'NDWCC01CLGL',
            Title: '车辆管理',
            childList: [
              {
                Id: 'NDWCC01CD01',
                Title: '部门管理',
                Url: '/dzcc/cdzgl',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/cdzgl') > -1
              },
              {
                Id: 'NDWCC01CL01',
                Title: '车辆管理',
                Url: '/dzcc/clgl',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/clgl') > -1
              },
              {
                Id: 'NDWCC01BG01',
                Title: '车辆办公点',
                Url: '/dzcc/clbgd',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/clbgd') > -1
              },
              {
                Id: 'NDWCC01CD04',
                Title: '人员车辆配置',
                Url: '/dzcc/cdzpz',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/cdzpz') > -1
              },
              {
                Id: 'NDWCC01CLPZ01',
                Title: '车辆信息配置',
                Url: '/dzcc/clxxpz',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/cdzpz') > -1
              },
              {
                Id: 'JDWCC01JBF03',
                Title: '加班费计算规则',
                Url: '/dzcc/jbfjs',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/jbfjs') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01CCD',
            Title: '出车单',
            childList: [
              {
                Id: 'NDWCC01CD02',
                Title: '出车单',
                Url: '/dzcc/ccd',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/ccd') > -1
              },
              {
                Id: 'NDWCC01GJ01',
                Title: '车辆轨迹',
                Url: '/dzcc/clgj',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/clgl') > -1
              },
              {
                Id: 'NDWCC01PJ01',
                Title: '出车单评价',
                Url: '/dzcc/pj',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/pj') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01GJ',
            Title: '告警',
            childList: [
              {
                Id: 'NDWCC01CY01',
                Title: '加班车辆信息',
                Url: '/dzcc/jbclxx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/jbclxx') > -1
              },
              {
                Id: 'NDWCC01CB01',
                Title: 'GPS异常记录',
                Url: '/dzcc/cbjl',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/cbjl') > -1
              },
              {
                Id: 'NDWCC01LC01',
                Title: '里程告警',
                Url: '/dzcc/lcgj',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/lcgj') > -1
              },
              {
                Id: 'NDWCC01CY03',
                Title: '异常车辆流程',
                Url: '/dzcc/yccllc',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/yccllc') > -1
              },
              {
                Id: 'NDWCC01CL02',
                Title: '跨界告警',
                Url: '/dzcc/xcgj',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/xcgj') > -1
              },
              {
                Id: 'NDWCC01GJ02',
                Title: '无轨迹告警',
                Url: '/dzcc/wgjgj',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/wgjgj') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01RYQX',
            Title: '人员权限',
            childList: [
              {
                Id: 'NDWCC01RY01',
                Title: '人员权限配置',
                Url: '/dzcc/ryqx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/ryqx') > -1
              },
              {
                Id: 'NDWCC01SJ01',
                Title: '司机配置',
                Url: '/dzcc/sjqx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/sjqx') > -1
              },
              {
                Id: 'NDWCC01JJR01',
                Title: '节假日配置',
                Url: '/dzcc/jjrpz',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/jjrpz') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01BB',
            Title: '报表',
            childList: [
              {
                Id: 'NDWCC01BB01',
                Title: '里程报表',
                Url: '/dzcc/bb',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/bb') > -1
              },
              {
                Id: 'NDWCC01CD05',
                Title: '行程报表',
                Url: '/dzcc/yxts',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/yxts') > -1
              },
              {
                Id: 'NDWCC01CY02',
                Title: '车辆移动情况',
                Url: '/dzcc/clyd',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/clyd') > -1
              },
              {
                Id: 'NDWCC01SY01',
                Title: '车辆使用情况',
                Url: '/dzcc/clsy',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/clsy') > -1
              },
              {
                Id: 'NDWCC01SJ02',
                Title: '司机填报里程',
                Url: '/dzcc/sjtblc',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/sjtblc') > -1
              },
              {
                Id: 'NDWCC01JBF01',
                Title: '加班费信息',
                Url: '/dzcc/jbfxx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/jbfxx') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01SJGG',
            Title: '司机公告',
            childList: [
              {
                Id: 'NDWCC01TG01',
                Title: '司机公告',
                Url: '/dzcc/sjtg',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/sjtg') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01XXTB',
            Title: '信息填报',
            childList: [
              {
                Id: 'NDWCC01SJ03',
                Title: '资质信息',
                Url: '/dzcc/zzxx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/zzxx') > -1
              },
              {
                Id: 'NDWCC01ZC01',
                Title: '驾驶员信息',
                Url: '/dzcc/jsyxx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/jsyxx') > -1
              }
            ]
          },
          {
            Id: 'NDWCC01ZCTZ',
            Title: '资产台账',
            childList: [
              {
                Id: 'NDWCC01ZC01',
                Title: '车辆信息',
                Url: '/dzcc/clxx',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/clxx') > -1
              },
            ]
          },
          {
            Id: 'JDWWX01',
            Title: '车辆维修',
            childList: [
              {
                Id: 'JDWWX01PZ01',
                Title: '厂家配置',
                Url: '/dzcc/clwx-cjpz',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/clwx/cjpz') > -1
              },
              {
                Id: 'JDWWX01DJ01',
                Title: '维修单',
                Url: '/dzcc/clwx-wxd',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/clwx/wxd') > -1
              },
            ]
          },
          {
            Id: 'NDWCC01JTSJ',
            Title: '集团属地化管理',
            childList: [
              {
                Id: 'NDWCC01JTSJ01',
                Title: '驾驶员工作量清单',
                Url: '/dzcc/jtsjqd',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/jtsjqd') > -1
              },
              {
                Id: 'NDWCC01YS01',
                Title: '延时',
                Url: '/dzcc/yssj',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/yssj') > -1
              },
              {
                Id: 'NDWCC01YSSP01',
                Title: '延时审批',
                Url: '/dzcc/yssp',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/yssp') > -1
              },
              {
                Id: 'NDWCC01CCDD01',
                Title: '出车地点',
                Url: '/dzcc/ccdd',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dzcc/ccdd') > -1
              },
              {
                Id: "NDWCC01CDGZL001",
                Title: "车队工作量汇总",
                Url: "/dzcc/cdgzl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf("/dzcc/cdgzl") > -1,
              },
              {
                Id: "NDWCC01JBF02",
                Title: "集团加班费",
                Url: "/dzcc/jtjbf",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf("/dzcc/jtjbf") > -1,
              },
            ]
          },
        ]
        let firstUrl = ''
        this.menuList.forEach((value) => {
          if (firstUrl != '') {
            return
          }
          value.childList.forEach((param) => {
            if (firstUrl != '') {
              return
            }
            if (param.HasPermi) {
              firstUrl = param.Url
            }
          })
        })
        //TODO 获取后台模块列表 默认跳转到第一个
        if (this.$router.currentRoute.fullPath !== firstUrl) {
          this.$router.push(firstUrl)
        }
        this.$forceUpdate()
      })
    }
  }
};
</script>
