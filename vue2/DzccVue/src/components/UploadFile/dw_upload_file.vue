<template>
  <div class="dw-upload-file" :class="{ 'is-disabled': disabled }">
    <el-upload
      ref="upload"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :file-list="fileList"
      :multiple="multiple"
      :accept="accept"
      :limit="limit"
      :disabled="disabled"
      :show-file-list="showFileList"
      :auto-upload="autoUpload"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :list-type="listType"
    >
      <!-- 只在非禁用状态下显示上传按钮 -->
      <template v-if="!disabled">
        <slot>
          <el-button size="small" type="primary">
            <i class="el-icon-upload"></i>
            {{ buttonText }}
          </el-button>
        </slot>
        <div slot="tip" class="el-upload__tip" v-if="tip">
          {{ tip }}
        </div>
      </template>
    </el-upload>

    <!-- 文件预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      :title="previewTitle"
      width="60%"
      append-to-body
    >
      <div class="preview-content">
        <!-- 图片预览 -->
        <img
          v-if="isImage(previewFile)"
          :src="previewFile.url"
          style="width: 100%; max-height: 500px; object-fit: contain;"
          alt="预览图片"
        />
        <!-- 视频预览 -->
        <div v-else-if="isVideo(previewFile)" class="video-preview">
          <div class="video-container">
            <video
              ref="videoPlayer"
              :src="getStreamingUrl(previewFile.filepath)"
              controls
              style="width: 100%; max-height: 500px;"
              preload="none"
              @loadstart="onVideoLoadStart"
              @loadedmetadata="onVideoLoadedMetadata"
              @canplay="onVideoCanPlay"
              @error="onVideoError"
              @progress="onVideoProgress"
            >
              您的浏览器不支持视频播放
            </video>

            <!-- 加载状态 -->
            <div v-if="videoLoading" class="video-loading">
              <el-progress
                :percentage="videoLoadProgress"
                :show-text="false"
                style="margin-bottom: 10px;"
              ></el-progress>
              <p>正在加载视频... {{ videoLoadProgress }}%</p>
            </div>

            <!-- 视频信息提示 -->
            <div v-if="showVideoInfo" class="video-info-tip">
              <el-alert
                :title="getVideoSizeWarning(previewFile.size)"
                type="warning"
                :closable="false"
                show-icon
              >
                <template slot="default">
                  <p>大文件建议下载后本地播放以获得更好体验</p>
                  <el-button size="mini" type="text" @click="forcePlayVideo">
                    仍要在线播放
                  </el-button>
                </template>
              </el-alert>
            </div>
          </div>

          <div class="file-actions">
            <p>文件名：{{ previewFile.name }}</p>

            <div class="action-buttons">
              <el-button type="primary" @click="downloadFile(previewFile)">
                <i class="el-icon-download"></i>
                下载文件
              </el-button>
              <el-button
                v-if="isLargeVideo(previewFile.size)"
                type="success"
                @click="openExternalPlayer(previewFile.url)"
              >
                <i class="el-icon-video-play"></i>
                外部播放器
              </el-button>
            </div>
          </div>
        </div>
        <!-- 其他文件类型显示文件信息 -->
        <div v-else class="file-info">
          <i class="el-icon-document"></i>
          <p>文件名：{{ previewFile.name }}</p>

          <el-button type="primary" @click="downloadFile(previewFile)">
            <i class="el-icon-download"></i>
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFileList, deleteFile } from '@/api/file'
import Cookies from "js-cookie";
import {getToken} from '@/utils/auth'

export default {
  name: 'DwUploadFile',
  props: {
    // v-model 绑定的值，文件ID字符串，如 "1,2,3"
    value: {
      type: String,
      default: ''
    },
    // 上传地址
    action: {
      type: String,
      default: '/api/file/upload'
    },
    // 上传请求头
    headers: {
      type: Object,
      default: () => ({})
    },
    // 上传时附带的额外参数
    data: {
      type: Object,
      default: () => ({})
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: ''
    },
    // 最大上传数量
    limit: {
      type: Number,
      default: 10
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示文件列表
    showFileList: {
      type: Boolean,
      default: true
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true
    },
    // 文件列表类型
    listType: {
      type: String,
      default: 'text' // text/picture/picture-card
    },
    // 按钮文字
    buttonText: {
      type: String,
      default: '选择文件'
    },
    // 提示文字
    tip: {
      type: String,
      default: ''
    },
    // 文件大小限制（MB）
    maxSize: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      fileList: [],
      previewVisible: false,
      previewFile: {},
      previewTitle: '文件预览',
      baseUrl: process.env.VUE_APP_BASE_API + '/dwDzcc',
      appUrl: '/app',
      token: Cookies.get('Admin-Token'),
      videoLoading: false,
      videoLoadProgress: 0,
      showVideoInfo: false
    }
  },
  computed: {
    uploadUrl() {
      return this.action
    },
    uploadHeaders() {
      return {
        'Authorization':  getToken(),
        ...this.headers
      }
    },
    uploadData() {
      return {
        ...this.data
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.initFileList(newVal)
      },
      immediate: true
    }
  },
  methods: {
    // 初始化文件列表
    async initFileList(fileIds) {
      if (!fileIds) {
        this.fileList = []
        return
      }

      try {
       // const ids = fileIds.split(',').filter(id => id.trim())
        if (fileIds.length === 0) {
          this.fileList = []
          return
        }

        // 调用API获取文件信息
        const response = await getFileList({ids:fileIds})
        if (response.code === 200 && response.result) {
          this.fileList = response.result.map(file => ({
            uid: file.id,
            name: file.filename || file.name,
            status: 'success',
            //url: file.filepath || file.url,
            url: this.baseUrl + '/dzcc/file/download?header=1&adminToken=' + this.token + '&filePath=' + file.filepath,

            //url: this.appUrl + '/static/' + file.filepath + '?header=1&adminToken=' + this.token,
            filepath: file.filepath,
            result: { data: { id: file.id } }
          }))
        }
      } catch (error) {
        console.error('获取文件列表失败:', error)
        this.$message.error('获取文件列表失败')
      }
    },

    // 上传前的钩子
    beforeUpload(file) {
      // 检查文件大小
      if (this.maxSize && file.size / 1024 / 1024 > this.maxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB`)
        return false
      }

      // 检查文件类型
      if (this.accept) {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileType = '.' + file.name.split('.').pop().toLowerCase()
        const mimeType = file.type

        const isValidType = acceptTypes.some(type => {
          if (type.startsWith('.')) {
            return fileType === type.toLowerCase()
          } else {
            return mimeType.includes(type)
          }
        })

        if (!isValidType) {
          this.$message.error(`只能上传 ${this.accept} 格式的文件`)
          return false
        }
      }

      this.$emit('before-upload', file)
      return true
    },

    // 上传成功回调
    handleSuccess(response, file, fileList) {
      if (response.code === 200 && response.data) {
        // 更新文件的response信息
        file.response = response
        file.uid = response.data.id

        // 更新v-model的值
        this.updateValue()

        this.$message.success('文件上传成功')
        this.$emit('success', response, file, fileList)
      } else {
        this.$message.error(response.message || '文件上传失败')
        this.handleError(response, file, fileList)
      }
    },

    // 上传失败回调
    handleError(error, file, fileList) {
      this.$message.error('文件上传失败')
      this.$emit('error', error, file, fileList)

      // 从文件列表中移除失败的文件
      const index = this.fileList.findIndex(item => item.uid === file.uid)
      if (index > -1) {
        this.fileList.splice(index, 1)
      }
    },

    // 删除文件回调
    async handleRemove(file, fileList) {
      try {
        // 如果文件已上传成功，调用删除API
        if (file.response && file.response.data && file.response.data.id) {
          await deleteFile(file.response.data.id)
        }

        // 更新v-model的值
        this.updateValue()

        this.$message.success('文件删除成功')
        this.$emit('remove', file, fileList)
      } catch (error) {
        console.error('删除文件失败:', error)
        this.$message.error('删除文件失败')
      }
    },

    // 文件超出个数限制回调
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传 ${this.limit} 个文件`)
      this.$emit('exceed', files, fileList)
    },

    // 文件预览回调
    handlePreview(file) {
      //判断是否是除了视频和照片以外的文件
      if (!this.isImage(file) && !this.isVideo(file)) {
        //直接下载
        this.downloadFile(file)
        return
      }

      this.previewFile = file
      this.previewTitle = `预览 - ${file.name}`
      this.previewVisible = true

      // 重置视频状态
      this.videoLoading = false
      this.videoLoadProgress = 0

      // 如果是大视频文件，显示警告信息
      if (this.isVideo(file) && this.isLargeVideo(file.size)) {
        this.showVideoInfo = true
      } else {
        this.showVideoInfo = false
      }

      this.$emit('preview', file)
    },

    // 更新v-model的值
    updateValue() {
      const fileIds = this.fileList
        .filter(file => file.response && file.response.data && file.response.data.id)
        .map(file => file.response.data.id)
        .join(',')

      this.$emit('input', fileIds)
      this.$emit('change', fileIds)
    },

    // 判断是否为图片
    isImage(file) {
      if (!file.name) return false
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      const fileExt = file.name.split('.').pop().toLowerCase()
      return imageTypes.includes(fileExt)
    },

    // 判断是否为视频
    isVideo(file) {
      if (!file.name) return false
      const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp', 'rmvb']
      const fileExt = file.name.split('.').pop().toLowerCase()
      return videoTypes.includes(fileExt)
    },

    // 判断是否为大视频文件（超过50MB）
    isLargeVideo(fileSize) {
      return fileSize && fileSize > 50 * 1024 * 1024
    },

    // 获取流式播放URL（支持HTTP Range请求）
    getStreamingUrl(originalUrl) {
      // 使用支持流式传输的download2接口
      const baseUrl = process.env.VUE_APP_BASE_API || ''
      return `${baseUrl}/dwDzcc/dzcc/file/download2?filePath=${encodeURIComponent(originalUrl)}`
    },

    // 获取视频大小警告信息
    getVideoSizeWarning(fileSize) {
      if (fileSize > 100 * 1024 * 1024) {
        return '文件较大（>100MB），在线播放可能较慢'
      } else if (fileSize > 50 * 1024 * 1024) {
        return '文件较大（>50MB），建议下载后播放'
      }
      return '正在加载视频...'
    },

    // 视频开始加载
    onVideoLoadStart() {
      this.videoLoading = true
      this.videoLoadProgress = 0
    },

    // 视频元数据加载完成
    onVideoLoadedMetadata() {
      this.videoLoadProgress = 30
    },

    // 视频可以开始播放
    onVideoCanPlay() {
      this.videoLoading = false
      this.videoLoadProgress = 100
      this.showVideoInfo = false
    },

    // 视频加载进度
    onVideoProgress() {
      if (this.$refs.videoPlayer) {
        const video = this.$refs.videoPlayer
        if (video.buffered.length > 0) {
          const bufferedEnd = video.buffered.end(video.buffered.length - 1)
          const duration = video.duration
          if (duration > 0) {
            this.videoLoadProgress = Math.min(90, (bufferedEnd / duration) * 100)
          }
        }
      }
    },

    // 视频加载错误
    onVideoError(error) {
      this.videoLoading = false
      this.$message.error('视频加载失败，请尝试下载后播放')
      console.error('Video load error:', error)
    },

    // 强制播放视频
    forcePlayVideo() {
      this.showVideoInfo = false
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.load()
      }
    },

    // 使用外部播放器打开
    openExternalPlayer(url) {
      // 尝试使用系统默认播放器打开
      const link = document.createElement('a')
      link.href = url
      link.target = '_blank'
      link.rel = 'noopener noreferrer'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.info('已在新窗口打开，请使用浏览器或系统播放器播放')
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 下载文件
    downloadFile(file) {
      if (file.url) {
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },

    // 手动上传
    submit() {
      this.$refs.upload.submit()
    },

    // 清空文件列表
    clearFiles() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.updateValue()
    },

    // 获取当前文件列表
    getFileList() {
      return this.fileList
    }
  }
}
</script>

<style scoped lang="scss">
.dw-upload-file {
  .el-upload__tip {
    color: #606266;
    font-size: 12px;
    margin-top: 7px;
  }

  // 文件列表样式优化
  :deep(.el-upload-list) {
    .el-upload-list__item {
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    .el-upload-list__item-name {
      color: #606266;

      &:hover {
        color: #409eff;
      }
    }
  }

  // 拖拽上传样式
  :deep(.el-upload-dragger) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
    }

    .el-icon-upload {
      font-size: 67px;
      color: #c0c4cc;
      margin: 40px 0 16px;
      line-height: 50px;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;
      text-align: center;

      em {
        color: #409eff;
        font-style: normal;
      }
    }
  }

  // 图片卡片样式
  :deep(.el-upload-list--picture-card) {
    .el-upload-list__item {
      border-radius: 6px;
      overflow: hidden;
    }
  }

  // 预览对话框样式
  .preview-content {
    text-align: center;

    .video-preview {
      .video-container {
        position: relative;
        margin-bottom: 20px;

        .video-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(255, 255, 255, 0.9);
          padding: 20px;
          border-radius: 6px;
          text-align: center;
          min-width: 200px;

          p {
            margin: 10px 0 0 0;
            color: #606266;
            font-size: 14px;
          }
        }

        .video-info-tip {
          margin-top: 15px;

          .el-alert {
            text-align: left;
          }
        }
      }

      .file-actions {
        padding: 20px 0;

        p {
          margin: 8px 0;
          color: #606266;
          font-size: 14px;
        }

        .action-buttons {
          margin-top: 16px;

          .el-button {
            margin-right: 10px;
          }
        }
      }
    }

    .file-info {
      padding: 20px;

      .el-icon-document {
        font-size: 48px;
        color: #909399;
        margin-bottom: 16px;
      }

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }

      .el-button {
        margin-top: 16px;
      }
    }
  }

  // 禁用状态样式
  &.is-disabled {
    :deep(.el-upload) {
      // 隐藏上传区域，但保留文件列表
      .el-upload__input {
        display: none !important;
      }

      .el-upload-dragger {
        display: none !important;
      }
    }

    :deep(.el-upload-list) {
      margin-top: 0 !important; // 移除文件列表的上边距

      .el-upload-list__item {
        .el-upload-list__item-delete {
          display: none !important; // 隐藏删除按钮
        }

        // 禁用状态下的文件项样式
        cursor: default;

        &:hover {
          background-color: transparent;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dw-upload-file {
    :deep(.el-dialog) {
      width: 90% !important;
    }
  }
}
</style>