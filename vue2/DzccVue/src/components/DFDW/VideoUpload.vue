<template>
  <div class="component-upload-image">
    <el-upload
        :disabled="disabled"
        :action="baseUrl + uploadUrl"
        :accept="'.'+fileType.join(',.')"
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        name="file"
        :on-remove="handleRemove"
        :show-file-list="true"
        :headers="headers"
        :file-list="fileList"
        :on-preview="handlePictureCardPreview"
        :class="{hide: this.fileList.length >= this.limit}"
        :data="data"
    >
      <el-button size="mini" type="primary" :disabled="disabled">点击上传</el-button>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b></template>
      的文件
    </div>

    <el-dialog
        :visible.sync="dialogVisible"
        title="预览"
        width="800"
        append-to-body
        destroy-on-close
    >
      <video
          height="600"
          :src="dialogImageUrl"
          controls="controls"
          style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script>
import {getToken} from '@/utils/auth'
import Cookies from 'js-cookie'

export default {
  props: {
    value: [String, Object, Array],
    // 图片数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 20
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['mp4', 'avi', 'rmvb']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    uploadUrl: {
      type: String,
      default: '/common/upload'
    },
    type: {
      type: Number,
      default: 0
    },
    data: {
      type: Object,
      default: () => {
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      hideUpload: false,
      baseUrl: process.env.VUE_APP_BASE_API + '/dwDzcc',
      appUrl: '/app',
      adminToken: Cookies.get('Admin-Token'),
      headers: {'Authorization': getToken()},
      fileList: []
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组
          let list;
          if (Array.isArray(val)) {
            list = val
          } else if (typeof val === 'string') {
            list = val.split(',')
          } else if (typeof val === 'object') {
            list = [val]
          }
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === 'string') {
              if (item.indexOf(this.baseUrl) === -1) {
                item = {
                  name: item,
                  url: this.baseUrl + '/dzcc/file/download?header=1&adminToken=' + this.adminToken + '&filePath=' + item,
                  filepath: item,
                  status: 'success'
                }
              } else {
                item = {
                  name: item,
                  url: '/dzcc/file/download?header=1&adminToken=' + this.adminToken + '&filePath=' + item,
                  filepath: item,
                  status: 'success'
                }
              }
            } else if (typeof item === 'object') {
              if (item.filepath.indexOf(this.baseUrl) === -1) {
                item.name = item.filename;
                item.url = this.baseUrl + '/dzcc/file/download?header=1&adminToken=' + this.adminToken + '&filePath=' + item;
                item.status = 'success';
              } else {
                item.name = item.filename;
                item.url = item.filepath;
                item.status = 'success';
              }
            }
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  methods: {
    // 删除图片
    handleRemove(file, fileList) {
      this.fileList = fileList;
      let filepath = this.fileList.length > 0 ? this.fileList.map(item => item.url).join(',') : null
      this.$emit('input', filepath)
      this.$emit('deleteData', file)
    },
    // 上传成功回调
    handleUploadSuccess(res) {
      // 返回的参数是对象
      const isObj = res.result instanceof Object
      if (isObj) {
        this.fileList.push({
          name: res.result.filename,
          url: res.result.filepath,
          id: res.result.id,
          type: this.type,
          filepath: res.result.filepath
        })
      } else {
        this.fileList.push({
          name: res.result,
          url: res.result,
          filepath: res.result
        })
      }
      this.$emit('uploadSuccessData', this.fileList.filter(item => item.status === 'success'))
      this.$emit('uploadErrorData', this.fileList.filter(item => item.status !== 'success'))
      this.$emit('input', this.fileList.filter(item => item.status === 'success').map(item => item.url).join(','))
      this.loading.close()
    },
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          return !!(fileExtension && fileExtension.indexOf(type) > -1)
        })
      } else {
        isImg = file.type.indexOf('image') > -1
      }

      if (!isImg) {
        this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join('/')}视频格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传视频大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.loading = this.$loading({
        lock: true,
        text: '上传中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError(err, file, fileList) {
      this.fileList = fileList;
      let isOk = true;
      this.fileList.forEach((item) => {
        if (item.status !== 'success' && item.status !== 'fail') {
          isOk = false;
        }
      })
      if (isOk) {
        this.$emit('uploadSuccessData', this.fileList.filter(item => item.status === 'success'))
        this.$emit('uploadErrorData', this.fileList.filter(item => item.status !== 'success'))
        this.$emit('input', this.fileList.filter(item => item.status === 'success').map(item => item.url).join(','))
      }
      this.loading.close()
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
  display: none;
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter, .el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}
</style>
