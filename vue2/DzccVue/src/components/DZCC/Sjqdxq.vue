<template>
  <div>
    <div style="margin-bottom: 10px">
      <span class="font-size14">周期：</span>
      <el-select v-model="queryParams.monthlyCycle" placeholder="请选择部门" @change="handleChange">
        <el-option
            v-for="item in monthlyCycleList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        >
        </el-option>
      </el-select>
      <el-button type="primary" icon="el-icon-download" @click="downJsyqdByIds">下载pdf</el-button>
    </div>
    <el-tabs type="border-card" v-model="activeKey">
      <el-tab-pane label="司机详情" name="first" style="height: 500px;" v-watermark="{label: watermark}">
        <el-form
            ref="driverParams"
            :model="driverParams"
            :inline="true"
            label-width="auto"
        >
          <el-divider content-position="left">基本信息</el-divider>
          <el-row>
            <el-col :span="8">
              <el-form-item label="周期" class="force-width-60">{{driverInfo.week}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="驾驶员" class="force-width-60">{{driverInfo.driverName}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车牌" class="force-width-60">{{driverInfo.licencePlate}}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="总延时" class="force-width-60">{{driverInfo.overtimeHoursAll}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="初始延时" class="force-width-60">{{driverInfo.overtimeHours}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审核延时" class="force-width-60">{{driverInfo.overtimeHoursApp}}</el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="left">出车详情</el-divider>
          <el-row >
            <el-table
                :data="timeList"
                style="width: 100%">
              <el-table-column
                  prop="week"
                  label="出车星期"
                  width="180"
                  align="center">
              </el-table-column>
              <el-table-column
                  prop="moveDate"
                  label="出车日期"
                  width="180"
                  align="center">
              </el-table-column>
              <el-table-column
                  label="出车地点"
                  align="center">
                <template slot-scope="scope">
                  <div v-html="formatAreaText(scope.row.concatenatedAreaText)"></div>
                </template>
              </el-table-column>
              <el-table-column
                  prop="overtimeHoursAll"
                  label="延时"
                  width="180"
                  align="center">
              </el-table-column>
            </el-table>
          </el-row>
        </el-form>
      </el-tab-pane>

    </el-tabs>
  </div>
</template>

<script>
import {downJsyqdByIds, getSjqdx} from '@/api/dzcc/jtsj/jtsjqd'
import {downLoad, getDate, numberToChinese} from "@/utils/tool";
import {getWeeks} from "api/dzcc/jtsj/yssqlb";

export default {
  name: 'Sjqdxq',
  mounted() {
    this.queryParams.monthlyCycle = this.driverParams.week
    this.getWeeks()
    this.getSjqdx()
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  props: {
    driverParams: {
      type: Object,
      required: true
    },
    startDate: {
      type: String,
      required: true
    },
    endDate: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      //司机信息
      driverInfo:{},
      //出车详情
      timeList:[],
      timeState:0,
      activeKey: 'first',
      queryParams:{
        driverName:"",
        monthlyCycle: 1
      },
      // 周期列表
      monthlyCycleList: []
    }
  },
  methods: {
    formatAreaText(value) {
      // 将字符串中的逗号替换为换行 <br/>
      return value.replace(/,/g, '<br/>');
    },
    getSjqdx() {
      this.queryParams.driverId = this.driverParams.driveId
      this.queryParams.monthlyCycles = [this.queryParams.monthlyCycle]
      this.queryParams.startDate = this.startDate
      this.queryParams.endDate = this.endDate
      getSjqdx( this.queryParams).then((res) => {
        this.driverInfo = res.result;
        this.timeList = res.result.jtsjDtos
      })
    },
    handleChange() {
      this.getSjqdx()
    },
    downJsyqdByIds() {
      const params = {
        ids: [this.driverParams.id],
        driverIds: [this.queryParams.driverId],
        monthlyCycles: [this.queryParams.monthlyCycle],
        downloadType: 'pdf',
        startDate: this.queryParams.startDate,
        endDate: this.queryParams.endDate
      }
      downJsyqdByIds(params).then((res) => {
        const month = this.monthlyCycleList.filter(item => item.value === this.queryParams.monthlyCycle)[0].label;
        const fileName = this.driverInfo.driverName + getDate(this.queryParams.startDate, 'YYYY年MM月') + month + '.pdf'
        downLoad(res, fileName)
      })
    },
    /** 获取周期列表 */
    getWeeks() {
      getWeeks({year: this.startDate.substring(0, 4), month: this.startDate.substring(5, 7), selectAll: true}).then(res => {
        this.monthlyCycleList = res.result.map(item => {
          return {label: '第' + numberToChinese(item.week) + '周', value: item.week}
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.text {
  font-size: 14px;
}

.box-card {
  width: 480px;
}
</style>
