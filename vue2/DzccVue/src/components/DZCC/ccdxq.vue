<template>
  <div>
    <div style="margin-bottom: 10px" v-if="downLoadShow">
      <el-button type="primary" icon="el-icon-download" @click="downCcdXq">下载pdf</el-button>
    </div>
    <el-tabs type="border-card" v-model="activeKey">
      <el-tab-pane label="出车单详情" name="first" style="height: 500px;" v-watermark="{label: watermark}">
        <el-form
            ref="ccdParams"
            :model="ccdParams"
            :inline="true"
            label-width="auto"
        >
          <el-divider content-position="left">基本信息</el-divider>
          <el-row>
            <el-col :span="8">
              <el-form-item label="出车单编号" class="force-width-60">{{ ccdParams.applyNo }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用车人" class="force-width-60">{{ ccdParams.ycrName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人" class="force-width-60">{{ ccdParams.applyUserName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请单位" class="force-width-60">{{ ccdParams.applyTopDeptName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人电话" class="force-width-60">{{ ccdParams.applyPhone }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出车时间" class="force-width-60">
                <div style="display: flex">{{ showTime(ccdParams.ccOpenTime) }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结束时间" class="force-width-60">{{ showTime(ccdParams.executeTime) }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="人数" class="force-width-60">{{ ccdParams.applyNum }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="上车点" class="force-width-60">{{ ccdParams.scdText }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出行事由" class="force-width-60">{{ ccdParams.note }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审核状态" class="force-width-60">
                <div v-if="ccdParams.approveState == 0">
                  未提交
                </div>
                <div v-if="ccdParams.approveState == 1">
                  审批中
                </div>
                <div v-if="ccdParams.approveState == 2">
                  已审批
                </div>
                <div v-if="ccdParams.approveState == 3">
                  已驳回
                </div>
                <div v-if="ccdParams.approveState == 4">
                  流程终止
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="执行状态" class="force-width-60">
                <div v-if="ccdParams.executeState == 0">
                  未派车
                </div>
                <div v-if="ccdParams.executeState == 1">
                  已派车
                </div>
                <div v-if="ccdParams.executeState == 2">
                  已到达
                </div>
                <div v-if="ccdParams.executeState == 3">
                  派车撤回
                </div>
                <div v-if="ccdParams.executeState == 4">
                  已取消
                </div>
                <div v-if="ccdParams.executeState == 5">
                  车辆异常
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-divider content-position="left">目的地</el-divider>
              <el-card class="box-card">
                <el-table
                    :data="addressList"
                    style="width: 100%">
                  <el-table-column
                      type="index"
                      width="50">
                  </el-table-column>
                  <el-table-column
                      prop="areaText"
                      label="市区"
                      width="100">
                  </el-table-column>
                  <el-table-column
                      prop="addressInfo"
                      label="地址信息">
                  </el-table-column>
                  <el-table-column
                      prop="arriveState"
                      label="是否到达"
                      width="80">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.arriveState == 1" type="success" disable-transitions>是</el-tag>
                      <el-tag v-else type="danger" disable-transitions>否</el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-divider content-position="left">派车信息</el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="车辆类型" class="force-width-60">{{ ccdParams.carMold }}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="车牌" class="force-width-60">{{ ccdParams.licensePlate }}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <!--                  <el-form-item label="驾驶员(代班)" class="force-width-60" v-if="ccdParams.isAvial">{{ ccdParams.availName  }}</el-form-item>-->
                  <!--                  <el-form-item label="驾驶员" class="force-width-60" v-else>{{ ccdParams.driverName }}</el-form-item>-->
                  <el-form-item label="驾驶员" class="force-width-60">{{ ccdParams.driverName }}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <!--                  <el-form-item label="驾驶员手机号(代班)" class="force-width-60" v-if="ccdParams.isAvial">{{ ccdParams.availPhone  }}</el-form-item>-->
                  <!--                  <el-form-item label="驾驶员手机号" class="force-width-60" v-else>{{ ccdParams.driverPhone }}</el-form-item>-->
                  <el-form-item label="手机号" class="force-width-60">{{ ccdParams.driverPhone }}</el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="备注" class="force-width-60">
                    <div style="width: 100%">{{ ccdParams.remark }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-divider content-position="left" v-if="timeState==1">每日出车时间</el-divider>
          <el-row v-if="timeState==1">
            <el-table
                :data="timeList"
                style="width: 100%">
              <el-table-column
                  prop="startTime"
                  label="开始时间"
                  :formatter="formatDate"
                  width="180">
              </el-table-column>
              <el-table-column
                  prop="endTime"
                  label="结束时间"
                  :formatter="formatDate"
                  width="180">
              </el-table-column>

            </el-table>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="出车单流程" name="second" style="height: 500px;overflow: auto" v-watermark="{label: watermark}" v-if="workFlowShow">
        <div v-for="(list, index) in lcList" :key="index">
          <el-alert
              title="主流程"
              type="success"
              :closable="false"
              v-if="index == ccdParams.id">
          </el-alert>
          <el-alert
              title="流程补充"
              type="warning"
              :closable="false"
              v-else>
          </el-alert>
          <el-timeline>
            <el-timeline-item v-for="(value) in list" :key="value.lc_jdID+value.lc_jdmc" :timestamp="value.startdate"
                              placement="top">
              <el-card>
                <h4>{{ value.lc_jdmc }}</h4>
                <p>{{ value.personName }} 提交于 {{ value.transdate }}</p>
                <p>审批意见：{{ value.feed }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>
      <el-tab-pane label="评价" name="third" style="height: 500px;overflow: auto" v-watermark="{label: watermark}" v-if="evaluateShow">
        <el-form
            ref="pjParams"
            :model="pjParams"
        >
          <el-row>
            <el-col :span="8">
              <el-form-item label="星级评价" class="force-width-60">
                <el-rate
                    v-model="pjParams.rate"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value}星">
                </el-rate>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row style="pointer-events: none;">
            <el-col :span="6">
              <el-form-item label="车内是否整洁" class="force-width-60">
                <el-checkbox v-model="pjParams.zjState" label=""></el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否安全驾驶" class="force-width-60">
                <el-checkbox v-model="pjParams.aqjsState" label=""></el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否准时到达" class="force-width-60">
                <el-checkbox v-model="pjParams.pdkzState" label=""></el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="服务是否满意" class="force-width-60">
                <el-checkbox v-model="pjParams.tsState" label=""></el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {GetAddressListById, GetLcListById, getCcdTimeByLcId, DownCcdXqById} from '@/api/dzcc/ccd/ccd'
import {downLoad} from "@/utils/tool";
import {getByLcId} from "api/dzcc/ccd/pj";

export default {
  name: 'ccdxq',
  mounted() {
    this.detectZoom()
    if (this.ccdParams !== undefined) {
      GetAddressListById({id: this.ccdParams.id}).then((res) => {
        this.addressList = res.result
      })
      GetLcListById({id: this.ccdParams.id}).then((res) => {
        let response = res
        if (typeof res == 'string') {
          response = eval("(" + res + ")")
        }
        this.lcList = response.result
      })
      getCcdTimeByLcId({id: this.ccdParams.id}).then((res) => {
        if (res.result != null && res.result != undefined && res.result.length > 0) {
          this.timeState = 1;
          this.timeList = res.result
        } else {
          this.timeState = 0;
        }
      })
      getByLcId(this.ccdParams.id).then((res) => {
        this.pjParams = res.result;
        if (this.pjParams.id != null) {
          this.pjParams.zjState = res.result.zjState === 1;
          this.pjParams.aqjsState = res.result.aqjsState === 1;
          this.pjParams.pdkzState = res.result.pdkzState === 1;
          this.pjParams.tsState = res.result.tsState === 1;
        }
      })
    }
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  props: {
    ccdParams: {
      type: Object,
      default: undefined
    },
    downLoadShow: {
      type: Boolean,
      default: true
    },
    workFlowShow: {
      type: Boolean,
      default: true
    },
    evaluateShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 目的地列表
      addressList: [],
      // 流程列表
      lcList: [],
      //每日出行时间
      timeList: [],
      timeState: 0,
      fontApplyNo: '',
      fontTitle: '',
      fontSize: '',
      activeKey: 'first',
      pjParams: {
        id: null,
        rate: 0,
        zjState: false,
        aqjsState: false,
        pdkzState: false,
        tsState: false
      },
    }
  },
  methods: {
    /** 出车单详情导出 */
    downCcdXq() {
      const params = {
        content: this.ccdParams.id,
        person: this.$store.getters.userid,
        type: 'pdf'
      }
      DownCcdXqById(params).then((res) => {
        const fileName = '出车单详情' + this.ccdParams.applyNo + '.pdf'
        downLoad(res, fileName)
      })
    },
    /** 展示时间 */
    showTime(date) {
      return this.$dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    },
    //方法区
    formatDate(row, column) {
      // 获取单元格数据
      let data = row[column.property]
      if (data == null) {
        return null
      }
      let dt = new Date(data)
      let month = this.replenish((dt.getMonth() + 1));

      return dt.getFullYear() + '-' + month + '-' + this.replenish(dt.getDate()) + ' ' + this.replenish(dt.getHours()) + ':' + this.replenish(dt.getMinutes()) + ':' + this.replenish(dt.getSeconds())
    },
    replenish(val) {
      return (val < 10 ? ('0' + val) : val)
    },
    detectZoom() {
      var ratio = 0,
          screen = window.screen,
          ua = navigator.userAgent.toLowerCase();

      if (window.devicePixelRatio !== undefined) {
        ratio = window.devicePixelRatio;
      } else if (~ua.indexOf('msie')) {
        if (screen.deviceXDPI && screen.logicalXDPI) {
          ratio = screen.deviceXDPI / screen.logicalXDPI;
        }
      } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
        ratio = window.outerWidth / window.innerWidth;
      }
      if (ratio) {
        ratio = Math.round(ratio * 100);
      }
      this.fontSize = Math.round(36 * (100 / ratio)) + 'px';
      this.fontTitle = Math.round(80 * (100 / ratio)) + 'px';
      this.fontApplyNo = Math.round(50 * (100 / ratio)) + 'px';
    }
  }
}
</script>

<style lang="less" scoped>
.text {
  font-size: 14px;
  display: flex;

  .areaText {
    width: 120px;
  }

  .addressInfo {
    width: 300px;
    margin-left: 10px;
  }
}

.box-card {
  width: 480px;
}
</style>
