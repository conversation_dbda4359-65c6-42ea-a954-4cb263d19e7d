import request from '@/utils/request'

/** 获取数据 */
export function GetClxxList (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/GetClxxList',
        method: 'post',
        data: params
    })
}

/** 获取车队类型下拉框 */
export function GetMoldDict (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/GetMoldDict',
        method: 'post',
        data: params
    })
}

/** 添加车队长管理 */
export function AddClxx (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/AddClxx',
        method: 'post',
        data: params
    })
}

/** 编辑车队长管理 */
export function EditClxx (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/EditClxx',
        method: 'post',
        data: params
    })
}

/** 根据id删除车队长 */
export function DeleteClxxById (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/DeleteClxxById',
        method: 'get',
        params: params
    })
}

/** 导出 */
export function DownLoad (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/DownLoad',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

/** 获取数据 */
export function GetCarMoveList (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/GetCarMoveList',
        method: 'post',
        data: params
    })
}



/** 获取数据 */
export function GetCarMoveChildList (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/GetCarMoveChildList',
        method: 'post',
        data: params
    })
}


/** 添加车队长管理 */
export function AddCarMove (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/AddCarMove',
        method: 'post',
        data: params
    })
}

/** 编辑车队长管理 */
export function EditCarMove (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/EditCarMove',
        method: 'post',
        data: params
    })
}

/** 根据id删除车队长 */
export function DeleteCarMoveById (params) {
    return request({
        url: '/dwDzcc/dzcc/clxx/DeleteCarMoveById',
        method: 'get',
        params: params
    })
}
