import request from '@/utils/request'

// 创建电子出车单-评价
export function createDzccTPj(data) {
  return request({
    url: '/dwDzcc/dzcc/pj/create',
    method: 'post',
    data: data
  })
}

// 更新电子出车单-评价
export function updateDzccTPj(data) {
  return request({
    url: '/dwDzcc/dzcc/pj/update',
    method: 'put',
    data: data
  })
}

// 删除电子出车单-评价
export function deleteDzccTPj(ids) {
  return request({
    url: '/dwDzcc/dzcc/pj/delete?ids=' + ids,
    method: 'delete'
  })
}

// 获得电子出车单-评价
export function getDzccTPj(id) {
  return request({
    url: '/dwDzcc/dzcc/pj/get?id=' + id,
    method: 'get'
  })
}

// 获得电子出车单-评价分页
export function getDzccTPjPage(data) {
  return request({
    url: '/dwDzcc/dzcc/pj/page',
    method: 'post',
    data: data
  })
}

// 导出电子出车单-评价 Excel
export function exportDzccTPjExcel(data) {
  return request({
    url: '/dwDzcc/dzcc/pj/export-excel',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

// 根据lcId获得电子出车单-评价
export function getByLcId(lcId) {
  return request({
    url: '/dwDzcc/dzcc/pj/getByLcId?lcId=' + lcId,
    method: 'get'
  })
}
