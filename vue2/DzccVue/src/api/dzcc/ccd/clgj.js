import request from '@/utils/request'

/** 出车单查询 */
export function getLocusList (params) {
  return request({
    url: '/dwDzcc/dzcc/locus/getLocusList',
    method: 'post',
    data: params
  })
}

/** 出车单查询 */
export function checkCarPerson (params) {
  return request({
    url: '/dwDzcc/dzcc/locus/checkCarPerson',
    method: 'post',
    data: params
  })
}


/** 导出 */
export function DownLoad (params) {
  return request({
    url: '/dwDzcc/dzcc/locus/DownLoad',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
