import request from '@/utils/request'

/** 车辆移动查询 */
export function GetClydList (params) {
  return request({
    url: '/dwDzcc/dzcc/clyd/GetClydList',
    method: 'post',
    data: params
  })
}

/** 导出 */
export function DownLoad (params) {
  return request({
    url: '/dwDzcc/dzcc/clyd/DownLoad',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/** 车辆移动信息 */
export function CarMove (params) {
  return request({
    url: '/dwDzcc/dzcc/clyd/carMove',
    method: 'post',
    data: params
  })
}