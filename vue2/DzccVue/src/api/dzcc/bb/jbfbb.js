import request from "@/utils/request";

/** 主报表查询 */
export function GetZbbList (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/GetZbbList',
        method: 'post',
        data: params
    })
}


/** 主报表导出 */
export function DownLoadZbb (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/DownLoadZbb',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}



/** 获取二级报表 */
export function GetCCLCDetailList (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/GetCCLCDetailList',
        method: 'post',
        data: params
    })
}


/** 二级报表导出 */
export function DownLoadSbb (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/DownLoadlevel2',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}




/** 获取三级报表 */
export function GetCcdByDriveIdAndTime (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/getCcdByDriveIdAndTime',
        method: 'post',
        data: params
    })
}

/** 三级报表导出 */
export function DownLoadTbb (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/DownLoadlevel3',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

/** 获取每天出车单报表 */
export function GetCcdStatusDaily (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/getCcdStatusDaily',
        method: 'post',
        data: params
    })
}

/** 每天出车单报表导出 */
export function DownCcdStatusDaily (params) {
    return request({
        url: '/dwDzcc/dzcc/jbfbb/downCcdStatusDaily',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}