import request from '@/utils/request'

/** 获取单位下拉框 */
export function GetGroupList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetGroupList',
    method: 'get',
    params: params
  })
}

/** 主报表查询 */
export function GetZbbList (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/GetZbbList',
    method: 'post',
    data: params
  })
}

/** 主报表月份解锁 */
export function LockMonth (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/LockMonth',
    method: 'post',
    data: params
  })
}

/** 主报表月份解锁 */
export function UnLockMonth (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/UnLockMonth',
    method: 'post',
    data: params
  })
}

/** 查看主报表月份锁定 */
export function ShowLockMonth (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/ShowLockMonth',
    method: 'post',
    data: params
  })
}

/** 次报表查询 */
export function GetSbbList (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/GetSbbList',
    method: 'post',
    data: params
  })
}

/** 三报表查询 */
export function GettbbList (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/GettbbList',
    method: 'post',
    data: params
  })
}

/** 主报表导出 */
export function DownLoadZbb (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/DownLoadZbb',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/** 次报表导出 */
export function DownLoadSbb (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/DownLoadSbb',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/** 三报表导出 */
export function DownLoadTbb (params) {
  return request({
    url: '/dwDzcc/dzcc/bb/DownLoadTbb',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/** 次报表查询 */
export function GetCCLCDetailList (type,id,year,month) {
  return request({
    url: '/dwDzcc/dzcc/bb/GetCCLCDetailList?selectType='+type+'&driveId='+id+'&year='+year+'&month='+month,
    method: 'get',
  })
}
