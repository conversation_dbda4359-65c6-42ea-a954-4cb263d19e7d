import request from '@/utils/request'


/** 获取加班费规则数据 */
export function getListPage(params) {
  return request({
    url: '/dwDzcc/dzcc/overTime/getListPage',
    method: 'post',
    data: params
  })
}

/**
 * 加班费规则新增
 * @param params
 */
export function createOverTime(params) {
  return request({
    url: '/dwDzcc/dzcc/overTime/create',
    method: 'post',
    data: params
  })
}

/**
 * 加班费规则修改
 * @param params
 */
export function updateOverTime(params) {
  return request({
    url: '/dwDzcc/dzcc/overTime/update',
    method: 'put',
    data: params
  })
}

/**
 * 加班费规则删除
 * @param id
 */
export function deleteOverTime (id) {
  return request({
    url: '/dwDzcc/dzcc/overTime/delete?id=' + id,
    method: 'delete',
  })
}

/**
 * 根据类型获取字典值
 * @param id
 */
export function getDictDataByType (type) {
  return request({
    url: '/dwDzcc/dfdw/dictData/dicDataByTypeAll?type=' + type,
    method: 'get',
  })
}



