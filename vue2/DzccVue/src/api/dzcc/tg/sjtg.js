import request from '@/utils/request'

/** 通告查询 */
export function GetNoticeList (params) {
  return request({
    url: '/dwDzcc/dzcc/notice/GetNoticeList',
    method: 'post',
    data: params
  })
}

/** 新增司机通告 */
export function addSjtg (params) {
  return request({
    url: '/dwDzcc/dzcc/notice/addSjtg',
    method: 'post',
    data: params
  })
}

/**
 * 获取图片路径
 * @param fileId 图片id
 */
export function getImage (fileId) {
  return request({
    url: `/dwDzcc/dzcc/notice/image?fileId=${fileId}`,
    method: 'get'
  })
}

/** 修改司机通告 */
export function editSjtg (params) {
  return request({
    url: '/dwDzcc/dzcc/notice/editSjtg',
    method: 'post',
    data: params
  })
}

/** 删除司机通告 */
export function deleteSjtg (noticeId) {
  return request({
    url: `/dwDzcc/dzcc/notice/deleteSjtg?noticeId=${noticeId}`,
    method: 'get'
  })
}

/** 文件限制 */
export function fileLimit (params) {
  return request({
    url: '/dwDzcc/dzcc/notice/fileLimit',
    method: 'post',
    data: params
  })
}
