import request from '@/utils/request'

/** 获取单位下拉框 */
export function GetGroupList (params) {
  return request({
    url: '/dwDzcc/dzcc/ryqx/GetGroupList',
    method: 'get',
    params: params
  })
}

/** 人员查询 */
export function GetPersonList (params) {
  return request({
    url: '/dwDzcc/dzcc/ryqx/GetPersonList',
    method: 'post',
    data: params
  })
}

/** 根据id查询角色 */
export function GetPersonRoleById (params) {
  return request({
    url: '/dwDzcc/dzcc/ryqx/GetPersonRoleById',
    method: 'post',
    data: params
  })
}

/** 角色查询 */
export function GetRoles () {
  return request({
    url: '/dwDzcc/dzcc/ryqx/GetRoles',
    method: 'post'
  })
}

/** 编辑权限 */
export function EditRoles (params) {
  return request({
    url: '/dwDzcc/dzcc/ryqx/EditRoles',
    method: 'post',
    data: params
  })
}
