import request from '@/utils/request'
/*****延时申请列表 */
/** 列表 */
export function getPageList(params) {
    return request({
        url: '/dwDzcc/dzcc/t_week/list',
        method: 'post',
        data: params
    })
}
export function getWorkSummaryList(params) {
    return request({
        url: '/dwDzcc/dzcc/t_week/getWorkSummaryList',
        method: 'post',
        data: params
    })
}
/** 获取周期列表 */
export function getWeeks(params) {
    return request({
        url: '/dwDzcc/dzcc/t_week/getWeeks',
        method: 'post',
        data: params
    })
}
export function exportWorkSummary(params) {
    return request({
        url: '/dwDzcc/dzcc/t_week/exportWorkSummary',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

/** 编辑 */
export function editAppendDelayHour(params) {
    return request({
        url: '/dwDzcc/dzcc/t_week/editAppendDelayHour',
        method: 'post',
        data: params
    })
}
/** 重新生成 */
export function syncDriver(params) {
    return request({
        url: '/dwDzcc/dzcc/t_week/syncDriver',
        method: 'post',
        data: params
    })
}
/** 获取详情 */
export function getDetail(id) {
    return request({
        url: '/dwDzcc/dzcc/t_week/getInfo/' + id,
        method: 'get',
    })
}


export function getDriveList(params){
    return request({
        url: '/dwDzcc/dzcc/t_week/getDriveList',
        method: 'post',
        data: params
    })
}

// 审批完业务回调
export function submitLc(params){
    return request({
        url: '/dwDzcc/dzcc/dzccFlow/submitLc',
        method: 'post',
        data: params
    })
}

// 审批完业务回调
export function rollBackForApply(params){
    return request({
        url: '/dwDzcc/dzcc/dzccFlow/rollBackForApply',
        method: 'post',
        data: params
    })
}

// 提交前置校验
export function checkSubmit(params){
    return request({
        url: '/dwDzcc/dzcc/dzccFlow/checkSubmit',
        method: 'post',
        data: params
    })
}