import request from '@/utils/request'

/** 出车单查询 */
export function GetCcdList (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/getCCDPageList',
    method: 'post',
    data: params
  })
}

/** 添加目的地 */
export function addCcdDestination (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/addCcd',
    method: 'post',
    data: params
  })
}



/** 目的地查询 */
export function GetAddressListById (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/GetAddressListById',
    method: 'post',
    data: params
  })
}

/** 流程查询 */
export function GetLcListById (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/GetLcListById',
    method: 'post',
    data: params
  })
}

/** 每日出车时间 */
export function getCcdTimeByLcId (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/getCcdTimeByLcId',
    method: 'post',
    data: params
  })
}

/** 出车单导出 */
export function DownLoadCcd (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/DownLoadCcd',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

/** 出车单详情导出 */
export function DownCcdXqById (params) {
  return request({
    url: '/dwDzcc/dzcc/ccd/DownCcdXqById',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
