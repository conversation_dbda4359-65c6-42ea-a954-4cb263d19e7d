import request from '@/utils/request'

/** 获取司机列表 */
export function GetDriverList(params) {
    return request({
        url: '/dwDzcc/dzcc/jtsj/GetDriverList',
        method: 'post',
        data: params
    })
}
/** 获取司机详情 */
export function getSjqdx(params) {
    return request({
        url: '/dwDzcc/dzcc/jtsj/getSjqdx',
        method: 'post',
        data: params
    })
}
/** 驾驶员工作量清单导出 */
export function downJsyqdByIds (params) {
    return request({
        url: '/dwDzcc/dzcc/jtsj/downJsyqdByIds',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}