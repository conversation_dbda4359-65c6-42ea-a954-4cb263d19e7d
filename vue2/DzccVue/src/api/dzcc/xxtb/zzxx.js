import request from '@/utils/request'

/** 部门查询 */
export function GetGroupList (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/GetGroupList',
        method: 'post',
        data: params
    })
}

/** 司机信息查询 */
export function GetList (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/GetList',
        method: 'post',
        data: params
    })
}

/** 字典查询 */
export function GetDict () {
    return request({
        url: '/dwDzcc/dzcc/employInfo/GetDict',
        method: 'get'
    })
}

/** 修改信息 */
export function EditInfo (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/EditInfo',
        method: 'post',
        data: params
    })
}

/** 删除信息 */
export function DeleteInfo (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/DeleteInfo',
        method: 'post',
        data: params
    })
}

/** 聘用信息 */
export function GetEmployList (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/GetEmployList',
        method: 'post',
        data: params
    })
}

/** 新增聘用信息 */
export function AddEmployPerson (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/AddEmployPerson',
        method: 'post',
        data: params
    })
}

/** 修改聘用信息 */
export function EditEmployPerson (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/EditEmployPerson',
        method: 'post',
        data: params
    })
}

/** 删除聘用信息 */
export function DeleteEmployPerson (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/DeleteEmployPerson',
        method: 'post',
        data: params
    })
}

/** 主报表导出 */
export function DownLoadZzxx (params) {
  return request({
    url: '/dwDzcc/dzcc/employInfo/DownLoadZzxx',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
