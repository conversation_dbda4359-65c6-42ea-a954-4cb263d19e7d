import request from '@/utils/request'

/** 驾驶员信息 */
export function GetJsyxxList (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/GetJsyxxList',
        method: 'post',
        data: params
    })
}

/** 新增驾驶员信息 */
export function AddJsyxx (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/AddJsyxx',
        method: 'post',
        data: params
    })
}

/** 修改驾驶员信息 */
export function EditJsyxx (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/EditJsyxx',
        method: 'post',
        data: params
    })
}

/** 删除驾驶员信息 */
export function DeleteJsyxx (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/DeleteJsyxx',
        method: 'post',
        data: params
    })
}

/** 驾驶员信息导出 */
export function DownLoadJsyxx (params) {
    return request({
        url: '/dwDzcc/dzcc/employInfo/DownLoadJsyxx',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}
