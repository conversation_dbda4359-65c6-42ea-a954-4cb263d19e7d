import request from '@/utils/request'

/** 获取车队长下拉框 */
export function GetBGDList (params) {
  return request({
    url: '/dwDzcc/dzcc/clbg/GetBGDList',
    method: 'post',
    data: params
  })
}
export function GetBgdListForBackHaul (params) {
  return request({
    url: '/dwDzcc/dzcc/clbg/GetBgdListForBackHaul',
    method: 'post',
    data: params
  })
}

/** 添加车队长管理 */
export function AddClbg (params) {
  return request({
    url: '/dwDzcc/dzcc/clbg/AddClbg',
    method: 'post',
    data: params
  })
}

/** 编辑车队长管理 */
export function EditClbg (params) {
  return request({
    url: '/dwDzcc/dzcc/clbg/EditClbg',
    method: 'post',
    data: params
  })
}

/** 根据id删除车队长 */
export function DeleteClbgById (params) {
  return request({
    url: '/dwDzcc/dzcc/clbg/DeleteClbgById',
    method: 'get',
    params: params
  })
}
