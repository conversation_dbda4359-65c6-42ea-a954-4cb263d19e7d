import request from '@/utils/request'

/** 获取单位下拉框 */
export function GetGroupList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetGroupList',
    method: 'get',
    params: params
  })
}


/** 获取单位下拉框 */
export function GetGroupList12 (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetGroupList12',
    method: 'get',
    params: params
  })
}


/** 获取车辆管理下拉框 */
export function GetCLGLList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetCLGLList',
    method: 'post',
    data: params
  })
}

/** 租赁时间查询 */
export function GetRentList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetRentList',
    method: 'post',
    data: params
  })
}

/** 车辆管理细节表查询 */
export function GetDetailList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetDetailList',
    method: 'post',
    data: params
  })
}

/** 驾驶员查询 */
export function GetDriverList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetDriverList',
    method: 'post',
    data: params
  })
}

/** 添加驾驶员 */
export function AddClglDetail (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/AddClglDetail',
    method: 'post',
    data: params
  })
}

/** 编辑驾驶员 */
export function EditClglDetail (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/EditClglDetail',
    method: 'post',
    data: params
  })
}

/** 根据id删除驾驶员 */
export function DeleteClglDetail (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/DeleteClglDetail',
    method: 'get',
    params: params
  })
}

/** 办公地点查询 */
export function GetUnitList (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/GetUnitList',
    method: 'post',
    data: params
  })
}

/** 修改办公地点 */
export function EditUnit (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/EditUnit',
    method: 'post',
    data: params
  })
}

/** 修改停车地点 */
export function EditStop (params) {
  return request({
    url: '/dwDzcc/dzcc/clgl/EditStop',
    method: 'post',
    data: params
  })
}
