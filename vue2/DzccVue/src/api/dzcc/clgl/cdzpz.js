import request from '@/utils/request'

/** 获取单位下拉框 */
export function GetGroupList (params) {
  return request({
    url: '/dwDzcc/dzcc/cdzpz/GetGroupList',
    method: 'get',
    params: params
  })
}

/** 车队长查询 */
export function GetCDZPZList (params) {
  return request({
    url: '/dwDzcc/dzcc/cdzpz/GetCDZPZList',
    method: 'post',
    data: params
  })
}

/** 得到部门车辆列表 */
export function GetCarList (params) {
  return request({
    url: '/dwDzcc/dzcc/cdzpz/GetCarList',
    method: 'post',
    data: params
  })
}

/** 编辑车队长配置 */
export function EditCDZPZ (params) {
  return request({
    url: '/dwDzcc/dzcc/cdzpz/EditCDZPZ',
    method: 'post',
    data: params
  })
}

/** 得到部门列表 */
export function GetCDZGroup (params) {
  return request({
    url: '/dwDzcc/dzcc/cdzpz/GetCDZGroup',
    method: 'post',
    data: params
  })
}

/** 得到部门分配车辆列表 */
export function GetCarListForCdz (params) {
  return request({
    url: '/dwDzcc/dzcc/cdzpz/GetCarListForCdz',
    method: 'post',
    data: params
  })
}
