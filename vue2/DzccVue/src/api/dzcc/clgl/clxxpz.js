import request from '@/utils/request'

/** 获取车队长下拉框 */
export function GetBackHaulList (params) {
    return request({
        url: '/dwDzcc/dzcc/clxxpz/GetBackHaulList',
        method: 'post',
        data: params
    })
}
export function GetCarList (params) {
    return request({
        url: '/dwDzcc/dzcc/clxxpz/getCarList?id='+params,
        method: 'get',
    })
}

/** 添加车队长管理 */
export function AddBackHaul (params) {
    return request({
        url: '/dwDzcc/dzcc/clxxpz/AddBackHaul',
        method: 'post',
        data: params
    })
}

/** 编辑车队长管理 */
export function EditBackHaul(params) {
    return request({
        url: '/dwDzcc/dzcc/clxxpz/EditBackHaul',
        method: 'post',
        data: params
    })
}

/** 根据id删除车队长 */
export function DeleteBackHaul (params) {
    return request({
        url: '/dwDzcc/dzcc/clxxpz/DeleteBackHaul',
        method: 'get',
        params: params
    })
}
