import request from '@/utils/request'

/** 车辆维修-PC-保修单-分页 */
export function bxdPage (params) {
    return request({
        url: '/dwDzcc/clwx/bxd/page',
        method: 'post',
        data: params
    })
}

/** 车辆维修-PC-保修单-根据id获取流程 */
export function lcListById (params) {
    return request({
        url: '/dwDzcc/clwx/bxd/lcListById',
        method: 'post',
        data: params
    })
}

/** 保修单详情导出 */
export function downWxdXqByIds (params) {
    return request({
        url: '/dwDzcc/clwx/bxd/downWxdXqByIds',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}


export function downloadFile (params) {
    return request({
        url: '/dwDzcc/clwx/bxd/downloadFile',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}