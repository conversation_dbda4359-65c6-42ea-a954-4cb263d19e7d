import request from '@/utils/request'

/** 车辆维修-PC-厂家配置-分页 */
export function repairFactoryPage (params) {
    return request({
        url: '/dwDzcc/clwx/repair-factory/page',
        method: 'post',
        data: params
    })
}

/** 车辆维修-PC-厂家配置-列表 */
export function repairFactoryList (params) {
    return request({
        url: '/dwDzcc/clwx/repair-factory/list',
        method: 'post',
        data: params
    })
}

/** 车辆维修-PC-厂家配置-新增 */
export function repairFactoryAdd (params) {
    return request({
        url: '/dwDzcc/clwx/repair-factory/add',
        method: 'post',
        data: params
    })
}

/** 车辆维修-PC-厂家配置-修改 */
export function repairFactoryEdit (params) {
    return request({
        url: '/dwDzcc/clwx/repair-factory/edit',
        method: 'post',
        data: params
    })
}

/** 车辆维修-PC-厂家配置-删除 */
export function repairFactoryDeleteByIds (params) {
    return request({
        url: '/dwDzcc/clwx/repair-factory/deleteByIds',
        method: 'post',
        data: params
    })
}