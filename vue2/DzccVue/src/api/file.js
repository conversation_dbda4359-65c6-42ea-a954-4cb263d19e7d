import request from '@/utils/request'

/**
 * 下载示例
 * @returns
 */
export function downloadImportExample(params){
    return request({
        url: '/dwDzcc/dzcc/file/downloadImportExample',
        method:'post',
        data: params,
        responseType: 'blob'
    })
}

/**
 * 读取文件名
 * @returns
 */
export function getFileName(params) {
    return request({
        url: '/dwDzcc/dzcc/file/getFileName',
        method: 'get',
        params: params
    })
}

/**
 * 下载
 * @returns
 */
export function download(params) {
    return request({
        url: '/dwDzcc/dzcc/file/download',
        method: 'get',
        params: params,
        responseType: 'blob'
    })
}

/**
 * 根据文件ID列表获取文件信息
 * @param {Object} params - 包含ids数组的参数对象
 * @returns
 */
export function getFileList(params) {
    return request({
        url: '/dwDzcc/dzcc/file/getFileList',
        method: 'post',
        data: params
    })
}

/**
 * 删除文件
 * @param {String|Number} fileId - 文件ID
 * @returns
 */
export function deleteFile(fileId) {
    return request({
        url: `/dwDzcc/dzcc/file/delete/${fileId}`,
        method: 'delete'
    })
}

/**
 * 上传文件
 * @param {FormData} formData - 文件表单数据
 * @returns
 */
export function uploadFile(formData) {
    return request({
        url: '/dwDzcc/dzcc/file/upload',
        method: 'post',
        data: formData,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

/**
 * 流式下载文件（支持Range请求，用于视频在线播放）
 * @param {String} filePath - 文件路径
 * @returns
 */
export function streamDownload(filePath) {
    return request({
        url: '/dwDzcc/dzcc/file/download2',
        method: 'get',
        params: { filePath },
        responseType: 'blob'
    })
}

/**
 * 获取流式播放URL
 * @param {String} filePath - 文件路径
 * @returns {String} 流式播放URL
 */
export function getStreamingUrl(filePath) {
    const baseUrl = process.env.VUE_APP_BASE_API || ''
    return `${baseUrl}/dwDzcc/dzcc/file/download2?filePath=${encodeURIComponent(filePath)}`
}
