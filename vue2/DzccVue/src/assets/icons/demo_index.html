<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3873535" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">二维码</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">3.1 密码</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe788;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe788;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe671;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe671;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">客服热线</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b1;</span>
                <div class="name">字体</div>
                <div class="code-name">&amp;#xe7b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76d;</span>
                <div class="name">向上1</div>
                <div class="code-name">&amp;#xe76d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe771;</span>
                <div class="name">向下1</div>
                <div class="code-name">&amp;#xe771;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76e;</span>
                <div class="name">向上2</div>
                <div class="code-name">&amp;#xe76e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe772;</span>
                <div class="name">向下2</div>
                <div class="code-name">&amp;#xe772;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe775;</span>
                <div class="name">向右1</div>
                <div class="code-name">&amp;#xe775;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe779;</span>
                <div class="name">向左1</div>
                <div class="code-name">&amp;#xe779;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b7;</span>
                <div class="name">205设置-线性</div>
                <div class="code-name">&amp;#xe8b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88b;</span>
                <div class="name">电话</div>
                <div class="code-name">&amp;#xe88b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c7;</span>
                <div class="name">眼睛</div>
                <div class="code-name">&amp;#xe8c7;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1679888740678') format('woff2'),
       url('iconfont.woff?t=1679888740678') format('woff'),
       url('iconfont.ttf?t=1679888740678') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-erweima"></span>
            <div class="name">
              二维码
            </div>
            <div class="code-name">.icon-erweima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-31mima"></span>
            <div class="name">
              3.1 密码
            </div>
            <div class="code-name">.icon-31mima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yonghu"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.icon-yonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-addNew"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.icon-addNew
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-kefurexian"></span>
            <div class="name">
              客服热线
            </div>
            <div class="code-name">.icon-kefurexian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ziti"></span>
            <div class="name">
              字体
            </div>
            <div class="code-name">.icon-ziti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshang1"></span>
            <div class="name">
              向上1
            </div>
            <div class="code-name">.icon-xiangshang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxia1"></span>
            <div class="name">
              向下1
            </div>
            <div class="code-name">.icon-xiangxia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangshang2"></span>
            <div class="name">
              向上2
            </div>
            <div class="code-name">.icon-xiangshang2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangxia2"></span>
            <div class="name">
              向下2
            </div>
            <div class="code-name">.icon-xiangxia2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangyou1"></span>
            <div class="name">
              向右1
            </div>
            <div class="code-name">.icon-xiangyou1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangzuo1"></span>
            <div class="name">
              向左1
            </div>
            <div class="code-name">.icon-xiangzuo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.icon-caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.icon-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shezhi-xianxing"></span>
            <div class="name">
              205设置-线性
            </div>
            <div class="code-name">.icon-shezhi-xianxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianhua"></span>
            <div class="name">
              电话
            </div>
            <div class="code-name">.icon-dianhua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yanjing"></span>
            <div class="name">
              眼睛
            </div>
            <div class="code-name">.icon-yanjing
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-erweima"></use>
                </svg>
                <div class="name">二维码</div>
                <div class="code-name">#icon-erweima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-31mima"></use>
                </svg>
                <div class="name">3.1 密码</div>
                <div class="code-name">#icon-31mima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yonghu"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#icon-yonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-addNew"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#icon-addNew</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-kefurexian"></use>
                </svg>
                <div class="name">客服热线</div>
                <div class="code-name">#icon-kefurexian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ziti"></use>
                </svg>
                <div class="name">字体</div>
                <div class="code-name">#icon-ziti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshang1"></use>
                </svg>
                <div class="name">向上1</div>
                <div class="code-name">#icon-xiangshang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxia1"></use>
                </svg>
                <div class="name">向下1</div>
                <div class="code-name">#icon-xiangxia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangshang2"></use>
                </svg>
                <div class="name">向上2</div>
                <div class="code-name">#icon-xiangshang2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangxia2"></use>
                </svg>
                <div class="name">向下2</div>
                <div class="code-name">#icon-xiangxia2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangyou1"></use>
                </svg>
                <div class="name">向右1</div>
                <div class="code-name">#icon-xiangyou1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangzuo1"></use>
                </svg>
                <div class="name">向左1</div>
                <div class="code-name">#icon-xiangzuo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#icon-caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#icon-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi-xianxing"></use>
                </svg>
                <div class="name">205设置-线性</div>
                <div class="code-name">#icon-shezhi-xianxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianhua"></use>
                </svg>
                <div class="name">电话</div>
                <div class="code-name">#icon-dianhua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yanjing"></use>
                </svg>
                <div class="name">眼睛</div>
                <div class="code-name">#icon-yanjing</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
