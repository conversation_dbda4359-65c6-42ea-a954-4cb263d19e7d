import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

export const constantRoutes = [
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/redirect/:path(.*)',
                component: () => import('@/views/redirect')
            }
        ]
    },
    {
        path: '/404',
        component: () => import('@/views/error/404'),
        hidden: true
    },
    {
        path: '/401',
        component: () => import('@/views/error/401'),
        hidden: true
    },
    {
        path: "*",
        name: "NotFound",
        component: () => import("@/views/error/404"),
    },
    //协同办公路由
    {
        path: '/dzcc',
        component: Layout,
        // redirect: 'index',
        children: [
            {
                path: 'index.html',
                name: 'index.html',
                component: () => import('@/views/index'),
            },
            {
                path: 'clgl',
                name: 'clgl',
                component: (resolve) => require(['@/views/dzcc/clgl/clgl'], resolve),
            },
            {
                path: 'cdzgl',
                name: 'cdzgl',
                component: (resolve) => require(['@/views/dzcc/clgl/cdzgl'], resolve),
            },
            {
                path: 'cdzpz',
                name: 'cdzpz',
                component: (resolve) => require(['@/views/dzcc/clgl/cdzpz'], resolve),
            },
            {
                path: 'clbgd',
                name: 'clbgd',
                component: (resolve) => require(['@/views/dzcc/clgl/clbgd'], resolve),
            },
            {
                path: 'clxxpz',
                name: 'clxxpz',
                component: (resolve) => require(['@/views/dzcc/clgl/clxxpz'], resolve),
            },
            {
                path: 'jbfjs',
                name: 'jbfjs',
                component: (resolve) => require(['@/views/dzcc/clgl/jbfjs'], resolve)
            },
            {
                path: 'ccd',
                name: 'ccd',
                component: (resolve) => require(['@/views/dzcc/ccd/ccd'], resolve),
            },
            {
                path: 'clgj',
                name: 'clgj',
                component: (resolve) => require(['@/views/dzcc/ccd/clgj'], resolve),
            },


            {
                path: 'cbjl',
                name: 'cbjl',
                component: (resolve) => require(['@/views/dzcc/gj/cbjl'], resolve),
            },
            {
                path: 'jbclxx',
                name: 'jbclxx',
                component: (resolve) => require(['@/views/dzcc/gj/jbclxx'], resolve),
            },
            {
                path: 'lcgj',
                name: 'lcgj',
                component: (resolve) => require(['@/views/dzcc/gj/lcgj'], resolve),
            },
            {
                path: 'xcgj',
                name: 'xcgj',
                component: (resolve) => require(['@/views/dzcc/gj/xcgj'], resolve),
            },
            {
                path: 'yccllc',
                name: 'yccllc',
                component: (resolve) => require(['@/views/dzcc/gj/yccllc'], resolve),
            },
            {
                path: 'wgjgj',
                name: 'wgjgj',
                component: (resolve) => require(['@/views/dzcc/gj/wgjgj'], resolve),
            },
            {
                path: 'jjrpz',
                name: 'jjrpz',
                component: (resolve) => require(['@/views/dzcc/ryqx/jjrpz'], resolve),
            },
            {
                path: 'ryqx',
                name: 'ryqx',
                component: (resolve) => require(['@/views/dzcc/ryqx/ryqx'], resolve),
            },
            {
                path: 'sjqx',
                name: 'sjqx',
                component: (resolve) => require(['@/views/dzcc/ryqx/sjqx'], resolve),
            },
            {
                path: 'bb',
                name: 'bb',
                component: (resolve) => require(['@/views/dzcc/bb/bb'], resolve),
            },
            {
                path: 'clsy',
                name: 'clsy',
                component: (resolve) => require(['@/views/dzcc/bb/clsy'], resolve),
            },
            {
                path: 'clyd',
                name: 'clyd',
                component: (resolve) => require(['@/views/dzcc/bb/clyd'], resolve),
            },
            {
                path: 'yxts',
                name: 'yxts',
                component: (resolve) => require(['@/views/dzcc/bb/yxts'], resolve),
            },
            {
                path: 'sjtblc',
                name: 'sjtblc',
                component: (resolve) => require(['@/views/dzcc/bb/sjtblc'], resolve),
            },
            {
                path: 'jbfxx',
                name: 'jbfxx',
                component: (resolve) => require(['@/views/dzcc/bb/jbfxx'], resolve),
            },
            {
                path: 'sjtg',
                name: 'sjtg',
                component: (resolve) => require(['@/views/dzcc/tg/sjtg'], resolve),
            },
            {
                path: 'zzxx',
                name: 'zzxx',
                component: (resolve) => require(['@/views/dzcc/xxtb/zzxx'], resolve)
            },
            {
                path: 'clxx',
                name: 'clxx',
                component: (resolve) => require(['@/views/dzcc/zctz/clxx'], resolve)
            },
            {
                path: 'jsyxx',
                name: 'jsyxx',
                component: (resolve) => require(['@/views/dzcc/xxtb/jsyxx'], resolve)
            },
            {
                path: 'jtsjqd',
                name: 'jtsjqd',
                component: (resolve) => require(['@/views/dzcc/jtsj/jtsjqd'], resolve)
            },
            {
                path: 'cdgzl',
                name: 'cdgzl',
                component: (resolve) => require(['@/views/dzcc/jtsj/cdgzl'], resolve)
            },
            {
                path: 'yssj',
                name: 'yssj',
                component: (resolve) => require(['@/views/dzcc/jtsj/yssj'], resolve)
            },
            {
                path: 'yssp',
                name: 'yssp',
                component: (resolve) => require(['@/views/dzcc/jtsj/yssp'], resolve)
            },
            {
                path: 'ccdd',
                name: 'ccdd',
                component: (resolve) => require(['@/views/dzcc/jtsj/ccdd'], resolve)
            },
            {
                path: 'jtjbf',
                name: 'jtjbf',
                component: (resolve) => require(['@/views/dzcc/jtsj/jtjbf'], resolve)
            },
            {
                path: 'pj',
                name: 'pj',
                component: (resolve) => require(['@/views/dzcc/ccd/pj'], resolve)
            },
            {
                path: 'clwx-cjpz',
                name: 'clwx-cjpz',
                component: (resolve) => require(['@/views/clwx/cjpz'], resolve)
            },
            {
                path: 'clwx-wxd',
                name: 'clwx-wxd',
                component: (resolve) => require(['@/views/clwx/wxd'], resolve)
            },
        ]
    },
    {
        path: '/dzcc/locus',
        name: 'locus',
        component: (resolve) => require(['@/views/dzcc/locus'], resolve)
    },
]

const router = new VueRouter({
    mode: 'history',
    scrollBehavior: () => ({ y: 0 }),
    base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
    routes: constantRoutes
})

export default router
