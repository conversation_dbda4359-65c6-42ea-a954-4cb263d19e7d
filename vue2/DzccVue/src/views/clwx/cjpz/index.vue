<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">单位：</span>
        <el-select v-model="queryParams.deptId" clearable filterable placeholder="请选择单位" @change="handleChange">
          <el-option v-for="item in groupItem" :key="item.id" :label="item.groupname" :value="item.id"></el-option>
        </el-select>
        <span class="font-size14">送修厂家：</span>
        <el-input v-model="queryParams.repairFactoryName" clearable placeholder="请输入送修厂家" @change="handleChange"></el-input>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-plus" @click="openDialog('add')">新增</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-edit" @click="openDialog('edit')">修改</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-delete" @click="openDialog('delete')">删除</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          :need-select="true"
          @getCurrentData="select"
          @getSelectionData="handleSelectionChange">

        <template slot="status" slot-scope="scope">
          <div>
            {{ scope.row.status === 1 ? '已启用' : scope.row.status === 0 ? '未启用' : '' }}
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="30%"
        :visible="dialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog(dialogType)"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="formParams"
          :model="formParams"
          :rules="formRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位" prop="deptId">
              <el-select v-model="formParams.deptId" clearable filterable placeholder="请选择单位">
                <el-option
                    v-for="item in groupItem2"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="送修厂家" prop="repairFactoryName">
              <el-input v-model="formParams.repairFactoryName" clearable placeholder="请输入送修厂家"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="启用状态" prop="status">
              <el-radio-group v-model="formParams.status">
                <el-radio :label="1" >启用</el-radio>
                <el-radio :label="0" >不启用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitDialog(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog(dialogType)">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {repairFactoryAdd, repairFactoryDeleteByIds, repairFactoryEdit, repairFactoryPage} from "api/clwx/repairFactory";
import {commonGroupList} from "api/clwx/common";

export default {
  name: 'clwx-cjpz',
  components: {Table, Pagination, Dropdown},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    commonGroupList().then(res => {
      this.groupItem = res.result
      this.groupItem2 = res.result.filter(item => item.id !== -1)
      this.getList()
    })
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        repairFactoryName: '',
        deptId: -1
      },
      // 查询部门列表
      groupItem: [],
      // 新增/修改部门列表
      groupItem2: [],
      // 列表
      list: [],
      realTableOptions: [],
      tableOptions: [
        {label: '单位', prop: 'deptName'},
        {label: '送修厂家', prop: 'repairFactoryName'},
        {label: '启用状态', prop: 'status',slot: true},
      ],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表
      multipleSelection: [],
      // dialog选择列表id
      selectID: 0,
      // dialog选择列表参数
      formParams: {},
      // dialog类型
      dialogType: '',
      // dialog标题
      dialogTitle: '',
      // dialog
      dialog: false,
      // 表单校验
      formRules: {
        repairFactoryName: [
          {required: true, message: '请输入送修厂家', trigger: 'blur'}
        ],
        deptId: [
          {required: true, message: '请选择单位', trigger: 'change'}
        ],
        status: [
          {required: true, message: '请选择是否启用', trigger: 'change'}
        ]
      }
    }
  },
  methods: {
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      repairFactoryPage(this.queryParams).then(res => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getList()
    },
    /** 重置查询车辆管理 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.formParams = row
    },
    /** 多选表事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 打开弹窗 */
    openDialog(type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add';
          this.dialogTitle = '新增厂家配置';
          this.selectID = 0;
          this.formParams = {};
          this.dialog = true;
          break;
        case 'edit':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogType = 'edit';
            this.dialogTitle = '修改厂家配置';
            this.dialog = true;
          }
          break;
        case 'delete':
          if (this.multipleSelection.length > 0) {
            this.$confirm('是否删除所选数据?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              repairFactoryDeleteByIds({ids: this.multipleSelection.map(item => item.id)}).then(() => {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.multipleSelection = [];
                this.getList();
             })
            })
          }
          break;
        default:
          break;
      }
    },
    /** 关闭弹窗 */
    closeDialog(type) {
      switch (type) {
        case 'add':
          this.dialog = false;
          break;
        case 'edit':
          this.dialog = false;
          break;
        default:
          break;
      }
    },
    submitDialog(type) {
      switch (type) {
        case 'add':
          this.$refs.formParams.validate((valid) => {
            if (valid) {
              repairFactoryAdd(this.formParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog(this.dialogType)
              }).finally(() => {
                this.getList()
              })
            } else {
              return false
            }
          })
          break;
        case 'edit':
          this.$refs.formParams.validate((valid) => {
            if (valid) {
              repairFactoryEdit(this.formParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog(this.dialogType)
              }).finally(() => {
                this.getList()
              })
            } else {
              return false
            }
          })
          break;
        default:
          break;
      }
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>