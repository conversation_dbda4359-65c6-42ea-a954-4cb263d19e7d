<template>
  <div>
    <div style="margin-bottom: 10px">
      <el-button type="primary" icon="el-icon-download" @click="downWxdXq">下载pdf</el-button>
    </div>
    <el-tabs type="border-card" v-model="activeKey">
      <el-tab-pane label="维修单详情" name="first" style="height: 500px;" v-watermark="{label: watermark}">
        <el-form
            ref="formParams"
            :model="formParams"
            :inline="true"
            label-width="auto"
        >
          <el-divider content-position="left">基本信息</el-divider>
          <el-row>
            <el-col :span="8">
              <el-form-item label="维修单编号" class="force-width-60">{{ formParams.applyNo }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="申请人" class="force-width-60">{{ formParams.applyUserName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单位" class="force-width-60">{{ formParams.applyTopDeptName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车牌" class="force-width-60">{{ formParams.licencePlate }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车型" class="force-width-60">{{ formParams.carMold }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产权性质" class="force-width-60">
                <span v-if="formParams.nature == 0">临租车辆</span>
                <span v-else-if="formParams.nature == 1">特殊车辆</span>
                <span v-else-if="formParams.nature == 2">产权车辆</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审批状态" class="force-width-60">
                <span v-if="formParams.approveState == 0">未提交</span>
                <span v-else-if="formParams.approveState == 1">审批中</span>
                <span v-else-if="formParams.approveState == 2">已审批</span>
                <span v-else-if="formParams.approveState == 3">已驳回</span>
                <span v-else-if="formParams.approveState == 4">流程终止</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="left">维修信息</el-divider>
          <el-row>
            <el-col :span="8">
              <el-form-item label="送修厂家" class="force-width-60">{{ formParams.repairFactoryName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报修日期" class="force-width-60">{{ getDate(formParams.repairDate) }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估金额" class="force-width-60">{{ formParams.estimatedAmount }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="预估材料费" class="force-width-60">{{ dictDataKeyToValue(formParams.estimatedMaterialCosts,"dfdw_clwx_material_cost")  }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实际维修金额" class="force-width-60">{{ formParams.actualRepairAmount }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实际材料费" class="force-width-60">{{ formParams.actualMaterialCost }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="报修项目" class="force-width-60">{{ formParams.repairItem }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="报修原因" class="force-width-60">{{ formParams.repairReason }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件" class="force-width-60">

                <dw-upload-file
                    v-model="formParams.annexIds"
                    :limit="5"
                    :max-size="10"
                    :disabled="true"
                    accept=".jpg,.png,.pdf,.doc,.docx"
                    button-text="上传附件"
                    tip="只能上传jpg/png/pdf/doc/docx文件，且不超过10MB"
                    @success="handleSuccess"
                    @error="handleError"
                />



              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="维修单流程" name="second" style="height: 500px;overflow: auto" v-watermark="{label: watermark}">
        <el-timeline>
          <el-timeline-item v-for="(value,index) in lcList" :key="value.lc_jdID+value.lc_jdmc+index" :timestamp="value.startdate"
                            placement="top">
            <el-card>
              <h4>{{ value.lc_jdmc }}</h4>
              <p>{{ value.personName }} 提交于 {{ value.startdate }}</p>
              <p>审批意见：{{ value.feed }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {downWxdXqByIds, lcListById} from "api/clwx/bxd";
import {downLoad, getDate} from "@/utils/tool";
import {listDataAll} from "@/api/dict/data";
import DwUploadFile from "@/components/UploadFile/dw_upload_file.vue";

export default {
  name: 'wxdxq',
  components: {DwUploadFile},
  mounted() {
    if (this.formParams !== undefined) {
      lcListById(this.formParams).then((res) => {
        let response = res
        if (typeof res == 'string') {
          response = eval("(" + res + ")")
        }
        this.lcList = response.result
      })

      this.getDictData()
    }
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  props: {
    formParams: {
      type: Object,
      default: undefined
    }
  },
  data() {
    return {
      // 流程列表
      lcList: [],
      activeKey: 'first',
      materialCostDictData:[]
    }
  },
  methods: {
    getDate,
    downWxdXq() {
      this.formParams.downloadType = 'pdf';
      this.formParams.idList = [this.formParams.id]
      downWxdXqByIds(this.formParams).then((res) => {
        const fileName = '报修单详情' + this.formParams.applyNo + '.pdf'
        downLoad(res, fileName)
      })
    },
    //获取字典
    getDictData(){
      listDataAll({type:"dfdw_clwx_material_cost"}).then(res=>{
        this.materialCostDictData = res.result;
      })
    },
    dictDataKeyToValue(dictKey, dictType) {
      console.log(this.formParams)
      if (dictType === "dfdw_clwx_material_cost") {
        const found = this.materialCostDictData.find(value => value.value === dictKey.toString());
        return found ? found.label : '-';
      }
      return '-';
    },
    handleSuccess(response, file, fileList) {
      console.log('上传成功:', response)
    },
    handleError(error, file, fileList) {
      console.log('上传失败:', error)
    }
  },
}
</script>

<style scoped lang="less">
.text {
  font-size: 14px;
}

.box-card {
  width: 480px;
}
</style>