<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">单 位：</span>
        <el-select v-model="queryParams.applyTopDeptId" clearable filterable placeholder="请选择单位" @change="handleChange">
          <el-option v-for="item in groupItem" :key="item.id" :label="item.groupname" :value="item.id"></el-option>
        </el-select>
        <span class="font-size14">车 牌：</span>
        <el-input v-model="queryParams.licencePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>
        <span class="font-size14">审批状态：</span>
        <el-select v-model="queryParams.approveState" clearable filterable placeholder="请选择审批状态" @change="handleChange">
          <el-option label="未提交" :value="0"></el-option>
          <el-option label="审批中" :value="1"></el-option>
          <el-option label="已审批" :value="2"></el-option>
          <el-option label="已驳回" :value="3"></el-option>
          <el-option label="流程终止" :value="4"></el-option>
        </el-select>
        <span class="font-size14">报修日期：</span>
        <el-date-picker
            v-model="queryParams.repairDateArray"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            clearable
            @change="handleChange">
        </el-date-picker>
        <div style="margin-top: 2px">
          <span class="font-size14">申请人：</span>
          <el-input v-model="queryParams.applyUserName" clearable placeholder="请输入申请人" @change="handleChange"></el-input>
          <span class="font-size14">确认状态：</span>
          <el-select v-model="queryParams.confirmedStatus" clearable filterable placeholder="请选择确认状态" @change="handleChange">
            <el-option label="未确认" :value="0"></el-option>
            <el-option label="已确认" :value="1"></el-option>
          </el-select>
          <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
          <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button class="rygf" size="mini" type="text" icon="el-icon-view" @click="openDialog('detailShow')">查看详情</el-button>
          <el-button class="rygf" size="mini" type="text" icon="el-icon-download" @click="openDialog('download')" v-has-permi="['JDWWX01DJ01QX05']">批量导出详情</el-button>
          <el-button class="rygf" size="mini" type="text" icon="el-icon-download" :disabled=" !this.paramsList.length > 0" @click="openDialog('downloadFile')" v-has-permi="['JDWWX01DJ01QX06']">批量下载附件</el-button>
          <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
        </div>

      </div>
    </div>
<!--    <div class="operate-pannel">-->
<!--      <div class="search-box">-->

<!--      </div>-->
<!--    </div>-->
    <div class="table-box">
      <Table
          ref="table"
          :needSelect="true"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @getSelectionData="selectionData"
          @rowdblclick="openDialog('detailShow')"
      >
        <template v-slot:nature="scope">
          <span v-if="scope.row.nature == 0">临租车辆</span>
          <span v-else-if="scope.row.nature == 1">特殊车辆</span>
          <span v-else-if="scope.row.nature == 2">产权车辆</span>
        </template>
        <template v-slot:repairDate="scope">
          <span>{{getDate(scope.row.repairDate)}}</span>
        </template>
        <template v-slot:estimatedMaterialCosts="scope">
          <span>{{dictDataKeyToValue(scope.row.estimatedMaterialCosts,"dfdw_clwx_material_cost")}}</span>
        </template>

        <template v-slot:confirmedStatus="scope">
          <span v-if="scope.row.confirmedStatus == 0"> 未确认 </span>
          <span v-if="scope.row.confirmedStatus == 1"> 已确认 </span>
        </template>
        <template v-slot:approveState="scope">
          <span v-if="scope.row.approveState == 0">未提交</span>
          <span v-else-if="scope.row.approveState == 1">审批中</span>
          <span v-else-if="scope.row.approveState == 2">已审批</span>
          <span v-else-if="scope.row.approveState == 3">已驳回</span>
          <span v-else-if="scope.row.approveState == 4">流程终止</span>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="60%"
        :visible="dialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog(dialogType)"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <wxdxq ref="detailRef" :formParams="formParams" v-if="dialog"></wxdxq>
    </el-dialog>
  </div>
</template>

<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import wxdxq from './components/wxdxq.vue'
import {commonGroupList} from "api/clwx/common";
import {bxdPage, downWxdXqByIds,downloadFile} from "api/clwx/bxd";
import {downLoad, getDate} from "@/utils/tool";
import {listDataAll} from "../../../api/dict/data";

export default {
  name: 'clwx-wxd',
  components: {Pagination, Table, Dropdown, wxdxq},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    commonGroupList().then(res => {
      this.groupItem = res.result
      this.getList()
      this.getDictData()
    })
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        applyNo: null,
        applyTopDeptId: -1,
        licencePlate: null,
        approveState: null,
        repairDateArray: [],
        applyUserName: '',
      },
      // 查询部门列表
      groupItem: [],
      // 列表
      list: [],
      realTableOptions: [],
      tableOptions: [
        {label: '维修编号', prop: 'applyNo'},
        {label: '单位', prop: 'applyTopDeptName'},
        {label: '车牌', prop: 'licencePlate'},
        {label: '车型', prop: 'carMold'},
        {label: '产权性质', prop: 'nature', slot: true},
        {label: '送修厂家', prop: 'repairFactoryName', tooltip: true},
        {label: '报修日期', prop: 'repairDate', slot: true},
        {label: '报修项目', prop: 'repairItem', tooltip: true},
        {label: '报修原因', prop: 'repairReason', tooltip: true},
        {label: '预计金额', prop: 'estimatedAmount'},
        {label: '预估材料费', prop: 'estimatedMaterialCosts',slot: true},
        {label: '实际维修金额', prop: 'actualRepairAmount'},
        {label: '实际材料费', prop: 'actualMaterialCost'},
        {label: '申请人', prop: 'applyUserName'},
        {label: '审批状态', prop: 'approveState', slot: true},
        {label: '确认状态', prop: 'confirmedStatus', slot: true},
      ],
      // 遮罩层
      loading: false,
      // dialog选择列表id
      selectID: 0,
      // dialog选择列表参数
      formParams: {},
      // 多选列表
      paramsList: [],
      // dialog类型
      dialogType: '',
      // dialog标题
      dialogTitle: '',
      // dialog
      dialog: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 预估材料费字典
      materialCostDictData:[]
    }
  },
  methods: {
    getDate,
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      if (this.queryParams.repairDateArray == null) {
        this.queryParams.repairDateArray = []
      }
      bxdPage(this.queryParams).then(res => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getList()
    },
    /** 重置查询车辆管理 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.formParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0])
      }
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      switch (type) {
        case 'detailShow':
          this.dialogType = 'detailShow'
          this.dialogTitle = '查看详情'
          this.dialog = true
          //this.$refs.detailRef.getDictData()
          break;
        case 'download':
          let ids = [];
          this.paramsList.forEach(value => {
            ids.push(value.id)
          })
          this.queryParams.idList = ids;
          this.queryParams.type = 'zip';
          downWxdXqByIds(this.queryParams).then((res) => {
            const fileName = '维修单详情.zip'
            downLoad(res, fileName)
          })
          break;
        case 'downloadFile':
          let idss = [];
          this.paramsList.forEach(value => {
            idss.push(value.id)
          })
          this.queryParams.idList = idss;
          downloadFile(this.queryParams).then((res) => {
            const fileName = '维修单附件.zip'
            downLoad(res, fileName)
          })
          break;
      }
    },
    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case 'detailShow':
          this.dialog = false
          break;
      }
    },

    //获取字典
    getDictData(){
      listDataAll({type:"dfdw_clwx_material_cost"}).then(res=>{
        this.materialCostDictData = res.result;
      })
    },
    dictDataKeyToValue(dictKey, dictType) {
      if (dictType === "dfdw_clwx_material_cost") {
        // 检查dictKey是否为null、undefined或空值
        if (dictKey == null || dictKey === '') {
          return '';
        }
        const found = this.materialCostDictData.find(value => value.value === dictKey.toString());
        return found ? found.label : '';
      }
      return '';
    }


  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>