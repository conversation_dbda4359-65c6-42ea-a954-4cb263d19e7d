<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.applyTopGroupId" filterable clearable placeholder="请选择部门"
                   @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">出车时间：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
        >
        </el-date-picker>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licencePlate" style="width: 260px;" clearable placeholder="请输入车辆" @change="handleChange"></el-input>
        <span class="font-size14">司机：</span>
        <el-input v-model="queryParams.driveName" style="width: 260px;" clearable placeholder="请输入司机" @change="handleChange"></el-input>
        <span class="font-size14">用车人：</span>
        <el-input v-model="queryParams.ycrName" style="width: 260px;" clearable placeholder="请输入用车人" @change="handleChange"></el-input>
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-button type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-view" @click="openDialog('show')" v-has-permi="['NDWCC01PJ01QX01']">查看</el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('download')" v-has-permi="['NDWCC01PJ01QX05']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
        <el-checkbox v-model="queryParams.hasTs" @change="handleChange" style="margin-left: 10px">只展示问题评价</el-checkbox>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          height="96%"
          @getCurrentData="select"
          @rowdblclick="openDialog('show')"
      >
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
        <template v-slot:ccOpenTime="scope">
          {{ showTime(scope.row.ccOpenTime) }}
        </template>
        <template v-slot:rate="scope">
          <el-rate
              v-model="scope.row.rate"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}">
          </el-rate>
        </template>
        <template v-slot:zjState="scope">
          {{ filterState(scope.row.zjState) }}
        </template>
        <template v-slot:aqjsState="scope">
          {{ filterState(scope.row.aqjsState) }}
        </template>
        <template v-slot:pdkzState="scope">
          {{ filterState(scope.row.pdkzState) }}
        </template>
        <template v-slot:tsState="scope">
          {{ filterState(scope.row.tsState) }}
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="dialogVisible"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="dialogVisible = false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <ccdxq :ccd-params="dialogParam" :down-load-show="false" :work-flow-show="false" :evaluate-show="true" v-if="dialogVisible"></ccdxq>
    </el-dialog>
  </div>
</template>

<script>
import {getDzccTPjPage, exportDzccTPjExcel} from "api/dzcc/ccd/pj";
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {downLoad} from "@/utils/tool";
import {GetGroupList} from "api/dzcc/clgl/clgl";
import ccdxq from "components/DZCC/ccdxq.vue";
import {GetCcdListById} from "api/dzcc/ccd/ccd";

export default {
  name: "test",
  components: {ccdxq, Table, Pagination, Dropdown},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        applyNo: null,
        applyTopGroupId: -1,
        beginDate: null,
        overDate: null,
        time: [],
        licencePlate: null,
        driveName: null,
        ycrName: null,
        hasTs: true,
      },
      // 查询部门列表
      groupItem: [],
      // 数据列表
      List: [],
      // 真实列表
      realTableOptions:[],
      // 列表配置
      tableOptions: [
        {label: '出车单编号', prop: 'applyNo'},
        {label: '部门', prop: 'applyTopDeptName'},
        {label: '出车日期', prop: 'ccOpenTime', slot: true},
        {label: '车牌', prop: 'licencePlate'},
        {label: '司机', prop: 'driveName'},
        {label: '用车人', prop: 'ycrName'},
        {label: '星级评价', prop: 'rate', slot: true},
        {label: '是否整洁', prop: 'zjState', slot: true},
        {label: '是否安全驾驶', prop: 'aqjsState', slot: true},
        {label: '是否准时到达', prop: 'pdkzState', slot: true},
        {label: '服务是否满意', prop: 'tsState', slot: true},
      ],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      selectParams: {},
      // 弹窗类型
      dialogType: '',
      // 弹窗标题
      dialogTitle: '',
      // 弹窗显示
      dialogVisible: false,
      // 新增/修改参数
      dialogParam: {},
      isOrNoOptions: [
          {label: '是', value: 1},
          {label: '否', value: 0}
      ],
    }
  },
  methods: {
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      if (this.queryParams.time.length > 0) {
        this.queryParams.beginDate = this.queryParams.time[0]
        this.queryParams.overDate = this.queryParams.time[1]
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询插拔记录 */
    getList () {
      this.loading = true
      getDzccTPjPage(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.selectParams = row
    },
    /** 表格字典数据筛选 */
    filterState(value) {
      return this.isOrNoOptions.find(item => item.value === value).label
    },
    /** 时间格式修改 */
    showTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
      return this.$dayjs(date).format(format)
    },
    /** 按钮点击事件 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            GetCcdListById(this.selectParams.lcId).then((res) => {
              this.dialogParam = res.result
              this.dialogType = type;
              this.dialogVisible = true;
              this.dialogTitle = '查看出车单详情';
            })
          }
          break;
        case 'download':
          exportDzccTPjExcel(this.queryParams).then((res) => {
            this.$message.success('导出成功！')
            downLoad(res, '电子出车单-评价.xlsx')
          })
          break;
      }
    },
    /** 关闭弹窗 */
    cancelDialog() {
      this.dialogVisible = false
      this.getList()
    },
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
    .el-input-number {
      width: 200px;
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>