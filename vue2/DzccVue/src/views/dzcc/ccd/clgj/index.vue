<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">

        <span class="font-size14">管理部门:</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14">司机姓名:</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入司机姓名" @change="handleChange"></el-input>

        <span class="font-size14">车牌:</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>

        <el-button

            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getLocusList"
        >查询
        </el-button>

        <el-button

            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="openDialog('showGJ')"
            v-if="this.$store.getters.permissions.indexOf('NDWCC01APP08QX01') > -1  && this.gjShow"
        >查看轨迹
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-if="this.$store.getters.permissions.indexOf('NDWCC01APP08QX01') > -1"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('showGJ')"
      >

      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>

</template>
<script>
import { getLocusList,DownLoad } from '@/api/dzcc/ccd/clgj'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {OpenMoudleWindow} from "@/utils/jiaohu";
import {downLoad} from "@/utils/tool";

export default {
  name: 'clgj',
  components: {
    Table, Pagination, Dropdown
  },
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getLocusList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        driverName: '',
        groupId: -1,
        licensePlate: ''
      },
      // 查询部门列表
      groupItem: [],
      // 主列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      locusParams: {},
      dialogType: '',
      dialogTitle: '',
      locusDialog: false,
      // 轨迹路径
      locus: {
        path: '/dzcc/locus',
        query: {
          carId: 0,
          startDate: '',
          endDate: ''
        }
      },
      realTableOptions:[],
      tableOptions:[
        {label: '所属部门', prop: 'groupName'},
        {label: '车牌号', prop: 'licensePlate'},
        {label: '司机', prop: 'driverName'}
      ],
      gjShow:true
    }
  },
  methods: {
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getLocusList()
    },
    /** 查询车队长 */
    getLocusList () {
      this.loading = true
      getLocusList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getLocusList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getLocusList()
    },
    /** 单击表事件 */
    select (row) {
      this.locus.query.carId = row.carId
      this.locus.query.licensePlate = row.licensePlate
      this.selectID = row.carId
      this.locusParams = row

      console.log(this.locusParams.carMold )
      if(this.locusParams.carMold == '工程车'){
        this.gjShow = true;
      }else{
        this.gjShow = false;
      }
      console.log(this.gjShow)
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'showGJ':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            // this.$router.push(this.locus)
            const newpage = this.$router.resolve(this.locus)
            window.open(newpage.href, '_blank')
          }
          break
        case 'download':
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, '车辆轨迹列表信息.xlsx')
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.locusDialog = false
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
