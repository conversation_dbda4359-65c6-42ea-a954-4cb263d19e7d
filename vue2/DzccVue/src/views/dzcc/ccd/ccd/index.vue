<template>
  <div class="search-container" style="height: 96%">
    <div class="operate-pannel">
      <div class="search-box" style="width: 100%">

        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.applyTopDeptId" filterable clearable placeholder="请选择管理部门"
                   @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14">司机姓名：</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入司机姓名"
                  @change="handleChange"></el-input>

        <span class="font-size14">审核状态：</span>
        <el-select v-model="queryParams.approveState" placeholder="请选择审核状态" @change="handleChange">
          <el-option
              v-for="item in approveState"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">执行状态：</span>
        <el-select v-model="queryParams.executeState" placeholder="请选择审核状态" @change="handleChange">
          <el-option
              v-for="item in executeState"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">选择时间：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="handleChange"
            >
        </el-date-picker>

        <el-row style="margin-top: 1vh;margin-bottom: -0.5vh">
          <span class="font-size14">车牌：</span>
          <el-input v-model="queryParams.licensePlate" style="width: 260px;" clearable placeholder="若需要查询其他车辆，请输入“其他车辆”"
                    @change="handleChange"></el-input>
          <span class="font-size14">申请人：</span>
          <el-input v-model="queryParams.applyUserName" clearable placeholder="请输入申请人姓名"
                    @change="handleChange"></el-input>

          <span class="font-size14">用车人：</span>
          <el-input v-model="queryParams.ycrName" clearable placeholder="请输入用车人姓名"
                    @change="handleChange"></el-input>

          <span class="font-size14">车辆标识：</span>
          <el-select v-model="queryParams.carTag" clearable filterable placeholder="请选择车辆标识" @change="handleChange"
                     style="width: 190px">
            <el-option
                :key="-1"
                label="全部"
                :value="-1">
            </el-option>
            <el-option
                :key="0"
                label="临租车辆"
                :value="0">
            </el-option>
            <el-option
                :key="1"
                label="特殊车辆"
                :value="1">
            </el-option>
            <el-option
                :key="2"
                label="产权车辆"
                :value="2">
            </el-option>
          </el-select>

          <el-button
              class="rygf"
              size="mini"
              type="text"
              icon="el-icon-search"
              @click="getCCDList"
          >查询
          </el-button>

          <el-button
              class="rygf"
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="resetQuery"
          >重置
          </el-button>

          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-view"
              @click="openDialog('show')"
              v-has-permi="['NDWCC01CD02QX01']"
          >查看详情
          </el-button>

          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-view"
              @click="openDialog('showGJ')"
              v-has-permi="['NDWCC01CD02QX03']"
          >查看轨迹
          </el-button>

          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-download"
              @click="openDialog('ccddownload')"
              v-has-permi="['NDWCC01CD02QX01']"
          >导出
          </el-button>

          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-download"
              @click="openDialog('ccdxqdownload')"
              v-has-permi="['NDWCC01CD02QX01']"
          >批量导出详情
          </el-button>

          <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
        </el-row>
      </div>
    </div>
    <div class="table-box">
      <Table
          :needSelect="true"
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @getSelectionData="selectionData"
          @rowdblclick="openDialog('show')"
      >
        <template slot="ccOpenTime" slot-scope="scope">
          <div>
            {{ showTime(scope.row.ccOpenTime) }}
          </div>
        </template>
        <template slot="executeTime" slot-scope="scope">
          <div>
            {{ showTime(scope.row.executeTime) }}
          </div>
        </template>
        <template slot="actualStartTime" slot-scope="scope">
          <div>
            {{ showTime(scope.row.actualStartTime) }}
          </div>
        </template>
        <template slot="actualEndTime" slot-scope="scope">
          <div>
            {{ showTime(scope.row.actualEndTime) }}
          </div>
        </template>

        <template slot="approveState" slot-scope="scope">
          <div v-if="scope.row.approveState == 0">
            未提交
          </div>
          <div v-if="scope.row.approveState == 1">
            审批中
          </div>
          <div v-if="scope.row.approveState == 2">
            已审批
          </div>
          <div v-if="scope.row.approveState == 3">
            已驳回
          </div>
          <div v-if="scope.row.approveState == 4">
            流程终止
          </div>
        </template>

        <template slot="executeState" slot-scope="scope">
          <div v-if="scope.row.executeState == 0">
            未派车
          </div>
          <div v-if="scope.row.executeState == 1">
            已派车
          </div>
          <div v-if="scope.row.executeState == 2">
            已到达
          </div>
          <div v-if="scope.row.executeState == 3">
            派车撤回
          </div>
          <div v-if="scope.row.executeState == 4">
            已取消
          </div>
          <div v-if="scope.row.executeState == 5">
            车辆异常
          </div>
        </template>

        <template #carTag="scope">
          <div v-if="scope.row.carTag == 0">
            临租车辆
          </div>
          <div v-else-if="scope.row.carTag == 1">
            特殊车辆
          </div>
          <div v-else-if="scope.row.carTag == 2">
            产权车辆
          </div>
          <div v-else>
            -
          </div>
        </template>

        <template slot="arriveTime" slot-scope="scope">
          <div v-if="scope.row.executeState == 2">
            {{ showTime(scope.row.arriveTime) }}
          </div>
          <div v-else>
            -
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="ccdDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="ccdDialog = false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <ccdxq :ccd-params="ccdParams" v-if="ccdDialog"></ccdxq>
    </el-dialog>
  </div>


</template>
<script>
import {GetCcdList, DownLoadCcd, DownCcdXqById} from '@/api/dzcc/ccd/ccd'
import {downLoad} from '@/utils/tool'
import ccdxq from '@/components/DZCC/ccdxq'
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'ccd',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getCCDList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        driverName: '',
        applyTopDeptId: -1,
        licensePlate: '',
        ccOpenTime: null,
        executeTime: null,
        time: [],
        approveState: 2,
        executeState: 2,
        applyUserName: '',
        ycrName: '',
        carTag: -1,
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      ccdParams: {},
      // 多选列表数据
      ccdParamsList: {},
      dialogType: '',
      dialogTitle: '',
      ccdDialog: false,
      // 审批状态
      approveState: [
        {
          value: -1,
          label: '全部'
        }, {
          value: 0,
          label: '未提交'
        }, {
          value: 1,
          label: '审核中'
        }, {
          value: 2,
          label: '已审核'
        }, {
          value: 3,
          label: '已驳回'
        }, {
          value: 4,
          label: '流程终止'
        }
      ],
      // 执行状态
      executeState: [
        {
          value: -1,
          label: '全部'
        }, {
          value: 0,
          label: '未派车'
        }, {
          value: 1,
          label: '已派车'
        }, {
          value: 2,
          label: '已到达'
        }, {
          value: 3,
          label: '派车驳回'
        }, {
          value: 4,
          label: '已取消'
        }, {
          value: 5,
          label: '车辆异常'
        }
      ],
      realTableOptions:[],
      tableOptions: [
        {label: '出车单编号', prop: 'applyNo'},
        {label: '出车时间', prop: 'ccOpenTime',slot: true},
        {label: '结束时间', prop: 'executeTime',slot: true},
        {label: '目的地', prop: 'addressInfo'},
        {label: '人数', prop: 'applyNum', width: '70px'},
        {label: '上车地点', prop: 'scdText'},
        {label: '用车事由', prop: 'note'},
        {label: '申请人', prop: 'applyUserName'},
        {label: '用车人', prop: 'ycrName'},
        {label: '申请单位', prop: 'applyTopDeptName'},
        {label: '车辆类型', prop: 'carMold'},
        {label: '车牌号', prop: 'licensePlate'},
        {label: '驾驶员', prop: 'driverName'},
        {label: '审核状态', prop:'approveState',slot: true},
        {label: '执行状态', prop:'executeState',slot: true},
        {label: '实际开始时间', prop:'actualStartTime',slot: true},
        {label: '实际结束时间', prop:'actualEndTime',slot: true},
        {label: '车辆标识', prop:'carTag',slot: true},
        {label: '用户确认时间', prop:'arriveTime',slot: true},

      ],
      // 轨迹路径
      locus: {
        path: '/dzcc/locus',
        query: {
          carId: 0,
          startDate: '',
          endDate: ''
        }
      }
    }
  },
  components: {
    ccdxq,Table, Pagination, Dropdown
  },
  methods: {
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getCCDList()
    },
    /** 查询车队长 */
    getCCDList() {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.ccOpenTime = null
        this.queryParams.executeTime = null
      } else {
        this.queryParams.ccOpenTime = this.queryParams.time[0]
        this.queryParams.executeTime = this.queryParams.time[1]
      }
      GetCcdList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getCCDList()
    },
    /** 重置查询车队长 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getCCDList()
    },
    /** 单击表事件 */
    select(row) {
      this.locus.query.carId = row.carId
      this.locus.query.licensePlate = row.licensePlate
      this.locus.query.startDate = row.ccOpenTime.substring(0, 10)
      this.locus.query.endDate = row.ccOpenTime.substring(0, 10)
      this.selectID = row.id
      this.ccdParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.ccdParamsList = datas
      if (this.ccdParamsList.length == 1) {
        this.select(this.ccdParamsList[0])
      }
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.ccdParamsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '查看出车单详情'
            this.dialogType = 'show'
            this.ccdParams.isAvial = false
            this.ccdDialog = true
          }
          break
        case 'showGJ':
          if (this.ccdParamsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else if (this.ccdParams.carTag == 2) {
            this.$message.error('产权车辆不可查看轨迹！')
          } else {
            // this.$router.push(this.locus)
            const newpage = this.$router.resolve(this.locus)
            window.open(newpage.href, '_blank')
          }
          break
        case 'ccddownload':
          DownLoadCcd(this.queryParams).then((res) => {
            const fileName =  '出车单信息' + '.xlsx'
            downLoad(res, fileName)
          })
          break
        case 'ccdxqdownload':
          let ids = [];
          this.ccdParamsList.forEach(value => {
            ids.push(value.id)
          })
          // const params = {
          //   content: Encrypt(ids.join(',')),
          //   person: Encrypt(this.$store.getters.userid),
          //   ss: Encrypt("95zKdij-Mmn9-TEDWEM-jAcj5-jess-Zc4YW-hTp2d-MVP6SGm"),
          //   date: Encrypt(getDate(Date.now(), 'YYYY-MM-DD HH:mm:ss')),
          //   type: Encrypt('zip')
          // }
          const params = {
            content: ids.join(','),
            person: this.$store.getters.userid,
            type: 'zip'
          }
          DownCcdXqById(params).then((res) => {
            const fileName = '出车单详情.zip'
            downLoad(res, fileName)
          })
          break;
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.ccdDialog = false
    },
    /** 展示时间 */
    showTime(date) {
      if (date == null) {
        return '-'
      }
      return this.$dayjs(date).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
