<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">车辆类型：</span>
        <el-select v-model="queryParams.carType" clearable filterable placeholder="请选择车辆类型" @change="handleChange" style="width: 190px">
          <el-option
              v-for="item in carTypes"
              :key="item.id"
              :label="item.content"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">车辆品牌：</span>
        <el-input v-model="queryParams.brand" clearable placeholder="请输入车辆品牌" @change="handleChange" style="width: 190px"></el-input>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licencePlate" clearable placeholder="请输入车牌" @change="handleChange" style="width: 190px"></el-input>
        <span class="font-size14">产权单位：</span>
        <el-input v-model="queryParams.carDeptName" clearable placeholder="请输入产权单位" @change="handleChange" style="width: 190px"></el-input>
        <span class="font-size14">GPS识别号：</span>
        <el-input v-model="queryParams.GPSNum" clearable placeholder="请输入GPS识别号" @change="handleChange" style="width: 190px"></el-input>
        <span class="font-size14">车辆标识：</span>
        <el-select v-model="queryParams.carTag" clearable filterable placeholder="请选择车辆标识" @change="handleChange" style="width: 190px">
          <el-option
              :key="-1"
              label="全部"
              :value="-1">
          </el-option>
          <el-option
              :key="0"
              label="临租车辆"
              :value="0">
          </el-option>
          <el-option
              :key="1"
              label="特殊车辆"
              :value="1">
          </el-option>
          <el-option
              :key="2"
              label="产权车辆"
              :value="2">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">确认状态：</span>
        <el-select v-model="queryParams.isConfirm" clearable filterable placeholder="请选择确认状态" @change="handleChange" style="width: 190px">
          <el-option
              :key="-1"
              label="全部"
              :value="-1">
          </el-option>
          <el-option
              :key="1"
              label="已确认"
              :value="1">
          </el-option>
          <el-option
              :key="0"
              label="未确认"
              :value="0">
          </el-option>
        </el-select>
        <span class="font-size14">配置部门：</span>
        <el-select v-model="queryParams.groupid" clearable filterable placeholder="请选择配置部门" @change="handleChange" style="width: 190px">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">车型：</span>
        <el-input v-model="queryParams.carMold" clearable placeholder="请输入车型" @change="handleChange" style="width: 190px"></el-input>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-has-permi="['NDWCC01ZC01QX02']"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01ZC01QX03']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-delete"
            @click="openDialog('del')"
            v-has-permi="['NDWCC01ZC01QX04']"
        >删除
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01ZC01QX05']"
        >导出
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="openDialog('group')"
            v-has-permi="['NDWCC01ZC01QX06']"
        >配置单位
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
      >
        <template #carType="scope">
          {{ getCarType(scope.row.carType) }}
        </template>
        <template #licenseCheckDate="scope">
          {{ getDate()(scope.row.licenseCheckDate) }}
        </template>
        <template #isEnable="scope">
          <div v-if="scope.row.isEnable == 0">
            未启用
          </div>
          <div v-else>
            启用
          </div>
        </template>
        <template #confirmTime="scope">
          <div v-if="scope.row.confirmTime == null">

          </div>
          <div v-else>
            {{ getDate()(scope.row.confirmTime) }}
          </div>
        </template>
        <template #carTag="scope">
          <div v-if="scope.row.carTag == 0">
            临租车辆
          </div>
          <div v-else-if="scope.row.carTag == 1">
            特殊车辆
          </div>
          <div v-else-if="scope.row.carTag == 2">
            产权车辆
          </div>
          <div v-else>
            -
          </div>
        </template>
        <template #GPSEnableDate="scope">
          <div v-if="scope.row.GPSEnableDate == null">

          </div>
          <div v-else>
            {{ getDate()(scope.row.GPSEnableDate) }}
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        :visible="dialog"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
        width="800px"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="clxxParams"
          :model="clxxParams"
          :rules="clxxRules"
          label-width="160px"
          v-watermark="{label: watermark}"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="车辆类型" prop="carType">
              <el-select v-model="clxxParams.carType" style="width: 100%;" filterable placeholder="请选择车辆类型">
                <el-option
                    v-for="item in carTypes"
                    :key="item.id"
                    :label="item.content"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌" prop="licencePlate">
              <el-input size="mini" style="width: 100%;" v-model="clxxParams.licencePlate" :disabled="dialogType == 'edit'"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="车辆品牌" prop="brand">
              <el-input size="mini" style="width: 100%;" v-model="clxxParams.brand"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆品牌型号" prop="model">
              <el-input size="mini" style="width: 100%;" v-model="clxxParams.model"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="行驶证登记时间" prop="licenseCheckDate">
              <el-date-picker
                  v-model="clxxParams.licenseCheckDate"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产权单位" prop="carDeptName">
              <el-input size="mini" style="width: 100%;" v-model="clxxParams.carDeptName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="是否启用" prop="isEnable">
              <el-select size="mini" style="width: 100%;" v-model="clxxParams.isEnable" placeholder="请选择是否启用">
                <el-option
                    :key="1"
                    label="启用"
                    :value="1">
                </el-option>
                <el-option
                    :key="0"
                    label="未启用"
                    :value="0">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆标识" prop="carTag">
              <el-select size="mini" style="width: 100%;" v-model="clxxParams.carTag" placeholder="请选择车辆标识">
                <el-option
                    :key="0"
                    label="临租车辆"
                    :value="0">
                </el-option>
                <el-option
                    :key="1"
                    label="特殊车辆"
                    :value="1">
                </el-option>
                <el-option
                    :key="2"
                    label="产权车辆"
                    :value="2">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="GPS设备启用时间" prop="GPSEnableDate">
              <el-date-picker
                  v-model="clxxParams.GPSEnableDate"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交强险截止日期" prop="insuranceEndDate">
              <el-date-picker
                  v-model="clxxParams.insuranceEndDate"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="GPS识别号" prop="GPSNum">
              <el-input size="mini" style="width: 100%;" v-model="clxxParams.GPSNum"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车型" prop="carMold">
              <el-input size="mini" style="width: 100%;" v-model="clxxParams.carMold"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submit(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        :visible="dialogGroup"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialogGroup"
        :close-on-click-modal="false"
        width="800px"
    >
      <div slot="title" align="left" class="dialogTitle">配置单位({{ clxxParams.licencePlate }})</div>
      <div class="operate-pannel">
        <div class="search-box">
          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-plus"
              @click="openDialog('addGroup')"
              v-has-permi="['NDWCC01ZC01QX02']"
          >新增
          </el-button>
          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-edit"
              @click="openDialog('editGroup')"
              v-has-permi="['NDWCC01ZC01QX03']"
          >修改
          </el-button>
          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-delete"
              @click="openDialog('delGroup')"
              v-has-permi="['NDWCC01ZC01QX04']"
          >删除
          </el-button>
          <el-button
              size="mini"
              type="text"
              class="rygf"
              icon="el-icon-refresh-right"
              @click="getGroupList"
              v-has-permi="['NDWCC01ZC01QX01']"
          >刷新
          </el-button>
        </div>
      </div>
      <div class="table-box">
        <Table
            :tableData="carMoveList"
            :tableOptions="carMoveTableOptions"
            :loading="dialogGroupLoading"
            height="500px"
            @getCurrentData="selectGroup"
            @rowdblclick="openDialog('carMoreChild')"
        >
          <template #startDate="scope">
            {{ getDate()(scope.row.startDate) }}
          </template>
          <template #endDate="scope">
            <div v-if="scope.row.endDate == null">
            </div>
            <div v-else>
              {{ getDate()(scope.row.endDate) }}
            </div>
          </template>
          <template #groupId="scope">
            {{ getGroup(scope.row.groupId) }}
          </template>
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        :visible="dialogGroupShow"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialogGroupShow"
        :close-on-click-modal="false"
        width="800px"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogGroupTitle }}</div>
      <el-form
          ref="carMoveParams"
          :model="carMoveParams"
          :rules="carMoveRules"
          label-width="160px"
          v-watermark="{label: watermark}"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="车牌" prop="licencePlate">
              <el-input size="mini" style="width: 100%;" v-model="carMoveParams.licencePlate" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配置部门" prop="groupId">
              <el-select v-model="carMoveParams.groupId" clearable filterable placeholder="请选择配置部门" style="width: 100%;" :disabled="new Date(carMoveParams.startDate) < new Date()">
                <el-option
                    v-for="item in groupItem"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                  v-model="carMoveParams.startDate"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
                  :picker-options="pickerOptions"
                  :disabled="new Date(carMoveParams.startDate) < new Date()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                  v-model="carMoveParams.endDate"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%;"
                  :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submit(dialogGroupType)">保存</el-button>
        <el-button size="mini" @click="closeDialogGroupShow">关闭</el-button>
      </div>
    </el-dialog>


    <el-dialog
        :visible="dialogCarMoreChild"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialogCarMoreChild"
        :close-on-click-modal="false"
        width="800px"
    >
      <div slot="title" align="left" class="dialogTitle">配置单位({{ clxxParams.licencePlate }})</div>
      <div class="table-box">
        <Table
            :tableData="carMoreChildList"
            :tableOptions="carMoreChildTableOptions"
            :loading="dialogCarMoreChildLoading"
            height="500px"
            @getCurrentData="selectGroup"
        >
          <template #StartDate="scope">
            {{ getDate()(scope.row.StartDate) }}
          </template>
            <template #EndDate="scope">
            <div v-if="scope.row.EndDate == null">
            </div>
            <div v-else>
              {{ getDate()(scope.row.EndDate) }}
            </div>
          </template>
          <template #GroupId="scope">
            {{ getGroup(scope.row.GroupId) }}
          </template>
        </Table>
      </div>
    </el-dialog>

  </div>


</template>
<script>
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {
  AddCarMove,
  AddClxx, DeleteCarMoveById,
  DeleteClxxById,
  DownLoad, EditCarMove,
  EditClxx,
  GetCarMoveList,
  GetCarMoveChildList,
  GetClxxList,
  GetMoldDict
} from "api/dzcc/zctz/clxx";
import {getDate} from "@/utils/tool";

export default {
  name: 'clxx',
  components: {Table, Pagination, Dropdown},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getList()
    GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
      data = data.slice(1, data.length)
      this.dialogGroupData = data
    })
    GetMoldDict().then((res) => {
      this.carTypes = res.result
    })
  },
  data () {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        groupid: -1,
        carMold: null,
        brand: null,
        licencePlate: null,
        carDeptName: null,
        GPSNum: null,
        isConfirm: -1,
        carTag: -1
      },
      tableOptions: [
        {label: '车辆类型', prop: 'carType', tooltip: true, slot: true},
        {label: '车牌', prop: 'licencePlate', tooltip: true},
        {label: '车辆品牌', prop: 'brand', tooltip: true},
        {label: '车辆品牌型号', prop: 'model', tooltip: true},
        {label: '行驶证登记时间', prop: 'licenseCheckDate', tooltip: true, slot: true},
        {label: '产权单位', prop: 'carDeptName', tooltip: true},
        {label: 'GPS识别号', prop: 'GPSNum', tooltip: true},
        {label: '驾驶人', prop: 'driverName', tooltip: true},
        {label: '是否启用', prop: 'isEnable', tooltip: true, slot: true},
        {label: '确认人', prop: 'confirmUserName', tooltip: true},
        {label: '确认时间', prop: 'confirmTime', tooltip: true, slot: true},
        {label: '车辆标识', prop: 'carTag', tooltip: true, slot: true},
        {label: '设备启用时间', prop: 'GPSEnableDate', tooltip: true, slot: true},
        {label: '配置单位', prop: 'groupname', tooltip: true},
        {label: '车型', prop: 'carMold', tooltip: true},
      ],
      realTableOptions:[],
      // 查询部门列表
      groupItem: [],
      // 车辆类型列表
      carTypes: [],
      // 列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      clxxParams: {},
      dialogGroupData: [],
      dialogType: '',
      dialogTitle: '',
      dialog: false,
      clxxRules: {
        carType: [
          {
            required: true,
            message: '请选择车辆类型！',
            trigger: 'blur'
          }
        ],
        licencePlate: [
          {
            required: true,
            message: '请输入车牌',
            trigger: 'blur'
          }
        ],
        brand: [
          {
            required: true,
            message: '请输入车辆品牌',
            trigger: 'blur'
          }
        ],
        model: [
          {
            required: true,
            message: '请输入车辆品牌型号',
            trigger: 'blur'
          }
        ],
        licenseCheckDate: [
          {
            required: true,
            message: '请选择行驶证登记时间！',
            trigger: 'blur'
          }
        ],
        carDeptName: [
          {
            required: true,
            message: '请输入产权单位',
            trigger: 'blur'
          }
        ],
        isEnable: [
          {
            required: true,
            message: '请选择是否启用',
            trigger: 'blur'
          }
        ],
        carTag: [
          {
            required: true,
            message: '请选择车辆标识',
            trigger: 'blur'
          }
        ],
        GPSNum: [
          {
            required: true,
            message: '请输入GPS识别号',
            trigger: 'blur'
          }
        ],
        carMold: [
          {
            required: true,
            message: '请输入车型',
            trigger: 'blur'
          }
        ],
      },
      dialogGroup: false,
      carMoveList: [],
      carMoveTableOptions: [
        {label: '车牌', prop: 'licencePlate', tooltip: true},
        {label: '开始时间', prop: 'startDate', tooltip: true, slot: true},
        {label: '结束时间', prop: 'endDate', tooltip: true, slot: true},
        {label: '配置部门', prop: 'groupId', tooltip: true, slot: true},
      ],
      // 选择列表id
      selectCarMoveID: 0,
      // 选择列表数据
      carMoveParams: {},
      dialogGroupShow: false,
      dialogGroupLoading: false,
      dialogGroupTitle: '',
      dialogGroupType: '',
      carMoveRules: {
        groupId: [
          {
            required: true,
            message: '请选择配置部门！',
            trigger: 'blur'
          }
        ],
        startDate: [
          {
            required: true,
            message: '请选择开始时间',
            trigger: 'blur'
          }
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();//如果没有后面的-8.64e7就是不可以选择今天的
        }
      },
      dialogCarMoreChild:false,
      dialogCarMoreChildLoading:false,
      carMoreChildList:[],
      carMoreChildTableOptions: [
        {label: '车牌', prop: 'LicencePlate', tooltip: true},
        {label: '开始时间', prop: 'StartDate', tooltip: true, slot: true},
        {label: '结束时间', prop: 'EndDate', tooltip: true, slot: true},
        {label: '配置部门', prop: 'GroupId', tooltip: true, slot: true},
      ],
    }
  },
  methods: {
    getDate() {
      return getDate
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    getCarType(value) {
      for (let i in this.carTypes) {
        if(this.carTypes[i].id == value){
          return this.carTypes[i].content
        }
      }
    },
    getGroup(value) {
      for (let i in this.groupItem) {
        if(this.groupItem[i].id == value){
          return this.groupItem[i].groupname
        }
      }
    },

    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询车队长 */
    getList () {
      this.loading = true
      GetClxxList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 查询车队长 */
    getGroupList () {
      this.dialogGroupLoading = true
      GetCarMoveList({licencePlate: this.clxxParams.licencePlate}).then((res) => {
        this.carMoveList = res.result
      }).finally(() => {
        this.dialogGroupLoading = false
      })
    },

    /** 查询车队长 */
    getCarMoveChildList () {
      this.dialogCarMoreChildLoading = true
      GetCarMoveChildList({licencePlate: this.clxxParams.licencePlate}).then((res) => {
        this.carMoreChildList = res.result
      }).finally(() => {
        this.dialogCarMoreChildLoading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select (row) {
      if(!row){
        return
      }
      this.selectID = row.id
      this.clxxParams = row
    },
    selectGroup (row) {
      if(!row){
        return
      }
      this.selectCarMoveID = row.id
      this.carMoveParams = row
    },
    /** 打开弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增车辆信息'
          this.selectID = 0
          this.clxxParams = {}
          this.dialog = true
          break
        case 'edit':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑车辆信息'
            this.dialogType = 'edit'
            this.dialog = true
          }
          break
        case 'del':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              DeleteClxxById({
                id: this.selectID
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
              })
            }
          }
          break
        case 'download':
          DownLoad(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = '车辆信息' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'group':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogType = 'group'
            this.getGroupList()
            this.dialogGroup = true
          }
          break
        case 'addGroup':
          this.dialogGroupType = 'addGroup'
          this.dialogGroupTitle = '新增部门配置(' + this.clxxParams.licencePlate + ')'
          this.selectCarMoveID = 0
          this.carMoveParams = {
            licencePlate: this.clxxParams.licencePlate
          }
          this.dialogGroupShow = true
          break
        case 'editGroup':
          if (this.selectCarMoveID == 0) {
            this.$message.error('请选择行！')
          } else {
            if (this.carMoveParams.endDate != null && new Date(this.carMoveParams.endDate) < new Date()) {
              this.$message.error('结束时间已经过了当日时间不可修改！')
            } else {
              this.dialogGroupTitle = '编辑部门配置'
              this.dialogGroupType = 'editGroup'
              this.dialogGroupShow = true
            }
          }
          break
        case 'delGroup':
          if (this.selectCarMoveID == 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              DeleteCarMoveById({
                id: this.selectCarMoveID
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getGroupList()
              })
            }
          }
          break
        case 'carMoreChild':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogType = 'carMoreChild'
            this.getCarMoveChildList()
            this.dialogCarMoreChild = true
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getList()
      this.selectID = 0
      this.dialog = false
    },
    closeDialogGroup () {
      this.dialogGroup = false
    },
    closeDialogGroupShow () {
      this.selectCarMoveID = 0
      this.dialogGroupShow = false
    },
    closeDialogCarMoreChild () {
      this.dialogCarMoreChild = false
    },
    /** 提交新增 */
    submit (type) {
      switch (type) {
        case 'edit':
          this.$refs.clxxParams.validate((valid) => {
            if (valid) {
              EditClxx(this.clxxParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
        case 'add':
          this.$refs.clxxParams.validate((valid) => {
            if (valid) {
              AddClxx(this.clxxParams).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
        case 'editGroup':
          this.$refs.carMoveParams.validate((valid) => {
            if (valid) {
              EditCarMove(this.carMoveParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getGroupList()
                this.closeDialogGroupShow()
              })
            } else {
              return false
            }
          })
          break
        case 'addGroup':
          this.$refs.carMoveParams.validate((valid) => {
            if (valid) {
              AddCarMove(this.carMoveParams).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getGroupList()
                this.closeDialogGroupShow()
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 190px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 120px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
