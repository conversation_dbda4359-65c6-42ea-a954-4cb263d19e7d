<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.topGroupId" placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.realName" clearable placeholder="请输入姓名"
                  @change="handleChange"></el-input>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getDriverList"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('add')"
            v-has-permi="['NDWCC01SJ01QX01']"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01SJ01QX02']"
        >修改
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog
        width="30%"
        :visible="driverDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="driverParams"
          :model="driverParams"
          :rules="driverRules"
          label-width="100px"
          v-if="dialogType === 'edit'"
          v-watermark="{label:watermark}"
      >
        <el-row>
          <el-col :span="18">
            <el-form-item label="姓名" prop="RealName">
              <el-input :disabled="true" size="mini" v-model="driverParams.RealName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="18">
            <el-form-item label="手机长号" class="force-width-60" prop="Telephone">
              <el-input size="mini" v-model="driverParams.Telephone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="18">
            <el-form-item label="手机短号" prop="sphone">
              <el-input size="mini" v-model="driverParams.sphone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <el-form
          ref="driverParams"
          :model="driverParams"
          :rules="driverRules"
          label-width="100px"
          v-else
          v-watermark="{label:watermark}"
      >
        <el-row>
          <el-col :span="18">
            <el-form-item label="姓名" prop="RealName">
              <el-input  size="mini" v-model="driverParams.RealName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="18">
            <el-form-item label="身份证" class="force-width-60" prop="CertificateID">
              <el-input size="mini" v-model="driverParams.CertificateID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="18">
            <el-form-item label="管理部门" class="force-width-60" prop="GroupID">
              <el-select style="width: 100%;" v-model="driverParams.GroupID" placeholder="请选择部门">
                <el-option
                    v-for="item in groupItemAdd"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="18">
            <el-form-item label="手机长号" class="force-width-60" prop="Telephone">
              <el-input size="mini" v-model="driverParams.Telephone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="18">
            <el-form-item label="手机短号" prop="sphone">
              <el-input size="mini" v-model="driverParams.sphone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { mapActions } from 'vuex'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import { EditDriverPhone, GetDriverList, InsertDriver } from '@/api/dzcc/ryqx/sjqx'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
export default {
  name: 'sjqx',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, Dropdown},
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getDriverList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
    GetGroupList({
      isShowAll: 0,
      parentId: 1
    }).then((res) => {
      this.groupItemAdd = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        realName: '',
        topGroupId: -1
      },
      // 查询部门列表
      groupItem: [],
      groupItemAdd: [],
      // 人员列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      driverParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      driverDialog: false,
      // 表单校验规则
      driverRules: {
        Telephone: [
          {
            required: true,
            message: '请输入手机号码',
            trigger: 'blur'
          },
          {
            min: 11,
            max: 11,
            message: '请输入11位手机号码',
            trigger: 'blur'
          },
          {
            pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
            message: '请输入正确的手机号码'
          }
        ],
        RealName: [
          {
            required: true,
            message: '请输入姓名',
            trigger: 'blur'
          }
        ],
        CertificateID: [
          { required: true, message: '请填写证件号码', trigger: 'blur' },
          {
            pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: '请输入正确的证件号码格式！',
            trigger: 'blur'
          }
        ],
        GroupID: [
          {
            required: true,
            message: '请选择部门',
            trigger: 'blur'
          }
        ]
      },
      realTableOptions: [],
      tableOptions: [
        {label: '登录名', prop: 'LoginName'},
        {label: '姓名', prop: 'RealName'},
        {label: '部门', prop: 'TopGroupName'},
        {label: '手机长号', prop: 'Telephone'},
        {label: '手机短号', prop: 'sphone'},
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getDriverList()
    },
    /** 人员查询 */
    getDriverList () {
      this.loading = true
      GetDriverList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getDriverList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getDriverList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.driverParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增司机'
          this.selectID = 0
          this.driverParams = {}
          this.driverDialog = true
          break
        case 'edit':
          // if (this.$store.getters.permissions.indexOf('NDWCC01SJ01QX02') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑司机手机号'
            this.dialogType = 'edit'
            this.driverDialog = true
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getDriverList()
      this.driverDialog = false
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'add':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              InsertDriver(this.driverParams).then(res => {

                if (res.code == 200){
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getDriverList()
                  this.closeDialog()
                }else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
        case 'edit':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              EditDriverPhone(this.driverParams).then(res => {
                if (res.code ==200){
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getDriverList()
                  this.closeDialog()
                }else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>

