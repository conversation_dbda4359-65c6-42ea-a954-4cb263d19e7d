<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">日期：</span>
        <el-date-picker
            type="date"
            placeholder="日期"
            v-model="queryParams.time"
            value-format="yyyy-MM-dd"
            @change="handleChange"
        ></el-date-picker>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="GetList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-has-permi="['NDWCC01JJR01QX01']"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01JJR01QX02']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-delete"
            @click="del()"
            v-has-permi="['NDWCC01JJR01QX03']"
        >删除
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="25%"
        :visible="driverDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="driverParams"
          :model="driverParams"
          :rules="Rules"
          :inline="true"
          label-width="100px"
          v-watermark="{label:watermark}"
      >
        <el-form-item label="开始日期" class="force-width-60" prop="startDate">
          <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="driverParams.startDate"
              style="width: 100%;"
              value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束日期" class="force-width-60" prop="endDate">
          <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="driverParams.endDate"
              style="width: 100%;"
              value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import { mapActions } from 'vuex'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import { GetList, EditHoilday, InsertHoilday, DelHoilday } from '@//api/dzcc/ryqx/jjrpz'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'sjqx',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, Dropdown},
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.GetList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        time: null
      },
      // 查询部门列表
      groupItem: [],
      // 人员列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      driverParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      driverDialog: false,
      // 表单校验规则
      Rules: {
        startDate: [
          {
            required: true,
            message: '请选择开始日期',
            trigger: 'blur'
          }
        ],
        endDate: [
          {
            required: true,
            message: '请选择结束日期',
            trigger: 'blur'
          }
        ]
      },
      realTableOptions: [],
      tableOptions: [
        {label: '开始日期', prop: 'startDate'},
        {label: '结束日期', prop: 'endDate'},
        {label: '创建人', prop: 'createUserName'},
        {label: '创建时间', prop: 'createTime'}
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.GetList()
    },
    /** 人员查询 */
    GetList () {
      this.loading = true
      GetList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.GetList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.GetList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.driverParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增节假日'
          this.selectID = 0
          this.driverParams = {}
          this.driverDialog = true
          break
        case 'edit':
          if (this.$store.getters.permissions.indexOf('NDWCC01JJR01QX02') == -1) {
            this.$message.error('该用户无修改权限！')
            return
          }
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑节假日'
            this.dialogType = 'edit'
            this.driverDialog = true
          }
          break
      }
    },
    del () {
      if (this.selectID === 0) {
        this.$message.error('请选择行！')
      } else {
        DelHoilday(this.driverParams).then(() => {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.GetList()
        })
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.GetList()
      this.driverDialog = false
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'add':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              const startDate1 = new Date(this.driverParams.startDate)
              const endDate1 = new Date(this.driverParams.endDate)
              if (startDate1 > endDate1) {
                this.$message({
                  type: 'error',
                  message: '开始日期需小于结束日期'
                })
                return false
              } else {
                InsertHoilday(this.driverParams).then(() => {
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.GetList()
                  this.closeDialog()
                })
              }
            } else {
              return false
            }
          })
          break
        case 'edit':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              const startDate1 = new Date(this.driverParams.startDate)
              const endDate1 = new Date(this.driverParams.endDate)
              if (startDate1 > endDate1) {
                this.$message({
                  type: 'error',
                  message: '开始日期需小于结束日期'
                })
                return false
              } else {
                EditHoilday(this.driverParams).then(() => {
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.GetList()
                  this.closeDialog()
                })
              }
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
