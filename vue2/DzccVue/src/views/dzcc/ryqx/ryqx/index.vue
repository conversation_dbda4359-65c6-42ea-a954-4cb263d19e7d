<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.GroupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14"> 姓名：</span>
        <el-input v-model="queryParams.RealName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getPersonList"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01RY01QX01']"
        >修改
        </el-button>

        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="20%"
        :visible="personDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="personParams"
          :model="personParams"
          :inline="true"
          label-width="auto"
          v-loading="roleLoading"
          v-watermark="{label:watermark}"
      >
        <el-form-item label="权限分配" class="force-width-60" prop="cdzId">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选
          </el-checkbox>
          <div style="margin: 15px 0;"></div>
          <el-checkbox-group v-model="checkedRoles" @change="handleCheckedRolesChange">
            <el-checkbox v-for="role in roles" :label="role.RoleName" :key="role.Id">{{
                role.RoleName
              }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import { mapActions } from 'vuex'
import { EditRoles, GetGroupList, GetPersonList, GetPersonRoleById, GetRoles } from '@/api/dzcc/ryqx/ryqx'
import { array2Tree } from '@/utils/tool'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'ryqx',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, Dropdown},
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getPersonList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      // this.groupItem = array2Tree(res.result.map(item => ({
      //   value: item.id,
      //   label: item.groupname,
      //   pid: item.parentid,
      //   id: item.id
      // })), 1)
      this.groupItem = res.result
    })
    GetRoles().then((res) => {
      this.roles = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        RealName: '',
        GroupId: -1
      },
      // 查询部门列表
      groupItem: [],
      // 人员列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      personParams: {},
      dialogType: '',
      dialogTitle: '',
      personDialog: false,
      checkAll: false,
      checkedRoles: [],
      roles: [],
      isIndeterminate: true,
      roleLoading: false,
      realTableOptions: [],
      tableOptions: [
        {label: '登录名', prop: 'LoginName'},
        {label: '姓名', prop: 'RealName'},
        {label: '手机号', prop: 'Telephone'},
        {label: '部门', prop: 'GroupName'}
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getPersonList()
    },
    handleDeptChange () {
      this.queryParams.pageNum = 1
      //this.queryParams.RealName = ''
      this.handleChange()
    },
    handleCheckAllChange (val) {
      this.checkedRoles = val ? this.roles.map(item => (item.RoleName)) : []
      this.isIndeterminate = false
    },
    handleCheckedRolesChange (value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.roles.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.roles.length
    },
    /** 人员查询 */
    getPersonList () {
      this.loading = true
      const groupId = this.queryParams.GroupId
      //this.queryParams.GroupId = this.queryParams.GroupId[this.queryParams.GroupId.length - 1]
      GetPersonList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.queryParams.GroupId = groupId
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getPersonList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getPersonList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.personParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'edit':
          if (this.$store.getters.permissions.indexOf('NDWCC01RY01QX01') == -1) {
            this.$message.error('该用户无修改权限！')
            return
          }
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑人员权限'
            this.dialogType = 'edit'
            this.roleLoading = true
            GetPersonRoleById({ id: this.selectID }).then((res) => {
              if (res.result == 'undefined' || res.result == undefined) {
                this.checkedRoles = []
              } else {
                this.checkedRoles = res.result.map(item => (item.RoleName))
              }
            }).finally(() => {
              this.roleLoading = false
            })
            this.personDialog = true
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.personDialog = false
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'edit':
          // eslint-disable-next-line no-case-declarations
          let ids = ''
          this.roles.forEach(value => {
            this.checkedRoles.forEach(value1 => {
              if (value1 === value.RoleName) {
                ids += value.Id + ','
              }
            })
          })
          if (ids !== '') {
            ids.substring(0, ids.length - 2)
          }
          EditRoles({
            ids: ids,
            personId: this.selectID
          }).then(() => {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getPersonList()
            this.closeDialog()
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
    .el-cascader{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
