<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.driveName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <span class="font-size14">月份：</span>
        <el-date-picker v-model="queryParams.startDate" clearable type="month" placeholder="选择月" value-format="yyyy-MM-dd" :clearable="false" @change="handleMonthChange"></el-date-picker>
        <span class="font-size14">周期：</span>
        <el-select v-model="queryParams.week" collapse-tags placeholder="请选择周期" @change="handleChange">
          <el-option
              v-for="item in monthlyCycleList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          >
          </el-option>
        </el-select>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-download" @click="handleExport" v-has-permi="['NDWCC01GZL01QX02']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :needSelect="false"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          :span-method="handleSpanMethod"
          @getCurrentData="select"
          @getSelectionData="selectionData"

      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
          :page-sizes="[15, 30, 45, 60]"
      />
    </div>

  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {downLoad, formatMonth, getMonthLast} from "@/utils/tool";
import Sjqdxq from '@/components/DZCC/Sjqdxq'
import {getPageList, getWeeks,getWorkSummaryList, exportWorkSummary} from "../../../../api/dzcc/jtsj/yssqlb";
import {numberToChinese} from "../../../../utils/tool";

export default {
  name: 'cdgzl',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    this.getWeeklyList()
  },
  components: {Table, Pagination, Dropdown, Sjqdxq},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getList()
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        total: 0,
        driveName: '',
        startDate: formatMonth(),
        week: 1,
        year: formatMonth().split('-')[0],
        month: formatMonth().split('-')[1],
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      driverParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      dialog: false,
      // 表单校验规则
      driverRules: {},
      realTableOptions: [],
      tableOptions: [
        {label: '司机姓名', prop: 'driveName'},
        {label: '车牌', prop: 'licencePlate'},
        {label: '初始延时', prop: 'oringOvertimeHours'},
        {label: '增加延时', prop: 'appendDelayHour'}
      ],
      // 多选列表
      paramsList: [],
      // 周期列表
      monthlyCycleList: [],
      xqDialog: false,
      // 添加合并相关的数据
      mergeObj: {}, // 存储合并信息
      mergeArr: ['driveName'], // 需要合并的列名
      columnIndexs: [], // 需要合并的列索引
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleMonthChange() {
      this.queryParams.endDate = getMonthLast(this.queryParams.startDate);
      this.queryParams.month = '';
      this.queryParams.week = '';
      this.queryParams.year = '';
      if(this.queryParams.startDate){
        let year = this.queryParams.startDate.split('-')[0];
        let month = this.queryParams.startDate.split('-')[1];
        this.queryParams.year = year;
        this.queryParams.month = month;
        this.getWeeklyList()
      }

      this.handleChange()
    },
    //获取当前年月的周期列表
    getWeeklyList(){
      getWeeks({year: this.queryParams.year, month:this.queryParams.month}).then(res=>{
        this.monthlyCycleList = [];
        //console.log(res.result)
        if(res.result.length > 0){
          res.result.forEach(item=>{
            this.monthlyCycleList.push( {label:"第"+numberToChinese(item.week)+"周", value:item.week})
          })
        }
      })
    },

    /** 数据查询 */
    getList() {
      // 添加检查逻辑
      if (!this.queryParams.startDate) {
        this.$message.error('请选择月份！')
        return
      }
      if (!this.queryParams.week) {
        this.$message.error('请选择周期！')
        return
      }

      this.loading = true
      this.list = [] // 在请求前清空列表
      this.mergeObj = {} // 重置合并对象
      getWorkSummaryList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
        // 计算合并信息
        if (this.list && this.list.length > 0) {
          this.getSpanArr(this.list)
        }
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.defaultForm.pageNum = 1
      this.defaultForm.pageSize = this.queryParams.pageSize
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.driverParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0])
      }
    },

    // 添加计算合并信息的方法
    getSpanArr(data) {
      this.mergeArr.forEach((key) => {
        let count = 0 // 记录合并起始位置
        this.mergeObj[key] = [] // 存储每列的合并信息

        data.forEach((item, index) => {
          if (index === 0) {
            this.mergeObj[key].push(1) // 第一行push 1
          } else {
            // 当前行与上一行值相同时
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1 // 合并行数+1
              this.mergeObj[key].push(0) // 被合并行push 0
            } else {
              count = index // 记录新的合并起始位置
              this.mergeObj[key].push(1) // 新的合并组push 1
            }
          }
        })
      })

      // 计算overtime和appendDelayHour的合并
      this.list.forEach((value, index) => {
        if (this.mergeObj.driveName[index] > 0) {
          value.overtimeTotal = 0
          value.appendDelayHourTotal = 0
          // 累加同一司机的overtime和appendDelayHour
          for (let i = index; i < index + this.mergeObj.driveName[index]; i++) {
            value.overtimeTotal += Number(this.list[i].oringOvertimeHours || 0)
            value.appendDelayHourTotal += Number(this.list[i].appendDelayHour || 0)
          }
        }
      })
    },

    // 添加合并方法
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 根据列名确定是否需要合并
      if (column.property === 'driveName' ||
          column.property === 'oringOvertimeHours' ||
          column.property === 'appendDelayHour') {
        const value = 'driveName'
        if (this.mergeObj[value]) {
          if (this.mergeObj[value][rowIndex]) {
            return [this.mergeObj[value][rowIndex], 1]
          } else {
            return [0, 0]
          }
        }
      }
    },
    // 添加导出方法
    handleExport() {
      // 使用与查询列表相同的参数
      exportWorkSummary(this.queryParams).then(res => {
        const blob = res
        // 构造文件名
        const fileName = `工作量统计表(${this.queryParams.startDate.substring(0, 7)})${this.queryParams.week ? '-第' + this.queryParams.week + '周' : ''}.xlsx`

        // 处理文件下载
        if ('download' in document.createElement('a')) {
          // 支持a标签download的浏览器
          const link = document.createElement('a')
          link.download = fileName
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        } else {
          // 其他浏览器
          navigator.msSaveBlob(blob, fileName)
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}


.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}

::v-deep .el-table .cell {
  white-space: pre-line; // 保留换行符
  line-height: 1.5;
}
</style>
