<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.topGroupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.realName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licencePlate" clearable placeholder="请输入车牌"
                  @change="handleChange"></el-input>
        <span class="font-size14">选择月份：</span>
        <el-date-picker
            v-model="queryParams.yearMonth"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange"
        >
        </el-date-picker>
        <!--        <span class="font-size14">排序方式：</span>-->
        <!--        <el-select v-model="queryParams.carOrPerson" placeholder="请选择管理部门" @change="handleChange">-->
        <!--          <el-option-->
        <!--              v-for="item in carPerson"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value">-->
        <!--          </el-option>-->
        <!--        </el-select>-->
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-button type="text" icon="el-icon-search" @click="getZbbList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <!--        <el-button type="text" icon="el-icon-view" @click="openDialog('fbbshow')" v-has-permi="['NDWCC01JBF01QX01']">查看详情</el-button>-->
        <el-button type="text" icon="el-icon-download" @click="openDialog('fbbdownload')" v-has-permi="['NDWCC01JBF01QX02']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="fbbList"
          show-summary
          :tableData="fbbList"
          :tableOptions="realTableOptions"
          @cell-dblclick="celldblClick"
          :loading="fbbLoading"
          :queryParam="queryParams"
          :stripe="false"
          :span-method="objectSpanMethod"
          @getCurrentData="fbbSelect"
      >
        <template #MONTH="scope">
          <div v-if="scope.row.MONTH < 10">
            {{ scope.row.YEAR }}-0{{ scope.row.MONTH }}
          </div>
          <div v-else>
            {{ scope.row.YEAR }}-{{ scope.row.MONTH }}
          </div>
        </template>
      </Table>
      <Pagination
          :total="queryParams.total"
          :queryParam="queryParams"
          @handleRefresh="handleCurrentChange"
      />
    </div>

    <el-dialog
        width="50%"
        :visible="fbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closefbbDialog"
        :close-on-click-modal="false">
      <div slot="title" align="left" class="dialogTitle">{{ fbbDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('sbbshow')" v-has-permi="['NDWCC01JBF01QX01']">查看详情</el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('sbbdownload')" v-has-permi="['NDWCC01JBF01QX02']">导出</el-button>
            <Dropdown :columnArr="tableOptionsSbb" @getNewArr="getNewArrSbb"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="sbbList"
            :tableData="sbbList"
            :tableOptions="realTableOptionsSbb"
            :loading="sbbLoading"
            height="400"
            @getCurrentData="sbbSelect"
            @rowdblclick="openDialog('sbbshow')">
          <!--          <template #moveDate="scope">-->
          <!--            {{ getDate()(scope.row.moveDate) }}-->
          <!--          </template>-->
          <template #dayType="scope">
            <div v-if="scope.row.dayType == 0">休息日</div>
            <div v-else-if="scope.row.dayType == 1">工作日</div>
            <div v-else-if="scope.row.dayType == 2">节假日</div>
            <div v-else>--</div>
          </template>
          <!--          <template #licencePlate="scope">-->
          <!--            {{ fbbParams.LicencePlate }}-->
          <!--          </template>-->
          <template #dayCount="scope">
            1
          </template>
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="60%"
        :visible="sbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closesbbDialog"

        :close-on-click-modal="false">
      <div slot="title" align="left" class="dialogTitle">{{ sbbDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('tbbshow')" v-has-permi="['NDWCC01JBF01QX01']">查看详情</el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('tbbdownload')" v-has-permi="['NDWCC01JBF01QX02']">导出</el-button>
            <Dropdown :columnArr="tableOptionsTbb" @getNewArr="getNewArrTbb"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="tbbList"
            :tableData="tbbList"
            :tableOptions="realTableOptionsTbb"
            :loading="tbbLoading"
            height="420"
            @getCurrentData="tbbSelect"
            @rowdblclick="openDialog('tbbshow')">
          <template #actualStartTime="scope">
            {{ getDate()(scope.row.actualStartTime, 'YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template #actualEndTime="scope">
            {{ getDate()(scope.row.actualEndTime, 'YYYY-MM-DD HH:mm:ss') }}
          </template>
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="tbbDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="closetbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ tbbDialogTitle }}</div>
      <ccdxq :ccd-params="tbbParams" v-if="tbbDialog"></ccdxq>
    </el-dialog>
    <el-dialog
        width="45%"
        :visible="ccdStatusDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeCcdStatusDialog"
        :close-on-click-modal="false">
      <div slot="title" align="left" class="dialogTitle">{{ ccdStatusTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('sbbshow')" v-has-permi="['NDWCC01JBF01QX01']">查看详情</el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('ccdStatusdownload')" v-has-permi="['NDWCC01JBF01QX02']">导出</el-button>
            <Dropdown :columnArr="tableOptionsCcdStatus" @getNewArr="getNewArrCcdStatus"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="ccdStatusList"
            :tableData="ccdStatusList"
            :tableOptions="realTableOptionsCcdStatus"
            :loading="ccdStatusLoading"
            height="400"
            @getCurrentData="ccdStatus"
            @rowdblclick="openDialog('sbbshow')"
        >
        </Table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import {formatMonth, getDate} from '@/utils/tool'
import {
  GetZbbList,
  DownLoadZbb,
  GetCCLCDetailList, GetCcdByDriveIdAndTime, DownLoadSbb, DownLoadTbb, GetCcdStatusDaily, DownCcdStatusDaily
} from '@/api/dzcc/bb/jbfbb'

import ccdxq from '@/components/DZCC/ccdxq'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

const d = new Date()
export default {
  name: 'jtjbf',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted() {
    // GetGroupList({isShowAll: 1, parentId: 1}).then((res) => {
    //   if (res.result !== undefined) {
    //     this.groupItem = res.result
    //     this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    //   }
    // })
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    this.getZbbList()
  },
  data() {
    return {
      tableOptions: [
        {label: '月份', prop: 'MONTH', width: '100', slot: true},
        {label: '申请单位', prop: 'TopGroupName', width: '120'},
        {label: '车型', prop: 'carMold', width: '120'},
        {label: '车牌', prop: 'LicencePlate', width: '100'},
        {label: '司机', prop: 'RealName', width: '100'},
        {label: '工作里程', prop: 'workValue', width: '120'},
        {label: '车辆里程', prop: 'allValue', width: '120'},
        {label: '公里津贴', prop: 'mileageCost', width: '120'},
        {label: '加班天数(双休日)', prop: 'sxrOvertimeDays', width: '150'},
        {label: '加班天数(节假日)', prop: 'jjrOvertimeDays', width: '150'},
        {label: '加班费合计', prop: 'overtimeCost', width: '150'},
        {label: '延时小时(工作日)', prop: 'gzrDelayHour', width: '150'},
        {label: '延时小时(双休日)', prop: 'sxrDelayHour', width: '150'},
        {label: '延时小时(节假日)', prop: 'jjrDelayHour', width: '150'},
        {label: '延时津贴', prop: 'delayCost', width: '120'},
        {label: '夜餐台班数', prop: 'nightShiftDays', width: '150'},
        {label: '夜餐金额', prop: 'nightShiftCost', width: '120'},
        {label: '出差补贴', prop: 'travelAllowance', width: '120'},
        {label: '合计', prop: 'allCost', width: '100'},
        {label: '出大市', prop: 'cdsNum', width: '100'},
        {label: '跨区域', prop: 'kqyNum', width: '100'},
        {label: '本区域', prop: 'bqyNum', width: '100'},
        {label: '鄞州', prop: 'yzNum', width: '100'}
      ],
      realTableOptions: [],
      tableOptionsSbb: [
        {label: '日期', prop: 'dateTemp', width: '180'},
        {label: '天数类型', prop: 'dayType', slot: true},
        {label: '车牌', prop: 'licencePlate'},
        {label: '总加班时长', prop: 'overtimeHoursAll'},
      ],
      realTableOptionsSbb: [],
      tableOptionsTbb: [
        {label: '出车单编号', prop: 'applyNo'},
        {label: '车牌', prop: 'licencePlate', width: '100'},
        {label: '实际开始时间', prop: 'actualStartTime', slot: true},
        {label: '实际结束时间', prop: 'actualEndTime', slot: true},
        {label: '申请人', prop: 'applyUserName', width: '100'},
        {label: '用车人', prop: 'ycrName', width: '100'},
        {label: '目的地', prop: 'addressInfo'},
        {label: '出车情况', prop: 'status', width: '100'},
      ],
      realTableOptionsTbb: [],
      tableOptionsLock: [
        {label: '日期', prop: 'date'},
        {label: '操作', prop: 'operate', slot: true},
      ],
      realTableOptionsLock: [],
      tableOptionsCcdStatus: [
        {label: '日期', prop: 'movedate'},
        {label: '出大市', prop: 'cdsNum'},
        {label: '跨区域', prop: 'kqyNum'},
        {label: '本区域', prop: 'bqyNum'},
        {label: '鄞州', prop: 'yzNum'},
      ],
      realTableOptionsCcdStatus: [],
      // 初始打开的tabs页面
      editableTabsValue: '1',
      // 可编辑tabs页
      editableTabs: [
        {
          title: '浙B2229H-马梦娜(2022年10月)出车情况',
          name: '2'
        }
      ],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        realName: '',
        topGroupId: 373,
        licencePlate: '',
        yearMonth: formatMonth(d.getTime()),
        carOrPerson: 1,
        type: 1 // 区分报表和属地化管理
      },
      // 统计方式
      carPerson: [
        {
          value: 0,
          label: '按车排序'
        }, {
          value: 1,
          label: '按人排序'
        }
      ],
      // 查询部门列表
      groupItem: [
        {id: 373, groupname: '永耀集团'}
      ],
      // 主报表列表
      fbbList: [],
      // 主报表遮罩层
      fbbLoading: false,
      // 次报表列表
      sbbList: [],
      // 次报表遮罩层
      sbbLoading: false,
      // 三报表列表
      tbbList: [],
      // 三报表遮罩层
      tbbLoading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 主报表窗口
      fbbDialog: false,
      // 选择主报表列表id
      fbbSelectID: 0,
      // 选择主报表列表数据
      fbbParams: {},
      // 主报表窗口标题
      fbbDialogTitle: '',
      // 次报表窗口标题
      sbbDialogTitle: '',
      // 次报表窗口
      sbbDialog: false,
      // 选择次报表列表id
      sbbSelectID: 0,
      // 选择次报表列表数据
      sbbParams: {},
      // 三报表窗口标题
      tbbDialogTitle: '',
      // 三报表窗口
      tbbDialog: false,
      // 选择三报表列表id
      tbbSelectID: 0,
      // 选择三报表列表数据
      tbbParams: {},
      // 锁定窗口
      lockDialog: false,
      // 锁定窗口标题
      lockDialogTitle: '',
      // 锁定列表
      lockList: [],
      // 锁定窗口
      lockLoading: false,
      // 用来记录需要合并行的下标
      mergeObj: {},
      // 表格中的列名
      mergeArr: ['driveId', 'carId'],
      // 表格中合并的列数
      columnIndexs: [-1, -1],
      // 出车单情况报表
      ccdStatusDialog: false,
      // 出车单情况标题
      ccdStatusTitle: '',
      // 出车单情况列表
      ccdStatusList: [],
      // 出车单情况遮罩层
      ccdStatusLoading: false,
    }
  },
  components: {
    ccdxq,
    Table,
    Pagination,
    Dropdown,
  },
  methods: {
    getDate() {
      return getDate
    },
    celldblClick(row, column, cell, event) {
      console.log(column.label)
      this.tableOptionsSbb = [
        {label: '日期', prop: 'dateTemp', width: '180'},
        {label: '天数类型', prop: 'dayType', slot: true},
        {label: '车牌', prop: 'licencePlate'}
      ]
      if (column.label == '延时津贴' || column.label == '延时小时(节假日)' || column.label == '延时小时(双休日)'
          || column.label == '延时小时(工作日)') {
        this.fbbParams.selectType = 2
        this.tableOptionsSbb.push({label: '总加班时长', prop: 'overtimeHoursAll'})
        this.tableOptionsSbb.push({label: '上午加班时长', prop: 'overtimeHoursAm'})
        this.tableOptionsSbb.push({label: '晚上加班时长', prop: 'overtimeHoursPm'})
        this.openDialog("fbbshow")
      } else if (column.label == '加班费合计' || column.label == '加班天数(节假日)' || column.label == '加班天数(双休日)') {
        this.fbbParams.selectType = 1
        this.tableOptionsSbb.push({label: '天数', prop: 'dayCount', slot: true})
        this.openDialog("fbbshow")
      } else if (column.label == '夜餐台班数' || column.label == '夜餐金额') {
        this.fbbParams.selectType = 3
        this.tableOptionsSbb.push({label: '天数', prop: 'dayCount', slot: true})
        this.openDialog("fbbshow")
      } else if (column.label == '出差补贴') {
        this.fbbParams.selectType = 4
        this.tableOptionsSbb.push({label: '补贴金额', prop: 'travelAllowance'})
        this.openDialog("fbbshow")
      } else if (column.label == '出大市' || column.label == '跨区域' || column.label == '本区域' || column.label == '鄞州') {
        this.openDialog("ccdStatus")
      }
    },
    // tabs页删除
    removeTab(targetName) {
      const tabs = this.editableTabs
      let activeName = this.editableTabsValue
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }
      this.editableTabsValue = activeName
      this.editableTabs = tabs.filter(tab => tab.name !== targetName)
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getZbbList()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getZbbList()
    },
    tableFbbRowClassName() {
      return 'background_white'
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (this.columnIndexs.indexOf(columnIndex) !== -1) {
        let value = 'driveId'
        // 判断列的属性
        if (this.mergeArr.indexOf(value) !== -1) {
          // 判断其值是不是为0
          if (this.mergeObj[value][rowIndex]) {
            return [this.mergeObj[value][rowIndex], 1]
          } else {
            // 如果为0则为需要合并的行
            return [0, 0]
          }
        }
      }
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
      this.columnIndexs = [-1, -1]
      this.realTableOptions.forEach((value, index) => {
        if (value.prop === 'RealName') {
          if (this.queryParams.carOrPerson === 1 && index > 0 && this.realTableOptions[index - 1].prop === 'LicencePlate') {
            this.realTableOptions[index] = this.realTableOptions.splice(index - 1, 1, this.realTableOptions[index])[0];
          }
        } else if (value.prop === 'allValue') {
          value.label = '工作里程'
        } else if (value.prop === 'workValue') {
          value.label = '车辆里程'
        }
      })
      this.realTableOptions.forEach((value, index) => {
        if (value.prop === 'RealName' && this.queryParams.carOrPerson === 1) {
          this.columnIndexs[0] = index + 1
        } else if (value.prop === 'allValue') {
          this.columnIndexs[1] = index + 1
        } else if (value.prop === 'mileageCost') {
          this.columnIndexs[2] = index + 1
        } else if (value.prop === 'sxrOvertimeDays') {
          this.columnIndexs[3] = index + 1
        } else if (value.prop === 'MONTH') {
          this.columnIndexs[4] = index + 1
        } else if (value.prop === 'TopGroupName') {
          this.columnIndexs[5] = index + 1
        } else if (value.prop === 'jjrOvertimeDays') {
          this.columnIndexs[6] = index + 1
        } else if (value.prop === 'overtimeCost') {
          this.columnIndexs[7] = index + 1
        } else if (value.prop === 'gzrDelayHour') {
          this.columnIndexs[8] = index + 1
        } else if (value.prop === 'sxrDelayHour') {
          this.columnIndexs[9] = index + 1
        } else if (value.prop === 'jjrDelayHour') {
          this.columnIndexs[10] = index + 1
        } else if (value.prop === 'delayCost') {
          this.columnIndexs[11] = index + 1
        } else if (value.prop === 'nightShiftDays') {
          this.columnIndexs[12] = index + 1
        } else if (value.prop === 'nightShiftCost') {
          this.columnIndexs[13] = index + 1
        } else if (value.prop === 'travelAllowance') {
          this.columnIndexs[14] = index + 1
        } else if (value.prop === 'allCost') {
          this.columnIndexs[15] = index + 1
        } else if (value.prop === 'carMold') {
          this.columnIndexs[16] = index + 1
        } else if (value.prop === 'cdsNum') {
          this.columnIndexs[17] = index + 1
        } else if (value.prop === 'kqyNum') {
          this.columnIndexs[18] = index + 1
        } else if (value.prop === 'bqyNum') {
          this.columnIndexs[19] = index + 1
        } else if (value.prop === 'yzNum') {
          this.columnIndexs[20] = index + 1
        }
      })
    },
    getNewArrSbb(newTableOptions) {
      this.realTableOptionsSbb = [...newTableOptions]
    },
    getNewArrTbb(newTableOptions) {
      this.realTableOptionsTbb = [...newTableOptions]
    },
    getNewArrLock(newTableOptions) {
      this.realTableOptionsLock = [...newTableOptions]
    },
    getNewArrCcdStatus(newTableOptions) {
      this.realTableOptionsCcdStatus = [...newTableOptions]
    },
    getZbbList() {
      this.fbbLoading = true
      GetZbbList(this.queryParams).then((res) => {
        this.fbbList = res.result.records
        if (this.fbbList !== undefined) {
          this.getSpanArr(this.fbbList)
        }
        this.queryParams.total = res.result.total
        this.getNewArr(this.realTableOptions)
      }).finally(() => {
        this.fbbLoading = false
      })
    },
    // getSpanArr方法
    getSpanArr(data) {
      this.mergeArr.forEach((key, index1) => {
        let count = 0 // 用来记录需要合并行的起始位置
        this.mergeObj[key] = [] // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1)
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1
              this.mergeObj[key].push(0)
            } else {
              // 如果当前行和上一行其值不相等
              count = index // 记录当前位置
              this.mergeObj[key].push(1) // 重新push 一个 1
            }
          }
        })
      })
      const carOrPerson = this.queryParams.carOrPerson === 0 ? 'carId' : 'driveId'
      this.fbbList.forEach((value, index) => {
        value.allValue = 0
        value.mileageCostValue = 0
        value.sxrOvertimeDaysValue = 0
        value.jjrOvertimeDaysValue = 0
        value.overtimeCostValue = 0
        value.gzrDelayHourValue = 0
        value.sxrDelayHourValue = 0
        value.jjrDelayHourValue = 0
        value.delayCostValue = 0
        value.nightShiftDaysValue = 0
        value.nightShiftCostValue = 0
        value.travelAllowanceValue = 0
        value.allCostValue = 0
        for (let i = index; i < index + this.mergeObj[carOrPerson][index]; i++) {
          value.allValue += this.fbbList[i].workValue
          value.mileageCostValue += this.fbbList[i].mileageCost
          value.sxrOvertimeDaysValue += this.fbbList[i].sxrOvertimeDays
          value.jjrOvertimeDaysValue += this.fbbList[i].jjrOvertimeDays
          value.overtimeCostValue += this.fbbList[i].overtimeCost
          value.gzrDelayHourValue += this.fbbList[i].gzrDelayHour
          value.sxrDelayHourValue += this.fbbList[i].gzrDelayHour
          value.jjrDelayHourValue += this.fbbList[i].jjrDelayHour
          value.delayCostValue += this.fbbList[i].delayCost
          value.nightShiftDaysValue += this.fbbList[i].nightShiftDays
          value.nightShiftCostValue += this.fbbList[i].nightShiftCost
          value.travelAllowanceValue += this.fbbList[i].travelAllowance
          value.allCostValue += this.fbbList[i].allCost
        }
      })
    },
    /** 重置查询车队长 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.fbbSelectID = 0
      this.getZbbList()
    },
    /** 单击表事件 */
    fbbSelect(row) {
      this.fbbSelectID = row.id
      // 列表无id，虚设
      this.fbbSelectID = 1
      this.fbbParams = row
      console.log(JSON.stringify(this.fbbParams))
      let Y = this.fbbParams.YEAR
      let M = this.fbbParams.MONTH
      let time = new Date(Y + '-' + M + '-01')
      this.fbbParams.startDate = time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate()
      if (M == 12) {
        Y = Y + 1
        M = 1
      } else {
        M = M + 1
      }
      time = new Date(Y + '-' + M + '-01')
      this.fbbParams.endDate = time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate()
    },
    sbbSelect(row) {
      this.sbbSelectID = row.id
      // 列表无id，虚设
      this.sbbSelectID = 1
      this.sbbParams = row
    },
    tbbSelect(row) {
      this.tbbSelectID = row.id
      this.tbbParams = row
    },
    ccdStatus(row) {
      // 列表无id，虚设
      this.sbbSelectID = 1
      this.sbbParams = {
        driveId: row.driveid,
        moveDate: row.movedate,
      }
    },
    /** 关闭弹窗 */
    closeLockDialog() {
      this.lockDialog = false
    },
    closefbbDialog() {
      this.fbbDialog = false
    },
    closesbbDialog() {
      this.sbbDialog = false
    },
    closetbbDialog() {
      this.tbbDialog = false
    },
    closeCcdStatusDialog() {
      this.ccdStatusDialog = false
    },
    /** 打开弹窗 */
    openDialog(type) {
      switch (type) {
        case 'fbbshow':
          if (this.fbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            var selectTypeValue = ''
            if (this.fbbParams.selectType == 1) {
              selectTypeValue = '加班费'
            } else if (this.fbbParams.selectType == 2) {
              selectTypeValue = '延时津贴'
            } else if (this.fbbParams.selectType == 3) {
              selectTypeValue = '夜餐台班数'
            } else if (this.fbbParams.selectType == 4) {
              selectTypeValue = '出差补贴'
            }
            this.fbbDialogTitle = this.fbbParams.RealName + '(' + this.fbbParams.YEAR + '年' + this.fbbParams.MONTH + '月)' + selectTypeValue
            this.dialogType = 'fbbshow'
            this.fbbDialog = true
            this.sbbLoading = true
            // this.fbbParams.selectType=1
            GetCCLCDetailList({
              "selectType": this.fbbParams.selectType,
              "driveId": this.fbbParams.driveId,
              "year": this.fbbParams.YEAR,
              "month": this.fbbParams.MONTH,
              "type": 1
            }).then((res) => {
              this.sbbList = res.result
            }).finally(() => {
              this.sbbLoading = false
            })
          }
          break
        case 'fbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.queryParams.topGroupName = value.groupname
            }
          })
          DownLoadZbb(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            let groupName = ''
            this.groupItem.forEach(value => {
              if (value.id === this.queryParams.topGroupId) {
                groupName = value.groupname
              }
            })
            const fileName = groupName + '(' + this.queryParams.yearMonth.substring(0, 7) + ')加班费报表' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'sbbshow':
          if (this.sbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.sbbDialogTitle = this.fbbParams.RealName + '(' + getDate(this.sbbParams.moveDate) + ')出车单'
            this.dialogType = 'sbbshow'
            this.sbbDialog = true
            this.tbbLoading = true
            GetCcdByDriveIdAndTime({
              'driveId': this.sbbParams.driveId,
              'date': getDate(this.sbbParams.moveDate),
              "type": 1
            }).then((res) => {
              this.tbbList = res.result
            }).finally(() => {
              this.tbbLoading = false
            })
          }
          break
        case 'sbbdownload':
          DownLoadSbb({
            "selectType": this.fbbParams.selectType,
            "driveId": this.fbbParams.driveId,
            "year": this.fbbParams.YEAR,
            "month": this.fbbParams.MONTH,
            "type": 1
          }).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            var selectTypeValue = ''
            if (this.fbbParams.selectType == 1) {
              selectTypeValue = '加班费'
            } else if (this.fbbParams.selectType == 2) {
              selectTypeValue = '延时津贴'
            } else if (this.fbbParams.selectType == 3) {
              selectTypeValue = '夜餐台班数'
            } else if (this.fbbParams.selectType == 4) {
              selectTypeValue = '出差补贴'
            }
            const fileName = this.fbbParams.RealName + '(' + this.fbbParams.YEAR + '年' + this.fbbParams.MONTH + '月)' + selectTypeValue + '.xlsx'

            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'tbbshow':
          if (this.tbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.tbbDialogTitle = '查看(' + this.tbbParams.applyNo + ')出车单详情'
            this.dialogType = 'tbbshow'
            console.log(this.fbbParams, this.tbbParams)
            if (this.fbbParams.realName !== this.tbbParams.driverName) {
              this.tbbParams.availName = this.fbbParams.realName
              this.tbbParams.availPhone = this.fbbParams.telePhone
              this.tbbParams.isAvial = true
            } else {
              this.tbbParams.isAvial = false
            }
            this.tbbParams.carMold = this.fbbParams.carMold
            this.tbbParams.driverName = this.fbbParams.RealName
            this.tbbParams.licensePlate = this.fbbParams.LicencePlate
            this.tbbDialog = true
          }
          break
        case 'tbbdownload':

          DownLoadTbb({
            'driveId': this.sbbParams.driveId,
            'date': getDate(this.sbbParams.moveDate),
            "type": 1
          }).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = this.fbbParams.RealName + '(' + getDate(this.sbbParams.moveDate) + ')出车单' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'ccdStatus':
          if (this.fbbSelectID === 0) {
            this.$message.error('请选择行！');
          } else {
            const params = {
              year: this.fbbParams.YEAR,
              month: this.fbbParams.MONTH,
              driveid: this.fbbParams.driveId,
              type: 1
            }
            this.ccdStatusTitle = this.fbbParams.RealName + '(' + params.year + '-' + params.month + ')出车单每日出车情况';
            this.ccdStatusDialog = true;
            this.ccdStatusLoading = true;
            GetCcdStatusDaily(params).then(res => {
              this.ccdStatusList = res.result
            }).finally(() => {
              this.ccdStatusLoading = false
            })
          }
          break;
        case 'ccdStatusdownload':
          const params = {
            year: this.fbbParams.YEAR,
            month: this.fbbParams.MONTH,
            driveid: this.fbbParams.driveId,
            type: 1
          }
          DownCcdStatusDaily(params).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = this.fbbParams.RealName + '(' + params.year + '-' + params.month + ')出车单每日出车情况' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break;
        default:
          break;
      }
    },

  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>

<style>
.background_white {
  background: #FFFFFF !important;
}
</style>
