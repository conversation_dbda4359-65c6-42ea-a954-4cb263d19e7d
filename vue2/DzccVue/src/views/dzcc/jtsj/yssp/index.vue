<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.applyUserName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <span class="font-size14">月份：</span>
        <el-date-picker v-model="queryParams.startDate" type="month" placeholder="选择月" value-format="yyyy-MM-dd" :clearable="false" @change="handleMonthChange"></el-date-picker>
<!--        <span class="font-size14">周期：</span>-->
<!--        <el-select v-model="queryParams.week" collapse-tags placeholder="请选择周期" @change="handleChange">-->
<!--          <el-option-->
<!--              v-for="item in monthlyCycleList"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value"-->
<!--          >-->
<!--          </el-option>-->
<!--        </el-select>-->
        <span class="font-size14">审核状态：</span>
        <el-select v-model="queryParams.approveState" placeholder="请选择审核状态" @change="handleChange">
          <el-option
              v-for="item in approveState"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>

        <el-checkbox v-model="queryParams.checked" @change="handleChange" style="margin-right: 10px;">我已审批记录</el-checkbox>

        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-view" @click="openDialog('showInfo')" v-has-permi="['NDWCC01YSSP01QX01']" >查看详情</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-s-check" @click="openDialog('flow')" v-has-permi="['NDWCC01YSSP01QX02']"  >审批</el-button>
        <!-- <el-button class="rygf" size="mini" type="text" icon="el-icon-download" @click="submitDialog('download')">下载 -->
        <!-- </el-button> -->
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :needSelect="true"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @getSelectionData="selectionData"

      >
        <template slot="startTime" slot-scope="scope">
          <div>
            {{formatDate(scope.row.startTime)}}
          </div>
        </template>
        <template slot="endTime" slot-scope="scope">
          <div>
            {{formatDate(scope.row.endTime)}}
          </div>
        </template>
        <template slot="sumDelay" slot-scope="scope">
          <div>
            {{((scope.row.oringDelay || 0) + (scope.row.appendDelay || 0)).toFixed(2)}}
          </div>
        </template>
        <template slot="approveState" slot-scope="scope">
          <div>
            {{scope.row.approveState == 0 ? '未提交' : scope.row.approveState == 1 ? '审核中' : scope.row.approveState == 2 ? '已审核' : scope.row.approveState == 3 ? '已驳回' : scope.row.approveState == 4 ? '流程终止' : ''}}
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="60%"
        :visible="xqDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="xqDialog = false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <infoDetail v-if="xqDialog" :data-params="dataParams"></infoDetail>
    </el-dialog>
    <FLow
        ref="flow"
        v-if="flowDialog"
        :yw-id="dataParams.id"
        :lc-jd-id="dataParams.lcJdid"
        :lc-define-id="dataParams.lcDefineID"
        :first-lc-jd="200321"
        :init-choose="1"
        :termination-disable="true"
        @close="appClose"
        @agree="appAgree"
        @reject="appReject"
    >
    </FLow>
  </div>
</template>

<script>
import Table from "@/components/MainTable/index.vue";
import Pagination from "@/components/Pagination/index.vue";
import Dropdown from "@/components/ColumnDropdown/index.vue";
import {getMonthLast, numberToChinese} from "@/utils/tool";
import {getPageList, getWeeks} from "@/api/dzcc/jtsj/yssplb";
import infoDetail from "./components/infoDetail.vue";
import FLow from "@/components/FLow/index.vue";
import {rollBackForApply, submitLc} from "api/dzcc/jtsj/yssqlb";

export default {
  name: 'yssp',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    //this.getWeeklyList()
  },
  components: {Table, Pagination, Dropdown, infoDetail, FLow},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getList()
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        driveName: '',
        approveState: -1,
        applyUserName: '',
        startDate: null,
        week: '',
        year: null,
        month: null,
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      dataParams: {
        id: 0,
        lcJdid: 0,
        lcDefineID: 0
      },
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      dialog: false,
      // 表单校验规则
      driverRules: {},
      realTableOptions: [],
      tableOptions: [
      // {label: '司机姓名', prop: 'driveName'},
        {label: '年份', prop: 'year'},
        {label: '月份', prop: 'month'},
        {label: '周期', prop: 'week'},
        {label: '开始日期', prop: 'startTime',slot: true},
        {label: '结束日期', prop: 'endTime',slot: true},
        // {label: '周期天数', prop: 'weekDays'},
        {label: '初始延时', prop: 'oringDelay'},
        {label: '增加延迟', prop: 'appendDelay'},
        {label: '最终延时', prop: 'sumDelay',slot: true},
        {label: '申请用户', prop: 'applyUserName'},
        {label: '审核状态', prop: 'approveState',slot: true},
      ],
       // 审批状态
       approveState: [
        {
          value: -1,
          label: '全部'
        }, {
          value: 0,
          label: '未提交'
        }, {
          value: 1,
          label: '审核中'
        }, {
          value: 2,
          label: '已审核'
        }, {
          value: 3,
          label: '已驳回'
        }, {
          value: 4,
          label: '流程终止'
        }
      ],
      // 多选列表
      paramsList: [],
      // 周期列表
      monthlyCycleList: [],
      xqDialog: false,
      // 审批
      flowDialog: false,
    }
  },
  methods: {
    formatDate(date) {
       const d = new Date(date);
       const year = d.getFullYear();
       const month = String(d.getMonth() + 1).padStart(2, '0'); // 月份从0开始
       const day = String(d.getDate()).padStart(2, '0');
       return `${year}-${month}-${day}`;
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleMonthChange() {
      this.queryParams.endDate = getMonthLast(this.queryParams.startDate);
      this.queryParams.month = '';
      this.queryParams.week = '';
      this.queryParams.year = '';
      if(this.queryParams.startDate) {
        let year = this.queryParams.startDate.split('-')[0];
        let month = this.queryParams.startDate.split('-')[1];
        this.queryParams.year = year;
        this.queryParams.month = month;
        //this.getWeeklyList()
      }
      this.handleChange()

    },
    //获取当前年月的周期列表
    getWeeklyList(){
      getWeeks({year: this.queryParams.year, month:this.queryParams.month}).then(res=>{
        this.monthlyCycleList = [];
        //console.log(res.result)
        if(res.result.length > 0){
          res.result.forEach(item=>{
            //numberToChinese(item.week)
            this.monthlyCycleList.push({label:"第"+numberToChinese(item.week)+"周", value:item.week})
          })
          this.queryParams.week = this.monthlyCycleList[0].value
          this.handleChange()
        }
      })
    },
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.selectID = 0;
      this.dataParams = {};
      getPageList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.defaultForm.pageNum = 1
      this.defaultForm.pageSize = this.queryParams.pageSize
      this.defaultForm.checked = false
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.dataParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0])
      }
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.paramsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '查看驾驶员工作量清单'
            this.dialogType = 'show'
            this.xqDialog = true
          }
          break;
        case 'showInfo':

          this.dialogTitle = '详情'
          this.dialogType = 'show'
          this.xqDialog = true
          break;
        case 'flow':
          if (this.paramsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else if (this.dataParams.approveState === 1 && this.dataParams.sendPersonZgh === this.$store.state.user.loginName + '~') {
            this.flowDialog = true;
            this.$nextTick(() => {
              this.$refs.flow.openShowDialog();
            })
          } else if (this.dataParams.sendPersonZgh !== this.$store.state.user.loginName + '~') {
            this.$message.error('你当前不是审核人，不可再次提交！')
          } else if (this.dataParams.approveState === 2) {
            this.$message.error('已审核，不可再次提交！')
          } else if (this.dataParams.approveState === 3) {
            this.$message.error('已驳回，不可再次提交，请前往延时界面重新提交！')
          } else if (this.dataParams.approveState === 4) {
            this.$message.error('流程已终止，不可再次提交！')
          }
          break;
      }
    },

    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case 'show':
          this.getList()
          this.dialog = false
          break;
      }
    },
    /** 按钮点击时间 */
    submitDialog(type) {
      switch (type) {
        case 'download':
          if (this.queryParams.monthlyCycles.length === 0) {
            this.$message.error('请选择周期！')
            return
          }
          if (this.paramsList.length === 0) {
            this.$message.error('请选择人员！')
            return
          }
          let ids = [];
          this.paramsList.forEach(value => {
            ids.push(value.Id)
          })
          const params = {
            driverIds: [...new Set(ids)],
            monthlyCycles: this.queryParams.monthlyCycles,
            downloadType: 'zip',
            startDate: this.queryParams.startDate,
            endDate: this.queryParams.endDate
          }
          // downJsyqdByIds(params).then((res) => {
          //   const fileName = '集团司机清单.zip'
          //   downLoad(res, fileName)
          // })
          break;
      }
    },
    // 审批通过回调
    appAgree(params) {
      this.flowDialog = false;
      submitLc(params).then(res=>{
        this.$message.success('业务提交成功');
        this.getList();
      })
    },
    appClose(params) {
      this.flowDialog = false;
    },
    appReject(params) {
      this.flowDialog = false;
      rollBackForApply(params).then(res=>{
        this.$message.success('业务驳回成功');
        this.getList();
      })
    },
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}


.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}
</style>
