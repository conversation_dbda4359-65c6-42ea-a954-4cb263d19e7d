 <template>
   <div>
     <el-tabs type="border-card" v-model="activeKey">
     <el-tab-pane label="司机详情" name="first" style="height: 500px;" v-watermark="{label: watermark}">
       <el-form
           ref="dataParams"
           :model="dataParams"
           :inline="true"
           label-width="auto"
       >
         <el-divider content-position="left">基本信息</el-divider>
         <el-row>
           <el-col :span="8">
             <el-form-item label="周期" class="force-width-60">{{'第'+ numberToChinese(dataParams.week) + '周'}}</el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="开始时间" class="force-width-60">{{ formatDate(dataParams.startTime) }}</el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="结束时间" class="force-width-60">{{formatDate(dataParams.endTime)}}</el-form-item>
           </el-col>

         </el-row>
         <el-row>
           <el-col :span="8">
             <el-form-item label="初始延时" class="force-width-60">{{dataParams.oringDelay}}</el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="增加延时" class="force-width-60">{{dataParams.appendDelay}}</el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="最终延时" class="force-width-60">{{((dataParams.oringDelay || 0) + (dataParams.appendDelay || 0)).toFixed(2)}}</el-form-item>
           </el-col>
         </el-row>
         <el-row>
           <el-col :span="8">
             <el-form-item label="申请人" class="force-width-60">{{dataParams.applyUserName}}</el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="审批状态" class="force-width-60"> {{ approveStates.find(state => state.value === dataParams.approveState)?.label }}</el-form-item>
           </el-col>
         </el-row>
         
         <el-divider content-position="left">司机信息</el-divider>
         <el-row >
           <el-table
               height="250"
               border
               stripe
               :data="driveData"
               style="width: 100%">
             <el-table-column
                 prop="driveName"
                 label="姓名"
                 
                 align="center">
             </el-table-column>
             <el-table-column
                 prop="oringOvertimeHours"
                 label="初始延时"
                 
                 align="center">
             </el-table-column>
             <el-table-column
                 prop="appendDelayHour"
                 label="增加延时"
                 align="center"
                >
                
             </el-table-column>
             <el-table-column
                 prop="endDelayHour"
                 label="最终延时"
                 align="center"
                >
                <template slot-scope="scope">
                  <div>
                    {{((scope.row.oringOvertimeHours || 0) + (scope.row.appendDelayHour || 0)).toFixed(2)}}
                  </div>
                </template>
             </el-table-column>
           </el-table>
         </el-row>
       </el-form>
     </el-tab-pane>
     <el-tab-pane label="流程数据" name="second" style="height: 500px;overflow: auto" v-watermark="{label: watermark}">
        <div v-for="(list, index) in lcList" :key="index">
          <el-alert
              title="主流程"
              type="success"
              :closable="false"
              v-if="index == dataParams.id">
          </el-alert>
          <el-alert
              title="流程补充"
              type="warning"
              :closable="false"
              v-else>
          </el-alert>
          <el-timeline>
            <el-timeline-item v-for="(value) in list" :key="value.lc_jdID+value.lc_jdmc" :timestamp="value.startdate"
                              placement="top">
              <el-card>
                <h4>{{ value.lc_jdmc }}</h4>
                <p>{{ value.personName }} 提交于 {{ value.startdate }}</p>
                <p>审批意见：{{ value.feed }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>

     </el-tabs>
   </div>
 </template>

 <script>
 import {numberToChinese} from "../../../../../utils/tool";
 import {getDriveList} from "../../../../../api/dzcc/jtsj/yssqlb";
 import {getLcListById} from "../../../../../api/dzcc/jtsj/yssplb";


 export default {
   name: 'infoDetail',
   computed: {
     watermark() {
       return this.$store.state.user.watermark
     }
   },
   props: {
     dataParams: {
       type: Object,
       required: true
     }
   },
   data() {
     return {
       activeKey: 'first',
       driveData:[],
       lcList:[],
       //审批状态
       approveStates: [
        {
          value: -1,
          label: '全部'
        }, {
          value: 0,
          label: '未提交'
        }, {
          value: 1,
          label: '审核中'
        }, {
          value: 2,
          label: '已审核'
        }, {
          value: 3,
          label: '已驳回'
        }, {
          value: 4,
          label: '流程终止'
        }
      ],
     }
   },
   mounted() {
     this.getDriveList(); 
     
   },
   methods: {
    numberToChinese,
    formatDate(date) {
       const d = new Date(date);
       const year = d.getFullYear();
       const month = String(d.getMonth() + 1).padStart(2, '0'); // 月份从0开始
       const day = String(d.getDate()).padStart(2, '0');
       return `${year}-${month}-${day}`;
    },

    getDriveList(){
      if(this.dataParams && this.dataParams.id){
        getDriveList({weekMainId:this.dataParams.id}).then(res=>{
          this.driveData = res.result
        })
      }

      this.getLCList()
    },
    getLCList(){
      if(this.dataParams && this.dataParams.id){
        getLcListById({id: this.dataParams.id}).then((res) => {
        let response = res
        if (typeof res == 'string') {
          response = eval("(" + res + ")")
        }
        this.lcList = response.result
      })
      }
      
    },
  
   }
 }
 </script>