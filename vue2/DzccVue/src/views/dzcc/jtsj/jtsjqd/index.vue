<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <span class="font-size14">月份：</span>
        <el-date-picker v-model="queryParams.startDate" type="month" placeholder="选择月" value-format="yyyy-MM-dd" :clearable="false" @change="handleMonthChange"></el-date-picker>
        <span class="font-size14">周期：</span>
        <el-select v-model="queryParams.monthlyCycles" multiple collapse-tags placeholder="请选择周期" @change="handleChange">
          <el-option
              v-for="item in monthlyCycleList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          >
          </el-option>
        </el-select>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-view" @click="openDialog('show')" v-has-permi="['NDWCC01JTSJ01QX01']">查看</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-download" @click="submitDialog('download')" v-has-permi="['NDWCC01JTSJ01QX02']">下载清单</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh-left" @click="syncDriver" v-has-permi="['NDWCC01JTSJ01QX03']">重新生成</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :needSelect="true"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @getSelectionData="selectionData"
          @rowdblclick="openDialog('show')"
      >
        <template v-slot:week="scope">
          <span>第{{ numberToChinese(scope.row.week) }}周</span>
        </template>
        <template v-slot:overtimeHoursAll="scope">
          <span v-if="scope.row.overtimeHoursAll == null">0</span>
          <span v-else>{{ scope.row.overtimeHoursAll }}</span>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
          :page-sizes="[15, 30, 45, 60]"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="xqDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="xqDialog = false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <Sjqdxq v-if="xqDialog" :driver-params="driverParams" :startDate="queryParams.startDate" :endDate="queryParams.endDate"></Sjqdxq>
    </el-dialog>
  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {downLoad, formatMonth, getMonthLast, numberToChinese} from "@/utils/tool";
import {downJsyqdByIds, GetDriverList} from '@/api/dzcc/jtsj/jtsjqd'
import {syncDriver} from "@/api/dzcc/jtsj/yssqlb";
import Sjqdxq from '@/components/DZCC/Sjqdxq'
import {getWeeks} from "api/dzcc/jtsj/yssqlb";

export default {
  name: 'jtsjqd',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, Dropdown, Sjqdxq},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getWeeks()
    this.getList()
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        total: 0,
        driverName: '',
        startDate: formatMonth(),
        endDate: getMonthLast(),
        monthlyCycles: []
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      driverParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      dialog: false,
      // 表单校验规则
      driverRules: {},
      realTableOptions: [],
      tableOptions: [
        {label: '司机姓名', prop: 'driveName'},
        {label: '周期', prop: 'week', slot: true},
        {label: '开始日期', prop: 'startTime'},
        {label: '结束日期', prop: 'endTime'},
        {label: '周期天数', prop: 'weekDays'},
        {label: '总延时', prop: 'overtimeHoursAll', slot: true},
      ],
      // 多选列表
      paramsList: [],
      // 周期列表
      monthlyCycleList: [],
      xqDialog: false,
    }
  },
  methods: {
    numberToChinese,
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    handleMonthChange() {
      this.queryParams.endDate = getMonthLast(this.queryParams.startDate);
      this.handleChange()
    },
    /** 数据查询 */
    getList() {
      this.loading = true
      GetDriverList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 重新生成 */
    syncDriver() {
      let params = {  };
      if(this.queryParams.startDate){
        params.year =this.queryParams.startDate.split('-')[0];
        params.month = this.queryParams.startDate.split('-')[1];
      }
      syncDriver(params).then(res=>{
        if(res.code == 200){
          this.$message.success('重新生成成功！')
          this.getList()
        }else{
          this.$message.error(res.message)
        }
      })
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.driverParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0])
      }
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.paramsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '查看驾驶员工作量清单'
            this.dialogType = 'show'
            this.xqDialog = true
          }
          break;
      }
    },
    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case 'show':
          this.getList()
          this.dialog = false
          break;
      }
    },
    /** 按钮点击时间 */
    submitDialog(type) {
      switch (type) {
        case 'download':
          if (this.paramsList.length === 0) {
            this.$message.error('请选择数据！')
            return
          }
          let ids = [];
          let monthlyCycles = [];
          let driverIds = [];
          this.paramsList.forEach(value => {
            ids.push(value.id)
            driverIds.push(value.driveId)
            monthlyCycles.push(value.week)
          });
          const params = {
            ids: ids,
            driverIds: [...new Set(driverIds)],
            monthlyCycles: [...new Set(monthlyCycles)],
            downloadType: 'zip',
            startDate: this.queryParams.startDate,
            endDate: this.queryParams.endDate
          }
          downJsyqdByIds(params).then((res) => {
            const fileName = '集团司机清单.zip'
            downLoad(res, fileName)
          })
          break;
      }
    },
    /** 获取周期列表 */
    getWeeks() {
      getWeeks({year: this.queryParams.startDate.substring(0, 4), month: this.queryParams.startDate.substring(5, 7), selectAll: true}).then(res => {
        this.monthlyCycleList = res.result.map(item => {
          return {label: '第' + numberToChinese(item.week) + '周', value: item.week}
        })
      })
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}


.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}
</style>
