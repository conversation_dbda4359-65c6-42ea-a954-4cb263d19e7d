<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.name" clearable placeholder="请输入姓名"
                  @change="handleChange"></el-input>
        <span class="font-size14">身份证：</span>
        <el-input v-model="queryParams.certificateID" clearable placeholder="请输入身份证"
                  @change="handleChange"></el-input>
        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.startDate"
            type="date"
            placeholder="选择日期"
            clearable
            @change="handleChange"
            :clearable="false"
            value-format="yyyy-MM-dd">
        </el-date-picker>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getList"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-has-permi="['NDWCC01SJ03QX02']"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01SJ03QX02']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="openDialog('del')"
            v-has-permi="['NDWCC01SJ03QX02']"
        >删除
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="openDialog('show')"
            v-has-permi="['NDWCC01SJ03QX04']"
        >查看
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-user"
            @click="openDialog('employ')"
            v-has-permi="['NDWCC01SJ03QX03','NDWCC01SJ03QX05']"
        >聘用
        </el-button>
		   <el-button
		   type="text"
		   icon="el-icon-download"
		   @click="openDialog('zzxxExport')"
			v-has-permi="['NDWCC01SJ03QX06']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

      </div>
    </div>
    <div class="table-box">
      <Table
          ref="List"
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
        <template slot="driverExpiryDate" slot-scope="scope">
          <span>{{ getDate()(scope.row.driverExpiryDate) }}</span>
        </template>
      </Table>

      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="80%"
        fullscreen
        :visible="xxtbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="xxtbParams"
          :model="xxtbParams"
          :rules="xxtbRules"
          :inline="true"
          label-width="auto"
          v-loading="formLoading"
      >
        <el-collapse v-model="collapseNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="身份证" class="force-width-60" prop="certificateID">
                  <el-input v-model="xxtbParams.certificateID" size="mini" @change="infoChange"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="性别" class="force-width-60" prop="sex">
                  <el-select v-model="xxtbParams.sex" placeholder="请选择性别" disabled>
                    <el-option
                        :key="1"
                        label="男"
                        :value="1">
                    </el-option>
                    <el-option
                        :key="2"
                        label="女"
                        :value="2">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出生年月" class="force-width-60" prop="brithday">
                  <el-date-picker
                      v-model="xxtbParams.brithday"
                      type="date"
                      placeholder="选择出生年月"
                      value-format="yyyy-MM-dd"
                      disabled>
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="民族" class="force-width-60" prop="nation">
                  <el-input v-model="xxtbParams.nation" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="政治面貌" class="force-width-60" prop="politicalOutlook">
                  <el-select v-model="xxtbParams.politicalOutlook" placeholder="请选择政治面貌">
                    <el-option
                        v-for="item in dict[970235]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="健康状况" class="force-width-60" prop="health">
                  <el-input v-model="xxtbParams.health" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="姓名" class="force-width-60" prop="name">
                  <el-input v-model="xxtbParams.name" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" class="force-width-60" prop="photo">
                  <el-input v-model="xxtbParams.photo" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="家庭住址" class="force-width-60" prop="address">
                  <el-input v-model="xxtbParams.address" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="劳务合同单位" class="force-width-60" prop="laborContractUnit">
                  <el-input v-model="xxtbParams.laborContractUnit" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="机动车驾驶证" name="2">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="驾驶证号" class="force-width-60" prop="driverLicense">
                  <el-input v-model="xxtbParams.driverLicense" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="初次领证时间" class="force-width-60" prop="driverCollectionTime">
                  <el-date-picker
                      v-model="xxtbParams.driverCollectionTime"
                      type="date"
                      placeholder="选择初次领证时间"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="准驾车型(驾驶证)" class="force-width-60" prop="driverCarType">
                  <el-select v-model="xxtbParams.driverCarType" placeholder="请选择准驾车型(驾驶证)">
                    <el-option
                        v-for="item in dict[970238]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="发证机关" class="force-width-60" prop="licenceIssuingAuthority">
                  <el-input v-model="xxtbParams.licenceIssuingAuthority" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="有效期(驾驶证)" class="force-width-60" prop="driverExpiryDate">
                  <el-date-picker
                      v-model="xxtbParams.driverExpiryDate"
                      type="date"
                      placeholder="选择有效期(驾驶证)"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="申领准驾证" name="3">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="准驶证号" class="force-width-60" prop="permitLicense">
                  <el-input v-model="xxtbParams.permitLicense" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="准假类型" class="force-width-60" prop="permitType">
                  <el-select v-model="xxtbParams.permitType" placeholder="请选择准假类型">
                    <el-option
                        v-for="item in dict[970239]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="初次申领时间" class="force-width-60" prop="permitApplicationTime">
                  <el-date-picker
                      v-model="xxtbParams.permitApplicationTime"
                      type="date"
                      placeholder="选择初次申领时间"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="准驾车型(准驶证)" class="force-width-60" prop="permitCarType">
                  <el-select v-model="xxtbParams.permitCarType" placeholder="请选择准驾车型(准驶证)">
                    <el-option
                        v-for="item in dict[970240]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="准驾车辆归属" class="force-width-60" prop="permitCarUnit">
                  <el-input v-model="xxtbParams.permitCarUnit" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="有效期(准驾证)" class="force-width-60" prop="permitExpiryDate">
                  <el-date-picker
                      v-model="xxtbParams.permitExpiryDate"
                      type="date"
                      placeholder="选择有效期(准驾证)"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="相关成绩" name="4">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="交通安全理论成绩" class="force-width-60" prop="trafficSafetyTheory">
                  <el-input-number v-model="xxtbParams.trafficSafetyTheory" controls-position="right" size="small"
                                   style="width: 100%"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="驾驶技能考核成绩" class="force-width-60" prop="drivingSkillResults">
                  <el-input-number v-model="xxtbParams.drivingSkillResults" controls-position="right" size="small"
                                   style="width: 100%"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="背景审查" class="force-width-60" prop="backgroundCheck">
                  <el-select v-model="xxtbParams.backgroundCheck" placeholder="请选择背景审查">
                    <el-option
                        v-for="item in dict[970241]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <div style="font-weight: bold;font-size: 14px;line-height: 36px;display: flex">
                  <div style="color: red;margin-right: 0.1vw">*</div>
                  近三年是否发生过本人负责同等及以上责任的一般及以上伤人交通
                </div>
              </el-col>
              <el-col :span="8">
                <el-form-item label="" class="force-width-60" prop="trafficAccident" style="margin-left: 7vw">
                  <el-input v-model="xxtbParams.trafficAccident" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="附件" name="5">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="身份证正面" class="force-width-60" prop="idCardFrontFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="1"
                      v-model="xxtbParams.idCardFrontFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证反面" class="force-width-60" prop="idCardReserveFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="2"
                      v-model="xxtbParams.idCardReserveFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="驾驶证正面" class="force-width-60" prop="licenseFrontFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="3"
                      v-model="xxtbParams.licenseFrontFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="驾驶证反面" class="force-width-60" prop="licenseReserveFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="4"
                      v-model="xxtbParams.licenseReserveFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="司机正面照" class="force-width-60" prop="facePhotoId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="5"
                      v-model="xxtbParams.facePhoto"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="意外保险单" class="force-width-60" prop="accidentInsurancePolicyFile">
                  <ImageUpload
                      :limit="5"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      :type="6"
                      v-model="xxtbParams.accidentInsurancePolicyFile"
                      uploadUrl="/dzcc/file/upload"
                      :data="{type: 'zzxx', hjID: 6}"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="劳动合同" class="force-width-60" prop="laborContractFile">
                  <ImageUpload
                      :limit="5"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      :type="7"
                      v-model="xxtbParams.laborContractFile"
                      uploadUrl="/dzcc/file/upload"
                      :data="{type: 'zzxx', hjID: 7}"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <div align="center" v-has-permi="['NDWCC01SJ03QX02']">
        <el-button size="mini" @click="submitSjtg(dialogType)" :loading="loginLooad">
          <span v-if="!loginLooad">保存</span>
          <span v-else>加载中...</span>
        </el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        fullscreen
        :visible="xxtbShowDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeShowDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="xxtbParams"
          :model="xxtbParams"
          :rules="xxtbRules"
          :inline="true"
          label-width="auto"
          v-loading="formLoading"
      >
        <el-collapse v-model="collapseNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="身份证" class="force-width-60" prop="certificateID">
                  <el-input v-model="xxtbParams.certificateID" size="mini" @change="infoChange"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="性别" class="force-width-60" prop="sex">
                  <el-select v-model="xxtbParams.sex" placeholder="请选择性别" disabled>
                    <el-option
                        :key="1"
                        label="男"
                        :value="1">
                    </el-option>
                    <el-option
                        :key="2"
                        label="女"
                        :value="2">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出生年月" class="force-width-60" prop="brithday">
                  <el-date-picker
                      v-model="xxtbParams.brithday"
                      type="date"
                      placeholder="选择出生年月"
                      value-format="yyyy-MM-dd"
                      disabled>
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="民族" class="force-width-60" prop="nation">
                  <el-input v-model="xxtbParams.nation" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="政治面貌" class="force-width-60" prop="politicalOutlook">
                  <el-select v-model="xxtbParams.politicalOutlook" placeholder="请选择政治面貌">
                    <el-option
                        v-for="item in dict[970235]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="健康状况" class="force-width-60" prop="health">
                  <el-input v-model="xxtbParams.health" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="姓名" class="force-width-60" prop="name">
                  <el-input v-model="xxtbParams.name" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话" class="force-width-60" prop="photo">
                  <el-input v-model="xxtbParams.photo" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="家庭住址" class="force-width-60" prop="address">
                  <el-input v-model="xxtbParams.address" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="劳务合同单位" class="force-width-60" prop="laborContractUnit">
                  <el-input v-model="xxtbParams.laborContractUnit" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="机动车驾驶证" name="2">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="驾驶证号" class="force-width-60" prop="driverLicense">
                  <el-input v-model="xxtbParams.driverLicense" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="初次领证时间" class="force-width-60" prop="driverCollectionTime">
                  <el-date-picker
                      v-model="xxtbParams.driverCollectionTime"
                      type="date"
                      placeholder="选择初次领证时间"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="准驾车型(驾驶证)" class="force-width-60" prop="driverCarType">
                  <el-select v-model="xxtbParams.driverCarType" placeholder="请选择准驾车型(驾驶证)">
                    <el-option
                        v-for="item in dict[970238]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="发证机关" class="force-width-60" prop="licenceIssuingAuthority">
                  <el-input v-model="xxtbParams.licenceIssuingAuthority" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="有效期(驾驶证)" class="force-width-60" prop="driverExpiryDate">
                  <el-date-picker
                      v-model="xxtbParams.driverExpiryDate"
                      type="date"
                      placeholder="选择有效期(驾驶证)"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="申领准驾证" name="3">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="准驶证号" class="force-width-60" prop="permitLicense">
                  <el-input v-model="xxtbParams.permitLicense" size="mini" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="准假类型" class="force-width-60" prop="permitType">
                  <el-select v-model="xxtbParams.permitType" placeholder="请选择准假类型">
                    <el-option
                        v-for="item in dict[970239]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="初次申领时间" class="force-width-60" prop="permitApplicationTime">
                  <el-date-picker
                      v-model="xxtbParams.permitApplicationTime"
                      type="date"
                      placeholder="选择初次申领时间"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="准驾车型(准驶证)" class="force-width-60" prop="permitCarType">
                  <el-select v-model="xxtbParams.permitCarType" placeholder="请选择准驾车型(准驶证)">
                    <el-option
                        v-for="item in dict[970240]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="准驾车辆归属" class="force-width-60" prop="permitCarUnit">
                  <el-input v-model="xxtbParams.permitCarUnit" size="mini"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="有效期(准驾证)" class="force-width-60" prop="permitExpiryDate">
                  <el-date-picker
                      v-model="xxtbParams.permitExpiryDate"
                      type="date"
                      placeholder="选择有效期(准驾证)"
                      value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="相关成绩" name="4">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="交通安全理论成绩" class="force-width-60" prop="trafficSafetyTheory">
                  <el-input-number v-model="xxtbParams.trafficSafetyTheory" controls-position="right" size="small"
                                   style="width: 100%"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="驾驶技能考核成绩" class="force-width-60" prop="drivingSkillResults">
                  <el-input-number v-model="xxtbParams.drivingSkillResults" controls-position="right" size="small"
                                   style="width: 100%"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="背景审查" class="force-width-60" prop="backgroundCheck">
                  <el-select v-model="xxtbParams.backgroundCheck" placeholder="请选择背景审查">
                    <el-option
                        v-for="item in dict[970241]"
                        :key="item.Content"
                        :label="item.Content"
                        :value="item.Content">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <div style="font-weight: bold;font-size: 14px;line-height: 36px;display: flex">
                  <div style="color: red;margin-right: 0.1vw">*</div>
                  近三年是否发生过本人负责同等及以上责任的一般及以上伤人交通
                </div>
              </el-col>
              <el-col :span="8">
                <el-form-item label="" class="force-width-60" prop="trafficAccident" style="margin-left: 7vw">
                  <el-input v-model="xxtbParams.trafficAccident" size="mini"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="附件" name="5">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="身份证正面" class="force-width-60" prop="idCardFrontFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="1"
                      v-model="xxtbParams.idCardFrontFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证反面" class="force-width-60" prop="idCardReserveFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="2"
                      v-model="xxtbParams.idCardReserveFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="驾驶证正面" class="force-width-60" prop="licenseFrontFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="3"
                      v-model="xxtbParams.licenseFrontFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="驾驶证反面" class="force-width-60" prop="licenseReserveFileId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="4"
                      v-model="xxtbParams.licenseReserveFile"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="司机正面照" class="force-width-60" prop="facePhotoId">
                  <ImageUpload2
                      :limit="1"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      type="zzxx"
                      hjID="5"
                      v-model="xxtbParams.facePhoto"
                      :uploadUrl="`/dzcc/file/upload`"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                      @uploadSuccessData="uploadSuccessData"
                      @deleteData="deleteData"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-form-item label="意外保险单" class="force-width-60" prop="accidentInsurancePolicyFile">
                  <ImageUpload
                      :limit="5"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      :type="6"
                      v-model="xxtbParams.accidentInsurancePolicyFile"
                      uploadUrl="/dzcc/file/upload"
                      :data="{type: 'zzxx', hjID: 6}"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="劳动合同" class="force-width-60" prop="laborContractFile">
                  <ImageUpload
                      :limit="5"
                      :disabled="!(this.$store.getters.permissions.indexOf('NDWCC01SJ03QX02') > -1)"
                      :isShowTip="false"
                      :type="7"
                      v-model="xxtbParams.laborContractFile"
                      uploadUrl="/dzcc/file/upload"
                      :data="{type: 'zzxx', hjID: 7}"
                      :file-size="fileLimit.imgSize"
                      :file-type="fileLimit.imgType"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <div align="center" v-has-permi="['NDWCC01SJ03QX02']">
        <el-button size="mini" @click="submitSjtg(dialogType)" :loading="loginLooad">
          <span v-if="!loginLooad">保存</span>
          <span v-else>加载中...</span>
        </el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="60%"
        :visible="pyxxDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closePyxxDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-row>
        <el-col :span="3">
          <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="openDialog('pyxxadd')"
              v-has-permi="['NDWCC01SJ03QX03']"
          >新增
          </el-button>
        </el-col>
        <el-col :span="3">
          <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="openDialog('pyxxedit')"
              v-has-permi="['NDWCC01SJ03QX03']"
          >修改
          </el-button>
        </el-col>
        <el-col :span="3">
          <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="openDialog('pyxxdelete')"
              v-has-permi="['NDWCC01SJ03QX03']"
          >删除
          </el-button>
        </el-col>
      </el-row>
      <el-table
          :border="true"
          ref="pyxxList"
          :data="pyxxList"
          highlight-current-row
          @row-click="pyxxSelect"
          height="51vh"
          v-loading="pyxxLoading"
      >
        <el-table-column label="序号" width="70" align="center">
          <template v-slot="scope">{{
              scope.$index + 1
            }}
          </template>
        </el-table-column>
        <el-table-column
            align="center"
            label="部门"
            prop="groupName"
            min-width="80"
        />
        <el-table-column
            align="center"
            label="开始时间"
            prop="startDate"
            min-width="80"
        />
        <el-table-column
            align="center"
            label="结束时间"
            prop="endDate"
            min-width="80"
        />
      </el-table>
    </el-dialog>
    <el-dialog
        width="30%"
        :visible="pyxxShowDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closePyxxShowDialog"
        :close-on-click-modal="false"
    >
      <el-form
          ref="pyxxParams"
          :model="pyxxParams"
          :rules="pyxxRules"
          :inline="true"
          label-width="auto"
      >
        <el-form-item label="所属部门" class="force-width-60" prop="groupId">
          <el-select size="mini" v-model="pyxxParams.groupId" placeholder="请选择管理部门">
            <el-option
                v-for="item in dialogGroup"
                :key="item.id"
                :label="item.groupname"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" class="force-width-60" prop="startDate">
          <el-date-picker
              v-model="pyxxParams.startDate"
              type="date"
              placeholder="选择开始时间"
              value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" class="force-width-60" prop="endDate">
          <el-date-picker
              v-model="pyxxParams.endDate"
              type="date"
              placeholder="选择结束时间"
              value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div align="center">
        <el-button size="mini" @click="submitSjtg(dialogType)" :loading="loginLooad">
          <span v-if="!loginLooad">保存</span>
          <span v-else>加载中...</span>
        </el-button>
        <el-button size="mini" @click="closePyxxShowDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {mapActions} from 'vuex'
import ImageUpload2 from 'components/DFDW/ImageUpload2.vue'
import {
  AddEmployPerson,
  DeleteEmployPerson,
  EditEmployPerson,
  EditInfo,
  GetDict,
  GetEmployList,
  GetList,
  GetGroupList,
  DeleteInfo,
  DownLoadZzxx
} from "@/api/dzcc/xxtb/zzxx";
import {fileLimit, getImage} from "@/api/dzcc/tg/sjtg";
import {getDate} from "@/utils/tool";
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import ImageUpload from "components/DFDW/ImageUpload.vue";

export default {
  name: 'zzxx',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {},
  components: {
    ImageUpload,
    ImageUpload2,Table, Pagination, Dropdown
  },
  mounted() {
    this.GetInfo()
    this.getList()
    GetGroupList({isShowAll: 1, parentId: 1}).then((res) => {
      let data = res.result
      this.groupItem = data
      data = data.slice(1, data.length)
      this.dialogGroup = data
    })
    fileLimit().then((res) => {
      const data = res.result
      data.forEach(value => {
        if (value.Content === '资质图片格式') {
          this.fileLimit.imgType = value.Parameter.split(',')
        } else if (value.Content === '资质图片大小（mb）') {
          this.fileLimit.imgSize = parseInt(value.Parameter)
        }
      })
    })
    this.getDict()
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      collapseNames: ['1', '2', '3', '4', '5'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        groupId: -1,
        startDate: getDate(),
        name: ''
      },
      realTableOptions: [],
      tableOptions: [
        {label: '部门', prop: 'groupName'},
        {label: '姓名', prop: 'name'},
        {label: '联系电话', prop: 'photo'},
        {label: '驾驶证有效期', prop: 'driverExpiryDate', slot: true},
        {label: '准驾车型', prop: 'permitCarType'}
      ],
      // 查询部门列表
      groupItem: [],
      // 列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      xxtbParams: {},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      xxtbDialog: false,
      xxtbShowDialog: false,
      xxtbRules: {
        certificateID: [
          {required: true, message: '请输入身份证！', trigger: 'blur'},
          {pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '身份证号码格式不正确', trigger: 'blur'}
        ],
        sex: [
          {required: true, message: '请选择性别！', trigger: 'blur'}
        ],
        brithday: [
          {required: true, message: '请选择出生年月！', trigger: 'blur'}
        ],
        nation: [
          {required: true, message: '请输入民族！', trigger: 'blur'}
        ],
        politicalOutlook: [
          {required: true, message: '请选择政治面貌！', trigger: 'blur'}
        ],
        health: [
          {required: true, message: '请输入健康状况！', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '请输入姓名！', trigger: 'blur'}
        ],
        photo: [
          {required: true, message: '请输入联系电话！', trigger: 'blur'},
          {
            pattern: /^1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}$/,
            message: '电话号码格式不正确',
            trigger: 'blur'
          }
        ],
        address: [
          {required: true, message: '请输入家庭地址！', trigger: 'blur'}
        ],
        laborContractUnit: [
          {required: true, message: '请输入劳务合同单位！', trigger: 'blur'}
        ],
        driverLicense: [
          {required: true, message: '请输入驾驶证号！', trigger: 'blur'}
        ],
        driverCollectionTime: [
          {required: true, message: '请输入选择初次领证时间！', trigger: 'blur'}
        ],
        driverCarType: [
          {required: true, message: '请输入选择准驾车型(驾驶证)！', trigger: 'blur'}
        ],
        licenceIssuingAuthority: [
          {required: true, message: '请输入发证机关！', trigger: 'blur'}
        ],
        driverExpiryDate: [
          {required: true, message: '请选择有效期(驾驶证)！', trigger: 'blur'}
        ],
        permitLicense: [
          {required: true, message: '请选输入准驾证号！', trigger: 'blur'}
        ],
        permitType: [
          {required: true, message: '请选择准假类型！', trigger: 'blur'}
        ],
        permitApplicationTime: [
          {required: true, message: '请选择初次申领时间！', trigger: 'blur'}
        ],
        permitCarType: [
          {required: true, message: '请选择准驾车型(准驶证)！', trigger: 'blur'}
        ],
        permitCarUnit: [
          {required: true, message: '请输入准驾车辆归属！', trigger: 'blur'}
        ],
        permitExpiryDate: [
          {required: true, message: '请选择有效期(准驶证)！', trigger: 'blur'}
        ],
        trafficSafetyTheory: [
          {required: true, message: '请输入交通安全理论成绩！', trigger: 'blur'}
        ],
        drivingSkillResults: [
          {required: true, message: '请输入驾驶技能考核成绩！', trigger: 'blur'}
        ],
        backgroundCheck: [
          {required: true, message: '请选择背景审查！', trigger: 'blur'}
        ],
        trafficAccident: [
          {required: true, message: '请输入近三年是否有事故！', trigger: 'blur'}
        ],
        idCardFrontFileId: [
          {required: true, message: '请上传身份证正面照片！', trigger: 'blur'}
        ],
        idCardReserveFileId: [
          {required: true, message: '请上传身份证反面照片！', trigger: 'blur'}
        ],
        licenseFrontFileId: [
          {required: true, message: '请上传驾驶证正面照片！', trigger: 'blur'}
        ],
        licenseReserveFileId: [
          {required: true, message: '请上传驾驶证反面照片！', trigger: 'blur'}
        ],
        facePhotoId: [
          {required: true, message: '请上传司机正面照片！', trigger: 'blur'}
        ],
      },
      formLoading: false,
      loginLooad: false,
      // 字典
      dict: {},
      // 图片视频限制
      fileLimit: {},
      pyxxDialog: false,
      pyxxList: [],
      pyxxParams: {},
      pyxxLoading: false,
      pyxxSelectID: 0,
      pyxxShowDialog: false,
      pyxxShowLoading: false,
      pyxxRules: {
        groupId: [
          {required: true, message: '请选择部门！', trigger: 'blur'},
        ],
        startDate: [
          {required: true, message: '请选择开始时间！', trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    getDate() {
      return getDate
    },
    ...mapActions(['GetInfo']),

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    getDict() {
      GetDict().then((res) => {
        this.dict = res.result
      })
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    infoChange() {
      const val = this.xxtbParams.certificateID.length
      let iden = this.xxtbParams.certificateID;
      let sex = null;
      let birth = null;
      let myDate = new Date();
      let month = myDate.getMonth() + 1;
      let day = myDate.getDate();
      let age = 0;
      if (val === 18) {
        age = myDate.getFullYear() - iden.substring(6, 10) - 1;
        sex = iden.substring(16, 17);
        birth =
            iden.substring(6, 10) +
            "-" +
            iden.substring(10, 12) +
            "-" +
            iden.substring(12, 14);
        if (
            iden.substring(10, 12) < month ||
            (iden.substring(10, 12) == month && iden.substring(12, 14) <= day)
        )
          age++;
      }
      if (val === 15) {
        age = myDate.getFullYear() - iden.substring(6, 8) - 1901;
        sex = iden.substring(13, 14);
        birth =
            "19" +
            iden.substring(6, 8) +
            "-" +
            iden.substring(8, 10) +
            "-" +
            iden.substring(10, 12);
        if (
            iden.substring(8, 10) < month ||
            (iden.substring(8, 10) == month && iden.substring(10, 12) <= day)
        )
          age++;
      }
      if (sex % 2 === 0) sex = 2;
      else sex = 1;
      //性别  ==> 1:男       2:女
      // 性别
      this.$set(this.xxtbParams, 'sex', sex)
      // 出生日期
      this.$set(this.xxtbParams, 'brithday', birth)
      // 年龄
      this.$set(this.xxtbParams, 'age', age)
      // 机动车驾驶证号
      this.$set(this.xxtbParams, 'driverLicense', this.xxtbParams.certificateID)
      // 机动车准驾证号
      this.$set(this.xxtbParams, 'permitLicense', this.xxtbParams.certificateID)
    },
    getList() {
      this.loading = true
      this.selectID = 0
      GetList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    getPyxxList() {
      this.pyxxLoading = true
      this.pyxxSelectID = 0
      GetEmployList({personId: this.selectID}).then((res) => {
        this.pyxxList = res.result
      }).finally(() => {
        this.pyxxLoading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询车队长 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.xxtbParams = row
    },
    /** 单击表事件 */
    pyxxSelect(row) {
      this.pyxxSelectID = row.id
      this.pyxxParams = row
    },
    /** 打开新增弹窗 */
    async openDialog(type) {
      let content;
      switch (type) {
        case 'add':
          this.dialogTitle = '新增准假资质信息'
          this.dialogType = 'add'
          this.selectID = 0
          this.xxtbParams = {}
          this.xxtbDialog = true
          break
        case 'edit':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑准假资质信息'
            this.dialogType = 'edit'
            const {
              idCardFrontFileId,
              idCardReserveFileId,
              licenseFrontFileId,
              licenseReserveFileId,
              facePhotoId
            } = this.xxtbParams
            this.xxtbDialog = true
            this.formLoading = true
            // 获取图片路径 加载图片
            if (idCardFrontFileId) {
              await getImage(idCardFrontFileId).then((res) => {
                this.xxtbParams.idCardFrontFile = res.result
              })
            }
            if (idCardReserveFileId) {
              await getImage(idCardReserveFileId).then((res) => {
                this.xxtbParams.idCardReserveFile = res.result
              })
            }
            if (licenseFrontFileId) {
              await getImage(licenseFrontFileId).then((res) => {
                this.xxtbParams.licenseFrontFile = res.result
              })
            }
            if (licenseReserveFileId) {
              await getImage(licenseReserveFileId).then((res) => {
                this.xxtbParams.licenseReserveFile = res.result
              })
            }
            if (facePhotoId) {
              await getImage(facePhotoId).then((res) => {
                this.xxtbParams.facePhoto = res.result
              })
            }
            this.formLoading = false
          }
          break
		case 'zzxxExport':
		  this.groupItem.forEach(value => {
		    if (value.id === this.queryParams.topGroupId) {
		      this.queryParams.topGroupName = value.groupname
		    }
		  })
		  DownLoadZzxx(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            let groupName = ''
            const fileName ='资质信息报表' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
		  })
		  break
        case 'del':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            content = confirm('是否要删除此人数据？');
            if (content) {
              DeleteInfo({
                id: this.selectID
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
              })
            }
          }
          break
        case 'show':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '查看准假资质信息'
            this.dialogType = 'show'
            const {
              idCardFrontFileId,
              idCardReserveFileId,
              licenseFrontFileId,
              licenseReserveFileId,
              facePhotoId
            } = this.xxtbParams
            this.xxtbDialog = true
            this.formLoading = true
            // 获取图片路径 加载图片
            if (idCardFrontFileId) {
              await getImage(idCardFrontFileId).then((res) => {
                this.xxtbParams.idCardFrontFile = res.result
              })
            }
            if (idCardReserveFileId) {
              await getImage(idCardReserveFileId).then((res) => {
                this.xxtbParams.idCardReserveFile = res.result
              })
            }
            if (licenseFrontFileId) {
              await getImage(licenseFrontFileId).then((res) => {
                this.xxtbParams.licenseFrontFile = res.result
              })
            }
            if (licenseReserveFileId) {
              await getImage(licenseReserveFileId).then((res) => {
                this.xxtbParams.licenseReserveFile = res.result
              })
            }
            if (facePhotoId) {
              await getImage(facePhotoId).then((res) => {
                this.xxtbParams.facePhoto = res.result
              })
            }
            this.formLoading = false
          }
          break
        case 'employ':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '聘用信息界面'
            this.dialogType = 'employ'
            this.getPyxxList()
            this.pyxxDialog = true
          }
          break
        case 'pyxxadd':
          this.dialogTitle = '新增聘用信息'
          this.dialogType = 'pyxxadd'
          this.pyxxSelectID = 0
          this.pyxxParams = {personId: this.selectID}
          this.pyxxShowDialog = true
          break
        case 'pyxxedit':
          if (this.pyxxSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑聘用信息'
            this.dialogType = 'pyxxedit'
            this.pyxxShowDialog = true
          }
          break
        case 'pyxxdelete':
          if (this.pyxxSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            content = confirm('是否要删除该行数据？');
            if (content) {
              DeleteEmployPerson({
                id: this.pyxxSelectID
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getPyxxList()
              })
            }
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.getList()
      this.xxtbDialog = false
    },
    closeShowDialog() {
      this.getList()
      this.xxtbShowDialog = false
    },
    closePyxxDialog() {
      this.getList()
      this.pyxxDialog = false
    },
    closePyxxShowDialog() {
      this.getPyxxList()
      this.pyxxShowDialog = false
    },
    /** 提交新增 */
    submitSjtg(type) {
      switch (type) {
        case 'add':
          this.$refs.xxtbParams.validate((valid) => {
            if (valid) {
              this.loginLooad = true
              let params = {
                trafficSafetyTheory: this.xxtbParams.trafficSafetyTheory,
                drivingSkillResults: this.xxtbParams.drivingSkillResults,
                certificateID: this.xxtbParams.certificateID,
                sex: this.xxtbParams.sex,
                brithday: this.xxtbParams.brithday,
                age: this.xxtbParams.age,
                driverLicense: this.xxtbParams.driverLicense,
                permitLicense: this.xxtbParams.permitLicense,
                nation: this.xxtbParams.nation,
                name: this.xxtbParams.name,
                politicalOutlook: this.xxtbParams.politicalOutlook,
                health: this.xxtbParams.health,
                photo: this.xxtbParams.photo,
                address: this.xxtbParams.address,
                laborContractUnit: this.xxtbParams.laborContractUnit,
                driverCollectionTime: this.xxtbParams.driverCollectionTime,
                driverCarType: this.xxtbParams.driverCarType,
                driverExpiryDate: this.xxtbParams.driverExpiryDate,
                accidentInsurancePolicyFile: this.xxtbParams.accidentInsurancePolicyFile,
                laborContractFile: this.xxtbParams.laborContractFile
              }
              EditInfo(params).then((res) => {
                params = {
                  id: res.result,
                  certificateID: this.xxtbParams.certificateID,
                  licenceIssuingAuthority: this.xxtbParams.licenceIssuingAuthority,
                  permitType: this.xxtbParams.permitType,
                  permitApplicationTime: this.xxtbParams.permitApplicationTime,
                  permitCarType: this.xxtbParams.permitCarType,
                  permitCarUnit: this.xxtbParams.permitCarUnit,
                  permitExpiryDate: this.xxtbParams.permitExpiryDate,
                  backgroundCheck: this.xxtbParams.backgroundCheck,
                  trafficAccident: this.xxtbParams.trafficAccident,
                  fileId1: this.xxtbParams.idCardFrontFileId.toString(),
                  fileId2: this.xxtbParams.idCardReserveFileId.toString(),
                  fileId3: this.xxtbParams.licenseFrontFileId.toString(),
                  fileId4: this.xxtbParams.licenseReserveFileId.toString(),
                  fileId5: this.xxtbParams.facePhotoId.toString(),
                  accidentInsurancePolicyFile: this.xxtbParams.accidentInsurancePolicyFile,
                  laborContractFile: this.xxtbParams.laborContractFile
                }
                EditInfo(params).then((res) => {
                  this.$message.success('新增成功')
                  this.$refs.xxtbParams.resetFields()
                  this.getList()
                  this.closeDialog()
                }).finally(() => {
                  this.loginLooad = false
                })
              })
            } else {
              return false
            }
          })
          break
        case 'edit':
          this.$refs.xxtbParams.validate((valid) => {
            if (valid) {
              this.loginLooad = true
              let params = {
                id: this.selectID,
                trafficSafetyTheory: this.xxtbParams.trafficSafetyTheory,
                drivingSkillResults: this.xxtbParams.drivingSkillResults,
                certificateID: this.xxtbParams.certificateID,
                sex: this.xxtbParams.sex,
                brithday: this.xxtbParams.brithday,
                age: this.xxtbParams.age,
                driverLicense: this.xxtbParams.driverLicense,
                permitLicense: this.xxtbParams.permitLicense,
                nation: this.xxtbParams.nation,
                name: this.xxtbParams.name,
                politicalOutlook: this.xxtbParams.politicalOutlook,
                health: this.xxtbParams.health,
                photo: this.xxtbParams.photo,
                address: this.xxtbParams.address,
                laborContractUnit: this.xxtbParams.laborContractUnit,
                driverCollectionTime: this.xxtbParams.driverCollectionTime,
                driverCarType: this.xxtbParams.driverCarType,
                driverExpiryDate: this.xxtbParams.driverExpiryDate,
                accidentInsurancePolicyFile: this.xxtbParams.accidentInsurancePolicyFile,
                laborContractFile: this.xxtbParams.laborContractFile
              }
              EditInfo(params).then((res) => {
                params = {
                  id: this.selectID,
                  certificateID: this.xxtbParams.certificateID,
                  licenceIssuingAuthority: this.xxtbParams.licenceIssuingAuthority,
                  permitType: this.xxtbParams.permitType,
                  permitApplicationTime: this.xxtbParams.permitApplicationTime,
                  permitCarType: this.xxtbParams.permitCarType,
                  permitCarUnit: this.xxtbParams.permitCarUnit,
                  permitExpiryDate: this.xxtbParams.permitExpiryDate,
                  backgroundCheck: this.xxtbParams.backgroundCheck,
                  trafficAccident: this.xxtbParams.trafficAccident,
                  fileId1: this.xxtbParams.idCardFrontFileId.toString(),
                  fileId2: this.xxtbParams.idCardReserveFileId.toString(),
                  fileId3: this.xxtbParams.licenseFrontFileId.toString(),
                  fileId4: this.xxtbParams.licenseReserveFileId.toString(),
                  fileId5: this.xxtbParams.facePhotoId.toString(),
                  accidentInsurancePolicyFile: this.xxtbParams.accidentInsurancePolicyFile,
                  laborContractFile: this.xxtbParams.laborContractFile
                }
                EditInfo(params).then((res) => {
                  this.$message.success('修改成功')
                  this.$refs.xxtbParams.resetFields()
                  this.getList()
                  this.closeDialog()
                }).finally(() => {
                  this.loginLooad = false
                })
              })
            } else {
              return false
            }
          })
          break
        case 'pyxxadd':
          this.$refs.pyxxParams.validate((valid) => {
            if (valid) {
              AddEmployPerson(this.pyxxParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getPyxxList()
                this.closePyxxShowDialog()
              })
            } else {
              return false
            }
          })
          break
        case 'pyxxedit':
          this.$refs.pyxxParams.validate((valid) => {
            if (valid) {
              EditEmployPerson(this.pyxxParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getPyxxList()
                this.closePyxxShowDialog()
              })
            } else {
              return false
            }
          })
          break
      }
    },
    // 获取上传的图片是属于哪一类
    uploadSuccessData(data) {
      this.$nextTick(() => {
        if (data[0].hjID == '1') {
          this.$set(this.xxtbParams, 'idCardFrontFileId', data[0].id)
        } else if (data[0].hjID == '2') {
          this.$set(this.xxtbParams, 'idCardReserveFileId', data[0].id)
        } else if (data[0].hjID == '3') {
          this.$set(this.xxtbParams, 'licenseFrontFileId', data[0].id)
        } else if (data[0].hjID == '4') {
          this.$set(this.xxtbParams, 'licenseReserveFileId', data[0].id)
        } else if (data[0].hjID == '5') {
          this.$set(this.xxtbParams, 'facePhotoId', data[0].id)
        }
      })
    },
    deleteData(type, data) {
      this.$nextTick(() => {
        if (type == '1') {
          this.$set(this.xxtbParams, 'idCardFrontFileId', null)
        } else if (type == '2') {
          this.$set(this.xxtbParams, 'idCardReserveFileId', null)
        } else if (type == '3') {
          this.$set(this.xxtbParams, 'licenseFrontFileId', null)
        } else if (type == '4') {
          this.$set(this.xxtbParams, 'licenseReserveFileId', null)
        } else if (type == '5') {
          this.$set(this.xxtbParams, 'facePhotoId', null)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>

.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
    .el-cascader{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.title {
  font-size: 20px;
  font-weight: bold;
}

.force-width-60 {
  width: 100%;

  /deep/ .el-form-item__content {
    width: 70% !important;

    .el-input {
      width: 100%;
    }

    .el-textarea {
      width: 100%;
    }

    .el-select {
      width: 100%;
    }

  }
}




</style>
