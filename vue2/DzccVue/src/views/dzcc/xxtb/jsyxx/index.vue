<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.name" clearable placeholder="请输入姓名"
                  @change="handleChange"></el-input>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getDriverList"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-has-permi="['NDWCC01ZC02QX02']"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01ZC02QX03']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="openDialog('delete')"
            v-has-permi="['NDWCC01ZC02QX04']"
        >删除
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-upload2"
            @click="openDialog('upload')"
            v-has-permi="['NDWCC01ZC02QX05']"
        >导入
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01ZC02QX06']"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog
        width="50%"
        :visible="driverDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="driverParams"
          :model="driverParams"
          :rules="driverRules"
          label-width="100px"
          v-watermark="{label:watermark}"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input size="mini" v-model="driverParams.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理部门" class="force-width-60" prop="groupId">
              <el-select style="width: 100%;" v-model="driverParams.groupId" placeholder="请选择部门">
                <el-option
                    v-for="item in groupItemAdd"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号" class="force-width-60" prop="photo">
              <el-input size="mini" v-model="driverParams.photo"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dialogType === 'add'">
            <el-form-item label="身份证" class="force-width-60" prop="certificateID">
              <el-input size="mini" v-model="driverParams.certificateID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="意外保险单" class="force-width-60" prop="accidentInsurancePolicyFile">
              <ImageUpload
                  :limit="5"
                  :isShowTip="false"
                  :type="1"
                  v-model="driverParams.accidentInsurancePolicyFile"
                  uploadUrl="/dzcc/file/upload"
                  :data="{type: 'jsyxx', hjID: 1}"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="劳动合同" class="force-width-60" prop="laborContractFile">
              <ImageUpload
                  :limit="5"
                  :isShowTip="false"
                  :type="2"
                  v-model="driverParams.laborContractFile"
                  uploadUrl="/dzcc/file/upload"
                  :data="{type: 'jsyxx', hjID: 2}"
              />
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="420px"
        :visible="uploadDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeUploadDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">文件上传</div>
      <FileImport
          :file-size="20"
          :file-type="['xlsx']"
          upload-url="/dzcc/employInfo/UploadLoadJsyxx"
          download-name="jsyxxImport.xlsx"
          @uploadSuccessData="uploadSuccessData"
          @uploadErrorData="uploadErrorData"
      />
    </el-dialog>
  </div>
</template>
<script>
import {GetGroupList} from 'api/dzcc/clgl/clgl'
import Table from 'components/MainTable/index.vue'
import Pagination from 'components/Pagination/index.vue'
import Dropdown from 'components/ColumnDropdown/index.vue'
import {AddJsyxx, DeleteJsyxx, DownLoadJsyxx, EditJsyxx, GetJsyxxList} from "api/dzcc/xxtb/jsyxx";
import {downLoad} from "@/utils/tool";
import {getToken} from "@/utils/auth";
import FileImport from "components/DFDW/FileImport.vue";
import ImageUpload from "components/DFDW/ImageUpload.vue";

export default {
  name: 'jsyxx',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, Dropdown, FileImport, ImageUpload},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getDriverList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
    GetGroupList({
      isShowAll: 0,
      parentId: 1
    }).then((res) => {
      this.groupItemAdd = res.result
    })
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + '/dwDzcc/dzcc/employInfo/UploadLoadJsyxx',
      headers: {
        'Authorization': getToken()
      },
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        name: '',
        groupId: -1
      },
      // 查询部门列表
      groupItem: [],
      // 查询部门列表
      groupItemAdd: [],
      // 人员列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      driverParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      driverDialog: false,
      // 表单校验规则
      driverRules: {
        photo: [
          {required: true, message: '请输入手机号码', trigger: 'blur'},
          {min: 11, max: 11, message: '请输入11位手机号码', trigger: 'blur'},
          {
            pattern: /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,
            message: '请输入正确的手机号码'
          }
        ],
        name: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        certificateID: [
          {required: true, message: '请填写证件号码', trigger: 'blur'},
          {
            pattern: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
            message: '请输入正确的证件号码格式！',
            trigger: 'blur'
          }
        ],
        groupId: [
          {required: true, message: '请选择部门', trigger: 'blur'}
        ]
      },
      realTableOptions: [],
      tableOptions: [
        {label: '部门', prop: 'groupName'},
        {label: '姓名', prop: 'name'},
        {label: '身份证', prop: 'certificateID'},
        {label: '手机号', prop: 'photo'},
      ],
      uploadDialog: false,
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getDriverList()
    },
    /** 人员查询 */
    getDriverList() {
      this.loading = true
      GetJsyxxList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getDriverList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getDriverList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.Id
      this.driverParams = row
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增司机'
          this.selectID = 0
          this.driverParams = {}
          this.driverDialog = true
          break
        case 'edit':
          // if (this.$store.getters.permissions.indexOf('NDWCC01SJ01QX02') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑司机手机号'
            this.dialogType = 'edit'
            this.driverDialog = true
          }
          break
        case 'delete':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              DeleteJsyxx(this.driverParams).then(res => {
                if (res.code === 200) {
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getDriverList()
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            }
          }
          break
        case 'upload':
          this.uploadDialog = true
          break
        case 'download':
          DownLoadJsyxx(this.queryParams).then((res) => {
            const fileName = '驾驶员信息' + '.xlsx'
            downLoad(res, fileName)
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.getDriverList()
      this.driverDialog = false
    },
    closeUploadDialog() {
      this.getDriverList()
      this.uploadDialog = false
    },
    /** 提交新增 */
    submitCdzgl(type) {
      switch (type) {
        case 'add':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              AddJsyxx(this.driverParams).then(res => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getDriverList()
                  this.closeDialog()
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
        case 'edit':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              EditJsyxx(this.driverParams).then(res => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getDriverList()
                  this.closeDialog()
                } else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
      }
    },
    uploadSuccessData() {
      this.$message({
        type: 'success',
        message: '上传成功'
      })
      this.closeUploadDialog()
    },
    uploadErrorData(message) {
      this.$message({
        type: 'error',
        message: '上传失败：' + message
      })
    },
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>

