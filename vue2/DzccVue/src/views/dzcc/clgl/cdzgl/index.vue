<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">所属部门：</span>
        <el-select v-model="queryParams.dept" clearable filterable placeholder="请选择所属部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.cdzname" clearable placeholder="姓名" @change="handleChange"></el-input>
        <span class="font-size14">所管理部门：</span>
        <el-select v-model="queryParams.manageGroupId" clearable filterable placeholder="请选择所管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">人员类型：</span>
        <el-select v-model="queryParams.personTypeId" filterable placeholder="请选择人员类型" @change="handleChange">
          <el-option
              v-for="item in personTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getCDZList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            v-has-permi="['NDWCC01CD01QX02']"
            @click="openDialog('edit')"
        >修改
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="tableData"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
      >
        <template slot="personType" slot-scope="scope">
          <div v-if="scope.row.personType == 1">
            车辆管理人员
          </div>
          <div v-if="scope.row.personType == 2">
            车辆调度人员
          </div>
          <div v-if="scope.row.personType == 3">
            车辆运监人员
          </div>
        </template>

      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog
        width="30%"
        :visible="cdzglDialog"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="cdzglParams"
          :model="cdzglParams"
          :rules="cdzglRules"

          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="22">
            <el-form-item label="车队长" prop="cdzId">
              <el-input :value="cdzglParams.RealName" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="管理部门" prop="deptId">
              <el-select size="mini" style="width: 100%;" filterable multiple collapse-tags v-model="cdzglParams.deptId" placeholder="请选择管理部门" @change="handleTagChange">
                <el-option
                    v-for="item in dialogGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="btn-box" style="text-align: right">
        <el-button size="mini" @click="submitCdzgl(dialogType)" type="primary">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>

  </div>

</template>
<script>
import {EditCdz, GetCDZGroupList, GetCDZList} from '@/api/dzcc/clgl/cdzgl'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
export default {
  name: 'cdzgl',
  components: {Table, Pagination, Dropdown},

  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getCDZList()
    GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        cdzname: '',
        dept: -1,
        manageGroupId: -1,
        personTypeId: 1
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      //
      tableData:[],
      realTableOptions: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cdzglParams: {},
      dialogCDZ: [],
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      cdzglDialog: false,
      cdzglRules: {},
      personTypes: [
        {value: 1, label: '车辆管理人员'},
        {value: 2, label: '车辆调度人员'},
        {value: 3, label: '车辆运监人员'}
      ],
      tableOptions: [
        {label: '姓名', prop: 'RealName'},
        {label: '手机号', prop: 'Telephone'},
        {label: '所属部门', prop: 'TopGroupName'},
        {label: '人员类型', prop:'personType',slot: true}
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getCDZList()
    },
    /** 查询车队长 */
    getCDZList () {
      this.loading = true
      GetCDZList(this.queryParams).then((res) => {
        this.tableData = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getCDZList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getCDZList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.cdzglParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'edit':
          // if (this.$store.state.user.roles.indexOf('NDWCC01CD01QX02') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑车队长管理部门'
            this.dialogType = 'edit'
            GetCDZGroupList({personId: this.cdzglParams.Id, personType: this.cdzglParams.personType}).then((res) => {
              this.dialogGroup = res.result
              let ids = []
              this.dialogGroup.forEach(value => {
                if (value.change) {
                  ids.push(value.value)
                }
              })
              this.cdzglParams.deptId = ids
            })
            this.cdzglDialog = true
          }
          break
      }
    },
    handleTagChange() {
      this.$forceUpdate();
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getCDZList()
      this.cdzglDialog = false
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'edit':
          this.$refs.cdzglParams.validate((valid) => {
            if (valid) {
              EditCdz({
                groupIds: this.cdzglParams.deptId.join(','),
                personId: this.cdzglParams.Id,
                personName: this.cdzglParams.RealName,
                personType: this.cdzglParams.personType
              }).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.cdzglDialog = false
                this.getCDZList()

              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
