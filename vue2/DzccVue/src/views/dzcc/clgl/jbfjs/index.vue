<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">所属部门：</span>
        <el-select v-model="queryParams.deptId" clearable filterable placeholder="请选择管理部门"
                   @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-plus"
            @click="openDialog('add')"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('edit')"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-delete"
            @click="openDialog('del')"
        >删除
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="tableList"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
      >
        <!-- 开始时间取值数据转化-->
        <template slot="startTimeType" slot-scope="scope">
          {{ startTimeTypePush(scope.row.startTimeType) }}
        </template>
        <!-- 是否统计午休数据转化-->
        <template slot="isComputeNoon" slot-scope="scope">
          {{ isComputeNoonPush(scope.row.isComputeNoon) }}
        </template>
        <!-- 出差补贴计算标准数据转化-->
        <template slot="travelAllowanceType" slot-scope="scope">
          {{ travelAllowanceTypePush(scope.row.travelAllowanceType) }}
        </template>
        <!-- 加班小时计算标准数据转化-->
        <template slot="overtimeHoursAllType" slot-scope="scope">
          {{ overtimeHoursAllTypePush(scope.row.overtimeHoursAllType) }}
        </template>
        <!-- 每月延时小时计算标准数据转化-->
        <template slot="overtimeHoursMonthType" slot-scope="scope">
          {{ overtimeHoursMonthTypePush(scope.row.overtimeHoursMonthType) }}
        </template>

        <!-- 双休日计算标准数据转化-->
        <template slot="sxrDelayHourMonthType" slot-scope="scope">
          {{ sxrDelayHourMonthTypePush(scope.row.sxrDelayHourMonthType) }}
        </template>

        <!-- 节假日计算标准数据转化-->
        <template slot="jjrDelayHourMonthType" slot-scope="scope">
          {{ jjrDelayHourMonthTypePush(scope.row.jjrDelayHourMonthType) }}
        </template>

      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog
        :visible="overTimeDialog"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
        width="800px"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form ref="overTimeForm" :model="overTimeForm" :rules="overTimeRules" label-width="170px"
               v-watermark="{label: watermark}">
        <el-form-item label="所属部门" prop="deptId">
          <el-select v-model="overTimeForm.deptId" style="width: 100%;" filterable placeholder="请选择管理部门">
            <el-option v-for="item in dialogGroup" :key="item.id" :label="item.groupname" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间取值" prop="startTimeType">
          <el-select v-model="overTimeForm.startTimeType" style="width: 100%;" filterable
                     placeholder="请选择开始时间取值">
            <el-option v-for="item in startTimeTypeDict" :key="item.id" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否统计午休" prop="isComputeNoon">
          <el-select v-model="overTimeForm.isComputeNoon" style="width: 100%;" filterable
                     placeholder="请选择是否统计午休">
            <el-option v-for="item in computeNoonDict" :key="item.id" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="出差补贴计算标准" prop="travelAllowanceType">
          <el-select v-model="overTimeForm.travelAllowanceType" style="width: 100%;" filterable
                     placeholder="请选择出差补贴计算标准">
            <el-option v-for="item in travelAllowanceDict" :key="item.id" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="加班小时计算标准" prop="overtimeHoursAllType">
          <el-select v-model="overTimeForm.overtimeHoursAllType" style="width: 100%;" filterable
                     placeholder="请选择加班小时计算标准">
            <el-option v-for="item in hoursAlleDict" :key="item.id" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="每月延时小时计算标准" prop="overtimeHoursMonthType">
          <el-select v-model="overTimeForm.overtimeHoursMonthType" style="width: 100%;" filterable
                     placeholder="请选择每月延时小时计算标准">
            <el-option v-for="item in hoursMonthDict" :key="item.id" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="双休日延时小时计算" prop="sxrDelayHourMonthType">
          <el-select v-model="overTimeForm.sxrDelayHourMonthType" style="width: 100%;" filterable
                     placeholder="请选择双休日延时小时计算标准">
            <el-option v-for="item in weekendDict" :key="item.id" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="节假日延时小时计算" prop="jjrDelayHourMonthType">
          <el-select v-model="overTimeForm.jjrDelayHourMonthType" style="width: 100%;" filterable
                     placeholder="请选择节假日延时小时计算标准">
            <el-option v-for="item in holidayDict" :key="item.id" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitOverTime(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import {getListPage, createOverTime, updateOverTime, deleteOverTime, getDictDataByType} from '@/api/dzcc/jbfjs/jbfjs'
import {GetGroupList12} from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'jbfjs',
  components: {Table, Pagination, Dropdown},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getDictData()
    GetGroupList12({isShowAll: 1, parentId: 1}).then((res) => {
      let data = res.result
      this.groupItem = data
      data = data.slice(1, data.length)
      this.dialogGroup = data
    })
    this.src = process.env.NODE_ENV === 'development' ? '/static/dzcc/html/BaiDuToBGD.html' : 'static/dzcc/html/BaiDuToBGD.html'
    this.getList()
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        deptId: -1
      },
      tableOptions: [
        {label: '所属部门', prop: 'groupName'},
        {label: '开始时间取值', prop: 'startTimeType', slot: true},
        {label: '是否统计午休', prop: 'isComputeNoon', slot: true},
        {label: '出差补贴计算标准', prop: 'travelAllowanceType', slot: true},
        {label: '加班小时计算标准', prop: 'overtimeHoursAllType', slot: true},
        {label: '每月延时小时计算标准', prop: 'overtimeHoursMonthType', slot: true},
        {label: '双休日延时小时计算', prop: 'sxrDelayHourMonthType', slot: true},
        {label: '节假日延时小时计算', prop: 'jjrDelayHourMonthType', slot: true},
      ],
      realTableOptions: [],
      // 查询部门列表
      groupItem: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      selectData: {},
      overTimeForm: {},
      overTimeRules: {
        deptId: [{required: true, message: '请选择所属部门！', trigger: 'blur'}],
        startTimeType: [{required: true, message: '请输入开始时间取值', trigger: 'blur'}],
        isComputeNoon: [{required: true, message: '请输入是否统计午休', trigger: 'blur'}],
        travelAllowanceType: [{required: true, message: '请输入出差补贴计算标准', trigger: 'blur'}],
        overtimeHoursAllType: [{required: true, message: '请输入加班小时计算标准', trigger: 'blur'}],
        overtimeHoursMonthType: [{required: true, message: '请输入每月延时小时计算标准', trigger: 'blur'}],
        jjrDelayHourMonthType: [{required: true, message: '请输入节假日每小时计算标准', trigger: 'blur'}],
        sxrDelayHourMonthType: [{required: true, message: '请输入双休日延时小时计算标准', trigger: 'blur'}],
      },
      overTimeDialog: false,
      dialogTitle: '',
      //所属部门
      dialogGroup: [],
      dialogType: '',
      tableList: [],
      startTimeTypeDict: [],
      computeNoonDict: [],
      travelAllowanceDict: [],
      hoursAlleDict: [],
      hoursMonthDict: [],
      weekendDict: [],
      holidayDict: [],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询加班费计算规则 */
    getList() {
      this.loading = true
      getListPage(this.queryParams).then((res) => {
        this.tableList = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      if (!row) {
        return
      }
      this.selectID = row.id
      this.selectData = row
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增加班费计算规则'
          this.selectID = 0
          this.selectData = {}
          this.overTimeDialog = true
          break
        case 'edit':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.overTimeForm = this.selectData
            this.overTimeForm.startTimeType = this.selectData.startTimeType + ''
            this.overTimeForm.isComputeNoon = this.selectData.isComputeNoon + ''
            this.overTimeForm.travelAllowanceType = this.selectData.travelAllowanceType + ''
            this.overTimeForm.overtimeHoursAllType = this.selectData.overtimeHoursAllType + ''
            this.overTimeForm.overtimeHoursMonthType = this.selectData.overtimeHoursMonthType + ''
            this.overTimeForm.jjrDelayHourMonthType = this.selectData.jjrDelayHourMonthType + ''
            this.overTimeForm.sxrDelayHourMonthType = this.selectData.sxrDelayHourMonthType + ''
            this.dialogTitle = '编辑加班费计算规则'
            this.dialogType = 'edit'
            this.overTimeDialog = true
          }
          break
        case 'del':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              deleteOverTime(this.selectID).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
              })
            }
          }
          break
      }
    },
    /** 提交新增 */
    submitOverTime(type) {
      switch (type) {
        case 'edit':
          this.$refs.overTimeForm.validate((valid) => {
            if (valid) {
              updateOverTime(this.overTimeForm).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
        case 'add':
          this.$refs.overTimeForm.validate((valid) => {
            if (valid) {
              createOverTime(this.overTimeForm).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getList()
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.overTimeForm = {}
      this.getList()
      this.overTimeDialog = false
    },
    //获取字典数据
    getDictData() {
      //开始时间取值字典
      getDictDataByType("dzcc-overTime-timeType").then(res => {
        this.startTimeTypeDict = res.result
      })
      //是否统计午休
      getDictDataByType("dzcc-overTime-computeNoon").then(res => {
        this.computeNoonDict = res.result
      })
      //出差补贴计算标准
      getDictDataByType("dzcc-overTime-travelAllowance").then(res => {
        this.travelAllowanceDict = res.result
      })
      //加班小时计算标准
      getDictDataByType("dzcc-overTime-hoursAll").then(res => {
        this.hoursAlleDict = res.result
      })
      //每月延时小时计算标准
      getDictDataByType("dzcc-overTime-hoursMonth").then(res => {
        this.hoursMonthDict = res.result
      })
      //双休日每小时计算标准
      getDictDataByType("dzcc-overTime-weekend").then(res => {
        this.weekendDict = res.result
      })
      //节假日每小时计算标准
      getDictDataByType("dzcc-overTime-holiday").then(res => {
        this.holidayDict = res.result
      })
    },
    //开始时间取值数据转化
    startTimeTypePush(value) {
      for (let item of this.startTimeTypeDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },
    //是否统计午休数据转化
    isComputeNoonPush(value) {
      for (let item of this.computeNoonDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },
    //出差补贴计算标准数据转化
    travelAllowanceTypePush(value) {
      for (let item of this.travelAllowanceDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },
    //加班小时计算标准数据转化
    overtimeHoursAllTypePush(value) {
      for (let item of this.hoursAlleDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },
    //每月延时小时计算标准数据转化
    overtimeHoursMonthTypePush(value) {
      for (let item of this.hoursMonthDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },

    //双休日算标准数据转化
    sxrDelayHourMonthTypePush(value) {
      for (let item of this.weekendDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },

    //节假日算标准数据转化
    jjrDelayHourMonthTypePush(value) {
      for (let item of this.holidayDict) {
        if (item.value + '' === value + '') {
          // 返回该对象的 label 属性
          return item.label;
        }
      }
    },

  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
