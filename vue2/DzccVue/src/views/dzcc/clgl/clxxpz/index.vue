<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <el-radio v-model="radioFirst" label="0" @input="radioFirstChange(0)">部门</el-radio>
        <el-radio v-model="radioFirst" label="1" @input="radioFirstChange(1)">车辆</el-radio>
        <span class="font-size14">所属部门：</span>
        <el-select v-model="queryParams.groupId" clearable filterable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">办公地点：</span>
        <el-input v-model="queryParams.unitName" clearable placeholder="请输入办公地点" @change="handleChange"></el-input>
        <span class="font-size14">车牌号：</span>
        <el-input v-model="queryParams.licencePlate" clearable placeholder="请输入车牌号" ></el-input>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getBackHaulList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-if="this.$store.getters.permissions.indexOf('NDWCC01CLPZ01QX01') > -1"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01CLPZ01QX02']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-delete"
            @click="openDialog('del')"
            v-has-permi="['NDWCC01CLPZ01QX03']"
        >删除
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        :visible="clbgDialog"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-radio-group v-model="radioAdd" class="centeredRadioGroup" size="medium">
        <el-radio-button label="0">部门</el-radio-button>
        <el-radio-button label="1">车辆</el-radio-button>
      </el-radio-group>
      <el-form v-if="radioAdd!=null"
          ref="backHaulParams"
          :model="backHaulParams"
          :rules="clbgRules"
          label-width="110px"
          v-watermark="{label: watermark}"
      >

        <el-row>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="groupId">
              <el-select v-model="backHaulParams.groupId" style="width: 100%;" filterable clearable placeholder="请选择管理部门" @change="deptChange">
                <el-option
                    v-for="item in dialogGroup"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="车牌号" prop="carId" v-if="radioAdd==1">
              <el-select v-model="backHaulParams.carId" style="width: 100%;" clearable filterable placeholder="请选择车牌">
                <el-option
                    v-for="item in dialogCarList"
                    :key="item.id"
                    :label="item.licencePlate"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="radioAdd==0" label="办公地点" prop="bgdId">
              <!--              <el-input size="mini" style="width: 100%;" v-model="clbgParams.unitName"></el-input>-->
              <el-select v-model="backHaulParams.bgdId" style="width: 100%;" filterable  clearable placeholder="请选择办公地点" @change="unitChange">
                <el-option
                    v-for="item in dialogUnitList"
                    :key="item.id"
                    :label="item.unitName"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="早上检测时间" prop="backStartTime">
              <el-time-picker class="backStartTime"
                              is-range
                              value-format="HH:mm:ss"
                              v-model="backHaulParams.backStartTime"
                              range-separator="|"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              placeholder="选择时间范围"
                              style="width: 100%">
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下午检测时间" prop="backEndTime">
              <el-time-picker class="backEndTime"
                              is-range
                              value-format="HH:mm:ss"
                              v-model="backHaulParams.backEndTime"
                              range-separator="|"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              placeholder="选择时间范围"
                              style="width: 100%">
              </el-time-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工作上班上午检测时间" prop="workStartTime">
              <el-time-picker class="pickerWidth"
                              is-range
                              value-format="HH:mm:ss"
                              v-model="backHaulParams.workStartTime"
                              range-separator="|"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              placeholder="选择时间范围"
                              style="width: 100%">
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作上班下午检测时间" prop="workEndTime">
              <el-time-picker class="pickerWidth"
                              value-format="HH:mm:ss"
                              is-range
                              v-model="backHaulParams.workEndTime"
                              range-separator="|"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              placeholder="选择时间范围"
                              style="width: 100%">
              </el-time-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="持续时间(分钟)" prop="continueMinute">
              <el-input
                  v-model="backHaulParams.continueMinute"
                  :type="'number'"
                  placeholder="只允许输入数字"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测范围(米)" prop="radius">
              <el-input
                  v-model="backHaulParams.radius"
                  :type="'number'"
                  placeholder="只允许输入数字"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="radioAdd==1" label="办公地点" prop="bgdId">
              <!--              <el-input size="mini" style="width: 100%;" v-model="clbgParams.unitName"></el-input>-->
              <el-select v-model="backHaulParams.bgdId" style="width: 100%;" filterable  clearable placeholder="请选择办公地点" @change="unitChange">
                <el-option
                    v-for="item in dialogUnitList"
                    :key="item.id"
                    :label="item.unitName"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="radioAdd==1" label="计算类型" prop="calculateType">
              <el-select v-model="backHaulParams.calculateType" style="width: 100%;" filterable  clearable placeholder="请选择计算类型">
                <el-option label="有工单计算" :value="0"></el-option>
                <el-option label="无工单计算" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitClbg(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="dtDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDTDialog"
        :close-on-click-modal="false"
    >
      <div style="width: 100%;height: 100%" v-if="dtDialog">
        <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                style="width: 100%;height: 500px"></iframe>
        <el-input v-model="clbgParams.unitGPS"  id="clbgd.gps" v-show="false"></el-input>
        <el-button @click="closeDTDialog" id="clbgd.dtDialogClose" v-show="false"></el-button>
      </div>
    </el-dialog>
  </div>


</template>
<script>

import { GetBackHaulList, AddBackHaul, GetCarList, EditBackHaul,DeleteBackHaul } from '@/api/dzcc/clgl/clxxpz'
import { GetBgdListForBackHaul } from '@/api/dzcc/clgl/clbg'
import {GetGroupList12} from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'clbgd',
  components: {Table, Pagination, Dropdown},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    this.radioFirstChange(0);
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getBackHaulList()
    GetGroupList12({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
      data = data.slice(1, data.length)
      this.dialogGroup = data
    })
    GetBgdListForBackHaul().then(res=>{
      let data = res.result
      this.dialogUnitList = data
    })
    // GetCarList({groupId:-1}).then(res=>{
    //   let data = res.result
    //   this.dialogCarList = data
    // })
    this.getCarList(-1);
    this.src = process.env.NODE_ENV === 'development' ? '/static/dzcc/html/BaiDuToBGD.html' : 'static/dzcc/html/BaiDuToBGD.html'
  },
  data () {
    return {
      radioFirst: '0',
      radioAdd: null,
      dialogUnitList:[],
      dialogCarList:[],
      // 地图src
      src: 'static/dzcc/html/BaiDuToBGD.html',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        unitName: '',
        groupId: -1,
        type: null
      },
      tableOptions: [
        // {label: '车牌', prop: 'licencePlate'},
        {label: '占位', prop: 'zone'},
        {label: '办公地点', prop: 'unitName'},
        {label: '上午检测时间', prop: 'backStartTime'},
        {label: '下午检测时间', prop: 'backEndTime'},
        {label: '持续时间(分钟)', prop: 'continueMinute'},
        {label: '检测范围(米)', prop: 'radius'},
        {label: '工作上班上午检测时间', prop: 'workStartTime'},
        {label: '工作下班下午检测时间', prop: 'workEndTime'}
      ],
      realTableOptions:[],
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      clbgParams: {},
      backHaulParams:{},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      clbgDialog: false,
      clbgRules: {
        workEndTime: [
          {
            required: true,
            message: '请选择工作上班下午检测时间！',
            trigger: 'blur'
          }
        ],
        workStartTime: [
          {
            required: true,
            message: '请选择工作上班上午检测时间',
            trigger: 'blur'
          }
        ],
        backEndTime: [
          {
            required: true,
            message: '请选择下午检测时间',
            trigger: 'blur'
          }
        ],
        backStartTime: [
          {
            required: true,
            message: '请选择上午检测时间',
            trigger: 'blur'
          }
        ],
        calculateType: [
          {
            required: true,
            message: '请选择计算类型',
            trigger: 'blur'
          }
        ]
      },
      dtDialog: false
    }
  },
  methods: {
    getCarList(params){
      GetCarList(params).then(res=>{
        let data = res.result
        this.dialogCarList = data
      })
    },
    tableOptionsChange(newDataItem){
      let existingItem = false;
      this.tableOptions.forEach(item => {
        if (item.prop === newDataItem.prop) {
          existingItem = true;
        }
      });
      if (!existingItem) {
        this.tableOptions.splice(0, 1);
        this.tableOptions.unshift(newDataItem);
      } else {
      }
    },
    radioFirstChange(type){
      if(type==0){
        if(this.realTableOptions){
          let newDataItem = { label: '所属部门', prop: 'groupName' };
          this.tableOptionsChange(newDataItem)
        }
        this.queryParams.type=0;
        this.getBackHaulList()
      }else if(type==1){
        if(this.realTableOptions){
          let newDataItem ={label: '车牌', prop: 'licencePlate'};
          this.tableOptionsChange(newDataItem)
        }
        this.queryParams.type=1;
        this.getBackHaulList()
      }
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getBackHaulList()
    },
    /** 查询车队长 */
    getBackHaulList () {
      this.loading = true
      GetBackHaulList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getBackHaulList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getBackHaulList()
    },
    /** 单击表事件 */
    select (row) {
      if(!row){
        return
      }
      this.selectID = row.id
      this.backHaulParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增车辆信息配置'
          this.selectID = 0
          //this.$refs.tableRef.setCurrentRow()
          this.clbgParams = {}
          this.backHaulParams = {}
          this.clbgDialog = true
          this.radioAdd=null
          break
        case 'edit':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            // this.backHaulParams=
            this.dialogTitle = '编辑车辆办公点'
            this.dialogType = 'edit'
            this.clbgDialog = true
          }
          break
        case 'del':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              DeleteBackHaul({
                id: this.selectID
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getBackHaulList()
              })
            }
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getBackHaulList()
      this.selectID = 0
      this.clbgDialog = false
    },
    closeDTDialog () {
      this.clbgParams.unitGPS = window.parent.document.getElementById('clbgd.gps').value
      this.$nextTick(() => this.$refs.clbgParams.clearValidate())
      this.dtDialog = false
    },
    /** 所属部门选项 */
    deptChange (e) {
      this.dialogGroup.forEach(value => {
        if (value.id == e) {
          this.clbgParams.groupName = value.groupname
        }
      })
      this.getCarList(e)
    },
    unitChange (e) {
      this.dialogUnitList.forEach(value => {
        if (value.id == e) {
          this.clbgParams.unitName = value.unitName
        }
      })
    },
    /** 提交新增 */
    submitClbg (type) {
      switch (type) {
        case 'edit':
          EditBackHaul(this.backHaulParams).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.getBackHaulList()
            this.closeDialog()
          });
          break
        case 'add':
          if((this.backHaulParams.carId!=null && this.backHaulParams.carId!='' )
              || (this.backHaulParams.groupId!=null && this.backHaulParams.groupId!='' )){

          }else{
            this.$message({
              type: 'error',
              message: '车辆号和部门号不能同时为空'
            })
            return;
          }
          this.$refs.backHaulParams.validate((valid) =>{
            if(valid){
              this.backHaulParams.type=this.radioAdd
              AddBackHaul(this.backHaulParams).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getBackHaulList()
                this.closeDialog()
              });
            }else{
              this.$message({
                type: 'error',
                message: '请填写必填项'
              })
            }
          })

          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
.pickerWidth .el-date-editor--daterange.el-input, .el-date-editor--daterange.el-input__inner, .el-date-editor--timerange.el-input, .el-date-editor--timerange.el-input__inner {
  width: 260px;
}
.centeredRadioGroup{
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}
</style>
