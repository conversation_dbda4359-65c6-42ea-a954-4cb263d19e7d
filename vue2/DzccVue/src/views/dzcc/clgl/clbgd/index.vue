<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">所属部门：</span>
        <el-select v-model="queryParams.groupId" clearable filterable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">办公地点：</span>
        <el-input v-model="queryParams.unitName" clearable placeholder="请输入办公地点" @change="handleChange"></el-input>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getBGDList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-if="this.$store.getters.permissions.indexOf('NDWCC01BG01QX01') > -1"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01BG01QX02']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-delete"
            @click="openDialog('del')"
            v-has-permi="['NDWCC01BG01QX03']"
        >删除
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        :visible="clbgDialog"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="clbgParams"
          :model="clbgParams"
          :rules="clbgRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="10">
            <el-form-item label="所属部门" prop="groupId">
              <el-select v-model="clbgParams.groupId" style="width: 100%;" filterable placeholder="请选择管理部门" @change="deptChange">
                <el-option
                    v-for="item in dialogGroup"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="10">
            <el-form-item label="办公地点" prop="unitName">
              <el-input size="mini" style="width: 100%;" v-model="clbgParams.unitName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="17">
            <el-form-item label="GPS" prop="unitGPS">
              <el-input size="mini" v-model="clbgParams.unitGPS" id="clbgd.gps" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-button size="mini" @click="dtDialog = true">从地图中选择</el-button>
        </el-row>


      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitClbg(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="dtDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDTDialog"
        :close-on-click-modal="false"
    >
      <div style="width: 100%;height: 100%" v-if="dtDialog">
        <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                style="width: 100%;height: 500px"></iframe>
        <el-input v-model="clbgParams.unitGPS"  id="clbgd.gps" v-show="false"></el-input>
        <el-button @click="closeDTDialog" id="clbgd.dtDialogClose" v-show="false"></el-button>
      </div>
    </el-dialog>
  </div>


</template>
<script>
import { AddClbg, DeleteClbgById, EditClbg, GetBGDList } from '@/api/dzcc/clgl/clbg'
import {GetGroupList12} from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'clbgd',
  components: {Table, Pagination, Dropdown},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getBGDList()
    GetGroupList12({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
      data = data.slice(1, data.length)
      this.dialogGroup = data
    })
    this.src = process.env.NODE_ENV === 'development' ? '/static/dzcc/html/BaiDuToBGD.html' : 'static/dzcc/html/BaiDuToBGD.html'
  },
  data () {
    return {
      // 地图src
      src: 'static/dzcc/html/BaiDuToBGD.html',
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        unitName: '',
        groupId: -1
      },
      tableOptions: [
        {label: '所属部门', prop: 'groupName'},
        {label: '办公地点', prop: 'unitName'},
        {label: '办公地点GPS', prop: 'unitGPS'}
      ],
      realTableOptions:[],
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      clbgParams: {},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      clbgDialog: false,
      clbgRules: {
        groupId: [
          {
            required: true,
            message: '请选择所属部门！',
            trigger: 'blur'
          }
        ],
        unitName: [
          {
            required: true,
            message: '请输入办公地点',
            trigger: 'blur'
          }
        ],
        unitGPS: [
          {
            required: true,
            message: '请输入办公地点GPS',
            trigger: 'blur'
          },
          {
            pattern: /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,14})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180),(\-|\+)?([0-8]?\d{1}\.\d{0,14}|90\.0{0,6}|[0-8]?\d{1}|90)$/, // 正则校验不用字符串
            message: '请输入正确的纬度和经度，并且用逗号(英文)隔开(经度在前，纬度在后)',
            trigger: 'blur'
          }
        ]
      },
      dtDialog: false
    }
  },
  methods: {

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getBGDList()
    },
    /** 查询车队长 */
    getBGDList () {
      this.loading = true
      GetBGDList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getBGDList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getBGDList()
    },
    /** 单击表事件 */
    select (row) {
      if(!row){
        return
      }
      this.selectID = row.id
      this.clbgParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增车辆办公点'
          this.selectID = 0
          //this.$refs.tableRef.setCurrentRow()
          this.clbgParams = {}
          this.clbgDialog = true
          break
        case 'edit':
          // if (this.$store.state.user.roles.indexOf('NDWCC01BG01QX02') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑车辆办公点'
            this.dialogType = 'edit'
            this.clbgDialog = true
          }
          break
        case 'del':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              DeleteClbgById({
                id: this.selectID,
                groupId: this.clbgParams.groupId
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getBGDList()
              })
            }
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getBGDList()
      this.clbgDialog = false
    },
    closeDTDialog () {
      this.clbgParams.unitGPS = window.parent.document.getElementById('clbgd.gps').value
      this.$nextTick(() => this.$refs.clbgParams.clearValidate())
      this.dtDialog = false
    },
    /** 所属部门选项 */
    deptChange (e) {
      this.dialogGroup.forEach(value => {
        if (value.id == e) {
          this.clbgParams.groupName = value.groupname
        }
      })
    },
    /** 提交新增 */
    submitClbg (type) {
      const gps = this.clbgParams.unitGPS.split(',')
      switch (type) {
        case 'edit':
          this.$refs.clbgParams.validate((valid) => {
            if (valid) {
              this.clbgParams.longitude = gps[0]
              this.clbgParams.latitude = gps[1]
              EditClbg(this.clbgParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getBGDList()
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
        case 'add':
          this.$refs.clbgParams.validate((valid) => {
            if (valid) {
              this.clbgParams.longitude = gps[0]
              this.clbgParams.latitude = gps[1]
              AddClbg(this.clbgParams).then((res) => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getBGDList()
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
