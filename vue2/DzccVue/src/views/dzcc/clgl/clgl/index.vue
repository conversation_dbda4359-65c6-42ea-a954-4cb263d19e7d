<template xmlns="">
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">所属部门：</span>
        <el-select v-model="queryParams.groupId" clearable filterable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌"
                  @change="handleChange"></el-input>
        <span class="font-size14">司机：</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入司机" @change="handleChange"></el-input>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getCLGLList"
        >查询
        </el-button>

        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>

        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('show')"
            v-has-permi="['NDWCC01CL01QX04']"
        >司机配置
        </el-button>

        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('unit')"
            v-has-permi="['NDWCC01CL01QX05']"
        >批量修改办公地点
        </el-button>

        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('stop')"
            v-has-permi="['NDWCC01CL01QX06']"
        >批量修改停车地点
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="clglList"
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          :need-select="true"
          @getSelectionData="handleSelectionChange">
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="showDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeShowDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogDriverTitle">查看车辆详情({{ dialogCarTitle }})</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div></div>
          <div class="search-box">
            <el-button
                size="mini"
                type="text"
                class="rygf"
                icon="el-icon-plus"
                @click="openDialog('add')"
            >新增
            </el-button>
            <el-button
                size="mini"
                type="text"
                class="rygf"
                icon="el-icon-edit"
                @click="openDialog('edit')"
            >修改
            </el-button>
            <el-button
                size="mini"
                type="text"
                class="rygf"
                icon="el-icon-delete"
                @click="openDialog('del')"
            >删除
            </el-button>
            <Dropdown :columnArr="tableOptionsDetail" @getNewArr="getNewArrDetail"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="detailList"
            :table-data="detailList"
            :table-options="realTableOptionsDetail"
            :loading="detailloading"
            @getCurrentData="select"
            style="min-height: 500px">
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="30%"
        :visible="sjDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogDriverTitle">{{ dialogDriverTitle }}</div>
      <el-form
          ref="detailParams"
          :model="detailParams"
          :rules="sjRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-form-item label="驾驶员" prop="driverId">
            <el-col :span="20">
              <el-select size="mini" style="width: 100%;" v-model="detailParams.driverId" placeholder="请选择驾驶员"
                         @change="deptChange">
                <el-option
                    v-for="item in driverList"
                    :key="item.id"
                    :label="item.RealName"
                    :value="item.Id">
                </el-option>
              </el-select>
            </el-col>
          </el-form-item>

        </el-row>
        <el-row>
          <el-form-item label="生效时间" prop="startDate">
            <el-col :span="20">
              <el-date-picker
                  type="date"
                  placeholder="选择日期"
                  v-model="detailParams.startDate"
                  style="width: 100%;"
                  :picker-options="pickerOptions"
                  value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </el-col>

          </el-form-item>

        </el-row>

      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitClgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="30%"
        :visible="unitDialog"

        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeUnitDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogDriverTitle">修改办公地点</div>
      <el-form
          ref="unitParams"
          :model="unitParams"
          :rules="unitRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="22">
            <el-form-item label="办公地点" prop="unitId">
              <el-select size="mini" style="width: 100%;" v-model="unitParams.unitId" placeholder="请选择办公地点"
                         @change="unitChange">
                <el-option
                    v-for="item in unitList"
                    :key="item.id"
                    :label="item.unitName"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitClgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeUnitDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="30%"
        :visible="stopDialog"

        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeStopDialog"
        :close-on-click-modal="false"
    >
      <el-form
          ref="stopParams"
          :model="stopParams"
          :rules="stopRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="22">
            <el-form-item label="停车地点" prop="stopName">
              <el-input size="mini" style="width: 100%;" v-model="stopParams.stopName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="17">
            <el-form-item label="GPS" prop="stopGPS">
              <el-input size="mini" style="width: 100%;" v-model="stopParams.stopGPS" id="clgl.gps" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-button size="mini" @click="dtDialog = true">从地图中选择</el-button>
          </el-col>
        </el-row>

      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitClgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeStopDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="dtDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDTDialog"
        :close-on-click-modal="false"
    >
      <div style="width: 100%;height: 100%" v-if="dtDialog">
        <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                style="width: 100%;height: 500px"></iframe>
        <el-input v-model="stopParams.stopGPS"  id="clgl.gps" v-show="false"></el-input>
        <el-button @click="closeDTDialog" id="clgl.dtDialogClose" v-show="false"></el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  AddClglDetail,
  DeleteClglDetail,
  EditClglDetail,
  EditStop,
  EditUnit,
  GetCLGLList,
  GetDetailList,
  GetDriverList,
  GetUnitList,
  GetGroupList12
} from '@/api/dzcc/clgl/clgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'clgl',
  components: {Table, Pagination, Dropdown},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    GetGroupList12({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
      this.queryParams.groupId = res.result[0].id
      this.getCLGLList()
      this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
    })
    this.src = process.env.NODE_ENV === 'development' ? '/static/dzcc/html/BaiDuToCLGL.html' : 'static/dzcc/html/BaiDuToCLGL.html'
  },
  data() {
    return {
      src: 'static/dzcc/html/BaiDuToCLGL.html',
      // checkNum和isCheck是批量操作按钮和已选几条展示的依据
      checkNum: 0,
      isCheck: false,
      allCheck: false,
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        unitName: '',
        groupId: -1
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      realTableOptions: [],
      tableOptions: [
        {label: '车牌', prop: 'licensePlate'},
        {label: '所属部门', prop: 'groupName'},
        {label: '驾驶员姓名', prop: 'driverName'},
        {label: '办公地点', prop: 'unitName'},
        {label: '停车地点', prop: 'stopName'}
      ],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表
      multipleSelection: [],
      // dialog选择列表id
      selectID: 0,
      dialogType: '',
      dialogDriverTitle: '',
      dialogCarTitle: '',
      sjDialog: false,
      sjRules: {
        driverId: [
          {
            required: true,
            message: '请选择驾驶员！',
            trigger: 'blur'
          }
        ],
        startDate: [
          {
            required: true,
            message: '请选择生效时间',
            trigger: 'blur'
          }
        ]
      },
      dtDialog: false,
      showDialog: false,
      // 选择车辆列表数据
      carParams: {},
      // 细节表数据
      detailList: [],
      detailloading: false,
      realTableOptionsDetail: [],
      tableOptionsDetail: [
        {label: '司机', prop: 'driverName'},
        {label: '生效时间', prop: 'startDate'},
      ],
      // 选择细节列表数据
      detailParams: {},
      // 驾驶员列表
      driverList: [],
      // 日期选择器限制
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 3600 * 24 * 1000
        }
      },
      // 细节按钮detail
      detailDisable: false,
      // 办公地点
      unitDialog: false,
      unitList: [],
      unitParams: {},
      unitRules: {
        unitId: [
          {
            required: true,
            message: '请选择办公地点！',
            trigger: 'blur'
          }
        ]
      },
      // 停车地点
      stopDialog: false,
      stopParams: {},
      stopRules: {
        stopName: [
          {
            required: true,
            message: '请输入停车地点！',
            trigger: 'blur'
          }
        ],
        stopGPS: [
          {
            required: true,
            message: '请输入停车地点GPS',
            trigger: 'blur'
          },
          {
            pattern: /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,14})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180),(\-|\+)?([0-8]?\d{1}\.\d{0,14}|90\.0{0,6}|[0-8]?\d{1}|90)$/, // 正则校验不用字符串
            message: '请输入正确的纬度和经度，并且用逗号(英文)隔开(经度在前，纬度在后)',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    getNewArrDetail(newTableOptions) {
      this.realTableOptionsDetail = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getCLGLList()
    },
    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property]
      if (data === '') {
        return ''
      } else {
        const dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate()
      }
    },
    /** 查询车辆管理 */
    getCLGLList() {
      this.loading = true
      GetCLGLList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 驾驶员查询 */
    getDriverList() {
      GetDriverList({groupId: this.carParams.groupId}).then((res) => {
        this.driverList = res.result
        this.driverList.forEach(value => {
          value.loginName = value.loginName + '/' + value.realName
        })
      })
    },
    /** 车辆管理细节表查询 */
    getDetailList() {
      this.detailloading = true
      GetDetailList({id: this.carParams.id}).then((res) => {
        this.detailList = res.result
      }).finally(() => {
        this.detailloading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getCLGLList()
    },
    /** 重置查询车辆管理 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getCLGLList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.detailParams = row
      const date = new Date(row.startDate)
      if (date.getTime() <= Date.now() - 3600 * 24 * 1000) {
        this.detailDisable = true
      } else {
        this.detailDisable = false
      }
    },
    /** 多选表事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'show':
          // if (this.$store.getters.permissions.indexOf('NDWCC01CL01QX04') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          if (this.multipleSelection.length === 0) {
            this.$message.error('请选择行！')
          } else if (this.multipleSelection.length > 1) {
            this.$message.error('只能选择一条行信息！')
          } else {
            this.carParams = this.multipleSelection[0]
            this.dialogCarTitle = this.carParams.groupName + '-' + this.carParams.licensePlate
            this.showDialog = true
            // this.getRentList()
            this.getDetailList()
            this.getDriverList()
          }
          break
        case 'add':
          this.dialogType = 'add'
          this.dialogDriverTitle = '新增驾驶员'
          this.selectID = 0
          this.detailParams = {}
          this.detailParams.cid = this.carParams.id
          this.detailParams.carId = this.carParams.carId
          this.detailParams.groupId = this.carParams.groupId
          this.sjDialog = true
          break
        case 'edit':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogDriverTitle = '编辑驾驶员'
            this.dialogType = 'edit'
            this.detailParams.cid = this.carParams.id
            this.detailParams.carId = this.carParams.carId
            this.detailParams.groupId = this.carParams.groupId
            this.sjDialog = true
          }
          break
        case 'del':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            var content = confirm('是否要删除该行数据？')
            if (content) {
              DeleteClglDetail({id: this.selectID}).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.getDetailList()
              })
            }
          }
          break
        case 'unit':
          if (this.multipleSelection.length === 0) {
            this.$message.error('请选择行！')
          } else {
            const list = this.multipleSelection
            let groupId = 0;
            let isSame = true;
            list.forEach(value => {
              if (value.groupId != groupId) {
                if (groupId === 0) {
                  groupId = value.groupId
                } else {
                  this.$message.error('请选择同一部门的车辆！')
                  isSame = false;
                }
              }
            })
            if (isSame) {
              this.dialogType = 'unit'
              this.unitDialog = true
              this.unitParams = {}
              GetUnitList({groupId: groupId}).then((res) => {
                this.unitList = res.result
              })
            }
          }
          break
        case 'stop':
          if (this.multipleSelection.length === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogType = 'stop'
            this.stopParams = {}
            if (this.multipleSelection.length == 1) {
              this.$set(this.stopParams, 'stopName', this.multipleSelection[0].stopName)
              this.$set(this.stopParams, 'stopGPS', this.multipleSelection[0].stopGPS)
            }
            this.stopDialog = true
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.getDetailList()
      this.sjDialog = false
    },
    closeDTDialog() {
      this.$set(this.stopParams, 'stopGPS', window.parent.document.getElementById('clgl.gps').value)
      this.$nextTick(() => this.$refs.stopParams.clearValidate())
      this.dtDialog = false
    },
    closeShowDialog() {
      this.showDialog = false
    },
    closeUnitDialog() {
      this.unitDialog = false
    },
    closeStopDialog() {
      this.stopDialog = false
    },
    /** 所属部门选项 */
    deptChange(e) {
      this.driverList.forEach(value => {
        if (value.Id == e) {
          this.detailParams.driverName = value.RealName
        }
      })
    },
    unitChange(e) {
      this.unitList.forEach(value => {
        if (value.id == e) {
          this.unitParams.unitName = value.unitName
        }
      })
    },
    /** 提交新增 */
    submitClgl(type) {
      switch (type) {
        case 'edit':
          this.$refs.detailParams.validate((valid) => {
            if (valid) {
              EditClglDetail(this.detailParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog()
              }).finally(() => {
                this.getDetailList()
              })
            } else {
              return false
            }
          })
          break
        case 'add':
          this.$refs.detailParams.validate((valid) => {
            if (valid) {
              AddClglDetail(this.detailParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog()
              }).finally(() => {
                this.getDetailList()
              })
            } else {
              return false
            }
          })
          break
        case 'unit':
          this.$refs.unitParams.validate((valid) => {
            if (valid) {
              let ids = ''
              this.multipleSelection.forEach(value => {
                ids += value.id + ','
              })
              if (ids === '') {
                ids = ids.substring(0, ids.length - 1)
              }
              this.unitParams.ids = ids
              EditUnit(this.unitParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeUnitDialog()
              }).finally(() => {
                this.getCLGLList()
              })
            } else {
              return false
            }
          })
          break
        case 'stop':
          this.$refs.stopParams.validate((valid) => {
            if (valid) {
              let ids = ''
              this.multipleSelection.forEach(value => {
                ids += value.id + ','
              })
              if (ids === '') {
                ids = ids.substring(0, ids.length - 1)
              }
              this.stopParams.ids = ids
              EditStop(this.stopParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeStopDialog()
              }).finally(() => {
                this.getCLGLList()
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
