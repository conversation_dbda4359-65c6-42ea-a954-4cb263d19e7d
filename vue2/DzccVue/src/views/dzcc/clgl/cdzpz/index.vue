<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.dept" filterable clearable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.cdzname" clearable placeholder="姓名"
                  @change="handleChange"></el-input>
        <span class="font-size14">人员类型：</span>
        <el-select v-model="queryParams.personTypeId" placeholder="请选择人员类型" @change="handleChange">
          <el-option
              v-for="item in personTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getCDZList"
        >查询
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['NDWCC01CD04QX02']"
        >修改
        </el-button>
        <el-button
            size="mini"
            type="text"
            class="rygf"
            icon="el-icon-view"
            @click="openDialog('show')"
            v-has-permi="['NDWCC01CD04QX03']"
        >查看全部
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @row-dblclick="openDialog('edit')"
      >
        <template slot="personType" slot-scope="scope">
          <div v-if="scope.row.personType == 1">
            车辆管理人员
          </div>
          <div v-if="scope.row.personType == 2">
            车辆调度人员
          </div>
          <div v-if="scope.row.personType == 3">
            车辆运监人员
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="30%"
        :visible="cdzpzDialog"

        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="cdzpzParams"
          :model="cdzpzParams"
          :rules="cdzpzRules"
          label-width="auto"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="22">
            <el-form-item label="车队长" class="force-width-40" prop="cdzId">
              {{ cdzpzParams.cdzName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="选择部门" class="force-width-40" prop="cdzId">
              <el-select size="mini" style="width: 100%" v-model="cdzpzParams.groupId" placeholder="请选择" @change="dialogGroupChange">
                <el-option
                    v-for="item in dialogGroup"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="车牌号" class="force-width-40" prop="deptId">
              <el-select size="mini" style="width: 100%;" filterable multiple collapse-tags v-model="cdzpzParams.carIds" placeholder="请选择车辆" @change="handleTagChange">
                <el-option
                    v-for="item in dialogCar"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="30%"
        :visible="cdzpzShowDialog"

        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeShowDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="cdzpzParams"
          :model="cdzpzParams"
          :rules="cdzpzRules"

          label-width="100px"
          v-watermark="{label: watermark}"
      >

        <el-row>
          <el-col :span="22">
            <el-form-item label="车队长"prop="cdzId">
              {{ cdzpzParams.cdzName }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="22">
            <el-form-item label="选择部门"prop="cdzId">
              <el-select size="mini" style="width: 100%;" v-model="cdzpzParams.groupId" placeholder="请选择" @change="dialogGroupShowChange">
                <el-option
                    v-for="item in dialogGroup"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="22">
            <el-form-item label="车牌号" prop="deptId">
              <el-card class="box-card">
                <div v-for="(value, index) in dialogCar" :key="value.id" class="text item">
                  {{ index + 1 }}.{{ value.licencePlate }}
                </div>
              </el-card>
            </el-form-item>
          </el-col>
        </el-row>



      </el-form>
      <div align="right">
<!--        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>-->
        <el-button size="mini" @click="closeShowDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import {EditCDZPZ, GetCarList, GetCarListForCdz, GetCDZGroup, GetCDZPZList,GetGroupList} from '@/api/dzcc/clgl/cdzpz'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'cdzpz',
  components: {Table, Pagination, Dropdown},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getCDZList()
    GetGroupList({ isShowAll: 1, parentId: 1 }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        cdzname: '',
        dept: -1,
        personTypeId: 2
      },
      tableOptions: [
        {label: '姓名', prop: 'cdzName'},
        {label: '电话号码', prop: 'telephone'},
        {label: '可配置部门', prop: 'deptName'},
        {label: '人员类型', prop:'personType',slot: true}
      ],
      realTableOptions:[],
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cdzpzParams: {},
      dialogGroup: [],
      dialogCar: [],
      dialogType: '',
      dialogTitle: '',
      cdzpzDialog: false,
      cdzpzRules: {},
      carprops: { multiple: true },
      cdzpzShowDialog: false,
      personTypes: [
        {value: 2, label: '车辆调度人员'},
        {value: 3, label: '车辆运监人员'}
      ]
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getCDZList()
    },
    /** 查询车队长 */
    getCDZList () {
      this.loading = true
      GetCDZPZList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getCDZList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getCDZList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.cdzId
      this.cdzpzParams = row
    },
    dialogGroupChange() {
      GetCarList({
        personId: this.cdzpzParams.cdzId,
        groupId: this.cdzpzParams.groupId
      }).then((res) => {
        this.dialogCar = res.result
        let ids = []
        this.dialogCar.forEach(value => {
          if (value.change) {
            ids.push(value.value)
          }
        })
        this.cdzpzParams.carIds = ids
      })
    },
    dialogGroupShowChange() {
      GetCarListForCdz({
        personId: this.cdzpzParams.cdzId,
        groupId: this.cdzpzParams.groupId
      }).then((res) => {
        this.dialogCar = res.result
      })
      this.$forceUpdate();
    },
    handleTagChange() {
      this.$forceUpdate();
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'edit':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑人员车辆配置'
            this.dialogType = 'edit'
            GetCDZGroup({
              personId: this.cdzpzParams.cdzId,
              isShowAll: 0
            }).then((res) => {
              this.dialogGroup = res.result
              this.cdzpzDialog = true
              if (this.queryParams.dept > 0) {
                this.cdzpzParams.groupId = this.queryParams.dept
              } else {
                if (this.dialogGroup.length > 0) {
                  this.cdzpzParams.groupId = this.dialogGroup[0].id
                }
              }
              if (this.cdzpzParams.groupId > 0) {
                this.dialogGroupChange()
              }
            })
          }
          break
        case 'show':
          if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '查看人员管理车辆'
            this.dialogType = 'show'
            GetCDZGroup({
              personId: this.cdzpzParams.cdzId,
              isShowAll: 1
            }).then((res) => {
              this.dialogGroup = res.result
              this.cdzpzShowDialog = true
              if (this.queryParams.dept > 0) {
                this.cdzpzParams.groupId = this.queryParams.dept
              } else {
                if (this.dialogGroup.length > 0) {
                  this.cdzpzParams.groupId = this.dialogGroup[0].id
                }
              }
              if (this.cdzpzParams.groupId > 0) {
                this.dialogGroupChange()
              }
            })
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.cdzpzDialog = false
      this.dialogCar = []
    },
    closeShowDialog () {
      this.cdzpzShowDialog = false
      this.dialogCar = []
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'edit':
          // if (this.$store.state.user.roles.indexOf('NDWCC01CD04QX02') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          this.$refs.cdzpzParams.validate((valid) => {
            if (valid) {
              EditCDZPZ({
                carIds: this.cdzpzParams.carIds.join(','),
                groupId: this.cdzpzParams.groupId,
                personId: this.cdzpzParams.cdzId
              }).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog()
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
