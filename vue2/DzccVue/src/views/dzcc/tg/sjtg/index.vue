<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">标题：</span>
        <el-input v-model="queryParams.title" clearable placeholder="请输入标题" @change="handleChange"></el-input>
        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.startTime"
            type="date"
            placeholder="选择日期"
            clearable
            @change="handleChange"
            value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
        <span class="font-size14">是否开启：</span>
        <el-select v-model="queryParams.isOpen" placeholder="请选择" @change="handleChange">
          <el-option
              v-for="item in isOpens"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-button type="text" icon="el-icon-search" @click="getNoticeList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-plus" @click="openDialog('add')" v-has-permi="['NDWCC01TG01QX02']">新增</el-button>
        <el-button type="text" icon="el-icon-edit" @click="openDialog('edit')" v-has-permi="['NDWCC01TG01QX03']">修改</el-button>
        <el-button type="text" icon="el-icon-delete" @click="openDialog('del')" v-has-permi="['NDWCC01TG01QX04']">删除</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="List"
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select">
        <template slot="noticeType" slot-scope="scope">
          <div v-if="scope.row.noticeType == 0">
            部门
          </div>
          <div v-if="scope.row.noticeType == 1">
            全部
          </div>
        </template>
        <template slot="groupName" slot-scope="scope">
          <div v-if="scope.row.groupName == undefined">
            无
          </div>
          <div v-else>
            {{ scope.row.groupName }}
          </div>
        </template>
        <template slot="isOpen" slot-scope="scope">
          <div v-if="scope.row.isOpen == 1">
            开启
          </div>
          <div v-else>
            未开启
          </div>
        </template>
      </Table>
      <Pagination
          :total="queryParams.total"
          :queryParam="queryParams"
          @handleRefresh="handleCurrentChange"
      />
    </div>
    <el-dialog
        width="60%"
        :visible="noticeDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="noticeParams"
          :model="noticeParams"
          :rules="noticeRules"
          :inline="true"
          label-width="auto"
          v-loading="formLoading"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="标题" class="force-width-60" prop="title">
              <el-input v-model="noticeParams.title" size="mini"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通告类型" class="force-width-60" prop="noticeType">
              <el-select v-model="noticeParams.noticeType" placeholder="请选择通告类型">
                <el-option
                    v-for="item in noticeTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门" class="force-width-60" prop="groupId" v-if="noticeParams.noticeType === 0">
              <el-select v-model="noticeParams.groupId" placeholder="请选择部门" @change="groupChange">
                <el-option
                    v-for="item in dialogGroup"
                    :key="item.id"
                    :label="item.groupname"
                    :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="标题图片" class="force-width-60" prop="titlefile">
            <ImageUpload
                :limit="1"
                :isShowTip="false"
                @uploadSuccessData="uploadSuccessData"
                @deleteData="deleteData"
                :type="1"
                v-model="noticeParams.titlefile"
                uploadUrl="/dzcc/notice/upload"
                :file-size="fileLimit.imgSize"
                :file-type="fileLimit.imgType"
                :data="{type: 1, joinId: `${btnType ===1?noticeParams.id:0}`}"
            />
          </el-form-item>
        </el-row>
        <el-form-item label="视频" class="force-width-60" prop="videofile">
          <VideoUpload
              :limit="1"
              :isShowTip="false"
              @uploadSuccessData="uploadSuccessData"
              @deleteData="deleteData"
              :type="2"
              v-model="noticeParams.videofile"
              uploadUrl="/dzcc/notice/upload"
              :file-size="fileLimit.videoSize"
              :file-type="fileLimit.videoType"
              :data="{type: 2, joinId: `${btnType ===1?noticeParams.id:0}`}"
          />
        </el-form-item>
        <el-form-item label="内容" class="force-width-60" prop="remark" style="height: 260px">
          <QuillEditor
              v-model="noticeParams.remark"
              :uploadUrl="`/dzccCommon/ok`"
          />
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="是否开启" class="force-width-60" prop="isOpen">
              <el-select v-model="noticeParams.isOpen" placeholder="请选择是否开启">
                <el-option
                    v-for="item in isOpen"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始时间" class="force-width-60" prop="startTime">
              <el-date-picker
                  v-model="noticeParams.startTime"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div align="center">
        <el-button size="mini" @click="submitSjtg(dialogType)" :loading="loginLooad">
          <span v-if="!loginLooad">保存</span>
          <span v-else>加载中...</span>
        </el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import { addSjtg, deleteSjtg, editSjtg, fileLimit, getImage, GetNoticeList } from '@/api/dzcc/tg/sjtg'
import ImageUpload from 'components/DFDW/ImageUpload.vue'
import VideoUpload from 'components/DFDW/VideoUpload.vue'
import QuillEditor from 'components/DFDW/QuillEditor.vue'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'sjtg',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {},
  components: {
    ImageUpload,
    VideoUpload,
    QuillEditor,
    Table,
    Pagination,
    Dropdown,
  },
  mounted () {
    this.getNoticeList()
    GetGroupList({ isShowAll: 1, parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
      data = data.slice(1, data.length)
      this.dialogGroup = data
    })
    fileLimit().then((res) => {
      const data = res.result
      data.forEach(value => {
        if (value.content === '图片格式') {
          this.fileLimit.imgType = value.parameter.split(',')
        } else if (value.content === '图片大小（mb）') {
          this.fileLimit.imgSize = parseInt(value.parameter)
        } else if (value.content === '视频格式') {
          this.fileLimit.videoType = value.parameter.split(',')
        } else if (value.content === '视频大小（mb）') {
          this.fileLimit.videoSize = parseInt(value.parameter)
        }
      })
    })
    if (this.$store.getters.roles.indexOf('协同办公-电子出车-车辆总管理') > -1) {
      this.noticeTypes.push({
        value: 1,
        label: '全部'
      })
    }
  },
  data () {
    return {
      tableOptions: [
        {label: '通告类型', prop: 'noticeType', slot: true},
        {label: '部门', prop: 'groupName', slot: true},
        {label: '标题', prop: 'title', tooltip: true},
        {label: '是否开启', prop: 'isOpen', slot: true},
        {label: '开始时间', prop: 'startTime'}
      ],
      realTableOptions: [],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        title: '',
        groupId: -1,
        startTime: null,
        isOpen: -1
      },
      isOpens: [{
        value: -1,
        label: '全部'
      }, {
        value: 0,
        label: '未开启'
      }, {
        value: 1,
        label: '开启'
      }],
      isOpen: [{
        value: 0,
        label: '未开启'
      }, {
        value: 1,
        label: '开启'
      }],
      noticeTypes: [{
        value: 0,
        label: '单位'
      }],
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      noticeParams: {},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      noticeDialog: false,
      noticeRules: {
        title: [{
          required: true,
          message: '请输入标题！',
          trigger: 'blur'
        }],
        titlefile: [{
          required: true,
          message: '标题图不能为空！',
          trigger: 'blur'
        }],
        remark: [{
          required: true,
          message: '请输入通告内容！',
          trigger: 'blur'
        }],
        noticeType: [{
          required: true,
          message: '请选择通告类型！',
          trigger: 'blur'
        }],
        groupId: [{
          required: true,
          message: '请选择部门！',
          trigger: 'blur'
        }],
        isOpen: [{
          required: true,
          message: '请选择是否开启！',
          trigger: 'blur'
        }],
        startTime: [{
          required: true,
          message: '请选择开始时间！',
          trigger: 'blur'
        }]
      },
      // 0新增 1修改
      btnType: 0,
      formLoading: false,
      loginLooad: false,
      // 图片视频限制
      fileLimit: {}
    }
  },
  methods: {
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getNoticeList()
    },
    groupChange () {
      this.dialogGroup.forEach(value => {
        if (value.id === this.noticeParams.groupId) {
          this.noticeParams.groupName = value.groupname
        }
      })
    },
    /** 查询司机公告 */
    getNoticeList () {
      this.loading = true
      this.selectID = 0
      GetNoticeList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    // 获取上传的图片是属于哪一类
    uploadSuccessData (data) {
      if (data[0].type === 1) {
        this.noticeParams.titlefileid = data[0].id
      } else if (data[0].type === 2) {
        this.noticeParams.videofileid = data[0].id
      }
    },
    // 获取删除的图片是属于哪一类
    deleteData (data) {
      if (data.type === 1) {
        this.noticeParams.titlefileid = 0
      } else if (data.type === 2) {
        this.noticeParams.videofileid = 0
      }
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getNoticeList()
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getNoticeList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.noticeParams = row
    },
    /** 打开新增弹窗 */
    async openDialog (type) {
      switch (type) {
        case 'add':
          this.btnType = 0
          this.dialogType = 'add'
          this.dialogTitle = '新增司机通告'
          this.selectID = 0
          this.noticeParams = {}
          this.noticeDialog = true
          break
        case 'edit':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.btnType = 1
            this.dialogTitle = '编辑司机通告'
            this.dialogType = 'edit'
            const {
              titlefileid,
              videofileid
            } = this.noticeParams
            this.noticeDialog = true
            this.formLoading = true
            // 获取图片路径 加载图片
            if (titlefileid) {
              await getImage(titlefileid).then((res) => {
                this.noticeParams.titlefile = res.result
              })
            }
            if (videofileid) {
              await getImage(videofileid).then((res) => {
                this.noticeParams.videofile = res.result
              })
            }
            this.formLoading = false
          }
          break
        case 'del':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            const content = confirm('是否要删除该行数据？')
            if (content) {
              deleteSjtg(this.selectID).then(() => {
                this.$message.success('删除成功！')
                this.getNoticeList()
              })
            }
          }
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getNoticeList()
      this.noticeDialog = false
    },
    /** 提交新增 */
    submitSjtg (type) {
      switch (type) {
        case 'edit':
          this.$refs.noticeParams.validate((valid) => {
            if (valid) {
              this.loginLooad = true
              editSjtg(this.noticeParams).then((res) => {
                this.$message.success('修改成功')
                this.$refs.noticeParams.resetFields()
                this.getNoticeList()
                this.closeDialog()
              }).finally(() => {
                this.loginLooad = false
              })
            } else {
              return false
            }
          })
          break
        case 'add':
          this.$refs.noticeParams.validate((valid) => {
            if (valid) {
              this.loginLooad = true
              addSjtg(this.noticeParams).then((res) => {
                this.$message.success('新增成功')
                this.$refs.noticeParams.resetFields()
                this.getNoticeList()
                this.closeDialog()
              }).finally(() => {
                this.loginLooad = false
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
