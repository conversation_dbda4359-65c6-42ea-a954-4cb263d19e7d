<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.topGroupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.realName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licencePlate" clearable placeholder="请输入车牌"
                  @change="handleChange"></el-input>
        <span class="font-size14">选择月份：</span>
        <el-date-picker
            v-model="queryParams.yearMonth"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange"
        >
        </el-date-picker>
        <span class="font-size14">排序方式：</span>
        <el-select v-model="queryParams.carOrPerson" placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in carPerson"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">车辆标识：</span>
        <el-select v-model="queryParams.carTag" clearable filterable placeholder="请选择车辆标识" @change="handleChange"
                   style="width: 190px">
          <el-option
              :key="-1"
              label="全部"
              :value="-1">
          </el-option>
          <el-option
              :key="0"
              label="临租车辆"
              :value="0">
          </el-option>
          <el-option
              :key="1"
              label="特殊车辆"
              :value="1">
          </el-option>
          <el-option
              :key="2"
              label="产权车辆"
              :value="2">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-button type="text" icon="el-icon-search" @click="getZbbList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-view" @click="openDialog('fbbshow')" v-has-permi="['NDWCC01BB01QX01']">
          查看详情
        </el-button>
        <el-button type="text" icon="el-icon-lock" @click="openDialog('lock')" v-has-permi="['NDWCC01BB01QX03']">
          月份锁定
        </el-button>
        <el-button type="text" icon="el-icon-unlock" @click="openDialog('unlock')" v-has-permi="['NDWCC01BB01QX04']">
          月份解锁
        </el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('fbbdownload')"
                   v-has-permi="['NDWCC01BB01QX02']">导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="fbbList"
          :tableData="fbbList"
          :tableOptions="realTableOptions"
          :loading="fbbLoading"
          :queryParam="queryParams"
          :stripe="false"
          :span-method="objectSpanMethod"
          @getCurrentData="fbbSelect"
          @rowdblclick="openDialog('fbbshow')">
        <template #MONTH="scope">
          <div v-if="scope.row.MONTH < 10">
            {{ scope.row.YEAR }}-0{{ scope.row.MONTH }}
          </div>
          <div v-else>
            {{ scope.row.YEAR }}-{{ scope.row.MONTH }}
          </div>
        </template>
        <template #carTag="scope">
          <div v-if="scope.row.carTag == 0">
            临租车辆
          </div>
          <div v-else-if="scope.row.carTag == 1">
            特殊车辆
          </div>
          <div v-else-if="scope.row.carTag == 2">
            产权车辆
          </div>
          <div v-else>
            -
          </div>
        </template>
        <template #actualMileage="scope">
          <div v-if="scope.row.actualMileage != null">
            {{ scope.row.actualMileage / 1000 }}
          </div>
          <div v-else>
            0
          </div>
        </template>
      </Table>
      <Pagination
          :total="queryParams.total"
          :queryParam="queryParams"
          @handleRefresh="handleCurrentChange"
      />
    </div>
    <el-dialog
        width="40%"
        :visible="lockDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeLockDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ lockDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <Dropdown :columnArr="tableOptionsLock" @getNewArr="getNewArrLock"/>
          </div>
        </div>
      </div>
      <Table
          ref="lockList"
          :tableData="lockList"
          :tableOptions="realTableOptionsLock"
          :loading="lockLoading"
          height="600">
        <template #operate="scope">
          <el-button @click="submitBb(dialogType, scope.row.id)" type="text" size="small" icon="el-icon-unlock">
            解锁
          </el-button>
        </template>
      </Table>
    </el-dialog>
    <el-dialog
        width="45%"
        :visible="fbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closefbbDialog"
        :close-on-click-modal="false">
      <div slot="title" align="left" class="dialogTitle">{{ fbbDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('sbbshow')" v-has-permi="['NDWCC01BB01QX01']">
              查看详情
            </el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('sbbdownload')"
                       v-has-permi="['NDWCC01BB01QX02']">导出
            </el-button>
            <Dropdown :columnArr="tableOptionsSbb" @getNewArr="getNewArrSbb"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="sbbList"
            :tableData="sbbList"
            :tableOptions="realTableOptionsSbb"
            :loading="sbbLoading"
            height="600"
            @getCurrentData="sbbSelect"
            @rowdblclick="openDialog('sbbshow')">
          <template #curDate="scope">
            {{ getDate(scope.row.curDate) }}
          </template>
          <template #realName="scope">
            {{ fbbParams.RealName }}
          </template>
          <template #licencePlate="scope">
            {{ fbbParams.LicencePlate }}
          </template>
          <template #actualMileage="scope">
            <div v-if="scope.row.actualMileage != null">
              {{ scope.row.actualMileage / 1000 }}
            </div>
            <div v-else>
              0
            </div>
          </template>
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="70%"
        :visible="sbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closesbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ sbbDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('tbbshow')" v-has-permi="['NDWCC01BB01QX01']">
              查看详情
            </el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('tbbdownload')"
                       v-has-permi="['NDWCC01BB01QX02']">导出
            </el-button>
            <Dropdown :columnArr="tableOptionsTbb" @getNewArr="getNewArrTbb"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="tbbList"
            :tableData="tbbList"
            :tableOptions="realTableOptionsTbb"
            :loading="tbbLoading"
            height="600"
            @getCurrentData="tbbSelect"
            @rowdblclick="openDialog('tbbshow')">
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="tbbDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="closetbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ tbbDialogTitle }}</div>
      <ccdxq :ccd-params="tbbParams" v-if="tbbDialog"></ccdxq>
    </el-dialog>
  </div>
</template>

<script>
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import {formatMonth, getDate} from '@/utils/tool'
import {
  DownLoadSbb,
  DownLoadTbb,
  DownLoadZbb,
  GetSbbList,
  GettbbList,
  GetZbbList,
  LockMonth,
  ShowLockMonth,
  UnLockMonth
} from '@/api/dzcc/bb/bb'
import ccdxq from '@/components/DZCC/ccdxq'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import dayjs from "dayjs";

const d = new Date()
d.setMonth(d.getMonth(), 0)
export default {
  name: 'bb',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted() {
    GetGroupList({isShowAll: 1, parentId: 1}).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
    this.getZbbList()
  },
  data() {
    return {
      tableOptions: [
        {label: '月份', prop: 'MONTH', slot: true},
        {label: '申请单位', prop: 'TopGroupName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'LicencePlate'},
        {label: '司机', prop: 'RealName'},
        {label: '工作里程', prop: 'workValue'},
        {label: '车辆里程', prop: 'allValue'},
        {label: '车辆标识', prop: 'carTag', slot: true},
        {label: '计算里程', prop: 'actualMileage', slot: true}
      ],
      realTableOptions: [],
      tableOptionsSbb: [
        {label: '日期', prop: 'curDate', slot: true},
        {label: '司机', prop: 'realName', width: 100, slot: true},
        {label: '车牌', prop: 'licencePlate', width: 150, slot: true},
        {label: '出市情况', prop: 'dayInfo', width: 100},
        {label: '计算里程', prop: 'actualMileage', width: 100, slot: true},
        {label: '出车单数', prop: 'ccdNum', width: 100}
      ],
      realTableOptionsSbb: [],
      tableOptionsTbb: [
        {label: '出车单', prop: 'applyNo'},
        {label: '申请人', prop: 'applyUserName'},
        {label: '申请单位', prop: 'applyDeptName'},
        {label: '出车时间', prop: 'ccOpenTime'},
        {label: '目的地', prop: 'addressInfo'},
        {label: '人数', prop: 'applyNum', width: '100px'},
        {label: '天数', prop: 'ccDays', width: '100px'},
        {label: '事由', prop: 'note'},
      ],
      realTableOptionsTbb: [],
      tableOptionsLock: [
        {label: '日期', prop: 'date'},
        {label: '操作', prop: 'operate', slot: true},
      ],
      realTableOptionsLock: [],
      // 初始打开的tabs页面
      editableTabsValue: '1',
      // 可编辑tabs页
      editableTabs: [
        {
          title: '浙B2229H-马梦娜(2022年10月)出车情况',
          name: '2'
        }
      ],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        realName: '',
        topGroupId: -1,
        licencePlate: '',
        yearMonth: formatMonth(d.getTime()),
        carOrPerson: 0,
        carTag: -1,
      },
      // 统计方式
      carPerson: [
        {
          value: 0,
          label: '按车排序'
        }, {
          value: 1,
          label: '按人排序'
        }
      ],
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      fbbList: [],
      // 主报表遮罩层
      fbbLoading: false,
      // 次报表列表
      sbbList: [],
      // 次报表遮罩层
      sbbLoading: false,
      // 三报表列表
      tbbList: [],
      // 三报表遮罩层
      tbbLoading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 主报表窗口
      fbbDialog: false,
      // 选择主报表列表id
      fbbSelectID: 0,
      // 选择主报表列表数据
      fbbParams: {},
      // 主报表窗口标题
      fbbDialogTitle: '',
      // 次报表窗口标题
      sbbDialogTitle: '',
      // 次报表窗口
      sbbDialog: false,
      // 选择次报表列表id
      sbbSelectID: 0,
      // 选择次报表列表数据
      sbbParams: {},
      // 三报表窗口标题
      tbbDialogTitle: '',
      // 三报表窗口
      tbbDialog: false,
      // 选择三报表列表id
      tbbSelectID: 0,
      // 选择三报表列表数据
      tbbParams: {},
      // 锁定窗口
      lockDialog: false,
      // 锁定窗口标题
      lockDialogTitle: '',
      // 锁定列表
      lockList: [],
      // 锁定窗口
      lockLoading: false,
      // 用来记录需要合并行的下标
      mergeObj: {},
      // 表格中的列名
      mergeArr: ['driveId', 'carId'],
      // 表格中合并的列数
      columnIndexs: [-1, -1],
    }
  },
  components: {
    ccdxq,
    Table,
    Pagination,
    Dropdown,
  },
  methods: {
    getDate,
    // tabs页删除
    removeTab(targetName) {
      const tabs = this.editableTabs
      let activeName = this.editableTabsValue
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }
      this.editableTabsValue = activeName
      this.editableTabs = tabs.filter(tab => tab.name !== targetName)
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getZbbList()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getZbbList()
    },
    tableFbbRowClassName() {
      return 'background_white'
    },
    objectSpanMethod({
                       row,
                       column,
                       rowIndex,
                       columnIndex
                     }) {
      if (columnIndex === this.columnIndexs[0] || columnIndex === this.columnIndexs[1]) {
        let value = ''
        if (this.queryParams.carOrPerson === 0) {
          value = 'carId'
        } else {
          value = 'driveId'
        }
        // 判断列的属性
        if (this.mergeArr.indexOf(value) !== -1) {
          // 判断其值是不是为0
          if (this.mergeObj[value][rowIndex]) {
            return [this.mergeObj[value][rowIndex], 1]
          } else {
            // 如果为0则为需要合并的行
            return [0, 0]
          }
        }
      }
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
      this.columnIndexs = [-1, -1]
      this.realTableOptions.forEach((value, index) => {
        if (value.prop === 'RealName') {
          if (this.queryParams.carOrPerson === 1 && index > 0 && this.realTableOptions[index - 1].prop === 'LicencePlate') {
            this.realTableOptions[index] = this.realTableOptions.splice(index - 1, 1, this.realTableOptions[index])[0];
          }
        } else if (value.prop === 'allValue') {
          if (this.queryParams.carOrPerson === 1) {
            value.label = '工作里程'
          } else {
            value.label = '车辆里程'
          }
        } else if (value.prop === 'workValue') {
          if (this.queryParams.carOrPerson === 1) {
            value.label = '车辆里程'
          } else {
            value.label = '工作里程'
          }
        } else if (value.prop === 'LicencePlate') {
          if (this.queryParams.carOrPerson === 0 && index > 0 && this.realTableOptions[index - 1].prop === 'RealName') {
            this.realTableOptions[index] = this.realTableOptions.splice(index - 1, 1, this.realTableOptions[index])[0];
          }
        }
      })
      this.realTableOptions.forEach((value, index) => {
        if (value.prop === 'RealName' && this.queryParams.carOrPerson === 1) {
          this.columnIndexs[0] = index + 1
        } else if (value.prop === 'allValue') {
          this.columnIndexs[1] = index + 1
        } else if (value.prop === 'LicencePlate' && this.queryParams.carOrPerson === 0) {
          this.columnIndexs[0] = index + 1
        }
      })
    },
    getNewArrSbb(newTableOptions) {
      this.realTableOptionsSbb = [...newTableOptions]
    },
    getNewArrTbb(newTableOptions) {
      this.realTableOptionsTbb = [...newTableOptions]
    },
    getNewArrLock(newTableOptions) {
      this.realTableOptionsLock = [...newTableOptions]
    },
    getZbbList() {
      this.fbbLoading = true
      GetZbbList(this.queryParams).then((res) => {
        this.fbbList = res.result.records
        if (this.fbbList !== undefined) {
          this.getSpanArr(this.fbbList)
        }
        this.queryParams.total = res.result.total
        this.getNewArr(this.realTableOptions)
        this.fbbSelectID = 0;
      }).finally(() => {
        this.fbbLoading = false
      })
    },
    // getSpanArr方法
    getSpanArr(data) {
      this.mergeArr.forEach((key, index1) => {
        let count = 0 // 用来记录需要合并行的起始位置
        this.mergeObj[key] = [] // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1)
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key] && item.TopGroupId === data[index - 1].TopGroupId) {
              this.mergeObj[key][count] += 1
              this.mergeObj[key].push(0)
            } else {
              // 如果当前行和上一行其值不相等
              count = index // 记录当前位置
              this.mergeObj[key].push(1) // 重新push 一个 1
            }
          }
        })
      })
      // const carOrPerson = this.queryParams.carOrPerson === 0 ? 'carId' : 'driveId'
      // this.fbbList.forEach((value, index) => {
      //   value.allValue = 0
      //   for (let i = index; i < index + this.mergeObj[carOrPerson][index]; i++) {
      //     value.allValue += this.fbbList[i].workValue
      //   }
      // })
    },
    /** 重置查询车队长 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.fbbSelectID = 0
      this.getZbbList()
    },
    /** 单击表事件 */
    fbbSelect(row) {
      this.fbbSelectID = row.id
      // 列表无id，虚设
      this.fbbSelectID = 1
      this.fbbParams = row
      let Y = this.fbbParams.YEAR
      let M = this.fbbParams.MONTH
      let time = new Date(Y + '-' + M + '-01')
      this.fbbParams.startDate = dayjs(time).format('YYYY-MM-DD')
      if (M == 12) {
        Y = Y + 1
        M = 1
      } else {
        M = M + 1
      }
      time = new Date(Y + '-' + M + '-01')
      this.fbbParams.endDate = dayjs(time).format('YYYY-MM-DD')
    },
    sbbSelect(row) {
      this.sbbSelectID = row.id
      // 列表无id，虚设
      this.sbbSelectID = 1
      this.sbbParams = row
    },
    tbbSelect(row) {
      this.tbbSelectID = row.id
      this.tbbParams = row
    },
    /** 关闭弹窗 */
    closeLockDialog() {
      this.lockDialog = false
    },
    closefbbDialog() {
      this.fbbDialog = false
    },
    closesbbDialog() {
      this.sbbDialog = false
    },
    closetbbDialog() {
      this.tbbDialog = false
    },
    /** 打开弹窗 */
    openDialog(type) {
      switch (type) {
        case 'fbbshow':
          if (this.fbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.fbbDialogTitle = this.fbbParams.LicencePlate + '-' + this.fbbParams.RealName + '(' + this.fbbParams.YEAR + '年' + this.fbbParams.MONTH + '月)出车情况'
            this.dialogType = 'fbbshow'
            this.fbbDialog = true
            this.sbbLoading = true
            GetSbbList(this.fbbParams).then((res) => {
              this.sbbList = res.result
            }).finally(() => {
              this.sbbLoading = false
            })
          }
          break
        case 'fbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.queryParams.topGroupName = value.groupname
            }
          })
          DownLoadZbb(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            let groupName = ''
            this.groupItem.forEach(value => {
              if (value.id === this.queryParams.topGroupId) {
                groupName = value.groupname
              }
            })
            const fileName = groupName + '(' + this.queryParams.yearMonth.substring(0, 7) + ')电子出车报表' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'sbbshow':
          if (this.sbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.sbbDialogTitle = this.fbbParams.LicencePlate + '-' + this.fbbParams.RealName + this.sbbParams.curDate + '出车单'
            this.dialogType = 'sbbshow'
            this.sbbDialog = true
            this.tbbLoading = true
            GettbbList(this.sbbParams).then((res) => {
              this.tbbList = res.result
            }).finally(() => {
              this.tbbLoading = false
            })
          }
          break
        case 'sbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.fbbParams.topGroupName = value.groupname
            }
          })
          DownLoadSbb(this.fbbParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            console.log(this.fbbParams)
            const fileName = this.fbbParams.LicencePlate + '-' + this.fbbParams.RealName + '(' + this.fbbParams.YEAR + '年' + this.fbbParams.MONTH + '月)出车情况' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'tbbshow':
          if (this.tbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.tbbDialogTitle = '查看(' + this.tbbParams.applyNo + ')出车单详情'
            this.dialogType = 'tbbshow'
            if (this.fbbParams.RealName !== this.tbbParams.driverName) {
              this.tbbParams.availName = this.fbbParams.RealName
              this.tbbParams.availPhone = this.fbbParams.TelePhone
              this.tbbParams.isAvial = true
            } else {
              this.tbbParams.isAvial = false
            }
            this.tbbDialog = true
          }
          break
        case 'tbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.sbbParams.groupName = value.groupname
            }
          })
          this.sbbParams.licencePlate = this.fbbParams.LicencePlate
          this.sbbParams.realName = this.fbbParams.RealName
          DownLoadTbb(this.sbbParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = this.fbbParams.LicencePlate + '-' + this.fbbParams.RealName + this.sbbParams.curDate + '出车单' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'lock':
          this.$confirm('确认锁定该月及之前的数据，锁定后无法对数据进行操作！', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(_ => {
            LockMonth({
              groupId: this.queryParams.topGroupId,
              date: this.queryParams.yearMonth
            }).then(() => {
              this.$message.success('锁定成功')
            })
          }).catch(_ => {
          })
          break
        case 'unlock':
          this.lockDialog = true
          this.dialogType = 'unlock'
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.lockDialogTitle = value.groupname + '锁定情况'
            }
          })
          this.lockLoading = true
          ShowLockMonth({
            groupId: this.queryParams.topGroupId
          }).then((res) => {
            this.lockList = res.result
            this.lockList.forEach(value => {
              value.date = value.date.substring(0, 7)
            })
          }).finally(() => {
            this.lockLoading = false
          })
          break
      }
    },
    submitBb(type, id) {
      switch (type) {
        case 'unlock':
          UnLockMonth({id: id}).then(() => {
            this.$message.success('解锁成功！')
            ShowLockMonth({
              groupId: this.queryParams.topGroupId
            }).then((res) => {
              this.lockList = res.result
              this.lockList.forEach(value => {
                value.date = value.date.substring(0, 7)
              })
            })
          })
          break
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>

<style>
.background_white {
  background: #FFFFFF !important;
}
</style>
