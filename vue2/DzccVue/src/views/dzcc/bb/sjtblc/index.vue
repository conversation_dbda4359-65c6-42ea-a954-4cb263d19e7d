<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupid" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licencePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>
        <span class="font-size14">填报人：</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入填报人" @change="handleChange"></el-input>
        <span class="font-size14">日期范围：</span>
        <el-date-picker
            v-model="queryParams.days"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange">
        </el-date-picker>
        <el-button type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('download')" v-has-permi="['NDWCC01SJ02QX02']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="List"
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="Loading"
          :queryParam="queryParams"
          @getCurrentData="Select">
        <template #operate="scope">
          <el-button @click="showPic(scope.row)" type="text" size="small">
            查看照片
          </el-button>
        </template>
      </Table>
      <Pagination
          :total="queryParams.total"
          :queryParam="queryParams"
          @handleRefresh="handleCurrentChange"
      />
    </div>
    <el-dialog
        :visible.sync="dialogVisible"
        title="预览"
        width="800"
        append-to-body
    >
      <img
          :src="imgfile"
          style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getDate } from '@/utils/tool'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import {DownLoad, GetSjtblcList} from "@/api/dzcc/bb/sjtblc";
import {getImage} from "@/api/dzcc/tg/sjtg";
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import Cookies from "js-cookie";

export default {
  name: 'sjlc',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted () {
    GetGroupList({ isShowAll: 1, parentId: 1 }).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
    this.getList()
  },
  data () {
    return {
      tableOptions: [
        {label: '车型', prop: 'carMold'},
        {label: '车牌号', prop: 'licencePlate'},
        {label: '填报人', prop: 'driverName'},
        {label: '里程数', prop: 'mileage'},
        {label: '填报时间', prop: 'date'},
        {label: '操作', prop: 'operate', slot: true}
      ],
      realTableOptions: [],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        groupid: -1,
        licencePlate: '',
        driverName: '',
        days: [getDate((new Date()).getTime() - 1000 * 60 * 60 * 24 * 30), getDate()],
        beginDate: getDate((new Date()).getTime() - 1000 * 60 * 60 * 24 * 30),
        overDate: getDate()
      },
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      List: [],
      // 主报表遮罩层
      Loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 主报表窗口
      Dialog: false,
      // 选择主报表列表id
      SelectID: 0,
      // 选择主报表列表数据
      Params: {},
      // 窗口类别
      DialogType: '',
      // 主报表窗口标题
      DialogTitle: '',
      // 预览图片路径
      imgfile: '',
      dialogVisible: false,
    }
  },
  components: {
    Table,
    Pagination,
    Dropdown,
  },
  methods: {
    /** queryParam事件 */
    handleChange () {
      this.queryParams.beginDate = this.queryParams.days[0]
      this.queryParams.overDate = this.queryParams.days[1]
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    getList () {
      this.Loading = true
      GetSjtblcList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.Loading = false
      })
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.SelectID = 0
      this.getList()
    },
    /** 单击表事件 */
    Select (row) {
      this.SelectID = row.id
      this.Params = row
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.Dialog = false
    },
    /** 打开弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupid) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = this.queryParams.groupName + '(' + this.queryParams.beginDate.substring(0, 10) + '-' + this.queryParams.overDate.substring(0, 10) + ')' + '司机填报里程' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
      }
    },
    /** 查看照片 */
    async showPic (row) {
      const {
        imgFileId
      } = row
      // 获取图片路径 加载图片
      if (imgFileId) {
        await getImage(imgFileId).then((res) => {
          this.imgfile = '../../../app/static/Upload/FileManage/Dzcc/' + res.result.filepath+ '?header=1&token=' + Cookies.get('jtoken')
          this.dialogVisible = true
        })
      } else {
        this.$message.warning('无图片展示')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
