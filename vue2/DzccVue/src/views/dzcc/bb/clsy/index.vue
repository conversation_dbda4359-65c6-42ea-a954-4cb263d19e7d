<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupid" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">车型：</span>
        <el-input v-model="queryParams.carMold" clearable placeholder="请输入车型" @change="handleChange"></el-input>
        <span class="font-size14">月份范围：</span>
        <el-date-picker
            v-model="queryParams.days"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange">
        </el-date-picker>
        <el-button type="text" icon="el-icon-search" @click="getClsyList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('download')" v-has-permi="['NDWCC01SY01QX02']">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="clsyList"
          :tableData="clsyList"
          :tableOptions="realTableOptions"
          :loading="clsyLoading"
          :queryParam="queryParams"
          @getCurrentData="clsySelect">
      </Table>
    </div>
  </div>
</template>

<script>
import {GetGroupList} from "@/api/dzcc/clgl/clgl";
import {formatMonth, getDate} from "@/utils/tool";
import {DownLoad, GetClsyList} from "@/api/dzcc/bb/clsy";
import Table from '@/components/MainTable'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: "clsy",
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted () {
    GetGroupList({ isShowAll: 1, parentId: 1 }).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
    this.getClsyList()
  },
  data () {
    return {
      tableOptions: [
        {label: '车型', prop: 'carMold'},
      ],
      realTableOptions: [],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        groupid: -1,
        days: [getDate(), getDate()],
        beginDate: formatMonth(),
        overDate: formatMonth(),
        carMold: ''
      },
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      clsyList: [],
      // 主报表遮罩层
      clsyLoading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 主报表窗口
      clsyDialog: false,
      // 选择主报表列表id
      clsySelectID: 0,
      // 选择主报表列表数据
      clsyParams: {},
      // 窗口类别
      DialogType: '',
      // 主报表窗口标题
      clsyDialogTitle: '',
      // 表格标题
      titleList: [],
    }
  },
  components: {
    Table,
    Dropdown,
  },
  methods: {
    /** queryParam事件 */
    handleChange () {
      this.queryParams.beginDate = this.queryParams.days[0]
      this.queryParams.overDate = this.queryParams.days[1]
      this.queryParams.pageNum = 1
      this.getClsyList()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getClsyList()
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    getClsyList () {
      this.clsyLoading = true
      this.titleList = []
      const beginDate = new Date(formatMonth(this.queryParams.beginDate))
      const overDate = new Date(formatMonth(this.queryParams.overDate))
      while (beginDate <= overDate) {
        let timeStr = beginDate.getFullYear() + '年'
        const Month = beginDate.getMonth() + 1
        if (Month < 10) {
          timeStr += '0'
        }
        timeStr += (beginDate.getMonth() + 1) + '月'
        this.titleList.push(timeStr)
        beginDate.setMonth(beginDate.getMonth() + 1)
      }
      this.titleList.sort()
      this.tableOptions = [{label: '车型', prop: 'carMold'},]
      this.titleList.forEach(value => {
        this.tableOptions.push({label: value + '车辆总数', prop: value})
      })
      GetClsyList(this.queryParams).then((res) => {
        // this.clsyList = res.result.records
        // this.queryParams.total = res.result.total
        this.clsyList = res.result
      }).finally(() => {
        this.clsyLoading = false
      })
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.clsySelectID = 0
      this.getClsyList()
    },
    /** 单击表事件 */
    clsySelect (row) {
      this.clsySelectID = row.id
      this.clsyParams = row
      this.$nextTick(() => {
        this.$refs.clsyList.setCurrentRow(row)
      })
    },
    /** 关闭弹窗 */
    closeClydDialog () {
      this.clsyDialog = false
    },
    /** 打开弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          if (this.clsyList == null || this.clsyList.length == 0) {
            this.$message.error('当前无数据，无法导出')
            break
          }
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupid) {
              this.queryParams.groupname = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据2023-01
            const dateRange = this.queryParams.beginDate.substring(0, 4) + '年' + this.queryParams.beginDate.substring(5, 7) + '月至' + this.queryParams.overDate.substring(0, 4) + '年' + this.queryParams.overDate.substring(5, 7) + '月'
            const fileName = this.queryParams.groupname + dateRange + '车辆使用情况' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
