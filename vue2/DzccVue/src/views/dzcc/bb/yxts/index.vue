<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.applyTopDeptId" placeholder="请选择部门" @change="handleChange" style="width: 180px">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入姓名" @change="handleChange" style="width: 180px"></el-input>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌" @change="handleChange" style="width: 180px"></el-input>
        <span class="font-size14">日期范围：</span>
        <el-date-picker
            v-model="queryParams.days"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange"
            style="width: 320px">
        </el-date-picker>
        <span class="font-size14">排序方式：</span>
        <el-select v-model="queryParams.carOrPerson" placeholder="请选择管理部门" @change="handleChange" style="width: 180px">
          <el-option
              v-for="item in carPerson"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">车辆标识：</span>
        <el-select v-model="queryParams.carTag" clearable filterable placeholder="请选择车辆标识" @change="handleChange" style="width: 180px">
          <el-option
              :key="-1"
              label="全部"
              :value="-1">
          </el-option>
          <el-option
              :key="0"
              label="临租车辆"
              :value="0">
          </el-option>
          <el-option
              :key="1"
              label="特殊车辆"
              :value="1">
          </el-option>
          <el-option
              :key="2"
              label="产权车辆"
              :value="2">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-checkbox v-model="queryParams.isQtcl" @change="handleChange">其他车辆</el-checkbox>
        <el-button type="text" icon="el-icon-search" @click="getCcdsBBList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-view" @click="openDialog('fbbshow')" v-has-permi="['NDWCC01CD05QX03']">
          查看详情
        </el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('fbbdownload')"
                   v-has-permi="['NDWCC01CD05QX02']">导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="fbbList"
          :tableData="fbbList"
          :tableOptions="realTableOptions"
          :loading="fbbLoading"
          :queryParam="queryParams"
          :stripe="false"
          :span-method="objectSpanMethod"
          @getCurrentData="fbbSelect"
          @rowdblclick="openDialog('fbbshow')">
        <template #carTag="scope">
          <div v-if="scope.row.carTag == 0">
            临租车辆
          </div>
          <div v-else-if="scope.row.carTag == 1">
            特殊车辆
          </div>
          <div v-else-if="scope.row.carTag == 2">
            产权车辆
          </div>
          <div v-else>
            -
          </div>
        </template>
      </Table>
      <Pagination
          :total="queryParams.total"
          :queryParam="queryParams"
          @handleRefresh="handleCurrentChange"
      />
    </div>
    <el-dialog
        width="40%"
        :visible="fbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closefbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ fbbDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('sbbshow')" v-has-permi="['NDWCC01CD05QX03']">
              查看详情
            </el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('sbbdownload')"
                       v-has-permi="['NDWCC01CD05QX02']">导出
            </el-button>
            <Dropdown :columnArr="tableOptionsSbb" @getNewArr="getNewArrSbb"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="sbbList"
            :tableData="sbbList"
            :tableOptions="realTableOptionsSbb"
            :loading="sbbLoading"
            @getCurrentData="sbbSelect"
            @rowdblclick="openDialog('sbbshow')">
          <template slot="realName" slot-scope="scope">
            {{ fbbParams.driverName }}
          </template>
          <template slot="licencePlate" slot-scope="scope">
            {{ fbbParams.licensePlate }}
          </template>
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="70%"
        :visible="sbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closesbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ sbbDialogTitle }}</div>
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button type="text" icon="el-icon-view" @click="openDialog('tbbshow')" v-has-permi="['NDWCC01CD05QX03']">
              查看详情
            </el-button>
            <el-button type="text" icon="el-icon-download" @click="openDialog('tbbdownload')"
                       v-has-permi="['NDWCC01CD05QX02']">导出
            </el-button>
            <Dropdown :columnArr="tableOptionsTbb" @getNewArr="getNewArrTbb"/>
          </div>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="tbbList"
            :tableData="tbbList"
            :tableOptions="realTableOptionsTbb"
            :loading="tbbLoading"
            @getCurrentData="tbbSelect"
            @rowdblclick="openDialog('tbbshow')">
        </Table>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="tbbDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="closetbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ tbbDialogTitle }}</div>
      <ccdxq :ccd-params="tbbParams" v-if="tbbDialog"></ccdxq>
    </el-dialog>
  </div>
</template>

<script>
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import {getDate} from '@/utils/tool'
import ccdxq from '@/components/DZCC/ccdxq'
import {DownLoadCcdsBB, GetCcdsBBList} from '@/api/dzcc/bb/yxtsbb'
import {DownLoadSbb, DownLoadTbb, GetSbbList, GettbbList} from '@/api/dzcc/bb/bb'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

const d = new Date()
d.setMonth(d.getMonth(), 0)
export default {
  name: 'yxts',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted() {
    GetGroupList({isShowAll: 1, parentId: 1}).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
    this.getCcdsBBList()
  },
  data() {
    return {
      tableOptions: [
        {label: '申请单位', prop: 'applyTopDeptName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'licensePlate'},
        {label: '司机', prop: 'driverName'},
        {label: '有效单数', prop: 'validDays'},
        {label: '有效总单数', prop: 'validDaySum'},
        {label: '车辆标识', prop: 'carTag', slot: true},
      ],
      realTableOptions: [],
      tableOptionsSbb: [
        {label: '日期', prop: 'curDate'},
        {label: '司机', prop: 'realName', slot: true},
        {label: '车牌', prop: 'licencePlate', slot: true},
        {label: '出市情况', prop: 'dayInfo'},
      ],
      realTableOptionsSbb: [],
      tableOptionsTbb: [
        {label: '出车单', prop: 'applyNo'},
        {label: '申请人', prop: 'applyUserName'},
        {label: '申请单位', prop: 'applyDeptName'},
        {label: '出车时间', prop: 'ccOpenTime'},
        {label: '目的地', prop: 'addressInfo'},
        {label: '人数', prop: 'applyNum', width: '100px'},
        {label: '天数', prop: 'ccDays', width: '100px'},
        {label: '事由', prop: 'note'},
      ],
      realTableOptionsTbb: [],
      // 初始打开的tabs页面
      editableTabsValue: '1',
      // 可编辑tabs页
      editableTabs: [
        {
          title: '浙B2229H-马梦娜(2022年10月)出车情况',
          name: '2'
        }
      ],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        driverName: '',
        applyTopDeptId: -1,
        licensePlate: '',
        days: [getDate(), getDate()],
        ccOpenTime: getDate(),
        executeTime: getDate(),
        carOrPerson: 0,
        isQtcl: false,
        carTag: -1,
      },
      // 统计方式
      carPerson: [
        {
          value: 0,
          label: '按车排序'
        }, {
          value: 1,
          label: '按人排序'
        }
      ],
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      fbbList: [],
      // 主报表遮罩层
      fbbLoading: false,
      // 次报表列表
      sbbList: [],
      // 次报表遮罩层
      sbbLoading: false,
      // 三报表列表
      tbbList: [],
      // 三报表遮罩层
      tbbLoading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 主报表窗口
      fbbDialog: false,
      // 选择主报表列表id
      fbbSelectID: 0,
      // 选择主报表列表数据
      fbbParams: {},
      // 窗口类别
      DialogType: '',
      // 主报表窗口标题
      fbbDialogTitle: '',
      // 次报表窗口标题
      sbbDialogTitle: '',
      // 次报表窗口
      sbbDialog: false,
      // 选择次报表列表id
      sbbSelectID: 0,
      // 选择次报表列表数据
      sbbParams: {},
      // 三报表窗口标题
      tbbDialogTitle: '',
      // 三报表窗口
      tbbDialog: false,
      // 选择三报表列表id
      tbbSelectID: 0,
      // 选择三报表列表数据
      tbbParams: {},
      // 用来记录需要合并行的下标
      mergeObj: {},
      // 表格中的列名
      mergeArr: ['driveId', 'carId'],
      // 表格中合并的列数
      columnIndexs: [-1, -1],
    }
  },
  components: {
    ccdxq,
    Table,
    Pagination,
    Dropdown,
  },
  methods: {
    // tabs页删除
    removeTab(targetName) {
      const tabs = this.editableTabs
      let activeName = this.editableTabsValue
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }
      this.editableTabsValue = activeName
      this.editableTabs = tabs.filter(tab => tab.name !== targetName)
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.ccOpenTime = this.queryParams.days[0]
      this.queryParams.executeTime = this.queryParams.days[1]
      this.queryParams.pageNum = 1
      this.getCcdsBBList()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getCcdsBBList()
    },
    tableFbbRowClassName() {
      return 'background_white'
    },
    objectSpanMethod({
                       row,
                       column,
                       rowIndex,
                       columnIndex
                     }) {
      if (columnIndex === this.columnIndexs[0] || columnIndex === this.columnIndexs[1]) {
        let value = ''
        if (this.queryParams.carOrPerson === 0) {
          value = 'carId'
        } else {
          value = 'driveId'
        }
        // 判断列的属性
        if (this.mergeArr.indexOf(value) !== -1) {
          // 判断其值是不是为0
          if (this.mergeObj[value][rowIndex]) {
            return [this.mergeObj[value][rowIndex], 1]
          } else {
            // 如果为0则为需要合并的行
            return [0, 0]
          }
        }
      }
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
      this.columnIndexs = [-1, -1]
      this.realTableOptions.forEach((value, index) => {
        if (value.prop === 'driverName') {
          if (this.queryParams.carOrPerson === 1 && index > 0 && this.realTableOptions[index - 1].prop === 'licensePlate') {
            this.realTableOptions[index] = this.realTableOptions.splice(index - 1, 1, this.realTableOptions[index])[0];
          }
        } else if (value.prop === 'licensePlate') {
          if (this.queryParams.carOrPerson === 0 && index > 0 && this.realTableOptions[index - 1].prop === 'driverName') {
            this.realTableOptions[index] = this.realTableOptions.splice(index - 1, 1, this.realTableOptions[index])[0];
          }
        }
      })
      this.realTableOptions.forEach((value, index) => {
        if (value.prop === 'driverName' && this.queryParams.carOrPerson === 1) {
          this.columnIndexs[0] = index + 1
        } else if (value.prop === 'validDaySum') {
          this.columnIndexs[1] = index + 1
        } else if (value.prop === 'licensePlate' && this.queryParams.carOrPerson === 0) {
          this.columnIndexs[0] = index + 1
        }
      })
    },
    getNewArrSbb(newTableOptions) {
      this.realTableOptionsSbb = [...newTableOptions]
    },
    getNewArrTbb(newTableOptions) {
      this.realTableOptionsTbb = [...newTableOptions]
    },
    getCcdsBBList() {
      this.fbbLoading = true
      GetCcdsBBList(this.queryParams).then((res) => {
        this.fbbList = res.result.records
        if (this.fbbList !== undefined) {
          this.getSpanArr(this.fbbList)
        }
        this.queryParams.total = res.result.total
        this.fbbSelectID = 0;
      }).finally(() => {
        this.fbbLoading = false
      })
    },
    // getSpanArr方法
    getSpanArr(data) {
      this.mergeArr.forEach((key, index1) => {
        let count = 0 // 用来记录需要合并行的起始位置
        this.mergeObj[key] = [] // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1)
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1
              this.mergeObj[key].push(0)
            } else {
              // 如果当前行和上一行其值不相等
              count = index // 记录当前位置
              this.mergeObj[key].push(1) // 重新push 一个 1
            }
          }
        })
      })
      const carOrPerson = this.queryParams.carOrPerson === 0 ? 'carId' : 'driveId'
      this.fbbList.forEach((value, index) => {
        value.allValue = 0
        for (let i = index; i < index + this.mergeObj[carOrPerson][index]; i++) {
          value.allValue += this.fbbList[i].workValue
        }
      })
    },
    /** 重置查询车队长 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.fbbSelectID = 0
      this.getCcdsBBList()
    },
    /** 单击表事件 */
    fbbSelect(row) {
      this.fbbSelectID = row.id
      // 列表无id，虚设
      this.fbbSelectID = 1
      this.fbbParams = row
      this.fbbParams.startDate = this.queryParams.ccOpenTime
      const date = new Date(this.queryParams.executeTime)
      const time = new Date(date.getTime() + 24 * 60 * 60 * 1000)
      this.fbbParams.endDate = time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate() + ' 00:00:00'
      this.fbbParams.realName = this.fbbParams.driverName
      this.fbbParams.topGroupId = this.fbbParams.applyTopDeptId
      this.fbbParams.topGroupName = this.fbbParams.applyTopDeptName
      this.fbbParams.licencePlate = this.fbbParams.licensePlate
    },
    sbbSelect(row) {
      this.sbbSelectID = row.id
      // 列表无id，虚设
      this.sbbSelectID = 1
      this.sbbParams = row
    },
    tbbSelect(row) {
      this.tbbSelectID = row.id
      this.tbbParams = row
    },
    closefbbDialog() {
      this.fbbDialog = false
    },
    closesbbDialog() {
      this.sbbDialog = false
    },
    closetbbDialog() {
      this.tbbDialog = false
    },
    /** 打开弹窗 */
    openDialog(type) {
      switch (type) {
        case 'fbbshow':
          if (this.fbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            let date = new Date(this.fbbParams.startDate)
            const start = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
            date = new Date(this.fbbParams.endDate)
            date = new Date(date.getTime() - 24 * 60 * 60 * 1000)
            const end = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
            this.fbbDialogTitle = this.fbbParams.licensePlate + '-' + this.fbbParams.driverName + '(' + start + '至' + end + ')出车情况'
            this.dialogType = 'fbbshow'
            this.fbbDialog = true
            this.sbbLoading = true
            GetSbbList(this.fbbParams).then((res) => {
              this.sbbList = res.result
            }).finally(() => {
              this.sbbLoading = false
            })
          }
          break
        case 'fbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.applyTopDeptId) {
              this.queryParams.applyTopDeptName = value.groupname
            }
          })
          DownLoadCcdsBB(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const groupName = this.queryParams.applyTopDeptName
            const fileName = groupName + '(' + this.queryParams.ccOpenTime + '-' + this.queryParams.executeTime + ')有效天数报表' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'sbbshow':
          if (this.sbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.sbbDialogTitle = this.fbbParams.licencePlate + '-' + this.fbbParams.realName + this.sbbParams.curDate + '出车单'
            this.dialogType = 'sbbshow'
            this.sbbDialog = true
            this.tbbLoading = true
            GettbbList(this.sbbParams).then((res) => {
              this.tbbList = res.result
            }).finally(() => {
              this.tbbLoading = false
            })
          }
          break
        case 'sbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.fbbParams.topGroupName = value.groupname
            }
          })
          DownLoadSbb(this.fbbParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            let date = new Date(this.fbbParams.startDate)
            const start = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
            date = new Date(this.fbbParams.endDate)
            date = new Date(date.getTime() - 24 * 60 * 60 * 1000)
            const end = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
            const fileName = this.fbbParams.licencePlate + '-' + this.fbbParams.realName + '(' + start + '至' + end + ')出车情况' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'tbbshow':
          if (this.tbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.tbbDialogTitle = '查看(' + this.tbbParams.applyNo + ')出车单详情'
            this.dialogType = 'tbbshow'
            if (this.fbbParams.realName !== this.tbbParams.driverName) {
              this.tbbParams.availName = this.fbbParams.realName
              this.tbbParams.availPhone = this.fbbParams.telePhone
              this.tbbParams.isAvial = true
            } else {
              this.tbbParams.isAvial = false
            }
            this.tbbDialog = true
          }
          break
        case 'tbbdownload':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.sbbParams.groupName = value.groupname
            }
          })
          this.sbbParams.licencePlate = this.fbbParams.licencePlate
          this.sbbParams.realName = this.fbbParams.realName
          DownLoadTbb(this.sbbParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = this.fbbParams.licencePlate + '-' + this.fbbParams.realName + this.sbbParams.curDate + '出车单' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
      }
    },
    submitBb(type) {
      switch (type) {
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }

    .el-checkbox {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>

<style>
.background_white {
  background: #FFFFFF !important;
}
</style>
