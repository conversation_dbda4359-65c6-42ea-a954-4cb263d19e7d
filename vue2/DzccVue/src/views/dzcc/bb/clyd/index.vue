<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>
        <span class="font-size14">日期范围：</span>
        <el-date-picker
            v-model="queryParams.days"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange">
        </el-date-picker>
        <el-button type="text" icon="el-icon-search" @click="getClydList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-view" @click="openDialog('guiji')" v-has-permi="['NDWCC01CY02QX03']">查看轨迹</el-button>
        <el-button type="text" icon="el-icon-download" @click="openDialog('download')" v-has-permi="['NDWCC01CY02QX02']">导出</el-button>
        <el-button type="text" icon="el-icon-view" @click="openDialog('carMove')" v-has-permi="['NDWCC01CY02QX04']">移动信息</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="clydList"
          :tableData="clydList"
          :tableOptions="realTableOptions"
          :loading="clydLoading"
          :queryParam="queryParams"
          @getCurrentData="clydSelect"
          @rowdblclick="openDialog('guiji')">
        <template slot="driverName" slot-scope="scope">
          <div v-if="scope.row.driverName == null || scope.row.driverName == ''">
            --
          </div>
          <div v-else>
            {{ scope.row.driverName }}
          </div>
        </template>
        <template slot="StartMoveTime" slot-scope="scope">
          <div v-if="scope.row.StartMoveTime == null">
            --
          </div>
          <div v-else>
            {{ scope.row.StartMoveTime }}
          </div>
        </template>
        <template slot="EndMoveTime" slot-scope="scope">
          <div v-if="scope.row.EndMoveTime == null">
            --
          </div>
          <div v-else>
            {{ scope.row.EndMoveTime }}
          </div>
        </template>
      </Table>
      <Pagination
          :total="queryParams.total"
          :queryParam="queryParams"
          @handleRefresh="handleCurrentChange"
      />
    </div>
    <el-dialog
        width="30%"
        :visible="clydDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeClydDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ clydDialogTitle }}</div>
      <Table
          ref="lockList"
          :tableData="carMoveList"
          :tableOptions="realTableOptionsCarMove"
          :loading="carMoveLoading"
          height="500">
      </Table>
    </el-dialog>
  </div>
</template>

<script>
import { dateFormat } from '@/utils/tool'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import {CarMove, DownLoad, GetClydList} from '@/api/dzcc/bb/clyd'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'clyd',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted () {
    GetGroupList({ isShowAll: 1, parentId: 1 }).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
    this.getClydList()
  },
  data () {
    return {
      tableOptions: [
        {label: '申请单位', prop: 'groupName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'licensePlate'},
        {label: '司机', prop: 'driverName', slot: true},
        {label: '开始移动时间', prop: 'StartMoveTime', slot: true},
        {label: '结束移动时间', prop: 'EndMoveTime', slot: true}
      ],
      realTableOptions: [],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        groupId: -1,
        licensePlate: '',
        days: [dateFormat(), dateFormat()],
        StartMoveTime: dateFormat(),
        EndMoveTime: dateFormat(),
        driverName: ''
      },
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      clydList: [],
      // 主报表遮罩层
      clydLoading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 主报表窗口
      clydDialog: false,
      // 选择主报表列表id
      clydSelectID: 0,
      // 选择主报表列表数据
      clydParams: {},
      // 窗口类别
      DialogType: '',
      // 主报表窗口标题
      clydDialogTitle: '',
      // 轨迹路径
      locus: {
        path: '/dzcc/locus',
        query: {
          carId: 0,
          startDate: '',
          endDate: ''
        }
      },
      // 移动信息
      carMoveList: [],
      carMoveLoading: false,
      realTableOptionsCarMove: [
        {label: '日期', prop: 'curDate'},
        {label: '省', prop: 'provincesName'},
        {label: '市', prop: 'cityName'},
        {label: '区', prop: 'areaName'},
      ],
    }
  },
  components: {
    Table,
    Pagination,
    Dropdown,
  },
  methods: {
    /** queryParam事件 */
    handleChange () {
      this.queryParams.StartMoveTime = this.queryParams.days[0]
      this.queryParams.EndMoveTime = this.queryParams.days[1]
      this.queryParams.pageNum = 1
      this.getClydList()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getClydList()
    },
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    getClydList () {
      this.clydLoading = true
      GetClydList(this.queryParams).then((res) => {
        this.clydList = res.result.records
        this.queryParams.total = res.result.total
        this.clydSelectID = 0;
      }).finally(() => {
        this.clydLoading = false
      })
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.clydSelectID = 0
      this.getClydList()
    },
    /** 单击表事件 */
    clydSelect (row) {
      this.locus.query.carId = row.carId
      this.locus.query.licensePlate = row.licensePlate
      this.locus.query.startDate = this.queryParams.StartMoveTime.substring(0, 10)
      this.locus.query.endDate = this.queryParams.EndMoveTime.substring(0, 10)
      this.clydSelectID = row.id
      this.clydParams = row
    },
    /** 关闭弹窗 */
    closeClydDialog () {
      this.clydDialog = false
    },
    /** 打开弹窗 */
    openDialog (type) {
      switch (type) {
        case 'guiji':
          if (this.clydSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            // this.$router.push(this.locus)
            const newpage = this.$router.resolve(this.locus)
            window.open(newpage.href, '_blank')
          }
          break
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            const blob = res
            // 构造一个blob对象来处理数据
            const fileName = this.queryParams.groupName + '(' + this.queryParams.StartMoveTime.substring(0, 10) + '-' + this.queryParams.EndMoveTime.substring(0, 10) + ')' + '车辆移动情况' + '.xlsx'
            // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // IE10以上支持blob但是依然不支持 download
            if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
              const link = document.createElement('a') // 创建a标签
              link.download = fileName // a标签添加属性
              link.style.display = 'none'
              link.href = URL.createObjectURL(blob)
              document.body.appendChild(link)
              link.click() // 执行下载
              URL.revokeObjectURL(link.href) // 释放url
              document.body.removeChild(link) // 释放标签
            } else { // 其他浏览器
              navigator.msSaveBlob(blob, fileName)
            }
          })
          break
        case 'carMove':
          const params = {
            StartMoveTime: this.queryParams.StartMoveTime,
            EndMoveTime: this.queryParams.EndMoveTime,
            carId: this.clydParams.carId,
            driverId: this.clydParams.driverId
          }
          this.carMoveLoading = true
          CarMove(params).then((res) => {
            this.carMoveList = res.result
            this.DialogType = 'carMove'
            this.clydDialogTitle = '车辆移动信息'
            this.clydDialog = true
          }).finally(() => {
            this.carMoveLoading = false
          })
          break;
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
