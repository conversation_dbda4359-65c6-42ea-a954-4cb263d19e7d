<template>
  <div style="width: 100%;height: 100%">
    <iframe :src="locus" id="qt" scrolling="no" frameborder="0"
            style="width: 100%;height: 1000px"></iframe>
  </div>
</template>

<script>
import {checkCar<PERSON>erson} from "@/api/dzcc/ccd/clgj";

export default {
  name: 'locus',
  mounted () {
    checkCarPerson({
      carId: this.$route.query.carId,
      licensePlate: this.$route.query.licensePlate
    }).then(() => {
      const a = process.env.NODE_ENV === 'production' ? '' : '/'
      this.locus = a + this.locus + '?carId=' + this.$route.query.carId + '&licensePlate=' + this.$route.query.licensePlate
      if (this.$route.query.startDate !== '' || this.$route.query.startDate !== undefined) {
        this.locus += '&startDate=' + this.$route.query.startDate
      }
      if (this.$route.query.endDate !== '' || this.$route.query.endDate !== undefined) {
        this.locus += '&endDate=' + this.$route.query.endDate
      }
      this.locus = encodeURI(this.locus)
    }).catch(() => {
      this.$confirm('车辆信息不存在或当前车辆未在管理范围内，确认回退?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).finally(() => {
        this.$router.back()
      })
    })
  },
  destroyed () {
    this.locus = 'static/dzcc/html/InfluxDB/history.html'
  },
  data () {
    return {
      // 轨迹路径
      locus: 'static/dzcc/html/InfluxDB/history.html'
    }
  }
}
</script>

<style scoped>

</style>
