<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.groupId" filterable clearable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>
        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getList"
        >查询
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01GJ02QX02']"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>
</template>

<script>
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import {downLoad, getDate} from '@/utils/tool'
import {DownLoad, getNotLocusList} from "@/api/dzcc/gj/wgjgj";
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

const d = new Date()
d.setMonth(d.getMonth(), 0)
export default {
  name: 'wgjgj',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {
    Table, Pagination, Dropdown
  },
  computed: {},
  mounted () {
    this.getList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        licensePlate: '',
        groupId: -1,
        time: [getDate((new Date()).getTime() - 1000 * 60 * 60 * 24 * 30), getDate()],
        beginDate: getDate((new Date()).getTime() - 1000 * 60 * 60 * 24 * 30),
        overDate: getDate()
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      clickParams: {},
      dialogType: '',
      dialogTitle: '',
      dialog: false,
      realTableOptions:[],
      tableOptions: [
        {label: '部门', prop: 'groupName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'licensePlate'},
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询插拔记录 */
    getList () {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.beginDate = null
        this.queryParams.overDate = null
      } else {
        this.queryParams.beginDate = this.queryParams.time[0]
        this.queryParams.overDate = this.queryParams.time[1]
      }
      getNotLocusList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.clickParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, this.queryParams.groupName + '(' + this.queryParams.beginDate + '至' + this.queryParams.overDate + ')' + '无轨迹告警.xlsx')
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.dialog = false
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
