<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.groupId" filterable clearable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>

        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>

        <span class="font-size14">类别：</span>
        <el-select v-model="queryParams.type" placeholder="请选择类别" @change="handleChange">
          <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>

        <span class="font-size14">审批状态：</span>
        <el-select v-model="queryParams.approveState" placeholder="请选择审批状态" @change="handleChange">
          <el-option
              v-for="item in approveStateList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getMoveLcList"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01CY03QX02']"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
      >

        <template slot="approveState" slot-scope="scope">
          <div v-if="scope.row.approveState == 0">
            审批中
          </div>
          <div v-if="scope.row.approveState == 1">
            同意
          </div>
          <div v-if="scope.row.approveState == 2">
            驳回
          </div>
        </template>

        <template slot="type" slot-scope="scope">
          <div v-if="scope.row.type == 1">
            非规定时间出行
          </div>
          <div v-if="scope.row.type == 2">
            未带工单出行车辆
          </div>
          <div v-if="scope.row.type == 3">
            七日内无行驶轨迹
          </div>
          <div v-if="scope.row.type == 4">
            超时未归场
          </div>
          <div v-if="scope.row.type == 5">
            跨界
          </div>
          <div v-if="scope.row.type == 6">
            节假日动车
          </div>
          <div v-if="scope.row.type == 7">
            被拔
          </div>
          <div v-if="scope.row.type == 8">
            车辆异常
          </div>
        </template>

      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>
</template>
<script>
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import {getMoveLcList, DownLoad} from '@/api/dzcc/gj/yccllc'
import {downLoad} from '@/utils/tool'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

export default {
  name: 'yccllc',
  components: {Table, Pagination, Dropdown},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getMoveLcList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        licensePlate: '',
        driveName: '',
        groupId: -1,
        time: [],
        startMoveTime: null,
        endMoveTime: null,
        type: 0,
        approveState: -1
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cbjlParams: {},
      dialogType: '',
      dialogTitle: '',
      cbjlDialog: false,
      typeList: [
        {value: 0, label: '全部'},
        {value: 1, label: '非规定时间出行'},
        {value: 2, label: '未带工单出行车辆'},
        {value: 3, label: '七日内无行驶轨迹'},
        {value: 4, label: '超时未归场'},
        {value: 6, label: '节假日动车'},
        {value: 7, label: '被拔'},
        {value: 8, label: '车辆异常'}
      ],
      approveStateList: [
        {value: -1, label: '全部'},
        {value: 0, label: '审批中'},
        {value: 1, label: '同意'},
        {value: 2, label: '驳回'}
      ],
      realTableOptions:[],
      tableOptions: [
        {label: '部门', prop: 'groupName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'licensePlate'},
        {label: '司机', prop: 'driveName'},
        {label: '车队长审批', prop: 'approveUserName'},
        {label: '审批意见', prop: 'approveNote'},
        {label: '审批时间', prop: 'approveTime'},
        {label: '审批状态', prop: 'approveState',slot: true},
        {label: '创建时间', prop: 'createTime'},
        {label: '类别', prop: 'type',slot: true}
      ],
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getMoveLcList()
    },
    /** 查询插拔记录 */
    // getYccbydList () {
    //   this.loading = true
    //   if (this.queryParams.time == null) {
    //     this.queryParams.startMoveTime = null
    //     this.queryParams.endMoveTime = null
    //   } else {
    //     this.queryParams.startMoveTime = this.queryParams.time[0]
    //     this.queryParams.endMoveTime = this.queryParams.time[1]
    //   }
    //   GetYccbydList(this.queryParams).then((res) => {
    //     this.List = res.result.records
    //     this.queryParams.total = res.result.total
    //   }).finally(() => {
    //     this.loading = false
    //   })
    // },

    getMoveLcList() {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.startMoveTime = null
        this.queryParams.endMoveTime = null
      } else {
        this.queryParams.startMoveTime = this.queryParams.time[0]
        this.queryParams.endMoveTime = this.queryParams.time[1]
      }
      getMoveLcList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getMoveLcList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.queryParams.startMoveTime = null
      this.queryParams.endMoveTime = null
      this.getMoveLcList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.cbjlParams = row
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, '异常车辆流程.xlsx')
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.cbjlDialog = false
    },
    /** 提交新增 */
    submitCdzgl(type) {
      switch (type) {
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
