<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>
        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌"
                  @change="handleChange"></el-input>
        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getYccbydList"
        >查询
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01CY01QX02']"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          :cell-style="cellStyle"
          @rowdblclick="openDialog('show')"
      >

      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
import { DownLoad, GetYccbydList } from '@/api/dzcc/gj/clyd'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
// import { downLoad } from '@/utils/tool'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {downLoad} from "../../../../utils/tool";


export default {
  name: 'clyd',
  components: {Table, Pagination, Dropdown},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getYccbydList()
    GetGroupList({
      isShowAll: 1,
      parentId: 1
    }).then((res) => {
      this.groupItem = res.result
    })
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        licensePlate: '',
        groupId: -1,
        time: [],
        StartMoveTime: null,
        EndMoveTime: null
      },
      // 查询部门列表
      groupItem: [],
      // 车队长列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      cbjlParams: {},
      dialogType: '',
      dialogTitle: '',
      cbjlDialog: false,
      realTableOptions:[],
      tableOptions: [
        {label: '车型', prop: 'carMold'},
        {label: '车牌', prop: 'licensePlate'},
        {label: '部门', prop: 'groupName'},
        {label: '开始移动时间', prop: 'StartMoveTime'},
        {label: '最后移动时间', prop: 'EndMoveTime'}
      ],
    }
  },
  methods: {
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getYccbydList()
    },
    /** 查询插拔记录 */
    getYccbydList () {
      this.loading = true
      if (this.queryParams.time == null) {
        this.queryParams.StartMoveTime = null
        this.queryParams.EndMoveTime = null
      } else {
        this.queryParams.StartMoveTime = this.queryParams.time[0]
        this.queryParams.EndMoveTime = this.queryParams.time[1]
      }
      GetYccbydList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getYccbydList()
    },
    /** 设置指定行、列、具体单元格颜色 */
    cellStyle ({
      row,
      column,
      rowIndex,
      columnIndex
    }) {
      // 开始移动时间，早上8点之前
      if (columnIndex === 4 && new Date(row.StartMoveTime).getHours() < 8) {
        return 'color: red'
      }
      // 结束移动时间 ，晚上17:30之后
      if (columnIndex === 5) {
        const hours = new Date(row.EndMoveTime).getHours()
        const minute = new Date(row.EndMoveTime).getMinutes()
        if (hours >= 18 || (hours == 17 && minute > 30)) {
          return 'color: red'
        }
      }
      return ''
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getYccbydList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.cbjlParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.groupId) {
              this.queryParams.groupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, '加班车辆信息.xlsx')
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.cbjlDialog = false
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

</style>
<style>
.dzcc_color_red {
  color: red;
}
</style>
