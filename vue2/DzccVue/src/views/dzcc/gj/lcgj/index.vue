<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">排序方式：</span>
        <el-select v-model="queryParams.carOrPerson" placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in carPerson"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>

        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.topGroupId" filterable clearable placeholder="请选择管理部门"
                   @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14" v-show="queryParams.carOrPerson === 1">司机姓名：</span>
        <el-input v-if="queryParams.carOrPerson === 1" v-model="queryParams.RealName" clearable
                  placeholder="请输入司机姓名"
                  @change="handleChange"></el-input>

        <span class="font-size14" v-show="queryParams.carOrPerson === 0">车牌：</span>
        <el-input v-if="queryParams.carOrPerson === 0" v-model="queryParams.LicencePlate" clearable
                  placeholder="请输入车牌"
                  @change="handleChange"></el-input>
        <span class="font-size14">选择月份：</span>
        <el-date-picker
            v-model="queryParams.yearMonth"
            type="month"
            placeholder="选择月"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange"
        >
        </el-date-picker>
        <span class="font-size14">车辆标识：</span>
        <el-select v-model="queryParams.carTag" clearable filterable placeholder="请选择车辆标识" @change="handleChange"
                   style="width: 190px">
          <el-option
              :key="-1"
              label="全部"
              :value="-1">
          </el-option>
          <el-option
              :key="0"
              label="临租车辆"
              :value="0">
          </el-option>
          <el-option
              :key="1"
              label="特殊车辆"
              :value="1">
          </el-option>
          <el-option
              :key="2"
              label="产权车辆"
              :value="2">
          </el-option>
        </el-select>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getLcGj"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01LC01QX02']"
        >导出
        </el-button>
        <Dropdown v-show="queryParams.carOrPerson ==1" :columnArr="tableOptions1" @getNewArr="getNewArr"/>
        <Dropdown v-show="queryParams.carOrPerson ==0" :columnArr="tableOptions0" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="lcgjList"
          :table-data="lcgjList"
          :table-options="realTableOptions"
          :loading="lcgjLoading"
          :queryParam="queryParams"
          :table-row-class-name="tableFbbRowClassName"
      >
        <template #carTag="scope">
          <div v-if="scope.row.carTag == 0">
            临租车辆
          </div>
          <div v-else-if="scope.row.carTag == 1">
            特殊车辆
          </div>
          <div v-else-if="scope.row.carTag == 2">
            产权车辆
          </div>
          <div v-else>
            -
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>

</template>

<script>
import {GetGroupList} from '@/api/dzcc/clgl/clgl'
import {downLoad, formatMonth} from '@/utils/tool'
import {GetLcgjList, DownLoad} from '@/api/dzcc/gj/lcgj'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

const d = new Date()
d.setMonth(d.getMonth(), 0)
export default {
  name: 'lcgj',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted() {
    GetGroupList({isShowAll: 1, parentId: 1}).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
    this.getLcGj()
  },
  data() {
    return {
      // 初始打开的tabs页面
      editableTabsValue: '1',
      // 可编辑tabs页
      editableTabs: [
        {
          title: '浙B2229H-马梦娜(2022年10月)出车情况',
          name: '2'
        }
      ],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        RealName: '',
        topGroupId: -1,
        LicencePlate: '',
        yearMonth: formatMonth(d.getTime()),
        carOrPerson: 0,
        carTag: -1,
      },
      // 统计方式
      carPerson: [
        {
          value: 0,
          label: '按车排序'
        }, {
          value: 1,
          label: '按人排序'
        }
      ],
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      lcgjList: [],
      // 主报表遮罩层
      lcgjLoading: false,
      realTableOptions: [],
      tableOptions0: [
        {label: '申请单位', prop: 'TopGroupName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌号', prop: 'LicencePlate'},
        {label: '司机', prop: 'RealName'},
        {label: '车辆里程', prop: 'allValue'},
        {label: '里程指标', prop: 'carIndex'},
        {label: '车辆标识', prop: 'carTag', slot: true},
      ],
      tableOptions1: [
        {label: '申请单位', prop: 'TopGroupName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌号', prop: 'LicencePlate'},
        {label: '司机', prop: 'RealName'},
        {label: '工作里程', prop: 'allValue'},
        {label: '里程指标', prop: 'driverIndex'},
        {label: '车辆标识', prop: 'carTag', slot: true},
      ],
    }
  },
  components: {Table, Pagination, Dropdown},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    // tabs页删除
    removeTab(targetName) {
      const tabs = this.editableTabs
      let activeName = this.editableTabsValue
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.name === targetName) {
            const nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.name
            }
          }
        })
      }
      this.editableTabsValue = activeName
      this.editableTabs = tabs.filter(tab => tab.name !== targetName)
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getLcGj()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getLcGj()
    },
    tableFbbRowClassName({
                           row,
                           rowIndex
                         }) {
      if (this.queryParams.carOrPerson === 0) {
        if (row.allValue < row.carIndex) {
          return 'dzcc_color_red background_white'
        }
      } else {
        if (row.allValue < row.driverIndex) {
          return 'dzcc_color_red background_white'
        }
      }
      return 'background_white'
    },
    getLcGj() {
      this.lcgjLoading = true
      if (this.queryParams.carOrPerson == 0) {
        this.queryParams.RealName = '';
      }
      if (this.queryParams.carOrPerson == 1) {
        this.queryParams.LicencePlate = '';
      }

      GetLcgjList(this.queryParams).then((res) => {
        this.lcgjList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.lcgjLoading = false
      })
    },
    /** 重置查询车队长 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.fbbSelectID = 0
      this.getLcGj()
    },
    /** 单击表事件 */
    fbbSelect(row) {
      this.fbbSelectID = row.id
      // 列表无id，虚设
      this.fbbSelectID = 1
      this.fbbParams = row
      this.$nextTick(() => {
        this.$refs.lcgjList.setCurrentRow(row)
      })
    },
    openDialog(type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.topGroupId) {
              this.queryParams.topGroupName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, this.queryParams.topGroupName + '里程告警(' + this.queryParams.yearMonth.substring(0, 7) + ').xlsx')
          })
          break
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>

<style>
.dzcc_color_red {
  color: red;
}
</style>
