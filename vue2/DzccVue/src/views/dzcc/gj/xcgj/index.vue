<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">管理部门：</span>
        <el-select v-model="queryParams.applyTopDeptId" filterable clearable placeholder="请选择管理部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id">
          </el-option>
        </el-select>

        <span class="font-size14">司机姓名：</span>
        <el-input v-model="queryParams.driverName" clearable placeholder="请输入司机姓名" @change="handleChange"></el-input>

        <span class="font-size14">车牌：</span>
        <el-input v-model="queryParams.licensePlate" clearable placeholder="请输入车牌" @change="handleChange"></el-input>

        <span class="font-size14">告警类型：</span>
        <el-select v-model="queryParams.alarmType" placeholder="请选择告警类型" @change="handleChange">
          <el-option
              v-for="item in alarmType"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>

        <span class="font-size14">日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :clearable="false"
            @change="handleChange"
            style="width: 20%"
        >
        </el-date-picker>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getXcglList"
        >查询
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>

        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['NDWCC01CL02QX02']"
        >导出
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="XcgjList"
          :tableOptions="realTableOptions"
          :loading="XcgjLoading"
          :queryParam="queryParams"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { GetGroupList } from '@/api/dzcc/clgl/clgl'
import { dateFormat, downLoad } from '@/utils/tool'
import { GetXcgjList, DownLoad } from '@/api/dzcc/gj/xcgj'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

const d = new Date()
d.setMonth(d.getMonth(), 0)
export default {
  name: 'lcgj',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  mounted () {
    GetGroupList({ isShowAll: 1, parentId: 1 }).then((res) => {
      if (res.result !== undefined) {
        this.groupItem = res.result
        console.log(res.result[0].id)
        this.queryParams.applyTopDeptId = res.result[0].id
        this.getXcglList()
        this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
      }
    })
  },
  data () {
    return {
      // 初始打开的tabs页面
      editableTabsValue: '1',
      // 可编辑tabs页
      editableTabs: [
        {
          title: '浙B2229H-马梦娜(2022年10月)出车情况',
          name: '2'
        }
      ],
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        driverName: '',
        applyTopDeptId: -1,
        licensePlate: '',
        time: [dateFormat(), dateFormat()],
        startDate: dateFormat(),
        endDate: dateFormat(),
        alarmType: '全部'
      },
      // 查询部门列表
      groupItem: [],
      // 主报表列表
      XcgjList: [],
      // 主报表遮罩层
      XcgjLoading: false,
      // 告警类型
      alarmType: [
        {
          value: '全部',
          label: '全部'
        },
        {
          value: '出大市',
          label: '出大市'
        }, {
          value: '跨区域',
          label: '跨区域'
        }
      ],
      realTableOptions:[],
      tableOptions: [
        {label: '日期', prop: 'curDate'},
        {label: '申请单位', prop: 'applyDeptName'},
        {label: '车型', prop: 'carMold'},
        {label: '车牌号', prop: 'LicencePlate'},
        {label: '驾驶员', prop: 'driverName'},
        {label: '告警类型', prop: 'status'}
      ],
    }
  },
  components: {Table, Pagination, Dropdown},
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  methods: {
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.startDate = this.queryParams.time[0]
      this.queryParams.endDate = this.queryParams.time[1]
      this.queryParams.pageNum = 1
      this.getXcglList()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getXcglList()
    },
    getXcglList () {
      this.XcgjLoading = true
      GetXcgjList(this.queryParams).then((res) => {
        this.XcgjList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.XcgjLoading = false
      })
    },
    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.selectID = 0
      this.getXcglList()
    },
    /** 单击表事件 */
    fbbSelect (row) {
      this.selectID = row.id
      // 列表无id，虚设
      this.selectID = 1
      this.params = row
      this.$nextTick(() => {
        this.$refs.XcgjList.setCurrentRow(row)
      })
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'download':
          this.groupItem.forEach(value => {
            if (value.id === this.queryParams.applyTopDeptId) {
              this.queryParams.applyDeptName = value.groupname
            }
          })
          DownLoad(this.queryParams).then((res) => {
            downLoad(res, this.queryParams.applyDeptName + '每日行程告警(' + this.queryParams.startDate.substring(0, 10) + '至' + this.queryParams.endDate.substring(0, 10) + ').xlsx')
          })
          break
      }
    }
  }
}
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>
