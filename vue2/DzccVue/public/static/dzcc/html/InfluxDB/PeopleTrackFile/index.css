/* -------Reset------------------------------------------------------------------- */

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
button,
textarea,
p,
blockquote,
th,
td {
	margin: 0;
	padding: 0;
}

body {
	background: #fff;
	color: #555;
	font-size: 22px;
	font-family: Verdana, Arial, Helvetica, sans-serif microsoft yahei;
}

td,
th,
caption {
	font-size: 18px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
	font-size: 100%;
}

address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var {
	font-style: normal;
	font-weight: normal;
}

a {
	color: #555;
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

img {
	border: none;
}

input,
textarea,
select,
button {
	font: 14px Verdana, Helvetica, Arial, sans-serif, microsoft yahei;
}

table {
	border-collapse: collapse;
}

.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}

.clearfix {
	*zoom: 1;
}

a img {
	border: none;
}

:focus {
	outline: 0;
}

a {
	color: #333;
	text-decoration: none;
	webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

a:hover {
	color: #2970a4;
	text-decoration: none;
	webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}

a:hover {}


/* 
   General 
------------------------------------------------------------------- */

html {
	height: 100%;
	/*padding-bottom: 1px;  force scrollbars */
}

body {
	color: #fff;
	background: #000f1d url(images/top.png) top center no-repeat;
	line-height: 1.5;
}


/* Floats */

.center,
.aligncenter {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.left,
.alignleft {
	float: left;
}

.right,
.alignright {
	float: right;
}

.aligncenter {
	text-align: center;
}

.clear,
.clearer {
	clear: both;
}

.clearer {
	display: block;
	font-size: 0;
	line-height: 15px;
	height: 0px;
}




/*  布局 ------------------------------------------------------------------- */

.center-wrapper {
	width: 100%;
	margin: 0 auto;
	overflow: hidden;
	border-top: none;
	height: auto;
	position: absolute;
	top: 86px;
	bottom: 30px;
}


.logo {
	width: 100%;
	height: 86px;
	padding: 0px 0 0 0px;
	text-align: center;
	font-size: 35px;
	font-weight: bold;
	letter-spacing: 8px;
	color: #ededed;
}

.base-modal {
	background: white;
	position: absolute;
	z-index: 10000;
	height: 90%;
	width: 90%;
	left: 5%;
	box-shadow: 0 0 15px 10px #0ca0a8;
	top: 3.2vh !important;
	padding: 0 !important;
}

.box_l {
	width: 42%;
	height: 97%;
	background: url("images/box_bg.png");
	float: left;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 0 20px 1%;
}

.box_2 {
	width: 40%;
	height: 97%;
	background: url("images/box_bg.png");
	float: left;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 0 20px 1%;
}

.box_3 {
	width: 98%;
	height: 97%;
	background: url("images/box_bg.png");
	float: left;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 0 20px 1%;
}

.box_r {
	width: 55%;
	height: 97%;
	background: url("images/box_bg.png");
	float: right;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 1% 20px 0;
}

.box_r2 {
	width: 57%;
	height: 97%;
	background: url("images/box_bg.png");
	float: right;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 1% 20px 0;
}

.border_t_l,
.border_t_r,
.border_b_l,
.border_b_r {
	width: 17px;
	height: 17px;
	position: absolute;
}

.border_t_l {
	background: url("images/border_t_l.png");
	top: -2px;
	left: -2px;
}

.border_t_r {
	background: url("images/border_t_r.png");
	top: -2px;
	right: -2px;
}

.border_b_l {
	background: url("images/border_b_l.png");
	bottom: -2px;
	left: -2px;
}

.border_b_r {
	background: url("images/border_b_r.png");
	bottom: -2px;
	right: -2px;
}

.border_t {
	width: 298px;
	height: 17px;
	background: url("images/border_t.png");
	top: -3px;
	position: absolute;
	left: 50%;
	margin-left: -149px;
}


.list_t {
	padding: 0px 0 0px 0px;
	/*height: 42px;*/
	/*line-height: 42px;*/
	margin: 0px 0 33px 0;
	width: 100%;
	overflow: hidden;

}

.lzcl_list {
	padding: 0px 0 0px 0px;
	/*height: 42px;*/
	/*line-height: 42px;*/
	margin: 0px 0 6px 0;
	width: 100%;
	overflow: hidden;

}

.lzcl_list p {
	background: url(images/b_dot.png) left center no-repeat;
	color: #ededed;
	font-size: 17px;
	font-weight: 700;
	float: left;
	line-height: 30px;
	height: 30px;
	text-align: left;
	padding: 0px 0 0px 23px;
	margin-top: 0px;
}

.list_t p {
	background: url(images/b_dot.png) left center no-repeat;
	color: #ededed;
	font-size: 17px;
	font-weight: 700;
	float: left;
	line-height: 30px;
	height: 30px;
	text-align: left;
	padding: 0px 0 0px 23px;
	margin-top: 0px;
}

.sel_box {
	right: 285px;
	top: 30px;
	position: absolute;
}

/* search box  */
.search_box {
	width: 260px;
	height: 30px;
	overflow: hidden;
	padding: 0px 0 0 0px;
	float: left;
	border: solid 1px #0260b2;
	border-radius: 16px;
	position: absolute;
	right: 20px;
	top: 30px;
}

.search_box:hover {
	border-color: #0ed4ff;
}


.sli_search_1 {
	float: left;
	padding: 0px 0px 0px 20px;
	vertical-align: middle;
	height: 30px;
	line-height: 30px;
	width: 180px;
	font-size: 14px;
	color: #fff;
	background: none;
	border: none
}

.search-btn {
	float: left;
	width: 48px;
	margin: 0px;
	height: 30px;
	background: url(images/search_icon.png) 20px center no-repeat;
	border: none;
	color: #fff;
	font-size: 24px;
	line-height: 38px;
	text-align: center;
}

.list_tb {
	border: none;
	width: 100%;
	font-size: 0.875em;
	border-collapse: collapse;
	background: #071b36;
}

.list_tb th {
	font-weight: 400;
	text-align: left;
	color: #ededed;
	border-bottom: none;
	padding: 0 20px;
	height: 58px;
	line-height: 58px;
	background: #11325f;
	font-size: 16px;
}

.list_tb td {
	color: #ededed;
;
	padding: 0 20px;
	height: 67px;
	line-height: 67px;
	font-size: 14px;
	font-weight: 400
}

.list_tb .col1 {
	background: #0d2749
}


.list_tb1 {
	border: none;
	width: 100%;
	font-size: 0.875em;
	border-collapse: collapse;
	background: #071b36;
}

.list_tb1 th {
	font-weight: 400;
	text-align: left;
	color: #ededed;
	border-bottom: none;
	height: 58px;
	line-height: 58px;
	background: #11325f;
	font-size: 16px;
}

.list_tb1 td {
	color: #ededed;
;
	padding: 0 20px;
	height: 67px;
	line-height: 67px;
	font-size: 14px;
	font-weight: 400
}

.list_tb1 .col1 {
	background: #0d2749
}

.curr {
	color: #facc14;
}

.chart_box {
	height: 78%;
	width: 100%;
}


.data_box {
	width: 96%;
	margin: 0 2%;
	height: 58px;
	border-radius: 10px;
	background: #07253e;
	padding: 30px 0;
}

.data_cont {
	float: left;
	width: 30%;
	height: 80px;
	position: relative;
	padding: 0 0 0 3%;
	background: url(images/b_line.png) right center no-repeat;
}

.data_icon {
	margin-right: 40px;
	-webkit-transition: .5s;
	transition: .5s;
	float: left;
}

.data_icon span {
	color: #2971f5;
	width: 68px;
	height: 68px;
	display: block;
	font-size: 48px;
	line-height: 48px;
}

.data_icon span.sgz {
	background: url(images/sgz_icon.png) center center no-repeat;
}

.data_icon span.xa {
	background: url(images/xa_icon.png) center center no-repeat;
}

.data_icon span.zx {
	background: url(images/zx_icon.png) center center no-repeat;
}

.data_txt {
	float: left;
}

.data_txt h3 {
	font-size: 12px;
	margin: 0;
	font-weight: 400;
	line-height: 24px;
	color: #ededed;
}

.data_txt p {
	font-size: 28px;
	line-height: 1.4;
	text-align: right;

	color: #facc14;
}

.data_txt em {
	font-size: 12px;
	line-height: 2;
	font-weight: 400;
	color: #ededed;
	padding-left: 20px;
}

.data_cont:hover .data_icon {
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
	transform: scale(1.2, 1.2);
}


.white_content {
	display: none;
	position: absolute;
	top: 20%;
	left: 10%;
	width: 80%;
	border: 6px solid #025ead;
	z-index: 1002;
	background: url(images/box_bg.png);
	float: left;
	padding: 0;
	position: relative;
	box-sizing: border-box;
	margin: 20px 0 20px 1%;
}

.close {
	float: right;
	clear: both;
	width: 20%;
	text-align: right;
	margin: 0 0 0 0;
	position: inherit;
}

.close a {
	color: #333;
	text-decoration: none;
	font-size: 14px;
	font-weight: 700
}

.closeIndex {
	clear: both;
	text-align: right;
	margin: 0 0 0 0;
	position: inherit;
	right: 0;
}

.closeIndex a {
	color: #333;
	text-decoration: none;
	font-size: 14px;
	font-weight: 700
}

.lzcl_l {
	width: 69%;
	height: 97%;
	background: url("images/box_bg.png");
	float: left;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 0 20px 1%;
}

.lzcl_r {
	width: 28%;
	height: 30%;
	background: url("images/box_bg.png");
	float: right;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 1% 10px 0;
}

.text {
	color: #ffffff;
	font-size: 15px;
	padding: 5px 15px;
	background: #031429;
	border: 1px transparent solid;
	border-radius: 30px;
	position: relative;
	width: 90%;
	height: 30px;
	margin-top: 10%;
}

.text:after {
	content: '';
	position: absolute;
	top: -4px;
	bottom: -4px;
	left: -4px;
	right: -4px;
	background: linear-gradient(135deg, #0090FF, #031429);
	border-radius: 30px;
	content: '';
	z-index: -1;
}

.lzcl_tb {
	border: none;
	width: 100%;
	font-size: 0.875em;
	border-collapse: collapse;
	background: #071b36;
}

.lzcl_tb th {
	font-weight: 400;
	text-align: left;
	color: #ededed;
	border-bottom: none;
	padding: 0 20px;
	height: 35px;
	line-height: 35px;
	font-size: 16px;
}

.lzcl_tb td {
	color: #ededed;
	padding: 0 20px;
	height: 35px;
	line-height: 35px;
	font-size: 14px;
	font-weight: 400
}

.lzcl_tb .col1 {
	background: #011428
}
.lzcl_tb .col2 {
	background: rgb(20,139,255,0.2);
}

.search_box1 {
	width: 150px;
	height: 30px;
	overflow: hidden;
	padding: 0px 0 0 0px;
	float: left;
	border: solid 1px #0260b2;
	border-radius: 16px;
	position: absolute;
	right: 20px;
	top: 15px;
}

.search_box1:hover {
	border-color: #0ed4ff;
}
.mileage {
	color: #fff;
	background:url(images/bg_mileage.png) top center no-repeat;
	line-height: 1.5;
	float: left;width: 48%;height: 80%;
}
.time {
	color: #fff;
	background: url(images/bg_time.png) top center no-repeat;
	line-height: 1.5;
	float: left;width: 48%;height: 80%;
}
.number{
	float: left;
	font-size: 45px;
	font-weight: bold;
	width: 65%;
	height: 55%;
}
.show{
	float: left;
	font-size: 13px;
	width: 27%;
	vertical-align: bottom;
	padding-top: 33%;
}
.show1{
	float: left;
	font-size: 13px;
	width: 96%;
	text-align: center;
}
.driver{
	margin: 24% 31% 30% 35%;
	height: 50%;
}
.lzcl_box{
	right: 30px;
	top: 20px;
	position: absolute;
}
.history_l {
	width: 30%;
	height: 97%;
	background: url("images/box_bg.png");
	float: left;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 0 20px 1%;
}
.history_r{
	width: 67%;
	height: 97%;
	background: url("images/box_bg.png");
	float: left;
	padding: 1%;
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 20px 0 20px 1%;
}
.history_list{
	padding: 0px 0 20px 0px;
	margin: 0px 0 6px 0;
	width: 100%;
	overflow: hidden;
}
.history_list p {
	background: url(images/b_dot.png) left center no-repeat;
	color: #ededed;
	font-size: 17px;
	font-weight: 700;
	float: left;
	line-height: 30px;
	height: 30px;
	text-align: left;
	padding: 0px 0 0px 23px;
	margin-top: 0px;
}
.history_box{
	width: 299px;
	height: 30px;
	overflow: hidden;
	padding: 0px 0 0 0px;
	float: left;
	border: solid 1px #0260b2;
	border-radius: 16px;
	position: absolute;
	right: 79%;
	top: 7%;
}
.sli_search_2{
	float: left;
	padding: 0px 0px 0px 20px;
	vertical-align: middle;
	height: 30px;
	line-height: 30px;
	width: 216px;
	font-size: 14px;
	color: #fff;
	background: none;
	border: none;
}
.history_botton{
	float: left;
	width: 127px;
	height: 50px;
	border: solid 2px #0090FF;
	background: #0090FF;
	font-size: 20px;
	text-align: center;
	line-height: 55px;
	border-radius: 5px;
	margin-left: 20px;
}

.box_alarm{
	width: 98%;
	height: 97%;
	background: url(images/box_bg.png);
	float: left;
	/* padding: 1%; */
	position: relative;
	box-sizing: border-box;
	border: solid 2px #025ead;
	margin: 1%;
}