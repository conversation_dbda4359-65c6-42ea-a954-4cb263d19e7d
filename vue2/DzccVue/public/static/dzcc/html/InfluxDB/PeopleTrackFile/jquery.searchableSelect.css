/*
Author: <PERSON>
E-mail: <EMAIL>
Date: 2014-11-05
*/

.searchable-select-hide {
    display: none;
}

.searchable-select {
    display: inline-block;
    min-width: 200px;
    height:34px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555;
    vertical-align: middle;
    position: relative;
    outline: none;
    background: #0a203c none;
    border: 1px solid #0090ff;
    border-radius: 1px;
    color: inherit;
    display: block;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    width: 90%;
    font-size: 14px;
    border-radius: 10px;
    border-width: 1px;
}

.searchable-select:active, .searchable-select.open, .searchable-select:focus{
    border-color: #0ed4ff;
}
.searchable-select-items::-webkit-scrollbar-thumb {
    background-color: #0090ff;
}
.searchable-select-items::-webkit-scrollbar-track {
    background-color: #025ead;
}
.searchable-select-holder{
    /*padding: 0px;*/
    /*background-color: #fff;*/
    /*background-image: none;*/
    /*border: 1px solid #ccc;*/
    /*border-radius: 4px;*/
    float:left;
    min-height: 12px;
    /*box-sizing: border-box;*/
    /*-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);*/
    /*box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);*/
    -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    /*background: #FFFFFF none;*/
    /*border: 1px solid #e5e6e7;*/
    /*border-radius: 1px;*/
    /*color: inherit;*/
    /*display: block;*/
    /*padding: 6px 12px;*/
    /*transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;*/
    /*width: 100%;*/
    /*font-size: 14px;*/
}

.searchable-select-caret {
    position: absolute;
    width: 0;
    height: 0;
    box-sizing: border-box;
    border-color: white transparent transparent transparent;
    top: 8px;
    bottom: 0;
    border-style: solid;
    border-width: 8px;
    margin: auto;
    right: 10px;
}

.searchable-select-dropdown {
    position: absolute;
    background-color: #0a203c;
   /*border: 1px solid #ccc;*/
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 4px;
    border-top: none;
    top: 36px;
    left: 0;
    right: 0;
    z-index: 9999999;
}

.searchable-select-input {
    margin-top: 5px;
    border: 1px solid #ccc;
    outline: none;
    padding: 4px;
    width: 100%;
    box-sizing: border-box;
    width: 100%;
}

.searchable-scroll {
    margin-top: 4px;
    position: relative;
}

.searchable-scroll.has-privious {
    padding-top: 16px;
}

.searchable-scroll.has-next {
    padding-bottom: 16px;
}

.searchable-has-privious {
    top: 0;
}

.searchable-has-next {
    bottom: 0;
}

.searchable-has-privious, .searchable-has-next {
    height: 16px;
    left: 0;
    right: 0;
    position: absolute;
    text-align: center;
    z-index: 10;
    background-color: #0a203c;
    line-height: 8px;
    cursor: pointer;
}

.searchable-select-items {
    max-height: 400px;
    overflow-y: scroll;
    position: relative;
}

.searchable-select-items::-webkit-scrollbar {
    width: 2px;
    background-color: #0090ff;
}

.searchable-select-item {
    padding: 5px 5px;
    cursor: pointer;
    min-height: 30px;
    box-sizing: border-box;
    transition: all 1s ease 0s;

}

.searchable-select-item.hover {

    background: #0090ff;
    color: white;
}

.searchable-select-item.selected {
    background: #0090ff;
    color: white;
}