<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"/>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="content-type" content="no-cache">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no"/>
    <style type="text/css">
        body, html {
            width: 100%;
            height: 100%;
            margin: 0;
            font-family: "微软雅黑",serif;
            font-size: 14px;
        }

        #l-map {
            height: 95%;
            width: 100%;
        }

        #r-result {
            width: 100%;
        }
    </style>
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: "8b02561920b9d089aae807cbd8fa88a4",
        };
    </script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=37e9402cd3d2f319d8fc46e5750ab930"></script>
    <title>地图</title>
</head>
<body>
<div id="r-result">
    选择地址:<input type="text" onKeyUp="keyup(event)" id="address" size="20" style="width:250px;"/>
    GPS：<input id="localtion" disabled="disabled" style="width:250px;"/>
    <input type="button" onclick="setGPS()" value="确定"/>
</div>
<div id="l-map"></div>

<div id="searchResultPanel" style="border:1px solid #C0C0C0;width:150px;height:auto; display:none;"></div>
</body>
</html>
<script type="text/javascript">
    // 百度地图API功能
    function G(id) {
        return document.getElementById(id);
    }

    var map = new AMap.Map("l-map", {
        resizeEnable: true,
        zoom: 13, //地图级别
        center: [121.584372, 29.876977], //地图中心点
    });
    var geocoder = null;
    AMap.plugin('AMap.Geocoder', function() {
        geocoder = new AMap.Geocoder({
            city: "全国", //城市设为北京，默认：“全国”
            radius: 1000 //范围，默认：500
        });
    })
    var redPoint;
    var g_point;
    var x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    // var lng = 0;
    // var lat = 0;

    var point = new AMap.LngLat(121.5374851, 29.8570321);

    var gps = window.parent.document.getElementById("clbgd.gps").value;
    if (gps !== '') {
        document.getElementById("localtion").value = gps;
        gps = gps.split(',');
        point = new AMap.LngLat(gps[0], gps[1]);
        redPoint = new AMap.Marker({
            position: point, //经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
            title: '办公点',
        });
        map.add(redPoint);
        map.setZoomAndCenter(16, point)
    } else {
        map.setZoomAndCenter(12, point); // 初始化地图,设置城市和地图级别。
    }

    // 实例化Autocomplete
    var autoOptions = {
        input: "address",
        city: '全国'
    }
    AMap.plugin(['AMap.PlaceSearch','AMap.AutoComplete'], function(){
        var auto = new AMap.AutoComplete(autoOptions);
        var placeSearch = new AMap.PlaceSearch({
            map: map
        });  //构造地点查询类
        auto.on("select", select);//注册监听，当选中某条记录时会触发
        function select(e) {
            // placeSearch.setCity(e.poi.adcode);
            // placeSearch.search(e.poi.name);  //关键字查询查询
            setPlace([e.poi.location.lng, e.poi.location.lat])
        }
    });

    map.on('click', function(e) {
        //清除地图上所有的覆盖物
        map.clearMap();
        const pt = e.lnglat;
        const lnglat = [pt.lng, pt.lat];
        setPlace(lnglat)
    });

    function bd09_To_Gcj02(lng, lat) {
        var z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_pi);
        var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_pi);
        var bd_lng = z * Math.cos(theta) + 0.0065;
        var bd_lat = z * Math.sin(theta) + 0.006;
        return [bd_lng, bd_lat]
    }

    function bd_decrypt(bd_lng, bd_lat) {
        var X_PI = Math.PI * 3000.0 / 180.0;
        var x = bd_lng - 0.0065;
        var y = bd_lat - 0.006;
        var z = Math.sqrt((x * x) + (y * y)) - 0.00002 * Math.sin(y * X_PI);
        var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
        var gg_lng = z * Math.cos(theta);
        gg_lng = gg_lng.toFixed(7)
        var gg_lat = z * Math.sin(theta);
        gg_lat = gg_lat.toFixed(7)
        return gg_lng + "," + gg_lat;
    }

    //根据地址获取坐标点信息，并将坐标点作为中心点
    function getLocalByAddr() {
        var keywords = document.getElementById("address").value;
        AMap.plugin('AMap.AutoComplete', function(){
            // 实例化Autocomplete
            var autoOptions = {
                city: '全国'
            }
            var autoComplete = new AMap.Autocomplete(autoOptions);
            autoComplete.search(keywords, function(status, result) {
                if (result.count > 0) {
                    const lnglat = [result.tips[0].location.lng, result.tips[0].location.lat];
                    setPlace(lnglat)
                }
            })
        })
    }

    function setPlace(lnglat) {
        map.clearMap();    //清除地图上所有覆盖物
        map.setCenter(lnglat);
        geocoder.getAddress(lnglat, function(status, result) {
            if (status === 'complete'&&result.regeocode) {
                var address = result.regeocode.formattedAddress;
                var marker = new AMap.Marker({
                    position: lnglat, //经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
                    title: address,
                });
                map.add(marker);
                document.getElementById("address").value = address;
                document.getElementById("localtion").value = lnglat[0] + ',' + lnglat[1];
            }else{
                alert('根据经纬度查询地址失败')
                document.getElementById("address").value = '';
                document.getElementById("localtion").value = '';
            }
        });
    }

    var opts = {
        width: 400,     // 信息窗口宽度
        height: 60,     // 信息窗口高度
        title: "", // 信息窗口标题
        enableMessage: true, //设置允许信息窗发送短息
        message: ""
    }

    //调用gpsspg网站，jsonp形式，确定，每日每个oid和key只有访问2000次

    /*参数lat_lng 可以传递 lat或者lng中的一个*/
    function baiduToGPS(lat_lng) {
        var abs_lat_lng = Math.abs(lat_lng);
        //a_  意为all，含有整数和小数部分。
        //o_  意为only，只有小数部分
        var du = parseInt(abs_lat_lng); //度(不含小数部分)
        var o_du = lat_lng - du;   //度(只有小数部分)
        var a_fen = o_du * 60;  //分(含有整数和小数部分)
        var fen = parseInt(a_fen);  //分(不含小数部分)
        var o_fen = a_fen - fen;  //分(只有小数部分)
        var a_miao = o_fen * 60;   //秒(含有整数和小数部分)
        var miao = a_miao.toFixed(2);      //秒(取两位小数)
        var value = du + '°' + fen + '′' + miao + '″';
        // console.log(value);
        return value;
    }

    function keyup(event) {
        if (event.keyCode == "13") {
            //回车执行查询
            getLocalByAddr();
        }
    }

    function setGPS() {
        window.parent.document.getElementById("clbgd.gps").value = document.getElementById("localtion").value;
        var win = window.parent.document.getElementById('clbgd.dtDialogClose');
        if (win) {
            win.click()
        }
    }
</script>
