import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

export const constantRoutes = [
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/redirect/:path(.*)',
                component: () => import('@/views/redirect')
            }
        ]
    },
    {
        path: '/404',
        component: () => import('@/views/error/404'),
        hidden: true
    },
    {
        path: '/401',
        component: () => import('@/views/error/401'),
        hidden: true
    },
    {
        path: "*",
        name: "NotFound",
        component: () => import("@/views/error/404"),
    },
    //协同办公路由
    {
        path: '/dfdwSjgd',
        component: Layout,
        // redirect: 'index',
        children: [
            {
                path: 'index.html',
                name: 'index.html',
                component: () => import('@/views/index'),
            },
            {
                path: 'dict',
                name: 'dict',
                component: (resolve) => require(['../views/sjgd/dict'], resolve),
            },
            {
                path: 'personManage',
                name: 'personManage',
                component: (resolve) => require(['@/views/sjgd/personManage'], resolve),
            },
            {
                path: 'projectManage',
                name: 'projectManage',
                component: (resolve) => require(['@/views/sjgd/projectManage'], resolve),
            },
            {
                path: 'fieldService',
                name: 'fieldService',
                component: (resolve) => require(['@/views/sjgd/fieldService'], resolve),
            },
            {
                path: 'serviceRecord',
                name: 'serviceRecord',
                component: (resolve) => require(['@/views/sjgd/serviceRecord'], resolve),
            },
            {
                path: 'problemFeedback',
                name: 'problemFeedback',
                component: (resolve) => require(['@/views/sjgd/problemFeedback'], resolve),
            },
            {
                path: 'sjgdApprove',
                name: 'sjgdApprove',
                component: (resolve) => require(['@/views/sjgd/approve'], resolve),
            },
        ]
    },
]

const router = new VueRouter({
    mode: 'history',
    scrollBehavior: () => ({y: 0}),
    base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
    routes: constantRoutes
})

export default router
