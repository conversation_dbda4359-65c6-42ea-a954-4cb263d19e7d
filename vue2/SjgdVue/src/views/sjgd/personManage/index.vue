<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入送修厂家"
                  @change="handleChange"></el-input>
        <span class="font-size14">专业：</span>
        <el-select v-model="queryParams.specialty" clearable placeholder="请选择专业" @change="handleChange">
          <el-option
              v-for="item in getDictDatas('sjgd-specialty')"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">状态：</span>
        <el-select v-model="queryParams.state" clearable placeholder="请选择状态" @change="handleChange">
          <el-option label="启用" :value="1"/>
          <el-option label="禁用" :value="0"/>
        </el-select>
        <span class="font-size14">是否已关联用户表：</span>
        <el-select v-model="queryParams.hasSrcUserId" clearable placeholder="请选择是否已关联用户表"
                   @change="handleChange">
          <el-option label="已关联" :value="true"/>
          <el-option label="未关联" :value="false"/>
        </el-select>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-plus" @click="openDialog('add')"
                   v-has-permi="['JDWSJ01PM01QX02']">新增
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-edit" @click="openDialog('edit')"
                   v-has-permi="['JDWSJ01PM01QX03']">修改
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-delete" @click="openDialog('delete')"
                   v-has-permi="['JDWSJ01PM01QX04']">删除
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-link" @click="openDialog('link')"
                   v-has-permi="['JDWSJ01PM01QX07']">关联用户表
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-upload" @click="openDialog('import')"
                   v-has-permi="['JDWSJ01PM01QX05']">导入
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-edit" @click="openDialog('role')"
                   v-has-permi="['JDWSJ01PM01QX08']">编辑角色
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          :need-select="true"
          @getCurrentData="select"
          @getSelectionData="handleSelectionChange">
        <template v-slot:specialty="scope">
          <div>
            {{ getDictDataLabel('sjgd-specialty', scope.row.specialty) }}
          </div>
        </template>
        <template v-slot:state="scope">
          <el-tag type="success" v-if="scope.row.state === 1">启用</el-tag>
          <el-tag type="info" v-if="scope.row.state === 0">禁用</el-tag>
        </template>
        <template v-slot:srcUserId="scope">
          <el-tag type="success" v-if="scope.row.srcUserId != null">已关联</el-tag>
          <el-tag type="info" v-if="scope.row.srcUserId == null">未关联</el-tag>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="30%"
        :visible="dialog"
        size="tiny"
        center
        @close="closeDialog(dialogType)"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="formParams"
          :model="formParams"
          :rules="formRules"
          label-width="100px"
          v-watermark="{label: watermark}"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="userName">
              <el-input v-model="formParams.userName" clearable placeholder="请输入姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门" prop="deptName">
              <el-input v-model="formParams.deptName" clearable placeholder="请输入部门"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="专业" prop="specialty">
              <el-select v-model="formParams.specialty" clearable filterable placeholder="请选择专业">
                <el-option
                    v-for="item in getDictDatas('sjgd-specialty')"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="telephone">
              <el-input v-model="formParams.telephone" clearable placeholder="请输入联系方式"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="启用状态" prop="state">
              <el-radio-group v-model="formParams.state">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitDialog(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog(dialogType)">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="35%"
        :visible="linkDialog"
        size="tiny"
        center
        @close="closeDialog(dialogType)"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <div style="min-height: 500px">
        <el-form ref="linkForm" :model="linkPerson" @keydown.native.enter.prevent>
          <el-form-item
              v-for="(value, index) in linkPerson"
              :key="value.id"
              :prop="'[' + index + '].srcUserId'"
              :label="value.userName"
              label-width="80px"
          >
            <multiple-column-selection
                v-model="value.srcUserId"
                valueKey="id"
                label="realname"
                placeholder="请选择关联用户"
                :filterable="true"
                :tableWidth="400"
                :options="permissionList"
                :tableOptions="linkTableOptions"
                :pagination="true"
                :clearable="true"
                @change="handleChangeLinkPerson"
                style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitDialog(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog(dialogType)">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="22%"
        :visible="importDialog"
        size="tiny"
        center
        @close="closeDialog(dialogType)"
        :destroy-on-close="true"
        :modal-append-to-body="false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <file-import
          :file-size="10"
          :file-type="['xlsx']"
          upload-url="/sjgd/designPerson/upload"
          downloadName="personManageImport.xlsx"
          @uploadSuccessData="importSuccess"
          @uploadErrorData="importError"
      />
    </el-dialog>
    <el-dialog
        width="20%"
        :visible="roleDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog(dialogType)"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
      <div style="margin: 15px 0;"></div>
      <el-checkbox-group v-model="rolePersonIds" @change="handleCheckedRoleChange">
        <el-checkbox v-for="role in roleList" :label="role.id" :key="'role' + role.id">{{ role.roleName }}</el-checkbox>
      </el-checkbox-group>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitDialog(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog(dialogType)">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import FileImport from "components/DFDW/FileImport.vue";
import {
  designPersonAdd,
  designPersonDeleteByIds,
  designPersonEdit,
  designPersonPage, editRoleById, getPermissonList, getPersonRoleById, getRole,
  linkPerson
} from "api/sjgd/personManage";
import {getDictDataLabel, getDictDatas} from "@/utils/dict";
import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";

export default {
  name: 'personManage',
  components: {MultipleColumnSelection, Table, Pagination, Dropdown, FileImport},
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams));
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark;
    }
  },
  mounted() {
    this.getList();
    getPermissonList({}).then((res) => {
      this.permissionList = res.result;
    })
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: null,
        deptId: null,
        specialty: null,
        state: null,
        hasSrcUserId: null
      },
      // 列表
      list: [],
      realTableOptions: [],
      tableOptions: [
        {label: '姓名', prop: 'userName'},
        {label: '部门', prop: 'deptName'},
        {label: '专业', prop: 'specialty', slot: true},
        {label: '联系方式', prop: 'telephone'},
        {label: '状态', prop: 'state', slot: true},
        {label: '关联用户表', prop: 'srcUserId', slot: true},
      ],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表
      multipleSelection: [],
      // dialog选择列表id
      selectID: 0,
      // dialog选择列表参数
      formParams: {},
      // dialog类型
      dialogType: '',
      // dialog标题
      dialogTitle: '',
      // dialog
      dialog: false,
      // 表单校验
      formRules: {
        userName: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        deptName: [
          {required: true, message: '请输入部门', trigger: 'blur'}
        ],
        specialty: [
          {required: true, message: '请选择专业', trigger: 'change'}
        ],
        telephone: [
          {required: true, message: '请输入联系方式', trigger: 'blur'},
          {pattern: /(^1[3-9]\d{9}$)/, message: '手机号格式不正确', trigger: 'blur'}
        ],
        state: [
          {required: true, message: '请选择启用状态', trigger: 'change'}
        ]
      },
      // 关联用户dialog
      linkDialog: false,
      // 关联人员列表
      linkPerson: [],
      // 所有有权限的人
      permissionList: [],
      // 关联人员多列选择器配置
      linkTableOptions: [
        {label: '工号', prop: 'loginname', filter: true},
        {label: '姓名', prop: 'realname', filter: true},
        {label: '部门', prop: 'topgroupname', filter: true},
      ],
      // 导入dialog
      importDialog: false,
      // 编辑角色dialog
      roleDialog: false,
      // 用户角色列表
      rolePersonIds: [],
      // 角色列表
      roleList: [],
      checkAll: false,
      isIndeterminate: true
    }
  },
  methods: {
    getDictDatas,
    getDictDataLabel,
    /** 获取新表格列表 */
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions];
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      designPersonPage(this.queryParams).then(res => {
        this.list = res.result.records;
        this.queryParams.total = res.result.total;
      }).finally(() => {
        this.loading = false;
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val;
      this.getList();
    },
    /** 重置查询车辆管理 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm));
      this.getList();
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id;
      this.formParams = row;
    },
    /** 多选表事件 */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /** 打开弹窗 */
    openDialog(type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add';
          this.dialogTitle = '新增人员管理';
          this.selectID = 0;
          this.formParams = {
            state: 1
          };
          this.dialog = true;
          break;
        case 'edit':
          if (this.multipleSelection.length > 1) {
            this.$message.error('只能选择一条行信息！')
          } else if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogType = 'edit';
            this.dialogTitle = '修改人员管理';
            this.dialog = true;
          }
          break;
        case 'delete':
          if (this.multipleSelection.length > 0) {
            this.$confirm('是否删除所选数据?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              designPersonDeleteByIds({ids: this.multipleSelection.map(item => item.id)}).then(() => {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                this.multipleSelection = [];
                this.getList();
              })
            })
          }
          break;
        case 'link':
          if (this.multipleSelection.length > 0) {
            this.linkPerson = JSON.parse(JSON.stringify(this.multipleSelection));
            this.dialogType = 'link';
            this.dialogTitle = '关联用户表';
            this.linkDialog = true;
          }
          break;
        case 'import':
          this.dialogType = 'import';
          this.dialogTitle = '人员导入';
          this.importDialog = true;
          break;
        case 'role':
          if (this.multipleSelection.length > 1) {
            this.$message.error('只能选择一条行信息！')
          } else if (this.selectID == 0) {
            this.$message.error('请选择行！')
          } else if (this.formParams.srcUserId == null
              || this.formParams.srcUserId == ''
              || this.formParams.srcUserId == 0
              || this.formParams.srcUserId == undefined
          ) {
            this.$message.error('请先关联用户表！')
          } else {
            getPersonRoleById({srcUserId: this.formParams.srcUserId}).then((res) => {
              this.rolePersonIds = res.result.map(item => (item.id));
              this.dialogType = 'role';
              this.dialogTitle = '编辑角色';
              this.roleDialog = true;
            })
            getRole().then((res) => {
              this.roleList = res.result;
            })
          }
          break;
        default:
          break;
      }
    },
    /** 关闭弹窗 */
    closeDialog(type) {
      switch (type) {
        case 'add':
          this.dialog = false;
          this.getList();
          break;
        case 'edit':
          this.dialog = false;
          this.getList();
          break;
        case 'link':
          this.$refs.linkForm.resetFields();
          this.linkDialog = false;
          this.getList();
          break;
        case 'import':
          this.importDialog = false;
          this.getList();
          break;
        case 'role':
          this.roleDialog = false;
          this.getList();
          break;
        default:
          break;
      }
    },
    // 弹窗提交按钮点击
    submitDialog(type) {
      switch (type) {
        case 'add':
          this.$refs.formParams.validate((valid) => {
            if (valid) {
              designPersonAdd(this.formParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog(this.dialogType);
              })
            } else {
              return false
            }
          })
          break;
        case 'edit':
          this.$refs.formParams.validate((valid) => {
            if (valid) {
              designPersonEdit(this.formParams).then(() => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                })
                this.closeDialog(this.dialogType);
              })
            } else {
              return false
            }
          })
          break;
        case 'link':
          if (this.linkPerson.length > 0) {
            this.$refs.linkForm.validate((valid) => {
              if (valid) {
                if (this.linkPerson.length > 0) {
                  linkPerson(this.linkPerson).then(() => {
                    this.$message({
                      type: 'success',
                      message: '关联用户表成功!'
                    })
                    this.linkPerson = [];
                    this.multipleSelection = [];
                    this.getList();
                    this.closeDialog(this.dialogType);
                  })
                }
              } else {
                return false;
              }
            });
          }
          break;
        case 'role':
          editRoleById({
            srcUserId: this.formParams.srcUserId,
            roleIds: this.rolePersonIds
          }).then(() => {
            this.$message({
              type: 'success',
              message: '编辑角色成功!'
            })
            this.rolePersonIds = [];
            this.multipleSelection = [];
            this.getList();
            this.closeDialog(this.dialogType);
          })
          break;
        default:
          break;
      }
    },
    importSuccess(message) {
      this.$message({
        type: 'success',
        message: message
      })
      this.closeDialog(this.dialogType)
    },
    importError(message) {
      this.$message({
        type: 'error',
        message: message
      })
    },
    handleChangeLinkPerson(value) {

    },
    handleCheckAllChange(val) {
      this.rolePersonIds = val ? this.roleList.map(item => (item.id)) : [];
      this.isIndeterminate = false;
    },
    handleCheckedRoleChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.roleList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.roleList.length;
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
