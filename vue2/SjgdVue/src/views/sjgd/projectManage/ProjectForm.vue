<template>
  <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
    <!-- 项目名称 -->
    <el-form-item label="项目名称" prop="projectName">
      <div class="input-with-selector">
        <el-input v-model="formData.projectName" placeholder="请输入项目名称">
        </el-input>
        <query-selector
          query-placeholder="请输入项目名称"
          :columns="projectColumns"
          :fetch-data="fetchProjectList"
          :row-key="{
            id: 'projectId',
            label: 'projectName',
            tableId: 'projectId',
            tableLabel: 'projectName',
          }"
          :present-data="getProjectPresentData()"
          @select="(row) => handleSelect(row, 'projectName')"
        >
          <template #trigger>
            <el-button type="primary" size="small">选择项目</el-button>
          </template>
        </query-selector>
      </div>
    </el-form-item>

    <!-- 设总人员 -->
    <el-form-item label="设总人员" prop="designDirectorUserName">
      <div class="input-with-selector">
        <el-input
          style="pointer-events: none"
          v-model="formData.designDirectorUserName"
          placeholder="请选择设总人员"
        >
        </el-input>
        <query-selector
          :columns="userColumns"
          :fetch-data="fetchDesignUserList"
          :row-key="{
            id: 'srcUserId',
            label: 'userName',
            tableId: 'srcUserId',
            tableLabel: 'userName',
          }"
          role-name="协同办公-设计工代-项目设总"
          @select="
            (row) =>
              handleSelect(
                row,
                'designDirectorUserName',
                'designDirectorUserId'
              )
          "
        >
          <template #trigger>
            <el-button type="primary" size="small">选择</el-button>
          </template>
        </query-selector>
      </div>
    </el-form-item>

    <!-- 项目总监 -->
    <el-form-item label="项目总监" prop="projectDirectorUserName">
      <div class="input-with-selector">
        <el-input
          style="pointer-events: none"
          v-model="formData.projectDirectorUserName"
          placeholder="请选择项目总监"
        >
        </el-input>
        <query-selector
          :columns="userColumns"
          :fetch-data="fetchSupervisorList"
          :row-key="{
            id: 'srcUserId',
            label: 'userName',
            tableId: 'srcUserId',
            tableLabel: 'userName',
          }"
          role-name="协同办公-设计工代-监理项目总监"
          @select="
            (row) =>
              handleSelect(
                row,
                'projectDirectorUserName',
                'projectDirectorUserId'
              )
          "
        >
          <template #trigger>
            <el-button type="primary" size="small">选择总监</el-button>
          </template>
        </query-selector>
      </div>
    </el-form-item>

    <!-- 项目专监 -->
    <el-form-item label="项目专监" prop="projectSpecialOverseeUserName">
      <div class="input-with-selector">
        <el-input
          style="pointer-events: none"
          v-model="formData.projectSpecialOverseeUserName"
          placeholder="请选择项目专监"
        >
        </el-input>
        <query-selector
          :columns="userColumns"
          :fetch-data="fetchSupervisorList"
          :row-key="{
            id: 'srcUserId',
            label: 'userName',
            tableId: 'srcUserId',
            tableLabel: 'userName',
          }"
          role-name="协同办公-设计工代-监理项目专监/施工单位"
          @select="
            (row) =>
              handleSelect(
                row,
                'projectSpecialOverseeUserName',
                'projectSpecialOverseeUserId'
              )
          "
        >
          <template #trigger>
            <el-button type="primary" size="small">选择专监</el-button>
          </template>
        </query-selector>
      </div>
    </el-form-item>
    <!-- 项目成员 -->
    <el-form-item label="项目成员" prop="memberNames">
      <div class="person-tags">
        <div class="tags-wrapper">
          <el-tag
            v-for="(name, index) in formData.memberNames.split(',')"
            :key="index"
            closable
            size="small"
            @close="removeMember(index)"
            v-if="name"
          >
            {{ name }}
          </el-tag>
        </div>
        <query-selector
          :columns="userColumns"
          :fetch-data="fetchDesignUserList"
          :row-key="{
            id: 'srcUserId',
            label: 'userName',
            tableId: 'srcUserId',
            tableLabel: 'userName',
          }"
          :multiple="true"
          @select="handleMemberSelect"
        >
          <template #trigger>
            <el-button type="text">选择成员</el-button>
          </template>
        </query-selector>
      </div>
    </el-form-item>
    <!-- 施工单位 -->
    <el-form-item label="施工单位" prop="constructionUnitNames">
      <div class="person-tags">
        <div class="tags-wrapper">
          <el-tag
            v-for="(name, index) in formData.constructionUnitNames.split(',')"
            :key="index"
            closable
            size="small"
            @close="removeConstructionUnit(index)"
            v-if="name"
          >
            {{ name }}
          </el-tag>
        </div>
        <query-selector
          role-name="协同办公-设计工代-监理项目专监/施工单位"
          :columns="userColumns"
          :fetch-data="fetchDesignUserList"
          :row-key="{
            id: 'srcUserId',
            label: 'userName',
            tableId: 'srcUserId',
            tableLabel: 'userName',
          }"
          :multiple="true"
          @select="handleConstructionUnitSelect"
        >
          <template #trigger>
            <el-button type="text">选择成员</el-button>
          </template>
        </query-selector>
      </div>
    </el-form-item>
    <!--附件 -->
    <el-form-item label="附件">
      <FileUpload
        :limit="5"
        :type="2"
        v-model="formData.projectFiles"
        :uploadUrl="`/Sjgd/file/upload`"
        :file-size="fileLimit.imgSize"
        :file-type="fileLimit.imgType"
        @uploadSuccessData="uploadSuccessData"
        @deleteData="deleteData"
        :data="{ type: 'xmgl', hjID: 1, functionId: 20036 }"
      />
    </el-form-item>
    <!-- 设计专业 -->
    <el-form-item label="设计专业" prop="fromSpecialtyPersonList">
      <div class="specialty-list">
        <div
          v-for="(item, index) in formData.fromSpecialtyPersonList"
          :key="index"
          class="specialty-item"
        >
          <!-- 专业选择 -->
          <el-select
            v-model="item.specialtyName"
            placeholder="请选择专业"
            filterable
            @change="(val) => handleSpecialtyChange(val, index)"
          >
            <el-option
              v-for="specialty in allSpecialtySelectList"
              :key="specialty.value"
              :label="specialty.label"
              :value="specialty.value"
            />
          </el-select>

          <!-- 人员选择 -->
          <div class="person-tags">
            <div class="tags-wrapper">
              <el-tag
                v-for="(name, pIndex) in item.personNames.split(',')"
                :key="pIndex"
                closable
                size="small"
                @close="removePersonFromSpecialty(index, pIndex)"
                v-if="name"
              >
                {{ name }}
              </el-tag>
            </div>
            <query-selector
              :ref="`querySelector_${index}`"
              :present-data="getSelectedPersons(index)"
              :columns="userColumns"
              :fetch-data="(params) => fetchDesignUserList(params, index)"
              :multiple="true"
              :row-key="{
                id: 'srcUserId',
                label: 'userName',
                tableId: 'srcUserId',
                tableLabel: 'userName',
              }"
              @select="(rows) => handlePersonSelect(rows, index)"
            >
              <template #trigger>
                <el-button type="text">选择人员</el-button>
              </template>
            </query-selector>
          </div>

          <!-- 删除按钮 -->
          <el-button
            type="text"
            icon="el-icon-delete"
            @click="removeSpecialtyItem(index)"
            v-if="formData.fromSpecialtyPersonList.length > 1"
          />
        </div>

        <!-- 新增按钮 -->
        <el-button type="text" icon="el-icon-plus" @click="addSpecialtyItem"
          >新增一行</el-button
        >
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import QuerySelector from "@/views/sjgd/projectManage/QuerySelector";
import FileUpload from "components/DFDW/FileUpload.vue";
import {
  getVProjectPageList,
  GetLeaderList,
  detail,
  getPersonPageList,
} from "@/api/sjgd/projectManage";
import { listDataAll } from "@/api/dict/data";
import { getInfo } from "@/api/login";

export default {
  name: "ProjectForm",
  components: { QuerySelector, FileUpload },
  props: {
    initialData: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      fileLimit: {
        imgType: ["jpg", "png", "pdf", "JPG", "PNG", "PDF"],
      },
      allSpecialtySelectList: [], //专业选择
      formData: {
        memberNames: "", // 成员姓名（多个用逗号分隔）
        memberIds: "", // 成员ID（多个用逗号分隔）
        id: "",
        projectName: "",
        projectId: "",
        designDirectorUserName: "", //设总人员
        designDirectorUserId: "",
        projectDirectorUserName: "", //项目总监
        projectDirectorUserId: "",
        projectSpecialOverseeUserName: "", //项目专监
        projectSpecialOverseeUserId: "",
        fromSpecialtyPersonList: [], // 修改这里：改为空数组
        constructionUnitNames: "", // 施工单位
        constructionUnitUserIds: "",
      },
      rules: {
        projectName: [
          { required: true, message: "请选择项目名称", trigger: "change" },
        ],
        designDirectorUserName: [
          { required: true, message: "请选择设总人员", trigger: "change" },
        ],
        // projectDirectorUserName: [
        //   { required: true, message: "请选择项目总监", trigger: "change" },
        // ],
        // projectSpecialOverseeUserName: [
        //   { required: true, message: "请选择项目专监", trigger: "change" },
        // ],
        fromSpecialtyPersonList: [
          {
            required: true,
            message: "请至少添加一个设计专业",
            trigger: "change",
          },
        ],
      },
      // 项目选择器列配置
      projectColumns: [
        { label: "项目名称", prop: "projectName", width: "600" },
        { label: "项目编号", prop: "projectCode", width: "150" },
      ],
      // 人员选择器列配置
      userColumns: [
        { label: "姓名", prop: "userName" },
        { label: "联系电话", prop: "telephone" },
        { label: "部门", prop: "deptName" },
      ],
    };
  },
  watch: {
    initialData: {
      handler(val) {
        if (val) {
          // 先重置表单
          this.$refs.form?.resetFields();

          // 基础表单数据
          this.formData = {
            ...this.formData,
            ...val,
            fromSpecialtyPersonList: val.fromSpecialtyPersonList || [], // 这里也改为空数组
          };

          // 只在编辑模式（有id）时获取详情
          if (val.id) {
            detail({ id: val.id }).then((res) => {
              this.formData = {
                ...this.formData,
                projectFiles: res.result.projectFiles,
              };
            });
          } else {
            // 新增模式：清空附件并获取当前用户信息设置为设总人员
            this.formData.projectFiles = [];
            getInfo().then((res) => {
              this.formData.designDirectorUserName = res.data.RealName;
              this.formData.designDirectorUserId = res.data.Id;
            });
          }
        } else {
          this.$refs.form?.resetFields();
          this.formData = {
            id: "",
            projectName: "",
            projectId: "",
            designDirectorUserName: "",
            designDirectorUserId: "",
            projectDirectorUserName: "",
            projectDirectorUserId: "",
            projectSpecialOverseeUserName: "",
            projectSpecialOverseeUserId: "",
            fromSpecialtyPersonList: [],
          };
        }
      },
      immediate: true,
    },
  },
  created() {
    // 修改 created 钩子中的逻辑
    listDataAll({ type: "sjgd-specialty" }).then((res) => {
      this.allSpecialtySelectList = res.result || [];
    });

    // 只在编辑模式下获取详情
    if (this.initialData && this.initialData.id) {
      detail({ id: this.initialData.id }).then((res) => {
        this.formData.projectFiles = res.result.projectFiles;
      });
    }
  },
  methods: {
    handleMemberSelect(rows) {
      const names = rows.map((row) => row.userName).join(",");
      const ids = rows.map((row) => row.srcUserId).join(",");
      this.formData.memberNames = names;
      this.formData.memberIds = ids;
    },
    handleConstructionUnitSelect(rows) {
      const names = rows.map((row) => row.userName).join(",");
      const ids = rows.map((row) => row.srcUserId).join(",");
      this.formData.constructionUnitNames = names;
      this.formData.constructionUnitUserIds = ids;
    },
    removePersonFromSpecialty(specialtyIndex, personIndex) {
      console.log("嘻嘻了" + this.formData.fromSpecialtyPersonList);
      // 获取当前专业的数据
      const specialty = this.formData.fromSpecialtyPersonList[specialtyIndex];

      // 将名字和ID字符串转换为数组
      const names = specialty.personNames
        ? specialty.personNames.split(",")
        : [];
      const ids = specialty.personIds ? specialty.personIds.split(",") : [];

      // 删除对应索引的名字和ID
      names.splice(personIndex, 1);
      ids.splice(personIndex, 1);

      // 将数组转回字符串并更新数据
      this.formData.fromSpecialtyPersonList[specialtyIndex].personNames =
        names.join(",");
      this.formData.fromSpecialtyPersonList[specialtyIndex].personIds =
        ids.join(",");
    },
    deleteData() {
      console.log("deleteData");
    },
    uploadSuccessData() {
      this.$message({
        type: "success",
        message: "上传成功",
      });
    },
    // 获取项目列表
    fetchProjectList(params) {
      // TODO: 替换为实际的项目查询接口
      return getVProjectPageList(params);
    },

    // 获取设总人员列表
    fetchDesignUserList(params, specialtyIndex) {
      console.log("params" + JSON.stringify(params));
      console.log("specialtyIndex:" + specialtyIndex);
      // 获取对应行的专业值
      const specialty =
        this.formData.fromSpecialtyPersonList[specialtyIndex]?.specialtyName;
      if (specialty) {
        const queryParams = {
          ...params,
          specialty: specialty, // 将当前行的专业值作为查询参数
        };
        return getPersonPageList(queryParams);
      }
      return getPersonPageList(params);
    },

    // 获取监理人员列表
    fetchSupervisorList(params) {
      // TODO: 替换为实际的监理人员查询接口
      return getPersonPageList(params);
    },

    // 处理选择结果
    handleSelect(row, nameField, idField) {
      if (nameField === "projectName") {
        this.formData.projectName = row.projectName;
        this.formData.projectId = row.projectId;
        this.getLeaderList();
      } else {
        this.formData[nameField] = row.userName;
        if (idField) {
          this.formData[idField] = row.srcUserId;
        }
      }
    },
    getLeaderList() {
      GetLeaderList(this.formData).then((res) => {
        const leaderList = res.result || [];

        // 遍历数据，根据 leaderType 分别赋值
        leaderList.forEach((leader) => {
          if (leader.leaderType === "directorId") {
            // 设置项目总监
            this.formData.projectDirectorUserName = leader.realname;
            this.formData.projectDirectorUserId = leader.id;
          } else if (leader.leaderType === "professionSupervisorId") {
            // 设置项目专监
            this.formData.projectSpecialOverseeUserName = leader.realname;
            this.formData.projectSpecialOverseeUserId = leader.id;
          }
        });
      });
    },
    // 表单验证
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            resolve(this.formData);
          } else {
            reject("表单验证失败");
          }
        });
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
    },

    // 添加确定按钮处理方法
    async handleSubmit() {
      try {
        const formData = await this.validate();
        this.$emit("submit", formData); // 向父组件发送表单数据
        this.$emit("close"); // 触发关闭事件
      } catch (error) {
        console.error(error);
      }
    },

    // 添加取消按钮处理方法
    handleCancel() {
      this.resetForm();
      this.$emit("cancel"); // 触发取消事件
      this.$emit("close"); // 触发关闭事件
    },

    // 处理专业变化
    handleSpecialtyChange(val, index) {
      const specialty = this.allSpecialtySelectList.find(
        (item) => item.value == val
      );
      if (specialty) {
        this.formData.fromSpecialtyPersonList[index].specialtyName = val;
        // 当专业改变时，清空该行已选择的人员
        this.formData.fromSpecialtyPersonList[index].personNames = "";
        this.formData.fromSpecialtyPersonList[index].personIds = "";
        // 获取 QuerySelector 组件的引用并清空选择
        const querySelectorRef = this.$refs[`querySelector_${index}`];
        console.log(querySelectorRef);
        if (querySelectorRef) {
          querySelectorRef[0].clearSelection(); // 调用 clearSelection 方法
        }
        // 强制更新组件
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },

    // 处理人员选择
    handlePersonSelect(rows, index) {
      if (Array.isArray(rows) && rows.length > 0) {
        const names = rows.map((row) => row.userName).join(",");
        const ids = rows.map((row) => row.srcUserId).join(",");
        this.formData.fromSpecialtyPersonList[index].personNames = names;
        this.formData.fromSpecialtyPersonList[index].personIds = ids;
      } else {
        // 当没有选择数据时，清空对应的字段
        this.formData.fromSpecialtyPersonList[index].personNames = "";
        this.formData.fromSpecialtyPersonList[index].personIds = "";
      }
    },

    // 删除专业项
    removeSpecialtyItem(index) {
      this.formData.fromSpecialtyPersonList.splice(index, 1);
    },

    // 新增专业项
    addSpecialtyItem() {
      this.formData.fromSpecialtyPersonList.push({
        specialtyName: "",
        specialtyId: "",
        personNames: "",
        personIds: "",
      });
    },

    // 获取项目的已选数据
    getProjectPresentData() {
      if (!this.formData.projectId) return [];
      return [
        {
          projectId: this.formData.projectId,
          projectName: this.formData.projectName,
        },
      ];
    },

    // 获取人员的已选数据
    getSelectedPersons(index) {
      const specialty = this.formData.fromSpecialtyPersonList[index];
      if (!specialty.personNames || !specialty.personIds) return [];

      const names = specialty.personNames.split(",");
      const ids = specialty.personIds.split(",");

      return names
        .map((userName, i) => ({
          userName,
          srcUserId: ids[i],
        }))
        .filter((item) => item.userName && item.srcUserId);
    },

    removeMember(index) {
      const names = this.formData.memberNames
        ? this.formData.memberNames.split(",")
        : [];
      const ids = this.formData.memberIds
        ? this.formData.memberIds.split(",")
        : [];

      names.splice(index, 1);
      ids.splice(index, 1);

      this.formData.memberNames = names.join(",");
      this.formData.memberIds = ids.join(",");
    },
    removeConstructionUnit(index) {
      const names = this.formData.constructionUnitNames
        ? this.formData.constructionUnitNames.split(",")
        : [];
      const ids = this.formData.constructionUnitUserIds
        ? this.formData.constructionUnitUserIds.split(",")
        : [];

      names.splice(index, 1);
      ids.splice(index, 1);

      this.formData.constructionUnitNames = names.join(",");
      this.formData.constructionUnitUserIds = ids.join(",");
    },
  },
};
</script>

<style lang="scss" scoped>
.person-tags {
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 4px 8px;
}

.tags-wrapper {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.el-tag {
  margin: 2px;
}

.input-with-selector {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-input {
    flex: 1;
  }
}
</style>
