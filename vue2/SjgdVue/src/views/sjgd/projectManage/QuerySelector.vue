<template>
  <div class="query-selector">
    <!-- 触发按钮 -->
    <el-button type="text" icon="el-icon-search" @click="openDialog">
      选择
    </el-button>

    <!-- 选择弹窗 -->
    <el-dialog
      title="请选择"
      :visible.sync="dialogVisible"
      width="800px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :modal="false"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          v-model="queryParams[rowKey.label]"
          :placeholder="queryPlaceholder"
          clearable
          @keyup.enter.native="handleSearch"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="handleSearch"
          ></el-button>
        </el-input>
      </div>

      <!-- 表格区域 -->
      <el-table
        ref="table"
        :data="tableData"
        height="350"
        border
        :highlight-current-row="!multiple"
        @current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        :row-key="getRowKey"
      >
        <!-- 修改多选列配置 -->
        <el-table-column
          v-if="multiple"
          type="selection"
          width="55"
          :reserve-selection="true"
        >
        </el-table-column>

        <el-table-column
          v-for="item in columns"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        />
      </el-table>

      <!-- 分页 -->
      <el-pagination
        :current-page.sync="queryParams.pageNum"
        :page-size.sync="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
      />

      <!-- 底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="multiple ? !selectedRows.length : !selectedRow"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "QuerySelector",
  props: {
    queryPlaceholder: {
      type: String,
      default: "请输入姓名",
    },
    presentData: {
      type: Array,
      default: () => [],
    },
    //多选属性
    multiple: {
      type: Boolean,
      default: false,
    },
    // 表格列配置
    columns: {
      type: Array,
      required: true,
    },
    // 获取数据的方法
    fetchData: {
      type: Function,
      required: true,
    },
    // 修改 rowKey 配置，增加表格数据字段映射
    rowKey: {
      type: Object,
    },
    roleName: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      innerPresentData: [],
      selectedRows: [], // 多选数组
      dialogVisible: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: "",
        roleName: null,
      },
      tableData: [],
      total: 0,
      selectedRow: null,
    };
  },
  methods: {
    clearSelection() {
      this.selectedRows = []; // 清空多选数组
      if (this.$refs.table) {
        this.$refs.table.clearSelection(); // 清空表格的选择状态
      }
    },
    handleSelectionChange(rows) {
      if (this.multiple) {
        // 找出新增的选中行
        rows.forEach((row) => {
          const exists = this.selectedRows.some(
            (selected) =>
              selected[this.rowKey.tableId] === row[this.rowKey.tableId]
          );
          if (!exists) {
            this.selectedRows.push(row);
          }
        });

        // 找出取消选中的行并删除
        const currentPageIds = rows.map((row) => row[this.rowKey.tableId]);
        this.selectedRows = this.selectedRows.filter((row) => {
          // 如果是当前页的数据，需要在当前选中行中存在
          if (
            this.tableData.find(
              (item) => item[this.rowKey.tableId] === row[this.rowKey.tableId]
            )
          ) {
            return currentPageIds.includes(row[this.rowKey.tableId]);
          }
          // 不是当前页的数据保留
          return true;
        });
      }
    },
    openDialog() {
      this.dialogVisible = true;
      this.getList().then(() => {
        this.checkCurrentRow();
      });
    },

    // 添加 getRowKey 方法
    getRowKey(row) {
      return row[this.rowKey.tableId];
    },

    // 修改 checkCurrentRow 方法
    checkCurrentRow() {
      this.innerPresentData = this.presentData;
      if (this.multiple && this.innerPresentData.length > 0) {
        // 清除之前的选择
        this.$refs.table.clearSelection();

        // 使用映射字段进行匹配
        const selectedRows = this.tableData.filter((row) =>
          this.innerPresentData.some(
            (item) => item[this.rowKey.id] == row[this.rowKey.tableId]
          )
        );

        this.$nextTick(() => {
          selectedRows.forEach((row) => {
            this.$refs.table.toggleRowSelection(row, true);
          });
        });
      }
    },
    async getList() {
      try {
        this.queryParams.roleName = this.roleName;
        const res = await this.fetchData(this.queryParams);
        this.tableData = res.records || res.result.records;
        this.total = res.total || res.result.total;
        this.checkCurrentRow();
      } catch (error) {
        console.error(error);
        this.$message.error("获取数据失败");
      }
    },

    handleSearch() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handlePageChange() {
      this.getList();
    },

    handleCurrentChange(row) {
      if (!this.multiple) {
        this.selectedRow = row;
      }
    },

    handleConfirm() {
      const formatData = (data) => {
        if (Array.isArray(data)) {
          return data.map((item) => ({
            [this.rowKey.id]: item[this.rowKey.tableId],
            [this.rowKey.label]: item[this.rowKey.tableLabel],
          }));
        } else if (data) {
          return {
            [this.rowKey.id]: data[this.rowKey.tableId],
            [this.rowKey.label]: data[this.rowKey.tableLabel],
          };
        }
        return null;
      };

      const result = this.multiple
        ? formatData(this.selectedRows)
        : formatData(this.selectedRow);
      this.$emit("select", result);
      console.log("result看下" + JSON.stringify(result));
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.query-selector {
  display: inline-block;

  .search-area {
    margin-bottom: 15px;
    .el-input {
      width: 280px;
      display: flex;

      :deep(.el-input__inner) {
        height: 36px;
        line-height: 36px;
        border-right: 0;
        &:hover,
        &:focus,
        &:not(:placeholder-shown),
        &:placeholder-shown {
          border-color: #dcdfe6;
          & + .el-input-group__append {
            border-color: #dcdfe6;
          }
        }
      }

      :deep(.el-input-group__append) {
        border: 1px solid #dcdfe6;
        border-left: 0;
        background-color: #fff;
        padding: 0;
        display: flex;
        align-items: center;

        .el-button {
          height: 34px;
          margin: 0;
          padding: 0 15px;
        }
      }

      &.is-focus {
        :deep(.el-input__inner) {
          border-color: #409eff;
        }
        :deep(.el-input-group__append) {
          border-color: #409eff;
        }
      }
    }
  }

  .el-pagination {
    margin-top: 15px;
    text-align: center;
  }

  // 添加以下样式来统一按钮外观
  :deep(.el-button) {
    &.el-button--text {
      padding: 0;
      height: 28px;
      line-height: 28px;
    }
  }

  // 处理作为 input append 时的样式
  &.el-input-group__append {
    .el-button {
      border: none;
      background: transparent;
      padding: 0 15px;
      height: 100%;

      &.el-button--text {
        color: #409eff;
      }
    }
  }
}

.dialog-footer {
  .el-button {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #ffffff;
    border: 1px solid #dcdfe6;
    border-color: #dcdfe6;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: 400;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 4px;

    .el-button--text {
      padding: 0;
      height: 28px;
      line-height: 28px;
    }
  }

  .el-button + .el-button {
    margin-left: 10px;
  }

  .el-button--primary:hover,
  .el-button--primary:focus {
    background: #6d92fc;
    border-color: #6d92fc;
    color: #ffffff;
  }

  .el-button--primary {
    color: #ffffff;
    background-color: #4877fb;
    border-color: #4877fb;
  }

  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:hover,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:active {
    color: #ffffff;
    cursor: not-allowed;
    background-image: none;
    background-color: #a4bbfd;
    border-color: #a4bbfd;
  }

  .el-button--mini {
    padding: 7px 15px;
    font-size: 12px;
    border-radius: 3px;
  }
}
</style>
