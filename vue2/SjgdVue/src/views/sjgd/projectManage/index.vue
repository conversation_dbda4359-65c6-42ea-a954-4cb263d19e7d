<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">项目名称：</span>
        <el-input
          v-model="queryParams.projectName"
          clearable
          placeholder="请输入项目名称"
          @change="handleChange"
        ></el-input>
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-search"
          v-has-permi="['JDWSJ01PM02QX01']"
          @click="getList"
          >查询</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-refresh"
          @click="resetQuery"
          >重置</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-plus"
          @click="handleAdd"
          v-has-permi="['JDWSJ01PM02QX02']"
          >新增</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-edit"
          @click="openDialog('edit')"
          v-has-permi="['JDWSJ01PM02QX03']"
          >编辑</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-delete"
          @click="handleDelete"
          v-has-permi="['JDWSJ01PM02QX04']"
          >删除</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-download"
          @click="handleExport"
          v-has-permi="['JDWSJ01PM02QX05']"
          >导出派驻函</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-download"
          @click="downloadExcel"
          v-has-permi="['JDWSJ01PM02QX06']"
          >导出Excel</el-button
        >

        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
      </div>
    </div>
    <div class="table-box">
      <Table
        ref="table"
        :needSelect="true"
        :tableData="list"
        :tableOptions="realTableOptions"
        :loading="loading"
        :queryParam="queryParams"
        @getCurrentData="select"
        @getSelectionData="selectionData"
        @rowdblclick="openDialog('show')"
      >
        <template slot="designMajor" slot-scope="scope">
          <el-button type="text" @click="showDesignMajorDetails(scope.row)"
            >查看</el-button
          >
        </template>
        <template slot="projectFiles" slot-scope="scope">
          <el-button type="text" @click="showProjectFiles(scope.row)"
            >查看</el-button
          >
        </template>
      </Table>
      <Pagination
        @handleRefresh="handleCurrentChange"
        :queryParam="queryParams"
        layout="total, sizes, prev, pager, next, jumper"
        :total="queryParams.total"
        :page-sizes="[10, 30, 45, 60]"
      />
    </div>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="650px"
      :destroy-on-close="false"
      :close-on-click-modal="false"
    >
      <project-form ref="projectForm" :initial-data="formInitialData" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="设计专业详情"
      :visible.sync="designMajorDialogVisible"
      width="800px"
      :destroy-on-close="true"
    >
      <el-table :data="designMajorList" border>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="majorName" label="专业名"></el-table-column>
        <el-table-column
          prop="designerName"
          label="设计工代人员姓名"
        ></el-table-column>
        <el-table-column prop="contactInfo" label="联系方式"></el-table-column>
      </el-table>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewImagesDialog" title="文件预览" width="40%">
      <div class="image-preview">
        <FileUpload
          :disabled="true"
          :isShowTip="false"
          v-model="previewImagesDialogData"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDataAll } from "@/api/dict/data";
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {
  getProjectPageList,
  addProject,
  updateProject,
  deleteProject,
  detail,
  exportProjectPdf,
  downloadExcel,
} from "@/api/sjgd/projectManage";
import ProjectForm from "@/views/sjgd/projectManage/ProjectForm.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import { downLoad, getDate } from "@/utils/tool";
export default {
  name: "ProjectManage",
  components: { Table, Pagination, Dropdown, ProjectForm, FileUpload },
  data() {
    return {
      formInitialData: null,
      previewImagesDialogData: "",
      previewImagesDialog: false,
      dialogVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        projectName: "",
        projectDirectorUserName: "",
        projectSpecialOverseeUserName: "",
      },
      // 设计专业字典数据
      allSpecialtySelectList: [],
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 选中数据
      selectID: 0,
      // 选中的数据
      dataParams: {},
      // 多选列表
      paramsList: [],
      // 弹窗显示
      dialog: false,
      // 弹窗标题
      dialogTitle: "",
      // 表单数据
      formData: {},
      // 表单校验规则
      rules: {
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        projectDirectorUserName: [
          { required: true, message: "请输入项目总监", trigger: "blur" },
        ],
      },
      realTableOptions: [],
      tableOptions: [
        { label: "项目名称", prop: "projectName" },
        { label: "设总人员", prop: "designDirectorUserName" },
        { label: "项目总监", prop: "projectDirectorUserName" },
        { label: "专监", prop: "projectSpecialOverseeUserName" },
        { label: "项目成员", prop: "memberNames" },
        { label: "施工单位", prop: "constructionUnitNames" },
        {
          label: "附件",
          prop: "projectFiles",
          slot: true,
        },
        {
          label: "设计专业",
          prop: "designMajor",
          slot: true,
        },
      ],
      designMajorDialogVisible: false,
      designMajorList: [],
    };
  },
  created() {
    this.getList();
    listDataAll({ type: "sjgd-specialty" }).then((res) => {
      this.allSpecialtySelectList = res.result || [];
    });
  },
  methods: {
    // 提交表单
    async handleSubmit() {
      try {
        const formData = await this.$refs.projectForm.validate();
        const isEdit = !!formData.id;
        const apiCall = isEdit ? updateProject : addProject;
        await apiCall(formData);
        this.$message.success(isEdit ? "修改成功" : "新增成功");
        this.dialogVisible = false;
        this.getList(); // 刷新列表
      } catch (error) {
        console.error(error);
        this.$message.error(typeof error === "string" ? error : "保存失败");
      }
    },
    handleDelete() {
      if (this.paramsList.length === 0) {
        this.$message.warning("请选择要删除的项目！");
        return;
      }
      // 只有设总/创建人/角色有协同办公-设计工代-总管理的人可以编辑
      if (
        this.$store.getters.userid !== this.dataParams.designDirectorUserId &&
        this.$store.getters.userid !== this.dataParams.createId &&
        !this.$store.getters.roles.includes("协同办公-设计工代-总管理")
      ) {
        this.$message.warning("仅允许设总及原设总操作变更");
        return;
      }
      const ids = this.paramsList.map((item) => item.id);
      this.$confirm("是否确认删除选中的项目?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return deleteProject({ ids: ids });
        })
        .then(() => {
          this.$message.success("删除成功");
          this.getList();
        });
    },
    // 打开新增弹窗
    handleAdd() {
      this.dialogTitle = "新增项目";
      this.formInitialData = {
        id: null,
        projectFiles: [],
      };
      this.dialogVisible = true;
    },
    // 获取列表数据
    getList() {
      this.loading = true;
      getProjectPageList(this.queryParams)
        .then((res) => {
          this.list = res.result.records;
          this.queryParams.total = res.result.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 查询条件变化
    handleChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        projectName: "",
        projectDirectorUserName: "",
        projectSpecialOverseeUserName: "",
      };
      this.getList();
    },

    // 分页变化
    handleCurrentChange(val) {
      this.queryParams = val;
      this.getList();
    },

    // 表格选择
    select(row) {
      this.selectID = row.id;
      this.dataParams = row;
    },

    // 多选
    selectionData(datas) {
      this.paramsList = datas;
      if (this.paramsList.length === 1) {
        this.select(this.paramsList[0]);
      } else {
        // 当没有选中数据或选中多条数据时，清空 selectID
        this.selectID = 0;
        this.dataParams = {};
      }
    },

    // 获取新的表格列
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions];
    },

    // 打开弹窗
    openDialog(type) {
      if (type === "edit") {
        if (!this.selectID) {
          this.$message.warning("请选择要编辑的项目");
          return;
        }
        // 只有设总/创建人/角色有协同办公-设计工代-总管理的人可以编辑
        if (
          this.$store.getters.userid !== this.dataParams.designDirectorUserId &&
          this.$store.getters.userid !== this.dataParams.createId &&
          !this.$store.getters.roles.includes("协同办公-设计工代-总管理")
        ) {
          this.$message.warning("仅允许设总及原设总操作变更");
          return;
        }
        this.dialogTitle = "编辑项目";
        this.formInitialData = JSON.parse(JSON.stringify(this.dataParams));
      } else {
        this.dialogTitle = "新增项目";
        this.formInitialData = {
          id: null,
          projectName: "",
          projectId: "",
          designDirectorUserName: "",
          designDirectorUserId: "",
          projectDirectorUserName: "",
          projectDirectorUserId: "",
          projectSpecialOverseeUserName: "",
          projectSpecialOverseeUserId: "",
          projectFiles: [],
          fromSpecialtyPersonList: [],
        };
      }
      this.dialogVisible = true;
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const isEdit = !!this.formData.id;
          // TODO: 替换为实际的API调用
          const apiCall = isEdit ? updateProject : addProject;
          apiCall(this.formData).then((res) => {
            this.$message.success(isEdit ? "修改成功" : "新增成功");
            this.dialog = false;
            this.getList();
          });
        }
      });
    },
    showDesignMajorDetails(row) {
      this.designMajorList = [];
      if (
        row.fromSpecialtyPersonList &&
        row.fromSpecialtyPersonList.length > 0
      ) {
        row.fromSpecialtyPersonList.forEach((item) => {
          const findSpecialty = this.allSpecialtySelectList.find(
            (specialty) => item.specialtyName === specialty.value
          );
          const names = item.personNames.split(",");
          const phones = item.phones.split(",");
          names.forEach((name, index) => {
            this.designMajorList.push({
              majorName: findSpecialty?.label,
              designerName: name,
              contactInfo: phones[index] || "-",
            });
          });
        });
      }
      this.designMajorDialogVisible = true;
    },
    showProjectFiles(row) {
      detail({ id: row.id }).then((res) => {
        this.previewImagesDialogData = res.result.projectFiles || [];
        console.log(this.previewImagesDialogData);
        this.previewImagesDialog = true;
      });
    },
    handleExport() {
      if (this.paramsList.length === 0) {
        this.$message.error("请选择要导出的项目！");
        return;
      }

      const ids = this.paramsList.map((item) => item.id);
      const params = {
        ids: ids,
        downloadType: "zip",
      };

      exportProjectPdf(params).then((res) => {
        const fileName = "项目清单.zip";
        downLoad(res, fileName);
      });
    },

    downloadExcel() {
      downloadExcel(this.queryParams).then((res) => {
        let fileName =
          "项目信息" + getDate(new Date(), "YYYYMMDDHHmmssss") + ".xlsx";
        downLoad(res, fileName);
      });
    },
  },
};
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-input {
      width: 200px;
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

.file-item {
  padding: 8px 0;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
