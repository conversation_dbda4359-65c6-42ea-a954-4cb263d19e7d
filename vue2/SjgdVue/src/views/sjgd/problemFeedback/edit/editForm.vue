<template>
  <el-form :model="form" :rules="initRules" ref="form" label-width="120px" v-watermark="{label: watermark}">
    <el-row>
      <el-col :span="12">
        <el-form-item label="项目名称" prop="designProjectId">
          <multiple-column-selection
              ref="projectSelect"
              v-model="form.designProjectId"
              valueKey="id"
              label="projectName"
              placeholder="请选择项目名称"
              :filterable="true"
              :tableWidth="600"
              :options="projectList"
              :tableOptions="multipleColumnTableOptions"
              @change="handleChangeProject"
              style="width: 100%"
              :readonly="dialogType === 'fwjl' || readonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="业主项目部" prop="proprietorProjectdepartment">
          <el-input v-model="form.proprietorProjectdepartment" placeholder="请输入业主项目部" :readonly="readonly"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="反馈日期 " prop="feedbackDate">
          <el-date-picker
              style="width: 100%"
              v-model="form.feedbackDate"
              type="datetime"
              placeholder="请选择反馈日期"
              value-format="yyyy-MM-dd"
              :readonly="readonly">
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设计工代人员" prop="specialtyPersonId">
          <el-input v-model="form.specialtyPersonName" placeholder="请输入设计工代人员" readonly></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="是否签收" prop="isSigner">
          <el-select v-model="form.isSigner" placeholder="请选择是否签收" style="width: 100%;" :disabled="readonly">
            <el-option v-for="(item,index) in [{value:1,label:'是'},{value:0,label:'否'}]" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="form.isSigner === 1">
        <el-form-item label="签收人" prop="signerUserId">
          <el-select v-model="form.signerUserId" placeholder="请选择签收人" style="width: 100%;"
                     :disabled="readonly"
                     @change="handleChangeSignerUser">
            <el-option v-for="item in signerUserList" :key="item.userId" :label="item.userName"
                       :value="item.userId"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
<!--      <el-col :span="12">-->
<!--        <el-form-item label="关联监理通知单" prop="supervisorNotice">-->
<!--          <el-input v-model="form.supervisorNotice" placeholder="请输入关联监理通知单"></el-input>-->
<!--        </el-form-item>-->
<!--      </el-col>-->
      <el-col :span="24">
        <el-form-item label="附件" prop="siteProblemFile">
          <FileUpload
              :limit="5"
              :type="1"
              v-model="form.siteProblemFile"
              :uploadUrl="`/Sjgd/file/upload`"
              :file-size="fileLimit.imgSize"
              :file-type="fileLimit.imgType"
              @uploadSuccessData="uploadSuccessData"
              @deleteData="deleteData"
              :data="{type: 'wtfk', hjID: 1,functionId: 20035}"
              :disabled="readonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="现场情况描述" prop="fieldCondition">
          <el-input v-model="form.fieldCondition" placeholder="请输入现场情况描述" type="textarea" :readonly="readonly"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="发现问题及解决措施" prop="problemsAndMeasures">
          <el-input v-model="form.problemsAndMeasures" placeholder="请输入发现问题及解决措施"
                    type="textarea" :readonly="readonly"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import {getProjectList} from "api/sjgd/fieldService";

export default {
  name: "wtfk-edit-form",
  components: {FileUpload, MultipleColumnSelection},
  mounted() {
    this.getProjectList();
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    },
    readonly() {
      return this.form.approveState === 1 || this.form.approveState === 2 || this.form.approveState === 4;
    },
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          id: null,
          designProjectId: null,
          projectName: "",
          proprietorProjectdepartment: null,
          feedbackDate: new Date(),
          specialtyPersonId: this.$store.state.user.userid,
          specialtyPersonName: this.$store.state.user.name,
          signerUserId: [],
          supervisorNotice: null,
          siteProblemFile: null,
          fieldCondition: null,
          problemsAndMeasures: null,
          isSigner:1,
        }
      }
    },
    dialogType: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      form: {
        id: null,
        designProjectId: null,
        proprietorProjectdepartment: null,
        feedbackDate: null,
        specialtyPersonName: null,
        signerUserId: [],
        supervisorNotice: null,
        siteProblemFile: null,
        fieldCondition: null,
        problemsAndMeasures: null,
      },
      //签收人列表
      signerUserList: [],
      fileLimit: {
        imgSize: 20,
        imgType: ["jpg", "png", "pdf"],
      },
      initRules: {
        designProjectId: [
          {required: true, message: '请选择项目', trigger: 'blur'}
        ],
        proprietorProjectdepartment: [
          {required: true, message: '请输入业主项目部', trigger: 'blur'}
        ],
        fieldCondition: [
          {required: true, message: '请输入现场情况描述', trigger: 'blur'}
        ],
        problemsAndMeasures: [
          {required: true, message: '请输入发现问题及解决措施', trigger: 'blur'}
        ],
        signerUserId: [
          {required: true, message: '请选择签收人', trigger: 'blur'}
        ],
      },
      projectList: [],
      multipleColumnTableOptions: [
        {label: '项目名称', prop: 'projectName', filter: true},
        {label: '项目总监名称', prop: 'projectDirectorUserName', filter: true},
        {label: '项目专监名称', prop: 'projectSpecialOverseeUserName', filter: true},
        {label: '设总人员名称', prop: 'designDirectorUserName', filter: true},
      ]
    }
  },
  watch: {
    value: {
      handler(val) {
        this.form = val;
      },
      immediate: true,
      deep: true
    },
    form: {
      handler(val) {
        this.$emit("input", val);
      },
      immediate: true,
      deep: true
    },
    "form.designProjectId": {
      handler(val) {
        if (val) {
          let project = this.projectList.find(item => item.id === val);
          if (project) {
            this.$set(this.form,"projectName",project.projectName)
            //获取当前选中项目的项目总监和专监
            this.signerUserList = []
            if(project.projectDirectorUserId && project.projectDirectorUserName){
              this.signerUserList.push({
                userId: project.projectDirectorUserId,
                userName: project.projectDirectorUserName,
              })
            }

            if (project.projectSpecialOverseeUserId && project.projectSpecialOverseeUserName && project.projectDirectorUserId !== project.projectSpecialOverseeUserId) {
              this.signerUserList.push({
                userId: project.projectSpecialOverseeUserId,
                userName: project.projectSpecialOverseeUserName,
              })
            }

            // 施工单位
            if(project.constructionUnitUserIds && project.constructionUnitUserIds.length > 0
                && project.constructionUnitNames && project.constructionUnitNames.length > 0
            ){
              project.constructionUnitUserIds.split(',').forEach((item,index) => {
                this.signerUserList.push({
                  value: item,
                  text: project.constructionUnitNames.split(',')[index],
                });
              });
            }
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    //获取项目列表
    getProjectList() {
      getProjectList({designUserId: this.$store.state.user.userid, pageNum: 1, pageSize: -1}).then(res => {
        this.projectList = res.result.records
      })
    },
    handleChangeProject(value) {
      let project = this.projectList.find(item => item.id === value)
      this.$set(this.form,"projectName",project.projectName)
      ///this.handleChangeSignerUser(project)
      //获取当前选中项目的项目总监和专监
      this.signerUserList = []
      if(project.projectDirectorUserId && project.projectDirectorUserName){
        this.signerUserList.push({
          userId: project.projectDirectorUserId,
          userName: project.projectDirectorUserName,
        })

        //默认赋值项目总监
        this.$set(this.form,"signerUserId",project.projectDirectorUserId)
        this.$set(this.form,"signerUserName",project.projectDirectorUserName)
      }

      if (project.projectSpecialOverseeUserId && project.projectSpecialOverseeUserName && project.projectDirectorUserId !== project.projectSpecialOverseeUserId) {
        this.signerUserList.push({
          userId: project.projectSpecialOverseeUserId,
          userName: project.projectSpecialOverseeUserName,
        })
      }

      // 施工单位
      if(project.constructionUnitUserIds && project.constructionUnitUserIds.length > 0
          && project.constructionUnitNames && project.constructionUnitNames.length > 0
      ){
        project.constructionUnitUserIds.split(',').forEach((item,index) => {
          this.signerUserList.push({
            value: item,
            text: project.constructionUnitNames.split(',')[index],
          });
        });
      }

    },
    handleChangeSignerUser(value) {
      const userName = this.signerUserList.find(e=>e.userId === value).userName;
      this.$set(this.form,"signerUserName",userName)
    },
    uploadSuccessData() {
      this.$message({
        type: "success",
        message: "上传成功",
      });
    },
    deleteData() {
    },
    // 表单验证
    validate(callback) {
      // 防御性校验：确保表单引用存在
      if (!this.$refs.form)  {
        console.error('Form ref not found');
        return typeof callback === 'function' ? callback(false) : Promise.resolve(false);
      }

      // 统一返回 Promise 以实现链式调用
      return new Promise((resolve) => {
        this.$refs.form.validate((valid)  => {
          // 处理回调函数
          if (typeof callback === 'function') {
            callback(valid);
          }
          // 无论是否有回调，都通过 Promise 返回结果
          resolve(valid);
        }).catch((error) => {
          console.error('Validation error:', error);
          resolve(false); // 验证失败时统一返回 false
        });
      });
    }
  }
}
</script>

<style scoped lang="less">
.readonly {
  pointer-events: none;
}
</style>
