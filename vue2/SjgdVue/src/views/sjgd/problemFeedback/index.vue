<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">项目名称</span>
        <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称"
                  @change="handleChange"></el-input>
        <span class="font-size14">单据编号</span>
        <el-input v-model="queryParams.applyNo" clearable placeholder="请输入单据编号"
                  @change="handleChange"></el-input>
        <span class="font-size14">审批状态</span>
        <el-select v-model="queryParams.approveState" clearable placeholder="请选择审批状态" @change="handleChange">
          <el-option v-for="item in approveStates" :key="item.value" :label="item.label"
                     :value="item.value"></el-option>
        </el-select>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-plus" @click="openDialog('add')"
                   v-has-permi="['JDWSJ01PF01QX02']">新增
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-edit" @click="openDialog('edit')"
                   v-has-permi="['JDWSJ01PF01QX03']">编辑
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-delete" @click="remove"
                   v-has-permi="['JDWSJ01PF01QX04']">删除
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-view" @click="openInfo"
                   v-has-permi="['JDWSJ01PF01QX01']">查看详情
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-download" @click="downloadPdf"
                   v-has-permi="['JDWSJ01PF01QX05']">导出PDF
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-download" @click="downloadExcel"
                   v-has-permi="['JDWSJ01PF01QX08']">导出Excel
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-s-check" @click="openDialog('flow')"
                   v-has-permi="['JDWSJ01PF01QX06']">提交审批
        </el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-s-claim" @click="openDialog('qs')"
                   v-has-permi="['JDWSJ01PF01QX07']">签收
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :needSelect="true"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
          @getSelectionData="selectionData"
      >
        <template slot="siteProblemFile" slot-scope="scope">
          <el-button type="text" @click="handlePreviewImages(scope.row)">查看</el-button>
        </template>
        <!-- 反馈日期 -->
        <template slot="feedbackDate" slot-scope="scope">
          <div>{{ getDate(scope.row.feedbackDate) }}</div>
        </template>
        <!-- 签收日期 -->
        <template slot="signerDate" slot-scope="scope">
          <div>{{ scope.row.signerDate ? getDate(scope.row.signerDate) : '-' }}</div>
        </template>
        <template slot="approveState" slot-scope="scope">
          <div>
            {{
              scope.row.approveState == 0 ? '未提交' : scope.row.approveState == 1 ? '审核中' : scope.row.approveState == 2 ? '已审核' : scope.row.approveState == 3 ? '已驳回' : scope.row.approveState == 4 ? '流程终止' : ''
            }}
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog :title="dialogTitle" :visible.sync="dialog" width="55%" :close-on-click-modal="false">
      <edit-form ref="form" v-model="form" :key="componentKey" :dialog-type="dialogType"/>
      <div align="right">
        <el-button type="primary" @click="submitForm('addOrUpdate')">提交</el-button>
        <el-button @click="dialog = false">取消</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewImagesDialog" title="图片预览" width="40%">
      <div class="image-preview">
        <FileUpload
            :disabled="true"
            :isShowTip="false"
            v-model="previewImagesDialogData"
        />
      </div>
    </el-dialog>

    <el-dialog :visible.sync="detailDialog" title="查看详情" width="85%">
      <wtfkxq :wtfkId="selectID"></wtfkxq>
    </el-dialog>

    <el-dialog :visible.sync="signDialog" title="签收" width="30%">
      <el-form :model="form" :rules="signRules" ref="form" label-width="120px" v-watermark="{label: watermark}">
        <el-row>

          <el-col :span="24">
            <el-form-item label="签收日期 " prop="signerDate">
              <el-date-picker
                  style="width: 100%"
                  v-model="form.signerDate"
                  type="datetime"
                  placeholder="请选择签收日期"
                  value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="签收意见" prop="signerOpinion">
              <el-input type="textarea" v-model="form.signerOpinion" placeholder="请输入签收意见"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div align="right">
        <el-button type="primary" @click="submitForm('signFor')">提交</el-button>
        <el-button @click="signDialog = false">取消</el-button>
      </div>
    </el-dialog>

    <!--  审批  -->
    <FLow
        ref="flow"
        v-if="flowDialog"
        :yw-id="dataParams.id"
        :lc-jd-id="200351"
        :lc-define-id="20035"
        :first-lc-jd="200351"
        :init-choose="1"
        :termination-disable="true"
        @close="appClose"
        @agree="appAgree"
        @reject="appReject"
    >
    </FLow>

  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {getPageList, addOrEdit, getDetail, deleteById, downloadPdf, signFor,downloadExcel} from "@/api/sjgd/problemFeedback";
import {submitLc, rollBackForApply} from "@/api/sjgd/approve";
import FileUpload from "components/DFDW/FileUpload.vue";
import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";
import FLow from "@/components/FLow/index.vue";
import wtfkxq from "./detail/index.vue";
import {getDate,downLoad} from "@/utils/tool";
import editForm from "./edit/editForm.vue";
import {getDictDatas} from "@/utils/dict";

export default {
  //问题反馈
  name: 'wtfk',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, Dropdown, FileUpload, MultipleColumnSelection, FLow, wtfkxq, editForm},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getList();
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        approveState: -1
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      dataParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      dialog: false,

      componentKey: 0,

      realTableOptions: [],
      tableOptions: [
        {label: '项目名称', prop: 'projectName'},
        {label: '单据编号', prop: 'applyNo', width: 150},
        {label: '业主项目部', prop: 'proprietorProjectdepartment'},
        {label: '设计工代人员', prop: 'specialtyPersonName', width: 120},
        {label: '反馈日期', prop: 'feedbackDate', width: 120, slot: true},
        {label: '签收人', prop: 'signerUserName', width: 100},
        {label: '签收意见', prop: 'signerOpinion', tooltip: true, width: 200},
        {label: '签收日期', prop: 'signerDate', width: 120, slot: true},
        // {label: '关联监理通知单', prop: 'supervisorNotice', width: 140},
        {label: '附件', prop: 'siteProblemFile', width: 100, slot: true},
        {label: '关联服务记录', prop: 'serviceRecordNo', width: 150},
        {label: '审批', prop: 'approveState', width: 100, slot: true},
      ],
      signRules: {
        signerOpinion: [
          {required: true, message: '请输入签收意见', trigger: 'blur'}
        ],
        signerDate: [
          {required: true, message: '请输入签收日期', trigger: 'blur'}
        ]
      },
      form: {
        designProjectId: null,
        projectName: '',
        applyUserName: '',
        applyUserId: null,
        specialty: []
      },

      previewImagesDialogData: '',
      previewImagesDialog: false,

      hjID: 1,

      detailDialog: false,
      // 审批
      flowDialog: false,
      approveStates: [
        {label: '全部', value: -1},
        {label: '未提交', value: 0},
        {label: '审核中', value: 1},
        {label: '已审核', value: 2},
        {label: '已驳回', value: 3},
        {label: '流程终止', value: 4},
      ],
      //签收弹框
      signDialog: false,
    }
  },
  methods: {
    downloadExcel(){
      downloadExcel(this.queryParams).then((res) => {
        let fileName ='问题反馈'+ getDate(new Date(),'YYYYMMDDHHmmssss') +'.xlsx'
        downLoad(res, fileName)
      });
    },
    downloadPdf() {
      if (this.paramsList.length == 0) {
        this.$message.error("请先选择一条记录");
        return;
      }

      let ids = [];
      //遍历
      for (let item of this.paramsList) {
        if (item.approveState != 2) {
          this.$message.error("请先审批通过才可以导出pdf");
          return;  // 这里会直接退出整个函数
        }
        ids.push(item.id);
      }

      downloadPdf({ids: ids}).then(res => {
        // 根据响应类型设置文件名和类型
        const isZip = res.type === 'application/zip'
        const fileName = isZip ? '设计工代现场问题反馈单.zip' : this.paramsList[0].applyNo + '设计工代现场问题反馈单.pdf'

        // 创建下载链接
        const blob = new Blob([res], {type: res.type})
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = fileName
        link.click()

        // 清理
        window.URL.revokeObjectURL(link.href)
      })
    },
    getDate,
    openInfo() {
      if (this.selectID == 0) {
        this.$message.error("请先选择一条记录！");
        return;
      }
      console.log(this.selectID);
      this.detailDialog = true;
    },
    /** 查看现场问题附件 */
    handlePreviewImages(value) {
      this.previewImagesDialog = true
      this.hjID = 1
      //获取详情
      getDetail(value.id).then(res => {

        this.previewImagesDialogData = res.result.siteProblemFile
      })

    },
    uploadSuccessData() {
      this.$message({
        type: 'success',
        message: '上传成功'
      })

    },
    deleteData() {
      console.log("deleteData")
    },
    /** 删除 */
    remove() {
      if (this.selectID == 0) {
        this.$message.error('请先选择数据')
        return
      }

      for (const item of this.paramsList) {
        if (item.approveState !== 0 && item.approveState !== 3) {
          this.$message.error("只有未提交以及已驳回的才可删除");
          return;
        }
        //创建人或设计工代人员才可以编辑和删除
        if (item.specialtyPersonId !== this.$store.state.user.userid && item.specialtyPersonId !== this.$store.state.user.userid) {
          this.$message.error("只有创建人或者设计工代人员才可以删除");
          return;
        }
      }

      const ids = this.paramsList.map((row) => row.id);
      const applyNos = this.paramsList.map((row) => row.applyNo).join("、");

      this.$confirm(`确定要删除单据编号为${applyNos}的记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteById(ids).then(res => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.selectID = 0;
      this.dataParams = {};
      getPageList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.defaultForm.pageNum = 1
      this.defaultForm.pageSize = this.queryParams.pageSize
      this.monthlyCycleList = []
      this.queryParams = JSON.parpde(JSON.stringify(this.defaultForm))
      this.queryParams.approveState = -1;
      this.getList()
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.dataParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0])
      }
    },
    /** 提交表单 */
    submitForm(type) {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (type === 'signFor') {
            signFor(this.form).then(res => {
              this.$message.success('提交成功')
              this.signDialog = false
              this.getList()
            })
          }
          if (type === 'addOrUpdate') {
            addOrEdit(this.form).then(res => {
              this.$message.success('提交成功')
              this.dialog = false
              this.getList()
            })
          }

        }
      })
    },
    /** 清空表单 */
    clearForm() {
      this.form = {
        designProjectId: null,
        projectName: '',
        designUnit: getDictDatas('sjgd-sj-unit')[0].label,
        applyUserName: '',
        applyUserId: null,
        specialty: []
      }
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      // 首先重置表单数据
      this.clearForm()

      switch (type) {
        case 'add':
          this.dialog = true
          this.dialogTitle = '新增'
          //获取当前登录用户
          if (!this.$store.state.user.name) {
            this.$message.error('请先登录')
            return
          }
          // 重置组件 key 强制组件渲染
          this.componentKey += 1

          this.$nextTick(() => {
            this.form = {
              designProjectId: null,
              projectName: '',
              isSigner:1,
              specialtyPersonId: this.$store.state.user.userid,
              specialtyPersonName: this.$store.state.user.name,
              feedbackDate: new Date(),
              specialty: [] // 确保多选字段被初始化为空数组
            }
            this.$forceUpdate()
          })


          break;
        case 'edit':
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！")
            return
          }
          if (this.paramsList.length > 1) {
            this.$message.warning("只能编辑一条记录！")
            return
          }
          if (this.dataParams.approveState == 2) {
            this.$message.warning("已审批的记录不允许编辑！")
            return
          }
          if (this.dataParams.approveState == 1) {
            this.$message.warning("审批中的记录不允许编辑！")
            return
          }
          if (this.dataParams.approveState == 4) {
            this.$message.warning("已取消的记录不允许修改！")
            return
          }
          //创建人或设计工代人员才可以编辑和删除
          if (this.dataParams.specialtyPersonId !== this.$store.state.user.userid && this.dataParams.specialtyPersonId !== this.$store.state.user.userid) {
            this.$message.error("只有创建人或者设计工代人员才允许修改");
            return;
          }
          //获取详情
          getDetail(this.selectID).then(res => {
            this.form = res.result
          })
          this.dialog = true
          this.dialogTitle = '编辑'

          break;
        case "flow":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.paramsList.length > 1) {
            this.$message.warning("只能一条一条的提交审批！")
            return
          }
          if (this.dataParams.specialtyPersonId != this.$store.state.user.userid && this.dataParams.approveState == 0) {
            this.$message.warning("只有发起人才可以提交审批！")
            return
          }
          if (this.dataParams.approveState != 0 && this.dataParams.approveState != 3) {
            this.$message.warning("不允许重复提交审批！")
            return
          }
          //是否签收选择了’否‘ 并且审批状态等于 未提交或者 已驳回  点击提交的时候直接修改审批状态为已审批
          if(this.dataParams.isSigner === 0   && ( this.dataParams.approveState === 0 ||  this.dataParams.approveState === 3) ){
            this.dataParams.approveState = 2
            addOrEdit(this.dataParams).then(res => {
              this.$message.success('提交成功')
              this.dialog = false
              this.getList()
            })
            return;
          }
          this.flowDialog = true;
          this.$nextTick(() => {
            this.$refs.flow.openShowDialog();
          });
          break;
        case "qs":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.dataParams.isSigner == 0) {
            this.$message.error('此记录不需要签收!')
            return;
          }
          if (this.dataParams.signerUserId != this.$store.state.user.userid) {
            this.$message.error('签收人不是自己不允许签收!')
            return;
          }
          if (this.dataParams.approveState != 2) {
            this.$message.error('只有已审批的记录才可以签收!')
            return;
          }
          if (this.dataParams.signerDate && this.dataParams.signerOpinion) {
            this.$message.error('不允许重复签收!')
            return;
          }
          this.signDialog = true
          this.form.id = this.selectID
          //this.form.signerDate = new Date()
          this.$set(this.form, 'signerOpinion', '已收到')
          this.$set(this.form, 'signerDate', new Date())

          break;
      }
    },
    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case 'show':
          this.getList()
          this.dialog = false
          break;
      }
    },

    // 审批通过回调
    appAgree(params) {
      this.flowDialog = false;
      submitLc(params).then((res) => {
        this.$message.success("业务提交成功");
        this.getList();
      });
    },
    appClose(params) {
      this.flowDialog = false;
    },
    appReject(params) {
      this.flowDialog = false;

      rollBackForApply(params).then((res) => {
        this.$message.success("业务驳回成功");
        this.getList();
      });
    },

  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}


.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}
</style>
