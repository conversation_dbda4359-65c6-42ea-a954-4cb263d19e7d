<template>
  <el-form
      ref="formParams"
      :model="formParams"
      label-width="100px"
  >
    <el-divider content-position="left">服务反馈基本信息</el-divider>
    <el-row>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="项目名称">{{ formParams.projectName }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="单据编号">{{ formParams.applyNo }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="业主项目部">
          {{formParams.proprietorProjectdepartment }}
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="设计工代人员">{{ formParams.specialtyPersonName }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="反馈日期">
          {{ getDate(formParams.feedbackDate) }}
        </el-form-item>
      </el-col>
      <el-col :span="8" >
        <el-form-item class="force-width-60" label="是否签收" v-if="formParams.isSigner === 0">否</el-form-item>
        <el-form-item class="force-width-60" label="是否签收" v-if="formParams.isSigner === 1">是</el-form-item>
      </el-col>
      <el-col :span="8" v-if="formParams.isSigner === 1">
        <el-form-item class="force-width-60" label="签收人">{{ formParams.signerUserName }}</el-form-item>
      </el-col>
      <el-col :span="8" v-if="formParams.isSigner === 1">
        <el-form-item class="force-width-60" label="签收意见">{{ formParams.signerOpinion }}</el-form-item>
      </el-col>
      <el-col :span="8" v-if="formParams.isSigner === 1">
        <el-form-item class="force-width-60" label="签收日期">
          {{ formParams.signerDate ? getDate(formParams.signerDate) : '' }}
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="审批状态">
          <span v-if="formParams.approveState == 0">未提交</span>
          <span v-else-if="formParams.approveState == 1">审批中</span>
          <span v-else-if="formParams.approveState == 2">已审批</span>
          <span v-else-if="formParams.approveState == 3">已驳回</span>
          <span v-else-if="formParams.approveState == 4">流程终止</span>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="现场情况描述">{{ formParams.fieldCondition }}</el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="发现问题及解决措施">
          {{ formParams.problemsAndMeasures }}
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="附件">
          <FileUpload
              v-model="formParams.siteProblemFile"
              :data="{type: 'jlsq', hjID: 2,functionId: 20035}"
              :disabled="true"
              :isShowTip="false"
              :limit="5"
              :type="2"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import FileUpload from "components/DFDW/FileUpload.vue";
import {getDate} from "@/utils/tool";
import {getDetail} from "api/sjgd/problemFeedback";

export default {
  name: "wtfkxq-form",
  components: {FileUpload},
  props: {
    wtfkId: {
      type: Number,
      default: 0
    }
  },
  watch: {
    wtfkId: {
      handler(newVal) {
        this.currentWtfkId = newVal
        // 如果需要在 ID 变化时重新加载数据
        this.getDetailById()
      },
      immediate: true  // 立即执行一次
    }
  },
  data() {
    return {
      // 流程列表
      formParams: {},
      currentWtfkId: this.wtfkId
    }
  },
  methods: {
    getDate,
    getDetailById() {
      //获取详情
      getDetail(this.wtfkId).then(res => {
        this.formParams = res.result
       // console.log(JSON.parse(JSON.stringify(res.result)))
        this.$emit('form', this.formParams);
      })
    }
  },
}
</script>

<style scoped lang="less">

</style>
