<template>
  <el-form :model="form" :rules="initRules" ref="form" label-width="100px" v-watermark="{label: watermark}">
    <el-row>
      <el-col :span="12">
        <el-form-item label="项目名称" prop="designProjectId">
          <multiple-column-selection
              ref="projectSelect"
              v-model="form.designProjectId"
              valueKey="id"
              label="projectName"
              placeholder="请选择项目名称"
              :filterable="true"
              :tableWidth="600"
              :options="projectList"
              :tableOptions="multipleColumnTableOptions"
              @change="handleChangeProject"
              style="width: 100%"
              :readonly="dialogType === 'fwsq' || readonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设计单位" prop="designUnit">
          <el-input
              v-model="form.designUnit"
              placeholder="请输入设计单位"
              :readonly="dialogType === 'fwsq' || readonly"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="填报日期 " prop="applyDate">
          <el-date-picker
              style="width: 100%"
              v-model="form.applyDate"
              type="date"
              placeholder="请选择填报日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :readonly="readonly"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设计工代人员" prop="specialtyPersonId">
          <el-input
              v-model="form.specialtyPersonName"
              placeholder="请输入设计工代人员"
              readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="12">
        <el-form-item label="节点" prop="nodes">
          <node-selection v-model="form.nodes" :disabled="readonly" @change="handleNodeChange"/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="施工阶段" prop="engineeringPhase">
          <el-input
              v-model="form.engineeringPhase"
              placeholder="请输入施工阶段"
              :readonly="readonly"
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="附件" prop="recordFiles">
          <FileUpload
              :limit="5"
              :type="1"
              v-model="form.recordFiles"
              :uploadUrl="`/Sjgd/file/upload`"
              :file-size="fileLimit.imgSize"
              :file-type="fileLimit.imgType"
              @uploadSuccessData="uploadSuccessData"
              @deleteData="deleteData"
              :data="{ type: 'fwjl', hjID: 1, functionId: 20034 }"
              :disabled="readonly"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="现场情况描述" prop="fieldCondition">
          <el-input
              v-model="form.fieldCondition"
              placeholder="请输入现场情况描述"
              type="textarea"
              :readonly="readonly"
          ></el-input>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="发现问题及解决措施" prop="problemsAndMeasures">
          <el-input
              v-model="form.problemsAndMeasures"
              placeholder="请输入发现问题及解决措施"
              type="textarea"
              :readonly="readonly"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";
import NodeSelection from "views/sjgd/serviceRecord/NodeSelection.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import {getProjectList} from "api/sjgd/fieldService";
import {getDictDatas} from "@/utils/dict";

export default {
  name: "fwjl-edit-form",
  components: {FileUpload, NodeSelection, MultipleColumnSelection},
  created() {
    this.getProjectList();
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark;
    },
    readonly() {
      return this.form.approveState === 1 || this.form.approveState === 2 || this.form.approveState === 4;
    },
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          id: null,
          designProjectId: null,
          projectName: "",
          designUnit: getDictDatas('sjgd-sj-unit')[0].label,
          applyDate: new Date(),
          specialtyPersonId: this.$store.state.user.userid,
          specialtyPersonName: this.$store.state.user.name,
          nodes: [],
          engineeringPhase: null,
          recordFiles: null,
          fieldCondition: null,
          problemsAndMeasures: null
        }
      }
    },
    dialogType: {
      type: String,
      required: true,
    }
  },
  watch: {
    value: {
      handler(val) {
        this.form = val;
      },
      deep: true,
      immediate: true,
    },
    form: {
      handler(val) {
        this.$emit("input", val);
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      form: {
        id: null,
        designProjectId: null,
        designUnit: null,
        applyDate: null,
        specialtyPersonName: null,
        nodes: [],
        engineeringPhase: null,
        recordFiles: null,
        fieldCondition: null,
        problemsAndMeasures: null
      },
      fileLimit: {
        imgSize: 20,
        imgType: ["jpg", "png", "pdf"],
      },
      initRules: {
        designProjectId: [
          {required: true, message: "请选择项目", trigger: "blur"},
        ],
        designUnit: [
          {required: true, message: "请输入设计单位", trigger: "blur"},
        ],
        engineeringPhase: [
          {required: true, message: "请输入施工阶段", trigger: "blur"},
        ],
        fieldCondition: [
          {required: true, message: "请输入现场情况描述", trigger: "blur"},
        ],
        applyDate: [
          {required: true, message: "请选择填报日期", trigger: "blur"},
        ],
        signerUserId: [
          {required: true, message: "请选择签收人", trigger: "blur"},
        ],
        nodes: [
          {required: true, message: "请选择节点", trigger: "change"},
          {
            type: "array",
            min: 1,
            message: "请至少选择一个节点",
            trigger: "change",
          },
        ],
      },
      projectList: [],
      multipleColumnTableOptions: [
        {label: "项目名称", prop: "projectName", filter: true},
        {
          label: "项目总监名称",
          prop: "projectDirectorUserName",
          filter: true,
        },
        {
          label: "项目专监名称",
          prop: "projectSpecialOverseeUserName",
          filter: true,
        },
        {label: '设总人员名称', prop: 'designDirectorUserName', filter: true},
      ],
    }
  },
  methods: {
    //获取项目列表
    getProjectList() {
      getProjectList({designUserId: this.$store.state.user.userid, pageNum: 1, pageSize: -1}).then((res) => {
        this.projectList = res.result.records;
      });
    },
    handleChangeProject(value, type) {
      let project = this.projectList.find((item) => item.id === value);
      this.form.projectName = project.projectName;
      this.form.designProjectId = project.id;

      //默认赋值项目总监
      this.form.signerUserId = project.projectDirectorUserId;
      this.form.signerUserName = project.projectDirectorUserName;
    },
    handleNodeChange(value) {
      console.log("选中的节点：", value);
    },
    uploadSuccessData() {
      this.$message({
        type: "success",
        message: "上传成功",
      });
    },
    deleteData() {
    },
    // 表单验证
    validate(callback) {
      // 防御性校验：确保表单引用存在
      if (!this.$refs.form)  {
        console.error('Form ref not found');
        return typeof callback === 'function' ? callback(false) : Promise.resolve(false);
      }

      // 统一返回 Promise 以实现链式调用
      return new Promise((resolve) => {
        this.$refs.form.validate((valid)  => {
          // 处理回调函数
          if (typeof callback === 'function') {
            callback(valid);
          }
          // 无论是否有回调，都通过 Promise 返回结果
          resolve(valid);
        }).catch((error) => {
          console.error('Validation error:', error);
          resolve(false); // 验证失败时统一返回 false
        });
      });
    }
  }
}
</script>

<style scoped lang="less">
.readonly {
  pointer-events: none;
}
</style>