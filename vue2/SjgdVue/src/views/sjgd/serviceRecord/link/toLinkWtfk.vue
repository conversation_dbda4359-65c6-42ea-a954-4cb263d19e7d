<template>
  <el-dialog
      :title="dialogTitle"
      :visible.sync="dialog"
      width="55%"
      :close-on-click-modal="false"
  >
    <Table
        ref="table"
        height="500"
        :needSelect="true"
        :tableData="list"
        :tableOptions="realTableOptions"
        :loading="loading"
        :queryParam="queryParams"
        @getCurrentData="select"
        @getSelectionData="selectionData"
    >
      <template v-slot:feedbackDate="scope">
        <div>{{ getDate(scope.row.feedbackDate) }}</div>
      </template>
      <template v-slot:operate="scope">
        <el-button type="text" icon="el-icon-search" @click="handleShow(scope.row)">查看</el-button>
      </template>
    </Table>
    <Pagination
        @handleRefresh="handleCurrentChange"
        :queryParam="queryParams"
        layout="total, sizes, prev, pager, next, jumper"
        :total="queryParams.total"
    />
    <el-dialog
        title="查看服务记录详情"
        :visible.sync="dialogXq"
        width="85%"
        :close-on-click-modal="false"
        append-to-body
    >
      <wtfkxq :wtfk-id="this.wtfkId"></wtfkxq>
    </el-dialog>
    <div align="right">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="dialog = false">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Pagination from "components/Pagination/index.vue";
import wtfkxq from "views/sjgd/problemFeedback/detail/index.vue";
import Table from "components/MainTable/index.vue";
import {getDate} from "@/utils/tool";
import {getWtfkAssociationList, serviceAssociation, getAssociationList} from "api/sjgd/serviceRecord";

export default {
  name: "toLinkWtfk",
  components: {Table, wtfkxq, Pagination},
  props: {
    fwjlParams: {
      type: Object,
      required: () => {}
    }
  },
  data() {
    return {
      dialog: false,
      dialogTitle: '关联服务记录',
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
      realTableOptions: [
        {label: "单据编号", prop: "applyNo", width: 150},
        {label: '业主项目部', prop: 'proprietorProjectdepartment'},
        {label: '设计工代人员', prop: 'specialtyPersonName', width: 120},
        {label: '反馈日期', prop: 'feedbackDate', width: 120, slot: true},
        {label: "操作", prop: "operate", width: 80, slot: true},
      ],
      loading: false,
      selectID: 0,
      dataParams: {},
      dialogXq: false,
      wtfkId: 0,
      associationList: [],
      associationBPList: [],
      isManualClear: false,
    }
  },
  methods: {
    getDate,
    async open() {
      this.dialog = true;
      this.dialogTitle = this.fwjlParams.projectName+"-关联问题反馈"
      await this.getAssociationList();
      await this.getList();
    },
    async getList() {
      this.loading = true;
      this.selectID = 0;
      this.dataParams = {};
      this.queryParams.id = this.fwjlParams.id;
      this.queryParams.designProjectId = this.fwjlParams.designProjectId;
      await getWtfkAssociationList(this.queryParams).then(res => {
        this.list = res.result.records;
        this.queryParams.total = res.result.total;
        this.handleListChange();
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val;
      this.getList();
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id;
      this.dataParams = row;
      // this.associationList = [{
      //   serviceRecordId: this.fwjlParams.id,
      //   problemFeedbackId: row.id,
      // }];
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.multipleSelection = datas;
      if (this.multipleSelection.length === 1) {
        this.select(this.multipleSelection[0]);
      }
      if (this.isManualClear) {
        return;
      }
      const allIds = this.list.map(item => item.id);
      const nowIds = this.multipleSelection.map(item => item.id);
      const serviceRecordIds = this.associationList.map(item => item.problemFeedbackId);
      this.multipleSelection.forEach(item => {
        if (!serviceRecordIds.includes(item.id)) {
          this.associationList.push({
            serviceRecordId: this.fwjlParams.id,
            problemFeedbackId: item.id,
          })
        }
      })
      this.associationList = this.associationList.filter(item => !allIds.includes(item.problemFeedbackId) || nowIds.includes(item.problemFeedbackId));
    },
    /** 提交按钮事件 */
    submitForm() {
      let serviceassociationList = [];
      const nowIds = this.associationList.map(item => item.problemFeedbackId);
      const oldIds = this.associationBPList.map(item => item.problemFeedbackId);
      serviceassociationList = serviceassociationList.concat(this.associationBPList.filter(item => nowIds.includes(item.problemFeedbackId)));
      serviceassociationList = serviceassociationList.concat(this.associationList.filter(item => !oldIds.includes(item.problemFeedbackId)));
      console.log(serviceassociationList)
      if(serviceassociationList.length > 1) {
        this.$message.warning("请只能选择一条记录");
        return;
      }
      serviceAssociation({
        id: this.fwjlParams.id,
        serviceassociationList: serviceassociationList,
        type: 'wtfk'
      }).then(res => {
        this.$message.success("关联数据成功！");
        this.dialog = false;
        this.$emit('submit');
      })
    },
    async getAssociationList() {
      await getAssociationList({serviceRecordId: this.fwjlParams.id}).then(res => {
        this.associationList = res.result.filter(item => item.problemFeedbackId);
        this.associationBPList = res.result.filter(item => item.problemFeedbackId);
      })
    },
    handleListChange() {
      this.isManualClear = true;
      this.$refs.table.clearSelection();
      this.isManualClear = false;
      // if (this.associationList.length === 1) {
      //   this.$nextTick(() => {
      //     this.$refs.table.setCurrentRow(this.associationList[0]);
      //   })
      // }

      const ids = this.associationList.map(item => item.problemFeedbackId);
      this.multipleSelection = this.list.filter(item => ids.includes(item.id));
      this.$nextTick(() => {
        this.multipleSelection.forEach(row => {
          this.$refs.table.toggleRowSelection(row, true);
        })
      })
    },
    /** 查看详情 */
    handleShow(row) {
      this.wtfkId = row.id;
      this.dialogXq = true;
    },
  }
}
</script>

<style scoped lang="less">

</style>
