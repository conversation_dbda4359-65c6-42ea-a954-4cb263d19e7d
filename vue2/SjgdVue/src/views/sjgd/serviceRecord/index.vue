<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">项目名称</span>
        <el-input
          v-model="queryParams.projectName"
          clearable
          placeholder="请输入项目名称"
          @change="handleChange"
        ></el-input>
        <span class="font-size14">单据编号</span>
        <el-input
          v-model="queryParams.applyNo"
          clearable
          placeholder="请输入单据编号"
          @change="handleChange"
        ></el-input>
        <span class="font-size14">审批状态</span>
        <el-select v-model="queryParams.approveState" clearable placeholder="请选择审批状态" @change="handleChange">
          <el-option v-for="item in approveStates" :key="item.value" :label="item.label"
                     :value="item.value"></el-option>
        </el-select>
        <span class="font-size14">关联服务申请</span>
        <el-input
            v-model="queryParams.supervisorApplyNo"
            clearable
            placeholder="请输入关联服务申请编号"
            @change="handleChange"
        ></el-input>
        <span class="font-size14">关联问题反馈</span>
        <el-input
            v-model="queryParams.problemFeedbackNo"
            clearable
            placeholder="请输入关联问题反馈编号"
            @change="handleChange"
        ></el-input>
      </div>
    </div>
    <div class="operate-pannel">
      <div class="search-box">
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getList"
        >查询</el-button
        >
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置</el-button
        >
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="openDialog('add')"
            v-has-permi="['JDWSJ01SR01QX02']"
        >新增
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['JDWSJ01SR01QX03']"
        >编辑
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="remove"
            v-has-permi="['JDWSJ01SR01QX04']"
        >删除
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="openInfo"
            v-has-permi="['JDWSJ01SR01QX01']"
        >查看详情
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleExport"
            v-has-permi="['JDWSJ01SR01QX05']"
        >导出PDF
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="downloadExcel"
            v-has-permi="['JDWSJ01SR01QX07']"
        >导出Excel
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-s-check"
            @click="openDialog('flow')"
            v-has-permi="['JDWSJ01SR01QX06']"
        >提交审批
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="openDialog('link_fwsq')"
            v-has-permi="['JDWSJ01SR01QX08']"
        >关联申请
        </el-button>
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="openDialog('link_wtfk')"
            v-has-permi="['JDWSJ01SR01QX08']"
        >关联反馈
        </el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
      </div>
    </div>
    <div class="table-box">
      <Table
        ref="table"
        :needSelect="true"
        :tableData="list"
        :tableOptions="realTableOptions"
        :loading="loading"
        :queryParam="queryParams"
        @getCurrentData="select"
        @rowdblclick="openDialog('appendDelayHour')"
        @getSelectionData="selectionData"
      >
        <!-- 填报日期 -->
        <template slot="applyDate" slot-scope="scope">
          <div>
            {{ getDate(scope.row.applyDate) }}
          </div>
        </template>

        <template slot="recordFiles" slot-scope="scope">
          <el-button
            type="text"
            @click="handlePreviewImages(scope.row, 'records')"
            >查看</el-button
          >
        </template>

        <template slot="approveState" slot-scope="scope">
          <div>
            {{
              scope.row.approveState == 0
                ? "未提交"
                : scope.row.approveState == 1
                ? "审核中"
                : scope.row.approveState == 2
                ? "已审核"
                : scope.row.approveState == 3
                ? "已驳回"
                : scope.row.approveState == 4
                ? "流程终止"
                : ""
            }}
          </div>
        </template>

        <template slot="node" slot-scope="scope">
          <div>{{ getNodeLabel(scope.row.node) }}</div>
        </template>
      </Table>
      <Pagination
        @handleRefresh="handleCurrentChange"
        :queryParam="queryParams"
        layout="total, prev, pager, next, jumper"
        :total="queryParams.total"
      />
    </div>

    <edit
        v-model="dialog"
        :dialog-title="dialogTitle"
        :dialog-type="dialogType"
        :form="form"
        :component-key="componentKey"
        @submitForm="getList"
    />

    <!-- 查看专业和人员信息 -->
    <el-dialog
      :title="specialtyDialogTitle"
      :visible.sync="specialtyDialog"
      width="30%"
    >
      <Table
        ref="table"
        :needSelect="false"
        :tableData="specialtyDialogTableData"
        :tableOptions="specialtyDialogTableOptions"
        :loading="loading"
        :queryParam="queryParams"
      >
      </Table>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="previewImagesDialog" title="文件预览" width="40%">
      <div class="image-preview">
        <FileUpload
          :disabled="true"
          :isShowTip="false"
          v-model="previewImagesDialogData"
        />
      </div>
    </el-dialog>

    <!-- 查看详情 -->
    <el-dialog
      v-if="detailDialog"
      :visible.sync="detailDialog"
      title="查看详情"
      width="85%"
    >
      <fwjlxq :fwjlId="this.selectID"></fwjlxq>
    </el-dialog>

    <!--  审批  -->
    <FLow
      ref="flow"
      v-if="flowDialog"
      :yw-id="dataParams.id"
      :lc-jd-id="200341"
      :lc-define-id="20034"
      :first-lc-jd="200341"
      :init-choose="1"
      :termination-disable="true"
      @close="appClose"
      @agree="appAgree"
      @reject="appReject"
    >
    </FLow>

    <link-fwsq ref="linkFwsq" v-if="dataParams.designProjectId" :fwjl-params="dataParams" @submit="getList"/>
    <link-wtfk ref="linkWtfk" v-if="dataParams.designProjectId" :fwjl-params="dataParams" @submit="getList"/>
  </div>
</template>

<script>
import fwjlxq from "./detail/index.vue";
import { listDataAll } from "@/api/dict/data";
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {
  getPageList,
  addOrEdit,
  getDetail,
  deleteServiceRecord,
  exportProjectPdf,
  downloadExcel
} from "@/api/sjgd/serviceRecord";
import { submitLc, rollBackForApply } from "@/api/sjgd/approve";
import FileUpload from "components/DFDW/FileUpload.vue";
import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";
import NodeSelection from "@/views/sjgd/serviceRecord/NodeSelection.vue";
import FLow from "@/components/FLow/index.vue";
import { downLoad, getDate } from "@/utils/tool";
import edit from "./edit";
import linkFwsq from "./link/toLinkFwsq.vue"
import linkWtfk from "./link/toLinkWtfk.vue"
import {getDictDatas} from "@/utils/dict";


export default {
  name: "fwjl",
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams));
    this.getNodeDict();
  },
  components: {
    fwjlxq,
    Table,
    Pagination,
    Dropdown,
    FileUpload,
    MultipleColumnSelection,
    NodeSelection,
    FLow,
    edit,
    linkFwsq,
    linkWtfk,
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark;
    },
  },
  mounted() {
    this.getList();
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ["1"],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      dataParams: {},
      // 弹出框类别
      dialogType: "",
      // 弹出框标题
      dialogTitle: "",
      // 弹出框标显示
      dialog: false,
      componentKey: 0,
      approveStates: [
        {label: '全部', value: -1},
        {label: '未提交', value: 0},
        {label: '审核中', value: 1},
        {label: '已审核', value: 2},
        {label: '已驳回', value: 3},
        {label: '流程终止', value: 4},
      ],
      realTableOptions: [],
      tableOptions: [
        { label: "项目名称", prop: "projectName" },
        { label: "单据编号", prop: "applyNo", width: 150 },
        { label: "设计单位", prop: "designUnit" },
        { label: "填报日期", prop: "applyDate", width: 120, slot: true },
        { label: "设计工代人员", prop: "specialtyPersonName", width: 120 },
        { label: "工程阶段", prop: "engineeringPhase", width: 100 },
        { label: "节点", prop: "node", slot: true },
        { label: "附件", prop: "recordFiles", width: 100, slot: true },
        { label: "关联服务申请", prop: "supervisorApplyNo", width: 150},
        { label: "关联问题反馈", prop: "problemFeedbackNo", width: 150},
        { label: "审批", prop: "approveState", width: 100, slot: true },
      ],
      form: {
        designProjectId: null,
        projectName: "",
        applyUserName: "",
        applyUserId: null,
        specialty: [],
        nodes: [],
      },

      specialtyDialog: false,
      specialtyType: "",
      specialtyDialogTableOptions: [],
      specialtyDialogTableData: [],
      specialtyDialogTitle: "",

      previewImagesDialogData: "",
      previewImagesDialog: false,
      previewImagesDialogType: "",
      previewImagesDialogTitle: "",
      hjID: 1,

      // 添加节点字典数据
      lineNodeDict: [],
      transformerNodeDict: [],
      // 审批
      flowDialog: false,

      selectedRows: [],
      detailDialog: false,
    };
  },
  methods: {
    getDate,
    openInfo() {
      if (this.selectID == 0) {
        this.$message.warning("请先选择一条记录！");
        return;
      }
      if (this.selectedRows.length > 1) {
        this.$message.warning("只能选择一条记录！");
        return;
      }
      console.log(this.selectID);
      this.detailDialog = true;
    },
    handleExport() {
      if (this.selectedRows.length === 0) {
        this.$message.error("请选择要导出的记录");
        return;
      }

      const ids = this.selectedRows.map((item) => item.id);
      const params = {
        ids: ids,
        downloadType: "zip",
      };

      exportProjectPdf(params).then((res) => {
        let fileName = "服务记录" + new Date().toISOString().slice(0, 10) + ".zip";
        if (ids.length === 1) {
          fileName = this.selectedRows[0].projectName + "服务记录" + "(" + this.selectedRows[0].applyNo + ").pdf";
        }
        downLoad(res, fileName);
      });
    },
    downloadExcel(){
      downloadExcel(this.queryParams).then((res) => {
        let fileName ='服务记录'+ getDate(new Date(),'YYYYMMDDHHmmssss') +'.xlsx'
        downLoad(res, fileName)
      });
    },


    /** 查看现场问题附件 */
    handlePreviewImages(value, type) {
      this.previewImagesDialog = true;
      // this.previewImagesDialogData = value
      this.previewImagesDialogType = type;
      this.previewImagesDialogTitle =
        type == "recordFiles" ? "现场问题附件" : "联系单附件";
      this.hjID = type == "recordFiles" ? 1 : 2;
      console.log("value", value.id);
      //获取详情
      getDetail({ id: value.id }).then((res) => {
        this.previewImagesDialogData =
          type == "recordFiles"
            ? res.result.recordFiles
            : res.result.recordFiles;
      });
    },
    uploadSuccessData() {
      this.$message({
        type: "success",
        message: "上传成功",
      });
    },
    deleteData() {
      console.log("deleteData");
    },
    /** 删除 */
    remove() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的记录");
        return;
      }

      for (const item of this.selectedRows) {
        if (item.approveState != 0 && item.approveState != 3) {
          this.$message.warning("只有未提交以及已驳回的才可删除");
          return;
        }
        // 创建人或设计工代人员才可以编辑和删除
        if (this.$store.getters.userid !== this.dataParams.specialtyPersonId && this.$store.getters.userid !== this.dataParams.createId) {
          this.$message.warning("创建人或设计工代人员才可以编辑和删除");
          return;
        }
      }

      const ids = this.selectedRows.map((row) => row.id);
      const applyNos = this.selectedRows.map((row) => row.applyNo).join("、");

      this.$confirm(`确定要删除单据编号为${applyNos}的记录吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteServiceRecord({ ids: ids }).then((res) => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions];
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 数据查询 */
    getList() {
      this.loading = true;
      this.selectID = 0;
      this.dataParams = {};
      getPageList(this.queryParams)
        .then((res) => {
          this.list = res.result.records;
          this.queryParams.total = res.result.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val;
      this.getList();
    },
    /** 重置查询 */
    resetQuery() {
      this.defaultForm.pageNum = 1;
      this.defaultForm.pageSize = this.queryParams.pageSize;
      this.monthlyCycleList = [];
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm));
      this.getList();
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id;
      this.dataParams = row;
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.selectedRows = datas;
      if (datas.length === 1) {
        this.select(datas[0]);
      }
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          addOrEdit(this.form).then((res) => {
            this.$message.success("提交成功");
            this.dialog = false;
            this.getList();
          });
        }
      });
    },
    /** 清空表单 */
    clearForm() {
      this.form = {
        designProjectId: null,
        projectName: "",
        designUnit: getDictDatas('sjgd-sj-unit')[0].label,
        applyUserName: "",
        applyUserId: null,
        specialty: [],
      };
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      // 首先重置表单数据
      this.clearForm();
      this.dialogType = type;
      switch (type) {
        case "add":
          this.dialog = true;
          this.dialogTitle = "新增";
          //获取当前登录用户
          if (!this.$store.state.user.name) {
            this.$message.error("请先登录");
            return;
          }
          // 重置组件 key 强制组件渲染
          this.componentKey += 1;

          this.$nextTick(() => {
            this.form = {
              designProjectId: null,
              projectName: "",
              designUnit: getDictDatas('sjgd-sj-unit')[0].label,
              specialtyPersonId: this.$store.state.user.userid,
              specialtyPersonName: this.$store.state.user.name,
              applyDate: new Date(),
              specialty: [],
            };
            this.$forceUpdate();
          });

          break;
        case "edit":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.dataParams.approveState == 1) {
            this.$message.warning("审批中不允许编辑！");
            return;
          }
          if (this.dataParams.approveState == 2) {
            this.$message.warning("已审批不允许编辑！");
            return;
          }
          if (this.dataParams.approveState == 4) {
            this.$message.warning("流程终止不允许编辑！");
            return;
          }
          // 创建人或设计工代人员才可以编辑和删除
          if (
            this.$store.getters.userid !== this.dataParams.specialtyPersonId &&
            this.$store.getters.userid !== this.dataParams.createId
          ) {
            this.$message.warning("创建人或设计工代人员才可以编辑和删除");
            return;
          }
          //获取详情
          getDetail({ id: this.selectID }).then((res) => {
            this.form = res.result;
            // 将node字符串转换为nodes数组
            if (res.result.node) {
              this.form.nodes = res.result.node.split(",");
            }

            // 重置组件 key 强制组件渲染
            this.componentKey += 1;

            // 确保在下一个 tick 中设置项目值
            this.$nextTick(() => {
              // 设置项目ID作为组件的值
              this.form.designProjectId = res.result.designProjectId;
              this.form.projectName = res.result.projectName;
              this.$forceUpdate();
            });
          });
          this.dialog = true;
          this.dialogTitle = "编辑";

          break;

        case "flow":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.selectedRows.length > 1) {
            this.$message.warning("只能一条一条的提交审批！");
            return;
          }
          if (
            this.dataParams.approveState != 0 &&
            this.dataParams.approveState != 3
          ) {
            this.$message.warning("不允许重复提交审批！");
            return;
          }
          this.flowDialog = true;
          this.$nextTick(() => {
            this.$refs.flow.openShowDialog();
          });
          break;
        case "link_fwsq":
          if (this.selectID == 0) {
            this.$message.warning("请先选择一条记录！");
            return;
          }
          if (this.selectedRows.length > 1) {
            this.$message.warning("只能选择一条记录！");
            return;
          }
          this.$refs.linkFwsq.open();
          break;
        case "link_wtfk":
          if (this.selectID == 0) {
            this.$message.warning("请先选择一条记录！");
            return;
          }
          if (this.selectedRows.length > 1) {
            this.$message.warning("只能选择一条记录！");
            return;
          }
          this.$refs.linkWtfk.open();
          break;
      }
    },
    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case "show":
          this.getList();
          this.dialog = false;
          break;
      }
    },
    // 获取节点字典数据
    getNodeDict() {
      listDataAll({ type: "sjgd-line-node" }).then((res) => {
        this.lineNodeDict = res.result;
      });
      listDataAll({ type: "sjgd-electricity-node" }).then((res) => {
        this.transformerNodeDict = res.result;
      });
    },
    // 获取节点label
    getNodeLabel(nodeValue) {
      if (!nodeValue) return "";
      const nodeValues = nodeValue.split(",");
      const labels = nodeValues.map((value) => {
        // 处理自定义节点
        if (value.startsWith("sjgd-line-node-12:")) {
          return `[线路工程]其他：${value.split(":")[1]}`;
        }
        if (value.startsWith("sjgd-electricity-node-16:")) {
          return `[变电工程]其他：${value.split(":")[1]}`;
        }

        // 处理常规节点
        const lineNode = this.lineNodeDict.find((item) => item.value === value);
        if (lineNode) return `[线路工程] ${lineNode.label}`;

        const transformerNode = this.transformerNodeDict.find(
          (item) => item.value === value
        );
        if (transformerNode) return `[变电工程] ${transformerNode.label}`;

        return value;
      });
      return labels.join("、");
    },

    // 审批通过回调
    appAgree(params) {
      this.flowDialog = false;
      submitLc(params).then((res) => {
        this.$message.success("业务提交成功");
        this.getList();
      });
    },
    appClose(params) {
      this.flowDialog = false;
    },
    appReject(params) {
      this.flowDialog = false;

      rollBackForApply(params).then((res) => {
        this.$message.success("业务驳回成功");
        this.getList();
      });
    },
  },
};
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}
</style>
