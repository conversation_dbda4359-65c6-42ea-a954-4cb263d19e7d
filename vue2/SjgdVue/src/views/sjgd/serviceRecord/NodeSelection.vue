<template>
  <div class="node-selection">
    <el-select
      v-model="selectedNodes"
      multiple
      placeholder="请选择节点"
      style="width: 100%"
      @change="handleChange"
      :value-key="'value'"
      :close-on-click-modal="false"
    >
      <el-option-group label="变电工程服务节点">
        <el-option
          v-for="item in transformerNodes"
          :key="'transformer_' + item.value"
          :label="getDisplayLabel(item, 'transformer')"
          :value="getOptionValue(item, 'transformer')"
        >
          <!-- 其他选项的输入框 -->
          <div
            v-if="
              item.value === 'sjgd-electricity-node-16' &&
              selectedNodes.includes(item.value)
            "
            class="other-input"
            @click.stop
          >
            其他:
            <input
              type="text"
              v-model="otherNodeName.transformer"
              placeholder="请输入节点名称"
              @input="handleOtherInput('transformer')"
              @blur="handleOtherInputBlur('transformer')"
              @click.stop
            />
          </div>
        </el-option>
      </el-option-group>
      <el-option-group label="线路工程服务节点">
        <el-option
            v-for="item in lineNodes"
            :key="'line_' + item.value"
            :label="getDisplayLabel(item, 'line')"
            :value="getOptionValue(item, 'line')"
        >
          <!-- 其他选项的输入框 -->
          <div
              v-if="
              item.value === 'sjgd-line-node-12' &&
              selectedNodes.includes(item.value)
            "
              class="other-input"
              @click.stop
          >
            其他:
            <input
                type="text"
                v-model="otherNodeName.line"
                placeholder="请输入节点名称"
                @input="handleOtherInput('line')"
                @blur="handleOtherInputBlur('line')"
                @click.stop
            />
          </div>
        </el-option>
      </el-option-group>
    </el-select>
  </div>
</template>

<script>
import { listDataAll } from "@/api/dict/data";
export default {
  name: "NodeSelection",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectedNodes: [],
      lineNodes: [
        { label: "施工准备阶段", value: "line_prep" },
        { label: "基础施工阶段", value: "line_foundation" },
        { label: "杆塔组立阶段", value: "line_tower" },
        { label: "架线施工阶段", value: "line_wiring" },
        { label: "竣工验收阶段", value: "line_completion" },
      ],
      transformerNodes: [
        { label: "施工准备阶段", value: "trans_prep" },
        { label: "土建施工阶段", value: "trans_civil" },
        { label: "设备安装阶段", value: "trans_equipment" },
        { label: "调试阶段", value: "trans_debug" },
        { label: "竣工验收阶段", value: "trans_completion" },
      ],
      otherNodeName: {
        transformer: "",
        line: "",
      },
    };
  },
  created() {
    this.lineNodes = [];
    this.transformerNodes = [];
    listDataAll({ type: "sjgd-line-node" }).then((res) => {
      res.result.forEach((item) => {
        this.lineNodes.push({ label: item.label, value: item.value });
      });
    });
    listDataAll({ type: "sjgd-electricity-node" }).then((res) => {
      res.result.forEach((item) => {
        this.transformerNodes.push({ label: item.label, value: item.value });
      });
    });
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        if (Array.isArray(val)) {
          this.selectedNodes = val;
        } else if (typeof val === "string") {
          // 如果传入的是字符串,则分割成数组
          this.selectedNodes = val.split(",").filter(Boolean);
        } else {
          this.selectedNodes = [];
        }
      },
    },
  },
  methods: {
    handleChange(value) {
      this.$emit("input", value);
      this.$emit("change", value);
    },
    getOptionValue(item, type) {
      const nodeMap = {
        transformer: {
          prefix: "sjgd-electricity-node-16:",
          nodeValue: "sjgd-electricity-node-16",
        },
        line: {
          prefix: "sjgd-line-node-12:",
          nodeValue: "sjgd-line-node-12",
        },
      };

      // 如果是其他选项，检查是否有自定义输入
      if (item.value === nodeMap[type].nodeValue) {
        const customValue = this.selectedNodes.find((val) =>
          val.startsWith(nodeMap[type].prefix)
        );
        return customValue || item.value;
      }
      return item.value;
    },
    handleOtherInput(type) {
      const nodeMap = {
        transformer: {
          prefix: "sjgd-electricity-node-16:",
          nodeValue: "sjgd-electricity-node-16",
        },
        line: {
          prefix: "sjgd-line-node-12:",
          nodeValue: "sjgd-line-node-12",
        },
      };
      const { prefix, nodeValue } = nodeMap[type];

      // 在输入过程中，不要触发值的更新
      if (!this.otherNodeName[type]) {
        return;
      }
    },
    handleOtherInputBlur(type) {
      // 失去焦点时才更新值
      const nodeMap = {
        transformer: {
          prefix: "sjgd-electricity-node-16:",
          nodeValue: "sjgd-electricity-node-16",
        },
        line: {
          prefix: "sjgd-line-node-12:",
          nodeValue: "sjgd-line-node-12",
        },
      };
      const { prefix, nodeValue } = nodeMap[type];

      // 找到并移除旧的值
      const index = this.selectedNodes.findIndex(
        (val) => val === nodeValue || val.startsWith(prefix)
      );

      if (index > -1) {
        const newValue = prefix + this.otherNodeName[type];
        this.selectedNodes.splice(index, 1, newValue);
        this.$emit("change", this.selectedNodes);
      }
    },
    getDisplayLabel(item, type) {
      const nodeMap = {
        transformer: {
          prefix: "sjgd-electricity-node-16:",
          nodeValue: "sjgd-electricity-node-16",
        },
        line: {
          prefix: "sjgd-line-node-12:",
          nodeValue: "sjgd-line-node-12",
        },
      };

      // 如果是其他选项
      if (item.value === nodeMap[type].nodeValue) {
        const customValue = this.selectedNodes.find((val) =>
          val.startsWith(nodeMap[type].prefix)
        );
        if (customValue) {
          const inputValue = customValue.split(":")[1];
          return `其他：${inputValue}`;
        }
        return "其他";
      }

      return item.label;
    },
  },
};
</script>

<style scoped lang="less">
.node-selection {
  width: 100%;
}
</style>
