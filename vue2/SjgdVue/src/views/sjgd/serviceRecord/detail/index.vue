<template>
  <div>
    <el-tabs v-model="activeKey" type="border-card">

      <el-tab-pane
          v-watermark="{label: watermark}"
          v-for="(value, index) in formParams.supervisorApplyList"
          :key="'fwsq' + index"
          label="服务申请详情"
          :name="'fwsq' + index"
          style="height: 500px;"
      >
        <fwsq-detail-form :fwsq-id="value.supervisorApplyId" @form="getFwsqForm($event, index)"/>
      </el-tab-pane>


      <el-tab-pane
        v-watermark="{ label: watermark }"
        label="服务记录详情"
        name="first"
        style="height: 500px"
      >
        <el-form
          ref="formParams"
          label-position="right"
          :model="formParams"
          label-width="100px"
        >
          <el-divider content-position="left">服务记录基本信息</el-divider>
          <el-row>
            <el-col :span="24">
              <el-form-item class="force-width-60" label="项目名称">{{
                formParams.projectName
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="单据编号">{{
                formParams.applyNo
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="设计单位">{{
                formParams.designUnit
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="设计工代人员">{{
                formParams.specialtyPersonName
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="填报日期">{{
                getDate(formParams.applyDate)
              }}</el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item class="force-width-60" label="施工阶段">{{
                formParams.engineeringPhase
              }}</el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item class="force-width-60" label="审批状态">
                <span v-if="formParams.approveState == 0">未提交</span>
                <span v-else-if="formParams.approveState == 1">审批中</span>
                <span v-else-if="formParams.approveState == 2">已审批</span>
                <span v-else-if="formParams.approveState == 3">已驳回</span>
                <span v-else-if="formParams.approveState == 4">流程终止</span>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item class="force-width-60" label="节点">
                <el-row :gutter="20">
                  <el-col
                    :span="11"
                    v-if="
                      formParams.transformerNodes &&
                      formParams.transformerNodes.length
                    "
                  >
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span>变电工程节点</span>
                      </div>
                      <div class="node-container">
                        <span
                          v-for="node in formParams.transformerNodes"
                          :key="node"
                          class="node-item"
                        >
                          {{ node }}
                        </span>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col
                      :span="11"
                      v-if="formParams.lineNodes && formParams.lineNodes.length"
                  >
                    <el-card class="box-card">
                      <div slot="header" class="clearfix">
                        <span>线路工程节点</span>
                      </div>
                      <div class="node-container">
                        <span
                            v-for="node in formParams.lineNodes"
                            :key="node"
                            class="node-item"
                        >
                          {{ node }}
                        </span>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item class="force-width-60" label="现场情况描述">{{
                formParams.fieldCondition
              }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="force-width-60" label="发现问题及解决措施">{{
                formParams.problemsAndMeasures
              }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item class="force-width-60" label="附件">
                <FileUpload
                  v-model="formParams.recordFiles"
                  :data="{ type: 'fwjl', hjID: 1, functionId: 20037 }"
                  :disabled="true"
                  :isShowTip="false"
                  :limit="5"
                  :type="2"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <wtfk-detail-form :wtfk-id="formParams.serviceassociationList[0].problemFeedbackId" @form="getWtfkForm($event, 0)" v-if="wtfkLcListShow ==1"/>
      </el-tab-pane>
<!--      <el-tab-pane-->
<!--          v-watermark="{label: watermark}"-->
<!--          v-for="(value, index) in formParams.serviceassociationList"-->
<!--          :key="'wtfk' + index"-->
<!--          label="问题反馈详情"-->
<!--          :name="'wtfk' + index"-->
<!--          style="height: 500px;"-->
<!--      >-->
<!--        <wtfk-detail-form :wtfk-id="value.problemFeedbackId" @form="getWtfkForm($event, index)"/>-->
<!--      </el-tab-pane>-->


      <el-tab-pane
        v-watermark="{ label: watermark }"
        label="流程日志"
        name="second"
        style="height: 500px; overflow: auto"
      >


        <template v-for="(item, index) in fwsqLcList">
          <el-divider content-position="left">服务申请</el-divider>
          <el-timeline>
            <el-timeline-item v-for="(value) in item"  :key="'lcid' + value.id"  :timestamp="value.startdate"
                              placement="top">
              <el-card>
                <h4>{{ value.lcJdmc }}</h4>
                <p>{{ value.personname }} 提交于 {{ value.startdate }}</p>
                <p>审批意见：{{ value.feed }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </template>

        <el-divider content-position="left">服务记录</el-divider>
        <el-timeline>
          <el-timeline-item v-for="value in lcList"  :key="'lcid' + value.id" :timestamp="value.startdate" placement="top">
            <el-card>
              <h4>{{ value.lcJdmc }}</h4>
              <p>{{ value.personname }} 提交于 {{ value.startdate }}</p>
              <p>审批意见：{{ value.feed }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>


        <template v-for="(item, index) in wtfkLcList">
          <el-divider content-position="left">问题反馈</el-divider>
          <el-timeline>
            <div v-if="wtfkFormList[0]?.isSigner == 0 ">
              <el-card>
                <h4>问题已处理，无需审批</h4>
              </el-card>
            </div>
            <el-timeline-item v-for="(value, index) in item" :key="'lcid' + value.id" :timestamp="value.startdate" placement="top" v-else>
              <el-card>
                <h4>{{ value.lcJdmc }}</h4>
                <p>{{ value.personname }} 提交于 {{ value.startdate }}</p>
                <p>审批意见：{{ value.feed }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </template>

      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import {getDetail, getLcListById, getLcListWtfkById,getLcListFwsqById} from "@/api/sjgd/serviceRecord";
import { getDate } from "@/utils/tool";
import { listDataAll } from "@/api/dict/data";
import wtfkDetailForm from "@/views/sjgd/problemFeedback/detail/detailForm.vue"
import fwsqDetailForm from "@/views/sjgd/fieldService/detail/detailForm.vue"

export default {
  name: "fwjlxq",
  components: { FileUpload, Table, wtfkDetailForm ,fwsqDetailForm},
  computed: {
    watermark() {
      return this.$store.state.user.watermark;
    },
  },
  props: {
    fwjlId: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    fwjlId: {
      async handler(newVal) {
        this.currentfwjlId = newVal;
        // 如果需要在 ID 变化时重新加载数据
        await this.getNodeDict();
        this.getDetailById();
      },
      immediate: true, // 立即执行一次
    },
  },
  data() {
    return {
      // 节点字典
      lineNodeDict: [],
      transformerNodeDict: [],
      // 流程列表
      lcList: [],
      activeKey: "first",
      formParams: {},
      specialtyDialogTableOptions: [
        { label: "专业名称", prop: "specialtyName" },
        { label: "姓名", prop: "userName" },
        { label: "手机号", prop: "telephone" },
      ],
      specialtyDialogTableData: [],
      currentfwjlId: this.fwjlId,
      lineNodes: [],
      transformerNodes: [],
      wtfkFormList: {},
      wtfkLcList: [],
      wtfkLcListShow:0,
      fwsqFormList: {},
      fwsqLcList:[]
    };
  },
  methods: {
    getDate,
    // 获取节点字典数据
    async getNodeDict() {
      try {
        const [lineRes, transformerRes] = await Promise.all([
          listDataAll({ type: "sjgd-line-node" }),
          listDataAll({ type: "sjgd-electricity-node" }),
        ]);
        this.lineNodes = lineRes.result;
        this.transformerNodes = transformerRes.result;
      } catch (error) {
        console.error("获取节点字典数据失败:", error);
      }
    },
    getDetailById() {
      getDetail({ id: this.fwjlId }).then((res) => {
        this.formParams = res.result;
        const lineNodes = [];
        const transformerNodes = [];

        this.formParams.node.split(",").forEach((value) => {
          // 处理线路工程其他节点
          if (value.startsWith("sjgd-line-node-12:")) {
            const label = value.split(":")[1];
            lineNodes.push(`[线路工程]其他：${label}`);
            return;
          }

          // 处理变电工程其他节点
          if (value.startsWith("sjgd-electricity-node-16:")) {
            const label = value.split(":")[1];
            transformerNodes.push(`[变电工程]其他：${label}`);
            return;
          }

          const lineNode = this.lineNodes.find((item) => item.value === value);
          if (lineNode) {
            lineNodes.push(lineNode.label);
          }

          const transformerNode = this.transformerNodes.find(
            (item) => item.value === value
          );
          if (transformerNode) {
            transformerNodes.push(transformerNode.label);
          }
        });

        this.formParams.lineNodes = lineNodes;
        this.formParams.transformerNodes = transformerNodes;
        this.getWtfkLcList(this.formParams.serviceassociationList);
        this.getFwsqLcList(this.formParams.supervisorApplyList);

      });

      //获取流程列表
      getLcListById(this.fwjlId).then((res) => {
        let response = res;
        if (typeof res == "string") {
          response = eval("(" + res + ")");
        }
        this.lcList = response.result;
      });
    },
    getWtfkForm(form, index) {
      this.wtfkFormList[(index + '')] = form;
      this.$forceUpdate();
    },
    async getWtfkLcList(associationList) {
      this.wtfkLcList = [];
      this.wtfkLcListShow = 0;
      for (const item of associationList) {
        try {
          const res = await getLcListWtfkById(item.problemFeedbackId);
          this.wtfkLcList.push(res.result);
          this.wtfkLcListShow = 1;
        } catch (e) {
          this.wtfkLcList.push([]);
          this.wtfkLcListShow = 0;
        }
      }
    },
    getFwsqForm(form, index) {
      this.fwsqFormList[(index + '')] = form;
      this.$forceUpdate();
    },
    async getFwsqLcList(associationList) {
      this.fwsqLcList = [];
      for (const item of associationList) {
        try {
          const res = await getLcListFwsqById(item.supervisorApplyId);
          this.fwsqLcList.push(res.result);
        } catch (e) {
          this.fwsqLcList.push([]);
        }
      }
    }
  },
};
</script>

<style lang="less" scoped>
.text {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.box-card {
  margin-bottom: 10px;
  width: 100%;
  display: block;

  /deep/ .el-card {
    width: 100%;
    display: block;
  }

  /deep/ .el-card__header {
    padding: 10px 15px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
    margin-bottom: 0;
    width: 100%;
    display: block;
    box-sizing: border-box;
  }

  /deep/ .el-card__body {
    padding: 15px;
    width: 100%;
    display: block;
    box-sizing: border-box;
  }

  .clearfix {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 0;
    width: 100%;
    box-sizing: border-box;

    span {
      font-weight: bold;
      font-size: 14px;
      color: #303133;
    }
  }
}

.node-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 5px 0;
}

.node-item {
  background-color: #f5f7fa;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
  border: 1px solid #e4e7ed;
}
</style>
