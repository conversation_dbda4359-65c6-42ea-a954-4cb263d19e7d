<template>
  <el-form
      ref="formParams"
      label-position="right"
      :model="formParams"
      label-width="100px"
  >
    <el-divider content-position="left">服务记录基本信息</el-divider>
    <el-row>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="项目名称">{{
            formParams.projectName
          }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="单据编号">{{
            formParams.applyNo
          }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="设计单位">{{
            formParams.designUnit
          }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="设计工代人员">{{
            formParams.specialtyPersonName
          }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="填报日期">{{
            getDate(formParams.applyDate)
          }}</el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item class="force-width-60" label="施工阶段">{{
            formParams.engineeringPhase
          }}</el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item class="force-width-60" label="审批状态">
          <span v-if="formParams.approveState == 0">未提交</span>
          <span v-else-if="formParams.approveState == 1">审批中</span>
          <span v-else-if="formParams.approveState == 2">已审批</span>
          <span v-else-if="formParams.approveState == 3">已驳回</span>
          <span v-else-if="formParams.approveState == 4">流程终止</span>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item class="force-width-60" label="节点">
          <el-row :gutter="20">
            <el-col
                :span="11"
                v-if="
                      formParams.transformerNodes &&
                      formParams.transformerNodes.length
                    "
            >
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>变电工程节点</span>
                </div>
                <div class="node-container">
                        <span
                            v-for="node in formParams.transformerNodes"
                            :key="node"
                            class="node-item"
                        >
                          {{ node }}
                        </span>
                </div>
              </el-card>
            </el-col>
            <el-col :span="11" v-if="formParams.lineNodes && formParams.lineNodes.length">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>线路工程节点</span>
                </div>
                <div class="node-container">
                        <span
                            v-for="node in formParams.lineNodes"
                            :key="node"
                            class="node-item"
                        >
                          {{ node }}
                        </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item class="force-width-60" label="现场情况描述">{{
            formParams.fieldCondition
          }}</el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="发现问题及解决措施">{{
            formParams.problemsAndMeasures
          }}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="附件">
          <FileUpload
              v-model="formParams.recordFiles"
              :data="{ type: 'fwjl', hjID: 1, functionId: 20037 }"
              :disabled="true"
              :isShowTip="false"
              :limit="5"
              :type="2"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

import FileUpload from "components/DFDW/FileUpload.vue";
import {getDate} from "@/utils/tool";
import {listDataAll} from "api/dict/data";
import {getDetail} from "api/sjgd/serviceRecord";

export default {
  name: "fwjlxq-form",
  components: {FileUpload},
  props: {
    fwjlId: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    fwjlId: {
      async handler(newVal) {
        this.currentfwjlId = newVal;
        // 如果需要在 ID 变化时重新加载数据
        await this.getNodeDict();
        this.getDetailById();
      },
      immediate: true, // 立即执行一次
    },
  },
  data() {
    return {
      // 节点字典
      lineNodeDict: [],
      transformerNodeDict: [],
      formParams: {},
      specialtyDialogTableOptions: [
        { label: "专业名称", prop: "specialtyName" },
        { label: "姓名", prop: "userName" },
        { label: "手机号", prop: "telephone" },
      ],
      specialtyDialogTableData: [],
      currentfwjlId: this.fwjlId,
      lineNodes: [],
      transformerNodes: [],
    };
  },
  methods: {
    getDate,
    // 获取节点字典数据
    async getNodeDict() {
      try {
        const [lineRes, transformerRes] = await Promise.all([
          listDataAll({ type: "sjgd-line-node" }),
          listDataAll({ type: "sjgd-electricity-node" }),
        ]);
        this.lineNodes = lineRes.result;
        this.transformerNodes = transformerRes.result;
      } catch (error) {
        console.error("获取节点字典数据失败:", error);
      }
    },
    getDetailById() {
      getDetail({ id: this.fwjlId }).then((res) => {
        this.formParams = res.result;
        this.$emit('form', this.formParams);
        const lineNodes = [];
        const transformerNodes = [];

        this.formParams.node.split(",").forEach((value) => {
          // 处理线路工程其他节点
          if (value.startsWith("sjgd-line-node-12:")) {
            const label = value.split(":")[1];
            lineNodes.push(`[线路工程]其他：${label}`);
            return;
          }

          // 处理变电工程其他节点
          if (value.startsWith("sjgd-electricity-node-16:")) {
            const label = value.split(":")[1];
            transformerNodes.push(`[变电工程]其他：${label}`);
            return;
          }

          const lineNode = this.lineNodes.find((item) => item.value === value);
          if (lineNode) {
            lineNodes.push(lineNode.label);
          }

          const transformerNode = this.transformerNodes.find(
              (item) => item.value === value
          );
          if (transformerNode) {
            transformerNodes.push(transformerNode.label);
          }
        });

        this.formParams.lineNodes = lineNodes;
        this.formParams.transformerNodes = transformerNodes;
      });
    }
  }
}
</script>

<style scoped lang="less">

</style>
