<template>
  <el-form
      ref="formParams"
      :model="formParams"
      label-width="100px"
  >
    <el-divider content-position="left">服务申请基本信息</el-divider>
    <el-row>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="项目名称">{{ formParams.projectName }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="单据编号">{{ formParams.applyNo }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="设计单位">{{ formParams.designUnit }}</el-form-item>
      </el-col>
      <!-- <el-col :span="8">
        <el-form-item class="force-width-60" label="设计工代专业">{{ formParams.specialty }}</el-form-item>
      </el-col> -->
      <el-col :span="8">
        <el-form-item class="force-width-60" label="申请到位时间">{{
            getDate(formParams.applyArriveDate)
          }}
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="发起人">{{ formParams.applyUserName }}</el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item class="force-width-60" label="答复到位时间">{{ formParams.replyArriveDays }}</el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="签收人">{{ formParams.signerUserName }}</el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item class="force-width-60" label="审批状态">
          <span v-if="formParams.approveState == 0">未提交</span>
          <span v-else-if="formParams.approveState == 1">审批中</span>
          <span v-else-if="formParams.approveState == 2">已审批</span>
          <span v-else-if="formParams.approveState == 3">已驳回</span>
          <span v-else-if="formParams.approveState == 4">流程终止</span>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="force-width-60" label="内容">{{ formParams.content }}</el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="联系单附件">
          <FileUpload
              v-model="formParams.contactFile"
              :data="{type: 'jlsq', hjID: 2,functionId: 20033}"
              :disabled="true"
              :isShowTip="false"
              :limit="5"
              :type="2"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item class="force-width-60" label="现场问题附件">

          <FileUpload
              v-model="formParams.siteProblemFile"
              :data="{type: 'jlsq', hjID: 1,functionId: 20033}"
              :disabled="true"
              :isShowTip="false"
              :limit="5"
              :type="1"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-divider content-position="left">设计工代专业人员信息</el-divider>
    <Table
        height="300"
        ref="table"
        :needSelect="false"
        :tableData="specialtyDialogTableData"
        :tableOptions="specialtyDialogTableOptions"
    >
    </Table>
  </el-form>
</template>

<script>

import Table from "components/MainTable/index.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import {getDetail, getPersonListBySupervisorApplyId, getLcListById} from "@/api/sjgd/fieldService";
import {getDate} from "@/utils/tool";

export default {
  name: "wfsqxq-form",
  components: {FileUpload, Table},
  props: {
    fwsqId: {
      type: Number,
      default: 0
    }
  },
  watch: {
    fwsqId: {
      handler(newVal) {
        this.currentFwsqId = newVal
        // 如果需要在 ID 变化时重新加载数据
        this.getDetailById()
      },
      immediate: true  // 立即执行一次
    }
  },
  data() {
    return {
      // 流程列表
      formParams: {},
      currentFwsqId: this.fwsqId,
      // 流程列表
      lcList: [],
      activeKey: 'first',
      specialtyDialogTableOptions: [
        {label: '专业名称', prop: 'specialtyName'},
        {label: '姓名', prop: 'userName'},
        {label: '手机号', prop: 'telephone'},
      ],
      specialtyDialogTableData: [],
    }
  },
  methods: {
    getDate,
    getDetailById() {
      //获取详情
      getDetail(this.fwsqId).then(res => {
        this.formParams = res.result
        this.formParams.specialty = JSON.parse(this.formParams.specialty)
      })
      //获取专业和人员信息
      getPersonListBySupervisorApplyId({supervisorApplyId: this.fwsqId}).then(res => {
        this.specialtyDialogTableData = res.result
      })
      //获取流程列表
      getLcListById(this.fwsqId).then(res => {
        let response = res
        if (typeof res == 'string') {
          response = eval("(" + res + ")")
        }
        this.lcList = response.result
      })
    }
  },
}
</script>

<style scoped lang="less">

</style>
