<template>
  <div>
    <el-tabs v-model="activeKey" type="border-card">
      <el-tab-pane v-watermark="{label: watermark}" label="服务申请详情" name="first" style="height: 500px;">
        <el-form ref="formParams" :model="formParams" label-width="100px">
          <el-divider content-position="left">服务申请基本信息</el-divider>
          <el-row>
            <el-col :span="24">
              <el-form-item class="force-width-60" label="项目名称">{{ formParams.projectName }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="单据编号">{{ formParams.applyNo }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="设计单位">{{ formParams.designUnit }}</el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item class="force-width-60" label="设计工代专业">{{ formParams.specialty }}</el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item class="force-width-60" label="申请到位时间">{{
                  getDate(formParams.applyArriveDate)
                }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="发起人">{{ formParams.applyUserName }}</el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item class="force-width-60" label="答复到位时间">{{ formParams.replyArriveDays }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="签收人">{{ formParams.signerUserName }}</el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item class="force-width-60" label="审批状态">
                <span v-if="formParams.approveState == 0">未提交</span>
                <span v-else-if="formParams.approveState == 1">审批中</span>
                <span v-else-if="formParams.approveState == 2">已审批</span>
                <span v-else-if="formParams.approveState == 3">已驳回</span>
                <span v-else-if="formParams.approveState == 4">流程终止</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="force-width-60" label="内容">{{ formParams.content }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="联系单附件">
                <FileUpload
                    v-model="formParams.contactFile"
                    :data="{type: 'jlsq', hjID: 2,functionId: 20033}"
                    :disabled="true"
                    :isShowTip="false"
                    :limit="5"
                    :type="2"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="force-width-60" label="现场问题附件">

                <FileUpload
                    v-model="formParams.siteProblemFile"
                    :data="{type: 'jlsq', hjID: 1,functionId: 20033}"
                    :disabled="true"
                    :isShowTip="false"
                    :limit="5"
                    :type="1"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider content-position="left">设计工代专业人员信息</el-divider>
          <Table
              height="300"
              ref="table"
              :needSelect="false"
              :tableData="specialtyDialogTableData"
              :tableOptions="specialtyDialogTableOptions"
          >
          </Table>
        </el-form>
      </el-tab-pane>
<!--      :label="fwjlFormList[(index + '')] ? fwjlFormList[(index + '')].applyNo : '服务记录详情(' + (index + 1) +')'"    -->
      <el-tab-pane
          v-watermark="{label: watermark}"
          v-for="(value, index) in formParams.serviceassociationList"
          :key="'fwjl' + index"
          :label="'服务记录详情(' + (index + 1) +')'"
          :name="'fwjl' + index"
          style="height: 500px;"
      >
        <fwjlDetailForm :fwjl-id="value.serviceRecordId" @form="getFwjlForm($event, index)"/>
        <wtfk-detail-form :wtfk-id="value.problemFeedbackId" @form="getWtfkForm($event, index)" v-if="value.problemFeedbackId"/>
      </el-tab-pane>
      <el-tab-pane v-watermark="{label: watermark}" label="流程" name="second" style="height: 500px;overflow: auto">
        <el-divider content-position="left">服务申请 {{formParams.applyNo}}</el-divider>
        <el-timeline>
          <el-timeline-item v-for="(value) in lcList" :key="'lcid' + value.id"  :timestamp="value.startdate"
                            placement="top">
            <el-card>
              <h4>{{ value.lcJdmc }}</h4>
              <p>{{ value.personname }} 提交于 {{ value.startdate }}</p>
              <p>审批意见：{{ value.feed }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        <template v-for="(item, index) in fwjlLcList">
          <el-divider content-position="left">服务记录 {{ fwjlFormList[(index + '')]?.applyNo }}</el-divider>
          <el-timeline>
            <el-timeline-item v-for="(value) in item" :key="'lcid' + value.id"  :timestamp="value.startdate"
                              placement="top">
              <el-card>
                <h4>{{ value.lcJdmc }}</h4>
                <p>{{ value.personname }} 提交于 {{ value.startdate }}</p>
                <p>审批意见：{{ value.feed }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </template>

        <template v-for="(item, index) in wtfkLcList">
          <el-divider content-position="left">服务反馈 {{ wtfkFormList[(index + '')]?.applyNo }}</el-divider>
          <el-timeline >
            <div v-if="wtfkFormList[(index + '')]?.isSigner == 0 ">
              <el-card>
                <h4>问题已处理，无需审批</h4>
              </el-card>
            </div>
            <el-timeline-item v-for="(value) in item" :key="'lcid' + value.id"  :timestamp="value.startdate"
                              placement="top" v-else>
              <el-card>
                <h4>{{ value.lcJdmc }}</h4>
                <p>{{ value.personname }} 提交于 {{ value.startdate }}</p>
                <p>审批意见：{{ value.feed }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import {getDetail, getPersonListBySupervisorApplyId, getLcListById, getLcListFwjlById} from "@/api/sjgd/fieldService";
import {getDate} from "@/utils/tool";
import fwjlDetailForm from "@/views/sjgd/serviceRecord/detail/detailForm.vue"
import wtfkDetailForm from "@/views/sjgd/problemFeedback/detail/detailForm.vue"

export default {
  name: 'jlsqxq',
  components: {FileUpload, Table, fwjlDetailForm,wtfkDetailForm},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  props: {
    jlsqId: {
      type: Number,
      default: 0
    }
  },
  watch: {
    jlsqId: {
      handler(newVal) {
        this.currentJlsqId = newVal
        // 如果需要在 ID 变化时重新加载数据
        this.getDetailById()
      },
      immediate: true  // 立即执行一次
    }
  },
  data() {
    return {
      // 流程列表
      lcList: [],
      activeKey: 'first',
      formParams: {},
      specialtyDialogTableOptions: [
        {label: '专业名称', prop: 'specialtyName'},
        {label: '姓名', prop: 'userName'},
        {label: '手机号', prop: 'telephone'},
      ],
      specialtyDialogTableData: [],
      currentJlsqId: this.jlsqId,
      fwjlFormList: {},
      fwjlLcList: [],
      wtfkFormList: {},
      wtfkLcList: [],
    }
  },
  methods: {
    getDate,
    getDetailById() {
      //获取详情
      getDetail(this.jlsqId).then(res => {
        this.formParams = res.result
        this.formParams.specialty = JSON.parse(this.formParams.specialty)
        this.getFwjlLcList(this.formParams.serviceassociationList);
        this.getWtfkLcList(this.formParams.serviceassociationList);
      })
      //获取专业和人员信息
      getPersonListBySupervisorApplyId({supervisorApplyId: this.jlsqId}).then(res => {
        this.specialtyDialogTableData = res.result
      })
      //获取流程列表
      getLcListById(this.jlsqId).then(res => {
        let response = res
        if (typeof res == 'string') {
          response = eval("(" + res + ")")
        }
        this.lcList = response.result
      })
    },
    getFwjlForm(form, index) {
      this.fwjlFormList[(index + '')] = form;
      this.$forceUpdate();
    },
    async getFwjlLcList(associationList) {
      this.fwjlLcList = [];
      for (const item of associationList) {
        try {
          const res = await getLcListFwjlById(item.serviceRecordId);
          this.fwjlLcList.push(res.result);
        } catch (e) {
          this.fwjlLcList.push([]);
        }
      }
    },
    getWtfkForm(form, index) {
      this.wtfkFormList[(index + '')] = form;
      this.$forceUpdate();
    },
    async getWtfkLcList(associationList) {
      this.wtfkLcList = [];
      for (const item of associationList) {
        try {
          if(item.problemFeedbackId){
            const res = await getLcListWtfkById(item.problemFeedbackId);
            this.wtfkLcList.push(res.result);
          }
        } catch (e) {
          this.wtfkLcList.push([]);
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.text {
  font-size: 14px;
}

.box-card {
  width: 480px;
}
</style>
