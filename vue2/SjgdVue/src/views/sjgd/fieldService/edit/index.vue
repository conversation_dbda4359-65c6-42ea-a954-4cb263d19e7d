<template>
  <el-dialog
      :title="dialogTitle"
      :visible.sync="dialog"
      width="55%"
      :close-on-click-modal="false"
  >
    <el-row style="margin-bottom: 20px" v-if="dialogType === 'edit_fwjl'" v-has-permi="['JDWSJ01FS01QX08']">
      <el-button size="small" type="primary" @click="handleTabsEdit(null, 'add')">
        <i class="el-icon-plus"></i>服务记录
      </el-button>
    </el-row>
    <el-row>
      <el-tabs v-model="editableTabsValue" type="card" @edit="handleTabsEdit">
        <el-tab-pane
            :key="item.name"
            v-for="(item, index) in editableTabs"
            :name="item.name"
            :closable="item.closable"
        >
          <span slot="label">
            {{item.title}}
            <i class="el-icon-success" style="color: #13ce66" v-if="item.status === 'success'"></i>
            <i class="el-icon-error" style="color: #bd2c00" v-if="item.status === 'error'"></i>
          </span>
          <el-alert
              v-if="item.errorInfo"
              :title="item.errorInfo"
              type="error"
              show-icon
              style="margin-bottom: 20px"
          />
          <edit-form :ref="'form' + index" v-if="index === 0" v-model="form" :dialog-type="dialogType"/>
          <fwjl-edit-form :ref="'form' + index" v-else v-model="item.form" dialog-type="fwsq"/>
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <div align="right">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="dialog = false">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>

import editForm from "./editForm.vue";
import fwjlEditForm from "@/views/sjgd/serviceRecord/edit/editForm.vue";
import {addOrEdit, addOrEditFwjl, getDetailFwjl, serviceAssociation} from "api/sjgd/fieldService";
import {getDictDatas} from "@/utils/dict";

export default {
  name: "fwsq-edit",
  components: {editForm, fwjlEditForm},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      required: true,
    },
    dialogType: {
      type: String,
      required: true,
    },
    form: {
      type: Object,
      required: true
    },
    componentKey: {
      type: Number,
      required: true
    }
  },
  watch: {
    value: {
      handler(val) {
        this.dialog = val
        if (val) {
          this.editableTabsValue = '1';
          this.editableTabs = [{
            title: '服务申请',
            name: '1',
            closable: false,
            status: null,
            errorInfo: null
          }];
          this.tabIndex = 1;
        }
      },
      immediate: true,
      deep: true
    },
    dialog: {
      handler(val) {
        this.$emit("input", val);
      },
      immediate: true,
      deep: true
    },
    "form.serviceassociationList": {
      async handler(val) {
        if (Array.isArray(val)) {
          this.tabIndex = val.length + 1;
          for (const item of val) {
            let index = val.indexOf(item);
            const name = (index + 2) + '';
            try {
              const res = await getDetailFwjl({id: item.serviceRecordId})
              let form = res.result;
              if (form.node) {
                form.nodes = form.node.split(",");
              }
              this.editableTabs.push({
                title: form.applyNo,
                name: name,
                closable: false,
                status: null,
                errorInfo: null,
                form: form
              })
            } catch (e) {
              this.editableTabs.push({
                title: '服务记录' + index,
                name: name,
                closable: false,
                status: null,
                errorInfo: e
              })
              this.$message.error(e);
            }
          }
        }
      },
      immediate: true,
      deep: true
    },
    "form.designProjectId": {
      handler(val) {
        this.editableTabs.forEach(item => {
          if (item.form) {
            item.form.designProjectId = val;
            item.form.projectName = this.form.projectName;
          }
        })
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      dialog: false,
      editableTabsValue: '1',
      editableTabs: [{
        title: '服务申请',
        name: '1',
        closable: false
      }],
      tabIndex: 1,
      userid: JSON.parse(JSON.stringify(this.$store.state.user.userid)),
      name: JSON.parse(JSON.stringify(this.$store.state.user.name)),
    }
  },
  methods: {
    /** 提交表单 */
    submitForm() {
      let refs = [];
      for (let i = 0; i < this.editableTabs.length; i++) {
        const refName = "form" + i;
        refs.push(this.$refs[refName][0].validate())
      }
      Promise.all(refs).then(async res => {
        if (res.every(item => item)) {
          let isOk = true;
          let errorIndex = -1;
          let applyId = this.form.id;
          let serviceAssociationList = []
          for (const item of this.editableTabs) {
            const index = this.editableTabs.indexOf(item);
            let res
            try {
              if (this.dialogType === 'add' || this.dialogType === 'edit') {
                res = await addOrEdit(this.form);
                this.form.id = res.result;
                applyId = res.result;
              } else if (index > 0) {
                // 防止审批中/已审批/流程终止记录再次提交
                if (item.form.approveState !== 1 && item.form.approveState !== 2 && item.form.approveState !== 4) {
                  res = await addOrEditFwjl(item.form);
                  item.form.id = res.result;
                  serviceAssociationList.push({
                    supervisorApplyId: applyId,
                    serviceRecordId: res.result
                  })
                } else {
                  serviceAssociationList.push({
                    supervisorApplyId: applyId,
                    serviceRecordId: item.form.id
                  })
                }
              }
              item.status = 'success';
              item.errorInfo = null;
            } catch (e) {
              isOk = false;
              item.status = 'error';
              item.errorInfo = e;
              if (errorIndex === -1) {
                errorIndex = index + 1;
              }
            }
          }
          if (serviceAssociationList.length > 0) {
            try {
              await serviceAssociation({
                id: applyId,
                serviceassociationList: serviceAssociationList
              })
            } catch (e) {
              isOk = false;
              this.$message.success(e);
            }
          }
          if (isOk) {
            this.$message.success("提交成功");
            this.dialog = false;
            this.$emit("submitForm");
          } else {
            this.editableTabsValue = errorIndex + '';
          }
        }
      })
    },
    handleTabsEdit(targetName, action) {
      if (action === 'add') {
        let newTabTitle = '服务记录 ' + this.tabIndex;
        let newTabName = ++this.tabIndex + '';
        let unitName = getDictDatas('sjgd-sj-unit')[0].label
        this.editableTabs.push({
          title: newTabTitle,
          name: newTabName,
          closable: true,
          form: {
            designProjectId: this.form.designProjectId,
            projectName: this.form.projectName,
            designUnit: unitName,
            applyDate: new Date(),
            specialtyPersonId: this.userid,
            specialtyPersonName: this.name,
            nodes: [],
            engineeringPhase: null,
            recordFiles: null,
            fieldCondition: null,
            problemsAndMeasures: null,
            approveState: 0,
          }
        });
        this.editableTabsValue = newTabName;
      }
      if (action === 'remove') {
        let tabs = this.editableTabs;
        let activeName = this.editableTabsValue;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.name;
              }
            }
          });
        }

        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter(tab => tab.name !== targetName);
      }
    }
  }
}
</script>

<style scoped lang="less">

</style>
