<template>
  <el-form
      :model="form"
      :rules="initRules"
      ref="form"
      label-width="120px"
      v-watermark="{ label: watermark }"
  >
    <el-row>
      <el-col :span="12">
        <el-form-item label="项目名称" prop="designProjectId">
          <multiple-column-selection
              ref="projectSelect"
              v-model="form.designProjectId"
              valueKey="id"
              label="projectName"
              placeholder="请选择项目名称"
              :filterable="true"
              :tableWidth="600"
              :options="projectList"
              :tableOptions="multipleColumnTableOptions"
              @change="handleChangeProject"
              style="width: 100%"
              :readonly="readonly"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设计单位" prop="designUnit">
          <el-input
              v-model="form.designUnit"
              placeholder="请输入设计单位"
              readonly
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="设计工代专业" prop="specialty">
          <el-select
              style="width: 100%"
              v-model="form.specialty"
              multiple
              placeholder="请选择设计工代专业"
              clearable
              :disabled="readonly"
          >
            <el-option
                v-for="item in specialtyDict"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="申请到位时间 " prop="applyArriveDate">
          <el-date-picker
              style="width: 100%"
              v-model="form.applyArriveDate"
              type="datetime"
              placeholder="请选择申请到位时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :readonly="readonly"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="发起人" prop="applyUserId">
          <multiple-column-selection
              ref="applyUserSelect"
              v-model="form.applyUserId"
              valueKey="srcUserId"
              label="userName"
              placeholder="请选择发起人"
              :filterable="true"
              :tableWidth="600"
              :options="personList"
              :tableOptions="multipleColumnTableOptions2"
              @change="handleChangePerson"
              :disabled="readonly"
              style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="现场问题情况附件" prop="siteProblemFile">
          <FileUpload
              :limit="5"
              :disabled="form.applyUserId !== $store.state.user.userid || readonly"
              :type="1"
              v-model="form.siteProblemFile"
              :uploadUrl="`/Sjgd/file/upload`"
              :file-size="fileLimit.imgSize"
              :file-type="fileLimit.imgType"
              @uploadSuccessData="uploadSuccessData"
              @deleteData="deleteData"
              :data="{ type: 'jlsq', hjID: 1, functionId: 20033 }"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24" v-if="form.approveState === 2">
        <el-form-item label="联系单附件" prop="contactFile">
          <FileUpload
              :limit="5"
              :type="2"
              v-model="form.contactFile"
              :uploadUrl="`/Sjgd/file/upload`"
              :file-size="fileLimit.imgSize"
              :file-type="fileLimit.imgType"
              @uploadSuccessData="uploadSuccessData"
              @deleteData="deleteData"
              :data="{ type: 'jlsq', hjID: 2, functionId: 20033 }"
              :disabled="readonly"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <el-form-item label="内容" prop="content">
          <el-input
              v-model="form.content"
              placeholder="请输入内容"
              type="textarea"
              :readonly="readonly"
          ></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";
import FileUpload from "components/DFDW/FileUpload.vue";
import {getPersonList, getProjectList, getSpecialtyDict,getPersonListByProjectId,getFilterSpecialtyDict} from "api/sjgd/fieldService";
import {getDictDatas} from "@/utils/dict";

export default {
  name: "fwsq-edit-form",
  components: {FileUpload, MultipleColumnSelection},
  created() {
    this.getProjectList();
    this.getSpecialtyDict();
    //this.getPersonList();
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark;
    },
    readonly() {
      return this.dialogType === "edit_fwjl" || this.form.approveState === 1 || this.form.approveState === 2 || this.form.approveState === 4;
    }
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          id: null,
          designProjectId: null,
          projectName: "",
          designUnit: getDictDatas('sjgd-sj-unit')[0].label,
          specialty: [],
          applyArriveDate: null,
          applyUserId: this.$store.state.user.userid,
          applyUserName: this.$store.state.user.name,
          siteProblemFile: null,
          contactFile: null,
          content: null
        }
      }
    },
    dialogType: {
      type: String,
      required: true,
    }
  },
  watch: {
    value: {
      handler(val) {
        this.form = val;
      },
      deep: true,
      immediate: true,
    },
    //添加 项目id监听，同步刷新 发起人下拉选项
    'form.designProjectId':{
      handler(val){
        //console.log("form.designProjectId-----watch",val)
        if (val){
          this.getPersonList(val)
        }else {
          //清空，防止上一个项目的数据串入
          this.personList = []
        }
      }
    },
    form: {
      handler(val) {
        this.$emit("input", val);
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      form: {
        id: null,
        designProjectId: null,
        designUnit: null,
        specialty: [],
        applyArriveDate: null,
        applyUserId: null,
        siteProblemFile: null,
        contactFile: null,
        content: null
      },
      fileLimit: {
        imgSize: 20,
        imgType: ["jpg", "png", "pdf"],
      },
      initRules: {
        designProjectId: [{required: true, message: "请选择项目", trigger: "blur"}],
        applyUserId: [{required: true, message: "请选择发起人", trigger: "blur"}],
        specialty: [{required: true, message: "请选择专业", trigger: "blur"}],
        content: [{required: true, message: "请输入内容", trigger: "blur"}],
        applyArriveDate: [{required: true, message: "请选择申请到位时间", trigger: "blur"}],
      },
      specialtyDictCont:[],
      specialtyDict: [],
      projectList: [],
      personList: [],
      multipleColumnTableOptions: [
        {label: "项目名称", prop: "projectName", filter: true},
        {label: "项目总监名称", prop: "projectDirectorUserName", filter: true,},
        {label: "项目专监名称", prop: "projectSpecialOverseeUserName", filter: true,},
        {label: "设总人员名称", prop: "designDirectorUserName", filter: true,},
      ],
      multipleColumnTableOptions2: [
        {label: "姓名", prop: "userName", filter: true},
        {label: "专业", prop: "specialtyName", filter: true},
        {label: "手机号", prop: "telephone", filter: true},
      ],
    }
  },
  methods: {
    //获取项目列表
    getProjectList() {
      getProjectList({designUserId: this.$store.state.user.userid, pageNum: 1, pageSize: -1}).then((res) => {
        this.projectList = res.result.records;
      });
    },
    handleChangeProject(value) {
      this.form.projectName = this.projectList.find(
          (item) => item.id === value
      ).projectName;

      //选完项目重置发起人为当前登录用户
      this.form.applyUserId = this.$store.state.user.userid
      this.form.applyUserName = this.$store.state.user.name
      this.getFilterSpecialtyDict();
      //this.getPersonList(value)
    },
    /** 获取专业字典 */
    getSpecialtyDict() {
      getSpecialtyDict("sjgd-specialty").then((res) => {
        this.specialtyDictCont = res.result;
      });
    },
    getFilterSpecialtyDict(){
      getFilterSpecialtyDict(this.form.designProjectId).then(res=>{
        let fromSpecialtyPersonList = res.result;
        this.specialtyDict = this.specialtyDictCont.filter(item => {
          return fromSpecialtyPersonList.some(specialty => specialty === item.value);
        });
      })
    },
    getPersonList(projectId) {
      this.personList = []
      getPersonListByProjectId(projectId).then((res)=>{
        this.personList = res.result;
      })

      // getPersonList({state: 1, pageNum: 1, pageSize: 100}).then((res) => {
      //   this.personList = res.result;
      // });
      this.$forceUpdate();
    },
    handleChangePerson(value) {
      this.form.applyUserName = this.personList.find(
          (item) => item.srcUserId === value
      ).userName;
    },
    uploadSuccessData() {
      this.$message({
        type: "success",
        message: "上传成功",
      });
    },
    deleteData() {
    },
    // 表单验证
    validate(callback) {
      // 防御性校验：确保表单引用存在
      if (!this.$refs.form)  {
        console.error('Form ref not found');
        return typeof callback === 'function' ? callback(false) : Promise.resolve(false);
      }

      // 统一返回 Promise 以实现链式调用
      return new Promise((resolve) => {
        this.$refs.form.validate((valid)  => {
          // 处理回调函数
          if (typeof callback === 'function') {
            callback(valid);
          }
          // 无论是否有回调，都通过 Promise 返回结果
          resolve(valid);
        }).catch((error) => {
          console.error('Validation error:', error);
          resolve(false); // 验证失败时统一返回 false
        });
      });
    }
  }
}
</script>

<style scoped lang="less">
.readonly {
  pointer-events: none;
}
</style>