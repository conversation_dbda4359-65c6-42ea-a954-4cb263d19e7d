<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">项目名称</span>
        <el-input
          v-model="queryParams.projectName"
          clearable
          placeholder="请输入项目名称"
          @change="handleChange"
        ></el-input>
        <span class="font-size14">单据编号</span>
        <el-input
          v-model="queryParams.applyNo"
          clearable
          placeholder="请输入单据编号"
          @change="handleChange"
        ></el-input>
        <span class="font-size14">审批状态</span>
        <el-select
          v-model="queryParams.approveState"
          clearable
          placeholder="请选择审批状态"
          @change="handleChange"
        >
          <el-option
            v-for="item in approveStates"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>

        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-search"
          @click="getList"
          >查询</el-button
        >

        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-refresh"
          @click="resetQuery"
          >重置</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-plus"
          @click="openDialog('add')"
          v-has-permi="['JDWSJ01FS01QX02']"
          >新增</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-edit"
          @click="openDialog('edit')"
          v-has-permi="['JDWSJ01FS01QX03']"
          >编辑</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-delete"
          @click="remove"
          v-has-permi="['JDWSJ01FS01QX04']"
          >删除</el-button
        >

        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-download"
          @click="downloadPdf"
          v-has-permi="['JDWSJ01FS01QX05']"
          >导出PDF</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-download"
          @click="downloadExcel"
          v-has-permi="['JDWSJ01FS01QX07']"
          >导出Excel</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-view"
          @click="openInfo"
          v-has-permi="['JDWSJ01FS01QX01']"
          >查看详情</el-button
        >
        <el-button
          class="rygf"
          size="mini"
          type="text"
          icon="el-icon-s-check"
          @click="openDialog('flow')"
          v-has-permi="['JDWSJ01FS01QX06']"
          >提交审批</el-button
        >
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit_fwjl')"
            v-has-permi="['JDWSJ01FS01QX08']"
          >编辑记录</el-button
        >
        <el-button
            class="rygf"
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="openDialog('link_fwjl')"
            v-has-permi="['JDWSJ01FS01QX08']"
          >关联记录</el-button
        >

        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr" />
      </div>
    </div>
    <div class="table-box">
      <Table
        ref="table"
        :needSelect="true"
        :tableData="list"
        :tableOptions="realTableOptions"
        :loading="loading"
        :queryParam="queryParams"
        @getCurrentData="select"
        @rowdblclick="openDialog('edit')"
        @getSelectionData="selectionData"
      >
        <template slot="specialty" slot-scope="scope">
          <el-button
            type="text"
            @click="handleSpecialty(scope.row.specialty, 'specialty')"
            >查看</el-button
          >
        </template>
        <template slot="specialtyAndPeople" slot-scope="scope">
          <el-button
            type="text"
            @click="handleSpecialty(scope.row.id, 'specialtyAndPeople')"
            >查看</el-button
          >
        </template>

        <template slot="siteProblemFile" slot-scope="scope">
          <el-button
            type="text"
            @click="handlePreviewImages(scope.row, 'siteProblemFile')"
            >查看</el-button
          >
        </template>
        <template slot="contactFile" slot-scope="scope">
          <el-button
            type="text"
            @click="handlePreviewImages(scope.row, 'contactFile')"
            >查看</el-button
          >
        </template>
        <!-- 申请到位时间 -->
        <template slot="applyArriveDate" slot-scope="scope">
          <div>
            {{ getDate(scope.row.applyArriveDate) }}
          </div>
        </template>

        <template slot="approveState" slot-scope="scope">
          <div>
            {{
              scope.row.approveState == 0
                ? "未提交"
                : scope.row.approveState == 1
                ? "审核中"
                : scope.row.approveState == 2
                ? "已审核"
                : scope.row.approveState == 3
                ? "已驳回"
                : scope.row.approveState == 4
                ? "流程终止"
                : ""
            }}
          </div>
        </template>
      </Table>
      <Pagination
        @handleRefresh="handleCurrentChange"
        :queryParam="queryParams"
        layout="total, sizes, prev, pager, next, jumper"
        :total="queryParams.total"
      />
    </div>

    <edit
        v-model="dialog"
        :dialog-title="dialogTitle"
        :dialog-type="dialogType"
        :form="form"
        :component-key="componentKey"
        @submitForm="getList"
    />

    <!-- 查看专业和人员信息 -->
    <el-dialog
      :title="specialtyDialogTitle"
      :visible.sync="specialtyDialog"
      width="30%"
    >
      <Table
        height="300"
        ref="table"
        :needSelect="false"
        :tableData="specialtyDialogTableData"
        :tableOptions="specialtyDialogTableOptions"
        :loading="loading"
        :queryParam="queryParams"
      >
      </Table>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="previewImagesDialog"
      :title="previewImagesDialogTitle"
      width="40%"
    >
      <div class="image-preview">
        <FileUpload
          :disabled="true"
          :isShowTip="false"
          v-model="previewImagesDialogData"
        />
      </div>
    </el-dialog>

    <!-- 审批 -->
    <FLow
      ref="flow"
      v-if="flowDialog"
      :yw-id="dataParams.id"
      :lc-jd-id="200331"
      :lc-define-id="20033"
      :first-lc-jd="200331"
      :init-choose="1"
      :termination-disable="true"
      @close="appClose"
      @agree="appAgree"
      @reject="appReject"
    >
    </FLow>

    <!-- 详情 -->
    <el-dialog :visible.sync="detailDialog" title="详情" width="80%">
      <jlsqxq :jlsqId="selectID"></jlsqxq>
    </el-dialog>

    <linkFwjl ref="linkFwjl" v-if="dataParams.designProjectId" :fwsqParams="dataParams" @submit="getList"/>
  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {
  getPageList,
  deleteById,
  getSpecialtyDict,
  getDetail,
  getPersonListBySupervisorApplyId,
  downloadPdf,
  downloadExcel,
} from "@/api/sjgd/fieldService";
import FileUpload from "components/DFDW/FileUpload.vue";
import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";
import { submitLc, rollBackForApply } from "@/api/sjgd/approve";
import FLow from "@/components/FLow/index.vue";
import jlsqxq from "./detail/index.vue";
import { getDate,downLoad } from "@/utils/tool";
import edit from "./edit";
import linkFwjl from "./link/toLinkFwjl.vue"
import {getDictDatas} from "@/utils/dict";

export default {
  //服务申请
  name: "jlsq",
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams));
  },
  components: {
    FLow,
    Table,
    Pagination,
    Dropdown,
    FileUpload,
    MultipleColumnSelection,
    jlsqxq,
    edit,
    linkFwjl,
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark;
    },
  },
  mounted() {
    this.getList();
    this.getSpecialtyDict();
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ["1"],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        approveState: -1,
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      dataParams: {},
      // 弹出框类别
      dialogType: "",
      // 弹出框标题
      dialogTitle: "",
      // 弹出框标显示
      dialog: false,
      // 专业字典
      specialtyDict: [],
      componentKey: 0,
      realTableOptions: [],
      tableOptions: [
        { label: "项目名称", prop: "projectName" },
        { label: "单据编号", prop: "applyNo", width: 150 },
        { label: "设计单位", prop: "designUnit" },
        { label: "设计工代专业", prop: "specialty", width: 120, slot: true },
        { label: "申请到位时间", prop: "applyArriveDate", width: 120, slot: true },
        { label: "发起人", prop: "applyUserName", width: 90 },
        { label: "现场问题附件", prop: "siteProblemFile", width: 120, slot: true },
        { label: "设计工代专业人员", prop: "specialtyAndPeople", width: 150, slot: true },
        { label: "答复到位时间", prop: "replyArriveDays", width: 120 },
        { label: "签收人", prop: "signerUserName", width: 90 },
        { label: "联系单附件", prop: "contactFile", width: 110, slot: true },
        { label: "关联服务记录数", prop: "serviceRecordCount", width: 130 },
        { label: "审批", prop: "approveState", width: 80, slot: true },
      ],
      form: {
        designProjectId: null,
        projectName: "",
        designUnit: "",
        applyUserName: "",
        applyUserId: null,
        specialty: [],
      },

      // 查看专业和人员信息
      specialtyDialog: false,
      specialtyType: "",
      specialtyDialogTableOptions: [],
      specialtyDialogTableData: [],
      specialtyDialogTitle: "",
      // 图片预览对话框
      previewImagesDialogData: "",
      previewImagesDialog: false,
      previewImagesDialogType: "",
      previewImagesDialogTitle: "",
      hjID: 1,
      // 审批
      flowDialog: false,
      initForm: {},
      detailDialog: false,
      approveStates: [
        { label: "全部", value: -1 },
        { label: "未提交", value: 0 },
        { label: "审核中", value: 1 },
        { label: "已审核", value: 2 },
        { label: "已驳回", value: 3 },
        { label: "流程终止", value: 4 },
      ],
    };
  },
  methods: {
    getDictDatas,
    downloadPdf() {
      if (this.paramsList.length == 0) {
        this.$message.error("请先选择一条记录");
        return;
      }

      let ids = [];
      //遍历
      for (let item of this.paramsList) {
        if (item.approveState != 2) {
          this.$message.error("请先审批通过才可以导出pdf");
          return; // 这里会直接退出整个函数
        }
        ids.push(item.id);
      }
      downloadPdf({ ids: ids }).then((res) => {
        // 根据响应类型设置文件名和类型
        const isZip = res.type === "application/zip";
        const fileName = isZip
          ? "设计工代现场服务联系单.zip"
          : this.paramsList[0].applyNo + "设计工代现场服务联系单.pdf";

        // 创建下载链接
        const blob = new Blob([res], { type: res.type });
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();

        // 清理
        window.URL.revokeObjectURL(link.href);
      });
    },
    //下载excel
    downloadExcel(){
      downloadExcel(this.queryParams).then((res) => {
        let fileName ='服务申请'+ getDate(new Date(),'YYYYMMDDHHmmssss') +'.xlsx'
        downLoad(res, fileName)
      });
    },
    getDate,
    openInfo() {
      if (this.selectID == 0) {
        this.$message.error("请先选择一条记录！");
        return;
      }
      console.log(this.selectID);
      this.detailDialog = true;
    },
    /** 查看现场问题附件 */
    handlePreviewImages(value, type) {
      this.previewImagesDialog = true;
      // this.previewImagesDialogData = value
      this.previewImagesDialogType = type;
      this.previewImagesDialogTitle = type == "siteProblemFile" ? "现场问题附件" : "联系单附件";
      this.hjID = type == "siteProblemFile" ? 1 : 2;
      //获取详情
      getDetail(value.id).then((res) => {
        this.previewImagesDialogData =
          type == "siteProblemFile"
            ? res.result.siteProblemFile
            : res.result.contactFile;
        console.log(this.previewImagesDialogData);
      });
    },
    /** 查看专业和人员信息 */
    handleSpecialty(value, type) {
      this.specialtyDialog = true;
      this.specialtyDialogTableData = [];
      this.specialtyType = type;
      if (type == "specialty") {
        this.specialtyDialogTitle = "专业信息";
        this.specialtyDialogTableOptions = [
          { label: "专业名称", prop: "specialtyName" },
        ];
        JSON.parse(value).forEach((item) => {
          this.specialtyDialogTableData.push({
            specialtyName: this.specialtyDict.find(
              (item1) => item1.value == item
            ).label,
          });
        });
      }
      if (type === "specialtyAndPeople") {
        this.specialtyDialogTitle = "专业和人员信息";
        this.specialtyDialogTableOptions = [
          { label: "专业名称", prop: "specialtyName" },
          { label: "姓名", prop: "userName" },
          { label: "手机号", prop: "telephone" },
        ];
        //获取专业和人员信息(请求接口)
        getPersonListBySupervisorApplyId({ supervisorApplyId: value }).then(
          (res) => {
            this.specialtyDialogTableData = res.result;
          }
        );
      }
    },
    /** 删除 */
    remove() {
      if (this.paramsList.length === 0) {
        this.$message.warning("请选择要删除的记录");
        return;
      }

      for (const item of this.paramsList) {
        if(item.approveState !== 0 && item.approveState !== 3){
          this.$message.error("只有未提交以及已驳回的才可删除");
          return;
        }
        // 创建人或申请人才可以编辑和删除
        if(item.applyUserId !== this.$store.state.user.userid && item.createId !== this.$store.state.user.userid ){
          this.$message.error("只有创建人或者发起人才可删除");
          return;
        }
      }

      const ids = this.paramsList.map((row) => row.id);
      const applyNos = this.paramsList.map((row) => row.applyNo).join("、");

      this.$confirm(`确定要删除单据编号为${applyNos}的记录吗？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
      }).then(() => {
        deleteById(ids).then((res) => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions];
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 获取专业字典 */
    getSpecialtyDict() {
      getSpecialtyDict("sjgd-specialty").then((res) => {
        this.specialtyDict = res.result;
      });
    },

    /** 数据查询 */
    getList() {
      this.loading = true;
      this.selectID = 0;
      this.dataParams = {};
      getPageList(this.queryParams)
        .then((res) => {
          this.list = res.result.records;
          this.queryParams.total = res.result.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val;
      this.getList();
    },
    /** 重置查询 */
    resetQuery() {
      this.defaultForm.pageNum = 1;
      this.defaultForm.pageSize = this.queryParams.pageSize;
      this.monthlyCycleList = [];
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm));
      this.queryParams.approveState = -1;
      this.getList();
    },
    /** 单击表事件 */
    select(row) {
      this.selectID = row.id;
      this.dataParams = row;
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas;
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0]);
      }
    },
    /** 清空表单 */
    clearForm() {
      this.form = {
        designProjectId: null,
        projectName: "",
        designUnit: getDictDatas('sjgd-sj-unit')[0].label,
        applyUserName: "",
        applyUserId: null,
        specialty: [],
        createId: null
      };
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      // 首先重置表单数据
      this.clearForm();
      this.dialogType = type;
      switch (type) {
        case "add":
          this.dialog = true;
          this.dialogTitle = "新增";
          //获取当前登录用户
          if (!this.$store.state.user.name) {
            this.$message.error("请先登录");
            return;
          }
          // 重置组件 key 强制组件渲染
          this.componentKey += 1;
          let unit = getDictDatas('sjgd-sj-unit')[0].label
          this.$nextTick(() => {
            this.form = {
              designProjectId: null,
              projectName: "",
              designUnit: unit,
              applyUserName: this.$store.state.user.name,
              applyUserId: this.$store.state.user.userid,
              specialty: [], // 确保多选字段被初始化为空数组
            };
            this.$forceUpdate();
          });

          break;
        case "edit":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.paramsList.length > 1) {
            this.$message.warning("只能编辑一条记录！");
            return;
          }
          if(this.dataParams.approveState == 1){
            this.$message.warning("审核中不允许编辑！");
            return;
          }
          if(this.dataParams.approveState == 4){
            this.$message.warning("已取消的记录不允许编辑！")
            return
          }
          // 创建人或申请人才可以编辑和删除
          if(this.dataParams.applyUserId !== this.$store.state.user.userid && this.dataParams.createId !== this.$store.state.user.userid ){
            this.$message.error("只有创建人或者发起人允许编辑");
            return;
          }
          //获取详情
          getDetail(this.selectID).then((res) => {
            this.form = res.result;
            this.form.specialty = JSON.parse(this.form.specialty);
          });
          this.dialog = true;
          this.dialogTitle = "编辑";

          break;
        case "flow":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.paramsList.length > 1) {
            this.$message.warning("只能一条一条的提交审批！");
            return;
          }

          if (
            this.dataParams.applyUserId != this.$store.state.user.userid &&
            this.dataParams.approveState == 0
          ) {
            this.$message.warning("只有发起人才可以提交审批！");
            return;
          }

          if (
            this.dataParams.approveState != 0 &&
            this.dataParams.approveState != 3
          ) {
            this.$message.warning("不允许重复提交审批！");
            return;
          }
          this.flowDialog = true;
          this.$nextTick(() => {
            this.$refs.flow.openShowDialog();
          });
          break;
        case "edit_fwjl":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.paramsList.length > 1) {
            this.$message.warning("只能编辑一条记录！");
            return;
          }
          if(this.dataParams.approveState !== 2 ){
            this.$message.warning("仅已审核允许编辑！");
            return;
          }
          //获取专业和人员信息(请求接口)
          getPersonListBySupervisorApplyId({ supervisorApplyId: this.selectID }).then((res) => {
            const list = res.result;
            // 创建人或申请人或该记录设计工代人员才可以编辑和删除
            if(!list.some(item => item.designUserId === this.$store.state.user.userid)
            ) {
              this.$message.warning("只有设计工代人员允许编辑记录");
              return;
            }
            //获取详情
            getDetail(this.selectID).then((res) => {
              this.form = res.result;
              this.form.specialty = JSON.parse(this.form.specialty);
            });
            this.dialog = true;
            this.dialogTitle = "编辑记录";
          });
          break;
        case "link_fwjl":
          if (this.selectID == 0) {
            this.$message.warning("请选择一条记录！");
            return;
          }
          if (this.paramsList.length > 1) {
            this.$message.warning("只能选择一条记录！");
            return;
          }
          if(this.dataParams.approveState !== 2 ){
            this.$message.warning("仅已审核允许关联！");
            return;
          }
          getPersonListBySupervisorApplyId({ supervisorApplyId: this.selectID }).then((res) => {
            const list = res.result;
            // 创建人或申请人或该记录设计工代人员才可以编辑和删除
            if(!list.some(item => item.designUserId === this.$store.state.user.userid)
            ) {
              this.$message.warning("只有设计工代人员允许关联记录");
              return;
            }

            this.$refs.linkFwjl.open();
          });

          break;
      }
    },
    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case "show":
          this.getList();
          this.dialog = false;
          break;
      }
    },

    // 审批通过回调
    appAgree(params) {
      this.flowDialog = false;
      submitLc(params).then((res) => {
        this.$message.success("业务提交成功");
        this.getList();
      });
    },
    appClose(params) {
      this.flowDialog = false;
    },
    appReject(params) {
      this.flowDialog = false;

      rollBackForApply(params).then((res) => {
        this.$message.success("业务驳回成功");
        this.getList();
      });
    },
  },
};
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}
</style>
