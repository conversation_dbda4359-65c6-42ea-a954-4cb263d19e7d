<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">审批类型：</span>
        <el-select v-model="queryParams.type" placeholder="请选择类别" @change="handleChange">
          <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <span class="font-size14">项目名称：</span>
        <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称" @change="handleChange"></el-input>
        <span class="font-size14">申请人：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入申请人姓名" @change="handleChange"></el-input>
<!--        <span class="font-size14">编号：</span>-->
<!--        <el-input v-model="queryParams.applyNo" clearable placeholder="请输入编号" @change="handleChange"></el-input>-->
        <span class="font-size14">审批状态：</span>
        <el-select v-model="queryParams.approveState" placeholder="请选择审批状态" @change="handleChange">
          <el-option
              v-for="item in approveStateList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-checkbox v-model="queryParams.checked" @change="handleChange" style="margin-right: 10px;" >我已审批记录</el-checkbox>

        <el-button class="rygf" size="mini" type="text" icon="el-icon-search" @click="getList">查询</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-view" @click="openDialog('show')"  v-has-permi="['JDWSJ01AP01QX01']"  >查看详情</el-button>
        <el-button class="rygf" size="mini" type="text" icon="el-icon-s-check" @click="openDialog('flow')"  v-has-permi="['JDWSJ01AP01QX02']"  >审批</el-button>

        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="table"
          :needSelect="true"
          :tableData="list"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @getSelectionData="selectionData"
          @rowdblclick="openDialog('show')"
      >
        <template slot="ywType" slot-scope="scope">
          <span v-if="scope.row.ywType == 1">服务申请</span>
          <span v-else-if="scope.row.ywType == 2">现场服务记录</span>
          <span v-else-if="scope.row.ywType == 3">现场问题反馈</span>
        </template>
        <template slot="approveState" slot-scope="scope">
          <div v-if="scope.row.approveState == 0">
            <el-tag>未提交</el-tag>
          </div>
          <div v-if="scope.row.approveState == 1">
            <el-tag type="warning">审批中</el-tag>
          </div>
          <div v-if="scope.row.approveState == 2">
            <el-tag type="success">已审批</el-tag>
          </div>
          <div v-if="scope.row.approveState == 3">
            <el-tag type="danger">已驳回</el-tag>
          </div>
          <div v-if="scope.row.approveState == 4">
            <el-tag type="info">流程终止</el-tag>
          </div>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, sizes, prev, pager, next, jumper"
          :total="queryParams.total"
          :page-sizes="[15, 30, 45, 60]"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="xqDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="xqDialog = false"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <jlsqxq :jlsqId="selectID" v-if="ywType ==1"></jlsqxq>
      <fwjlxq :fwjlId="selectID" v-if="ywType ==2"></fwjlxq>
      <wtfkxq :wtfkId="selectID" v-if="ywType ==3"></wtfkxq>
    </el-dialog>
    <FLow
        ref="flow"
        v-if="flowDialog"
        :yw-id="dataParams.id"
        :lc-jd-id="dataParams.lcJdid"
        :lc-define-id="dataParams.lcDefineID"
        :first-lc-jd="dataParams.firstLcJd"
        :init-choose="1"
        :init-form="initForm"
        :init-rules="initRules"
        :before-submit="checkSubmit"
        :termination-disable="true"
        @close="appClose"
        @agree="appAgree"
        @reject="appReject"
        @approvalOpinion="approvalOpinion"
    >
      <template v-slot:formSearch="scope" v-if="dataParams.ywType == 1 && dataParams.lcJdid == 200332 && approvalType =='agree' ">
        <el-row>
          <el-col :span="24">
            <el-form-item label="到位时间(天)" prop="replyArriveDays">
              <el-input-number v-model="initForm.replyArriveDays" :min="0" :precision="0" placeholder="请输入到位时间(天)"></el-input-number>
            </el-form-item>

            <el-form-item label="设计工代人员" prop="designUser">
              <el-cascader
                  :options="specialtyList"
                  :props="props"
                  clearable @change="change1"></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </FLow>
  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import Dropdown from "components/ColumnDropdown/index.vue";
import {getFlowList,submitLc,rollBackForApply,getSpecialtyUser,checkSubmit} from '@/api/sjgd/approve'
import FLow from "@/components/FLow/index.vue";
import jlsqxq from "../fieldService/detail/index.vue";
import wtfkxq from "../problemFeedback/detail/index.vue";
import fwjlxq from "../serviceRecord/detail/index.vue";
export default {
  name: 'approve',
  created() {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {fwjlxq, wtfkxq, jlsqxq, Table, Pagination, Dropdown,FLow},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  mounted() {
    this.getList()
  },
  data() {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 30,
        total: 0,
        type: -1,
        userName:"",
        projectName:"",
        applyNo:"",
        approveState:-1,
        isHistory:0
      },
      // 数据列表
      list: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      ywType:0,
      // 选择列表数据
      dataParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      dialog: false,
      // 表单校验规则
      driverRules: {},
      realTableOptions: [],
      tableOptions: [
        {label: '审批类型', prop: 'ywType', width: 200, slot: true},
        {label: '项目名称', prop: 'projectName'},
        {label: '申请人', prop: 'userName', width: 200},
        {label: '编号', prop: 'applyNo', width: 200},
        {label: '审批状态', prop: 'approveState', width: 200, slot: true},
        {label: '创建时间', prop: 'createTime', width: 200},
      ],
      typeList: [
        {value: -1, label: '全部'},
        {value: 1, label: '服务申请'},
        {value: 2, label: '现场服务记录'},
        {value: 3, label: '现场问题反馈'}
      ],
      approveStateList: [
        {value: -1, label: '全部'},
        {value: 0, label: '未提交'},
        {value: 1, label: '审批中'},
        {value: 2, label: '已审批'},
        {value: 3, label: '已驳回'},
        {value: 4, label: '流程终止'}
      ],

      // 多选列表
      paramsList: [],
      xqDialog: false,
      // 审批
      flowDialog: false,
      initForm: {
        replyArriveDays :null,
        designUser:[]
      },
      approvalType:'agree',
      initRules: {
        designUser: [
          {required: true, message: '请选择人员', trigger: 'blur'}
        ],
        replyArriveDays: [
          {required: true, message: '请输入到位时间', trigger: 'blur'},
        ],
      },
      props: { multiple: true },
      specialtyList: []
    }

  },
  methods: {
    checkSubmit,
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },

    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      if(this.queryParams.checked){
        this.queryParams.isHistory = 1
      }else{
        this.queryParams.isHistory=0
      }
      this.getList()
    },

    /** 数据查询 */
    getList() {
      this.loading = true
      getFlowList(this.queryParams).then((res) => {
        this.list = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },

    /** 单击表事件 */
    select(row) {
      this.selectID = row.id
      this.ywType = row.ywType;
      this.dataParams = row
    },
    /** 多选表事件 */
    selectionData(datas) {
      this.paramsList = datas
      if (this.paramsList.length == 1) {
        this.select(this.paramsList[0])
      }
    },
    /** 弹窗打开事件 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.paramsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '查看详情'
            if(this.dataParams.ywType ==1){
              this.dialogTitle = '查看服务申请详情';
            }else if(this.dataParams.ywType ==2){
              this.dialogTitle = '查看服务记录详情';
            }else if(this.dataParams.ywType ==3){
              this.dialogTitle = '查看问题反馈详情';
            }
            this.dialogType = 'show'
            this.xqDialog = true
          }
          break;
        case 'flow':
          if (this.paramsList.length > 1) {
            this.$message.error('不可查看多条数据！')
          } else if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else if (this.dataParams.approveState === 1 && this.dataParams.sendPersonZgh === this.$store.state.user.loginName + '~') {
            if(this.dataParams.ywType ==1){
              //服务申请，获取对应专业及以下人员
              this.specialtyList = [];
              getSpecialtyUser({ywId: this.dataParams.id}).then((res) => {
                this.specialtyList = res.result
              })
            }
            this.flowDialog = true;
            this.$nextTick(() => {
              this.$refs.flow.openShowDialog();
            })
          } else if (this.dataParams.sendPersonZgh !== this.$store.state.user.loginName + '~') {
            this.$message.error('你当前不是审核人，不可再次提交！')
          } else if (this.dataParams.approveState === 2) {
            this.$message.error('已审核，不可再次提交！')
          } else if (this.dataParams.approveState === 3) {
            this.$message.error('已驳回，不可再次提交，请前往申请界面重新提交！')
          } else if (this.dataParams.approveState === 4) {
            this.$message.error('流程已终止，不可再次提交！')
          }
          break;
      }
    },
    /** 弹窗关闭事件 */
    closeDialog(type) {
      switch (type) {
        case 'show':
          this.getList()
          this.dialog = false
          break;
      }
    },
    change1(val){
      console.log(val)
      this.initForm.designUser = [];
      if(val.length >0){
        for (let i = 0; i <val.length ; i++) {
          this.initForm.designUser.push({"specialty":val[i][0],"designUserId":val[i][1]})
        }
      }

    },
    // 审批通过回调
    appAgree(params) {
      this.flowDialog = false;
      this.initForm =  {
        designUser: [],
        replyArriveDays :null
      }
      submitLc(params).then(res=>{
        this.$message.success('业务提交成功');
        this.getList();
      })
    },
    appClose(params) {
      this.flowDialog = false;
    },
    appReject(params) {
      this.flowDialog = false;
      this.initForm =  {
        designUserId: null,
      }
      rollBackForApply(params).then(res=>{
        this.$message.success('业务驳回成功');
        this.getList();
      })
    },
    approvalOpinion(params){
      this.approvalType =  params
      console.log(params)
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}


.rowBorder {
  border: 1px solid black;
  border-bottom: none;
  margin-bottom: 0px;
}
</style>
