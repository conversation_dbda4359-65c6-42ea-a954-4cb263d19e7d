<template>
  <div>
    <logo :collapse="false"/>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="$route.path"
          router
          :unique-opened="true"
          class="el-menu-vertical-demo"
          :active-Title-color="theme">
        <component
            v-for="route in menuList"
            :key="route.Id"
            :index="route.Url || route.Title"
            :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'"
            :route="{path: route.Url}"
            :disabled="!!route.HasPermi"
        >
          <template slot="title">
            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
            <span slot="title" class="font-size14">{{route.Title}}</span>
          </template>
          <template v-if="route.childList && route.childList.length > 0">
            <el-menu-item
                v-for="routeChild in route.childList"
                :key="routeChild.Id"
                :index="routeChild.Url"
                :route="{path: routeChild.Url}"
                :disabled="!routeChild.HasPermi">
              <span slot="title">{{routeChild.Title}}</span>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin} from "api/login";
import {getModule} from "api/common";

export default {
  components: {Logo},
  data() {
    return {
      urlList: [],
      menuList: []
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  created() {
    MLogin().then(res=>{
      if(res.success){
        this.getMenuList()
        this.$store.dispatch('dict/GetDictDatas')
      }else{
        this.$router.push('/login')
      }
    })
  },
  methods:{
    async getMenuList(){
      await getModule().then(res=>{
        const functionList = (res.data || []).filter(item => item.Id === 'JDWSJ01')
        this.urlList = functionList.flatMap(item => item.childList.map(child => child.Url));
        this.menuList = [
          {
            Id: 'JDWSJ01PM01',
            Title: '人员管理',
            Url: '/dfdwSjgd/personManage',
            HasPermi: !(this.urlList.indexOf('/dfdwSjgd/personManage') > -1)
          },
          {
            Id: 'JDWSJ01PM02',
            Title: '项目管理',
            Url: '/dfdwSjgd/projectManage',
            HasPermi: !(this.urlList.indexOf('/dfdwSjgd/projectManage') > -1)
          },
          {
            Id: 'JDWSJ01',
            Title: '现场服务',
            childList: [
              {
                Id: 'JDWSJ01FS01',
                Title: '服务申请',
                Url: '/dfdwSjgd/fieldService',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dfdwSjgd/fieldService') > -1
              },
              {
                Id: 'JDWSJ01SR01',
                Title: '服务记录',
                Url: '/dfdwSjgd/serviceRecord',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dfdwSjgd/serviceRecord') > -1
              },
              {
                Id: 'JDWSJ01PF01',
                Title: '问题反馈',
                Url: '/dfdwSjgd/problemFeedback',
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/dfdwSjgd/problemFeedback') > -1
              }
            ]
          },
          {
            Id: 'JDWSJ01AP01',
            Title: '审批',
            Url: '/dfdwSjgd/sjgdApprove',
            Disable: !(this.urlList.indexOf('/dfdwSjgd/approve') > -1)
          },
          {
            Id: 'JDWSJ01ZD01',
            Title: '字典',
            Url: '/dfdwSjgd/dict',
            Disable: !(this.urlList.indexOf('/dfdwSjgd/dict') > -1)
          }
        ]
        let firstUrl = ''
        this.menuList.forEach((value) => {
          if (firstUrl != '') {
            return
          }
          value.Url && (!value.HasPermi && (firstUrl = value.Url))
          value.childList && value.childList.forEach((param) => {
            if (firstUrl != '') {
              return
            }
            if (param.HasPermi) {
              firstUrl = param.Url
            }
          })
        })
        //TODO 获取后台模块列表 默认跳转到第一个
        if (this.$router.currentRoute.fullPath !== firstUrl) {
          this.$router.push(firstUrl)
        }
        this.$forceUpdate()
      })
    }
  }
};
</script>
