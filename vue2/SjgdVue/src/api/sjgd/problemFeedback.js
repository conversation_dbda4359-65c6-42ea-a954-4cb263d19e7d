import request from '@/utils/request'

export function getPageList(params) {
    return request({
      url: 'dwSjgd/sjgd/fieldproblemfeedback/listPage',
      method: 'post',
      data: params
    })
  }


// 添加或编辑
export function addOrEdit(params) {
    return request({
      url: 'dwSjgd/sjgd/fieldproblemfeedback/addOrEdit',
      method: 'post',
      data: params
    })
}
//签收
export function signFor(params) {
  return request({
    url: 'dwSjgd/sjgd/fieldproblemfeedback/signFor',
    method: 'post',
    data: params
  })
}


  // 获取详情
  export function getDetail(id) {
    return request({
      url: `dwSjgd/sjgd/fieldproblemfeedback/detail/${id}`,
      method: 'get'
    })
  }

  // 删除
  export function deleteById(ids) {
    return request({
      url: `dwSjgd/sjgd/fieldproblemfeedback/delete/${ids}`,
      method: 'delete'
    })
  }

  //获取流程列表
export function getLcListById(id){
  return request({
    url: `dwSjgd/sjgd/fieldproblemfeedback/getLcListById/${id}`,
    method: 'get'
  })
}


//下载pdf
export function downloadPdf(params) {
  return request({
    url: `dwSjgd/sjgd/fieldproblemfeedback/downloadPdf`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

//下载excel
export function downloadExcel(params) {
  return request({
    url: `dwSjgd/sjgd/fieldproblemfeedback/downloadExcel`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
