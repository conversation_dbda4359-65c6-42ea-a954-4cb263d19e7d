import request from '@/utils/request'

// 获取设计工代的项目列表
export function getProjectPageList(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/getPageList',
        method: 'post',
        data: data
    })
}
// 获取checkV2的项目列表
export function getVProjectPageList(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/getVProjectPageList',
        method: 'post',
        data: data
    })
}

// 新增项目
export function addProject(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/addProject',
        method: 'post',
        data: data
    })
}
// 项目详情
export function detail(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/detail',
        method: 'get',
        params: data
    })
}

// 修改项目
export function updateProject(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/update',
        method: 'put',
        data: data
    })
}
export function deleteProject(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/delete',
        method: 'delete',
        data: data
    })
}
export function GetLeaderList(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/getLeaderList',
        method: 'post',
        data: data
    })
}

// 导出PDF
export function exportProjectPdf (params) {
    return request({
        url: 'dwSjgd/Sjgd/project/export',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

export function getPersonPageList(data) {
    return request({
        url: 'dwSjgd/Sjgd/project/getPersonPageList',
        method: 'post',
        data: data
    })
}

//下载excel
export function downloadExcel(params) {
    return request({
        url: `dwSjgd/Sjgd/project/downloadExcel`,
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}
