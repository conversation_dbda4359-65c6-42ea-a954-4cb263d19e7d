import request from '@/utils/request'

export function getPageList(params) {
    return request({
      url: 'dwSjgd/Sjgd/serviceRecord/getPageList',
      method: 'post',
      data: params
    })
  }
  export function addOrEdit(params) {
    return request({
      url: 'dwSjgd/Sjgd/serviceRecord/addOrEdit',
      method: 'post',
      data: params
    })
  }
  export function getDetail(id) {
    return request({
      url: 'dwSjgd/Sjgd/serviceRecord/getDetail',
      method: 'get',
      params: id
    })
  }
  export function deleteServiceRecord(data) {
    return request({
        url: 'dwSjgd/Sjgd/serviceRecord/delete',
        method: 'delete',
        data: data
    })
}
// 导出PDF
export function exportProjectPdf (params) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/export',
      method: 'post',
      data: params,
      responseType: 'blob'
  })
}
//获取流程列表
export function getLcListById(id){
  return request({
    url: `dwSjgd/Sjgd/serviceRecord/getLcListById/${id}`,
    method: 'get'
  })
}

// 问题反馈添加或编辑
export function addOrEditWtfk(params) {
  return request({
    url: 'dwSjgd/sjgd/fieldproblemfeedback/addOrEdit',
    method: 'post',
    data: params
  })
}

//服务申请、服务记录关联
export function serviceAssociation(params) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/serviceAssociation',
    method: 'post',
    data: params
  })
}

// 获取问题反馈详情
export function getDetailWtfk(id) {
  return request({
    url: `dwSjgd/sjgd/fieldproblemfeedback/detail/${id}`,
    method: 'get'
  })
}

//获取问题反馈流程列表
export function getLcListWtfkById(id){
  return request({
    url: `dwSjgd/sjgd/fieldproblemfeedback/getLcListById/${id}`,
    method: 'get'
  })
}



//获取服务申请流程列表
export function getLcListFwsqById(id){
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/getLcListById/${id}`,
    method: 'get'
  })
}

//下载excel
export function downloadExcel(params) {
  return request({
    url: `dwSjgd/Sjgd/serviceRecord/downloadExcel`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

//得到服务申请未关联数据
export function getFwsqAssociationList(params) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/getFwsqAssociationList',
    method: 'post',
    data: params
  })
}

//得到问题反馈未关联数据
export function getWtfkAssociationList(params) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/getWtfkAssociationList',
    method: 'post',
    data: params
  })
}

//得到关联数据
export function getAssociationList(params) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/getAssociationList',
    method: 'post',
    data: params
  })
}
