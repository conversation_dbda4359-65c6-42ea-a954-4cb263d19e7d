import request from '@/utils/request'


export function getFlowList (params) {
    return request({
        url: '/dwSjgd/Sjgd/DesignFlow/getFlowList',
        method: 'post',
        data: params
    })
}

// 审批完业务回调
export function submitLc(params){
    return request({
        url: '/dwSjgd/Sjgd/DesignFlow/submitLc',
        method: 'post',
        data: params
    })
}

// 审批完-驳回业务回调
export function rollBackForApply(params){
    return request({
        url: '/dwSjgd/Sjgd/DesignFlow/rollBackForApply',
        method: 'post',
        data: params
    })
}


//审批前置校验
export function checkSubmit(params){
    return request({
        url: '/dwSjgd/Sjgd/DesignFlow/checkSubmit',
        method: 'post',
        data: params
    })
}

//获取专业人员
export function getSpecialtyUser (param) {
    return request({
        url: '/dwSjgd/Sjgd/DesignFlow/getSpecialtyUser',
        method: 'get',
        params: param
    })
}
