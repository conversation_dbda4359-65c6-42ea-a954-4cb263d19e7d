import request from '@/utils/request'

/** 设计工代-PC-人员管理-分页 */
export function designPersonPage (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/page',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-列表 */
export function designPersonList (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/list',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-新增 */
export function designPersonAdd (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/add',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-修改 */
export function designPersonEdit (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/edit',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-删除 */
export function designPersonDeleteByIds (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/deleteByIds',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-关联用户表 */
export function linkPerson (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/linkPerson',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-查询所有有权限的人 */
export function getPermissonList (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/getPermissonList',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-根据id查询角色 */
export function getPersonRoleById (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/getPersonRoleById',
        method: 'post',
        data: params
    })
}

/** 设计工代-PC-人员管理-查询角色 */
export function getRole (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/getRole',
        method: 'get',
        params: params
    })
}

/** 设计工代-PC-人员管理-编辑id角色 */
export function editRoleById (params) {
    return request({
        url: '/dwSjgd/sjgd/designPerson/editRoleById',
        method: 'post',
        data: params
    })
}
