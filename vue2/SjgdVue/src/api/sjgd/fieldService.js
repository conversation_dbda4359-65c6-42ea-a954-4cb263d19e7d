import request from '@/utils/request'

export function getPageList(params) {
  return request({
    url: 'dwSjgd/sjgd/designSupervisorApply/listPage',
    method: 'post',
    data: params
  })
}

export function addOrEdit(params) {
  return request({
    url: 'dwSjgd/sjgd/designSupervisorApply/addOrEdit',
    method: 'post',
    data: params
  })
}

export function deleteById(ids) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/delete/${ids}`,
    method: 'delete'
  })
}

//获取项目列表
export function getProjectList(params) {
  return request({
    url: 'dwSjgd/Sjgd/project/getList',
    method: 'post',
    data: params
  })
}

//获取用户
export function getPersonList(params) {
  return request({
    url: 'dwSjgd/sjgd/designPerson/getList',
    method: 'post',
    data: params
  })
}
//获取专业字典
export function getSpecialtyDict(type) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/getDictList/${type}`,
    method: 'get'
  })
}

//获取详情
export function getDetail(id) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/detail/${id}`,
    method: 'get'
  })
}

//获取附件
export function getFileList(id) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/getFileList`,
    method: 'post',
    data: params
  })
}

//根据服务申请id 获取设计工代人员列表
export function getPersonListBySupervisorApplyId(params) {
  return request({
    url: 'dwSjgd/sjgd/supervisorApplySpecialty/getPersonList',
    method: 'post',
    data: params
  })
}

//获取流程列表
export function getLcListById(id) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/getLcListById/${id}`,
    method: 'get'
  })
}

//下载pdf
export function downloadPdf(params) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/downloadPdf`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
//下载excel
export function downloadExcel(params) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/downloadExcel`,
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

//服务记录新增/修改
export function addOrEditFwjl(params) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/addOrEdit',
    method: 'post',
    data: params
  })
}

//服务申请、服务记录关联
export function serviceAssociation(params) {
  return request({
    url: 'dwSjgd/sjgd/designSupervisorApply/serviceAssociation',
    method: 'post',
    data: params
  })
}

//服务记录详情
export function getDetailFwjl(id) {
  return request({
    url: 'dwSjgd/Sjgd/serviceRecord/getDetail',
    method: 'get',
    params: id
  })
}

//获取服务记录流程列表
export function getLcListFwjlById(id) {
  return request({
    url: `dwSjgd/Sjgd/serviceRecord/getLcListById/${id}`,
    method: 'get'
  })
}

//得到服务记录未关联数据
export function getFwjlAssociationList(params) {
  return request({
    url: 'dwSjgd/sjgd/designSupervisorApply/getFwjlAssociationList',
    method: 'post',
    data: params
  })
}

//得到关联数据
export function getAssociationList(params) {
  return request({
    url: 'dwSjgd/sjgd/designSupervisorApply/getAssociationList',
    method: 'post',
    data: params
  })
}

export function getPersonListByProjectId(projectId) {
  return request({
    url: 'dwSjgd/sjgd/designPerson/getPersonListByProjectId/' + projectId,
    method: 'post',
  })
}
//获取筛选过的专业
export function getFilterSpecialtyDict(id) {
  return request({
    url: `dwSjgd/sjgd/designSupervisorApply/getFilterSpecialtyDict?id=` + id,
    method: 'get'
  })
}