import request from '@/utils/request'

/**
 * 下载示例
 * @returns
 */
export function downloadImportExample(params) {
    return request({
        url: '/dwSjgd/Sjgd/file/downloadImportExample',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

/**
 * 读取文件名
 * @returns
 */
export function getFileName(params) {
    return request({
        url: '/dwSjgd/Sjgd/file/getFileName',
        method: 'get',
        params: params
    })
}

/**
 * 下载
 * @returns
 */
export function download(params) {
    return request({
        url: '/dwSjgd/Sjgd/file/download',
        method: 'get',
        params: params,
        responseType: 'blob'
    })
}
