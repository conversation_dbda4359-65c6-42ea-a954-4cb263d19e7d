@font-face {
  font-family: "iconfont"; /* Project id 3873535 */
  src: url('iconfont.woff2?t=1679888740678') format('woff2'),
       url('iconfont.woff?t=1679888740678') format('woff'),
       url('iconfont.ttf?t=1679888740678') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-erweima:before {
  content: "\e605";
}

.icon-31mima:before {
  content: "\e600";
}

.icon-yonghu:before {
  content: "\e788";
}

.icon-addNew:before {
  content: "\e671";
}

.icon-kefurexian:before {
  content: "\e623";
}

.icon-ziti:before {
  content: "\e7b1";
}

.icon-xiangshang1:before {
  content: "\e76d";
}

.icon-xiangxia1:before {
  content: "\e771";
}

.icon-xiangshang2:before {
  content: "\e76e";
}

.icon-xiangxia2:before {
  content: "\e772";
}

.icon-xiangyou1:before {
  content: "\e775";
}

.icon-xiangzuo1:before {
  content: "\e779";
}

.icon-caidan:before {
  content: "\e790";
}

.icon-sousuo:before {
  content: "\e622";
}

.icon-shezhi-xianxing:before {
  content: "\e8b7";
}

.icon-dianhua:before {
  content: "\e88b";
}

.icon-yanjing:before {
  content: "\e8c7";
}

