<template>
  <div class="dfdw-multiple-column-select">
    <el-popover
        v-model="visible"
        trigger="focus"
        placement="bottom-start"
        :width="tableWidth"
        :disabled="disabled || readonly"
        @show="onPopoverShow"
        @hide="onPopoverHide"
        @after-leave="onPopoverAfterLeave"
        style="width: 100%;display: inline-block">
      <div slot="reference">
        <div class="dfdw-multiple-column-select-tag" v-if="multiple" ref="tagContainer">
          <div v-if="collapseTags">
            <el-tag
                v-if="selectedRows.length > 0"
                :key="selectedRows[0][label]"
                closable
                type="info"
                @close="tagClose(selectedRows[0])">
              {{selectedRows[0][label]}}
            </el-tag>
            <el-tag v-if="selectedRows.length > 1" type="info">
              {{ '+' + (selectedRows.length - 1) }}
            </el-tag>
          </div>
          <div v-else>
            <el-tag
                v-for="row in selectedRows"
                :key="row[label]"
                closable
                type="info"
                @close="tagClose(row)">
              {{row[label]}}
            </el-tag>
          </div>
        </div>
        <el-input
            ref="input"
            v-model="selectedValue"
            :size="size"
            :placeholder="realPlaceholder"
            :suffix-icon="suffixIcon"
            :disabled="disabled"
            :style="inputStyle"
            :clearable="clearable"
            :readonly="readonly"
            @input="inputChange"
            @keydown.native="onKeydown"
            @clear="inputClear"
        >
        </el-input>
      </div>

      <div class="selectMore" v-if="showMore">
        <el-checkbox v-model="selectMore" @change="selectMoreChange">更多</el-checkbox>
      </div>
      <!-- 自定义的表格内容 -->
      <el-table
          ref="table"
          :data="realOptions"
          style="width: 100%;"
          :highlight-current-row="true"
          :max-height="tableMaxHeight"
          :row-class-name="getRowClassName"
          @row-click="onRowClick"
          @keydown.native="onKeydown"
          @selection-change="handleSelectionChange"
          tabindex="0"
      >
        <el-table-column
            v-if="multiple"
            type="selection"
            width="55">
        </el-table-column>
        <template v-for="(item, index) in tableOptions">
          <el-table-column
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :width="item.width || ''"
              align="center"
          >
          </el-table-column>
        </template>
      </el-table>
      <Pagination
          v-if="pagination"
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="prev, pager, next"
          :total="queryParams.total"
      />
    </el-popover>
  </div>
</template>

<script>
import Pagination from "components/Pagination/index.vue";

export default {
  name: "MultipleColumnSelection",
  mounted() {
    this.updateInputHeight();
  },
  components: {Pagination},
  props: {
    // 绑定值
    value: {
      type: [String, Number, Array, null],
      default: null
    },
    // 绑定值字段名
    valueKey: {
      type: String,
      default: 'value'
    },
    // 显示字段名
    label: {
      type: String,
      default: 'label'
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 输入框尺寸
    size: {
      type: String,
      default: 'mini'
    },
    // 下拉选项
    options: {
      type: Array,
      default: () => []
    },
    // 下拉表格配置
    tableOptions: {
      type: Array,
      default: () => []
    },
    // 是否可选
    disabled: {
      type: Boolean,
      default: false
    },
    // 下拉表格宽度
    tableWidth: {
      type: Number,
      default: 400
    },
    // 下拉表格宽度
    tableMaxHeight: {
      type: Number,
      default: 400
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: false
    },
    // 是否分页
    pagination: {
      type: Boolean,
      default: false
    },
    // 是否为远程搜索
    remote: {
      type: Boolean,
      default: false
    },
    // 远程搜索方法
    remoteMethod: {
      type: Function,
    },
    // 新增多选属性
    multiple: {
      type: Boolean,
      default: false
    },
    // 多选时是否将选中值按文字的形式展示
    collapseTags: {
      type: Boolean,
      default: false
    },
    // 显示更多
    showMore: {
      type: Boolean,
      default: false
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (this.multiple) {
          if (!Array.isArray(newValue)) {
            this.$emit('input', []);
            this.$emit('change', []);
          }
          // 多选模式
          if (Array.isArray(newValue) && newValue.length > 0) {
            this.selectedRows = this.options.filter(item => newValue.includes(item[this.valueKey]));
            this.selectedLabels = this.selectedRows.map(item => item[this.label]);
            this.selectedValue = '';
            this.defaultValue = '';
          } else {
            this.selectedRows = [];
            this.selectedLabels = [];
            this.selectedValue = '';
            this.defaultValue = '';
          }
        } else {
          // 单选模式
          if (newValue != null) {
            const selectedItem = this.options.find(item => item[this.valueKey] === newValue);
            if (selectedItem) {
              this.selectedValue = selectedItem[this.label];
              this.defaultValue = this.selectedValue;
              this.selectedRows = [selectedItem];
            } else {
              this.selectedValue = '';
              this.defaultValue = '';
              this.selectedRows = [];
            }
          } else {
            this.selectedValue = '';
            this.defaultValue = '';
            this.selectedRows = [];
          }
        }
        this.selectIndexChange();
      },
      immediate: true,
      deep: true
    },
    placeholder: {
      handler(newValue) {
        this.$nextTick(() => {
          this.realPlaceholder = newValue;
          if (this.multiple && Array.isArray(this.value)) {
            this.realPlaceholder = this.value.length > 0 ? '' : newValue;
          }
        })
      },
      immediate: true,
      deep: true
    },
    options: {
      handler(newValue) {
        const value = this.value;
        this.$emit('input', null);
        this.$nextTick(() => {
          if (this.pagination) {
            this.queryParams.total = this.options.length;
            this.realOptions = JSON.parse(JSON.stringify(this.options.slice(this.queryParams.pageNum - 1, this.queryParams.pageSize)));
          } else {
            this.realOptions = JSON.parse(JSON.stringify(this.options));
          }
          this.$emit('input', value);
        })
      },
      immediate: true,
      deep: true
    },
    selectedRows: {
      handler() {
        this.updateInputHeight();
      },
      deep: true
    }
  },
  data() {
    return {
      visible: false,  // 用于控制popover显示与隐藏
      selectedValue: null,  // 选中的值
      focusedRowIndex: -1, // 记录当前焦点所在行的索引
      suffixIcon: 'el-icon-arrow-down',
      defaultValue: null,
      realPlaceholder: this.placeholder,
      realOptions: [],
      selectIndexs: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      selectedRows: [], // 存储选中的行
      isPageChange: false,
      selectedLabels: [],
      inputHeight: 28, // 默认高度
      selectMore: false
    };
  },
  computed: {
    inputStyle() {
      return {
        '--input-height': `${this.inputHeight}px`
      }
    }
  },
  methods: {
    // 强制刷新表格布局
    refreshTable() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      });
    },
    onPopoverShow() {
      this.focusedRowIndex = -1;
      this.suffixIcon = 'el-icon-arrow-up';
      
      this.$nextTick(() => {
        if (this.multiple && Array.isArray(this.value)) {
          // 多选模式下设置选中状态
          this.value.forEach(val => {
            const row = this.realOptions.find(item => item[this.valueKey] === val);
            if (row) {
              this.$refs.table.toggleRowSelection(row, true);
            }
          });
        } else if (!this.multiple) {
          // 单选模式
          const index = this.realOptions.findIndex(item => item[this.valueKey] === this.value);
          if (index > -1) {
            this.$refs.table.setCurrentRow(this.realOptions[index]);
          }
        }
        
        if (this.filterable) {
          if (this.multiple && this.value.length > 0) {
            this.realPlaceholder = null;
          } else {
            this.selectedValue = null;
            this.realPlaceholder = this.defaultValue;
          }
          this.$refs.input.$el.focus();
        } else {
          this.$refs.table.$el.focus();
        }
      });
    },
    onPopoverHide() {
      // 关闭popover时重置焦点索引
      this.focusedRowIndex = -1;
      this.suffixIcon = 'el-icon-arrow-down';
      this.selectedValue = this.defaultValue;
      this.realPlaceholder = this.placeholder;
      if (this.multiple && this.value.length > 0) {
        this.realPlaceholder = '';
      }
    },
    onPopoverAfterLeave() {
      if (this.multiple) {
        this.realOptions = JSON.parse(JSON.stringify(this.options.slice(this.queryParams.pageNum - 1, this.queryParams.pageSize)));
      } else {
        this.realOptions = JSON.parse(JSON.stringify(this.options));
      }
    },
    onRowClick(row, column, event) {
      if (this.multiple) {
        // 多选模式
        if (this.value.includes(row[this.valueKey])) {
          this.$refs.table.toggleRowSelection(row, false);
        } else {
          this.$refs.table.toggleRowSelection(row, true);
        }
      } else {
        // 单选模式
        this.selectedValue = row[this.label];
        this.visible = false;
        this.$emit('input', row[this.valueKey]);
        this.$emit('change', row[this.valueKey]);
      }
    },
    onKeydown(event) {
      const rowCount = this.realOptions.length;
      // 上下键导航
      if (event.key === 'ArrowDown') {
        if (this.focusedRowIndex < rowCount - 1) {
          this.focusedRowIndex++;
        } else if (this.focusedRowIndex === rowCount - 1) {
          this.focusedRowIndex = 0;
        }
      } else if (event.key === 'ArrowUp') {
        if (this.focusedRowIndex > 0) {
          this.focusedRowIndex--;
        } else if (this.focusedRowIndex === 0) {
          this.focusedRowIndex = rowCount - 1;
        }
      } else if (event.key === 'ArrowLeft') {
        if (this.queryParams.pageNum > 1) {
          this.queryParams.pageNum--;
          this.handleCurrentChange(this.queryParams);
        }
      } else if (event.key === 'ArrowRight') {
        if (this.queryParams.pageNum < this.queryParams.total / this.queryParams.pageSize) {
          this.queryParams.pageNum++;
          this.handleCurrentChange(this.queryParams);
        }
      } else if (event.key === 'Enter' && this.focusedRowIndex >= 0) {
        // 回车键选择当前行
        if (this.multiple) {
          const selectedRow = this.realOptions[this.focusedRowIndex];
          if (this.value.includes(selectedRow[this.valueKey])) {
            this.$refs.table.toggleRowSelection(selectedRow, false);
          } else {
            this.$refs.table.toggleRowSelection(selectedRow, true);
          }
        } else {
          const selectedRow = this.realOptions[this.focusedRowIndex];
          this.selectedValue = selectedRow[this.label];
          this.visible = false;  // 隐藏popover
          this.$emit('input', selectedRow[this.valueKey]);
          this.$emit('change', selectedRow[this.valueKey]);
        }
      }

      // 刷新表格的高亮行
      this.$nextTick(() => {
        this.$refs.table.setCurrentRow(this.realOptions[this.focusedRowIndex]);

        // 获取当前焦点行的 DOM 元素
        const focusedRow = this.$refs.table.$el.querySelector(`tr.el-table__row:nth-child(${this.focusedRowIndex + 1})`);
        if (focusedRow) {
          focusedRow.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
        }
      });
    },
    inputChange(value) {
      this.focusedRowIndex = -1;
      if (this.remote) {
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          searchValue: value,
        }
        this.remoteMethod(value).then(res => {
          this.realOptions = res.data.list;
          this.queryParams.total = res.data.total;
          this.selectIndexChange();
          this.$nextTick(() => {
            this.$refs.table.setCurrentRow();
          })
        });
      } else {
        const list = JSON.parse(JSON.stringify(this.options.filter(item => {
          let isIncludes = false;
          this.tableOptions.forEach((option) => {
            if (option.filter) {
              if (item[option.prop].includes(value)) {
                isIncludes = true;
              }
            }
          })
          return isIncludes;
        })));
        this.queryParams = {
          pageNum: 1,
          pageSize: 10,
          total: list.length
        }
        this.realOptions = list.slice(this.queryParams.pageNum - 1, this.queryParams.pageSize);
        this.selectIndexChange();
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        })
      }
    },
    getRowClassName({ row, rowIndex }) {
      if (Array.isArray(this.value)) {
        return this.value.includes(row[this.valueKey]) ? 'select-row' : '';
      } else if (typeof this.value === 'string') {
        return row[this.valueKey] == this.value ? 'select-row' : '';
      }
    },
    handleCurrentChange(val) {
      this.queryParams = val
      this.focusedRowIndex = -1;
      const start = (val.pageNum - 1) * val.pageSize;
      const end = val.pageNum * val.pageSize;
      this.isPageChange = true;
      this.realOptions = JSON.parse(JSON.stringify(this.options.slice(start, end)));
      this.selectIndexChange();
      this.$nextTick(() => {
        this.$refs.table.setCurrentRow();
        if (this.multiple) {
          this.realOptions.forEach((item) => {
            if (this.value.includes(item[this.valueKey])) {
              this.$refs.table.toggleRowSelection(item, true);
            }
          })
        }
      })
    },
    handleSelectionChange(selection) {
      if (this.multiple) {
        if (this.isPageChange) {
          this.selectIndexChange();
          this.isPageChange = false;
        } else {
          let value = this.realOptions.map(item => item[this.valueKey]);
          this.selectedRows = this.selectedRows.filter(item => !value.includes(item[this.valueKey])).concat(selection);
          const selectedValues = this.selectedRows.map(item => item[this.valueKey]);
          const selectedLabels = this.selectedRows.map(item => item[this.label]);
          this.selectedValue = selectedLabels.join(', ');
          this.selectIndexChange();
          this.$emit('input', selectedValues);
          this.$emit('change', selectedValues);
        }
        this.updateInputHeight();
      }
    },
    selectIndexChange() {
      this.selectIndexs = []
      this.realOptions.forEach((item, index) => {
        if (this.multiple && this.selectedRows.includes(item)) {
          this.selectIndexs.push(index)
        } else if (!this.multiple && item[this.valueKey] === this.value) {
          this.selectIndexs.push(index)
        }
      })
    },
    tagClose(row) {
      this.visible = false;
      this.selectedRows = this.selectedRows.filter(item => item[this.valueKey] !== row[this.valueKey]);
      const selectedValues = this.selectedRows.map(item => item[this.valueKey]);
      this.selectIndexChange();
      this.$emit('input', selectedValues);
      this.$emit('change', selectedValues);
      this.updateInputHeight();
    },
    updateInputHeight() {
      this.$nextTick(() => {
        if (this.multiple) {
          if (this.$refs.tagContainer) {
            // 获取标签容器的高度
            const tagHeight = this.$refs.tagContainer.offsetHeight;
            // 设置最小高度为50px
            this.inputHeight = Math.max(28, tagHeight); // 加10px作为内边距
            this.visible = false;
            this.$nextTick(() => {
              if (this.value.length > 0) {
                this.visible = true;
              }
            });
          } else {
            this.inputHeight = 28; // 重置为默认高度
          }
          if (this.value.length === 0) {
            this.inputHeight = 28; // 重置为默认高度
          }
        } else {
          this.inputHeight = 28; // 重置为默认高度
        }
      });
    },
    selectMoreChange(value) {
      this.$emit('selectMore', value);
    },
    inputClear() {
      if (this.multiple) {
        this.$emit('input', []);
        this.$emit('change', []);
      } else {
        this.$emit('input', null);
        this.$emit('change', null);
      }
    }
  },
};
</script>

<style scoped lang="less">
.dfdw-multiple-column-select {
  /* 设置表格可接收焦点 */
  .el-input {
    display: block;
    width: 100%;
    height: 100%;
    cursor: pointer;
    /deep/ .el-input__inner {
      height: var(--input-height, 28px);
      transition: height 0.3s;
    }
  }
  .dfdw-multiple-column-select-tag {
    position: absolute;
    line-height: normal;
    white-space: normal;
    z-index: 1;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 5px;
    width: calc(100% - 25px);

    .el-tag {
      margin: 2px 0 2px 6px;
      max-width: 100%;
      align-items: center;
      /deep/ .el-tag__close {
        background-color: #c0c4cc;
      }
    }
  }
}

.selectMore {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  outline: none;
  /deep/ .select-row {
    color: #4877fb !important; /* 设置选中行的字体颜色 */
  }
}
</style>
