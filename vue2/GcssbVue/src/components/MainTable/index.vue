<template>
  <el-table
      ref="tableRef"
      :data="tableData"
      :stripe="stripe"
      border
      highlight-current-row
      @row-click="handleCurrentChange"
      @row-dblclick="rowdblClick"
      @cell-dblclick="celldblClick"
      @selection-change="handleSelectionChange"
      :cell-style="cellStyle"
      :row-class-name="tableRowClassName"
      :span-method="spanMethod"
      style="width: 100%"
      :height="height"
      v-loading="loading"
      v-watermark="{label:watermark}"
      :rowKey="rowKey"
  >
    <el-table-column
        type="selection"
        width="50"
        :reserve-selection="true"
        v-if="needSelect">
    </el-table-column>
    <el-table-column v-if="needIndex" type="index" label="序号" width="50" align="center">
      <template slot-scope="scope">
        {{ (queryParam.pageNum - 1) * queryParam.pageSize + 1 + scope.$index }}
      </template>
    </el-table-column>
    <template v-for="(item, index) in tableOptions">
      <el-table-column
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :align="item.align || 'center'"
          :width="item.width || ''"
          :show-overflow-tooltip="item.tooltip || false"
          :sortable="item.sortable!==false"
          :formatter="item.formatDate || null">
        <template slot-scope="scope">
          <slot
              v-if="item.slot"
              :name="scope.column.property"
              :row="scope.row"
              :rowIndex="scope.$index">
          </slot>
          <span v-else>{{ scope.row[scope.column.property] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>
<script>
export default {
  name: 'index',
  props: {
    needIndex: {//序号显示
      type: Boolean,
      default: true
    },
    needSelect: {//多选显示
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableOptions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    queryParam: {
      type: Object,
      default: () => {
        return {
          pageNum: 1,
          pageSize: 10
        }
      }
    },
    stripe: {// 斑马纹
      type: Boolean,
      default: true
    },
    spanMethod: {// 合并行或列的计算方法
      type: Function,
      default: function () {}
    },
    cellStyle: {// 行、列、具体单元格样式
      type: Function,
      default: function () {}
    },
    tableRowClassName: {
      type: Function,
      default: function () {}
    },
    height: {
      type: String,
      default: '100%'
    },
    rowKey: {
      type: String,
      default: 'id'
    }
  },
  watch: {
    tableOptions: {
      handler (val) {
        if (val) {
          return val
        } else {
          return []
        }
      },
      deep: true,
      immediate: true
    },
    tableData: {
      handler (val) {
        if (val) {
          if (this.$refs.tableRef) {
            this.$refs.tableRef.setCurrentRow();
            if (!this.needSelect) {
              this.multipleSelection = [];
              this.$refs.tableRef.clearSelection();
            }
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  data () {
    return {
      multipleSelection: []
    }
  },
  methods: {
    //高亮
    setCurrent(row) {
      this.$refs.tableRef.setCurrentRow(row)
    },
    //选中数据
    handleCurrentChange(val) {
      if (val != null) {
        if (this.multipleSelection.length > 0) {
          // 如果结果数组不为空，判断所选的这条是否在结果数组里
          if (JSON.stringify(this.multipleSelection).indexOf(JSON.stringify(val)) == -1) {
            if (!this.needSelect) {
              this.multipleSelection = [];
              this.$refs.tableRef.clearSelection();
            }
            this.multipleSelection.push(val)
            this.$refs.tableRef.toggleRowSelection(val, true)
          } else {
            // 所选数据在结果数组里，那就要清除掉它，不然会造成数据重复
            this.multipleSelection.map((item, index) => {
              if (item.id == val.id) {
                this.multipleSelection.splice(index, 1)
                this.$refs.tableRef.toggleRowSelection(val, false)
              }
            })
          }
        } else {
          this.multipleSelection.push(val)
          this.$refs.tableRef.toggleRowSelection(val, true)
        }
        this.$emit('getCurrentData', val)
      }
    },
    //选中数据
    rowdblClick(val) {
      this.$emit('rowdblclick', val)
    },
    celldblClick(row, column, cell, event) {
       this.$emit('cell-dblclick', row, column, cell, event)
    },
    // 多选表事件
    handleSelectionChange (val) {
      this.$emit('getSelectionData', val)
    },
  }
}
</script>
<style lang="scss" scoped>

</style>
