<template>
  <div class="downloading">
    <div class="fileInfo">
      <span>下载进度：</span>
      <span class="fileName">{{ file.name }}</span>
      <span class="fileSize">{{ formatSize(file) }}</span>
      <span class="downloadSpeed">{{ file.downloadSpeed }}</span>
    </div>

    <div class="progress">
      <el-progress :percentage="file.downloadPersentage" :format="formatPercentage" style="width: 400px"></el-progress>
      <el-button icon="el-icon-video-pause" circle  @click="changeDownloadStop" v-if="!file.downloadingStop"></el-button>
      <el-button icon="el-icon-video-play" circle @click="changeDownloadStop" v-else></el-button>
    </div>
  </div>
</template>

<script>
import {downloadChunk} from "api/fileManage/file";

export default {
  name: "FileDownloadChunk",
  props: {
    fileCache: {
      type: Object,
      default() {
        return {}
      }
    },
    ywId: {
      type: Number,
      required: true
    }
  },
  watch: {
    fileCache: {
      handler (newVal) {
        if (newVal) {
          this.file = JSON.parse(JSON.stringify(newVal));
          this.downloadFile()
        }
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      file:{
        name: '',
        size: 0,
        downloadingStop: false,
        downloadSpeed: "0 M/s",
        downloadPersentage: 0,
        blobList: [],
        chunkList: [],
        path: ''
      }
    }
  },
  methods: {
    formatPercentage(percentage) {
      if (percentage === 0) {
        return '加载中';
      } else if (percentage === 100) {
        return '完成';
      } else {
        return `${percentage}%`;
      }
    },
    //换算文件的大小单位
    formatSize(file) {
      // console.log("size", file.size);
      let size = file.size;
      let unit;
      let units = [" B", " K", " M", " G"];
      let pointLength = 2;
      while ((unit = units.shift()) && size > 1024) {
        size = size / 1024;
      }
      return (
          (unit === "B" ? size : size.toFixed(pointLength === undefined ? 2 : pointLength)) + unit
      );
    },
    //点击暂停下载
    changeDownloadStop() {
      this.$set(this.file, "downloadingStop", !this.file.downloadingStop);
      console.log("暂停", this.file.downloadingStop);
      if (!this.file.downloadingStop) {
        console.log("开始。。");
        this.downloadChunk(1);
      }
    },
    //点击下载文件
    downloadFile() {
      this.$set(this.file, "downloadingStop", false);
      this.$set(this.file, "downloadSpeed", "0 b/s");
      this.$set(this.file, "downloadPersentage", 0);
      this.$set(this.file, "blobList", []);
      this.$set(this.file, "chunkList", []);
      this.downloadChunk(1);
    },
    //点击下载文件分片
    downloadChunk(index) {
      let chunkSize = 1024 * 1024;
      let chunkTotal = Math.ceil(this.file.size / chunkSize);

      if (index <= chunkTotal) {
        // console.log("下载进度",index);
        let exit = this.file.chunkList.includes(index);
        console.log("存在", exit);

        if (!exit) {
          if (!this.file.downloadingStop) {
            let formData = {
              ywId: this.ywId,
              chunkSize: chunkSize,
              chunkTotal: chunkTotal,
              index: index,
              fileName: this.file.name,
              filePath: this.file.path,
            };
            // if (index * chunkSize >= file.size) {
            //   chunkSize = file.size - (index - 1) * chunkSize;
            //   formData.set("chunkSize", chunkSize);
            // }

            let startTime = new Date().valueOf();

            downloadChunk(formData).then((res) => {
              let chunkList = this.file.chunkList;
              chunkList.push(index);
              this.$set(this.file, "chunkList", chunkList);
              let endTime = new Date().valueOf();
              let timeDif = (endTime - startTime) / 1000;
              let downloadSpeed = '0 b/s';
              if ((1 / timeDif) > 1) {
                downloadSpeed = (1 / timeDif).toFixed(1) + " M/s";
              } else if ((1024 / timeDif) > 1) {
                downloadSpeed = (1024 / timeDif).toFixed(1) + " kb/s";
              } else if ((1024 * 1024 / timeDif) > 1) {
                downloadSpeed = (1024 * 1024 / timeDif).toFixed(1) + " b/s";
              }
              this.$set(this.file, "downloadSpeed", downloadSpeed);
              //todo
              this.$set(this.file, "downloadPersentage", parseInt((index / chunkTotal) * 100));
              // let chunk = res.data.data.chunk
              // const blob = new Blob([res.data]);
              const blob = res;

              let blobList = this.file.blobList;
              blobList.push(blob);
              this.$set(this.file, "blobList", blobList);
              // console.log("res", blobList);
              if (index == chunkTotal) {
                this.$set(this.file, "downloadingStop", true);
                let resBlob = new Blob(this.file.blobList, {
                  type: "application/octet-stream",
                });
                // console.log("resb", resBlob);

                let url = window.URL.createObjectURL(resBlob); // 将获取的文件转化为blob格式
                let a = document.createElement("a"); // 此处向下是打开一个储存位置
                a.style.display = "none";
                a.href = url;
                // 下面两行是自己项目需要的处理，总之就是得到下载的文件名（加后缀）即可

                let fileName = this.file.name + '.pdf';

                a.setAttribute("download", fileName);
                document.body.appendChild(a);
                a.click(); //点击下载
                document.body.removeChild(a); // 下载完成移除元素
                window.URL.revokeObjectURL(url); // 释放掉blob对象
                this.$emit("downloadSuccess");
              }

              this.downloadChunk(index + 1);
            });
          }
        } else {
          this.$set(this.file, "downloadPersentage", parseInt((index / chunkTotal) * 100))
          this.downloadChunk(index + 1);
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.downloading {

  .fileInfo {
    display: flex;
    justify-content: space-between;
  }

  .progress {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>