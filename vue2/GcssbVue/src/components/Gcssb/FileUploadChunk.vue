<template>
  <div>
    <el-upload
        class="upload-demo"
        drag
        action="your-backend-api-url"
        :auto-upload="false"
        :on-change="handleFileChange"
        :data="{
          ywId: ywId
         }"
        :headers="uploadHeaders"
        :limit="limit"
        :accept="'.'+fileType.join(',.')"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>
    <div class="parse">
      <div class="span_jd">解析进度： </div>
      <el-progress :percentage="progressPercentage"></el-progress>
    </div>
    <div class="parse">
      <div class="span_jd1">上传进度： </div>
      <el-progress :percentage="uploadPercentage"></el-progress>
    </div>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getToken} from "@/utils/auth";
import {uploadChunk,uploadSplit} from '@/api/fileManage/file'
export default {
  name: "FileUploadChunk",
  props: {
    // 图片数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['pdf', 'xlsx', 'docx']
    fileType: {
      type: Array,
      default: () => ['pdf', 'xlsx', 'docx']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 上传接口
    uploadUrl: {
      type: String,
      default: ''
    },
    // 导入示例文件名
    downloadName: {
      type: String,
      default: ''
    },
    ywId: {
      type: Number,
      default: -1
    },
  },
  data() {
    return {
      file: null,
      uploadData: {},
      uploadHeaders: {'Authorization': getToken()},
      progressVisible: false,
      progressPercentage: 0,
      uploadPercentage:0,
      splitPercentage:0,
      splitProgressVisible:false,
      baseUrl: process.env.VUE_APP_BASE_API + '/dwGcssb',
      appUrl: '/app',
      token: Cookies.get('jtoken'),
      chunkInfo:{},
    };
  },
  methods: {
    handleFileChange(file) {
      console.log(file)
      this.file = file.raw;
      this.uploadFileInChunks();
    },
    async uploadFileInChunks() {

      this.progressVisible = false;
      this.progressPercentage = 0;
      this.uploadPercentage = 0;
      this.splitPercentage = 0;
      this.splitProgressVisible = false;



      const chunkSize = 1024 * 1024; // 每个分片大小，例如1MB
      let chunks = Math.ceil(this.file.size / chunkSize);
      let uploadedChunks = 0;
      //多循环一次  i =chunks 保存oss
      for (let i = 0; i <=chunks; i++) {
        let chunk = null;
        if(i != chunks){
          let start = i * chunkSize;
          let end = (i + 1) * chunkSize >= this.file.size ? this.file.size : (i + 1) * chunkSize;
           chunk = this.file.slice(start, end);
        }
        try {
          this.progressVisible = true;
          this.progressPercentage = parseInt((i/chunks)*100);
          await this.uploadChunk(chunk, i, chunks);
          uploadedChunks++;
        } catch (error) {
          console.error('上传分片错误:', error);
        }
      }

    },

    async uploadChunk(chunk, chunkIndex, totalChunks) {
      const formData = new FormData();
      formData.append('chunk', chunk);
      formData.append('fileName', this.file.name);
      formData.append('chunkIndex', chunkIndex);
      formData.append('totalChunks', totalChunks);
      formData.append('ywId', this.ywId);
      console.log("chunkIndex：" + chunkIndex + "totalChunks：" + totalChunks)
      await uploadChunk(formData).then(res => {
        if (res.code == 200) {
          let chunkInfo = res.result;
          if (chunkIndex == 0 && chunkInfo.hz == "pdf") {
            this.splitProgressVisible = true;
          }
          this.uploadPercentage = parseInt((chunkIndex / totalChunks) * 100);
          console.log(this.uploadPercentage)
          if (this.uploadPercentage > 99) {
            this.uploadPercentage = 100
          }

          //开始调取分页保存数据逻辑
          if (chunkIndex == totalChunks && res.code == 200) {
            this.chunkInfo = chunkInfo;
            this.uploadSplit()
            this.$emit('uploadSuccess', this.file);
          }
          console.log(res)
        }

      })
    },

   //pdf后端分页存储
    async uploadSplit(){
      const dataSplit = new FormData();
      dataSplit.append('ywId', this.ywId);
      dataSplit.append('hz', this.chunkInfo.hz);
      dataSplit.append('newName', this.chunkInfo.newName);
      dataSplit.append('fileNameChunk', this.chunkInfo.fileNameChunk);
      uploadSplit(dataSplit).then(  res => {

      })

    }
  },
};
</script>
