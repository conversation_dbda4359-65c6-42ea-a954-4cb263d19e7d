import request from '@/utils/request'

/**
 * 分片上传
 * @param params
 */
export function uploadChunk(param) {
    return request({
        url: '/dwGcssb/gcssb/File/uploadChunk',
        method: 'post',
        data: param,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
export function uploadSplit(param) {
    return request({
        url: '/dwGcssb/gcssb/File/uploadSplit',
        method: 'post',
        data: param
    })
}


/**
 * 检查文件是否存在
 * @param id
 */
export function check (param) {
    return request({
        url: '/dwGcssb/gcssb/File/check',
        method: 'get',
        params: param
    })
}

/**
 * 文件下载
 * @param data
 */
export function downloadChunk (data) {
    return request({
        url: '/dwGcssb/gcssb/File/downloadChunk',
        method: 'get',
        params: data,
        responseType: "blob"
    })
}


