import request from '@/utils/request'

/** 获取白名单数据 */
export function getList(params) {
    return request({
        url: '/dwGcssb/gcssb/whiteList/getList',
        method: 'post',
        data: params
    })
}

/**
 * 获取原有人员信息
 */
export function GetPersonEntityList(params) {
    return request({
        url: '/dwGcssb/gcssb/whiteList/getPersonEntityList',
        method: 'post',
        data: params
    })
}


/**
 * 白名单新增
 * @param params
 */
export function createWhiteList(params) {
    return request({
        url: '/dwGcssb/gcssb/whiteList/create',
        method: 'post',
        data: params
    })
}

/**
 * 白名单修改
 * @param params
 */
export function updateWhiteList(params) {
    return request({
        url: '/dwGcssb/gcssb/whiteList/update',
        method: 'put',
        data: params
    })
}

/**
 * 白名单删除
 * @param id
 */
export function deleteWhiteList (id) {
    return request({
        url: '/dwGcssb/gcssb/whiteList/delete?id=' + id,
        method: 'delete',
    })
}

