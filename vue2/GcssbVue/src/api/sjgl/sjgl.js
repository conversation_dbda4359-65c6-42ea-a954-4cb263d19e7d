import request from '@/utils/request'

/** 获取类别名称树 */
export function getMainNameListByIsEnable() {
    return request({
        url: '/dwGcssb/gcssb/main/getMainNameListByIsEnable',
        method: 'get',
    })
}


/** 根据CategoryId获取数据 */
export function getListByCategoryId(params) {
    return request({
        url: '/dwGcssb/gcssb/books/getListByCategoryId',
        method: 'post',
        data: params
    })
}

/**
 * 书籍新增
 * @param params
 */
export function createBooks(params) {
    return request({
        url: '/dwGcssb/gcssb/books/create',
        method: 'post',
        data: params
    })
}

/**
 * 书籍修改
 * @param params
 */
export function updateBooks(params) {
    return request({
        url: '/dwGcssb/gcssb/books/update',
        method: 'put',
        data: params
    })
}

/**
 * 书籍删除
 * @param id
 */
export function deleteBooks (id) {
    return request({
        url: '/dwGcssb/gcssb/books/delete?id=' + id,
        method: 'delete',
    })
}

/**
 * 章节新增
 * @param params
 */
export function createChapter(params) {
    return request({
        url: '/dwGcssb/gcssb/chapter/create',
        method: 'post',
        data: params
    })
}

/**
 * 章节删除
 * @param id
 */
export function deleteChapter (id) {
    return request({
        url: '/dwGcssb/gcssb/chapter/delete?id=' + id,
        method: 'delete',
    })
}

/**
 * 章节修改
 * @param params
 */
export function updateChapter(params) {
    return request({
        url: '/dwGcssb/gcssb/chapter/update',
        method: 'put',
        data: params
    })
}

/** 根据书籍获取章节数据 */
export function getListByBooksId(params) {
    return request({
        url: '/dwGcssb/gcssb/books/getListByBooksId',
        method: 'post',
        data: params
    })
}

/** 根据书籍获取章节数据 */
export function getAllList(params) {
    return request({
        url: '/dwGcssb/gcssb/chapter/getAllList',
        method: 'post',
        data: params
    })
}


/**
 * 导入章节excel
 */
export function exportExcel(params, booksId) {
    return request({
        url: '/dwGcssb/gcssb/chapter/importExcel/' + booksId,
        method: 'post',
        data: params,
        headers: {"Content-Type": "multipart/form-data"}
    })
}

/** 导入章节excel模板 */
export function exportExcelTemplate() {
    return request({
        url: '/dwGcssb/gcssb/chapter/importExcelTemplate',
        method: 'post',
        responseType: 'blob'
    })
}

/**
 * 导入书籍excel
 */
export function exportExcelBooks(params, categoryId) {
    return request({
        url: '/dwGcssb/gcssb/books/importExcelBooks/' + categoryId,
        method: 'post',
        data: params,
        headers: {"Content-Type": "multipart/form-data"}
    })
}

/** 导入书籍excel模板 */
export function exportExcelTemplateBooks() {
    return request({
        url: '/dwGcssb/gcssb/books/importExcelTemplateBooks',
        method: 'post',
        responseType: 'blob'
    })
}

/** 根据id获取书籍视图 */
export function getVBooksById(params) {
    return request({
        url: '/dwGcssb/gcssb/books/getVBooksById',
        method: 'post',
        data: params
    })
}

