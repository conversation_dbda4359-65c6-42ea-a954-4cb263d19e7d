import request from '@/utils/request'

/**
 * 日志新增
 * @param params
 */
export function createDownload(params) {
    return request({
        url: '/dwGcssb/gcssb/download/create',
        method: 'post',
        data: params
    })
}

/**
 * 日志修改
 * @param params
 */
export function updateDownload(params) {
    return request({
        url: '/dwGcssb/gcssb/download/update',
        method: 'put',
        data: params
    })
}

/**
 * 日志删除
 * @param id
 */
export function deleteDownload(id) {
    return request({
        url: '/dwGcssb/gcssb/download/delete?id=' + id,
        method: 'delete',
    })
}

/**
 * 获取下载日志据page
 * @param params
 */
export function getListPage(params) {
    return request({
        url: '/dwGcssb/gcssb/download/getListPage',
        method: 'post',
        data: params
    })
}


/**
 * 日志导出
 * @param params
 * @returns {*}
 */
export function exportExcel(params) {
    return request({
        url: '/dwGcssb/gcssb/download/export',
        method: 'post',
        data: params,
        contentType: 'application/json',
        responseType: 'blob'
    })
}

/**
 * 获取书籍信息
 */
export function getBooksPageList(params) {
    return request({
        url: '/dwGcssb/gcssb/books/getBooksPageList',
        method: 'post',
        data: params
    })
}
