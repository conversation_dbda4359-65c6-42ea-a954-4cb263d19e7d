import request from '@/utils/request'

/**
 * 类别新增
 * @param params
 */
export function createCategory(params) {
    return request({
        url: '/dwGcssb/gcssb/main/create',
        method: 'post',
        data: params
    })
}

/**
 * 类别删除
 * @param id
 */
export function deleteMain(id) {
    return request({
        url: '/dwGcssb/gcssb/main/delete?id=' + id,
        method: 'delete',
    })
}

/**
 * 类别修改
 * @param params
 */
export function updateMation(params) {
    return request({
        url: '/dwGcssb/gcssb/main/update',
        method: 'put',
        data: params
    })
}

/** 获取类别名称树 */
export function getMainNameList() {
    return request({
        url: '/dwGcssb/gcssb/main/getMainNameList',
        method: 'get',
    })
}

/** 获取一级类别名称树 */
export function getMainFirstNameList() {
    return request({
        url: '/dwGcssb/gcssb/main/getMainFirstNameList',
        method: 'get',
    })
}


/** 根据权限获取类别名称树 */
export function getMainListByPermission() {
    return request({
        url: '/dwGcssb/gcssb/main/getMainListByPermission',
        method: 'get',
    })
}

/** 根据父id获取数据 */
export function getListByParentId(params) {
    return request({
        url: '/dwGcssb/gcssb/main/getListByParentId',
        method: 'post',
        data: params
    })
}
export function updCategoryFile(param) {
    return request({
        url: '/dwGcssb/gcssb/main/updCategoryFile',
        method: 'post',
        data: param,
    })
}

/**
 * 获取图片路径
 * @param ywId 类别id
 */
export function getImage (ywId) {
    return request({
        url: `/dwGcssb/gcssb/main/image?id=${ywId}`,
        method: 'get'
    })
}

/**
 * 判断用户是否包含白名单编辑权限
 */
export function isEditPermission () {
    return request({
        url: `/dwGcssb/gcssb/whiteList/isEditPermission`,
        method: 'get'
    })
}

