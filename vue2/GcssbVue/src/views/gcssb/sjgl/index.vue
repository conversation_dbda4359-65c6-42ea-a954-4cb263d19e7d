<template>

  <div class="main" ref="main">
    <div class="list-box" style="width: 20%">
      <div class="operate-pannel">
        <div>
          <el-tree
              :data="data"
              ref="tree"
              :props="defaultProps"
              node-key="id"
              @node-click="handleNodeClick"
              :expand-on-click-node="false"
              default-expand-all
              :highlight-current="true"/>
        </div>
      </div>
    </div>
    <div class="process-info" style="width: 79%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">

          <div class="search-box">
            <span class="font-size14">书籍名称：</span>
            <el-input v-model="queryParams.name" clearable placeholder="请输入书籍名称"
                      @change="byParentId()"></el-input>
            <el-button size="small" type="text" icon="el-icon-search" v-has-permi="['JDWGB01SJ01QX01']"
                       @click="byParentId()">查询
            </el-button>
            <el-button size="small" type="text" icon="el-icon-refresh" v-has-permi="['JDWGB01SJ01QX01']"
                       @click="resetQuery()">重置
            </el-button>
            <el-button size="small" type="text" icon="el-icon-plus" v-has-permi="['JDWGB01SJ01QX02']"
                       @click="addCategory()" v-if="permissionButton">新增
            </el-button>
            <el-button size="small" type="text" icon="el-icon-edit" v-has-permi="['JDWGB01SJ01QX03']"
                       @click="updateCategory()" v-if="permissionButton">修改
            </el-button>
            <el-button size="small" type="text" icon="el-icon-delete" v-has-permi="['JDWGB01SJ01QX04']"
                       @click="deleteCategory()" v-if="permissionButton">删除
            </el-button>
            <el-button size="small" type="text" icon="el-icon-upload" v-has-permi="['JDWGB01SJ01QX05']"
                       @click="uploadBooks()" v-if="permissionButton">上传书籍
            </el-button>
            <el-button size="small" type="text" icon="el-icon-download" v-has-permi="['JDWGB01SJ01QX06']"
                       @click="downBooks()" v-if="permissionButton">下载书籍
            </el-button>
            <el-button size="small" type="text" icon="el-icon-download" @click="importExcelTemplateBooks()"
                       v-if="permissionButton">下载导入模版
            </el-button>
            <el-button size="small" type="text" icon="el-icon-upload2" @click="importExcelBooks()"
                       v-if="permissionButton">导入书籍
            </el-button>
            <el-button size="small" type="text" icon="el-icon-tickets" v-has-permi="['JDWGB01SJ01QX07']"
                       @click="chapterMaintain()" v-if="permissionButton">章节维护
            </el-button>
          </div>
        </div>
        <div class="table-box">
          <Table
              :tableData="parentIdList"
              :tableOptions="options"
              :loading="parentIdLoading"
              @getCurrentData="parentIdSelect"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <template slot="totalPages" slot-scope="scope">
              {{ showTotalPages(scope.row.totalPages) }}
            </template>
          </Table>
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="queryParams.pageNum"
              :page-size="queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="queryParams.total"
          />
        </div>
      </div>

      <!-- 新增修改对话框-->
      <el-dialog :title=tableName :visible.sync="tableVisible" width="600px" :before-close="handleClose">
        <el-form label-width="80px" :model="categoryAddForm" :rules="Rules" ref="addForm">
          <el-form-item label="上级类别" prop="categoryId">
            <treeselect v-model="categoryAddForm.categoryId" :options="data" :show-count="true"
                        :normalizer="normalizeCategory"
                        placeholder="选择上级类别" style='width: 400px'/>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="categoryAddForm.name" style='width: 400px'></el-input>
          </el-form-item>
          <el-form-item label="前置页码" prop="advancePageNum">
            <el-input v-model="categoryAddForm.advancePageNum" style='width: 400px'></el-input>
          </el-form-item>
          <el-form-item label="是否启用" prop="isEnable">
            <el-select v-model="categoryAddForm.isEnable" placeholder="请选择状态" style='width: 400px'>
              <el-option label="启用" value="1"></el-option>
              <el-option label="不启用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否可见" prop="isVisible">
            <el-select v-model="categoryAddForm.isVisible" placeholder="请选择状态" style='width: 400px ;'>
              <el-option label="可见" value="1"></el-option>
              <el-option label="不可见" value="0"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" autosize placeholder="请输入内容" v-model="categoryAddForm.remark"
                      style='width: 400px'></el-input>
            <!--            <el-input v-model="categoryAddForm.remark"></el-input>-->
          </el-form-item>
          <el-form-item size="large">
            <el-button type="primary" @click="onSubmit('addForm')">确定</el-button>
            <el-button @click="tableVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <!--章节维护   -->
      <el-dialog :title=chapterMaintainName :visible.sync="chapterVisible" width="60%"
                 :before-close="chapterHandleClose">
        <el-form :model="chapterQueryParams" ref="chapterTableForm" size="small" :inline="true">
          <el-form-item label="章节名称" prop="name">
            <el-input v-model="chapterQueryParams.name" placeholder="请输入章节名称" clearable @change="chapterQuery"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="chapterQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="chapterRest">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="chapterAdd(1)">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-sort" size="mini" @click="toggleExpandAll">展开/折叠</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="importExcelTemplate">
              下载Excel模版
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="importExcel">导入Excel
            </el-button>
          </el-col>
        </el-row>
        <br>

        <el-table v-if="refreshTable" v-loading="chapterLoading" :data="chapterList" row-key="id"
                  :default-expand-all="treeTableShowAll" height="500"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column prop="chapterNumber" label="章节编号" width="200">
            <template slot-scope="{ row }">
              <span v-if="(row.children ==undefined || row.children ==null) && row.parentId ==0"
                    style="margin-left: 23px;">{{ row.chapterNumber }}</span>
              <span v-else>{{ row.chapterNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="章节名称" width="200" align="center"></el-table-column>
          <el-table-column prop="pageNum" label="章节所在页码" align="center" width="200"/>
          <el-table-column prop="sort" label="排序" align="center" width="100"></el-table-column>
          <el-table-column prop="remark" label="备注" align="center" width="200"/>
          <el-table-column label="操作" align="center" width="200">
            <template v-slot="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-plus" @click="chapterAdd(scope.row)">新增</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

      </el-dialog>

      <!-- 章节新增/修改-->
      <el-dialog :title=chapterAddName :visible.sync="chapterAddVisible" width="700px"
                 :before-close="chapterAddHandleClose">
        <el-form label-width="110px" :model="chapterAddForm" :rules="chapterAddRules" ref="chapterAddForm">
          <el-form-item label="上级章节" prop="parentId">
            <treeselect v-model="chapterAddForm.parentId" :normalizer="normalizeOptions" noChildrenText="没有子选项"
                        noOptionsText="没有可选项"
                        :options="chapterListSelect" :show-count="true" placeholder="选择上级章节"
                        style='width: 500px'/>
          </el-form-item>
          <el-form-item label="章节编号" prop="chapterNumber">
            <el-input v-model="chapterAddForm.chapterNumber" style='width: 500px'></el-input>
          </el-form-item>
          <el-form-item label="章节名称" prop="name">
            <el-input v-model="chapterAddForm.name" style='width: 500px'></el-input>
          </el-form-item>
          <el-form-item label="所在章节页码" prop="pageNum">
            <el-input v-model="chapterAddForm.pageNum" style='width: 500px'></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="chapterAddForm.sort" :min="0" label="排序"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" autosize placeholder="请输入内容" v-model="chapterAddForm.remark"
                      style='width: 500px'></el-input>
          </el-form-item>
          <el-form-item size="large">
            <el-button type="primary" @click="chapterAddSubmit('chapterAddForm')">确定</el-button>
            <el-button @click="chapterAddVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <el-dialog
          width="450px"
          :visible="uploadDialog"
          size="tiny"
          center
          :destroy-on-close="true"
          :modal-append-to-body="false"
          @close="closeUploadDialog"
          :close-on-click-modal="false"
      >
        <div slot="title" align="left" class="dialogTitle">文件上传</div>
        <!--        <FIleUpload-->
        <!--            :file-size="20"-->
        <!--            :file-type="['pdf','dox','docx','xls','xlsx']"-->
        <!--            :yw-id = ywId-->
        <!--            upload-url="/gcssb/File/upload"-->
        <!--            @uploadSuccessData="uploadSuccessData"-->
        <!--            @uploadErrorData="uploadErrorData"-->
        <!--        />-->
        <FileUploadChunk
            :file-size="20"
            :file-type="['pdf','dox','docx','xls','xlsx']"
            :yw-id=ywId
            :limit="1"
            @uploadSuccess="uploadSuccess"
        />
        <span slot="footer" class="dialog-footer">
           <el-button @click="closeUploadDialog">关 闭</el-button>
          <!--           <el-button type="primary" @click="closeUploadDialog = false">确 定</el-button>-->
       </span>
      </el-dialog>

      <div v-show="false">
        <el-upload
            ref="fileExcelUpload"
            accept=".xls,.xlsx"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :auto-upload="true"
            action=""
            :beforeUpload="beforeUpload"
            :http-request="uploadHttpRequest">
          <el-button type="text" size="mini" class="uploac-button">
            导入excel
          </el-button>
        </el-upload>
      </div>

      <div v-show="false">
        <el-upload
            ref="fileExcelBooksUpload"
            accept=".xls,.xlsx"
            name="file"
            :showUploadList="false"
            :multiple="false"
            :auto-upload="true"
            action=""
            :beforeUpload="beforeUpload"
            :http-request="uploadHttpBooksRequest">
          <el-button type="text" size="mini" class="uploac-button">
            导入excel
          </el-button>
        </el-upload>
      </div>

      <el-dialog
          width="500px"
          :visible="downloadDialog"
          size="tiny"
          center
          :destroy-on-close="false"
          :modal-append-to-body="false"
          @close="closeDownloadDialog"
          :close-on-click-modal="false"
      >
        <div slot="title" align="left" class="dialogTitle">文件下载</div>
        <file-download-chunk
            :yw-id="ywId"
            :file-cache="{name: vBooks.name, size: vBooks.fileSize, path: vBooks.filePath}"
            v-if="downloadDialog"
            @downloadSuccess="closeDownloadDialog"
        />
      </el-dialog>
    </div>
  </div>
</template>


<script>
import Table from '@/components/MainTable'
import {createCategory, getListByParentId, isEditPermission, updateMation} from '@/api/lbgl/lbgl'
import {
  getMainNameListByIsEnable,
  createBooks,
  updateBooks,
  getListByCategoryId,
  getAllList,
  createChapter,
  updateChapter,
  exportExcelTemplate,
  exportExcel,
  getVBooksById,
  deleteBooks,
  deleteChapter,
  exportExcelTemplateBooks,
  exportExcelBooks
} from '@/api/sjgl/sjgl'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FIleUpload from "@/components/Gcssb/FIleUpload.vue";
import FileUploadChunk from "@/components/Gcssb/FileUploadChunk.vue";
import FileDownloadChunk from "components/Gcssb/FileDownloadChunk.vue";
import {downLoad, getFileNameTime} from "@/utils/tool";

export default {
  components: {Table, Treeselect, FIleUpload, FileUploadChunk, FileDownloadChunk},
  data() {
    return {
      /**章节数据 */
      showSearch: true,
      chapterVisible: false,
      chapterList: [],
      refreshTable: true,
      treeTableShowAll: true,
      chapterOptions: [
        {label: '名称', prop: 'name'},
        {label: '章节所在页码', prop: 'advancePageNum'},
        {label: '排序', prop: 'sort'},
        {label: '备注', prop: 'remark'},
        {label: '操作', prop: 'remark'},
      ],
      //章节维护查询参数
      chapterQueryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        name: null,
      },
      chapterListoOriginal: [],
      chapterLoading: false,
      chapterAddVisible: false,
      chapterAddForm: {
        id: null,
        parentId: null,
        pageNum: null,
        name: null,
        sort: null,
        remark: '',
        chapterNumber: null
      },
      chapterAddName: '',
      chapterMaintainName: '',
      chapterListSelect: [],
      /**书籍数据 */
      tableName: '',
      tableVisible: false,
      parentIdLoading: false,
      categoryAddForm: {
        categoryId: null,
        name: null,
        isEnable: '1',
        isVisible: '1',
        advancePageNum: null,
        remark: null
      },
      treeNodeClickData: {},
      parentIdSelectData: {},
      parentIdList: [],
      deptList: [],
      deptIds: [],
      options: [
        {label: '名称', prop: 'name'},
        {label: '类别名称', prop: 'categoryText'},
        {label: '前置页码', prop: 'advancePageNum'},
        {label: '是否启用', prop: 'isEnableText'},
        {label: '是否可见', prop: 'isVisibleText'},
        {label: '备注', prop: 'remark'},
        {label: '总页数', prop: 'totalPages', slot: true}
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        name: null,
        categoryId: null
      },
      Rules: {
        categoryId: [{required: true, message: '请输入上级类别', trigger: 'submit'}],
        name: [{required: true, message: '请输入书籍名称', trigger: 'blur'}],
        isVisible: [{required: true, message: '请选择是否可见', trigger: 'change'}],
        isEnable: [{required: true, message: '请选择是否启用', trigger: 'change'}],
        advancePageNum: [{required: true, message: '请输入前置页码', trigger: 'blur'}],
      },

      chapterAddRules: {
        parentId: [{required: true, message: '请输入上级章节', trigger: 'submit'}],
        chapterNumber: [{required: true, message: '请输入章节编号', trigger: 'blur'}],
        name: [{required: true, message: '请输入章节名称', trigger: 'blur'}],
        sort: [{required: true, message: '请输入排序', trigger: 'change'}],
        pageNum: [{required: true, message: '请输入章节所在页码', trigger: 'blur'}],
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      uploadDialog: false,
      ywId: 0,
      vBooks: {
        filePath: '',
        name: '',
        fileSize: 0
      },
      downloadDialog: false,
      permissionButton: false,
    };
  },
  created() {
    this.getListName()
    //判断用户是否包含白名单编辑权限
    this.getPermission()
  },
  methods: {
    //获取类别名称
    getListName() {
      // this.data.splice(0)
      getMainNameListByIsEnable().then(res => {
        // console.log(res.result)
        this.data = res.result
        this.$forceUpdate();
      })
    },
    //树点击行
    handleNodeClick(data) {
      this.treeNodeClickData = data
      // console.log(data)
      this.getParentList()
    },
    //获取第一页右侧书籍数据
    getParentList() {
      this.queryParams.pageNum = 1
      this.byParentId()
    },
    //获取右侧书籍数据
    byParentId() {
      this.queryParams.categoryId = this.treeNodeClickData.id
      getListByCategoryId(this.queryParams).then(res => {
        this.queryParams.total = res.result.total
        res.result.records.forEach(item => {
          item.isEnable = item.isEnable + ""
          item.isVisible = item.isVisible + ""
        })
        this.parentIdList = res.result.records
      })
    },
    //章节查询
    chapterQuery() {
      this.chapterQueryParams.booksId = this.parentIdSelectData.id
      getAllList(this.chapterQueryParams).then(res => {
        // console.log(res.result)
        this.chapterListoOriginal = res.result
        //集合转化为树
        this.chapterList = this.handleTree(res.result, "id");
        this.chapterListSelect = JSON.parse(JSON.stringify(this.chapterList))
        this.chapterListSelect.push({name: "顶级", id: 0})
      })
    },
    //章节新增
    chapterAdd(row) {
      this.chapterAddName = '章节新增'
      this.chapterAddForm.booksId = this.parentIdSelectData.id
      //row=1 最上层新增 ,else行数据新增
      if (row == 1) {
        this.chapterAddForm.parentId = 0
        const filteredItems = this.chapterListoOriginal.filter(item => item.parentId === 0);
        let nextNumber = filteredItems.length;
        this.chapterAddForm.chapterNumber = nextNumber + 1
      } else {
        const filteredItems = this.chapterListoOriginal.filter(item => item.parentId === row.id);
        let nextNumber = filteredItems.length + 1;
        this.chapterAddForm.chapterNumber = row.chapterNumber + '.' + nextNumber
        this.chapterAddForm.parentId = row.id
      }
      this.chapterAddVisible = true
    },
    handleUpdate(row) {
      this.chapterAddName = '章节修改'
      this.chapterAddForm = row
      this.chapterAddVisible = true
    },
    handleDelete(row) {
      deleteChapter(row.id).then(res => {
        this.chapterQuery()
        this.$message({
          type: 'success',
          message: '删除成功！'
        })
      })
    },
    //章节新增确认
    chapterAddSubmit(formName) {
      const INT_MIN = -Math.pow(2, 31); // 最小值
      const INT_MAX = Math.pow(2, 31) - 1; // 最大值
      if (this.chapterAddForm.pageNum) {
        if (!/^\d+$/.test(this.chapterAddForm.pageNum)) {
          this.$message.error('章节所在页码必须是数字');
          return;
        }
        let num = parseInt(this.chapterAddForm.pageNum); // 确保转换为整数
        if (num < INT_MIN || num > INT_MAX) {
          // 超出整数类型范围的处理逻辑
          this.$message.error('章节所在页码超出整数类型范围');
          return;
        }
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.chapterAddForm.id == null || this.chapterAddForm.id == "") {
            createChapter(this.chapterAddForm).then(res => {
              this.chapterQuery()
              this.chapterAddVisible = false
              this.resetForm('chapterAddForm')
              this.plushData()
              this.$message({
                type: 'success',
                message: '新增成功！'
              })
            })
          } else {
            updateChapter(this.chapterAddForm).then(res => {
              this.chapterAddVisible = false
              this.chapterQuery()
              this.resetForm('chapterAddForm')
              this.$message({
                type: 'success',
                message: '修改成功！'
              })
            })
          }
        } else {
          return false;
        }
      });

    },
    //新增
    addCategory() {
      this.tableName = '新增书籍'
      //类别id
      this.categoryAddForm.categoryId = this.treeNodeClickData.id
      this.tableVisible = !this.tableVisible;
    },
    updateCategory() {
      this.tableName = '修改书籍'
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择书籍！'
        })
      } else {
        this.categoryAddForm = JSON.parse(JSON.stringify(this.parentIdSelectData))
        this.tableVisible = !this.tableVisible;
      }
    },
    //新增修改确认
    onSubmit(formName) {
      if (!/^\d+$/.test(this.categoryAddForm.advancePageNum)) {
        this.$message.error('前置页码必须是数字');
        return;
      }
      //校验必填项
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.categoryAddForm.id == undefined || this.categoryAddForm.id == null || this.categoryAddForm.id == "") {
            createBooks(this.categoryAddForm).then(response => {
              if (response.result == null || response.result == undefined) {
                this.resetForm('categoryAddForm');
                this.tableVisible = false
                this.$message({
                  type: 'warning',
                  message: '新增失败！'
                })
              } else {
                // response.result
                this.tableVisible = false
                this.resetForm('categoryAddForm');
                this.getListName()
                if (this.treeNodeClickData.id != undefined && this.treeNodeClickData.id != "") {
                  this.byParentId()
                }
                this.$message({
                  type: 'success',
                  message: '新增成功！'
                })
              }
            });
          } else {
            updateBooks(this.categoryAddForm).then(response => {
              this.parentIdSelectData.id = null
              this.tableVisible = false
              this.resetForm('categoryAddForm');
              this.getListName()
              this.byParentId()
              this.$message({
                type: 'success',
                message: '修改成功！'
              })
            })
          }
        } else {
          return false;
        }
      });

    },
    //删除
    deleteCategory() {
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择书籍！'
        })
      } else {
        this.$confirm('此操作将删除书籍名称:' + this.parentIdSelectData.name + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteBooks(this.parentIdSelectData.id).then(res => {
            this.getListName()
            if (this.treeNodeClickData.id) {
              this.getParentList()
            }
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        })
      }
    },
    //章节维护
    chapterMaintain() {
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择书籍！'
        })
      } else {
        this.chapterMaintainName = "《" + this.parentIdSelectData.name + "》-章节管理"
        this.chapterVisible = true
        this.chapterQuery()
      }
    },
    //章节导入
    importExcel() {
      this.$refs.fileExcelUpload.$el.querySelector('.uploac-button').click();
    },
    //章节导入excel
    beforeUpload(file) {
      // 获取上传excel文件的信息
      const fileContent = file.raw;
      // 获取文件类型
      const types = file.name.split(".")[1];
      const fileType = ["xlsx", "xls"].some(
          (item) => item === types
      );
      if (!fileContent) {
        if (!fileType) {
          alert("格式错误！请重新选择");
          return;
        }
      }
    },
    uploadHttpRequest(item) {
      const form = new FormData()
      form.append('file', item.file)
      exportExcel(form, this.parentIdSelectData.id).then(res => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.chapterQuery();
        } else {
          this.$message.error(res.message)
        }
        this.$refs.fileExcelUpload.clearFiles();//清空原来上传的文件
      }).catch(err => {
        this.$refs.fileExcelUpload.clearFiles();//清空原来上传的文件
      })
    },
    //章节模版
    importExcelTemplate() {
      exportExcelTemplate().then((res) => {
        let fileName = '章节导入模板' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      })
    },
    //书籍导入
    uploadHttpBooksRequest(item) {
      const form = new FormData()
      form.append('file', item.file)
      exportExcelBooks(form, this.treeNodeClickData.id).then(res => {
        if (res.code == 200) {
          this.$message.success(res.message)
          this.byParentId();
        } else {
          this.$message.error(res.message)
        }
        this.$refs.fileExcelUpload.clearFiles();//清空原来上传的文件
      }).catch(err => {
        this.$refs.fileExcelUpload.clearFiles();//清空原来上传的文件
      })
    },
    //章节导入
    importExcelBooks() {
      this.$refs.fileExcelBooksUpload.$el.querySelector('.uploac-button').click();
    },
    //书籍模版
    importExcelTemplateBooks() {
      exportExcelTemplateBooks().then((res) => {
        let fileName = '书籍导入模板' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      })
    },
    /** 单击表事件 */
    parentIdSelect(row) {
      this.parentIdSelectData = row
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.byParentId()
    },
    //每页行数
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.byParentId()
    },
    //章节分页查询
    // chapterHandleCurrentChange(val) {
    //   this.queryParams.pageNum = val
    //   this.chapterQuery()
    // },
    //章节新增弹窗关闭
    chapterAddHandleClose() {
      this.resetForm('chapterAddForm');
      this.chapterQuery()
      this.chapterAddVisible = false
    },
    //书籍重置
    resetQuery() {
      this.queryParams = {pageNum: 1, pageSize: 10, total: 0, name: null, categoryId: null}
      this.byParentId()
    },
    //章节重置
    chapterRest() {
      this.chapterQueryParams = {pageNum: 1, pageSize: 10, total: 0, name: null}
      this.chapterQuery()
    },
    /*弹窗关闭*/
    handleClose() {
      this.tableVisible = false
      this.resetForm('categoryAddForm');
    },
    //章节维护弹窗关闭
    chapterHandleClose() {
      //重置书籍选择
      this.parentIdSelectData = {}
      this.chapterVisible = false
    },
    //章节搜索条件展开/关闭
    toggleExpandAll() {
      this.refreshTable = false;
      this.treeTableShowAll = !this.treeTableShowAll
      this.$nextTick(() => {
        this.refreshTable = true;
      });
      // console.log(this.treeTableShowAll)
    },
    //章节搜索条件展开/关闭
    plushData() {
      this.refreshTable = false;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    uploadBooks() {
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选中一条数据！'
        })
      } else {
        this.ywId = this.parentIdSelectData.id
        this.uploadDialog = true
      }

    },
    uploadSuccessData() {
      this.$message({
        type: 'success',
        message: '上传成功'
      })
      this.closeUploadDialog()
    },
    uploadErrorData(message) {
      this.$message({
        type: 'error',
        message: '上传失败：' + message
      })
      this.closeUploadDialog()
    },
    closeUploadDialog() {
      this.byParentId()
      this.uploadDialog = false
    },
    handleTree(data, id, parentId, children, rootId) {
      id = id || 'id'
      parentId = parentId || 'parentId'
      children = children || 'children'
      rootId = rootId || Math.min.apply(Math, data.map(item => {
        return item[parentId]
      })) || 0
      //对源数据深度克隆
      const cloneData = JSON.parse(JSON.stringify(data))
      //循环所有项
      const treeData = cloneData.filter(father => {
        let branchArr = cloneData.filter(child => {
          //返回每一项的子级数组
          return father[id] === child[parentId]
        });
        branchArr.length > 0 ? father[children] = branchArr : '';
        //返回第一层
        return father[parentId] === rootId;
      });
      return treeData !== '' ? treeData : data;
    },
    // 规范化选项数据的方法
    normalizeOptions(node) {
      if (node.children && !node.children.length) {
        // 去掉children=[]的children属性
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    // 规范化选项数据的方法
    normalizeCategory(node) {
      if (node.children && !node.children.length) {
        // 去掉children=[]的children属性
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    showTotalPages(val) {
      if (val != null && val != "") {
        if (val == -1) {
          return '解析中，请稍后查看';
        }
      }
      return val;
    },
    uploadSuccess(file) {
      updateBooks({id: this.ywId, fileSize: file.size})
    },
    downBooks() {
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择书籍！'
        })
      } else {
        this.ywId = this.parentIdSelectData.id
        getVBooksById({id: this.ywId}).then(res => {
          this.vBooks = res.result
          if (this.vBooks.fileId) {
            this.downloadDialog = true
          } else {
            this.$message({
              type: 'warning',
              message: '书籍未上传！'
            })
          }
        })
      }
    },
    closeDownloadDialog() {
      this.downloadDialog = false
    },
    //判断用户是否包含白名单编辑权限
    getPermission() {
      isEditPermission().then(res => {
        this.permissionButton = res.result
      })
    },
    resetForm(formName) {
      for (var key in this[formName]) {
        if (key == 'isVisible' || key == 'isEnable') {
          this[formName][key] = '1'
        } else {
          this[formName][key] = ''
        }
      }
    },
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
