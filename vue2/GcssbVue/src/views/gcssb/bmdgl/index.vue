<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <!-- 搜索工作栏 -->
        <span class="font-size14">用户名：</span>
        <el-input v-model="queryParams.userName" placeholder="请输入用户名" style="width: 240px" clearable
                  @change="handleChange"/>
        <span class="font-size14">编辑标识：</span>
        <el-select v-model="queryParams.editTag" clearable placeholder="请选择" style="width: 240px">
          <el-option
              v-for="item in whitelistTagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click.native="handleChange">
          </el-option>
        </el-select>
        <span class="font-size14">白名单标识：</span>
        <el-select v-model="queryParams.whitelistTag" clearable placeholder="请选择" style="width: 240px">
          <el-option
              v-for="item in whitelistTagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click.native="handleChange">
          </el-option>
        </el-select>
        <!-- 搜索/重置 -->
        <el-button size="medium" type="text" icon="el-icon-search" v-has-permi="['JDWGB01BM01QX01']"
                   @click="handleQuery">查询
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-refresh" v-has-permi="['JDWGB01BM01QX01']"
                   @click="resetQuery">重置
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-plus" v-has-permi="['JDWGB01BM01QX02']"
                   @click="whiteListAdd">新增
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-edit" v-has-permi="['JDWGB01BM01QX03']"
                   @click="whiteListUpdate">修改
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-error" v-has-permi="['JDWGB01BM01QX04']"
                   @click="whiteListDelete">删除
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="dataList"
          :tableOptions="dataListOptions"
          :loading="dataListLoading"
          @getCurrentData="dataListSelect"
          style="height: 75vh"
      >
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <!-- 新增修改对话框-->
    <el-dialog :title=tableName :visible.sync="tableVisible" width="700px" :before-close="handleClose">
      <el-form label-width="100px" :model="whiteListForm" :rules="Rules" ref="addForm">
        <el-form-item label="用户名称" prop="userName">
          <el-input v-model="whiteListForm.userName" disabled style='width: 500px'>&nbsp;</el-input>
          <el-button type="success" @click="getUserName()">获取</el-button>
        </el-form-item>
        <el-form-item label="白名单标识" prop="whitelistTag">
          <el-select v-model="whiteListForm.whitelistTag" placeholder="请选择状态" style='width: 560px ;'>
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="白名单权限" prop="whitelistPermissionList" v-if="whiteListForm.whitelistTag==1">
          <treeselect :multiple="true" :options="categoryDataList" placeholder="请选择白名单权限"
                      :auto-select-descendants="true" :auto-deselect-descendants="true"
                      :flat="true" :normalizer="normalizeOptions"
                      v-model="whiteListForm.whitelistPermissionList"/>
        </el-form-item>
        <el-form-item label="编辑标识" prop="editTag">
          <el-select v-model="whiteListForm.editTag" placeholder="请选择状态" style='width: 560px ;'>
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="编辑权限" prop="editPermissionList" v-if="whiteListForm.editTag==1">
          <treeselect v-model="whiteListForm.editPermissionList" :options="categoryDataEditList" :multiple="true"
                      :disable-branch-nodes="false" :auto-select-descendants="true"
                      :auto-deselect-descendants="true" :normalizer="normalizeOptions"
                      :clear-on-select="true" placeholder="请选择"/>
        </el-form-item>
        <el-form-item size="large">
          <el-button type="primary" @click="onSubmit('addForm')">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!--   选择人员 -->
    <el-dialog title="人员信息" :visible.sync="dialogTableVisible">
      <addPersionTable ref="persionTable" @handleSubmit="handleSubmit" @handleClose="dialogTableVisible = false"/>
    </el-dialog>
  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import Treeselect from "@riophae/vue-treeselect";
import {getMainNameList, getMainFirstNameList} from '@/api/lbgl/lbgl';
import {getList, createWhiteList, updateWhiteList, deleteWhiteList} from '@/api/bmdgl/bmdgl'
import addPersionTable from "./components/addPersionTable.vue";
import Pagination from "components/Pagination";

export default {
  components: {Pagination, addPersionTable, Table, Treeselect},
  data() {
    return {
      tableName: '',
      tableVisible: false,
      dialogTableVisible: false,
      whiteListForm: {
        userName: null,
        userId: null,
        whitelistTag: null,
        editTag: '',
        whitelistPermissionList: [],
        editPermissionList: []
      },
      dataListLoading: false,
      dataClick: {},
      dataList: [],
      categoryDataList: [],
      categoryDataEditList: [],
      whitelistTagOptions: [
        {value: 1, label: '是'},
        {value: 0, label: '否'},
      ],
      Rules: {
        userName: [{required: true, message: '请输入白名单名称', trigger: 'blur'}],
        editTag: [{required: true, message: '请选择是否可见', trigger: 'change'}],
        whitelistTag: [{required: true, message: '请选择是否启用', trigger: 'change'}],
      },
      dataListOptions: [
        {label: '用户名称', prop: 'userName'},
        {label: '白名单标识', prop: 'whitelistTagText'},
        {label: '白名单权限', prop: 'whitelistPermissionText'},
        {label: '编辑标识', prop: 'editTagText'},
        {label: '编辑权限', prop: 'editPermissionText'},
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userId: null,
        userName: null,
        editTag: null,
        whitelistTag: null
      },
    }
  },
  created() {
    this.getListName()
    this.getWhiteList()
  },
  methods: {
    resetForm(formName) {
      if (formName === 'whiteListForm') {
        this.whiteListForm = {
          id: null,
          userName: null,
          userId: null,
          whitelistTag: null,
          editTag: '',
          whitelistPermissionList: [],
          editPermissionList: []
        }
      } else {
        for (var key in this[formName]) {
          this[formName][key] = ''
        }
      }
    },
    //获取白名单
    getWhiteList() {
      getList(this.queryParams).then(res => {
        this.queryParams.total = res.result.total
        res.result.records.forEach(item => {
          item.whitelistTag = item.whitelistTag + ""
          item.editTag = item.editTag + ""
        })
        this.dataList = res.result.records
      })
    },
    //获取类别
    getListName() {
      //白名单权限
      getMainNameList().then(res => {
        // console.log(res.result)
        this.categoryDataList = res.result
        this.$forceUpdate();
      })
      //编辑权限
      getMainFirstNameList().then(res => {
        this.categoryDataEditList = res.result
      })
    },
    // 白名单新增
    whiteListAdd() {
      this.tableName = '新增白名单'
      this.tableVisible = true
    },
    // 白名单修改
    whiteListUpdate() {
      if (this.dataClick.id == undefined || this.dataClick.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        this.tableVisible = true
        this.tableName = '修改白名单'
        this.whiteListForm = JSON.parse(JSON.stringify(this.dataClick))
        //白名单标识
        this.whiteListForm.whitelistPermissionList = this.dataClick.whitelistPermission.split(',')[0] == '' ? null : this.dataClick.whitelistPermission.split(',').map(item => parseInt(item.trim()));
        //编辑标识
        this.whiteListForm.editPermissionList = this.dataClick.editPermission.split(',')[0] == '' ? null : this.dataClick.editPermission.split(',').map(item => parseInt(item.trim()));
      }

    },
    //白名单删除
    whiteListDelete() {
      if (this.dataClick.id == undefined || this.dataClick.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        deleteWhiteList(this.dataClick.id).then(res => {
          this.handleChange()
          this.dataClick.id = null
          this.$message({
            type: 'success',
            message: '删除成功！'
          })
        })
      }
    },
    //弹窗确认
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //编辑权限
          if (this.whiteListForm.editPermissionList != null) {
            this.whiteListForm.editPermission = this.whiteListForm.editPermissionList.join(', ');
          }
          //白名单权限
          if (this.whiteListForm.whitelistPermissionList != null) {
            this.whiteListForm.whitelistPermission = this.whiteListForm.whitelistPermissionList.join(', ');
          }
          //新增
          if (this.whiteListForm.id == undefined || this.whiteListForm.id == null || this.whiteListForm.id == "") {
            createWhiteList(this.whiteListForm).then(res => {
              if (res.code == 200) {
                this.tableVisible = false
                this.resetForm('whiteListForm');
                this.dataClick.id = null
                this.getWhiteList()
                this.$message({
                  type: 'success',
                  message: '新增成功'
                })
              } else {
                return this.$message({
                  type: 'warning',
                  message: res.message
                })
              }
            })
          } else {
            updateWhiteList(this.whiteListForm).then(res => {
              this.dataClick.id = null
              //清空点击表格数据
              this.resetForm('whiteListForm');
              this.tableVisible = false
              this.getWhiteList()
              this.$message({
                type: 'success',
                message: '修改成功！'
              })

            })
          }
        } else {
          return false;
        }
      });
    },
    //弹窗关闭
    handleClose() {
      this.resetForm('whiteListForm');
      this.getWhiteList()
      this.tableVisible = false
    },
    //搜索
    handleQuery() {
      this.getWhiteList()
    },
    //重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userId: null,
        userName: null,
        editTag: null,
        whitelistTag: null
      }
      this.getWhiteList()
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getWhiteList()
    },
    handleSubmit(data) {
      if (data.length != 1) {
        this.$message.error("请选择一条数据！");
        return;
      }
      //用户名
      this.whiteListForm.userName = data[0].realname
      //用户id
      this.whiteListForm.userId = data[0].id
      //关闭弹窗
      this.dialogTableVisible = false;
    },
    //获取用户
    getUserName() {
      this.dialogTableVisible = true;
      this.$refs.persionTable.resetQuery();
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getWhiteList()
    },
    /** 单击表事件 */
    dataListSelect(row) {
      this.dataClick = row
    },
    // 规范化选项数据的方法
    normalizeOptions(node) {
      if (node.children && !node.children.length) {
        // 去掉children=[]的children属性
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
  }
};
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
