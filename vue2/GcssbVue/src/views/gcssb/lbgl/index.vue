<template>

  <div class="main" ref="main">
    <div class="list-box" style="width: 20%">
      <div class="operate-pannel">
        <div>
          <el-tree
              :data="data"
              ref="tree"
              :props="defaultProps"
              node-key="id"
              @node-click="handleNodeClick"
              :expand-on-click-node="false"
              default-expand-all
              :highlight-current="true"/>
        </div>
      </div>
    </div>
    <div class="process-info" style="width: 79%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button size="medium" type="text" icon="el-icon-plus" v-has-permi="['JDWGB01LB01QX02']"
                       @click="addCategory()" v-if="permissionButton">新增
            </el-button>
            <el-button size="medium" type="text" icon="el-icon-edit" v-has-permi="['JDWGB01LB01QX03']"
                       @click="updateCategory()" v-if="permissionButton">修改
            </el-button>
            <el-button size="medium" type="text" icon="el-icon-delete" v-has-permi="['JDWGB01LB01QX04']"
                       @click="deleteCategory()" v-if="permissionButton">删除
            </el-button>
            <el-button size="medium" type="text" icon="el-icon-upload" v-has-permi="['JDWGB01LB01QX05']"
                       @click="uploadImg()" v-if="permissionButton">类型图片
            </el-button>
            <!--            <el-button  size="mini" type="text" icon="el-icon-plus" @click="uploadBooks()">上传书籍</el-button>-->
          </div>
        </div>
        <div class="table-box">
          <Table
              :tableData="parentIdList"
              :tableOptions="parentIdOptions"
              :loading="parentIdLoading"
              @getCurrentData="parentIdSelect"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
          </Table>
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="queryParams.pageNum"
              :page-size="queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="queryParams.total"
          />
        </div>
      </div>

      <!-- 新增修改对话框-->
      <el-dialog :title=tableName :visible.sync="tableVisible" width="600px" :before-close="handleClose">
        <el-form label-width="80px" :model="categoryAddForm" :rules="Rules" ref="categoryAddForm">
          <el-form-item label="上级部门" prop="parentId">
            <treeselect v-model="categoryAddForm.parentId" :options="data" :show-count="true"
                        :normalizer="normalizeOptions"
                        placeholder="选择上级部门" style='width: 400px'/>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="categoryAddForm.name" style='width: 400px'></el-input>
          </el-form-item>
          <el-form-item label="是否启用" prop="isEnable">
            <el-select v-model="categoryAddForm.isEnable" placeholder="请选择状态" style='width: 400px'>
              <el-option label="启用" value="1"></el-option>
              <el-option label="不启用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否可见" prop="isVisible">
            <el-select v-model="categoryAddForm.isVisible" placeholder="请选择状态" style='width: 400px ;'>
              <el-option label="可见" value="1"></el-option>
              <el-option label="不可见" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input-number v-model="categoryAddForm.sort" :min="0" label="排序"></el-input-number>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" autosize placeholder="请输入内容" v-model="categoryAddForm.remark"
                      style='width: 400px'></el-input>
          </el-form-item>
          <el-form-item size="large">
            <el-button type="primary" @click="onSubmit('categoryAddForm')">确定</el-button>
            <el-button @click="handleClose">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>


      <el-dialog
          width="420px"
          :visible="uploadImgDialog"
          size="tiny"
          center
          :destroy-on-close="true"
          :modal-append-to-body="false"
          @close="closeUploadImgDialog"
          :close-on-click-modal="false"
      >
        <div slot="title" align="left" class="dialogTitle">类型图片上传</div>
        <ImageUpload
            :limit="1"
            :isShowTip="true"
            @uploadSuccessData="uploadSuccessData"
            v-model="imgTFile"
            :type="1"
            uploadUrl="/gcssb/File/upload"
            :file-type="['png', 'jpg', 'jpeg']"
            :data="{ywId: ywId,hjID:'category'}"
        />
        <div style="padding-top: 50px;text-align: right">
          <el-button type="primary" @click="updCategoryFile">确定</el-button>
          <el-button @click="closeUploadImgDialog">取消</el-button>
        </div>

      </el-dialog>

    </div>
  </div>
</template>


<script>
import Table from '@/components/MainTable'
import {
  createCategory,
  getMainNameList,
  getImage,
  getListByParentId,
  updateMation,
  deleteMain,
  getMainListByPermission,
  updCategoryFile,
  isEditPermission
} from '@/api/lbgl/lbgl'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FIleUpload from "@/components/Gcssb/FIleUpload.vue";
import ImageUpload from '@/components/Gcssb/ImageUpload.vue'

export default {
  components: {Table, Treeselect, FIleUpload, ImageUpload},
  data() {
    return {
      imgTFile: '',
      tableName: '',
      tableVisible: false,
      parentIdLoading: false,
      categoryAddForm: {
        parentId: null,
        name: null,
        isEnable: '1',
        isVisible: '1',
        sort: null,
        remark: ''
      },
      treeNodeClickData: {},
      parentIdSelectData: {},
      parentIdList: [],
      deptList: [],
      deptIds: [],
      parentIdOptions: [
        {label: '类别名称', prop: 'name'},
        {label: '是否启用', prop: 'isEnableText'},
        {label: '是否可见', prop: 'isVisibleText'},
        {label: '备注', prop: 'remark'},
        {label: '排序', prop: 'sort'},
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      Rules: {
        name: [{required: true, message: '请输入类别名称', trigger: 'blur'}],
        type: [{required: true, message: '请输入类型', trigger: 'blur'}],
        isVisible: [{required: true, message: '请选择是否可见', trigger: 'change'}],
        parentId: [{required: true, message: '请选择上级', trigger: ['blur', 'change', 'submit']}],
        isEnable: [{required: true, message: '请选择是否启用', trigger: 'change'}],
        advancePageNum: [{required: true, message: '请输入前置页码', trigger: 'blur'}],
        sort: [{required: true, message: '请输入类别排序', trigger: 'blur'}],
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      uploadDialog: false,
      ywId: 0,
      uploadImgDialog: false,
      permissionButton : false,
      fileInfo: {}
    };
  },
  created() {
    this.getListName()
    //判断用户是否包含白名单编辑权限
    this.getPermission()
  },
  methods: {
    //获取类别名称
    getListName() {
      getMainListByPermission().then(res => {
        if (res.code != 200) {
          this.$message({
            type: 'warning',
            message: res.message,
          })
          return;
        }
        this.data = res.result
        this.$forceUpdate();
      })
    },
    //树点击行
    handleNodeClick(data) {
      this.treeNodeClickData = data
      this.getParentList()
    },
    //获取第一页数据
    getParentList() {
      this.queryParams.pageNum = 1
      this.byParentId()
    },
    byParentId() {
      this.queryParams.parentId = this.treeNodeClickData.id
      getListByParentId(this.queryParams).then(res => {
        this.queryParams.total = res.result.total
        res.result.records.forEach(item => {
          item.type = item.type + ""
          item.isEnable = item.isEnable + ""
          item.isVisible = item.isVisible + ""
        })
        this.parentIdList = res.result.records
      })
    },
    //新增
    addCategory() {
      this.tableName = '新增类别'
      //默认类型-类别
      this.categoryAddForm.type = 1
      //父id
      this.categoryAddForm.parentId = this.treeNodeClickData.id
      this.tableVisible = !this.tableVisible;
    },
    updateCategory() {
      this.tableName = '修改类别'
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择类别！'
        })
      } else {
        this.categoryAddForm = this.parentIdSelectData
        this.tableVisible = !this.tableVisible;
      }
    },
    //新增修改确认
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.categoryAddForm.id == undefined || this.categoryAddForm.id == null) {
            createCategory(this.categoryAddForm).then(response => {
              if (response.result == undefined || response.result == null) {
                this.tableVisible = false
                this.$message({
                  type: 'warning',
                  message: '新增失败！'
                })
                this.resetForm('categoryAddForm')
              } else {
                this.tableVisible = false
                this.getListName()
                this.resetForm('categoryAddForm')
                if (this.treeNodeClickData.id != undefined && this.treeNodeClickData.id != "") {
                  this.byParentId()
                }
                this.$message({
                  type: 'success',
                  message: '新增成功！'
                })
              }
            });
          } else {
            updateMation(this.categoryAddForm).then(response => {
              this.tableVisible = false
              this.resetForm('categoryAddForm')
              this.getListName()
              this.byParentId()
              this.$message({
                type: 'success',
                message: '修改成功！'
              })
            })
          }
        } else {
          return false;
        }
      });
    },
    //删除
    deleteCategory() {
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择类别！'
        })
      } else {
        this.$confirm('此操作将删除类别名称:' + this.parentIdSelectData.name + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteMain(this.parentIdSelectData.id).then(res => {
            this.getListName()
            if (this.treeNodeClickData.id) {
              this.getParentList()
            }
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        })
      }
    },
    //重置数据
    resetForm(formName) {
      for (var key in this[formName]) {
        if (key == 'isEnable' || key == 'isVisible') {
          this[formName][key] = '1'
        } else {
          this[formName][key] = ''
        }
      }
    },
    /** 单击表事件 */
    parentIdSelect(row) {
      this.parentIdSelectData = row
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.byParentId()
    },
    //每页行数
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.byParentId()
    },
    /*弹窗关闭*/
    handleClose() {
      this.tableVisible = false
      this.resetForm('categoryAddForm')
      this.byParentId()
    },
    uploadSuccessData(data) {
      //保存数据
      this.fileInfo = data[0]
      console.log(this.fileInfo)
    },
    //上传类型图片
    uploadImg() {
      if (this.parentIdSelectData.id == undefined || this.parentIdSelectData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选中一条数据！'
        })
      } else {
        this.ywId = this.parentIdSelectData.id
        //获取是否上传图片
        getImage(this.ywId).then((res) => {
          this.imgTFile = res.result
        })
        this.uploadImgDialog = true
      }
    },
    closeUploadImgDialog() {
      this.getListName()
      this.uploadImgDialog = false
    },
    updCategoryFile() {
      const formData = new FormData();
      formData.append('ywId', this.ywId);
      formData.append('fileId', this.fileInfo.id);
      formData.append('filePath', this.fileInfo.filePath);
      updCategoryFile(formData).then(res => {
        this.$message({
          type: 'success',
          message: '文件保存成功！'
        })
        this.closeUploadImgDialog();
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '文件保存失败'
        });
      });
    },
    // 规范化选项数据的方法
    normalizeOptions(node) {
      if (node.children && !node.children.length) {
        // 去掉children=[]的children属性
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children,
      };
    },
    //判断用户是否包含白名单编辑权限
    getPermission(){
      isEditPermission().then(res => {
        this.permissionButton = res.result
      })
    }
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
