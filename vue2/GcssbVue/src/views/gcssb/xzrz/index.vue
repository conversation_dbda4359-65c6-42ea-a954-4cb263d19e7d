<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <!-- 搜索工作栏 -->
        <span class="font-size14">用户名：</span>
        <el-input v-model="queryParams.userName" placeholder="请输入用户名" style="width: 240px" clearable
                  @change="handleQuery"/>
        <span class="font-size14">书籍名称：</span>
        <el-input v-model="queryParams.booksName" placeholder="请输入用户名" style="width: 240px" clearable
                  @change="handleQuery"/>
        <span class="font-size14">下载日期：</span>
        <el-date-picker style="width: 240px" v-model="queryParams.downloadTimeArray"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"/>
        <span class="font-size14">类型：</span>
        <el-select v-model="queryParams.downloadType" clearable placeholder="请选择类型" style='width: 240px'
                   @change="handleQuery">
          <el-option label="APP" value="APP"></el-option>
          <el-option label="PC" value="PC"></el-option>
        </el-select>

        <el-button size="medium" type="text" icon="el-icon-search" v-has-permi="['JDWGB01ZX01QX01']"
                   @click="handleQuery">查询
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-refresh" v-has-permi="['JDWGB01ZX01QX01']"
                   @click="resetQuery">重置
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-upload2" v-has-permi="['JDWGB01ZX01QX02']"
                   @click="downloadExport()">导出
        </el-button>
        <!--            <el-button type="primary" plain size="mini" icon="el-icon-plus" @click="downloadAdd" >新增 </el-button>-->
        <!--        <el-button type="warning" plain size="mini" icon="el-icon-edit" @click="downloadUpdate" >修改 </el-button>-->
        <!--        <el-button type="danger" plain size="mini" icon="el-icon-error" @click="downloadDelete" >删除 </el-button>-->
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="dataList"
          :tableOptions="dataListOptions"
          :loading="dataListLoading"
          @getCurrentData="dataListSelect"
          style="height: 75vh"
      >
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <!-- 新增修改对话框-->
    <el-dialog :title=tableName :visible.sync="tableVisible" width="650px" :before-close="handleClose">
      <el-form label-width="100px" :model="downloadForm" :rules="Rules" ref="downloadForm">
        <el-form-item label="用户名称" prop="userName">
          <el-input v-model="downloadForm.userName" disabled style='width: 400px'>&nbsp;</el-input>
          <el-button type="success" @click="getUserName()">获取</el-button>
        </el-form-item>
        <el-form-item label="书籍名称" prop="booksName">
          <el-input v-model="downloadForm.booksName" disabled style='width: 400px'>&nbsp;</el-input>
          <el-button type="success" @click="getBooksName()">获取</el-button>
        </el-form-item>
        <el-form-item label="下载日期:" prop="downloadTime">
          <el-date-picker v-model="downloadForm.downloadTime" type="datetime" placeholder="选择日期时间"
                          style='width: 455px'></el-date-picker>
        </el-form-item>
        <el-form-item size="large">
          <el-button type="primary" @click="onSubmit('downloadForm')">确定</el-button>
          <el-button @click="tableVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!--   选择人员 -->
    <el-dialog title="人员信息" :visible.sync="dialogTableVisible">
      <addPersionTable ref="persionTable" @handleSubmit="handleSubmit" @handleClose="dialogTableVisible = false"/>
    </el-dialog>

    <!--   选择书籍 -->
    <el-dialog title="书籍信息" :visible.sync="bookTableVisible">
      <addBookTable ref="bookTable" @handleSubmit="bookSubmit" @handleClose="bookTableVisible = false"/>
    </el-dialog>

  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import {createDownload, updateDownload, deleteDownload, getListPage, exportExcel} from '@/api/xzrz/xzrz';
import {downLoad, getFileNameTime} from "@/utils/tool";
import {updateMation} from "api/lbgl/lbgl";
import addPersionTable from "views/gcssb/bmdgl/components/addPersionTable.vue";
import addBookTable from "views/gcssb/xzrz/components/addBookTable.vue";
import Pagination from "components/Pagination/index.vue";

export default {
  components: {Pagination, addPersionTable, Table, addBookTable},
  data() {
    return {
      dialogTableVisible: false,
      bookTableVisible: false,
      tableVisible: false,
      dataListLoading: false,
      tableName: '',
      downloadForm: {
        userName: null,
        booksName: null,
        downloadTime: null
      },
      dataList: [],
      selectTableData: {},
      dataListOptions: [
        {label: '用户名称', prop: 'userName', width: '300px'},
        {label: '书籍名称', prop: 'booksName'},
        {label: '下载时间', prop: 'downloadTime'},
        {label: '类型', prop: 'downloadType', width: '300px'},
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        booksName: null,
        userName: null,
        downloadTimeArray: [],
        downloadType: null,
      },
      Rules: {
        userName: [{required: true, message: '请获取用户名', trigger: 'blur'}],
        booksName: [{required: true, message: '请输入书籍', trigger: 'blur'}],
        downloadTime: [{required: true, message: '请输入下载时间', trigger: 'change'}],
      },
    }
  },
  created() {
    this.handleQuery()
  },
  methods: {
    getList() {
      this.queryParams.pageNum = 1
      this.handleQuery()
    },
    //查询
    handleQuery() {
      //解析查询日期
      this.formatDateTime(this.queryParams.downloadTimeArray)
      //查询
      getListPage(this.queryParams).then(res => {
        this.queryParams.downloadTimeStart = null
        this.queryParams.downloadTimeEnd = null
        this.queryParams.total = res.result.total
        this.dataList = res.result.records
      })
    },
    //导出
    downloadExport() {
      this.dataListLoading = true
      exportExcel(this.queryParams).then((res) => {
        let fileName = '下载日志' + getFileNameTime() + '.xlsx'
        downLoad(res, fileName)
      }).finally(() => {
        this.dataListLoading = false
      })
    },
    //删除
    downloadDelete() {
      if (this.selectTableData.id == undefined || this.selectTableData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择需要删除的日志数据！'
        })
      } else {
        this.$confirm('此操作将删除日志名称:' + this.selectTableData.userName + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteDownload(this.selectTableData.id).then(res => {
            this.getList()
            this.$message({
              type: 'success',
              message: '删除成功！'
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        })
      }
    },
    //修改
    downloadUpdate() {
      if (this.selectTableData.id == undefined || this.selectTableData.id == null) {
        this.$message({
          type: 'warning',
          message: '请选择需要删除的日志数据！'
        })
      } else {
        this.tableName = '日志修改'
        this.tableVisible = true
        this.downloadForm = this.selectTableData
      }

    },
    //新增
    downloadAdd() {
      this.tableName = '日志新增'
      this.tableVisible = true
    },
    //弹窗确认
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.downloadForm.id === undefined || this.downloadForm.id === null) {
            // 新增操作
            createDownload(this.downloadForm).then(res => {
              this.tableVisible = false;
              this.resetForm(formName);
              this.handleQuery();
              this.$message({
                type: 'success',
                message: '新增成功'
              });
            }).catch(error => {
              this.$message.error('新增失败，请重试'); // 在操作失败时显示错误消息
            });
          } else {
            // 修改操作
            updateDownload(this.downloadForm).then(res => {
              this.tableVisible = false;
              this.resetForm(formName);
              this.selectTableData = {}
              this.handleQuery();
              this.$message({
                type: 'success',
                message: '修改成功'
              });
            }).catch(error => {
              this.$message.error('修改失败，请重试');
            });
          }
        } else {
          return false;
        }
      });
    },
    getUserName() {
      this.dialogTableVisible = true
      this.$refs.persionTable.resetQuery();
    },
    getBooksName() {
      this.bookTableVisible = true;
      this.$refs.bookTable.resetQuery();
    },
    bookSubmit(data) {
      if (data.length != 1) {
        this.$message.error("请选择一条数据！");
        return;
      }
      //用户名
      this.downloadForm.booksName = data[0].name
      //用户id
      this.downloadForm.booksId = data[0].id
      //关闭弹窗
      this.bookTableVisible = false;
    },
    handleSubmit(data) {
      if (data.length != 1) {
        this.$message.error("请选择一条数据！");
        return;
      }
      //用户名
      this.downloadForm.userName = data[0].realname
      //用户id
      this.downloadForm.userId = data[0].id
      //关闭弹窗
      this.dialogTableVisible = false;
    },
    resetForm(formName) {
      for (var key in this[formName]) {
        this[formName][key] = ''
      }
    },
    //弹窗关闭
    handleClose() {
      this.resetForm("downloadForm");
      this.handleQuery()
      this.selectTableData = {}
      this.tableVisible = false
    },
    //重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        booksName: null,
        userName: null,
        downloadTimeArray: [],
        downloadType: null
      }
      this.queryParams.downloadTimeStart = null
      this.queryParams.downloadTimeEnd = null
      this.handleQuery()
    },
    //表格点击
    dataListSelect(row) {
      this.selectTableData = row
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.handleQuery()
    },
    formatDateTime(time) {
      if (time != null && time.length != 0) {
        const startDateTime = time[0]; // 获取开始日期时间
        const endDateTime = time[1]; // 获取结束日期时间
        const startDate = new Date(startDateTime); // 创建开始日期时间对象
        const endDate = new Date(endDateTime); // 创建结束日期时间对象
        // 获取日期部分
        const formattedStartDate = `${startDate.getFullYear()}-${('0' + (startDate.getMonth() + 1)).slice(-2)}-${('0' + startDate.getDate()).slice(-2)}`;
        const formattedEndDate = `${endDate.getFullYear()}-${('0' + (endDate.getMonth() + 1)).slice(-2)}-${('0' + endDate.getDate()).slice(-2)}`;
        // 获取时间部分
        const formattedStartTime = `${('0' + startDate.getHours()).slice(-2)}:${('0' + startDate.getMinutes()).slice(-2)}:${('0' + startDate.getSeconds()).slice(-2)}`;
        const formattedEndTime = `${('0' + endDate.getHours()).slice(-2)}:${('0' + endDate.getMinutes()).slice(-2)}:${('0' + endDate.getSeconds()).slice(-2)}`;

        this.queryParams.downloadTimeStart = `${formattedStartDate} ${formattedStartTime}`
        this.queryParams.downloadTimeEnd = `${formattedEndDate} ${formattedEndTime}`
      }
    },
  }
};
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
