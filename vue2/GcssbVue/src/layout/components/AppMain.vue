<template>
  <el-main :style="{background: `linear-gradient(to bottom, ${theme} 40%, #eaeff3 40% 100%)`}">
    <div class="route-container">
        <router-view/>
    </div>
  </el-main>
</template>

<script>

export default {
  name: 'AppMain',
  computed: {
    theme(){
      return this.$store.state.settings.theme
    },
  }
}
</script>

<style lang="scss" scoped>
.route-container{
  background: #fff;
  border-radius: 10px;
  height: 100%;
  width: 100%;
  padding: 20px;
  
  .yygl-container{
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: space-between;
  }
}
</style>
