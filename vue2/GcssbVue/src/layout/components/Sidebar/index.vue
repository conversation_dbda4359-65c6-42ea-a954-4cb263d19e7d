<template>
  <div>
    <logo :collapse="false"/>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="$route.path"
          router
          :unique-opened="true"
          class="el-menu-vertical-demo"
          :active-Title-color="theme">
        <component
            v-for="route in menuList"
            :key="route.Id"
            :index="route.Title"
            :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'">
          <template slot="title">
            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
            <span slot="title" class="font-size14">{{route.Title}}</span>
          </template>
          <template v-if="route.childList && route.childList.length > 0">
            <el-menu-item
                v-for="routeChild in route.childList"
                :key="routeChild.Id"
                :index="routeChild.Url"
                :route="{path: routeChild.Url}"
                :disabled="!routeChild.HasPermi">
              <span slot="title">{{routeChild.Title}}</span>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin} from "api/login";
import {getModule} from "api/common";

export default {
  components: {Logo},
  data() {
    return {
      urlList: [],
      menuList: []
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  created() {
    MLogin().then(res => {
      if (res.success) {
        this.getMenuList()
      } else {
        this.$router.push('/login')
      }
    })
  },
  methods: {
    async getMenuList() {
      await getModule().then(res => {
        const functionList = (res.data || []).find(item => item.Id === 'JDWGB01')
        this.urlList = functionList.childList.map((value) => {
          return value.Url
        })
        let firstUrl = ''
        this.menuList = [
          {
            Id: 'JDWGB01',
            Title: '工程随身宝',
            childList: [
              {
                Id: "JDWGB01LB01",
                Title: "类别管理",
                Url: "/gcssb/lbgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/gcssb/lbgl') > -1
              },
              {
                Id: "JDWGB01SJ01",
                Title: "书籍管理",
                Url: "/gcssb/sjgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/gcssb/sjgl') > -1
              },
              {
                Id: "JDWGB01BM01",
                Title: "白名单管理",
                Url: "/gcssb/bmdgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/gcssb/bmdgl') > -1
              },
              {
                Id: "JDWGB01ZX01",
                Title: "下载日志",
                Url: "/gcssb/xzrz",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/gcssb/xzrz') > -1
              },
              {
                Id: "JDWGB01ZD01",
                Title: "字典管理",
                Url: "/gcssb/zdgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/gcssb/zdgl') > -1
              }
            ]
          },
        ]
        this.menuList.forEach((value) => {
          if (firstUrl != '') {
            return
          }
          value.childList.forEach((param) => {
            if (firstUrl != '') {
              return
            }
            if (param.HasPermi) {
              firstUrl = param.Url
            }
          })
        })
        //TODO 获取后台模块列表 默认跳转到第一个
        if (this.$router.currentRoute.fullPath !== firstUrl) {
          this.$router.push(firstUrl)
        }
        this.$forceUpdate()
      })
    }
  }
};
</script>
