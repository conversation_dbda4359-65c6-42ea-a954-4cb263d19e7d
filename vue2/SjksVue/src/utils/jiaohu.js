export var cwMap0 = new Map();
export var cwMap0Func = new Map();
export const _sysmark = 'wpframe'
const baseURL = process.env.VUE_APP_BASE_API

export function SafeOpenFunction(win,fid,furl,ftitle)
{
    if(typeof(eval('win.NavFunction'))=="function")
    {
        win.NavFunction(furl,fid,ftitle);
    }
}

/**
 * 
 * @param {*} id module_mix
 * @param {*} url url
 * @param {*} fid Id
 * @param {*} furl Url
 * @param {*} ftitle Title
 */
export function OpenMoudleWindow(id,url,fid,furl,ftitle) {
    url = baseURL + url
    var urlstr ="";
    var loadflag=false;
    if (url.indexOf("?") > 0) {
    if (url.indexOf("?xml=true") > 0) {
        url = url.replace("?xml=true", "");
        urlstr = url + "?mid="+id+"&sm="+_sysmark;
    }else
    {
        urlstr = url + "&mt=m&mid=" + id+"&sm="+_sysmark;
    }
    }else
    {
        urlstr = url + "?mt=m&mid=" + id+"&sm="+_sysmark;
    }

    // alert(urlstr);
    if(fid!=undefined&&furl!=undefined&&ftitle!=undefined)
    {
        var cwfunctmp = cwMap0Func.get(id);
        if (cwfunctmp!=null&&cwfunctmp!=undefined) {
            cwMap0Func.delete(id);
            cwMap0Func.set(id, {fid: fid, furl: furl, ftitle: ftitle});
        } else {
            cwMap0Func.set(id, {fid: fid, furl: furl, ftitle: ftitle});
        }
    }

    var h = window.screen.availHeight;
    var w = window.screen.availWidth;

    var cwtmp = cwMap0.get(id);
    if (cwtmp == undefined || cwtmp==null) {
        cwtmp = window.open('about:blank');
        cwtmp.location = urlstr;
        cwMap0.set(id, cwtmp);
    } else {
        if (cwtmp.closed) {
            cwtmp = window.open('about:blank');
            cwtmp.location = urlstr;
            cwMap0.set(id, cwtmp);
        } else {
            cwtmp.focus();
        }
    }

    if(cwtmp!=undefined&&fid!=undefined&&furl!=undefined&&ftitle!=undefined)
    {
        if(cwtmp.loadfinish==true){
            SafeOpenFunction(cwtmp,fid,furl,ftitle);
        }
    }

    //ShowModalWin(urlstr, "", w, h);
}

export function closePages(){
    cwMap0.forEach(function (cwtmp, key, mapObj) {
        if (!cwtmp.closed)
        {
            cwtmp.close();
        }
    });
}

