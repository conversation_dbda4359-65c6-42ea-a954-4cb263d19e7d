import ExcelJS from 'exceljs'
import { downLoad } from './tool'

/**
 * Excel导出工具类
 */
export class ExcelExporter {
  constructor() {
    this.workbook = new ExcelJS.Workbook()
  }

  /**
   * 导出表格数据到Excel
   * @param {Object} options 导出配置
   * @param {Array} options.data 表格数据
   * @param {Array} options.headers 表头配置
   * @param {String} options.sheetName 工作表名称
   * @param {String} options.fileName 文件名
   * @param {Object} options.summaryData 汇总数据（可选）
   */
  async exportToExcel(options) {
    const {
      data = [],
      headers = [],
      sheetName = 'Sheet1',
      fileName = 'export.xlsx',
      summaryData = null
    } = options

    // 创建工作表
    const worksheet = this.workbook.addWorksheet(sheetName)

    // 设置列宽
    this.setColumnWidths(worksheet, headers)

    // 添加表头
    this.addHeaders(worksheet, headers)

    // 添加数据
    this.addData(worksheet, data, headers)

    // 添加汇总行
    if (summaryData) {
      this.addSummaryRow(worksheet, summaryData, headers, data.length + 3)
    }

    // 设置样式
    this.setStyles(worksheet, headers, data.length, summaryData)

    // 生成Excel文件并下载
    await this.downloadExcel(fileName)
  }

  /**
   * 设置列宽
   * @param {Object} worksheet 工作表
   * @param {Array} headers 表头配置
   */
  setColumnWidths(worksheet, headers) {
    const columns = []

    headers.forEach(header => {
      if (header.children && header.children.length > 0) {
        header.children.forEach(child => {
          // 根据列类型设置不同宽度
          if (child.prop && child.prop.includes('Amount')) {
            columns.push({ width: 15 }) // 金额列较宽
          } else if (child.label === '单位名称') {
            columns.push({ width: 25 }) // 单位名称列最宽
          } else {
            columns.push({ width: 12 }) // 其他列默认宽度
          }
        })
      } else {
        if (header.prop && header.prop.includes('Amount')) {
          columns.push({ width: 15 })
        } else if (header.label === '单位名称') {
          columns.push({ width: 25 })
        } else {
          columns.push({ width: 12 })
        }
      }
    })

    worksheet.columns = columns
  }

  /**
   * 添加表头
   * @param {Object} worksheet 工作表
   * @param {Array} headers 表头配置
   */
  addHeaders(worksheet, headers) {
    // 第一行：主表头
    const firstRow = []
    headers.forEach(header => {
      if (header.children && header.children.length > 0) {
        // 分组表头
        firstRow.push(header.label)
        for (let i = 1; i < header.children.length; i++) {
          firstRow.push('')
        }
      } else {
        // 普通表头（如单位名称）
        firstRow.push(header.label)
      }
    })
    worksheet.addRow(firstRow)

    // 第二行：子表头
    const secondRow = []
    headers.forEach(header => {
      if (header.children && header.children.length > 0) {
        // 添加子表头
        header.children.forEach(child => {
          secondRow.push(child.label)
        })
      } else {
        // 普通表头在第二行留空
        secondRow.push('')
      }
    })
    worksheet.addRow(secondRow)

    // 设置合并单元格
    this.setMergedCells(worksheet, headers)
  }

  /**
   * 设置合并单元格
   * @param {Object} worksheet 工作表
   * @param {Array} headers 表头配置
   */
  setMergedCells(worksheet, headers) {
    let currentCol = 1 // ExcelJS列从1开始

    headers.forEach(header => {
      if (header.children && header.children.length > 0) {
        // 合并主表头单元格
        const startCol = currentCol
        const endCol = currentCol + header.children.length - 1

        // 合并第一行的单元格
        worksheet.mergeCells(1, startCol, 1, endCol)

        currentCol += header.children.length
      } else {
        // 普通表头需要合并两行
        worksheet.mergeCells(1, currentCol, 2, currentCol)
        currentCol += 1
      }
    })
  }

  /**
   * 添加数据
   * @param {Object} worksheet 工作表
   * @param {Array} data 数据
   * @param {Array} headers 表头配置
   */
  addData(worksheet, data, headers) {
    data.forEach(row => {
      const dataRow = []
      headers.forEach(header => {
        if (header.children && header.children.length > 0) {
          // 处理分组列
          header.children.forEach(child => {
            let value = row[child.prop]
            // 处理undefined或null的情况
            if (value === undefined || value === null) {
              value = ''
            }
            // 如果是金额字段，格式化显示
            if (child.prop && child.prop.includes('Amount') && (value !== '' && value !== null && value !== undefined)) {
              value = this.formatAmount(value)
            }
            dataRow.push(value)
          })
        } else {
          // 处理普通列
          let value = row[header.prop]
          // 处理undefined或null的情况
          if (value === undefined || value === null) {
            value = ''
          }
          // 如果是金额字段，格式化显示
          if (header.prop && header.prop.includes('Amount') && (value !== '' && value !== null && value !== undefined)) {
            value = this.formatAmount(value)
          }
          dataRow.push(value)
        }
      })
      worksheet.addRow(dataRow)
    })
  }

  /**
   * 添加汇总行
   * @param {Object} worksheet 工作表
   * @param {Object} summaryData 汇总数据
   * @param {Array} headers 表头配置
   * @param {Number} rowIndex 行索引
   */
  addSummaryRow(worksheet, summaryData, headers, rowIndex) {
    const summaryRow = []
    let isFirstColumn = true

    headers.forEach(header => {
      if (header.children && header.children.length > 0) {
        header.children.forEach(child => {
          if (isFirstColumn) {
            summaryRow.push('总计')
            isFirstColumn = false
          } else {
            let value = summaryData[child.prop]
            // 处理undefined或null的情况
            if (value === undefined || value === null) {
              value = ''
            }
            // 如果是金额字段，格式化显示
            if (child.prop && child.prop.includes('Amount') && (value !== '' && value !== null && value !== undefined)) {
              value = this.formatAmount(value)
            }
            summaryRow.push(value)
          }
        })
      } else {
        if (isFirstColumn) {
          summaryRow.push('总计')
          isFirstColumn = false
        } else {
          let value = summaryData[header.prop]
          // 处理undefined或null的情况
          if (value === undefined || value === null) {
            value = ''
          }
          // 如果是金额字段，格式化显示
          if (header.prop && header.prop.includes('Amount') && (value !== '' && value !== null && value !== undefined)) {
            value = this.formatAmount(value)
          }
          summaryRow.push(value)
        }
      }
    })

    worksheet.addRow(summaryRow)
  }

  /**
   * 设置样式
   * @param {Object} worksheet 工作表
   * @param {Array} headers 表头配置
   * @param {Number} dataRowCount 数据行数
   * @param {Object} summaryData 汇总数据
   */
  setStyles(worksheet, headers, dataRowCount, summaryData) {
    // 计算总列数
    let totalCols = 0
    headers.forEach(header => {
      if (header.children && header.children.length > 0) {
        totalCols += header.children.length
      } else {
        totalCols += 1
      }
    })

    // 表头样式：灰底黑字，居中，加粗
    const headerStyle = {
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF808080' } // 深灰色背景
      },
      font: {
        color: { argb: 'FFFFFFFF' }, // 白色字体
        bold: true
      },
      alignment: {
        horizontal: 'center',
        vertical: 'middle'
      },
      border: {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }

    // 数据样式：居中，有边框
    const dataStyle = {
      alignment: {
        horizontal: 'center',
        vertical: 'middle'
      },
      border: {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }

    // 汇总行样式：浅蓝底，加粗，居中
    const summaryStyle = {
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' } // 浅蓝色背景
      },
      font: {
        color: { argb: 'FF000000' },
        bold: true
      },
      alignment: {
        horizontal: 'center',
        vertical: 'middle'
      },
      border: {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } }
      }
    }

    // 应用样式
    const totalRows = 2 + dataRowCount + (summaryData ? 1 : 0)

    for (let row = 1; row <= totalRows; row++) {
      for (let col = 1; col <= totalCols; col++) {
        const cell = worksheet.getCell(row, col)

        if (row <= 2) {
          // 表头行（前两行）
          cell.style = headerStyle
        } else if (summaryData && row === totalRows) {
          // 汇总行（最后一行）
          cell.style = summaryStyle
        } else {
          // 数据行
          cell.style = dataStyle
        }
      }
    }
  }



  /**
   * 格式化金额显示
   * @param {Number} amount 金额
   * @returns {String} 格式化后的金额
   */
  formatAmount(amount) {
    // 处理null、undefined、空字符串等情况，但保留数字0
    if (amount === null || amount === undefined || amount === '') return '0.00'
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  /**
   * 下载Excel文件
   * @param {String} fileName 文件名
   */
  async downloadExcel(fileName) {
    // 生成Excel文件的二进制数据
    const buffer = await this.workbook.xlsx.writeBuffer()

    // 创建Blob对象
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 使用工具类下载文件
    downLoad(blob, fileName)
  }
}

/**
 * 导出应收账款汇总数据到Excel
 * @param {Array} data 汇总数据
 * @param {Object} summaryData 汇总行数据
 */
export async function exportAccountsReceivableSummary(data, summaryData) {
  const exporter = new ExcelExporter()

  // 定义表头结构
  const headers = [
    {
      label: '单位名称',
      prop: 'unit'
    },
    {
      label: '账龄6个月以内',
      children: [
        { label: '个数', prop: 'sixMonCount' },
        { label: '金额', prop: 'sixMonAmount' },
        { label: '台账数', prop: 'sixMonLedger' }
      ]
    },
    {
      label: '账龄1年以内',
      children: [
        { label: '个数', prop: 'oneYearCount' },
        { label: '金额', prop: 'oneYearAmount' },
        { label: '台账数', prop: 'oneYearLedger' }
      ]
    },
    {
      label: '账龄1-2年',
      children: [
        { label: '个数', prop: 'oneToTwoCount' },
        { label: '金额', prop: 'oneToTwoAmount' },
        { label: '台账数', prop: 'oneToTwoLedger' }
      ]
    },
    {
      label: '账龄2-3年',
      children: [
        { label: '个数', prop: 'twoToThreeCount' },
        { label: '金额', prop: 'twoToThreeAmount' },
        { label: '台账数', prop: 'twoToThreeLedger' }
      ]
    },
    {
      label: '账龄3年以上',
      children: [
        { label: '个数', prop: 'threeAboveCount' },
        { label: '金额', prop: 'threeAboveAmount' },
        { label: '台账数', prop: 'threeAboveLedger' }
      ]
    }
  ]

  // 生成文件名（包含时间戳）
  const now = new Date()
  const timestamp = now.getFullYear() +
    String(now.getMonth() + 1).padStart(2, '0') +
    String(now.getDate()).padStart(2, '0') + '_' +
    String(now.getHours()).padStart(2, '0') +
    String(now.getMinutes()).padStart(2, '0') +
    String(now.getSeconds()).padStart(2, '0')

  const fileName = `应收账款汇总_${timestamp}.xlsx`

  // 导出Excel
  await exporter.exportToExcel({
    data,
    headers,
    sheetName: '应收账款汇总',
    fileName,
    summaryData
  })
}

/**
 * 导出债务管理汇总数据到Excel
 * @param {Array} data 汇总数据
 * @param {Object} summaryData 汇总行数据
 */
export async function exportDebtManagementSummary(data, summaryData) {
  const exporter = new ExcelExporter()

  // 定义表头结构
  const headers = [
    {
      label: '单位名称',
      prop: 'companyName'
    },
    {
      label: '账龄6个月以内',
      children: [
        { label: '个数', prop: 'sixMonCount' },
        { label: '金额', prop: 'sixMonAmount' },
        { label: '台账数', prop: 'sixMonLedger' }
      ]
    },
    {
      label: '账龄1年以内',
      children: [
        { label: '个数', prop: 'oneYearCount' },
        { label: '金额', prop: 'oneYearAmount' },
        { label: '台账数', prop: 'oneYearLedger' }
      ]
    },
    {
      label: '账龄1-2年',
      children: [
        { label: '个数', prop: 'oneToTwoCount' },
        { label: '金额', prop: 'oneToTwoAmount' },
        { label: '台账数', prop: 'oneToTwoLedger' }
      ]
    },
    {
      label: '账龄2-3年',
      children: [
        { label: '个数', prop: 'twoToThreeCount' },
        { label: '金额', prop: 'twoToThreeAmount' },
        { label: '台账数', prop: 'twoToThreeLedger' }
      ]
    },
    {
      label: '账龄3年以上',
      children: [
        { label: '个数', prop: 'threeAboveCount' },
        { label: '金额', prop: 'threeAboveAmount' },
        { label: '台账数', prop: 'threeAboveLedger' }
      ]
    }
  ]

  // 生成文件名（包含时间戳）
  const now = new Date()
  const timestamp = now.getFullYear() +
    String(now.getMonth() + 1).padStart(2, '0') +
    String(now.getDate()).padStart(2, '0') + '_' +
    String(now.getHours()).padStart(2, '0') +
    String(now.getMinutes()).padStart(2, '0') +
    String(now.getSeconds()).padStart(2, '0')

  const fileName = `债务管理汇总_${timestamp}.xlsx`

  // 导出Excel
  await exporter.exportToExcel({
    data,
    headers,
    sheetName: '债务管理汇总',
    fileName,
    summaryData
  })
}
