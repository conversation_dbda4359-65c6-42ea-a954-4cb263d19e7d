<template>
  <div>
    <logo :collapse="false"/>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="$route.path"
          router
          :unique-opened="true"
          class="el-menu-vertical-demo"
          :active-Title-color="theme">
        <component
            v-for="route in menuList"
            :key="route.Id"
            :index="route.Title"
            :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'"
            :route="{path: route.Url}"
            :disabled="!!!route.HasPermi">
          <template slot="title">
            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
            <span slot="title" class="font-size14">{{ route.Title }}</span>
          </template>
          <template v-if="route.childList && route.childList.length > 0">
            <el-menu-item
                v-for="routeChild in route.childList"
                :key="routeChild.Id"
                :index="routeChild.Url"
                :route="{path: routeChild.Url}"
                :disabled="!!!routeChild.HasPermi">
              <span slot="title">{{ routeChild.Title }}</span>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin} from "api/login";
import {getModule} from "api/common";

export default {
  components: {Logo},
  data() {
    return {
      urlList: [],
      menuList: []
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  created() {
    MLogin().then(res => {
      if (res.success) {
        this.getMenuList()
        this.$store.dispatch('dict/GetDictDatas')
      } else {
        this.$router.push('/login')
      }
    })
  },
  methods: {
    async getMenuList() {
      await getModule().then(res => {
        const functionList = (res.data || []).filter(item => item.Id === 'JDWSJ02')
        this.urlList = functionList.flatMap(item => item.childList.map(child => child.Url));
        this.menuList = [
          {
            Id: "JDWSJ02EWF01",
            Title: "考试二维码",
            Url: "/sjks/ewm",
            Parameter: 0,
            leaf: true,
            DisplayName: "",
            HasPermi: this.urlList.indexOf('../../../../app/wfw/sjks/ewm') > -1
          },

        ]
        let firstUrl = ''
        this.menuList.forEach((value) => {
          if (firstUrl != '') {
            return
          }
          if (value.childList) {
            value.childList.forEach((param) => {
              if (firstUrl != '') {
                return
              }
              if (param.HasPermi) {
                firstUrl = param.Url
              }
            })
          }
          if (value.HasPermi) {
            firstUrl = value.Url
          }
        })
        //TODO 获取后台模块列表 默认跳转到第一个
        // if (this.$router.currentRoute.fullPath !== firstUrl) {
        //   this.$router.push(firstUrl)
        // }
        this.$forceUpdate()
      })
    }
  }
};
</script>
