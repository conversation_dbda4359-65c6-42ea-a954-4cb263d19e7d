<template>
  <div class="navbar" :style="{backgroundColor: theme}">
    <template v-if="!sidebar">
      <img :src="logo" class="title-logo" />
      <div class="breadcrumb-title text-weight font-size20">数字化平台</div>
    </template>
    
    <div class="right-menu">
      <template>
        <div class="right-menu-item hover-effect">
          <el-popover 
            placement="bottom"
            title="数字化平台运维热线"
            width="200"
            trigger="click"
            popper-class="text-center">
            <div class="content-contain" style="position:relative">
              <div style="height: 30px">
                0574-51103708
              </div>
              <i class="iconfont font-size28" style="position:absolute; right:0;bottom:0">&#xe623;</i>
            </div>
            <i class="iconfont font-size24" slot="reference">&#xe88b;</i>
          </el-popover>
        </div>
        
        <div class="right-menu-item hover-effect">
          <el-popover
            placement="bottom"
            trigger="click"
            popper-class="text-center">
            <div style="display:flex">
              <div>
                <div class="font-size14">安卓APP下载</div>
                <img src="@/assets/images/downloadApp.png" width="120"/>
              </div>
              <div>
                <div class="font-size14">苹果APP下载</div>
                <img src="@/assets/images/iosDownload.jpg" width="120"/>
              </div>
            </div>
            <i class="iconfont font-size20" slot="reference">&#xe605;</i>
          </el-popover>
        </div>
        <div class="right-menu-item">
          <i class="el-icon-date font-size18"></i>
          <span class="font-size14" style="margin-left:4px">{{currentDate}}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import logoImg from '@/assets/logo.png'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
export default {
  computed: {
    ...mapGetters([
      'sidebar'
    ]),
    theme() {
      return this.$store.state.settings.theme;
    },
    size() {
        return this.$store.state.settings.size
    },
    currentDate(){
      return dayjs().format('YYYY-MM-DD')
    },
    logo(){
      return logoImg
    }
  },
  mounted(){
    //新页面初始化字体大小
    window.document.documentElement.setAttribute("data-size",this.size)
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  // box-shadow: 0 1px 4px rgba(0,21,41,.08);
  padding: 0 20px;
  flex-shrink: 0;
  border-bottom: #ffffff55 1px solid;

  .hamburger{
    line-height: 46px;
    height: 100%;
    float: left;
    color: #fff;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .title-logo{
    margin: 10px 0;
    float: left;
    height: 32px;
  }
  .breadcrumb-title{
    color: #fff;
    float: left;
    line-height: 50px;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      color: #fff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }
  }

  .radio-group{
    display:flex;
    flex-direction:column;

    .radio-option{
      margin-bottom: 10px;
    }
  }

  .confirm-btn{
    text-align: right;
  }
}
</style>
