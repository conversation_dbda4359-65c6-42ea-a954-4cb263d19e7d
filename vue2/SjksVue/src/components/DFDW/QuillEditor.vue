<template>
  <div>
    <!-- 图片上传组件辅助 -->
    <el-upload
        class="avatar-uploader quill-img"
        :action="baseUrl + uploadUrl"
        name="file"
        :headers="headers"
        :show-file-list="false"
        :on-success="quillImgSuccess"
        :on-error="uploadError"
        :before-upload="quillImgBefore"
        accept='.jpg,.jpeg,.png,.gif'
    ></el-upload>

    <!-- 富文本组件 -->
    <quill-editor
        class="editor"
        v-model="content"
        ref="quillEditor"
        :options="editorOption"
        @blur="onEditorBlur($event)"
        @focus="onEditorFocus($event)"
        @change="onEditorChange($event)"
    ></quill-editor>
  </div>
</template>

<script>
/* eslint-disable */
import {getToken} from '@/utils/auth'
import {quillEditor, Quill} from 'vue-quill-editor'
// import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
// import 'quill/dist/quill.bubble.css'
// quill-image-resize-module该插件是用于控制上传的图片的大小
import ImageResize from 'quill-image-resize-module'
import Cookies from 'js-cookie'

Quill.register('modules/imageResize', ImageResize);

const fonts = ['Microsoft-YaHei', 'SimSun', 'SimHei', 'KaiTi', 'Arial', 'Times-New-Roman']
// 工具栏配置
const toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike'],    //加粗，斜体，下划线，删除线
  ['blockquote', 'code-block'],     //引用，代码块

  [{'header': 1}, {'header': 2}],        // 标题，键值对的形式；1、2表示字体大小
  [{'list': 'ordered'}, {'list': 'bullet'}],     //列表
  [{'script': 'sub'}, {'script': 'super'}],   // 上下标
  [{'indent': '-1'}, {'indent': '+1'}],     // 缩进
  [{'direction': 'rtl'}],             // 文本方向

  [{'size': ['small', false, 'large', 'huge']}], // 字体大小
  [{'header': [1, 2, 3, 4, 5, 6, false]}],     //几级标题

  [{'color': []}, {'background': []}],     // 字体颜色，字体背景颜色
  [{'align': []}],    //对齐方式
  [{'font': fonts}],     //字体
  ['clean'],    //清除字体样式
  ['image']    //上传图片、上传视频

]

export default {
  props: {
    /* 编辑器的内容 */
    value: {
      type: String
    },
    /* 图片大小 */
    maxSize: {
      type: Number,
      default: 4000 //kb
    },
    uploadUrl: {
      type: String,
      default: '/common/upload?fileType=zxxx&'
    },
  },
  components: {quillEditor},
  data() {
    return {
      content: this.value,
      editorOption: {
        theme: 'snow', // or 'bubble'
        placeholder: '请输入内容',
        modules: {
          // 调整图片大小
          imageResize: {
            displayStyles: {
              backgroundColor: 'black',
              border: 'none',
              color: 'white'
            },
            modules: ['Resize', 'DisplaySize', 'Toolbar']
          },
          toolbar: {
            container: toolbarOptions,
            handlers: {
              image: function (value) {
                if (value) {
                  // 触发input框选择图片文件
                  document.querySelector('.quill-img input').click()
                } else {
                  this.quill.format('image', false)
                }
              }
            }
          }
        },
      },
      baseUrl: process.env.VUE_APP_BASE_API + '/dwSjks',
      token: Cookies.get('jtoken'),
      headers: {
        'Authorization': getToken()
      }
    }
  },
  watch: {
    value: function () {
      this.content = this.value
    }
  },
  methods: {
    onEditorBlur() {
      //失去焦点事件
    },
    onEditorFocus() {
      //获得焦点事件
    },
    onEditorChange() {
      //内容改变事件
      this.$emit('input', this.content)
    },

    // 富文本图片上传前
    quillImgBefore(file) {

      let fileType = file.type
      if (fileType === 'image/jpeg' || fileType === 'image/png') {
        return true
      } else {
        this.$message.error('请插入图片类型文件(jpg/jpeg/png)')
        return false
      }
    },

    quillImgSuccess(res, file) {
      // res为图片服务器返回的数据
      // 获取富文本组件实例
      let quill = this.$refs.quillEditor.quill

      // 如果上传成功
      if (res.code == 200) {
        // 获取光标所在位置
        let length = quill.getSelection().index
        //
        // 插入图片  res.url为服务器返回的图片地址
        // 返回的参数是对象
        let url = 'data:image/png;base64,'
        this.getBase64(file.raw).then(resBase64 => {
          url = url + resBase64.split(',')[1]　　//直接拿到base64信息
          // console.log(url)
          quill.insertEmbed(length, 'image', url)
          // alert(res.url)
          // 调整光标到最后
          quill.setSelection(length + 1)
        })
      } else {
        this.$message.error('图片插入失败')
      }
    },
    // 富文本图片上传失败
    uploadError() {
      // loading动画消失
      this.$message.error('图片插入失败')
    },
    // file转base64
    getBase64(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        let fileResult = "";
        reader.readAsDataURL(file);　　　　　//开始转
        reader.onload = function () {
          fileResult = reader.result;
        };　　　　　//转 失败
        reader.onerror = function (error) {
          reject(error);
        };　　　　　//转 结束  咱就 resolve 出去
        reader.onloadend = function () {
          resolve(fileResult);
        };
      });
    }
  }
}
</script>

<style>
.editor {
  line-height: normal !important;
  height: 192px;
}

.quill-img {
  display: none;
}

.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "请输入链接地址:";
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: "保存";
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: "14px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
  content: "10px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
  content: "18px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
  content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: "文本";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "标题1";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "标题2";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "标题3";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: "标题4";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: "标题5";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: "标题6";
}

.ql-editor .ql-font-Microsoft-YaHei {
  font-family: "Microsoft YaHei", serif;
}

.ql-editor .ql-font-SimSun {
  font-family: "SimSun", serif;
}

.ql-editor .ql-font-SimHei {
  font-family: "SimHei", serif;
}

.ql-editor .ql-font-KaiTi {
  font-family: "KaiTi", serif;
}

.ql-editor .ql-font-Arial {
  font-family: "Arial", serif;
}

.ql-editor .Times-New-Roman {
  font-family: "Times New Roman", serif;
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '\5fae\8f6f\96c5\9ed1';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Microsoft-YaHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Microsoft-YaHei]::before {
  content: '\5fae\8f6f\96c5\9ed1';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimSun]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimSun]::before {
  content: '\5b8b\4f53';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimHei]::before {
  content: '\9ed1\4f53';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=KaiTi]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=KaiTi]::before {
  content: '\6977\4f53';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Arial]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Arial]::before {
  content: '\7b49\7ebf\4f53';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Times-New-Roman]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Times-New-Roman]::before {
  content: '\65b0\7f57\9a6c\5b57\4f53';
}
</style>
