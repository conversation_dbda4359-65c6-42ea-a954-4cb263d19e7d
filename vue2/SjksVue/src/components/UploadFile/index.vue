<template>
    <el-upload
        style="display:inline-block;"
        :show-file-list="false"
        action="#"
        ref="upload"
        :file-list="fileList"
        type="primary"
        :on-change="uploadFile"
        :auto-upload="false"
    >
        <el-button :icon="btnIcon" type="text" class="btn-style" >{{btnName}}</el-button>
    </el-upload>
</template>

<script>
export default {
    name: 'index',
    props:{
        // 文件类型, 例如["docx", "doc"]
        fileType: {
            type: Array,
            default: () => ["doc","docx","xls","xlsx","ppt","rar","zip","txt","mp3","rm","jpg","jpeg","gif","bmp","png","pdf","wps","ceb","dwg"],
        },
        btnName:{
            type:String,
            default: '新增'
        },
        btnIcon:{
            type:String,
            default: 'el-icon-plus'
        },
        limit:{
            type:Number,
            default: 1
        }
    },
    data(){
        return {
            fileList:[],
        }
    },
    methods:{
        uploadFile(file,fileList){
            const fileTemp = file.raw
            const fileName = file.raw.name
            const upFileType = fileName.substring(fileName.lastIndexOf('.') + 1)
            // 判断上传文件格式
            if (fileTemp) {
                const isRight = this.fileType.some(item => item === upFileType)
                if (isRight) {
                    const formData = new FormData()
                    formData.append('file', file.raw)
                    this.$emit('importFile',formData)//传递数据到父组件调用接口
                } else {
                    this.$message({
                        type: 'warning',
                        message: '附件格式错误，请重新上传！'
                    })
                }
            } else {
                this.$message({
                    type: 'warning',
                    message: '请上传附件！'
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.btn-style{
    margin-right: 10px;
}
</style>