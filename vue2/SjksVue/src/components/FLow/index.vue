<template>
  <el-dialog
      width="25%"
      :visible="showDialog"
      size="tiny"
      center
      :destroy-on-close="true"
      :modal-append-to-body="false"
      @close="closeShowDialog"
      :close-on-click-modal="false"
  >
    <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
    <el-form ref="form" :model="form" :rules="rules" label-width="110px">
      <slot name="formSearch" :row="form"></slot>
      <el-row>
        <el-col :span="24" v-if="lcJdId != firstLcJd">
          <el-form-item label="审批" prop="approvalOpinion">
            <el-radio-group v-model="form.approvalOpinion" @change="handleChangeApp">
              <el-radio label="agree">同意</el-radio>
              <el-radio label="reject" v-if="!rejectDisable">驳回</el-radio>
              <el-radio label="termination" v-if="!terminationDisable">终止</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" v-if="form.approvalOpinion === 'agree'">
          <el-form-item label="审批人" prop="sendPersonId">
            <multiple-column-selection
                v-model="form.sendPersonId"
                valueKey="id"
                label="realName"
                placeholder="请选择审批人"
                :filterable="true"
                :multiple="personMultiple"
                :tableWidth="600"
                :disabled="form.sendPersonId === -1"
                :options="resultParams.personList"
                :tableOptions="tableOptions"
                :show-more="true"
                @change="handleChangePerson"
                @selectMore="selectMorePerson"
                style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="审批意见" prop="feeds">
            <el-input v-model="form.feeds" type="textarea" :rows="3" placeholder="请输入审批意见"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeShowDialog()">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="submitDialog()">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {toGet, toPost} from "api/flow/approve";
import MultipleColumnSelection from "components/MultipleColumnSelection/index.vue";

export default {
  name: "Flow",
  components: { MultipleColumnSelection },
  props: {
    // 流程定义id
    lcDefineId: {
      type: Number,
      required: true
    },
    // 当前流程节点id(申请时为0)
    lcJdId: {
      type: Number,
      required: true
    },
    // 业务id
    ywId: {
      type: Number,
      default: null
    },
    // 开始流程节点id
    firstLcJd: {
      type: Number,
      required: true
    },
    // 部门权限控制，-1不做控制，0当前部门 ,1TopDeptId
    isShowAll: {
      type: Number,
      default: -1
    },
    // 是否能选择自己 0不可以, 1可以
    initChoose: {
      type: Number,
      default: 0
    },
    // 自定义Form表单
    initForm: {
      type: Object,
      default: () => {}
    },
    // 自定义Form表单校验规则
    initRules: {
      type: Object,
      default: () => {}
    },
    // 是否禁用驳回流程
    rejectDisable: {
      type: Boolean,
      default: false
    },
    // 是否禁用终止流程
    terminationDisable: {
      type: Boolean,
      default: false
    },
    // 提交前置函数
    beforeSubmit: {
      type: Function,
      default: null
    },
    // 审批人员多选
    personMultiple: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    ywId: {
      handler(val) {
        if (val != null) {
          this.getPersonList();
        } else {
          this.resultParams.personList = []
        }
      },
      deep: true,
      immediate: true
    },
    initForm: {
      handler(val) {
        this.form = {...this.defaultform, ...val};
      },
      deep: true,
      immediate: true
    },
    initRules: {
      handler(val) {
        this.rules = {...this.defaultRules, ...val};
      },
      deep: true,
      immediate: true
    },
    lcJdId: {
      handler(val) {
        this.$nextTick(() => {
          if (val === this.firstLcJd) {
            this.form.feeds = '请审批';
          }
        })
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      // 弹窗是否可见
      showDialog: false,
      // 弹窗标题
      dialogTitle: '审 批',
      // 弹窗默认Form表单
      defaultform: {
        approvalOpinion: 'agree',
        sendPersonId: null,
        sendPersonName: null,
        feeds: '同意'
      },
      // 弹窗Form表单
      form: {},
      // 弹窗默认Params
      resultParams: {
        personList: [],
        lcNextJdId: 0,
      },
      // 弹窗默认Form表单校验规则
      defaultRules: {
        approvalOpinion: [
          {required: true, message: '请选择审批意见', trigger: 'blur'}
        ],
        sendPersonId: [
          {required: true, message: '请选择审批人', trigger: 'blur'}
        ],
        feeds: [
          {required: true, message: '请输入审批意见', trigger: 'blur'}
        ]
      },
      // 弹窗Form表单校验规则
      rules: {},
      // 提交按钮Loading
      btnLoading: false,
      // 审批人员多列选择器配置
      tableOptions: [
        { label: '工号', prop: 'loginName', filter: true},
        { label: '姓名', prop: 'realName', filter: true},
        { label: '部门', prop: 'topGroupName', filter: true},
        { label: '节点名称', prop: 'lcNextJdmc', filter: false}
      ],
      // 审批人选择更多
      selectMorePerson: false,
    }
  },
  methods: {
    openShowDialog() {
      this.showDialog = true;
      this.form = {...this.defaultform, ...this.initForm};
    },
    closeShowDialog() {
      this.showDialog = false;
      this.$emit('close');
    },
    getPersonList() {
      this.$set(this.form, 'sendPersonId', null);
      this.$set(this.form, 'sendPersonName', null);
      toGet({
        url: '/dwFlow/flow/dfdwApprove/getNextPerson',
        lcDefineID: this.lcDefineId,
        lcJdId: this.lcJdId,
        ywId: this.ywId,
        applyUserId: this.$store.getters.userid,
        isShowAll: this.isShowAll,
        initChoose: this.initChoose,
        selectMorePerson: this.selectMorePerson
      }).then(res => {
        this.resultParams = res.result;
        this.resultParams.personList.forEach(item => {
          item.lcNextJdmc = res.result.lcNextJdmc;
        })
        if (this.resultParams.personList.length === 0 && this.resultParams.lcNextJdId === 0) {
          this.$set(this.form, 'sendPersonId', -1);
          this.$set(this.form, 'sendPersonName', '完成');
          this.$set(this.defaultform, 'sendPersonId', -1);
          this.$set(this.defaultform, 'sendPersonName', '完成');
          this.resultParams.personList.push({
            id: -1,
            realName: '完成'
          })
        }
        if (this.resultParams.personList.length === 1) {
          this.$set(this.form, 'sendPersonId', this.resultParams.personList[0].id);
          this.$set(this.form, 'sendPersonName', this.resultParams.personList[0].realName);
          this.$set(this.defaultform, 'sendPersonId', this.resultParams.personList[0].id);
          this.$set(this.defaultform, 'sendPersonName', this.resultParams.personList[0].realName);
        }
      })
    },
    handleChangePerson(value) {
      if (Array.isArray(value)) {
        let names = [];
        value.forEach(item => {
          names.push(this.resultParams.personList.find(person => person.id === item).realName);
        })
        this.form.sendPersonName = names.join(',');
      } else {
        this.form.sendPersonName = this.resultParams.personList.find(item => item.id === value).realName;
      }
    },
    handleChangeApp(value) {
      if (value === 'agree') {
        this.form.feeds = '同意';
      } else if (value === 'reject') {
        this.form.feeds = '驳回';
      } else if (value === 'termination') {
        this.form.feeds = '终止';
      }
      this.$emit('approvalOpinion', value)
    },
    async submitDialog() {
      this.btnLoading = true;
      const params = {
        lcDefineId: this.lcDefineId,
        lcJdId: this.lcJdId,
        ywId: this.ywId,
        lcNextJdId: this.resultParams.lcNextJdId,
        ...this.form
      }
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            if (this.beforeSubmit != null) {
              const res = await this.beforeSubmit(params);
              if (res.code !== 200) {
                this.btnLoading = false;
                return;
              }
            }
            switch (this.form.approvalOpinion) {
              case 'agree':
                toPost({
                  url: '/dwFlow/flow/dfdwApprove/submitLc',
                  ...params
                }).then(res => {
                  this.$message.success('流程提交成功');
                  this.showDialog = false;
                  this.$emit('agree', params);
                }).finally(() => {
                  this.btnLoading = false;
                })
                break;
              case 'reject':
                toPost({
                  url: '/dwFlow/flow/dfdwApprove/rollBackForApply',
                  ...params
                }).then(res => {
                  this.$message.success('流程驳回成功');
                  this.showDialog = false;
                  this.$emit('reject', params);
                }).finally(() => {
                  this.btnLoading = false;
                })
                break;
              case 'termination':
                toPost({
                  url: '/dwFlow/flow/dfdwApprove/rollBackForEnd',
                  ...params
                }).then(res => {
                  this.$message.success('流程终止成功');
                  this.showDialog = false;
                  this.$emit('termination', params);
                }).finally(() => {
                  this.btnLoading = false;
                })
                break;
              default:
                this.btnLoading = false;
                return;
            }
          } catch (e) {
            this.btnLoading = false;
          }
        } else {
          this.btnLoading = false;
        }
      });
    },
    selectMorePerson(value) {
      this.selectMorePerson = value;
      this.getPersonList();
    }
  }
}
</script>

<style scoped lang="less">
.dialogTitle {
  font-weight: bold;
}
</style>
