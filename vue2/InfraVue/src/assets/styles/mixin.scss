@mixin add_size($val) {
    // font-size: $val;
    [data-size="mini"] & {
        font-size: ($val + $size_mini) !important;
    }
    [data-size="small"] & {
        font-size: ($val + $size_small) !important;
    }
    [data-size="medium"] & {
        font-size: ($val + $size_medium) !important;
    }
}

@mixin scrollBar {
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }
  
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }