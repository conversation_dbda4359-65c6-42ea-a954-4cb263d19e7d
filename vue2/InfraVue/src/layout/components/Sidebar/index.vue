<template>
  <div>
    <logo :collapse="false"/>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="$route.path"
          router
          :unique-opened="true"
          class="el-menu-vertical-demo"
          :active-Title-color="theme">
        <component
            v-for="route in menuList"
            :key="route.Id"
            :index="route.Title"
            :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'">
          <template slot="title">
            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
            <span slot="title" class="font-size14">{{route.Title}}</span>
          </template>
          <template v-if="route.childList && route.childList.length > 0">
            <el-menu-item
                v-for="routeChild in route.childList"
                :key="routeChild.Id"
                :index="routeChild.Url"
                :route="{path: routeChild.Url}"
                :disabled="!routeChild.HasPermi">
              <span slot="title">{{routeChild.Title}}</span>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin} from "api/login";
import {getModule} from "api/common";
import store from '@/store'

export default {
  components: {Logo},
  data() {
    return {
      urlList: [],
      menuList: []
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  created() {
    MLogin().then(res => {
      if (res.success) {
        // 获取字典数据
        store.dispatch('dict/loadDictDatas')
        this.getMenuList()
      } else {
        this.$router.push('/login')
      }
    })
  },
  methods: {
    async getMenuList() {
      await getModule().then(res => {
        const functionList = res.data || []
        this.urlList = []
        functionList.forEach(item => {
          this.urlList.push(...item.childList.map((value) => {
            return value.Url
          }))
        })
        const permissions = store.getters && store.getters.permissions
        let firstUrl = ''
        this.menuList = [
          {
            Id: 'JDWCN01',
            Title: '代码生成',
            childList: [
              {
                Id: "JDWGB01ZD01",
                Title: "字典管理",
                Url: "/infra/zdgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: permissions.some(permission => {
                  return 'JDWGB01ZD01QX01' === permission
                })
              },
              {
                Id: "DMSCQ",
                Title: "代码生成器",
                Url: "/infra/codegen",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: true
              },
              {
                Id: "JDWIN01TT01",
                Title: "代码生成器测试",
                Url: "/infra/test",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/infra/test') > -1
              },
            ]
          }
        ]
        this.menuList.forEach((value) => {
          if (firstUrl != '') {
            return
          }
          value.childList.forEach((param) => {
            if (firstUrl != '') {
              return
            }
            if (param.HasPermi) {
              firstUrl = param.Url
            }
          })
        })
        //TODO 获取后台模块列表 默认跳转到第一个
        if (this.$router.currentRoute.fullPath !== firstUrl) {
          this.$router.push(firstUrl)
        }
        this.$forceUpdate()
      })
    }
  }
};
</script>
