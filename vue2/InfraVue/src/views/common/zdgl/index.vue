<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <!-- 搜索工作栏 -->
        <span class="font-size14">字典名称：</span>
        <el-input v-model="queryParams.name" placeholder="请输入字典名称" clearable
                  @keyup.enter.native="handleQuery"/>
        <span class="font-size14">字典类型：</span>
        <el-input v-model="queryParams.type" placeholder="请输入字典类型" clearable
                  @keyup.enter.native="handleQuery"/>
        <span class="font-size14">状态：</span>
        <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery" clearable>
          <el-option label="启用" value="0"></el-option>
          <el-option label="停用" value="1"></el-option>
        </el-select>
        <span class="font-size14">创建时间：</span>
        <el-date-picker v-model="queryParams.createTimeArray" value-format="yyyy-MM-dd HH:mm:ss"
                        type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"/>

        <el-button size="medium" type="text" icon="el-icon-search" v-has-permi="['JDWGB01ZD01QX01']"
                   @click="handleQuery">搜索
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-refresh" v-has-permi="['JDWGB01ZD01QX01']"
                   @click="resetQuery">重置
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-plus" v-has-permi="['JDWGB01ZD01QX02']"
                   @click="handleAdd">新增
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-edit" v-has-permi="['JDWGB01ZD01QX03']"
                   @click="handleUpdate">修改
        </el-button>
        <el-button size="medium" type="text" icon="el-icon-delete" v-has-permi="['JDWGB01ZD01QX04']"
                   @click="handleDelete">删除
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="typeList"
          :tableOptions="tableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="selectTypeRow"
      >
        <!-- 类型-->
        <template slot="type" slot-scope="scope">
          <a @click="handleClick(scope.row)" style="color:blue;cursor:pointer">{{ scope.row.type }} </a>
        </template>
        <!-- 状态-->
        <template slot="status" slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status === 0">启用</el-tag>
          <el-tag type="danger" v-if="scope.row.status === 1">停用</el-tag>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" :before-close="cancel" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入字典名称"/>
        </el-form-item>
        <el-form-item label="字典类型" prop="type">
          <el-input v-model="form.type" placeholder="请输入字典类型"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">开启</el-radio>
            <el-radio :label="1">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 字典数据弹窗-->
    <dict-data
        ref="dictData"
        v-if="dataVisible"
        @refreshDataList="getList"
    ></dict-data>

  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import {updateType, addType, listType, delType} from '@/api/zdgl/type';
import DictData from "views/common/zdgl/dictData.vue";
import Pagination from "components/Pagination/index.vue";

export default {
  components: {Pagination, DictData, Table},
  data() {
    return {
      title: '',
      loading: false,
      open: false,
      dataVisible: false,
      typeList: [],
      typeRowData: {},
      // 表单参数
      form: {
        name: null,
        type: null,
        status: 0,
        remark: null,
      },
      // 表单校验
      rules: {
        name: [{required: true, message: "字典名称不能为空", trigger: "blur"}],
        type: [{required: true, message: "字典类型不能为空", trigger: "blur"}],
        status: [{required: true, message: "字典状态不能为空", trigger: "blur"}]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        name: undefined,
        type: undefined,
        status: undefined,
        createTimeArray: []
      },
      tableOptions: [
        {label: '字典编号', prop: 'id'},
        {label: '字典名称', prop: 'name'},
        {label: '字典类型', prop: 'type', slot: true},
        {label: '状态', prop: 'status', slot: true},
        {label: '备注', prop: 'remark'},
        {label: '创建时间', prop: 'createTime'}
      ],
    }
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      this.open = true;
      this.title = "添加字典类型";
    },
    //修改
    handleUpdate() {
      if (this.typeRowData.id == undefined || this.typeRowData.id === null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        this.open = true;
        this.title = "修改字典类型";
        this.form = this.typeRowData
      }
    },
    //删除
    handleDelete() {
      if (this.typeRowData.id == undefined || this.typeRowData.id === null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        this.$confirm('删除该字典<' + this.typeRowData.name + '>, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delType(this.typeRowData.id).then(res => {
            if (res.code === 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.resetForm('form')
              this.handleQuery();
              this.typeRowData = {}
            } else {
              this.$message({
                message: '删除失败;' + res.getMessage(),
                type: 'warning'
              });
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined && this.form.id !== "") {
            updateType(this.form).then(response => {
              if (response.code === 200) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                });
                this.resetForm('form')
                this.open = false;
                this.getList();
                this.typeRowData = {}
              } else {
                this.$message({
                  message: '修改失败;' + response.getMessage(),
                  type: 'warning'
                });
              }
            });
          } else {
            addType(this.form).then(response => {
              if (response.code === 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                });
                this.resetForm('form')
                this.open = false;
                this.getList();
              } else {
                this.$message({
                  message: '新增失败;' + response.getMessage(),
                  type: 'warning'
                });
              }
            });
          }
        }
      });
    },
    //类型点击
    handleClick(row) {
      this.dataVisible = true
      this.$nextTick(() => {
        this.$refs.dictData.tableName = "《" + row.name + "》-数据管理"
        this.$refs.dictData.dictTypeData = row;
        this.$refs.dictData.init()
      });
    },
    //点击行事件
    selectTypeRow(row) {
      // console.log(row)
      this.typeRowData = row
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      //查询
      listType(this.queryParams).then(res => {
        //创建时间剔除毫秒
        res.result.records.forEach(item => {
          item.createTime = item.createTime.slice(0, 19)
        })
        this.queryParams.total = res.result.total
        this.typeList = res.result.records
      })
    },
    //取消
    cancel() {
      this.open = false
      this.resetForm('form')
      this.typeRowData = {}
      this.handleQuery()
    },
    //清空表单
    resetForm(formName) {
      for (var key in this[formName]) {
        if (key === 'status') {
          this[formName][key] = 0
        } else {
          this[formName][key] = ''
        }
      }
    },
    //清空查询参数
    rest() {
      this.queryParams.createTimeArray.splice(0)
      // 查询参数
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.total = 0
      this.queryParams.name = null
      this.queryParams.type = null
      this.queryParams.status = null
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.rest();
      this.handleQuery();
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getList()
    },
  }
};
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
</style>