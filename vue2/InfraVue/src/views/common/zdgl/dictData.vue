<template>
  <!-- 字典数据弹窗   -->
  <div class="search-container">
    <el-dialog :title="tableName" :visible.sync="visible" width="70%">

      <div class="operate-pannel">
        <div class="search-box">
          <!-- 搜索工作栏 -->
          <span class="font-size14">字典标签：</span>
          <el-input v-model="queryParams.label" placeholder="请输入字典标签" clearable
                    @keyup.enter.native="handleQuery"/>
          <span class="font-size14">状态：</span>
          <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery" clearable>
            <el-option label="停用" value="1"></el-option>
            <el-option label="启用" value="0"></el-option>
          </el-select>

          <el-button size="medium" type="text" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button size="medium" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          <el-button size="medium" type="text" icon="el-icon-plus" @click="handleAdd">新增</el-button>
          <el-button size="medium" type="text" icon="el-icon-edit" @click="handleUpdate()">修改</el-button>
          <el-button size="medium" type="text" icon="el-icon-delete" @click="handleDelete()">删除</el-button>
        </div>
      </div>

      <div class="table-box">
        <Table
            height="480px"
            :tableData="dataList"
            :tableOptions="tableOptions"
            :loading="loading"
            :queryParam="queryParams"
            @getCurrentData="selectDataRow"
        >
          <!-- 状态-->
          <template slot="status" slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status === 0">启用</el-tag>
            <el-tag type="danger" v-if="scope.row.status === 1">停用</el-tag>
          </template>
        </Table>
        <Pagination
            @handleRefresh="handleCurrentChange"
            :queryParam="queryParams"
            layout="total, prev, pager, next, jumper"
            :total="queryParams.total"
        />
      </div>

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="addOrUpdateTitle" :visible.sync="open" width="600px" :before-close="cancel"
                 :append-to-body="true">
        <el-form ref="form" :model="form" :rules="rules" label-width="90px">
          <el-form-item label="字典类型">
            <el-input v-model="form.dictType" :disabled="true"/>
          </el-form-item>
          <el-form-item label="数据标签" prop="label">
            <el-input v-model="form.label" placeholder="请输入数据标签"/>
          </el-form-item>
          <el-form-item label="数据键值" prop="value">
            <el-input v-model="form.value" placeholder="请输入数据键值"/>
          </el-form-item>
          <el-form-item label="显示排序" prop="sort">
            <el-input-number v-model="form.sort" controls-position="right" :min="0"/>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="0">开启</el-radio>
              <el-radio :label="1">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import {addData, updateData, listData, delData} from "api/zdgl/data";
import Pagination from "components/Pagination/index.vue";
import Table from "components/MainTable/index.vue";

export default {
  components: {Table, Pagination},
  data() {
    return {
      dataList: [],
      dictTypeData: {},
      visible: false,
      loading: false,
      open: false,
      tableName: '',
      addOrUpdateTitle: '',
      // 表单参数
      form: {
        dictType: null,
        label: null,
        value: null,
        sort: null,
        status: 0,
        remark: null
      },
      // 表单校验
      rules: {
        label: [{required: true, message: "字典标签不能为空", trigger: "blur"}],
        value: [{required: true, message: "字典键值不能为空", trigger: "blur"}],
        status: [{required: true, message: "字典状态不能为空", trigger: "blur"}],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        label: undefined,
        dictType: undefined,
        status: undefined,
      },
      tableOptions: [
        {label: '字典编号', prop: 'id'},
        {label: '字典标签', prop: 'label'},
        {label: '字典键值', prop: 'value'},
        {label: '状态', prop: 'status', slot: true},
        {label: '备注', prop: 'remark'},
        {label: '字典排序', prop: 'sort'},
        {label: '创建时间', prop: 'createTime'}
      ],
      dataRow: {}
    };
  },
  created() {

  },
  methods: {
    init() {
      this.visible = true;
      this.queryParams.dictType = this.dictTypeData.type
      this.tableName = this.dictTypeData.name + "-数据管理"
      this.handleQuery()
    },
    getList() {
      this.queryParams.pageNum = 1
      this.handleQuery()
    },
    handleQuery() {
      listData(this.queryParams).then(res => {
        //创建时间剔除毫秒
        res.result.records.forEach(item => {
          item.createTime = item.createTime.slice(0, 19)
        })
        this.queryParams.total = res.result.total
        this.dataList = res.result.records
      })
    },
    //数据新增
    handleAdd() {
      this.addOrUpdateTitle = '字典数据新增'
      this.form.dictType = this.dictTypeData.type
      this.open = true;
    },
    //修改
    handleUpdate() {
      if (this.dataRow.id == undefined || this.dataRow.id === null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        this.addOrUpdateTitle = '字典数据修改'
        this.form = this.dataRow
        this.open = true;
      }
    },
    //删除
    handleDelete() {
      if (this.dataRow.id == undefined || this.dataRow.id === null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        delData(this.dataRow.id).then(res => {
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          this.getList();
        })
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined && this.form.id !== "") {
            updateData(this.form).then(response => {
              if (response.code === 200) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                });
                this.resetForm('form')
                this.open = false;
                this.handleQuery();
              } else {
                this.$message({
                  message: '修改失败;' + response.getMessage(),
                  type: 'warning'
                });
              }

            });
          } else {
            addData(this.form).then(response => {
              if (response.code === 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                });
                this.resetForm('form')
                this.open = false;
                this.handleQuery();
              } else {
                this.$message({
                  message: '新增失败;' + response.getMessage(),
                  type: 'warning'
                });
              }
            });
          }
        }
      });
    },
    //选择行
    selectDataRow(row) {
      this.dataRow = row
    },
    //新增修改弹窗取消
    cancel() {
      this.open = false;
      this.resetForm('form')
      this.dataRow = {}
      this.handleQuery();
    },
    //清空表单
    resetForm(formName) {
      for (var key in this[formName]) {
        if (key === 'status') {
          this[formName][key] = 0
        } else {
          this[formName][key] = ''
        }
      }
    },
    //清空查询参数
    rest() {
      // 查询参数
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.total = 10
      this.queryParams.label = undefined
      this.queryParams.status = undefined
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.rest();
      this.handleQuery();
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.handleQuery()
    },
  }
};
</script>

<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
