<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <!-- 搜索工作栏 -->
        <span class="font-size14">表名称：</span>
        <el-input v-model="queryParams.tableName" placeholder="请输入表名称" clearable
                  @keyup.enter.native="handleQuery"/>
        <span class="font-size14">表描述：</span>
        <el-input v-model="queryParams.tableComment" placeholder="请输入表描述" clearable
                  @keyup.enter.native="handleQuery"/>
        <span class="font-size14">创建时间：</span>
        <el-date-picker v-model="queryParams.createTimeArray" value-format="yyyy-MM-dd HH:mm:ss" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"/>

        <el-button size="medium" type="text" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button size="medium" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button size="medium" type="text" icon="el-icon-upload" @click="openImportTable">导入</el-button>
      </div>
    </div>

    <div class="table-box">
      <Table
          :tableData="tableList"
          :tableOptions="tableOptions"
          :loading="loading"
          :queryParam="queryParams"
      >
        <template v-slot:dataSourceConfigId="scope">
          {{ dataSourceConfigNameFormat(scope.row) }}
        </template>
        <template v-slot:createTime="scope">
          {{ getDate(scope.row.createTime) }}
        </template>
        <template v-slot:updateTime="scope">
          {{ getDate(scope.row.updateTime) }}
        </template>
        <template v-slot:operate="scope">
          <el-button size="medium" type="text" icon="el-icon-view" @click="handlePreview(scope.row)">预览</el-button>
          <el-button size="medium" type="text" icon="el-icon-edit" @click="handleEditTable(scope.row)">编辑</el-button>
          <el-button size="medium" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          <el-button size="medium" type="text" icon="el-icon-refresh" @click="handleSynchDb(scope.row)">同步</el-button>
          <el-button size="medium" type="text" icon="el-icon-download" @click="handleGenTable(scope.row)">生成代码</el-button>
        </template>
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <!-- 预览界面 -->
    <el-dialog :title="preview.title" :visible.sync="preview.open" width="90%" top="5vh" append-to-body class="scrollbar">
      <el-row>
        <el-col :span="7">
          <el-tree :data="preview.fileTree" :expand-on-click-node="false" default-expand-all highlight-current
                   @node-click="handleNodeClick"/>
        </el-col>
        <el-col :span="17">
          <el-tabs v-model="preview.activeName">
            <el-tab-pane v-for="item in preview.data" :label="item.filePath.substring(item.filePath.lastIndexOf('/') + 1)"
                         :name="item.filePath" :key="item.filePath">
              <el-link :underline="false" icon="el-icon-document-copy" v-clipboard:copy="item.code" v-clipboard:success="clipboardSuccess" style="float:right">复制</el-link>
              <pre><code class="hljs" v-html="highlightedCode(item)"></code></pre>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </el-dialog>

    <!-- 基于 DB 导入 -->
    <import-table ref="import" @ok="handleQuery" />

    <!-- 编辑 -->
    <el-dialog :title="editTitle" :visible.sync="editVisible" width="1700px" top="5vh" append-to-body>
      <editTable ref="editTable" :tableId="tableId" @close="editClose"/>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCodegenTablePage,
  previewCodegen,
  downloadCodegen,
  deleteCodegen,
  syncCodegenFromDB
} from "@/api/codegen/codegen";
import {getDataSourceConfigList} from "@/api/codegen/dataSourceConfig";

import Table from "components/MainTable/index.vue";
import Pagination from "components/Pagination/index.vue";
import importTable from "./importTable";
import editTable from "./editTable.vue";

// 代码高亮插件
import hljs from "highlight.js/lib/highlight";
import "highlight.js/styles/github-gist.css";
import {downLoad, getDate} from "@/utils/tool";
import {MessageBox} from "element-ui";
import {handleTree} from "@/utils/ruoyi";

hljs.registerLanguage("java", require("highlight.js/lib/languages/java"));
hljs.registerLanguage("xml", require("highlight.js/lib/languages/xml"));
hljs.registerLanguage("html", require("highlight.js/lib/languages/xml"));
hljs.registerLanguage("vue", require("highlight.js/lib/languages/xml"));
hljs.registerLanguage("javascript", require("highlight.js/lib/languages/javascript"));
hljs.registerLanguage("sql", require("highlight.js/lib/languages/sql"));
hljs.registerLanguage("typescript", require("highlight.js/lib/languages/typescript"));
export default {
  name: "codegen",
  components: {Pagination, Table, importTable, editTable},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 唯一标识符
      uniqueId: "",
      // 选中表数组
      tableNames: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表数据
      tableList: [],
      // 日期范围
      dateRange: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tableName: undefined,
        tableComment: undefined,
        createTimeArray: []
      },
      // 预览参数
      preview: {
        open: false,
        title: "代码预览",
        fileTree: [],
        data: {},
        activeName: "",
      },
      // 数据源列表
      dataSourceConfigs: [],
      // 表格配置
      tableOptions: [
        {label: '数据源', prop: 'dataSourceConfigId', slot: true},
        {label: '表名称', prop: 'tableName'},
        {label: '表描述', prop: 'tableComment'},
        {label: '实体', prop: 'className'},
        {label: '创建时间', prop: 'createTime', slot: true},
        {label: '更新时间', prop: 'updateTime', slot: true},
        {label: '操作', prop: 'operate', slot: true},
      ],
      // 选择表id
      tableId: 0,
      // 编辑dialog
      editVisible: false,
      editTitle: '',
    }
  },
  created() {
    this.getList();
    // 加载数据源
    getDataSourceConfigList().then(response => {
      this.dataSourceConfigs = response.data;
    });
  },
  methods: {
    getDate,
    /** 查询表集合 */
    getList() {
      this.loading = true;
      getCodegenTablePage(this.queryParams).then(response => {
            this.tableList = response.data.records;
            this.total = response.data.total;
            this.loading = false;
          }
      );
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 生成代码操作 */
    handleGenTable(row) {
      downloadCodegen(row.id).then(response => {
        downLoad(response, 'codegen-' + row.tableName + '.zip')
      })
    },
    /** 同步数据库操作 */
    handleSynchDb(row) {
      // 基于 DB 同步
      const tableName = row.tableName;
      MessageBox.confirm('确认要强制同步"' + tableName + '"表结构吗？').then(function () {
        return syncCodegenFromDB(row.id);
      }).then(() => {
        this.$message.success("同步成功");
      }).catch(() => {
      });
    },
    /** 打开导入表弹窗 */
    openImportTable() {
      this.$refs.import.show();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        tableName: undefined,
        tableComment: undefined,
        createTimeArray: []
      }
      this.handleQuery();
    },
    /** 预览按钮 */
    handlePreview(row) {
      previewCodegen(row.id).then(response => {
        this.preview.data = response.data;
        let files = this.handleFiles(response.data);
        this.preview.fileTree = handleTree(files, "id", "parentId", "children", "/"); // "/" 为根节点
        // console.log(this.preview.fileTree)
        this.preview.activeName = response.data[0].filePath;
        this.preview.open = true;
      });
    },
    /** 高亮显示 */
    highlightedCode(item) {
      // const vmName = key.substring(key.lastIndexOf("/") + 1, key.indexOf(".vm"));
      // var language = vmName.substring(vmName.indexOf(".") + 1, vmName.length);
      const language = item.filePath.substring(item.filePath.lastIndexOf('.') + 1)
      const result = hljs.highlight(language, item.code || "", true);
      return result.value || '&nbsp;';
    },
    /** 复制代码成功 */
    clipboardSuccess() {
      this.$message.success("复制成功");
    },
    /** 生成 files 目录 **/
    handleFiles(datas) {
      let exists = {}; // key：file 的 id；value：true
      let files = [];
      // 遍历每个元素
      for (const data of datas) {
        let paths = data.filePath.split('/');
        let fullPath = ''; // 从头开始的路径，用于生成 id
        // 特殊处理 java 文件
        if (paths[paths.length - 1].indexOf('.java') >= 0) {
          let newPaths = [];
          for (let i = 0; i < paths.length; i++) {
            let path = paths[i];
            if (path !== 'java') {
              newPaths.push(path);
              continue;
            }
            newPaths.push(path);
            // 特殊处理中间的 package，进行合并
            let tmp = undefined;
            while (i < paths.length) {
              path = paths[i + 1];
              if (path === 'controller'
                  || path === 'service'
                  || path === 'vo'
                  || path === 'entity'
                  || path === 'mapper'
                  || path === 'impl') {
                break;
              }
              tmp = tmp ? tmp + '.' + path : path;
              i++;
            }
            if (tmp) {
              newPaths.push(tmp);
            }
          }
          paths = newPaths;
        }
        // 遍历每个 path， 拼接成树
        for (let i = 0; i < paths.length; i++) {
          // 已经添加到 files 中，则跳过
          let oldFullPath = fullPath;
          // 下面的 replaceAll 的原因，是因为上面包处理了，导致和 tabs 不匹配，所以 replaceAll 下
          fullPath = fullPath.length === 0 ? paths[i] : fullPath.replaceAll('.', '/') + '/' + paths[i];
          if (exists[fullPath]) {
            continue;
          }
          // 添加到 files 中
          exists[fullPath] = true;
          files.push({
            id: fullPath,
            label: paths[i],
            parentId: oldFullPath || '/'  // "/" 为根节点
          });
        }
      }
      return files;
    },
    /** 节点单击事件 **/
    handleNodeClick(data, node) {
      if (node && !node.isLeaf) {
        return false;
      }
      // 判断，如果非子节点，不允许选中
      this.preview.activeName = data.id;
    },
    /** 修改按钮操作 */
    handleEditTable(row) {
      const tableId = row.id;
      const tableName = row.tableName || this.tableNames[0];
      this.tableId = tableId;
      this.editTitle = "修改[" + tableName + "]生成配置";
      this.editVisible = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tableIds = row.id;
      MessageBox.confirm('是否确认删除表名称为"' + row.tableName + '"的数据项?').then(function () {
        return deleteCodegen(tableIds);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      }).catch(() => {
      });
    },
    // 数据源配置的名字
    dataSourceConfigNameFormat(row, column) {
      for (const config of this.dataSourceConfigs) {
        if (row.dataSourceConfigId === config.id) {
          return config.name;
        }
      }
      return '未知';
    },
    /** 编辑按钮关闭操作 */
    editClose() {
      this.editVisible = false;
      this.getList();
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  padding: 0;
}

.el-tag + .el-tag {
  margin-left: 10px;
}
</style>