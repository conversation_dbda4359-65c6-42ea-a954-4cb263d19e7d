import request from '@/utils/request'

// 创建代码生成器测试
export function createTest(data) {
    return request({
        url: '/dwInfra/Infra/test/create',
        method: 'post',
        data: data
    })
}

// 更新代码生成器测试
export function updateTest(data) {
    return request({
        url: '/dwInfra/Infra/test/update',
        method: 'put',
        data: data
    })
}

// 删除代码生成器测试
export function deleteTest(id) {
    return request({
        url: '/dwInfra/Infra/test/delete?id=' + id,
        method: 'delete'
    })
}

// 获得代码生成器测试
export function getTest(id) {
    return request({
        url: '/dwInfra/Infra/test/get?id=' + id,
        method: 'get'
    })
}

// 获得代码生成器测试分页
export function getTestPage(query) {
    return request({
        url: '/dwInfra/Infra/test/page',
        method: 'get',
        params: query
    })
}

// 导出代码生成器测试 Excel
export function exportTestExcel(query) {
    return request({
        url: '/dwInfra/Infra/test/export-excel',
        method: 'get',
        params: query,
        responseType: 'blob'
    })
}
