import request from '@/utils/request'

/**
 * 下载示例
 * @returns
 */
export function downloadImportExample(params){
    return request({
        url: '/dwInfra/codegen/ghsj/importTemplate',
        method:'post',
        data: params,
        responseType: 'blob'
    })
}

/**
 * 下载项目管理模板
 * @param params
 * @returns {*}
 */
export function downloadProjectImportExample(params){
    return request({
        url: '/dwInfra/codegen/project/importTemplate',
        method:'post',
        data: params,
        responseType: 'blob'
    })
}
