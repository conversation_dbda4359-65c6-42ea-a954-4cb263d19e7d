<template>
    <div>
        <logo :collapse="false" />
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-menu
                :default-active="$route.path"
                router
                :unique-opened="true"
                class="el-menu-vertical-demo"
                :active-text-color="theme">
                <component
                    v-for="route in menuList"
                    :key="route.Url"
                    :index="route.Title"
                    :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'">
                    <template slot="title">
                        <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
                        <span slot="title" class="font-size14">{{route.Title}}</span>
                    </template>
                    <template v-if="route.childList && route.childList.length > 0">
                        <el-menu-item
                            v-for="routeChild in route.childList"
                            :key="routeChild.Url"
                            :index="routeChild.Url"
                            :route="{path: routeChild.Url}">
                            <span slot="title">{{routeChild.Title}}</span>
                        </el-menu-item>
                    </template>
                </component>
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin,getModule} from '@/api/yygl'
export default {
    components: { Logo},
    data(){
        return {
            menuList:[]
        }
    },
    computed: {
        theme(){
            return this.$store.state.settings.theme
        }
    },
    created(){
        MLogin().then(res=>{
            if(res.success){
                this.getMenuList()
            }else{
                this.$router.push('/login')
            }
        })
    },
    methods:{
        async getMenuList(){
            await getModule().then(res=>{
                this.menuList[0] = (res.data || []).find(item => item.Id === 'JYYKJYYGL01')
                this.$router.push(this.menuList[0]?.childList[0]?.Url)
                this.$forceUpdate()
            })
        }
    }
};
</script>
