import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: "*",
    name: "NotFound",
    component: () => import("@/views/error/404"),
  },
  //协同办公路由
  {
    path: '/',
    component: Layout,
    // redirect: 'index',
    children:[
      {
        path: 'yygladd',
        name: 'yygladd',
        component: () => import('@/views/yyglAdd/index'),
      },
      {
        path: 'yyglprocess',
        name: 'yyglprocess',
        component: () => import('@/views/yyglProcess/index'),
      },
      {
        path: 'yyglsearch',
        name: 'yyglsearch',
        component: () => import('@/views/yyglSearch/index'),
      },
      {
        path: 'yytjsearch',
        name: 'yytjsearch',
        component: () => import('@/views/yytjSearch/index'),
      },
      {
        path: 'yysqxx',
        name: 'yysqxx',
        component: () => import('@/views/yysqxx/index'),
      },
  	  {
  	    path: 'electronicSeal',
  	    name: 'electronicSeal',
  	    component: () => import('@/views/electronicSeal/index'),
  	  }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
  routes:constantRoutes
})

export default router
