<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div>
        <el-button type="text" icon="el-icon-view" @click="handleOpenDialog">查看</el-button>
      </div>
      <div class="search-box">
        <el-input placeholder="用印编号、需求描述、用印呈送" v-model="searchInput" clearable/>
        <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="yyglSearchTable"
          :tableData="tableData"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParam"
          @getCurrentData="getCurrentData">
        <template slot="yycount" slot-scope="scope">
          {{ scope.row.yycount == 0 ? '' : scope.row.yycount }}
        </template>
      </Table>
      <Pagination
          :total="total"
          :queryParam="queryParam"
          @handleRefresh="handleRefresh"
      />
    </div>

    <el-dialog
        title="印章授权信息"
        :visible.sync="dialogVisible"
        fullscreen>
      <el-tabs type="border-card">
        <el-tab-pane label="基本信息">
          <el-row :gutter="20">
            <el-col :span="3"><span>需求描述：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.type" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>项目编号：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.bh" readonly></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="3"><span>借用类型：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.qblx" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>用印呈送：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.yysend" readonly></el-input>
            </el-col>
          </el-row>
          <el-row v-if="currentRowData.type === '用印申请'" :gutter="20">
            <el-col :span="3"><span>印章类型：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.yzfl" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>用印份数：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.yycount" readonly></el-input>
            </el-col>
          </el-row>
          <el-row v-else-if="(currentRowData.type || '').includes('外借')" :gutter="20">
            <el-col :span="3"><span>外借期限：</span></el-col>
            <el-col :span="7">
              <el-input v-model="currentRowData.jystartdate" readonly></el-input>
            </el-col>
            <el-col :span="3">
              <el-input v-model="currentRowData.sdatequantum" readonly></el-input>
            </el-col>
            <el-col :span="1">
              <div style="text-align:center">至</div>
            </el-col>
            <el-col :span="7">
              <el-input v-model="currentRowData.jyedndate" readonly></el-input>
            </el-col>
            <el-col :span="3">
              <el-input v-model="currentRowData.edatequantum" readonly></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="3"><span>申请时间：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.time" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>申请人：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.applicant" readonly></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="3"><span>申请人单位（部门）：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.groupname" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>联系电话：</span></el-col>
            <el-col :span="9">
              <el-input v-model="currentRowData.phone" readonly></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="3"><span>申请单位（部门）负责人：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.sqfzrTrue" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>申请单位（部门）负责人意见：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.fzryjTrue" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>申请单位（部门）负责人意见确认时间：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.fzrqrsjTrue" readonly></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-show="currentRowData.yysend === '永耀集团'">
            <el-col :span="3">
              <span>综合管理部负责人：</span>
            </el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.zhglbfzrTrue" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>综合管理部意见：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.zhglbyjTrue" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>综合管理部意见确认时间：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.zhglbqrsjTrue" readonly></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-show="currentRowData.yysend !== '本公司' && currentRowData.yysend !== '永耀集团'">
            <el-col :span="3"><span>总公司负责人：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.zgsleaderTrue" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>总公司负责人意见：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.zgsleaderyjTrue" readonly></el-input>
            </el-col>
            <el-col :span="3"><span>总公司负责人意见确认时间：</span></el-col>
            <el-col :span="5">
              <el-input v-model="currentRowData.zgsleadersjTrue" readonly></el-input>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="3"><span>申请理由：</span></el-col>
            <el-col :span="21">
              <el-input type="textarea" v-model="currentRowData.yysy" readonly></el-input>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="流程信息">
          <Table
              :tableData="processTableData"
              :tableOptions="processTableOptions"
              :loading="processLoading">
            <template slot="transdate" slot-scope="scope">
              {{ formatDate(scope.row.transdate) }}
            </template>
          </Table>
        </el-tab-pane>
        <el-tab-pane label="印章信息">
          <el-statistic
              ref="statistic"
              format="HH:mm:ss"
              :value="endTime"
              title="智能印控使用倒计时："
              time-indices
          >
          </el-statistic>
          <Table
              :tableData="yzxxTableData"
              :tableOptions="yzxxTableOptions"
              :loading="yzxxLoading"
              @getCurrentData="getyzxxData">
            <template slot="lastUseTime" slot-scope="scope">
              {{ formatDate(scope.row.lastUseTime) }}
            </template>
            <template slot="operate" slot-scope="scope">
              <el-button v-show="scope.row.empowerView == 1" v-if="scope.row.empower_type == 0" type="text"
                         @click="openSealEmpower(scope.row)">印章授权
              </el-button>
              <el-button v-show="scope.row.empowerView == 1" v-else type="text" @click="closeSealEmpower(scope.row)">
                取消授权
              </el-button>
              <el-button type="text" @click="openEmpowerLogList(scope.row)">授权记录</el-button>
              <el-button v-show="scope.row.openLockView == 1" type="text" @click="openDevice(scope.row)">印章解锁
              </el-button>
              <el-button type="text" @click="tzjv(scope.row)">用印记录</el-button>
              <el-button v-show="scope.row.empowerView == 1" v-if="scope.row.lock == 0" type="text"
                         @click="lock(scope.row)">紧急锁定
              </el-button>
              <el-button v-show="scope.row.empowerView == 1" v-else type="text" @click="unlock(scope.row)">设备解锁
              </el-button>
            </template>
          </Table>
        </el-tab-pane>
      </el-tabs>
      <div class="btn-box">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="授权记录"
        :visible.sync="dialogSqJlVisible"
        width="40%">
      <Table
          :tableData="empowerLogData"
          :tableOptions="empowerLogOptions"
          :loading="empowerLogLoading">
        <template slot="create_time" slot-scope="scope">
          {{ formatDate(scope.row.create_time) }}
        </template>
        <template slot="power_type" slot-scope="scope">
          <div v-if="scope.row.power_type == 1">授权</div>
          <div v-else>撤回授权</div>
        </template>
      </Table>
    </el-dialog>
    <el-dialog
        title="用印记录"
        :visible.sync="dialogYyJlVisible"
        width="85%">
      <el-tabs type="border-card" @tab-click="yyjlButton" v-model="yyjlName">
        <el-tab-pane label="全部" name="first">
          <el-row>
            <el-col :span="6" v-for="(o, index) in photoList" :key="index">
              <el-card :body-style="{ padding: '0px' }">
                <img :src="o.iamge" class="image">
                <div style="padding: 14px;display: block">
                  <div style="width: 100%;text-align: center">{{ formatDate(o.useTime) }}</div>
                  <div style="width: 100%;text-align: center">{{ o.location.split("【")[0] }}</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="盖章照片" name="second">
          <el-row>
            <el-col :span="6" v-for="(o, index) in photoList" :key="index">
              <el-card :body-style="{ padding: '0px' }">
                <img :src="o.iamge" class="image">
                <div style="padding: 14px;display: block">
                  <div style="width: 100%;text-align: center">{{ formatDate(o.useTime) }}</div>
                  <div style="width: 100%;text-align: center">{{ o.location.split("【")[0] }}</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="使用人照片" name="third">
          <el-row>
            <el-col :span="6" v-for="(o, index) in photoList" :key="index">
              <el-card :body-style="{ padding: '0px' }">
                <img :src="o.iamge" class="image">
                <div style="padding: 14px;display: block">
                  <div style="width: 100%;text-align: center">{{ formatDate(o.useTime) }}</div>
                  <div style="width: 100%;text-align: center">{{ o.location.split("【")[0] }}</div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
      <Pagination
          :total="yyjlTotal"
          :queryParam="yyjlParam"
          @handleRefresh="handleRefresh"
      />
    </el-dialog>
  </div>
</template>
<script>
import {
  getWorkFlowList,
} from '@/api/yygl'
import Table from '@/components/MainTable/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import Dropdown from '@/components/ColumnDropdown/index.vue'
import dayjs from 'dayjs'
import {
  getyyglQueryList,
  closeSealEmpower,
  getEmpowerLogList,
  getSealYYLCList, getYyjlList,
  getYYLCEXTEND, lockSeal,
  openDevice,
  openSealEmpower, unlockSeal
} from "@/api/yyglznyy";
import Cookies from 'js-cookie'

export default {
  name: 'yysqxx',
  components: {Table, Pagination, Dropdown},
  data() {
    return {
      searchInput: '',
      tableData: [],
      tableOptions: [
        {label: '项目编号', prop: 'bh'},
        {label: '流程阶段', prop: 'lcJdmc'},
        {label: '需求描述', prop: 'type'},
        {label: '申请理由', prop: 'yysy'},
        {label: '用印呈送', prop: 'yysend'},
        {label: '印章类型', prop: 'yzfl'},
        {label: '用印份数', prop: 'yycount', width: 100, slot: true},
        {label: '外借期限', prop: 'wjDate', width: 160},
      ],
      realTableOptions: [],
      loading: false,
      queryParam: {
        current: 1,
        size: 10,
      },
      total: 0,
      currentSelectId: '',
      currentRowData: {},
      dialogVisible: false,
      processTableData: [],
      processTableOptions: [
        {label: '部门', prop: 'groupname', sortable: false},
        {label: '流程名称', prop: 'lcJdmc', sortable: false},
        {label: '处理人账号', prop: 'personzgh', sortable: false},
        {label: '处理人姓名', prop: 'personname', sortable: false},
        {label: '处理日期', prop: 'transdate', sortable: false, slot: true},
        {label: '意见', prop: 'feed', sortable: false},
      ],
      processLoading: false,
      yzxxTableData: [],
      yzxxTableOptions: [
        {label: '印章名称', prop: 'seal_name', sortable: false},
        {label: '次数', prop: 'use_count', sortable: false},
        {label: '总次数', prop: 'yy_count', sortable: false},
        {label: '最后使用时间', prop: 'lastUseTime', sortable: false, slot: true},
        {label: '操作', prop: 'operate', sortable: false, slot: true},
      ],
      yzxxLoading: false,
      createTime: null,
      endTime: null,
      countdownd: 0,
      countdownh: 0,
      countdownm: 0,
      countdowns: 0,
      dialogSqJlVisible: false,
      empowerLogData: [],
      empowerLogOptions: [
        {label: '授权人', prop: 'empower_name', sortable: false},
        {label: '被授权人', prop: 'applicant_name', sortable: false},
        {label: '时间', prop: 'create_time', sortable: false, slot: true},
        {label: '操作', prop: 'power_type', sortable: false, slot: true},
      ],
      empowerLogLoading: false,
      dialogYyJlVisible: false,
      photoList: [],
      yyjlTotal: 0,
      yyjlParam: {
        current: 1,
        size: 10,
      },
      hjID: '',
      yyjlName: 'first',
      yzxxRow: {},
    }
  },
  mounted() {
    this.getTableList()
  },
  methods: {
    formatDate(date) {
      return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : date
    },
    openSealEmpower(row) {
      this.loading = true
      this.$confirm('请确认是否进行印章授权?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        openSealEmpower({id: row.id}).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
          this.handleGetYZXXData();
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {

      });
    },
    closeSealEmpower(row) {
      this.loading = true
      this.$confirm('请确认是否取消授权?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        closeSealEmpower({id: row.id}).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
          this.handleGetYZXXData();
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {

      });
    },
    openEmpowerLogList(row) {
      this.empowerLogLoading = true
      getEmpowerLogList({id: row.id}).then(res => {
        this.empowerLogData = res.result
        this.dialogSqJlVisible = true
      }).finally(() => {
        this.empowerLogLoading = false
      })
    },
    openDevice(row) {
      this.loading = true
      this.$confirm('请确认是否进行印章解锁?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        openDevice({id: row.id}).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
          this.handleGetYZXXData();
        }).catch(err => {
          this.$message({
            type: 'error',
            message: res.message
          })
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {

      });
    },
    tzjv(row) {
      const param = {
        pageNum: this.yyjlParam.current,
        pageSize: this.yyjlParam.size,
        applicationId: row.order_num,
        sealId: row.seal_id,
        hjID: this.hjID,
      }
      getYyjlList(param).then(res => {
        this.photoList = res.result.records
        this.yyjlTotal = res.result.total
        this.dialogYyJlVisible = true
        this.photoList.forEach(value => {
          value.iamge = `/app/static/` + value.filepath + '?token=' + Cookies.get('jtoken')
        })
      }).finally(() => {
      })
    },
    lock(row) {
      this.loading = true
      this.$confirm('请确认对设备进行紧急锁定?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        lockSeal({id: row.id}).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
          this.handleGetYZXXData();
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {

      });
    },
    unlock(row) {
      this.loading = true
      this.$confirm('请确认对设备进行解锁?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        unlockSeal({id: row.id}).then(res => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
          this.handleGetYZXXData();
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {

      });
    },
    yyjlButton() {
      this.yyjlParam = {
        current: 1,
        size: 10,
      }
      if (this.yyjlName == 'first') {
        this.hjID = ''
      } else if (this.yyjlName == 'second') {
        this.hjID = 'user'
      } else if (this.yyjlName == 'third') {
        this.hjID = 'face'
      }
      this.photoList = []
      this.tzjv(this.yzxxRow)
    },
    getyzxxData(val) {
      this.yzxxRow = val
    },
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    async getTableList() {
      this.loading = true
      const params = {...this.queryParam, CID: this.searchInput}
      await getyyglQueryList(params).then(res => {
        this.tableData = res.records || []
        this.total = res.total || 0
        this.$refs.yyglSearchTable.setCurrent(this.tableData[0])
        this.loading = false
      }).catch(err => {
        this.loading = false
      })
    },
    //模糊查询和刷新
    handleRefresh(queryParam) {
      this.queryParam = {...queryParam}
      this.getTableList()
    },
    //获取当前点击的数据id
    getCurrentData(val) {
      //初始化
      this.currentRowData = {}
      this.currentSelectId = val.id
      const {time, jystartdate, jyedndate} = val
      this.currentRowData = val
      this.currentRowData.time = this.formatDate(time)
      this.currentRowData.jystartdate = this.formatDate(jystartdate)
      this.currentRowData.jyedndate = this.formatDate(jyedndate)
    },
    //查看打开窗口
    handleOpenDialog() {
      if (!this.currentSelectId) {
        this.$message({
          type: 'warning',
          message: '请先选择一条数据！'
        })
        return
      }
      this.dialogVisible = true
      this.handleGetWorkFlowData()
      this.handleGetYZXXData()
    },
    //获取流程信息
    async handleGetWorkFlowData() {
      this.processLoading = true
      await getWorkFlowList(this.currentSelectId).then(res => {
        this.processTableData = res || []
        this.processTableData.map(process => {
          const {personname, feed, transdate} = process
          //不是退回的
          if (process.isback !== 1) {
            //基本信息：申请单位负责人910202 综合管理部负责人910203 总公司负责人910206
            if (process.lcJdid == 910202) {
              this.currentRowData.sqfzrTrue = personname
              this.currentRowData.fzryjTrue = feed
              this.currentRowData.fzrqrsjTrue = this.formatDate(transdate)
            } else if (process.lcJdid == 910203) {
              this.currentRowData.zhglbfzrTrue = personname
              this.currentRowData.zhglbyjTrue = feed
              this.currentRowData.zhglbqrsjTrue = this.formatDate(transdate)
            } else if (process.lcJdid == 910206) {
              this.currentRowData.zgsleaderTrue = personname
              this.currentRowData.zgsleaderyjTrue = feed
              this.currentRowData.zgsleadersjTrue = this.formatDate(transdate)
            }
          }
        })
        this.processLoading = false
      }).catch(err => {
        this.processLoading = false
      })
    },
    //获取印章信息
    async handleGetYZXXData() {
      this.yzxxLoading = true
      await getSealYYLCList({id: this.currentSelectId}).then(res => {
        this.yzxxTableData = res.result || []
        this.yzxxLoading = false
      }).catch(err => {
        this.yzxxLoading = false
      })
      await this.getYYLCEXTEND()
    },
    //获取倒计时
    async getYYLCEXTEND() {
      await getYYLCEXTEND({id: this.currentSelectId}).then(res => {
        const yylcExtend = res.result
        if (yylcExtend != null && yylcExtend.length > 0) {
          this.createTime = yylcExtend[0].create_date;
          this.endTime = (new Date(yylcExtend[0].endDate)).getTime();
        }
      }).catch(err => {
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .search-box {
      display: flex;

      .el-input {
        width: 220px;
        margin-right: 5px;
      }
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.image {
  width: 100%;
  display: block;
  object-fit: fill;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}
</style>
