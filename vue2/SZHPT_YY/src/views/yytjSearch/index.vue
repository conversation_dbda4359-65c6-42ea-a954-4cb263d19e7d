<template>
    <div class="search-container">
        <div class="operate-pannel">
            <span class="font-size14">申请时间：</span>
            <el-date-picker
                v-model="timeValue"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleChangeDate">
            </el-date-picker>
            <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
            <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
            <el-button type="text" icon="el-icon-download" @click="exportData">导出</el-button>
        </div>
        <div class="table-box">
            <Table 
                ref="tjSearchTable"
                :tableData="tableData"
                :tableOptions="tableOptions"
                :loading="loading"
            />
        </div>
    </div>
</template>
<script>
import {
    getyyglTJList,
    exportExcel
} from '@/api/yygl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import dayjs from 'dayjs'
import fileDownload from 'js-file-download'
export default {
    name:'index',
    components:{Table, Pagination},
    data(){
        return {
            tableData:[],
            tableOptions:[
                {label: '使用单位', prop:'groupName'},
                {label: '拟稿数量', prop:'CGNumb'},
                {label: '在途数量', prop:'ZTNumb'},
                {label: '归档数量', prop:'GDNumb'},
                {label: '作废数量', prop:'ZFNumb'},
                {label: '合计', prop:'HJNumb'},
            ],
            loading:false,
            timeValue:'',
            params: {
                Svalue: '',
                Evalue: ''
            }
            
        }
    },
    mounted(){
        this.getTableList()
    },
    methods:{
        handleChangeDate(date){
            this.params.Svalue = date ? dayjs(date[0]).format('YYYY-MM-DD') : ''
            this.params.Evalue = date ? dayjs(date[1]).format('YYYY-MM-DD') : ''
        },
        async getTableList(){
            this.loading = true
            await getyyglTJList(this.params).then(res=>{
                this.tableData = res.list || []
                this.loading = false
            }).catch(err=>{
                this.loading = false
            })
        },
        //导出excel
        exportData(){
            exportExcel(this.params).then(res=>{
                fileDownload(res, '用印统计数据.xls')
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.search-container{
    width: 100%;
    height: 100%;

    .operate-pannel{
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .el-date-editor{
            margin-right: 5px;
        }
    }
    .table-box{
        height: calc(100% - 50px);

        ::v-deep .el-pagination{
            margin-top: 10px;
            text-align:center;
        }
    }
}
</style>