<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div>
        <el-button type="text" icon="el-icon-view" @click="openWifiDialog">wifi连接</el-button>
        <el-button type="text" icon="el-icon-view" @click="openFingerDialog">指纹设置</el-button>
        <el-button type="text" icon="el-icon-view" @click="openDeviceLock">开物理锁</el-button>
        <el-button type="text" icon="el-icon-view" @click="showUsageRecord">使用记录</el-button>
      </div>
      <div class="search-box">
        <el-input placeholder="印章名称，uuid" v-model="searchInput" clearable/>
        <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          ref="sealTable"
          :tableData="tableData"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParam"
          @getCurrentData="getCurrentData">
        <!-- <template slot="yycount" slot-scope="scope">
            {{scope.row.yycount == 0 ? '' : scope.row.yycount}}
        </template> -->
      </Table>
      <Pagination
          :total="total"
          :queryParam="queryParam"
      />
    </div>
    <el-dialog
        title="连接wifi"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="40%"
        center>
      <el-row>
        <el-input
            placeholder="wifi名称"
            v-model='wifiObj.ssid'
            size="medium">
        </el-input>
      </el-row>
      <el-row>
        <el-input
            placeholder="密码"
            v-model="wifiObj.password"
            size="medium">
        </el-input>
      </el-row>
      <div class="btn-box">
        <el-button type="primary" @click="setLinkWifi()">连接</el-button>
        <!-- <el-button type="primary" @click="dialogVisible = false">断开当前wifi</el-button> -->
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="指纹列表"
        :visible.sync="fingerDialogVisible"
        :close-on-click-modal="false"
        width="50%"
        center>
      <div class="operate-pannel">
        <div>
          <el-button type="text" icon="el-icon-view" @click="addFinger">添加指纹</el-button>
          <el-button type="text" icon="el-icon-view" @click="delFinger">删除指纹</el-button>
        </div>
        <div class="search-box">
          <el-input placeholder="姓名" v-model="fingerSearchInput" clearable/>
          <el-button type="text" icon="el-icon-search" @click="getFingerTableList()">刷新</el-button>
        </div>
      </div>
      <div>
        <Table
            ref="fingerTable"
            :tableData="fingerTableData"
            :tableOptions="fingerTableOptions"
            :loading="fingerLoading"
            :queryParam="fingerQueryParam"
			@getCurrentData="getFingerCurrentData"
            style="height: 50vh">
          <!-- <template slot="yycount" slot-scope="scope">
              {{scope.row.yycount == 0 ? '' : scope.row.yycount}}
          </template> -->
        </Table>
        <Pagination
            :total="fingerTotal"
            :queryParam="fingerQueryParam"
        />
      </div>
      <div class="btn-box">
        <el-button @click="fingerDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        title="使用记录列表"
        :visible.sync="UsageRecordDialogVisible"
        :close-on-click-modal="false"
        width="70%"
        center>
      <div>
        <div class="operate-pannel">
          <div class="search-box">
            <el-input placeholder="用印人，申请单名称" v-model="UsageRecordQueryParam.searchUsage" clearable/>
            <el-select v-model="UsageRecordQueryParam.model" placeholder="请选择用印模式">
              <el-option
                  v-for="item in modelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
            <el-select v-model="UsageRecordQueryParam.error" placeholder="请选择状态">
              <el-option
                  v-for="item in errorOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
            <el-button type="text" icon="el-icon-search" @click="getUsageRecord">查询</el-button>
            <el-button type="text" icon="el-icon-refresh" @click="getUsageFlash">刷新</el-button>
          </div>
        </div>
        <Table
            ref="UsageRecordTable"
            :tableData="UsageRecordTableData"
            :tableOptions="UsageRecordTableOptions"
            :loading="UsageRecordLoading"
            :queryParam="UsageRecordQueryParam"
            style="height: 50vh"
        >
          <template slot="useTime" slot-scope="scope">
            {{ formatDate(scope.row.useTime) }}
          </template>
          <template slot="model" slot-scope="scope">
            {{ modelValue(scope.row.model) }}
          </template>
          <template slot="error" slot-scope="scope">
            <el-tag type="success" v-if="scope.row.error == 0">{{ errorValue(scope.row.error) }}</el-tag>
            <el-tag type="danger" v-else>{{ errorValue(scope.row.error) }}</el-tag>
          </template>
        </Table>
        <Pagination
            :total="UsageRecordTotal"
            :queryParam="UsageRecordQueryParam"
        />
      </div>
      <div class="btn-box">
        <el-button @click="UsageRecordDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {
  getFingerPage,
  getSealPage,
  addFingerprint,
  delFingerprint,
  setWifi,
  openLock, getUsageRecord
} from "@/api/yyglznyy";
import dayjs from "dayjs";

export default {
  name: 'index',
  components: {Table, Pagination, Dropdown},
  data() {
    return {
      searchInput: '',
      tableData: [],
      tableOptions: [
        {label: '印章名称', prop: 'seal_name'},
        {label: '设备uuid', prop: 'device_uuid'},
        {label: '所属单位', prop: 'group_name'}
      ],
      realTableOptions: [],
      loading: false,
      queryParam: {
        current: 1,
        size: 10,
      },
      total: 0,
      fingerSearchInput: '',
      fingerTableData: [],
      fingerTableOptions: [
        {label: '姓名', prop: 'userName'},
        {label: '录入日期', prop: 'time'}
      ],
      fingerLoading: false,
      fingerQueryParam: {
        current: 1,
        size: 10,
      },
      fingerTotal: 0,
      currentSelectId: '',
      currentRowData: {},
	  currentFingerRowData: {},
      dialogVisible: false,
      fingerDialogVisible: false,
      createTime: null,
      endTime: null,
      wifiObj: {
        ssid: '',
        password: ''
      },
      UsageRecordDialogVisible: false,
      UsageRecordTableData: [],
      UsageRecordTableOptions: [
        {label: '用印时间', prop: 'useTime', slot: true},
        {label: '用印人', prop: 'userName'},
        {label: '申请单名称', prop: 'applicationId', tooltip: true},
        {label: '用印模式', prop: 'model', slot: true},
        {label: '状态', prop: 'error', slot: true},
        {label: '次数', prop: 'useCount'},
        {label: '地址', prop: 'location', tooltip: true}
      ],
      UsageRecordLoading: false,
      UsageRecordQueryParam: {
        current: 1,
        size: 10,
        searchUsage: null,
        model: null,
        error: null
      },
      UsageRecordTotal: 0,
      modelOptions: [
        {
          value: '-1',
          label: '未知模式'
        },
        {
          value: '1',
          label: '审批模式'
        },
        {
          value: '2',
          label: '指纹模式'
        },
        {
          value: '5',
          label: '密码模式'
        },
        {
          value: '10',
          label: '临时模式'
        },
        {
          value: '11',
          label: '远程用印'
        }
      ],
      errorOptions: [
        {
          value: -1,
          label: '异常'
        },
        {
          value: 0,
          label: '正常'
        },
      ],
    }
  },
  mounted() {
    this.getTableList()
  },
  methods: {
    formatDate(date) {
      return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : date
    },
    modelValue(value) {
      switch (value) {
        case '-1':
          return '未知模式'
        case '1':
          return '审批模式'
        case '2':
          return '指纹模式'
        case '3':
          return '锁定模式'
        case '4':
          return '装章模式'
        case '5':
          return '密码模式'
        case '6':
          return 'OTA模式'
        case '7':
          return '休眠模式'
        case '8':
          return '产测模式'
        case '10':
          return '临时模式'
        case '11':
          return '远程用印'
      }
    },
    errorValue(value) {
      switch (value) {
        case -1:
          return '异常'
        case 0:
          return '正常'
      }
    },
    getTableList() {
      this.loading = true
      const params = {...this.queryParam, CID: this.searchInput}
      getSealPage(params).then(res => {
        this.tableData = res.result.records || []
        this.total = res.result.total || 0
        this.$refs.sealTable.setCurrent(this.tableData[0])
        this.loading = false
      }).catch(err => {
        this.loading = false
      })
    },
    //获取当前点击的数据id
    getCurrentData(val) {
      //初始化
      this.currentRowData = {}
      this.currentSelectId = val.id
      this.currentRowData = val
    },
	getFingerCurrentData(val){
	  this.currentFingerRowData = val
	},
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    //查看打开窗口
    openWifiDialog() {
      if (!this.currentSelectId) {
        this.$message({
          type: 'warning',
          message: '请先选择一条数据！'
        })
        return
      }
      this.dialogVisible = true
    },
    setLinkWifi() {
      this.fingerLoading = true
      const params = {...this.wifiObj, uuid: this.currentRowData.device_uuid}
      setWifi(params).then(res => {
        this.$message({
          type: 'success',
          message: res.message
        })
        this.fingerLoading = false
      }).catch(err => {
        this.$message({
          type: 'error',
          message: res.message
        })
        this.fingerLoading = false
      })
    },
    openFingerDialog() {
      if (!this.currentSelectId) {
        this.$message({
          type: 'warning',
          message: '请先选择一条数据！'
        })
        return
      }
      this.getFingerTableList()
      this.fingerDialogVisible = true
    },
    getFingerTableList() {
      this.fingerLoading = true
      const params = {...this.fingerQueryParam, userName: this.fingerSearchInput, uuid: this.currentRowData.device_uuid}
      getFingerPage(params).then(res => {
        this.fingerTableData = res.result || []
        this.fingerTotal = this.fingerTableData.length || 0
        this.$refs.fingerTable.setCurrent(this.fingerTableData[0])
        this.fingerLoading = false
      }).catch(err => {
        this.fingerLoading = false
      })
    },
    addFinger() {

      this.$prompt('请输入姓名', '是否开始添加指纹？', {
        distinguishCancelAndClose: true,
        confirmButtonText: '开始添加',
        cancelButtonText: '取消'
      })
          .then(({value}) => {
            const params = {uuid: this.currentRowData.device_uuid, userId: value}
            addFingerprint(params).then(res => {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.fingerLoading = false
            }).catch(err => {
              this.$message({
                type: 'error',
                message: res.message
              })
              this.fingerLoading = false
            })
          })
          .catch(action => {

          });
    },
    delFinger() {
      this.$confirm('是否删除该指纹？', '请确认', {
        distinguishCancelAndClose: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      })
          .then(() => {
            const params = {uuid: this.currentRowData.device_uuid,znyyUserId: this.currentFingerRowData.userName}
            delFingerprint(params).then(res => {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.fingerLoading = false

            }).catch(err => {
              this.$message({
                type: 'error',
                message: res.message
              })
              this.fingerLoading = false
            })
          })
          .catch(action => {

          });
    },
    openDeviceLock() {
      this.$confirm('是否开启章锁？', '请确认', {
        distinguishCancelAndClose: true,
        confirmButtonText: '开启',
        cancelButtonText: '取消'
      })
          .then(() => {
            const params = {uuid: this.currentRowData.device_uuid}
            openLock(params).then(res => {
              this.$message({
                type: 'success',
                message: res.message
              })
              this.fingerLoading = false
            }).catch(err => {
              this.$message({
                type: 'error',
                message: res.message
              })
              this.fingerLoading = false
            })
          })
          .catch(action => {

          });
    },
    showUsageRecord() {
      this.UsageRecordDialogVisible = true
      this.getUsageRecord()
    },
    getUsageFlash() {
      this.UsageRecordQueryParam = {
        current: 1,
        size: 10,
        searchUsage: null,
        model: null,
        error: null
      }
      this.getUsageRecord()
    },
    getUsageRecord() {
      const param = {
        pageNum: this.UsageRecordQueryParam.current,
        pageSize: this.UsageRecordQueryParam.size,
        uuid: this.currentRowData.device_uuid,
        searchUsage: this.UsageRecordQueryParam.searchUsage,
        model: this.UsageRecordQueryParam.model,
        error: this.UsageRecordQueryParam.error,
      }
      this.UsageRecordLoading = true
      getUsageRecord(param).then(res => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: res.message
          })
          this.UsageRecordTableData = res.result.records
          this.UsageRecordTotal = res.result.total
        } else {
          this.$message({
            type: 'error',
            message: res.message
          })
          this.UsageRecordTableData = []
          this.UsageRecordTotal = 0
        }

        this.UsageRecordLoading = false

      }).catch(err => {
        this.$message({
          type: 'error',
          message: err.message
        })
        this.UsageRecordLoading = false
      })
    }
  },
  watch: {
    'queryParam.current'() {
      this.getTableList()
    },
    'queryParam.size'() {
      this.getTableList()
    },
    'fingerQueryParam.current'() {
      this.getFingerTableList()
    },
    'fingerQueryParam.size'() {
      this.getFingerTableList()
    },
    'UsageRecordQueryParam.current'() {
      this.getUsageRecord()
    },
    'UsageRecordQueryParam.size'() {
      this.getUsageRecord()
    }
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .search-box {
      display: flex;

      .el-input {
        width: 220px;
        margin-right: 5px;
      }

      .el-select {
        width: 220px;
        margin-right: 5px;
      }
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

.image {
  width: 100%;
  display: block;
  object-fit: fill;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both
}
</style>
