import resize from './resize/index'
import setWatermark from './watermark/index'
import hasRole from './permission/hasRole'
import hasPermi from './permission/hasPermi'

const install = function(Vue){
    Vue.directive('resize', resize)
    Vue.directive('watermark',setWatermark)
    Vue.directive('hasRole', hasRole)
    Vue.directive('hasPermi', hasPermi)
}

if (window.Vue) {
    window['hasRole'] = hasRole
    window['hasPermi'] = hasPermi
    Vue.use(install); // eslint-disable-line
}

export default install