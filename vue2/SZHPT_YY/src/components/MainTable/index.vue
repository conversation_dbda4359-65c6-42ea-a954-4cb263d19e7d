<template>
    <el-table
        ref="tableRef"
        :data="tableData"
        stripe
        border
        highlight-current-row
        @current-change="handleCurrentChange"
        style="width: 100%"
        height="100%"
        v-loading="loading"
        v-watermark="{label:watermark}">
        <el-table-column v-if="needIndex" type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
                {{(queryParam.current - 1) * queryParam.size + 1 + scope.$index}}
            </template>
        </el-table-column>
        <template v-for="(item, index) in tableOptions">
            <el-table-column
                :key="index"
                :prop="item.prop"
                :label="item.label"
                :align="item.align || 'center'"
                :width="item.width || ''"
                :show-overflow-tooltip="item.tooltip || false"
                :sortable="item.sortable === false ? false : true">
                <template slot-scope="scope">
                    <slot 
                        v-if="item.slot"
                        :name="scope.column.property"
                        :row="scope.row"
                        :rowIndex="scope.$index">
                    </slot>
                    <span v-else>{{ scope.row[scope.column.property] }}</span>
                </template>
            </el-table-column>
        </template>
    </el-table>
</template>
<script>
export default {
    name:'index',
    props:{
        needIndex:{//序号显示
            type: Boolean,
            default: true
        },
        tableData:{
            type: Array,
            default:()=>[]
        },
        tableOptions:{
            type: Array,
            default:()=>[]
        },
        loading:{
            type:Boolean,
            default:false
        },
        queryParam:{
            type: Object,
            default:()=>{
                return {
                    current: 1,
                    size: 10
                }
            }
        },
    },
    computed:{
        watermark(){
            return this.$store.state.user.watermark
        }
    },
    methods:{
        //高亮
        setCurrent(row){
            this.$refs.tableRef.setCurrentRow(row)
        },
        //选中数据
        handleCurrentChange(val){
            this.$emit('getCurrentData', val)
        },
        
        
    }
}
</script>
<style lang="scss" scoped>

</style>