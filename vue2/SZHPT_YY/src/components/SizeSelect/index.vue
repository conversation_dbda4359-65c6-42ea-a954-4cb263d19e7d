<template>
  <el-dropdown trigger="click" @command="handleSetSize">
    <div>
      <i class="iconfont font-size24">&#xe7b1;</i>
    </div>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="item of sizeOptions" :key="item.value" :disabled="size===item.value" :command="item.value">
        {{ item.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  data() {
    return {
      sizeOptions: [
        { label: '小号', value: 'mini' },
        { label: '中号', value: 'small' },
        { label: '大号', value: 'medium' },
        
      ]
    }
  },
  computed: {
    size() {
      return this.$store.state.settings.size
    }
  },
  methods: {
    handleSetSize(size) {
      this.$ELEMENT.size = size
      window.document.documentElement.setAttribute("data-size",size)
      this.$store.dispatch('settings/setSize', size)
      // this.$router.go(0)
      this.$message({
        message: '更改字体大小成功！',
        type: 'success'
      })
    },
    refreshView() {
      // In order to make the cached page re-rendered
      //this.$store.dispatch('tagsView/delAllCachedViews', this.$route)

      const { fullPath } = this.$route

      // this.$nextTick(() => {
      //   this.$router.replace({
      //     path: '/redirect' + fullPath
      //   })
      // })
    }
  }

}
</script>
