<template>
  <el-popover placement="bottom" trigger="click">
    <template>
      <div class="theme-box">
            <div class="theme-item" v-for="(item,index) in colorThemes" :key="index" >
                <div 
                    class="colorBlock"
                    :style="{background: `${item.color}`}"
                    @click="themeChange(item.color)">
                    <div
                        v-show="item.color == $store.state.settings.theme"
                        class="theme-checked font-size20"
                    ><i class="iconfont font-size24">&#xe8c7;</i></div>
                </div>
                <div class="theme-title font-size12">{{item.title}}</div>
            </div>
        </div>
    </template>
    <i class="iconfont font-size20" slot="reference">&#xe8b7;</i>
  </el-popover>
</template>

<script>
import {chalkCss} from './chalk'
// const version = require('element-ui/package.json').version // element-ui version from node_modules
const ORIGINAL_THEME = '#4877fb' // default color
export default {
    data(){
        return {
            colorThemes: [
                {title:'默认', color: '#4877fb'},
                {title:'舒眼绿', color: '#11b280'},
                {title:'灵动灰', color: '#7a7474'},
                {title:'悦动橙', color: '#ff8b00'},
                {title:'典雅红', color: '#e64856'},
            ],
            theme: '',
            chalk: ''
        }
    },
    computed:{
        defaultTheme() {
            return this.$store.state.settings.theme
        }
    },
    watch: {
        defaultTheme: {
            handler: function(val, oldVal) {
                this.theme = val
            },
            immediate: true
        },
        async theme(val) {
            await this.setTheme(val)
        }
    },
    created() {
        if(this.defaultTheme !== ORIGINAL_THEME) {
            this.setTheme(this.defaultTheme)
        }
    },
    methods:{
        themeChange(color){
            this.$emit('themeChange',color)
            this.theme = color
        },

        async setTheme(val) {
            const oldVal = this.chalk ? this.theme : ORIGINAL_THEME
            if (typeof val !== 'string') return
            const themeCluster = this.getThemeCluster(val.replace('#', ''))
            const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))
            
            const getHandler = (variable, id) => {
                return () => {
                    const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))
                    const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)

                    let styleTag = document.getElementById(id)
                    if (!styleTag) {
                        styleTag = document.createElement('style')
                        styleTag.setAttribute('id', id)
                        document.head.appendChild(styleTag)
                    }
                    styleTag.innerText = newStyle
                }
            }

            if (!this.chalk) {
                // console.log(version)
                // const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`
                // await this.getCSSString(url, 'chalk')
                this.chalk = chalkCss.replace(/@font-face{[^}]+}/, '')
            }

            const chalkHandler = getHandler('chalk', 'chalk-style')

            chalkHandler()

            const styles = [].slice.call(document.querySelectorAll('style'))
                .filter(style => {
                const text = style.innerText
                return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)
                })
            styles.forEach(style => {
                const { innerText } = style
                if (typeof innerText !== 'string') return
                style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)
            })
            this.$emit('change', val)
        },

        updateStyle(style, oldCluster, newCluster) {
            let newStyle = style
            oldCluster.forEach((color, index) => {
                newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])
            })
            return newStyle
        },
        getCSSString(url, variable) {
            return new Promise(resolve => {
                const xhr = new XMLHttpRequest()
                xhr.onreadystatechange = () => {
                    if (xhr.readyState === 4 && xhr.status === 200) {
                        this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')
                        resolve()
                    }
                }
                xhr.open('GET', url)
                xhr.send()
            })
        },
        getThemeCluster(theme) {
            const tintColor = (color, tint) => {
                let red = parseInt(color.slice(0, 2), 16)
                let green = parseInt(color.slice(2, 4), 16)
                let blue = parseInt(color.slice(4, 6), 16)

                if (tint === 0) { // when primary color is in its rgb space
                    return [red, green, blue].join(',')
                } else {
                    red += Math.round(tint * (255 - red))
                    green += Math.round(tint * (255 - green))
                    blue += Math.round(tint * (255 - blue))

                    red = red.toString(16)
                    green = green.toString(16)
                    blue = blue.toString(16)

                    return `#${red}${green}${blue}`
                }
            }

            const shadeColor = (color, shade) => {
                let red = parseInt(color.slice(0, 2), 16)
                let green = parseInt(color.slice(2, 4), 16)
                let blue = parseInt(color.slice(4, 6), 16)

                red = Math.round((1 - shade) * red)
                green = Math.round((1 - shade) * green)
                blue = Math.round((1 - shade) * blue)

                red = red.toString(16)
                green = green.toString(16)
                blue = blue.toString(16)

                return `#${red}${green}${blue}`
            }

            const clusters = [theme]
            for (let i = 0; i <= 9; i++) {
                clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))
            }
            clusters.push(shadeColor(theme, 0.1))
            return clusters
        }
    }
}
</script>

<style lang="scss" scoped>

.theme-box{
    display: flex;

    .theme-item{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-content: center;

        .colorBlock{
            cursor: pointer;
            margin:0 20px;
            width: 50px;
            height: 50px;
            position: relative;
            border-radius: 6px;

            .theme-checked{
                position: absolute;
                color: #fff;
                left: 50%;
                top: 50%;
                transform: translate(-50%,-50%);
            }
        }

        .theme-title{
            text-align: center;
        }
        
    }
    
}

.theme-icon{
    color:#fff;
    cursor: pointer;
}
</style>