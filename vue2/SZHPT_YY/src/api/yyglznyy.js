import request from '@/utils/request'

/**
 * 用印管理查询
 * @param {*} params
 * @returns
 */
export function getyyglQueryList(params){
    return request({
        url:'/yongyin/znyy/yylc/device/querylist',
        method: 'get',
        params
    })
}

/**
 * 获取用印列表
 * @param {*} params
 * @returns
 */
export function getSealYYLCList(params){
    return request({
        url:'/yongyin/znyy/yylc/device/getSealYYLCList',
        method: 'get',
        params
    })
}

/**
 * 初始化
 * @param {*} params
 * @returns
 */
export function getYYLCEXTEND(params){
    return request({
        url:'/yongyin/znyy/yylc/device/getYYLCEXTEND',
        method: 'get',
        params
    })
}

/**
 * 印章授权
 * @param params
 * @returns {*}
 */
export function openSealEmpower(params){
    return request({
        url:'/yongyin/znyy/yylc/device/openSealEmpower',
        method: 'get',
        params
    })
}

/**
 * 印章取消授权
 * @param params
 * @returns {*}
 */
export function closeSealEmpower(params){
    return request({
        url:'/yongyin/znyy/yylc/device/closeSealEmpower',
        method: 'get',
        params
    })
}

/**
 * 印章授权记录
 * @param params
 * @returns {*}
 */
export function getEmpowerLogList(params){
    return request({
        url:'/yongyin/znyy/yylc/device/getEmpowerLogList',
        method: 'get',
        params
    })
}

/**
 * 印章解锁
 * @param params
 * @returns {*}
 */
export function openDevice(params){
    return request({
        url:'/yongyin/znyy/yylc/device/openDevice',
        method: 'get',
        params
    })
}

/**
 * 用印记录
 * @param params
 * @returns {*}
 */
export function getYyjlList(params){
    return request({
        url:'/yongyin/znyy/yyjl/record/getList',
        method: 'post',
        data: params
    })
}

/**
 * 紧急锁定
 * @param params
 * @returns {*}
 */
export function lockSeal(params){
    return request({
        url:'/yongyin/znyy/yylc/device/lockSeal',
        method: 'get',
        params
    })
}

/**
 * 解锁
 * @param params
 * @returns {*}
 */
export function unlockSeal(params){
    return request({
        url:'/yongyin/znyy/yylc/device/unlockSeal',
        method: 'get',
        params
    })
}

/**
 * 印章管理
 * @param params
 * @returns {*}
 */
export function getSealPage(params){
    return request({
        url:'/yongyin/znyy/yylc/device/getSealPage',
        method: 'post',
        data: params
    })
}

/**
 * 设置wifi
 * @param params
 * @returns {*}
 */
export function setWifi(params){
    return request({
        url:'/yongyin/znyy/yylc/device/setLinkWifi',
        method: 'post',
        data: params
    })
}

/**
 * 印章管理
 * @param params
 * @returns {*}
 */
export function getFingerPage(params){
    return request({
        url:'/yongyin/znyy/yylc/device/getFingerPage',
        method: 'post',
        data: params
    })
}

/**
 * 添加指纹
 * @param params
 * @returns {*}
 */
export function addFingerprint(params){
    return request({
        url:'/yongyin/znyy/yylc/device/addFingerprint',
        method: 'post',
        data: params
    })
}

/**
 * 删除指纹
 * @param params
 * @returns {*}
 */
export function delFingerprint(params){
    return request({
        url:'/yongyin/znyy/yylc/device/delFingerprint',
        method: 'post',
        data: params
    })
}

/**
 * 删除指纹
 * @param params
 * @returns {*}
 */
export function openLock(params){
    return request({
        url:'/yongyin/znyy/yylc/device/openLock',
        method: 'post',
        data: params
    })
}

/**
 * 设备使用记录
 * @param params
 * @returns {*}
 */
export function getUsageRecord(params){
    return request({
        url:'/yongyin/znyy/yylc/device/getUsageRecord',
        method: 'post',
        data: params
    })
}
