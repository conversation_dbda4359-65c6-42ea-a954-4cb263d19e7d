<template>
  <el-container>
    <sidebar class="sidebar-container"/>
    <div class="main-container">
        <navbar />
        <app-main />
    </div>
  </el-container>
</template>

<script>
import { default as AppMain } from './components/AppMain.vue'
import { default as Navbar } from './components/Navbar.vue'
import { default as Sidebar} from './components/Sidebar/index.vue'
import { mapState } from 'vuex'
export default {
    name: 'layout',
    components:{AppMain,Navbar,Sidebar},
    computed:{
        ...mapState({
            size: state => state.settings.size,
            theme: state => state.settings.theme,
        })
    },
    mounted(){
        this.size ? 
        window.document.documentElement.setAttribute("data-size",this.size) :
        window.document.documentElement.setAttribute("data-size",'mini')
    }
}
</script>

<style>

</style>