<template>
    <div>
        <logo :collapse="false" />
        <el-scrollbar wrap-class="scrollbar-wrapper">
            <el-menu
                :default-active="$route.path"
                router
                :unique-opened="true"
                class="el-menu-vertical-demo"
                :active-text-color="theme">
                <component
                    v-for="route in menuList"
                    :key="route.url"
                    :index="route.text"
                    :is="(route.children && route.children.length > 0) ? 'el-submenu' : 'el-menu-item'">
                    <template slot="title">
                        <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
                        <span slot="title" class="font-size14">{{route.text}}</span>
                    </template>
                    <template v-if="route.children && route.children.length > 0">
                        <el-menu-item
                            v-for="routeChild in route.children"
                            :key="routeChild.url"
                            :index="routeChild.url"
                            :disabled="!routeChild.hasPermi"
                            :route="{path: routeChild.url}">
                            <span slot="title">{{routeChild.text}}</span>
                        </el-menu-item>
                    </template>
                </component>
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script>
import Logo from "./Logo";
import {getModule} from "../../../api/login";

export default {
    components: { Logo},
    data(){
        return {
            menuList:[
                {
                    text: '配置管理',
                    children:[
                        {"id":"JDWKQ01BM01","text":"部门定位点","url":"/kqgl/bmdwd","Parameter":0,"leaf":true,"DisplayName":""
                          ,hasPermi: (this.$store.getters.roles.indexOf('协同办公-考勤管理-部门管理员') > -1 || this.$store.getters.roles.indexOf('协同办公-考勤管理-单位管理员') > -1)
                        },
                       {"id":"JDWKQ01GR01","text":"个人定位点","url":"/kqgl/grdwd","Parameter":0,"leaf":true,"DisplayName":""
                         ,hasPermi:(this.$store.getters.roles.indexOf('协同办公-考勤管理-部门管理员') > -1 || this.$store.getters.roles.indexOf('协同办公-考勤管理-单位管理员') > -1)
                       },
                        {"id":"JDWKQ01WD01","text":"节假日配置","url":"/kqgl/workday","Parameter":0,"leaf":true,"DisplayName":""
                          ,hasPermi:(this.$store.getters.roles.indexOf('协同办公-考勤管理-部门管理员') > -1 || this.$store.getters.roles.indexOf('协同办公-考勤管理-单位管理员') > -1)
                        },
                      {"id":"JDWKQ01WD01","text":"身份校验","url":"/kqgl/sfjy","Parameter":0,"leaf":true,"DisplayName":""
                        ,hasPermi:(this.$store.getters.roles.indexOf('协同办公-考勤管理-身份校验管理员') > -1 )
                      }

                    ]
                },
              {
                text: '统计',
                children:[
                    {"id":"JDWKQ01MY01","text":"每月考勤情况","url":"/kqgl/mykq","Parameter":0,"leaf":true,"DisplayName":""
                   ,hasPermi:(this.$store.getters.roles.indexOf('协同办公-考勤管理-部门管理员') > -1 || this.$store.getters.roles.indexOf('协同办公-考勤管理-单位管理员') > -1)
                   },
                  {"id":"JDWKQ01MR01","text":"每日考勤情况","url":"/kqgl/mrkq","Parameter":0,"leaf":true,"DisplayName":""
                    ,hasPermi:(this.$store.getters.roles.indexOf('协同办公-考勤管理-部门管理员') > -1 || this.$store.getters.roles.indexOf('协同办公-考勤管理-单位管理员') > -1)
                  },
                  {"id":"JDWKQ01DW01","text":"点位匹配","url":"/kqgl/dwpp","Parameter":0,"leaf":true,"DisplayName":""
                    ,hasPermi:(this.$store.getters.roles.indexOf('协同办公-考勤管理-部门管理员') > -1 || this.$store.getters.roles.indexOf('协同办公-考勤管理-单位管理员') > -1)
                  }
                ]
              }
            ]
        }
    },
    computed: {
        theme(){
            return this.$store.state.settings.theme
        }
    },
    created(){
      getModule().then((res) => {
        //TODO 获取后台模块列表 默认跳转到第一个
        if(this.$router.currentRoute.fullPath !== this.menuList[0].children[0].url)
          this.$router.push(this.menuList[0].children[0].url)
      })
    }
};
</script>
