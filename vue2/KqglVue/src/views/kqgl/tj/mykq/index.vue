<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">选择日期：</span>
          <el-date-picker
              v-model="queryParams.month"
              type="month"
              placeholder="选择月份"
              value-format="yyyy-MM-dd"
              @change="handleChange"
              :clearable="false"
          >
          </el-date-picker>
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <el-button type="text" icon="el-icon-search" @click="getListMytj">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>

        <el-button type="text" icon="el-icon-view"  @click="openDialog('show')"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01MY01QX02') > -1">考勤日历</el-button>
        <el-button type="text" icon="el-icon-download"  @click="openDialog('mytjDownLoad')"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01MY01QX03') > -1">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="tableData"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
         >
        <template  slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </Table>
      <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog
        width="80%"
        :visible="kqrlDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="kqrlDialog = false"
        :close-on-click-modal="false"
        fullscreen
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <mykqCalendar :date="value" :user-id="clbgParams.userId" @click="openDialog"  @calenderClickData="calenderClickData"></mykqCalendar>
      <div class="btn-box">
        <el-button @click="kqrlDialog = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="80%"
        :visible="dkxqDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="dkxqDialog = false"
        :close-on-click-modal="false"
        fullscreen
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle1 }}</div>
      <dkxq :xqDetail="xqDetail"></dkxq>
      <div class="btn-box">
        <el-button @click="dkxqDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import {getCalenDarList, getListMytj,mytjDownLoad} from '@/api/kqgl/tj/tj'
import {GetGroupList} from '@/api/kqgl/pzgl/pzgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {formatMonth} from '@/utils/tool'
import dkxq from '../mrkq/dkxq'
import mykqCalendar from "@/views/kqgl/tj/mykq/mykq_Calendar.vue";
import fileDownload from "js-file-download";
const d = new Date()
d.setMonth(d.getMonth() + 1, 0)


export default {
  name: 'index',
  components: {Table, Pagination, Dropdown,dkxq,mykqCalendar},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  data() {
    return {
      value: new Date(),
      tableData: [],
      tableOptions: [
        {label: '姓名', prop: 'realName'},
        {label: '部门', prop: 'groupname'},
        {label: '实际打卡天数', prop: 'dkts'},
        {label: '旷工', prop: 'kgts'},
        {label: '打卡异常天数', prop: 'ycts'},
        {label: '迟到', prop: 'cdts'},
        {label: '早退', prop: 'ztts'},
        {label: '异地打卡', prop: 'yddkts'},
        {label: '定位点数不够', prop: 'dwycts'},
        {label: '手机号不匹配', prop: 'sjhyc'}
      ],
      realTableOptions: [],
      loading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        groupId: -1,
        month: formatMonth(d.getTime())
      },
      // 查询部门列表
      groupItem: [],

      selectID: 0,
      // 选择列表数据
      clbgParams: {},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      dialogTitle1: '',
      kqrlDialog: false,
      dkxqDialog: false,
      dtDialog: false,
      dialogCalendar: [],
      xqDetail:{
        applyUser:0,
        clockTime:""
      }
    }
  },
  mounted() {
    this.getListMytj()
    GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
    })
  },
  methods: {
    handleChange() {
      this.queryParams.pageNum = 1
      this.getListMytj()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams.pageNum = val
      this.getListMytj()
    },
    getListMytj(){
      this.loading = true
      getListMytj(this.queryParams).then((res) => {
        this.tableData = res.result.records || []
        this.queryParams.total = res.result.total
       // this.$refs.yyglSearchTable.setCurrent(this.tableData[0])
      }).finally(() => {
        this.loading = false
      })
    },

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getListMytj()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.clbgParams = row
      this.xqDetail.applyUser =  this.clbgParams.userId;
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            getCalenDarList({time: this.queryParams.month, applyUser: this.clbgParams.userId}).then((res) => {
              this.dialogCalendar = res.result
              this.dialogTitle = this.clbgParams.realName + ' 考勤日历'
              this.dialogType = 'show'
              this.kqrlDialog = true
            })
          }
          break
        case 'mytjDownLoad':
          mytjDownLoad(this.queryParams).then((res) => {
            // 构造一个blob对象来处理数据
            const blob = res
            // 构造一个blob对象来处理数据
            var mon = this.queryParams.month;
            const fileName =mon.substring(0,mon.length-3) +'打卡情况' + '.xlsx'
            fileDownload(blob, fileName)


          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.getListMytj()
      this.kqrlDialog = false
      this.dkxqDialog = false
    },

    calenderClickData(data){
      console.log(data)
      this.xqDetail.clockTime =  data.day;
      this.dialogTitle1 = this.clbgParams.realName+data.day+'考勤信息'
      this.dialogType = 'kqxq'
      this.dkxqDialog = true
    },
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
       margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
