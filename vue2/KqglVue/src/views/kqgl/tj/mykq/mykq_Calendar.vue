<template>
  <el-calendar v-model="value">
    <!-- 这里使用的是 2.5 slot 语法，对于新项目请使用 2.6 slot 语法-->
    <template
      slot="dateCell"
      slot-scope="{date, data}">
      <div slot="reference" @click="openDialog(data,'showXq')">
        <div style="font-size: 15px">
          {{ data.day.split('-').slice(1).join('-') }}
        </div>
        <div style="margin-top: 10px;font-size: 15px">
          {{ list | kqglCalendar(data.day) }}
        </div>
      </div>
    </template>

  </el-calendar>
</template>

<script>

import { getCalenDarList } from '@/api/kqgl/tj/tj'
import { formatMonth, getMonthLast } from '@/utils/tool'

export default {
  name: 'mykq_Calendar',
  props: {
    date: {
      type: Date,
      default: new Date()
    },
    userId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      value: this.date,
      list: [],
      dkxqDialog: false,
    }
  },
  mounted () {
    this.getCalenDarList()

    //点击上一个月
    let prevBtn = document.querySelector(
      '.el-calendar__button-group .el-button-group>button:nth-child(1)'
    )
    prevBtn.addEventListener('click', e => {
      this.getCalenDarList()
    })

    //点击下一个月
    let nextBtn = document.querySelector(
      '.el-calendar__button-group .el-button-group>button:nth-child(3)'
    )
    nextBtn.addEventListener('click', () => {
      this.getCalenDarList()
    })

    //点击今天
    let todayBtn = document.querySelector(
      '.el-calendar__button-group .el-button-group>button:nth-child(2)'
    )
    todayBtn.addEventListener('click', () => {
      this.getCalenDarList()
    })
  },
  methods: {
    /** 打开新增弹窗 */
    openDialog (data, type) {
      switch (type) {
        default :
          this.$emit('calenderClickData', data)
          break
      }
    },
    getCalenDarList () {
      const date = this.value
      getCalenDarList({
        time: formatMonth(date.getTime()),
        applyUser: this.userId
      }).then((res) => {
        this.list = res.result
      })
    }
  }
}
</script>

<style scoped>

</style>
