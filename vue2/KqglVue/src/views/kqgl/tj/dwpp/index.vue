<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">选择日期：</span>
        <el-date-picker
            v-model="queryParams.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期时间"
            @change="handleChange"
            :clearable="false"
        >
        </el-date-picker>
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <el-button type="text" icon="el-icon-search" @click="dwppList">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>

        <el-button type="text" icon="el-icon-view"  @click="openDialog('show')"    v-if="this.$store.state.user.permissions.indexOf('JDWKQ01DW01QX02') > -1" >查看详情</el-button>
        <el-button type="text" icon="el-icon-view"  @click="openDialog('showGJ')"   v-if="this.$store.state.user.permissions.indexOf('JDWKQ01DW01QX04') > -1" >查看轨迹</el-button>
        <el-button type="text" icon="el-icon-download"  @click="openDialog('dwppDownLoad')"   v-if="this.$store.state.user.permissions.indexOf('JDWKQ01DW01QX03') > -1">导出</el-button>
        <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="tableData"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          :cellStyleVal = 2
         >
        <template  slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </Table>
      <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="dkxqDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="dkxqDialog = false"
        :close-on-click-modal="false"
        fullscreen
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <dkxq :xqDetail="clbgParams"></dkxq>
      <div class="btn-box">
        <el-button @click="dkxqDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { dwppList,dwppDownLoad} from '@/api/kqgl/tj/dwpp'
import {GetGroupList} from '@/api/kqgl/pzgl/pzgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {dateFormat, getDate} from "@/utils/tool";
import dkxq from '../mrkq/dkxq'
import fileDownload from "js-file-download";

export default {
  name: 'index',
  components: {Table, Pagination, Dropdown,dkxq},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  data() {
    return {
      // 地图src
      tableData: [],
      tableOptions: [
        {label: '姓名', prop: 'applyUserName'},
        {label: '部门', prop: 'groupName'},
        {label: '日期', prop: 'clockTimeStr'},
        {label: '上班打卡时间', prop: 'clockStartTime'},
        {label: '下班打卡时间', prop: 'clockEndTime'},
        {label: '定位采集开始时间', prop: 'influxStartTime'},
        {label: '点位采集结束时间', prop: 'influxEndTime'}
      ],
      realTableOptions: [],
      loading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        groupId: -1,
        date:getDate()
      },
      // 查询部门列表
      groupItem: [],

      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      clbgParams: {},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      dkxqDialog: false,
      dtDialog: false,


      // 轨迹路径
      locus: {
        path: '/kqgl/locus',
        query: {
          userId: '',
          date:'',
          userName: ''
        }
      },

    }
  },
  mounted() {
    this.dwppList()
    GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
    })
  },
  methods: {
    handleChange() {
      this.queryParams.pageNum = 1
      this.dwppList()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams.pageNum = val
      this.dwppList()
    },
    dwppList(){
      this.loading = true
      dwppList(this.queryParams).then((res) => {
        this.tableData = res.result.records || []
        this.queryParams.total = res.result.total

      }).finally(() => {
        this.loading = false
      })
    },

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.dwppList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.clbgParams = row
      this.clbgParams.clockTime =  this.dateFormatYmd(row.clockTime)
      this.locus.query.userId = row.applyUser
      this.locus.query.date = this.dateFormatYmd(row.clockTime)
      this.locus.query.userName = row.applyUserName
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            console.log(this.clbgParams )
            this.dialogTitle = this.clbgParams.applyUserName+this.dateFormatYmd(this.clbgParams.clockTime)+'考勤信息'
            this.dialogType = 'show'
            this.dkxqDialog = true
          }
          break
        case 'showGJ':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            // this.$router.push(this.locus)
            console.log(this.locus)
            const newpage = this.$router.resolve(this.locus)
            window.open(newpage.href, '_blank')
          }
          break
        case 'dwppDownLoad':
          dwppDownLoad(this.queryParams).then((res) => {
            // 构造一个blob对象来处理数据
            const blob = res
            const fileName =this.queryParams.date+'点位匹配信息' + '.xlsx'
            fileDownload(blob, fileName)
          })
          break
      }
    },
    dateFormatYmd(time) {
      var date=new Date(time);
      var year=date.getFullYear();
      /* 在日期格式中，月份是从0开始的，因此要加0
       * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
       * */
      var month= date.getMonth()+1<10 ? "0"+(date.getMonth()+1) : date.getMonth()+1;
      var day=date.getDate()<10 ? "0"+date.getDate() : date.getDate();
      // var hours=date.getHours()<10 ? "0"+date.getHours() : date.getHours();
      // var minutes=date.getMinutes()<10 ? "0"+date.getMinutes() : date.getMinutes();
      // var seconds=date.getSeconds()<10 ? "0"+date.getSeconds() : date.getSeconds();
      // 拼接
      return year+"-"+month+"-"+day;
    }
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
       margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>
