<template>
  <div>
    <el-tabs type="border-card">
      <el-tab-pane label="考勤详情" style="height: 560px;">
        <el-form
          :inline="true"
          label-width="auto"
        >
          <el-divider content-position="left" v-if="clockInfoShow">考勤信息</el-divider>
          <el-divider content-position="left" v-else>暂无数据</el-divider>
          <el-row v-if="clockInfoShow">
            <el-col :span="8">
              <el-form-item label="上班打卡时间" class="force-width-60">{{ ClockInfo.clockStartTime }} <span
                v-if="ClockInfo.clockStartPlace == 1">(异地打卡)</span></el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="地址" class="force-width-60">{{ ClockInfo.clockStartAddress }}</el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="下班打卡时间" class="force-width-60">{{ ClockInfo.clockEndTime }} <span
                v-if="ClockInfo.clockEndPlace == 1">(异地打卡)</span></el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="地址" class="force-width-60">{{ ClockInfo.clockEndAddress }}</el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="clockInfoShow">
            <el-col :span="24">
              <el-divider content-position="left" v-if="ClockInfo.type.includes('0.9')">总状态：正常 &nbsp
                审批人：{{ ClockInfo.bmrkUserName }}（{{ ClockInfo.bmrkNote }}）
              </el-divider>
              <el-divider content-position="left" v-else-if="ClockInfo.type ==',' ">总状态：正常</el-divider>
              <el-divider content-position="left" v-else>总状态：异常</el-divider>
              <el-form-item label="" class="force-width-60">
                <el-card class="box-card">
                  <div class="text">
                    <i class="el-icon-success" style="color: #1296db" v-if="ClockInfo.type.indexOf('1') >0"></i>
                    <i class="el-icon-error" style="color: red" v-else></i>
                    旷工
                  </div>
                  <div class="text">
                    <i class="el-icon-success" style="color: #1296db" v-if="ClockInfo.type.indexOf('2') >0"></i>
                    <i class="el-icon-error" style="color: red" v-else></i>
                    迟到
                  </div>
                  <div class="text">
                    <i class="el-icon-success" style="color: #1296db" v-if="ClockInfo.type.indexOf('3') >0"></i>
                    <i class="el-icon-error" style="color: red" v-else></i>
                    早退
                  </div>
                  <div class="text">
                    <i class="el-icon-success" style="color: #1296db" v-if="ClockInfo.type.indexOf('4') >0"></i>
                    <i class="el-icon-error" style="color: red" v-else></i>
                    异地打卡
                  </div>
                  <div class="text">
                    <i class="el-icon-success" style="color: #1296db" v-if="ClockInfo.type.indexOf('5') >0"></i>
                    <i class="el-icon-error" style="color: red" v-else></i>
                    定位点数不够
                  </div>
                  <div class="text">
                    <i class="el-icon-success" style="color: #1296db"
                       v-if="ClockInfo.clockStartPhoneStatus == 0 || ClockInfo.clockEndPhoneStatus == 0"></i>
                    <i class="el-icon-error" style="color: red" v-else></i>
                    手机号不匹配
                  </div>
                </el-card>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="出车单" style="height: 560px;overflow: auto;">
        <el-form
          :inline="true"
          label-width="auto"
        >
          <el-divider content-position="left" v-if="!cclcListShow">暂无数据</el-divider>
          <div v-for="(item, index) in cclcList" :key="index">
            <el-divider content-position="left"></el-divider>
            <el-row>
              <el-col :span="8">
                <el-form-item label="出行日期" class="force-width-60">{{ item.planDate }}</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开始时间" class="force-width-60">{{ item.openTime }}</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="结束时间" class="force-width-60">{{ item.endTime }}</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出行时长" class="force-width-60">{{ item.travelHour }}小时</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出行事由" class="force-width-60">{{ item.note }}</el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审批人" class="force-width-60">{{ item.approveName }}</el-form-item>
              </el-col>
            </el-row>
          </div>

        </el-form>
      </el-tab-pane>
      <el-tab-pane label="轨迹列表" style="height: 560px;overflow: auto">
        <div class="block" style="margin-left: 10px">
          <el-divider content-position="left">今天点位数：{{ reportCount }}个</el-divider>
          <el-timeline>
            <el-timeline-item v-for="(item, index) in Location" :key="index" :color="item.isAbnormal==1?'red':'#1296db'"
                              size="large" type="primary">
              <div :style="{color:(item.isAbnormal==1?'red':'#1296db')}">
                {{ item.loc_time }}
              </div>
            </el-timeline-item>

          </el-timeline>
        </div>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import { getListMrTj, getClockInfo, getTjList, getUserHistoryLocation } from '@/api/kqgl/tj/tj'

export default {
  name: 'dkxq',
  mounted () {
    this.getClockInfo()
  },
  props: {
    xqDetail: {
      type: Object,
    }
  },
  watch: {
    xqDetail: {
      handler (val) {
        this.getClockInfo()
      },
      deep: true,
      immediate: true
    }
  },
  data () {
    return {
      ClockInfo: {},
      cclcList: {},
      Location: {},
      reportCount: 0,
      clockInfoShow:true,
      cclcListShow: true
    }
  },
  methods: {
    /** 获取考勤详情**/
    getClockInfo () {
      getClockInfo({
        'applyUser': this.xqDetail.applyUser,
        'time': this.xqDetail.clockTime
      }).then((res) => {
         console.log(res.result)
        if (res.result != undefined && res.result != null ) {
          this.clockInfoShow = true
          this.ClockInfo = res.result
          console.log(1111)
        } else {
          this.clockInfoShow = false
          this.ClockInfo = {}
          console.log(2222)
        }
      }).finally(() => {
        this.loading = false
      })
      getTjList({
        'userId': this.xqDetail.applyUser,
        'date': this.xqDetail.clockTime
      }).then((res) => {

        if (res.result != undefined && res.result != null) {
          this.cclcListShow = true
          this.cclcList = res.result
        } else {
          this.cclcListShow = false
          this.cclcList = {}
        }
        this.loading = false
      })
      getUserHistoryLocation({
        'userId': this.xqDetail.applyUser,
        'date': this.xqDetail.clockTime
      }).then((res) => {
        this.Location = res.result.locationList
        this.reportCount = res.result.reportCount
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style  scoped>
.text {
  font-size: 14px;
}

.box-card {
  width: 480px;
}
</style>
