<template>
  <el-form :model="clbgParams" :rules="rules" ref="grdwdForm" label-width="120px" v-watermark="{label: watermark}">
    <el-form-item label="所属部门" >
      <el-select  v-model="clbgParams.groupName" clearable filterable placeholder="请选择管理部门" @change="deptChange" style="width: 80%">
        <el-option
            v-for="item in dialogGroup"
            :key="item.id"
            :label="item.groupname"
            :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="姓名"  prop="userId">
      <el-select size="mini" v-model="clbgParams.userId" filterable clearable placeholder="请选择用户"
                 @click.native="xmClick()" @change="userChange" style="width: 80%">
        <el-option
            v-for="item in userGroup"
            :key="item.id"
            :label="item.realname"
            :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="日期" prop="clockDate">
      <div class="block">
        <el-date-picker
            v-model="clbgParams.clockDate"
            type="date"
            placeholder="选择日期"  style="width: 80%">
        </el-date-picker>
      </div>
    </el-form-item>
    <el-form-item label="时间段" prop="clockTime">
      <el-input size="mini" v-model="clbgParams.clockTime" placeholder="8:30:00|17:00:00" v-show="false"></el-input>
      <el-time-picker
          is-range
          clearable
          v-model="clockStartTimePicker"
          range-separator="|"
          value-format="HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          @change="changeClockStartTimePicker"
          style="width: 80%"
      >
      </el-time-picker>
    </el-form-item>
    <el-form-item label="操作类型" prop="type"  >
      <el-select v-model="clbgParams.type" placeholder="请选择操作类型"   @change="typeChange"  style="width: 80%">
        <el-option label="新增点位" value="0"></el-option>
        <el-option label="删除点位" value="1"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="办公地点经度" prop="longitude" style="display: none">
      <el-input size="mini" v-model="clbgParams.longitude" disabled></el-input>
    </el-form-item>
    <el-form-item label="办公地点纬度" prop="latitude" style="display: none">
      <el-input size="mini" v-model="clbgParams.latitude" disabled></el-input>
    </el-form-item>

    <el-form-item label="GPS"  v-if="clbgParams.type=='0'">
      <el-input size="mini" v-model="clbgParams.unitGPS" id="clbgd.gps" disabled  style="width: 80%"></el-input>
      <el-button size="mini" @click="dtDialog = true">从地图中选择</el-button>
    </el-form-item>
    <!--        <el-form-item label="日期时间段" prop="date">-->
    <!--          <div class="block">-->
    <!--            <el-date-picker-->
    <!--                v-model="clbgParams.date"-->
    <!--                type="datetimerange"-->
    <!--                range-separator="至"-->
    <!--                start-placeholder="开始日期"-->
    <!--                end-placeholder="结束日期">-->
    <!--            </el-date-picker>-->
    <!--          </div>-->
    <!--        </el-form-item>-->


    <el-dialog
        width="80%"
        :visible.sync="dtDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :append-to-body="true"
        :modal-append-to-body="false"
        @close="closeDTDialog"
        :close-on-click-modal="false"
    >
      <div style="width: 100%;height: 100%">
        <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                style="width: 100%;height: 500px"></iframe>
        <el-button @click="closeDTDialog" id="clbgd.dtDialogClose" v-show="false"></el-button>
      </div>
    </el-dialog>


    <el-form-item>
      <div style="text-align: right">
        <el-button type="primary" @click="handleSubmit('grdwdForm')">保存</el-button>
        <el-button @click="$emit('handleClose')">关闭</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import {editPosition, getDeptUsers, GetGroupList} from "../../../../../api/kqgl/pzgl/pzgl";

export default {
  name: 'FormPannel',
  props: {},
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  data() {
    return {
      // 地图src
      src: process.env.NODE_ENV === 'development' ? '/static/kqgl/html/BaiDuToBGD.html' : 'static/kqgl/html/BaiDuToBGD.html',
      //选择列表数据
      clbgParams: {},
      dialogGroup: [],
      dtDialog: false,
      clockStartTimePicker: '',
      clockEndTimePicker: '',
      userGroup: [], //用户下拉框数据
      rules: {
        clockDate: [
          {
            required: true,
            message: '请选择日期',
            trigger: 'blur'
          }
        ],
        clockTime: [
          {
            required: true,
            message: '请选择时间段',
            trigger: 'blur'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择操作类型',
            trigger: 'blur'
          }
        ],

        unitGPS: [
          {
            required: true,
            message: '请输入办公地点GPS',
            trigger: 'blur'
          },
          {
            pattern: /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,14})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180),(\-|\+)?([0-8]?\d{1}\.\d{0,14}|90\.0{0,6}|[0-8]?\d{1}|90)$/, // 正则校验不用字符串
            message: '请输入正确的纬度和经度，并且用逗号(英文)隔开(经度在前，纬度在后)',
            trigger: 'blur'
          }
        ]
      },

    }
  },
  mounted() {
    this.src = process.env.NODE_ENV === 'development' ? '/static/kqgl/html/BaiDuToBGD.html' : 'static/kqgl/html/BaiDuToBGD.html'

    GetGroupList({isShowAll: 1, parentId: 1}).then((res) => {
      let data = res.result
      data = data.slice(1, data.length)
      this.dialogGroup = data

    })
    // this.clbgParams.date = [new Date(2000, 10, 10, 10, 10), new Date(2000, 10, 11, 10, 10)];

  },
  methods: {
    changeClockStartTimePicker(val) {
      this.clbgParams.clockTime = val[0] + "|" + val[1];
      this.clbgParams.clockStartTime = val[0];
      this.clbgParams.clockEndTime = val[1];
    },
    /** 所属部门选项 */
    deptChange(e) {
      console.log(e)
      this.dialogGroup.forEach(value => {
        if (value.id == e) {
          this.clbgParams.groupId = e
          this.clbgParams.deptName = value.groupname
          getDeptUsers({deptId: value.id}).then((res) => {
            let data = res.result
            this.userGroup = data
          })
        }
      })
    },

    userChange(e) {
      this.userGroup.forEach(value => {
        if (value.id == e) {
          this.clbgParams.userName = value.realname
          this.clbgParams.userId = value.id
        }
      })
    },
    xmClick() {
      if (this.userGroup.length == 0) {
        this.$message.error('请先选择部门！')
      }
    },
    typeChange(value){

    },
    closeDTDialog() {
      this.clbgParams.unitGPS = window.parent.document.getElementById('clbgd.gps').value
      // let startTime = this.clbgParams.unitGPS.split(",")[0]
      // let endTime = this.clbgParams.unitGPS.split(",")[1]
      // this.clbgParams.longitude = this.dateFormatAll(startTime)
      // this.clbgParams.latitude = this.dateFormatAll(endTime)
      this.clbgParams.longitude =this.clbgParams.unitGPS.split(",")[0]
      this.clbgParams.latitude =  this.clbgParams.unitGPS.split(",")[1]
      //this.$nextTick(() => this.$refs.stopParams.clearValidate())

      this.dtDialog = false
    },
    /** 关闭弹窗 */
    closeDialog() {
      //this.getListBm()
      this.clbgDialog = false
    },


    //提交表单数据
    handleSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if(this.clbgParams.type=='0'){
            if(this.clbgParams.unitGPS == null || this.clbgParams.unitGPS == '' || this.clbgParams.unitGPS == undefined){
              this.$message.error('请选择gps')
              return false
            }
            const gps = this.clbgParams.unitGPS.split(",")
            this.clbgParams.longitude = gps[0]
            this.clbgParams.latitude = gps[1]
          }

          this.clbgParams.clockDate = this.dateFormat( this.clbgParams.clockDate);
          // this.clbgParams.clockStartTime = this.clockTime[0]
          // this.clbgParams.clockEndTime =this.clockTime[1]
          this.$emit('handleSubmit', this.clbgParams)

        } else {
          return false
        }
      })
    },
    //初始化
    initData(data) {
      console.log(data)
      if (data.id) {
        this.clbgParams = {...data}

        this.deptChange(this.clbgParams.groupId)

        this.clbgParams.unitGPS = this.clbgParams.longitude + "," + this.clbgParams.latitude
        this.clockStartTimePicker = []
        this.clockStartTimePicker.push(this.clbgParams.clockStartTime.split("|")[0])
        this.clockStartTimePicker.push(this.clbgParams.clockStartTime.split("|")[1])

        this.clockEndTimePicker = []
        this.clockEndTimePicker.push(this.clbgParams.clockEndTime.split("|")[0])
        this.clockEndTimePicker.push(this.clbgParams.clockEndTime.split("|")[1])
      }
    },
    dateFormatAll(time) {
      let date = new Date()
      if (time !== undefined) {
        date = new Date(time)
      }
      const year = date.getFullYear()
      const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
      const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
      var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
      var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
      var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
    },
    dateFormat(time) {
      let date = new Date()
      if (time !== undefined) {
        date = new Date(time)
      }
      const year = date.getFullYear()
      const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
      const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()

      return year + '-' + month + '-' + day ;
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
