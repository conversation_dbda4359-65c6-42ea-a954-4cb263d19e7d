<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">选择日期：</span>
        <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            :clearable="false"
        >
        </el-date-picker>
        <span class="font-size14">部门：</span>
        <el-select v-model="queryParams.groupId" placeholder="请选择部门" @change="handleChange">
          <el-option
              v-for="item in groupItem"
              :key="item.id"
              :label="item.groupname"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <span class="font-size14">姓名：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入姓名" @change="handleChange"></el-input>
        <span class="font-size14">异常情况：</span>
        <el-select v-model="queryParams.type" placeholder="请选择异常情况"  @change="handleChange">
          <el-option label="全部" value="-1"></el-option>
          <el-option label="旷工" value="1"></el-option>
          <el-option label="迟到" value="2"></el-option>
          <el-option label="早退" value="3"></el-option>
          <el-option label="异地打卡" value="4"></el-option>
          <el-option label="点位异常" value="5"></el-option>
          <!--                        <el-option label="缺卡" value="6"></el-option>-->
          <el-option label="手机号不匹配" value="7"></el-option>
        </el-select>

          <!--   分行处理     -->
        <el-row style="margin-top: 1vh;margin-bottom: -0.5vh">
          <div class="search-box">
            <el-button type="text" icon="el-icon-search" @click="getListMrTj">查询</el-button>
            <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>

            <el-button type="text" icon="el-icon-view"  @click="openDialog('show')"       v-if="this.$store.state.user.permissions.indexOf('JDWKQ01MR01QX02') > -1">查看详情</el-button>
            <el-button type="text" icon="el-icon-view"  @click="openDialog('showGJ')"    v-if="this.$store.state.user.permissions.indexOf('JDWKQ01MR01QX03') > -1">查看轨迹</el-button>
            <el-button type="text" icon="el-icon-download"  @click="openDialog('mrkqdownload')"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01MR01QX04') > -1">导出</el-button>
            <el-button type="text" icon="el-icon-view"  @click="openDialog('fbbshow')">查看ping</el-button>
            <el-button type="text" icon="el-icon-view"  @click="openDialog('insertPoint')" v-if="this.$store.state.user.loginName == '11071019' ">点位管理</el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

          </div>
        </el-row>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="tableData"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          :cellStyleVal = 1
         >
        <template  slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
        <template #clockStartPlace="scope">
           <div v-if="scope.row.clockStartPlace == null">
           </div>
          <div v-else>
            {{scope.row.clockStartPlace === 1 ? '是':'否' }}
          </div>
        </template>
        <template #clockEndPlace="scope">
          <div v-if="scope.row.clockEndPlace == null">

          </div>
          <div v-else>
            {{scope.row.clockEndPlace === 1 ? '是':'否'}}
          </div>
        </template>

      </Table>
      <el-pagination
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>
    <el-dialog
        width="80%"
        :visible="dkxqDialog"
        size="tiny"
        center
        :destroy-on-close="false"
        :modal-append-to-body="false"
        @close="dkxqDialog = false"
        :close-on-click-modal="false"
        fullscreen
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <dkxq :xqDetail="clbgParams"></dkxq>
      <div class="btn-box">
        <el-button @click="dkxqDialog = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
        width="60%"
        :visible="fbbDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closefbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ fbbDialogTitle }}</div>
      <div style="padding-bottom: 10px" v-if="pingInfo.isShow">
        <span>本地点位：{{pingInfo.pointSize}}个</span>
        <span style="padding-left: 20px">redis点位：{{pingInfo.residsSize}}个</span>
      </div>
      <el-table
          :border="true"
          :data="fbbList"
          highlight-current-row
          height="51vh"
      >
        <el-table-column label="序号" width="70" align="center">
          <template v-slot="scope">{{
              scope.$index + 1
            }}
          </template>
        </el-table-column>
        <el-table-column
            align="center"
            label="uuid"
            prop="uuid"
            min-width="120"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="登录账号"
            prop="loginName"
            min-width="80"
        >
        </el-table-column>
        <el-table-column
            align="center"
            label="日期"
            prop="createTime"
            min-width="80"
        >
        </el-table-column>
      </el-table>
      <div class="btn-box">
        <el-button @click="closefbbDialog">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
        width="25%"
        :visible="insertPointDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closefbbDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ fbbDialogTitle }}</div>
      <FormPannel
          ref="grdwdForm"
          @handleSubmit="handleSubmit"
          @handleClose="dialogFormVisible = false"/>
    </el-dialog>


  </div>
</template>
<script>
import {getListMrTj, mrTjDownLoad, getListPing,addUserPoint,deladdUserPoint} from '@/api/kqgl/tj/tj'
import {GetGroupList} from '@/api/kqgl/pzgl/pzgl'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {dateFormat, getDate} from "@/utils/tool";
import dkxq from './dkxq'
import fileDownload from "js-file-download";
import FormPannel from './components/FormPannel'

export default {
  name: 'index',
  components: {Table, Pagination, Dropdown,dkxq,FormPannel},
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  data() {
    return {
      // 地图src
      tableData: [],
      tableOptions: [
        {label: '姓名', prop: 'applyUserName'},
        {label: '部门', prop: 'applyUserDeptName'},
        {label: '日期', prop: 'clockTime'},
        {label: '上班打卡时间', prop: 'clockStartTime'},
        {label: '上班是否异地打卡', prop: 'clockStartPlace',slot:true},
        {label: '上班异地打卡说明', prop: 'clockStartPlaceNote'},
        {label: '下班打卡时间', prop: 'clockEndTime'},
        {label: '下班是否异地打卡', prop: 'clockEndPlace',slot:true},
        {label: '下班异地打卡说明', prop: 'clockEndPlaceNote'},
        {label: '异常类型', prop: 'errorInfo'},
        {label: '部门确认信息', prop: 'bmrkUserName'}
      ],
      realTableOptions: [],
      loading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        userName: '',
        groupId: -1,
        type:"-1",
        // date:getDate(),
        time: [this.dateFormatYmd(dateFormat()), this.dateFormatYmd(dateFormat())],
        startDate: this.dateFormatYmd(dateFormat()),
        endDate: this.dateFormatYmd(dateFormat())
      },
      // 查询部门列表
      groupItem: [],

      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      clbgParams: {},
      dialogGroup: [],
      dialogType: '',
      dialogTitle: '',
      dkxqDialog: false,
      dtDialog: false,


      // 轨迹路径
      locus: {
        path: '/kqgl/locus',
        query: {
          userId: '',
          date:'',
          userName: ''
        }
      },

      // // 主报表窗口
      fbbDialog: false,
      // 主报表窗口标题
      fbbDialogTitle: '',
      insertPointDialog: false,
      // 主报表列表
      fbbList: [],
      pingParams:{
        date:"",
        uid:"",
      },
      pingInfo:{
        isShow:true,
        pointSize:0,
        residsSize:0
      }
    }
  },
  mounted() {
    let x =  this.$store.state.user;
    console.log("xxx:"+ JSON.stringify(x))
    this.getListMrTj()
    GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
    })
  },
  methods: {
    handleChange() {
      this.queryParams.startDate = this.queryParams.time[0]
      this.queryParams.endDate = this.queryParams.time[1]
      this.queryParams.pageNum = 1
      this.getListMrTj()
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams.pageNum = val
      this.getListMrTj()
    },
    getListMrTj(){
      this.loading = true
      getListMrTj(this.queryParams).then((res) => {
        this.tableData = res.result.records || []
        this.queryParams.total = res.result.total

      }).finally(() => {
        this.loading = false
      })
    },

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },


    /** 重置查询车队长 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getListMrTj()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.clbgParams = row
      this.locus.query.userId = row.applyUser
      this.locus.query.date = row.clockTime
      this.locus.query.userName = row.applyUserName

      //ping数据
      this.pingParams.uid = row.applyUser;
      this.pingParams.date =  row.clockTime;
    },
    /** 打开新增弹窗 */
    openDialog(type) {
      switch (type) {
        case 'show':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = this.clbgParams.applyUserName+this.clbgParams.clockTime+'考勤信息'
            this.dialogType = 'show'
            this.dkxqDialog = true
          }
          break
        case 'showGJ':
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            // this.$router.push(this.locus)
            const newpage = this.$router.resolve(this.locus)
            window.open(newpage.href, '_blank')
          }
          break
        case 'mrkqdownload':
          mrTjDownLoad(this.queryParams).then((res) => {
            // 构造一个blob对象来处理数据
            const blob = res
            const fileName =this.queryParams.startDate+'-'+this.queryParams.endDate+'打卡情况' + '.xlsx'
            fileDownload(blob, fileName)


            //
            // // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
            // // IE10以上支持blob但是依然不支持 download
            // if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            //   const link = document.createElement('a') // 创建a标签
            //   link.download = fileName // a标签添加属性
            //   link.style.display = 'none'
            //   link.href = URL.createObjectURL(blob)
            //   document.body.appendChild(link)
            //   link.click() // 执行下载
            //   URL.revokeObjectURL(link.href) // 释放url
            //   document.body.removeChild(link) // 释放标签
            // } else { // 其他浏览器
            //   navigator.msSaveBlob(blob, fileName)
            // }
          })
          break
        case 'fbbshow':
          if (this.fbbSelectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.fbbDialogTitle = this.clbgParams.applyUserName+this.clbgParams.clockTime+'Ping信息'
            this.dialogType = 'fbbshow'
            this.fbbDialog = true
            //数据绑定

            getListPing(this.pingParams).then((res) => {
              this.fbbList = res.result.pingList
              this.pingInfo.isShow = res.result.isShow
              this.pingInfo.pointSize = res.result.pointSize
              this.pingInfo.residsSize = res.result.residsSize
            })
          }
          break
        case 'insertPoint':
            this.fbbDialogTitle = '点位补充'
            this.dialogType = 'insertPoint'
            this.insertPointDialog = true

          break
      }
    },
    /** 关闭弹窗 */
    closeDialog() {
      this.dkxqDialog = false
    },
    closeDTDialog () {
      this.clbgParams.unitGPS = window.parent.document.getElementById('clbgd.gps').value
      this.$nextTick(() => this.$refs.stopParams.clearValidate())
      this.dtDialog = false
    },

    //新增和修改完成后提交
    async handleSubmit(data){
      let submitData = {...data}
        if(submitData.type=='0'){
          //新增点位
          await addUserPoint(submitData).then(res=>{
            if(res.success){
              this.$message({
                type: 'success',
                message: res.message
              })
            }else{
              this.$message.error(res.message)
            }
          })
        }else{
          //删除点位
          await deladdUserPoint(submitData).then(res=>{
            if(res.success){
              this.$message({
                type: 'success',
                message: res.message
              })
            }else{
              this.$message.error(res.message)
            }
          })
        }

     //   this.insertPointDialog = false

    },
    /** 时间格式化 */
    formatDate(row, column) {
      // 获取单元格数据
      const data = row[column.property];
      const dt = new Date(data);
      var month = dt.getMonth() + 1;
      var day = dt.getDate();
      var hour = dt.getHours();
      var minutes = dt.getMinutes();
      var seconds = dt.getSeconds();
      if (month < 10) {
        month = "0" + month;
      }
      if (day < 10) {
        day = "0" + day;
      }
      if (hour < 10) {
        hour = "0" + hour;
      }
      if (minutes < 10) {
        minutes = "0" + minutes;
      }
      if (seconds < 10) {
        seconds = "0" + seconds;
      }
      if (data === null) {
      } else {
        return (
            dt.getFullYear() +
            "-" +
            month +
            "-" +
            day +
            " " +
            hour +
            ":" +
            minutes +
            ":" +
            seconds
        );
      }
    },
    closefbbDialog () {
      this.fbbDialog= false
      this.insertPointDialog= false
    },

    dateFormatYmd(time) {
      var date=new Date(time);
      var year=date.getFullYear();
      /* 在日期格式中，月份是从0开始的，因此要加0
       * 使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05
       * */
      var month= date.getMonth()+1<10 ? "0"+(date.getMonth()+1) : date.getMonth()+1;
      var day=date.getDate()<10 ? "0"+date.getDate() : date.getDate();
      // var hours=date.getHours()<10 ? "0"+date.getHours() : date.getHours();
      // var minutes=date.getMinutes()<10 ? "0"+date.getMinutes() : date.getMinutes();
      // var seconds=date.getSeconds()<10 ? "0"+date.getSeconds() : date.getSeconds();
      // 拼接
      return year+"-"+month+"-"+day;
    }
  }
}
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
       margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 108px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}

</style>
