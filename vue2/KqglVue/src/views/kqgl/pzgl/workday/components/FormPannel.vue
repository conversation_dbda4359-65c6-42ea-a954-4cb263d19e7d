<template>
  <el-form :model="clbgParams" :rules="rules" ref="workdayForm" label-width="120px" v-watermark="{label: watermark}">
    <el-row>
      <el-col :span="10">
        <el-form-item label="日期" prop="date">
          <el-date-Picker
              clearable
              v-model="clbgParams.date"
              style="width: 100%"
          >
          </el-date-Picker>
        </el-form-item>

      </el-col>
      <el-col :span="10">
        <el-form-item label="日期类型" prop="status">
          <el-input size="mini" v-model="clbgParams.id" v-show="false"></el-input>
          <el-select size="mini" v-model="clbgParams.status"  filterable clearable  placeholder="请选择类型">
            <el-option
                v-for="item in statusGroup"
                :key="item.id"
                :label="item.realname"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

      </el-col>
    </el-row>


    <el-dialog
        width="80%"
        :visible.sync="dtDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :append-to-body="true"
        :modal-append-to-body="false"
        @close="closeDTDialog"
        :close-on-click-modal="false"
    >
      <div style="width: 100%;height: 100%">
        <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                style="width: 100%;height: 500px"></iframe>
        <el-button @click="closeDTDialog" id="clbgd.dtDialogClose" v-show="false"></el-button>
      </div>
    </el-dialog>


    <el-form-item>
      <div style="text-align: right">
        <el-button v-if="btnType !== '查看'" type="primary" @click="handleSubmit('workdayForm')">保存</el-button>
        <el-button @click="$emit('handleClose')">关闭</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import {getYyglType, getYySend} from '@/api/yygl'
import {editPosition, getDeptUsers, GetGroupList} from "../../../../../api/kqgl/pzgl/pzgl";

export default {
  name: 'FormPannel',
  props: {
    btnType: {
      type: String,
      default: '新增'
    }
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  data() {
    return {
      // 地图src
      src: process.env.NODE_ENV === 'development' ? '/static/kqgl/html/BaiDuToBGD.html' : 'static/kqgl/html/BaiDuToBGD.html',
      //选择列表数据
      clbgParams: {},

      dtDialog: false,
      clockStartTimePicker: '',
      clockEndTimePicker: '',
      statusGroup: [
        {
          id: 0,
          realname: '休息日'
        },
        {
          id: 1,
          realname: '工作日'
        },
        {
          id: 2,
          realname: '节假日'
        },
      ], //日期类型下拉框数据
      rules: {
        date: [
          {
            required: true,
            message: '请选择日期',
            trigger: 'blur'
          }
        ],
        status: [
          {
            required: true,
            message: '请选择日期类型',
            trigger: 'blur'
          }
        ],
      },

    }
  },
  mounted() {
    //if(this.btnType !== '查看') this.getOptions()
    this.src = process.env.NODE_ENV === 'development' ? '/static/kqgl/html/BaiDuToBGD.html' : 'static/kqgl/html/BaiDuToBGD.html'
    if (this.btnType == "新增") {

    }


  },
  methods: {

    closeDTDialog() {

      //this.$nextTick(() => this.$refs.stopParams.clearValidate())

      this.dtDialog = false
    },
    /** 关闭弹窗 */
    closeDialog() {
      //this.getListBm()
      this.clbgDialog = false
    },


    //提交表单数据
    handleSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {

          this.$emit('handleSubmit', this.clbgParams)

        } else {
          return false
        }
      })
    },
    //初始化
    initData(data) {
      this.clbgParams = {}
      console.log(data)
      if (data.id) {
        this.clbgParams = {...data}
      }
    },

  }
}
</script>

<style lang="scss" scoped>
</style>
