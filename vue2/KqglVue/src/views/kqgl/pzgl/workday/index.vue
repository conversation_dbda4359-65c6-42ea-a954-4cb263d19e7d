<template>
  <div class="yygl-container">
    <div class="list-box" style=" width: 96%">
      <div class="operate-pannel">
        <div class="search-box">
          <span class="font-size14" style="align-items: center; display: flex;">时间段：</span>
          <el-date-picker
              v-model="queryParam.dates"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="dateChange"
              value-format="yyyy-MM-dd"
              :default-time="['00:00:00', '23:59:59']">
          </el-date-picker>
          <span class="font-size14" style="align-items: center; display: flex;">年份：</span>
          <el-input v-model="queryParam.year" clearable placeholder="请输入年" style="width: 5vw;"></el-input>
          <span class="font-size14" style="align-items: center; display: flex;">月份：</span>
          <el-input v-model="queryParam.month" clearable placeholder="请输入月" style="width: 5vw;"></el-input>
          <span class="font-size14" style="align-items: center; display: flex;">日期类型：</span>
          <el-select v-model="queryParam.status" clearable placeholder="请选择" @change="getTableList">
            <el-option
                v-for="item in statusGroup"
                :key="item.id"
                :label="item.realname"
                :value="item.id">
            </el-option>
          </el-select>

          <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
          <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
          <el-button type="text" icon="el-icon-plus" @click="handleOpenDialog('新增')"   v-if="this.$store.state.user.permissions.indexOf('JDWKQ01GR01QX01') > -1">新增</el-button>
          <el-button type="text" icon="el-icon-edit"  @click="handleOpenDialog('修改')"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01GR01QX02') > -1">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01GR01QX03') > -1">删除</el-button>
          <el-button type="text" icon="el-icon-view" @click="handleOpenDialog('查看')">查看</el-button>
          <el-button type="text" icon="el-icon-s-order" @click="getWorkDay">获取今年最新节假日</el-button>
<!--          <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>-->
        </div>
      </div>
      <div class="table-box">

        <el-table
            :border="true"
            ref="eltab_List"
            :data="tableData"
            stripe
            border
            highlight-current-row
            v-loading="loading"
            :tableOptions="tableOptions"
            style="width: 100%"
            height="90%"
            @current-change="getCurrentData"
            v-watermark="{label:watermark}"
        >
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              {{
                (queryParam.pageNum - 1) * queryParam.pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>

          <el-table-column prop="date" label="日期" width="width" align="center">
            <template slot-scope="{row,$index}">
              <span>{{formatDate(row.date)}}</span>
            </template>
          </el-table-column>

          <el-table-column prop="clockImgFileUrl" label="星期" width="width" align="center">
            <template slot-scope="{row,$index}">
              <span>{{ formatWeek(row.week) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="clockImgFileUrl" label="日期类型" width="width" align="center">
            <template slot-scope="{row,$index}">
              <span v-if="row.status==0">休息日</span>
              <span v-if="row.status==1">工作日</span>
              <span v-if="row.status==2">节假日</span>
            </template>
          </el-table-column>

        </el-table>
        <Pagination
            :total="total"
            :queryParam="queryParam"
            @handleRefresh="handleRefresh"
        />
      </div>
    </div>

    <el-dialog :title="btnType" :visible.sync="dialogFormVisible" destroy-on-close>
      <FormPannel
          ref="workdayForm"
          :btnType="btnType"
          @handleSubmit="handleSubmit"
          @handleClose="dialogFormVisible = false"/>
    </el-dialog>



  </div>
</template>
<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import UploadFile from '@/components/UploadFile'
import SubmitForm from '@/components/SubmitForm'
import FormPannel from './components/FormPannel'
import Dropdown from '@/components/ColumnDropdown'
import dayjs from 'dayjs'
import {
  GetGroupList, listWorkDay, getWorkDayDetails, addWorkDay, updateWorkDay, delWorkDayById, runGetWorkDay
} from "../../../../api/kqgl/pzgl/pzgl";
export default {
  name:'index',
  components:{Table,Pagination,UploadFile,SubmitForm,FormPannel,Dropdown},
  data(){
    return {

      statusGroup: [
        {
          id: 0,
          realname: '休息日'
        },
        {
          id: 1,
          realname: '工作日'
        },
        {
          id: 2,
          realname: '节假日'
        },
      ], //日期类型下拉框数据
      tableData:[],
      tableOptions:[
        {label: '日期', prop:'date'},
        {label: '月份', prop:'month'},
      ],
      realTableOptions:[],
      queryParam: {
        current: 1,
        size: 10,
      },
      total:0,
      loading:false,

      dialogFormVisible: false, //新增修改表单显示
      btnType: '新增',//默认新增按钮
      currentSelectId:'',
      currentRowData:{},
      remark:'',//流程意见
      submitDialogVisible: false,


      // 查询部门列表
      groupItem: [],


    }
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    },
  },
  mounted(){
    this.getTableList()
    GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
      let data = res.result
      this.groupItem = data
      // data = data.slice(1, data.length)
      // this.dialogGroup = data
    })
  },
  methods:{
    dateChange(val){
      this.queryParam.beginDate = null
      this.queryParam.overDate = null
      if (val) {
        this.queryParam.beginDate = dayjs(val[0]).format('YYYY-MM-DD')
        this.queryParam.overDate = dayjs(val[1]).format('YYYY-MM-DD')
      }
      this.getTableList()
    },

    formatDate(date){
      return date ? dayjs(date).format('YYYY-MM-DD') : date
    },
    formatWeek(week){
      if (week === 1) return '星期一'
      if (week === 2) return '星期二'
      if (week === 3) return '星期三'
      if (week === 4) return '星期四'
      if (week === 5) return '星期五'
      if (week === 6) return '星期六'
      if (week === 7) return '星期日'
      return week
    },
    //获取新表格列表
    getNewArr(newTableOptions){
      this.realTableOptions = [...newTableOptions]
      // this.$refs.yyglTable.doLayout()
    },
    getWorkDay(){
      runGetWorkDay().then(res=>{
        this.$message.info(res.message)
      })
    },
    //获取表格数据
    async getTableList(){
      this.loading = true
      this.queryParam.pageSize = this.queryParam.size
      this.queryParam.pageNum = this.queryParam.current
      const params = {...this.queryParam}
      await listWorkDay(params).then(res=>{
        this.tableData = res.result.records || []
        this.total = res.result.total || 0
        //this.$refs.yyglSearchTable.setCurrent(this.tableData[0])
        this.loading = false
      }).catch(err=>{
        this.loading = false
      })
    },
    //模糊查询和刷新
    handleRefresh(queryParam){
      this.queryParam = {...queryParam}
      this.getTableList()
    },
    //打开表单弹窗
    handleOpenDialog(type){
      if(type !== '新增' && !this.currentSelectId){
        this.$message({
          type: 'warning',
          message: '请先选择一条数据！'
        })
        return
      }
      this.dialogFormVisible = true
      this.btnType = type
      if(type !== '新增'){
        //修改和查看初始化数据
        getWorkDayDetails(this.currentSelectId).then(res=>{
          this.$refs['workdayForm'].initData(res.result || {})
        }).catch(err=>{
          this.$message({
            type:'error',
            message:'获取数据失败！'
          })
        })
      }
    },
    //获取当前点击的数据id
    getCurrentData(val){
      this.currentSelectId = val.id
      this.currentRowData = val
    },
    //新增和修改完成后提交
    async handleSubmit(data){
      let submitData = {...data}

      if(this.btnType === '新增'){
        await addWorkDay(submitData).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '新增数据成功！'
            })
          }else{
            this.$message({
              type: 'success',
              message: '新增数据失败！'
            })
          }
        })
        this.dialogFormVisible = false
        this.getTableList()
      }else{
        await updateWorkDay(submitData).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '修改数据成功！'
            })
          }else{
            this.$message({
              type: 'error',
              message: '修改数据失败！'
            })
          }
        })
        this.currentSelectId = ''
        this.dialogFormVisible = false
        this.getTableList()
      }
    },
    //删除一条数据
    handleDelete(){
      if(!this.currentSelectId){
        this.$message({
          type: 'warning',
          message: '请先选择一条数据！'
        })
        return
      }
      this.$confirm('确认删除该条记录？').then(async _=>{
        await delWorkDayById(this.currentSelectId).then(res=>{
          if(res){
            this.$message({
              type: 'success',
              message: '删除数据成功！'
            })
          }else{
            this.$message({
              type: 'error',
              message: '删除数据失败！'
            })
          }
        })
        this.currentSelectId = ''
        this.getTableList()
      }).catch(_=>{
        this.$message({
          type: 'info',
          message: '已取消删除！'
        })
      })

    },


  }
}
</script>
<style lang="scss" scoped>
.list-box{
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;
  .operate-pannel{
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-box{
      display: flex;
      .el-input{
        width: 220px;
        margin-right: 5px;
      }
    }
    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }

  .table-box{
    width: 100%;
    height: calc(100% - 100px);

    ::v-deep .el-pagination{
      margin-top: 10px;
      text-align:center;
    }
  }
}

.fold-icon{
  cursor: pointer;
  float:right;
}

.process-info{
  width: 30%;
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;

  .process-title{
    height: 30px;
    font-weight: bold;
  }
  .process-table-box{
    width: 100%;
    height: 50%;
  }

  .opinion-process{
    margin: 10px 0;
    display: flex;

    .opinion-text{
      width: 100%;
      height: 60px;
      overflow-y: auto;
      border: #dfe6ec 1px solid;
      padding: 4px;
    }
  }

  .appendix-table-box{
    height: calc(50% - 150px);
  }
}
//折叠后样式
.fold-style{
  width: 0 !important;
  height: 100%;
  padding: 10px;
  transition: all .5s
}

</style>
