<template>
    <el-form :model="params" :rules="rules" ref="grdwdForm" label-width="120px"  v-watermark="{label: watermark}">
      <el-row>
            <el-col :span="20">
              <el-form-item label="姓名"  prop="userName">
                <el-input size="mini" v-model="params.userName"></el-input>
              </el-form-item>
              <el-form-item label="身份证号"  prop="cardNo">
                <el-input size="mini" v-model="params.cardNo"></el-input>
              </el-form-item>
            </el-col>
        </el-row>

        <el-form-item>
            <div style="text-align: right">
                <el-button type="primary" @click="handleSubmit('grdwdForm')">保存</el-button>
                <el-button @click="$emit('handleClose')">关闭</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script>
export default {
    name:'FormPannel',
    computed:{
        watermark(){
            return this.$store.state.user.watermark
        }
    },
    data(){
        return {
            //选择列表数据
            params: {},
            rules:{
              userName: [
                {
                  required: true,
                  message: '请输入姓名',
                  trigger: 'blur'
                }
              ],
              cardNo: [
                {
                  required: true,
                  message: '请选择身份证号',
                  trigger: 'blur'
                }
              ],
            },
        }
    },
    mounted(){

    },
    methods:{
        //提交表单数据
        handleSubmit(formName){
            this.$refs[formName].validate((valid)=>{
              if (valid) {
                this.$emit('handleSubmit', this.params)
              } else {
                return false
              }
            })
        },
    }
}
</script>

<style lang="scss" scoped>
</style>
