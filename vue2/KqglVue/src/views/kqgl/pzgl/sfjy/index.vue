<template>
  <div class="yygl-container">
    <div class="list-box" style=" width: 96%">
      <div class="operate-pannel">
        <div class="search-box">
          <el-button type="text" icon="el-icon-s-custom" @click="handleOpenDialog('单个校验')" v-if="this.$store.state.user.permissions.indexOf('JDWKQ01SF01QX01') > -1">单个校验</el-button>
          <el-button type="text" icon="el-icon-upload" @click="handleOpenDialog('导入检验')" v-if="this.$store.state.user.permissions.indexOf('JDWKQ01SF01QX02') > -1">导入检验</el-button>
          <el-button type="text" icon="el-icon-download" @click="handleOpenDialog('download')" v-if="this.$store.state.user.permissions.indexOf('JDWKQ01SF01QX03') > -1">导出</el-button>
          <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
        </div>
      </div>
      <div class="table-box">
        <Table
            ref="yyglTable"
            :tableData="tableData"
            :tableOptions="realTableOptions"
            :loading="loading"
            @getCurrentData="getCurrentData">
          <!-- 状态-->
          <template slot="sfzMatched" slot-scope="scope">
            <el-tag type="success" v-if="scope.row.sfzMatched == 1">通过</el-tag>
            <el-tag type="danger" v-if="scope.row.sfzMatched != 1">未通过</el-tag>
          </template>
        </Table>

      </div>
    </div>

    <el-dialog :title="btnType" :visible.sync="dialogFormVisible" destroy-on-close width="400px">
      <form-pannel v-if="btnType === '单个校验'" @handleSubmit="handleSubmit"/>
      <upload-file
          v-if="btnType === '导入检验'"
          :fileType="['xls','xlsx']"
          upload-url="/sfzCheck/importExcel"
          @uploadSuccessData="uploadSuccessData"/>
    </el-dialog>
  </div>
</template>
<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import UploadFile from '@/components/UploadFile'
import FormPannel from './components/FormPannel'
import Dropdown from '@/components/ColumnDropdown'
import dayjs from 'dayjs'
import fileDownload from 'js-file-download'
import {check, importExcel,DownLoad} from "api/kqgl/pzgl/sfjy";
export default {
  name:'index',
  components:{Table,Pagination,UploadFile,FormPannel,Dropdown},
  data(){
    return {
      tableData:[],
      tableOptions:[
        {label: '姓名', prop:'userName'},
        {label: '身份证', prop:'cardNo'},
        {label: '认证结果', prop:'sfzMatched', slot: true},
        {label: '核验信息', prop:'resutMsg'},
      ],
      realTableOptions:[],
      loading:false,
      dialogFormVisible: false, //新增修改表单显示
      btnType: '新增',//默认新增按钮
      currentSelectId:'',
      currentRowData:{},
    }
  },
  mounted(){
    this.tableData = []
  },
  methods:{
    formatDate(date){
      return date ? dayjs(date).format('YYYY-MM-DD') : date
    },
    //获取新表格列表
    getNewArr(newTableOptions){
      this.realTableOptions = [...newTableOptions]
    },
    //打开表单弹窗
    handleOpenDialog(type){
      if(type == 'download'){
       let res =  JSON.stringify(this.tableData)
        DownLoad(this.tableData).then((res) => {
          const blob = res
          // 构造一个blob对象来处理数据
          const fileName = '身份核验结果' + '.xlsx'
          // 导出文件名 // 对于<a>标签，只有 Firefox和Chrome（内核）支持 download 属性
          // IE10以上支持blob但是依然不支持 download
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = document.createElement('a') // 创建a标签
            link.download = fileName // a标签添加属性
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            document.body.appendChild(link)
            link.click() // 执行下载
            URL.revokeObjectURL(link.href) // 释放url
            document.body.removeChild(link) // 释放标签
          } else { // 其他浏览器
            navigator.msSaveBlob(blob, fileName)
          }
        })
      }else{
        this.dialogFormVisible = true
        this.btnType = type
      }

    },
    //获取当前点击的数据id
    getCurrentData(val){
      this.currentSelectId = val.id
      this.currentRowData = val
    },
    handleSubmit(param){
      this.loading = true
      check(param).then(res => {
        console.log(res)
        this.tableData = res.result
      }).finally(() => {
        this.loading = false
        this.dialogFormVisible = false
      })
    },
    uploadSuccessData(data){
      this.tableData = data
    }
  }
}
</script>
<style lang="scss" scoped>
.list-box{
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;
  .operate-pannel{
    height: 50px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-box{
      display: flex;
      .el-input{
        width: 220px;
        margin-right: 5px;
      }
    }
    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }

  .table-box{
    width: 100%;
    height: calc(100% - 100px);

    ::v-deep .el-pagination{
      margin-top: 10px;
      text-align:center;
    }
  }
}

.fold-icon{
  cursor: pointer;
  float:right;
}

.process-info{
  width: 30%;
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;

  .process-title{
    height: 30px;
    font-weight: bold;
  }
  .process-table-box{
    width: 100%;
    height: 50%;
  }

  .opinion-process{
    margin: 10px 0;
    display: flex;

    .opinion-text{
      width: 100%;
      height: 60px;
      overflow-y: auto;
      border: #dfe6ec 1px solid;
      padding: 4px;
    }
  }

  .appendix-table-box{
    height: calc(50% - 150px);
  }
}
//折叠后样式
.fold-style{
  width: 0 !important;
  height: 100%;
  padding: 10px;
  transition: all .5s
}

</style>
