<template>
    <div class="yygl-container">
        <div class="list-box" style=" width: 96%">
            <div class="operate-pannel">
                <div class="search-box">

                    <span class="font-size14" style="align-items: center; display: flex;">部门：</span>
                    <el-select v-model="queryParam.groupId" clearable filterable placeholder="请选择管理部门" @change="getTableList">
                      <el-option
                          v-for="item in groupItem"
                          :key="item.id"
                          :label="item.groupname"
                          :value="item.id">
                      </el-option>
                    </el-select>
                  <span class="font-size14" style="align-items: center; display: flex;">办公地址：</span>
                  <el-input v-model="queryParam.companyAddress" clearable placeholder="请输入办公地点地址" @change="getTableList"></el-input>
                    <el-button type="text" icon="el-icon-search" @click="getTableList">查询</el-button>
                    <el-button type="text" icon="el-icon-refresh" @click="getTableList">刷新</el-button>
                    <el-button type="text" icon="el-icon-plus"  @click="handleOpenDialog('新增')"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01BM01QX01') > -1">新增</el-button>
                    <el-button type="text" icon="el-icon-edit"  @click="handleOpenDialog('修改')"   v-if="this.$store.state.user.permissions.indexOf('JDWKQ01BM01QX02') > -1">修改</el-button>
                    <el-button type="text" icon="el-icon-delete"  @click="handleDelete"  v-if="this.$store.state.user.permissions.indexOf('JDWKQ01BM01QX03') > -1">删除</el-button>
                    <el-button type="text" icon="el-icon-view" @click="handleOpenDialog('查看')">查看</el-button>
                    <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
                </div>
            </div>
            <div class="table-box">
                <Table
                    ref="yyglTable"
                    :tableData="tableData"
                    :tableOptions="realTableOptions"
                    :loading="loading"
                    :queryParam="queryParam"
                    @getCurrentData="getCurrentData">
                    <template slot="yycount" slot-scope="scope">
                        {{scope.row.yycount == 0 ? '' : scope.row.yycount}}
                    </template>
                </Table>
                <Pagination
                    :total="total"
                    :queryParam="queryParam"
                    @handleRefresh="handleRefresh"
                />
            </div>
        </div>

        <el-dialog :title="btnType" :visible.sync="dialogFormVisible" destroy-on-close>
            <FormPannel
                ref="bmdwdForm"
                :btnType="btnType"
                @handleSubmit="handleSubmit"
                @handleClose="dialogFormVisible = false"/>
        </el-dialog>

        <el-dialog title="表单" :visible.sync="submitDialogVisible" destroy-on-close>
            <SubmitForm
                ref="submitForm"
                :rowData="currentRowData"
                @handleSubmit="handleProcessSubmit"
                @handleClose="submitDialogVisible = false"/>
        </el-dialog>


    </div>
</template>
<script>
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import UploadFile from '@/components/UploadFile'
import SubmitForm from '@/components/SubmitForm'
import FormPannel from './components/FormPannel'
import Dropdown from '@/components/ColumnDropdown'
import {
    getyyglList,
    queryYygl,
    addYygl,
    editYygl,
    deleteYygl,
    getYyglFile,
    addYyglFile,
    deleteYyglFile,
    downloadYyglFile,
    getWorkFlowList,
    submitFlow,
    getCanSubmitWjDate
} from '@/api/yygl'
import dayjs from 'dayjs'
import fileDownload from 'js-file-download'
import {
  getListBm,
  GetGroupList,
  addPosition,
  queryPositionById,
  editPosition,
  delPositionById
} from "../../../../api/kqgl/pzgl/pzgl";
export default {
    name:'index',
    components:{Table,Pagination,UploadFile,SubmitForm,FormPannel,Dropdown},
    data(){
        return {


            tableData:[],
            tableOptions:[
              {label: '部门', prop:'deptName'},
              {label: '上午上班时间', prop:'clockStartTime'},
              {label: '下午上班时间', prop:'clockEndTime'},
              {label: '办公地址', prop:'companyAddress'},
              {label: '办公地点经度', prop:'longitude'},
              {label: '办公地点纬度', prop:'latitude'}
            ],
            realTableOptions:[],
            queryParam: {
                current: 1,
                size: 10,
                unitName: '',
                companyAddress: '',
                groupId: -1,
                type: '1' // 查询部门打卡点
            },
            total:0,
            loading:false,

            dialogFormVisible: false, //新增修改表单显示
            btnType: '新增',//默认新增按钮
            currentSelectId:'',
            currentRowData:{},
            remark:'',//流程意见
            submitDialogVisible: false,


            // 查询部门列表
            groupItem: [],


        }
    },
    mounted(){
        this.getTableList()
        GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
          let data = res.result
          this.groupItem = data
          // data = data.slice(1, data.length)
          // this.dialogGroup = data
        })
    },
    methods:{
        formatDate(date){
            return date ? dayjs(date).format('YYYY-MM-DD') : date
        },
        //获取新表格列表
        getNewArr(newTableOptions){
            this.realTableOptions = [...newTableOptions]
            // this.$refs.yyglTable.doLayout()
        },
        //获取表格数据
        async getTableList(){
            this.loading = true
          this.queryParam.pageSize = this.queryParam.size
          this.queryParam.pageNum = this.queryParam.current
          const params = {...this.queryParam}
          await getListBm(params).then(res=>{
            this.tableData = res.result.records || []
            this.total = res.result.total || 0
            this.$refs.yyglSearchTable.setCurrent(this.tableData[0])
            this.loading = false
          }).catch(err=>{
            this.loading = false
          })
        },
        //模糊查询和刷新
        handleRefresh(queryParam){
            this.queryParam = {...queryParam}
            this.getTableList()
        },
        //打开表单弹窗
        handleOpenDialog(type){
            if(type !== '新增' && !this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return
            }
            this.dialogFormVisible = true
            this.btnType = type
            if(type !== '新增'){
                //修改和查看初始化数据
              queryPositionById(this.currentSelectId).then(res=>{
                    this.$refs['bmdwdForm'].initData(res.result || {})
                }).catch(err=>{
                    this.$message({
                        type:'error',
                        message:'获取数据失败！'
                    })
                })
            }
        },
        //获取当前点击的数据id
        getCurrentData(val){
            this.currentSelectId = val.id
            this.currentRowData = val
            //获取流程信息
            //this.handleGetWorkFlowData()
            //获取附件信息
            //this.handleGetAppendixData()
        },
        //获取流程信息表格中点击数据的id
        getCurrentProcessData(val){
            this.remark = val.feed
        },
        //新增和修改完成后提交
        async handleSubmit(data){
            let submitData = {...data}

            if(this.btnType === '新增'){
                await addPosition(submitData).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '新增数据成功！'
                        })
                    }else{
                        this.$message({
                            type: 'success',
                            message: '新增数据失败！'
                        })
                    }
                })
                this.dialogFormVisible = false
                this.getTableList()
            }else{
                await editPosition(submitData).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '修改数据成功！'
                        })
                    }else{
                        this.$message({
                            type: 'error',
                            message: '修改数据失败！'
                        })
                    }
                })
                this.dialogFormVisible = false
                this.getTableList()
            }
        },
        //删除一条数据
        handleDelete(){
            if(!this.currentSelectId){
                this.$message({
                    type: 'warning',
                    message: '请先选择一条数据！'
                })
                return
            }
            this.$confirm('确认删除该条记录？').then(async _=>{
                await delPositionById({
                  id: this.currentSelectId
                }).then(res=>{
                    if(res){
                        this.$message({
                            type: 'success',
                            message: '删除数据成功！'
                        })
                    }else{
                        this.$message({
                            type: 'error',
                            message: '删除数据失败！'
                        })
                    }
                })
                this.getTableList()
            }).catch(_=>{
                this.$message({
                    type: 'info',
                    message: '已取消删除！'
                })
            })

        },

        //提交流程
        async handleProcessSubmit(val){
            const params={
                lcjdid: 910201,
                ywid: this.currentSelectId,
                ...val
            }
            await submitFlow(params).then(res=>{
                if(res.success){
                    this.$message({
                        type: 'success',
                        message: '提交成功！'
                    })
                }
            })
            this.submitDialogVisible = false
            this.getTableList()
        },

        //新增附件
        async importFile(formData){
            formData.append('id',this.currentSelectId)
            await addYyglFile(formData).then(res=>{
                if(res.success === true){
                    this.$message({
                        type: 'success',
                        message: res.text
                    })
                }else{
                    this.$message({
                    type: 'error',
                    message: res.text
                })
                }
            })
            this.handleGetAppendixData()
        },

    }
}
</script>
<style lang="scss" scoped>
.list-box{
    height: 100%;
    box-shadow: 0 0 6px #d9e2f1aa;
    border-radius: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    transition: all .5s;
    .operate-pannel{
        height: 50px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .search-box{
            display: flex;
            .el-input{
                width: 220px;
                margin-right: 10px;
            }
        }
      .el-date-editor{
        margin-right: 10px;
      }
      .el-input {
        width: 200px;
        margin-right: 10px;
      }
      .el-select{
        margin-right: 10px;
      }
    }

    .table-box{
        width: 100%;
        height: calc(100% - 100px);

        ::v-deep .el-pagination{
            margin-top: 10px;
            text-align:center;
        }
    }
}

.fold-icon{
    cursor: pointer;
    float:right;
}

.process-info{
    width: 30%;
    height: 100%;
    box-shadow: 0 0 6px #d9e2f1aa;
    border-radius: 10px;
    padding: 10px;
    transition: all .5s;

    .process-title{
        height: 30px;
        font-weight: bold;
    }
    .process-table-box{
        width: 100%;
        height: 50%;
    }

    .opinion-process{
        margin: 10px 0;
        display: flex;

        .opinion-text{
            width: 100%;
            height: 60px;
            overflow-y: auto;
            border: #dfe6ec 1px solid;
            padding: 4px;
        }
    }

    .appendix-table-box{
        height: calc(50% - 150px);
    }
}
//折叠后样式
.fold-style{
    width: 0 !important;
    height: 100%;
    padding: 10px;
    transition: all .5s
}

</style>
