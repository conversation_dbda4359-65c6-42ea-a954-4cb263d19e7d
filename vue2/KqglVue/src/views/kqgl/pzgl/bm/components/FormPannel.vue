<template>
    <el-form :model="clbgParams" :rules="rules" ref="bmdwdForm" label-width="120px" v-watermark="{label: watermark}">
        <el-row>
            <el-col :span="20">
                <el-form-item label="所属部门" prop="groupId">

                  <el-input size="mini" v-model="clbgParams.groupId" :value="clbgParams.groupId" v-show="false"></el-input>

                  <el-select size="mini" v-model="clbgParams.groupName" clearable filterable placeholder="请选择管理部门" style="width: 100%" @change="deptChange">
                    <el-option
                        v-for="item in dialogGroup"
                        :key="item.id"
                        :label="item.groupname"
                        :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="10">
                <el-form-item label="上午上班时间" prop="clockStartTime">
                  <el-input size="mini" v-model="clbgParams.clockStartTime" placeholder="8:30:00|11:30:00" v-show="false"></el-input>
                  <el-time-picker
                      is-range
                      clearable
                      v-model="clockStartTimePicker"
                      range-separator="|"
                      value-format="HH:mm:ss"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围"
                      @change="changeClockStartTimePicker"
                      style="width: 100%"
                  >
                  </el-time-picker>
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item label="下午上班时间" prop="clockEndTime">
                  <el-input size="mini" v-model="clbgParams.clockEndTime" placeholder="13:30:00|17:00:00" v-show="false"></el-input>
                  <el-time-picker
                      is-range
                      clearable
                      v-model="clockEndTimePicker"
                      range-separator="|"
                      value-format="HH:mm:ss"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      placeholder="选择时间范围"
                      @change="changeClockEndTimePicker"
                      style="width: 100%"
                  >
                  </el-time-picker>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="20">
              <el-input size="mini" v-model="clbgParams.status" value="1" v-show="false"></el-input>
              <el-form-item label="办公地点地址"  prop="companyAddress">
                <el-input size="mini" v-model="clbgParams.companyAddress"></el-input>
              </el-form-item>
              <el-form-item label="办公地点经度" prop="longitude" style="display: none">
                <el-input size="mini" v-model="clbgParams.longitude" disabled></el-input>
              </el-form-item>
              <el-form-item label="办公地点纬度" prop="latitude" style="display: none">
                <el-input size="mini" v-model="clbgParams.latitude" disabled></el-input>
              </el-form-item>

              <el-form-item label="GPS"  prop="unitGPS">
                <el-input size="mini" v-model="clbgParams.unitGPS" id="clbgd.gps" disabled></el-input>
                <el-button size="mini" @click="dtDialog = true">从地图中选择</el-button>
              </el-form-item>
            </el-col>
        </el-row>


        <el-dialog
            width="80%"
            :visible.sync="dtDialog"
            size="tiny"
            center
            :destroy-on-close="true"
            :append-to-body="true"
            :modal-append-to-body="false"
            @close="closeDTDialog"
            :close-on-click-modal="false"
        >
          <div style="width: 100%;height: 100%">
            <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                    style="width: 100%;height: 500px"></iframe>
            <el-button @click="closeDTDialog" id="clbgd.dtDialogClose" v-show="false"></el-button>
          </div>
        </el-dialog>


        <el-form-item>
            <div style="text-align: right">
                <el-button v-if="btnType !== '查看'" type="primary" @click="handleSubmit('bmdwdForm')">保存</el-button>
                <el-button @click="$emit('handleClose')">关闭</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script>
import {getYyglType, getYySend} from '@/api/yygl'
import {editPosition, GetGroupList} from "../../../../../api/kqgl/pzgl/pzgl";
export default {
    name:'FormPannel',
    props:{
        btnType:{
            type:String,
            default:'新增'
        }
    },
    computed:{
        watermark(){
            return this.$store.state.user.watermark
        }
    },
    data(){
        return {
            // 地图src
            src: process.env.NODE_ENV === 'development' ? '/static/kqgl/html/BaiDuToBGD.html' : 'static/kqgl/html/BaiDuToBGD.html',
            //选择列表数据
            clbgParams: {},
            dialogGroup: [],
            dtDialog: false,
            clockStartTimePicker:'',
            clockEndTimePicker:'',
            rules:{
              groupId: [
                {
                  required: true,
                  message: '请选择所属部门！',
                  trigger: 'blur'
                }
              ],
              companyAddress: [
                {
                  required: true,
                  message: '请输入办公地点',
                  trigger: 'blur'
                }
              ],
              clockStartTime: [
                {
                  required: true,
                  message: '请选择上午上班时间',
                  trigger: 'blur'
                }
              ],
              clockEndTime: [
                {
                  required: true,
                  message: '请选择下午上班时间',
                  trigger: 'blur'
                }
              ],
              unitGPS: [
                {
                  required: true,
                  message: '请输入办公地点GPS',
                  trigger: 'blur'
                },
                {
                  pattern: /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,14})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180),(\-|\+)?([0-8]?\d{1}\.\d{0,14}|90\.0{0,6}|[0-8]?\d{1}|90)$/, // 正则校验不用字符串
                  message: '请输入正确的纬度和经度，并且用逗号(英文)隔开(经度在前，纬度在后)',
                  trigger: 'blur'
                }
              ]
            },

        }
    },
    mounted(){
        //if(this.btnType !== '查看') this.getOptions()
        this.src = process.env.NODE_ENV === 'development' ? '/static/kqgl/html/BaiDuToBGD.html' : 'static/kqgl/html/BaiDuToBGD.html'
        if(this.btnType == "新增"){

        }
        GetGroupList({ isShowAll: 1,parentId: 1 }).then((res) => {
          let data = res.result
          data = data.slice(1, data.length)
          this.dialogGroup = data

        })

    },
    methods:{
        changeClockEndTimePicker(val){
          this.clbgParams.clockEndTime = val[0]+"|"+val[1]
        },
        changeClockStartTimePicker(val){
          this.clbgParams.clockStartTime = val[0]+"|"+val[1]
        },
        /** 所属部门选项 */
        deptChange (e) {
          console.log(e)
          this.dialogGroup.forEach(value => {
            if (value.id == e) {
              this.clbgParams.groupId = e
              this.clbgParams.deptName = value.groupname
            }
          })
        },

        closeDTDialog () {
          this.clbgParams.unitGPS = window.parent.document.getElementById('clbgd.gps').value
          this.clbgParams.longitude = this.clbgParams.unitGPS.split(",")[0]
          this.clbgParams.latitude = this.clbgParams.unitGPS.split(",")[1]
          //this.$nextTick(() => this.$refs.stopParams.clearValidate())

          this.dtDialog = false
        },
        /** 关闭弹窗 */
        closeDialog () {
          //this.getListBm()
          this.clbgDialog = false
        },


        //提交表单数据
        handleSubmit(formName){
            this.$refs[formName].validate((valid)=>{
              if (valid) {
                const gps = this.clbgParams.unitGPS.split(",")
                this.clbgParams.longitude = gps[0]
                this.clbgParams.latitude = gps[1]

                this.clbgParams.clockEndTime=this.clockEndTimePicker[0]+"|"+this.clockEndTimePicker[1]
                this.clbgParams.clockStartTime=this.clockStartTimePicker[0]+"|"+this.clockStartTimePicker[1]

                this.$emit('handleSubmit', this.clbgParams)

              } else {
                return false
              }
            })
        },
        //初始化
        initData(data){
          console.log(data)
            if(data.id){
                this.clbgParams = {...data}

                this.deptChange(this.clbgParams.groupId)

                this.clbgParams.unitGPS = this.clbgParams.longitude+","+this.clbgParams.latitude
                this.clockStartTimePicker = []
                this.clockStartTimePicker.push(this.clbgParams.clockStartTime.split("|")[0])
                this.clockStartTimePicker.push(this.clbgParams.clockStartTime.split("|")[1])

                this.clockEndTimePicker = []
                this.clockEndTimePicker.push(this.clbgParams.clockEndTime.split("|")[0])
                this.clockEndTimePicker.push(this.clbgParams.clockEndTime.split("|")[1])
            }
        },

    }
}
</script>

<style lang="scss" scoped>
</style>
