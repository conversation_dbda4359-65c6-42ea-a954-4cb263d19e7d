<template>
  <div style="width: 100%;height: 100%">
    <iframe :src="locus" id="qt" scrolling="no" frameborder="0"
            style="width: 100%;height: 1000px"></iframe>
  </div>
</template>

<script>

export default {
  name: 'locus',
  mounted () {
    console.log( this.$route.query)
    const a=  process.env.NODE_ENV === "production" ? "" : "/"
    this.locus = a + this.locus + '?userId=' + this.$route.query.userId
    console.log(this.locus)
    if (this.$route.query.date !== '' || this.$route.query.date !== undefined) {
      this.locus += '&date=' + this.$route.query.date+"&userName="+this.$route.query.userName
    }
    this.locus = encodeURI(this.locus)
  },
  destroyed () {
    this.locus = 'static/kqgl/html/InfluxDB/history.html'
  },
  data () {
    return {
      // 轨迹路径
      locus: 'static/kqgl/html/InfluxDB/history.html'
    }
  }
}
</script>

<style scoped>

</style>
