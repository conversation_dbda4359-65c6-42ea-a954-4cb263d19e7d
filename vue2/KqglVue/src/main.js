import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'
import './assets/styles/index.scss' // global css
import './assets/icons/iconfont.css'
// import './assets/icons/onlineIcon.css'
import './utils/kqgl/calendar_filter'
import 'default-passive-events'
import App from './App.vue'
import router from './router'
import store from './store'
import directive from './directive'
import './permission'

Vue.use(directive)

Vue.use(Element,{
  size: localStorage.getItem('layout-size') || 'mini' // set element-ui default size
})
Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
