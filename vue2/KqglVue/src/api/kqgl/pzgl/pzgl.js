import request from '@/utils/request'

/** 获取车队长下拉框 */
export function getListBm (params) {
  return request({
    url: '/dwKqgl/position/getListBm',
    method: 'post',
    data: params
  })
}



/** 获取单位下拉框 */
export function GetGroupList (params) {
  return request({
    url: '/dwKqgl/position/getGroupListAll',
    method: 'get',
    params: params
  })
}

/** 获取人员下拉框 */
export function getDeptUsers (params) {
  return request({
    url: '/dwKqgl/position/getDeptUsers',
    method: 'get',
    params: params
  })
}




/** 添加部门或者个人打卡点位 */
export function addPosition (params) {
  return request({
    url: '/dwKqgl/position/addPosition',
    method: 'post',
    data: params
  })
}

/**
 * 查看
 * @param {*} id
 * @returns
 */
export function queryPositionById(id){
  return request({
    url: `/dwKqgl/position/getPositionById?id=${id}`,
    method: 'get'
  })
}



/** 编辑 */
export function editPosition (params) {
  return request({
    url: '/dwKqgl/position/editPosition',
    method: 'post',
    data: params
  })
}

/** 根据id删除 */
export function delPositionById (params) {
  return request({
    url: '/dwKqgl/position/delPosition',
    method: 'get',
    params: params
  })
}

/**  节假日设置 */
/** 根据id删除 */
export function delWorkDayById (params) {
  return request({
    url: `/dwKqgl/workDay/delWorkDay/${params}`,
    method: 'get',
  })
}
// 新增节假日设置
export function addWorkDay (data) {
  return request({
    url: '/dwKqgl/workDay/add',
    method: 'post',
    data: data
  })
}

// 修改节假日设置
export function updateWorkDay (data) {
  return request({
    url: '/dwKqgl/workDay/',
    method: 'put',
    data: data
  })
}

// 查询节假日设置
export function getWorkDayDetails (id) {
  return request({
    url: '/dwKqgl/workDay/' + id,
    method: 'get'
  })
}
// 查询节假日设置列表
export function listWorkDay (query) {
  return request({
    url: '/dwKqgl/workDay/list',
    method: 'post',
    data: query
  })
}

// 获取今年最新节假日
export function runGetWorkDay () {
  return request({
    url: '/dwKqgl/workDay/runGetWorkDay',
    method: 'get'
  })
}




// 获取今年最新节假日
export function checkSfjy (data) {
  return request({
    url: '/dwKqgl/sfzCheck/check',
    method: 'post',
    data: data
  })
}

