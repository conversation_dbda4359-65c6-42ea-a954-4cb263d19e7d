<template>
    <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.current"
        :page-sizes="[5,10,20,30]"
        :page-size="queryParam.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
    </el-pagination>
</template>

<script>
export default {
    name:'index',
    props:{
        queryParam:{
            type: Object,
            default:()=>{
                return {
                    current: 1,
                    size: 10
                }
            }
        },
        total:{
            type:Number,
            default: 0
        },
    },
    methods:{
        handleSizeChange(val){
            this.queryParam.size = val
            this.$emit('handleRefresh',this.queryParam)
        },
        handleCurrentChange(val){
            this.queryParam.current = val
            this.$emit('handleRefresh',this.queryParam)
        },
    }
}
</script>

<style>

</style>