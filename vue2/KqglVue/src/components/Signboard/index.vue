<template>
  <div ref="board_container" id="board_container" style="height:100%" class="board_container">
    <div class="font-size18 text-weight">分包看板</div>
    <div :style="{ width: '100%', height: '100%' }" ref="boardChart" class="boardChart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getFBKBData } from '@/api/home'
export default {
  data() {
    return {
      myChart: undefined,
      FBKBData: []
    }
  },
  mounted() {
    this.getFBKBData();
    window.addEventListener('resize', () => {
      this.resize()
    })
  },
  methods: {
    getFBKBData() {
      getFBKBData().then((res) => {
        // console.log(res);
        this.FBKBData = res.Table.map(item => {
          return {
            name: item.Group_Name,
            FBHTAvgDay: item.Fbht_AvgDay,
            FBJSAvgDay: item.Fbjs_AvgDay
          }
        })
        const chartDom = this.$refs.boardChart;
        const myChart = echarts.init(chartDom);
        this.myChart = myChart;
        this.initChart();
      })
    },
    initChart() {
      if (this.FBKBData.length == 0) {
        alert("分包看板无数据");
      } else {
        const name = [];
        const fbht = [];
        const fbjs = [];
        this.FBKBData.forEach((item) => {
          name.push(item.name);
          fbht.push(item.FBHTAvgDay);
          fbjs.push(item.FBJSAvgDay);
        })
        const option = {
          title: [
            {
              text: '单位：天',
              left: '80%',
              textStyle: {
                fontSize: 12,
                fontWeight: 'normal'
              }
            }
          ],
          // 提示框组件
          tooltip: {
            // 坐标轴触发
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {},
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          //
          xAxis: {
            type: 'category',
            axisLabel: {
              interval: 0,
              fontSize: 10
            },
            data: name
          },
          yAxis: {
            type: 'value',
            boundaryGap: [0, 0.01]
          },
          series: [
            {
              name: '分包合同审核时长',
              type: 'bar',
              data: fbht
            },
            {
              name: '分包结算时长',
              type: 'bar',
              itemStyle: {
                color: 'orange'
              },
              data: fbjs
            }
          ]
        };
        this.myChart.setOption(option);
      }

    },
    getEchartData() {
      // this.$emit('getchart', myChart);
      // this.$on('hook:destroyed', ()=>{
      //   divDom.removeEventListener("resize", function(){
      //     myChart.resize();
      //   })
      // })
    },
    resize() {
      if(this.myChart){
        this.myChart.resize()
      }
      // console.log(this.$refs.boardChart.clientHeight);
      // this.getEchartData();
    }
  }

}
</script>

<style lang="scss">
.boardChart {
  position: absolute;
  top: 0;
}

.board_container {
  position: relative;
}
</style>