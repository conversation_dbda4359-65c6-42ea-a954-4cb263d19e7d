<template>
  <el-table
      ref="tableRef"
      :data="tableData"
      stripe
      border
      highlight-current-row
      @current-change="handleCurrentChange"
      :cell-style="cellStyle"
      style="width: 100%"
      height="100%"
      v-loading="loading"
      v-watermark="{label:watermark}">
    <el-table-column v-if="needIndex" type="index" label="序号" width="50" align="center">
      <template slot-scope="scope">
        {{ (queryParam.pageNum - 1) * queryParam.pageSize + 1 + scope.$index }}
      </template>
    </el-table-column>
    <template v-for="(item, index) in tableOptions">
      <el-table-column
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :align="item.align || 'center'"
          :width="item.width || ''"
          :show-overflow-tooltip="item.tooltip || false"
          :sortable="item.sortable === false ? false : true">
        <template slot-scope="scope">
          <slot
              v-if="item.slot"
              :name="scope.column.property"
              :row="scope.row"
              :rowIndex="scope.$index">
          </slot>
          <span v-else>{{ scope.row[scope.column.property] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>
<script>
export default {
  name: 'index',
  props: {
    needIndex: {//序号显示
      type: Boolean,
      default: true
    },
    tableData: {
      type: Array,
      default: () => []
    },
    tableOptions: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    queryParam: {
      type: Object,
      default: () => {
        return {
          pageNum: 1,
          pageSize: 10
        }
      }
    },
    cellStyleVal: 0,  //样式设置
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  methods: {
    //高亮
    setCurrent(row) {
      this.$refs.tableRef.setCurrentRow(row)
    },
    //选中数据
    handleCurrentChange(val) {
      this.$emit('getCurrentData', val)
    },

    /** 设置指定行、列、具体单元格颜色 */
    cellStyle({row, column, rowIndex, columnIndex}) {
      //每日考勤情况样式设置
      if (this.cellStyleVal == 1) {
        if (row.type.includes('0.9')) {
          return ''
        } else if (row.type == (',')) {
          return ''
        } else {
          return 'color: red'
        }
      }
      //点位匹配设置
      else if(this.cellStyleVal ==2){
        //上班时间!=时序库上班时间
        if (columnIndex == 4 && row.clockStartTime != row.influxStartTime) {
          return 'color: red'
        }
        //下班时间!=时序库下班时间
        if(row.clockEndTime != null && row.clockEndTime !=undefined){
          if (columnIndex ==5 && row.clockEndTime != row.influxEndTime) {
            return 'color: red'
          }
        }
        return ''
      } else {
        return ''
      }

    },


  }
}
</script>
<style lang="scss" scoped>

</style>
