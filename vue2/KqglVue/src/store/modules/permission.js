import router from '@/router'
import { getRouters,getChildrenRouters } from '@/api/menu'

const dynamicRoutes = []

const permission = {
    state:{
        routes:dynamicRoutes,
        addRoutes:[]
    },
    mutations:{
        SET_ROUTERS: (state, routers) => {
            state.addRoutes = routers
            state.routes = dynamicRoutes.concat(routers)
        }
    },
    actions:{
        //生成路由
        GenerateRoutes({commit}){
            return new Promise((resolve,reject)=>{
                return new Promise((resolve,reject)=>{
                    getRouters().then(res=>{
                        resolve(res.mglist)
                    })
                }).then(routeList=>{
                    return Promise.all(routeList.map(route=>{
                        return new Promise(async (resolve,reject)=>{
                            let routeItem = {
                                path:'',
                                name: route.GP_NAME,
                                meta: { title: route.GP_NAME, icon: ''},
                            }
                            await getChildrenRouters(route.GP_ID).then(childRes=>{
                                if(childRes.navlist.length){
                                    routeItem.children = []
                                    childRes.navlist.map(childRoute=>{
                                        const temp = {
                                            path: childRoute.purl || childRoute.url,
                                            module_mix: childRoute.module_mix,
                                            name: childRoute.module_name,
                                            meta:{title:childRoute.module_name},
                                            hasPermi: childRoute.module_right == 1 ? true : false
                                        }
                                        router.addRoute(route.GP_NAME,temp)
                                        routeItem.children.push(temp)
                                    })
                                }
                            })
                            resolve(routeItem)
                        })
                    }))
                }).then(routers=>{
                    resolve(routers)
                    commit('SET_ROUTERS', routers)
                })
            })
        }
    }
}

export default permission