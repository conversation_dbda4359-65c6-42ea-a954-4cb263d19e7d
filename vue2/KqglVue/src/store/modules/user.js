import { login, logout, getInfo, changePostLogin } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import Cookies from 'js-cookie'
import {_sysmark,closePages} from '@/utils/jiaohu'
import dayjs from 'dayjs'

const user = {
  state: {
    token: getToken(),
    name: '',
    loginName: '',
    avatar: '',
    watermark:'',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_LOGINNAME: (state, loginName) => {
      state.loginName = loginName
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_WATERMARK: (state, watermark)=>{
      state.watermark = watermark
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const code = userInfo.code
      const Skey = userInfo.Skey;
      return new Promise((resolve, reject) => {
        login(username, code, Skey).then(res => {
          if (res.success == true) {
            setToken(res.token)
            Cookies.set('jtoken',res.jtoken || '')
            commit('SET_TOKEN', res.token)
            sessionStorage.setItem(_sysmark+"_xsystem","true");
            sessionStorage.setItem(_sysmark+"_token",res.token);
            resolve()
          } else {
            reject()
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 切换用户登录
    changeLogin({commit}, loginName){
      return new Promise((resolve, reject)=>{
        changePostLogin(loginName,true).then(res=>{
          if(res.code == 200){
            setToken(res.token)
            commit('SET_TOKEN', res.token)
            sessionStorage.setItem(_sysmark+"_xsystem","true");
            sessionStorage.setItem(_sysmark+"_token",res.token);
            resolve()
          } else {
            reject()
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          Cookies.set('access_token',res.utoken || '')
          const user = res.data;
          const avatar = (user.HeadIcon == "" || user.HeadIcon == null) ? require("@/assets/images/profile.png") : user.HeadIcon;
          if (user.RoleList && user.RoleList.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', user.RoleList)
            commit('SET_PERMISSIONS', user.PermissionList)

          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.RealName)
          commit('SET_LOGINNAME', user.LoginName)
          commit('SET_AVATAR', avatar)
          //添加水印信息
          commit('SET_WATERMARK', user.RealName + ',' + user.GroupName + ',' + dayjs().format('YYYY-MM-DD'))
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          Cookies.remove('jtoken')
          Cookies.remove('access_token')
          removeToken()
          closePages()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
