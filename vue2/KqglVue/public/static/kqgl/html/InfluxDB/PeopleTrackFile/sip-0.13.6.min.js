!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SIP=t():e.SIP=t()}(this,function(){return function(e){var t={};function r(i){if(t[i])return t[i].exports;var s=t[i]={i:i,l:!1,exports:{}};return e[i].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(i,s,function(t){return e[t]}.bind(null,s));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=30)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.STATUS_EARLY=1]="STATUS_EARLY",e[e.STATUS_CONFIRMED=2]="STATUS_CONFIRMED"}(t.DialogStatus||(t.DialogStatus={})),function(e){e[e.STATUS_NULL=0]="STATUS_NULL",e[e.STATUS_INVITE_SENT=1]="STATUS_INVITE_SENT",e[e.STATUS_1XX_RECEIVED=2]="STATUS_1XX_RECEIVED",e[e.STATUS_INVITE_RECEIVED=3]="STATUS_INVITE_RECEIVED",e[e.STATUS_WAITING_FOR_ANSWER=4]="STATUS_WAITING_FOR_ANSWER",e[e.STATUS_ANSWERED=5]="STATUS_ANSWERED",e[e.STATUS_WAITING_FOR_PRACK=6]="STATUS_WAITING_FOR_PRACK",e[e.STATUS_WAITING_FOR_ACK=7]="STATUS_WAITING_FOR_ACK",e[e.STATUS_CANCELED=8]="STATUS_CANCELED",e[e.STATUS_TERMINATED=9]="STATUS_TERMINATED",e[e.STATUS_ANSWERED_WAITING_FOR_PRACK=10]="STATUS_ANSWERED_WAITING_FOR_PRACK",e[e.STATUS_EARLY_MEDIA=11]="STATUS_EARLY_MEDIA",e[e.STATUS_CONFIRMED=12]="STATUS_CONFIRMED"}(t.SessionStatus||(t.SessionStatus={})),function(e){e[e.STATUS_TRYING=1]="STATUS_TRYING",e[e.STATUS_PROCEEDING=2]="STATUS_PROCEEDING",e[e.STATUS_CALLING=3]="STATUS_CALLING",e[e.STATUS_ACCEPTED=4]="STATUS_ACCEPTED",e[e.STATUS_COMPLETED=5]="STATUS_COMPLETED",e[e.STATUS_TERMINATED=6]="STATUS_TERMINATED",e[e.STATUS_CONFIRMED=7]="STATUS_CONFIRMED"}(t.TransactionStatus||(t.TransactionStatus={})),function(e){e[e.AckClientTransaction=0]="AckClientTransaction",e[e.ClientContext=1]="ClientContext",e[e.ConfigurationError=2]="ConfigurationError",e[e.Dialog=3]="Dialog",e[e.DigestAuthentication=4]="DigestAuthentication",e[e.DTMF=5]="DTMF",e[e.IncomingMessage=6]="IncomingMessage",e[e.IncomingRequest=7]="IncomingRequest",e[e.IncomingResponse=8]="IncomingResponse",e[e.InvalidStateError=9]="InvalidStateError",e[e.InviteClientContext=10]="InviteClientContext",e[e.InviteClientTransaction=11]="InviteClientTransaction",e[e.InviteServerContext=12]="InviteServerContext",e[e.InviteServerTransaction=13]="InviteServerTransaction",e[e.Logger=14]="Logger",e[e.LoggerFactory=15]="LoggerFactory",e[e.MethodParameterError=16]="MethodParameterError",e[e.NameAddrHeader=17]="NameAddrHeader",e[e.NonInviteClientTransaction=18]="NonInviteClientTransaction",e[e.NonInviteServerTransaction=19]="NonInviteServerTransaction",e[e.NotSupportedError=20]="NotSupportedError",e[e.OutgoingRequest=21]="OutgoingRequest",e[e.Parameters=22]="Parameters",e[e.PublishContext=23]="PublishContext",e[e.ReferClientContext=24]="ReferClientContext",e[e.ReferServerContext=25]="ReferServerContext",e[e.RegisterContext=26]="RegisterContext",e[e.RenegotiationError=27]="RenegotiationError",e[e.RequestSender=28]="RequestSender",e[e.ServerContext=29]="ServerContext",e[e.Session=30]="Session",e[e.SessionDescriptionHandler=31]="SessionDescriptionHandler",e[e.SessionDescriptionHandlerError=32]="SessionDescriptionHandlerError",e[e.SessionDescriptionHandlerObserver=33]="SessionDescriptionHandlerObserver",e[e.Subscription=34]="Subscription",e[e.Transport=35]="Transport",e[e.TransportError=36]="TransportError",e[e.UA=37]="UA",e[e.URI=38]="URI"}(t.TypeStrings||(t.TypeStrings={})),function(e){e[e.STATUS_INIT=0]="STATUS_INIT",e[e.STATUS_STARTING=1]="STATUS_STARTING",e[e.STATUS_READY=2]="STATUS_READY",e[e.STATUS_USER_CLOSED=3]="STATUS_USER_CLOSED",e[e.STATUS_NOT_READY=4]="STATUS_NOT_READY"}(t.UAStatus||(t.UAStatus={}))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(16);!function(e){e.USER_AGENT=i.title+"/"+i.version,e.SIP="sip",e.SIPS="sips",function(e){e.CONNECTION_ERROR="Connection Error",e.INTERNAL_ERROR="Internal Error",e.REQUEST_TIMEOUT="Request Timeout",e.SIP_FAILURE_CODE="SIP Failure Code",e.ADDRESS_INCOMPLETE="Address Incomplete",e.AUTHENTICATION_ERROR="Authentication Error",e.BUSY="Busy",e.DIALOG_ERROR="Dialog Error",e.INCOMPATIBLE_SDP="Incompatible SDP",e.NOT_FOUND="Not Found",e.REDIRECTED="Redirected",e.REJECTED="Rejected",e.UNAVAILABLE="Unavailable",e.BAD_MEDIA_DESCRIPTION="Bad Media Description",e.CANCELED="Canceled",e.EXPIRES="Expires",e.NO_ACK="No ACK",e.NO_ANSWER="No Answer",e.NO_PRACK="No PRACK",e.RTP_TIMEOUT="RTP Timeout",e.USER_DENIED_MEDIA_ACCESS="User Denied Media Access",e.WEBRTC_ERROR="WebRTC Error",e.WEBRTC_NOT_SUPPORTED="WebRTC Not Supported"}(e.causes||(e.causes={})),function(e){e.REQUIRED="required",e.SUPPORTED="supported",e.UNSUPPORTED="none"}(e.supported||(e.supported={})),e.SIP_ERROR_CAUSES={ADDRESS_INCOMPLETE:[484],AUTHENTICATION_ERROR:[401,407],BUSY:[486,600],INCOMPATIBLE_SDP:[488,606],NOT_FOUND:[404,604],REDIRECTED:[300,301,302,305,380],REJECTED:[403,603],UNAVAILABLE:[480,410,408,430]},e.ACK="ACK",e.BYE="BYE",e.CANCEL="CANCEL",e.INFO="INFO",e.INVITE="INVITE",e.MESSAGE="MESSAGE",e.NOTIFY="NOTIFY",e.OPTIONS="OPTIONS",e.REGISTER="REGISTER",e.UPDATE="UPDATE",e.SUBSCRIBE="SUBSCRIBE",e.PUBLISH="PUBLISH",e.REFER="REFER",e.PRACK="PRACK",e.REASON_PHRASE={100:"Trying",180:"Ringing",181:"Call Is Being Forwarded",182:"Queued",183:"Session Progress",199:"Early Dialog Terminated",200:"OK",202:"Accepted",204:"No Notification",300:"Multiple Choices",301:"Moved Permanently",302:"Moved Temporarily",305:"Use Proxy",380:"Alternative Service",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",410:"Gone",412:"Conditional Request Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Unsupported URI Scheme",417:"Unknown Resource-Priority",420:"Bad Extension",421:"Extension Required",422:"Session Interval Too Small",423:"Interval Too Brief",428:"Use Identity Header",429:"Provide Referrer Identity",430:"Flow Failed",433:"Anonymity Disallowed",436:"Bad Identity-Info",437:"Unsupported Certificate",438:"Invalid Identity Header",439:"First Hop Lacks Outbound Support",440:"Max-Breadth Exceeded",469:"Bad Info Package",470:"Consent Needed",478:"Unresolvable Destination",480:"Temporarily Unavailable",481:"Call/Transaction Does Not Exist",482:"Loop Detected",483:"Too Many Hops",484:"Address Incomplete",485:"Ambiguous",486:"Busy Here",487:"Request Terminated",488:"Not Acceptable Here",489:"Bad Event",491:"Request Pending",493:"Undecipherable",494:"Security Agreement Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Server Time-out",505:"Version Not Supported",513:"Message Too Large",580:"Precondition Failure",600:"Busy Everywhere",603:"Decline",604:"Does Not Exist Anywhere",606:"Not Acceptable"},e.OPTION_TAGS={"100rel":!0,199:!0,answermode:!0,"early-session":!0,eventlist:!0,explicitsub:!0,"from-change":!0,"geolocation-http":!0,"geolocation-sip":!0,gin:!0,gruu:!0,histinfo:!0,ice:!0,join:!0,"multiple-refer":!0,norefersub:!0,nosub:!0,outbound:!0,path:!0,policy:!0,precondition:!0,pref:!0,privacy:!0,"recipient-list-invite":!0,"recipient-list-message":!0,"recipient-list-subscribe":!0,replaces:!0,"resource-priority":!0,"sdp-anat":!0,"sec-agree":!0,tdialog:!0,timer:!0,uui:!0},function(e){e.INFO="info",e.RTP="rtp"}(e.dtmfType||(e.dtmfType={}))}(t.C||(t.C={}))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(1),s=r(0),n=r(4);!function(e){e.defer=function(){var e={};return e.promise=new Promise(function(t,r){e.resolve=t,e.reject=r}),e},e.reducePromises=function(e,t){return e.reduce(function(e,t){return e=e.then(t)},Promise.resolve(t))},e.str_utf8_length=function(e){return encodeURIComponent(e).replace(/%[A-F\d]{2}/g,"U").length},e.generateFakeSDP=function(e){if(e){var t=e.indexOf("o="),r=e.indexOf("\r\n",t);return"v=0\r\n"+e.slice(t,r)+"\r\ns=-\r\nt=0 0\r\nc=IN IP4 0.0.0.0"}},e.isDecimal=function(e){var t=parseInt(e,10);return!isNaN(t)&&parseFloat(e)===t},e.createRandomToken=function(e,t){void 0===t&&(t=32);for(var r="",i=0;i<e;i++)r+=Math.floor(Math.random()*t).toString(t);return r},e.newTag=function(){return e.createRandomToken(10)},e.newUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=Math.floor(16*Math.random());return("x"===e?t:t%4+8).toString(16)})},e.normalizeTarget=function(t,r){if(t){if(t.type===s.TypeStrings.URI)return t;if("string"==typeof t){var o=t.split("@"),a=void 0,c=void 0;switch(o.length){case 1:if(!r)return;a=t,c=r;break;case 2:a=o[0],c=o[1];break;default:a=o.slice(0,o.length-1).join("@"),c=o[o.length-1]}return a=a.replace(/^(sips?|tel):/i,""),/^[\-\.\(\)]*\+?[0-9\-\.\(\)]+$/.test(a)&&(a=a.replace(/[\-\.\(\)]/g,"")),t=i.C.SIP+":"+e.escapeUser(a)+"@"+c,n.Grammar.URIParse(t)}}},e.escapeUser=function(e){return encodeURIComponent(decodeURIComponent(e)).replace(/%3A/gi,":").replace(/%2B/gi,"+").replace(/%3F/gi,"?").replace(/%2F/gi,"/")},e.headerize=function(e){for(var t={"Call-Id":"Call-ID",Cseq:"CSeq","Min-Se":"Min-SE",Rack:"RAck",Rseq:"RSeq","Www-Authenticate":"WWW-Authenticate"},r=e.toLowerCase().replace(/_/g,"-").split("-"),i=r.length,s="",n=0;n<i;n++)0!==n&&(s+="-"),s+=r[n].charAt(0).toUpperCase()+r[n].substring(1);return t[s]&&(s=t[s]),s},e.sipErrorCause=function(e){for(var t in i.C.SIP_ERROR_CAUSES)if(-1!==i.C.SIP_ERROR_CAUSES[t].indexOf(e))return i.C.causes[t];return i.C.causes.SIP_FAILURE_CODE},e.getReasonPhrase=function(e,t){return t||i.C.REASON_PHRASE[e]||""},e.getReasonHeaderValue=function(t,r){return"SIP;cause="+t+';text="'+(r=e.getReasonPhrase(t,r))+'"'},e.getCancelReason=function(t,r){if(t&&t<200||t>699)throw new TypeError("Invalid statusCode: "+t);if(t)return e.getReasonHeaderValue(t,r)},e.buildStatusLine=function(t,r){if(!t||t<100||t>699)throw new TypeError("Invalid statusCode: "+t);if(r&&"string"!=typeof r&&!(r instanceof String))throw new TypeError("Invalid reason: "+r);return"SIP/2.0 "+t+" "+(r=e.getReasonPhrase(t,r))+"\r\n"}}(t.Utils||(t.Utils={}))},function(e,t,r){"use strict";var i,s="object"==typeof Reflect?Reflect:null,n=s&&"function"==typeof s.apply?s.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};i=s&&"function"==typeof s.ownKeys?s.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!=e};function a(){a.init.call(this)}e.exports=a,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var c=10;function u(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function h(e,t,r,i){var s,n,o,a;if("function"!=typeof r)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof r);if(void 0===(n=e._events)?(n=e._events=Object.create(null),e._eventsCount=0):(void 0!==n.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),n=e._events),o=n[t]),void 0===o)o=n[t]=r,++e._eventsCount;else if("function"==typeof o?o=n[t]=i?[r,o]:[o,r]:i?o.unshift(r):o.push(r),(s=u(e))>0&&o.length>s&&!o.warned){o.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=o.length,a=c,console&&console.warn&&console.warn(a)}return e}function d(e,t,r){var i={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},s=function(){for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);this.fired||(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,n(this.listener,this.target,e))}.bind(i);return s.listener=r,i.wrapFn=s,s}function p(e,t,r){var i=e._events;if(void 0===i)return[];var s=i[t];return void 0===s?[]:"function"==typeof s?r?[s.listener||s]:[s]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(s):f(s,s.length)}function l(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function f(e,t){for(var r=new Array(t),i=0;i<t;++i)r[i]=e[i];return r}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");c=e}}),a.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return u(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var i="error"===e,s=this._events;if(void 0!==s)i=i&&void 0===s.error;else if(!i)return!1;if(i){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var a=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var c=s[e];if(void 0===c)return!1;if("function"==typeof c)n(c,this,t);else{var u=c.length,h=f(c,u);for(r=0;r<u;++r)n(h[r],this,t)}return!0},a.prototype.addListener=function(e,t){return h(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return h(this,e,t,!0)},a.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.on(e,d(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.prependListener(e,d(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,i,s,n,o;if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);if(void 0===(i=this._events))return this;if(void 0===(r=i[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(s=-1,n=r.length-1;n>=0;n--)if(r[n]===t||r[n].listener===t){o=r[n].listener,s=n;break}if(s<0)return this;0===s?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,s),1===r.length&&(i[e]=r[0]),void 0!==i.removeListener&&this.emit("removeListener",e,o||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,i;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var s,n=Object.keys(r);for(i=0;i<n.length;++i)"removeListener"!==(s=n[i])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;i>=0;i--)this.removeListener(e,t[i]);return this},a.prototype.listeners=function(e){return p(this,e,!0)},a.prototype.rawListeners=function(e){return p(this,e,!1)},a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):l.call(e,t)},a.prototype.listenerCount=l,a.prototype.eventNames=function(){return this._eventsCount>0?i(this._events):[]}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(31);!function(e){e.parse=function(e,t){var r={startRule:t};try{i.parse(e,r)}catch(e){r.data=-1}return r.data},e.nameAddrHeaderParse=function(t){var r=e.parse(t,"Name_Addr_Header");return-1!==r?r:void 0},e.URIParse=function(t){var r=e.parse(t,"SIP_URI");return-1!==r?r:void 0}}(t.Grammar||(t.Grammar={}))},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),o=function(e){function t(t,r,i){var s=e.call(this,i)||this;return s.code=t,s.name=r,s.message=i,s}return s(t,e),t}(Error);!function(e){var t=function(e){function t(t,r){var i=e.call(this,1,"CONFIGURATION_ERROR",r?"Invalid value "+JSON.stringify(r)+" for parameter '"+t+"'":"Missing parameter: "+t)||this;return i.type=n.TypeStrings.ConfigurationError,i.parameter=t,i.value=r,i}return s(t,e),t}(o);e.ConfigurationError=t;var r=function(e){function t(t){var r=e.call(this,2,"INVALID_STATE_ERROR","Invalid status: "+t)||this;return r.type=n.TypeStrings.InvalidStateError,r.status=t,r}return s(t,e),t}(o);e.InvalidStateError=r;var i=function(e){function t(t){var r=e.call(this,3,"NOT_SUPPORTED_ERROR",t)||this;return r.type=n.TypeStrings.NotSupportedError,r}return s(t,e),t}(o);e.NotSupportedError=i;var a=function(e){function t(t){var r=e.call(this,5,"RENEGOTIATION_ERROR",t)||this;return r.type=n.TypeStrings.RenegotiationError,r}return s(t,e),t}(o);e.RenegotiationError=a;var c=function(e){function t(t,r,i){var s=e.call(this,6,"METHOD_PARAMETER_ERROR",i?"Invalid value "+JSON.stringify(i)+" for parameter '"+r+"'":"Missing parameter: "+r)||this;return s.type=n.TypeStrings.MethodParameterError,s.method=t,s.parameter=r,s.value=i,s}return s(t,e),t}(o);e.MethodParameterError=c;var u=function(e){function t(t){var r=e.call(this,7,"TRANSPORT_ERROR",t)||this;return r.type=n.TypeStrings.TransportError,r}return s(t,e),t}(o);e.TransportError=u;var h=function(e){function t(t,r,i){var s=e.call(this,8,"SESSION_DESCRIPTION_HANDLER_ERROR",i||"Error with Session Description Handler")||this;return s.type=n.TypeStrings.SessionDescriptionHandlerError,s.method=t,s.error=r,s}return s(t,e),t}(o);e.SessionDescriptionHandlerError=h}(t.Exceptions||(t.Exceptions={}))},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(1),o=r(0),a=r(4),c=r(2),u=function(e){var t=[];e.method===n.C.REGISTER?t.push("path","gruu"):e.method===n.C.INVITE&&(e.ua.contact.pubGruu||e.ua.contact.tempGruu)&&t.push("gruu"),e.ua.configuration.rel100===n.C.supported.SUPPORTED&&t.push("100rel"),e.ua.configuration.replaces===n.C.supported.SUPPORTED&&t.push("replaces"),t.push("outbound"),t=t.concat(e.ua.configuration.extraSupported||[]);var r=e.ua.configuration.hackAllowUnregisteredOptionTags||!1,i={};return"Supported: "+(t=t.filter(function(e){var t=n.C.OPTION_TAGS[e],s=!i[e];return i[e]=!0,(t||r)&&s})).join(", ")+"\r\n"},h=function(){function e(e,t,r,i,s,n){void 0===i&&(i={}),this.type=o.TypeStrings.OutgoingRequest,this.logger=r.getLogger("sip.sipmessage"),this.ua=r,this.headers={},this.method=e,this.ruri=t,this.body=n,this.extraHeaders=(s||[]).slice(),this.statusCode=i.statusCode,this.reasonPhrase=i.reasonPhrase,i.routeSet?this.setHeader("route",i.routeSet):r.configuration.usePreloadedRoute&&r.transport&&this.setHeader("route",r.transport.server.sipUri),this.setHeader("via",""),this.setHeader("max-forwards","70");var u=i.toUri||t,h=i.toDisplayName||0===i.toDisplayName?'"'+i.toDisplayName+'" ':"";h+="<"+(u.type===o.TypeStrings.URI?u.toRaw():u)+">",h+=i.toTag?";tag="+i.toTag:"",this.to=a.Grammar.nameAddrHeaderParse(h),this.setHeader("to",h);var d,p=i.fromUri||r.configuration.uri||"";d=i.fromDisplayName||0===i.fromDisplayName?'"'+i.fromDisplayName+'" ':r.configuration.displayName?'"'+r.configuration.displayName+'" ':"",d+="<"+(p.type===o.TypeStrings.URI?p.toRaw():p)+">;tag=",d+=i.fromTag||c.Utils.newTag(),this.from=a.Grammar.nameAddrHeaderParse(d),this.setHeader("from",d),this.callId=i.callId||r.configuration.sipjsId+c.Utils.createRandomToken(15),this.setHeader("call-id",this.callId),this.cseq=i.cseq||Math.floor(1e4*Math.random()),this.setHeader("cseq",this.cseq+" "+e)}return e.prototype.setHeader=function(e,t){this.headers[c.Utils.headerize(e)]=t instanceof Array?t:[t]},e.prototype.getHeader=function(e){var t=this.headers[c.Utils.headerize(e)];if(t){if(t[0])return t[0]}else for(var r=new RegExp("^\\s*"+e+"\\s*:","i"),i=0,s=this.extraHeaders;i<s.length;i++){var n=s[i];if(r.test(n))return n.substring(n.indexOf(":")+1).trim()}},e.prototype.cancel=function(e,t){},e.prototype.getHeaders=function(e){var t=[],r=this.headers[c.Utils.headerize(e)];if(r)for(var i=0,s=r;i<s.length;i++){var n=s[i];t.push(n)}else for(var o=new RegExp("^\\s*"+e+"\\s*:","i"),a=0,u=this.extraHeaders;a<u.length;a++){var h=u[a];o.test(h)&&t.push(h.substring(h.indexOf(":")+1).trim())}return t},e.prototype.hasHeader=function(e){if(this.headers[c.Utils.headerize(e)])return!0;for(var t=new RegExp("^\\s*"+e+"\\s*:","i"),r=0,i=this.extraHeaders;r<i.length;r++){var s=i[r];if(t.test(s))return!0}return!1},e.prototype.toString=function(){var e="";for(var t in e+=this.method+" "+(this.ruri.type===o.TypeStrings.URI?this.ruri.toRaw():this.ruri)+" SIP/2.0\r\n",this.headers)if(this.headers[t])for(var r=0,i=this.headers[t];r<i.length;r++){e+=t+": "+i[r]+"\r\n"}for(var s=0,n=this.extraHeaders;s<n.length;s++){e+=(t=n[s]).trim()+"\r\n"}return e+=u(this),e+="User-Agent: "+this.ua.configuration.userAgentString+"\r\n",this.body?"string"==typeof this.body?(e+="Content-Length: "+c.Utils.str_utf8_length(this.body)+"\r\n\r\n",e+=this.body):this.body.body&&this.body.contentType?(e+="Content-Type: "+this.body.contentType+"\r\n",e+="Content-Length: "+c.Utils.str_utf8_length(this.body.body)+"\r\n\r\n",e+=this.body.body):e+="Content-Length: 0\r\n\r\n":e+="Content-Length: 0\r\n\r\n",e},e}();t.OutgoingRequest=h;var d=function(){function e(){this.type=o.TypeStrings.IncomingMessage,this.headers={}}return e.prototype.addHeader=function(e,t){var r={raw:t};e=c.Utils.headerize(e),this.headers[e]?this.headers[e].push(r):this.headers[e]=[r]},e.prototype.getHeader=function(e){var t=this.headers[c.Utils.headerize(e)];if(t)return t[0]?t[0].raw:void 0},e.prototype.getHeaders=function(e){var t=this.headers[c.Utils.headerize(e)],r=[];if(!t)return[];for(var i=0,s=t;i<s.length;i++){var n=s[i];r.push(n.raw)}return r},e.prototype.hasHeader=function(e){return!!this.headers[c.Utils.headerize(e)]},e.prototype.parseHeader=function(e,t){if(void 0===t&&(t=0),e=c.Utils.headerize(e),this.headers[e]&&!(t>=this.headers[e].length)){var r=this.headers[e][t],i=r.raw;if(r.parsed)return r.parsed;var s=a.Grammar.parse(i,e.replace(/-/g,"_"));return-1===s?void this.headers[e].splice(t,1):(r.parsed=s,s)}},e.prototype.s=function(e,t){return void 0===t&&(t=0),this.parseHeader(e,t)},e.prototype.setHeader=function(e,t){this.headers[c.Utils.headerize(e)]=[{raw:t}]},e.prototype.toString=function(){return this.data},e}(),p=function(e){function t(t){var r=e.call(this)||this;return r.type=o.TypeStrings.IncomingRequest,r.logger=t.getLogger("sip.sipmessage"),r.ua=t,r}return s(t,e),t.prototype.reply=function(e,t,r,i,s,o){var a=c.Utils.buildStatusLine(e,t);if(r=(r||[]).slice(),this.method===n.C.INVITE&&e>100&&e<=200)for(var h=0,d=this.getHeaders("record-route");h<d.length;h++){a+="Record-Route: "+d[h]+"\r\n"}for(var p=0,l=this.getHeaders("via");p<l.length;p++){a+="Via: "+l[p]+"\r\n"}var f=this.getHeader("to")||"";!this.toTag&&e>100?f+=";tag="+c.Utils.newTag():this.toTag&&!this.s("to").hasParam("tag")&&(f+=";tag="+this.toTag),a+="To: "+f+"\r\n",a+="From: "+this.getHeader("From")+"\r\n",a+="Call-ID: "+this.callId+"\r\n",a+="CSeq: "+this.cseq+" "+this.method+"\r\n";for(var g=0,T=r;g<T.length;g++){a+=T[g].trim()+"\r\n"}return a+=u(this),a+="User-Agent: "+this.ua.configuration.userAgentString+"\r\n",i?"string"==typeof i?(a+="Content-Type: application/sdp\r\n",a+="Content-Length: "+c.Utils.str_utf8_length(i)+"\r\n\r\n",a+=i):i.body&&i.contentType?(a+="Content-Type: "+i.contentType+"\r\n",a+="Content-Length: "+c.Utils.str_utf8_length(i.body)+"\r\n\r\n",a+=i.body):a+="Content-Length: 0\r\n\r\n":a+="Content-Length: 0\r\n\r\n",this.serverTransaction&&this.serverTransaction.receiveResponse(e,a).then(s,o),a},t.prototype.reply_sl=function(e,t){for(var r=c.Utils.buildStatusLine(e,t),i=0,s=this.getHeaders("via");i<s.length;i++){r+="Via: "+s[i]+"\r\n"}var n=this.getHeader("To")||"";!this.toTag&&e>100?n+=";tag="+c.Utils.newTag():this.toTag&&!this.s("to").hasParam("tag")&&(n+=";tag="+this.toTag),r+="To: "+n+"\r\n",r+="From: "+this.getHeader("From")+"\r\n",r+="Call-ID: "+this.callId+"\r\n",r+="CSeq: "+this.cseq+" "+this.method+"\r\n",r+="User-Agent: "+this.ua.configuration.userAgentString+"\r\n",r+="Content-Length: 0\r\n\r\n",this.transport&&this.transport.send(r)},t}(d);t.IncomingRequest=p;var l=function(e){function t(t){var r=e.call(this)||this;return r.type=o.TypeStrings.IncomingResponse,r.logger=t.getLogger("sip.sipmessage"),r.headers={},r}return s(t,e),t}(d);t.IncomingResponse=l},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(1),a=r(0),c=r(8),u=r(6),h=r(2),d=function(e){function t(r,i,s,n){var o=e.call(this)||this;return o.data={},t.initializer(o,r,i,s,n),o}return s(t,e),t.initializer=function(e,t,r,i,s){if(e.type=a.TypeStrings.ClientContext,void 0===i)throw new TypeError("Not enough arguments");e.ua=t,e.logger=t.getLogger("sip.clientcontext"),e.method=r;var n=t.normalizeTarget(i);if(!n)throw new TypeError("Invalid target: "+i);(s=Object.create(s||Object.prototype)).extraHeaders=(s.extraHeaders||[]).slice(),e.request=new u.OutgoingRequest(e.method,n,e.ua,s.params,s.extraHeaders),s.body&&(e.body={},e.body.body=s.body,s.contentType&&(e.body.contentType=s.contentType),e.request.body=e.body),e.request.from&&(e.localIdentity=e.request.from),e.request.to&&(e.remoteIdentity=e.request.to)},t.prototype.send=function(){return new c.RequestSender(this,this.ua).send(),this},t.prototype.receiveResponse=function(e){var t=e.statusCode||0,r=h.Utils.getReasonPhrase(t);switch(!0){case/^1[0-9]{2}$/.test(t.toString()):this.emit("progress",e,r);break;case/^2[0-9]{2}$/.test(t.toString()):this.ua.applicants[this.toString()]&&delete this.ua.applicants[this.toString()],this.emit("accepted",e,r);break;default:this.ua.applicants[this.toString()]&&delete this.ua.applicants[this.toString()],this.emit("rejected",e,r),this.emit("failed",e,r)}},t.prototype.onRequestTimeout=function(){this.emit("failed",void 0,o.C.causes.REQUEST_TIMEOUT)},t.prototype.onTransportError=function(){this.emit("failed",void 0,o.C.causes.CONNECTION_ERROR)},t}(n.EventEmitter);t.ClientContext=d},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(1),s=r(0),n=r(9),o=function(){function e(e,t){this.type=s.TypeStrings.RequestSender,this.logger=t.getLogger("sip.requestsender"),this.ua=t,this.applicant=e,this.method=e.request.method,this.request=e.request,this.credentials=void 0,this.challenged=!1,this.staled=!1,t.status===s.UAStatus.STATUS_USER_CLOSED&&this.method!==i.C.BYE&&this.method!==i.C.ACK&&this.onTransportError()}return e.prototype.send=function(){if(!this.ua.transport)throw new Error("No transport to make transaction");switch(this.method){case"INVITE":this.clientTransaction=new n.InviteClientTransaction(this,this.request,this.ua.transport);break;case"ACK":this.clientTransaction=new n.AckClientTransaction(this,this.request,this.ua.transport);break;default:this.clientTransaction=new n.NonInviteClientTransaction(this,this.request,this.ua.transport)}return this.clientTransaction.send(),this.clientTransaction},e.prototype.onRequestTimeout=function(){this.applicant.onRequestTimeout()},e.prototype.onTransportError=function(){this.applicant.onTransportError()},e.prototype.receiveResponse=function(e){var t=e&&e.statusCode?e.statusCode:0;if(401===t||407===t){var r=void 0,s=void 0;if(401===t?(r=e.parseHeader("www-authenticate"),s="authorization"):(r=e.parseHeader("proxy-authenticate"),s="proxy-authorization"),!r)return this.logger.warn(t+" with wrong or missing challenge, cannot authenticate"),void this.applicant.receiveResponse(e);if(!this.challenged||!this.staled&&!0===r.stale){if(!this.credentials&&this.ua.configuration.authenticationFactory&&(this.credentials=this.ua.configuration.authenticationFactory(this.ua)),!this.credentials.authenticate(this.request,r))return void this.applicant.receiveResponse(e);this.challenged=!0,r.stale&&(this.staled=!0);var n=void 0;e.method===i.C.REGISTER?n=this.applicant.cseq+=1:this.request.dialog?n=this.request.dialog.localSeqnum+=1:(n=(this.request.cseq||0)+1,this.request.cseq=n),this.request.setHeader("cseq",n+" "+this.method),this.request.setHeader(s,this.credentials.toString()),this.send()}else this.applicant.receiveResponse(e)}else this.applicant.receiveResponse(e)},e}();t.RequestSender=o},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(1),a=r(0),c=r(6),u=r(11),h={STATUS_TRYING:1,STATUS_PROCEEDING:2,STATUS_CALLING:3,STATUS_ACCEPTED:4,STATUS_COMPLETED:5,STATUS_TERMINATED:6,STATUS_CONFIRMED:7,NON_INVITE_CLIENT:"nict",NON_INVITE_SERVER:"nist",INVITE_CLIENT:"ict",INVITE_SERVER:"ist"},d=function(e,t,r){var i="SIP/2.0/"+(e.configuration.hackViaTcp?"TCP":t.server.scheme);return i+=" "+e.configuration.viaHost+";branch="+r,e.configuration.forceRport&&(i+=";rport"),i},p=function(e){function t(t,r,i){var s=e.call(this)||this;s.kind=h.NON_INVITE_CLIENT,s.type=a.TypeStrings.NonInviteClientTransaction,s.transport=i,s.id="z9hG4bK"+Math.floor(1e7*Math.random()),s.requestSender=t,s.request=r,s.logger=t.ua.getLogger("sip.transaction.nict",s.id);var n=d(t.ua,i,s.id);return s.request.setHeader("via",n),s.requestSender.ua.newTransaction(s),s}return s(t,e),t.prototype.stateChanged=function(e){this.state=e,this.emit("stateChanged")},t.prototype.send=function(){var e=this;this.stateChanged(a.TransactionStatus.STATUS_TRYING),this.F=setTimeout(function(){return e.timer_F()},u.Timers.TIMER_F),this.transport.send(this.request).catch(function(){return e.onTransportError()})},t.prototype.receiveResponse=function(e){var t=this,r=e.statusCode||0;if(r<200)switch(this.state){case a.TransactionStatus.STATUS_TRYING:case a.TransactionStatus.STATUS_PROCEEDING:this.stateChanged(a.TransactionStatus.STATUS_PROCEEDING),this.requestSender.receiveResponse(e)}else switch(this.state){case a.TransactionStatus.STATUS_TRYING:case a.TransactionStatus.STATUS_PROCEEDING:this.stateChanged(a.TransactionStatus.STATUS_COMPLETED),this.F&&clearTimeout(this.F),408===r?this.requestSender.onRequestTimeout():this.requestSender.receiveResponse(e),this.K=setTimeout(function(){return t.timer_K()},u.Timers.TIMER_K);break;case a.TransactionStatus.STATUS_COMPLETED:}},t.prototype.onTransportError=function(){this.logger.log("transport error occurred, deleting non-INVITE client transaction "+this.id),this.F&&(clearTimeout(this.F),this.F=void 0),this.K&&(clearTimeout(this.K),this.K=void 0),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this),this.requestSender.onTransportError()},t.prototype.timer_F=function(){this.logger.debug("Timer F expired for non-INVITE client transaction "+this.id),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this),this.requestSender.onRequestTimeout()},t.prototype.timer_K=function(){this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this)},t}(n.EventEmitter);t.NonInviteClientTransaction=p;var l=function(e){function t(t,r,i){var s=e.call(this)||this;s.kind=h.INVITE_CLIENT,s.type=a.TypeStrings.InviteClientTransaction,s.transport=i,s.id="z9hG4bK"+Math.floor(1e7*Math.random()),s.requestSender=t,s.request=r,s.logger=t.ua.getLogger("sip.transaction.ict",s.id);var n=d(t.ua,i,s.id);return s.request.setHeader("via",n),s.requestSender.ua.newTransaction(s),s.request.cancel=function(e,t){for(var r="",i=0,n=t=(t||[]).slice();i<n.length;i++){r+=n[i].trim()+"\r\n"}s.cancelRequest(s,e,r)},s}return s(t,e),t.prototype.stateChanged=function(e){this.state=e,this.emit("stateChanged")},t.prototype.send=function(){var e=this;this.stateChanged(a.TransactionStatus.STATUS_CALLING),this.B=setTimeout(function(){return e.timer_B()},u.Timers.TIMER_B),this.transport.send(this.request).catch(function(){return e.onTransportError()})},t.prototype.receiveResponse=function(e){var t=this,r=e.statusCode||0;if(e.transaction=this,this.response&&this.response.statusCode===e.statusCode&&this.response.cseq===e.cseq)return this.logger.debug("ICT Received a retransmission for cseq: "+e.cseq),void(this.ackSender&&this.ackSender.send());if(this.response=e,r>=100&&r<=199)switch(this.state){case a.TransactionStatus.STATUS_CALLING:this.stateChanged(a.TransactionStatus.STATUS_PROCEEDING),this.requestSender.receiveResponse(e),this.cancel&&this.transport.send(this.cancel);break;case a.TransactionStatus.STATUS_PROCEEDING:this.requestSender.receiveResponse(e)}else if(r>=200&&r<=299)switch(this.state){case a.TransactionStatus.STATUS_CALLING:case a.TransactionStatus.STATUS_PROCEEDING:this.stateChanged(a.TransactionStatus.STATUS_ACCEPTED),this.M=setTimeout(function(){return t.timer_M()},u.Timers.TIMER_M),this.requestSender.receiveResponse(e);break;case h.STATUS_ACCEPTED:this.requestSender.receiveResponse(e)}else if(r>=300&&r<=699)switch(this.state){case a.TransactionStatus.STATUS_CALLING:case a.TransactionStatus.STATUS_PROCEEDING:this.stateChanged(a.TransactionStatus.STATUS_COMPLETED),this.sendACK(),this.requestSender.receiveResponse(e);break;case a.TransactionStatus.STATUS_COMPLETED:this.sendACK()}},t.prototype.sendACK=function(e){var t,r=this;if(void 0===e&&(e={}),t=this.response&&this.response.getHeader("contact")?this.response.parseHeader("contact").uri:this.request.ruri,this.response){var i=new c.OutgoingRequest("ACK",t.toString(),this.request.ua,{cseq:this.response.cseq,callId:this.response.callId,fromUri:this.response.from.uri,fromTag:this.response.fromTag,toUri:this.response.to.uri,toTag:this.response.toTag,routeSet:this.response.getHeaders("record-route").reverse()},e.extraHeaders||[],e.body);if(!i.ua.transport)throw new Error("No transport to make transaction");return this.ackSender=new f({onTransportError:this.requestSender.applicant?this.requestSender.applicant.onTransportError.bind(this.requestSender.applicant):function(){r.logger.warn("ACK Request had a transport error")},ua:i.ua},i,i.ua.transport),this.ackSender.send(),i}},t.prototype.onTransportError=function(){this.logger.log("transport error occurred, deleting INVITE client transaction "+this.id),this.B&&(clearTimeout(this.B),this.B=void 0),this.D&&(clearTimeout(this.D),this.D=void 0),this.M&&(clearTimeout(this.M),this.M=void 0),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this),this.state!==a.TransactionStatus.STATUS_ACCEPTED&&this.requestSender.onTransportError()},t.prototype.timer_M=function(){this.logger.debug("Timer M expired for INVITE client transaction "+this.id),this.state===a.TransactionStatus.STATUS_ACCEPTED&&(this.B&&(clearTimeout(this.B),this.B=void 0),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this))},t.prototype.timer_B=function(){this.logger.debug("Timer B expired for INVITE client transaction "+this.id),this.state===a.TransactionStatus.STATUS_CALLING&&(this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this),this.requestSender.onRequestTimeout())},t.prototype.timer_D=function(){this.logger.debug("Timer D expired for INVITE client transaction "+this.id),this.B&&(clearTimeout(this.B),this.B=void 0),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.requestSender.ua.destroyTransaction(this)},t.prototype.cancelRequest=function(e,t,r){var i=e.request;this.cancel=o.C.CANCEL+" "+i.ruri+" SIP/2.0\r\n",this.cancel+="Via: "+i.headers.Via.toString()+"\r\n",this.request.headers.Route&&(this.cancel+="Route: "+i.headers.Route.toString()+"\r\n"),this.cancel+="To: "+i.headers.To.toString()+"\r\n",this.cancel+="From: "+i.headers.From.toString()+"\r\n",this.cancel+="Call-ID: "+i.headers["Call-ID"].toString()+"\r\n",this.cancel+="Max-Forwards: 70\r\n",this.cancel+="CSeq: "+i.headers.CSeq.toString().split(" ")[0]+" CANCEL\r\n",t&&(this.cancel+="Reason: "+t+"\r\n"),r&&(this.cancel+=r),this.cancel+="Content-Length: 0\r\n\r\n",this.state===a.TransactionStatus.STATUS_PROCEEDING&&this.transport.send(this.cancel)},t}(n.EventEmitter);t.InviteClientTransaction=l;var f=function(e){function t(t,r,i){var s=e.call(this)||this;s.type=a.TypeStrings.AckClientTransaction,s.transport=i,s.id="z9hG4bK"+Math.floor(1e7*Math.random()),s.requestSender=t,s.request=r,s.logger=t.ua.getLogger("sip.transaction.nict",s.id);var n=d(t.ua,i,s.id);return s.request.setHeader("via",n),s}return s(t,e),t.prototype.send=function(){var e=this;this.transport.send(this.request).catch(function(){e.logger.log("transport error occurred, for an ACK client transaction "+e.id),e.requestSender.onTransportError()})},t}(n.EventEmitter);t.AckClientTransaction=f;var g=function(e){function t(t,r){var i=e.call(this)||this;return i.kind=h.NON_INVITE_SERVER,i.type=a.TypeStrings.NonInviteServerTransaction,i.id=t.viaBranch,i.request=t,i.transport=r.transport,i.ua=r,i.lastResponse="",i.transportError=!1,t.serverTransaction=i,i.logger=r.getLogger("sip.transaction.nist",i.id),i.state=a.TransactionStatus.STATUS_TRYING,r.newTransaction(i),i}return s(t,e),t.prototype.stateChanged=function(e){this.state=e,this.emit("stateChanged")},t.prototype.receiveResponse=function(e,t){var r=this;return new Promise(function(i,s){if(100===e)switch(r.state){case a.TransactionStatus.STATUS_TRYING:r.stateChanged(h.STATUS_PROCEEDING),r.transport&&r.transport.send(t).catch(function(){return r.onTransportError()});break;case a.TransactionStatus.STATUS_PROCEEDING:r.lastResponse=t,r.transport&&r.transport.send(t).then(i).catch(function(){r.onTransportError(),s()})}else if(e>=200&&e<=699)switch(r.state){case a.TransactionStatus.STATUS_TRYING:case a.TransactionStatus.STATUS_PROCEEDING:r.stateChanged(h.STATUS_COMPLETED),r.lastResponse=t,r.J=setTimeout(function(){r.logger.debug("Timer J expired for non-INVITE server transaction "+r.id),r.stateChanged(h.STATUS_TERMINATED),r.ua.destroyTransaction(r)},u.Timers.TIMER_J),r.transport&&r.transport.send(t).then(i).catch(function(){r.onTransportError(),s()});break;case a.TransactionStatus.STATUS_COMPLETED:}})},t.prototype.onTransportError=function(){this.transportError||(this.transportError=!0,this.logger.log("transport error occurred, deleting non-INVITE server transaction "+this.id),this.J&&(clearTimeout(this.J),this.J=void 0),this.stateChanged(h.STATUS_TERMINATED),this.ua.destroyTransaction(this))},t}(n.EventEmitter);t.NonInviteServerTransaction=g;var T=function(e){function t(t,r){var i=e.call(this)||this;return i.kind=h.INVITE_SERVER,i.type=a.TypeStrings.InviteServerTransaction,i.id=t.viaBranch,i.request=t,i.transport=r.transport,i.ua=r,i.lastResponse="",i.transportError=!1,t.serverTransaction=i,i.logger=r.getLogger("sip.transaction.ist",i.id),i.state=a.TransactionStatus.STATUS_PROCEEDING,r.newTransaction(i),t.reply(100),i}return s(t,e),t.prototype.stateChanged=function(e){this.state=e,this.emit("stateChanged")},t.prototype.timer_I=function(){this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.ua.destroyTransaction(this)},t.prototype.receiveResponse=function(e,t){var r=this;return new Promise(function(i,s){if(e>=100&&e<=199&&r.state===a.TransactionStatus.STATUS_PROCEEDING)r.transport&&r.transport.send(t).catch(function(){return r.onTransportError()}),r.lastResponse=t,e>100&&void 0===r.resendProvisionalTimer&&(r.resendProvisionalTimer=setInterval(function(){r.transport&&r.transport.send(t).catch(function(){return r.onTransportError()})},u.Timers.PROVISIONAL_RESPONSE_INTERVAL));else if(e>=200&&e<=299)switch(r.state){case a.TransactionStatus.STATUS_PROCEEDING:r.stateChanged(h.STATUS_ACCEPTED),r.lastResponse=t,r.L=setTimeout(function(){return r.timer_L()},u.Timers.TIMER_L),void 0!==r.resendProvisionalTimer&&(clearInterval(r.resendProvisionalTimer),r.resendProvisionalTimer=void 0);case a.TransactionStatus.STATUS_ACCEPTED:r.transport&&r.transport.send(t).then(i).catch(function(e){r.logger.error(e),r.onTransportError(),s()})}else if(e>=300&&e<=699)switch(r.state){case a.TransactionStatus.STATUS_PROCEEDING:void 0!==r.resendProvisionalTimer&&(clearInterval(r.resendProvisionalTimer),r.resendProvisionalTimer=void 0),r.transport&&r.transport.send(t).then(function(){r.stateChanged(a.TransactionStatus.STATUS_COMPLETED),r.H=setTimeout(function(){return r.timer_H()},u.Timers.TIMER_H),i()}).catch(function(e){r.logger.error(e),r.onTransportError(),s()})}})},t.prototype.timer_H=function(){this.logger.debug("Timer H expired for INVITE server transaction "+this.id),this.state===a.TransactionStatus.STATUS_COMPLETED&&this.logger.warn("transactions: ACK for INVITE server transaction was never received, call will be terminated"),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.ua.destroyTransaction(this)},t.prototype.timer_L=function(){this.logger.debug("Timer L expired for INVITE server transaction "+this.id),this.state===a.TransactionStatus.STATUS_ACCEPTED&&(this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.ua.destroyTransaction(this))},t.prototype.onTransportError=function(){this.transportError||(this.transportError=!0,this.logger.log("transport error occurred, deleting INVITE server transaction "+this.id),void 0!==this.resendProvisionalTimer&&(clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=void 0),this.L&&(clearTimeout(this.L),this.L=void 0),this.H&&(clearTimeout(this.H),this.H=void 0),this.I&&(clearTimeout(this.I),this.I=void 0),this.stateChanged(a.TransactionStatus.STATUS_TERMINATED),this.ua.destroyTransaction(this))},t}(n.EventEmitter);t.InviteServerTransaction=T,t.checkTransaction=function(e,t){var r=e.transactions.ist[t.viaBranch];switch(t.method){case o.C.INVITE:if(r){switch(r.state){case a.TransactionStatus.STATUS_PROCEEDING:r.transport&&r.transport.send(r.lastResponse);break;case a.TransactionStatus.STATUS_ACCEPTED:}return!0}break;case o.C.ACK:if(!r)return!1;if(r.state===a.TransactionStatus.STATUS_ACCEPTED)return!1;if(r.state===a.TransactionStatus.STATUS_COMPLETED)return r.stateChanged(a.TransactionStatus.STATUS_CONFIRMED),r.I=setTimeout(r.timer_I.bind(r),u.Timers.TIMER_I),!0;break;case o.C.CANCEL:return r?(t.reply_sl(200),r.state!==a.TransactionStatus.STATUS_PROCEEDING):(t.reply_sl(481),!0);default:var i=e.transactions.nist[t.viaBranch];if(i){switch(i.state){case a.TransactionStatus.STATUS_TRYING:break;case a.TransactionStatus.STATUS_PROCEEDING:case a.TransactionStatus.STATUS_COMPLETED:i.transport&&i.transport.send(i.lastResponse)}return!0}}return!1}},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(1),o=r(0),a=function(){function e(e){for(var t in this.parameters={},this.type=o.TypeStrings.Parameters,e)e.hasOwnProperty(t)&&this.setParam(t,e[t])}return e.prototype.setParam=function(e,t){e&&(this.parameters[e.toLowerCase()]=null==t?null:t.toString())},e.prototype.getParam=function(e){if(e)return this.parameters[e.toLowerCase()]},e.prototype.hasParam=function(e){return!!e&&!!this.parameters.hasOwnProperty(e.toLowerCase())},e.prototype.deleteParam=function(e){if(e=e.toLowerCase(),this.parameters.hasOwnProperty(e)){var t=this.parameters[e];return delete this.parameters[e],t}},e.prototype.clearParams=function(){this.parameters={}},e}();t.Parameters=a;var c=function(e){function t(t,r,i,s,a,c){var u=e.call(this,a)||this;if(u.headers={},u.type=o.TypeStrings.URI,!i)throw new TypeError('missing or invalid "host" parameter');for(var h in t=t||n.C.SIP,c)c.hasOwnProperty(h)&&u.setHeader(h,c[h]);return u.raw={scheme:t,user:r,host:i,port:s},u.normal={scheme:t.toLowerCase(),user:r,host:i.toLowerCase(),port:s},u}return s(t,e),Object.defineProperty(t.prototype,"_normal",{get:function(){return this.normal},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_raw",{get:function(){return this.raw},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scheme",{get:function(){return this.normal.scheme},set:function(e){this.raw.scheme=e,this.normal.scheme=e.toLowerCase()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"user",{get:function(){return this.normal.user},set:function(e){this.normal.user=this.raw.user=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"host",{get:function(){return this.normal.host},set:function(e){this.raw.host=e,this.normal.host=e.toLowerCase()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"aor",{get:function(){return this.normal.user+"@"+this.normal.host},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"port",{get:function(){return this.normal.port},set:function(e){this.normal.port=this.raw.port=e},enumerable:!0,configurable:!0}),t.prototype.setHeader=function(e,t){this.headers[this.headerize(e)]=t instanceof Array?t:[t]},t.prototype.getHeader=function(e){if(e)return this.headers[this.headerize(e)]},t.prototype.hasHeader=function(e){return!!e&&!!this.headers.hasOwnProperty(this.headerize(e))},t.prototype.deleteHeader=function(e){if(e=this.headerize(e),this.headers.hasOwnProperty(e)){var t=this.headers[e];return delete this.headers[e],t}},t.prototype.clearHeaders=function(){this.headers={}},t.prototype.clone=function(){return new t(this._raw.scheme,this._raw.user||"",this._raw.host,this._raw.port,JSON.parse(JSON.stringify(this.parameters)),JSON.parse(JSON.stringify(this.headers)))},t.prototype.toRaw=function(){return this._toString(this._raw)},t.prototype.toString=function(){return this._toString(this._normal)},t.prototype._toString=function(e){var t=e.scheme+":";for(var r in e.scheme.toLowerCase().match("^sips?$")||(t+="//"),e.user&&(t+=this.escapeUser(e.user)+"@"),t+=e.host,(e.port||0===e.port)&&(t+=":"+e.port),this.parameters)this.parameters.hasOwnProperty(r)&&(t+=";"+r,null!==this.parameters[r]&&(t+="="+this.parameters[r]));var i=[];for(var s in this.headers)if(this.headers.hasOwnProperty(s))for(var n in this.headers[s])this.headers[s].hasOwnProperty(n)&&i.push(s+"="+this.headers[s][n]);return i.length>0&&(t+="?"+i.join("&")),t},t.prototype.escapeUser=function(e){return encodeURIComponent(decodeURIComponent(e)).replace(/%3A/gi,":").replace(/%2B/gi,"+").replace(/%3F/gi,"?").replace(/%2F/gi,"/")},t.prototype.headerize=function(e){for(var t={"Call-Id":"Call-ID",Cseq:"CSeq","Min-Se":"Min-SE",Rack:"RAck",Rseq:"RSeq","Www-Authenticate":"WWW-Authenticate"},r=e.toLowerCase().replace(/_/g,"-").split("-"),i=r.length,s="",n=0;n<i;n++)0!==n&&(s+="-"),s+=r[n].charAt(0).toUpperCase()+r[n].substring(1);return t[s]&&(s=t[s]),s},t}(a);t.URI=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=500;t.Timers={T1:i,T2:4e3,T4:5e3,TIMER_B:32e3,TIMER_D:0,TIMER_F:32e3,TIMER_H:32e3,TIMER_I:0,TIMER_J:0,TIMER_K:0,TIMER_L:32e3,TIMER_M:32e3,TIMER_N:32e3,PROVISIONAL_RESPONSE_INTERVAL:6e4}},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(1),s=r(0),n=r(8),o=r(6),a=function(){function e(e,t,r,i){if(this.pracked=[],this.uacPendingReply=!1,this.uasPendingReply=!1,this.type=s.TypeStrings.Dialog,!t.hasHeader("contact"))throw new Error("unable to create a Dialog without Contact header field");if(t.type===s.TypeStrings.IncomingResponse){var n=t.statusCode;i=n&&n<200?s.DialogStatus.STATUS_EARLY:s.DialogStatus.STATUS_CONFIRMED}else i=i||s.DialogStatus.STATUS_CONFIRMED;var o=t.parseHeader("contact");"UAS"===r&&t.type===s.TypeStrings.IncomingRequest?(this.id={callId:t.callId,localTag:t.toTag,remoteTag:t.fromTag,toString:function(){return t.callId+t.toTag+t.fromTag}},this.state=i,this.remoteSeqnum=t.cseq,this.localUri=(t.parseHeader("to")||{}).uri,this.remoteUri=(t.parseHeader("from")||{}).uri,this.remoteTarget=o.uri,this.routeSet=t.getHeaders("record-route"),this.inviteSeqnum=t.cseq,this.localSeqnum=t.cseq):(this.id={callId:t.callId,localTag:t.fromTag,remoteTag:t.toTag,toString:function(){return t.callId+t.fromTag+t.toTag}},this.state=i,this.inviteSeqnum=t.cseq,this.localSeqnum=t.cseq,this.localUri=t.parseHeader("from").uri,this.pracked=[],this.remoteUri=t.parseHeader("to").uri,this.remoteTarget=o.uri,this.routeSet=t.getHeaders("record-route").reverse()),this.logger=e.ua.getLogger("sip.dialog",this.id.toString()),this.owner=e,e.ua.dialogs[this.id.toString()]=this,this.logger.log("new "+r+" dialog created with status "+(this.state===s.DialogStatus.STATUS_EARLY?"EARLY":"CONFIRMED")),e.emit("dialog",this)}return e.prototype.update=function(e,t){this.state=s.DialogStatus.STATUS_CONFIRMED,this.logger.log("dialog "+this.id.toString()+"  changed to CONFIRMED state"),"UAC"===t&&(this.routeSet=e.getHeaders("record-route").reverse())},e.prototype.terminate=function(){this.logger.log("dialog "+this.id.toString()+" deleted"),this.sessionDescriptionHandler&&this.state!==s.DialogStatus.STATUS_CONFIRMED&&this.sessionDescriptionHandler.close(),delete this.owner.ua.dialogs[this.id.toString()]},e.prototype.createRequest=function(e,t,r){void 0===t&&(t=[]),t=t.slice(),this.localSeqnum||(this.localSeqnum=Math.floor(1e4*Math.random()));var s=e===i.C.CANCEL||e===i.C.ACK?this.inviteSeqnum:this.localSeqnum+=1,n=new o.OutgoingRequest(e,this.remoteTarget,this.owner.ua,{cseq:s,callId:this.id.callId,fromUri:this.localUri,fromTag:this.id.localTag,toIri:this.remoteUri,toTag:this.id.remoteTag,routeSet:this.routeSet},t,r);return n.dialog=this,n},e.prototype.checkInDialogRequest=function(e){var t=this;if(this.remoteSeqnum){if(e.cseq<this.remoteSeqnum)return e.method!==i.C.ACK&&e.reply(500),e.cseq===this.inviteSeqnum}else this.remoteSeqnum=e.cseq;switch(e.method){case i.C.INVITE:if(!0===this.uacPendingReply)e.reply(491);else{if(!0===this.uasPendingReply&&e.cseq>this.remoteSeqnum){var r=Math.floor(10*Math.random())+1;return e.reply(500,void 0,["Retry-After:"+r]),this.remoteSeqnum=e.cseq,!1}this.uasPendingReply=!0;var n=function(){!e.serverTransaction||e.serverTransaction.state!==s.TransactionStatus.STATUS_ACCEPTED&&e.serverTransaction.state!==s.TransactionStatus.STATUS_COMPLETED&&e.serverTransaction.state!==s.TransactionStatus.STATUS_TERMINATED||(e.serverTransaction.removeListener("stateChanged",n),t.uasPendingReply=!1)};e.serverTransaction&&e.serverTransaction.on("stateChanged",n)}e.hasHeader("contact")&&e.serverTransaction&&e.serverTransaction.on("stateChanged",function(){e.serverTransaction&&e.serverTransaction.state===s.TransactionStatus.STATUS_ACCEPTED&&(t.remoteTarget=e.parseHeader("contact").uri)});break;case i.C.NOTIFY:e.hasHeader("contact")&&e.serverTransaction&&e.serverTransaction.on("stateChanged",function(){e.serverTransaction&&e.serverTransaction.state===s.TransactionStatus.STATUS_COMPLETED&&(t.remoteTarget=e.parseHeader("contact").uri)})}return e.cseq>this.remoteSeqnum&&(this.remoteSeqnum=e.cseq),!0},e.prototype.sendRequest=function(e,t,r){var o=this;void 0===r&&(r={});var a,c=(r.extraHeaders||[]).slice();r.body&&(r.body.body?a=r.body:((a={}).body=r.body,r.contentType&&(a.contentType=r.contentType)));var u=this.createRequest(t,c,a),h=function(t){var r=new n.RequestSender({request:u,onRequestTimeout:e.onRequestTimeout.bind(e),onTransportError:e.onTransportError.bind(e),receiveResponse:function(r){408===r.statusCode||481===r.statusCode?e.onDialogError(r):r.method===i.C.INVITE&&491===r.statusCode?t?e.receiveResponse(r):(u.cseq=o.localSeqnum+=1,setTimeout(function(){void 0!==o.owner.status&&o.owner.status!==s.SessionStatus.STATUS_TERMINATED&&h(!0)},1e3)):e.receiveResponse(r)}},o.owner.ua);if(r.send(),r.clientTransaction&&r.clientTransaction.type!==s.TypeStrings.AckClientTransaction&&u.method===i.C.INVITE&&r.clientTransaction&&r.clientTransaction.state!==s.TransactionStatus.STATUS_TERMINATED){o.uacPendingReply=!0;var a=function(){var e=r.clientTransaction.state;r.clientTransaction&&r.clientTransaction.type!==s.TypeStrings.AckClientTransaction&&(!r.clientTransaction||e!==s.TransactionStatus.STATUS_ACCEPTED&&e!==s.TransactionStatus.STATUS_COMPLETED&&e!==s.TransactionStatus.STATUS_TERMINATED||(r.clientTransaction.removeListener("stateChanged",a),o.uacPendingReply=!1))};r.clientTransaction.on("stateChanged",a)}};return h(!1),u},e.prototype.receiveRequest=function(e){this.checkInDialogRequest(e)&&this.owner.receiveRequest(e)},e.C=s.DialogStatus,e}();t.Dialog=a},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(1),a=r(0),c=r(4),u=r(9),h=r(2),d=function(e){function t(r,i){var s=e.call(this)||this;return s.data={},t.initializer(s,r,i),s}return s(t,e),t.initializer=function(e,t,r){if(e.type=a.TypeStrings.ServerContext,e.ua=t,e.logger=t.getLogger("sip.servercontext"),e.request=r,r.method===o.C.INVITE?e.transaction=new u.InviteServerTransaction(r,t):e.transaction=new u.NonInviteServerTransaction(r,t),r.body&&(e.body=r.body),r.hasHeader("Content-Type")&&(e.contentType=r.getHeader("Content-Type")),e.method=r.method,e.localIdentity=r.to,e.remoteIdentity=r.from,r.hasHeader("P-Asserted-Identity")){var i=r.getHeader("P-Asserted-Identity");i&&(e.assertedIdentity=c.Grammar.nameAddrHeaderParse(i))}},t.prototype.progress=function(e){return void 0===e&&(e={}),e.statusCode=e.statusCode||180,e.minCode=100,e.maxCode=199,e.events=["progress"],this.reply(e)},t.prototype.accept=function(e){return void 0===e&&(e={}),e.statusCode=e.statusCode||200,e.minCode=200,e.maxCode=299,e.events=["accepted"],this.reply(e)},t.prototype.reject=function(e){return void 0===e&&(e={}),e.statusCode=e.statusCode||480,e.minCode=300,e.maxCode=699,e.events=["rejected","failed"],this.reply(e)},t.prototype.reply=function(e){var t=this;void 0===e&&(e={});var r=e.statusCode||100,i=e.minCode||100,s=e.maxCode||699,n=h.Utils.getReasonPhrase(r,e.reasonPhrase),o=e.extraHeaders||[],a=e.body,c=e.events||[];if(r<i||r>s)throw new TypeError("Invalid statusCode: "+r);var u=this.request.reply(r,n,o,a);return c.forEach(function(e){t.emit(e,u,n)}),this},t.prototype.onRequestTimeout=function(){this.emit("failed",void 0,o.C.causes.REQUEST_TIMEOUT)},t.prototype.onTransportError=function(){this.emit("failed",void 0,o.C.causes.CONNECTION_ERROR)},t}(n.EventEmitter);t.ServerContext=d},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t){for(var r,i=[],s=e.split(/\r\n/),n=0;n<s.length;){var o=s[n];if(/^m=(?:audio|video)/.test(o))r={index:n,stripped:[]},i.push(r);else if(r){var a=/^a=rtpmap:(\d+) ([^\/]+)\//.exec(o);if(a&&t===a[2]){s.splice(n,1),r.stripped.push(a[1]);continue}}n++}for(var c=0,u=i;c<u.length;c++){for(var h=u[c],d=s[h.index].split(" "),p=3;p<d.length;)-1===h.stripped.indexOf(d[p])?p++:d.splice(p,1);s[h.index]=d.join(" ")}return s.join("\r\n")},s=function(e,t){var r=new RegExp("m="+t+".*$","gm"),i=new RegExp("^a=group:.*$","gm");if(r.test(e)){var s,n=(e=e.split(/^m=/gm).filter(function(e){if(e.substr(0,t.length)===t){if(s=e.match(/^a=mid:.*$/gm)){var r=s[0].match(/:.+$/g);r&&(s=r[0].substr(1))}return!1}return!0}).join("m=")).match(i);if(n&&1===n.length){var o=n[0],a=new RegExp(" *"+s+"[^ ]*","g");o=o.replace(a,""),e=e.split(i).join(o)}}return e};t.stripTcpCandidates=function(e){return e.sdp=(e.sdp||"").replace(/^a=candidate:\d+ \d+ tcp .*?\r\n/gim,""),Promise.resolve(e)},t.stripTelephoneEvent=function(e){return e.sdp=i(e.sdp||"","telephone-event"),Promise.resolve(e)},t.cleanJitsiSdpImageattr=function(e){return e.sdp=(e.sdp||"").replace(/^(a=imageattr:.*?)(x|y)=\[0-/gm,"$1$2=[1:"),Promise.resolve(e)},t.stripG722=function(e){return e.sdp=i(e.sdp||"","G722"),Promise.resolve(e)},t.stripRtpPayload=function(e){return function(t){return t.sdp=i(t.sdp||"",e),Promise.resolve(t)}},t.stripVideo=function(e){return e.sdp=s(e.sdp||"","video"),Promise.resolve(e)},t.addMidLines=function(e){var t=e.sdp||"";if(-1===t.search(/^a=mid.*$/gm)){var r=t.match(/^m=.*$/gm),i=t.split(/^m=.*$/gm);r&&r.forEach(function(e,t){r[t]=e+"\na=mid:"+t}),i.forEach(function(e,t){r&&r[t]&&(i[t]=e+r[t])}),t=i.join(""),e.sdp=t}return Promise.resolve(e)}},function(e){e.exports={name:"sip.js",title:"SIP.js",description:"A simple, intuitive, and powerful JavaScript signaling library",version:"0.13.6",license:"MIT",main:"lib/index.js",homepage:"https://sipjs.com",author:"OnSIP <<EMAIL>> (https://sipjs.com/aboutus/)",contributors:[{url:"https://github.com/onsip/SIP.js/blob/master/THANKS.md"}],repository:{type:"git",url:"https://github.com/onsip/SIP.js.git"},keywords:["sip","websocket","webrtc","library","javascript"],dependencies:{"crypto-js":"^3.1.9-1"},devDependencies:{"@types/node":"^11.9.6","circular-dependency-plugin":"^5.0.2","jasmine-core":"^3.3.0",karma:"^4.0.1","karma-chrome-launcher":"^2.2.0","karma-cli":"^2.0.0","karma-jasmine":"^2.0.1","karma-jasmine-html-reporter":"^1.4.0","karma-mocha-reporter":"^2.2.5","karma-webpack":"^3.0.5",pegjs:"^0.10.0","ts-loader":"^5.3.3","ts-pegjs":"0.2.2",tslint:"^5.13.1",typescript:"^3.3.3333",webpack:"^4.29.6","webpack-cli":"^3.2.3"},engines:{node:">=8.0"},scripts:{prebuild:"tslint -p tsconfig.json -c tslint.json","generate-grammar":"node build/grammarGenerator.js","build-reg-bundle":"webpack --progress --config build/webpack.config.js --env.buildType reg","build-min-bundle":"webpack --progress --config build/webpack.config.js --env.buildType min","build-bundles":"npm run build-reg-bundle && npm run build-min-bundle","build-lib":"tsc","copy-dist-files":"cp dist/sip.js dist/sip-$npm_package_version.js && cp dist/sip.min.js  dist/sip-$npm_package_version.min.js",build:"npm run generate-grammar && npm run build-lib && npm run build-reg-bundle && npm run build-min-bundle && npm run copy-dist-files",browserTest:"sleep 2 && open http://0.0.0.0:9876/debug.html & karma start --reporters kjhtml --no-single-run",commandLineTest:"karma start --reporters mocha --browsers ChromeHeadless --single-run",buildAndTest:"npm run build && npm run commandLineTest",buildAndBrowserTest:"npm run build && npm run browserTest"},types:"./types"}},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),o=function(e){function t(t,r,i){var s=e.call(this,i)||this;if(s.type=n.TypeStrings.NameAddrHeader,!t||t.type!==n.TypeStrings.URI)throw new TypeError('missing or invalid "uri" parameter');return s.uri=t,s._displayName=r,s}return s(t,e),Object.defineProperty(t.prototype,"friendlyName",{get:function(){return this.displayName||this.uri.aor},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"displayName",{get:function(){return this._displayName},set:function(e){this._displayName=e},enumerable:!0,configurable:!0}),t.prototype.clone=function(){return new t(this.uri.clone(),this._displayName,JSON.parse(JSON.stringify(this.parameters)))},t.prototype.toString=function(){var e=this.displayName||"0"===this.displayName?'"'+this.displayName+'" ':"";for(var t in e+="<"+this.uri.toString()+">",this.parameters)this.parameters.hasOwnProperty(t)&&(e+=";"+t,null!==this.parameters[t]&&(e+="="+this.parameters[t]));return e},t}(r(10).Parameters);t.NameAddrHeader=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(32),s=r(0),n=r(2),o=function(){function e(e){this.type=s.TypeStrings.DigestAuthentication,this.logger=e.getLogger("sipjs.digestauthentication"),this.username=e.configuration.authorizationUser,this.password=e.configuration.password,this.nc=0,this.ncHex="00000000"}return e.prototype.authenticate=function(e,t,r){if(this.algorithm=t.algorithm,this.realm=t.realm,this.nonce=t.nonce,this.opaque=t.opaque,this.stale=t.stale,this.algorithm){if("MD5"!==this.algorithm)return this.logger.warn("challenge with Digest algorithm different than 'MD5', authentication aborted"),!1}else this.algorithm="MD5";if(!this.realm)return this.logger.warn("challenge without Digest realm, authentication aborted"),!1;if(!this.nonce)return this.logger.warn("challenge without Digest nonce, authentication aborted"),!1;if(t.qop)if(t.qop.indexOf("auth")>-1)this.qop="auth";else{if(!(t.qop.indexOf("auth-int")>-1))return this.logger.warn("challenge without Digest qop different than 'auth' or 'auth-int', authentication aborted"),!1;this.qop="auth-int"}else this.qop=void 0;return this.method=e.method,this.uri=e.ruri,this.cnonce=n.Utils.createRandomToken(12),this.nc+=1,this.updateNcHex(),4294967296===this.nc&&(this.nc=1,this.ncHex="00000001"),this.calculateResponse(r),!0},e.prototype.toString=function(){var e=[];if(!this.response)throw new Error("response field does not exist, cannot generate Authorization header");return e.push("algorithm="+this.algorithm),e.push('username="'+this.username+'"'),e.push('realm="'+this.realm+'"'),e.push('nonce="'+this.nonce+'"'),e.push('uri="'+this.uri+'"'),e.push('response="'+this.response+'"'),this.opaque&&e.push('opaque="'+this.opaque+'"'),this.qop&&(e.push("qop="+this.qop),e.push('cnonce="'+this.cnonce+'"'),e.push("nc="+this.ncHex)),"Digest "+e.join(", ")},e.prototype.updateNcHex=function(){var e=Number(this.nc).toString(16);this.ncHex="00000000".substr(0,8-e.length)+e},e.prototype.calculateResponse=function(e){var t,r=i(this.username+":"+this.realm+":"+this.password);"auth"===this.qop?(t=i(this.method+":"+this.uri),this.response=i(r+":"+this.nonce+":"+this.ncHex+":"+this.cnonce+":auth:"+t)):"auth-int"===this.qop?(t=i(this.method+":"+this.uri+":"+i(e||"")),this.response=i(r+":"+this.nonce+":"+this.ncHex+":"+this.cnonce+":auth-int:"+t)):void 0===this.qop&&(t=i(this.method+":"+this.uri),this.response=i(r+":"+this.nonce+":"+t))},e}();t.DigestAuthentication=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i,s=r(0);!function(e){e[e.error=0]="error",e[e.warn=1]="warn",e[e.log=2]="log",e[e.debug=3]="debug"}(i=t.Levels||(t.Levels={}));var n=function(){function e(){this.builtinEnabled=!0,this._level=i.log,this.loggers={},this.type=s.TypeStrings.LoggerFactory,this.logger=this.getLogger("sip:loggerfactory")}return Object.defineProperty(e.prototype,"level",{get:function(){return this._level},set:function(e){e>=0&&e<=3?this._level=e:e>3?this._level=3:i.hasOwnProperty(e)?this._level=e:this.logger.error("invalid 'level' parameter value: "+JSON.stringify(e))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"connector",{get:function(){return this._connector},set:function(e){e?"function"==typeof e?this._connector=e:this.logger.error("invalid 'connector' parameter value: "+JSON.stringify(e)):this._connector=void 0},enumerable:!0,configurable:!0}),e.prototype.getLogger=function(e,t){if(t&&3===this.level)return new o(this,e,t);if(this.loggers[e])return this.loggers[e];var r=new o(this,e);return this.loggers[e]=r,r},e.prototype.genericLog=function(e,t,r,s){this.level>=e&&(this.builtinEnabled&&this.print(console[i[e]],t,r,s),this.connector&&this.connector(i[e],t,r,s))},e.prototype.print=function(e,t,r,i){if("string"==typeof i){var s=[new Date,t];r&&s.push(r),i=s.concat(i).join(" | ")}e.call(console,i)},e}();t.LoggerFactory=n;var o=function(){function e(e,t,r){this.type=s.TypeStrings.Logger,this.logger=e,this.category=t,this.label=r}return e.prototype.error=function(e){this.genericLog(i.error,e)},e.prototype.warn=function(e){this.genericLog(i.warn,e)},e.prototype.log=function(e){this.genericLog(i.log,e)},e.prototype.debug=function(e){this.genericLog(i.debug,e)},e.prototype.genericLog=function(e,t){this.logger.genericLog(e,this.category,this.label,t)},e}();t.Logger=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),s=r(4),n=r(6);!function(e){function t(e,t){var r=t,i=0,s=0;if(e.substring(r,r+2).match(/(^\r\n)/))return-2;for(;0===i;){if(-1===(s=e.indexOf("\r\n",r)))return s;!e.substring(s+2,s+4).match(/(^\r\n)/)&&e.charAt(s+2).match(/(^\s+)/)?r=s+2:i=s}return i}function r(e,t,r,n){var o,a=t.indexOf(":",r),c=t.substring(r,a).trim(),u=t.substring(a+1,n).trim();switch(c.toLowerCase()){case"via":case"v":e.addHeader("via",u),1===e.getHeaders("via").length?(o=e.parseHeader("Via"))&&(e.via=o,e.viaBranch=o.branch):o=0;break;case"from":case"f":e.setHeader("from",u),(o=e.parseHeader("from"))&&(e.from=o,e.fromTag=o.getParam("tag"));break;case"to":case"t":e.setHeader("to",u),(o=e.parseHeader("to"))&&(e.to=o,e.toTag=o.getParam("tag"));break;case"record-route":if(-1===(o=s.Grammar.parse(u,"Record_Route"))){o=void 0;break}for(var h in o)o[h]&&(e.addHeader("record-route",u.substring(o[h].position,o[h].offset)),e.headers["Record-Route"][e.getHeaders("record-route").length-1].parsed=o[h].parsed);break;case"call-id":case"i":e.setHeader("call-id",u),(o=e.parseHeader("call-id"))&&(e.callId=u);break;case"contact":case"m":if(-1===(o=s.Grammar.parse(u,"Contact"))){o=void 0;break}for(var h in o)o[h]&&(e.addHeader("contact",u.substring(o[h].position,o[h].offset)),e.headers.Contact[e.getHeaders("contact").length-1].parsed=o[h].parsed);break;case"content-length":case"l":e.setHeader("content-length",u),o=e.parseHeader("content-length");break;case"content-type":case"c":e.setHeader("content-type",u),o=e.parseHeader("content-type");break;case"cseq":e.setHeader("cseq",u),(o=e.parseHeader("cseq"))&&(e.cseq=o.value),e.type===i.TypeStrings.IncomingResponse&&(e.method=o.method);break;case"max-forwards":e.setHeader("max-forwards",u),o=e.parseHeader("max-forwards");break;case"www-authenticate":e.setHeader("www-authenticate",u),o=e.parseHeader("www-authenticate");break;case"proxy-authenticate":e.setHeader("proxy-authenticate",u),o=e.parseHeader("proxy-authenticate");break;case"refer-to":case"r":e.setHeader("refer-to",u),(o=e.parseHeader("refer-to"))&&(e.referTo=o);break;default:e.setHeader(c,u),o=0}return void 0!==o||{error:"error parsing header '"+c+"'"}}e.getHeader=t,e.parseHeader=r,e.parseMessage=function(e,i){var o=0,a=e.indexOf("\r\n"),c=i.getLogger("sip.parser");if(-1!==a){var u,h=e.substring(0,a),d=s.Grammar.parse(h,"Request_Response");if(-1!==d){var p;for(d.status_code?((u=new n.IncomingResponse(i)).statusCode=d.status_code,u.reasonPhrase=d.reason_phrase):((u=new n.IncomingRequest(i)).method=d.method,u.ruri=d.uri),u.data=e,o=a+2;;){if(-2===(a=t(e,o))){p=o+2;break}if(-1===a)return void c.error("malformed message");if(!0!==r(u,e,o,a))return void c.error(d.error);o=a+2}return u.hasHeader("content-length")?u.body=e.substr(p,Number(u.getHeader("content-length"))):u.body=e.substring(p),u}c.warn('error parsing first line of SIP message: "'+h+'"')}else c.warn("no CRLF found, not a SIP message, discarded")}}(t.Parser||(t.Parser={}))},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(7),o=r(1),a=r(0),c=r(5),u=r(6),h=r(2),d=function(e){function t(t,r,i,s){void 0===s&&(s={});var n=this;if(s.extraHeaders=(s.extraHeaders||[]).slice(),s.contentType=s.contentType||"text/plain","number"!=typeof s.expires||s.expires%1!=0?s.expires=3600:s.expires=Number(s.expires),"boolean"!=typeof s.unpublishOnClose&&(s.unpublishOnClose=!0),null==r||""===r)throw new c.Exceptions.MethodParameterError("Publish","Target",r);if(void 0===(r=t.normalizeTarget(r)))throw new c.Exceptions.MethodParameterError("Publish","Target",r);if((n=e.call(this,t,o.C.PUBLISH,r,s)||this).type=a.TypeStrings.PublishContext,n.options=s,n.target=r,null==i||""===i)throw new c.Exceptions.MethodParameterError("Publish","Event",i);return n.event=i,n.logger=t.getLogger("sip.publish"),n.pubRequestExpires=n.options.expires,t.on("transportCreated",function(e){e.on("transportError",function(){return n.onTransportError()})}),n}return s(t,e),t.prototype.publish=function(e){this.publishRefreshTimer&&(clearTimeout(this.publishRefreshTimer),this.publishRefreshTimer=void 0),this.options.body=e,this.pubRequestBody=this.options.body,0===this.pubRequestExpires&&(this.pubRequestExpires=this.options.expires,this.pubRequestEtag=void 0),this.ua.publishers[this.target.toString()+":"+this.event]||(this.ua.publishers[this.target.toString()+":"+this.event]=this),this.sendPublishRequest()},t.prototype.unpublish=function(){this.publishRefreshTimer&&(clearTimeout(this.publishRefreshTimer),this.publishRefreshTimer=void 0),this.pubRequestBody=void 0,this.pubRequestExpires=0,void 0!==this.pubRequestEtag&&this.sendPublishRequest()},t.prototype.close=function(){this.options.unpublishOnClose?this.unpublish():(this.publishRefreshTimer&&(clearTimeout(this.publishRefreshTimer),this.publishRefreshTimer=void 0),this.pubRequestBody=void 0,this.pubRequestExpires=0,this.pubRequestEtag=void 0),this.ua.publishers[this.target.toString()+":"+this.event]&&delete this.ua.publishers[this.target.toString()+":"+this.event]},t.prototype.onRequestTimeout=function(){e.prototype.onRequestTimeout.call(this),this.emit("unpublished",void 0,o.C.causes.REQUEST_TIMEOUT)},t.prototype.onTransportError=function(){e.prototype.onTransportError.call(this),this.emit("unpublished",void 0,o.C.causes.CONNECTION_ERROR)},t.prototype.receiveResponse=function(e){var t=this,r=e.statusCode||0,i=h.Utils.getReasonPhrase(r);switch(!0){case/^1[0-9]{2}$/.test(r.toString()):this.emit("progress",e,i);break;case/^2[0-9]{2}$/.test(r.toString()):if(e.hasHeader("SIP-ETag")?this.pubRequestEtag=e.getHeader("SIP-ETag"):this.logger.warn("SIP-ETag header missing in a 200-class response to PUBLISH"),e.hasHeader("Expires")){var s=Number(e.getHeader("Expires"));"number"==typeof s&&s>=0&&s<=this.pubRequestExpires?this.pubRequestExpires=s:this.logger.warn("Bad Expires header in a 200-class response to PUBLISH")}else this.logger.warn("Expires header missing in a 200-class response to PUBLISH");0!==this.pubRequestExpires?(this.publishRefreshTimer=setTimeout(function(){return t.refreshRequest()},900*this.pubRequestExpires),this.emit("published",e,i)):this.emit("unpublished",e,i);break;case/^412$/.test(r.toString()):void 0!==this.pubRequestEtag&&0!==this.pubRequestExpires?(this.logger.warn("412 response to PUBLISH, recovering"),this.pubRequestEtag=void 0,this.emit("progress",e,i),this.publish(this.options.body)):(this.logger.warn("412 response to PUBLISH, recovery failed"),this.pubRequestExpires=0,this.emit("failed",e,i),this.emit("unpublished",e,i));break;case/^423$/.test(r.toString()):if(0!==this.pubRequestExpires&&e.hasHeader("Min-Expires")){var n=Number(e.getHeader("Min-Expires"));"number"==typeof n||n>this.pubRequestExpires?(this.logger.warn("423 code in response to PUBLISH, adjusting the Expires value and trying to recover"),this.pubRequestExpires=n,this.emit("progress",e,i),this.publish(this.options.body)):(this.logger.warn("Bad 423 response Min-Expires header received for PUBLISH"),this.pubRequestExpires=0,this.emit("failed",e,i),this.emit("unpublished",e,i))}else this.logger.warn("423 response to PUBLISH, recovery failed"),this.pubRequestExpires=0,this.emit("failed",e,i),this.emit("unpublished",e,i);break;default:this.pubRequestExpires=0,this.emit("failed",e,i),this.emit("unpublished",e,i)}0===this.pubRequestExpires&&(this.publishRefreshTimer&&(clearTimeout(this.publishRefreshTimer),this.publishRefreshTimer=void 0),this.pubRequestBody=void 0,this.pubRequestEtag=void 0)},t.prototype.refreshRequest=function(){if(this.publishRefreshTimer&&(clearTimeout(this.publishRefreshTimer),this.publishRefreshTimer=void 0),this.pubRequestBody=void 0,void 0===this.pubRequestEtag)throw new c.Exceptions.MethodParameterError("Publish","Body",void 0);if(0===this.pubRequestExpires)throw new c.Exceptions.MethodParameterError("Publish","Expire",this.pubRequestExpires);this.sendPublishRequest()},t.prototype.sendPublishRequest=function(){var e=Object.create(this.options||Object.prototype);e.extraHeaders=(this.options.extraHeaders||[]).slice(),e.extraHeaders.push("Event: "+this.event),e.extraHeaders.push("Expires: "+this.pubRequestExpires),void 0!==this.pubRequestEtag&&e.extraHeaders.push("SIP-If-Match: "+this.pubRequestEtag),this.request=new u.OutgoingRequest(o.C.PUBLISH,this.target,this.ua,this.options.params,e.extraHeaders),void 0!==this.pubRequestBody&&(this.request.body={},this.request.body.body=this.pubRequestBody,this.request.body.contentType=this.options.contentType),this.send()},t}(n.ClientContext);t.PublishContext=d},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var o=r(7),a=r(1),c=r(0),u=r(5),h=r(4),d=r(2);function p(e){var t={expires:600,extraContactHeaderParams:[],instanceId:void 0,params:{},regId:void 0,registrar:void 0},r={mandatory:{},optional:{expires:function(e){if(d.Utils.isDecimal(e)){var t=Number(e);if(t>=0)return t}},extraContactHeaderParams:function(e){if(e instanceof Array)return e.filter(function(e){return"string"==typeof e})},instanceId:function(e){if("string"==typeof e)return/^uuid:/i.test(e)&&(e=e.substr(5)),-1===h.Grammar.parse(e,"uuid")?void 0:e},params:function(e){if("object"==typeof e)return e},regId:function(e){if(d.Utils.isDecimal(e)){var t=Number(e);if(t>=0)return t}},registrar:function(e){if("string"==typeof e){/^sip:/i.test(e)||(e=a.C.SIP+":"+e);var t=h.Grammar.URIParse(e);return t?t.user?void 0:t:void 0}}}};for(var i in r.mandatory){if(!e.hasOwnProperty(i))throw new u.Exceptions.ConfigurationError(i);var s=e[i];if(void 0===(n=r.mandatory[i](s)))throw new u.Exceptions.ConfigurationError(i,s);t[i]=n}for(var i in r.optional)if(e.hasOwnProperty(i)){var n;if((s=e[i])instanceof Array&&0===s.length)continue;if(null===s||""===s||void 0===s||"number"==typeof s&&isNaN(s))continue;if(void 0===(n=r.optional[i](s)))throw new u.Exceptions.ConfigurationError(i,s);t[i]=n}return t}var l=function(e){function t(t,r){void 0===r&&(r={});var i=this,s=p(r);if(s.regId&&!s.instanceId?s.instanceId=d.Utils.newUUID():!s.regId&&s.instanceId&&(s.regId=1),s.params.toUri=s.params.toUri||t.configuration.uri,s.params.toDisplayName=s.params.toDisplayName||t.configuration.displayName,s.params.callId=s.params.callId||d.Utils.createRandomToken(22),s.params.cseq=s.params.cseq||Math.floor(1e4*Math.random()),!s.registrar){var n={};"object"==typeof t.configuration.uri?(n=t.configuration.uri.clone()).user=void 0:n=t.configuration.uri,s.registrar=n}for(var o in(i=e.call(this,t,a.C.REGISTER,s.registrar,s)||this).type=c.TypeStrings.RegisterContext,i.options=s,i.logger=t.getLogger("sip.registercontext"),i.logger.log("configuration parameters for RegisterContext after validation:"),s)s.hasOwnProperty(o)&&i.logger.log("\xb7 "+o+": "+JSON.stringify(s[o]));return i.expires=s.expires,i.cseq=s.params.cseq,i.contact=t.contact.toString(),i.registered=!1,t.on("transportCreated",function(e){e.on("disconnected",function(){return i.onTransportDisconnected()})}),i}return s(t,e),t.prototype.register=function(e){var t=this;void 0===e&&(e={}),this.options=n({},this.options,e);var r=(this.options.extraHeaders||[]).slice();r.push("Contact: "+this.generateContactHeader(this.expires)),r.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),this.closeHeaders=this.options.closeWithHeaders?(this.options.extraHeaders||[]).slice():[],this.receiveResponse=function(e){if(e.cseq===t.cseq){void 0!==t.registrationTimer&&(clearTimeout(t.registrationTimer),t.registrationTimer=void 0);var r=(e.statusCode||0).toString();switch(!0){case/^1[0-9]{2}$/.test(r):t.emit("progress",e);break;case/^2[0-9]{2}$/.test(r):t.emit("accepted",e);var i=void 0;e.hasHeader("expires")&&(i=Number(e.getHeader("expires"))),void 0!==t.registrationExpiredTimer&&(clearTimeout(t.registrationExpiredTimer),t.registrationExpiredTimer=void 0);var s=e.getHeaders("contact").length;if(!s){t.logger.warn("no Contact header in response to REGISTER, response ignored");break}for(var n=void 0;s--;){if((n=e.parseHeader("contact",s)).uri.user===t.ua.contact.uri.user){i=n.getParam("expires");break}n=void 0}if(!n){t.logger.warn("no Contact header pointing to us, response ignored");break}void 0===i&&(i=t.expires),t.registrationTimer=setTimeout(function(){t.registrationTimer=void 0,t.register(t.options)},1e3*i-3e3),t.registrationExpiredTimer=setTimeout(function(){t.logger.warn("registration expired"),t.registered&&t.unregistered(void 0,a.C.causes.EXPIRES)},1e3*i),n.hasParam("temp-gruu")&&(t.ua.contact.temp_gruu=h.Grammar.URIParse(n.getParam("temp-gruu").replace(/"/g,""))),n.hasParam("pub-gruu")&&(t.ua.contact.pub_gruu=h.Grammar.URIParse(n.getParam("pub-gruu").replace(/"/g,""))),t.registered=!0,t.emit("registered",e||void 0);break;case/^423$/.test(r):e.hasHeader("min-expires")?(t.expires=Number(e.getHeader("min-expires")),t.register(t.options)):(t.logger.warn("423 response received for REGISTER without Min-Expires"),t.registrationFailure(e,a.C.causes.SIP_FAILURE_CODE));break;default:t.registrationFailure(e,d.Utils.sipErrorCause(e.statusCode||0))}}},this.onRequestTimeout=function(){t.registrationFailure(void 0,a.C.causes.REQUEST_TIMEOUT)},this.onTransportError=function(){t.registrationFailure(void 0,a.C.causes.CONNECTION_ERROR)},this.cseq++,this.request&&(this.request.cseq=this.cseq,this.request.setHeader("cseq",this.cseq+" REGISTER"),this.request.extraHeaders=r),this.send()},t.prototype.close=function(){var e={all:!1,extraHeaders:this.closeHeaders};this.registeredBefore=this.registered,this.registered&&this.unregister(e)},t.prototype.unregister=function(e){var t=this;void 0===e&&(e={}),this.registered||e.all||this.logger.warn("Already unregistered, but sending an unregister anyways.");var r=(e.extraHeaders||[]).slice();this.registered=!1,void 0!==this.registrationTimer&&(clearTimeout(this.registrationTimer),this.registrationTimer=void 0),e.all?(r.push("Contact: *"),r.push("Expires: 0")):r.push("Contact: "+this.generateContactHeader(0)),this.receiveResponse=function(e){var r=e&&e.statusCode?e.statusCode.toString():"";switch(!0){case/^1[0-9]{2}$/.test(r):t.emit("progress",e);break;case/^2[0-9]{2}$/.test(r):t.emit("accepted",e),void 0!==t.registrationExpiredTimer&&(clearTimeout(t.registrationExpiredTimer),t.registrationExpiredTimer=void 0),t.unregistered(e);break;default:t.unregistered(e,d.Utils.sipErrorCause(e.statusCode||0))}},this.onRequestTimeout=function(){},this.cseq++,this.request&&(this.request.cseq=this.cseq,this.request.setHeader("cseq",this.cseq+" REGISTER"),this.request.extraHeaders=r),this.send()},t.prototype.unregistered=function(e,t){this.registered=!1,this.emit("unregistered",e||void 0,t||void 0)},t.prototype.registrationFailure=function(e,t){this.emit("failed",e||void 0,t||void 0)},t.prototype.onTransportDisconnected=function(){this.registeredBefore=this.registered,void 0!==this.registrationTimer&&(clearTimeout(this.registrationTimer),this.registrationTimer=void 0),void 0!==this.registrationExpiredTimer&&(clearTimeout(this.registrationExpiredTimer),this.registrationExpiredTimer=void 0),this.registered&&this.unregistered(void 0,a.C.causes.CONNECTION_ERROR)},t.prototype.generateContactHeader=function(e){void 0===e&&(e=0);var t=this.contact;return this.options.regId&&this.options.instanceId&&(t+=";reg-id="+this.options.regId,t+=';+sip.instance="<urn:uuid:'+this.options.instanceId+'>"'),this.options.extraContactHeaderParams&&this.options.extraContactHeaderParams.forEach(function(e){t+=";"+e}),t+=";expires="+e},t}(o.ClientContext);t.RegisterContext=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(1),s=r(0),n=r(2);!function(e){function t(e,t,r){for(var i=n.Utils.buildStatusLine(e),s=0,o=t.getHeaders("via");s<o.length;s++){i+="Via: "+o[s]+"\r\n"}var a=t.getHeader("To")||"";t.toTag||(a+=";tag="+n.Utils.newTag()),i+="To: "+a+"\r\n",i+="From: "+t.getHeader("From")+"\r\n",i+="Call-ID: "+t.callId+"\r\n",i+="CSeq: "+t.cseq+" "+t.method+"\r\n",i+="\r\n",r.send(i)}function r(e,r,i){return!(!e.ruri||"sip"!==e.ruri.scheme)||(t(416,e,i),!1)}function o(e,r,i){return!(!e.toTag&&e.callId.substr(0,5)===r.configuration.sipjsId)||(t(482,e,i),!1)}function a(e,r,i){var s=n.Utils.str_utf8_length(e.body),o=e.getHeader("content-length");return!(o&&s<Number(o))||(t(400,e,i),!1)}function c(e,r,s){var n=e.fromTag,o=e.callId,a=e.cseq;if(!e.toTag)if(e.method===i.C.INVITE){if(r.transactions.ist[e.viaBranch])return!0;for(var c in r.transactions.ist){if(r.transactions.ist.hasOwnProperty(c))if((u=r.transactions.ist[c]).request.fromTag===n&&u.request.callId===o&&u.request.cseq===a)return t(482,e,s),!1}}else{if(r.transactions.nist[e.viaBranch])return!0;for(var c in r.transactions.nist){var u;if(r.transactions.nist.hasOwnProperty(c))if((u=r.transactions.nist[c]).request.fromTag===n&&u.request.callId===o&&u.request.cseq===a)return t(482,e,s),!1}}return!0}function u(e,t){return!(e.getHeaders("via").length>1)||(t.getLogger("sip.sanitycheck").warn("More than one Via header field present in the response. Dropping the response"),!1)}function h(e,t){return e.via.host===t.configuration.viaHost&&void 0===e.via.port||(t.getLogger("sip.sanitycheck").warn("Via sent-by in the response does not match UA Via host value. Dropping the response"),!1)}function d(e,t){var r=n.Utils.str_utf8_length(e.body),i=e.getHeader("content-length");return!(i&&r<Number(i))||(t.getLogger("sip.sanitycheck").warn("Message body length is lower than the value in Content-Length header field. Dropping the response"),!1)}function p(e,t){for(var r=0,i=["from","to","call_id","cseq","via"];r<i.length;r++){var s=i[r];if(!e.hasHeader(s))return t.getLogger("sip.sanitycheck").warn("Missing mandatory header field : "+s+". Dropping the response"),!1}return!0}e.reply=t,e.rfc3261_8_2_2_1=r,e.rfc3261_16_3_4=o,e.rfc3261_18_3_request=a,e.rfc3261_8_2_2_2=c,e.rfc3261_8_1_3_3=u,e.rfc3261_18_1_2=h,e.rfc3261_18_3_response=d,e.minimumHeaders=p,e.sanityCheck=function(e,t,i){for(var n=[r,o,a,c],l=[u,h,d],f=0,g=[p];f<g.length;f++)if(!(0,g[f])(e,t,i))return!1;if(e.type===s.TypeStrings.IncomingRequest){for(var T=0,v=n;T<v.length;T++)if(!(0,v[T])(e,t,i))return!1}else if(e.type===s.TypeStrings.IncomingResponse)for(var m=0,S=l;m<S.length;m++)if(!(0,S[m])(e,t,i))return!1;return!0}}(t.SanityCheck||(t.SanityCheck={}))},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(7),a=r(1),c=r(13),u=r(0),h=r(5),d=r(4),p=r(8),l=r(14),f=r(34),g=r(6),T=r(11),v=r(2),m=function(e){function t(r){var i=e.call(this)||this;if(i.data={},i.type=u.TypeStrings.Session,!r)throw new h.Exceptions.SessionDescriptionHandlerError("A session description handler is required for the session to function");return i.status=t.C.STATUS_NULL,i.dialog=void 0,i.pendingReinvite=!1,i.earlyDialogs={},i.sessionDescriptionHandlerFactory=r,i.hasOffer=!1,i.hasAnswer=!1,i.timers={ackTimer:void 0,expiresTimer:void 0,invite2xxTimer:void 0,userNoAnswerTimer:void 0,rel1xxTimer:void 0,prackTimer:void 0},i.startTime=void 0,i.endTime=void 0,i.tones=void 0,i.localHold=!1,i.earlySdp=void 0,i.rel100=a.C.supported.UNSUPPORTED,i.originalReceiveRequest=i.receiveRequest,i}return s(t,e),t.prototype.dtmf=function(e,t){var r=this;if(void 0===t&&(t={}),this.status!==u.SessionStatus.STATUS_CONFIRMED&&this.status!==u.SessionStatus.STATUS_WAITING_FOR_ACK)throw new h.Exceptions.InvalidStateError(this.status);if(!e||!e.toString().match(/^[0-9A-D#*,]+$/i))throw new TypeError("Invalid tones: "+e);var i=function(){if(r.status!==u.SessionStatus.STATUS_TERMINATED&&r.tones&&0!==r.tones.length){var e,s=r.tones.shift();","===s.tone?e=2e3:(s.on("failed",function(){r.tones=void 0}),s.send(t),e=s.duration+s.interToneGap),setTimeout(i,e)}else r.tones=void 0};e=e.toString();var s=this.ua.configuration.dtmfType;this.sessionDescriptionHandler&&s===a.C.dtmfType.RTP&&(this.sessionDescriptionHandler.sendDtmf(e,t)||(this.logger.warn("Attempt to use dtmfType 'RTP' has failed, falling back to INFO packet method"),s=a.C.dtmfType.INFO));if(s===a.C.dtmfType.INFO){for(var n=[],o=e.split("");o.length>0;)n.push(new f.DTMF(this,o.shift(),t));if(this.tones)return this.tones=this.tones.concat(n),this;this.tones=n,i()}return this},t.prototype.bye=function(e){if(void 0===e&&(e={}),this.status===u.SessionStatus.STATUS_TERMINATED)return this.logger.error("Error: Attempted to send BYE in a terminated session."),this;this.logger.log("terminating Session");var t=e.statusCode;if(t&&(t<200||t>=700))throw new TypeError("Invalid statusCode: "+t);return e.receiveResponse=function(){},this.sendRequest(a.C.BYE,e).terminated()},t.prototype.refer=function(e,t){if(void 0===t&&(t={}),this.status!==u.SessionStatus.STATUS_CONFIRMED)throw new h.Exceptions.InvalidStateError(this.status);return this.referContext=new y(this.ua,this,e,t),this.emit("referRequested",this.referContext),this.referContext.refer(t),this.referContext},t.prototype.sendRequest=function(e,t){var r=this;if(void 0===t&&(t={}),t=t||{},!this.dialog)throw new Error("sending request without a dialog");var i=new g.OutgoingRequest(e,this.dialog.remoteTarget,this.ua,{cseq:t.cseq||(this.dialog.localSeqnum+=1),callId:this.dialog.id.callId,fromUri:this.dialog.localUri,fromTag:this.dialog.id.localTag,ToUri:this.dialog.remoteUri,toTag:this.dialog.id.remoteTag,routeSet:this.dialog.routeSet,statusCode:t.statusCode,reasonPhrase:t.reasonPhrase},t.extraHeaders||[],t.body);return new p.RequestSender({request:i,onRequestTimeout:function(){return r.onRequestTimeout()},onTransportError:function(){return r.onTransportError()},receiveResponse:function(e){return(t.receiveResponse||r.receiveNonInviteResponse.bind(r))(e)}},this.ua).send(),this.emit(e.toLowerCase(),i),this},t.prototype.close=function(){if(this.status===u.SessionStatus.STATUS_TERMINATED)return this;for(var e in this.logger.log("closing INVITE session "+this.id),this.sessionDescriptionHandler&&this.sessionDescriptionHandler.close(),this.timers)this.timers[e]&&clearTimeout(this.timers[e]);for(var t in this.dialog&&(this.dialog.terminate(),delete this.dialog),this.earlyDialogs)this.earlyDialogs.hasOwnProperty(t)&&(this.earlyDialogs[t].terminate(),delete this.earlyDialogs[t]);return this.status=u.SessionStatus.STATUS_TERMINATED,this.ua.transport&&this.ua.transport.removeListener("transportError",this.errorListener),delete this.ua.sessions[this.id],this},t.prototype.createDialog=function(e,t,r){void 0===r&&(r=!1);var i,s=e["UAS"===t?"toTag":"fromTag"],n=e["UAS"===t?"fromTag":"toTag"],o=e.callId+s+n;if(r)return!!this.earlyDialogs[o]||((i=new c.Dialog(this,e,t,c.Dialog.C.STATUS_EARLY)).error?(this.logger.error(i.error),this.failed(e,a.C.causes.INTERNAL_ERROR),!1):(this.earlyDialogs[o]=i,!0));if(i=this.earlyDialogs[o]){for(var u in i.update(e,t),this.dialog=i,delete this.earlyDialogs[o],this.earlyDialogs)this.earlyDialogs.hasOwnProperty(u)&&(this.earlyDialogs[u].terminate(),delete this.earlyDialogs[u]);return!0}var h=new c.Dialog(this,e,t);return h.error?(this.logger.error(h.error),this.failed(e,a.C.causes.INTERNAL_ERROR),!1):(this.toTag=e.toTag,this.dialog=h,!0)},t.prototype.hold=function(e,t){if(void 0===e&&(e={}),void 0===t&&(t=[]),this.status!==u.SessionStatus.STATUS_WAITING_FOR_ACK&&this.status!==u.SessionStatus.STATUS_CONFIRMED)throw new h.Exceptions.InvalidStateError(this.status);this.localHold?this.logger.log("Session is already on hold, cannot put it on hold again"):(e.modifiers=t,this.sessionDescriptionHandler&&e.modifiers.push(this.sessionDescriptionHandler.holdModifier),this.localHold=!0,this.sendReinvite(e))},t.prototype.unhold=function(e,t){if(void 0===e&&(e={}),void 0===t&&(t=[]),this.status!==u.SessionStatus.STATUS_WAITING_FOR_ACK&&this.status!==u.SessionStatus.STATUS_CONFIRMED)throw new h.Exceptions.InvalidStateError(this.status);this.localHold?(e.modifiers=t,this.localHold=!1,this.sendReinvite(e)):this.logger.log("Session is not on hold, cannot unhold it")},t.prototype.reinvite=function(e,t){return void 0===e&&(e={}),void 0===t&&(t=[]),e.modifiers=t,this.sendReinvite(e)},t.prototype.receiveRequest=function(e){switch(e.method){case a.C.BYE:e.reply(200),this.status===u.SessionStatus.STATUS_CONFIRMED&&(this.emit("bye",e),this.terminated(e,a.C.BYE));break;case a.C.INVITE:this.status===u.SessionStatus.STATUS_CONFIRMED&&(this.logger.log("re-INVITE received"),this.receiveReinvite(e));break;case a.C.INFO:if(this.status===u.SessionStatus.STATUS_CONFIRMED||this.status===u.SessionStatus.STATUS_WAITING_FOR_ACK){if(this.onInfo)return this.onInfo(e);var t=e.getHeader("content-type");if(t)if(t.match(/^application\/dtmf-relay/i)){if(e.body){var r=e.body.split("\r\n",2);if(2===r.length){var i=void 0,s=void 0,n=/^(Signal\s*?=\s*?)([0-9A-D#*]{1})(\s)?.*/;n.test(r[0])&&(i=r[0].replace(n,"$2"));var o=/^(Duration\s?=\s?)([0-9]{1,4})(\s)?.*/;o.test(r[1])&&(s=parseInt(r[1].replace(o,"$2"),10)),i&&s&&new f.DTMF(this,i,{duration:s}).init_incoming(e)}}}else e.reply(415,void 0,["Accept: application/dtmf-relay"])}break;case a.C.REFER:if(this.status===u.SessionStatus.STATUS_CONFIRMED)if(this.logger.log("REFER received"),this.referContext=new A(this.ua,e),this.listeners("referRequested").length)this.emit("referRequested",this.referContext);else{this.logger.log("No referRequested listeners, automatically accepting and following the refer");var c={followRefer:!0};this.passedOptions&&(c.inviteOptions=this.passedOptions),this.referContext.accept(c,this.modifiers)}break;case a.C.NOTIFY:if(this.referContext&&this.referContext.type===u.TypeStrings.ReferClientContext&&e.hasHeader("event")&&/^refer(;.*)?$/.test(e.getHeader("event")))return void this.referContext.receiveNotify(e);e.reply(200,"OK"),this.emit("notify",e)}},t.prototype.terminate=function(e){return this},t.prototype.onTransportError=function(){this.status!==u.SessionStatus.STATUS_CONFIRMED&&this.status!==u.SessionStatus.STATUS_TERMINATED&&this.failed(void 0,a.C.causes.CONNECTION_ERROR)},t.prototype.onRequestTimeout=function(){this.status===u.SessionStatus.STATUS_CONFIRMED?this.terminated(void 0,a.C.causes.REQUEST_TIMEOUT):this.status!==u.SessionStatus.STATUS_TERMINATED&&(this.failed(void 0,a.C.causes.REQUEST_TIMEOUT),this.terminated(void 0,a.C.causes.REQUEST_TIMEOUT))},t.prototype.onDialogError=function(e){this.status===u.SessionStatus.STATUS_CONFIRMED?this.terminated(e,a.C.causes.DIALOG_ERROR):this.status!==u.SessionStatus.STATUS_TERMINATED&&(this.failed(e,a.C.causes.DIALOG_ERROR),this.terminated(e,a.C.causes.DIALOG_ERROR))},t.prototype.receiveReinvite=function(e){var t,r=this;if(this.emit("reinvite",this,e),e.hasHeader("P-Asserted-Identity")&&(this.assertedIdentity=d.Grammar.nameAddrHeaderParse(e.getHeader("P-Asserted-Identity"))),this.sessionDescriptionHandler){if("0"!==e.getHeader("Content-Length")||e.getHeader("Content-Type")){if(!this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||""))return e.reply(415),void this.emit("reinviteFailed",this);t=this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).then(this.sessionDescriptionHandler.getDescription.bind(this.sessionDescriptionHandler,this.sessionDescriptionHandlerOptions,this.modifiers))}else t=this.sessionDescriptionHandler.getDescription(this.sessionDescriptionHandlerOptions,this.modifiers);this.receiveRequest=function(e){e.method===a.C.ACK&&r.status===u.SessionStatus.STATUS_WAITING_FOR_ACK?r.sessionDescriptionHandler&&r.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||"")?(r.hasAnswer=!0,r.sessionDescriptionHandler.setDescription(e.body,r.sessionDescriptionHandlerOptions,r.modifiers).then(function(){clearTimeout(r.timers.ackTimer),clearTimeout(r.timers.invite2xxTimer),r.status=u.SessionStatus.STATUS_CONFIRMED,r.emit("confirmed",e)})):(clearTimeout(r.timers.ackTimer),clearTimeout(r.timers.invite2xxTimer),r.status=u.SessionStatus.STATUS_CONFIRMED,r.emit("confirmed",e)):r.originalReceiveRequest(e)},t.catch(function(t){var i;throw t.type===u.TypeStrings.SessionDescriptionHandlerError?i=500:t.type===u.TypeStrings.RenegotiationError?(r.emit("renegotiationError",t),r.logger.warn(t.toString()),i=488):(r.logger.error(t),i=488),e.reply(i),r.emit("reinviteFailed",r),t}).then(function(t){var i=["Contact: "+r.contact];e.reply(200,void 0,i,t,function(){r.status=u.SessionStatus.STATUS_WAITING_FOR_ACK,r.setACKTimer(),r.emit("reinviteAccepted",r)})})}else this.logger.warn("No SessionDescriptionHandler to reinvite")},t.prototype.sendReinvite=function(e){var t=this;if(void 0===e&&(e={}),this.pendingReinvite)this.logger.warn("Reinvite in progress. Please wait until complete, then try again.");else if(this.sessionDescriptionHandler){this.pendingReinvite=!0,e.modifiers=e.modifiers||[];var r=(e.extraHeaders||[]).slice();r.push("Contact: "+this.contact),r.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),this.sessionDescriptionHandler.getDescription(e.sessionDescriptionHandlerOptions,e.modifiers).then(function(e){t.sendRequest(a.C.INVITE,{extraHeaders:r,body:e,receiveResponse:function(e){return t.receiveReinviteResponse(e)}})}).catch(function(e){if(e.type===u.TypeStrings.RenegotiationError)throw t.pendingReinvite=!1,t.emit("renegotiationError",e),t.logger.warn("Renegotiation Error"),t.logger.warn(e.toString()),e;throw t.logger.error("sessionDescriptionHandler error"),t.logger.error(e),e})}else this.logger.warn("No SessionDescriptionHandler, can't reinvite..")},t.prototype.receiveReinviteResponse=function(e){var t=this;if(this.status!==u.SessionStatus.STATUS_TERMINATED)if(this.pendingReinvite){var r=e&&e.statusCode?e.statusCode.toString():"";switch(!0){case/^1[0-9]{2}$/.test(r):break;case/^2[0-9]{2}$/.test(r):if(this.status=u.SessionStatus.STATUS_CONFIRMED,this.emit("ack",e.transaction.sendACK()),this.pendingReinvite=!1,clearTimeout(this.timers.invite2xxTimer),!this.sessionDescriptionHandler||!this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||"")){this.logger.error("2XX response received to re-invite but did not have a description"),this.emit("reinviteFailed",this),this.emit("renegotiationError",new h.Exceptions.RenegotiationError("2XX response received to re-invite but did not have a description"));break}this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).catch(function(e){throw t.logger.error("Could not set the description in 2XX response"),t.logger.error(e),t.emit("reinviteFailed",t),t.emit("renegotiationError",e),t.sendRequest(a.C.BYE,{extraHeaders:["Reason: "+v.Utils.getReasonHeaderValue(488,"Not Acceptable Here")]}),t.terminated(void 0,a.C.causes.INCOMPATIBLE_SDP),e}).then(function(){t.emit("reinviteAccepted",t)});break;default:this.pendingReinvite=!1,this.logger.log("Received a non 1XX or 2XX response to a re-invite"),this.emit("reinviteFailed",this),this.emit("renegotiationError",new h.Exceptions.RenegotiationError("Invalid response to a re-invite"))}}else this.logger.error("Received reinvite response, but have no pending reinvite");else this.logger.error("Received reinvite response, but in STATUS_TERMINATED")},t.prototype.acceptAndTerminate=function(e,t,r){var i=[];return t&&i.push("Reason: "+v.Utils.getReasonHeaderValue(t,r)),(this.dialog||this.createDialog(e,"UAC"))&&(this.emit("ack",e.transaction.sendACK()),this.sendRequest(a.C.BYE,{extraHeaders:i})),this},t.prototype.setInvite2xxTimer=function(e,t){var r=this,i=T.Timers.T1,s=function(){if(r.status===u.SessionStatus.STATUS_WAITING_FOR_ACK){r.logger.log("no ACK received, attempting to retransmit OK");var n=["Contact: "+r.contact];e.reply(200,void 0,n,t),i=Math.min(2*i,T.Timers.T2),r.timers.invite2xxTimer=setTimeout(s,i)}};this.timers.invite2xxTimer=setTimeout(s,i)},t.prototype.setACKTimer=function(){var e=this;this.timers.ackTimer=setTimeout(function(){e.status===u.SessionStatus.STATUS_WAITING_FOR_ACK&&(e.logger.log("no ACK received for an extended period of time, terminating the call"),clearTimeout(e.timers.invite2xxTimer),e.sendRequest(a.C.BYE),e.terminated(void 0,a.C.causes.NO_ACK))},T.Timers.TIMER_H)},t.prototype.failed=function(e,t){return this.status===u.SessionStatus.STATUS_TERMINATED?this:(this.emit("failed",e,t),this)},t.prototype.rejected=function(e,t){return this.emit("rejected",e,t),this},t.prototype.canceled=function(){return this.sessionDescriptionHandler&&this.sessionDescriptionHandler.close(),this.emit("cancel"),this},t.prototype.accepted=function(e,t){return e instanceof String||(t=v.Utils.getReasonPhrase(e&&e.statusCode||0,t)),this.startTime=new Date,this.replacee&&(this.replacee.emit("replaced",this),this.replacee.terminate()),this.emit("accepted",e,t),this},t.prototype.terminated=function(e,t){return this.status===u.SessionStatus.STATUS_TERMINATED?this:(this.endTime=new Date,this.close(),this.emit("terminated",e,t),this)},t.prototype.connecting=function(e){return this.emit("connecting",{request:e}),this},t.prototype.receiveNonInviteResponse=function(e){},t.C=u.SessionStatus,t}(n.EventEmitter);t.Session=m;var S=function(e){function t(t,r){var i=this;if(!t.configuration.sessionDescriptionHandlerFactory)throw t.logger.warn("Can't build ISC without SDH Factory"),new Error("ISC Constructor Failed");i=e.call(this,t.configuration.sessionDescriptionHandlerFactory)||this,l.ServerContext.initializer(i,t,r),i.type=u.TypeStrings.InviteServerContext;var s=r.parseHeader("Content-Disposition");s&&"render"===s.type&&(i.renderbody=r.body,i.rendertype=r.getHeader("Content-Type")),i.status=u.SessionStatus.STATUS_INVITE_RECEIVED,i.fromTag=r.fromTag,i.id=r.callId+i.fromTag,i.request=r,i.contact=i.ua.contact.toString(),i.receiveNonInviteResponse=function(){},i.logger=t.getLogger("sip.inviteservercontext",i.id),i.ua.sessions[i.id]=i;var n=function(e,t){r.hasHeader(e)&&r.getHeader(e).toLowerCase().indexOf("100rel")>=0&&(i.rel100=t)};if(n("require",a.C.supported.REQUIRED),n("supported",a.C.supported.SUPPORTED),r.toTag=v.Utils.newTag(),i.createDialog(r,"UAS",!0)){var o={extraHeaders:["Contact: "+i.contact]};if(i.rel100!==a.C.supported.REQUIRED&&i.progress(o),i.status=u.SessionStatus.STATUS_WAITING_FOR_ANSWER,i.timers.userNoAnswerTimer=setTimeout(function(){r.reply(408),i.failed(r,a.C.causes.NO_ANSWER),i.terminated(r,a.C.causes.NO_ANSWER)},i.ua.configuration.noAnswerTimeout||60),r.hasHeader("expires")){var c=1e3*Number(r.getHeader("expires")||0);i.timers.expiresTimer=setTimeout(function(){i.status===u.SessionStatus.STATUS_WAITING_FOR_ANSWER&&(r.reply(487),i.failed(r,a.C.causes.EXPIRES),i.terminated(r,a.C.causes.EXPIRES))},c)}return i.errorListener=i.onTransportError.bind(i),t.transport&&t.transport.on("transportError",i.errorListener),i}r.reply(500,"Missing Contact header field")}return s(t,e),t.prototype.reject=function(e){var t=this;if(void 0===e&&(e={}),this.status===u.SessionStatus.STATUS_TERMINATED)throw new h.Exceptions.InvalidStateError(this.status);this.logger.log("rejecting RTCSession");var r=e.statusCode||480,i=v.Utils.getReasonPhrase(r,e.reasonPhrase),s=e.extraHeaders||[];if(r<300||r>699)throw new TypeError("Invalid statusCode: "+r);var n=this.request.reply(r,i,s,e.body);return["rejected","failed"].forEach(function(e){t.emit(e,n,i)}),this.terminated()},t.prototype.reply=function(e){return void 0===e&&(e={}),this},t.prototype.terminate=function(e){var t=this;void 0===e&&(e={});var r=(e.extraHeaders||[]).slice();if(this.status===u.SessionStatus.STATUS_WAITING_FOR_ACK&&this.request.serverTransaction&&this.request.serverTransaction.state!==u.TransactionStatus.STATUS_TERMINATED){var i=this.dialog;this.receiveRequest=function(e){e.method===a.C.ACK&&(t.sendRequest(a.C.BYE,{extraHeaders:r}),t.dialog&&t.dialog.terminate())},this.request.serverTransaction.on("stateChanged",function(){t.request.serverTransaction&&t.request.serverTransaction.state===u.TransactionStatus.STATUS_TERMINATED&&t.dialog&&(t.bye(),t.dialog.terminate())}),this.emit("bye",this.request),this.terminated(),this.dialog=i,this.dialog&&(this.ua.dialogs[this.dialog.id.toString()]=this.dialog)}else this.status===u.SessionStatus.STATUS_CONFIRMED?this.bye(e):this.reject(e);return this},t.prototype.progress=function(e){var t=this;void 0===e&&(e={});var r=e.statusCode||180,i=(e.extraHeaders||[]).slice();if(r<100||r>199)throw new TypeError("Invalid statusCode: "+r);if(this.status===u.SessionStatus.STATUS_TERMINATED)return this;var s,n=function(){var r=e.statusCode||183;t.status=u.SessionStatus.STATUS_WAITING_FOR_PRACK,i.push("Contact: "+t.contact),i.push("Require: 100rel"),i.push("RSeq: "+Math.floor(1e4*Math.random())),t.sessionDescriptionHandler?t.sessionDescriptionHandler.getDescription(e.sessionDescriptionHandlerOptions,e.modifiers).then(function(s){if(t.status!==u.SessionStatus.STATUS_TERMINATED){t.earlySdp=s.body,t[t.hasOffer?"hasAnswer":"hasOffer"]=!0;var n=T.Timers.T1,o=function(){t.request.reply(r,void 0,i,s),n*=2,t.timers.rel1xxTimer=setTimeout(o,n)};t.timers.rel1xxTimer=setTimeout(o,n),t.timers.prackTimer=setTimeout(function(){t.status===u.SessionStatus.STATUS_WAITING_FOR_PRACK&&(t.logger.log("no PRACK received, rejecting the call"),clearTimeout(t.timers.rel1xxTimer),t.request.reply(504),t.terminated(void 0,a.C.causes.NO_PRACK))},64*T.Timers.T1);var c=t.request.reply(r,e.reasonPhrase,i,s);t.emit("progress",c,e.reasonPhrase)}},function(){t.request.reply(480),t.failed(void 0,a.C.causes.WEBRTC_ERROR),t.terminated(void 0,a.C.causes.WEBRTC_ERROR)}):t.logger.warn("No SessionDescriptionHandler, can't do 100rel")};return 100!==e.statusCode&&(this.rel100===a.C.supported.REQUIRED||this.rel100===a.C.supported.SUPPORTED&&e.rel100||this.rel100===a.C.supported.SUPPORTED&&this.ua.configuration.rel100===a.C.supported.REQUIRED)?(this.sessionDescriptionHandler=this.setupSessionDescriptionHandler(),this.emit("SessionDescriptionHandler-created",this.sessionDescriptionHandler),this.sessionDescriptionHandler.hasDescription(this.request.getHeader("Content-Type")||"")?(this.hasOffer=!0,this.sessionDescriptionHandler.setDescription(this.request.body,e.sessionDescriptionHandlerOptions,e.modifiers).then(n).catch(function(e){throw t.logger.warn("invalid description"),t.logger.warn(e),t.failed(void 0,a.C.causes.WEBRTC_ERROR),t.terminated(void 0,a.C.causes.WEBRTC_ERROR),e})):n()):(s=t.request.reply(r,e.reasonPhrase,i,e.body),t.emit("progress",s,e.reasonPhrase)),this},t.prototype.accept=function(e){var t=this;void 0===e&&(e={}),this.onInfo=e.onInfo;var r=(e.extraHeaders||[]).slice(),i=function(e){r.push("Contact: "+t.contact),r.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),t.hasOffer?t.hasAnswer=!0:t.hasOffer=!0;var i=t.request.reply(200,void 0,r,e,function(){t.status=u.SessionStatus.STATUS_WAITING_FOR_ACK,t.setInvite2xxTimer(t.request,e),t.setACKTimer()},function(){t.failed(void 0,a.C.causes.CONNECTION_ERROR),t.terminated(void 0,a.C.causes.CONNECTION_ERROR)});t.status!==u.SessionStatus.STATUS_TERMINATED&&t.accepted(i,v.Utils.getReasonPhrase(200))},s=function(e){throw e.type===u.TypeStrings.SessionDescriptionHandlerError&&(t.logger.log(e.message),e.error&&t.logger.log(e.error)),t.request.reply(480),t.failed(void 0,a.C.causes.WEBRTC_ERROR),t.terminated(void 0,a.C.causes.WEBRTC_ERROR),e};if(this.status===u.SessionStatus.STATUS_WAITING_FOR_PRACK)return this.status=u.SessionStatus.STATUS_ANSWERED_WAITING_FOR_PRACK,this;if(this.status===u.SessionStatus.STATUS_WAITING_FOR_ANSWER)this.status=u.SessionStatus.STATUS_ANSWERED;else if(this.status!==u.SessionStatus.STATUS_EARLY_MEDIA)throw new h.Exceptions.InvalidStateError(this.status);if(!this.createDialog(this.request,"UAS"))return this.request.reply(500,"Missing Contact header field"),this;if(clearTimeout(this.timers.userNoAnswerTimer),this.status===u.SessionStatus.STATUS_EARLY_MEDIA)i({});else if(this.sessionDescriptionHandler=this.setupSessionDescriptionHandler(),this.emit("SessionDescriptionHandler-created",this.sessionDescriptionHandler),"0"!==this.request.getHeader("Content-Length")||this.request.getHeader("Content-Type")){if(!this.sessionDescriptionHandler.hasDescription(this.request.getHeader("Content-Type")||""))return this.request.reply(415),this;this.hasOffer=!0,this.sessionDescriptionHandler.setDescription(this.request.body,e.sessionDescriptionHandlerOptions,e.modifiers).then(function(){if(!t.sessionDescriptionHandler)throw new Error("No SDH");return t.sessionDescriptionHandler.getDescription(e.sessionDescriptionHandlerOptions,e.modifiers)}).catch(s).then(i)}else this.sessionDescriptionHandler.getDescription(e.sessionDescriptionHandlerOptions,e.modifiers).catch(s).then(i);return this},t.prototype.receiveRequest=function(e){var t=this,r=function(){clearTimeout(t.timers.ackTimer),clearTimeout(t.timers.invite2xxTimer),t.status=u.SessionStatus.STATUS_CONFIRMED;var r=e.getHeader("Content-Disposition");r&&"render"===r.type&&(t.renderbody=e.body,t.rendertype=e.getHeader("Content-Type")),t.emit("confirmed",e)};switch(e.method){case a.C.CANCEL:this.status!==u.SessionStatus.STATUS_WAITING_FOR_ANSWER&&this.status!==u.SessionStatus.STATUS_WAITING_FOR_PRACK&&this.status!==u.SessionStatus.STATUS_ANSWERED_WAITING_FOR_PRACK&&this.status!==u.SessionStatus.STATUS_EARLY_MEDIA&&this.status!==u.SessionStatus.STATUS_ANSWERED||(this.status=u.SessionStatus.STATUS_CANCELED,this.request.reply(487),this.canceled(),this.rejected(e,a.C.causes.CANCELED),this.failed(e,a.C.causes.CANCELED),this.terminated(e,a.C.causes.CANCELED));break;case a.C.ACK:this.status===u.SessionStatus.STATUS_WAITING_FOR_ACK&&(this.status=u.SessionStatus.STATUS_CONFIRMED,this.sessionDescriptionHandler&&this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||"")?(this.hasAnswer=!0,this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).catch(function(r){throw t.logger.warn(r),t.terminate({statusCode:"488",reasonPhrase:"Bad Media Description"}),t.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION),t.terminated(e,a.C.causes.BAD_MEDIA_DESCRIPTION),r}).then(function(){return r()})):r());break;case a.C.PRACK:this.status===u.SessionStatus.STATUS_WAITING_FOR_PRACK||this.status===u.SessionStatus.STATUS_ANSWERED_WAITING_FOR_PRACK?this.hasAnswer?(clearTimeout(this.timers.rel1xxTimer),clearTimeout(this.timers.prackTimer),e.reply(200),this.status===u.SessionStatus.STATUS_ANSWERED_WAITING_FOR_PRACK&&(this.status=u.SessionStatus.STATUS_EARLY_MEDIA,this.accept()),this.status=u.SessionStatus.STATUS_EARLY_MEDIA):(this.sessionDescriptionHandler=this.setupSessionDescriptionHandler(),this.emit("SessionDescriptionHandler-created",this.sessionDescriptionHandler),this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||"")?(this.hasAnswer=!0,this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).then(function(){clearTimeout(t.timers.rel1xxTimer),clearTimeout(t.timers.prackTimer),e.reply(200),t.status===u.SessionStatus.STATUS_ANSWERED_WAITING_FOR_PRACK&&(t.status=u.SessionStatus.STATUS_EARLY_MEDIA,t.accept()),t.status=u.SessionStatus.STATUS_EARLY_MEDIA},function(r){t.logger.warn(r),t.terminate({statusCode:"488",reasonPhrase:"Bad Media Description"}),t.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION),t.terminated(e,a.C.causes.BAD_MEDIA_DESCRIPTION)})):(this.terminate({statusCode:"488",reasonPhrase:"Bad Media Description"}),this.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION),this.terminated(e,a.C.causes.BAD_MEDIA_DESCRIPTION))):this.status===u.SessionStatus.STATUS_EARLY_MEDIA&&e.reply(200);break;default:m.prototype.receiveRequest.apply(this,[e])}},t.prototype.setupSessionDescriptionHandler=function(){return this.sessionDescriptionHandler?this.sessionDescriptionHandler:this.sessionDescriptionHandlerFactory(this,this.ua.configuration.sessionDescriptionHandlerFactoryOptions)},t}(m);t.InviteServerContext=S;var C=function(e){function t(t,r,i,s){void 0===i&&(i={}),void 0===s&&(s=[]);var n=this;if(!t.configuration.sessionDescriptionHandlerFactory)throw t.logger.warn("Can't build ISC without SDH Factory"),new Error("ICC Constructor Failed");i.params=i.params||{};var c=i.anonymous||!1,d=v.Utils.newTag();i.params.fromTag=d;var p=t.contact.toString({anonymous:c,outbound:c?!t.contact.temp_gruu:!t.contact.pub_gruu}),l=(i.extraHeaders||[]).slice();if(c&&t.configuration.uri&&(i.params.from_displayName="Anonymous",i.params.from_uri="sip:<EMAIL>",l.push("P-Preferred-Identity: "+t.configuration.uri.toString()),l.push("Privacy: id")),l.push("Contact: "+p),l.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),t.configuration.rel100===a.C.supported.REQUIRED&&l.push("Require: 100rel"),t.configuration.replaces===a.C.supported.REQUIRED&&l.push("Require: replaces"),i.extraHeaders=l,n=e.call(this,t.configuration.sessionDescriptionHandlerFactory)||this,o.ClientContext.initializer(n,t,a.C.INVITE,r,i),n.type=u.TypeStrings.InviteClientContext,n.passedOptions=i,n.sessionDescriptionHandlerOptions=i.sessionDescriptionHandlerOptions||{},n.modifiers=s,n.inviteWithoutSdp=i.inviteWithoutSdp||!1,n.anonymous=i.anonymous||!1,n.renderbody=i.renderbody||void 0,n.rendertype=i.rendertype||"text/plain",n.fromTag=d,n.contact=p,n.status!==u.SessionStatus.STATUS_NULL)throw new h.Exceptions.InvalidStateError(n.status);return n.isCanceled=!1,n.received100=!1,n.method=a.C.INVITE,n.logger=t.getLogger("sip.inviteclientcontext"),t.applicants[n.toString()]=n,n.id=n.request.callId+n.fromTag,n.onInfo=i.onInfo,n.errorListener=n.onTransportError.bind(n),t.transport&&t.transport.on("transportError",n.errorListener),n}return s(t,e),t.prototype.receiveNonInviteResponse=function(e){this.receiveInviteResponse(e)},t.prototype.receiveResponse=function(e){this.receiveInviteResponse(e)},t.prototype.send=function(){return new p.RequestSender(this,this.ua).send(),this},t.prototype.invite=function(){var e=this;return this.ua.sessions[this.id]=this,Promise.resolve().then(function(){e.inviteWithoutSdp?(e.request.body=e.renderbody,e.status=u.SessionStatus.STATUS_INVITE_SENT,e.send()):(e.sessionDescriptionHandler=e.sessionDescriptionHandlerFactory(e,e.ua.configuration.sessionDescriptionHandlerFactoryOptions||{}),e.emit("SessionDescriptionHandler-created",e.sessionDescriptionHandler),e.sessionDescriptionHandler.getDescription(e.sessionDescriptionHandlerOptions,e.modifiers).then(function(t){e.isCanceled||e.status===u.SessionStatus.STATUS_TERMINATED||(e.hasOffer=!0,e.request.body=t,e.status=u.SessionStatus.STATUS_INVITE_SENT,e.send())},function(t){t.type===u.TypeStrings.SessionDescriptionHandlerError&&(e.logger.log(t.message),t.error&&e.logger.log(t.error)),e.status!==u.SessionStatus.STATUS_TERMINATED&&(e.failed(void 0,a.C.causes.WEBRTC_ERROR),e.terminated(void 0,a.C.causes.WEBRTC_ERROR))}))}),this},t.prototype.receiveInviteResponse=function(e){var t=this;if(this.status!==u.SessionStatus.STATUS_TERMINATED&&e.method===a.C.INVITE){var r=e.callId+e.fromTag+e.toTag,i=[];if(this.dialog&&e.statusCode&&e.statusCode>=200&&e.statusCode<=299){if(r!==this.dialog.id.toString()){if(!this.createDialog(e,"UAC",!0))return;return this.emit("ack",e.transaction.sendACK({body:v.Utils.generateFakeSDP(e.body)})),this.earlyDialogs[r].sendRequest(this,a.C.BYE),void(this.status!==u.SessionStatus.STATUS_CONFIRMED&&(this.failed(e,a.C.causes.WEBRTC_ERROR),this.terminated(e,a.C.causes.WEBRTC_ERROR)))}if(this.status===u.SessionStatus.STATUS_CONFIRMED)return void this.emit("ack",e.transaction.sendACK());if(!this.hasAnswer)return}var s=e&&e.statusCode;if(this.dialog&&s&&s<200){var n=e.getHeader("rseq");if(n&&(-1!==this.dialog.pracked.indexOf(n)||Number(this.dialog.pracked[this.dialog.pracked.length-1])>=Number(n)&&this.dialog.pracked.length>0))return;if(!this.earlyDialogs[r]&&!this.createDialog(e,"UAC",!0))return;if(-1!==this.earlyDialogs[r].pracked.indexOf(e.getHeader("rseq"))||this.earlyDialogs[r].pracked[this.earlyDialogs[r].pracked.length-1]>=Number(n)&&this.earlyDialogs[r].pracked.length>0)return;return i.push("RAck: "+e.getHeader("rseq")+" "+e.getHeader("cseq")),this.earlyDialogs[r].pracked.push(e.getHeader("rseq")),void this.earlyDialogs[r].sendRequest(this,a.C.PRACK,{extraHeaders:i,body:v.Utils.generateFakeSDP(e.body)})}if(this.isCanceled){if(s&&s>=100&&s<200)this.request.cancel(this.cancelReason,i),this.canceled();else if(s&&s>=200&&s<299)this.acceptAndTerminate(e),this.emit("bye",this.request);else if(s&&s>=300){var o=a.C.REASON_PHRASE[e.statusCode||0]||a.C.causes.CANCELED;this.rejected(e,o),this.failed(e,o),this.terminated(e,o)}}else{var c=s?s.toString():"";switch(!0){case/^100$/.test(c):this.received100=!0,this.emit("progress",e);break;case/^1[0-9]{2}$/.test(c):if(!e.toTag){this.logger.warn("1xx response received without to tag");break}if(e.hasHeader("contact")&&!this.createDialog(e,"UAC",!0))break;if(this.status=u.SessionStatus.STATUS_1XX_RECEIVED,e.hasHeader("P-Asserted-Identity")&&(this.assertedIdentity=d.Grammar.nameAddrHeaderParse(e.getHeader("P-Asserted-Identity"))),e.hasHeader("require")&&-1!==e.getHeader("require").indexOf("100rel")){if(this.dialog||!this.earlyDialogs[r])break;var h=e.getHeader("rseq");if(-1!==this.earlyDialogs[r].pracked.indexOf(h)||this.earlyDialogs[r].pracked[this.earlyDialogs[r].pracked.length-1]>=Number(h)&&this.earlyDialogs[r].pracked.length>0)return;if(this.sessionDescriptionHandler=this.sessionDescriptionHandlerFactory(this,this.ua.configuration.sessionDescriptionHandlerFactoryOptions||{}),this.emit("SessionDescriptionHandler-created",this.sessionDescriptionHandler),this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||""))if(this.hasOffer){if(!this.createDialog(e,"UAC"))break;this.hasAnswer=!0,void 0!==this.dialog&&h&&this.dialog.pracked.push(h),this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).then(function(){i.push("RAck: "+e.getHeader("rseq")+" "+e.getHeader("cseq")),t.sendRequest(a.C.PRACK,{extraHeaders:i,receiveResponse:function(){}}),t.status=u.SessionStatus.STATUS_EARLY_MEDIA,t.emit("progress",e)},function(r){t.logger.warn(r),t.acceptAndTerminate(e,488,"Not Acceptable Here"),t.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION)})}else{var p=this.earlyDialogs[r];p.sessionDescriptionHandler=this.sessionDescriptionHandlerFactory(this,this.ua.configuration.sessionDescriptionHandlerFactoryOptions||{}),this.emit("SessionDescriptionHandler-created",p.sessionDescriptionHandler),h&&p.pracked.push(h),p.sessionDescriptionHandler&&p.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).then(function(){return p.sessionDescriptionHandler.getDescription(t.sessionDescriptionHandlerOptions,t.modifiers)}).then(function(r){i.push("RAck: "+h+" "+e.getHeader("cseq")),p.sendRequest(t,a.C.PRACK,{extraHeaders:i,body:r}),t.status=u.SessionStatus.STATUS_EARLY_MEDIA,t.emit("progress",e)}).catch(function(e){if(h&&e.type===u.TypeStrings.SessionDescriptionHandlerError){if(p.pracked.push(h),t.status===u.SessionStatus.STATUS_TERMINATED)return;t.failed(void 0,a.C.causes.WEBRTC_ERROR),t.terminated(void 0,a.C.causes.WEBRTC_ERROR)}else h&&p.pracked.splice(p.pracked.indexOf(h),1),t.logger.warn("invalid description"),t.logger.warn(e)})}else i.push("RAck: "+e.getHeader("rseq")+" "+e.getHeader("cseq")),this.earlyDialogs[r].pracked.push(e.getHeader("rseq")),this.earlyDialogs[r].sendRequest(this,a.C.PRACK,{extraHeaders:i}),this.emit("progress",e)}else this.emit("progress",e);break;case/^2[0-9]{2}$/.test(c):if(this.request.cseq+" "+this.request.method!==e.getHeader("cseq"))break;if(e.hasHeader("P-Asserted-Identity")&&(this.assertedIdentity=d.Grammar.nameAddrHeaderParse(e.getHeader("P-Asserted-Identity"))),this.status===u.SessionStatus.STATUS_EARLY_MEDIA&&this.dialog){this.status=u.SessionStatus.STATUS_CONFIRMED;var l={};this.renderbody&&(i.push("Content-Type: "+this.rendertype),l.extraHeaders=i,l.body=this.renderbody),this.emit("ack",e.transaction.sendACK(l)),this.accepted(e);break}if(this.dialog)break;if(this.hasOffer)if(this.hasAnswer){l={};this.renderbody&&(i.push("Content-Type: "+this.rendertype),l.extraHeaders=i,l.body=this.renderbody),this.emit("ack",e.transaction.sendACK(l))}else{if(!this.sessionDescriptionHandler||!this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||"")){this.acceptAndTerminate(e,400,"Missing session description"),this.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION);break}if(!this.createDialog(e,"UAC"))break;this.hasAnswer=!0,this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).then(function(){var r={};t.status=u.SessionStatus.STATUS_CONFIRMED,t.renderbody&&(i.push("Content-Type: "+t.rendertype),r.extraHeaders=i,r.body=t.renderbody),t.emit("ack",e.transaction.sendACK(r)),t.accepted(e)},function(r){t.logger.warn(r),t.acceptAndTerminate(e,488,"Not Acceptable Here"),t.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION)})}else if(this.earlyDialogs[r]&&this.earlyDialogs[r].sessionDescriptionHandler){if(this.hasOffer=!0,this.hasAnswer=!0,this.sessionDescriptionHandler=this.earlyDialogs[r].sessionDescriptionHandler,!this.createDialog(e,"UAC"))break;this.status=u.SessionStatus.STATUS_CONFIRMED,this.emit("ack",e.transaction.sendACK()),this.accepted(e)}else{if(this.sessionDescriptionHandler=this.sessionDescriptionHandlerFactory(this,this.ua.configuration.sessionDescriptionHandlerFactoryOptions||{}),this.emit("SessionDescriptionHandler-created",this.sessionDescriptionHandler),!this.sessionDescriptionHandler.hasDescription(e.getHeader("Content-Type")||"")){this.acceptAndTerminate(e,400,"Missing session description"),this.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION);break}if(!this.createDialog(e,"UAC"))break;this.hasOffer=!0,this.sessionDescriptionHandler.setDescription(e.body,this.sessionDescriptionHandlerOptions,this.modifiers).then(function(){return t.sessionDescriptionHandler.getDescription(t.sessionDescriptionHandlerOptions,t.modifiers)}).then(function(r){t.isCanceled||t.status===u.SessionStatus.STATUS_TERMINATED||(t.status=u.SessionStatus.STATUS_CONFIRMED,t.hasAnswer=!0,t.emit("ack",e.transaction.sendACK({body:r})),t.accepted(e))}).catch(function(r){r.type===u.TypeStrings.SessionDescriptionHandlerError&&(t.logger.warn("invalid description"),t.logger.warn(r.toString()),t.acceptAndTerminate(e,488,"Invalid session description"),t.failed(e,a.C.causes.BAD_MEDIA_DESCRIPTION))})}break;default:o=v.Utils.sipErrorCause(s||0);this.rejected(e,o),this.failed(e,o),this.terminated(e,o)}}}},t.prototype.cancel=function(e){if(void 0===e&&(e={}),e.extraHeaders=(e.extraHeaders||[]).slice(),this.isCanceled)throw new h.Exceptions.InvalidStateError(u.SessionStatus.STATUS_CANCELED);if(this.status===u.SessionStatus.STATUS_TERMINATED||this.status===u.SessionStatus.STATUS_CONFIRMED)throw new h.Exceptions.InvalidStateError(this.status);this.logger.log("canceling RTCSession"),this.isCanceled=!0;var t=v.Utils.getCancelReason(e.statusCode,e.reasonPhrase);return this.status===u.SessionStatus.STATUS_NULL||this.status===u.SessionStatus.STATUS_INVITE_SENT&&!this.received100?this.cancelReason=t:this.status!==u.SessionStatus.STATUS_INVITE_SENT&&this.status!==u.SessionStatus.STATUS_1XX_RECEIVED&&this.status!==u.SessionStatus.STATUS_EARLY_MEDIA||this.request.cancel(t,e.extraHeaders),this.canceled()},t.prototype.terminate=function(e){return this.status===u.SessionStatus.STATUS_TERMINATED?this:(this.status===u.SessionStatus.STATUS_WAITING_FOR_ACK||this.status===u.SessionStatus.STATUS_CONFIRMED?this.bye(e):this.cancel(e),this)},t.prototype.receiveRequest=function(t){return t.method,a.C.CANCEL,t.method===a.C.ACK&&this.status===u.SessionStatus.STATUS_WAITING_FOR_ACK&&(clearTimeout(this.timers.ackTimer),clearTimeout(this.timers.invite2xxTimer),this.status=u.SessionStatus.STATUS_CONFIRMED,this.accepted()),e.prototype.receiveRequest.call(this,t)},t}(m);t.InviteClientContext=C;var y=function(e){function t(t,r,i,s){void 0===s&&(s={});var n=this;if(void 0===t||void 0===r||void 0===i)throw new TypeError("Not enough arguments");if((n=e.call(this,t,a.C.REFER,r.remoteIdentity.uri.toString(),s)||this).type=u.TypeStrings.ReferClientContext,n.options=s,n.extraHeaders=(n.options.extraHeaders||[]).slice(),n.applicant=r,"string"==typeof i||i.type!==u.TypeStrings.InviteServerContext&&i.type!==u.TypeStrings.InviteClientContext){var o=d.Grammar.parse(i,"Refer_To");n.target=o&&o.uri?o.uri:i;var c=n.ua.normalizeTarget(n.target);if(!c)throw new TypeError("Invalid target: "+i);n.target=c}else{var h=i.dialog;if(!h)throw new TypeError("Invalid target due to no dialog: "+i);n.target='"'+i.remoteIdentity.friendlyName+'" <'+h.remoteTarget.toString()+"?Replaces="+h.id.callId+"%3Bto-tag%3D"+h.id.remoteTag+"%3Bfrom-tag%3D"+h.id.localTag+">"}return n.ua&&n.extraHeaders.push("Referred-By: <"+n.ua.configuration.uri+">"),n.extraHeaders.push("Contact: "+r.contact),n.extraHeaders.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),n.extraHeaders.push("Refer-To: "+n.target),n.errorListener=n.onTransportError.bind(n),t.transport&&t.transport.on("transportError",n.errorListener),n}return s(t,e),t.prototype.refer=function(e){var t=this;void 0===e&&(e={});var r=(this.extraHeaders||[]).slice();return e.extraHeaders&&r.concat(e.extraHeaders),this.applicant.sendRequest(a.C.REFER,{extraHeaders:this.extraHeaders,receiveResponse:function(r){var i=r&&r.statusCode?r.statusCode.toString():"";/^1[0-9]{2}$/.test(i)?t.emit("referRequestProgress",t):/^2[0-9]{2}$/.test(i)?t.emit("referRequestAccepted",t):/^[4-6][0-9]{2}$/.test(i)&&t.emit("referRequestRejected",t),e.receiveResponse&&e.receiveResponse(r)}}),this},t.prototype.receiveNotify=function(e){var t=e.hasHeader("Content-Type")?e.getHeader("Content-Type"):void 0;if(t&&-1!==t.search(/^message\/sipfrag/)){var r=d.Grammar.parse(e.body,"sipfrag");if(-1===r)return void e.reply(489,"Bad Event");switch(!0){case/^1[0-9]{2}$/.test(r.statusCode):this.emit("referProgress",this);break;case/^2[0-9]{2}$/.test(r.statusCode):this.emit("referAccepted",this),!this.options.activeAfterTransfer&&this.applicant.terminate&&this.applicant.terminate();break;default:this.emit("referRejected",this)}return e.reply(200),void this.emit("notify",e)}e.reply(489,"Bad Event")},t}(o.ClientContext);t.ReferClientContext=y;var A=function(e){function t(t,r){var i=e.call(this,t,r)||this;return i.type=u.TypeStrings.ReferServerContext,i.ua=t,i.status=u.SessionStatus.STATUS_INVITE_RECEIVED,i.fromTag=r.fromTag,i.id=r.callId+i.fromTag,i.request=r,i.contact=i.ua.contact.toString(),i.logger=t.getLogger("sip.referservercontext",i.id),i.cseq=Math.floor(1e4*Math.random()),i.callId=i.request.callId,i.fromUri=i.request.to.uri,i.fromTag=i.request.to.parameters.tag,i.remoteTarget=i.request.headers.Contact[0].parsed.uri,i.toUri=i.request.from.uri,i.toTag=i.request.fromTag,i.routeSet=i.request.getHeaders("record-route"),i.request.hasHeader("refer-to")?(i.referTo=i.request.parseHeader("refer-to"),i.referredSession=i.ua.findSession(r),i.request.hasHeader("referred-by")&&(i.referredBy=i.request.getHeader("referred-by")),i.referTo.uri.hasHeader("replaces")&&(i.replaces=i.referTo.uri.getHeader("replaces")),i.errorListener=i.onTransportError.bind(i),t.transport&&t.transport.on("transportError",i.errorListener),i.status=u.SessionStatus.STATUS_WAITING_FOR_ANSWER,i):(i.logger.warn("Invalid REFER packet. A refer-to header is required. Rejecting refer."),i.reject(),i)}return s(t,e),t.prototype.receiveNonInviteResponse=function(e){},t.prototype.progress=function(){if(this.status!==u.SessionStatus.STATUS_WAITING_FOR_ANSWER)throw new h.Exceptions.InvalidStateError(this.status);this.request.reply(100)},t.prototype.reject=function(t){if(void 0===t&&(t={}),this.status===u.SessionStatus.STATUS_TERMINATED)throw new h.Exceptions.InvalidStateError(this.status);this.logger.log("Rejecting refer"),this.status=u.SessionStatus.STATUS_TERMINATED,e.prototype.reject.call(this,t),this.emit("referRequestRejected",this)},t.prototype.accept=function(e,t){var r=this;if(void 0===e&&(e={}),this.status!==u.SessionStatus.STATUS_WAITING_FOR_ANSWER)throw new h.Exceptions.InvalidStateError(this.status);if(this.status=u.SessionStatus.STATUS_ANSWERED,this.request.reply(202,"Accepted"),this.emit("referRequestAccepted",this),e.followRefer){this.logger.log("Accepted refer, attempting to automatically follow it");var i=this.referTo.uri;if(!i.scheme||!i.scheme.match("^sips?$"))return this.logger.error("SIP.js can only automatically follow SIP refer target"),void this.reject();var s=e.inviteOptions||{},n=(s.extraHeaders||[]).slice();if(this.replaces&&n.push("Replaces: "+decodeURIComponent(this.replaces)),this.referredBy&&n.push("Referred-By: "+this.referredBy),s.extraHeaders=n,i.clearHeaders(),this.targetSession=this.ua.invite(i.toString(),s,t),this.emit("referInviteSent",this),this.targetSession){this.targetSession.once("progress",function(e){var t=e.statusCode||100,i=e.reasonPhrase;r.sendNotify(("SIP/2.0 "+t+" "+i).trim()),r.emit("referProgress",r),r.referredSession&&r.referredSession.emit("referProgress",r)}),this.targetSession.once("accepted",function(){r.logger.log("Successfully followed the refer"),r.sendNotify("SIP/2.0 200 OK"),r.emit("referAccepted",r),r.referredSession&&r.referredSession.emit("referAccepted",r)});var o=function(e){if(r.status!==u.SessionStatus.STATUS_TERMINATED){if(r.logger.log("Refer was not successful. Resuming session"),e&&429===e.statusCode)return r.logger.log("Alerting referrer that identity is required."),void r.sendNotify("SIP/2.0 429 Provide Referrer Identity");r.sendNotify("SIP/2.0 603 Declined"),r.status=u.SessionStatus.STATUS_TERMINATED,r.emit("referRejected",r),r.referredSession&&r.referredSession.emit("referRejected")}};this.targetSession.once("rejected",o),this.targetSession.once("failed",o)}}else this.logger.log("Accepted refer, but did not automatically follow it"),this.sendNotify("SIP/2.0 200 OK"),this.emit("referAccepted",this),this.referredSession&&this.referredSession.emit("referAccepted",this)},t.prototype.sendNotify=function(e){if(this.status!==u.SessionStatus.STATUS_ANSWERED)throw new h.Exceptions.InvalidStateError(this.status);if(-1===d.Grammar.parse(e,"sipfrag"))throw new Error("sipfrag body is required to send notify for refer");var t=new g.OutgoingRequest(a.C.NOTIFY,this.remoteTarget,this.ua,{cseq:this.cseq+=1,callId:this.callId,fromUri:this.fromUri,fromTag:this.fromTag,toUri:this.toUri,toTag:this.toTag,routeSet:this.routeSet},["Event: refer","Subscription-State: terminated","Content-Type: message/sipfrag"],e);new p.RequestSender({request:t,onRequestTimeout:function(){},onTransportError:function(){},receiveResponse:function(){}},this.ua).send()},t}(l.ServerContext);t.ReferServerContext=A},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(7),o=r(1),a=r(13),c=r(0),u=r(11),h=r(2),d=function(e){function t(t,r,i,s){void 0===s&&(s={});var n,a=this;if(!i)throw new TypeError("Event necessary to create a subscription.");return s.extraHeaders=(s.extraHeaders||[]).slice(),"number"!=typeof s.expires?(t.logger.warn("expires must be a number. Using default of 3600."),n=3600):n=s.expires,s.extraHeaders.push("Event: "+i),s.extraHeaders.push("Expires: "+n),s.extraHeaders.push("Contact: "+t.contact.toString()),s.extraHeaders.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),(a=e.call(this,t,o.C.SUBSCRIBE,r,s)||this).type=c.TypeStrings.Subscription,a.event=i,a.requestedExpires=n,a.state="init",a.contact=t.contact.toString(),a.extraHeaders=s.extraHeaders,a.logger=t.getLogger("sip.subscription"),a.expires=n,a.timers={N:void 0,subDuration:void 0},a.errorCodes=[404,405,410,416,480,481,482,483,484,485,489,501,604],a}return s(t,e),t.prototype.subscribe=function(){var e=this;return"active"===this.state?(this.refresh(),this):"notify_wait"===this.state?this:(clearTimeout(this.timers.subDuration),clearTimeout(this.timers.N),this.timers.N=setTimeout(function(){return e.timer_fire()},u.Timers.TIMER_N),this.request&&this.request.from&&(this.ua.earlySubscriptions[this.request.callId+this.request.from.parameters.tag+this.event]=this),this.send(),this.state="notify_wait",this)},t.prototype.refresh=function(){"terminated"!==this.state&&"pending"!==this.state&&"notify_wait"!==this.state&&this.dialog&&this.dialog.sendRequest(this,o.C.SUBSCRIBE,{extraHeaders:this.extraHeaders,body:this.body})},t.prototype.receiveResponse=function(e){var t=this,r=e.statusCode?e.statusCode:0,i=h.Utils.getReasonPhrase(r);if("notify_wait"===this.state&&r>=300||"notify_wait"!==this.state&&-1!==this.errorCodes.indexOf(r))this.failed(e,void 0);else if(/^2[0-9]{2}$/.test(r.toString())){this.emit("accepted",e,i);var s=e.getHeader("Expires");s&&Number(s)<=this.requestedExpires?(this.expires=Number(s),this.timers.subDuration=setTimeout(function(){return t.refresh()},900*Number(s))):s?(this.logger.warn("Expires header in a 200-class response to SUBSCRIBE with a higher value than the one in the request"),this.failed(e,"Invalid Expires Header")):(this.logger.warn("Expires header missing in a 200-class response to SUBSCRIBE"),this.failed(e,"Expires Header Missing"))}else r>300&&(this.emit("failed",e,i),this.emit("rejected",e,i))},t.prototype.unsubscribe=function(){var e=this,t=[];this.state="terminated",t.push("Event: "+this.event),t.push("Expires: 0"),t.push("Contact: "+this.contact),t.push("Allow: "+["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"].toString()),this.receiveResponse=function(){},this.dialog&&this.dialog.sendRequest(this,o.C.SUBSCRIBE,{extraHeaders:t,body:this.body}),clearTimeout(this.timers.subDuration),clearTimeout(this.timers.N),this.timers.N=setTimeout(function(){return e.timer_fire()},u.Timers.TIMER_N),this.emit("terminated")},t.prototype.receiveRequest=function(e){var t,r=this,i=function(){t.expires&&(clearTimeout(r.timers.subDuration),t.expires=Math.min(r.expires,Math.max(t.expires,0)),r.timers.subDuration=setTimeout(function(){return r.refresh()},900*t.expires))};if(this.matchEvent(e))if(this.dialog||this.createConfirmedDialog(e,"UAS")&&this.dialog&&(this.id=this.dialog.id.toString(),this.request&&this.request.from&&(delete this.ua.earlySubscriptions[this.request.callId+this.request.from.parameters.tag+this.event],this.ua.subscriptions[this.id||""]=this)),t=e.parseHeader("Subscription-State"),e.reply(200),clearTimeout(this.timers.N),this.emit("notify",{request:e}),"terminated"!==this.state)switch(t.state){case"active":this.state="active",i();break;case"pending":"notify_wait"===this.state&&i(),this.state="pending";break;case"terminated":if(clearTimeout(this.timers.subDuration),t.reason)switch(this.logger.log("terminating subscription with reason "+t.reason),t.reason){case"deactivated":case"timeout":return void this.subscribe();case"probation":case"giveup":return void(t.params&&t.params["retry-after"]?this.timers.subDuration=setTimeout(function(){return r.subscribe()},t.params["retry-after"]):this.subscribe())}this.close()}else"terminated"===t.state&&(this.terminateDialog(),clearTimeout(this.timers.N),clearTimeout(this.timers.subDuration),delete this.ua.subscriptions[this.id||""]);else e.reply(489)},t.prototype.close=function(){"notify_wait"===this.state?(this.state="terminated",clearTimeout(this.timers.N),clearTimeout(this.timers.subDuration),this.receiveResponse=function(){},this.request&&this.request.from&&delete this.ua.earlySubscriptions[this.request.callId+this.request.from.parameters.tag+this.event],this.emit("terminated")):"terminated"!==this.state&&this.unsubscribe()},t.prototype.onDialogError=function(e){this.failed(e,o.C.causes.DIALOG_ERROR)},t.prototype.timer_fire=function(){"terminated"===this.state?(this.terminateDialog(),clearTimeout(this.timers.N),clearTimeout(this.timers.subDuration),delete this.ua.subscriptions[this.id||""]):"notify_wait"===this.state||"pending"===this.state?this.close():this.refresh()},t.prototype.createConfirmedDialog=function(e,t){this.terminateDialog();var r=new a.Dialog(this,e,t);return this.request&&(r.inviteSeqnum=this.request.cseq,r.localSeqnum=this.request.cseq),!r.error&&(this.dialog=r,!0)},t.prototype.terminateDialog=function(){this.dialog&&(delete this.ua.subscriptions[this.id||""],this.dialog.terminate(),delete this.dialog)},t.prototype.failed=function(e,t){return this.close(),this.emit("failed",e,t),this.emit("rejected",e,t),this},t.prototype.matchEvent=function(e){if(!e.hasHeader("Event"))return this.logger.warn("missing Event header"),!1;if(!e.hasHeader("Subscription-State"))return this.logger.warn("missing Subscription-State header"),!1;var t=e.parseHeader("event").event;return this.event===t||(this.logger.warn("event match failed"),e.reply(481,"Event Match Failed"),!1)},t}(n.ClientContext);t.Subscription=d},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(0),a=function(e){function t(t,r){var i=e.call(this)||this;return i.type=o.TypeStrings.Transport,i.logger=t,i}return s(t,e),t.prototype.connect=function(e){var t=this;return void 0===e&&(e={}),this.connectPromise(e).then(function(e){e.overrideEvent||t.emit("connected")})},t.prototype.send=function(e,t){var r=this;return void 0===t&&(t={}),this.sendPromise(e).then(function(e){e.overrideEvent||r.emit("messageSent",e.msg)})},t.prototype.disconnect=function(e){var t=this;return void 0===e&&(e={}),this.disconnectPromise(e).then(function(e){e.overrideEvent||t.emit("disconnected")})},t.prototype.afterConnected=function(e){this.isConnected()?e():this.once("connected",e)},t.prototype.waitForConnected=function(){var e=this;return console.warn("DEPRECATION WARNING Transport.waitForConnected(): use afterConnected() instead"),new Promise(function(t){e.afterConnected(t)})},t}(n.EventEmitter);t.Transport=a},function(e,t,r){"use strict";(function(e){var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(7),a=r(1),c=r(18),u=r(0),h=r(5),d=r(4),p=r(19),l=r(20),f=r(21),g=r(22),T=r(23),v=r(14),m=r(24),S=r(25),C=r(9),y=r(10),A=r(2),E=r(28),R=r(29),_=e.window||e,b=function(t){function r(e){var i=t.call(this)||this;i.type=u.TypeStrings.UA,i.log=new p.LoggerFactory,i.logger=i.getLogger("sip.ua"),i.cache={credentials:{}},i.configuration={},i.dialogs={},i.applicants={},i.data={},i.sessions={},i.subscriptions={},i.earlySubscriptions={},i.publishers={},i.status=u.UAStatus.STATUS_INIT,i.transactions={nist:{},nict:{},ist:{},ict:{}},void 0===e?e={}:("string"==typeof e||e instanceof String)&&(e={uri:e}),e.log&&(e.log.hasOwnProperty("builtinEnabled")&&(i.log.builtinEnabled=e.log.builtinEnabled),e.log.hasOwnProperty("level")&&(i.log.level=e.log.level),e.log.hasOwnProperty("connector")&&(i.log.connector=e.log.connector));try{i.loadConfig(e)}catch(e){throw i.status=u.UAStatus.STATUS_NOT_READY,i.error=r.C.CONFIGURATION_ERROR,e}return i.registerContext=new g.RegisterContext(i,e.registerOptions),i.registerContext.on("failed",i.emit.bind(i,"registrationFailed")),i.registerContext.on("registered",i.emit.bind(i,"registered")),i.registerContext.on("unregistered",i.emit.bind(i,"unregistered")),i.configuration.autostart&&i.start(),i}return s(r,t),Object.defineProperty(r.prototype,"transactionsCount",{get:function(){for(var e=0,t=0,r=["nist","nict","ist","ict"];t<r.length;t++){var i=r[t];e+=Object.keys(this.transactions[i]).length}return e},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"nictTransactionsCount",{get:function(){return Object.keys(this.transactions.nict).length},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"nistTransactionsCount",{get:function(){return Object.keys(this.transactions.nist).length},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"ictTransactionsCount",{get:function(){return Object.keys(this.transactions.ict).length},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"istTransactionsCount",{get:function(){return Object.keys(this.transactions.ist).length},enumerable:!0,configurable:!0}),r.prototype.register=function(e){return void 0===e&&(e={}),e.register&&(this.configuration.register=!0),this.registerContext.register(e),this},r.prototype.unregister=function(e){var t=this;return this.configuration.register=!1,this.transport&&this.transport.afterConnected(function(){t.registerContext.unregister(e)}),this},r.prototype.isRegistered=function(){return this.registerContext.registered},r.prototype.invite=function(e,t,r){var i=this,s=new m.InviteClientContext(this,e,t,r);return this.transport&&this.transport.afterConnected(function(){s.invite(),i.emit("inviteSent",s)}),s},r.prototype.subscribe=function(e,t,r){var i=new S.Subscription(this,e,t,r);return this.transport&&this.transport.afterConnected(function(){return i.subscribe()}),i},r.prototype.publish=function(e,t,r,i){var s=new f.PublishContext(this,e,t,i);return this.transport&&this.transport.afterConnected(function(){s.publish(r)}),s},r.prototype.message=function(e,t,r){if(void 0===r&&(r={}),void 0===t)throw new TypeError("Not enough arguments");return r.contentType=r.contentType||"text/plain",r.body=t,this.request(a.C.MESSAGE,e,r)},r.prototype.request=function(e,t,r){var i=new o.ClientContext(this,e,t,r);return this.transport&&this.transport.afterConnected(function(){return i.send()}),i},r.prototype.stop=function(){var t=this;if(this.logger.log("user requested closure..."),this.status===u.UAStatus.STATUS_USER_CLOSED)return this.logger.warn("UA already closed"),this;for(var r in this.logger.log("closing registerContext"),this.registerContext.close(),this.sessions)this.sessions[r]&&(this.logger.log("closing session "+r),this.sessions[r].terminate());for(var i in this.subscriptions)this.subscriptions[i]&&(this.logger.log("unsubscribing from subscription "+i),this.subscriptions[i].close());for(var s in this.earlySubscriptions)this.earlySubscriptions[s]&&(this.logger.log("unsubscribing from early subscription "+s),this.earlySubscriptions[s].close());for(var n in this.publishers)this.publishers[n]&&(this.logger.log("unpublish "+n),this.publishers[n].close());for(var o in this.applicants)this.applicants[o]&&this.applicants[o].close();if(this.status=u.UAStatus.STATUS_USER_CLOSED,0===this.nistTransactionsCount&&0===this.nictTransactionsCount&&this.transport)this.transport.disconnect();else{var a=function(){0===t.nistTransactionsCount&&0===t.nictTransactionsCount&&(t.removeListener("transactionDestroyed",a),t.transport&&t.transport.disconnect())};this.on("transactionDestroyed",a)}return"function"==typeof _.removeEventListener&&(e.chrome&&e.chrome.app&&e.chrome.app.runtime||_.removeEventListener("unload",this.environListener)),this},r.prototype.start=function(){var t=this;if(this.logger.log("user requested startup..."),this.status===u.UAStatus.STATUS_INIT){if(this.status=u.UAStatus.STATUS_STARTING,!this.configuration.transportConstructor)throw new h.Exceptions.TransportError("Transport constructor not set");this.transport=new this.configuration.transportConstructor(this.getLogger("sip.transport"),this.configuration.transportOptions),this.setTransportListeners(),this.emit("transportCreated",this.transport),this.transport.connect()}else this.status===u.UAStatus.STATUS_USER_CLOSED?(this.logger.log("resuming"),this.status=u.UAStatus.STATUS_READY,this.transport&&this.transport.connect()):this.status===u.UAStatus.STATUS_STARTING?this.logger.log("UA is in STARTING status, not opening new connection"):this.status===u.UAStatus.STATUS_READY?this.logger.log("UA is in READY status, not resuming"):this.logger.error("Connection is down. Auto-Recovery system is trying to connect");return this.configuration.autostop&&"function"==typeof _.addEventListener&&(e.chrome&&e.chrome.app&&e.chrome.app.runtime||(this.environListener=this.stop,_.addEventListener("unload",function(){return t.environListener()}))),this},r.prototype.normalizeTarget=function(e){return A.Utils.normalizeTarget(e,this.configuration.hostportParams)},r.prototype.getLogger=function(e,t){return this.log.getLogger(e,t)},r.prototype.newTransaction=function(e){this.transactions[e.kind][e.id]=e,this.emit("newTransaction",{transaction:e})},r.prototype.destroyTransaction=function(e){delete this.transactions[e.kind][e.id],this.emit("transactionDestroyed",{transaction:e})},r.prototype.findSession=function(e){return this.sessions[e.callId+e.fromTag]||this.sessions[e.callId+e.toTag]||void 0},r.prototype.saveCredentials=function(e){return this.cache.credentials[e.realm]=this.cache.credentials[e.realm]||{},this.cache.credentials[e.realm][e.uri]=e,this},r.prototype.getCredentials=function(e){var t=e.ruri.type===u.TypeStrings.URI?e.ruri.host:"";if(t&&this.cache.credentials[t]&&this.cache.credentials[t][e.ruri.toString()]){var r=this.cache.credentials[t][e.ruri.toString()];return r.method=e.method,r}},r.prototype.onTransportError=function(){this.status!==u.UAStatus.STATUS_USER_CLOSED&&(this.error&&this.error===r.C.NETWORK_ERROR||(this.status=u.UAStatus.STATUS_NOT_READY,this.error=r.C.NETWORK_ERROR))},r.prototype.setTransportListeners=function(){var e=this;this.transport&&(this.transport.on("connected",function(){return e.onTransportConnected()}),this.transport.on("message",function(t){return e.onTransportReceiveMsg(t)}),this.transport.on("transportError",function(){return e.onTransportError()}))},r.prototype.onTransportConnected=function(){var e=this;this.configuration.register&&Promise.resolve().then(function(){return e.registerContext.register()})},r.prototype.onTransportReceiveMsg=function(e){var t=l.Parser.parseMessage(e,this);if(this.status===u.UAStatus.STATUS_USER_CLOSED&&t&&t.type===u.TypeStrings.IncomingRequest)this.logger.warn("UA received message when status = USER_CLOSED - aborting");else if(t&&this.transport&&T.SanityCheck.sanityCheck(t,this,this.transport))if(t.type===u.TypeStrings.IncomingRequest)t.transport=this.transport,this.receiveRequest(t);else if(t.type===u.TypeStrings.IncomingResponse)switch(t.method){case a.C.INVITE:var r=this.transactions.ict[t.viaBranch];r&&r.receiveResponse(t);break;case a.C.ACK:break;default:var i=this.transactions.nict[t.viaBranch];i&&i.receiveResponse(t)}},r.prototype.receiveRequest=function(e){var t=function(t){return!!t&&!!e.ruri&&t.user===e.ruri.user};if(this.configuration.uri.type===u.TypeStrings.URI&&!(t(this.configuration.uri)||this.contact&&(t(this.contact.uri)||t(this.contact.pubGruu)||t(this.contact.tempGruu))))return this.logger.warn("Request-URI does not point to us"),void(e.method!==a.C.ACK&&e.reply_sl(404));if(e.ruri&&e.ruri.scheme===a.C.SIPS)e.reply_sl(416);else if(!this.checkTransaction(e)){var i,s=e.method;if(s===a.C.OPTIONS){new C.NonInviteServerTransaction(e,this);e.reply(200,void 0,["Allow: "+r.C.ALLOWED_METHODS.toString(),"Accept: "+r.C.ACCEPTED_BODY_TYPES.toString()])}else s===a.C.MESSAGE?((i=new v.ServerContext(this,e)).body=e.body,i.contentType=e.getHeader("Content-Type")||"text/plain",e.reply(200,void 0),this.emit("message",i)):s!==a.C.INVITE&&s!==a.C.ACK&&(i=new v.ServerContext(this,e));if(e.toTag){var n=this.findDialog(e);if(n){if(s===a.C.INVITE)new C.InviteServerTransaction(e,this);n.receiveRequest(e)}else if(s===a.C.NOTIFY){p=this.findSession(e);var o=this.findEarlySubscription(e);p?p.receiveRequest(e):o?o.receiveRequest(e):(this.logger.warn("received NOTIFY request for a non existent session or subscription"),e.reply(481,"Subscription does not exist"))}else s!==a.C.ACK&&e.reply(481)}else switch(s){case a.C.INVITE:var c=this.configuration.replaces!==a.C.supported.UNSUPPORTED&&e.parseHeader("replaces"),h=void 0;if(c){if(!(h=this.dialogs[c.call_id+c.replaces_to_tag+c.replaces_from_tag]))return void e.reply_sl(481,void 0);if(h.owner.type!==u.TypeStrings.Subscription&&h.owner.status===u.SessionStatus.STATUS_TERMINATED)return void e.reply_sl(603,void 0);if(h.state===u.DialogStatus.STATUS_CONFIRMED&&c.earlyOnly)return void e.reply_sl(486,void 0)}var d=new m.InviteServerContext(this,e);h&&h.owner.type!==u.TypeStrings.Subscription&&(d.replacee=h&&h.owner),this.emit("invite",d);break;case a.C.BYE:e.reply(481);break;case a.C.CANCEL:var p;(p=this.findSession(e))?p.receiveRequest(e):this.logger.warn("received CANCEL request for a non existent session");break;case a.C.ACK:break;case a.C.NOTIFY:this.configuration.allowLegacyNotifications&&this.listeners("notify").length>0?(e.reply(200,void 0),this.emit("notify",{request:e})):e.reply(481,"Subscription does not exist");break;case a.C.REFER:if(this.logger.log("Received an out of dialog refer"),this.configuration.allowOutOfDialogRefers){this.logger.log("Allow out of dialog refers is enabled on the UA");var l=new m.ReferServerContext(this,e);this.listeners("outOfDialogReferRequested").length?this.emit("outOfDialogReferRequested",l):(this.logger.log("No outOfDialogReferRequest listeners, automatically accepting and following the out of dialog refer"),l.accept({followRefer:!0}));break}e.reply(405);break;default:e.reply(405)}}},r.prototype.checkTransaction=function(e){return C.checkTransaction(this,e)},r.prototype.findDialog=function(e){return this.dialogs[e.callId+e.fromTag+e.toTag]||this.dialogs[e.callId+e.toTag+e.fromTag]||void 0},r.prototype.findEarlySubscription=function(e){return this.earlySubscriptions[e.callId+e.toTag+e.getHeader("event")]||void 0},r.prototype.checkAuthenticationFactory=function(e){if(e instanceof Function)return e.initialize||(e.initialize=function(){return Promise.resolve()}),e},r.prototype.loadConfig=function(e){var t=this,r={viaHost:A.Utils.createRandomToken(12)+".invalid",uri:new y.URI("sip","anonymous."+A.Utils.createRandomToken(6),"anonymous.invalid",void 0,void 0),custom:{},displayName:"",password:void 0,register:!0,registerOptions:{},transportConstructor:R.Transport,transportOptions:{},userAgentString:a.C.USER_AGENT,noAnswerTimeout:60,hackViaTcp:!1,hackIpInContact:!1,hackWssInTransport:!1,hackAllowUnregisteredOptionTags:!1,sessionDescriptionHandlerFactoryOptions:{constraints:{},peerConnectionOptions:{}},extraSupported:[],contactName:A.Utils.createRandomToken(8),contactTransport:"ws",forceRport:!1,autostart:!0,autostop:!0,rel100:a.C.supported.UNSUPPORTED,dtmfType:a.C.dtmfType.INFO,replaces:a.C.supported.UNSUPPORTED,sessionDescriptionHandlerFactory:E.SessionDescriptionHandler.defaultFactory,authenticationFactory:this.checkAuthenticationFactory(function(e){return new c.DigestAuthentication(e)}),allowLegacyNotifications:!1,allowOutOfDialogRefers:!1},i=this.getConfigurationCheck();for(var s in i.mandatory){if(!e.hasOwnProperty(s))throw new h.Exceptions.ConfigurationError(s);var n=e[s];if(void 0===(o=i.mandatory[s](n)))throw new h.Exceptions.ConfigurationError(s,n);r[s]=o}for(var s in i.optional)if(e.hasOwnProperty(s)){var o;if((n=e[s])instanceof Array&&0===n.length||null===n||""===n||void 0===n||"number"==typeof n&&isNaN(n))continue;if(void 0===(o=i.optional[s](n)))throw new h.Exceptions.ConfigurationError(s,n);r[s]=o}0===r.displayName&&(r.displayName="0"),r.sipjsId=A.Utils.createRandomToken(5);var u=r.uri.clone();if(u.user=void 0,r.hostportParams=u.toRaw().replace(/^sip:/i,""),r.authorizationUser||(r.authorizationUser=r.uri.user),r.noAnswerTimeout=1e3*r.noAnswerTimeout,r.hackIpInContact)if("boolean"==typeof r.hackIpInContact){var d=Math.floor(254*Math.random()+1);r.viaHost="192.0.2."+d}else"string"==typeof r.hackIpInContact&&(r.viaHost=r.hackIpInContact);r.hackWssInTransport&&(r.contactTransport="wss"),this.contact={pubGruu:void 0,tempGruu:void 0,uri:new y.URI("sip",r.contactName,r.viaHost,void 0,{transport:r.contactTransport}),toString:function(e){void 0===e&&(e={});var i=e.anonymous||!1,s=e.outbound||!1,n="<";return n+=i?(t.contact.tempGruu||"sip:<EMAIL>;transport="+r.contactTransport).toString():(t.contact.pubGruu||t.contact.uri).toString(),s&&(n+=";ob"),n+=">"}};var p={};for(var s in r)r.hasOwnProperty(s)&&(p[s]=r[s]);for(var s in Object.assign(this.configuration,p),this.logger.log("configuration parameters after validation:"),r)if(r.hasOwnProperty(s))switch(s){case"uri":case"sessionDescriptionHandlerFactory":this.logger.log("\xb7 "+s+": "+r[s]);break;case"password":this.logger.log("\xb7 "+s+": NOT SHOWN");break;case"transportConstructor":this.logger.log("\xb7 "+s+": "+r[s].name);break;default:this.logger.log("\xb7 "+s+": "+JSON.stringify(r[s]))}},r.prototype.getConfigurationCheck=function(){return{mandatory:{},optional:{uri:function(e){/^sip:/i.test(e)||(e=a.C.SIP+":"+e);var t=d.Grammar.URIParse(e);return t&&t.user?t:void 0},transportConstructor:function(e){if(e instanceof Function)return e},transportOptions:function(e){if("object"==typeof e)return e},authorizationUser:function(e){return-1===d.Grammar.parse('"'+e+'"',"quoted_string")?void 0:e},displayName:function(e){return-1===d.Grammar.parse('"'+e+'"',"displayName")?void 0:e},dtmfType:function(e){switch(e){case a.C.dtmfType.RTP:return a.C.dtmfType.RTP;case a.C.dtmfType.INFO:default:return a.C.dtmfType.INFO}},hackViaTcp:function(e){if("boolean"==typeof e)return e},hackIpInContact:function(e){return"boolean"==typeof e?e:"string"==typeof e&&-1!==d.Grammar.parse(e,"host")?e:void 0},hackWssInTransport:function(e){if("boolean"==typeof e)return e},hackAllowUnregisteredOptionTags:function(e){if("boolean"==typeof e)return e},contactTransport:function(e){if("string"==typeof e)return e},extraSupported:function(e){if(e instanceof Array){for(var t=0,r=e;t<r.length;t++){if("string"!=typeof r[t])return}return e}},forceRport:function(e){if("boolean"==typeof e)return e},noAnswerTimeout:function(e){if(A.Utils.isDecimal(e)){var t=Number(e);if(t>0)return t}},password:function(e){return String(e)},rel100:function(e){return e===a.C.supported.REQUIRED?a.C.supported.REQUIRED:e===a.C.supported.SUPPORTED?a.C.supported.SUPPORTED:a.C.supported.UNSUPPORTED},replaces:function(e){return e===a.C.supported.REQUIRED?a.C.supported.REQUIRED:e===a.C.supported.SUPPORTED?a.C.supported.SUPPORTED:a.C.supported.UNSUPPORTED},register:function(e){if("boolean"==typeof e)return e},registerOptions:function(e){if("object"==typeof e)return e},userAgentString:function(e){if("string"==typeof e)return e},autostart:function(e){if("boolean"==typeof e)return e},autostop:function(e){if("boolean"==typeof e)return e},sessionDescriptionHandlerFactory:function(e){if(e instanceof Function)return e},sessionDescriptionHandlerFactoryOptions:function(e){if("object"==typeof e)return e},authenticationFactory:this.checkAuthenticationFactory,allowLegacyNotifications:function(e){if("boolean"==typeof e)return e},custom:function(e){if("object"==typeof e)return e},contactName:function(e){if("string"==typeof e)return e}}}},r.C={STATUS_INIT:0,STATUS_STARTING:1,STATUS_READY:2,STATUS_USER_CLOSED:3,STATUS_NOT_READY:4,CONFIGURATION_ERROR:1,NETWORK_ERROR:2,ALLOWED_METHODS:["ACK","CANCEL","INVITE","MESSAGE","BYE","OPTIONS","INFO","NOTIFY","REFER"],ACCEPTED_BODY_TYPES:["application/sdp","application/dtmf-relay"],MAX_FORWARDS:70,TAG_LENGTH:10},r}(n.EventEmitter);t.UA=b}).call(this,r(12))},function(e,t,r){"use strict";(function(e){var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(0),a=r(5),c=r(2),u=r(15),h=r(35),d=function(t){function r(r,i,s){var n=t.call(this)||this;n.type=o.TypeStrings.SessionDescriptionHandler,n.options=s||{},n.logger=r,n.observer=i,n.dtmfSender=void 0,n.shouldAcquireMedia=!0,n.CONTENT_TYPE="application/sdp",n.C={DIRECTION:{NULL:null,SENDRECV:"sendrecv",SENDONLY:"sendonly",RECVONLY:"recvonly",INACTIVE:"inactive"}},n.logger.log("SessionDescriptionHandlerOptions: "+JSON.stringify(n.options)),n.direction=n.C.DIRECTION.NULL,n.modifiers=n.options.modifiers||[],Array.isArray(n.modifiers)||(n.modifiers=[n.modifiers]);var a=e.window||e;return n.WebRTC={MediaStream:a.MediaStream,getUserMedia:a.navigator.mediaDevices.getUserMedia.bind(a.navigator.mediaDevices),RTCPeerConnection:a.RTCPeerConnection},n.iceGatheringTimeout=!1,n.initPeerConnection(n.options.peerConnectionOptions),n.constraints=n.checkAndDefaultConstraints(n.options.constraints),n}return s(r,t),r.defaultFactory=function(e,t){return new r(e.ua.getLogger("sip.invitecontext.sessionDescriptionHandler",e.id),new h.SessionDescriptionHandlerObserver(e,t),t)},r.prototype.close=function(){this.logger.log("closing PeerConnection"),this.peerConnection&&"closed"!==this.peerConnection.signalingState&&(this.peerConnection.getSenders?this.peerConnection.getSenders().forEach(function(e){e.track&&e.track.stop()}):(this.logger.warn("Using getLocalStreams which is deprecated"),this.peerConnection.getLocalStreams().forEach(function(e){e.getTracks().forEach(function(e){e.stop()})})),this.peerConnection.getReceivers?this.peerConnection.getReceivers().forEach(function(e){e.track&&e.track.stop()}):(this.logger.warn("Using getRemoteStreams which is deprecated"),this.peerConnection.getRemoteStreams().forEach(function(e){e.getTracks().forEach(function(e){e.stop()})})),this.resetIceGatheringComplete(),this.peerConnection.close())},r.prototype.getDescription=function(e,t){var r=this;void 0===e&&(e={}),void 0===t&&(t=[]),e.peerConnectionOptions&&this.initPeerConnection(e.peerConnectionOptions);var i=Object.assign({},this.constraints,e.constraints);return i=this.checkAndDefaultConstraints(i),JSON.stringify(i)!==JSON.stringify(this.constraints)&&(this.constraints=i,this.shouldAcquireMedia=!0),Array.isArray(t)||(t=[t]),t=t.concat(this.modifiers),Promise.resolve().then(function(){if(r.shouldAcquireMedia)return r.acquire(r.constraints).then(function(){r.shouldAcquireMedia=!1})}).then(function(){return r.createOfferOrAnswer(e.RTCOfferOptions,t)}).then(function(e){return r.emit("getDescription",e),{body:e.sdp,contentType:r.CONTENT_TYPE}})},r.prototype.hasDescription=function(e){return e===this.CONTENT_TYPE},r.prototype.holdModifier=function(e){return e.sdp?(/a=(sendrecv|sendonly|recvonly|inactive)/.test(e.sdp)?(e.sdp=e.sdp.replace(/a=sendrecv\r\n/g,"a=sendonly\r\n"),e.sdp=e.sdp.replace(/a=recvonly\r\n/g,"a=inactive\r\n")):e.sdp=e.sdp.replace(/(m=[^\r]*\r\n)/g,"$1a=sendonly\r\n"),Promise.resolve(e)):Promise.resolve(e)},r.prototype.setDescription=function(e,t,r){var i=this;void 0===t&&(t={}),void 0===r&&(r=[]),t.peerConnectionOptions&&this.initPeerConnection(t.peerConnectionOptions),Array.isArray(r)||(r=[r]),r=r.concat(this.modifiers);var s={type:this.hasOffer("local")?"answer":"offer",sdp:e};return Promise.resolve().then(function(){if(i.shouldAcquireMedia&&i.options.alwaysAcquireMediaFirst)return i.acquire(i.constraints).then(function(){i.shouldAcquireMedia=!1})}).then(function(){return c.Utils.reducePromises(r,s)}).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var t=new a.Exceptions.SessionDescriptionHandlerError("setDescription",e,"The modifiers did not resolve successfully");throw i.logger.error(t.message),i.emit("peerConnection-setRemoteDescriptionFailed",t),t}).then(function(e){return i.emit("setDescription",e),i.peerConnection.setRemoteDescription(e)}).catch(function(s){if(s.type===o.TypeStrings.SessionDescriptionHandlerError)throw s;if(/^m=video.+$/gm.test(e)&&!t.disableAudioFallback)return t.disableAudioFallback=!0,i.setDescription(e,t,[u.stripVideo].concat(r));var n=new a.Exceptions.SessionDescriptionHandlerError("setDescription",s);throw n.error&&i.logger.error(n.error),i.emit("peerConnection-setRemoteDescriptionFailed",n),n}).then(function(){i.peerConnection.getReceivers?i.emit("setRemoteDescription",i.peerConnection.getReceivers()):i.emit("setRemoteDescription",i.peerConnection.getRemoteStreams()),i.emit("confirmed",i)})},r.prototype.sendDtmf=function(e,t){if(void 0===t&&(t={}),!this.dtmfSender&&this.hasBrowserGetSenderSupport()){var r=this.peerConnection.getSenders();r.length>0&&(this.dtmfSender=r[0].dtmf)}if(!this.dtmfSender&&this.hasBrowserTrackSupport()){var i=this.peerConnection.getLocalStreams();if(i.length>0){var s=i[0].getAudioTracks();s.length>0&&(this.dtmfSender=this.peerConnection.createDTMFSender(s[0]))}}if(!this.dtmfSender)return!1;try{this.dtmfSender.insertDTMF(e,t.duration,t.interToneGap)}catch(e){if("InvalidStateError"===e.type||"InvalidCharacterError"===e.type)return this.logger.error(e),!1;throw e}return this.logger.log("DTMF sent via RTP: "+e.toString()),!0},r.prototype.getDirection=function(){return this.direction},r.prototype.createOfferOrAnswer=function(e,t){var r=this;void 0===e&&(e={}),void 0===t&&(t=[]);var i=this.hasOffer("remote")?"createAnswer":"createOffer",s=this.peerConnection;return this.logger.log(i),s[i](e).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var t=new a.Exceptions.SessionDescriptionHandlerError("createOfferOrAnswer",e,"peerConnection-"+i+"Failed");throw r.emit("peerConnection-"+i+"Failed",t),t}).then(function(e){return c.Utils.reducePromises(t,r.createRTCSessionDescriptionInit(e))}).then(function(e){return r.resetIceGatheringComplete(),r.logger.log("Setting local sdp."),r.logger.log("sdp is "+e.sdp||!1),s.setLocalDescription(e)}).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var t=new a.Exceptions.SessionDescriptionHandlerError("createOfferOrAnswer",e,"peerConnection-SetLocalDescriptionFailed");throw r.emit("peerConnection-SetLocalDescriptionFailed",t),t}).then(function(){return r.waitForIceGatheringComplete()}).then(function(){var e=r.createRTCSessionDescriptionInit(r.peerConnection.localDescription);return c.Utils.reducePromises(t,e)}).then(function(e){return r.setDirection(e.sdp||""),e}).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var t=new a.Exceptions.SessionDescriptionHandlerError("createOfferOrAnswer",e);throw r.logger.error(t.toString()),t})},r.prototype.createRTCSessionDescriptionInit=function(e){return{type:e.type,sdp:e.sdp}},r.prototype.addDefaultIceCheckingTimeout=function(e){return void 0===e.iceCheckingTimeout&&(e.iceCheckingTimeout=5e3),e},r.prototype.addDefaultIceServers=function(e){return e.iceServers||(e.iceServers=[{urls:"stun:stun.l.google.com:19302"}]),e},r.prototype.checkAndDefaultConstraints=function(e){var t={audio:!0,video:!this.options.alwaysAcquireMediaFirst};return e=e||t,0===Object.keys(e).length&&e.constructor===Object?t:e},r.prototype.hasBrowserTrackSupport=function(){return Boolean(this.peerConnection.addTrack)},r.prototype.hasBrowserGetSenderSupport=function(){return Boolean(this.peerConnection.getSenders)},r.prototype.initPeerConnection=function(e){var t=this;void 0===e&&(e={}),(e=this.addDefaultIceCheckingTimeout(e)).rtcConfiguration=e.rtcConfiguration||{},e.rtcConfiguration=this.addDefaultIceServers(e.rtcConfiguration),this.logger.log("initPeerConnection"),this.peerConnection&&(this.logger.log("Already have a peer connection for this session. Tearing down."),this.resetIceGatheringComplete(),this.peerConnection.close()),this.peerConnection=new this.WebRTC.RTCPeerConnection(e.rtcConfiguration),this.logger.log("New peer connection created"),"ontrack"in this.peerConnection?this.peerConnection.addEventListener("track",function(e){t.logger.log("track added"),t.observer.trackAdded(),t.emit("addTrack",e)}):(this.logger.warn("Using onaddstream which is deprecated"),this.peerConnection.onaddstream=function(e){t.logger.log("stream added"),t.emit("addStream",e)}),this.peerConnection.onicecandidate=function(e){t.emit("iceCandidate",e),e.candidate?t.logger.log("ICE candidate received: "+(null===e.candidate.candidate?null:e.candidate.candidate.trim())):null===e.candidate&&(t.logger.log("ICE candidate gathering complete"),t.triggerIceGatheringComplete())},this.peerConnection.onicegatheringstatechange=function(){switch(t.logger.log("RTCIceGatheringState changed: "+t.peerConnection.iceGatheringState),t.peerConnection.iceGatheringState){case"gathering":t.emit("iceGathering",t),!t.iceGatheringTimer&&e.iceCheckingTimeout&&(t.iceGatheringTimeout=!1,t.iceGatheringTimer=setTimeout(function(){t.logger.log("RTCIceChecking Timeout Triggered after "+e.iceCheckingTimeout+" milliseconds"),t.iceGatheringTimeout=!0,t.triggerIceGatheringComplete()},e.iceCheckingTimeout));break;case"complete":t.triggerIceGatheringComplete()}},this.peerConnection.oniceconnectionstatechange=function(){var e;switch(t.peerConnection.iceConnectionState){case"new":e="iceConnection";break;case"checking":e="iceConnectionChecking";break;case"connected":e="iceConnectionConnected";break;case"completed":e="iceConnectionCompleted";break;case"failed":e="iceConnectionFailed";break;case"disconnected":e="iceConnectionDisconnected";break;case"closed":e="iceConnectionClosed";break;default:return void t.logger.warn("Unknown iceConnection state: "+t.peerConnection.iceConnectionState)}t.logger.log("ICE Connection State changed to "+e),t.emit(e,t)}},r.prototype.acquire=function(e){var t=this;return e=this.checkAndDefaultConstraints(e),new Promise(function(r,i){t.logger.log("acquiring local media"),t.emit("userMediaRequest",e),e.audio||e.video?t.WebRTC.getUserMedia(e).then(function(e){t.observer.trackAdded(),t.emit("userMedia",e),r(e)}).catch(function(e){t.emit("userMediaFailed",e),i(e)}):r([])}).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var r=new a.Exceptions.SessionDescriptionHandlerError("acquire",e,"unable to acquire streams");throw t.logger.error(r.message),r.error&&t.logger.error(r.error),r}).then(function(e){t.logger.log("acquired local media streams");try{return t.peerConnection.removeTrack&&t.peerConnection.getSenders().forEach(function(e){t.peerConnection.removeTrack(e)}),e}catch(e){return Promise.reject(e)}}).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var r=new a.Exceptions.SessionDescriptionHandlerError("acquire",e,"error removing streams");throw t.logger.error(r.message),r.error&&t.logger.error(r.error),r}).then(function(e){try{(e=[].concat(e)).forEach(function(e){t.peerConnection.addTrack?e.getTracks().forEach(function(r){t.peerConnection.addTrack(r,e)}):t.peerConnection.addStream(e)})}catch(e){return Promise.reject(e)}return Promise.resolve()}).catch(function(e){if(e.type===o.TypeStrings.SessionDescriptionHandlerError)throw e;var r=new a.Exceptions.SessionDescriptionHandlerError("acquire",e,"error adding stream");throw t.logger.error(r.message),r.error&&t.logger.error(r.error),r})},r.prototype.hasOffer=function(e){var t="have-"+e+"-offer";return this.peerConnection.signalingState===t},r.prototype.isIceGatheringComplete=function(){return"complete"===this.peerConnection.iceGatheringState||this.iceGatheringTimeout},r.prototype.resetIceGatheringComplete=function(){this.iceGatheringTimeout=!1,this.logger.log("resetIceGatheringComplete"),this.iceGatheringTimer&&(clearTimeout(this.iceGatheringTimer),this.iceGatheringTimer=void 0),this.iceGatheringDeferred&&(this.iceGatheringDeferred.reject(),this.iceGatheringDeferred=void 0)},r.prototype.setDirection=function(e){var t=e.match(/a=(sendrecv|sendonly|recvonly|inactive)/);if(null===t)return this.direction=this.C.DIRECTION.NULL,void this.observer.directionChanged();var r=t[1];switch(r){case this.C.DIRECTION.SENDRECV:case this.C.DIRECTION.SENDONLY:case this.C.DIRECTION.RECVONLY:case this.C.DIRECTION.INACTIVE:this.direction=r;break;default:this.direction=this.C.DIRECTION.NULL}this.observer.directionChanged()},r.prototype.triggerIceGatheringComplete=function(){this.isIceGatheringComplete()&&(this.emit("iceGatheringComplete",this),this.iceGatheringTimer&&(clearTimeout(this.iceGatheringTimer),this.iceGatheringTimer=void 0),this.iceGatheringDeferred&&(this.iceGatheringDeferred.resolve(),this.iceGatheringDeferred=void 0))},r.prototype.waitForIceGatheringComplete=function(){return this.logger.log("waitForIceGatheringComplete"),this.isIceGatheringComplete()?(this.logger.log("ICE is already complete. Return resolved."),Promise.resolve()):(this.iceGatheringDeferred||(this.iceGatheringDeferred=c.Utils.defer()),this.logger.log("ICE is not complete. Returning promise"),this.iceGatheringDeferred?this.iceGatheringDeferred.promise:Promise.resolve())},r}(n.EventEmitter);t.SessionDescriptionHandler=d}).call(this,r(12))},function(e,t,r){"use strict";(function(e){var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n,o=r(0),a=r(5),c=r(4),u=r(26),h=r(2);!function(e){e[e.STATUS_CONNECTING=0]="STATUS_CONNECTING",e[e.STATUS_OPEN=1]="STATUS_OPEN",e[e.STATUS_CLOSING=2]="STATUS_CLOSING",e[e.STATUS_CLOSED=3]="STATUS_CLOSED"}(n=t.TransportStatus||(t.TransportStatus={}));var d=function(t){function r(r,i){void 0===i&&(i={});var s=t.call(this,r,i)||this;return s.WebSocket=(e.window||e).WebSocket,s.type=o.TypeStrings.Transport,s.reconnectionAttempts=0,s.status=n.STATUS_CONNECTING,s.configuration={},s.loadConfig(i),s}return s(r,t),r.prototype.isConnected=function(){return this.status===n.STATUS_OPEN},r.prototype.sendPromise=function(e,t){if(void 0===t&&(t={}),!this.statusAssert(n.STATUS_OPEN,t.force))return this.onError("unable to send message - WebSocket not open"),Promise.reject();var r=e.toString();return this.ws?(!0===this.configuration.traceSip&&this.logger.log("sending WebSocket message:\n\n"+r+"\n"),this.ws.send(r),Promise.resolve({msg:r})):(this.onError("unable to send message - WebSocket does not exist"),Promise.reject())},r.prototype.disconnectPromise=function(e){var t=this;return void 0===e&&(e={}),this.disconnectionPromise?this.disconnectionPromise:(e.code=e.code||1e3,this.statusTransition(n.STATUS_CLOSING,e.force)?(this.emit("disconnecting"),this.disconnectionPromise=new Promise(function(r,i){t.disconnectDeferredResolve=r,t.reconnectTimer&&(clearTimeout(t.reconnectTimer),t.reconnectTimer=void 0),t.ws?(t.stopSendingKeepAlives(),t.logger.log("closing WebSocket "+t.server.wsUri),t.ws.close(e.code,e.reason)):i("Attempted to disconnect but the websocket doesn't exist")}),this.disconnectionPromise):this.status===n.STATUS_CLOSED?Promise.resolve({overrideEvent:!0}):this.connectionPromise?this.connectionPromise.then(function(){return Promise.reject("The websocket did not disconnect")}).catch(function(){return Promise.resolve({overrideEvent:!0})}):Promise.reject("The websocket did not disconnect"))},r.prototype.connectPromise=function(e){var t=this;return void 0===e&&(e={}),this.status!==n.STATUS_CLOSING||e.force?this.connectionPromise?this.connectionPromise:(this.server=this.server||this.getNextWsServer(e.force),this.connectionPromise=new Promise(function(r,i){if((t.status===n.STATUS_OPEN||t.status===n.STATUS_CLOSING)&&!e.force)return t.logger.warn("WebSocket "+t.server.wsUri+" is already connected"),void i("Failed status check - attempted to open a connection but already open/closing");t.connectDeferredResolve=r,t.status=n.STATUS_CONNECTING,t.emit("connecting"),t.logger.log("connecting to WebSocket "+t.server.wsUri),t.disposeWs();try{t.ws=new WebSocket(t.server.wsUri,"sip")}catch(e){return t.ws=null,t.status=n.STATUS_CLOSED,t.onError("error connecting to WebSocket "+t.server.wsUri+":"+e),void i("Failed to create a websocket")}t.ws?(t.connectionTimeout=setTimeout(function(){t.statusTransition(n.STATUS_CLOSED),t.logger.warn("took too long to connect - exceeded time set in configuration.connectionTimeout: "+t.configuration.connectionTimeout+"s"),t.emit("disconnected",{code:1e3}),t.connectionPromise=void 0,i("Connection timeout")},1e3*t.configuration.connectionTimeout),t.boundOnOpen=t.onOpen.bind(t),t.boundOnMessage=t.onMessage.bind(t),t.boundOnClose=t.onClose.bind(t),t.boundOnError=t.onWebsocketError.bind(t),t.ws.addEventListener("open",t.boundOnOpen),t.ws.addEventListener("message",t.boundOnMessage),t.ws.addEventListener("close",t.boundOnClose),t.ws.addEventListener("error",t.boundOnError)):i("Unexpected instance websocket not set")}),this.connectionPromise):Promise.reject("WebSocket "+this.server.wsUri+" is closing")},r.prototype.onMessage=function(e){var t,r=e.data;if(/^(\r\n)+$/.test(r))return this.clearKeepAliveTimeout(),void(!0===this.configuration.traceSip&&this.logger.log("received WebSocket message with CRLF Keep Alive response"));if(r){if("string"!=typeof r){try{t=String.fromCharCode.apply(null,new Uint8Array(r))}catch(e){return void this.logger.warn("received WebSocket binary message failed to be converted into string, message discarded")}!0===this.configuration.traceSip&&this.logger.log("received WebSocket binary message:\n\n"+r+"\n")}else!0===this.configuration.traceSip&&this.logger.log("received WebSocket text message:\n\n"+r+"\n"),t=r;this.emit("message",t)}else this.logger.warn("received empty message, message discarded")},r.prototype.onOpen=function(){if(this.status===n.STATUS_CLOSED){var e=this.ws;return this.disposeWs(),void e.close(1e3)}this.status=n.STATUS_OPEN,this.emit("connected"),this.connectionTimeout&&(clearTimeout(this.connectionTimeout),this.connectionTimeout=void 0),this.logger.log("WebSocket "+this.server.wsUri+" connected"),void 0!==this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=void 0),this.reconnectionAttempts=0,this.disconnectionPromise=void 0,this.disconnectDeferredResolve=void 0,this.startSendingKeepAlives(),this.connectDeferredResolve?this.connectDeferredResolve({overrideEvent:!0}):this.logger.warn("Unexpected websocket.onOpen with no connectDeferredResolve")},r.prototype.onClose=function(e){if(this.logger.log("WebSocket disconnected (code: "+e.code+(e.reason?"| reason: "+e.reason:"")+")"),this.status!==n.STATUS_CLOSING&&(this.logger.warn("WebSocket closed without SIP.js requesting it"),this.emit("transportError")),this.stopSendingKeepAlives(),this.connectionTimeout&&clearTimeout(this.connectionTimeout),this.connectionTimeout=void 0,this.connectionPromise=void 0,this.connectDeferredResolve=void 0,this.disconnectDeferredResolve)return this.disconnectDeferredResolve({overrideEvent:!0}),this.statusTransition(n.STATUS_CLOSED),void(this.disconnectDeferredResolve=void 0);this.status=n.STATUS_CLOSED,this.emit("disconnected",{code:e.code,reason:e.reason}),this.reconnect()},r.prototype.disposeWs=function(){this.ws&&(this.ws.removeEventListener("open",this.boundOnOpen),this.ws.removeEventListener("message",this.boundOnMessage),this.ws.removeEventListener("close",this.boundOnClose),this.ws.removeEventListener("error",this.boundOnError),this.ws=void 0)},r.prototype.onError=function(e){this.logger.warn("Transport error: "+e),this.emit("transportError")},r.prototype.onWebsocketError=function(){this.onError("The Websocket had an error")},r.prototype.reconnect=function(){var e=this;if(this.reconnectionAttempts>0&&this.logger.log("Reconnection attempt "+this.reconnectionAttempts+" failed"),this.noAvailableServers())return this.logger.warn("no available ws servers left - going to closed state"),this.status=n.STATUS_CLOSED,this.emit("closed"),void this.resetServerErrorStatus();this.isConnected()&&(this.logger.warn("attempted to reconnect while connected - forcing disconnect"),this.disconnect({force:!0})),this.reconnectionAttempts+=1,this.reconnectionAttempts>this.configuration.maxReconnectionAttempts?(this.logger.warn("maximum reconnection attempts for WebSocket "+this.server.wsUri),this.logger.log("transport "+this.server.wsUri+" failed | connection state set to 'error'"),this.server.isError=!0,this.emit("transportError"),this.server=this.getNextWsServer(),this.reconnectionAttempts=0,this.reconnect()):(this.logger.log("trying to reconnect to WebSocket "+this.server.wsUri+" (reconnection attempt "+this.reconnectionAttempts+")"),this.reconnectTimer=setTimeout(function(){e.connect(),e.reconnectTimer=void 0},1===this.reconnectionAttempts?0:1e3*this.configuration.reconnectionTimeout))},r.prototype.resetServerErrorStatus=function(){for(var e=0,t=this.configuration.wsServers;e<t.length;e++){t[e].isError=!1}},r.prototype.getNextWsServer=function(e){if(void 0===e&&(e=!1),!this.noAvailableServers()){for(var t=[],r=0,i=this.configuration.wsServers;r<i.length;r++){var s=i[r];s.isError&&!e||(0===t.length?t.push(s):s.weight>t[0].weight?t=[s]:s.weight===t[0].weight&&t.push(s))}return t[Math.floor(Math.random()*t.length)]}this.logger.warn("attempted to get next ws server but there are no available ws servers left")},r.prototype.noAvailableServers=function(){for(var e=0,t=this.configuration.wsServers;e<t.length;e++){if(!t[e].isError)return!1}return!0},r.prototype.sendKeepAlive=function(){var e=this;if(!this.keepAliveDebounceTimeout)return this.keepAliveDebounceTimeout=setTimeout(function(){e.emit("keepAliveDebounceTimeout"),e.clearKeepAliveTimeout()},1e3*this.configuration.keepAliveDebounce),this.send("\r\n\r\n")},r.prototype.clearKeepAliveTimeout=function(){this.keepAliveDebounceTimeout&&clearTimeout(this.keepAliveDebounceTimeout),this.keepAliveDebounceTimeout=void 0},r.prototype.startSendingKeepAlives=function(){var e,t,r=this;this.configuration.keepAliveInterval&&!this.keepAliveInterval&&(this.keepAliveInterval=setInterval(function(){r.sendKeepAlive(),r.startSendingKeepAlives()},(e=this.configuration.keepAliveInterval,t=.8*e,1e3*(Math.random()*(e-t)+t))))},r.prototype.stopSendingKeepAlives=function(){this.keepAliveInterval&&clearInterval(this.keepAliveInterval),this.keepAliveDebounceTimeout&&clearTimeout(this.keepAliveDebounceTimeout),this.keepAliveInterval=void 0,this.keepAliveDebounceTimeout=void 0},r.prototype.statusAssert=function(e,t){return e===this.status||(t?(this.logger.warn("Attempted to assert "+Object.keys(n)[this.status]+" as "+Object.keys(n)[e]+"- continuing with option: 'force'"),!0):(this.logger.warn("Tried to assert "+Object.keys(n)[e]+" but is currently "+Object.keys(n)[this.status]),!1))},r.prototype.statusTransition=function(e,t){return void 0===t&&(t=!1),this.logger.log("Attempting to transition status from "+Object.keys(n)[this.status]+" to "+Object.keys(n)[e]),e===n.STATUS_CONNECTING&&this.statusAssert(n.STATUS_CLOSED,t)||e===n.STATUS_OPEN&&this.statusAssert(n.STATUS_CONNECTING,t)||e===n.STATUS_CLOSING&&this.statusAssert(n.STATUS_OPEN,t)||e===n.STATUS_CLOSED?(this.status=e,!0):(this.logger.warn("Status transition failed - result: no-op - reason: either gave an nonexistent status or attempted illegal transition"),!1)},r.prototype.loadConfig=function(e){var t={wsServers:[{scheme:"WSS",sipUri:"<sip:edge.sip.onsip.com;transport=ws;lr>",weight:0,wsUri:"wss://edge.sip.onsip.com",isError:!1}],connectionTimeout:5,maxReconnectionAttempts:3,reconnectionTimeout:4,keepAliveInterval:0,keepAliveDebounce:10,traceSip:!1},r=this.getConfigurationCheck();for(var i in r.mandatory){if(!e.hasOwnProperty(i))throw new a.Exceptions.ConfigurationError(i);var s=e[i];if(void 0===(n=r.mandatory[i](s)))throw new a.Exceptions.ConfigurationError(i,s);t[i]=n}for(var i in r.optional)if(e.hasOwnProperty(i)){var n;if((s=e[i])instanceof Array&&0===s.length||null===s||""===s||void 0===s||"number"==typeof s&&isNaN(s))continue;if(void 0===(n=r.optional[i](s)))throw new a.Exceptions.ConfigurationError(i,s);t[i]=n}var o={};for(var i in t)t.hasOwnProperty(i)&&(o[i]={value:t[i]});for(var i in Object.defineProperties(this.configuration,o),this.logger.log("configuration parameters after validation:"),t)t.hasOwnProperty(i)&&this.logger.log("\xb7 "+i+": "+JSON.stringify(t[i]))},r.prototype.getConfigurationCheck=function(){return{mandatory:{},optional:{wsServers:function(e){if("string"==typeof e)e=[{wsUri:e}];else{if(!(e instanceof Array))return;for(var t=0;t<e.length;t++)"string"==typeof e[t]&&(e[t]={wsUri:e[t]})}if(0===e.length)return!1;for(var r=0,i=e;r<i.length;r++){var s=i[r];if(!s.wsUri)return;if(s.weight&&!Number(s.weight))return;var n=c.Grammar.parse(s.wsUri,"absoluteURI");if(-1===n)return;if(["wss","ws","udp"].indexOf(n.scheme)<0)return;s.sipUri="<sip:"+n.host+(n.port?":"+n.port:"")+";transport="+n.scheme.replace(/^wss$/i,"ws")+";lr>",s.weight||(s.weight=0),s.isError=!1,s.scheme=n.scheme.toUpperCase()}return e},keepAliveInterval:function(e){if(h.Utils.isDecimal(e)){var t=Number(e);if(t>0)return t}},keepAliveDebounce:function(e){if(h.Utils.isDecimal(e)){var t=Number(e);if(t>0)return t}},traceSip:function(e){if("boolean"==typeof e)return e},connectionTimeout:function(e){if(h.Utils.isDecimal(e)){var t=Number(e);if(t>0)return t}},maxReconnectionAttempts:function(e){if(h.Utils.isDecimal(e)){var t=Number(e);if(t>=0)return t}},reconnectionTimeout:function(e){if(h.Utils.isDecimal(e)){var t=Number(e);if(t>0)return t}}}}},r.C=n,r}(u.Transport);t.Transport=d}).call(this,r(12))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(7);t.ClientContext=i.ClientContext;var s=r(1);t.C=s.C;var n=r(13);t.Dialog=n.Dialog;var o=r(18);t.DigestAuthentication=o.DigestAuthentication;var a=r(0);t.DialogStatus=a.DialogStatus,t.SessionStatus=a.SessionStatus,t.TransactionStatus=a.TransactionStatus,t.TypeStrings=a.TypeStrings,t.UAStatus=a.UAStatus;var c=r(5);t.Exceptions=c.Exceptions;var u=r(4);t.Grammar=u.Grammar;var h=r(19);t.LoggerFactory=h.LoggerFactory;var d=r(17);t.NameAddrHeader=d.NameAddrHeader;var p=r(20);t.Parser=p.Parser;var l=r(21);t.PublishContext=l.PublishContext;var f=r(22);t.RegisterContext=f.RegisterContext;var g=r(8);t.RequestSender=g.RequestSender;var T=r(23).SanityCheck.sanityCheck;t.sanityCheck=T;var v=r(14);t.ServerContext=v.ServerContext;var m=r(24);t.InviteClientContext=m.InviteClientContext,t.InviteServerContext=m.InviteServerContext,t.ReferClientContext=m.ReferClientContext,t.ReferServerContext=m.ReferServerContext,t.Session=m.Session;var S=r(6);t.IncomingRequest=S.IncomingRequest,t.IncomingResponse=S.IncomingResponse,t.OutgoingRequest=S.OutgoingRequest;var C=r(25);t.Subscription=C.Subscription;var y=r(11);t.Timers=y.Timers;var A=r(9),E={AckClientTransaction:A.AckClientTransaction,checkTransaction:A.checkTransaction,InviteClientTransaction:A.InviteClientTransaction,InviteServerTransaction:A.InviteServerTransaction,NonInviteClientTransaction:A.NonInviteClientTransaction,NonInviteServerTransaction:A.NonInviteServerTransaction};t.Transactions=E;var R=r(26);t.Transport=R.Transport;var _=r(27);t.UA=_.UA;var b=r(10);t.URI=b.URI;var I=r(2);t.Utils=I.Utils;var w=r(36);t.Web=w;var D=r(16),O=D.title;t.name=O;var N=D.version;t.version=N},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(17),o=r(10),a=function(e){function t(r,i,s,n){var o=e.call(this)||this;return o.message=r,o.expected=i,o.found=s,o.location=n,o.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(o,t),o}return s(t,e),t.buildMessage=function(e,t){function r(e){return e.charCodeAt(0).toString(16).toUpperCase()}function i(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(e){return"\\x0"+r(e)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(e){return"\\x"+r(e)})}function s(e){return e.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(e){return"\\x0"+r(e)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(e){return"\\x"+r(e)})}function n(e){switch(e.type){case"literal":return'"'+i(e.text)+'"';case"class":var t=e.parts.map(function(e){return Array.isArray(e)?s(e[0])+"-"+s(e[1]):s(e)});return"["+(e.inverted?"^":"")+t+"]";case"any":return"any character";case"end":return"end of input";case"other":return e.description}}return"Expected "+function(e){var t,r,i=e.map(n);if(i.sort(),i.length>0){for(t=1,r=1;t<i.length;t++)i[t-1]!==i[t]&&(i[r]=i[t],r++);i.length=r}switch(i.length){case 1:return i[0];case 2:return i[0]+" or "+i[1];default:return i.slice(0,-1).join(", ")+", or "+i[i.length-1]}}(e)+" but "+((o=t)?'"'+i(o)+'"':"end of input")+" found.";var o},t}(Error);t.SyntaxError=a,t.parse=function(e,t){t=void 0!==t?t:{};var r,i={},s={Contact:Ga,Name_Addr_Header:function(){var e,t,r,s,n,o,a;for(e=vo,t=[],r=Ka();r!==i;)t.push(r),r=Ka();if(t!==i)if((r=ra())!==i)if(pa()!==i)if(ta()!==i){for(s=[],n=vo,(o=sa())!==i&&(a=za())!==i?n=o=[o,a]:(vo=n,n=i);n!==i;)s.push(n),n=vo,(o=sa())!==i&&(a=za())!==i?n=o=[o,a]:(vo=n,n=i);s!==i?(mo=e,t=Gi(),e=t):(vo=e,e=i)}else vo=e,e=i;else vo=e,e=i;else vo=e,e=i;else vo=e,e=i;return e},Record_Route:function(){var e,t,r,s,n,o;if(e=vo,(t=oc())!==i){for(r=[],s=vo,(n=ia())!==i&&(o=oc())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=ia())!==i&&(o=oc())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=Rs(),e=t):(vo=e,e=i)}else vo=e,e=i;return e},Request_Response:Na,SIP_URI:pa,Subscription_State:function(){var t,r,s,n,o,a;if(t=vo,(r=function(){var t,r;return t=vo,e.substr(vo,6).toLowerCase()===js?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(Gs)),r===i&&(e.substr(vo,7).toLowerCase()===Bs?(r=e.substr(vo,7),vo+=7):(r=i,0===Ao&&Do(Ws)),r===i&&(e.substr(vo,10).toLowerCase()===Ks?(r=e.substr(vo,10),vo+=10):(r=i,0===Ao&&Do(Vs)),r===i&&(r=Xo()))),r!==i&&(mo=t,r=Ys()),t=r}())!==i){for(s=[],n=vo,(o=sa())!==i&&(a=cc())!==i?n=o=[o,a]:(vo=n,n=i);n!==i;)s.push(n),n=vo,(o=sa())!==i&&(a=cc())!==i?n=o=[o,a]:(vo=n,n=i);s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t},Supported:function(){var e,t,r,s,n,o,a;if(e=vo,t=vo,(r=Xo())!==i){for(s=[],n=vo,(o=ia())!==i&&(a=Xo())!==i?(mo=n,o=Ms(r,a),n=o):(vo=n,n=i);n!==i;)s.push(n),n=vo,(o=ia())!==i&&(a=Xo())!==i?(mo=n,o=Ms(r,a),n=o):(vo=n,n=i);s!==i?(mo=t,r=ks(r,s),t=r):(vo=t,t=i)}else vo=t,t=i;return t===i&&(t=null),t!==i&&(mo=e,t=Tn(t)),e=t},Require:function(){var e,t,r,s,n,o,a;if(e=vo,t=vo,(r=Xo())!==i){for(s=[],n=vo,(o=ia())!==i&&(a=Xo())!==i?(mo=n,o=Ms(r,a),n=o):(vo=n,n=i);n!==i;)s.push(n),n=vo,(o=ia())!==i&&(a=Xo())!==i?(mo=n,o=Ms(r,a),n=o):(vo=n,n=i);s!==i?(mo=t,r=ks(r,s),t=r):(vo=t,t=i)}else vo=t,t=i;return t===i&&(t=null),t!==i&&(mo=e,t=Fs(t)),e=t},Via:function(){var e,t,r,s,n,o;if(e=vo,(t=hc())!==i){for(r=[],s=vo,(n=ia())!==i&&(o=hc())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=ia())!==i&&(o=hc())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?e=t=[t,r]:(vo=e,e=i)}else vo=e,e=i;return e},absoluteURI:Ua,Call_ID:ja,Content_Disposition:function(){var t,r,s,n,o,a;if(t=vo,(r=function(){var t,r;return t=vo,e.substr(vo,6).toLowerCase()===ri?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(ii)),r===i&&(e.substr(vo,7).toLowerCase()===si?(r=e.substr(vo,7),vo+=7):(r=i,0===Ao&&Do(ni)),r===i&&(e.substr(vo,4).toLowerCase()===oi?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(ai)),r===i&&(e.substr(vo,5).toLowerCase()===ci?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(ui)),r===i&&(r=Xo())))),r!==i&&(mo=t,r=hi()),t=r}())!==i){for(s=[],n=vo,(o=sa())!==i&&(a=$a())!==i?n=o=[o,a]:(vo=n,n=i);n!==i;)s.push(n),n=vo,(o=sa())!==i&&(a=$a())!==i?n=o=[o,a]:(vo=n,n=i);s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t},Content_Length:function(){var e,t,r;if(e=vo,t=[],(r=Uo())!==i)for(;r!==i;)t.push(r),r=Uo();else t=i;return t!==i&&(mo=e,t=vi(t)),e=t},Content_Type:function(){var t,r;return t=vo,(r=function(){var t,r,s,n,o,a,c,u;if(t=vo,(r=function(){var t;return(t=function(){var t;return e.substr(vo,4).toLowerCase()===Si?(t=e.substr(vo,4),vo+=4):(t=i,0===Ao&&Do(Ci)),t===i&&(e.substr(vo,5).toLowerCase()===yi?(t=e.substr(vo,5),vo+=5):(t=i,0===Ao&&Do(Ai)),t===i&&(e.substr(vo,5).toLowerCase()===Ei?(t=e.substr(vo,5),vo+=5):(t=i,0===Ao&&Do(Ri)),t===i&&(e.substr(vo,5).toLowerCase()===_i?(t=e.substr(vo,5),vo+=5):(t=i,0===Ao&&Do(bi)),t===i&&(e.substr(vo,11).toLowerCase()===Ii?(t=e.substr(vo,11),vo+=11):(t=i,0===Ao&&Do(wi)),t===i&&(t=Xa()))))),t}())===i&&(t=function(){var t;return e.substr(vo,7).toLowerCase()===Di?(t=e.substr(vo,7),vo+=7):(t=i,0===Ao&&Do(Oi)),t===i&&(e.substr(vo,9).toLowerCase()===Ni?(t=e.substr(vo,9),vo+=9):(t=i,0===Ao&&Do(Ui)),t===i&&(t=Xa())),t}()),t}())!==i)if((s=Zo())!==i)if((n=function(){var e;return(e=Xa())===i&&(e=Xo()),e}())!==i){for(o=[],a=vo,(c=sa())!==i&&(u=Ja())!==i?a=c=[c,u]:(vo=a,a=i);a!==i;)o.push(a),a=vo,(c=sa())!==i&&(u=Ja())!==i?a=c=[c,u]:(vo=a,a=i);o!==i?t=r=[r,s,n,o]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;return t}())!==i&&(mo=t,r=mi()),t=r},CSeq:function(){var e,t,r,s;return e=vo,(t=function(){var e,t,r;if(e=vo,t=[],(r=Uo())!==i)for(;r!==i;)t.push(r),r=Uo();else t=i;return t!==i&&(mo=e,t=Hi(t)),e=t}())!==i&&(r=Ko())!==i&&(s=Fa())!==i?e=t=[t,r,s]:(vo=e,e=i),e},displayName:Ka,Event:function(){var e,t,r,s,n,o;if(e=vo,(t=Qa())!==i){for(r=[],s=vo,(n=sa())!==i&&(o=za())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=za())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=qi(t),e=t):(vo=e,e=i)}else vo=e,e=i;return e},From:function(){var e,t,r,s,n,o;if(e=vo,(t=da())===i&&(t=Wa()),t!==i){for(r=[],s=vo,(n=sa())!==i&&(o=Za())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=Za())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=Li(),e=t):(vo=e,e=i)}else vo=e,e=i;return e},host:va,Max_Forwards:function(){var e,t,r;if(e=vo,t=[],(r=Uo())!==i)for(;r!==i;)t.push(r),r=Uo();else t=i;return t!==i&&(mo=e,t=ji(t)),e=t},Min_SE:function(){var e,t,r,s,n,o;if(e=vo,(t=Ya())!==i){for(r=[],s=vo,(n=sa())!==i&&(o=za())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=za())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=Xn(t),e=t):(vo=e,e=i)}else vo=e,e=i;return e},Proxy_Authenticate:function(){return tc()},quoted_string:aa,Refer_To:function(){var e,t,r,s,n,o;if(e=vo,(t=da())===i&&(t=Wa())===i&&(t=vo,(r=ra())===i&&(r=null),r!==i&&(s=Ua())!==i?((n=ta())===i&&(n=null),n!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i)),t!==i){for(r=[],s=vo,(n=sa())!==i&&(o=za())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=za())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=bs(),e=t):(vo=e,e=i)}else vo=e,e=i;return e},Replaces:function(){var e,t,r,s,n,o;if(e=vo,(t=function(){var e,t;return e=vo,(t=ja())!==i&&(mo=e,t=ws()),e=t}())!==i){for(r=[],s=vo,(n=sa())!==i&&(o=ac())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=ac())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=Is(),e=t):(vo=e,e=i)}else vo=e,e=i;return e},Session_Expires:function(){var e,t,r,s,n,o;if(e=vo,(t=Ya())!==i){for(r=[],s=vo,(n=sa())!==i&&(o=fc())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=fc())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=Gn(t),e=t):(vo=e,e=i)}else vo=e,e=i;return e},stun_URI:function(){var t,r,s,n;return t=vo,(r=function(){var t,r;return t=vo,e.substr(vo,5).toLowerCase()===Jn?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(Qn)),r===i&&(e.substr(vo,4).toLowerCase()===Zn?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(eo))),r!==i&&(mo=t,r=to(r)),t=r}())!==i?(58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s!==i&&(n=gc())!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i),t},To:function(){var e,t,r,s,n,o;if(e=vo,(t=da())===i&&(t=Wa()),t!==i){for(r=[],s=vo,(n=sa())!==i&&(o=uc())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=uc())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,t=vn(),e=t):(vo=e,e=i)}else vo=e,e=i;return e},turn_URI:function(){var t,r,s,n,o,a,c;return t=vo,(r=function(){var t,r;return t=vo,e.substr(vo,5).toLowerCase()===no?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(oo)),r===i&&(e.substr(vo,4).toLowerCase()===ao?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(co))),r!==i&&(mo=t,r=to(r)),t=r}())!==i?(58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s!==i&&(n=gc())!==i?(o=vo,e.substr(vo,11)===io?(a=io,vo+=11):(a=i,0===Ao&&Do(so)),a!==i&&(c=pc())!==i?o=a=[a,c]:(vo=o,o=i),o===i&&(o=null),o!==i?t=r=[r,s,n,o]:(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t},uuid:function(){var t,r,s,n,o,a;return t=vo,(r=function(){var e,t,r;return e=vo,(t=Tc())!==i&&(r=Tc())!==i?e=t=[t,r]:(vo=e,e=i),e}())!==i?(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s!==i&&Tc()!==i?(45===e.charCodeAt(vo)?(n=Y,vo++):(n=i,0===Ao&&Do(z)),n!==i&&Tc()!==i?(45===e.charCodeAt(vo)?(o=Y,vo++):(o=i,0===Ao&&Do(z)),o!==i&&Tc()!==i?(45===e.charCodeAt(vo)?(a=Y,vo++):(a=i,0===Ao&&Do(z)),a!==i&&function(){var e,t,r,s;return e=vo,(t=Tc())!==i&&(r=Tc())!==i&&(s=Tc())!==i?e=t=[t,r,s]:(vo=e,e=i),e}()!==i?(mo=t,r=uo(),t=r):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t},WWW_Authenticate:function(){return tc()},challenge:tc,sipfrag:function(){var e,t,r,s,n,o;if(e=vo,(t=Na())!==i){for(r=[],s=Da();s!==i;)r.push(s),s=Da();r!==i?(s=vo,(n=No())!==i&&(o=function(){var e,t;for(e=[],t=qo();t!==i;)e.push(t),t=qo();return e}())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?e=t=[t,r,s]:(vo=e,e=i)):(vo=e,e=i)}else vo=e,e=i;return e},Referred_By:function(){var t,r,s,n,o,a,c,u;if(t=vo,e.substr(vo,11)===ho?(r=ho,vo+=11):(r=i,0===Ao&&Do(po)),r===i&&(98===e.charCodeAt(vo)?(r=lo,vo++):(r=i,0===Ao&&Do(fo))),r!==i)if((s=Yo())!==i)if((n=function(){var e;return(e=Wa())===i&&(e=da()),e}())!==i){for(o=[],a=vo,(c=sa())!==i?((u=vc())===i&&(u=za()),u!==i?a=c=[c,u]:(vo=a,a=i)):(vo=a,a=i);a!==i;)o.push(a),a=vo,(c=sa())!==i?((u=vc())===i&&(u=za()),u!==i?a=c=[c,u]:(vo=a,a=i)):(vo=a,a=i);o!==i?t=r=[r,s,n,o]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;return t}},c=Ga,u="\r\n",h=_o("\r\n",!1),d=/^[0-9]/,p=bo([["0","9"]],!1,!1),l=/^[a-zA-Z]/,f=bo([["a","z"],["A","Z"]],!1,!1),g=/^[0-9a-fA-F]/,T=bo([["0","9"],["a","f"],["A","F"]],!1,!1),v=/^[\0-\xFF]/,m=bo([["\0","\xff"]],!1,!1),S=/^["]/,C=bo(['"'],!1,!1),y=" ",A=_o(" ",!1),E="\t",R=_o("\t",!1),_=/^[a-zA-Z0-9]/,b=bo([["a","z"],["A","Z"],["0","9"]],!1,!1),I=";",w=_o(";",!1),D="/",O=_o("/",!1),N="?",U=_o("?",!1),P=":",x=_o(":",!1),H="@",q=_o("@",!1),L="&",M=_o("&",!1),k="=",F=_o("=",!1),j="+",G=_o("+",!1),B="$",W=_o("$",!1),K=",",V=_o(",",!1),Y="-",z=_o("-",!1),$="_",X=_o("_",!1),J=".",Q=_o(".",!1),Z="!",ee=_o("!",!1),te="~",re=_o("~",!1),ie="*",se=_o("*",!1),ne="'",oe=_o("'",!1),ae="(",ce=_o("(",!1),ue=")",he=_o(")",!1),de="%",pe=_o("%",!1),le=function(){return" "},fe=function(){return":"},ge=(bo([["!","~"]],!1,!1),/^[\x80-\uFFFF]/),Te=bo([["\x80","\uffff"]],!1,!1),ve=/^[\x80-\xBF]/,me=bo([["\x80","\xbf"]],!1,!1),Se=(bo([["a","f"]],!1,!1),"`"),Ce=_o("`",!1),ye="<",Ae=_o("<",!1),Ee=">",Re=_o(">",!1),_e="\\",be=_o("\\",!1),Ie="[",we=_o("[",!1),De="]",Oe=_o("]",!1),Ne="{",Ue=_o("{",!1),Pe="}",xe=_o("}",!1),He=function(){return"*"},qe=function(){return"/"},Le=function(){return"="},Me=function(){return">"},ke=function(){return"<"},Fe=function(){return","},je=function(){return";"},Ge=function(){return":"},Be=function(){return'"'},We=(bo([["!","'"]],!1,!1),bo([["*","["]],!1,!1),/^[\]-~]/),Ke=bo([["]","~"]],!1,!1),Ve=function(e){return e},Ye=/^[#-[]/,ze=bo([["#","["]],!1,!1),$e=/^[\0-\t]/,Xe=bo([["\0","\t"]],!1,!1),Je=/^[\x0B-\f]/,Qe=bo([["\v","\f"]],!1,!1),Ze=/^[\x0E-\x7F]/,et=bo([["\x0e","\x7f"]],!1,!1),tt=function(){(t=t||{data:{}}).data.uri=new o.URI(t.data.scheme,t.data.user,t.data.host,t.data.port),delete t.data.scheme,delete t.data.user,delete t.data.host,delete t.data.host_type,delete t.data.port},rt=function(){(t=t||{data:{}}).data.uri=new o.URI(t.data.scheme,t.data.user,t.data.host,t.data.port,t.data.uri_params,t.data.uri_headers),delete t.data.scheme,delete t.data.user,delete t.data.host,delete t.data.host_type,delete t.data.port,delete t.data.uri_params,"SIP_URI"===t.startRule&&(t.data=t.data.uri)},it="sips",st=_o("sips",!0),nt="sip",ot=_o("sip",!0),at=function(e){(t=t||{data:{}}).data.scheme=e},ct=function(){(t=t||{data:{}}).data.user=decodeURIComponent(Eo().slice(0,-1))},ut=function(){(t=t||{data:{}}).data.password=Eo()},ht=function(){return(t=t||{data:{}}).data.host=Eo(),t.data.host},dt=function(){return(t=t||{data:{}}).data.host_type="domain",Eo()},pt=/^[a-zA-Z0-9_\-]/,lt=bo([["a","z"],["A","Z"],["0","9"],"_","-"],!1,!1),ft=/^[a-zA-Z0-9\-]/,gt=bo([["a","z"],["A","Z"],["0","9"],"-"],!1,!1),Tt=function(){return(t=t||{data:{}}).data.host_type="IPv6",Eo()},vt="::",mt=_o("::",!1),St=function(){return(t=t||{data:{}}).data.host_type="IPv6",Eo()},Ct=function(){return(t=t||{data:{}}).data.host_type="IPv4",Eo()},yt="25",At=_o("25",!1),Et=/^[0-5]/,Rt=bo([["0","5"]],!1,!1),_t="2",bt=_o("2",!1),It=/^[0-4]/,wt=bo([["0","4"]],!1,!1),Dt="1",Ot=_o("1",!1),Nt=/^[1-9]/,Ut=bo([["1","9"]],!1,!1),Pt=function(e){return t=t||{data:{}},e=parseInt(e.join("")),t.data.port=e,e},xt="transport=",Ht=_o("transport=",!0),qt="udp",Lt=_o("udp",!0),Mt="tcp",kt=_o("tcp",!0),Ft="sctp",jt=_o("sctp",!0),Gt="tls",Bt=_o("tls",!0),Wt=function(e){(t=t||{data:{}}).data.uri_params||(t.data.uri_params={}),t.data.uri_params.transport=e.toLowerCase()},Kt="user=",Vt=_o("user=",!0),Yt="phone",zt=_o("phone",!0),$t="ip",Xt=_o("ip",!0),Jt=function(e){(t=t||{data:{}}).data.uri_params||(t.data.uri_params={}),t.data.uri_params.user=e.toLowerCase()},Qt="method=",Zt=_o("method=",!0),er=function(e){(t=t||{data:{}}).data.uri_params||(t.data.uri_params={}),t.data.uri_params.method=e},tr="ttl=",rr=_o("ttl=",!0),ir=function(e){(t=t||{data:{}}).data.params||(t.data.params={}),t.data.params.ttl=e},sr="maddr=",nr=_o("maddr=",!0),or=function(e){(t=t||{data:{}}).data.uri_params||(t.data.uri_params={}),t.data.uri_params.maddr=e},ar="lr",cr=_o("lr",!0),ur=function(){(t=t||{data:{}}).data.uri_params||(t.data.uri_params={}),t.data.uri_params.lr=void 0},hr=function(e,r){(t=t||{data:{}}).data.uri_params||(t.data.uri_params={}),r=null===r?void 0:r[1],t.data.uri_params[e.toLowerCase()]=r},dr=function(e,r){e=e.join("").toLowerCase(),r=r.join(""),(t=t||{data:{}}).data.uri_headers||(t.data.uri_headers={}),t.data.uri_headers[e]?t.data.uri_headers[e].push(r):t.data.uri_headers[e]=[r]},pr=function(){"Refer_To"===(t=t||{data:{}}).startRule&&(t.data.uri=new o.URI(t.data.scheme,t.data.user,t.data.host,t.data.port,t.data.uri_params,t.data.uri_headers),delete t.data.scheme,delete t.data.user,delete t.data.host,delete t.data.host_type,delete t.data.port,delete t.data.uri_params)},lr="//",fr=_o("//",!1),gr=function(){(t=t||{data:{}}).data.scheme=Eo()},Tr=_o("SIP",!0),vr=function(){(t=t||{data:{}}).data.sip_version=Eo()},mr="INVITE",Sr=_o("INVITE",!1),Cr="ACK",yr=_o("ACK",!1),Ar=(_o("VXACH",!1),"OPTIONS"),Er=_o("OPTIONS",!1),Rr="BYE",_r=_o("BYE",!1),br="CANCEL",Ir=_o("CANCEL",!1),wr="REGISTER",Dr=_o("REGISTER",!1),Or="SUBSCRIBE",Nr=_o("SUBSCRIBE",!1),Ur="NOTIFY",Pr=_o("NOTIFY",!1),xr="REFER",Hr=_o("REFER",!1),qr="PUBLISH",Lr=_o("PUBLISH",!1),Mr=function(){return(t=t||{data:{}}).data.method=Eo(),t.data.method},kr=function(e){(t=t||{data:{}}).data.status_code=parseInt(e.join(""))},Fr=function(){(t=t||{data:{}}).data.reason_phrase=Eo()},jr=function(){(t=t||{data:{}}).data=Eo()},Gr=function(){var e,r;for(r=(t=t||{data:{}}).data.multi_header.length,e=0;e<r;e++)if(null===t.data.multi_header[e].parsed){t.data=null;break}null!==t.data?t.data=t.data.multi_header:t.data=-1},Br=function(){var e;(t=t||{data:{}}).data.multi_header||(t.data.multi_header=[]);try{e=new n.NameAddrHeader(t.data.uri,t.data.displayName,t.data.params),delete t.data.uri,delete t.data.displayName,delete t.data.params}catch(t){e=null}t.data.multi_header.push({position:vo,offset:Ro().start.offset,parsed:e})},Wr=function(e){'"'===(e=Eo().trim())[0]&&(e=e.substring(1,e.length-1)),(t=t||{data:{}}).data.displayName=e},Kr="q",Vr=_o("q",!0),Yr=function(e){(t=t||{data:{}}).data.params||(t.data.params={}),t.data.params.q=e},zr="expires",$r=_o("expires",!0),Xr=function(e){(t=t||{data:{}}).data.params||(t.data.params={}),t.data.params.expires=e},Jr=function(e){return parseInt(e.join(""))},Qr="0",Zr=_o("0",!1),ei=function(){return parseFloat(Eo())},ti=function(e,r){(t=t||{data:{}}).data.params||(t.data.params={}),r=null===r?void 0:r[1],t.data.params[e.toLowerCase()]=r},ri="render",ii=_o("render",!0),si="session",ni=_o("session",!0),oi="icon",ai=_o("icon",!0),ci="alert",ui=_o("alert",!0),hi=function(){"Content_Disposition"===(t=t||{data:{}}).startRule&&(t.data.type=Eo().toLowerCase())},di="handling",pi=_o("handling",!0),li="optional",fi=_o("optional",!0),gi="required",Ti=_o("required",!0),vi=function(e){(t=t||{data:{}}).data=parseInt(e.join(""))},mi=function(){(t=t||{data:{}}).data=Eo()},Si="text",Ci=_o("text",!0),yi="image",Ai=_o("image",!0),Ei="audio",Ri=_o("audio",!0),_i="video",bi=_o("video",!0),Ii="application",wi=_o("application",!0),Di="message",Oi=_o("message",!0),Ni="multipart",Ui=_o("multipart",!0),Pi="x-",xi=_o("x-",!0),Hi=function(e){(t=t||{data:{}}).data.value=parseInt(e.join(""))},qi=function(e){(t=t||{data:{}}).data.event=e.toLowerCase()},Li=function(){var e=(t=t||{data:{}}).data.tag;t.data=new n.NameAddrHeader(t.data.uri,t.data.displayName,t.data.params),e&&t.data.setParam("tag",e)},Mi="tag",ki=_o("tag",!0),Fi=function(e){(t=t||{data:{}}).data.tag=e},ji=function(e){(t=t||{data:{}}).data=parseInt(e.join(""))},Gi=function(){(t=t||{data:{}}).data=new n.NameAddrHeader(t.data.uri,t.data.displayName,t.data.params)},Bi="digest",Wi=_o("Digest",!0),Ki="realm",Vi=_o("realm",!0),Yi=function(e){(t=t||{data:{}}).data.realm=e},zi="domain",$i=_o("domain",!0),Xi="nonce",Ji=_o("nonce",!0),Qi=function(e){(t=t||{data:{}}).data.nonce=e},Zi="opaque",es=_o("opaque",!0),ts=function(e){(t=t||{data:{}}).data.opaque=e},rs="stale",is=_o("stale",!0),ss="true",ns=_o("true",!0),os=function(){(t=t||{data:{}}).data.stale=!0},as="false",cs=_o("false",!0),us=function(){(t=t||{data:{}}).data.stale=!1},hs="algorithm",ds=_o("algorithm",!0),ps="md5",ls=_o("MD5",!0),fs="md5-sess",gs=_o("MD5-sess",!0),Ts=function(e){(t=t||{data:{}}).data.algorithm=e.toUpperCase()},vs="qop",ms=_o("qop",!0),Ss="auth-int",Cs=_o("auth-int",!0),ys="auth",As=_o("auth",!0),Es=function(e){(t=t||{data:{}}).data.qop||(t.data.qop=[]),t.data.qop.push(e.toLowerCase())},Rs=function(){var e,r;for(r=(t=t||{data:{}}).data.multi_header.length,e=0;e<r;e++)if(null===t.data.multi_header[e].parsed){t.data=null;break}null!==t.data?t.data=t.data.multi_header:t.data=-1},_s=function(){var e;(t=t||{data:{}}).data.multi_header||(t.data.multi_header=[]);try{e=new n.NameAddrHeader(t.data.uri,t.data.displayName,t.data.params),delete t.data.uri,delete t.data.displayName,delete t.data.params}catch(t){e=null}t.data.multi_header.push({position:vo,offset:Ro().start.offset,parsed:e})},bs=function(){(t=t||{data:{}}).data=new n.NameAddrHeader(t.data.uri,t.data.displayName,t.data.params)},Is=function(){(t=t||{data:{}}).data.replaces_from_tag&&t.data.replaces_to_tag||(t.data=-1)},ws=function(){(t=t||{data:{}}).data={call_id:t.data}},Ds="from-tag",Os=_o("from-tag",!0),Ns=function(e){(t=t||{data:{}}).data.replaces_from_tag=e},Us="to-tag",Ps=_o("to-tag",!0),xs=function(e){(t=t||{data:{}}).data.replaces_to_tag=e},Hs="early-only",qs=_o("early-only",!0),Ls=function(){(t=t||{data:{}}).data.early_only=!0},Ms=function(e,t){return t},ks=function(e,t){return function(e,t){return[e].concat(t)}(e,t)},Fs=function(e){"Require"===(t=t||{data:{}}).startRule&&(t.data=e||[])},js="active",Gs=_o("active",!0),Bs="pending",Ws=_o("pending",!0),Ks="terminated",Vs=_o("terminated",!0),Ys=function(){(t=t||{data:{}}).data.state=Eo()},zs="reason",$s=_o("reason",!0),Xs=function(e){t=t||{data:{}},void 0!==e&&(t.data.reason=e)},Js=function(e){t=t||{data:{}},void 0!==e&&(t.data.expires=e)},Qs="retry_after",Zs=_o("retry_after",!0),en=function(e){t=t||{data:{}},void 0!==e&&(t.data.retry_after=e)},tn="deactivated",rn=_o("deactivated",!0),sn="probation",nn=_o("probation",!0),on="rejected",an=_o("rejected",!0),cn="timeout",un=_o("timeout",!0),hn="giveup",dn=_o("giveup",!0),pn="noresource",ln=_o("noresource",!0),fn="invariant",gn=_o("invariant",!0),Tn=function(e){"Supported"===(t=t||{data:{}}).startRule&&(t.data=e||[])},vn=function(){var e=(t=t||{data:{}}).data.tag;t.data=new n.NameAddrHeader(t.data.uri,t.data.displayName,t.data.params),e&&t.data.setParam("tag",e)},mn="ttl",Sn=_o("ttl",!0),Cn=function(e){(t=t||{data:{}}).data.ttl=e},yn="maddr",An=_o("maddr",!0),En=function(e){(t=t||{data:{}}).data.maddr=e},Rn="received",_n=_o("received",!0),bn=function(e){(t=t||{data:{}}).data.received=e},In="branch",wn=_o("branch",!0),Dn=function(e){(t=t||{data:{}}).data.branch=e},On="rport",Nn=_o("rport",!0),Un=function(e){t=t||{data:{}},void 0!==e&&(t.data.rport=e.join(""))},Pn=function(e){(t=t||{data:{}}).data.protocol=e},xn=_o("UDP",!0),Hn=_o("TCP",!0),qn=_o("TLS",!0),Ln=_o("SCTP",!0),Mn=function(e){(t=t||{data:{}}).data.transport=e},kn=function(){(t=t||{data:{}}).data.host=Eo()},Fn=function(e){(t=t||{data:{}}).data.port=parseInt(e.join(""))},jn=function(e){return parseInt(e.join(""))},Gn=function(e){"Session_Expires"===(t=t||{data:{}}).startRule&&(t.data.deltaSeconds=e)},Bn="refresher",Wn=_o("refresher",!1),Kn="uas",Vn=_o("uas",!1),Yn="uac",zn=_o("uac",!1),$n=function(e){"Session_Expires"===(t=t||{data:{}}).startRule&&(t.data.refresher=e)},Xn=function(e){"Min_SE"===(t=t||{data:{}}).startRule&&(t.data=e)},Jn="stuns",Qn=_o("stuns",!0),Zn="stun",eo=_o("stun",!0),to=function(e){(t=t||{data:{}}).data.scheme=e},ro=function(e){(t=t||{data:{}}).data.host=e},io="?transport=",so=_o("?transport=",!1),no="turns",oo=_o("turns",!0),ao="turn",co=_o("turn",!0),uo=function(){(t=t||{data:{}}).data=Eo()},ho="Referred-By",po=_o("Referred-By",!1),lo="b",fo=_o("b",!1),go="cid",To=_o("cid",!1),vo=0,mo=0,So=[{line:1,column:1}],Co=0,yo=[],Ao=0;if(void 0!==t.startRule){if(!(t.startRule in s))throw new Error("Can't start parsing from rule \""+t.startRule+'".');c=s[t.startRule]}function Eo(){return e.substring(mo,vo)}function Ro(){return wo(mo,vo)}function _o(e,t){return{type:"literal",text:e,ignoreCase:t}}function bo(e,t,r){return{type:"class",parts:e,inverted:t,ignoreCase:r}}function Io(t){var r,i=So[t];if(i)return i;for(r=t-1;!So[r];)r--;for(i={line:(i=So[r]).line,column:i.column};r<t;)10===e.charCodeAt(r)?(i.line++,i.column=1):i.column++,r++;return So[t]=i,i}function wo(e,t){var r=Io(e),i=Io(t);return{start:{offset:e,line:r.line,column:r.column},end:{offset:t,line:i.line,column:i.column}}}function Do(e){vo<Co||(vo>Co&&(Co=vo,yo=[]),yo.push(e))}function Oo(e,t,r){return new a(a.buildMessage(e,t),e,t,r)}function No(){var t;return e.substr(vo,2)===u?(t=u,vo+=2):(t=i,0===Ao&&Do(h)),t}function Uo(){var t;return d.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(p)),t}function Po(){var t;return l.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(f)),t}function xo(){var t;return g.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(T)),t}function Ho(){var e;return(e=Mo())===i&&(e=ko()),e}function qo(){var t;return v.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(m)),t}function Lo(){var t;return S.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(C)),t}function Mo(){var t;return 32===e.charCodeAt(vo)?(t=y,vo++):(t=i,0===Ao&&Do(A)),t}function ko(){var t;return 9===e.charCodeAt(vo)?(t=E,vo++):(t=i,0===Ao&&Do(R)),t}function Fo(){var t;return _.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(b)),t}function jo(){var t;return 59===e.charCodeAt(vo)?(t=I,vo++):(t=i,0===Ao&&Do(w)),t===i&&(47===e.charCodeAt(vo)?(t=D,vo++):(t=i,0===Ao&&Do(O)),t===i&&(63===e.charCodeAt(vo)?(t=N,vo++):(t=i,0===Ao&&Do(U)),t===i&&(58===e.charCodeAt(vo)?(t=P,vo++):(t=i,0===Ao&&Do(x)),t===i&&(64===e.charCodeAt(vo)?(t=H,vo++):(t=i,0===Ao&&Do(q)),t===i&&(38===e.charCodeAt(vo)?(t=L,vo++):(t=i,0===Ao&&Do(M)),t===i&&(61===e.charCodeAt(vo)?(t=k,vo++):(t=i,0===Ao&&Do(F)),t===i&&(43===e.charCodeAt(vo)?(t=j,vo++):(t=i,0===Ao&&Do(G)),t===i&&(36===e.charCodeAt(vo)?(t=B,vo++):(t=i,0===Ao&&Do(W)),t===i&&(44===e.charCodeAt(vo)?(t=K,vo++):(t=i,0===Ao&&Do(V))))))))))),t}function Go(){var e;return(e=Fo())===i&&(e=Bo()),e}function Bo(){var t;return 45===e.charCodeAt(vo)?(t=Y,vo++):(t=i,0===Ao&&Do(z)),t===i&&(95===e.charCodeAt(vo)?(t=$,vo++):(t=i,0===Ao&&Do(X)),t===i&&(46===e.charCodeAt(vo)?(t=J,vo++):(t=i,0===Ao&&Do(Q)),t===i&&(33===e.charCodeAt(vo)?(t=Z,vo++):(t=i,0===Ao&&Do(ee)),t===i&&(126===e.charCodeAt(vo)?(t=te,vo++):(t=i,0===Ao&&Do(re)),t===i&&(42===e.charCodeAt(vo)?(t=ie,vo++):(t=i,0===Ao&&Do(se)),t===i&&(39===e.charCodeAt(vo)?(t=ne,vo++):(t=i,0===Ao&&Do(oe)),t===i&&(40===e.charCodeAt(vo)?(t=ae,vo++):(t=i,0===Ao&&Do(ce)),t===i&&(41===e.charCodeAt(vo)?(t=ue,vo++):(t=i,0===Ao&&Do(he)))))))))),t}function Wo(){var t,r,s,n,o;return t=vo,r=vo,37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s!==i&&(n=xo())!==i&&(o=xo())!==i?r=s=[s,n,o]:(vo=r,r=i),t=r!==i?e.substring(t,vo):r}function Ko(){var e,t,r,s;for(e=vo,t=vo,r=[],s=Ho();s!==i;)r.push(s),s=Ho();if(r!==i&&(s=No())!==i?t=r=[r,s]:(vo=t,t=i),t===i&&(t=null),t!==i){if(r=[],(s=Ho())!==i)for(;s!==i;)r.push(s),s=Ho();else r=i;r!==i?(mo=e,e=t=le()):(vo=e,e=i)}else vo=e,e=i;return e}function Vo(){var e;return(e=Ko())===i&&(e=null),e}function Yo(){var t,r,s;for(t=vo,r=[],(s=Mo())===i&&(s=ko());s!==i;)r.push(s),(s=Mo())===i&&(s=ko());return r!==i?(58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s!==i&&Vo()!==i?(mo=t,t=r=fe()):(vo=t,t=i)):(vo=t,t=i),t}function zo(){var t;return ge.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(Te)),t}function $o(){var t;return ve.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(me)),t}function Xo(){var t,r,s;if(t=vo,r=[],(s=Fo())===i&&(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s===i&&(46===e.charCodeAt(vo)?(s=J,vo++):(s=i,0===Ao&&Do(Q)),s===i&&(33===e.charCodeAt(vo)?(s=Z,vo++):(s=i,0===Ao&&Do(ee)),s===i&&(37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s===i&&(42===e.charCodeAt(vo)?(s=ie,vo++):(s=i,0===Ao&&Do(se)),s===i&&(95===e.charCodeAt(vo)?(s=$,vo++):(s=i,0===Ao&&Do(X)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(96===e.charCodeAt(vo)?(s=Se,vo++):(s=i,0===Ao&&Do(Ce)),s===i&&(39===e.charCodeAt(vo)?(s=ne,vo++):(s=i,0===Ao&&Do(oe)),s===i&&(126===e.charCodeAt(vo)?(s=te,vo++):(s=i,0===Ao&&Do(re)))))))))))),s!==i)for(;s!==i;)r.push(s),(s=Fo())===i&&(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s===i&&(46===e.charCodeAt(vo)?(s=J,vo++):(s=i,0===Ao&&Do(Q)),s===i&&(33===e.charCodeAt(vo)?(s=Z,vo++):(s=i,0===Ao&&Do(ee)),s===i&&(37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s===i&&(42===e.charCodeAt(vo)?(s=ie,vo++):(s=i,0===Ao&&Do(se)),s===i&&(95===e.charCodeAt(vo)?(s=$,vo++):(s=i,0===Ao&&Do(X)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(96===e.charCodeAt(vo)?(s=Se,vo++):(s=i,0===Ao&&Do(Ce)),s===i&&(39===e.charCodeAt(vo)?(s=ne,vo++):(s=i,0===Ao&&Do(oe)),s===i&&(126===e.charCodeAt(vo)?(s=te,vo++):(s=i,0===Ao&&Do(re))))))))))));else r=i;return t=r!==i?e.substring(t,vo):r}function Jo(){var t,r,s;if(t=vo,r=[],(s=Fo())===i&&(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s===i&&(33===e.charCodeAt(vo)?(s=Z,vo++):(s=i,0===Ao&&Do(ee)),s===i&&(37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s===i&&(42===e.charCodeAt(vo)?(s=ie,vo++):(s=i,0===Ao&&Do(se)),s===i&&(95===e.charCodeAt(vo)?(s=$,vo++):(s=i,0===Ao&&Do(X)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(96===e.charCodeAt(vo)?(s=Se,vo++):(s=i,0===Ao&&Do(Ce)),s===i&&(39===e.charCodeAt(vo)?(s=ne,vo++):(s=i,0===Ao&&Do(oe)),s===i&&(126===e.charCodeAt(vo)?(s=te,vo++):(s=i,0===Ao&&Do(re))))))))))),s!==i)for(;s!==i;)r.push(s),(s=Fo())===i&&(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s===i&&(33===e.charCodeAt(vo)?(s=Z,vo++):(s=i,0===Ao&&Do(ee)),s===i&&(37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s===i&&(42===e.charCodeAt(vo)?(s=ie,vo++):(s=i,0===Ao&&Do(se)),s===i&&(95===e.charCodeAt(vo)?(s=$,vo++):(s=i,0===Ao&&Do(X)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(96===e.charCodeAt(vo)?(s=Se,vo++):(s=i,0===Ao&&Do(Ce)),s===i&&(39===e.charCodeAt(vo)?(s=ne,vo++):(s=i,0===Ao&&Do(oe)),s===i&&(126===e.charCodeAt(vo)?(s=te,vo++):(s=i,0===Ao&&Do(re)))))))))));else r=i;return t=r!==i?e.substring(t,vo):r}function Qo(){var t,r,s;if(t=vo,r=[],(s=Fo())===i&&(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s===i&&(46===e.charCodeAt(vo)?(s=J,vo++):(s=i,0===Ao&&Do(Q)),s===i&&(33===e.charCodeAt(vo)?(s=Z,vo++):(s=i,0===Ao&&Do(ee)),s===i&&(37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s===i&&(42===e.charCodeAt(vo)?(s=ie,vo++):(s=i,0===Ao&&Do(se)),s===i&&(95===e.charCodeAt(vo)?(s=$,vo++):(s=i,0===Ao&&Do(X)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(96===e.charCodeAt(vo)?(s=Se,vo++):(s=i,0===Ao&&Do(Ce)),s===i&&(39===e.charCodeAt(vo)?(s=ne,vo++):(s=i,0===Ao&&Do(oe)),s===i&&(126===e.charCodeAt(vo)?(s=te,vo++):(s=i,0===Ao&&Do(re)),s===i&&(40===e.charCodeAt(vo)?(s=ae,vo++):(s=i,0===Ao&&Do(ce)),s===i&&(41===e.charCodeAt(vo)?(s=ue,vo++):(s=i,0===Ao&&Do(he)),s===i&&(60===e.charCodeAt(vo)?(s=ye,vo++):(s=i,0===Ao&&Do(Ae)),s===i&&(62===e.charCodeAt(vo)?(s=Ee,vo++):(s=i,0===Ao&&Do(Re)),s===i&&(58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s===i&&(92===e.charCodeAt(vo)?(s=_e,vo++):(s=i,0===Ao&&Do(be)),s===i&&(s=Lo())===i&&(47===e.charCodeAt(vo)?(s=D,vo++):(s=i,0===Ao&&Do(O)),s===i&&(91===e.charCodeAt(vo)?(s=Ie,vo++):(s=i,0===Ao&&Do(we)),s===i&&(93===e.charCodeAt(vo)?(s=De,vo++):(s=i,0===Ao&&Do(Oe)),s===i&&(63===e.charCodeAt(vo)?(s=N,vo++):(s=i,0===Ao&&Do(U)),s===i&&(123===e.charCodeAt(vo)?(s=Ne,vo++):(s=i,0===Ao&&Do(Ue)),s===i&&(125===e.charCodeAt(vo)?(s=Pe,vo++):(s=i,0===Ao&&Do(xe)))))))))))))))))))))))),s!==i)for(;s!==i;)r.push(s),(s=Fo())===i&&(45===e.charCodeAt(vo)?(s=Y,vo++):(s=i,0===Ao&&Do(z)),s===i&&(46===e.charCodeAt(vo)?(s=J,vo++):(s=i,0===Ao&&Do(Q)),s===i&&(33===e.charCodeAt(vo)?(s=Z,vo++):(s=i,0===Ao&&Do(ee)),s===i&&(37===e.charCodeAt(vo)?(s=de,vo++):(s=i,0===Ao&&Do(pe)),s===i&&(42===e.charCodeAt(vo)?(s=ie,vo++):(s=i,0===Ao&&Do(se)),s===i&&(95===e.charCodeAt(vo)?(s=$,vo++):(s=i,0===Ao&&Do(X)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(96===e.charCodeAt(vo)?(s=Se,vo++):(s=i,0===Ao&&Do(Ce)),s===i&&(39===e.charCodeAt(vo)?(s=ne,vo++):(s=i,0===Ao&&Do(oe)),s===i&&(126===e.charCodeAt(vo)?(s=te,vo++):(s=i,0===Ao&&Do(re)),s===i&&(40===e.charCodeAt(vo)?(s=ae,vo++):(s=i,0===Ao&&Do(ce)),s===i&&(41===e.charCodeAt(vo)?(s=ue,vo++):(s=i,0===Ao&&Do(he)),s===i&&(60===e.charCodeAt(vo)?(s=ye,vo++):(s=i,0===Ao&&Do(Ae)),s===i&&(62===e.charCodeAt(vo)?(s=Ee,vo++):(s=i,0===Ao&&Do(Re)),s===i&&(58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s===i&&(92===e.charCodeAt(vo)?(s=_e,vo++):(s=i,0===Ao&&Do(be)),s===i&&(s=Lo())===i&&(47===e.charCodeAt(vo)?(s=D,vo++):(s=i,0===Ao&&Do(O)),s===i&&(91===e.charCodeAt(vo)?(s=Ie,vo++):(s=i,0===Ao&&Do(we)),s===i&&(93===e.charCodeAt(vo)?(s=De,vo++):(s=i,0===Ao&&Do(Oe)),s===i&&(63===e.charCodeAt(vo)?(s=N,vo++):(s=i,0===Ao&&Do(U)),s===i&&(123===e.charCodeAt(vo)?(s=Ne,vo++):(s=i,0===Ao&&Do(Ue)),s===i&&(125===e.charCodeAt(vo)?(s=Pe,vo++):(s=i,0===Ao&&Do(xe))))))))))))))))))))))));else r=i;return t=r!==i?e.substring(t,vo):r}function Zo(){var t,r;return t=vo,Vo()!==i?(47===e.charCodeAt(vo)?(r=D,vo++):(r=i,0===Ao&&Do(O)),r!==i&&Vo()!==i?(mo=t,t=qe()):(vo=t,t=i)):(vo=t,t=i),t}function ea(){var t,r;return t=vo,Vo()!==i?(61===e.charCodeAt(vo)?(r=k,vo++):(r=i,0===Ao&&Do(F)),r!==i&&Vo()!==i?(mo=t,t=Le()):(vo=t,t=i)):(vo=t,t=i),t}function ta(){var t,r;return t=vo,62===e.charCodeAt(vo)?(r=Ee,vo++):(r=i,0===Ao&&Do(Re)),r!==i&&Vo()!==i?(mo=t,t=r=Me()):(vo=t,t=i),t}function ra(){var t,r;return t=vo,Vo()!==i?(60===e.charCodeAt(vo)?(r=ye,vo++):(r=i,0===Ao&&Do(Ae)),r!==i?(mo=t,t=ke()):(vo=t,t=i)):(vo=t,t=i),t}function ia(){var t,r;return t=vo,Vo()!==i?(44===e.charCodeAt(vo)?(r=K,vo++):(r=i,0===Ao&&Do(V)),r!==i&&Vo()!==i?(mo=t,t=Fe()):(vo=t,t=i)):(vo=t,t=i),t}function sa(){var t,r;return t=vo,Vo()!==i?(59===e.charCodeAt(vo)?(r=I,vo++):(r=i,0===Ao&&Do(w)),r!==i&&Vo()!==i?(mo=t,t=je()):(vo=t,t=i)):(vo=t,t=i),t}function na(){var e;return e=vo,Vo()!==i&&Lo()!==i?(mo=e,e=Be()):(vo=e,e=i),e}function oa(){var e;return e=vo,Lo()!==i&&Vo()!==i?(mo=e,e=Be()):(vo=e,e=i),e}function aa(){var t,r,s,n,o,a;if(t=vo,r=vo,(s=Vo())!==i)if((n=Lo())!==i){for(o=[],(a=ua())===i&&(a=ha());a!==i;)o.push(a),(a=ua())===i&&(a=ha());o!==i&&(a=Lo())!==i?r=s=[s,n,o,a]:(vo=r,r=i)}else vo=r,r=i;else vo=r,r=i;return t=r!==i?e.substring(t,vo):r}function ca(){var t,r,s,n;if(t=vo,Vo()!==i)if(Lo()!==i){for(r=vo,s=[],(n=ua())===i&&(n=ha());n!==i;)s.push(n),(n=ua())===i&&(n=ha());(r=s!==i?e.substring(r,vo):s)!==i&&(s=Lo())!==i?(mo=t,t=Ve(r)):(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;return t}function ua(){var t;return(t=Ko())===i&&(33===e.charCodeAt(vo)?(t=Z,vo++):(t=i,0===Ao&&Do(ee)),t===i&&(Ye.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(ze)),t===i&&(We.test(e.charAt(vo))?(t=e.charAt(vo),vo++):(t=i,0===Ao&&Do(Ke)),t===i&&(t=zo())))),t}function ha(){var t,r,s;return t=vo,92===e.charCodeAt(vo)?(r=_e,vo++):(r=i,0===Ao&&Do(be)),r!==i?($e.test(e.charAt(vo))?(s=e.charAt(vo),vo++):(s=i,0===Ao&&Do(Xe)),s===i&&(Je.test(e.charAt(vo))?(s=e.charAt(vo),vo++):(s=i,0===Ao&&Do(Qe)),s===i&&(Ze.test(e.charAt(vo))?(s=e.charAt(vo),vo++):(s=i,0===Ao&&Do(et)))),s!==i?t=r=[r,s]:(vo=t,t=i)):(vo=t,t=i),t}function da(){var t,r,s;return t=vo,la()!==i?(58===e.charCodeAt(vo)?(r=P,vo++):(r=i,0===Ao&&Do(x)),r!==i?((s=fa())===i&&(s=null),s!==i&&Ta()!==i?(mo=t,t=tt()):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t}function pa(){var t,r,s,n;return t=vo,la()!==i?(58===e.charCodeAt(vo)?(r=P,vo++):(r=i,0===Ao&&Do(x)),r!==i?((s=fa())===i&&(s=null),s!==i&&Ta()!==i&&function(){var t,r,s,n;for(t=[],r=vo,59===e.charCodeAt(vo)?(s=I,vo++):(s=i,0===Ao&&Do(w)),s!==i&&(n=Ia())!==i?r=s=[s,n]:(vo=r,r=i);r!==i;)t.push(r),r=vo,59===e.charCodeAt(vo)?(s=I,vo++):(s=i,0===Ao&&Do(w)),s!==i&&(n=Ia())!==i?r=s=[s,n]:(vo=r,r=i);return t}()!==i?((n=function(){var t,r,s,n,o,a,c;if(t=vo,63===e.charCodeAt(vo)?(r=N,vo++):(r=i,0===Ao&&Do(U)),r!==i)if((s=Da())!==i){for(n=[],o=vo,38===e.charCodeAt(vo)?(a=L,vo++):(a=i,0===Ao&&Do(M)),a!==i&&(c=Da())!==i?o=a=[a,c]:(vo=o,o=i);o!==i;)n.push(o),o=vo,38===e.charCodeAt(vo)?(a=L,vo++):(a=i,0===Ao&&Do(M)),a!==i&&(c=Da())!==i?o=a=[a,c]:(vo=o,o=i);n!==i?t=r=[r,s,n]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;return t}())===i&&(n=null),n!==i?(mo=t,t=rt()):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t}function la(){var t,r;return t=vo,e.substr(vo,4).toLowerCase()===it?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(st)),r===i&&(e.substr(vo,3).toLowerCase()===nt?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(ot))),r!==i&&(mo=t,r=at(r)),t=r}function fa(){var t,r,s,n;return t=vo,function(){var e,t;if(e=[],(t=Go())===i&&(t=Wo())===i&&(t=ga()),t!==i)for(;t!==i;)e.push(t),(t=Go())===i&&(t=Wo())===i&&(t=ga());else e=i;return e}()!==i?(r=vo,58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s!==i&&(n=function(){var t,r,s;for(t=vo,r=[],(s=Go())===i&&(s=Wo())===i&&(38===e.charCodeAt(vo)?(s=L,vo++):(s=i,0===Ao&&Do(M)),s===i&&(61===e.charCodeAt(vo)?(s=k,vo++):(s=i,0===Ao&&Do(F)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(36===e.charCodeAt(vo)?(s=B,vo++):(s=i,0===Ao&&Do(W)),s===i&&(44===e.charCodeAt(vo)?(s=K,vo++):(s=i,0===Ao&&Do(V)))))));s!==i;)r.push(s),(s=Go())===i&&(s=Wo())===i&&(38===e.charCodeAt(vo)?(s=L,vo++):(s=i,0===Ao&&Do(M)),s===i&&(61===e.charCodeAt(vo)?(s=k,vo++):(s=i,0===Ao&&Do(F)),s===i&&(43===e.charCodeAt(vo)?(s=j,vo++):(s=i,0===Ao&&Do(G)),s===i&&(36===e.charCodeAt(vo)?(s=B,vo++):(s=i,0===Ao&&Do(W)),s===i&&(44===e.charCodeAt(vo)?(s=K,vo++):(s=i,0===Ao&&Do(V)))))));return r!==i&&(mo=t,r=ut()),t=r}())!==i?r=s=[s,n]:(vo=r,r=i),r===i&&(r=null),r!==i?(64===e.charCodeAt(vo)?(s=H,vo++):(s=i,0===Ao&&Do(q)),s!==i?(mo=t,t=ct()):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t}function ga(){var t;return 38===e.charCodeAt(vo)?(t=L,vo++):(t=i,0===Ao&&Do(M)),t===i&&(61===e.charCodeAt(vo)?(t=k,vo++):(t=i,0===Ao&&Do(F)),t===i&&(43===e.charCodeAt(vo)?(t=j,vo++):(t=i,0===Ao&&Do(G)),t===i&&(36===e.charCodeAt(vo)?(t=B,vo++):(t=i,0===Ao&&Do(W)),t===i&&(44===e.charCodeAt(vo)?(t=K,vo++):(t=i,0===Ao&&Do(V)),t===i&&(59===e.charCodeAt(vo)?(t=I,vo++):(t=i,0===Ao&&Do(w)),t===i&&(63===e.charCodeAt(vo)?(t=N,vo++):(t=i,0===Ao&&Do(U)),t===i&&(47===e.charCodeAt(vo)?(t=D,vo++):(t=i,0===Ao&&Do(O))))))))),t}function Ta(){var t,r,s,n,o;return t=vo,(r=va())!==i?(s=vo,58===e.charCodeAt(vo)?(n=P,vo++):(n=i,0===Ao&&Do(x)),n!==i&&(o=ba())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?t=r=[r,s]:(vo=t,t=i)):(vo=t,t=i),t}function va(){var e,t;return e=vo,(t=ma())===i&&(t=Ra())===i&&(t=Ca()),t!==i&&(mo=e,t=ht()),e=t}function ma(){var t,r,s,n,o;for(t=vo,r=[],s=vo,(n=Sa())!==i?(46===e.charCodeAt(vo)?(o=J,vo++):(o=i,0===Ao&&Do(Q)),o!==i?s=n=[n,o]:(vo=s,s=i)):(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=Sa())!==i?(46===e.charCodeAt(vo)?(o=J,vo++):(o=i,0===Ao&&Do(Q)),o!==i?s=n=[n,o]:(vo=s,s=i)):(vo=s,s=i);return r!==i&&(s=function(){var t,r,s,n;if(t=vo,l.test(e.charAt(vo))?(r=e.charAt(vo),vo++):(r=i,0===Ao&&Do(f)),r!==i){for(s=[],ft.test(e.charAt(vo))?(n=e.charAt(vo),vo++):(n=i,0===Ao&&Do(gt));n!==i;)s.push(n),ft.test(e.charAt(vo))?(n=e.charAt(vo),vo++):(n=i,0===Ao&&Do(gt));s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t}())!==i?(46===e.charCodeAt(vo)?(n=J,vo++):(n=i,0===Ao&&Do(Q)),n===i&&(n=null),n!==i?(mo=t,t=r=dt()):(vo=t,t=i)):(vo=t,t=i),t}function Sa(){var t,r;if(t=[],pt.test(e.charAt(vo))?(r=e.charAt(vo),vo++):(r=i,0===Ao&&Do(lt)),r!==i)for(;r!==i;)t.push(r),pt.test(e.charAt(vo))?(r=e.charAt(vo),vo++):(r=i,0===Ao&&Do(lt));else t=i;return t}function Ca(){var t,r,s;return t=vo,91===e.charCodeAt(vo)?(r=Ie,vo++):(r=i,0===Ao&&Do(we)),r!==i&&ya()!==i?(93===e.charCodeAt(vo)?(s=De,vo++):(s=i,0===Ao&&Do(Oe)),s!==i?(mo=t,t=r=Tt()):(vo=t,t=i)):(vo=t,t=i),t}function ya(){var t,r,s,n,o,a,c,u,h,d,p,l,f,g,T;return t=vo,r=vo,(s=Aa())!==i?(58===e.charCodeAt(vo)?(n=P,vo++):(n=i,0===Ao&&Do(x)),n!==i&&(o=Aa())!==i?(58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?(58===e.charCodeAt(vo)?(u=P,vo++):(u=i,0===Ao&&Do(x)),u!==i&&(h=Aa())!==i?(58===e.charCodeAt(vo)?(d=P,vo++):(d=i,0===Ao&&Do(x)),d!==i&&(p=Aa())!==i?(58===e.charCodeAt(vo)?(l=P,vo++):(l=i,0===Ao&&Do(x)),l!==i&&(f=Aa())!==i?(58===e.charCodeAt(vo)?(g=P,vo++):(g=i,0===Ao&&Do(x)),g!==i&&(T=Ea())!==i?r=s=[s,n,o,a,c,u,h,d,p,l,f,g,T]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Aa())!==i?(58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?(58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?(58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Aa())!==i?(58===e.charCodeAt(vo)?(p=P,vo++):(p=i,0===Ao&&Do(x)),p!==i&&(l=Aa())!==i?(58===e.charCodeAt(vo)?(f=P,vo++):(f=i,0===Ao&&Do(x)),f!==i&&(g=Ea())!==i?r=s=[s,n,o,a,c,u,h,d,p,l,f,g]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Aa())!==i?(58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?(58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?(58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Aa())!==i?(58===e.charCodeAt(vo)?(p=P,vo++):(p=i,0===Ao&&Do(x)),p!==i&&(l=Ea())!==i?r=s=[s,n,o,a,c,u,h,d,p,l]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Aa())!==i?(58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?(58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?(58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Ea())!==i?r=s=[s,n,o,a,c,u,h,d]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Aa())!==i?(58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?(58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Ea())!==i?r=s=[s,n,o,a,c,u]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Aa())!==i?(58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Ea())!==i?r=s=[s,n,o,a]:(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Ea())!==i?r=s=[s,n]:(vo=r,r=i),r===i&&(r=vo,e.substr(vo,2)===vt?(s=vt,vo+=2):(s=i,0===Ao&&Do(mt)),s!==i&&(n=Aa())!==i?r=s=[s,n]:(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(e.substr(vo,2)===vt?(n=vt,vo+=2):(n=i,0===Ao&&Do(mt)),n!==i&&(o=Aa())!==i?(58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?(58===e.charCodeAt(vo)?(u=P,vo++):(u=i,0===Ao&&Do(x)),u!==i&&(h=Aa())!==i?(58===e.charCodeAt(vo)?(d=P,vo++):(d=i,0===Ao&&Do(x)),d!==i&&(p=Aa())!==i?(58===e.charCodeAt(vo)?(l=P,vo++):(l=i,0===Ao&&Do(x)),l!==i&&(f=Ea())!==i?r=s=[s,n,o,a,c,u,h,d,p,l,f]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(n=vo,58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?n=o=[o,a]:(vo=n,n=i),n===i&&(n=null),n!==i?(e.substr(vo,2)===vt?(o=vt,vo+=2):(o=i,0===Ao&&Do(mt)),o!==i&&(a=Aa())!==i?(58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?(58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Aa())!==i?(58===e.charCodeAt(vo)?(p=P,vo++):(p=i,0===Ao&&Do(x)),p!==i&&(l=Ea())!==i?r=s=[s,n,o,a,c,u,h,d,p,l]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(n=vo,58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?n=o=[o,a]:(vo=n,n=i),n===i&&(n=null),n!==i?(o=vo,58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?o=a=[a,c]:(vo=o,o=i),o===i&&(o=null),o!==i?(e.substr(vo,2)===vt?(a=vt,vo+=2):(a=i,0===Ao&&Do(mt)),a!==i&&(c=Aa())!==i?(58===e.charCodeAt(vo)?(u=P,vo++):(u=i,0===Ao&&Do(x)),u!==i&&(h=Aa())!==i?(58===e.charCodeAt(vo)?(d=P,vo++):(d=i,0===Ao&&Do(x)),d!==i&&(p=Ea())!==i?r=s=[s,n,o,a,c,u,h,d,p]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(n=vo,58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?n=o=[o,a]:(vo=n,n=i),n===i&&(n=null),n!==i?(o=vo,58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?o=a=[a,c]:(vo=o,o=i),o===i&&(o=null),o!==i?(a=vo,58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?a=c=[c,u]:(vo=a,a=i),a===i&&(a=null),a!==i?(e.substr(vo,2)===vt?(c=vt,vo+=2):(c=i,0===Ao&&Do(mt)),c!==i&&(u=Aa())!==i?(58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Ea())!==i?r=s=[s,n,o,a,c,u,h,d]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(n=vo,58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?n=o=[o,a]:(vo=n,n=i),n===i&&(n=null),n!==i?(o=vo,58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?o=a=[a,c]:(vo=o,o=i),o===i&&(o=null),o!==i?(a=vo,58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?a=c=[c,u]:(vo=a,a=i),a===i&&(a=null),a!==i?(c=vo,58===e.charCodeAt(vo)?(u=P,vo++):(u=i,0===Ao&&Do(x)),u!==i&&(h=Aa())!==i?c=u=[u,h]:(vo=c,c=i),c===i&&(c=null),c!==i?(e.substr(vo,2)===vt?(u=vt,vo+=2):(u=i,0===Ao&&Do(mt)),u!==i&&(h=Ea())!==i?r=s=[s,n,o,a,c,u,h]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(n=vo,58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?n=o=[o,a]:(vo=n,n=i),n===i&&(n=null),n!==i?(o=vo,58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?o=a=[a,c]:(vo=o,o=i),o===i&&(o=null),o!==i?(a=vo,58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?a=c=[c,u]:(vo=a,a=i),a===i&&(a=null),a!==i?(c=vo,58===e.charCodeAt(vo)?(u=P,vo++):(u=i,0===Ao&&Do(x)),u!==i&&(h=Aa())!==i?c=u=[u,h]:(vo=c,c=i),c===i&&(c=null),c!==i?(u=vo,58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Aa())!==i?u=h=[h,d]:(vo=u,u=i),u===i&&(u=null),u!==i?(e.substr(vo,2)===vt?(h=vt,vo+=2):(h=i,0===Ao&&Do(mt)),h!==i&&(d=Aa())!==i?r=s=[s,n,o,a,c,u,h,d]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i),r===i&&(r=vo,(s=Aa())!==i?(n=vo,58===e.charCodeAt(vo)?(o=P,vo++):(o=i,0===Ao&&Do(x)),o!==i&&(a=Aa())!==i?n=o=[o,a]:(vo=n,n=i),n===i&&(n=null),n!==i?(o=vo,58===e.charCodeAt(vo)?(a=P,vo++):(a=i,0===Ao&&Do(x)),a!==i&&(c=Aa())!==i?o=a=[a,c]:(vo=o,o=i),o===i&&(o=null),o!==i?(a=vo,58===e.charCodeAt(vo)?(c=P,vo++):(c=i,0===Ao&&Do(x)),c!==i&&(u=Aa())!==i?a=c=[c,u]:(vo=a,a=i),a===i&&(a=null),a!==i?(c=vo,58===e.charCodeAt(vo)?(u=P,vo++):(u=i,0===Ao&&Do(x)),u!==i&&(h=Aa())!==i?c=u=[u,h]:(vo=c,c=i),c===i&&(c=null),c!==i?(u=vo,58===e.charCodeAt(vo)?(h=P,vo++):(h=i,0===Ao&&Do(x)),h!==i&&(d=Aa())!==i?u=h=[h,d]:(vo=u,u=i),u===i&&(u=null),u!==i?(h=vo,58===e.charCodeAt(vo)?(d=P,vo++):(d=i,0===Ao&&Do(x)),d!==i&&(p=Aa())!==i?h=d=[d,p]:(vo=h,h=i),h===i&&(h=null),h!==i?(e.substr(vo,2)===vt?(d=vt,vo+=2):(d=i,0===Ao&&Do(mt)),d!==i?r=s=[s,n,o,a,c,u,h,d]:(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i)):(vo=r,r=i))))))))))))))),r!==i&&(mo=t,r=St()),t=r}function Aa(){var e,t,r,s,n;return e=vo,(t=xo())!==i?((r=xo())===i&&(r=null),r!==i?((s=xo())===i&&(s=null),s!==i?((n=xo())===i&&(n=null),n!==i?e=t=[t,r,s,n]:(vo=e,e=i)):(vo=e,e=i)):(vo=e,e=i)):(vo=e,e=i),e}function Ea(){var t,r,s,n;return t=vo,(r=Aa())!==i?(58===e.charCodeAt(vo)?(s=P,vo++):(s=i,0===Ao&&Do(x)),s!==i&&(n=Aa())!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i),t===i&&(t=Ra()),t}function Ra(){var t,r,s,n;return t=vo,_a()!==i?(46===e.charCodeAt(vo)?(r=J,vo++):(r=i,0===Ao&&Do(Q)),r!==i&&_a()!==i?(46===e.charCodeAt(vo)?(s=J,vo++):(s=i,0===Ao&&Do(Q)),s!==i&&_a()!==i?(46===e.charCodeAt(vo)?(n=J,vo++):(n=i,0===Ao&&Do(Q)),n!==i&&_a()!==i?(mo=t,t=Ct()):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t}function _a(){var t,r,s,n;return t=vo,e.substr(vo,2)===yt?(r=yt,vo+=2):(r=i,0===Ao&&Do(At)),r!==i?(Et.test(e.charAt(vo))?(s=e.charAt(vo),vo++):(s=i,0===Ao&&Do(Rt)),s!==i?t=r=[r,s]:(vo=t,t=i)):(vo=t,t=i),t===i&&(t=vo,50===e.charCodeAt(vo)?(r=_t,vo++):(r=i,0===Ao&&Do(bt)),r!==i?(It.test(e.charAt(vo))?(s=e.charAt(vo),vo++):(s=i,0===Ao&&Do(wt)),s!==i&&(n=Uo())!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i),t===i&&(t=vo,49===e.charCodeAt(vo)?(r=Dt,vo++):(r=i,0===Ao&&Do(Ot)),r!==i&&(s=Uo())!==i&&(n=Uo())!==i?t=r=[r,s,n]:(vo=t,t=i),t===i&&(t=vo,Nt.test(e.charAt(vo))?(r=e.charAt(vo),vo++):(r=i,0===Ao&&Do(Ut)),r!==i&&(s=Uo())!==i?t=r=[r,s]:(vo=t,t=i),t===i&&(t=Uo())))),t}function ba(){var e,t,r,s,n,o,a;return e=vo,t=vo,(r=Uo())===i&&(r=null),r!==i?((s=Uo())===i&&(s=null),s!==i?((n=Uo())===i&&(n=null),n!==i?((o=Uo())===i&&(o=null),o!==i?((a=Uo())===i&&(a=null),a!==i?t=r=[r,s,n,o,a]:(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t!==i&&(mo=e,t=Pt(t)),e=t}function Ia(){var t;return(t=function(){var t,r,s;return t=vo,e.substr(vo,10).toLowerCase()===xt?(r=e.substr(vo,10),vo+=10):(r=i,0===Ao&&Do(Ht)),r!==i?(e.substr(vo,3).toLowerCase()===qt?(s=e.substr(vo,3),vo+=3):(s=i,0===Ao&&Do(Lt)),s===i&&(e.substr(vo,3).toLowerCase()===Mt?(s=e.substr(vo,3),vo+=3):(s=i,0===Ao&&Do(kt)),s===i&&(e.substr(vo,4).toLowerCase()===Ft?(s=e.substr(vo,4),vo+=4):(s=i,0===Ao&&Do(jt)),s===i&&(e.substr(vo,3).toLowerCase()===Gt?(s=e.substr(vo,3),vo+=3):(s=i,0===Ao&&Do(Bt)),s===i&&(s=Xo())))),s!==i?(mo=t,r=Wt(s),t=r):(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,5).toLowerCase()===Kt?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(Vt)),r!==i?(e.substr(vo,5).toLowerCase()===Yt?(s=e.substr(vo,5),vo+=5):(s=i,0===Ao&&Do(zt)),s===i&&(e.substr(vo,2).toLowerCase()===$t?(s=e.substr(vo,2),vo+=2):(s=i,0===Ao&&Do(Xt)),s===i&&(s=Xo())),s!==i?(mo=t,r=Jt(s),t=r):(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,7).toLowerCase()===Qt?(r=e.substr(vo,7),vo+=7):(r=i,0===Ao&&Do(Zt)),r!==i&&(s=Fa())!==i?(mo=t,r=er(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,4).toLowerCase()===tr?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(rr)),r!==i&&(s=lc())!==i?(mo=t,r=ir(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,6).toLowerCase()===sr?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(nr)),r!==i&&(s=va())!==i?(mo=t,r=or(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s,n,o;return t=vo,e.substr(vo,2).toLowerCase()===ar?(r=e.substr(vo,2),vo+=2):(r=i,0===Ao&&Do(cr)),r!==i?(s=vo,61===e.charCodeAt(vo)?(n=k,vo++):(n=i,0===Ao&&Do(F)),n!==i&&(o=Xo())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?(mo=t,r=ur(),t=r):(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s,n,o;return t=vo,(r=function(){var t,r,s;if(t=vo,r=[],(s=wa())!==i)for(;s!==i;)r.push(s),s=wa();else r=i;return t=r!==i?e.substring(t,vo):r}())!==i?(s=vo,61===e.charCodeAt(vo)?(n=k,vo++):(n=i,0===Ao&&Do(F)),n!==i&&(o=function(){var t,r,s;if(t=vo,r=[],(s=wa())!==i)for(;s!==i;)r.push(s),s=wa();else r=i;return t=r!==i?e.substring(t,vo):r}())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?(mo=t,r=hr(r,s),t=r):(vo=t,t=i)):(vo=t,t=i),t}()),t}function wa(){var t;return(t=function(){var t;return 91===e.charCodeAt(vo)?(t=Ie,vo++):(t=i,0===Ao&&Do(we)),t===i&&(93===e.charCodeAt(vo)?(t=De,vo++):(t=i,0===Ao&&Do(Oe)),t===i&&(47===e.charCodeAt(vo)?(t=D,vo++):(t=i,0===Ao&&Do(O)),t===i&&(58===e.charCodeAt(vo)?(t=P,vo++):(t=i,0===Ao&&Do(x)),t===i&&(38===e.charCodeAt(vo)?(t=L,vo++):(t=i,0===Ao&&Do(M)),t===i&&(43===e.charCodeAt(vo)?(t=j,vo++):(t=i,0===Ao&&Do(G)),t===i&&(36===e.charCodeAt(vo)?(t=B,vo++):(t=i,0===Ao&&Do(W)))))))),t}())===i&&(t=Go())===i&&(t=Wo()),t}function Da(){var t,r,s,n;return t=vo,(r=function(){var e,t;if(e=[],(t=Oa())===i&&(t=Go())===i&&(t=Wo()),t!==i)for(;t!==i;)e.push(t),(t=Oa())===i&&(t=Go())===i&&(t=Wo());else e=i;return e}())!==i?(61===e.charCodeAt(vo)?(s=k,vo++):(s=i,0===Ao&&Do(F)),s!==i&&(n=function(){var e,t;for(e=[],(t=Oa())===i&&(t=Go())===i&&(t=Wo());t!==i;)e.push(t),(t=Oa())===i&&(t=Go())===i&&(t=Wo());return e}())!==i?(mo=t,t=r=dr(r,n)):(vo=t,t=i)):(vo=t,t=i),t}function Oa(){var t;return 91===e.charCodeAt(vo)?(t=Ie,vo++):(t=i,0===Ao&&Do(we)),t===i&&(93===e.charCodeAt(vo)?(t=De,vo++):(t=i,0===Ao&&Do(Oe)),t===i&&(47===e.charCodeAt(vo)?(t=D,vo++):(t=i,0===Ao&&Do(O)),t===i&&(63===e.charCodeAt(vo)?(t=N,vo++):(t=i,0===Ao&&Do(U)),t===i&&(58===e.charCodeAt(vo)?(t=P,vo++):(t=i,0===Ao&&Do(x)),t===i&&(43===e.charCodeAt(vo)?(t=j,vo++):(t=i,0===Ao&&Do(G)),t===i&&(36===e.charCodeAt(vo)?(t=B,vo++):(t=i,0===Ao&&Do(W)))))))),t}function Na(){var e;return(e=function(){var e,t,r,s,n,o;return e=vo,(t=ka())!==i&&(r=Mo())!==i&&(s=function(){var e,t;return e=vo,(t=function(){var e,t,r,s;return e=vo,(t=Uo())!==i&&(r=Uo())!==i&&(s=Uo())!==i?e=t=[t,r,s]:(vo=e,e=i),e}())!==i&&(mo=e,t=kr(t)),e=t}())!==i&&(n=Mo())!==i&&(o=function(){var e,t,r;for(e=vo,t=[],(r=jo())===i&&(r=Go())===i&&(r=Wo())===i&&(r=zo())===i&&(r=$o())===i&&(r=Mo())===i&&(r=ko());r!==i;)t.push(r),(r=jo())===i&&(r=Go())===i&&(r=Wo())===i&&(r=zo())===i&&(r=$o())===i&&(r=Mo())===i&&(r=ko());return t!==i&&(mo=e,t=Fr()),e=t}())!==i?e=t=[t,r,s,n,o]:(vo=e,e=i),e}())===i&&(e=function(){var e,t,r,s,n,o;return e=vo,(t=Fa())!==i&&(r=Mo())!==i&&(s=function(){var e;return(e=pa())===i&&(e=Ua()),e}())!==i&&(n=Mo())!==i&&(o=ka())!==i?e=t=[t,r,s,n,o]:(vo=e,e=i),e}()),e}function Ua(){var t,r,s;return t=vo,function(){var t,r,s,n,o;if(t=vo,r=vo,(s=Po())!==i){for(n=[],(o=Po())===i&&(o=Uo())===i&&(43===e.charCodeAt(vo)?(o=j,vo++):(o=i,0===Ao&&Do(G)),o===i&&(45===e.charCodeAt(vo)?(o=Y,vo++):(o=i,0===Ao&&Do(z)),o===i&&(46===e.charCodeAt(vo)?(o=J,vo++):(o=i,0===Ao&&Do(Q)))));o!==i;)n.push(o),(o=Po())===i&&(o=Uo())===i&&(43===e.charCodeAt(vo)?(o=j,vo++):(o=i,0===Ao&&Do(G)),o===i&&(45===e.charCodeAt(vo)?(o=Y,vo++):(o=i,0===Ao&&Do(z)),o===i&&(46===e.charCodeAt(vo)?(o=J,vo++):(o=i,0===Ao&&Do(Q)))));n!==i?r=s=[s,n]:(vo=r,r=i)}else vo=r,r=i;return r!==i&&(mo=t,r=gr()),t=r}()!==i?(58===e.charCodeAt(vo)?(r=P,vo++):(r=i,0===Ao&&Do(x)),r!==i?((s=function(){var t,r,s,n,o;return t=vo,(r=function(){var t,r,s,n;return t=vo,e.substr(vo,2)===lr?(r=lr,vo+=2):(r=i,0===Ao&&Do(fr)),r!==i&&(s=function(){var t;return(t=function(){var t,r,s,n;return t=vo,r=vo,(s=fa())!==i?(64===e.charCodeAt(vo)?(n=H,vo++):(n=i,0===Ao&&Do(q)),n!==i?r=s=[s,n]:(vo=r,r=i)):(vo=r,r=i),r===i&&(r=null),r!==i&&(s=Ta())!==i?t=r=[r,s]:(vo=t,t=i),t===i&&(t=null),t}())===i&&(t=Ma()),t}())!==i?((n=Pa())===i&&(n=null),n!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i),t}())===i&&(r=Pa()),r!==i?(s=vo,63===e.charCodeAt(vo)?(n=N,vo++):(n=i,0===Ao&&Do(U)),n!==i&&(o=function(){var e,t;for(e=[],t=xa();t!==i;)e.push(t),t=xa();return e}())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?t=r=[r,s]:(vo=t,t=i)):(vo=t,t=i),t}())===i&&(s=function(){var t,r,s,n;if(t=vo,(r=function(){var t;return(t=Go())===i&&(t=Wo())===i&&(59===e.charCodeAt(vo)?(t=I,vo++):(t=i,0===Ao&&Do(w)),t===i&&(63===e.charCodeAt(vo)?(t=N,vo++):(t=i,0===Ao&&Do(U)),t===i&&(58===e.charCodeAt(vo)?(t=P,vo++):(t=i,0===Ao&&Do(x)),t===i&&(64===e.charCodeAt(vo)?(t=H,vo++):(t=i,0===Ao&&Do(q)),t===i&&(38===e.charCodeAt(vo)?(t=L,vo++):(t=i,0===Ao&&Do(M)),t===i&&(61===e.charCodeAt(vo)?(t=k,vo++):(t=i,0===Ao&&Do(F)),t===i&&(43===e.charCodeAt(vo)?(t=j,vo++):(t=i,0===Ao&&Do(G)),t===i&&(36===e.charCodeAt(vo)?(t=B,vo++):(t=i,0===Ao&&Do(W)),t===i&&(44===e.charCodeAt(vo)?(t=K,vo++):(t=i,0===Ao&&Do(V))))))))))),t}())!==i){for(s=[],n=xa();n!==i;)s.push(n),n=xa();s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t}()),s!==i?(mo=t,t=pr()):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t}function Pa(){var t,r,s;return t=vo,47===e.charCodeAt(vo)?(r=D,vo++):(r=i,0===Ao&&Do(O)),r!==i&&(s=function(){var t,r,s,n,o,a;if(t=vo,(r=Ha())!==i){for(s=[],n=vo,47===e.charCodeAt(vo)?(o=D,vo++):(o=i,0===Ao&&Do(O)),o!==i&&(a=Ha())!==i?n=o=[o,a]:(vo=n,n=i);n!==i;)s.push(n),n=vo,47===e.charCodeAt(vo)?(o=D,vo++):(o=i,0===Ao&&Do(O)),o!==i&&(a=Ha())!==i?n=o=[o,a]:(vo=n,n=i);s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t}())!==i?t=r=[r,s]:(vo=t,t=i),t}function xa(){var e;return(e=jo())===i&&(e=Go())===i&&(e=Wo()),e}function Ha(){var t,r,s,n,o,a;for(t=vo,r=[],s=La();s!==i;)r.push(s),s=La();if(r!==i){for(s=[],n=vo,59===e.charCodeAt(vo)?(o=I,vo++):(o=i,0===Ao&&Do(w)),o!==i&&(a=qa())!==i?n=o=[o,a]:(vo=n,n=i);n!==i;)s.push(n),n=vo,59===e.charCodeAt(vo)?(o=I,vo++):(o=i,0===Ao&&Do(w)),o!==i&&(a=qa())!==i?n=o=[o,a]:(vo=n,n=i);s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t}function qa(){var e,t;for(e=[],t=La();t!==i;)e.push(t),t=La();return e}function La(){var t;return(t=Go())===i&&(t=Wo())===i&&(58===e.charCodeAt(vo)?(t=P,vo++):(t=i,0===Ao&&Do(x)),t===i&&(64===e.charCodeAt(vo)?(t=H,vo++):(t=i,0===Ao&&Do(q)),t===i&&(38===e.charCodeAt(vo)?(t=L,vo++):(t=i,0===Ao&&Do(M)),t===i&&(61===e.charCodeAt(vo)?(t=k,vo++):(t=i,0===Ao&&Do(F)),t===i&&(43===e.charCodeAt(vo)?(t=j,vo++):(t=i,0===Ao&&Do(G)),t===i&&(36===e.charCodeAt(vo)?(t=B,vo++):(t=i,0===Ao&&Do(W)),t===i&&(44===e.charCodeAt(vo)?(t=K,vo++):(t=i,0===Ao&&Do(V))))))))),t}function Ma(){var t,r;if(t=[],(r=Go())===i&&(r=Wo())===i&&(36===e.charCodeAt(vo)?(r=B,vo++):(r=i,0===Ao&&Do(W)),r===i&&(44===e.charCodeAt(vo)?(r=K,vo++):(r=i,0===Ao&&Do(V)),r===i&&(59===e.charCodeAt(vo)?(r=I,vo++):(r=i,0===Ao&&Do(w)),r===i&&(58===e.charCodeAt(vo)?(r=P,vo++):(r=i,0===Ao&&Do(x)),r===i&&(64===e.charCodeAt(vo)?(r=H,vo++):(r=i,0===Ao&&Do(q)),r===i&&(38===e.charCodeAt(vo)?(r=L,vo++):(r=i,0===Ao&&Do(M)),r===i&&(61===e.charCodeAt(vo)?(r=k,vo++):(r=i,0===Ao&&Do(F)),r===i&&(43===e.charCodeAt(vo)?(r=j,vo++):(r=i,0===Ao&&Do(G)))))))))),r!==i)for(;r!==i;)t.push(r),(r=Go())===i&&(r=Wo())===i&&(36===e.charCodeAt(vo)?(r=B,vo++):(r=i,0===Ao&&Do(W)),r===i&&(44===e.charCodeAt(vo)?(r=K,vo++):(r=i,0===Ao&&Do(V)),r===i&&(59===e.charCodeAt(vo)?(r=I,vo++):(r=i,0===Ao&&Do(w)),r===i&&(58===e.charCodeAt(vo)?(r=P,vo++):(r=i,0===Ao&&Do(x)),r===i&&(64===e.charCodeAt(vo)?(r=H,vo++):(r=i,0===Ao&&Do(q)),r===i&&(38===e.charCodeAt(vo)?(r=L,vo++):(r=i,0===Ao&&Do(M)),r===i&&(61===e.charCodeAt(vo)?(r=k,vo++):(r=i,0===Ao&&Do(F)),r===i&&(43===e.charCodeAt(vo)?(r=j,vo++):(r=i,0===Ao&&Do(G))))))))));else t=i;return t}function ka(){var t,r,s,n,o,a,c;if(t=vo,e.substr(vo,3).toLowerCase()===nt?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(Tr)),r!==i)if(47===e.charCodeAt(vo)?(s=D,vo++):(s=i,0===Ao&&Do(O)),s!==i){if(n=[],(o=Uo())!==i)for(;o!==i;)n.push(o),o=Uo();else n=i;if(n!==i)if(46===e.charCodeAt(vo)?(o=J,vo++):(o=i,0===Ao&&Do(Q)),o!==i){if(a=[],(c=Uo())!==i)for(;c!==i;)a.push(c),c=Uo();else a=i;a!==i?(mo=t,t=r=vr()):(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i}else vo=t,t=i;else vo=t,t=i;return t}function Fa(){var t,r;return t=vo,(r=function(){var t;return e.substr(vo,6)===mr?(t=mr,vo+=6):(t=i,0===Ao&&Do(Sr)),t}())===i&&(r=function(){var t;return e.substr(vo,3)===Cr?(t=Cr,vo+=3):(t=i,0===Ao&&Do(yr)),t}())===i&&(r=function(){var t;return e.substr(vo,7)===Ar?(t=Ar,vo+=7):(t=i,0===Ao&&Do(Er)),t}())===i&&(r=function(){var t;return e.substr(vo,3)===Rr?(t=Rr,vo+=3):(t=i,0===Ao&&Do(_r)),t}())===i&&(r=function(){var t;return e.substr(vo,6)===br?(t=br,vo+=6):(t=i,0===Ao&&Do(Ir)),t}())===i&&(r=function(){var t;return e.substr(vo,8)===wr?(t=wr,vo+=8):(t=i,0===Ao&&Do(Dr)),t}())===i&&(r=function(){var t;return e.substr(vo,9)===Or?(t=Or,vo+=9):(t=i,0===Ao&&Do(Nr)),t}())===i&&(r=function(){var t;return e.substr(vo,7)===qr?(t=qr,vo+=7):(t=i,0===Ao&&Do(Lr)),t}())===i&&(r=function(){var t;return e.substr(vo,6)===Ur?(t=Ur,vo+=6):(t=i,0===Ao&&Do(Pr)),t}())===i&&(r=function(){var t;return e.substr(vo,5)===xr?(t=xr,vo+=5):(t=i,0===Ao&&Do(Hr)),t}())===i&&(r=Xo()),r!==i&&(mo=t,r=Mr()),t=r}function ja(){var t,r,s,n;return t=vo,Qo()!==i?(r=vo,64===e.charCodeAt(vo)?(s=H,vo++):(s=i,0===Ao&&Do(q)),s!==i&&(n=Qo())!==i?r=s=[s,n]:(vo=r,r=i),r===i&&(r=null),r!==i?(mo=t,t=jr()):(vo=t,t=i)):(vo=t,t=i),t}function Ga(){var t,r,s,n,o,a,c;if(t=vo,(r=function(){var t,r;return t=vo,Vo()!==i?(42===e.charCodeAt(vo)?(r=ie,vo++):(r=i,0===Ao&&Do(se)),r!==i&&Vo()!==i?(mo=t,t=He()):(vo=t,t=i)):(vo=t,t=i),t}())===i)if(r=vo,(s=Ba())!==i){for(n=[],o=vo,(a=ia())!==i&&(c=Ba())!==i?o=a=[a,c]:(vo=o,o=i);o!==i;)n.push(o),o=vo,(a=ia())!==i&&(c=Ba())!==i?o=a=[a,c]:(vo=o,o=i);n!==i?r=s=[s,n]:(vo=r,r=i)}else vo=r,r=i;return r!==i&&(mo=t,r=Gr()),t=r}function Ba(){var e,t,r,s,n,o;if(e=vo,(t=da())===i&&(t=Wa()),t!==i){for(r=[],s=vo,(n=sa())!==i&&(o=Va())!==i?s=n=[n,o]:(vo=s,s=i);s!==i;)r.push(s),s=vo,(n=sa())!==i&&(o=Va())!==i?s=n=[n,o]:(vo=s,s=i);r!==i?(mo=e,e=t=Br()):(vo=e,e=i)}else vo=e,e=i;return e}function Wa(){var e,t,r,s,n;return e=vo,(t=Ka())===i&&(t=null),t!==i&&(r=ra())!==i&&(s=pa())!==i&&(n=ta())!==i?e=t=[t,r,s,n]:(vo=e,e=i),e}function Ka(){var e,t,r,s,n,o,a;if(e=vo,t=vo,(r=Xo())!==i){for(s=[],n=vo,(o=Ko())!==i&&(a=Xo())!==i?n=o=[o,a]:(vo=n,n=i);n!==i;)s.push(n),n=vo,(o=Ko())!==i&&(a=Xo())!==i?n=o=[o,a]:(vo=n,n=i);s!==i?t=r=[r,s]:(vo=t,t=i)}else vo=t,t=i;return t===i&&(t=aa()),t!==i&&(mo=e,t=Wr(t)),e=t}function Va(){var t;return(t=function(){var t,r,s;return t=vo,e.substr(vo,1).toLowerCase()===Kr?(r=e.charAt(vo),vo++):(r=i,0===Ao&&Do(Vr)),r!==i&&ea()!==i&&(s=function(){var t,r,s,n,o,a,c;return t=vo,48===e.charCodeAt(vo)?(r=Qr,vo++):(r=i,0===Ao&&Do(Zr)),r!==i?(s=vo,46===e.charCodeAt(vo)?(n=J,vo++):(n=i,0===Ao&&Do(Q)),n!==i?((o=Uo())===i&&(o=null),o!==i?((a=Uo())===i&&(a=null),a!==i?((c=Uo())===i&&(c=null),c!==i?s=n=[n,o,a,c]:(vo=s,s=i)):(vo=s,s=i)):(vo=s,s=i)):(vo=s,s=i),s===i&&(s=null),s!==i?(mo=t,r=ei(),t=r):(vo=t,t=i)):(vo=t,t=i),t}())!==i?(mo=t,r=Yr(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,7).toLowerCase()===zr?(r=e.substr(vo,7),vo+=7):(r=i,0===Ao&&Do($r)),r!==i&&ea()!==i&&(s=Ya())!==i?(mo=t,r=Xr(s),t=r):(vo=t,t=i),t}())===i&&(t=za()),t}function Ya(){var e,t,r;if(e=vo,t=[],(r=Uo())!==i)for(;r!==i;)t.push(r),r=Uo();else t=i;return t!==i&&(mo=e,t=Jr(t)),e=t}function za(){var e,t,r,s,n;return e=vo,(t=Xo())!==i?(r=vo,(s=ea())!==i&&(n=function(){var e;return(e=Xo())===i&&(e=va())===i&&(e=aa()),e}())!==i?r=s=[s,n]:(vo=r,r=i),r===i&&(r=null),r!==i?(mo=e,e=t=ti(t,r)):(vo=e,e=i)):(vo=e,e=i),e}function $a(){var t;return(t=function(){var t,r,s,n;return t=vo,e.substr(vo,8).toLowerCase()===di?(r=e.substr(vo,8),vo+=8):(r=i,0===Ao&&Do(pi)),r!==i&&(s=ea())!==i?(e.substr(vo,8).toLowerCase()===li?(n=e.substr(vo,8),vo+=8):(n=i,0===Ao&&Do(fi)),n===i&&(e.substr(vo,8).toLowerCase()===gi?(n=e.substr(vo,8),vo+=8):(n=i,0===Ao&&Do(Ti)),n===i&&(n=Xo())),n!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=za()),t}function Xa(){var t;return(t=Xo())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,2).toLowerCase()===Pi?(r=e.substr(vo,2),vo+=2):(r=i,0===Ao&&Do(xi)),r!==i&&(s=Xo())!==i?t=r=[r,s]:(vo=t,t=i),t}()),t}function Ja(){var e,t,r,s;return e=vo,(t=Xo())!==i&&(r=ea())!==i&&(s=function(){var e;return(e=Xo())===i&&(e=aa()),e}())!==i?e=t=[t,r,s]:(vo=e,e=i),e}function Qa(){var t,r,s,n,o,a,c;if(t=vo,r=vo,(s=Jo())!==i){for(n=[],o=vo,46===e.charCodeAt(vo)?(a=J,vo++):(a=i,0===Ao&&Do(Q)),a!==i&&(c=Jo())!==i?o=a=[a,c]:(vo=o,o=i);o!==i;)n.push(o),o=vo,46===e.charCodeAt(vo)?(a=J,vo++):(a=i,0===Ao&&Do(Q)),a!==i&&(c=Jo())!==i?o=a=[a,c]:(vo=o,o=i);n!==i?r=s=[s,n]:(vo=r,r=i)}else vo=r,r=i;return t=r!==i?e.substring(t,vo):r}function Za(){var e;return(e=ec())===i&&(e=za()),e}function ec(){var t,r,s;return t=vo,e.substr(vo,3).toLowerCase()===Mi?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(ki)),r!==i&&ea()!==i&&(s=Xo())!==i?(mo=t,t=r=Fi(s)):(vo=t,t=i),t}function tc(){var t,r,s,n,o,a,c,u;if(t=vo,e.substr(vo,6).toLowerCase()===Bi?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(Wi)),r!==i)if((s=Ko())!==i)if((n=ic())!==i){for(o=[],a=vo,(c=ia())!==i&&(u=ic())!==i?a=c=[c,u]:(vo=a,a=i);a!==i;)o.push(a),a=vo,(c=ia())!==i&&(u=ic())!==i?a=c=[c,u]:(vo=a,a=i);o!==i?t=r=[r,s,n,o]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;return t===i&&(t=function(){var e,t,r,s,n,o,a,c;if(e=vo,(t=Xo())!==i)if((r=Ko())!==i)if((s=rc())!==i){for(n=[],o=vo,(a=ia())!==i&&(c=rc())!==i?o=a=[a,c]:(vo=o,o=i);o!==i;)n.push(o),o=vo,(a=ia())!==i&&(c=rc())!==i?o=a=[a,c]:(vo=o,o=i);n!==i?e=t=[t,r,s,n]:(vo=e,e=i)}else vo=e,e=i;else vo=e,e=i;else vo=e,e=i;return e}()),t}function rc(){var e,t,r,s;return e=vo,(t=Xo())!==i&&(r=ea())!==i?((s=Xo())===i&&(s=aa()),s!==i?e=t=[t,r,s]:(vo=e,e=i)):(vo=e,e=i),e}function ic(){var t;return(t=function(){var t,r,s,n;return t=vo,e.substr(vo,5).toLowerCase()===Ki?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(Vi)),r!==i&&(s=ea())!==i&&(n=function(){var e,t;return e=vo,(t=ca())!==i&&(mo=e,t=Yi(t)),e=t}())!==i?t=r=[r,s,n]:(vo=t,t=i),t}())===i&&(t=function(){var t,r,s,n,o,a,c,u,h;if(t=vo,e.substr(vo,6).toLowerCase()===zi?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do($i)),r!==i)if((s=ea())!==i)if((n=na())!==i)if((o=sc())!==i){if(a=[],c=vo,u=[],(h=Mo())!==i)for(;h!==i;)u.push(h),h=Mo();else u=i;for(u!==i&&(h=sc())!==i?c=u=[u,h]:(vo=c,c=i);c!==i;){if(a.push(c),c=vo,u=[],(h=Mo())!==i)for(;h!==i;)u.push(h),h=Mo();else u=i;u!==i&&(h=sc())!==i?c=u=[u,h]:(vo=c,c=i)}a!==i&&(c=oa())!==i?t=r=[r,s,n,o,a,c]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;return t}())===i&&(t=function(){var t,r,s,n;return t=vo,e.substr(vo,5).toLowerCase()===Xi?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(Ji)),r!==i&&(s=ea())!==i&&(n=function(){var e,t;return e=vo,(t=ca())!==i&&(mo=e,t=Qi(t)),e=t}())!==i?t=r=[r,s,n]:(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,6).toLowerCase()===Zi?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(es)),r!==i&&ea()!==i&&(s=ca())!==i?(mo=t,r=ts(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s,n,o;return t=vo,e.substr(vo,5).toLowerCase()===rs?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(is)),r!==i&&(s=ea())!==i?(n=vo,e.substr(vo,4).toLowerCase()===ss?(o=e.substr(vo,4),vo+=4):(o=i,0===Ao&&Do(ns)),o!==i&&(mo=n,o=os()),(n=o)===i&&(n=vo,e.substr(vo,5).toLowerCase()===as?(o=e.substr(vo,5),vo+=5):(o=i,0===Ao&&Do(cs)),o!==i&&(mo=n,o=us()),n=o),n!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,9).toLowerCase()===hs?(r=e.substr(vo,9),vo+=9):(r=i,0===Ao&&Do(ds)),r!==i&&ea()!==i?(e.substr(vo,3).toLowerCase()===ps?(s=e.substr(vo,3),vo+=3):(s=i,0===Ao&&Do(ls)),s===i&&(e.substr(vo,8).toLowerCase()===fs?(s=e.substr(vo,8),vo+=8):(s=i,0===Ao&&Do(gs)),s===i&&(s=Xo())),s!==i?(mo=t,r=Ts(s),t=r):(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s,n,o,a,c,u,h,d;if(t=vo,e.substr(vo,3).toLowerCase()===vs?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(ms)),r!==i)if((s=ea())!==i)if((n=na())!==i){if(o=vo,(a=nc())!==i){for(c=[],u=vo,44===e.charCodeAt(vo)?(h=K,vo++):(h=i,0===Ao&&Do(V)),h!==i&&(d=nc())!==i?u=h=[h,d]:(vo=u,u=i);u!==i;)c.push(u),u=vo,44===e.charCodeAt(vo)?(h=K,vo++):(h=i,0===Ao&&Do(V)),h!==i&&(d=nc())!==i?u=h=[h,d]:(vo=u,u=i);c!==i?o=a=[a,c]:(vo=o,o=i)}else vo=o,o=i;o!==i&&(a=oa())!==i?t=r=[r,s,n,o,a]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;return t}())===i&&(t=rc()),t}function sc(){var e;return(e=Ua())===i&&(e=Pa()),e}function nc(){var t,r;return t=vo,e.substr(vo,8).toLowerCase()===Ss?(r=e.substr(vo,8),vo+=8):(r=i,0===Ao&&Do(Cs)),r===i&&(e.substr(vo,4).toLowerCase()===ys?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(As)),r===i&&(r=Xo())),r!==i&&(mo=t,r=Es(r)),t=r}function oc(){var e,t,r,s,n;if(e=vo,Wa()!==i){for(t=[],r=vo,(s=sa())!==i&&(n=za())!==i?r=s=[s,n]:(vo=r,r=i);r!==i;)t.push(r),r=vo,(s=sa())!==i&&(n=za())!==i?r=s=[s,n]:(vo=r,r=i);t!==i?(mo=e,e=_s()):(vo=e,e=i)}else vo=e,e=i;return e}function ac(){var t,r,s;return t=vo,e.substr(vo,8).toLowerCase()===Ds?(r=e.substr(vo,8),vo+=8):(r=i,0===Ao&&Do(Os)),r!==i&&ea()!==i&&(s=Xo())!==i?(mo=t,t=r=Ns(s)):(vo=t,t=i),t===i&&(t=vo,e.substr(vo,6).toLowerCase()===Us?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(Ps)),r!==i&&ea()!==i&&(s=Xo())!==i?(mo=t,t=r=xs(s)):(vo=t,t=i),t===i&&(t=vo,e.substr(vo,10).toLowerCase()===Hs?(r=e.substr(vo,10),vo+=10):(r=i,0===Ao&&Do(qs)),r!==i&&(mo=t,r=Ls()),(t=r)===i&&(t=za()))),t}function cc(){var t,r,s;return t=vo,e.substr(vo,6).toLowerCase()===zs?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do($s)),r!==i&&ea()!==i&&(s=function(){var t;return e.substr(vo,11).toLowerCase()===tn?(t=e.substr(vo,11),vo+=11):(t=i,0===Ao&&Do(rn)),t===i&&(e.substr(vo,9).toLowerCase()===sn?(t=e.substr(vo,9),vo+=9):(t=i,0===Ao&&Do(nn)),t===i&&(e.substr(vo,8).toLowerCase()===on?(t=e.substr(vo,8),vo+=8):(t=i,0===Ao&&Do(an)),t===i&&(e.substr(vo,7).toLowerCase()===cn?(t=e.substr(vo,7),vo+=7):(t=i,0===Ao&&Do(un)),t===i&&(e.substr(vo,6).toLowerCase()===hn?(t=e.substr(vo,6),vo+=6):(t=i,0===Ao&&Do(dn)),t===i&&(e.substr(vo,10).toLowerCase()===pn?(t=e.substr(vo,10),vo+=10):(t=i,0===Ao&&Do(ln)),t===i&&(e.substr(vo,9).toLowerCase()===fn?(t=e.substr(vo,9),vo+=9):(t=i,0===Ao&&Do(gn)),t===i&&(t=Xo()))))))),t}())!==i?(mo=t,t=r=Xs(s)):(vo=t,t=i),t===i&&(t=vo,e.substr(vo,7).toLowerCase()===zr?(r=e.substr(vo,7),vo+=7):(r=i,0===Ao&&Do($r)),r!==i&&ea()!==i&&(s=Ya())!==i?(mo=t,t=r=Js(s)):(vo=t,t=i),t===i&&(t=vo,e.substr(vo,11).toLowerCase()===Qs?(r=e.substr(vo,11),vo+=11):(r=i,0===Ao&&Do(Zs)),r!==i&&ea()!==i&&(s=Ya())!==i?(mo=t,t=r=en(s)):(vo=t,t=i),t===i&&(t=za()))),t}function uc(){var e;return(e=ec())===i&&(e=za()),e}function hc(){var t,r,s,n,o,a,c,u;if(t=vo,(r=function(){var t,r,s,n,o,a;return t=vo,(r=function(){var t,r;return t=vo,e.substr(vo,3).toLowerCase()===nt?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(Tr)),r===i&&(r=Xo()),r!==i&&(mo=t,r=Pn(r)),t=r}())!==i&&(s=Zo())!==i&&(n=Xo())!==i&&(o=Zo())!==i&&(a=pc())!==i?t=r=[r,s,n,o,a]:(vo=t,t=i),t}())!==i)if((s=Ko())!==i)if((n=function(){var t,r,s,n,o;return t=vo,(r=function(){var e,t;return e=vo,(t=ma())===i&&(t=Ra())===i&&(t=Ca()),t!==i&&(mo=e,t=kn()),e=t}())!==i?(s=vo,(n=function(){var t,r;return t=vo,Vo()!==i?(58===e.charCodeAt(vo)?(r=P,vo++):(r=i,0===Ao&&Do(x)),r!==i&&Vo()!==i?(mo=t,t=Ge()):(vo=t,t=i)):(vo=t,t=i),t}())!==i&&(o=function(){var e,t,r,s,n,o,a;return e=vo,t=vo,(r=Uo())===i&&(r=null),r!==i?((s=Uo())===i&&(s=null),s!==i?((n=Uo())===i&&(n=null),n!==i?((o=Uo())===i&&(o=null),o!==i?((a=Uo())===i&&(a=null),a!==i?t=r=[r,s,n,o,a]:(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t!==i&&(mo=e,t=Fn(t)),e=t}())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?t=r=[r,s]:(vo=t,t=i)):(vo=t,t=i),t}())!==i){for(o=[],a=vo,(c=sa())!==i&&(u=dc())!==i?a=c=[c,u]:(vo=a,a=i);a!==i;)o.push(a),a=vo,(c=sa())!==i&&(u=dc())!==i?a=c=[c,u]:(vo=a,a=i);o!==i?t=r=[r,s,n,o]:(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;else vo=t,t=i;return t}function dc(){var t;return(t=function(){var t,r,s;return t=vo,e.substr(vo,3).toLowerCase()===mn?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(Sn)),r!==i&&ea()!==i&&(s=lc())!==i?(mo=t,r=Cn(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,5).toLowerCase()===yn?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(An)),r!==i&&ea()!==i&&(s=va())!==i?(mo=t,r=En(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,8).toLowerCase()===Rn?(r=e.substr(vo,8),vo+=8):(r=i,0===Ao&&Do(_n)),r!==i&&ea()!==i?((s=Ra())===i&&(s=ya())===i&&(s=Ca()),s!==i?(mo=t,r=bn(s),t=r):(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s;return t=vo,e.substr(vo,6).toLowerCase()===In?(r=e.substr(vo,6),vo+=6):(r=i,0===Ao&&Do(wn)),r!==i&&ea()!==i&&(s=Xo())!==i?(mo=t,r=Dn(s),t=r):(vo=t,t=i),t}())===i&&(t=function(){var t,r,s,n;if(t=vo,e.substr(vo,5).toLowerCase()===On?(r=e.substr(vo,5),vo+=5):(r=i,0===Ao&&Do(Nn)),r!==i)if(ea()!==i){for(s=[],n=Uo();n!==i;)s.push(n),n=Uo();s!==i?(mo=t,r=Un(s),t=r):(vo=t,t=i)}else vo=t,t=i;else vo=t,t=i;return t}())===i&&(t=za()),t}function pc(){var t,r;return t=vo,e.substr(vo,3).toLowerCase()===qt?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(xn)),r===i&&(e.substr(vo,3).toLowerCase()===Mt?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(Hn)),r===i&&(e.substr(vo,3).toLowerCase()===Gt?(r=e.substr(vo,3),vo+=3):(r=i,0===Ao&&Do(qn)),r===i&&(e.substr(vo,4).toLowerCase()===Ft?(r=e.substr(vo,4),vo+=4):(r=i,0===Ao&&Do(Ln)),r===i&&(r=Xo())))),r!==i&&(mo=t,r=Mn(r)),t=r}function lc(){var e,t,r,s,n;return e=vo,t=vo,(r=Uo())!==i?((s=Uo())===i&&(s=null),s!==i?((n=Uo())===i&&(n=null),n!==i?t=r=[r,s,n]:(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t!==i&&(mo=e,t=jn(t)),e=t}function fc(){var t;return(t=function(){var t,r,s;return t=vo,e.substr(vo,9)===Bn?(r=Bn,vo+=9):(r=i,0===Ao&&Do(Wn)),r!==i&&ea()!==i?(e.substr(vo,3)===Kn?(s=Kn,vo+=3):(s=i,0===Ao&&Do(Vn)),s===i&&(e.substr(vo,3)===Yn?(s=Yn,vo+=3):(s=i,0===Ao&&Do(zn))),s!==i?(mo=t,r=$n(s),t=r):(vo=t,t=i)):(vo=t,t=i),t}())===i&&(t=za()),t}function gc(){var t,r,s,n,o;return t=vo,(r=function(){var e,t;return e=vo,(t=Ra())===i&&(t=Ca())===i&&(t=Ma()),t!==i&&(mo=e,t=ro(t)),e=t}())!==i?(s=vo,58===e.charCodeAt(vo)?(n=P,vo++):(n=i,0===Ao&&Do(x)),n!==i&&(o=ba())!==i?s=n=[n,o]:(vo=s,s=i),s===i&&(s=null),s!==i?t=r=[r,s]:(vo=t,t=i)):(vo=t,t=i),t}function Tc(){var e,t,r,s,n;return e=vo,(t=xo())!==i&&(r=xo())!==i&&(s=xo())!==i&&(n=xo())!==i?e=t=[t,r,s,n]:(vo=e,e=i),e}function vc(){var t,r,s,n;return t=vo,e.substr(vo,3)===go?(r=go,vo+=3):(r=i,0===Ao&&Do(To)),r!==i&&(s=ea())!==i&&(n=function(){var t,r,s,n,o,a;return t=vo,(r=na())!==i&&(s=Bo())!==i?(64===e.charCodeAt(vo)?(n=H,vo++):(n=i,0===Ao&&Do(q)),n!==i?((o=Bo())===i&&(o=va()),o!==i&&(a=oa())!==i?t=r=[r,s,n,o,a]:(vo=t,t=i)):(vo=t,t=i)):(vo=t,t=i),t}())!==i?t=r=[r,s,n]:(vo=t,t=i),t}if(t.data={},(r=c())!==i&&vo===e.length)return r;throw r!==i&&vo<e.length&&Do({type:"end"}),Oo(yo,Co<e.length?e.charAt(Co):null,Co<e.length?wo(Co,Co+1):wo(Co,Co))}},function(e,t,r){var i;e.exports=(i=r(33),function(e){var t=i,r=t.lib,s=r.WordArray,n=r.Hasher,o=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=o.MD5=n.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var i=t+r,s=e[i];e[i]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var n=this._hash.words,o=e[t+0],c=e[t+1],l=e[t+2],f=e[t+3],g=e[t+4],T=e[t+5],v=e[t+6],m=e[t+7],S=e[t+8],C=e[t+9],y=e[t+10],A=e[t+11],E=e[t+12],R=e[t+13],_=e[t+14],b=e[t+15],I=n[0],w=n[1],D=n[2],O=n[3];I=u(I,w,D,O,o,7,a[0]),O=u(O,I,w,D,c,12,a[1]),D=u(D,O,I,w,l,17,a[2]),w=u(w,D,O,I,f,22,a[3]),I=u(I,w,D,O,g,7,a[4]),O=u(O,I,w,D,T,12,a[5]),D=u(D,O,I,w,v,17,a[6]),w=u(w,D,O,I,m,22,a[7]),I=u(I,w,D,O,S,7,a[8]),O=u(O,I,w,D,C,12,a[9]),D=u(D,O,I,w,y,17,a[10]),w=u(w,D,O,I,A,22,a[11]),I=u(I,w,D,O,E,7,a[12]),O=u(O,I,w,D,R,12,a[13]),D=u(D,O,I,w,_,17,a[14]),I=h(I,w=u(w,D,O,I,b,22,a[15]),D,O,c,5,a[16]),O=h(O,I,w,D,v,9,a[17]),D=h(D,O,I,w,A,14,a[18]),w=h(w,D,O,I,o,20,a[19]),I=h(I,w,D,O,T,5,a[20]),O=h(O,I,w,D,y,9,a[21]),D=h(D,O,I,w,b,14,a[22]),w=h(w,D,O,I,g,20,a[23]),I=h(I,w,D,O,C,5,a[24]),O=h(O,I,w,D,_,9,a[25]),D=h(D,O,I,w,f,14,a[26]),w=h(w,D,O,I,S,20,a[27]),I=h(I,w,D,O,R,5,a[28]),O=h(O,I,w,D,l,9,a[29]),D=h(D,O,I,w,m,14,a[30]),I=d(I,w=h(w,D,O,I,E,20,a[31]),D,O,T,4,a[32]),O=d(O,I,w,D,S,11,a[33]),D=d(D,O,I,w,A,16,a[34]),w=d(w,D,O,I,_,23,a[35]),I=d(I,w,D,O,c,4,a[36]),O=d(O,I,w,D,g,11,a[37]),D=d(D,O,I,w,m,16,a[38]),w=d(w,D,O,I,y,23,a[39]),I=d(I,w,D,O,R,4,a[40]),O=d(O,I,w,D,o,11,a[41]),D=d(D,O,I,w,f,16,a[42]),w=d(w,D,O,I,v,23,a[43]),I=d(I,w,D,O,C,4,a[44]),O=d(O,I,w,D,E,11,a[45]),D=d(D,O,I,w,b,16,a[46]),I=p(I,w=d(w,D,O,I,l,23,a[47]),D,O,o,6,a[48]),O=p(O,I,w,D,m,10,a[49]),D=p(D,O,I,w,_,15,a[50]),w=p(w,D,O,I,T,21,a[51]),I=p(I,w,D,O,E,6,a[52]),O=p(O,I,w,D,f,10,a[53]),D=p(D,O,I,w,y,15,a[54]),w=p(w,D,O,I,c,21,a[55]),I=p(I,w,D,O,S,6,a[56]),O=p(O,I,w,D,b,10,a[57]),D=p(D,O,I,w,v,15,a[58]),w=p(w,D,O,I,R,21,a[59]),I=p(I,w,D,O,g,6,a[60]),O=p(O,I,w,D,A,10,a[61]),D=p(D,O,I,w,l,15,a[62]),w=p(w,D,O,I,C,21,a[63]),n[0]=n[0]+I|0,n[1]=n[1]+w|0,n[2]=n[2]+D|0,n[3]=n[3]+O|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,s=8*t.sigBytes;r[s>>>5]|=128<<24-s%32;var n=e.floor(i/4294967296),o=i;r[15+(s+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),r[14+(s+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,c=a.words,u=0;u<4;u++){var h=c[u];c[u]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}return a},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,r,i,s,n,o){var a=e+(t&r|~t&i)+s+o;return(a<<n|a>>>32-n)+t}function h(e,t,r,i,s,n,o){var a=e+(t&i|r&~i)+s+o;return(a<<n|a>>>32-n)+t}function d(e,t,r,i,s,n,o){var a=e+(t^r^i)+s+o;return(a<<n|a>>>32-n)+t}function p(e,t,r,i,s,n,o){var a=e+(r^(t|~i))+s+o;return(a<<n|a>>>32-n)+t}t.MD5=n._createHelper(c),t.HmacMD5=n._createHmacHelper(c)}(Math),i.MD5)},function(e,t,r){var i;e.exports=(i=i||function(e,t){var r=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),i={},s=i.lib={},n=s.Base={extend:function(e){var t=r(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=s.WordArray=n.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,r=e.words,i=this.sigBytes,s=e.sigBytes;if(this.clamp(),i%4)for(var n=0;n<s;n++){var o=r[n>>>2]>>>24-n%4*8&255;t[i+n>>>2]|=o<<24-(i+n)%4*8}else for(var n=0;n<s;n+=4)t[i+n>>>2]=r[n>>>2];return this.sigBytes+=s,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=n.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var r,i=[],s=function(t){var t=t,r=987654321,i=4294967295;return function(){var s=((r=36969*(65535&r)+(r>>16)&i)<<16)+(t=18e3*(65535&t)+(t>>16)&i)&i;return s/=4294967296,(s+=.5)*(e.random()>.5?1:-1)}},n=0;n<t;n+=4){var a=s(4294967296*(r||e.random()));r=987654071*a(),i.push(4294967296*a()|0)}return new o.init(i,t)}}),a=i.enc={},c=a.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],s=0;s<r;s++){var n=t[s>>>2]>>>24-s%4*8&255;i.push((n>>>4).toString(16)),i.push((15&n).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i+=2)r[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new o.init(r,t/2)}},u=a.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,i=[],s=0;s<r;s++){var n=t[s>>>2]>>>24-s%4*8&255;i.push(String.fromCharCode(n))}return i.join("")},parse:function(e){for(var t=e.length,r=[],i=0;i<t;i++)r[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new o.init(r,t)}},h=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},d=s.BufferedBlockAlgorithm=n.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=h.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r=this._data,i=r.words,s=r.sigBytes,n=this.blockSize,a=4*n,c=s/a,u=(c=t?e.ceil(c):e.max((0|c)-this._minBufferSize,0))*n,h=e.min(4*u,s);if(u){for(var d=0;d<u;d+=n)this._doProcessBlock(i,d);var p=i.splice(0,u);r.sigBytes-=h}return new o.init(p,h)},clone:function(){var e=n.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),p=(s.Hasher=d.extend({cfg:n.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){e&&this._append(e);var t=this._doFinalize();return t},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new p.HMAC.init(e,r).finalize(t)}}}),i.algo={});return i}(Math),i)},function(e,t,r){"use strict";var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n=r(3),o=r(1),a=r(0),c=r(5),u=r(2),h=function(e){function t(t,r,i){void 0===i&&(i={});var s=e.call(this)||this;if(s.C={MIN_DURATION:70,MAX_DURATION:6e3,DEFAULT_DURATION:100,MIN_INTER_TONE_GAP:50,DEFAULT_INTER_TONE_GAP:500},s.type=a.TypeStrings.DTMF,void 0===r)throw new TypeError("Not enough arguments");if(s.logger=t.ua.getLogger("sip.invitecontext.dtmf",t.id),s.owner=t,"string"==typeof r)r=r.toUpperCase();else{if("number"!=typeof r)throw new TypeError("Invalid tone: "+r);r=r.toString()}if(!r.match(/^[0-9A-D#*]$/))throw new TypeError("Invalid tone: "+r);s.tone=r;var n=i.duration,o=i.interToneGap;if(n&&!u.Utils.isDecimal(n))throw new TypeError("Invalid tone duration: "+n);if(n?n<s.C.MIN_DURATION?(s.logger.warn("'duration' value is lower than the minimum allowed, setting it to "+s.C.MIN_DURATION+" milliseconds"),n=s.C.MIN_DURATION):n>s.C.MAX_DURATION?(s.logger.warn("'duration' value is greater than the maximum allowed, setting it to "+s.C.MAX_DURATION+" milliseconds"),n=s.C.MAX_DURATION):n=Math.abs(n):n=s.C.DEFAULT_DURATION,s.duration=n,o&&!u.Utils.isDecimal(o))throw new TypeError("Invalid interToneGap: "+o);return o?o<s.C.MIN_INTER_TONE_GAP?(s.logger.warn("'interToneGap' value is lower than the minimum allowed, setting it to "+s.C.MIN_INTER_TONE_GAP+" milliseconds"),o=s.C.MIN_INTER_TONE_GAP):o=Math.abs(o):o=s.C.DEFAULT_INTER_TONE_GAP,s.interToneGap=o,s}return s(t,e),t.prototype.send=function(e){if(void 0===e&&(e={}),this.owner.status!==a.SessionStatus.STATUS_CONFIRMED&&this.owner.status!==a.SessionStatus.STATUS_WAITING_FOR_ACK)throw new c.Exceptions.InvalidStateError(this.owner.status);var t=e.extraHeaders?e.extraHeaders.slice():[],r={contentType:"application/dtmf-relay",body:"Signal= "+this.tone+"\r\nDuration= "+this.duration};if(this.owner.dialog){var i=this.owner.dialog.sendRequest(this,o.C.INFO,{extraHeaders:t,body:r});this.owner.emit("dtmf",i,this)}},t.prototype.init_incoming=function(e){e.reply(200),this.tone&&this.duration?this.owner.emit("dtmf",e,this):this.logger.warn("invalid INFO DTMF received, discarded")},t.prototype.receiveResponse=function(e){var t=e&&e.statusCode?e.statusCode:0;switch(!0){case/^1[0-9]{2}$/.test(t.toString()):break;case/^2[0-9]{2}$/.test(t.toString()):this.emit("succeeded",{originator:"remote",response:e});break;default:var r=u.Utils.sipErrorCause(t);this.emit("failed",e,r)}},t.prototype.onRequestTimeout=function(){this.emit("failed",void 0,o.C.causes.REQUEST_TIMEOUT),this.owner.onRequestTimeout()},t.prototype.onTransportError=function(){this.emit("failed",void 0,o.C.causes.CONNECTION_ERROR),this.owner.onTransportError()},t.prototype.onDialogError=function(e){this.emit("failed",e,o.C.causes.DIALOG_ERROR),this.owner.onDialogError(e)},t}(n.EventEmitter);t.DTMF=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(0),s=function(){function e(e,t){this.type=i.TypeStrings.SessionDescriptionHandlerObserver,this.session=e,this.options=t}return e.prototype.trackAdded=function(){this.session.emit("trackAdded")},e.prototype.directionChanged=function(){this.session.emit("directionChanged")},e}();t.SessionDescriptionHandlerObserver=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(15);t.Modifiers=i;var s=r(37);t.Simple=s.Simple;var n=r(28);t.SessionDescriptionHandler=n.SessionDescriptionHandler;var o=r(29);t.Transport=o.Transport},function(e,t,r){"use strict";(function(e){var i,s=this&&this.__extends||(i=function(e,t){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0});var n,o=r(3),a=r(27),c=r(15);!function(e){e[e.STATUS_NULL=0]="STATUS_NULL",e[e.STATUS_NEW=1]="STATUS_NEW",e[e.STATUS_CONNECTING=2]="STATUS_CONNECTING",e[e.STATUS_CONNECTED=3]="STATUS_CONNECTED",e[e.STATUS_COMPLETED=4]="STATUS_COMPLETED"}(n=t.SimpleStatus||(t.SimpleStatus={}));var u=function(t){function r(r){var i=t.call(this)||this;if(r.media.remote.video?i.video=!0:i.video=!1,r.media.remote.audio?i.audio=!0:i.audio=!1,!i.audio&&!i.video)throw new Error("At least one remote audio or video element is required for Simple.");i.options=r;var s=e.navigator.userAgent.toLowerCase(),o=!1,u=!1;s.indexOf("safari")>-1&&s.indexOf("chrome")<0?o=!0:s.indexOf("firefox")>-1&&s.indexOf("chrome")<0&&(u=!0);var h={};return o&&(h.modifiers=[c.stripG722]),u&&(h.alwaysAcquireMediaFirst=!0),i.options.ua.uri?i.anonymous=!1:i.anonymous=!0,i.ua=new a.UA({uri:i.options.ua.uri,authorizationUser:i.options.ua.authorizationUser,password:i.options.ua.password,displayName:i.options.ua.displayName,userAgentString:i.options.ua.userAgentString,register:!0,sessionDescriptionHandlerFactoryOptions:h,transportOptions:{traceSip:i.options.ua.traceSip,wsServers:i.options.ua.wsServers}}),i.state=n.STATUS_NULL,i.logger=i.ua.getLogger("sip.simple"),i.ua.on("registered",function(){i.emit("registered",i.ua)}),i.ua.on("unregistered",function(){i.emit("unregistered",i.ua)}),i.ua.on("failed",function(){i.emit("unregistered",i.ua)}),i.ua.on("invite",function(e){if(i.state!==n.STATUS_NULL&&i.state!==n.STATUS_COMPLETED)return i.logger.warn("Rejecting incoming call. Simple only supports 1 call at a time"),void e.reject();i.session=e,i.setupSession(),i.emit("ringing",i.session)}),i.ua.on("message",function(e){i.emit("message",e)}),i}return s(r,t),r.prototype.call=function(e){if(this.ua&&this.checkRegistration()){if(this.state===n.STATUS_NULL||this.state===n.STATUS_COMPLETED)return this.options.media.remote.audio&&(this.options.media.remote.audio.autoplay=!0),this.options.media.remote.video&&(this.options.media.remote.video.autoplay=!0),this.options.media.local&&this.options.media.local.video&&(this.options.media.local.video.autoplay=!0,this.options.media.local.video.volume=0),this.session=this.ua.invite(e,{sessionDescriptionHandlerOptions:{constraints:{audio:this.audio,video:this.video}}}),this.setupSession(),this.session;this.logger.warn("Cannot make more than a single call with Simple")}else this.logger.warn("A registered UA is required for calling")},r.prototype.answer=function(){if(this.state===n.STATUS_NEW||this.state===n.STATUS_CONNECTING)return this.options.media.remote.audio&&(this.options.media.remote.audio.autoplay=!0),this.options.media.remote.video&&(this.options.media.remote.video.autoplay=!0),this.session.accept({sessionDescriptionHandlerOptions:{constraints:{audio:this.audio,video:this.video}}});this.logger.warn("No call to answer")},r.prototype.reject=function(){if(this.state===n.STATUS_NEW||this.state===n.STATUS_CONNECTING)return this.session.reject();this.logger.warn("Call is already answered")},r.prototype.hangup=function(){if(this.state===n.STATUS_CONNECTED||this.state===n.STATUS_CONNECTING||this.state===n.STATUS_NEW)return this.state!==n.STATUS_CONNECTED?this.session.cancel():this.session?this.session.bye():void 0;this.logger.warn("No active call to hang up on")},r.prototype.hold=function(){if(this.state===n.STATUS_CONNECTED&&this.session&&!this.session.localHold)return this.mute(),this.logger.log("Placing session on hold"),this.session.hold();this.logger.warn("Cannot put call on hold")},r.prototype.unhold=function(){if(this.state===n.STATUS_CONNECTED&&this.session&&this.session.localHold)return this.unmute(),this.logger.log("Placing call off hold"),this.session.unhold();this.logger.warn("Cannot unhold a call that is not on hold")},r.prototype.mute=function(){this.state===n.STATUS_CONNECTED?(this.logger.log("Muting Audio"),this.toggleMute(!0),this.emit("mute",this)):this.logger.warn("An acitve call is required to mute audio")},r.prototype.unmute=function(){this.state===n.STATUS_CONNECTED?(this.logger.log("Unmuting Audio"),this.toggleMute(!1),this.emit("unmute",this)):this.logger.warn("An active call is required to unmute audio")},r.prototype.sendDTMF=function(e){this.state===n.STATUS_CONNECTED&&this.session?(this.logger.log("Sending DTMF tone: "+e),this.session.dtmf(e)):this.logger.warn("An active call is required to send a DTMF tone")},r.prototype.message=function(e,t){this.ua&&this.checkRegistration()?e&&t?this.ua.message(e,t):this.logger.warn("A destination and message are required to send a message"):this.logger.warn("A registered UA is required to send a message")},r.prototype.checkRegistration=function(){return this.anonymous||this.ua&&this.ua.isRegistered()},r.prototype.setupRemoteMedia=function(){var t=this;if(this.session){var r,i=this.session.sessionDescriptionHandler.peerConnection;i.getReceivers?(r=new e.window.MediaStream,i.getReceivers().forEach(function(e){var t=e.track;t&&r.addTrack(t)})):r=i.getRemoteStreams()[0],this.video?(this.options.media.remote.video.srcObject=r,this.options.media.remote.video.play().catch(function(){t.logger.log("play was rejected")})):this.audio&&(this.options.media.remote.audio.srcObject=r,this.options.media.remote.audio.play().catch(function(){t.logger.log("play was rejected")}))}else this.logger.warn("No session to set remote media on")},r.prototype.setupLocalMedia=function(){if(this.session){if(this.video&&this.options.media.local&&this.options.media.local.video){var t,r=this.session.sessionDescriptionHandler.peerConnection;r.getSenders?(t=new e.window.MediaStream,r.getSenders().forEach(function(e){var r=e.track;r&&"video"===r.kind&&t.addTrack(r)})):t=r.getLocalStreams()[0],this.options.media.local.video.srcObject=t,this.options.media.local.video.volume=0,this.options.media.local.video.play()}}else this.logger.warn("No session to set local media on")},r.prototype.cleanupMedia=function(){this.video&&(this.options.media.remote.video.srcObject=null,this.options.media.remote.video.pause(),this.options.media.local&&this.options.media.local.video&&(this.options.media.local.video.srcObject=null,this.options.media.local.video.pause())),this.audio&&(this.options.media.remote.audio.srcObject=null,this.options.media.remote.audio.pause())},r.prototype.setupSession=function(){var e=this;this.session?(this.state=n.STATUS_NEW,this.emit("new",this.session),this.session.on("progress",function(){return e.onProgress()}),this.session.on("accepted",function(){return e.onAccepted()}),this.session.on("rejected",function(){return e.onEnded()}),this.session.on("failed",function(){return e.onFailed()}),this.session.on("terminated",function(){return e.onEnded()})):this.logger.warn("No session to set up")},r.prototype.destroyMedia=function(){this.session&&this.session.sessionDescriptionHandler&&this.session.sessionDescriptionHandler.close()},r.prototype.toggleMute=function(e){if(this.session){var t=this.session.sessionDescriptionHandler.peerConnection;t.getSenders?t.getSenders().forEach(function(t){t.track&&(t.track.enabled=!e)}):t.getLocalStreams().forEach(function(t){t.getAudioTracks().forEach(function(t){t.enabled=!e}),t.getVideoTracks().forEach(function(t){t.enabled=!e})})}else this.logger.warn("No session to toggle mute")},r.prototype.onAccepted=function(){var e=this;this.session?(this.state=n.STATUS_CONNECTED,this.emit("connected",this.session),this.setupLocalMedia(),this.setupRemoteMedia(),this.session.sessionDescriptionHandler&&(this.session.sessionDescriptionHandler.on("addTrack",function(){e.logger.log("A track has been added, triggering new remoteMedia setup"),e.setupRemoteMedia()}),this.session.sessionDescriptionHandler.on("addStream",function(){e.logger.log("A stream has been added, trigger new remoteMedia setup"),e.setupRemoteMedia()})),this.session.on("dtmf",function(t,r){e.emit("dtmf",r.tone)}),this.session.on("bye",function(){return e.onEnded()})):this.logger.warn("No session for accepting")},r.prototype.onProgress=function(){this.state=n.STATUS_CONNECTING,this.emit("connecting",this.session)},r.prototype.onFailed=function(){this.onEnded()},r.prototype.onEnded=function(){this.state=n.STATUS_COMPLETED,this.emit("ended",this.session),this.cleanupMedia()},r.C=n,r}(o.EventEmitter);t.Simple=u}).call(this,r(12))}])});