<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="cache-control" content="no-cache">
    <link rel="stylesheet" type="text/css" href="./PeopleTrackFile/index.css" media="screen" />
    <script src="./PeopleTrackFile/echarts.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./PeopleTrackFile/jquery-1.10.2.min.js" type="text/javascript" charset="UTF-8"></script>
    <script src="./PeopleTrackFile/jquery.cookie.js" type="text/javascript"></script>
    <script src="./PeopleTrackFile/szyyAjax.js" type="text/javascript"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <style>
        .anchorBL{
            display:none;
        }
        #map_container {
            position: absolute;
            top: 19%;
            left: 2%;
            right: 2%;
            bottom: 1%;
            border-radius: 5px;
        }
        /*设置 tbody高度大于400px时 出现滚动条*/
        table tbody {
            display: block;
            height: 85%;
            overflow-y: scroll;
            position: absolute;
            width: 94%;
        }

        table thead,
        tbody tr {
            display: table;
            table-layout: fixed;
            width: 100%;
        }

        /*滚动条默认宽度是16px 将thead的宽度减16px*/
        table thead {
            width: calc(100%);
        }

        /*table tbody::-webkit-scrollbar{
        display:none;
    }*/
        ul::-webkit-scrollbar {
            width: 2px;
        }

        ul::-webkit-scrollbar-track {
            background-color: #025ead;
        }

        ul::-webkit-scrollbar-thumb {
            background-color: #0090ff;
        }

        ul::-webkit-scrollbar-thumb:hover {
            background-color: #025ead;
        }

        ul::-webkit-scrollbar-thumb:active {
            background-color: #025ead;
        }

        table tbody::-webkit-scrollbar {
            width: 3px;
        }

        table tbody::-webkit-scrollbar-track {
            background-color: #025ead;
        }

        table tbody::-webkit-scrollbar-thumb {
            background-color: #0090ff;
        }

        table tbody::-webkit-scrollbar-thumb:hover {
            background-color: #025ead;
        }

        table tbody::-webkit-scrollbar-thumb:active {
            background-color: #025ead;
        }
        .car{
            float: left;
            width: 40%;
            margin: 1% 5%;
        }
        .num{
            float: left;
            width: 50%;
            color: #F9ED0A;
            font-size: 25px;
            margin-top: -1%;
            font-weight: bold;
        }
        .history_botton{
            float: left;
            width: 110px;
            height: 30px;
            border: solid 2px #0090FF;
            background: #0090FF;
            font-size: 20px;
            text-align: center;
            line-height: 30px;
            border-radius: 5px;
            margin-left: 20px;
        }
    </style>
</head>
<body id="body">
<!-- <div class="logo">零租车辆轨迹</div> -->
<div class="center-wrapper" >
    <div class="history_l" style="width: 98%;height: 96%;">
        <div class="border_t"></div>
        <div class="border_t_l"></div>
        <div class="border_t_r"></div>
        <div class="border_b_l"></div>
        <div class="border_b_r"></div>
        <!--        <div class="history_list" style="margin-left: 15px;">
                    <p>车辆列表</p>
                </div>
                <div id="search_box" class="search_box">
                    <input type="text" size="20" id="sli_search_1"  class="sli_search_1" placeholder="请输入关键字">
                    <button class="search-btn" onclick="searchCarList()" style="cursor: pointer;"></button>
                </div> -->
        <table class="list_tb" >
            <thead>
            <tr>
                <th style='text-align: center;width: 20%'>序号</th>
                <th style='text-align: center;width: 40%'>离线开始时间</th>
                <th style='text-align: center;width: 40%'>离线结束时间</th>
                <!-- <th style='text-align: center;width: 15%'>状态</th> -->
            </tr>
            </thead>
            <tbody id="car_list_tbody" style="width: 98%;">
            </tbody>
        </table>
    </div>
    <script>
        var page = 1;
        var size = 20;
        var isCanLoadMore = true;
        $(function(){
            loadMore();
            $("#car_list_tbody").scroll(function(){
                var scrollTop = $(this).scrollTop();
                var scrollHeight =  $(this)[0].scrollHeight;
                var windowHeight = $(this).height();
                console.log(scrollTop+"  "+scrollHeight+"  "+windowHeight)
                if(scrollTop + windowHeight >= scrollHeight){
                    // clickMore();
                    if(isCanLoadMore) {
                        console.log("111")
                        page++;
                        loadMore();
                    }

                }
            });
        })
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg);  //匹配目标参数
            if (r != null && r!=undefined) return unescape(r[2]); return ''; //返回参数值
        }
        function loadMore() {
            // if($("#sli_search_1").val()==undefined || $("#sli_search_1").val().length<1 ) {return;}
            szyyAjaxByCookie({
                url: "../../Service/Kqgl/UserLocation/getOfflineReport",
                type: "post",
                dataType: 'json',
                async:true,
                contentType: "application/json",
                data:JSON.stringify({userId:getUrlParam("userId"),
                    startDate:getUrlParam("startDate"),
                    endDate:getUrlParam("endDate"),
                    page:(page+""),size:(size+"")}),
                success: function (data) {
                    var nodes="";
                    if(data.code==200){
                        // $("#car_list_tbody").empty();
                        // clearLoadInitData();
                        if(data.data!=null){
                            for (var i = 0; i < data.data.records.length; i++) {
                                nodes += "<tr style=\"cursor: pointer;\" class=\"col"+parseInt(((page-1)*size+i+1)%2+1)+"\">\n" +
                                    "         <td style=\"text-align: center;width: 20%;\">"+((page-1)*size+i+1)+"</td>\n" +
                                    "         <td style=\"width: 40%;text-align: center;\">"+(data.data.records[i].startTime==undefined?"--":data.data.records[i].startTime)+"</td>\n" +
                                    "         <td style=\"width: 40%;text-align: center;\">"+(data.data.records[i].endTime==undefined?"--":data.data.records[i].endTime)+"</td>\n" +
                                    "    </tr>"
                            }
                            if(data.data.records.length<size) {
                                isCanLoadMore = false;
                            }
                        }
                    }
                    $("#car_list_tbody").append(nodes);
                },
                error: function (data) {
                    layer.msg("查询错误："+data.responseJSON);
                }
            });
        }
    </script>

</div>
<script type="text/javascript" src="./PeopleTrackFile/nice-select.js"></script>
<link rel="stylesheet" type="text/css" href="./PeopleTrackFile/nice-select.css" media="screen" />
</body>
</html>
