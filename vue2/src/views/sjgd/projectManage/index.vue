<template>
  <div class="project-manage">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">新增项目</el-button>
      <!-- 其他按钮... -->
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="650px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <project-form
        ref="projectForm"
        :initial-data="formInitialData"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
    
    <!-- 其他内容... -->
  </div>
</template>

<script>
import ProjectForm from '@/components/ProjectForm'

export default {
  components: { ProjectForm },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      formInitialData: null
    }
  },
  methods: {
    // 打开新增弹窗
    handleAdd() {
      this.dialogTitle = '新增项目'
      this.formInitialData = null
      this.dialogVisible = true
    },

    // 提交表单
    async handleSubmit() {
      try {
        const formData = await this.$refs.projectForm.validate()
        // TODO: 调用保存接口
        await this.$api.saveProject(formData)
        this.$message.success('保存成功')
        this.dialogVisible = false
        this.getList() // 刷新列表
      } catch (error) {
        console.error(error)
        this.$message.error(typeof error === 'string' ? error : '保存失败')
      }
    }
  }
}
</script>