{"name": "init-digital", "description": "数字化平台", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^1.2.6", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "default-passive-events": "^2.0.0", "echarts": "^5.5.1", "element-ui": "^2.15.12", "fuse.js": "6.4.3", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "js-file-download": "^0.4.12", "jspdf": "^2.5.1", "less-loader": "^6.1.0", "nprogress": "0.2.0", "quill": "^1.3.7", "quill-image-resize-module": "^3.0.0", "sass": "1.32.13", "script-ext-html-webpack-plugin": "^2.1.5", "spark-md5": "^3.0.2", "splitpanes": "^2.4.1", "vue": "^2.6.11", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.12", "@vue/cli-plugin-router": "~4.5.12", "@vue/cli-plugin-vuex": "~4.5.12", "@vue/cli-service": "~4.5.12", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}