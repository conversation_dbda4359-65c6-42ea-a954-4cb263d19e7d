<template>
  <div>
    为确保上传数据与列表内容匹配，请先下载
    <el-button
        size="mini"
        type="text"
        @click="download(downloadName)"
    >导入模板
    </el-button>
    <el-upload
        class="upload-demo"
        drag
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :action="baseUrl + uploadUrl"
        name="file"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :headers="headers"
        :show-file-list="false"
        accept=".xlsx"
        :file-list="fileList"
        :class="{width: '100%'}"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">只能上传xlsx文件，且不超过20M</div>
    </el-upload>
  </div>
</template>

<script>
import Cookies from "js-cookie";
import {getToken} from "@/utils/auth";
import {DownLoadProject, downloadProjectImportExample} from "api/file";
import {downLoad} from "@/utils/tool";

export default {
  name: "FIleUpload",
  props: {
    // 图片数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 上传接口
    uploadUrl: {
      type: String,
      default: ''
    },
    // 导入示例文件名
    downloadName: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API + '/dwCnsj',
      appUrl: '/app',
      token: Cookies.get('jtoken'),
      headers: {
        'Authorization': getToken()
      },
      fileList: []
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  methods: {
    // 上传成功回调
    handleUploadSuccess(res) {
      // 返回的参数是对象
      console.log(res)
      if (res.code === 200) {
        this.$emit('uploadSuccessData', "上传成功")
      } else {
        this.$emit('uploadErrorData', res.message)
      }
      this.loading.close()
    },
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true
          return !!(fileExtension && fileExtension.indexOf(type) > -1)
        })
      } else {
        isImg = file.type.indexOf('image') > -1
      }

      if (!isImg) {
        this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
        )
        return false
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.loading = this.$loading({
        lock: true,
        text: '上传中',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError() {
      this.$message({
        type: 'error',
        message: '上传失败'
      })
      this.loading.close()
    },
    // 下载导入示例
    download(name) {
      downloadProjectImportExample({name: name}).then((res) => {
        const fileName =  '项目管理导入模板' + '.xlsx'
        downLoad(res, fileName)
      })
    },
  }
}
</script>

<style scoped>

</style>
