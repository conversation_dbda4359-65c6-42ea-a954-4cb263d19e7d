<template>
  <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParam.pageNum"
      :page-sizes="[20,50,100]"
      :page-size="queryParam.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
  </el-pagination>
</template>

<script>
export default {
  name:'index',
  props:{
    queryParam:{
      type: Object,
      default:()=>{
        return {
          pageNum: 1,
          pageSize: 20
        }
      }
    },
    total:{
      type:Number,
      default: 0
    },
  },
  methods:{
    handleSizeChange(val){
      this.queryParam.pageSize = val
      this.$emit('handleRefresh',this.queryParam)
    },
    handleCurrentChange(val){
      this.queryParam.pageNum = val
      this.$emit('handleRefresh',this.queryParam)
    },
  }
}
</script>

<style>

</style>