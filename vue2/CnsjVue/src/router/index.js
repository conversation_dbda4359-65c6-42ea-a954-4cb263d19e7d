import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layout'

Vue.use(VueRouter)

export const constantRoutes = [
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/redirect/:path(.*)',
                component: () => import('@/views/redirect')
            }
        ]
    },
    {
        path: '/404',
        component: () => import('@/views/error/404'),
        hidden: true
    },
    {
        path: '/401',
        component: () => import('@/views/error/401'),
        hidden: true
    },
    {
        path: "*",
        name: "NotFound",
        component: () => import("@/views/error/404"),
    },
    //协同办公路由
    {
        path: '/cnsj',
        component: Layout,
        // redirect: 'index',
        children: [
            {
                path: 'index.html',
                name: 'index.html',
                component: () => import('@/views/index'),
            },
            {
                path: 'zdgl',
                name: 'zdgl',
                component: () => import('@/views/common/zdgl/index'),
            },
            {
                path: 'ghzj',
                name: 'ghzj',
                component: () => import('@/views/cnsj/ghzj/index'),
            },
            {
                path: 'ghsj',
                name: 'ghsj',
                component: () => import('@/views/cnsj/ghsj/index'),
            },
            {
                path: 'xmgl',
                name: 'xmgl',
                component: () => import('@/views/cnsj/projectManage/index'),
            },
            {
                path: 'dxrfhqx',
                name: 'dxrfhqx',
                component: () => import('@/views/cnsj/dxrfhqx/index'),
            },
            {
                path: 'yzdfhqx',
                name: 'yzdfhqx',
                component: () => import('@/views/cnsj/yzdfhqx/index'),
            },
            {
                path: 'cncsqx',
                name: 'cncsqx',
                component: () => import('@/views/cnsj/cncsqx/index'),
            },
        ]
    },
]

const router = new VueRouter({
    mode: 'history',
    scrollBehavior: () => ({y: 0}),
    base: process.env.NODE_ENV === 'development' ? '/' : process.env.VUE_APP_BASE_API,
    routes: constantRoutes
})

export default router
