export default{
    bind(el, binding){
        /**
         *  给一个页面元素添加水印背景
         * @param text 文字内容
         * @param textColor 文字颜色
         * @param backgroundColor 背景色
         * @param sourceBody 挂载元素
         */
        function setWatermark({text, textColor, backgroundColor}, sourceBody) {
            let can = document.createElement('canvas')
            can.width = 380
            can.height = 280

            let cans = can.getContext('2d')
            cans.rotate(-20 * Math.PI / 180)
            cans.font = '40px Vedana'

            cans.fillStyle = textColor
            cans.textAlign = 'left'
            cans.textBaseline = 'Middle'
            let textArr = text.split(',')
            textArr.forEach((element,index) => {
                cans.fillText(element, can.width / 40, can.height/2 + (index*40))
            });
            sourceBody.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
            sourceBody.style.backgroundColor = backgroundColor
        }

        setWatermark({
            text:binding.value.label,
            textColor:"#ddd",
            backgroundColor:"#fff",
        },el)
    }
}

