const theme = localStorage.getItem('layout-theme') || ''
const percent = JSON.parse(localStorage.getItem('resize-percent')) || ''
const state = {
    theme: theme || '#4877fb',
    size: localStorage.getItem('layout-size') || 'mini',
    sidebar: localStorage.getItem('layout-sidebar') ? !!+localStorage.getItem('layout-sidebar') : true,
    leftResize: percent.leftResize === undefined ? 50 : percent.leftResize,
    topResize: percent.topResize === undefined ? 50 : percent.topResize,
    bottomResize: percent.bottomResize === undefined ? 50 : percent.bottomResize
}

const mutations = {
  CHANGE_SETTING: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar = !state.sidebar
    if (state.sidebar) {
      localStorage.setItem('layout-sidebar', 1)
    } else {
      localStorage.setItem('layout-sidebar', 0)
    }
  },
  SET_SIZE: (state, size) => {
    state.size = size
    localStorage.setItem('layout-size', size)
  },
  SET_RESIZE:(state, {key, value}) =>{
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  }
}
  
const actions = {
// 修改布局设置
  changeSetting({ commit }, data) {
      commit('CHANGE_SETTING', data)
  },
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setResize({commit}, data){
    commit('SET_RESIZE', data)
  }
}
export default {
    namespaced: true,
    state,
    mutations,
    actions
}