<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">用户名称：</span>
        <el-select v-model="queryParams.userNum" clearable filterable placeholder="请选择用户名称"
                   @change="handleChange">
          <el-option
              v-for="item in userList"
              :key="item.userNum"
              :label="item.userName"
              :value="item.userNum">
          </el-option>
        </el-select>
        <span class="font-size14">选择日期：</span>
        <el-date-picker
            v-model="value1"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            @change="monthrangeChange"
        >
        </el-date-picker>
        <el-button type="text" icon="el-icon-search" @click="getMaxLoad" v-has-permi="['JDWCN01QX01QX01']">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="completeMaxLoad" v-has-permi="['JDWCN01QX01QX02']">重新计算</el-button>
      </div>
    </div>
    <div class="table-box">
      <div id="main" style="width: 100%;height: 100%;"></div>
    </div>
  </div>
</template>

<script>
import { GetUserList} from "api/dxrfh/dxrfh";
import {getDate} from "@/utils/tool";
import * as echarts from 'echarts';
import {CompleteMaxLoad, GetMaxLoad} from "api/yzdfhqx/yzdfhqx";

export default {
  name: 'yzdfhqx',
  created() {
    this.getUserList()
  },
  mounted() {

  },
  data() {
    return {
      // 查询参数
      queryParams: {
        userNum: undefined,
        useDate: undefined,
        startYear:null,
        endYear:null,
        startMonth:null,
        endMonth:null,
      },
      userList: [],
      dailyLoad: {},
      chartDom: '',
      mychart: '',
      option: '',
      timeArray: [],
      value1: '',
    }
  },
  methods: {
    getUserList() {
      GetUserList().then(res => {
        if (res.code === 200) {
          this.userList = res.result
          // if (this.userList.length > 0) {
          //   this.queryParams.userNum = this.userList[0].userNum
          // }
        }
      })
    },
    monthrangeChange(){
      this.timeArray = []

      this.queryParams.startYear = Number(this.value1[0].split("-")[0])
      this.queryParams.endYear = Number(this.value1[1].split("-")[0])
      this.queryParams.endMonth = Number(this.value1[1].split('-')[1])
      this.queryParams.startMonth = Number(this.value1[0].split('-')[1])

      let start = new Date(this.value1[0]);
      let end = new Date(this.value1[1]);
      while (start <= end) {
        let month = start.getMonth() + 1; // 获取月份，注意月份从0开始，所以要加1
        let year = start.getFullYear();
        //console.log(`${year}-${month}`);
        this.timeArray.push(`${year}-${month}`)
        start.setMonth(start.getMonth() + 1);
      }

      this.chartDom = document.getElementById('main')
      this.mychart = echarts.init(this.chartDom)
      this.initChart()

      if (this.queryParams.userNum != null){
        this.getMaxLoad()
      }


    },
    handleChange() {
    },
    checkQueryParam(){
      if(this.queryParams.userNum == null){
        this.$message({
          type: 'error',
          message: '请选择用户名称！'
        })
        return false
      }
      if (this.value1 == null || this.value1 === ''){
        console.log(22222222222)
        this.$message({
          type: 'error',
          message: '请选择时间区间！'
        })
        return false
      }
      return true
    },

    getMaxLoad() {
      if(!this.checkQueryParam()){
        return
      }

      GetMaxLoad(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dailyLoad = res.result
          if(this.dailyLoad.length === 0){
            this.$message({
              type: 'warning',
              message: '暂无数据！'
            })
            return
          }
          this.actualLoadList = []
          //数据补充 防止数据对不上
          for (let i = 0; i < this.timeArray.length; i++) {
            const yyyy = Number(this.timeArray[i].split("-")[0]);
            const MM = Number(this.timeArray[i].split("-")[1]);
            const res = this.dailyLoad.find(ee=>ee.year === yyyy && ee.month === MM )
            if(res != null){
              this.actualLoadList.push(res.maxLoad)
            }else {
              this.actualLoadList.push(0)
            }

          }

          this.initChart()
        }
      })
    },
    completeMaxLoad() {
      if(!this.checkQueryParam()){
        return
      }

      CompleteMaxLoad(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dailyLoad = res.result
          this.actualLoadList = []
          if(this.dailyLoad.length === 0){
            this.$message({
              type: 'warning',
              message: '暂无数据！'
            })
            return
          }

          this.$message({
            type: 'success',
            message: '重新计算成功！'
          })

          //数据补充 防止数据对不上
          for (let i = 0; i < this.timeArray.length; i++) {
            const yyyy = Number(this.timeArray[i].split("-")[0]);
            const MM = Number(this.timeArray[i].split("-")[1]);
            const res = this.dailyLoad.find(ee=>ee.year === yyyy && ee.month === MM )
            if(res != null){
              this.actualLoadList.push(res.maxLoad)
            }else {
              this.actualLoadList.push(0)
            }

          }

          this.initChart()
        }

      })
    },
    initChart() {
      this.option = {
        title: {
          text: '月最大负荷曲线'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['功率']
        },

        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          name: '时间', // 设置 x 轴标题
          nameLocation: 'end', // 设置标题显示在 x 轴的末端
          nameGap: 20, // 设置标题与轴线之间的距离
          type: 'category',
          data: this.timeArray,
          axisLine: {
            // lineStyle: {
            //   color: 'white' // 设置坐标轴线颜色为白色
            // }
          },
          axisLabel: {
            // color: 'white', // 设置坐标轴标签颜色为白色
            rotate: 45, // 设置标签旋转角度为45度
            fontStyle: 'italic' // 设置字体为斜体
          },
          splitLine: {
            show: true,
            lineStyle: {
              // color: 'white' // 设置分割线颜色为白色
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '功率(kW)', // 设置 y 轴标题
          nameLocation: 'end', // 设置标题显示在 y 轴的末端
          nameGap: 20, // 设置标题与轴线之间的距离
          axisLine: {
            lineStyle: {
              // color: 'white' // 设置坐标轴线颜色为白色
            }
          },
          axisLabel: {
            // color: 'white' // 设置坐标轴标签颜色为白色
          },
          splitLine: {
            show: true,
            lineStyle: {
              // color: 'white' // 设置分割线颜色为白色
            }
          }
        },
        series: [
          {
            name: '功率',
            type: 'line',
            symbolSize: 10, // 调整点的大小，单位为像素
            data: this.actualLoadList,
            label: {
              show: true, // 显示数据标签
              // color: 'white' // 设置数据标签颜色为白色
            },
            itemStyle: {
              color: '#ec812f',
              width: 8
            },
            lineStyle: {
              color: '#a3b9e3',
              width: 5
            },
            zlevel: 1,
          }
        ]
      };
      this.option && this.mychart.setOption(this.option);
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }

    .el-input-number {
      width: 200px;
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>