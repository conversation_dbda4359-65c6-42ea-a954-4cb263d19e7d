<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">用户名称：</span>
        <el-select v-model="queryParams.userNum" clearable filterable placeholder="请选择用户名称"
                   @change="handleChange">
          <el-option
              v-for="item in userList"
              :key="item.userNum"
              :label="item.userName"
              :value="item.userNum">
          </el-option>
        </el-select>
        <span class="font-size14">开始月份：</span>
        <el-date-picker
            v-model="queryParams.useDate"
            type="month"
            placeholder="开始月份"
            value-format="yyyy-MM-dd"
            @change="handleChange"
        >
        </el-date-picker>
        <el-button type="text" icon="el-icon-search" @click="getDailySorce">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="completeDailySorce">重新计算</el-button>
      </div>
    </div>
    <div class="table-box">
      <div id="main" style="width: 100%;height: 100%;"></div>
    </div>
  </div>
</template>

<script>
import {GetUserList} from "api/dxrfh/dxrfh";
import {CompleteDailySorce, GetDailySorce} from "api/cncs/cncs";
import * as echarts from "echarts";
import {getDate} from "@/utils/tool";
import {listDataAll} from "api/zdgl/data";


export default {
  name: 'dxrfhqx',
  created() {
    this.queryParams.useDate = getDate(new Date(), 'YYYY-MM') + '-01'
    this.getUserList()
    this.getDict()
  },
  mounted() {
    this.$nextTick(() => {
      this.chartDom = document.getElementById('main')
      this.mychart = echarts.init(this.chartDom)
      this.initChart()
    })
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        userNum: undefined,
        useDate: undefined,
      },
      userList: [],
      dailySorceList: [],
      chartDom: '',
      mychart: '',
      option: '',
      powerArray: [],
      utilizeDays: [],
      dictList: [],
      power: 0
    }
  },
  methods: {
    getUserList() {
      GetUserList().then(res => {
        if (res.code === 200) {
          this.userList = res.result
          if (this.userList.length > 0) {
            this.queryParams.userNum = this.userList[0].userNum
          }
        }
      })
    },
    getDict() {
      listDataAll({dictType: 'cnsj-coefficient'}).then(res => {
        if (res.code === 200) {
          this.dictList = res.result
          if (Array.isArray(this.dictList)) {
            this.dictList.forEach(value => {
              if (value.value === 'cnsj-coefficient-conclusionDays') {
                this.power = Number(value.label)
              }
            })
          }
        }
      })
    },
    handleChange() {},
    getDailySorce() {
      this.mychart.showLoading({
        text: 'loading',
        color: 'rgba(20, 149, 247, 0.7)',	//设置加载颜色
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0
      })
      GetDailySorce(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dailySorceList = res.result
          this.powerArray = this.dailySorceList.map(item => {
            return item.power
          })
          this.utilizeDays = this.dailySorceList.map(item => {
            return item.utilizeDays
          })
          this.initChart()
        }
      }).finally(() => {
        this.mychart.hideLoading()
      })
    },
    completeDailySorce() {
      this.mychart.showLoading({
        text: 'loading',
        color: 'rgba(20, 149, 247, 0.7)',	//设置加载颜色
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0
      })
      CompleteDailySorce(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dailySorceList = res.result
          this.powerArray = this.dailySorceList.map(item => {
            return item.power
          })
          this.utilizeDays = this.dailySorceList.map(item => {
            return item.utilizeDays
          })
          this.initChart()
        }
      }).finally(() => {
        this.mychart.hideLoading()
      })
    },
    initChart() {
      this.option = {
        title: {
          text: '储能测算'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['年利用天数']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.powerArray,
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '年利用天数',
            type: 'line',
            data: this.utilizeDays,
            itemStyle: {
              color: '#2f4554'
            },
            lineStyle: {
              color: '#2f4554'
            },
            markLine: {
              data: [{ name: 'Y 轴水平线', yAxis: this.power}]
            }
          }
        ]
      };
      this.option && this.mychart.setOption(this.option);
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }

    .el-input-number {
      width: 200px;
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>