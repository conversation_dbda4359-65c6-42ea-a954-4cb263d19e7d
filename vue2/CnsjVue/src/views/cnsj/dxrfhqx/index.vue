<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">用户名称：</span>
        <el-select v-model="queryParams.userNum" clearable filterable placeholder="请选择用户名称"
                   @change="handleChange">
          <el-option
              v-for="item in userList"
              :key="item.userNum"
              :label="item.userName"
              :value="item.userNum">
          </el-option>
        </el-select>
        <span class="font-size14">选择日期：</span>
        <el-date-picker
            v-model="queryParams.useDate"
            type="date"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
        >
        </el-date-picker>
        <span class="font-size14">负荷值：</span>
        <el-input-number v-model="queryParams.inputPower" clearable :min="0" @change="handleChange"></el-input-number>
        <el-button type="text" icon="el-icon-search" @click="getDailyLoad">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="completeDailyLoad">重新计算</el-button>
      </div>
    </div>
    <div class="table-box">
      <div id="main" style="width: 100%;height: 100%;"></div>
    </div>
  </div>
</template>

<script>
import {CompleteDailyLoad, GetDailyLoad, GetUserList} from "api/dxrfh/dxrfh";
import * as echarts from 'echarts';
import {getDate} from "@/utils/tool";

export default {
  name: 'dxrfhqx',
  created() {
    this.queryParams.useDate = getDate()
    this.getUserList()
  },
  mounted() {
    this.$nextTick(() => {
      // 从0点开始，到23点59分结束，每15分钟添加一个时间点
      for (let hour = 0; hour < 24; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          let time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          this.timeArray.push(time);
        }
      }

      this.chartDom = document.getElementById('main')
      this.mychart = echarts.init(this.chartDom)
      this.initChart()
    })
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        userNum: undefined,
        useDate: undefined,
        inputPower: 200
      },
      userList: [],
      dailyLoad: {},
      chartDom: '',
      mychart: '',
      option: '',
      timeArray: [],
      actualLoadList: [],
      dischargeLoadList: [],
      weightLoadList: [],
      estimateLoadList: []
    }
  },
  methods: {
    getUserList() {
      GetUserList().then(res => {
        if (res.code === 200) {
          this.userList = res.result
          if (this.userList.length > 0) {
            this.queryParams.userNum = this.userList[0].userNum
          }
        }
      })
    },
    handleChange() {},
    getDailyLoad() {
      this.mychart.showLoading({
        text: 'loading',
        color: 'rgba(20, 149, 247, 0.7)',	//设置加载颜色
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0
      })
      GetDailyLoad(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dailyLoad = res.result
          this.actualLoadList = this.dailyLoad.actualLoadList.map(item => {
            return item.power
          })
          this.dischargeLoadList = this.dailyLoad.dischargeLoadList.map(item => {
            return item.power
          })
          this.weightLoadList = this.dailyLoad.weightLoadList.map(item => {
            return item.power
          })
          this.estimateLoadList = this.dailyLoad.estimateLoadList.map(item => {
            return item.power
          })
          this.initChart()
        }
      }).finally(() => {
        this.mychart.hideLoading()
      })
    },
    completeDailyLoad() {
      this.mychart.showLoading({
        text: 'loading',
        color: 'rgba(20, 149, 247, 0.7)',	//设置加载颜色
        textColor: '#000',
        maskColor: 'rgba(255, 255, 255, 0.2)',
        zlevel: 0
      })
      CompleteDailyLoad(this.queryParams).then(res => {
        if (res.code === 200) {
          this.dailyLoad = res.result
          this.actualLoadList = this.dailyLoad.actualLoadList.map(item => {
            return item.power
          })
          this.dischargeLoadList = this.dailyLoad.dischargeLoadList.map(item => {
            return item.power
          })
          this.weightLoadList = this.dailyLoad.weightLoadList.map(item => {
            return item.power
          })
          this.estimateLoadList = this.dailyLoad.estimateLoadList.map(item => {
            return item.power
          })
          this.initChart()
        }
      }).finally(() => {
        this.mychart.hideLoading()
      })
    },
    initChart() {
      this.option = {
        title: {
          text: '储能策略表'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['实际负荷', '储能充放电', '加权后负荷', '预测需要值']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.timeArray,
          axisLabel: {
            interval: 3
          }
        },
        yAxis: {
          type: 'value',
          name: '功率(kW)', // 设置 y 轴标题
          nameLocation: 'end', // 设置标题显示在 y 轴的末端
          nameGap: 20, // 设置标题与轴线之间的距离
        },
        series: [
          {
            name: '实际负荷',
            type: 'line',
            data: this.actualLoadList,
            itemStyle: {
              color: '#2f4554'
            },
            lineStyle: {
              color: '#2f4554'
            },
            zlevel: 2,
          },
          {
            name: '储能充放电',
            type: 'line',
            data: this.dischargeLoadList,
            itemStyle: {
              color: '#d48265'
            },
            lineStyle: {
              color: '#d48265'
            },
            zlevel: 3,
          },
          {
            name: '加权后负荷',
            type: 'line',
            data: this.weightLoadList,
            itemStyle: {
              color: '#c23531'
            },
            lineStyle: {
              color: '#c23531'
            },
            zlevel: 1,
          },
          {
            name: '预测需要值',
            type: 'line',
            data: this.estimateLoadList,
            itemStyle: {
              color: '#61a0a8'
            },
            lineStyle: {
              color: '#61a0a8'
            },
            zlevel: 4,
          }
        ]
      };
      this.option && this.mychart.setOption(this.option);
    }
  }
}
</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }

    .el-input-number {
      width: 200px;
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
</style>