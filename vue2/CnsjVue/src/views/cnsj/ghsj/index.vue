<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">

        <span class="font-size14">用户名称：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入用户名称"
                  @change="handleChange"></el-input>
        <span class="font-size14">用户编号：</span>
        <el-input v-model="queryParams.userNum" clearable placeholder="请输入用户编号"
                  @change="handleChange"></el-input>
        <span class="font-size14">使用日期：</span>
        <el-date-picker
            v-model="queryParams.useDateArray"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="handleChange"
            style="width: 20%">
        </el-date-picker>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            v-has-permi="['JDWCN01GH01QX01']"
            @click="getList"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button size="mini" type="text" icon="el-icon-upload2" v-has-permi="['JDWCN01GH01QX02']"
                   @click="openDialog('upload')">导入
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <!--  上传文件  -->
    <el-dialog
        width="420px"
        :visible="uploadDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeUploadDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">文件上传</div>
      <FIleUpload
          :file-size="20"
          :file-type="['xlsx']"
          upload-url="/cnsj/ghsj/importSheetsExcel"
          download-name="jsyxxImport.xlsx"
          @uploadSuccessData="uploadSuccessData"
          @uploadErrorData="uploadErrorData"
      />
    </el-dialog>

  </div>
</template>
<script>
import Table from "components/MainTable/index.vue";
import Pagination from '@/components/Pagination/ghsj'
import { GetGhsjList , delRecord, updateRecord} from '@/api/ghsj/ghsj';
import { getToken } from '@/utils/auth'
// 上传文件
import FIleUpload from "@/components/CNSJ/FIleUpload.vue";

export default {
  name: 'ghsj',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination, FIleUpload},
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getList()
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
        userName: '',
        userNum: '',
        useDateArray:[]
      },
      // 列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      driverParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      driverDialog: false,
      uploadDialog: false,
      // 表单校验规则
      driverRules: {
      },
      realTableOptions: [
        {label: '用户名称', prop: 'userName'},
        {label: '用户编号', prop: 'userNum'},
        {label: '使用日期', prop: 'useDate'},
        {label: '使用功率（瞬时有功）', prop: 'workingPower'},
        // {label: '手机短号', prop: 'sphone'},
      ],
      tableOptions: [
        {label: '用户名称', prop: 'userName'},
        {label: '用户编号', prop: 'userNum'},
        {label: '使用日期', prop: 'useDate'},
        {label: '使用功率（瞬时有功）', prop: 'workingPower'},
        // {label: '手机短号', prop: 'sphone'},
      ],
    }
  },
  methods: {

    // 上传文件开始=======================
    closeUploadDialog () {
      this.getList()
      this.uploadDialog = false
    },
    uploadSuccessData() {
      this.$message({
        type: 'success',
        message: '上传成功，Excel解析中，请稍等'
      })
      this.closeUploadDialog()
    },
    uploadErrorData(message) {
      this.$message({
        type: 'error',
        message: '上传失败：' + message
      })
    },
    // =======================================上传文件结束

    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 列表查询 */
    getList () {
      this.loading = true
      GetGhsjList(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.driverParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增'
          this.selectID = 0
          this.driverParams = {}
          this.driverDialog = true
          break
        case 'upload':
          this.uploadDialog = true
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getList()
      this.driverDialog = false
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'add':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              InsertDriver(this.driverParams).then(res => {

                if (res.code == 200){
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getList()
                  this.closeDialog()
                }else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
        case 'edit':
          this.$refs.driverParams.validate((valid) => {
            if (valid) {
              EditDriverPhone(this.driverParams).then(res => {
                if (res.code ==200){
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getList()
                  this.closeDialog()
                }else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>

