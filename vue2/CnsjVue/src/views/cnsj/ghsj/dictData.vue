<template>
  <!-- 字典数据弹窗   -->
  <el-dialog :title="tableName" :visible.sync="visible" width="70%">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="字典标签" prop="label">
        <el-input v-model="queryParams.label" placeholder="请输入字典标签" clearable style="width: 240px"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <!--      <el-form-item label="字典类型" prop="type">-->
      <!--        <el-input v-model="queryParams.type" placeholder="请输入字典类型" clearable style="width: 240px" @keyup.enter.native="handleQuery"/>-->
      <!--      </el-form-item>-->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option label="停用" value="1"></el-option>
          <el-option label="启用" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="medium" type="text" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button size="medium" type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button size="medium" type="text" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="dataList" border height="500">
      <el-table-column label="字典编码" align="center" prop="id"/>
      <el-table-column label="字典标签" align="center" prop="label"/>
      <el-table-column label="字典键值" align="center" prop="value"/>
      <el-table-column label="字典排序" align="center" prop="sort"/>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.status === 0">启用</el-tag>
          <el-tag type="danger" v-if="scope.row.status === 1">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180"></el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template v-slot="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="queryParams.total"/>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="addOrUpdateTitle" :visible.sync="open" width="500px" :before-close="cancel"
               :append-to-body="true">
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="字典类型">
          <el-input v-model="form.dictType" :disabled="true"/>
        </el-form-item>
        <el-form-item label="数据标签" prop="label">
          <el-input v-model="form.label" placeholder="请输入数据标签"/>
        </el-form-item>
        <el-form-item label="数据键值" prop="value">
          <el-input v-model="form.value" placeholder="请输入数据键值"/>
        </el-form-item>
        <el-form-item label="显示排序" prop="sort">
          <el-input-number v-model="form.sort" controls-position="right" :min="0"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">开启</el-radio>
            <el-radio :label="1">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {addData, updateData, listData, delData} from "api/ghsj/data";

export default {
  components: {},
  data() {
    return {
      dataList: [],
      dictTypeData: {},
      visible: false,
      loading: false,
      open: false,
      tableName: '',
      addOrUpdateTitle: '',
      // 表单参数
      form: {
        dictType: null,
        label: null,
        value: null,
        sort: null,
        status: null,
        remark: null
      },
      // 表单校验
      rules: {
        label: [{required: true, message: "字典标签不能为空", trigger: "blur"}],
        value: [{required: true, message: "字典键值不能为空", trigger: "blur"}],
        status: [{required: true, message: "字典状态不能为空", trigger: "blur"}],
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        label: undefined,
        dictType: undefined,
        status: undefined,
      },
    };
  },
  created() {

  },
  methods: {
    init() {
      this.visible = true;
      this.queryParams.dictType = this.dictTypeData.type
      this.tableName = this.dictTypeData.name + "-数据管理"
      this.handleQuery()
    },
    handleQuery() {
      listData(this.queryParams).then(res => {
        //创建时间剔除毫秒
        res.result.records.forEach(item => {
          item.createTime = item.createTime.slice(0, 19)
        })
        this.queryParams.total = res.result.total
        this.dataList = res.result.records
      })
    },
    //数据新增
    handleAdd() {
      this.addOrUpdateTitle = '字典数据新增'
      this.form.dictType = this.dictTypeData.type
      this.open = true;
    },
    //修改
    handleUpdate(row) {
      this.addOrUpdateTitle = '字典数据修改'
      this.form = row
      this.open = true;
    },
    //删除
    handleDelete(row) {
      delData(row.id).then(res => {
        this.$message({
          message: '删除成功',
          type: 'success'
        });
        this.handleQuery();
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id !== undefined && this.form.id !== "") {
            updateData(this.form).then(response => {
              if (response.code === 200) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                });
                this.resetForm('form')
                this.open = false;
                this.handleQuery();
              } else {
                this.$message({
                  message: '修改失败;' + response.getMessage(),
                  type: 'warning'
                });
              }

            });
          } else {
            addData(this.form).then(response => {
              if (response.code === 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                });
                this.resetForm('form')
                this.open = false;
                this.handleQuery();
              } else {
                this.$message({
                  message: '新增失败;' + response.getMessage(),
                  type: 'warning'
                });
              }
            });
          }
        }
      });
    },
    //新增修改弹窗取消
    cancel() {
      this.open = false;
      this.resetForm('form')
      this.handleQuery();
    },
    //清空表单
    resetForm(formName) {
      for (var key in this[formName]) {
        this[formName][key] = ''
      }
    },
    //清空查询参数
    rest() {
      // 查询参数
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.total = 10
      this.queryParams.label = undefined
      this.queryParams.status = undefined
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.rest();
      this.handleQuery();
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
  }
};
</script>
