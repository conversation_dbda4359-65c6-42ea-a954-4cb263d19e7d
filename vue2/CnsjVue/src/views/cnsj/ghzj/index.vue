<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">
        <span class="font-size14">用户名称：</span>
        <el-select v-model="queryParams.userNum" clearable filterable placeholder="请选择用户名称"
                   @change="handleChange">
          <el-option
              v-for="item in userList"
              :key="item.userNum"
              :label="item.userName"
              :value="item.userNum">
          </el-option>
        </el-select>
        <span class="font-size14">选择日期：</span>
        <el-date-picker
            v-model="queryParams.years"
            type="year"
            placeholder="选择日期"
            value-format="yyyy"
            @change="handleChange"
        >
        </el-date-picker>

        <el-button type="text" icon="el-icon-search" @click="handleQuery" v-has-permi="['JDWCN01GH02QX01']">查询</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        <el-button type="text" icon="el-icon-refresh" @click="computeGhzj" v-has-permi="['JDWCN01GH02QX02']">重新计算</el-button>
      </div>
    </div>

      <div class="table-box">
        <el-descriptions title="" direction="vertical" :column="tableData.length" border  >
          <template  v-for="(item,index) in tableData">
            <el-descriptions-item :label="item.month+'月最高功时段'" :key="index"  :label-style="label_style" :content-style="content_style">{{ item.maxWorkingPowerTime }}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="最高功率" :key="index" :label-style="label_style" :content-style="content_style">{{ item.maxWorkingPower }}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="11-13点最高功率" :key="index" :label-style="label_style" :content-style="content_style"> {{ item.maxWorkingPower11To13 }}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="相差功率" :key="index" :label-style="label_style" :content-style="content_style"> {{item.powerDifference}}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="8-10点最低功率时间"  :key="index" :label-style="label_style" :content-style="content_style"> {{ item.minWorkingPowerTime8To10 }}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="最低功率"  :key="index" :label-style="label_style" :content-style="content_style">{{item.minWorkingPower8To10}} </el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="10-11点最低功率时间"  :key="index" :label-style="label_style" :content-style="content_style"> {{ item.minWorkingPowerTime10To11 }}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="最低功率"  :key="index" :label-style="label_style" :content-style="content_style"> {{ item.minWorkingPower10To11 }}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="15-17点最低功率时间" :key="index" :label-style="label_style" :content-style="content_style"> {{item.minWorkingPowerTime15To17}}</el-descriptions-item>
          </template>

          <template  v-for="(item,index) in tableData">
            <el-descriptions-item  label="最低功率" :key="index" :label-style="label_style" :content-style="content_style"> {{item.minWorkingPower15To17}} </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>

  </div>
</template>

<script>
import Table from "components/MainTable/index.vue";
import {computeGhzj, getGhzjData} from "api/ghsj/ghzj";
import {GetUserList} from "api/dxrfh/dxrfh";
export default {
  components: { Table},
  data() {
    return {
      label_style:{
        'text-align': 'center',//文本居中
      },
      content_style:{
        'text-align': 'center',//文本居中
      },
      // 查询参数
      queryParams: {
        year:null,
        userNum:null,
        years:''
      },
      loading: false,
      userList: [],
      tableData:[]

    }
  },
  computed: {
    watermark() {
      return this.$store.state.user.watermark
    }
  },
  created() {
    //this.getGhsj()
    this.getUserList()
  },
  methods: {
    getUserList() {
      GetUserList().then(res => {
        if (res.code === 200) {
          this.userList = res.result
          // if (this.userList.length > 0) {
          //   this.queryParams.userNum = this.userList[0].userNum
          // }
        }
      })
    },
    handleChange() {

    },
    handleQuery(){
      this.getGhsj()
    },
    resetQuery(){
      this.queryParams.year = null
      this.queryParams.userNum = null
      this.queryParams.years = null
    },

    /**
     * 重新计算
     */
    computeGhzj(){
      this.tableData = []
      if(!this.checkQueryParam()){
        return
      }

      computeGhzj(this.queryParams).then(res=>{
        if(res.code === 200){
          if(res.result.length === 0){
            this.$message({
              type: 'warning',
              message: '暂无数据！'
            })
            return
          }
          this.$message({
            type: 'success',
            message: '重新计算成功！'
          })
          this.tableData = res.result

        }
      })
    },
    checkQueryParam() {
      if(this.queryParams.years === '' || this.queryParams.years === null){
        this.$message({
          type: 'error',
          message: '请选择年份！'
        })
        return false
      }
      if(this.queryParams.userNum === '' || this.queryParams.userNum === null){
        this.$message({
          type: 'error',
          message: '请选择用户名称！'
        })
        return false
      }
      return true;
    },

    //获取功耗数据
    getGhsj(){
      this.tableData = []
      if(!this.checkQueryParam()){
        return
      }


      this.queryParams.year = Number(this.queryParams.years)
      getGhzjData(this.queryParams).then(res=>{
        if(res.code === 200){

          if(res.result.length === 0){
            this.$message({
              type: 'warning',
              message: '暂无数据！'
            })
            return
          }
          this.tableData = res.result
          console.log(res)
        }
      })
    },
  }
}

</script>

<style scoped lang="less">
.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }

    .el-input-number {
      width: 200px;
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.label-style{
  text-align: center;//文本居中
}
.content-style{
  text-align: center;//文本居中

}
</style>