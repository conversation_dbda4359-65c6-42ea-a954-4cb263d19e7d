<template>
  <div class="search-container">
    <div class="operate-pannel">
      <div class="search-box">

        <span class="font-size14">项目名称：</span>
        <el-input v-model="queryParams.projectName" clearable placeholder="请输入项目名称"
                  @change="handleChange"></el-input>
        <span class="font-size14">用户编号：</span>
        <el-input v-model="queryParams.userNum" clearable placeholder="请输入用户编号"
                  @change="handleChange"></el-input>
        <span class="font-size14">用户名称：</span>
        <el-input v-model="queryParams.userName" clearable placeholder="请输入用户名称"
                  @change="handleChange"></el-input>
        <span class="font-size14">创建日期：</span>
        <el-date-picker
            v-model="queryParams.createTimeArray"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="handleChange"
            style="width: 20%"
            :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="getList"
            v-has-permi="['JDWCN01XM01QX01']"
        >查询
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="resetQuery"
        >重置
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('add')"
            v-has-permi="['JDWCN01XM01QX02']"
        >新增
        </el-button>
        <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="openDialog('edit')"
            v-has-permi="['JDWCN01XM01QX03']"
        >修改
        </el-button>
        <el-button size="mini" type="text" icon="el-icon-delete" v-has-permi="['JDWCN01XM01QX04']"
                   @click="handleDelete">删除
        </el-button>
        <el-button size="mini" type="text" icon="el-icon-upload2" v-has-permi="['JDWCN01XM01QX05']"
                   @click="openDialog('upload')">导入
        </el-button>
        <!--   导出    -->
        <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="openDialog('download')"
            v-has-permi="['JDWCN01XM01QX06']"
        >导出
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <Table
          :tableData="List"
          :tableOptions="realTableOptions"
          :loading="loading"
          :queryParam="queryParams"
          @getCurrentData="select"
          @rowdblclick="openDialog('edit')"
      >
      </Table>
      <Pagination
          @handleRefresh="handleCurrentChange"
          :queryParam="queryParams"
          layout="total, prev, pager, next, jumper"
          :total="queryParams.total"
      />
    </div>

    <el-dialog
        width="30%"
        :visible="driverDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">{{ dialogTitle }}</div>
      <el-form
          ref="reqParams"
          :model="reqParams"
          :rules="reqRules"
          label-width="100px"
          v-if="dialogType === 'edit'"
          v-watermark="{label:watermark}"
      >
        <el-row>
          <el-col :span="25">
            <el-form-item label="项目名称" prop="projectName">
              <el-input size="mini" v-model="reqParams.projectName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="25">
            <el-form-item label="用户编号" class="force-width-60" prop="userNum">
              <el-input size="mini" v-model="reqParams.userNum"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="25">
            <el-form-item label="用户名称" prop="userName">
              <el-input size="mini" v-model="reqParams.userName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <el-form
          ref="reqParams"
          :model="reqParams"
          :rules="reqRules"
          label-width="100px"
          v-else
          v-watermark="{label:watermark}"
      >
        <el-row>
          <el-col :span="25">
            <el-form-item label="项目名称" prop="projectName">
              <el-input  size="mini" v-model="reqParams.projectName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="25">
            <el-form-item label="用户编号" class="force-width-60" prop="userNum">
              <el-input size="mini" v-model="reqParams.userNum"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="25">
            <el-form-item label="用户名称" class="force-width-60" prop="userName">
              <el-input size="mini" v-model="reqParams.userName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>


      </el-form>
      <div align="right">
        <el-button size="mini" type="primary" @click="submitCdzgl(dialogType)">保存</el-button>
        <el-button size="mini" @click="closeDialog">关闭</el-button>
      </div>
    </el-dialog>

    <!--  上传文件  -->
    <el-dialog
        width="420px"
        :visible="uploadDialog"
        size="tiny"
        center
        :destroy-on-close="true"
        :modal-append-to-body="false"
        @close="closeUploadDialog"
        :close-on-click-modal="false"
    >
      <div slot="title" align="left" class="dialogTitle">文件上传</div>
      <FIleUpload
          :file-size="20"
          :file-type="['xlsx']"
          upload-url="/cnsj/project/importProjectExcel"
          download-name="jsyxxImport.xlsx"
          @uploadSuccessData="uploadSuccessData"
          @uploadErrorData="uploadErrorData"
      />
    </el-dialog>

  </div>
</template>
<script>
import Table from "components/MainTable/index.vue";
import {updateRecord, addProject, listProject, delRecord, DownLoadJsyxx} from 'api/projectManage/xmgl';
import Pagination from '@/components/Pagination/ghsj'
import { getToken } from '@/utils/auth'
// 导出文件
import {downLoad} from "@/utils/tool";
// 上传文件
import FIleUpload from "@/components/CNSJ/ProjectFIleUpload.vue";
export default {
  name: 'xmgl',
  created () {
    this.defaultForm = JSON.parse(JSON.stringify(this.queryParams))
  },
  components: {Table, Pagination,FIleUpload},
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    }
  },
  mounted () {
    this.getList()
  },
  data () {
    return {
      // 初始打开的折叠面板
      activeNames: ['1'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
        projectName: '',
        userNum:'',
        userName:'',
        createTimeArray:[]
      },
      // 查询列表
      groupItem: [],
      groupItemAdd: [],
      // 人员列表
      List: [],
      // 遮罩层
      loading: false,
      // 拷贝初始化查询参数
      defaultForm: {},
      // 选择列表id
      selectID: 0,
      // 选择列表数据
      reqParams: {},
      // 弹出框类别
      dialogType: '',
      // 弹出框标题
      dialogTitle: '',
      // 弹出框标显示
      driverDialog: false,
      uploadDialog: false,
      // 表单校验规则
      reqRules: {
        projectName: [
          {
            required: true,
            message: '请输入项目名称',
            trigger: 'blur'
          }
        ],
        userNum: [
          {
            required: true,
            message: '请输入用户编号',
            trigger: 'blur'
          }
        ],
        userName: [
          {
            required: true,
            message: '请输入用户名称',
            trigger: 'blur'
          }
        ]
      },
      realTableOptions: [
        {label: '项目名称', prop: 'projectName'},
        {label: '用户编号', prop: 'userNum'},
        {label: '用户名称', prop: 'userName'},
        {label: '创建人', prop: 'realName'},
        {label: '创建时间', prop: 'createTime'},
      ],
      tableOptions: [],
    }
  },
  methods: {
    // 上传文件开始==========================
    closeUploadDialog () {
      this.getList()
      this.uploadDialog = false
    },
    uploadSuccessData() {
      this.$message({
        type: 'success',
        message: '上传成功'
      })
      this.closeUploadDialog()
    },
    uploadErrorData(message) {
      this.$message({
        type: 'error',
        message: '上传失败：' + message
      })
    },
    // =======================================上传文件结束
    /** queryParam事件 */
    handleChange () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 人员查询 */
    getList () {
      this.loading = true
      listProject(this.queryParams).then((res) => {
        this.List = res.result.records
        this.queryParams.total = res.result.total
        this.selectID = 0;
      }).finally(() => {
        this.loading = false
      })
    },
    /** 分页查询 */
    handleCurrentChange (val) {
      // debugger
      this.queryParams = val
      this.getList()
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams = JSON.parse(JSON.stringify(this.defaultForm))
      this.getList()
    },
    /** 单击表事件 */
    select (row) {
      this.selectID = row.Id
      this.reqParams = row
    },
    /** 打开新增弹窗 */
    openDialog (type) {
      switch (type) {
        case 'add':
          this.dialogType = 'add'
          this.dialogTitle = '新增项目'
          this.selectID = 0
          this.reqParams = {}
          this.driverDialog = true
          break
        case 'edit':
          // if (this.$store.getters.permissions.indexOf('NDWCC01SJ01QX02') == -1) {
          //   this.$message.error('该用户无修改权限！')
          //   return
          // }
          if (this.selectID === 0) {
            this.$message.error('请选择行！')
          } else {
            this.dialogTitle = '编辑项目'
            this.dialogType = 'edit'
            this.driverDialog = true
          }
          break
        case 'upload':
          this.uploadDialog = true
          break
        case 'download':
          DownLoadJsyxx(this.queryParams).then((res) => {
            const fileName =  '项目信息' + '.xlsx'
            downLoad(res, fileName)
          })
          break
      }
    },
    /** 关闭弹窗 */
    closeDialog () {
      this.getList()
      this.driverDialog = false
    },
    //删除
    handleDelete() {
      if (this.selectID == undefined || this.selectID === null) {
        this.$message({
          type: 'warning',
          message: '请选择数据！'
        })
      } else {
        this.$confirm('删除该<' + this.reqParams.projectName + '>项目, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delRecord(this.selectID).then(res => {
            if (res.code === 200) {
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.getList();
            } else {
              this.$message({
                message: '删除失败;' + res.getMessage(),
                type: 'warning'
              });
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
      }
    },
    /** 提交新增 */
    submitCdzgl (type) {
      switch (type) {
        case 'add':
          this.$refs.reqParams.validate((valid) => {
            if (valid) {
              addProject(this.reqParams).then(res => {

                if (res.code == 200){
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getList()
                  this.closeDialog()
                }else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
        case 'edit':
          this.$refs.reqParams.validate((valid) => {
            if (valid) {
              updateRecord(this.reqParams).then(res => {
                if (res.code ==200){
                  this.$message({
                    type: 'success',
                    message: '操作成功'
                  })
                  this.getList()
                  this.closeDialog()
                }else {
                  this.$message({
                    type: 'error',
                    message: res.message
                  })
                }
              })
            } else {
              return false
            }
          })
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.search-container {
  width: 100%;
  height: 100%;
  .operate-pannel{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor{
      margin-right: 10px;
    }
    .el-input {
      width: 200px;
      margin-right: 10px;
    }
    .el-select{
      margin-right: 10px;
    }
  }
  .table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

::v-deep .el-dialog__body {
  height: calc(100% - 54px);

  .el-tabs {
    height: calc(100% - 30px);
    // overflow: auto;

    .el-tabs__content {
      //39px el-tabs header高度
      height: calc(100% - 39px);
      overflow: auto;

      .el-tab-pane {
        height: 100%;
      }
    }
  }

  .btn-box {
    margin-top: 10px;
    text-align: center;
  }
}

.el-row {
  margin-bottom: 20px;
}
.text {
  font-size: 14px;
}

.item {
  padding: 0;
}
</style>