import request from '@/utils/request'

// 查询字典数据列表
export function listData(query) {
    return request({
        url: '/dwCnsj/cnsj/ghsj/GetGhsjList',
        method: 'post',
        data: query
    })
}

// 新增字典数据
export function addData(data) {
    return request({
        url: '/dwCnsj/dfdw/dictData/create',
        method: 'post',
        data: data
    })
}

// 修改字典数据
export function updateData(data) {
    return request({
        url: '/dwCnsj/dfdw/dictData/update',
        method: 'put',
        data: data
    })
}

// 删除字典数据
export function delData(dictId) {
    return request({
        url: '/dwCnsj/dfdw/dictData/delete?id=' + dictId,
        method: 'delete'
    })
}

