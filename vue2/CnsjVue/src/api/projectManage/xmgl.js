import request from '@/utils/request'

// 查询项目数据列表
export function listProject(query) {
    return request({
        url: '/dwCnsj/cnsj/project/GetProjectList',
        method: 'post',
        data: query
    })
}

// 新增项目
export function addProject(data) {
    return request({
        url: '/dwCnsj/cnsj/project/create',
        method: 'post',
        data: data
    })
}

// 修改记录
export function updateRecord(data) {
    return request({
        url: '/dwCnsj/cnsj/project/update',
        method: 'put',
        data: data
    })
}


// 删除功耗数据
export function delRecord(dictId) {
    return request({
        url: '/dwCnsj/cnsj/project/delete?id=' + dictId,
        method: 'delete'
    })
}

/** 驾驶员信息导出 */
export function DownLoadJsyxx (params) {
    return request({
        url: '/dwCnsj/cnsj/project/DownLoadProject',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}
