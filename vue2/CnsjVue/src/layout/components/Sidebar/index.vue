<template>
  <div>
    <logo :collapse="false"/>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
          :default-active="$route.path"
          router
          :unique-opened="true"
          class="el-menu-vertical-demo"
          :active-Title-color="theme">
        <component
            v-for="route in menuList"
            :key="route.Id"
            :index="route.Title"
            :is="(route.childList && route.childList.length > 0) ? 'el-submenu' : 'el-menu-item'">
          <template slot="title">
            <i class="iconfont font-size14" style="margin-right:10px">&#xe790;</i>
            <span slot="title" class="font-size14">{{route.Title}}</span>
          </template>
          <template v-if="route.childList && route.childList.length > 0">
            <el-menu-item
                v-for="routeChild in route.childList"
                :key="routeChild.Id"
                :index="routeChild.Url"
                :route="{path: routeChild.Url}"
                :disabled="!routeChild.HasPermi">
              <span slot="title">{{routeChild.Title}}</span>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import Logo from "./Logo";
import {MLogin} from "api/login";
import {getModule} from "api/common";
import store from '@/store'

export default {
  components: {Logo},
  data() {
    return {
      urlList: [],
      menuList: []
    }
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme
    }
  },
  created() {
    MLogin().then(res => {
      if (res.success) {
        this.getMenuList()
      } else {
        this.$router.push('/login')
      }
    })
  },
  methods: {
    async getMenuList() {
      await getModule().then(res => {
        const functionList = res.data || []
        this.urlList = []
        functionList.forEach(item => {
          this.urlList.push(...item.childList.map((value) => {
            return value.Url
          }))
        })
        const permissions = store.getters && store.getters.permissions
        let firstUrl = ''
        this.menuList = [
          {
            Id: 'JDWCN01',
            Title: '储能设计',
            childList: [
              {
                Id: "JDWCN01XM01",
                Title: "项目管理",
                Url: "/cnsj/xmgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/cnsj/xmgl') > -1
              },
              {
                Id: "JDWCN01GH01",
                Title: "功耗数据",
                Url: "/cnsj/ghsj",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/cnsj/ghsj') > -1
              },
              {
                Id: "JDWCN01GH02",
                Title: "功耗总结",
                Url: "/cnsj/ghzj",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/cnsj/ghsj') > -1
              },
              {
                Id: "JDWCN01QX01",
                Title: "月最大负荷曲线",
                Url: "/cnsj/yzdfhqx",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/cnsj/yzdfhqx') > -1
              },
              {
                Id: "JDWCN01QX02",
                Title: "典型日负荷曲线",
                Url: "/cnsj/dxrfhqx",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/cnsj/dxrfhqx') > -1
              },
              {
                Id: "JDWCN01QX03",
                Title: "储能测算曲线",
                Url: "/cnsj/cncsqx",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: this.urlList.indexOf('/cnsj/cncsqx') > -1
              },
              {
                Id: "JDWGB01ZD01",
                Title: "字典管理",
                Url: "/cnsj/zdgl",
                Parameter: 0,
                leaf: true,
                DisplayName: "",
                HasPermi: permissions.some(permission => {
                  return 'JDWGB01ZD01QX01' === permission
                })
              },
            ]
          }
        ]
        this.menuList.forEach((value) => {
          if (firstUrl != '') {
            return
          }
          value.childList.forEach((param) => {
            if (firstUrl != '') {
              return
            }
            if (param.HasPermi) {
              firstUrl = param.Url
            }
          })
        })
        //TODO 获取后台模块列表 默认跳转到第一个
        if (this.$router.currentRoute.fullPath !== firstUrl) {
          this.$router.push(firstUrl)
        }
        this.$forceUpdate()
      })
    }
  }
};
</script>
