<template>

  <div class="search-container">
    <div class="table-box"  style="height: 60vh">
      <el-table
          :border="true"
          ref="eltab_List"
          :data="personList"
          stripe
          border
          highlight-current-row
          v-loading="expenseSubsidyLimitLogLoading"
          :tableOptions="personRealTableOptions"
          style="width: 100%"
          height="90%"
          v-watermark="{label:watermark}"
      >
        <el-table-column type="index" label="序号" width="50" align="center">
          <template slot-scope="scope">
            {{
              (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
            }}
          </template>
        </el-table-column>



        <template v-for="(item, index) in personRealTableOptions">
          <el-table-column
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :align="item.align || 'center'"
              :width="item.width || ''"
              :show-overflow-tooltip="item.tooltip || false"
              :sortable="item.sortable === false ? false : true">
            <template slot-scope="scope">
              <slot
                  v-if="item.slot"
                  :name="scope.column.property"
                  :row="scope.row"
                  :rowIndex="scope.$index">
              </slot>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>

        <el-table-column prop="clockImgFileUrl" label="图片" width="width" align="center">
          <template slot scope="{row,$index}">
            <el-image :src="row.clockImgFileUrl" :preview-src-list="[row.clockImgFileUrl]" alt="" style="height: 100px" />
          </template>
        </el-table-column>

      </el-table>

<!--      <Pagination-->
<!--          @handleRefresh="handleCurrentChange"-->
<!--          :queryParam="queryParams"-->
<!--          layout="total, prev, pager, next, jumper"-->
<!--          :total="queryParams.total"-->
<!--      />-->
    </div>
  </div>




</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

import {GetPersonLimitLog} from "api/xcgl/rygl";
import {getInfoByLcId} from "api/xcgl/xchz";

export default {
  name: 'xchzDetail',
  components: {Table, Pagination, Dropdown},
  props:{
    lcId:{
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      data: [],
      dtDialog: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        status: '',
        type: ''
      },
      flag: false,
      clbgParams: {},
      dialogTableVisible: false,
      dialogTableVisibleUpd: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,

      },
      //计划名称、经纬度、里程、费用、到达时间、实际打卡地址、图片
      personRealTableOptions: [
        {label: '计划名称', prop: 'planName'},
        {label: '经纬度', prop: 'longitudeLatitude'},
        {label: '里程', prop: 'mileage'},
        {label: '费用', prop: 'cost'},
        {label: '到达时间', prop: 'arrivedTime'},
        {label: '实际打卡地址', prop: 'arrivedLocationName'}
        //{label: '图片', prop: 'clockImgFileUrl'}
      ],
      expenseSubsidyLimitLogLoading: false,
      personList: [],

      srcPersonId: 0,
    };
  },
  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.personRealTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1

      this.getInfoByLcId()
    },




    getInfoByLcId() {
      this.expenseSubsidyLimitLogLoading = true
      getInfoByLcId(this.lcId).then((res) => {
        this.personList = res.result

      }).finally(() => {
        this.expenseSubsidyLimitLogLoading = false
      })
    },
    /** 重置查询 */
    resetQuery (val) {
      console.log(this.lcId)
      //暂时写死
      // this.lcId = 842061
      //this.queryParams.srcPersonId = this.personId
      this.getInfoByLcId()
    },
  },





  created() {
    this.handleChange();
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    },
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 80%;
  width: 80%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
