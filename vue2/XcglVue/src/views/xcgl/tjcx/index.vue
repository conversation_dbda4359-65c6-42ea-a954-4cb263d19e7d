<template>
	<el-container>
	    <el-header style="height: 20px">
			<div>
				<span  class="font-size14" >统计类型：</span>
				  <el-select v-model="statisticsType" placeholder="请选择">
					<el-option
					  v-for="item in options"
					  :key="item.value"
					  :label="item.label"
					  :value="item.value"
					  @click.native ="typeChange">
					</el-option>
				  </el-select>
					<span  class="font-size14" style="margin-left: 15px;">部门：</span>
				<!--  <el-cascader :options="deptOptions" clearable  :show-all-levels="false" v-model="deptName"   :props="{ checkStrictly: true }"  @change="handleChange"></el-cascader> -->
				 <treeSelect
				     @input="deptClick"
					 style="width: 20%"
					 :placeholder="'选择部门'"
					 :multiple="false"
					 v-model="queryParams.deptId"
					 filterable
					 :data="deptOptions"
				 />
				  <span  class="font-size14" style="margin-left: 15px;" >时间：</span>
				  <el-date-picker
					v-model="searchTime"
					type="daterange"
					range-separator="至"
					start-placeholder="开始日期"
					value-format="yyyy-MM-dd"
					end-placeholder="结束日期">
				  </el-date-picker>
				  <el-button
					v-if="queryShow"
				      class="rygf"
				      size="mini"
				      type="text"
				      icon="el-icon-search"
				      @click="search"
				  >查询
				  </el-button>
				  <el-button
					v-if="reflashShow"
				      class="rygf"
				      size="mini"
				      type="text"
				      icon="el-icon-refresh"
				      @click="reflash"
				  >重置
				  </el-button>
				  <el-button
					v-if="exportShow"
				      size="mini"
				      type="text"
				      class="rygf"
				      icon="el-icon-download"
				      @click="exportData"
				  >导出
				  </el-button>
				  <Dropdown v-show="statisticsType==1"  :columnArr="tableOptions" @getNewArr="getNewArr"/>
				    <Dropdown v-show="statisticsType==2"  :columnArr="tableOptions2" @getNewArr="getNewArr"/>
					  <Dropdown v-show="statisticsType==3"  :columnArr="tableOptions3" @getNewArr="getNewArr"/>

			</div>
		</el-header>
	    <el-main style="height: 100vh;overflow: hidden; padding: 30px">
		<!-- 	<div v-if="this.statisticsType==1"  class="table-box"> -->
	<!-- 			<el-table
					:data="tableData"
					height="650"
					border
					style="width: 100%">
					<el-table-column
					      type="index"
					      width="50">
					    </el-table-column>
					<el-table-column
					  prop="deptName"
					  label="部门"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="performMileage"
					  label="里程数(公里)"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="performCost"
					  label="补贴费用(元)">
					</el-table-column>
				</el-table> -->
				<Table
				     :tableData="tableData"
				     :tableOptions="realTableOptions"
				     :loading="false"
				 >
				   <template slot-scope="scope">
				     {{
				       (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
				     }}
				   </template>
				 </Table>
			<!-- </div> -->
		<!-- 	<div v-if="this.statisticsType==2">
				<el-table
					:data="tableData"
					height="650"
					border
					style="width: 100%">
					<el-table-column
					     type="index"
					     width="50">
					   </el-table-column>
					<el-table-column
					  prop="deptName"
					  label="部门"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="userName"
					  label="姓名"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="performMileage"
					  label="里程数(公里)"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="performCost"
					  label="补贴费用(元)">
					</el-table-column>
				</el-table>
			</div>
			<div v-if="this.statisticsType==3">
				<el-table
					:data="tableData"
					height="650"
					border
					style="width: 100%">
					<el-table-column
					     type="index"
					     width="50">
					   </el-table-column>
					<el-table-column
					  prop="deptName"
					  label="部门"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="usersName"
					  label="姓名"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="alarmTime"
					  label="告警时间"
					  width="180">
					</el-table-column>
					<el-table-column
					  prop="alarmTypeString"
					  label="告警类型">
					</el-table-column>
					<el-table-column
					  prop="alarmNote"
					  label="告警内容">
					</el-table-column>
				</el-table>
			</div> -->
		  <div class="block">

			<el-pagination
			  @size-change="handleSizeChange"
			  @current-change="handleCurrentChange"
			  :current-page="currentPage"
			  :page-sizes="[5, 10, 20, 30]"
			  :page-size="10"
			  layout="total, sizes, prev, pager, next, jumper"
			  :total="this.total">
			</el-pagination>
		  </div>
		</el-main>
	</el-container>

</template>

<script>
	import Table from '@/components/MainTable'
	import Pagination from '@/components/Pagination'
	import Dropdown from '@/components/ColumnDropdown'
	import { getStatisticsInfo, exportData} from '@/api/xcgl/tjcx'
	import {getDeptList } from '@/api/xcgl/zzjg'
	import {downLoad, getFileNameTime} from "@/utils/tool";
	import TreeSelect from "components/TreeSelect/treeSelect.vue";
	  export default {
		  components: { TreeSelect,Table, Pagination, Dropdown},
	    data() {
	      return {
			  queryParams: {
			    deptId: 0,
			  },
			    queryShow : false,
			    reflashShow : false,
			    exportShow : false,
			   realTableOptions:[],
			 tableOptions: [
			   {label: '部门', prop: 'deptName'},
			   {label: '里程数(公里)', prop: 'performMileage'},
			   {label: '补贴费用(元)', prop: 'performCost'}
			 ],
			 tableOptions2: [
			   {label: '部门', prop: 'deptName'},
			    {label: '姓名', prop: 'userName'},
			   {label: '里程数(公里)', prop: 'performMileage'},
			   {label: '补贴费用(元)', prop: 'performCost'}
			 ],
			 tableOptions3: [
			   {label: '部门', prop: 'deptName'},
			   {label: '姓名', prop: 'usersName'},
			   {label: '告警时间', prop: 'alarmTime'},
			   {label: '告警类型', prop: 'alarmTypeString'},
			   {label: '告警内容', prop: 'alarmNote'},
			 ],
			currentPage:1,
			pageSize: 10,
			total: 0,
	        deptOptions: [],
	        statisticsType: '',
		    searchTime: '',
			tableData: [],
			options: [{
          value:  1,
          label: '部门'
        }, {
          value:  2,
          label: '人员'
        }, {
          value:  3 ,
          label: '告警'
        }],
			deptName: [],
	      }
	    }
	  ,
	  methods: {
		  deptClick(){
			   this.search()
		  },
		  getNewArr(newTableOptions) {
			this.realTableOptions = [...newTableOptions]
		  },
		typeChange(val){
		console.log('来了')
		this.currentPage=1;
		this.tableData=null;
		console.log(val)
		if(this.statisticsType==1){
			this.realTableOptions=this.tableOptions
		}
		if(this.statisticsType==2){
			this.realTableOptions=this.tableOptions2
		}
		if(this.statisticsType==3){
			this.realTableOptions=this.tableOptions3
		}
		},
		handleSizeChange(val) {
			console.log('val1'+val)
		 this.pageSize=val;
		},
		handleCurrentChange(val) {
		 this.currentPage=val;
		 this.search()
		},
		reflash(){
			this.deptName=null;
			this.statisticsType=null;
			this.searchTime=null;
      this.queryParams.deptId = this.deptOptions[0].id
		},
		exportData()
		{
			let params={"statisticsType":this.statisticsType,"deptId":this.deptName[this.deptName.length-1],"searchTime":this.searchTime,
			"currentPage":this.currentPage,"pageSize":this.pageSize}

		exportData(params).then((res)=>{
			let typeNameExcel='0';
					if(this.statisticsType==1){
						typeNameExcel='监理站'
					}
					else if(this.statisticsType==2){
						typeNameExcel='人员'
					}
					else if(this.statisticsType==3){
						typeNameExcel='告警'
					}
					let fileName = '统计查询' +typeNameExcel+ getFileNameTime() + '.xlsx';
					downLoad(res,fileName)
	})
		},
		 getDeptList(){
		 	getDeptList().then((res) => {
		 	  let a = [];
		 	  a.push(res.result)
		      this.deptOptions = a
        //默认选中顶级节点
        this.queryParams.deptId = this.deptOptions[0].id
		 	console.log('dpe'+JSON.stringify(this.deptOptions))
		 	}).finally(() => {
		 	})
		 },
			getTreeData(data) {
			 // 循环遍历json数据
			 for (var i = 0; i < data.length; i++) {
				 if (data[i].children.length < 1) {
					 // children若为空数组，则将children设为undefined
					 data[i].children = undefined;
				 } else {
					 // children若不为空数组，则继续 递归调用 本方法
					 this.getTreeData(data[i].children);
				 }
			 }
			 return data;
			 },
			 handleChange(value){
				    console.log(JSON.stringify(value));
				 this.deptName=value
			 },
			 search(){
				let params={"statisticsType":this.statisticsType,"deptId":this.queryParams.deptId,"searchTime":this.searchTime,
				"currentPage":this.currentPage,"pageSize":this.pageSize}
				console.log('didididid:'+JSON.stringify(params))
				 getStatisticsInfo(params).then((res)=>{
					 console.log(JSON.stringify(res))
					 this.tableData=res.result
					 let msg=res.message.split(",")
					  console.log(res.msg)
					 this.total=Number(msg[0])
					 this.page
				 })
			 }

	  },
	  created(){
		      let permissions =  this.$store.getters.permissions
		      if (permissions.includes('913101')){
		        this.queryShow = true
		      }
		      if (permissions.includes('913102')){
		        this.reflashShow = true
		      }
		      if (permissions.includes('913103')){
		        this.exportShow = true
		      }
		this.getDeptList()
	  }
	  }
</script>

<style lang="less" scoped>
	.list-box {
	  height: 100%;
	  box-shadow: 0 0 6px #d9e2f1aa;
	  border-radius: 10px;
	  padding: 10px;
	  display: flex;
	  flex-direction: row;
	  transition: all .5s;


	  .operate-pannel {
	    height: 100%;
	    width: 100%;

	    justify-content: space-between;
	    align-items: center;
	    overflow: auto;
	    background: #FFFFFF;
		}
	}

	.table-box {
    height: calc(100% - 80px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
	.el-main {
	    padding: 0;
	    height: 100vh ;
		overflow:hidden

	}

  }
</style>

