<template>

  <div class="main" ref="main">


    <div class="list-box" style="width: 20%">
      <div class="operate-pannel">
        <div v-loading="treeLoading">
          <el-tree :data="data"
                   ref="tree"
                   :props="defaultProps"
                   node-key="id"
                   @node-click="handleNodeClick"
                   :expand-on-click-node="false"
                   :default-expanded-keys="[1]"
                   :highlight-current="true"
          />
        </div>

      </div>
    </div>
    <div class="process-info" style="width: 79%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <span class="font-size14">姓名：</span>
            <el-input v-model="queryParams.userName"></el-input>
            <span class="font-size14">日期：</span>
            <el-date-picker
                v-model="queryParams.time"
                :clearable="false"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleChange"
                style="width: 40%">
            </el-date-picker>
            <el-button
                v-if="queryShow"
                size="mini"
                type="text"
                icon="el-icon-search"
                @click="getXcdkzphzList()"
            >查询
            </el-button>
            <el-button
                v-if="resetShow"
                class="rygf"
                size="mini"
                type="text"
                icon="el-icon-refresh"
                @click="resetQueryParam()"
            >重置
            </el-button>
            <el-button
                v-if="exportShow"
                size="mini"
                type="text"
                class="rygf"
                icon="el-icon-download"
                @click="downLoadZip()"
            >导出
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>

          </div>
        </div>
        <div class="table-box">
          <Table
              :tableData="xchzList"
              :tableOptions="realTableOptions"
              :loading="xcglLoading"
              @getCurrentData="select"
              :show-overflow-tooltip="true"
              :cell-style-val="2"
              height="250px"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
            <template slot="clockImgFileUrl" scope="scope">
              <el-image v-if="scope.row.clockImgId" :src="scope.row.clockImgFileUrl" :preview-src-list="[scope.row.clockImgFileUrl]" alt="" style="height: 100px" />
              <span v-else>暂无照片</span>
            </template>
          </Table>

          <Pagination
              @handleRefresh="handleCurrentChange"
              :queryParam="queryParams"
              layout="total, prev, pager, next, jumper"
              :total="queryParams.total"
          />
        </div>
      </div>


    </div>

  </div>

</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {downloadImgZip, getDeptList} from '@/api/xcgl/xchz'
import SplitPane from "@/components/split-pane";
import {downLoad, getFileNameTime} from "@/utils/tool";
import {GetXcdkzphzList} from "api/xcgl/xchz";
export default {
  name: 'index',
  components: { SplitPane, Table, Pagination, Dropdown},
  data() {
    return {
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      flag: false,
      // 拷贝初始化查询参数
      deptId: null,
      queryShow:false,
      resetShow:false,
      exportShow:false,
      clbgParams:{},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        time: [],
        planStartDate: null,
        planEndDate: null,
      },
      realTableOptions: [],
      tableOptions: [
        {label: '姓名', prop: 'userName',tooltip:true},
        {label: '打卡时间', prop: 'arrivedTime',tooltip:true},
        {label: '打卡地址', prop: 'arrivedLocationName',tooltip:true},
        {label: '打卡照片', prop: 'clockImgFileUrl',tooltip:true,slot:true},
      ],
      xcglLoading: false,
      treeLoading:true,
      xchzList: [],
      dialogTableVisible: false,
    };
  },

  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      //this.queryParams.deptId = 1
      this.queryParams.pageNum = 1
      this.getXcdkzphzList()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams = val
      this.getXcdkzphzList()
    },
    /**
     * 下载 照片
     */
    downLoadZip(){
      downloadImgZip(this.queryParams).then(res=>{
        let fileName =getFileNameTime() + '.zip'
        downLoad(res, fileName)
      })
    },
    //获取打卡照片汇总list
    getXcdkzphzList() {
      if(!this.queryParams.deptId){
        this.$message({
          showClose: true,
          message: "请先点击选择左侧部门!",
          type: 'error',
          offset:400
        });
        return
      }


      if (this.queryParams.time == null) {
        this.queryParams.planStartDate = null
        this.queryParams.planEndDate = null
      } else {
        this.queryParams.planStartDate = this.queryParams.time[0]
        this.queryParams.planEndDate = this.queryParams.time[1]
      }
      this.xcglLoading = true
      GetXcdkzphzList(this.queryParams).then((res) => {
        if (res.success == true){
          this.xchzList = res.result.records
          this.queryParams.total = res.result.total
        }else {
          this.$message({
            showClose: true,
            message: res.message,
            type: 'error',
            offset:400
          });
        }
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    //部门点击
    handleNodeClick(data, checked) {
      if (checked) {
        this.$refs.tree.setCheckedNodes([data])
        this.deptId = this.$refs.tree.getCheckedKeys()[0];
        // this.queryParams.deptId = this.$refs.tree.getCheckedKeys()[0];
        this.queryParams.deptId = data.id;
        this.deptId = data.id;
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10
        this.getXcdkzphzList();
      }
    },

    /** 单击表事件 */
    select (row) {
      this.selectID = row.lcId
      this.clbgParams = row
    },
    resetQueryParam(){
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        time: this.queryParams.time,
        planStartDate: this.queryParams.planStartDate,
        planEndDate: this.queryParams.planEndDate,
        deptId:this.deptId
      }

      this.getXcdkzphzList()
    },

    getDeptList(){
      getDeptList().then((res) => {
        let a = [];
        a.push(res.result)
        this.data = a
      }).finally(() => {
        this.treeLoading = false
      })
    },
    setQueryDate(){
      const now = this.getDateNow()
      this.queryParams.time = [now,now]
      this.queryParams.planStartDate = now
      this.queryParams.planEndDate = now
    },
    getDateNow(){
      let now = new Date()
      let year = now.getFullYear()
      // let month = now.getMonth() +1
      // let day = now.getDate()
      let month = (now.getMonth() + 1).toString().padStart(2, '0'); // 确保月份是两位数
      let day = now.getDate().toString().padStart(2, '0'); // 确保日期是两位数
      return year + '-' + month + '-' + day
    }

  },

  created() {
    this.setQueryDate()
    let permissions =  this.$store.getters.permissions
    if (permissions.includes('913502')){
      this.queryShow = true
    }
    if (permissions.includes('913503')){
      this.resetShow = true
    }
    if (permissions.includes('913501')){
      this.exportShow = true
    }
    this.getDeptList()
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    },
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}
.abow_dialog {
  display: flex;
  justify-content: center;
  align-items: Center;
  overflow: hidden;
  .el-dialog {
    margin: 0 auto !important;
    height: 90%;
    overflow: hidden;
    .el-dialog__body {
      position: absolute;
      left: 0;
      top: 54px;
      bottom: 0;
      right: 0;
      padding: 0;
      z-index: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }
}

</style>
