<template>

  <div class="main" ref="main">


    <div class="list-box" style="width: 20%">
      <div class="operate-pannel">
        <div>
          <el-tree :data="data"
                   ref="tree"
                   :props="defaultProps"
                   node-key="id"
                   @node-click="handleNodeClick"
                   :expand-on-click-node="false"
                   :default-expanded-keys="[1]"
                   :highlight-current="true"
          />
        </div>

      </div>
    </div>
    <div class="process-info" style="width: 79%;height: 100%">
      <div class="search-container">
        <div class="operate-pannel">
          <div class="search-box">
            <el-button
			v-if="addShow"
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="add()"
            >新增
            </el-button>

            <el-button
			v-if="updateShow"
                size="mini"
                type="text"
                icon="el-icon-edit"
				@click="upd()"
            >修改
            </el-button>

            <el-button
			v-if="deleteShow"
                size="mini"
                type="text"
                icon="el-icon-delete"
				@click="delDept()"
            >删除
            </el-button>
            <el-button
			v-if="enableShow"
                size="mini"
                type="text"
                icon="el-icon-check"
				@click="enable()"
            >启用
            </el-button>
            <el-button
			v-if="disableShow"
                size="mini"
                type="text"
                icon="el-icon-close"
				@click="disable()"
            >停用
            </el-button>
            <Dropdown :columnArr="tableOptions" @getNewArr="getNewArr"/>
          </div>
        </div>
       <div class="table-box">
          <Table
              :tableData="xcglList"
              :tableOptions="realTableOptions"
              :loading="xcglLoading"
              @getCurrentData="select"
          >
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
          </Table>
          <el-pagination
              @current-change="handleCurrentChange"
              :current-page="queryParams.pageNum"
              :page-size="queryParams.pageSize"
              layout="total, prev, pager, next, jumper"
              :total="queryParams.total"
          />
        </div>
      </div>

      <el-dialog title="新增组织架构" :visible.sync="dialogTableVisible">
        <el-form :label-position="labelPosition" label-width="80px" :model="formLabelAlign" :rules="Rules" ref="addForm">
          <el-form-item label="名称"  prop="name">
            <el-input v-model="formLabelAlign.name"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="formLabelAlign.status" placeholder="请选择状态">
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="GPS" prop="unitGPS">
            <el-input size="mini" v-model="clbgParams.unitGPS" id="clbgd.gps" disabled></el-input>
            <el-button size="mini" @click="dtDialog = true">从地图中选择</el-button>
          </el-form-item>
		  <el-form-item label="排序" prop="sort">
		    <el-input v-model="formLabelAlign.sort"></el-input>
		  </el-form-item>
          <el-form-item size="large">
            <el-button type="primary" @click="onSubmit('addForm')">确定</el-button>
            <el-button @click="cancel">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>


	  <el-dialog title="修改组织架构" :visible.sync="dialogTableVisibleUpd">
	    <el-form :label-position="labelPosition" label-width="80px" :model="formLabelAlign">
	      <el-form-item label="名称">
	        <el-input v-model="formLabelAlign.name"></el-input>
	      </el-form-item>
	      <el-form-item label="状态">
	        <el-select v-model="formLabelAlign.status" placeholder="请选择状态">
	          <el-option label="启用" value="1"></el-option>
	          <el-option label="停用" value="0"></el-option>
	        </el-select>
	      </el-form-item>
	      <el-form-item label="GPS" prop="unitGPS">
	        <el-input size="mini" v-model="clbgParams.unitGPS" id="clbgd.gps" disabled></el-input>
	        <el-button size="mini" @click="dtDialog = true">从地图中选择</el-button>
	      </el-form-item>
		  <el-form-item label="排序">
		    <el-input v-model="formLabelAlign.sort"></el-input>
		  </el-form-item>
	      <el-form-item size="large">
	        <el-button type="primary" @click="updDept">确定</el-button>
	        <el-button @click="cancelUpd">取消</el-button>
	      </el-form-item>
	    </el-form>
	  </el-dialog>


      <el-dialog
          width="80%"
          :visible.sync="dtDialog"
          size="tiny"
          center
          :destroy-on-close="true"
          :append-to-body="true"
          :modal-append-to-body="false"
          @close="closeDTDialog"
          :close-on-click-modal="false"
      >
        <div style="width: 100%;height: 100%">
          <iframe :src="src" id="qt" scrolling="no" frameborder="0"
                  style="width: 100%;height: 500px"></iframe>
          <el-button @click="closeDTDialog" id="clbgd.dtDialogClose" v-show="false"></el-button>
        </div>
      </el-dialog>
    </div>

  </div>

</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'
import {getDeptList, addDept, getDepartmentList,delDept,enable,disable,updDept} from '@/api/xcgl/zzjg'
import SplitPane from "@/components/split-pane";

export default {
  name: 'index',
  components: {SplitPane, Table, Pagination, Dropdown},
  data() {
    return {
		Rules:{
			name:[{   required: true, message: '请输入部门名称', trigger: 'blur'}],
			status:[	{ required: true, message: '请选择状态', trigger: 'change' }],
			sort:[{   required: true, message: '请输入部门排序', trigger: 'blur'}],

		},
		addShow: false,
		updateShow: false,
		deleteShow: false,
		enableShow: false,
		disableShow: false,
      data: [],
      src: process.env.NODE_ENV === 'development' ? '/static/xcgl/html/BaiDuToBGD.html' : 'static/xcgl/html/BaiDuToBGD.html',
      dtDialog: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        status: '',
        type: '',
		sort: null,
      },
      flag: false,
      clbgParams: {},
      dialogTableVisible: false,
	  dialogTableVisibleUpd: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      realTableOptions: [],
      tableOptions: [
        {label: '名称', prop: 'deptname'},
        {label: 'GPS', prop: 'GPS'},
        {label: '状态', prop: 'stateTxt'},
		{label:'排序',prop:'sort'},
      ],
      xcglLoading: false,
      xcglList: []
    };
  },
  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.realTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.queryParams.id = 1
      this.getDepartmentList()
    },
    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getDepartmentList()
    },
    getDepartmentList() {
      this.xcglLoading = true
      getDepartmentList(this.queryParams).then((res) => {
        this.xcglList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.xcglLoading = false
      })
    },

    //部门点击
    handleNodeClick(data, checked) {
      if (checked) {
		  console.log('data:'+JSON.stringify(data))
        this.$refs.tree.setCheckedNodes([data])
        this.selectNode = data;
		console.log('tree:'+this.$refs.tree.getCheckedKeys())
        this.queryParams.id = data.id;
        this.queryParams.pageNum = 1;
        this.queryParams.pageSize = 10
        console.log("xxxxxxxxx: " + this.queryParams.id)
        this.getDepartmentList();
      }
    },

    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.clbgParams = row
	  console.log('row:'+JSON.stringify(row))
    },
    add() {
      let checkId = this.$refs.tree.getCheckedKeys()[0];
      if (checkId == null || checkId.length == 0) {
        this.$alert('请选择组织架构', {
          confirmButtonText: '确定',
        });
      } else {
        this.dialogTableVisible = !this.dialogTableVisible;
      }
    },
	upd() {
	  if (this.selectID == null ) {
	    this.$alert('请选择组织架构', {
	      confirmButtonText: '确定',
	    });
	  } else {
	    this.dialogTableVisibleUpd = !this.dialogTableVisibleUpd;
	  }
	  console.log('upd'+ JSON.stringify(this.clbgParams) )
	  this.formLabelAlign.name=this.clbgParams.deptname
	  this.formLabelAlign.status=this.clbgParams.stateTxt
	  this.clbgParams.unitGPS=this.clbgParams.GPS
	},


	delDept() {
       this.$confirm('此操作将删除该部门, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
		this.delDept2()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
	 },
	delDept2() {
		let param={"id":this.selectID}
		delDept(param).then(res=>{
			if(res){
				this.$message({
				  type: 'success',
				  message: '删除成功！'
				})
			   	this.getDeptList()
				this.getDepartmentList()
			}else{

			}
		})
	 },
	 disable(){
			let param={"id":this.selectID}
			console.log('北仑人'+this.selectID)
			if(this.selectID==null){
				this.$message({
				  type: 'error',
				  message: '请选择部门！'
				})
				return;
			}
			disable(param).then(res=>{
				if(res.code==200){
					this.$message({
					  type: 'success',
					  message: '已停用！'
					})

					this.getDepartmentList()
				}else{
					this.$message({
					  type: 'error',
					  message: res.message
					})
				}
			})
	 },
	 enable() {
	 	let param={"id":this.selectID}
		if(this.selectID==null){
			this.$message({
			  type: 'error',
			  message: '请选择部门！'
			})
			return;
		}
	 	enable(param).then(res=>{
	 		if(res.code==200){
	 			this.$message({
	 			  type: 'success',
	 			  message: '启用成功！'
	 			})

				this.getDepartmentList()
	 		}else{
					this.$message({
					  type: 'error',
					  message: res.message
					})
	 		}
	 	})
	  },
    onSubmit(formName) {
	this.$refs[formName].validate((valid) => {
		  if (valid) {
			console.log(this.formLabelAlign.name, this.formLabelAlign.status, this.clbgParams.unitGPS)
					let state=null
			if(typeof(this.formLabelAlign.status)== 'number'){
				 state=this.formLabelAlign.status
			}
			if(typeof(this.formLabelAlign.status)==  'string'){
				if(this.formLabelAlign.status=='1' ||  this.formLabelAlign.status=='0'){
					 state=parseInt(this.formLabelAlign.status)
				}else{
					state= parseInt(this.formLabelAlign.status=='启用'?1:0)
				}
			}

			let data = {
			  id: this.$refs.tree.getCheckedKeys()[0].toString(),
			  name: this.formLabelAlign.name,
			  status: state,
			  GPS: this.clbgParams.unitGPS,
					sort:this.formLabelAlign.sort,
			}
			addDept(data).then(res => {
			  if (res) {
			    console.log(res)
			    if (res.code == 500) {
			      this.$message({
			        type: 'error',
			        message: '新增数据失败！'
			      })
			    } else {
			      this.$message({
			        type: 'success',
			        message: '新增数据成功！'
			      })
						this.getDeptList()
						this.getDepartmentList()
			    }
			  } else {

			  }
			})
			this.dialogTableVisible=false
			this.getDeptList()
		  } else {
			return false;
		  }
		});

    },
	updDept(){
		console.log('updddeeept')
		let deptName=this.formLabelAlign.name
		let state=null
		console.log(this.formLabelAlign.status)
		console.log(typeof(this.formLabelAlign.status) )
		console.log(typeof(1))
		if(typeof(this.formLabelAlign.status)== 'number'){
			 state=this.formLabelAlign.status
		}
		if(typeof(this.formLabelAlign.status)==  'string'){
			if(this.formLabelAlign.status=='1' ||  this.formLabelAlign.status=='0'){
				 state=parseInt(this.formLabelAlign.status)
			}else{
				state= parseInt(this.formLabelAlign.status=='启用'?1:0)
			}
		}
		let	 sort=this.formLabelAlign.sort
		let	deptGPS=this.clbgParams.GPS
		let deptNameId=this.selectID
		let params={"id":deptNameId,"state":state,"name":deptName,"gps":this.clbgParams.unitGPS,"sort":sort}
		updDept(params).then(res=>{
			if(res.code==500){
				this.$message({
				  type: 'error',
				  message: res.message
				})
			}else{
				this.$message({
				  type: 'success',
				  message: '修改数据成功！'
				})
				this.getDepartmentList()
				this.dialogTableVisibleUpd=false;
			}
		})


	},
    cancel() {
      this.dialogTableVisible = false;
    },
	cancelUpd() {
	  this.dialogTableVisibleUpd = false;
	},
    closeDTDialog() {
      this.clbgParams.unitGPS = window.parent.document.getElementById('clbgd.gps').value
      this.clbgParams.longitude = this.clbgParams.unitGPS.split(",")[0]
      this.clbgParams.latitude = this.clbgParams.unitGPS.split(",")[1]
      //this.$nextTick(() => this.$refs.stopParams.clearValidate())

      this.dtDialog = false
    },
	getDeptList(){
		getDeptList().then((res) => {
		  let a = [];
		  a.push(res.result)
		  this.data = a
		  console.log(res.result)
		}).finally(() => {
		})
	}
  },

  created() {
	      let permissions =  this.$store.getters.permissions
	      if (permissions.includes('913201')){
	        this.addShow = true
	      }
	      if (permissions.includes('913202')){
	        this.updateShow = true
	      }
	      if (permissions.includes('913203')){
	        this.deleteShow = true
	      }
		  if (permissions.includes('913204')){
		    this.enableShow = true
		  }
		  if (permissions.includes('913205')){
		    this.disableShow = true
		  }
		this.getDeptList()
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
