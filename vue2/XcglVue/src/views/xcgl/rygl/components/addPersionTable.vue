<template>

    <div class="search-container" style="height: 60vh">
      <div class="operate-pannel">
        <div class="search-box">
          <span class="font-size14">工号：</span>
          <el-input v-model="queryParams.loginName" clearable placeholder="请输入工号"
                    @change="handleChange"></el-input>
          <span class="font-size14">姓名：</span>
          <el-input v-model="queryParams.userRealName" clearable placeholder="请输入姓名"
                    @change="handleChange"></el-input>
          <el-button
              size="mini"
              type="text"
              icon="el-icon-search"
              @click="getPersonEntityList()"
          >查询
          </el-button>

          <el-button
              size="mini"
              type="text"
              icon="el-icon-refresh"
              @click="resetQuery"
          >重置
          </el-button>
          <Dropdown :columnArr="personTableOptions" @getNewArr="getNewArr"/>
        </div>
      </div>
      <div class="table-box">
        <el-table
            :border="true"
            ref="eltab_List"
            :data="personList"
            stripe
            border
            highlight-current-row
            v-loading="personLoading"
            :tableOptions="personRealTableOptions"
            tooltip-effect="dark"
            @select-all="ryglSelectAll"
            @selection-change="handleSelectionChange"
            @row-click="ryglSelect"
            style="width: 100%"
            height="90%"
            v-watermark="{label:watermark}"
        >
          <el-table-column
              type="selection"
              width="55"
              align="center">
          </el-table-column>
          <el-table-column label="序号" width="70" align="center">
            <template v-slot="scope">{{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>
          <template v-for="(item, index) in personRealTableOptions">
            <el-table-column
                :key="index"
                :prop="item.prop"
                :label="item.label"
                :align="item.align || 'center'"
                :width="item.width || ''"
                :show-overflow-tooltip="item.tooltip || false"
                :sortable="item.sortable === false ? false : true">
              <template slot-scope="scope">
                <slot
                    v-if="item.slot"
                    :name="scope.column.property"
                    :row="scope.row"
                    :rowIndex="scope.$index">
                </slot>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>

        <Pagination
            @handleRefresh="handleCurrentChange"
            :queryParam="queryParams"
            layout="total, prev, pager, next, jumper"
            :total="queryParams.total"
        />
      </div>
      <div style="text-align: right;margin-top: 3vh">
        <el-button type="primary" @click="handleSubmit('bmdwdForm')">保存</el-button>
        <el-button @click="$emit('handleClose')">关闭</el-button>
      </div>
    </div>




</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

import {GetPersonEntityList} from "api/xcgl/rygl";

export default {
  name: 'AddPersionTable',
  components: {Table, Pagination, Dropdown},
  data() {
    return {
      data: [],
      dtDialog: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        status: '',
        type: ''
      },
      flag: false,
      clbgParams: {},
      dialogTableVisible: false,
      dialogTableVisibleUpd: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      personRealTableOptions: [],
      personTableOptions: [
        {label: '工号', prop: 'loginname'},
        {label: '姓名', prop: 'realname'},
        {label: '短号', prop: 'sphone'},
        {label: '联系方式', prop: 'telephone'}
      ],
      personLoading: false,
      personList: [],
      // 选择列表id
      multipleSelection: [],
      // checkNum和isCheck是批量操作按钮和已选几条展示的依据
      checkNum: 0,
      isCheck: false,
      allCheck: false,
    };
  },
  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.personRealTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.getPersonEntityList()
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      //console.log(val)
      this.queryParams = val
      this.getPersonEntityList()
    },

    ryglSelectAll (val) {
      if (val.length === 0) {
        this.allCheck = false
      } else {
        this.allCheck = true
      }
    },
    /** 多选表事件 */
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    ryglSelect (row, event, column) {
      // checkNum和isCheck是批量操作按钮和已选几条展示的依据
      this.checkNum = 0
      this.isCheck = false
      if (event.label == '操作') {
        return
      } else {
        // 如果status为无效不让勾选
        if (row.status == '0') {
          return
        } else {
          if (this.multipleSelection.length > 0) {
            // 如果结果数组不为空，判断所选的这条是否在结果数组里
            if (JSON.stringify(this.multipleSelection).indexOf(JSON.stringify(row)) == -1) {
              this.multipleSelection.push(row)
              this.$refs.eltab_List.toggleRowSelection(row, true)
            } else {
              // 所选数据在结果数组里，那就要清除掉它，不然会造成数据重复
              this.multipleSelection.map((item, index) => {
                if (item.id == row.id) {
                  this.multipleSelection.splice(index, 1)
                  this.$refs.eltab_List.toggleRowSelection(row, false)
                }
              })
            }
          } else {
            this.multipleSelection.push(row)
            this.$refs.eltab_List.toggleRowSelection(row, true)
          }
        }
      }
      // 这里几行代码是根据结果数组是否有值判断批量操作按钮和已选几条展示是否需要解除置灰和展示
      if (this.multipleSelection.length > 0) {
        this.checkNum = this.multipleSelection.length
        this.isCheck = true
      } else {
        this.isCheck = false
      }
    },

    //提交表单数据
    handleSubmit(){
      console.log(this.multipleSelection);
      if (this.multipleSelection.length == 0){
        this.$message.error('请选择至少一行！')
      }
      this.$emit('handleSubmit', this.multipleSelection)

    },


    getPersonEntityList() {
      this.personLoading = true
      GetPersonEntityList(this.queryParams).then((res) => {
        this.personList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.personLoading = false
      })
    },
    /** 重置查询 */
    resetQuery () {
      this.queryParams.pageNum = 1;
      this.queryParams.loginName = "";
      this.queryParams.userRealName = "";
      this.getPersonEntityList()
    },

    /** 单击表事件 */
    select (row) {
      this.selectID = row.id
      this.clbgParams = row
      console.log('row:'+JSON.stringify(row))
    },

  },





  created() {
    this.handleChange();
    this.multipleSelection =[];
    //console.log(this.$store.state.user.permissions)
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    },
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 80%;
  width: 80%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
