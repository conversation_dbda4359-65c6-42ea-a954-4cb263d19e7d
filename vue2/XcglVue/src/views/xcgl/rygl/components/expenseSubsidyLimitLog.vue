<template>

    <div class="search-container">
      <div class="table-box"  style="height: 60vh">
        <el-table
            :border="true"
            ref="eltab_List"
            :data="personList"
            stripe
            border
            highlight-current-row
            v-loading="expenseSubsidyLimitLogLoading"
            :tableOptions="personRealTableOptions"


            style="width: 100%"
            height="90%"
            v-watermark="{label:watermark}"
        >
<!--          <el-table-column-->
<!--              type="selection"-->
<!--              width="55"-->
<!--              align="center">-->
<!--          </el-table-column>-->
          <el-table-column type="index" label="序号" width="50" align="center">
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>
          <template v-for="(item, index) in personRealTableOptions">
            <el-table-column
                :key="index"
                :prop="item.prop"
                :label="item.label"
                :align="item.align || 'center'"
                :width="item.width || ''"
                :show-overflow-tooltip="item.tooltip || false"
                :sortable="item.sortable === false ? false : true">
              <template slot-scope="scope">
                <slot
                    v-if="item.slot"
                    :name="scope.column.property"
                    :row="scope.row"
                    :rowIndex="scope.$index">
                </slot>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table>

        <Pagination
            @handleRefresh="handleCurrentChange"
            :queryParam="queryParams"
            layout="total, prev, pager, next, jumper"
            :total="queryParams.total"
        />
      </div>
    </div>




</template>
<script>
import {mapActions} from 'vuex'
import Table from '@/components/MainTable'
import Pagination from '@/components/Pagination'
import Dropdown from '@/components/ColumnDropdown'

import {GetPersonLimitLog} from "api/xcgl/rygl";

export default {
  name: 'expenseSubsidyLimitLog',
  components: {Table, Pagination, Dropdown},
  props:{
    personId:{//序号显示
      type: Object,
      default: 0
    },
  },
  data() {
    return {
      data: [],
      dtDialog: false,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      labelPosition: 'right',
      formLabelAlign: {
        name: '',
        status: '',
        type: ''
      },
      flag: false,
      clbgParams: {},
      dialogTableVisible: false,
      dialogTableVisibleUpd: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,

      },
      // personRealTableOptions: [],
      personRealTableOptions: [
        {label: '姓名', prop: 'realName'},
        {label: '补贴费用上限', prop: 'Limit'},
        {label: '修改日期', prop: 'CreateTime'}
      ],
      expenseSubsidyLimitLogLoading: false,
      personList: [],

      srcPersonId: 0,
    };
  },
  methods: {
    ...mapActions(['GetInfo']),
    //获取新表格列表
    getNewArr(newTableOptions) {
      this.personRealTableOptions = [...newTableOptions]
    },
    /** queryParam事件 */
    handleChange() {
      this.queryParams.pageNum = 1
      this.queryParams.srcPersonId = this.personId
      this.getPersonLimitLogList()
    },

    /** 分页查询 */
    handleCurrentChange(val) {
      this.queryParams.srcPersonId = this.personId
      //console.log(val)
      this.queryParams = val
      this.getPersonLimitLogList()
    },


    getPersonLimitLogList() {
      this.expenseSubsidyLimitLogLoading = true
      GetPersonLimitLog(this.queryParams).then((res) => {
        this.personList = res.result.records
        this.queryParams.total = res.result.total
      }).finally(() => {
        this.expenseSubsidyLimitLogLoading = false
      })
    },
    /** 重置查询 */
    resetQuery (val) {
      this.queryParams.pageNum = 1;
      this.queryParams.srcPersonId = this.personId
      this.getPersonLimitLogList()
    },
  },





  created() {
    this.handleChange();
  },
  computed: {
    watermark(){
      return this.$store.state.user.watermark
    },
  }
};
</script>
<style lang="scss" scoped>

.main {
  height: 80%;
  width: 80%;
  display: flex;
  justify-content: space-between;
}


.list-box {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  transition: all .5s;

  .operate-pannel {
    height: 100%;
    width: 100%;

    justify-content: space-between;
    align-items: center;
    overflow: auto;
    background: #FFFFFF;
  }

}

.process-info {
  height: 100%;
  box-shadow: 0 0 6px #d9e2f1aa;
  border-radius: 10px;
  padding: 10px;
  transition: all .5s;
}

.search-container {
  width: 100%;
  height: 100%;

  .operate-pannel {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;

    .el-date-editor {
      margin-right: 10px;
    }

    .el-input {
      width: 200px;
      margin-right: 10px;
    }

    .el-select {
      margin-right: 10px;
    }
  }

  .table-box {
    height: calc(100% - 100px);

    ::v-deep .el-pagination {
      margin-top: 10px;
      text-align: center;
    }
  }
}

</style>
