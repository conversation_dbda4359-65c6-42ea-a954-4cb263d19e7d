import request from '@/utils/request'

/** 每月考勤 */
export function getDeptList (params) {
    return request({
        url: '/dwXcgl/zzjg/getDeptList',
        method: 'post',
        data: params
    })
}

/**
 * 获取行程报备list
 * @param params
 * @returns {*}
 */
export function GetXcbbList(params){
    return request({
        url: '/dwXcgl/xcbb/selectPageList',
        method: 'post',
        data: params
    })
}


/** 导出 */
export function DownLoad (params) {
    return request({
        url: '/dwXcgl/xcbb/exportExcel',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

