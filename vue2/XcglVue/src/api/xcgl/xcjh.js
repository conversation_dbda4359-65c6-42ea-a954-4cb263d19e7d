import request from '@/utils/request'
import {exportExcel} from "api/xcgl/xchz";

/** 每月考勤 */
export function getDeptList (params) {
    return request({
        url: '/dwXcgl/zzjg/getDeptList',
        method: 'post',
        data: params
    })
}

/**
 * 获取行程报备list
 * @param params
 * @returns {*}
 */
export function GetXcjhList(params){
    return request({
        url: '/dwXcgl/xcjh/selectPageList',
        method: 'post',
        data: params
    })
}

/**
 * 流程日志
 * @param params
 * @returns {*}
 */
export function getWorkFlowList(params){
    return request({
        url: '/dwXcgl/xcjh/getWorkFlowList',
        method: 'post',
        data: params
    })
}

/**
 * 确认流程日志
 * @param params
 * @returns {*}
 */
export function getConfirmProcessLogList(params){
    return request({
        url: '/dwXcgl/xcjh/getConfirmProcessLogList',
        method: 'post',
        data: params
    })
}


/** 导出 */
export function DownLoad (params) {
    return request({
        url: '/dwXcgl/xcjh/exportExcel',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

export function getInfoByLcId(params){
    return request({
        url: '/dwXcgl/xcjh/getInfoByLcId',
        method: 'post',
        data: params
    })
}

export function getCalcellationLog(params){
    return request({
        url: '/dwXcgl/xcjh/getCalcellationLog',
        method: 'post',
        data: params
    })
}

export function xcjhExportExcel (params) {
    return request({
        url: '/dwXcgl/xcjh/exportExcel',
        method: 'post',
        data: params,
        contentType:'application/json',
        responseType: 'blob'
    })
}

export function xcjhExportExcelChildPlan (params) {
    return request({
        url: '/dwXcgl/xcjh/exportExcelChildPlan',
        method: 'post',
        data: params,
        contentType:'application/json',
        responseType: 'blob'
    })
}
