import request from '@/utils/request'

/** 每月考勤 */
export function GetDeptList (params) {
    return request({
        url: '/dwXcgl/zzjg/getDeptList',
        method: 'post',
        data: params
    })
}

/**
 * 获取人员管理list
 * @param params
 * @returns {*}
 */
export function GetPersonList(params){
    return request({
        url: '/dwXcgl/rygl/getPersonList',
        method: 'post',
        data: params
    })
}




/**
 * 获取人员补贴上限记录
 * @param params
 * @returns {*}
 * @constructor
 */
export function GetPersonLimitLog(params){
    return request({
        url: '/dwXcgl/rygl/getPersonLimitLog',
        method: 'post',
        data: params
    })
}

/**
 * 根据id删除人员
 */
export function DeleteById(params){
    return request({
        url: '/dwXcgl/rygl/deleteByIds',
        method: 'post',
        data: params
    })
}



/**
 * 根据id启用用户
 */
export function StartPersonById(params){
    return request({
        url: '/dwXcgl/rygl/startPersonById',
        method: 'post',
        data: params
    })
}
/**
 * 根据id停用用户
 */
export function DisablePersonById(params){
    return request({
        url: '/dwXcgl/rygl/disablePersonById',
        method: 'post',
        data: params
    })
}

/**
 * 获取原有人员信息
 */
export function GetPersonEntityList(params){
    return request({
        url: '/dwXcgl/rygl/getPersonEntityList',
        method: 'post',
        data: params
    })
}

/**
 * 从原有人员信息添加到新的组织架构中
 * @param params
 */
export function CreatPerson(params){
    return request({
        url: '/dwXcgl/rygl/creatPerson',
        method: 'post',
        data: params
    })
}


/**
 * 导入excel
 */
export function ImportExcel(params){
    return request({
        url: '/dwXcgl/rygl/importExcel',
        method: 'post',
        data: params,
        headers: { "Content-Type": "multipart/form-data" }
    })
}

/**
 * 根据人员id更新补贴费用上限
 */
export function InsertLimitByPersonId(params){
    return request({
        url: '/dwXcgl/rygl/insertLimitByPersonId',
        method: 'post',
        data: params
    })
}


/** 导出 */
export function ExportExcel (params) {
    return request({
        url: '/dwXcgl/rygl/exportExcel',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}
/** 导出excel模板 */
export function ExportExcelTemplate () {
    return request({
        url: '/dwXcgl/rygl/exportExcelTemplate',
        method: 'post',
        responseType: 'blob'
    })
}


/**
 * 获取详情
 */
export function getPersonInfo(id){
    return request({
        url: '/dwXcgl/rygl/getInfo/'+id,
        method: 'GET'
    })
}



/**
 * 清除终点数据
 */
export function cleanEndData(params){
    return request({
        url: '/dwXcgl/rygl/cleanEndData',
        method: 'post',
        data: params
    })
}

