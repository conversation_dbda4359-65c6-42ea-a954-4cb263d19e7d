import request from "@/utils/request";

export function getDeptList (params) {
    return request({
        url: '/dwXcgl/zzjg/getDeptList',
        method: 'post',
        data: params
    })
}
export function getXchz (params) {
    return request({
        url: '/dwXcgl/xchz/getXchzList',
        method: 'post',
        data: params
    })
}

export function exportExcel (params) {
    return request({
        url: '/dwXcgl/xchz/exportExcel',
        method: 'post',
        data: params,
        contentType:'application/json',
        responseType: 'blob'
    })
}

/**
 * 根据lcid获取行程详情
 */
export function getInfoByLcId(params){
    return request({
        url: '/dwXcgl/xchz/getInfoByLcId',
        method: 'post',
        data: params
    })
}


/**
 * 获取行程报备list
 * @param params
 * @returns {*}
 */
export function GetXcdkzphzList(params){
    return request({
        url: '/dwXcgl/xchz/getdkzphzList',
        method: 'post',
        data: params
    })
}


export function downloadImgZip (params) {
    return request({
        url: '/dwXcgl/xchz/downloadImgZip',
        method: 'post',
        data: params,
        contentType:'application/json',
        responseType: 'blob'
    })
}