import request from '@/utils/request'

/** 每月考勤 */
export function getListMytj (params) {
    return request({
        url: '/dwKqgl/kqtj/getListMytj',
        method: 'post',
        data: params
    })
}

/** 每日考勤 */
export function getListMrTj (params) {
    return request({
        url: '/dwKqgl/kqtj/getListMrTj',
        method: 'post',
        data: params
    })
}

/** 考勤详情 */
export function getClockInfo (params) {
    return request({
        url: '/dwKqgl/kqtj/getClockInfo',
        method: 'get',
        params: params
    })
}

/** 获取出行申请信息列表 */
export function getTjList (params) {
    return request({
        url: '/dwKqgl/kqtj/getTjList',
        method: 'get',
        params: params
    })
}


/** 获取人员轨迹列表 */
export function getUserHistoryLocation (params) {
    return request({
        url: '/dwKqgl/kqtj/getUserHistoryLocationNew',
        method: 'get',
        params: params
    })
}

/** 获取人员轨迹列表 */
export function getCalenDarList (params) {
    return request({
        url: '/dwKqgl/kqtj/getCalenDarList',
        method: 'get',
        params: params
    })
}


/** 每月考勤导出 */
export function mytjDownLoad (params) {
    return request({
        url: '/dwKqgl/kqtj/mytjDownLoad',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}


/** 每日考勤导出 */
export function mrTjDownLoad (params) {
    return request({
        url: '/dwKqgl/kqtj/mrTjDownLoad',
        method: 'post',
        data: params,
        responseType: 'blob'
    })
}

export function getListPing (params) {
    return request({
        url: '/dwKqgl/ping/getList',
        method: 'post',
        data: params
    })
}
