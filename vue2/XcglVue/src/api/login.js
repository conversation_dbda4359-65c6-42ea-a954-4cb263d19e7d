import request from '@/utils/request'

// 登录方法
export function login(username, code, Skey) {
  const params = {
    LoginName:username,
    code,
    Skey
  }
  return request({
    url: '/wpframe/Service/User/Login',
    headers: {
      isToken: false
    },
    method: 'get',
    params: params
  })
}

/**
 * 用户快速切换
 * @param {*} params
 * @returns
 */
 export function changePostLogin(loginName, KKey){
  const params = {
    loginName, KKey
  }
  return request({
      url: '/wpframe/Service/User/ChangePostLogin',
      method: 'get',
      params
  })
}


// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/wpframe/Service/User/GetCurrentPerson',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/wpframe/Service/User/LogOut',
    method: 'get'
  })
}

// 获取验证码*
export function sendCode(data) {
  return request({
    url: '/wpframe/Service/User/PostYzm',
    method: 'get',
    params: data
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 生成登录二维码
export function initScanImage() {
  return request({
    url: '/wpframe/Service/User/InitScanImage',
    method: 'get'
  })
}

export function getScanImage() {
  return request({
    url: '/wpframe/Service/User/GetScanImage',
    method: 'get'
  })
}

// 检查二维码扫描结果
export function checkScanResult(key) {
  return request({
    url: '/wpframe/Service/User/CheckScanResult',
    params: {key: key}
  })
}

export function getModule(){
  return request({
    url: '/dwXcgl/Service/Base/User/MLogin',
    method:'get'
  })
}
