import request from '@/utils/request'
import { method } from 'lodash'

/**
 * 用印管理添加
 * @param {*} params
 * @returns
 */
export function getyyglList(params){
    return request({
        url:'/dwKqgl/service/xtbg/yygl/list',
        method: 'get',
        params
    })
}

/**
 * 获取字典列表
 * id=66 需求描述、id=65 印章类型
 * @param {*} id
 * @returns
 */
export function getYyglType(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/GetType?id=${id}`,
        method: 'get'
    })
}

/**
 * 获取用印呈送列表
 * @returns
 */
export function getYySend(){
    return request({
        url: '/dwKqgl/service/xtbg/yygl/GetyySend',
        method: 'get'
    })
}

/**
 * 新增
 * @param {*} data
 * @returns
 */
export function addYygl(data){
    return request({
        url:'/dwKqgl/service/xtbg/yygl/add',
        method: 'post',
        data
    })
}

/**
 * 编辑
 * @param {*} data
 * @returns
 */
export function editYygl(data){
    return request({
        url: '/dwKqgl/service/xtbg/yygl/edit',
        method: 'put',
        data
    })
}

/**
 * 删除
 * @param {*} id
 * @returns
 */
export function deleteYygl(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/deleteById?id=${id}`,
        method: 'delete'
    })
}

/**
 * 查看
 * @param {*} id
 * @returns
 */
export function queryYygl(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/queryById?id=${id}`,
        method: 'get'
    })
}

/**
 * 获取附件列表
 * @param {*} id
 * @returns
 */
export function getYyglFile(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/FileByIDList?id=${id}`,
        method: 'get'
    })
}

/**
 * 新增附件
 * @returns
 */
export function addYyglFile(data){
    return request({
        url: '/dwKqgl/service/xtbg/yygl/AddFile',
        method: 'post',
        headers:{
            'Content-Type':'multipart/form-data/'
        },
        data
    })
}

/**
 * 删除附件
 * @param {*} id
 * @returns
 */
export function deleteYyglFile(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/DeleteFile?id=${id}`,
        method: 'delete'
    })
}

/**
 * 下载附件
 * @param {*} id
 * @returns
 */
export function downloadYyglFile(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/downloadFile?id=${id}`,
        responseType: 'blob',
        method: 'get'
    })
}

/**
 * 获取流程信息
 * @param {*} id
 * @returns
 */
export function getWorkFlowList(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/GetWorkFlowList?id=${id}`,
        method: 'get'
    })
}

/**
 * 用印管理流程
 * @param {*} params
 * @returns
 */
export function getyyglFlowList(params){
    return request({
        url:'/dwKqgl/service/xtbg/yygl/flowlist',
        method: 'get',
        params
    })
}

/**
 * 用印管理查询
 * @param {*} params
 * @returns
 */
export function getyyglQueryList(params){
    return request({
        url:'/dwKqgl/service/xtbg/yygl/querylist',
        method: 'get',
        params
    })
}

/**
 * 导出为word
 * @param {*} id
 * @returns
 */
export function exportWord(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/exportWord?id=${id}`,
        responseType: 'blob',
        method: 'post'
    })
}

/**
 * 导出为Pdf
 * @param {*} id
 * @returns
 */
export function exportPdf(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/exportPdf?id=${id}`,
        responseType: 'blob',
        method: 'post'
    })
}

/**
 * 用印统计查询
 * @param {*} params
 * @returns
 */
export function getyyglTJList(params){
    return request({
        url:'/dwKqgl/service/xtbg/yygl/TJlist',
        method: 'get',
        params
    })
}

/**
 * 统计页导出
 * @param {*} params
 * @returns
 */
export function exportExcel(params){
    return request({
        url: '/dwKqgl/service/xtbg/yygl/exportExcel',
        method: 'get',
        responseType: 'blob',
        params
    })
}


/**
 * 外借期限是否被占用
 * @param {*} id
 * @returns true || false
 */
export function getCanSubmitWjDate(id){
    return request({
        url: `/dwKqgl/service/xtbg/yygl/getCanSubmitWjDate?id=${id}`,
        method: 'get'
    })
}

export function getModule(){
    return request({
        url: '/dwKqgl/Service/Base/User/MLogin',
        method:'get'
    })
}

/**
 * 流程获取人员
 * @param {*} params
 * @returns
 */
export function getFlowUsers(params){
    return request({
        url: '/process/flow/getFlowUsers',
        method: 'get',
        params
    })
}

/**
 * 提交流程
 * @param {*} params
 * @returns
 */
export function submitFlow(params){
    return request({
        url: '/process/flow/submit',
        method: 'post',
        params
    })
}

/**
 * 退回流程
 * @param {*} params
 * @returns
 */
export function backFlow(params){
    return request({
        url: '/process/flow/back',
        method: 'post',
        params
    })
}
