<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"/>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="cache-control" content="no-cache">
    <link rel="stylesheet" type="text/css" href="./PeopleTrackFile/index.css" media="screen" />
    <script src="./PeopleTrackFile/echarts.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./PeopleTrackFile/jquery-1.10.2.min.js" type="text/javascript" charset="UTF-8"></script>
    <script src="./PeopleTrackFile/jquery.cookie.js" type="text/javascript"></script>
    <script src="./PeopleTrackFile/szyyAjax.js" type="text/javascript"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script type="text/javascript" src="https://api.map.baidu.com/getscript?v=4.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ&s=1"></script>
    <!--    <script type="text/javascript" src="//api.map.baidu.com/library/LuShu/1.2/src/LuShu_min.js"></script>-->
    <script type="text/javascript" src="./PeopleTrackFile/LuShu_min.js"></script>
    <script src="./PeopleTrackFile/common.js"></script>
    <script src="https://mapv.baidu.com/build/mapv.min.js"></script>
    <script src="https://code.bdstatic.com/npm/mapvgl@1.0.0-beta.109/dist/mapvgl.min.js"></script>
    <script src="./PeopleTrackFile/sip-0.13.6.min.js"></script>
    <script src="./PeopleTrackFile/style.js" type="text/javascript" charset="UTF-8"></script>
    <script type="text/javascript" src="./layer/layer.js"></script>
    <script type="text/javascript" src="./PeopleTrackFile/transformCoordinate.js"></script>
    <script src="./PeopleTrackFile/map.js" type="text/javascript" charset="UTF-8"></script>
    <title></title>
    <style>
        .img-hor {
            -moz-transform: scaleX(-1);

            -o-transform: scaleX(-1);

            -webkit-transform: scaleX(-1);

            transform: scaleX(-1);

            filter: FlipH;

            -ms-filter: "FlipH";

        }

        .img-vert {
            -moz-transform: scaleY(-1);

            -o-transform: scaleY(-1);

            -webkit-transform: scaleY(-1);

            transform: scaleY(-1);

            filter: FlipV;

            -ms-filter: "FlipV";

        }
        .anchorBL{
            display:none;
        }
        #map_container {
            position: absolute;
            top: 18%;
            left: 2%;
            right: 2%;
            bottom: 1%;
            border-radius: 5px;
        }
        /*设置 tbody高度大于400px时 出现滚动条*/
        table tbody {
            display: block;
            height: 85%;
            overflow-y: scroll;
            position: absolute;
            width: 94%;
        }

        table thead,
        tbody tr {
            display: table;
            table-layout: fixed;
            width: 100%;
        }

        /*滚动条默认宽度是16px 将thead的宽度减16px*/
        table thead {
            width: calc(100%);
        }

        /*table tbody::-webkit-scrollbar{
        display:none;
    }*/
        ul::-webkit-scrollbar {
            width: 2px;
        }

        ul::-webkit-scrollbar-track {
            background-color: #025ead;
        }

        ul::-webkit-scrollbar-thumb {
            background-color: #0090ff;
        }

        ul::-webkit-scrollbar-thumb:hover {
            background-color: #025ead;
        }

        ul::-webkit-scrollbar-thumb:active {
            background-color: #025ead;
        }

        table tbody::-webkit-scrollbar {
            width: 3px;
        }

        table tbody::-webkit-scrollbar-track {
            background-color: #025ead;
        }

        table tbody::-webkit-scrollbar-thumb {
            background-color: #0090ff;
        }

        table tbody::-webkit-scrollbar-thumb:hover {
            background-color: #025ead;
        }

        table tbody::-webkit-scrollbar-thumb:active {
            background-color: #025ead;
        }
        .car{
            float: left;
            width: 40%;
            margin: 1% 5%;
        }
        .num{
            float: left;
            width: 50%;
            color: #F9ED0A;
            font-size: 25px;
            margin-top: -1%;
            font-weight: bold;
        }
        .history_botton{
            /*float: left;*/
            width: 110px;
            height: 30px;
            border: solid 2px #0090FF;
            background: #0090FF;
            font-size: 20px;
            text-align: center;
            line-height: 30px;
            border-radius: 5px;
            margin-left: 20px;
        }
        input[type="date"]::-webkit-calendar-picker-indicator {
            border: 1px solid #ccc;
            border-radius: 2px;
            box-shadow: inset 0 1px #fff, 0 1px #eee;
            background-color: #eee;
            background-image: url("./PeopleTrackFile/select.png");
            color: #666;
        }
        .img_police{
            width: 30px;
            height: 30px;
            float: right;
            margin-right: 10px;
        }
    </style>
</head>
<body id="body">
<div class="logo" onclick="hide()" id="title">考勤管理历史轨迹</div>
<div class="center-wrapper">
    <div class="history_r" style="width: 98%;">
        <div class="border_t"></div>
        <div class="border_t_l"></div>
        <div class="border_t_r"></div>
        <div class="border_b_l"></div>
        <div class="border_b_r"></div>
        <div class="lzcl_list" style="margin-left: 15px;">
            <p id="licencePlateId">历史轨迹</p>
        </div>
        <div  style="margin-left:3%;">

            <a id="driverMileageA" style="margin-right:10px;float:right;color:#fff;font-size:15px;height:33px;line-height:33px;display: none">总公里数：-------</a>

            <select  id='speedSelect' style="float: left;" onchange='selectSpeed()'>
                <option value='' >请选择</option>
                <option selected value='1000' >播放速度：1km/s</option>
                <option value='2000' >播放速度：2km/s</option>
                <option value='5000' >播放速度：5km/s</option>
                <option value='10000' >播放速度：10km/s</option>
                <option value='20000' >播放速度：20km/s</option>
                <option value='50000' >播放速度：50km/s</option>
                <option value='100000' >播放速度：100km/s</option>
            </select>

            <select  id='speedSelectByTime' style="float: left;" onchange='selectSpeedTime()'>
                <option selected value='' >请选择</option>
                <option value='60' >播放速度：X60/s</option>
                <option value='300' >播放速度：X300/s</option>
                <option value='900' >播放速度：X900/s</option>
                <option value='1800' >播放速度：X1800/s</option>
                <option value='3600' >播放速度：X3600/s</option>
            </select>


            <input type="date" style="float: left;margin-top: 3px;margin-left: 1%;border-radius: 5px;background-color: #031429;color: #FFFFFF;border-width: 1px;border-color: #0090ff;" onchange="dateChange()" id="startDate" value="2015-09-24">
            <a style="float: left;color: #ffffff">--</a>
            <input type="date" style="float: left;margin-top: 3px;border-radius: 5px;background-color: #031429;color: #FFFFFF;border-width: 1px;border-color: #0090ff;"  onchange="dateChange()" id="endDate" value="2015-09-24">
            <input id="userId" style="display: none"></input>
            <div class="history_botton"  style="float: left;width: 90px;cursor:pointer;" onclick="startDrawing(1)">查询</div>
            <img src="./PeopleTrackFile/history_play.png" class="history_botton" style="cursor:pointer;float: left;width: 50px;height: 30px;object-fit:contain" onclick="startDrawing(2)" id="startBtnId">

        </div>
        <div style="margin-left:2%;margin-top: 4%">
            <a id="coordinateA" style="margin-right:10px;float:right;color:#fff;font-size:15px;height:33px;line-height:33px">当前经纬度：--,--</a>
            <!--            <a id="speedA" style="margin-right:10px;float:right;color:#fff;font-size:15px;height:33px;line-height:33px">当前速度：&#45;&#45;</a>-->
            <a id="timeA" style="margin-right:10px;float:right;color:#fff;font-size:15px;height:33px;line-height:33px">时间：--</a>
        </div>
        <div id="map_container"></div>

    </div>
    <div style="position: absolute;width: 100%;height: 100%;background-color: rgba(255,255,255,0);display: none" onclick="hide()" id="pop1">
        <div style="background: #000f1d ;position: absolute;z-index: 10000;height: 90%;width: 90%;left: 5%;box-shadow: 0 0 15px 10px #0ca0a8;top: 3.2vh!important;padding: 0!important;">
            <div id="pop" style="height: 100%;width: 100%;"></div>
        </div>
    </div>
    <script type="text/javascript">

      //打开地图界面
      var isShowPop = true;
      function postMap(){
        if(isShowPop) {
          var pop = document.getElementById("pop");
          var pop1 = document.getElementById("pop1");
          var url = "/yydp/LZCL/reportList.html?deviceId="+curGpsNum
            +"&startDate="+$("#startDate").val()+"T00:00:00"
            +"&endDate="+$("#endDate").val()+"T23:59:59";
          pop.innerHTML = '<object type="text/html" data="' + url + '" width="100%" height="100%"></object><img src="./PeopleTrackFile/close.png" style="position: absolute;z-index: 9998;right: 1%;cursor: pointer;" onclick="hide()">';
          pop1.style.display = 'block';
          isShowPop=false;
        }
      }

      function hide() {
        var pop1=document.getElementById("pop1");
        pop1.style.display="none";
        isShowPop=true;
      }


    </script>
    <script type="text/javascript">
      //路书
      var lushu;
      //标识物
      var marker;
      //点位
      var arrpois=[];
      //速度
      var speedNums = [];
      //播放速度 按时间计算
      var playSpeedByTime = [];
      //时间
      var times = [];
      //地图
      var map;
      //当前绘制路径gps编号
      var curGpsNum;
      //当前车牌号
      var curCarNum;
      //当前时间限制
      var curGPSEnableDate;
      //播放速度
      var playSpeed;
      //非零速度
      var notZeroSpeed=0.0;
      //日期限制
      var searchLimitData = 0;

      function selectSpeed() {

        // playSpeed=$("#speedSelect option:selected").val();
        if($("#speedSelect option:selected").val().length>0) {
          $(".nice-select")[1].childNodes[0].innerHTML='请选择'
          startDrawing(1);
        }
      }

      function selectSpeedTime() {

        if($("#speedSelectByTime option:selected").val().length>0) {
          $(".nice-select")[0].childNodes[0].innerHTML='请选择';
          startDrawing(1);
        }
      }

      function clearLoadInitData() {
        curGpsNum = undefined;
        curCarNum = undefined;
        curGPSEnableDate = 'undefined';
        lushuMarker = undefined;
        $("#licencePlateId").text("历史轨迹");
        // $("#speedA").text("车辆速度:"+"--");
        $("#timeA").text("时间:"+"--");
        $("#driverMileageA").text("总公里数:"+"-------");
        $("#coordinateA").text("车辆经纬度:"+"--,--");
        notZeroSpeed=0.0;
        recentPointIndex = -1;
        try {
          lushu.stop()
          clickIndex = 1;
          // $("#startBtnId").text("播放");
          $("#startBtnId").attr("src", "./PeopleTrackFile/history_play.png");
        }catch (e) {
          console.log(e.message);
        }
        lushu =undefined;
        map.clearOverlays();
      }


      function dateChange() {
        if(curGPSEnableDate!='undefined'&&curGPSEnableDate!=undefined) {
          if (curGPSEnableDate > $("#startDate").val()) {$("#startDate").val(curGPSEnableDate);}
          if (curGPSEnableDate > $("#endDate").val()) $("#endDate").val(curGPSEnableDate);
        }
        var limitData = new Date();
        limitData = limitData.setDate(limitData.getDate()- searchLimitData+1);
        limitData = new Date(limitData);
        limitData = formatDate("yyyy-MM-dd",limitData);
        if(searchLimitData!=0) {
          if (limitData > $("#startDate").val()) {$("#startDate").val(limitData);}
          if (limitData > $("#endDate").val()) $("#endDate").val(limitData);
        }
      }

      function initData(){
        var curDate = new Date();
        var startDate = formatDate("yyyy-MM-dd",curDate);
        var endDate = formatDate("yyyy-MM-dd",curDate);
        $("#startDate").val(startDate);
        $("#endDate").val(endDate);
      }

      function formatDate(fmt,date) {
        var o = {
          "M+": date.getMonth() + 1, //月份
          "d+": date.getDate(), //日
          "H+": date.getHours(), //小时
          "m+": date.getMinutes(), //分
          "s+": date.getSeconds(), //秒
          "q+": Math.floor((date.getMonth() + 3) / 3), //季度
          "S": date.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
          if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
      }



      function lushuMoveBind() {

        BMapLib.LuShu.prototype._move = function(n, j, m) {
          var i = this,
            playSpeedByTimeIndex = i.i+1;
          h = 0,
            e = 10,
            // f = this._opts.speed / (1000 / e),
            f = (playSpeedByTime[playSpeedByTimeIndex]==0?0.1:playSpeedByTime[playSpeedByTimeIndex]) / (1000 / e),
            l = this._projection.lngLatToPoint(n),
            k = this._projection.lngLatToPoint(j),
            g = Math.round(i._getDistance(l, k) / f);
          if (g < 1) {
            i._moveNext(++i.i);
            return
          }
          i._intervalFlag = setInterval(function() {
            if (h >= g) {
              clearInterval(i._intervalFlag);
              if (i.i > i._path.length) {
                return
              }
              i._moveNext(++i.i)
            } else {
              h++;
              var o = m(l.x, k.x, h, g),
                r = m(l.y, k.y, h, g),
                q = i._projection.pointToLngLat(new BMap.Pixel(o, r));
              if (h == 1) {
                var p = null;
                if (i.i - 1 >= 0) {
                  p = i._path[i.i - 1]
                }
                if (i._opts.enableRotation == true) {
                  i.setRotation(p, n, j)
                }
                if (i._opts.autoView) {
                  if (!i._map.getBounds().containsPoint(q)) {
                    i._map.setCenter(q)
                  }
                }
              }
              // console.log("--------q---------");
              // console.log(q);
              i._marker.setPosition(q);
              i._setInfoWin(q)
            }
          }, e)
        }

        BMapLib.LuShu.prototype._addMarker = function(f) {
          if (this._marker) {
            this.stop();
            this._map.removeOverlay(this._marker);
            clearTimeout(this._timeoutFlag)
          }
          this._overlay && this._map.removeOverlay(this._overlay);
          var e = new BMap.Marker(this._path[0]);
          this._opts.icon && e.setIcon(this._opts.icon);
          if(recentPointIndex>-1) {
            lushuMarker=e;
            lushuMarker.setPosition(arrpois[recentPointIndex]);
          }
          this._map.addOverlay(e);
          if(recentPointIndex<0) {
            e.setAnimation(BMAP_ANIMATION_DROP);
          }
          this._marker = e
        }

        BMapLib.LuShu.prototype.setRotation = function(l, f, m) {
          var j = this;
          var e = 0;
          f = j._map.pointToPixel(f);
          m = j._map.pointToPixel(m);
          if (m.x != f.x) {
            var k = (m.y - f.y) / (m.x - f.x),
              g = Math.atan(k);
            e = g * 360 / (2 * Math.PI);
            if (m.x < f.x) {
              e = -e + 90 + 90
            } else {
              e = -e
            }
            // console.log("aaa "+e);
            if(e<90||e>270) {
              $("img[src='./PeopleTrackFile/car.gif']").addClass("img-hor");
            }else {
              $("img[src='./PeopleTrackFile/car.gif']").removeClass("img-hor");
            }
            // j._marker.setRotation(-e)
          } else {
            var h = m.y - f.y;
            var i = 0;
            if (h > 0) {
              i = -1
            } else {
              i = 1
            }
            // console.log("bbbb "+(-i * 90));
            // j._marker.setRotation(-i * 90)
          }
          return
        }

        BMapLib.LuShu.prototype._moveNext = function(e) {
          var f = this;
          lushuMarker = f._marker;
          if(recentPointIndex>-1) {
            f.i=recentPointIndex;
            e=recentPointIndex;
            recentPointIndex=-1;
            // if(recentPointMarker!=undefined) {
            //     map.removeOverlay(recentPointMarker);
            // }
            // map.addOverlay(lushuMarker);
          }
          // console.log("--------aaaa---------")
          // console.log(e);
          // lng lat,
          // console.log(speedNums[e])
          // console.log(arrpois[e])
          notZeroSpeed = speedNums[e]>0?speedNums[e]:notZeroSpeed;
          $("#timeA").text("时间: "+times[e]);
          //  $("#speedA").text("车辆速度:"+notZeroSpeed+"km/h");
          $("#coordinateA").text("车辆经纬度:"+arrpois[e].lat.toFixed(6)+","+arrpois[e].lng.toFixed(6));
          if(e==(arrpois.length-1)) {
            clickIndex = 3;
            // $("#startBtnId").text("播放");
            $("#startBtnId").attr("src", "./PeopleTrackFile/history_play.png");
          }

          if (e < this._path.length - 1) {
            f._move(f._path[e], f._path[e + 1], f._tween.linear)
          }else {
            f._marker.setPosition(arrpois[e]);
          }
        }

      }

      var clickIndex = 1;
      function startDrawing(index) {
        if($(".nice-select")[0].childNodes[0].innerHTML=='请选择' &&
          $(".nice-select")[1].childNodes[0].innerHTML=='请选择') {
          layer.msg("请选择播放速度");return;
        }

        if(index==2&&clickIndex==1) {layer.msg("请先查询路线");return;}
        if(index==1) clickIndex=index;
        if(clickIndex==1) {
          arrpois = [];
          speedNums = [];
          times = [];
          try {
            lushu.stop()
          } catch (e) {
            console.log(e.message);
          }
          map.clearOverlays();
          loadPoints();
          // $("#startBtnId").text("播放");
          $("#startBtnId").attr("src", "./PeopleTrackFile/history_play.png");
        }else if(clickIndex==2) {
          pauseDrawing();
          clickIndex = 3;
          // $("#startBtnId").text("播放");
          $("#startBtnId").attr("src", "./PeopleTrackFile/history_play.png");
        }else if(clickIndex==3) {
          continueDrawing();
          clickIndex = 2;
          // $("#startBtnId").text("暂停");
          $("#startBtnId").attr("src", "./PeopleTrackFile/history_pause.png");
        }
      }

      function pauseDrawing() {
        lushu.pause();
      }

      function continueDrawing() {
        lushu.start();
      }

      function stopDrawing() {
        lushu.stop();
        clickIndex = 1;
        $("#startBtnId").text("开始");
      }
      function drawingPath() {
        playSpeed=$("#speedSelect option:selected").val();
        if(arrpois==undefined||arrpois.length<1) {
          layer.msg("无点位数据");
          return;
        }
        map.setViewport(arrpois);
        // marker=new BMap.Marker(arrpois[0],{
        //     icon  :new BMap.Icon('http://developer.baidu.com/map/jsdemo/img/car.png',new BMap.Size(52,26),{anchor :new BMap.Size(27, 13)})
        // });
        // var label =new BMap.Label("粤A30780",{offset:new BMap.Size(0,-30)});
        // label.setStyle({border:"1px solid rgb(204, 204, 204)",color:"rgb(0, 0, 0)",borderRadius:"10px",padding:"5px",background:"rgb(255, 255, 255)",});
        // marker.setLabel(label);
        // map.addOverlay(marker);

        //绘制箭头及其样式
        // var sy = new BMap.Symbol(BMap_Symbol_SHAPE_BACKWARD_OPEN_ARROW, {
        //     scale: 0.6,//图标缩放大小
        //     strokeColor:'#fff',//设置矢量图标的线填充颜色
        //     strokeWeight: 2,//设置线宽
        // });
        // var icons = new BMap.IconSequence(sy, '100%', '90%',false);//设置为true，可以对轨迹进行编辑

        map.addOverlay(new BMap.Polyline(arrpois, {
          strokeColor:"#18a45b",//设置颜色
          strokeWeight:5 ,//宽度
          strokeOpacity:1,//折线的透明度，取值范围0 - 1
          enableEditing: false,//是否启用线编辑，默认为false
          enableClicking: false,//是否响应点击事件，默认为true
          // icons:[icons]
        }));
        lushu = new BMapLib.LuShu(map,arrpois,{
          defaultContent:"",//"从天安门到百度大厦"
          autoView:true,//是否开启自动视野调整，如果开启那么路书在运动过程中会根据视野自动调整
          icon  : new BMap.Icon('./PeopleTrackFile/car.gif', new BMap.Size(80,80),{anchor : new BMap.Size(40, 20)}),
          speed: 1000,
          enableRotation:true,//是否设置marker随着道路的走向进行旋转
          landmarkPois: [
          ]});

        // lushu.start();
        clickIndex = 3;
        // $("#startBtnId").text("播放");
        $("#startBtnId").attr("src", "./PeopleTrackFile/history_play.png");
      }
      var latitude = 0.0;
      var longitude = 0.0;
      //加载点位数据
      function loadPoints() {
        //loading层
        var loadIndex = layer.load(1, {
          shade: [0.2,'#fff'] //0.1透明度的白色背景
        });
        szyyAjaxByCookie({
          url: "/app/wfw/dwKqgl/kqtj/getPolylinePoints?userId="+$("#userId").val()
            +"&startDate="+$("#startDate").val()+"T00:00:00"
            +"&endDate="+$("#endDate").val()+"T23:59:59",
          type: "get",
          dataType: 'json',
          async:true,
          contentType: "application/json",
          success: function (data) {
            layer.close(loadIndex);
            if(data.code==200) {
              if(data.result == null) {
                layer.msg("无点位数据");
                return;
              }
              if (data.result.driverMileageShow == 1) {
                $("#driverMileageA").show();
              } else {
                $("#driverMileageA").hide();
              }
              lushuMarker = undefined;
              //   $("#speedA").text("车辆速度:" + "--");
              $("#timeA").text("时间:" + "--");
              $("#driverMileageA").text("总公里数:" + "-------");
              $("#coordinateA").text("车辆经纬度:" + "--,--");
              recentPointIndex = -1;

             // console.log(data.result.carLocationList);
              if (data.result.carLocationList != null) {
                var speedSelectByTimeValue = $("#speedSelectByTime option:selected").val() * 1000;
                var lastUpTime = data.result.carLocationList[0].loc_time;
                var lastUpIndex = 0;
                var lastUpSpeed = 0;
                var lastOneTime = 0;
                playSpeedByTime = [];
                for (var i = 0; i < data.result.carLocationList.length; i++) {
                  if (longitude != data.result.carLocationList[i].longitude || latitude != data.result.carLocationList[i].latitude) {
                    var gcj02Result = wgs84togcj02(data.result.carLocationList[i].longitude, data.result.carLocationList[i].latitude);
                  //  var bd09Result = gcj02tobd09(gcj02Result[0], gcj02Result[1]);
                    var bd09Result = gcj02tobd09(data.result.carLocationList[i].longitude,data.result.carLocationList[i].latitude);

                    longitude = data.result.carLocationList[i].longitude;
                    latitude = data.result.carLocationList[i].latitude;
                    arrpois.push(new BMap.Point(bd09Result[0], bd09Result[1]));
                    //  speedNums.push(data.result.carLocationList[i].speed);
                    times.push(timestampToTime(data.result.carLocationList[i].loc_time));
                    lastOneTime = data.result.carLocationList[i].loc_time;
                    if ($(".nice-select")[0].childNodes[0].innerHTML == '请选择') {
                      if (arrpois.length > 1) {
                        lastUpSpeed += GetDistance(bd09Result[1], bd09Result[0], arrpois[arrpois.length - 2].lat, arrpois[arrpois.length - 2].lng);
                        // console.log(i + " " + data.result.carLocationList[i].loc_time + " " + lastUpTime + " " + ((data.result.carLocationList[i].loc_time - lastUpTime)) + "  " + lastUpSpeed);
                      }
                      if (speedSelectByTimeValue < (data.result.carLocationList[i].loc_time - lastUpTime)) {
                        for (var n = lastUpIndex; n < speedNums.length; n++) {
                          // if ((data.result.carLocationList[i - 1].loc_time - lastUpTime) > speedSelectByTimeValue) {
                          playSpeedByTime.push(lastUpSpeed * 1.0 / (((data.result.carLocationList[i].loc_time - lastUpTime) * 1.0) / (speedSelectByTimeValue * 1.0)));
                          // } else {
                          //     playSpeedByTime.push((lastUpSpeed > 0 ? Math.round(lastUpSpeed * (((data.result.carLocationList[i - 1].loc_time - lastUpTime) * 1.0) / (speedSelectByTimeValue * 1.0))) : 1000));
                          // }
                          // console.log("进入" + (n + 1) + "  " + data.result.carLocationList[i].loc_time + " " + lastUpTime + "  耗时：" + (data.result.carLocationList[i].loc_time - lastUpTime) / 1000 + "  " + "  距离：" + lastUpSpeed + "  速度：" + playSpeedByTime[playSpeedByTime.length - 1] + "   下标：" + lastUpIndex);
                        }
                        lastUpIndex = speedNums.length;
                        lastUpTime = data.result.carLocationList[i].loc_time;
                        lastUpSpeed = 0;
                        // console.log("进入" + lastUpTime + " " + lastUpIndex + " " + playSpeedByTime.length)
                      }
                    } else {
                      playSpeedByTime.push($("#speedSelect option:selected").val());
                    }
                  }
                }
                longitude = -1.0;
                latitude = -1.0;
                if ($(".nice-select")[0].childNodes[0].innerHTML == '请选择') {
                  for (var n = lastUpIndex; n < speedNums.length; n++) {
                    playSpeedByTime.push(lastUpSpeed * 1.0 / (((lastOneTime - lastUpTime) * 1.0) / (speedSelectByTimeValue * 1.0)));

                    console.log("--进入" + (n + 1) + "  " + lastOneTime + " " + lastUpTime + "  耗时：" + (lastOneTime - lastUpTime) / 1000 + "  " + "  距离：" + lastUpSpeed + "  速度：" + playSpeedByTime[playSpeedByTime.length - 1] + "   下标：" + lastUpIndex);
                  }
                }
                drawingPath();
              } else {
                layer.msg("无点位数据");
              }

            }else {
              layer.msg(data.message);
            }
          },
          error: function (data) {
            layer.close(loadIndex);
            layer.msg("加载失败"+data.responseJSON);
          }
        });
      }


      function timestampToTime(timestamp) {
        var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1): date.getMonth() + 1) + '-';
        var D = (date.getDate()<10?'0'+date.getDate():date.getDate()) + ' ';
        var h = (date.getHours()<10?'0'+date.getHours():date.getHours()) + ':';
        var m = (date.getMinutes()<10?'0'+date.getMinutes():date.getMinutes()) + ':';
        var s = date.getSeconds()<10?'0'+date.getSeconds():date.getSeconds();
        return Y + M + D + h + m + s;
        // return h+m+s;
      }

      function Rad(d) {
        return d * Math.PI / 180.0; //经纬度转换成三角函数中度分表形式。
      }
      //计算距离，参数分别为第一点的纬度，经度；第二点的纬度，经度
      function GetDistance(lat1, lng1, lat2, lng2) {
        var radLat1 = Rad(lat1);
        var radLat2 = Rad(lat2);
        var a = radLat1 - radLat2;
        var b = Rad(lng1) - Rad(lng2);
        var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * 6378.137; // 地球半径，千米;
        // s = Math.round(s * 10000) / 10000; //输出为公里
        s = Math.round(s * 1000) / 1; //单位修改为米,取整
        //s=s.toFixed(4);
        return s;
      }

      function setRotation(marker, f, m) {
        var e = 0;
        f = map.pointToPixel(f);
        m = map.pointToPixel(m);
        if (m.x != f.x) {
          var k = (m.y - f.y) / (m.x - f.x),
            g = Math.atan(k);
          e = g * 360 / (2 * Math.PI);
          if (m.x < f.x) {
            e = -e + 90 + 90
          } else {
            e = -e
          }
          if(e<90||e>270)  {
            $("img[src='./PeopleTrackFile/car.gif']").addClass("img-hor");
          }else {
            $("img[src='./PeopleTrackFile/car.gif']").removeClass("img-hor");
          }
          // marker.setRotation(-e)
        } else {
          var h = m.y - f.y;
          var i = 0;
          if (h > 0) {
            i = -1
          } else {
            i = 1
          }
          // marker.setRotation(-i * 90)
        }
        return
      }

      var recentPointIndex = -1;
      var recentPointMarker = undefined;
      var lushuMarker = undefined;
      //地图初始化
      function baiduMapInit() {
        map = new BMap.Map('map_container',{ enableMapClick: false });
        map.enableScrollWheelZoom();
        map.centerAndZoom(new BMap.Point(121.56, 29.86), 11);
        map.addEventListener('click', function (e) {
          console.log('点击位置经纬度：' + e.point.lng + ',' + e.point.lat);
          if(arrpois==undefined||arrpois.length<1 || lushuMarker == undefined) {
            // layer.msg("无点位数据");
            return;
          }
          var minDistance=99999999999999;
          for (let i = 0; i < arrpois.length; i++) {
            var distance = GetDistance(e.point.lat,e.point.lng,arrpois[i].lat,arrpois[i].lng);
            // console.log("距离  "+ distance);
            if(minDistance>distance) {
              minDistance = distance;
              recentPointIndex = i;
              // console.log("点位  "+ recentPointIndex);
            }
          }
          if(recentPointIndex>-1) {
            if(arrpois.length>(recentPointIndex+1)) {
              setRotation(marker, arrpois[recentPointIndex], arrpois[recentPointIndex + 1]);
            }
            $("#timeA").text("时间: "+times[recentPointIndex]);
            //    $("#speedA").text("车辆速度:"+speedNums[recentPointIndex]+"km/h");
            $("#coordinateA").text("车辆经纬度:"+arrpois[recentPointIndex].lat.toFixed(6)+","+arrpois[recentPointIndex].lng.toFixed(6));
            lushu.pause();
            lushuMarker.setPosition(arrpois[recentPointIndex]);
            if(clickIndex==2) {
              lushu.start();
            }
          }
        });
        map.setMapStyle({styleJson: mapJson });
        $('#map_container div.anchorBL').hide();
      }

      function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = window.location.search.substr(1).match(reg);  //匹配目标参数
        if (r != null && r!=undefined) return unescape(decodeURI(r[2])); return ''; //返回参数值
      }


      $(function(){
        baiduMapInit(); //先话地图

        setTimeout(function (){
          if(getUrlParam("date")!=undefined && getUrlParam("date").length>0) {
            $("#startDate").val(getUrlParam("date"));
          }
          if(getUrlParam("date")!=undefined && getUrlParam("date").length>0) {
            $("#endDate").val(getUrlParam("date"));
          }
          if(getUrlParam("userId")!=undefined && getUrlParam("userId").length>0) {
            $("#userId").val(getUrlParam("userId"));
          }
          if(getUrlParam("userName")!=undefined && getUrlParam("userName").length>0) {
            $("#title").text(getUrlParam("userName") + ' 历史轨迹');
          }
        },10);

        initData(); //初始化日期
        lushuMoveBind();//路书绑定
      });
    </script>
</div>
<script type="text/javascript" src="./PeopleTrackFile/nice-select.js"></script>
<link rel="stylesheet" type="text/css" href="./PeopleTrackFile/nice-select.css" media="screen" />
</body>
</html>
