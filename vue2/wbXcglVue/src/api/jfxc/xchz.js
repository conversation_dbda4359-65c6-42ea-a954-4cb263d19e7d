import request from "@/utils/request";

export function getDeptList (params) {
    return request({
        url: '/dwWbXcgl/zzjg/getDeptList',
        method: 'post',
        data: params
    })
}
export function getXchz (params) {
    return request({
        url: '/dwWbXcgl/xchz/getXchzList',
        method: 'post',
        data: params
    })
}

export function exportExcel (params) {
    return request({
        url: '/dwWbXcgl/xchz/exportExcel',
        method: 'post',
        data: params,
        contentType:'application/json',
        responseType: 'blob'
    })
}

/**
 * 根据lcid获取行程详情
 */
export function getInfoByLcId(params){
    return request({
        url: '/dwWbXcgl/xchz/getInfoByLcId',
        method: 'post',
        data: params
    })
}


/**
 * 获取行程报备list
 * @param params
 * @returns {*}
 */
export function GetXcdkzphzList(params){
    return request({
        url: '/dwWbXcgl/xchz/getdkzphzList',
        method: 'post',
        data: params
    })
}


export function downloadImgZip (params) {
    return request({
        url: '/dwWbXcgl/xchz/downloadImgZip',
        method: 'post',
        data: params,
        contentType:'application/json',
        responseType: 'blob'
    })
}