import request from '@/utils/request'

/** 每月考勤 */
export function getDeptList (params) {
    return request({
        url: '/dwWbXcgl/zzjg/getDeptList',
        method: 'post',
        data: params
    })
}
export function addDept (params) {
    return request({
        url: '/dwWbXcgl/zzjg/addDept',
        method: 'post',
        data: params
    })
}



export function getDepartmentList (params) {
    return request({
        url: '/dwWbXcgl/zzjg/getDepartmentList',
        method: 'post',
        data: params
    })
}


export function delDept (params) {
    return request({
        url: '/dwWbXcgl/zzjg/delDept',
        method: 'post',
        data: params
    })
}
export function enable (params) {
    return request({
        url: '/dwWbXcgl/zzjg/enable',
        method: 'post',
        data: params
    })
}
export function disable (params) {
    return request({
        url: '/dwWbXcgl/zzjg/disable',
        method: 'post',
        data: params
    })
}

export function updDept (params) {
    return request({
        url: '/dwWbXcgl/zzjg/updDept',
        method: 'post',
        data: params
    })
}

