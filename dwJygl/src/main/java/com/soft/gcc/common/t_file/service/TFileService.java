package com.soft.gcc.common.t_file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.common.t_file.entity.TFile;
import org.springframework.web.multipart.MultipartFile;

/**
* <AUTHOR>
* @description 针对表【T_File】的数据库操作Service
* @createDate 2023-02-23 21:23:38
*/
public interface TFileService extends IService<TFile> {

    TFile uploadAndSaveFile(MultipartFile file,String fileName, String type, String hjID,Integer projectid);

}
