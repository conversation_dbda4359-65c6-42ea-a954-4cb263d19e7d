package com.soft.gcc.common.dfdw_dict.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictType;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictTypeService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RequestMapping("/dfdw/dictType")
@RestController
public class DfdwTDictTypeController extends BaseController {

    @Resource
    private DfdwTDictTypeService dfdwTDictTypeService;


    /**
     * 新增字典类型
     * @param dfdwTDictType
     * @return
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermi('JDWGB01ZD01QX02')")
    public Result<Object> createDictType( @RequestBody DfdwTDictType dfdwTDictType) {
        Result<Object> result = dfdwTDictTypeService.createDictType(dfdwTDictType);
        return result ;

    }

    /**
     * 删除字典类型
     * @return
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWGB01ZD01QX04')")
    public Result<Object> deleteDictType(@RequestParam("id") Integer id) {
        Result<Object> result = dfdwTDictTypeService.deleteDictType(id);
        return result;
    }

    /**
     * 修改字典类型
     * @return
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('JDWGB01ZD01QX03')")
    public Result<Object> updateDictType(@RequestBody DfdwTDictType dfdwTDictType) {
        Result<Object> result = dfdwTDictTypeService.updateDictType(dfdwTDictType);
        return result;
    }

    /**
     * 字典类型分页
     * @param dfdwTDictType
     * @return
     */
    @PostMapping("/typePage")
    @PreAuthorize("@ss.hasPermi('JDWGB01ZD01QX01')")
    public Result<Object> typePage( @RequestBody DfdwTDictType dfdwTDictType) {
        IPage<DfdwTDictType> dfdwTDictTypeIPage =  dfdwTDictTypeService.getList(dfdwTDictType);
        return Result.ok(dfdwTDictTypeIPage);
    }
}
