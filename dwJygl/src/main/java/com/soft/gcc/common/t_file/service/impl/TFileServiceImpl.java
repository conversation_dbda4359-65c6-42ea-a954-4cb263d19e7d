package com.soft.gcc.common.t_file.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.mapper.TFileMapper;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.jygl.util.AliyunOSSUtils;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.Date;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【T_File】的数据库操作Service实现
* @createDate 2023-02-23 21:23:38
*/
@Service
public class TFileServiceImpl extends ServiceImpl<TFileMapper, TFile>
    implements TFileService{


    @Override
    public TFile uploadAndSaveFile(MultipartFile file,String fileName, String type, String hjID,Integer projectid) {
        PersonEntity person = SessionHelper.getSessionPerson();
        LocalDate localDate = LocalDate.now();
        String YEAR_MONTH = localDate.getYear() + "/" + localDate.getMonthValue() + "/";
        String path = "Upload/FileManage/Jygl/" + type + "/" + YEAR_MONTH;
        String hz = fileName.substring(fileName.lastIndexOf(".") + 1);

        String url = path + AliyunOSSUtils.uploadFile(path, (UUID.randomUUID() + "." + hz), file);

        TFile sf = new TFile();
        sf.setProjectid(projectid);
        //type
        sf.setFilename(fileName);
        sf.setFilepath(url);
        sf.setUploaddate(new Date());
        sf.setType(type);
        sf.setPersonname(person.getRealName());
        sf.setPersonzgh(person.getLoginName());
        //lcDefine.LcID
        sf.setFunctionid(20017);
        //lcjd.lcjdID
        sf.setHjid(hjID);
        //1 2
        sf.setSubtname(type);
        this.save(sf);
        return sf;
    }
}




