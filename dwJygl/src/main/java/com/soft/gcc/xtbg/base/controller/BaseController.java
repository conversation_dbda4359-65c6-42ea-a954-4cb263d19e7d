package com.soft.gcc.xtbg.base.controller;

import com.alibaba.fastjson.JSONObject;
import com.soft.framework.helper.SessionHelper;
import com.yyszc.wpbase.ventity.PersonEntity;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/12 15:02:14
 */
public class BaseController {
    public PersonEntity user() {
        PersonEntity personEntity =  SessionHelper.getSessionPerson();
        if (personEntity !=null){
            return personEntity;
        }else{
            return null;
        }
    }
}
