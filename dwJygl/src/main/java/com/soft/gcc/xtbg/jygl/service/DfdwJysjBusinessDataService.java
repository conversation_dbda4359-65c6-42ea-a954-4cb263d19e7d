package com.soft.gcc.xtbg.jygl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.dto.AccountsReceivableDto;
import com.soft.gcc.xtbg.jygl.dto.ProjectNameAndCodeDto;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjBusinessDataParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Business_data(经营数据-经营数据管理)】的数据库操作Service
* @createDate 2025-07-15 14:11:24
*/
public interface DfdwJysjBusinessDataService extends IService<DfdwJysjBusinessData> {




    IPage<DfdwJysjBusinessData> getList(DfdwJysjBusinessDataParam param);


    /**
     * 导入
     * @param file
     * @return
     */
    Result<Object> importExcel(MultipartFile file);

    /**
     * 获取汇总列表
     * @return
     */
    List<AccountsReceivableDto> getSummary();

    /**
     * 获取项目名称和项目编号
     * @return
     */
    List<ProjectNameAndCodeDto> getProjectNameAndCode();

}
