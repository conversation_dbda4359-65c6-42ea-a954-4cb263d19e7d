package com.soft.gcc.xtbg.jygl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.jygl.dto.AccountsReceivableDto;
import com.soft.gcc.xtbg.jygl.dto.ProjectNameAndCodeDto;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Business_data(经营数据-经营数据管理)】的数据库操作Mapper
* @createDate 2025-07-15 14:11:24
* @Entity com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData
*/
public interface DfdwJysjBusinessDataMapper extends BaseMapper<DfdwJysjBusinessData> {
    /**
     * 批量插入数据
     * @param list 要插入的数据列表
     */
    void batchInsert(@Param("list") List<DfdwJysjBusinessData> list);

    List<AccountsReceivableDto> getSummary(@Param("deptId") Integer deptId);


    List<ProjectNameAndCodeDto> getProjectNameAndCode();
}




