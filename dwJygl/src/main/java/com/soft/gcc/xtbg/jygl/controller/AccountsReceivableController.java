package com.soft.gcc.xtbg.jygl.controller;

import com.alibaba.fastjson.JSON;
import com.soft.framework.domain.AjaxResult;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjBusinessData;
import com.soft.gcc.xtbg.jygl.params.DfdwJysjBusinessDataParam;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjBusinessDataService;
import com.soft.gcc.xtbg.jygl.util.ExcelUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("accountsReceivable")
public class AccountsReceivableController extends BaseController {
    @Resource
    private DfdwJysjBusinessDataService businessDataService;

    @PostMapping("/getList")
    public Result<?> getList(@RequestBody DfdwJysjBusinessDataParam param){
        PersonEntity user = user();
        if (user == null){
            throw new RuntimeException("用户未登录");
        }
        return Result.ok(businessDataService.getList(param));
    }

    /**
     * 上传
     */
    @PostMapping("/importExcel")
    public Result<?> importExcel(MultipartFile file){
        return businessDataService.importExcel(file);
    }


    /**
     * 获取汇总列表
     */
    @GetMapping("/getSummary")
    public Result<?> getSummary(){
        PersonEntity user = user();
        if (user == null){
            return Result.error("用户未登录");
        }
        return Result.ok(businessDataService.getSummary());
    }


    /**
     * 获取工程名称工程编号
     */
    @GetMapping("/getProjectNameAndCode")
    public Result<?> getProjectNameAndCode(){
        return Result.ok(businessDataService.getProjectNameAndCode());
    }


    /**
     * 导出excel
     */
    @PostMapping("exportExcel")
    public void exportExcel(@RequestBody DfdwJysjBusinessDataParam param, HttpServletResponse response) throws IOException {
        PersonEntity user = user();
        if (user == null){
            throw new RuntimeException("用户未登录");
        }
        //设置 -1 让mybatis-plus 不分页
        param.setPageSize(-1);
        param.setPageNum(1);
        //poi 导出excel
        List<DfdwJysjBusinessData> excelList = businessDataService.getList(param).getRecords();
        String fileName = "应收账款数据" + System.currentTimeMillis() + ".xls";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        ExcelUtil.exportExcelX(excelList, null, "应收账款数据", DfdwJysjBusinessData.class, fileName, response);
    }




    /**
     * 导出excel模板
     * @return
     */
    @PostMapping("exportExcelTemplate")
//    @PreAuthorize("@ss.hasPermi('JDWJF01RYGL01QX09')")
    public void exportExcelTemplate( HttpServletResponse response) throws IOException {
        try {
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("应收账款数据导入模板");

            XSSFRow row = sheet.createRow(0);
            row = sheet.createRow(0);
            row.createCell(0).setCellValue("公司名称");
            row.createCell(1).setCellValue("单位");
            row.createCell(2).setCellValue("科目编号");
            row.createCell(3).setCellValue("科目名称");
            row.createCell(4).setCellValue("客户档案编号");
            row.createCell(5).setCellValue("客户档案");
            row.createCell(6).setCellValue("项目类型编号");
            row.createCell(7).setCellValue("项目类型名称");
            row.createCell(8).setCellValue("项目编号");
            row.createCell(9).setCellValue("项目名称");
            row.createCell(10).setCellValue("凭证号");
            row.createCell(11).setCellValue("金额");
            row.createCell(12).setCellValue("款项发生时间");
            row.createCell(13).setCellValue("账龄(天数)");
            row.createCell(14).setCellValue("账龄时间分段");
            row.createCell(15).setCellValue("单位来源");
            row.createCell(16).setCellValue("客商分类");
            row.createCell(17).setCellValue("添加年月");
            row.createCell(18).setCellValue("清除年月");
            row.createCell(19).setCellValue("摘要");

            //设置灰色背景
            XSSFCellStyle backgroundStyle = workbook.createCellStyle();
            // 设置背景色为25%灰色
            backgroundStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
            backgroundStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建白色字体
            XSSFFont whiteFont = workbook.createFont();
            whiteFont.setColor(IndexedColors.WHITE.getIndex());
//            whiteFont.setBold(true); // 可选：设置字体加粗

            // 将白色字体应用到样式
            backgroundStyle.setFont(whiteFont);

//            // 设置边框（可选）
//            backgroundStyle.setBorderTop(BorderStyle.THIN);
//            backgroundStyle.setBorderBottom(BorderStyle.THIN);
//            backgroundStyle.setBorderLeft(BorderStyle.THIN);
//            backgroundStyle.setBorderRight(BorderStyle.THIN);

            // 设置文字居中对齐（可选）
            backgroundStyle.setAlignment(HorizontalAlignment.CENTER);
            backgroundStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 应用样式到所有表头单元格
            for (int i = 0; i < 20; i++) {
                row.getCell(i).setCellStyle(backgroundStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("应收账款数据导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setCharacterEncoding("utf-8");

            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException e) {
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write(JSON.toJSONString(AjaxResult.error("导出Excel模板失败：" + e.getMessage())));
        }


    }
}
