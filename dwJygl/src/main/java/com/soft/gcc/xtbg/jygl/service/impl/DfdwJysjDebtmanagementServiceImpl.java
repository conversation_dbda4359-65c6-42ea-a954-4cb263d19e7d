package com.soft.gcc.xtbg.jygl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.dto.DebtManagementDto;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjDebtmanagement;
import com.soft.gcc.xtbg.jygl.mapper.DfdwJysjDebtmanagementMapper;
import com.soft.gcc.xtbg.jygl.params.DebtmanagementParams;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjDebtmanagementService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_DebtManagement(隐性债券数据)】的数据库操作Service实现
* @createDate 2025-07-15 16:15:14
*/
@Service
public class DfdwJysjDebtmanagementServiceImpl extends ServiceImpl<DfdwJysjDebtmanagementMapper, DfdwJysjDebtmanagement>
    implements DfdwJysjDebtmanagementService {

    @Resource
    private GroupitemService groupitemService;



    @Override
    public IPage<DfdwJysjDebtmanagement> getList(DebtmanagementParams debtmanagementParams, PersonEntity user) {
        boolean allAdmin = false;
        for (String roleVO : user.getRoleList()) {
            if ("经营分析-隐性债权-总管理".equals(roleVO)) {
                allAdmin = true;
                break;
            }
        }

        IPage<DfdwJysjDebtmanagement> page = new Page<>(debtmanagementParams.getPageNum(), debtmanagementParams.getPageSize());
        return page(page, new LambdaQueryWrapper<DfdwJysjDebtmanagement>()
                .like(StringUtils.isNotBlank(debtmanagementParams.getCompanyName()) && !debtmanagementParams.getIsSummary(),DfdwJysjDebtmanagement::getCompanyName,debtmanagementParams.getCompanyName())
                .eq(StringUtils.isNotBlank(debtmanagementParams.getCompanyName()) && debtmanagementParams.getIsSummary(),DfdwJysjDebtmanagement::getCompanyName,debtmanagementParams.getCompanyName())
                .like(StringUtils.isNotBlank(debtmanagementParams.getProjectCode()),DfdwJysjDebtmanagement::getProjectCode,debtmanagementParams.getProjectCode())
                .like(StringUtils.isNotBlank(debtmanagementParams.getProjectName()),DfdwJysjDebtmanagement::getProjectName,debtmanagementParams.getProjectName())
                .eq(!allAdmin,DfdwJysjDebtmanagement::getCompanyId,user.getTopGroupId())
                .orderByDesc(DfdwJysjDebtmanagement::getProjectCompletedDate)
        );
    }

    @Override
    public Result<Object> importExcel(MultipartFile file) {
        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);

            // 验证Excel是否有数据
            if (sheet.getLastRowNum() < 1) {
                return Result.error("Excel文件没有数据行");
            }

            // 验证标题行
            XSSFRow headerRow = sheet.getRow(0);
            String headerValidationResult = validateHeaders(headerRow);
            if (headerValidationResult != null) {
                return Result.error(headerValidationResult);
            }

            //excel中的数据
            List<DfdwJysjDebtmanagement> excelList = new ArrayList<>();

            //数据从第二行开始，第一行是标题
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                XSSFRow row = sheet.getRow(i);
                if (row == null) {
                    continue; // 跳过空行
                }

                // 检查行是否为空行（所有单元格都为空）
                if (isEmptyRow(row)) {
                    continue; // 跳过空行
                }

                DfdwJysjDebtmanagement entity = parseRowToEntity(row, i + 1);
                if (entity != null) {
                    excelList.add(entity);
                }
            }
            // 进行数据库操作
            if (!excelList.isEmpty()) {
                // 获取数据库中现有数据
                List<DfdwJysjDebtmanagement> debtmanagementList = list();
                //获取部门信息，用于匹配excel中的单位
                List<Groupitem> groupItemList = groupitemService.list();

                // 分离新增和修改数据
                List<DfdwJysjDebtmanagement> insertList = new ArrayList<>();
                List<DfdwJysjDebtmanagement> updateList = new ArrayList<>();

                // 创建projectCode的Map，提高查找效率
                Map<String, DfdwJysjDebtmanagement> existingDataMap = debtmanagementList.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getProjectCode()))
                        .collect(Collectors.toMap(DfdwJysjDebtmanagement::getProjectCode, item -> item, (v1, v2) -> v1));

                //创建uComapanyQC的map,提高查找效率
                Map<String, Groupitem> groupItemMap = groupItemList.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getUcomapanyqc()))
                        .collect(Collectors.toMap(Groupitem::getUcomapanyqc, item -> item, (v1, v2) -> v1));

                // 遍历Excel数据，判断新增还是修改
                for (DfdwJysjDebtmanagement excelItem : excelList) {
                    if (StringUtils.isNotBlank(excelItem.getProjectCode()) &&
                        existingDataMap.containsKey(excelItem.getProjectCode())) {
                        // 存在于数据库中，设置ID后放入修改列表
                        DfdwJysjDebtmanagement existingItem = existingDataMap.get(excelItem.getProjectCode());
                        Groupitem groupItem = groupItemMap.get(excelItem.getCompanyName());
                        if (groupItem != null) {
                            excelItem.setCompanyId(groupItem.getId());
                            excelItem.setGroupItemName(groupItem.getGroupname());
                        }
                        excelItem.setId(existingItem.getId());
                        updateList.add(excelItem);
                    } else {
                        // 不存在于数据库中，清空ID后放入新增列表
                        excelItem.setId(null);
                        Groupitem groupItem = groupItemMap.get(excelItem.getCompanyName());
                        if (groupItem != null) {
                            excelItem.setCompanyId(groupItem.getId());
                            excelItem.setGroupItemName(groupItem.getGroupname());
                        }
                        insertList.add(excelItem);
                    }
                }

                // 执行数据库操作
                int insertCount = 0;
                int updateCount = 0;

                // 批量新增（每次50条，SQL Server参数限制）
                if (!insertList.isEmpty()) {
                    batchInsert(insertList);
                    insertCount = insertList.size();
                }

                // 批量修改
                if (!updateList.isEmpty()) {
                    updateBatchById(updateList);
                    updateCount = updateList.size();
                }

                return Result.ok("导入成功! 新增" + insertCount + "条，修改" + updateCount + "条数据");
            } else {
                return Result.error("没有有效的数据可导入");
            }

        } catch (Exception e) {
            log.error("隐形债券明细导入失败:" + e, e);
            return Result.error("导入失败：" + e);
        }
    }

    @Override
    public List<DebtManagementDto> getSummary(PersonEntity user) {
        try {
            boolean allAdmin = false;
            for (String roleVO : user.getRoleList()) {
                if ("经营分析-隐性债权-总管理".equals(roleVO)) {
                    allAdmin = true;
                    break;
                }
            }

            //首先获取所有的数据
            List<DfdwJysjDebtmanagement> list = list(new LambdaQueryWrapper<DfdwJysjDebtmanagement>()
                    .eq(!allAdmin,DfdwJysjDebtmanagement::getCompanyId,user.getTopGroupId())
            );
            //用于展示的汇总数据
            List<DebtManagementDto> dtos = new ArrayList<>();
            if (!list.isEmpty()) {
                // 获取当前日期
                Date currentDate = new Date();

                // 按单位名称分组
                Map<String, List<DfdwJysjDebtmanagement>> companyGroupMap = list.stream()
                    .filter(item -> item.getGroupItemName() != null && !item.getGroupItemName().trim().isEmpty())
                    .collect(Collectors.groupingBy(DfdwJysjDebtmanagement::getGroupItemName));

                // 遍历每个单位分组
                for (Map.Entry<String, List<DfdwJysjDebtmanagement>> entry : companyGroupMap.entrySet()) {
                    String companyName = entry.getKey();
                    List<DfdwJysjDebtmanagement> companyData = entry.getValue();

                    DebtManagementDto dto = new DebtManagementDto();
                    dto.setCompanyName(companyName);

                    // 设置companyNameQC字段，取第一条记录的companyName值
                    if (!companyData.isEmpty()) {
                        dto.setCompanyNameQC(companyData.get(0).getCompanyName());
                        dto.setCompanyId(companyData.get(0).getCompanyId());
                    }

                    // 初始化各时间段的统计数据
                    int sixMonCount = 0, oneYearCount = 0, oneToTwoCount = 0, twoToThreeCount = 0, threeAboveCount = 0;
                    BigDecimal sixMonAmount = BigDecimal.ZERO, oneYearAmount = BigDecimal.ZERO,
                              oneToTwoAmount = BigDecimal.ZERO, twoToThreeAmount = BigDecimal.ZERO,
                              threeAboveAmount = BigDecimal.ZERO;

                    // 遍历该单位的所有项目数据
                    for (DfdwJysjDebtmanagement item : companyData) {
                        Date projectCompletedDate = item.getProjectCompletedDate();
                        BigDecimal contractAmount = item.getContractAmount() != null ? item.getContractAmount() : BigDecimal.ZERO;

                        if (projectCompletedDate != null) {
                            // 计算项目完工时间与当前日期的天数差
                            long diffInMillis = currentDate.getTime() - projectCompletedDate.getTime();
                            long daysDiff = diffInMillis / (24 * 60 * 60 * 1000);

                            // 根据天数差进行分类统计
                            if (daysDiff >= 0 && daysDiff < 182) {
                                // 六个月以内
                                sixMonCount++;
                                sixMonAmount = sixMonAmount.add(contractAmount);
                            } else if (daysDiff >= 182 && daysDiff < 365) {
                                // 一年以内
                                oneYearCount++;
                                oneYearAmount = oneYearAmount.add(contractAmount);
                            } else if (daysDiff >= 365 && daysDiff < 730) {
                                // 一到两年
                                oneToTwoCount++;
                                oneToTwoAmount = oneToTwoAmount.add(contractAmount);
                            } else if (daysDiff >= 730 && daysDiff < 1095) {
                                // 两到三年
                                twoToThreeCount++;
                                twoToThreeAmount = twoToThreeAmount.add(contractAmount);
                            } else if (daysDiff >= 1095) {
                                // 三年以上
                                threeAboveCount++;
                                threeAboveAmount = threeAboveAmount.add(contractAmount);
                            }
                        }
                    }

                    // 设置统计结果到DTO
                    dto.setSixMonCount(sixMonCount);
                    dto.setSixMonAmount(sixMonAmount);
                    dto.setOneYearCount(oneYearCount);
                    dto.setOneYearAmount(oneYearAmount);
                    dto.setOneToTwoCount(oneToTwoCount);
                    dto.setOneToTwoAmount(oneToTwoAmount);
                    dto.setTwoToThreeCount(twoToThreeCount);
                    dto.setTwoToThreeAmount(twoToThreeAmount);
                    dto.setThreeAboveCount(threeAboveCount);
                    dto.setThreeAboveAmount(threeAboveAmount);

                    // 台账数暂时不计算，按需求设置为0
                    dto.setSixMonLedger(0);
                    dto.setOneYearLedger(0);
                    dto.setOneToTwoLedger(0);
                    dto.setTwoToThreeLedger(0);
                    dto.setThreeAboveLedger(0);

                    dtos.add(dto);
                }
                if (!dtos.isEmpty()) {
                    List<Groupitem> groupItemList = groupitemService.list();
                    //根据dto中的companyId查找到相应的groupItem，然后使用GroupItem中的XH对dtos进行排序
                    
                    // 创建companyId到GroupItem的映射，提高查找效率
                    Map<Integer, Groupitem> groupItemMap = groupItemList.stream()
                            .filter(item -> item.getId() != null)
                            .collect(Collectors.toMap(Groupitem::getId, item -> item, (v1, v2) -> v1));
                    
                    // 使用GroupItem中的XH字段对dtos进行排序
                    dtos.sort((dto1, dto2) -> {
                        Groupitem group1 = groupItemMap.get(dto1.getCompanyId());
                        Groupitem group2 = groupItemMap.get(dto2.getCompanyId());
                        
                        // 获取XH值，如果为null则使用Integer.MAX_VALUE作为默认值（排在最后）
                        Integer xh1 = (group1 != null && group1.getXh() != null) ? group1.getXh() : Integer.MAX_VALUE;
                        Integer xh2 = (group2 != null && group2.getXh() != null) ? group2.getXh() : Integer.MAX_VALUE;
                        
                        // 按XH升序排序
                        return xh1.compareTo(xh2);
                    });

                }
            }
            return dtos;
        } catch (Exception e) {
            throw new RuntimeException("获取隐形债权汇总失败:" + e, e);
        }
    }

    @Override
    public List<DfdwJysjDebtmanagement> getExcelList(DebtmanagementParams param, PersonEntity user) {

        boolean allAdmin = false;
        for (String roleVO : user.getRoleList()) {
            if ("经营分析-隐性债权-总管理".equals(roleVO)) {
                allAdmin = true;
                break;
            }
        }

        return list(new LambdaQueryWrapper<DfdwJysjDebtmanagement>()
                .like(StringUtils.isNotBlank(param.getCompanyName()) && !param.getIsSummary(),DfdwJysjDebtmanagement::getCompanyName,param.getCompanyName())
                .eq(StringUtils.isNotBlank(param.getCompanyName()) && param.getIsSummary(),DfdwJysjDebtmanagement::getCompanyName,param.getCompanyName())
                .like(StringUtils.isNotBlank(param.getProjectCode()),DfdwJysjDebtmanagement::getProjectCode,param.getProjectCode())
                .like(StringUtils.isNotBlank(param.getProjectName()),DfdwJysjDebtmanagement::getProjectName,param.getProjectName())
                .eq(!allAdmin,DfdwJysjDebtmanagement::getCompanyId,user.getTopGroupId())
                .orderByDesc(DfdwJysjDebtmanagement::getProjectCompletedDate)
        );
    }

    /**
     * 验证Excel标题行是否正确
     */
    private String validateHeaders(XSSFRow headerRow) {
        if (headerRow == null) {
            return "标题行不能为空";
        }

        // 定义期望的标题列表（按顺序）
        String[] expectedHeaders = {
            "集团编码", "集团名称", "单位编码", "单位名称", "承揽部门", "项目编码", "项目名称",
            "是否期初", "项目类型", "项目状态", "客户名称", "工程客户性质", "项目完工时间",
            "项目完工时间分类-按年", "项目完工时间分类-按月", "清理状态", "合同金额（元）",
            "送审时间", "送审金额（元）", "审定时间", "审定金额（元）",
            "业+应用前已开票并已收款金额（元）", "业+应用前已开票但未收款金额（元）",
            "ERP系统开票金额-财务凭证（元）", "累计开票金额（元）", "隐性债权待清理金额（元）",
            "上月隐性债权清理金额（元）", "本月隐性债权清理金额（元）", "本年隐性债权清理金额（元）"
        };

        // 检查标题数量
        if (headerRow.getLastCellNum() < expectedHeaders.length) {
            return "Excel标题列数不足，期望" + expectedHeaders.length + "列，实际" + headerRow.getLastCellNum() + "列";
        }

        // 检查每个标题是否匹配
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String actualHeader = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(actualHeader)) {
                return "第" + (i + 1) + "列标题不匹配，期望：" + expectedHeaders[i] + "，实际：" + actualHeader;
            }
        }

        return null; // 验证通过
    }

    /**
     * 将Excel行数据解析为实体对象
     */
    private DfdwJysjDebtmanagement parseRowToEntity(XSSFRow row, int rowNum) {
        DfdwJysjDebtmanagement entity = new DfdwJysjDebtmanagement();

        // 按列顺序解析数据
        entity.setGroupCode(getCellStringValue(row.getCell(0), rowNum, "集团编码"));
        entity.setGroupName(getCellStringValue(row.getCell(1), rowNum, "集团名称"));
        entity.setCompanyCode(getCellStringValue(row.getCell(2), rowNum, "单位编码"));
        entity.setCompanyName(getCellStringValue(row.getCell(3), rowNum, "单位名称"));
        entity.setContractingDept(getCellStringValue(row.getCell(4), rowNum, "承揽部门"));
        entity.setProjectCode(getCellStringValue(row.getCell(5), rowNum, "项目编码"));
        entity.setProjectName(getCellStringValue(row.getCell(6), rowNum, "项目名称"));

        // 是否期初（转换为Boolean）
        String beginningStr = getCellStringValue(row.getCell(7), rowNum, "是否期初");
        entity.setBeginning(parseBoolean(beginningStr));

        entity.setProjectType(getCellStringValue(row.getCell(8), rowNum, "项目类型"));
        entity.setProjectStatus(getCellStringValue(row.getCell(9), rowNum, "项目状态"));
        entity.setCustomerName(getCellStringValue(row.getCell(10), rowNum, "客户名称"));
        entity.setCustomerNature(getCellStringValue(row.getCell(11), rowNum, "工程客户性质"));

        // 项目完工时间
        entity.setProjectCompletedDate(parseDate(getCellStringValue(row.getCell(12), rowNum, "项目完工时间"), rowNum, "项目完工时间"));

        entity.setProjectCompletedTypeByYear(getCellStringValue(row.getCell(13), rowNum, "项目完工时间分类-按年"));
        entity.setProjectCompletedTypeByMonth(getCellStringValue(row.getCell(14), rowNum, "项目完工时间分类-按月"));

        // 清理状态 - 直接使用Excel中的原始数据
        entity.setCleanType(getCellStringValue(row.getCell(15), rowNum, "清理状态"));

        // 金额字段
        entity.setContractAmount(parseBigDecimal(getCellStringValue(row.getCell(16), rowNum, "合同金额（元）"), rowNum, "合同金额（元）"));
        entity.setForReviewTime(parseDate(getCellStringValue(row.getCell(17), rowNum, "送审时间"), rowNum, "送审时间"));
        entity.setForReviewAmount(parseBigDecimal(getCellStringValue(row.getCell(18), rowNum, "送审金额（元）"), rowNum, "送审金额（元）"));
        entity.setReviewTime(parseDate(getCellStringValue(row.getCell(19), rowNum, "审定时间"), rowNum, "审定时间"));
        entity.setReviewAmount(parseBigDecimal(getCellStringValue(row.getCell(20), rowNum, "审定金额（元）"), rowNum, "审定金额（元）"));
        entity.setInvoiceReceivedTakeAmount(parseBigDecimal(getCellStringValue(row.getCell(21), rowNum, "业+应用前已开票并已收款金额（元）"), rowNum, "业+应用前已开票并已收款金额（元）"));
        entity.setInvoiceReceivedNoTakeAmount(parseBigDecimal(getCellStringValue(row.getCell(22), rowNum, "业+应用前已开票但未收款金额（元）"), rowNum, "业+应用前已开票但未收款金额（元）"));
        entity.setErpFinancialVouchersAmountd(parseBigDecimal(getCellStringValue(row.getCell(23), rowNum, "ERP系统开票金额-财务凭证（元）"), rowNum, "ERP系统开票金额-财务凭证（元）"));
        entity.setCumulativeInvoicingAmount(parseBigDecimal(getCellStringValue(row.getCell(24), rowNum, "累计开票金额（元）"), rowNum, "累计开票金额（元）"));
        entity.setToBeCleanedAmount(parseBigDecimal(getCellStringValue(row.getCell(25), rowNum, "隐性债权待清理金额（元）"), rowNum, "隐性债权待清理金额（元）"));
        entity.setLastMonthCleanUpAmount(parseBigDecimal(getCellStringValue(row.getCell(26), rowNum, "上月隐性债权清理金额（元）"), rowNum, "上月隐性债权清理金额（元）"));
        entity.setThisMonthCleanUpAmount(parseBigDecimal(getCellStringValue(row.getCell(27), rowNum, "本月隐性债权清理金额（元）"), rowNum, "本月隐性债权清理金额（元）"));
        entity.setThisYearCleanUpAmount(parseBigDecimal(getCellStringValue(row.getCell(28), rowNum, "本年隐性债权清理金额（元）"), rowNum, "本年隐性债权清理金额（元）"));

        return entity;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return StringUtils.trimToNull(cell.getStringCellValue());
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                        return sdf.format(cell.getDateCellValue());
                    } else {
                        // 数字转字符串，避免科学计数法
                        double numericValue = cell.getNumericCellValue();
                        if (numericValue == (long) numericValue) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    // 公式单元格，尝试获取计算结果
                    try {
                        return StringUtils.trimToNull(cell.getStringCellValue());
                    } catch (Exception e) {
                        try {
                            double numericValue = cell.getNumericCellValue();
                            if (numericValue == (long) numericValue) {
                                return String.valueOf((long) numericValue);
                            } else {
                                return String.valueOf(numericValue);
                            }
                        } catch (Exception ex) {
                            return null;
                        }
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            throw new RuntimeException("单元格读取失败");
        }
    }

    /**
     * 获取单元格字符串值（带错误定位）
     */
    private String getCellStringValue(Cell cell, int rowNum, String fieldName) {
        try {
            return getCellStringValue(cell);
        } catch (Exception e) {
            throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
        }
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr, int rowNum, String fieldName) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        // 按优先级尝试四种日期格式
        String[] dateFormats = {"yyyy/MM/dd", "yyyy-MM-dd", "yyyy/MM/dd HH:mm:ss", "yyyy-MM-dd HH:mm:ss"};
        for (String format : dateFormats) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                return sdf.parse(dateStr.trim());
            } catch (Exception ignored) {
                // 继续尝试下一种格式
            }
        }

        // 所有格式都失败，抛出异常
        throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
    }

    /**
     * 解析BigDecimal字符串
     */
    private BigDecimal parseBigDecimal(String numStr, int rowNum, String fieldName) {
        if (StringUtils.isBlank(numStr)) {
            return null;
        }

        try {
            // 移除可能的货币符号和逗号
            String cleanStr = numStr.trim().replaceAll("[￥$,]", "");
            return new BigDecimal(cleanStr);
        } catch (Exception e) {
            throw new RuntimeException("第" + rowNum + "行【" + fieldName + "】字段格式错误");
        }
    }

    /**
     * 解析布尔值
     */
    private Boolean parseBoolean(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        value = value.trim();
        if ("是".equals(value)) {
            return true;
        } else if ("否".equals(value)) {
            return false;
        }

        return null;
    }

    /**
     * 判断行是否为空行
     */
    private boolean isEmptyRow(XSSFRow row) {
        if (row == null) {
            return true;
        }

        // 检查前29列（我们需要的列数），如果都为空则认为是空行
        for (int i = 0; i < 29; i++) {
            Cell cell = row.getCell(i);
            if (cell != null && StringUtils.isNotBlank(getCellStringValue(cell))) {
                return false; // 有非空单元格，不是空行
            }
        }

        return true; // 所有单元格都为空，是空行
    }

    /**
     * 批量插入数据，每次50条（SQL Server参数限制2100个，29字段*50条=1450个参数）
     */
    private void batchInsert(List<DfdwJysjDebtmanagement> insertList) {
        int batchSize = 50;  // SQL Server参数限制，29字段*50条=1450个参数 < 2100
        for (int i = 0; i < insertList.size(); i += batchSize) {
            List<DfdwJysjDebtmanagement> batchList = insertList.subList(i, Math.min(i + batchSize, insertList.size()));
            baseMapper.batchInsert(batchList);
        }
    }


}
