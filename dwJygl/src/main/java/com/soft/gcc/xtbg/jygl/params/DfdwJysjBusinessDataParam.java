package com.soft.gcc.xtbg.jygl.params;

import lombok.Data;

import java.io.Serializable;

@Data
public class DfdwJysjBusinessDataParam implements Serializable {
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 单位
     */
    private String unit;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 是否是汇总页面调过来的搜索条件
     */
    private Integer isSummary;


    /**
     * 是否涉诉  0 否 1 是
     */
    private Integer isInLawsuit;
}
