package com.soft.gcc.xtbg.jygl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 经营数据-经营数据管理
 * @TableName DFDW_JYSJ_Business_data
 */
@TableName(value ="DFDW_JYSJ_Business_data")
@Data
public class DfdwJysjBusinessData {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称",width = 25)
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 单位
     */
    @Excel(name = "单位",width = 15)
    @TableField(value = "unit")
    private String unit;

    /**
     * 科目编号
     */
    @Excel(name = "科目编号",width = 15)
    @TableField(value = "subject_number")
    private String subjectNumber;

    /**
     * 科目名称
     */
    @Excel(name = "科目名称",width = 15)
    @TableField(value = "subject_name")
    private String subjectName;

    /**
     * 客户档案编号
     */
    @Excel(name = "客户档案编号",width = 35)
    @TableField(value = "customer_file_number")
    private String customerFileNumber;

    /**
     * 客户档案
     */
    @Excel(name = "客户档案",width = 35)
    @TableField(value = "customer")
    private String customer;

    /**
     * 项目类型编号
     */
    @Excel(name = "项目类型编号",width = 30)
    @TableField(value = "project_type_code")
    private String projectTypeCode;

    /**
     * 项目类型名称
     */
    @Excel(name = "项目类型名称",width = 30)
    @TableField(value = "project_type_name")
    private String projectTypeName;

    /**
     * 项目编号
     */
    @Excel(name = "项目编号",width = 30)
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称",width = 40)
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 转账凭证
     */
    @Excel(name = "凭证号",width = 30)
    @TableField(value = "transfer_voucher")
    private String transferVoucher;

    /**
     * 金额
     */
    @Excel(name = "金额",width = 20)
    @TableField(value = "money")
    private BigDecimal money;

    /**
     * 款项发生时间
     */
    @Excel(name = "款项发生时间",format = "yyyy-MM-dd", timezone = "GMT+8",width = 20)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @JSONField(format="yyyy-MM-dd")
    @TableField(value = "transfer_time")
    private Date transferTime;

    /**
     * 账龄（天数
     */
    @Excel(name = "账龄(天数)",width = 20)
    @TableField(value = "aging_of_accounts")
    private String agingOfAccounts;

    /**
     * 账龄时间分段
     */
    @Excel(name = "账龄时间分段",width = 20)
    @TableField(value = "aging_buckets")
    private String agingBuckets;

    /**
     * 单位来源
     */
    @Excel(name = "单位来源",width = 15)
    @TableField(value = "funding_source")
    private String fundingSource;

    /**
     * 客商分类
     */
    @Excel(name = "客商分类",width = 15)
    @TableField(value = "business_partner_classification")
    private String businessPartnerClassification;

    /**
     * 添加年月
     */
    @Excel(name = "添加年月",format = "yyyy-MM", timezone = "GMT+8")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    @JSONField(format="yyyy-MM")
    @TableField(value = "add_time")
    private Date addTime;

    /**
     * 清除年月
     */
    @Excel(name = "清除年月")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM")
    @JSONField(format="yyyy-MM")
    @TableField(value = "remove_time")
    private Date removeTime;

    /**
     * 摘要
     */
    @Excel(name = "摘要",width = 30)
    @TableField(value = "remark")
    private String remark;

    /**
     * 部门id
     */
    @TableField(value = "topGroupId")
    private Integer topGroupId;

    /**
     * 收回金额
     */
    @TableField(value = "recovered_amount")
    private BigDecimal recoveredAmount;

    @TableField(value = "recovered_time")
    private Date recoveredTime;

    /**
     * 状态 0 开启 1 关闭
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 总催收次数
     */
    @TableField(value = "collection_counts")
    private String collectionCounts;

    /**
     * 最近一次催收时间
     */
    @TableField(value = "last_collection_time")
    private Date lastCollectionTime;



    /**
     * 是否涉诉  0 否 1 是
     */
    @TableField(value = "is_inLawsuit")
    private Integer isInLawsuit;

    /**
     *
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     *
     */
    @TableField(value = "enable")
    private String enable;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "update_by")
    private Date updateBy;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DfdwJysjBusinessData other = (DfdwJysjBusinessData) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
                && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
                && (this.getSubjectNumber() == null ? other.getSubjectNumber() == null : this.getSubjectNumber().equals(other.getSubjectNumber()))
                && (this.getSubjectName() == null ? other.getSubjectName() == null : this.getSubjectName().equals(other.getSubjectName()))
                && (this.getCustomerFileNumber() == null ? other.getCustomerFileNumber() == null : this.getCustomerFileNumber().equals(other.getCustomerFileNumber()))
                && (this.getCustomer() == null ? other.getCustomer() == null : this.getCustomer().equals(other.getCustomer()))
                && (this.getProjectTypeCode() == null ? other.getProjectTypeCode() == null : this.getProjectTypeCode().equals(other.getProjectTypeCode()))
                && (this.getProjectTypeName() == null ? other.getProjectTypeName() == null : this.getProjectTypeName().equals(other.getProjectTypeName()))
                && (this.getProjectCode() == null ? other.getProjectCode() == null : this.getProjectCode().equals(other.getProjectCode()))
                && (this.getProjectName() == null ? other.getProjectName() == null : this.getProjectName().equals(other.getProjectName()))
                && (this.getTransferVoucher() == null ? other.getTransferVoucher() == null : this.getTransferVoucher().equals(other.getTransferVoucher()))
                && (this.getMoney() == null ? other.getMoney() == null : this.getMoney().equals(other.getMoney()))
                && (this.getTransferTime() == null ? other.getTransferTime() == null : this.getTransferTime().equals(other.getTransferTime()))
                && (this.getAgingOfAccounts() == null ? other.getAgingOfAccounts() == null : this.getAgingOfAccounts().equals(other.getAgingOfAccounts()))
                && (this.getAgingBuckets() == null ? other.getAgingBuckets() == null : this.getAgingBuckets().equals(other.getAgingBuckets()))
                && (this.getFundingSource() == null ? other.getFundingSource() == null : this.getFundingSource().equals(other.getFundingSource()))
                && (this.getBusinessPartnerClassification() == null ? other.getBusinessPartnerClassification() == null : this.getBusinessPartnerClassification().equals(other.getBusinessPartnerClassification()))
                && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
                && (this.getCollectionCounts() == null ? other.getCollectionCounts() == null : this.getCollectionCounts().equals(other.getCollectionCounts()))
                && (this.getLastCollectionTime() == null ? other.getLastCollectionTime() == null : this.getLastCollectionTime().equals(other.getLastCollectionTime()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
                && (this.getEnable() == null ? other.getEnable() == null : this.getEnable().equals(other.getEnable()))
                && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
                && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getSubjectNumber() == null) ? 0 : getSubjectNumber().hashCode());
        result = prime * result + ((getSubjectName() == null) ? 0 : getSubjectName().hashCode());
        result = prime * result + ((getCustomerFileNumber() == null) ? 0 : getCustomerFileNumber().hashCode());
        result = prime * result + ((getCustomer() == null) ? 0 : getCustomer().hashCode());
        result = prime * result + ((getProjectTypeCode() == null) ? 0 : getProjectTypeCode().hashCode());
        result = prime * result + ((getProjectTypeName() == null) ? 0 : getProjectTypeName().hashCode());
        result = prime * result + ((getProjectCode() == null) ? 0 : getProjectCode().hashCode());
        result = prime * result + ((getProjectName() == null) ? 0 : getProjectName().hashCode());
        result = prime * result + ((getTransferVoucher() == null) ? 0 : getTransferVoucher().hashCode());
        result = prime * result + ((getMoney() == null) ? 0 : getMoney().hashCode());
        result = prime * result + ((getTransferTime() == null) ? 0 : getTransferTime().hashCode());
        result = prime * result + ((getAgingOfAccounts() == null) ? 0 : getAgingOfAccounts().hashCode());
        result = prime * result + ((getAgingBuckets() == null) ? 0 : getAgingBuckets().hashCode());
        result = prime * result + ((getFundingSource() == null) ? 0 : getFundingSource().hashCode());
        result = prime * result + ((getBusinessPartnerClassification() == null) ? 0 : getBusinessPartnerClassification().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getCollectionCounts() == null) ? 0 : getCollectionCounts().hashCode());
        result = prime * result + ((getLastCollectionTime() == null) ? 0 : getLastCollectionTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getEnable() == null) ? 0 : getEnable().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyName=").append(companyName);
        sb.append(", unit=").append(unit);
        sb.append(", subjectNumber=").append(subjectNumber);
        sb.append(", subjectName=").append(subjectName);
        sb.append(", customerFileNumber=").append(customerFileNumber);
        sb.append(", customer=").append(customer);
        sb.append(", projectTypeCode=").append(projectTypeCode);
        sb.append(", projectTypeName=").append(projectTypeName);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", projectName=").append(projectName);
        sb.append(", transferVoucher=").append(transferVoucher);
        sb.append(", money=").append(money);
        sb.append(", transferTime=").append(transferTime);
        sb.append(", agingOfAccounts=").append(agingOfAccounts);
        sb.append(", agingBuckets=").append(agingBuckets);
        sb.append(", fundingSource=").append(fundingSource);
        sb.append(", businessPartnerClassification=").append(businessPartnerClassification);
        sb.append(", addTime=").append(addTime);
        sb.append(", collectionCounts=").append(collectionCounts);
        sb.append(", lastCollectionTime=").append(lastCollectionTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", enable=").append(enable);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append("]");
        return sb.toString();
    }
}