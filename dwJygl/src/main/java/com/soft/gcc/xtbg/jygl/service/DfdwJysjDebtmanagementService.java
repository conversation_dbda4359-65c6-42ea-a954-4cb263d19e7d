package com.soft.gcc.xtbg.jygl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.dto.DebtManagementDto;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjDebtmanagement;
import com.soft.gcc.xtbg.jygl.params.DebtmanagementParams;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_DebtManagement(隐性债券数据)】的数据库操作Service
* @createDate 2025-07-15 16:15:14
*/
public interface DfdwJysjDebtmanagementService extends IService<DfdwJysjDebtmanagement> {

    /**
     * 获取列表
     * @param debtmanagementParams
     * @return
     */
    IPage<DfdwJysjDebtmanagement> getList(DebtmanagementParams debtmanagementParams, PersonEntity user);

    /**
     * 导入
     * @param file
     * @return
     */
    Result<Object> importExcel(MultipartFile file);

    /**
     * 获取汇总列表
     * @return
     */
    List<DebtManagementDto> getSummary(PersonEntity user);

    List<DfdwJysjDebtmanagement> getExcelList(DebtmanagementParams param, PersonEntity user);
}
