package com.soft.gcc.xtbg.jygl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjAssociated;
import com.soft.gcc.xtbg.jygl.params.DebtmanagementParams;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
* <AUTHOR>
* @description 针对表【DFDW_JYSJ_Associated(经营数据-往来户单位维护)】的数据库操作Service
* @createDate 2025-07-15 14:09:10
*/
public interface DfdwJysjAssociatedService extends IService<DfdwJysjAssociated> {

    IPage<DfdwJysjAssociated> getList(DebtmanagementParams params);

    void exportExcelTemplate(HttpServletResponse response) throws IOException;

    Result<?> importExcel(MultipartFile file);
}
