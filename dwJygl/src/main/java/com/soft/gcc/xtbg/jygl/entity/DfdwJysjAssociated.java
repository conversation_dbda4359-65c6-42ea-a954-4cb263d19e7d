package com.soft.gcc.xtbg.jygl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 经营数据-往来户单位维护
 * @TableName DFDW_JYSJ_Associated
 */
@TableName(value ="DFDW_JYSJ_Associated")
@Data
public class DfdwJysjAssociated {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 往来户单位名称
     */
    @TableField(value = "associated_units")
    private String associatedUnits;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "USCC")
    private String uscc;

    /**
     * 联系人
     */
    @TableField(value = "contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField(value = "contact_person_number")
    private String contactPersonNumber;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DfdwJysjAssociated other = (DfdwJysjAssociated) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getAssociatedUnits() == null ? other.getAssociatedUnits() == null : this.getAssociatedUnits().equals(other.getAssociatedUnits()))
                && (this.getUscc() == null ? other.getUscc() == null : this.getUscc().equals(other.getUscc()))
                && (this.getContactPerson() == null ? other.getContactPerson() == null : this.getContactPerson().equals(other.getContactPerson()))
                && (this.getContactPersonNumber() == null ? other.getContactPersonNumber() == null : this.getContactPersonNumber().equals(other.getContactPersonNumber()))
                && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAssociatedUnits() == null) ? 0 : getAssociatedUnits().hashCode());
        result = prime * result + ((getUscc() == null) ? 0 : getUscc().hashCode());
        result = prime * result + ((getContactPerson() == null) ? 0 : getContactPerson().hashCode());
        result = prime * result + ((getContactPersonNumber() == null) ? 0 : getContactPersonNumber().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", associatedUnits=").append(associatedUnits);
        sb.append(", uscc=").append(uscc);
        sb.append(", contactPerson=").append(contactPerson);
        sb.append(", contactPersonNumber=").append(contactPersonNumber);
        sb.append(", email=").append(email);
        sb.append("]");
        return sb.toString();
    }
}