package com.soft.gcc.xtbg.jygl.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 隐性债权汇总
 */
@Data
public class DebtManagementDto implements Serializable {

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 单位名称QC（来自DfdwJysjDebtmanagement的companyName字段）
     */
    private String companyNameQC;

    /**
     * 六个月以内个数
     */
    private Integer sixMonCount;

    /**
     * 六个月以内金额
     */
    private BigDecimal sixMonAmount;

    /**
     * 六个月以内台账数
     */
    private Integer sixMonLedger;

    /**
     * 一年以内个数
     */
    private Integer oneYearCount;

    /**
     * 一年以内金额
     */
    private BigDecimal oneYearAmount;

    /**
     * 一年以内台账数
     */
    private Integer oneYearLedger;

    /**
     * 一到两年个数
     */
    private Integer oneToTwoCount;

    /**
     * 一到两年金额
     */
    private BigDecimal oneToTwoAmount;

    /**
     * 一到两年台账数
     */
    private Integer oneToTwoLedger;

    /**
     * 两到三年个数
     */
    private Integer twoToThreeCount;

    /**
     * 两到三年金额
     */
    private BigDecimal twoToThreeAmount;

    /**
     * 两到三年台账数
     */
    private Integer twoToThreeLedger;

    /**
     * 三年以上个数
     */
    private Integer threeAboveCount;

    /**
     * 三年以上金额
     */
    private BigDecimal threeAboveAmount;

    /**
     * 三年以上台账数
     */
    private Integer threeAboveLedger;

    /**
     * 单位id，关联的groupItem的id
     */
    private Integer companyId;

}
