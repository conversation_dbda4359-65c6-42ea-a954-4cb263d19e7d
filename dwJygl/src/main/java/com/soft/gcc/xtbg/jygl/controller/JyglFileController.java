package com.soft.gcc.xtbg.jygl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.util.AliyunOSSUtils;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 */
@RequestMapping("/jygl/file")
@RestController
public class JyglFileController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(JyglFileController.class);
    @Autowired
    TFileService tFileService;

    @RequestMapping("/upload")
    public Result<Object> upload(MultipartFile file, HttpServletRequest request, String type, String hjID) {
        PersonEntity person = user();
        try {
            if (null == file) {
                file = ((StandardMultipartHttpServletRequest) request).getMultiFileMap().get("uploadFile").get(0);
            }
            LocalDate localDate = LocalDate.now();
            String YEAR_MONTH = localDate.getYear() + "/" + localDate.getMonthValue() + "/";
            String path = "Upload/FileManage/Jygl/" + type + "/" + YEAR_MONTH;
            String hz = FilenameUtils.getExtension(file.getOriginalFilename()).toLowerCase();
            String fileName = UUID.randomUUID() + "." + hz;
            String url = path + AliyunOSSUtils.uploadFile(path, fileName, file);
            String name = file.getOriginalFilename();
            TFile sf = new TFile();
            //type
            sf.setFilename(name);
            sf.setFilepath(url);
            sf.setUploaddate(new Date());
            sf.setType(type);
            sf.setPersonname(person.getRealName());
            sf.setPersonzgh(person.getLoginName());
            //lcDefine.LcID
            sf.setFunctionid(20017);
            //lcjd.lcjdID
            sf.setHjid(hjID);
            //1 2
            sf.setSubtname(type);
            if (tFileService.save(sf)) {
                return Result.ok(sf, "上传文件成功！");
            } else {
                return Result.error("上传文件失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/downloadImportExample")
    public Result<Object> upload(@RequestBody Map<String, Object> map, HttpServletResponse response) throws IOException {
        OutputStream outputStream = null;
        try (BufferedInputStream bis = new BufferedInputStream(new ClassPathResource("\\static\\jygl\\" + map.get("name").toString()).getInputStream())) {
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int i = bis.read(buffer);
            while (i != -1) {
                outputStream.write(buffer, 0, i);
                i = bis.read(buffer);
            }
            return Result.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        } finally {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        }
    }

    @GetMapping("/getFileName")
    public Result<Object> getFileName(String filePath) {
        List<TFile> list = tFileService.list(new LambdaQueryWrapper<TFile>().eq(TFile::getFilepath, filePath));
        if (!list.isEmpty()) {
            return Result.ok(list.get(0).getFilename());
        } else {
            return Result.error("文件不存在！");
        }
    }

    @PostMapping("/getFileList")
    public Result<Object> getFileList(@RequestBody Map<String, Object> params){
        if(Objects.isNull(params.get("ids"))){
            return Result.error("参数错误");
        }
        String ids = (String) params.get("ids");
        //转lsit
        List<String> idList = Arrays.asList(ids.split(","));
        List<TFile> list = tFileService.list(new LambdaQueryWrapper<TFile>().in(TFile::getId,idList ));
        return Result.ok(list);
    }



    @GetMapping("/download")
    public Result<Object> download(String filePath, HttpServletResponse response) {
        if ( AliyunOSSUtils.existsByFileName(filePath)) {
            byte[] result = AliyunOSSUtils.downloadFileStream(filePath);
            if(result == null){
                return Result.error("查询数据失败!");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=" + FilenameUtils.getName(filePath));
            try (OutputStream outputStream = response.getOutputStream()) {
                // 写入数据
                outputStream.write(result);
                outputStream.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return Result.ok();
        }else{
            return Result.error("查询数据失败");
        }
    }
}
