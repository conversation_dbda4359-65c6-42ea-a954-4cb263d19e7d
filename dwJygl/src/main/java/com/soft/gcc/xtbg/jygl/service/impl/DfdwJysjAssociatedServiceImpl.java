package com.soft.gcc.xtbg.jygl.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.domain.AjaxResult;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.jygl.entity.DfdwJysjAssociated;
import com.soft.gcc.xtbg.jygl.mapper.DfdwJysjAssociatedMapper;
import com.soft.gcc.xtbg.jygl.params.DebtmanagementParams;
import com.soft.gcc.xtbg.jygl.service.DfdwJysjAssociatedService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_JYSJ_Associated(经营数据-往来户单位维护)】的数据库操作Service实现
 * @createDate 2025-07-15 14:09:10
 */
@Service
public class DfdwJysjAssociatedServiceImpl extends ServiceImpl<DfdwJysjAssociatedMapper, DfdwJysjAssociated>
        implements DfdwJysjAssociatedService {

    @Override
    public IPage<DfdwJysjAssociated> getList(DebtmanagementParams params) {
        IPage<DfdwJysjAssociated> page = new Page<>(params.getPageNum(), params.getPageSize());
        return page(page, new LambdaQueryWrapper<DfdwJysjAssociated>()
                .like(StringUtils.isNotBlank(params.getCompanyName()), DfdwJysjAssociated::getAssociatedUnits, params.getCompanyName())
                .orderByAsc(DfdwJysjAssociated:: getId)
        );
    }

    @Override
    public void exportExcelTemplate(HttpServletResponse response) throws IOException {
        try {
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("往来户单位数据导入模板");

            XSSFRow row = sheet.createRow(0);
            row.createCell(0).setCellValue("往来户单位名称");
            row.createCell(1).setCellValue("统一社会信用代码");
            row.createCell(2).setCellValue("联系人");
            row.createCell(3).setCellValue("联系电话");
            row.createCell(4).setCellValue("邮箱");

            // 创建表头样式（白色背景）
            XSSFCellStyle headerStyle = workbook.createCellStyle();

            // 创建字体
            XSSFFont font = workbook.createFont();
            font.setColor(IndexedColors.BLACK.getIndex());
            font.setFontHeightInPoints((short) 12);
            font.setBold(true);

            // 将字体应用到样式
            headerStyle.setFont(font);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            //设置表头行高度
            row.setHeightInPoints(35);

            // 应用样式到表头单元格
            for (int i = 0; i < row.getLastCellNum(); i++) {
                //设置单元格宽度
                sheet.setColumnWidth(i, 40 * 256);
                row.getCell(i).setCellStyle(headerStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("应收账款数据导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setCharacterEncoding("utf-8");

            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException e) {
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write(JSON.toJSONString(AjaxResult.error("导出Excel模板失败：" + e.getMessage())));
        }

    }

    @Override
    public Result<?> importExcel(MultipartFile file) {
        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            List<DfdwJysjAssociated> excelList = new ArrayList<>();

            // 验证Excel是否有数据
            if (sheet.getLastRowNum() < 1) {
                return Result.error("Excel文件没有数据行");
            }

            // 验证标题行
            XSSFRow headerRow = sheet.getRow(0);
            String headerValidationResult = validateHeaders(headerRow);
            if (headerValidationResult != null) {
                return Result.error(headerValidationResult);
            }

            // 读取数据并保存
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                XSSFRow row = sheet.getRow(i);
                //验证是否有邮箱和往来户单位名称，为必填项
                if (row.getCell(0) == null) {
                    return Result.error("第" + (i + 1) + "行往来户单位名称为空");
                }
                if (row.getCell(4) == null) {
                    return Result.error("第" + (i + 1) + "行邮箱为空");
                }
                //验证是否是正确的邮箱
                if (!isValidEmail(getCellValueAsString(row.getCell(4)))) {
                    return Result.error("第" + (i + 1) + "行邮箱格式错误");
                }
                DfdwJysjAssociated entity = parseRowToEntity(row);
                if (entity != null) {
                    excelList.add(entity);
                }
            }
            if (excelList.isEmpty()) {
                return Result.error("没有有效的数据可导入");
            }
            //批量新增
            batchInsert(excelList);
            return Result.ok("导入成功");

        } catch (Exception e) {
            log.error("往来户单位导入失败:" + e, e);
            return Result.error("导入失败：" + e);
        }
    }

    /**
     * 验证邮箱格式
     * @param stringCellValue
     * @return
     */
    private boolean isValidEmail(String stringCellValue) {
        return stringCellValue.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    private void batchInsert(List<DfdwJysjAssociated> excelList) {
        int batchSize = 100;  // SQL Server参数限制，5字段*100条=500个参数 < 2100
        for (int i = 0; i < excelList.size(); i += batchSize) {
            List<DfdwJysjAssociated> batchList = excelList.subList(i, Math.min(i + batchSize, excelList.size()));
            baseMapper.batchInsert(batchList);
        }
    }

    /**
     *
     * @param row
     * @return
     */
    private DfdwJysjAssociated parseRowToEntity(XSSFRow row) {
        DfdwJysjAssociated entity = new DfdwJysjAssociated();
        entity.setAssociatedUnits(getCellValueAsString(row.getCell(0)));
        entity.setUscc(getCellValueAsString(row.getCell(1)));
        entity.setContactPerson(getCellValueAsString(row.getCell(2)));
        entity.setContactPersonNumber(getCellValueAsString(row.getCell(3)));
        entity.setEmail(getCellValueAsString(row.getCell(4)));
        return entity;
    }

    /**
     * 安全地获取单元格的字符串值，支持数字和字符串类型
     * @param cell Excel单元格
     * @return 字符串值
     */
    private String getCellValueAsString(XSSFCell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 检查是否是日期类型
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 数字类型，转换为字符串（去掉小数点后的0）
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
            default:
                return "";
        }
    }

    private String validateHeaders(XSSFRow headerRow) {
        if (headerRow == null) {
            return "标题行不能为空";
        }

        // 定义期望的标题列表（按顺序）
        String[] expectedHeaders = {
                "往来户单位名称", "统一社会信用代码", "联系人", "联系电话", "邮箱"
        };

        // 检查标题数量
        if (headerRow.getLastCellNum() < expectedHeaders.length) {
            return "Excel标题列数不足，期望" + expectedHeaders.length + "列，实际" + headerRow.getLastCellNum() + "列";
        }

        // 检查每个标题是否匹配
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String actualHeader = cell.getStringCellValue();
            if (!expectedHeaders[i].equals(actualHeader)) {
                return "第" + (i + 1) + "列标题不匹配，期望：" + expectedHeaders[i] + "，实际：" + actualHeader;
            }
        }

        return null; // 验证通过
    }


}




