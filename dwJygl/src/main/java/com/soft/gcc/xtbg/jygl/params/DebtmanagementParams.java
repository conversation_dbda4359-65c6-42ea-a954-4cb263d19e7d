package com.soft.gcc.xtbg.jygl.params;

import lombok.Data;

import java.io.Serializable;

@Data
public class DebtmanagementParams implements Serializable {
    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Integer total;

    /**
     * 是否是汇总
     */
    private Boolean isSummary;
}
