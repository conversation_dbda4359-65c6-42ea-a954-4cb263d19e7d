package com.soft.gcc.xtbg.jygl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 隐性债券数据
 * @TableName DFDW_JYSJ_DebtManagement
 */
@TableName(value ="DFDW_JYSJ_DebtManagement")
@Data
public class DfdwJysjDebtmanagement {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 集团编码
     */
    @Excel(name = "集团编码",width = 15)
    @TableField("group_code")
    private String groupCode;

    /**
     * 集团名称
     */
    @Excel(name = "集团名称",width = 30)
    @TableField("group_name")
    private String groupName;

    /**
     * 单位编码
     */
    @Excel(name = "单位编码",width = 15)
    @TableField("company_code")
    private String companyCode;

    /**
     * 单位名称
     */
    @Excel(name = "单位名称",width = 30)
    @TableField("company_name")
    private String companyName;

    /**
     * 单位id
     */
    @TableField("company_id")
    private Integer companyId;

    /**
     * 承揽部门
     */
    @Excel(name = "承包部门",width = 30)
    @TableField("contracting_dept")
    private String contractingDept;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码",width = 15)
    @TableField("project_code")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称",width = 50)
    @TableField("project_name")
    private String projectName;

    /**
     * 是否期初（0否1是
     */
    @Excel(name = "是否期初", width = 10, replace = {"是_true", "否_false"})
    @TableField("beginning")
    private Boolean beginning;

    /**
     * 项目类型
     */
    @Excel(name = "项目类型",width = 15)
    @TableField("project_type")
    private String projectType;

    /**
     * 项目状态
     */
    @Excel(name = "项目状态",width = 15)
    @TableField("project_status")
    private String projectStatus;

    /**
     * 客户名称
     */
    @Excel(name = "客户名称",width = 30)
    @TableField("customer_name")
    private String customerName;

    /**
     * 客户性质
     */
    @Excel(name = "客户性质",width = 15)
    @TableField("customer_nature")
    private String customerNature;

    /**
     * 项目完工时间
     */
    @Excel(name = "项目完工时间",format = "yyyy-MM-dd", timezone = "GMT+8",width = 20)
    @TableField("project_completed_date")
    private Date projectCompletedDate;

    /**
     * 项目完工时间分类-按年
     */
    @Excel(name = "项目完工时间分类-按年",width = 15)
    @TableField("project_completed_type_by_year")
    private String projectCompletedTypeByYear;

    /**
     * 项目完工时间分类-按月
     */
    @Excel(name = "项目完工时间分类-按月",width = 15)
    @TableField("project_completed_type_by_month")
    private String projectCompletedTypeByMonth;

    /**
     * 清理状态
     */
    @Excel(name = "清理状态",width = 15)
    @TableField("clean_type")
    private String cleanType;

    /**
     * 合同金额（元）
     */
    @Excel(name = "合同金额（元）",width = 20)
    @TableField("contract_amount")
    private BigDecimal contractAmount;

    /**
     * 送审时间
     */
    @Excel(name = "送审时间",format = "yyyy-MM-dd", timezone = "GMT+8",width = 20)
    @TableField("for_review_time")
    private Date forReviewTime;

    /**
     * 送审金额（元）
     */
    @Excel(name = "送审金额（元）",width = 20)
    @TableField("for_review_amount")
    private BigDecimal forReviewAmount;

    /**
     * 审定时间
     */
    @Excel(name = "审定时间",format = "yyyy-MM-dd", timezone = "GMT+8",width = 20)
    @TableField("review_time")
    private Date reviewTime;

    /**
     * 审定金额（元）
     */
    @Excel(name = "审定金额（元）",width = 20)
    @TableField("review_amount")
    private BigDecimal reviewAmount;

    /**
     * 业+应用前已开票并已收款金额（元）
     */
    @Excel(name = "业+应用前已开票并已收款金额（元）",width = 20)
    @TableField("invoice_received_take_amount")
    private BigDecimal invoiceReceivedTakeAmount;

    /**
     * 业+应用前已开票并未收款金额（元）
     */
    @Excel(name = "业+应用前已开票并未收款金额（元）",width = 20)
    @TableField("invoice_received_no_take_amount")
    private BigDecimal invoiceReceivedNoTakeAmount;

    /**
     * ERP系统开票金额-财务凭证（元）
     */
    @Excel(name = "ERP系统开票金额-财务凭证（元）",width = 20)
    @TableField("erp_financial_vouchers_amountd")
    private BigDecimal erpFinancialVouchersAmountd;

    /**
     * 累计开票金额（元）
     */
    @Excel(name = "累计开票金额（元）",width = 20)
    @TableField("cumulative_invoicing_amount")
    private BigDecimal cumulativeInvoicingAmount;

    /**
     * 隐性债权待清理金额（元）
     */
    @Excel(name = "隐性债权待清理金额（元）",width = 20)
    @TableField("to_be_cleaned_amount")
    private BigDecimal toBeCleanedAmount;

    /**
     * 上月隐性债权清理金额（元）
     */
    @Excel(name = "上月隐性债权清理金额（元）",width = 20)
    @TableField("last_month_clean_up_amount")
    private BigDecimal lastMonthCleanUpAmount;

    /**
     * 本月隐性债权清理金额（元）
     */
    @Excel(name = "本月隐性债权清理金额（元）",width = 20)
    @TableField("this_month_clean_up_amount")
    private BigDecimal thisMonthCleanUpAmount;

    /**
     * 今年隐性债权清理金额（元）
     */
    @Excel(name = "今年隐性债权清理金额（元）",width = 20)
    @TableField("this_year_clean_up_amount")
    private BigDecimal thisYearCleanUpAmount;

    @TableField("group_item_name")
    private String groupItemName;

}