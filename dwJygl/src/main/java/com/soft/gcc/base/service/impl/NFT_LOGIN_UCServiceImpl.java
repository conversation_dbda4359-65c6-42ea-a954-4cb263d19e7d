package com.soft.gcc.base.service.impl;

import com.soft.gcc.base.entity.NFT_LOGIN_UC;
import com.soft.gcc.base.mapper.NFT_LOGIN_UCMapper;
import com.soft.gcc.base.service.INFT_LOGIN_UCService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户能够切换的账户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Service
public class NFT_LOGIN_UCServiceImpl extends ServiceImpl<NFT_LOGIN_UCMapper, NFT_LOGIN_UC> implements INFT_LOGIN_UCService {

}
