package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 模块--模块分组展示
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_ModuleGroup")
@ApiModel(value="NFT_ModuleGroup对象", description="模块--模块分组展示")
public class NFT_ModuleGroup extends Model<NFT_ModuleGroup> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组编号")
    @JSONField(name="GP_ID")
    private Integer GP_ID;

    @ApiModelProperty(value = "分组名称")
    @JSONField(name="GP_NAME")
    private String GP_NAME;

    @ApiModelProperty(value = "分组模块列表")
    @JSONField(name="GP_MLIST")
    private String GP_MLIST;


    @Override
    protected Serializable pkVal() {
        return this.GP_ID;
    }

}
