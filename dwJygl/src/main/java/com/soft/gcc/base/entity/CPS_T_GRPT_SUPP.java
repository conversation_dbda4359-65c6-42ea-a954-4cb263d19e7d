package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CPS_T_GRPT_SUPP")
@ApiModel(value="CPS_T_GRPT_SUPP对象", description="")
public class CPS_T_GRPT_SUPP extends Model<CPS_T_GRPT_SUPP> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "补充类型")
    @JSONField(name="SUPP_TY")
    private String SUPP_TY;

    @ApiModelProperty(value = "补充标识")
    @JSONField(name="SUPP_FG")
    private String SUPP_FG;

    @ApiModelProperty(value = "单据业务ID")
    @JSONField(name="SUPP_YW")
    private Long SUPP_YW;

    @ApiModelProperty(value = "补充内容")
    @JSONField(name="SUPP_IF")
    private String SUPP_IF;


    @Override
    protected Serializable pkVal() {
        return this.SUPP_TY;
    }

}
