package com.soft.framework.utils;

import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.EnvironmentStringPBEConfig;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;

/**
 * <AUTHOR>
 */
public class EncryptUtils {
    private static final String PBEWITHMD5ANDDES = "PBEWithMD5AndDES";

    private static final String PBEWITHHMACSHA512ANDAES_256 = "PBEWITHHMACSHA512ANDAES_256";


    public static void main(String[] args) {
        String secret1 = encryptWithMD5("yyszpt", "EWRREWRERWECCCXC");
        System.out.println(secret1);
        String name1 = decryptWithMD5(secret1, "EWRREWRERWECCCXC");
        System.out.println(name1);

        String secret2 = encryptWithMD5("nb.syGyckcj1126", "EWRREWRERWECCCXC");
        System.out.println(secret2);
        String name2 = decryptWithMD5(secret2, "EWRREWRERWECCCXC");
        System.out.println(name2);

        String secret3 = encryptWithMD5("zpepc001@", "EWRREWRERWECCCXC");
        System.out.println(secret3);
        String name3 = decryptWithMD5(secret3, "EWRREWRERWECCCXC");
        System.out.println(name3);
    }


    /**
     *  jasypt 加密（PBEWithMD5AndDES）
     * @param message 待加密的原文
     * @param password 加解密秘钥
     * @return
     */
    public static String encryptWithMD5(String message, String password) {
        // 1. 创建加解密工具实例
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        // 2. 加解密配置
        EnvironmentStringPBEConfig config = new EnvironmentStringPBEConfig();
        config.setAlgorithm(PBEWITHMD5ANDDES);
        config.setPassword(password);
        // 设置迭代次数
        config.setKeyObtentionIterations( "10000");
        // 为减少配置文件的书写，以下都是 Jasyp 3.x 版本，配置文件默认配置
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        // 3. 加密
        return encryptor.encrypt(message);
    }


    /**
     *  jasypt 解密（PBEWithMD5AndDES）
     * @param encryptedMessage 待解密的原文
     * @param password 加解密秘钥
     * @return
     */
    public static String decryptWithMD5(String encryptedMessage, String password) {
        // 1. 创建加解密工具实例
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        // 2. 加解密配置
        EnvironmentStringPBEConfig config = new EnvironmentStringPBEConfig();
        config.setAlgorithm(PBEWITHMD5ANDDES);
        config.setPassword(password);
        // 设置迭代次数
        config.setKeyObtentionIterations( "10000");
        // 为减少配置文件的书写，以下都是 Jasyp 3.x 版本，配置文件默认配置
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        // 3. 解密
        return encryptor.decrypt(encryptedMessage);
    }


    /**
     * jasypt 加密 （PBEWITHHMACSHA512ANDAES_256）
     * @param message 待加密的原文
     * @param password 加解密秘钥
     * @return
     */
    public static String encryptWithSHA512(String message, String password) {
        // 1. 创建加解密工具实例
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        // 2. 加解密配置
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm(PBEWITHHMACSHA512ANDAES_256);
        // 设置迭代次数
        config.setKeyObtentionIterations( "10000");
        // 为减少配置文件的书写，以下都是 Jasyp 3.x 版本，配置文件默认配置
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        // 3. 加密
        return encryptor.encrypt(message);
    }

    /**
     * jasypt 解密（PBEWITHHMACSHA512ANDAES_256）
     * @param encryptedMessage 待解密的原文
     * @param password 加解密秘钥
     * @return
     */
    public static String decryptWithSHA512(String encryptedMessage, String password) {
        // 1. 创建加解密工具实例
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        // 2. 加解密配置
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm(PBEWITHHMACSHA512ANDAES_256);
        // 设置迭代次数
        config.setKeyObtentionIterations( "10000");
        // 为减少配置文件的书写，以下都是 Jasyp 3.x 版本，配置文件默认配置
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        // 3. 解密
        return encryptor.decrypt(encryptedMessage);
    }
}
