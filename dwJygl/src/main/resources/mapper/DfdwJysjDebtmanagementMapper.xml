<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.jygl.mapper.DfdwJysjDebtmanagementMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.jygl.entity.DfdwJysjDebtmanagement">
            <id property="id" column="id" />
            <result property="groupCode" column="group_code" />
            <result property="groupName" column="group_name" />
            <result property="companyCode" column="company_code" />
            <result property="companyName" column="company_name" />
            <result property="companyId" column="company_id" />
            <result property="contractingDept" column="contracting_dept" />
            <result property="projectCode" column="project_code" />
            <result property="projectName" column="project_name" />
            <result property="beginning" column="beginning" />
            <result property="projectType" column="project_type" />
            <result property="projectStatus" column="project_status" />
            <result property="customerName" column="customer_name" />
            <result property="customerNature" column="customer_nature" />
            <result property="projectCompletedDate" column="project_completed_date" />
            <result property="projectCompletedTypeByYear" column="project_completed_type_by_year" />
            <result property="projectCompletedTypeByMonth" column="project_completed_type_by_month" />
            <result property="cleanType" column="clean_type" />
            <result property="contractAmount" column="contract_amount" />
            <result property="forReviewTime" column="for_review_time" />
            <result property="forReviewAmount" column="for_review_amount" />
            <result property="reviewTime" column="review_time" />
            <result property="reviewAmount" column="review_amount" />
            <result property="invoiceReceivedTakeAmount" column="invoice_received_take_amount" />
            <result property="invoiceReceivedNoTakeAmount" column="invoice_received_no_take_amount" />
            <result property="erpFinancialVouchersAmountd" column="erp_financial_vouchers_amountd" />
            <result property="cumulativeInvoicingAmount" column="cumulative_invoicing_amount" />
            <result property="toBeCleanedAmount" column="to_be_cleaned_amount" />
            <result property="lastMonthCleanUpAmount" column="last_month_clean_up_amount" />
            <result property="thisMonthCleanUpAmount" column="this_month_clean_up_amount" />
            <result property="thisYearCleanUpAmount" column="this_year_clean_up_amount" />
    </resultMap>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO DFDW_JYSJ_DebtManagement (
            group_code, group_name, company_code, company_name, contracting_dept,
            project_code, project_name, beginning, project_type, project_status,
            customer_name, customer_nature, project_completed_date,
            project_completed_type_by_year, project_completed_type_by_month,
            clean_type, contract_amount, for_review_time, for_review_amount,
            review_time, review_amount, invoice_received_take_amount,
            invoice_received_no_take_amount, erp_financial_vouchers_amountd,
            cumulative_invoicing_amount, to_be_cleaned_amount,
            last_month_clean_up_amount, this_month_clean_up_amount,
            this_year_clean_up_amount,company_id,group_item_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.groupCode,jdbcType=VARCHAR},
                #{item.groupName,jdbcType=VARCHAR},
                #{item.companyCode,jdbcType=VARCHAR},
                #{item.companyName,jdbcType=VARCHAR},
                #{item.contractingDept,jdbcType=VARCHAR},
                #{item.projectCode,jdbcType=VARCHAR},
                #{item.projectName,jdbcType=VARCHAR},
                #{item.beginning,jdbcType=BOOLEAN},
                #{item.projectType,jdbcType=VARCHAR},
                #{item.projectStatus,jdbcType=VARCHAR},
                #{item.customerName,jdbcType=VARCHAR},
                #{item.customerNature,jdbcType=VARCHAR},
                #{item.projectCompletedDate,jdbcType=TIMESTAMP},
                #{item.projectCompletedTypeByYear,jdbcType=VARCHAR},
                #{item.projectCompletedTypeByMonth,jdbcType=VARCHAR},
                #{item.cleanType,jdbcType=VARCHAR},
                #{item.contractAmount,jdbcType=DECIMAL},
                #{item.forReviewTime,jdbcType=TIMESTAMP},
                #{item.forReviewAmount,jdbcType=DECIMAL},
                #{item.reviewTime,jdbcType=TIMESTAMP},
                #{item.reviewAmount,jdbcType=DECIMAL},
                #{item.invoiceReceivedTakeAmount,jdbcType=DECIMAL},
                #{item.invoiceReceivedNoTakeAmount,jdbcType=DECIMAL},
                #{item.erpFinancialVouchersAmountd,jdbcType=DECIMAL},
                #{item.cumulativeInvoicingAmount,jdbcType=DECIMAL},
                #{item.toBeCleanedAmount,jdbcType=DECIMAL},
                #{item.lastMonthCleanUpAmount,jdbcType=DECIMAL},
                #{item.thisMonthCleanUpAmount,jdbcType=DECIMAL},
                #{item.thisYearCleanUpAmount,jdbcType=DECIMAL},
                #{item.companyId,jdbcType=INTEGER},
                #{item.groupItemName,jdbcType=VARCHAR}

            )
        </foreach>
    </insert>

</mapper>
