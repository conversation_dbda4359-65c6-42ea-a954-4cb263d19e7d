package com.soft.gcc.common.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.mapper.PersonMapper;
import com.soft.gcc.common.person.service.PersonService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【Person】的数据库操作Service实现
* @createDate 2023-03-28 14:23:59
*/
@Service
public class PersonServiceImpl extends ServiceImpl<PersonMapper, Person>
    implements PersonService{

    @Override
    public Result<Object> selectPageList(Map<String, String> param) {
        try{
            int pageNum = ParseUtil.tryParseInt(param.get("pageNum"));
            int pageSize = ParseUtil.tryParseInt(param.get("pageSize"),1);
            String loginName = ParseUtil.tryParseString(param.get("loginName"));
            String realName = ParseUtil.tryParseString(param.get("userRealName"));

            QueryWrapper<Person> queryWrapper = new QueryWrapper<>();
            //只查询指定字段
            queryWrapper.select("Id","LoginName","RealName","Sphone","Telephone");

            queryWrapper.lambda().like(!"".equals(realName) ,Person::getRealname,realName);
            queryWrapper.lambda().like(!"".equals(loginName) ,Person::getLoginname,loginName);
            queryWrapper.lambda().orderByDesc(Person::getId);
            return Result.ok(this.baseMapper.selectPage(new Page<>(pageNum,pageSize),queryWrapper));

        }catch (Exception ex){
            String s = "程序产生异常:" + ex.getMessage() + "!";
            return Result.error(s);
        }
    }
}




