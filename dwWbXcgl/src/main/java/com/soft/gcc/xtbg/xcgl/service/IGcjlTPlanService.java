package com.soft.gcc.xtbg.xcgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcdkzphzDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPlan;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.xcgl.vo.GcjlTPlanVo;
import com.yyszc.wpbase.ventity.PersonEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_Plan(工程监理--计划行程子表)】的数据库操作Service
* @createDate 2023-07-12 10:59:28
*/
public interface IGcjlTPlanService extends IService<GcjlTPlan> {

    IPage<GcjlTPlanVo> getXchzShowList(String deptfullpath, Date startDate, Date endDate, Integer pageNum, Integer pageSize);

    List<GcjlPlanDto> getInfo(Integer lcId);

    void exportExcel(String deptfullpath, Date startDate, Date endDate, HttpServletResponse response);

    /**
     * 20240508新增   PersonEntity user 判断是否有 ‘协同办公-行程管理-国网建设分公司管理’ 角色 如果有oss走另外一套
     * @param xcdkzphzDto
     * @param user
     * @return
     */
    IPage<GcjlPlanDto> selectJoinPage(GcjlTXcdkzphzDto xcdkzphzDto, PersonEntity user);


    List<GcjlPlanDto> selectList(GcjlTXcdkzphzDto xcdkzphzDto);
}
