package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTCancellationLog;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTCancellationLogMapper;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTCancellationLogService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GcjlTCancellationLogServiceImpl extends ServiceImpl<GcjlTCancellationLogMapper, GcjlTCancellationLog> implements IGcjlTCancellationLogService {


    @Override
    public GcjlTCancellationLog getInfo(GcjlTCancellationLog cancellationLog) {
        List<GcjlTCancellationLog> list = this.list(new LambdaQueryWrapper<GcjlTCancellationLog>()
                .eq(GcjlTCancellationLog::getYwId,cancellationLog.getYwId())
                .orderByDesc(GcjlTCancellationLog::getId)
        );
        return list.isEmpty()?null:list.get(0);
    }
}
