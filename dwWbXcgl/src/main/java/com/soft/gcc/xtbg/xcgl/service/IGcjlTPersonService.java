package com.soft.gcc.xtbg.xcgl.service;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTPersonDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.models.auth.In;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_Person(工程监理--人员表)】的数据库操作Service
* @createDate 2023-07-12 10:58:51
*/
public interface IGcjlTPersonService extends IService<GcjlTPerson> {

    Result<Object> getPersonList(Map<String, String> map);

    Integer cleanEndData(GcjlTPerson person);


    Result<Object> startOrdisablePersonById(Integer id,Integer type);

    /**
     * 从原有人员信息添加到新的组织架构中
     * @param map
     * @return
     */
    Result<Object> creatPerson(GcjlTPersonDto gcjlTPersonDto);

    Result<Object> importExcel(MultipartFile multipartFile);

    /**
     * 根据人员id更新补贴费用上限
     * @param gcjlTPerson
     * @return
     */
    Result<Object> insertLimitByPersonId(GcjlTPerson gcjlTPerson);

    /**
     * 获取人员补贴费用上限
     */
    Result<Object> getPersonLimitLog(Map<String, String> param);

    /**
     * 导出excel
     * @return
     */
    void exportExcel(Map<String,String> param,HttpServletResponse response);


}
