package com.soft.gcc.xtbg.xcgl.Controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.aspectj.lang.enums.DataSourceType;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcbbDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTXcbb;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTXcbbService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import retrofit2.http.POST;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 行程报备
 */

@RestController
@RequestMapping("xcbb")
public class XcglXcbbController {

    @Autowired
    private IGcjlTXcbbService gcjlTXcbbService;

    /**
     * 获取行程报备列表
     */
    @PostMapping("selectPageList")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    public Result<Object> selectPageList(@RequestBody GcjlTXcbbDto gcjlTXcbb){
        return gcjlTXcbbService.selectPageList(gcjlTXcbb);
    }

    /**
     * 导出excel
     * @param gcjlTXcbb
     * @param response
     * @return
     */
    @PostMapping("exportExcel")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    public Result<Object> exportExcel(@RequestBody GcjlTXcbbDto gcjlTXcbb, HttpServletResponse response){
        return gcjlTXcbbService.exportExcel(gcjlTXcbb,response);
    }
}
