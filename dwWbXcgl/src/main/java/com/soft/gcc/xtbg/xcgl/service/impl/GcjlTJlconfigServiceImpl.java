package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTJlconfig;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTJlconfigService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTJlconfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_JlConfig】的数据库操作Service实现
* @createDate 2023-07-12 10:58:24
*/
@Service
public class GcjlTJlconfigServiceImpl extends ServiceImpl<GcjlTJlconfigMapper, GcjlTJlconfig>
    implements IGcjlTJlconfigService{

}




