package com.soft.gcc.xtbg.xcgl.service.impl;

import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.aspectj.lang.enums.DataSourceType;
import com.soft.framework.config.datasource.DynamicDataSourceContextHolder;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.service.PersonService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTPersonDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTPersonService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTPersonMapper;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTPersonlimitlogService;
import io.swagger.models.auth.In;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_Person(工程监理--人员表)】的数据库操作Service实现
* @createDate 2023-07-12 10:58:51
*/
@Service
public class GcjlTPersonServiceImpl extends ServiceImpl<GcjlTPersonMapper, GcjlTPerson>
    implements IGcjlTPersonService{

    @Autowired
    private GcjlTDepartmentService gcjlTDepartmentService;


    @Autowired
    private IGcjlTPersonlimitlogService gcjlTPersonlimitlogService;

    @Autowired
    private PersonService personService;

    
    @Override
    public Result<Object> getPersonList(Map<String, String> map) {
        int pageNum = ParseUtil.tryParseInt(map.get("pageNum"));
        int pageSize = ParseUtil.tryParseInt(map.get("pageSize"), 1);
//        QueryWrapper<GcjlTPerson> queryWrapper = new QueryWrapper();
//        queryWrapper.lambda().eq(StringUtils.isNotBlank(map.get("deptId")),GcjlTPerson::getDeptId,map.get("deptId"));
//        queryWrapper.lambda().orderByDesc(GcjlTPerson::getCreateTime);
        if (map.get("deptId") == null && StringUtils.isBlank(map.get("realName")) ){
            return Result.ok();
        }
        //有姓名优先查询姓名
        if (StringUtils.isNotBlank(map.get("realName"))){
            map.put("deptId","0");
        }
        return Result.ok(this.baseMapper.getPersonList(new Page<>(pageNum,pageSize), Integer.valueOf(map.get("deptId")),map.get("realName")));
    }

    @Override
    public Integer cleanEndData(GcjlTPerson person) {
        return this.baseMapper.cleanEndData(person.getId());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> startOrdisablePersonById(Integer id,Integer type) {
        GcjlTPerson person = this.baseMapper.selectById(id);
        if (person ==null){
            return Result.error("未查询到相关数据!");
        }

        try {
            person.setState(type);
            this.baseMapper.updateById(person);
        }catch (Exception ex){
            log.error("根据id启用/禁用用户失败:"+ex.getMessage());
            if (type == 0){
                return Result.error("禁用失败!");
            }
            return Result.error("启用失败!");
        }

        return Result.ok();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> creatPerson(GcjlTPersonDto gcjlTPersonDto) {
//        String ids = ParseUtil.tryParseString(map.get("ids"));
//        int deptId = ParseUtil.tryParseInt(map.get("deptId"));
//        String[] idstr = ids.split(",");
            if (gcjlTPersonDto.getDeptId() == null || gcjlTPersonDto.getIds() == null){
                return Result.error("必填参数不能为空！");
            }

            //查询当前deptid的信息
            GcjlTDepartment department = gcjlTDepartmentService.getDepartmentById(gcjlTPersonDto.getDeptId());
            if (department == null){
                return Result.error("部门不存在！");
            }
            List<GcjlTPerson> list = new ArrayList<>();
            for (Integer id : gcjlTPersonDto.getIds()) {
                GcjlTPerson gcjlTPerson = new GcjlTPerson();
                gcjlTPerson.setSrcUserId(Integer.valueOf(id));
                gcjlTPerson.setTopDeptId(department.getParentid());
                gcjlTPerson.setDeptId(gcjlTPersonDto.getDeptId());

                Person person = personService.getById(id);
                if (person != null){
                    gcjlTPerson.setUserFullPath(department.getDeptfullpath()+"\\"+person.getRealname());
                }

                //查询 当前人员 是否存在 监理公司下面子部门 或者 慈溪输变电下面子部门
                //setup1 查询GCJL_T_Person 表当前人员记录
                QueryWrapper<GcjlTPerson> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(GcjlTPerson::getSrcUserId,id);
                List<GcjlTPerson> gcjlTPersonList = this.baseMapper.selectList(queryWrapper);
                if (gcjlTPersonList.size() > 0){
                    //setup2 取任意一条记录 同当前选择的部门做比对
                    String fullPath = gcjlTPersonList.get(0).getUserFullPath().split("\\\\")[0];
                    if (!fullPath.equals(department.getDeptfullpath().split("\\\\")[0])){
                        //String resMsg = person.getRealname()+"已存在"+fullPath+"下面的部门！";
                        String resMsg = person.getRealname()+"已在其他组织机构，请联系管理员！";
                        return Result.error(resMsg);
                    }
                }
                //查询GcjlTPerson 是否已经存在记录
                queryWrapper.lambda().eq(GcjlTPerson::getDeptId,gcjlTPersonDto.getDeptId());
                Integer count = this.baseMapper.selectCount(queryWrapper);
                if (count >0){
                    return Result.error(person.getRealname()+"已存在该部门！");
                }

                gcjlTPerson.setState(1);
                gcjlTPerson.setCreateTime(new Date());
                gcjlTPerson.setCostTopLimit(new BigDecimal(0.0));

                list.add(gcjlTPerson);
            }
            if (list.size() != 0 ){
    //            for (int i = 0; i < list.size(); i++) {
    //                this.baseMapper.insert(list.get(i));
    //            }
                this.baseMapper.batchInsert(list);
                return Result.ok("新增成功");
            }
            return Result.ok();

    }


    @Override
    public Result<Object> importExcel(MultipartFile multipartFile) {
        String fileName = multipartFile.getOriginalFilename();
        System.out.println(fileName);
        if (!".xlsx".equals(fileName.substring(fileName.lastIndexOf("."))) &&
                !".xls".equals(fileName.substring(fileName.lastIndexOf(".")))
        ){
            return Result.error("不是excel文件!");
        }

        try {
            ///监理人员导入列表
            List<GcjlTPerson> addPersonList = new ArrayList<>();
            ///所有监理人员信息
            List<GcjlTPerson> personList = this.baseMapper.selectList(new QueryWrapper<GcjlTPerson>());
            ///原有人员信息列表
            List<Person> PList = personService.list();
            ///监理站列表
            List<GcjlTDepartment> dList = gcjlTDepartmentService.list();



//            //根据路径获取这个操作excel的实例
//            HSSFWorkbook wb = new HSSFWorkbook(multipartFile.getInputStream());
//            //根据页面index 获取sheet页
//            HSSFSheet sheet = wb.getSheetAt(0);
//            HSSFRow row = null;
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            XSSFRow row = null;
            int one = 0;

            //循环sesheet页中数据从第二行开始，第一行是标题
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                //获取每一行数据
                row = sheet.getRow(i);

                GcjlTPerson entity = new GcjlTPerson();

                if(row.getCell(0) == null || row.getCell(1) == null || row.getCell(2) == null){
                    one = i+1;
                    return Result.error("有数据为空，请检查第" + one + "行数据！");
                }

                //工号纯数字会导致读取错误，故先转换成string 再读取，可解决问题
                XSSFCell cell = row.getCell(0);
                //设置单元格类型
                cell.setCellType(CellType.STRING);
                //获取单元格数据
                String loginName = cell.getStringCellValue();
                //全部设置成string 在读取
                row.getCell(1).setCellType(CellType.STRING);
                row.getCell(2).setCellType(CellType.STRING);

                String realName = row.getCell(1).getStringCellValue().trim();
                List<Person> havePersonList = PList.stream().filter(p -> loginName.equals(p.getLoginname()))
                        .filter(p -> realName.equals(p.getRealname())).collect(Collectors.toList());
                if (havePersonList.size() == 0 || havePersonList == null) {
                    one = i + 1;
                    return Result.error("工号与姓名无法查找到该人员，请检查第" + one + "行数据！");
                }

                String deptName =  row.getCell(2).getStringCellValue().trim();
                List<GcjlTDepartment> haveDepartmentList = dList.stream().filter(p->deptName.equals(p.getDeptname())).collect(Collectors.toList());
                if (haveDepartmentList.size() == 0 || havePersonList == null){
                    one = i + 1;
                    return Result.error( "无法查询到该部门，请检查第" + one + "行数据！");
                }

                entity.setSrcUserId(havePersonList.get(0).getId());
                entity.setTopDeptId(haveDepartmentList.get(0).getParentid());
                entity.setDeptId(haveDepartmentList.get(0).getId());
                entity.setUserFullPath(haveDepartmentList.get(0).getDeptfullpath() + "\\" + havePersonList.get(0).getRealname());
                entity.setState(1);
                entity.setCreateTime(new Date());
                entity.setCostTopLimit(new BigDecimal(0.0));

                int srcUserId = entity.getSrcUserId();
                int deptId = entity.getDeptId();

                //查询 当前人员 是否存在 监理公司下面子部门 或者 慈溪输变电下面子部门
                List<GcjlTPerson> gcjlTPersonList = personList.stream().filter(p->p.getSrcUserId() == srcUserId).collect(Collectors.toList());
                if (gcjlTPersonList.size()>0){
                    //取任意一条记录 同当前选择的部门做比对
                    String fullPath = gcjlTPersonList.get(0).getUserFullPath().split("\\\\")[0];
                    if (!fullPath.equals(haveDepartmentList.get(0).getDeptfullpath().split("\\\\")[0])){
                        one = i + 1;
                        //String resMsg = havePersonList.get(0).getRealname()+"已存在"+fullPath+"下面的部门！请检查第" + one + "行数据！";
                        String resMsg = "请检查第" + one + "行数据！"+havePersonList.get(0).getRealname()+"已在其他组织机构，请联系管理员！";
                        return Result.error(resMsg);
                    }
                }

                //判断当前导入的excel里面是否有重复部门。是否前一条数据是监理公司 后一条是 慈溪输变电
                String checkResMsg = checkListData(addPersonList,srcUserId,entity.getUserFullPath(),entity.getDeptId());
                if (!"".equals(checkResMsg)){
                    one = i + 1;
                    String resMsg = havePersonList.get(0).getRealname()+"第"+one+"行部门和"+checkResMsg+"请检查";
                    return Result.error(resMsg);
                }

                int count = personList.stream().filter(p->p.getSrcUserId() == srcUserId).filter(p->p.getDeptId() == deptId).collect(Collectors.toList()).size();
                if (count > 0)
                {
                    one = i + 1;
                    return Result.error(havePersonList.get(0).getRealname()+"已存在该部门，请检查第" + one + "行数据！");
                }
                addPersonList.add(entity);
            }

            if (addPersonList.size()>0){
                //存入数据库
                this.baseMapper.batchInsert(addPersonList);
            }

        }catch (Exception ex){
            log.error("导入失败:" + ex.getMessage());
            return Result.error("导入失败！");
        }
        return Result.ok("导入成功!");
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> insertLimitByPersonId(GcjlTPerson gcjlTPerson) {
        GcjlTPersonlimitlog entity = new GcjlTPersonlimitlog();
        if (gcjlTPerson.getCostTopLimit() != null ){
            entity.setSrcPersonId(gcjlTPerson.getSrcUserId());
            entity.setLimit(gcjlTPerson.getCostTopLimit());
            entity.setCreateTime(new Date());
            gcjlTPersonlimitlogService.save(entity);

//            GcjlTPerson person = this.baseMapper.selectById(gcjlTPerson.getSrcUserId());
//            person.setCostTopLimit(gcjlTPerson.getCostTopLimit());

            this.baseMapper.updateCostTopLimit(gcjlTPerson.getSrcUserId(),gcjlTPerson.getCostTopLimit());
        }
        return Result.ok("更新成功!");
    }

    @Override
    public Result<Object> getPersonLimitLog(Map<String, String> param) {
        int srcPersonId = ParseUtil.tryParseInt(param.get("srcPersonId"));
        int pageNum = ParseUtil.tryParseInt(param.get("pageNum"));
        int pageSize = ParseUtil.tryParseInt(param.get("pageSize"), 1);
        Page<GcjlTPersonlimitlog> page = new Page<>(pageNum,pageSize);
        return Result.ok(gcjlTPersonlimitlogService.getPersonLimitLogBySrcPersonId(page,srcPersonId));
    }

    @Override
    public void exportExcel(Map<String,String> param, HttpServletResponse response) {
        if (param.get("deptId") == null && StringUtils.isBlank(param.get("realName")) ){
            return;
        }
        //有姓名优先查询姓名
        if (StringUtils.isNotBlank(param.get("realName"))){
            param.put("deptId","0");
        }
       List<GcjlTPerson> list = this.baseMapper.exportExcel(Integer.valueOf(param.get("deptId")),param.get("realName"));
       List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
       cmlist.add(new ToolHelper.ExportColumnMode("RealName", "姓名", 20));
       cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "组织", 30));
       cmlist.add(new ToolHelper.ExportColumnMode("Sphone", "短号", 15));
       cmlist.add(new ToolHelper.ExportColumnMode("Telephone", "联系方式", 30));
       cmlist.add(new ToolHelper.ExportColumnMode("StateStr", "状态", 15));
       ToolHelper.ExportExcelList(list, "人员管理", cmlist,false,response );
    }

    /**
     * 校验excel中导入的数据 是否有重复的
     * @param list
     * @param srcUserId
     * @param currentDeptFullPath
     * @param currentDeptId
     * @return
     */
    public String checkListData(List<GcjlTPerson> list , int srcUserId,String currentDeptFullPath,int currentDeptId){
        for (int i = 0; i < list.size(); i++) {
            //如果有相同用户id
            if (srcUserId == list.get(i).getSrcUserId()){
                String listUserFullPath = list.get(i).getUserFullPath().split("\\\\")[0];
                String currentUserFullPath = currentDeptFullPath.split("\\\\")[0];
                //顶级部门不一样
                if (!listUserFullPath.equals(currentUserFullPath)){
                    return "第"+(i+2)+"行部门不一致";
                }
                //部门相同
                if (list.get(i).getDeptId() == currentDeptId){
                    return "第"+(i+2)+"行部门相同";
                }
            }
        }
        return "";
    }

}




