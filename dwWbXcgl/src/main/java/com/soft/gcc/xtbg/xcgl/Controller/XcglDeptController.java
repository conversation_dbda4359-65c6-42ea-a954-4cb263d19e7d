package com.soft.gcc.xtbg.xcgl.Controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.gcc.xtbg.base.controller.BaseController;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.dto.TreeMenu;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
@RequestMapping(value = "/zzjg")
@RestController
public class XcglDeptController extends BaseController {
    @Autowired
    GcjlTDepartmentService gcjlTDepartmentService;


    /**
     * 获取行程管理部门信息
     * @return
     */
    @RequestMapping(value = "/getDeptList")
//    @PreAuthorize("@ss.hasRole('协同办公-行程管理-组织架构')")
    public Result<Object> getDeptList() {
        //我要全部
        List<GcjlTDepartment> departmentList=gcjlTDepartmentService.list();
        GcjlTDepartment dept1 = null;

        //获取 建分部门信息
        dept1 = departmentList.stream().filter(p->p.getId().equals(200)).findFirst().orElse(null);

        TreeMenu treeMenu=new TreeMenu();
        if (dept1 != null) {
            treeMenu.setId(dept1.getId())
                    .setPid(0).setLabel(dept1.getDeptname())
                    .setChildren(getChildren(dept1.getId(),departmentList));
        }
        return Result.ok(treeMenu);
    }

    public List<TreeMenu> getChildren(Integer id,List<GcjlTDepartment> departmentList){
        List<TreeMenu>  childrenTreeMenuList=new ArrayList<>();
        List<GcjlTDepartment> list = departmentList.stream().filter(p->p.getParentid().equals(id)).collect(Collectors.toList());
        if(list.size()>0){
            for(int i=0;i<list.size();i++){
                TreeMenu treeMenu=new TreeMenu();
                GcjlTDepartment dept = list.get(i);
                treeMenu.setLabel(dept.getDeptname())
                        .setId(dept.getId()).setPid(dept.getParentid())
                        .setChildren(getChildren(dept.getId(),departmentList));
                childrenTreeMenuList.add(treeMenu);
            }
        }
        return childrenTreeMenuList;
    }

    /**
     * 新增
     * @param map
     * @return
     */
    @RequestMapping(value = "/addDept")
    @PreAuthorize("@ss.hasPermi('JDWXC01ZZ01QX02')")
    public Result<Object> addDept(@RequestBody HashMap<String,Object> map) {
        String name = (String)map.get("name");
        Integer status = (Integer)map.get("status");
        String gps =(String)map.get("GPS");
        String pid = (String)map.get("id");
        Integer sort=null;
        if(map.get("sort")!=null && !map.get("sort").equals("") ){
            String a=(String)map.get("sort");
            sort=Integer.valueOf(a);
        }
        BigDecimal longitude=null;
        BigDecimal latitude=null;
        if(StringUtil.IsNullOrEmpty(name) || status==null ){
            return Result.error("部门名称或状态为空");
        }
        if(StringUtil.IsNullOrEmpty(gps)){
            GcjlTDepartment JLCompany = null;
                //获取 外部部门信息
            JLCompany = gcjlTDepartmentService.getById(200);

            longitude=JLCompany == null ? null : JLCompany.getLongitude();
            latitude=JLCompany == null ? null : JLCompany.getLatitude();
        }else{
            String[] split = gps.split(",");
            longitude = new BigDecimal(split[0]);
            latitude = new BigDecimal(split[1]);
        }
        GcjlTDepartment parentDept = gcjlTDepartmentService.getById(pid);
        GcjlTDepartment dept=new GcjlTDepartment();
        dept.setDeptname(name).setState(status).setLongitude(longitude).setLatitude(latitude).setSort(sort)
                        .setParentid(Integer.valueOf(pid)).setDeptfullpath(parentDept.getDeptfullpath()+"\\"+name).setCreatetime(new Date());
        gcjlTDepartmentService.save(dept);
        return Result.ok();
    }

    /**
     * 删除
     * @param map
     * @return
     */
    @RequestMapping(value = "/delDept")
    @PreAuthorize("@ss.hasPermi('JDWXC01ZZ01QX04')")
    public Result<Object> delDept(@RequestBody HashMap<String,Object> map) {
        if(map.get("id")==null){
            return Result.error("参数不能为空");
        }
        return Result.ok( gcjlTDepartmentService.removeById((Serializable) map.get("id")));
    }

    /**
     * 根据id查询单条记录
     * @param id
     * @return
     */
    @RequestMapping("/getDepartmentById")
    @PreAuthorize("@ss.hasRole('协同办公-行程管理-组织架构')")
    public Result<Object> getDepartmentById(Integer id) {
       GcjlTDepartment department =  gcjlTDepartmentService.getDepartmentById(id);
       return Result.ok(department);
    }


    /**
     * 根据Parentid查询单条记录
     * @return
     */
    @RequestMapping("/getDepartmentList")
    @PreAuthorize("@ss.hasPermi('JDWXC01ZZ01QX01')")
    public Result<Object> getDepartmentList(@RequestBody GcjlTDepartment ent){
        IPage<GcjlTDepartment> list = new Page<>();
        list.setCurrent(ent.getPageNum());
        list.setSize(ent.getPageSize());
        list = gcjlTDepartmentService.page(list, new LambdaQueryWrapper<GcjlTDepartment>()
               .eq(GcjlTDepartment::getParentid,ent.getId())
                .orderByAsc(GcjlTDepartment::getSort));
        for (GcjlTDepartment dep :list.getRecords() ) {
            dep.setGPS(dep.getLongitude()+","+dep.getLatitude());
            if(dep.getState()==1){
                dep.setStateTxt("启用");
            }else {
                dep.setStateTxt("停用");
            }
        }
        return Result.ok(list);
    }

    /**
     * 启用
     * @param map
     * @return
     */
    @RequestMapping(value = "/enable")
    @PreAuthorize("@ss.hasPermi('JDWXC01ZZ01QX05')")
    public Result<Object> enable(@RequestBody HashMap<String,Object> map) {
        if(map.get("id")==null){
            return Result.error("参数不能为空");
        }
        GcjlTDepartment dept = gcjlTDepartmentService.getById((Serializable) map.get("id"));
        dept.setState(1);
        return Result.ok( gcjlTDepartmentService.updateById(dept));
    }

    /**
     * 停用
     * @param map
     * @return
     */
    @RequestMapping(value = "/disable")
    @PreAuthorize("@ss.hasPermi('JDWXC01ZZ01QX06')")
    public Result<Object> disable(@RequestBody HashMap<String,Object> map) {
        if(map.get("id")==null){
            return Result.error("参数不能为空");
        }
        List<TreeMenu>  treeMenuList=new ArrayList<>();
        List<GcjlTDepartment> departmentList=gcjlTDepartmentService.list();

        List<TreeMenu> treeMenu = getChildren((Integer) map.get("id"),departmentList);
        if(treeMenu.size()==0){
            GcjlTDepartment dept = gcjlTDepartmentService.getById((Serializable) map.get("id"));
            dept.setState(0);
            return Result.ok( gcjlTDepartmentService.updateById(dept));
        }else {
            return Result.error("该机构下有子机构或者人员，不允许停用！");
        }
    }


    /**
     * 修改
     * @param map
     * @return
     */
    @RequestMapping(value = "/updDept")
    @PreAuthorize("@ss.hasPermi('JDWXC01ZZ01QX03')")
    public Result<Object> updDept(@RequestBody HashMap<String,Object> map) {
        if(map.get("id")==null || map.get("name")==null || map.get("gps")==null || map.get("state")==null){
            return Result.error("参数不能为空");
        }
        Integer sort=null;
        if(map.get("sort")!=null){
            String a=(String)map.get("sort");
            if (!"".equals(a)){
                sort = Integer.valueOf(a);
            }
//           if(a.equals("")){
//
//           } else{
//               sort=Integer.valueOf(a);
//           }
        }
        GcjlTDepartment dept = gcjlTDepartmentService.getById((Serializable) map.get("id"));
        dept.setDeptname(map.get("name").toString());
        dept.setState((Integer) map.get("state"));
        dept.setLongitude( new BigDecimal(map.get("gps").toString().split(",")[0]));
        dept.setLatitude( new BigDecimal(map.get("gps").toString().split(",")[1]));
        dept.setSort(sort);
        gcjlTDepartmentService.updateById(dept);
        return Result.ok();
    }
}
