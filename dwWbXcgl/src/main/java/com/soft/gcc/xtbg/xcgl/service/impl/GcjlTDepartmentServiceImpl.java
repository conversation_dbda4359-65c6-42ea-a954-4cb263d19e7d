package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.aspectj.lang.enums.DataSourceType;
import com.soft.gcc.xtbg.xcgl.dto.TjcxDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTDepartmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
*
*/
@Service
public class GcjlTDepartmentServiceImpl extends ServiceImpl<GcjlTDepartmentMapper, GcjlTDepartment>
implements GcjlTDepartmentService{
    @Autowired
    GcjlTDepartmentMapper gcjlTDepartmentMapper;

    @Override
    public GcjlTDepartment getDepartmentById(Integer id) {
        return baseMapper.getDepartmentById(id);
    }

    @Override
    public List<TjcxDto> getTjcxDtoDept(String deptName, String startTime, String endTime, Set<Integer> ids) {
        return gcjlTDepartmentMapper.getTjcxDtoDept(deptName,startTime,endTime, ids);
    }
    @Override
    public List<TjcxDto> getTjcxDtoPerson(String deptName, String startTime, String endTime, Set<Integer> ids) {
        return gcjlTDepartmentMapper.getTjcxDtoPerson(deptName,startTime,endTime, ids);
    }
    @Override
    public List<TjcxDto> getTjcxDtoAlarm(String deptName, String startTime, String endTime, Set<Integer> ids) {
        return gcjlTDepartmentMapper.getTjcxDtoAlarm(deptName,startTime,endTime, ids);
    }
}
