package com.soft.gcc.xtbg.xcgl.Controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.aspectj.lang.enums.DataSourceType;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.date.DateUtils;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.AliyunOSSUtils;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcdkzphzDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTXcbb;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTPlanService;
import com.soft.gcc.xtbg.xcgl.vo.GcjlTPlanVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @createDate 2023/7/27
 */

@Slf4j
@RequestMapping(value = "/xchz")
@RestController
public class XcglXchzController extends BaseController {
    @Autowired
    private IGcjlTPlanService planService;
    @Autowired
    private GcjlTDepartmentService departmentService;
    /**
     * 获取行程汇总
     * @return
     */
    
    @RequestMapping(value = "/getXchzList")
    @PreAuthorize("@ss.hasPermi('JDWXC01HZ01QX01')")
    public Result<Object> getList(@RequestBody Map<String, String> param) {
        try {
            if (StringUtils.isEmpty(param.get("deptId"))) {
                return Result.error("请先点击选择左侧部门!");
            }
            Integer deptId = ParseUtil.tryParseInt(param.get("deptId"));
            Integer pageNum = -1;
            Integer pageSize = -1;
            List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, deptId));
            if (CollectionUtils.isEmpty(departments)){
                return Result.error("该部门或许已不存在，请重新获取!");
            }
            String deptfullpath = departments.get(0).getDeptfullpath();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = null;
            Date endDate = null;
            if (StringUtils.isNotEmpty(param.get("startTime"))){
                startDate = sdf.parse(param.get("startTime"));
            }
            if (StringUtils.isNotEmpty(param.get("endTime"))){
                endDate = sdf.parse(param.get("endTime"));
            }
            if (StringUtils.isNotEmpty(param.get("pageNum"))){
                pageNum = ParseUtil.tryParseInt(param.get("pageNum"));
            }
            if (StringUtils.isNotEmpty(param.get("pageSize"))){
                pageSize = ParseUtil.tryParseInt(param.get("pageSize"));
            }
            IPage<GcjlTPlanVo> page = planService.getXchzShowList(deptfullpath,startDate,endDate,pageNum,pageSize);
            return Result.ok(page);
        }catch (Exception ex){
            log.error(ex.getMessage());
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }

    @RequestMapping(value = "/exportExcel")
    @PreAuthorize("@ss.hasPermi('JDWXC01HZ01QX02')")
    public void exportExcel(@RequestBody Map<String, String> param, HttpServletResponse response) {
        try {

            Integer deptId = ParseUtil.tryParseInt(param.get("deptId"));
            List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, deptId));
            String deptfullpath = departments.get(0).getDeptfullpath();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = null;
            Date endDate = null;
            if (StringUtils.isNotEmpty(param.get("startTime"))){
                startDate = sdf.parse(param.get("startTime"));
            }
            if (StringUtils.isNotEmpty(param.get("endTime"))){
                endDate = sdf.parse(param.get("endTime"));
            }
            planService.exportExcel(deptfullpath,startDate,endDate,response);
        }catch (Exception ex){
            log.error(ex.getMessage());
            ex.printStackTrace();
        }
    }


    @PreAuthorize("@ss.hasPermi('JDWXC01HZ01QX03')")
    @PostMapping("getInfoByLcId")
    public Result<Object> getInfo(@RequestBody Integer lcId) {
        if (lcId == null){
            Result.error("参数错误");
        }
        return Result.ok(planService.getInfo(lcId));
    }


    /**
     * 获取行程打卡照片汇总
     * @return
     */
    @RequestMapping(value = "/getdkzphzList")
    @PreAuthorize("@ss.hasPermi('JDWXC01DK01QX01')")
    public Result<Object> getList(@RequestBody GcjlTXcdkzphzDto dto) {
        try {
            List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, dto.getDeptId()));
            if (CollectionUtils.isEmpty(departments)){
                return Result.error("该部门或许已不存在，请重新获取!");
            }
            String deptfullpath = departments.get(0).getDeptfullpath();
            dto.setDeptFullPath(deptfullpath);
            //20240508新增
            IPage<GcjlPlanDto> page = planService.selectJoinPage(dto,user());
            return Result.ok(page);
        }catch (Exception ex){
            log.error(ex.getMessage());
            ex.printStackTrace();
            return Result.error(ex.getMessage());
        }
    }



    /**
     * 导出 行程打卡照片
     */
    @RequestMapping(value = "/downloadImgZip")
    @PreAuthorize("@ss.hasPermi('JDWXC01DK01QX02')")
    public void downloadImgZip(@RequestBody GcjlTXcdkzphzDto dto, HttpServletResponse response) throws IOException {
        String date = DateUtils.parseDateToStr("yyyyMMddHHmmss",new Date());
        String zipFile = date + ".zip";
        // 返给前端流
        // 设置响应的内容类型
        response.setContentType("application/zip");
        // 设置文件名，并进行URL编码
        String zipFileUtf8 = URLEncoder.encode(zipFile, "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + zipFileUtf8 + "\"");
        ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
        // 压缩包
        try {
            List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, dto.getDeptId()));
            if (CollectionUtils.isEmpty(departments)){
                 throw new RuntimeException("该部门或许已不存在，请重新获取!");
            }
            String deptfullpath = departments.get(0).getDeptfullpath();
            dto.setDeptFullPath(deptfullpath);
            List<GcjlPlanDto> list = planService.selectList(dto);

            for (GcjlPlanDto item : list) {

                String name = item.getUserName() + DateUtils.parseDateToStr("yyyyMMddHHmmss",item.getArrivedTime()) + item.getFileName();

                if(com.baomidou.mybatisplus.core.toolkit.StringUtils.checkValNotNull(item.getSubTName())){
                    name = item.getUserName()+DateUtils.parseDateToStr("yyyyMMddHHmmss",item.getArrivedTime())+"."+item.getSubTName();
                }

                zos.putNextEntry(new ZipEntry(name));
                byte[] bytes = AliyunOSSUtils.downloadFileStream(item.getFilePath());
                try (InputStream inputStream = new ByteArrayInputStream(bytes) ) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = inputStream.read(buffer)) != -1) {
                        zos.write(buffer, 0, len);
                    }
                }
                zos.closeEntry();

            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            zos.close();
        }


    }






}
