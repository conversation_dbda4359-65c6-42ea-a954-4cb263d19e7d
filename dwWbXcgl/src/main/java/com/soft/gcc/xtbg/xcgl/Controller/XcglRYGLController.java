package com.soft.gcc.xtbg.xcgl.Controller;

import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.aspectj.lang.enums.DataSourceType;
import com.soft.gcc.common.person.service.PersonService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTPersonDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTPersonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 行程管理-人员管理
 *
 *  注意，整个controller 都使用的从库数据源，如果要使用主库的，在每个方法前面标记
 */
//
@Slf4j
@RequestMapping("rygl")
@RestController
public class XcglRYGLController extends BaseController {

    @Autowired
    private IGcjlTPersonService iGcjlTPersonService;
    @Autowired
    private PersonService personService;

    /**
     * 行程管理-人员管理-根据部门id获取人员列表
     * @return
     */
    
    @RequestMapping("getPersonList")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX01')")
    public Result<Object> getPersonList(@RequestBody Map<String, String> map){
        //GCJL_T_Person

        return iGcjlTPersonService.getPersonList(map);
    }

    /**
     * 获取详情
     * @param id
     * @return
     */
    
    @GetMapping("getInfo/{id}")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX01')")
    public Result<Object> getInfo(@PathVariable("id") Integer id){
        return Result.ok( iGcjlTPersonService.getById(id));
    }

    /**
     * 清除数据
     * @param person
     * @return
     */
    
    @PostMapping("cleanEndData")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX01')")
    public Result<Object> cleanEndData(@RequestBody GcjlTPerson person){
       return Result.ok(iGcjlTPersonService.cleanEndData(person));
    }

    /**
     * 根据id删除人员
     * @param ids
     * @return
     */
    
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("deleteByIds")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX3')")
    public Result<Object> deleteByIds(@RequestBody List<Integer> ids){
        try {
            iGcjlTPersonService.removeByIds(ids);
        }catch (Exception ex){
            log.error("删除失败"+ex.getMessage());
            return Result.error("程序异常，删除失败！");
        }
        return Result.ok();
    }

    /**
     * 根据id启用用户
     * @return
     */
    
    @PostMapping("startPersonById")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX4')")
    public Result<Object> startPersonById(@RequestBody Integer id){
        return iGcjlTPersonService.startOrdisablePersonById(id,1);
    }

    /**
     * 根据id停用用户
     * @return
     */
    
    @PostMapping("disablePersonById")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX5')")
    public Result<Object> disablePersonById(@RequestBody  Integer id){
        return iGcjlTPersonService.startOrdisablePersonById(id,0);
    }

    /**
     * 获取原有人员信息
     * @param param
     * @return
     */
    
    @PostMapping("getPersonEntityList")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX01')")
    public Result<Object> getPersonEntityList(@RequestBody Map<String, String> param){
        return personService.selectPageList(param);
    }



    /**
     * 从原有人员信息添加到新的组织架构中
     */
    
    @PostMapping("creatPerson")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX01')")
    public Result<Object> creatPerson(@RequestBody GcjlTPersonDto gcjlTPersonDto){
        return iGcjlTPersonService.creatPerson(gcjlTPersonDto);
    }

    //todo:是否能导出

    /**
     * 上传
     */
    
    @PostMapping("importExcel")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX7')")
    public Result<Object> importExcel(MultipartFile file){
        return iGcjlTPersonService.importExcel(file);
    }

    /**
     * 根据人员id更新补贴费用上限
     */
    
    @PostMapping("/insertLimitByPersonId")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX10')")
    public Result<Object> insertLimitByPersonId(@RequestBody GcjlTPerson gcjlTPerson){
        return iGcjlTPersonService.insertLimitByPersonId(gcjlTPerson);
    }


    /**
     * 获取人员补贴上限记录
     */
    
    @PostMapping("/getPersonLimitLog")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX9')")
    public Result<Object> getPersonLimitLog(@RequestBody Map<String, String> param){
        return iGcjlTPersonService.getPersonLimitLog(param);
    }

    /**
     * 导出excel模板
     * @return
     */
    
    @PostMapping("exportExcelTemplate")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX6')")
    public Result<Object> exportExcelTemplate( HttpServletResponse response){
        try {
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("人员管理导入模板");
            //sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
            XSSFRow row = sheet.createRow(0);
            row = sheet.createRow(0);
            row.createCell(0).setCellValue("工号");
            row.createCell(1).setCellValue("姓名");
            row.createCell(2).setCellValue("部门");
            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "人员管理导入模板.xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            return Result.error(e.getMessage());
        }

        return Result.ok();
    }


    /**
     * 导出excel
     */
    
    @PostMapping("exportExcel")
    @PreAuthorize("@ss.hasPermi('JDWXC01RY01QX6')")
    public Result<Object> exportExcel(@RequestBody Map<String,String> param, HttpServletResponse response){
        try {

            iGcjlTPersonService.exportExcel(param,response);
        }catch (Exception ex){
            Result.error(ex.getMessage());
        }
        return Result.ok();
    }




}
