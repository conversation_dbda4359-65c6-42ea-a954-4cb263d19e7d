package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcjhDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTLcService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTLcMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【GCJL_T_LC(工程监理--计划主表-流程表)】的数据库操作Service实现
 * @createDate 2023-07-12 10:55:55
 */
@Service
public class GcjlTLcServiceImpl extends ServiceImpl<GcjlTLcMapper, GcjlTLc>
        implements IGcjlTLcService {

    @Autowired
    GcjlTDepartmentService departmentService;

    @Override
    public Result<Object> selectPageList(GcjlTXcjhDto xcjhDto) {
        int pageNum = ParseUtil.tryParseInt(xcjhDto.getPageNum().toString());
        int pageSize = ParseUtil.tryParseInt(xcjhDto.getPageSize().toString(), 1);
        Page<GcjlTLc> page = new Page<>(pageNum, pageSize);

        //查询部门全路径
        List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, xcjhDto.getDeptId()));
        if (CollectionUtils.isEmpty(departments)) {
            return Result.error("该部门或许已不存在，请重新获取!");
        }

        String deptfullpath = departments.get(0).getDeptfullpath();

        QueryWrapper<GcjlTLc> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().like(xcjhDto.getDeptId() != null, GcjlTLc::getUserFullPath, deptfullpath)
                .like(StringUtils.isNotEmpty(xcjhDto.getUserName()), GcjlTLc::getUserName, xcjhDto.getUserName())
                .ge(xcjhDto.getPlanStartDate() != null, GcjlTLc::getPlanTime, xcjhDto.getPlanStartDate())
                .le(xcjhDto.getPlanEndDate() != null, GcjlTLc::getPlanTime, xcjhDto.getPlanEndDate())
                .eq(xcjhDto.getApprovingState() != -1, GcjlTLc::getApprovingState, xcjhDto.getApprovingState())
                .eq(xcjhDto.getPerformState() != -1, GcjlTLc::getPerformState, xcjhDto.getPerformState())
                .eq(xcjhDto.getNotarizeState() != -1, GcjlTLc::getNotarizeState, xcjhDto.getNotarizeState());
        queryWrapper.lambda().orderByDesc(GcjlTLc::getPlanTime);
        return Result.ok(this.baseMapper.selectPage(page, queryWrapper));
    }

    @Override
    public void exportExcel(GcjlTXcjhDto xcjhDto , HttpServletResponse response) {
        //查询部门全路径
        List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, xcjhDto.getDeptId()));
        if (CollectionUtils.isEmpty(departments)) {
            throw new RuntimeException("该部门或许已不存在，请重新获取!");
        }

        String deptfullpath = departments.get(0).getDeptfullpath();

        QueryWrapper<GcjlTLc> queryWrapper = new QueryWrapper<>();
        // 使用 CASE 语句来获取 各个状态的中文描述
        queryWrapper.select("*, " +
                "CASE " +
                "WHEN ApprovingState = 0 THEN '未提交' " +
                "WHEN ApprovingState = 1 THEN '审核中' " +
                "WHEN ApprovingState = 2 THEN '已驳回' " +
                "WHEN ApprovingState = 3 THEN '已审核' " +
                "WHEN ApprovingState = 4 THEN '已过期' " +
                "WHEN ApprovingState = 5 THEN '已作废' " +
                "ELSE '未知状态' END AS approvingStateDescription, " +

                "CASE " +
                "WHEN NotarizeState = 0 THEN '未提交' " +
                "WHEN NotarizeState = 1 THEN '审核中' " +
                "WHEN NotarizeState = 2 THEN '已驳回' " +
                "WHEN NotarizeState = 3 THEN '确认通过' " +
                "WHEN NotarizeState = 4 THEN '已作废' " +
                "ELSE '未知状态' END AS notarizeStateDescription, " +

                "CASE " +
                "WHEN PerformState = 0 THEN '未执行' " +
                "WHEN PerformState = 1 THEN '执行中' " +
                "WHEN PerformState = 2 THEN '已执行' " +
                "WHEN PerformState = 3 THEN '已取消' " +
                "ELSE '未知状态' END AS performStateDescription,"+

                "PlanTime AS planTimeStr");

        queryWrapper.lambda().like(xcjhDto.getDeptId() != null, GcjlTLc::getUserFullPath, deptfullpath)
                .like(StringUtils.isNotEmpty(xcjhDto.getUserName()), GcjlTLc::getUserName, xcjhDto.getUserName())
                .ge(xcjhDto.getPlanStartDate() != null, GcjlTLc::getPlanTime, xcjhDto.getPlanStartDate())
                .le(xcjhDto.getPlanEndDate() != null, GcjlTLc::getPlanTime, xcjhDto.getPlanEndDate())
                .eq(xcjhDto.getApprovingState() != -1, GcjlTLc::getApprovingState, xcjhDto.getApprovingState())
                .eq(xcjhDto.getPerformState() != -1, GcjlTLc::getPerformState, xcjhDto.getPerformState())
                .eq(xcjhDto.getNotarizeState() != -1, GcjlTLc::getNotarizeState, xcjhDto.getNotarizeState());
        queryWrapper.lambda().orderByDesc(GcjlTLc::getPlanTime);
        List<GcjlTLc>  list = this.baseMapper.selectList(queryWrapper);


        List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();

        cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "部门", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("UserName", "人员姓名", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("planTimeStr", "计划时间", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("Note", "出行事由", 30));
        cmlist.add(new ToolHelper.ExportColumnMode("PlanMileage", "计划里程", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("PlanCost", "计划费用", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("PerformMileage", "执行里程", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("PerformCost", "执行费用", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("approvingStateDescription", "审批状态", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("performStateDescription", "执行状态", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("notarizeStateDescription", "确认状态", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("ApplyPostponeDays", "申请延期天数", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("ApprovePostponeDays", "审批延期天数", 15));
        ToolHelper.ExportExcelList(list, "行程计划", cmlist, false, response);

    }


    public void exportExcelChildPlan(GcjlTXcjhDto xcjhDto , HttpServletResponse response) {
        //查询部门全路径
        List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, xcjhDto.getDeptId()));
        if (CollectionUtils.isEmpty(departments)) {
            throw new RuntimeException("该部门或许已不存在，请重新获取!");
        }

        String deptfullpath = departments.get(0).getDeptfullpath();
        xcjhDto.setDeptFullPath(deptfullpath);

        List<GcjlPlanDto> list = this.baseMapper.selectAllList(xcjhDto);

        List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
        cmlist.add(new ToolHelper.ExportColumnMode("LcId", "LcId", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "部门", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("UserName", "人员姓名", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("planTimeStr", "计划时间", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("approvingStateDescription", "审批状态", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("performStateDescription", "执行状态", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("notarizeStateDescription", "确认状态", 10));

        cmlist.add(new ToolHelper.ExportColumnMode("PlanName", "计划名称", 40));
        cmlist.add(new ToolHelper.ExportColumnMode("mileage", "里程", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("cost", "费用", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("PointStateStr", "到达状态", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("ArrivedTimeStr", "到达时间", 20));
        cmlist.add(new ToolHelper.ExportColumnMode("IsGoCompanyStr", "点标识", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("ArrivedLocationName", "实际打卡地址", 40));

        ToolHelper.ExportExcelList(list, "行程计划", cmlist, false, response);

    }




}




