package com.soft.framework.config.datasource;

import java.util.Map;
import javax.sql.DataSource;

import com.soft.framework.aspectj.lang.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * 动态数据源
 * 
 * <AUTHOR>
 */
@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource
{
    public DynamicDataSource(DataSource defaultTargetDataSource, Map<Object, Object> targetDataSources)
    {
        super.setDefaultTargetDataSource(defaultTargetDataSource);
        super.setTargetDataSources(targetDataSources);
        super.afterPropertiesSet();
    }

    @Override
    protected Object determineCurrentLookupKey()
    {
        String dataSourceType = DynamicDataSourceContextHolder.getDataSourceType();
        log.info("当前使用的数据源: {}", dataSourceType);
        // 如果获取不到数据源，默认使用主库
        if (dataSourceType == null) {
            log.warn("未指定数据源，使用默认从库");
            return DataSourceType.SLAVE.name();
        }
        return dataSourceType;
        //return DynamicDataSourceContextHolder.getDataSourceType();
    }
}