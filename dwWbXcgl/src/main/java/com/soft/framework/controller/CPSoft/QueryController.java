package com.soft.framework.controller.CPSoft;

import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.PatternsRequestCondition;
import org.springframework.web.servlet.mvc.condition.RequestMethodsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用查询工具
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Service/CpSoft/Query")
public class QueryController
{
    @Autowired
    WebApplicationContext applicationContext;

    @RequestMapping(value = "GetParamProx",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult GetParamProx(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        //参数获取
        String sMode = request.getParameter("sMode");
        if(sMode.equals("0"))
        {
            ajaxResult=getAllUrl();
        }else if(sMode.equals("1"))
        {
            ajaxResult=getServerParams();
        }else
        {
            ajaxResult=AjaxResult.error("传输参数有误！");
        }
        return ajaxResult;
    }

    public AjaxResult getAllUrl() {
        RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        // 获取url与类和方法的对应信息
        Map<RequestMappingInfo,HandlerMethod> map = mapping.getHandlerMethods();

        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> m : map.entrySet()) {
            Map<String, String> map1 = new HashMap<String, String>();
            RequestMappingInfo info = m.getKey();
            HandlerMethod method = m.getValue();
            PatternsRequestCondition p = info.getPatternsCondition();
            for (String url : p.getPatterns()) {
                map1.put("url", url);
            }
            map1.put("className", method.getMethod().getDeclaringClass().getName()); // 类名
            map1.put("method", method.getMethod().getName()); // 方法名
            RequestMethodsRequestCondition methodsCondition = info.getMethodsCondition();
            for (RequestMethod requestMethod : methodsCondition.getMethods()) {
                map1.put("type", requestMethod.toString());
            }

            list.add(map1);
        }

        String jsonstr="";
        for (Map<String, String> mapo:list){
            jsonstr+=mapo.toString()+"\n";
        }

        return AjaxResult.success("执行查询成功！",jsonstr);
    }

    public AjaxResult getServerParams() {
        String jsonstr="";
        jsonstr+="Profile:"+ BaseConfig.getProfile()+";\n";
        jsonstr+="DownPath:"+BaseConfig.getDownloadPath()+";\n";
        jsonstr+="TemplatePath:"+BaseConfig.getTemplatePath()+";\n";
        jsonstr+="UploadPath:"+BaseConfig.getUploadPath()+";\n";
        jsonstr+="user.dir:"+System.getProperty("user.dir")+";\n";
        jsonstr+="java.version:"+System.getProperty("java.version")+";\n";
        jsonstr+="java.home:"+System.getProperty("java.home")+";\n";
        jsonstr+="java.vm.version:"+System.getProperty("java.vm.version")+";\n";
        jsonstr+="java.vm.name:"+System.getProperty("java.vm.name")+";\n";
        jsonstr+="os.name:"+System.getProperty("os.name")+";\n";
        jsonstr+="os.arch:"+System.getProperty("os.arch")+";\n";
        jsonstr+="os.version:"+System.getProperty("os.version")+";\n";
        jsonstr+="file.separator:"+System.getProperty("file.separator")+";\n";
        jsonstr+="path.separator:"+System.getProperty("path.separator")+";\n";
        jsonstr+="line.separator:"+System.getProperty("line.separator")+";\n";
        jsonstr+="user.name:"+System.getProperty("user.name")+";\n";
        jsonstr+="user.home:"+System.getProperty("user.home")+";\n";
        File fi = new File("");
        try{
            jsonstr+="CanonicalPath:"+fi.getCanonicalPath()+";\n";
            jsonstr+="AbsolutePath:"+fi.getAbsolutePath()+";\n";
        }catch(Exception ex){

        }
        return AjaxResult.success("执行查询成功！",jsonstr);
    }
}
