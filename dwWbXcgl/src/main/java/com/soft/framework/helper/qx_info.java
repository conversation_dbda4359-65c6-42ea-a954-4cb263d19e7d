package com.soft.framework.helper;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class qx_info {
    @ApiModelProperty(value = "用户成功标志")
    @JSONField(name="usernameSuccess")
    private Boolean usernameSuccess;

    @ApiModelProperty(value = "密码错误消息")
    @JSONField(name="passwordError")
    private String passwordError;

    @ApiModelProperty(value = "用户名称错误信息")
    @JSONField(name="usernameError")
    private String usernameError;
}
