package com.soft.framework.filter;

import com.alibaba.fastjson.JSON;
import com.soft.framework.config.ServerConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.LogHelper;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * token过滤器 验证token有效性
 * 
 * <AUTHOR>
 */
public class ProxyFilter extends OncePerRequestFilter
{
    private final String[] imgarr={".jpg",".jpeg",".ico",".bmp",".gif",".png",".tif"};
    private final String[] bytearr={".avi",".mpg",".mpeg",".wav",".mid",".mp3",".swf",".fla",".flv",".flc", ".mp4",".ts",".m3u8"};

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException
    {
        try{
            String tokens="";
            if(request.getParameter("jtoken")!=null)
            {
                tokens=request.getParameter("jtoken");
            }

            String requestURL = request.getRequestURL().toString();

            String rooturl= ServerConfig.getDomain(request);
            String context= request.getContextPath();
            StringBuffer sbreal=new StringBuffer();
            String realRequestUrl = getRealRequestUrl(rooturl,context,requestURL,sbreal);
            realRequestUrl=realRequestUrl+"&jtoken="+tokens;

            LogHelper.WriteSysLog("ProxyFilter,请求地址："+request.getRequestURI()+",代理地址:"+realRequestUrl);

            response.sendRedirect(realRequestUrl);
            return;
        }catch(Exception Ex)
        {

        }
    }

    /**
     * 截取请求地址，并根据需要配置即可
     * @param requestUrl
     * @return
     */
    private String getRealRequestUrl(String rooturl,String context,String requestUrl,StringBuffer sb) {
        String tmpsurl=requestUrl.toLowerCase();
        String rfurl=requestUrl.replaceAll(rooturl+"/","");
        String realurl="";

        int ftype=-1; //ftype:0,image,1:bytearray,2.pdf,3:attach;
        if(tmpsurl.contains(".pdf")){
            ftype=2;
        }

        //判断是否属于图片
        if(ftype==-1) {
            for (String imgext : imgarr) {
                if (tmpsurl.contains(imgext)) {
                    ftype = 0;
                    break;
                }
            }
        }

        //判断是否二进制流
        if(ftype==-1)
        {
            //判断是否属于图片
            for (String baext:bytearr) {
                if(tmpsurl.contains(baext)){
                    ftype=1;
                    break;
                }
            }
        }

        //判断是否二进制流
        if(ftype==-1)
        {
            ftype=3;
        }

        if(ftype==0)
        {
            realurl=context+"/Service/Base/UploadMan/getImage";
        }else if(ftype==1)
        {
            realurl=context+"/Service/Base/UploadMan/getByteStream";
        }else if(ftype==2)
        {
            realurl=context+"/Service/Base/UploadMan/getPdf";
        }else if(ftype==3)
        {
            realurl=context+"/Service/Base/UploadMan/getAttach";
        }

        sb.append(realurl);
        return realurl+"?rfurl="+rfurl;
    }

    private void ResponseNoLoginSessionInfo(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult = AjaxResult.error("检测到当前账户没有登录，终止操作", -1, true);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
}
