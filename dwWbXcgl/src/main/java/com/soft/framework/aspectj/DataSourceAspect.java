package com.soft.framework.aspectj;

import com.soft.framework.aspectj.lang.annotation.DataSource;
import com.soft.framework.config.datasource.DynamicDataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
@Slf4j
@Aspect
@Component
@Order(1)
public class DataSourceAspect {

    @Pointcut("@annotation(com.soft.framework.aspectj.lang.annotation.DataSource)")
    public void dataSourcePointCut() {
    }

//    @Around("dataSourcePointCut()")
//    public Object around(ProceedingJoinPoint point) throws Throwable {
//        MethodSignature signature = (MethodSignature) point.getSignature();
//        Method method = signature.getMethod();
//        DataSource dataSource = method.getAnnotation(DataSource.class);
//
//        if (dataSource != null) {
//            DynamicDataSourceContextHolder.setDataSourceType(dataSource.value().name());
//        }
//
//        try {
//            return point.proceed();
//        } finally {
//            DynamicDataSourceContextHolder.clearDataSourceType();
//        }
//    }

    @Around("dataSourcePointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        DataSource dataSource = method.getAnnotation(DataSource.class);

        // 打印当前方法名和数据源注解信息
        log.info("执行方法: {}, 数据源注解: {}", method.getName(), dataSource);

        // 保存当前数据源
        String currentDataSource = DynamicDataSourceContextHolder.getDataSourceType();
        log.info("切换前数据源: {}", currentDataSource);

        try {
            if (dataSource != null) {
                String targetDataSource = dataSource.value().name();
                log.info("切换到数据源: {}", targetDataSource);
                DynamicDataSourceContextHolder.setDataSourceType(targetDataSource);
            }

            Object result = point.proceed();
            log.info("方法执行完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("方法执行异常", e);
            throw e;
        } finally {
            // 恢复原来的数据源
            //DynamicDataSourceContextHolder.setDataSourceType(currentDataSource);
            //log.info("恢复数据源: {}", currentDataSource);
        }
    }


}
