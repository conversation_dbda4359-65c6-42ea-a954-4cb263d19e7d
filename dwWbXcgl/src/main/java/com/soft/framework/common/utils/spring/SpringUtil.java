package com.soft.framework.common.utils.spring;

import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.MetaHelper;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringUtil implements ApplicationContextAware {
    private static ApplicationContext applicationContext;// 声明一个静态变量保存

    /**
     * @param context spring的上下文
     * @Title: setContext
     * @Description: 由于直接在setApplicationContext中对静态变量赋值， findbugs报警告，这个方法只是为了避免findbugs报警告
     */
    private static void setContext(final ApplicationContext context) {
        applicationContext = context;
    }

    /**
     * @return Spring上下文
     * @Title: getApplicationContext
     * @Description: 获取Spring上下文
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public void setApplicationContext(ApplicationContext context) throws BeansException {
        try {
            setContext(context);
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return;
        }
    }


    /**
     * 获取对象
     * @param name
     * @return Object 一个以所给名字注册的bean的实例
     * @throws org.springframework.beans.BeansException
     *
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) throws BeansException
    {
        return (T) applicationContext.getBean(name);
    }

    /**
     * @param requiredType 需要的bean的类型
     * @param <T>          指定返回的bean类型
     * @return Object bean名称对应服务类
     * @Title: getBean
     * @Description: 根据提供的bean的类型得到对应bean
     */
    public static <T> T getBean(final Class<T> requiredType) {
        return applicationContext.getBean(requiredType);
    }

    /**
     * @param name  bean名称
     * @param clazz 返回的bean类型,若类型不匹配,将抛出异常
     * @param <T>   指定返回的bean类型
     * @return T bean名称对应于指定类型的服务类
     * @Title: getBean
     * @Description: 根据提供的bean名称得到对应于指定类型的服务类
     */
    public static <T> T getBean(final String name, final Class<T> clazz) {
        return applicationContext.getBean(name, clazz);
    }

    /**
     * @param name bean的名字
     * @return boolean 是否包含该bean
     * @Title: containsBean
     * @Description: 检查bean工厂里是否包含名字为name的bean
     */
    public static boolean containsBean(final String name) {
        return applicationContext.containsBean(name);
    }

    /**
     * @param name 属性名称
     * @return 属性值
     * @Title: getProperty
     * @Description: 根据名称，从Spring启动加载的所有Property中找到指定属性
     */
    public static String getProperty(String name) {
        return applicationContext.getEnvironment().getProperty(name);
    }

}
