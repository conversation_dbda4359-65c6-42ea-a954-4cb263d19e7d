package com.soft.framework.common.features;

/**
 * Created by Administrator on 2016/1/26.
 */
public interface IDBFeatures {
    public String FormatDateToStr(int strlen, String colname);

    public String FormatStrToDate(String strval);

    public String GetColType(String schema,String strtab, String colname);

    public String ToPageSql(String strSql, String orderStr,int intPageSize, int intCurrentPage);

    public String ToLimitSql(String strSql, String orderStr,int start, int end);

    public String PackFunc(String funcname);

    public String PackProc(String procname, String valstr);

    public String PackMetaQry(String sqlstr);

    public String PackTreeOrder(String orderstr);

    public String GetDefaultDT();
}
