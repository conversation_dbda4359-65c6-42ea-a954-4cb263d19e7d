<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlLcWorkflowMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.LcWorkflow">
            <result property="ID" column="ID" jdbcType="INTEGER"/>
            <result property="lc_defineID" column="lc_defineID" jdbcType="INTEGER"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="lc_jdID" column="lc_jdID" jdbcType="INTEGER"/>
            <result property="lc_jdmc" column="lc_jdmc" jdbcType="VARCHAR"/>
            <result property="groupID" column="groupID" jdbcType="INTEGER"/>
            <result property="groupName" column="groupName" jdbcType="VARCHAR"/>
            <result property="personZgh" column="personZgh" jdbcType="VARCHAR"/>
            <result property="personName" column="personName" jdbcType="VARCHAR"/>
            <result property="transdate" column="transdate" jdbcType="TIMESTAMP"/>
            <result property="feed" column="feed" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
            <result property="PNO" column="PNO" jdbcType="VARCHAR"/>
            <result property="startdate" column="startdate" jdbcType="TIMESTAMP"/>
            <result property="lcByRole" column="LcByRole" jdbcType="INTEGER"/>
            <result property="isback" column="isback" jdbcType="INTEGER"/>
            <result property="useback" column="useback" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,lc_defineID,ywID,
        lc_jdID,lc_jdmc,groupID,
        groupName,personZgh,personName,
        transdate,feed,number,
        BXType,PNO,startdate,
        LcByRole,isback,useback
    </sql>
</mapper>
