<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlLcdefineMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.Lcdefine">
            <id property="ID" column="ID" jdbcType="INTEGER"/>
            <result property="lcID" column="LcID" jdbcType="INTEGER"/>
            <result property="lcName" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywb" column="ywb" jdbcType="VARCHAR"/>
            <result property="ywUrl" column="ywUrl" jdbcType="VARCHAR"/>
            <result property="xszd" column="xszd" jdbcType="VARCHAR"/>
            <result property="isUse" column="isUse" jdbcType="INTEGER"/>
            <result property="app_url" column="app_url" jdbcType="VARCHAR"/>
            <result property="app_ywb" column="app_ywb" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,LcID,lcName,
        ywb,ywUrl,xszd,
        isUse,app_url,app_ywb
    </sql>
</mapper>
