<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTJlconfigMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTJlconfig">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="jlName" column="JlName" jdbcType="VARCHAR"/>
            <result property="latitude" column="Latitude" jdbcType="DECIMAL"/>
            <result property="longitude" column="Longitude" jdbcType="DECIMAL"/>
            <result property="provinces" column="Provinces" jdbcType="INTEGER"/>
            <result property="city" column="City" jdbcType="INTEGER"/>
            <result property="area" column="Area" jdbcType="INTEGER"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,JlName,Latitude,
        Longitude,Provinces,City,
        Area,CreateTime
    </sql>
</mapper>
