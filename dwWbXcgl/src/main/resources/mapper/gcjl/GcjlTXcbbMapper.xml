<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTXcbbMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTXcbb">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="bizNo" column="BizNo" jdbcType="VARCHAR"/>
            <result property="userId" column="UserId" jdbcType="INTEGER"/>
            <result property="userName" column="UserName" jdbcType="VARCHAR"/>
            <result property="userFullPath" column="UserFullPath" jdbcType="VARCHAR"/>
            <result property="deptId" column="DeptId" jdbcType="INTEGER"/>
            <result property="topDeptId" column="TopDeptId" jdbcType="INTEGER"/>
            <result property="planStartDate" column="PlanStartDate" jdbcType="TIMESTAMP"/>
            <result property="planEndDate" column="PlanEndDate" jdbcType="TIMESTAMP"/>
            <result property="note" column="Note" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="address" column="Address" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Base_Column_List">
      Id, BizNo,UserId,UserName,UserFullPath,DeptId,DeptName,
      TopDeptId,PlanStartDate, PlanEndDate,Note,CreateTime, Address
    </sql>



</mapper>
