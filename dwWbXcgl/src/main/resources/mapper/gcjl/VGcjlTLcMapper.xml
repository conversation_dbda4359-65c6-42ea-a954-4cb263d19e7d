<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.VGcjlTLcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.VGcjlTLc">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="bizNo" column="BizNo" jdbcType="VARCHAR"/>
            <result property="userId" column="UserId" jdbcType="INTEGER"/>
            <result property="userName" column="UserName" jdbcType="VARCHAR"/>
            <result property="userFullPath" column="UserFullPath" jdbcType="VARCHAR"/>
            <result property="planTime" column="PlanTime" jdbcType="DATE"/>
            <result property="note" column="Note" jdbcType="VARCHAR"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="planMileage" column="PlanMileage" jdbcType="DECIMAL"/>
            <result property="planCost" column="PlanCost" jdbcType="DECIMAL"/>
            <result property="performMileage" column="PerformMileage" jdbcType="DECIMAL"/>
            <result property="performCost" column="PerformCost" jdbcType="DECIMAL"/>
            <result property="bizType" column="BizType" jdbcType="VARCHAR"/>
            <result property="approvingState" column="ApprovingState" jdbcType="INTEGER"/>
            <result property="approveUserId" column="ApproveUserId" jdbcType="INTEGER"/>
            <result property="approveNote" column="ApproveNote" jdbcType="VARCHAR"/>
            <result property="approveTime" column="ApproveTime" jdbcType="TIMESTAMP"/>
            <result property="approveDeptId" column="ApproveDeptId" jdbcType="INTEGER"/>
            <result property="topDeptId" column="TopDeptId" jdbcType="INTEGER"/>
            <result property="deptId" column="DeptId" jdbcType="INTEGER"/>
            <result property="deptName" column="DeptName" jdbcType="VARCHAR"/>
            <result property="phone" column="Phone" jdbcType="VARCHAR"/>
            <result property="performState" column="PerformState" jdbcType="INTEGER"/>
            <result property="lc_defineID" column="Lc_defineID" jdbcType="INTEGER"/>
            <result property="lc_Name" column="Lc_Name" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="isMany" column="isMany" jdbcType="INTEGER"/>
            <result property="lc_jdmc" column="lc_jdmc" jdbcType="VARCHAR"/>
            <result property="lc_jdid" column="lc_jdid" jdbcType="INTEGER"/>
            <result property="lc_isback" column="lc_isback" jdbcType="INTEGER"/>
            <result property="lc_tojdid" column="lc_tojdid" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,BizNo,UserId,
        UserName,UserFullPath,PlanTime,
        Note,CreateTime,PlanMileage,
        PlanCost,PerformMileage,PerformCost,
        BizType,ApprovingState,ApproveUserId,
        ApproveNote,ApproveTime,ApproveDeptId,
        TopDeptId,DeptId,DeptName,
        Phone,PerformState,Lc_defineID,
        Lc_Name,ywID,sendPerson,
        sendPersonZgh,AllPersonZgh,isMany,
        lc_jdmc,lc_jdid,lc_isback,
        lc_tojdid,number,BXType
    </sql>
</mapper>
