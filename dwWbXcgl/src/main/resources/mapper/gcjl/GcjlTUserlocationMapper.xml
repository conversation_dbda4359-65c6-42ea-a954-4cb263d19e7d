<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTUserlocationMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTUserlocation">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="userId" column="UserId" jdbcType="INTEGER"/>
            <result property="latitude" column="Latitude" jdbcType="DECIMAL"/>
            <result property="longitude" column="Longitude" jdbcType="DECIMAL"/>
            <result property="planPointId" column="PlanPointId" jdbcType="INTEGER"/>
            <result property="monitorSpan" column="MonitorSpan" jdbcType="BIGINT"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="locationTime" column="LocationTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,UserId,Latitude,
        Longitude,PlanPointId,MonitorSpan,
        CreateTime,LocationTime
    </sql>
</mapper>
