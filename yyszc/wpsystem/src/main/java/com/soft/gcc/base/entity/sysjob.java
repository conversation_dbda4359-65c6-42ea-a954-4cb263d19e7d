package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * sysjob
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sysjob")
@ApiModel(value="sysjob对象", description="sysjob")
public class sysjob extends Model<sysjob> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务序号")
    @TableId(value = "JobId", type = IdType.AUTO)
    @JSONField(name="JobId")
    private Long JobId;

    @ApiModelProperty(value = "任务名称")
    @JSONField(name="JobName")
    private Integer JobName;

    @ApiModelProperty(value = "任务组名")
    @JSONField(name="JobGroup")
    private String JobGroup;

    @ApiModelProperty(value = "调用目标字符串")
    @JSONField(name="InvokeTarget")
    private String InvokeTarget;

    @ApiModelProperty(value = "执行表达式")
    @JSONField(name="CronExpression")
    private String CronExpression;

    @ApiModelProperty(value = "计划策略0=默认,1=立即触发执行,2=触发一次执行,3=不触发立即执行")
    @JSONField(name="MisfirePolicy")
    private String MisfirePolicy;

    @ApiModelProperty(value = "并发执行0=允许,1=禁止")
    @JSONField(name="Concurrent")
    private String Concurrent;

    @ApiModelProperty(value = "任务状态0=正常,1=暂停")
    @JSONField(name="Status")
    private String Status;


    @Override
    protected Serializable pkVal() {
        return this.JobId;
    }

}
