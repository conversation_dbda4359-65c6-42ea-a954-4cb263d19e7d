package com.soft.gcc.base.service.impl;

import com.soft.gcc.base.entity.NFT_ModuleGroup;
import com.soft.gcc.base.mapper.NFT_ModuleGroupMapper;
import com.soft.gcc.base.service.INFT_ModuleGroupService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 模块--模块分组展示 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Service
public class NFT_ModuleGroupServiceImpl extends ServiceImpl<NFT_ModuleGroupMapper, NFT_ModuleGroup> implements INFT_ModuleGroupService {

}
