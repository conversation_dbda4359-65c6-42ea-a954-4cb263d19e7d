package com.soft.framework.controller.Schdeule;

import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.yyszc.wpbase.entity.Lcjd;
import com.yyszc.wpbase.entity.NFT_ModuleLCLink;
import com.yyszc.wpbase.entity.NFV_WorkFlow;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@Configuration
@RequestMapping(value="/Service/Schdeule/SchdeuleMan")
@Api(tags ="基本框架接口->待办管理接口")
public class SchdeuleController {
    @RequestMapping(value="/GetNowSchdeuleManage")
    @ResponseBody
    @ApiOperation(value ="GetNowSchdeuleManage",notes="获取当前模块待办信息【返回目录及第一项第一页明细】接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetNowSchdeuleManage(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            String setLcjd = request.getParameter("setLcJD");
            String selecttj = request.getParameter("selectTJ");
            String mid = ConfigHelper.getModuleId();

            //自定义对象
            ToolHelper.NowSchdeuleManage NowObj = new ToolHelper.NowSchdeuleManage();
            PersonEntity person = SessionHelper.getSessionPerson();
            if (person == null)
            {
                ajaxResult =AjaxResult.error("未发现登录信息!");
                return ajaxResult;
            }

            String rpstr = person.getRolePermissionString();
            String gpstr = person.getGroupId().toString()+ "~";

            String cpermstr = "0";
            String[] col_r = rpstr.split(",");
            for (int i = 0; i < col_r.length; i++)
            {
                if (MetaHelper.IsValidateInt(col_r[i]))
                {
                    if (cpermstr == "") {
                        cpermstr = col_r[i];
                    } else {
                        cpermstr = cpermstr + "," + col_r[i];
                    }
                }
            }

            final String permstr = cpermstr;

            boolean success = true;
            String text = "";

            List<NFV_WorkFlow> _dblist = new ArrayList<NFV_WorkFlow>();

            String strsql = "";
            String strsql1 = "";
            String strsql2 = "";
            String LcUser = person.getLoginName();

            strsql1 = "select distinct a.lc_defineID,a.ywID,a.lc_jdid,a.lc_jdmc,c.startdate,c.wfexists,";
            strsql1 += "b.LcName,1111 as LcCount, b.ywb as LcProjParam,a.LcProjName,'' as LcProjUrl ";
            strsql1 += "from NFV_WorkFlow a,(select lc_defineID,lc_jdID,ywID,startdate,transdate,feed,BXTYPE,PNO,LcByRole,1 as wfexists from Lc_workFlow ";
            strsql1 += "where transdate is null and (personZgh='"+LcUser+"' or personZgh='')) c,Lcdefine b ";
            strsql1 += "where a.lc_defineID=b.lcid and a.lc_defineID=c.lc_defineID and a.lc_jdid=c.lc_jdID and a.ywID=c.ywID and isnull(a.lc_jdid,0)>0 and dbo.FUNC_LC_ISRUN(a.lc_defineID,a.lc_jdid,a.ywid)=1 ";
            if (!mid.equals(""))
            {
                String lclist = sqlhelper.ExecuteScalarList("select lk_lcid from NFV_ModuleLCLink where LK_MID in(" + mid + ")");
                strsql1 += " and a.lc_defineID in(select lk_lcid from NFV_ModuleLCLink where LK_MID in(" + mid + "))";
            }
            strsql2 = strsql1;
            strsql1 += ToolHelper.SmartLikeStr(LcUser);
            strsql2 += " and a.sendGroupIDs like '%" + gpstr + "%' and a.sendPersonZgh=''";
            strsql1 += " order by lc_defineID,lc_jdid";
            strsql2 += " order by lc_defineID,lc_jdid";


            List<NFV_WorkFlow> _list1 =WpServiceHelper.GetWorkFlowList(strsql1);
            List<NFV_WorkFlow> _list2 =WpServiceHelper.GetWorkFlowList(strsql2);
            List<NFV_WorkFlow> list = new ArrayList<NFV_WorkFlow>();
            if (_list1.size() > 0) {
                list.addAll(_list1);
            }
            _list2 = _list2.stream().filter(u ->u.getLc_jdid()!=null&&permstr.contains(u.getLc_jdid().toString())).collect(Collectors.toList());
            if (_list2.size() > 0) {
                list.addAll(_list2);
            }

            try
            {
                for (NFV_WorkFlow pi: list)
                {
                    String tmpstr = pi.getLcProjParam();
                    if (StringUtil.IsNullOrEmpty(pi.getLcProjName())&&!StringUtil.IsNullOrEmpty(tmpstr))
                    {
                        String[] arr = tmpstr.split("\\||:");
                        if (arr.length >= 3)
                        {
                            String arr2str = arr[2].replace("^", "'");
                            arr2str = arr2str.replace("&1", pi.getLc_defineID().toString()).replace("&2", pi.getLc_jdid().toString()).replace("&3", pi.getYwID().toString());
                            strsql = "select " + arr2str + " from " + arr[0] + " where " + arr[1] + "=" + pi.getYwID().toString();
                            String tmpc = sqlhelper.ExecuteScalar(strsql);
                            pi.setLcProjName(tmpc);
                        }
                    }

                    if (!pi.getLcProjName().equals(""))
                    {
                        if (!selecttj.equals(""))
                        {
                            if (pi.getLcProjName().indexOf(selecttj) >= 0)
                            {
                                _dblist.add(pi);
                            }
                        }
                        else
                        {
                            _dblist.add(pi);
                        }
                    }
                }
            }
            catch (Exception Ex)
            {

            }

            List<String> listStr = new ArrayList<String>();
            for(NFV_WorkFlow u:_dblist)
            {
                String Lcjdstr=u.getLc_jdid().toString();
                if(listStr.indexOf(Lcjdstr)<0)
                {
                    listStr.add(Lcjdstr);
                }
            }

            String strButton = "";
            String strRWD = "";

            for (int j = 0; j < listStr.size(); j++)
            {
                String jdstr=listStr.get(j).toString();
                Lcjd jd =sqlhelper.GetObject(Lcjd.class,"select * from Lcjd where LcjdID="+jdstr);
                List<NFV_WorkFlow> Lcjdlist=(List<NFV_WorkFlow>)CollectionUtils.select(_dblist, new Predicate() {
                    public boolean evaluate(Object arg0) {
                        NFV_WorkFlow u=(NFV_WorkFlow)arg0;
                        return jdstr.equals(u.getLc_jdid().toString());
                    }
                });

                strRWD += "<li onclick=\"LcManageGetNext(1," + jd.getLcjdID().toString() + ",1)\"><a><span class='leftspan'></span> " + jd.getJdmc().toString() + "  <span class='rightspan'>" + Lcjdlist.size() + "</span></a></li>";
            }

            String strList = "";
            int MaxNum = 0;
            String tmenu = "";
            String tnode = "";

            final String Lcjdid = listStr.size()>0?listStr.get(0):"0";
            if (listStr.size() > 0)
            {
                List<NFV_WorkFlow> cklist=(List<NFV_WorkFlow>)CollectionUtils.select(_dblist, new Predicate() {
                    public boolean evaluate(Object arg0) {
                        NFV_WorkFlow u=(NFV_WorkFlow)arg0;
                        return u.getLc_jdid().toString().equals(Lcjdid);
                    }
                });

                int Count = cklist.size();

                if (Count > 0)
                {
                    NFT_ModuleLCLink mcl=WpServiceHelper.GetNFT_ModuleLCLinkByLc(cklist.get(0).getLc_defineID().toString());
                    if(mcl!=null)
                    {
                        tmenu=mcl.getLK_TMENU();
                        tnode=mcl.getLK_TNODE();
                    }
                }

                MaxNum = (int)Math.ceil((double)Count/10);
                strButton = cklist.get(0).getLc_jdmc().toString()+ "(" + cklist.size()+ ")";

                for (int i = 0; i < 10; i++)
                {
                    if (i < Count)
                    {
                        Date startdate = Date.from(cklist.get(i).getStartdate().atZone(ZoneId.systemDefault()).toInstant());
                        Date nowdate=new Date();

                        int days = 0;
                        days = ToolHelper.getRealDays(startdate,nowdate);
                        days = days + 1;

                        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");

                        String warnstr = "list-element";
                        String clstr = "list-handle";
                        strList += " <li class=\"" + warnstr + "\">";
                        strList += cklist.get(i).getLcProjName();
                        strList += "  <div class=\"list-detail\">";
                        strList += "  <a href=\"javascript:onZZShowButtonClick('" + tmenu + "','" + tnode + "','" + cklist.get(i).getYwID() + "','" + cklist.get(i).getLc_jdid()+ "')\" class=\"" + clstr + "\">处理</a>";
                        if(startdate.compareTo(sdf.parse("2000-01-01"))<0)
                        {

                        }
                        else
                        {
                            strList += "  <span class=\"list-days\"></span>" + DateUtil.date2Str(startdate,"YYYY-MM-dd")+ "，持续天数：" + days + "天";
                        }
                        strList += "</div></li>";
                    }
                    else
                    {
                        break;
                    }
                }
            }

            String strB = "";
            for (int i = 0; i < MaxNum; i++)
            {
                int j = i + 1;
                strB += "<button class=\"list-nav-btn\" value=\"" + j + "\" onclick=\"LcManageGetNext(" + j + "," + Lcjdid + ")\">" + j + "</button>";
            }

            NowObj.hzContent = strRWD;
            NowObj.schdeuleContent = strList;
            NowObj.titleContent = strButton;
            NowObj.sgContent = strB;

            List<ToolHelper.NowSchdeuleManage> _rlist=new ArrayList<ToolHelper.NowSchdeuleManage>();
            _rlist.add(NowObj);

            ajaxResult=AjaxResult.success("获取数据成功！",_rlist);
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetNowSchdeuleManageNext")
    @ResponseBody
    @ApiOperation(value ="GetNowSchdeuleManageNext",notes="获取当前模块待办信息【指定目录项指定页明细】接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetNowSchdeuleManageNext(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            String setlcjd = request.getParameter("setLcJD");
            String selecttj = request.getParameter("selectTJ");

            String num = "1";
            String numo=request.getParameter("num");
            if (StringUtil.IsNullOrEmpty(numo)) {
            }
            else
            {
                num = numo;
            }

            //自定义对象
            ToolHelper.NowSchdeuleManage NowObj = new ToolHelper.NowSchdeuleManage();
            PersonEntity person = SessionHelper.getSessionPerson();
            if (person == null)
            {
                ajaxResult =AjaxResult.error("未发现登录信息!");
                return ajaxResult;
            }

            String rpstr = person.getRolePermissionString();
            String gpstr = person.getGroupId().toString()+ "~";

            String cpermstr = "0";
            String[] col_r = rpstr.split(",");
            for (int i = 0; i < col_r.length; i++)
            {
                if (MetaHelper.IsValidateInt(col_r[i]))
                {
                    if (cpermstr == "") {
                        cpermstr = col_r[i];
                    } else {
                        cpermstr = cpermstr + "," + col_r[i];
                    }
                }
            }
            final String permstr=cpermstr;

            boolean success = true;
            String text = "";
            String strWorkContent = "";
            String strButton = "";
            int MaxNum = 0;

            List<NFV_WorkFlow> _dblist = new ArrayList<NFV_WorkFlow>();

            String strsql = "";
            String strsql1 = "";
            String strsql2 = "";
            String LcUser = person.getLoginName();
            LcUser = "fhadmin";

            strsql1 = "select distinct a.lc_defineID,a.ywID,a.lc_jdid,a.lc_jdmc,c.startdate,c.wfexists,";
            strsql1 += "b.LcName,1111 as LcCount, b.ywb as LcProjParam,a.LcProjName,'' as LcProjUrl ";
            strsql1 += "from NFV_WorkFlow a,(select lc_defineID,lc_jdID,ywID,startdate,transdate,feed,BXTYPE,PNO,LcByRole,1 as wfexists from Lc_workFlow ";
            strsql1 += "where transdate is null and (personZgh='"+LcUser+"' or personZgh='')) c,Lcdefine b ";
            strsql1 += "where a.lc_defineID=b.lcid and a.lc_defineID=c.lc_defineID and a.lc_jdid=c.lc_jdID and a.ywID=c.ywID and isnull(a.lc_jdid,0)>0 and dbo.FUNC_LC_ISRUN(a.lc_defineID,a.lc_jdid,a.ywid)=1 ";
            if(!StringUtil.IsNullOrEmpty(setlcjd)) {
                strsql1+=" and a.lc_jdid="+setlcjd;
            }
            strsql2 = strsql1;
            strsql1 += ToolHelper.SmartLikeStr(LcUser);
            strsql2 += " and a.sendGroupIDs like '%" + gpstr + "%' and a.sendPersonZgh=''";
            strsql1 += " order by lc_defineID,lc_jdid";
            strsql2 += " order by lc_defineID,lc_jdid";

            List<NFV_WorkFlow> _list1 =WpServiceHelper.GetWorkFlowList(strsql1);
            List<NFV_WorkFlow> _list2 =WpServiceHelper.GetWorkFlowList(strsql2);
            List<NFV_WorkFlow> list = new ArrayList<NFV_WorkFlow>();
            if (_list1.size() > 0) {
                list.addAll(_list1);
            }
            _list2 = _list2.stream().filter(u ->u.getLc_jdid()!=null&&permstr.contains(u.getLc_jdid().toString())).collect(Collectors.toList());
            if (_list2.size() > 0) {
                list.addAll(_list2);
            }

            try
            {
                for (NFV_WorkFlow pi: list)
                {
                    String tmpstr = pi.getLcProjParam();
                    if (StringUtil.IsNullOrEmpty(pi.getLcProjName())&&!StringUtil.IsNullOrEmpty(tmpstr))
                    {
                        String[] arr = tmpstr.split("\\||:");
                        if (arr.length >= 3)
                        {
                            String arr2str = arr[2].replace("^", "'");
                            arr2str = arr2str.replace("&1", pi.getLc_defineID().toString()).replace("&2", pi.getLc_jdid().toString()).replace("&3", pi.getYwID().toString());
                            strsql = "select " + arr2str + " from " + arr[0] + " where " + arr[1] + "=" + pi.getYwID().toString();
                            String tmpc = sqlhelper.ExecuteScalar(strsql);
                            pi.setLcProjName(tmpc);
                        }
                    }

                    if (!pi.getLcProjName().equals(""))
                    {
                        if (!selecttj.equals(""))
                        {
                            if (pi.getLcProjName().indexOf(selecttj) >= 0)
                            {
                                _dblist.add(pi);
                            }
                        }
                        else
                        {
                            _dblist.add(pi);
                        }
                    }
                }
            }
            catch (Exception Ex)
            {

            }

            List<String> listStr = new ArrayList<String>();
            for(NFV_WorkFlow u:_dblist)
            {
                String Lcjdstr=u.getLc_jdid().toString();
                if(listStr.indexOf(Lcjdstr)<0)
                {
                    listStr.add(Lcjdstr);
                }
            }

            final String Lcjdid = listStr.size()>0?listStr.get(0):"0";
            String strLcjdmc = "";
            String tmenu = "";
            String tnode = "";
            if (listStr.size() > 0)
            {
                List<NFV_WorkFlow> cklist=(List<NFV_WorkFlow>)CollectionUtils.select(_dblist, new Predicate() {
                    public boolean evaluate(Object arg0) {
                        NFV_WorkFlow u=(NFV_WorkFlow)arg0;
                        return u.getLc_jdid().toString().equals(Lcjdid);
                    }
                });

                int Count = cklist.size();

                strButton = "";

                MaxNum = (int)Math.ceil((double)Count/10);
                strLcjdmc = cklist.get(0).getLc_jdmc().toString()+ "(" + cklist.size()+ ")";
                strButton = strLcjdmc;

                int start = 10*(Integer.parseInt(num) - 1);
                int limit = 10*Integer.parseInt(num);

                if (Count > 0)
                {
                    NFT_ModuleLCLink mcl=WpServiceHelper.GetNFT_ModuleLCLinkByLc(cklist.get(0).getLc_defineID().toString());
                    if(mcl!=null)
                    {
                        tmenu=mcl.getLK_TMENU();
                        tnode=mcl.getLK_TNODE();
                    }
                }

                for (int i = start; i < limit; i++)
                {
                    if (i < Count)
                    {
                        Date startdate = Date.from(cklist.get(i).getStartdate().atZone(ZoneId.systemDefault()).toInstant());
                        Date nowdate=new Date();

                        int days = 0;
                        days = ToolHelper.getRealDays(startdate,nowdate);
                        days = days + 1;

                        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");

                        String warnstr = "list-element";
                        String clstr = "list-handle";
                        strWorkContent += " <li class=\"" + warnstr + "\">";
                        strWorkContent += cklist.get(i).getLcProjName();
                        strWorkContent += "  <div class=\"list-detail\">";
                        strWorkContent += "  <a href=\"javascript:onZZShowButtonClick('" + tmenu + "','" + tnode + "','" + cklist.get(i).getYwID() + "','" + cklist.get(i).getLc_jdid()+ "')\" class=\"" + clstr + "\">处理</a>";
                        if(startdate.compareTo(sdf.parse("2000-01-01"))<0)
                        {

                        }
                        else
                        {
                            strWorkContent += "  <span class=\"list-days\"></span>" + DateUtil.date2Str(startdate,"YYYY-MM-dd")+ "，持续天数：" + days + "天";
                        }
                        strWorkContent += "</div></li>";
                    }
                    else
                    {
                        break;
                    }
                }
            }

            String strB = "";
            for (int i = 0; i < MaxNum; i++)
            {
                int j = i + 1;
                strB += "<button class=\"list-nav-btn\" value=\"" + j + "\" onclick=\"LcManageGetNext(" + j + ",'" + setlcjd + "')\">" + j + "</button>";
            }

            NowObj.schdeuleContent = strWorkContent;
            NowObj.titleContent = strButton;
            NowObj.sgContent = strB;

            List<ToolHelper.NowSchdeuleManage> _rlist=new ArrayList<ToolHelper.NowSchdeuleManage>();
            _rlist.add(NowObj);

            ajaxResult=AjaxResult.success("获取数据成功！",_rlist);
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
