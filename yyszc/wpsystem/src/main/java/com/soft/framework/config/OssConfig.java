package com.soft.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "oconfig")
public class OssConfig
{
    public static String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        OssConfig.endPoint = endPoint;
    }

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        OssConfig.accessKeyId = accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        OssConfig.accessKeySecret = accessKeySecret;
    }

    public static String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        OssConfig.bucketName = bucketName;
    }

    public static String getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(String openFalg) {
        OssConfig.openFlag = openFalg;
    }

    /** 地址配置 */
    private static String endPoint;

    /** 访问键 */
    private static String accessKeyId;

    /** 密码 */
    private static String accessKeySecret;

    /** 桶 */
    private static String bucketName;

    /** 开启标志 */
    private static String openFlag;
}
