package com.soft.framework.controller.Base;

import com.soft.framework.helper.WpServiceHelper;
import com.yyszc.wpbase.entity.Person;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

@Controller
@Configuration
@RequestMapping("/" )
public class MetaController {
    @RequestMapping("/index")
    public String index(){
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/Index")
    public String Index(){
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/MIndex")
    public String MIndex(){
        return "redirect:/MIndex.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/mIndex")
    public String mIndex(){
        return "redirect:/MIndex.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/FProtal")
    public String FProtal(){
        return "redirect:/FProtal.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/")
    public String Default(){
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/Login")
    public String Login(){
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/Logout")
    public String Logout(HttpSession session) {
        session.invalidate();
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/login")
    public String login(){
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/Index.html?v="+String.valueOf(Math.random());
    }

    //测试接口
    @RequestMapping(value="/test",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET})
    @ResponseBody
    public String test(HttpServletRequest request){
        String token = request.getHeader("token");

        Person person = WpServiceHelper.TestDBEntity("qidf");
        if(person!=null) {
            return person.getLoginName() + ":" + person.getRealName() + ":" + person.getPassword();
        }else {
            return "";
        }
    }
}
