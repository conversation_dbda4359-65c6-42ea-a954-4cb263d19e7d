package com.soft.framework.helper;

import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.yyszc.wpbase.entity.Lc_currentState;
import com.yyszc.wpbase.entity.Lc_workFlow;
import com.yyszc.wpbase.entity.Lcdefine;
import com.yyszc.wpbase.entity.Lcjd;
import com.yyszc.wpbase.ventity.PersonEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class WrokProcHelper {
    public List<Lcjd> listLcjd = new ArrayList<Lcjd>();
    public List<Lcdefine> listLcDefine = new ArrayList<Lcdefine>();
    int endLcjd = 0;

    public WrokProcHelper(int lcid, int endlcjd)
    {
        SqlHelper sqlhelper = new SqlHelper();

        listLcDefine = sqlhelper.GetObjectList(Lcdefine.class,"select * from Lcdefine where lcID='"+lcid+"'");
        listLcjd = sqlhelper.GetObjectList(Lcjd.class,"select * from Lcjd where lc_defineID='"+lcid+"'");
        endLcjd = endlcjd;
    }

    private void addFirstLc_workFlow(Lcjd lc, int lc_defineID, int ywid, PersonEntity person, String feed, SqlProc sqlproc) throws Exception {
        Lc_workFlow workFlow = new Lc_workFlow();
        workFlow.setLc_defineID(lc_defineID);
        workFlow.setLc_jdID(lc.getLcjdID());
        workFlow.setLc_jdmc(lc.getJdmc());
        workFlow.setYwID(ywid);
        workFlow.setGroupID(person.getGroupId());
        workFlow.setGroupName(person.getGroupName());
        workFlow.setStartdate(DateUtil.toLocalDateTime(new Date()));
        workFlow.setTransdate(DateUtil.toLocalDateTime(new Date()));
        workFlow.setPersonZgh(person.getLoginName());
        workFlow.setPersonName(person.getRealName());
        workFlow.setFeed(feed);
        workFlow.setNumber(1);
        workFlow.setBXType("主线");

        String strsql = DBHelper.GetInsertSQL(workFlow, "Lc_workFlow", Arrays.asList("ID"));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    private String deleteSamePerson(String persons)
    {
        String result = "";
        persons= persons.replace(",", "");
        persons=StringUtil.deleteLastChar(persons,'~');
        String[] ps=persons.split("~");

        for (int i = 0; i < ps.length; i++)
        {
            if (result.indexOf(ps[i]) < 0)
            {
                result = result + ps[i] + "~";
            }
        }
        return result;
    }

    public Lcjd getFirstLcjd(int lc_defineID)
    {
        Lcjd lc = ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&&q.getType()==1);
        return lc;
    }

    public Lcjd getFirstLcjd(int lc_defineID,int lcjdId)
    {
        Lcjd lc = ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&&q.getLcjdID()==lcjdId&&q.getType()==1);
        return lc;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, SqlProc sqlproc)
    {
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState2(int lc_defineID, int ywid, int lcjdid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and isnull(PNO,'')='" + pnostr + "' and sendPersonZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState2(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and isnull(PNO,'')='" + pnostr + "' and AllPersonZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, int lcjdid, String pesonZgh, SqlProc sqlproc)
    {
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and sendPersonZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, int lcjdid, String pesonZgh, SqlProc sqlproc)
    {
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and personZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow2(int lc_defineID, int ywid, int lcjdid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "'  order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow3(int lc_defineID, int ywid, int lcjdid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and and PNO like '%" + pnostr + "%'   order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "'  order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }


    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' and personZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, int lcjdid, SqlProc sqlproc)
    {
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + "  order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, int lcjdid, SqlProc sqlproc)
    {
        List<Lc_currentState> lists =sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ " order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Boolean checkWorkFlowExists(int lc_defineID, int ywid, int lcjdid, SqlProc sqlproc)
    {
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkWorkFlowExists(int lc_defineID, int ywid, int lcjdid, String pesonZgh, SqlProc sqlproc)
    {
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and personZgh like '%" + pesonZgh + "%'  order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkWorkFlowExists2(int lc_defineID, int ywid, int lcjdid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "'  order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkWorkFlowExists(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' and personZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkStateExpPnoIsFinish(int lc_defineID, int ywid, int lcjdid, String pno, SqlProc sqlproc)
    {
        Boolean retint = true;
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists = sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and BXType='子线' and isnull(PNO,'')!='" + pnostr + "' order by ID desc");
        if (lists.size() > 0)
        {
            for (int i = 0; i < lists.size(); i++)
            {
                Lc_currentState cs = lists.get(i);
                if (cs.getLc_jdid()== lcjdid)
                {
                    retint = false;
                    break;
                }
            }
        }
        return retint;
    }


    public Lcjd getNextLcjd(int lc_defineID, Lcjd currentLcjd)
    {
        int nextID = currentLcjd.getNextID();
        Lcjd nextLc =ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&&q.getLcjdID()==nextID);
        return nextLc;
    }

    public void addCurrentState(Lcjd lc, int lc_defineID, int ywid, String sendPerson, String sendPersonZgh, PersonEntity person, String bxtype, String pno, String sendGroupIDs, SqlProc sqlproc) throws Exception {
        Lcdefine df=ToolHelper.getElement(listLcDefine, q -> q.getLcID() == lc_defineID);

        String pnostr = (pno == null) ? "" : pno;
        Lc_currentState currentState = new Lc_currentState();
        currentState.setYwID(ywid);
        currentState.setLc_defineID(lc_defineID);
        currentState.setLc_jdid(lc.getLcjdID());
        currentState.setLc_jdmc(lc.getJdmc());
        currentState.setLc_Name(df.getLcName());
        currentState.setSendPerson(deleteSamePerson(sendPerson));
        currentState.setSendPersonZgh(deleteSamePerson(sendPersonZgh));
        currentState.setAllPersonZgh(person.getLoginName()+ "~" + currentState.getSendPersonZgh().replace("~~", "~"));
        currentState.setLc_tojdid(lc.getNextID().toString());
        if (StringUtil.deleteLastChar(currentState.getSendPerson(),'~').indexOf("~") > 1)
        {
            currentState.setIsMany(1);
        }
        else
        {
            currentState.setIsMany(0);
        }
        //获取最新流程信息的number
        currentState.setNumber(1);
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (list.size() > 0)
        {
            if (list.get(list.size() - 1).getNumber()!= null)
            {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }
        currentState.setBXType(bxtype);
        currentState.setPNO(pno);
        currentState.setSendGroupIDs(sendGroupIDs);
        //stateLinqAccess.AddEntity(currentState);

        String strsql = DBHelper.GetInsertSQL(currentState,"Lc_currentState", Arrays.asList("ID"));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    public void addNextLc_workFlow(Lcjd lc, int lc_defineID, int ywid, String sendPerson, String sendPersonZgh, String sendPersonGroup, String bxtype, String pno, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        String[] sendGroups = StringUtil.deleteLastChar(sendPersonGroup,'~').split("~");//发送班组
        String[] sendPersons = StringUtil.deleteLastChar(sendPerson,'~').split("~"); //发送人
        String[] sendPersonZghs = StringUtil.deleteLastChar(sendPersonZgh,'~').split("~"); //发送zgh
        int number = 0;
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "' and feed!='' order by ID asc");
        if (list.size() > 0)
        {
            number = list.get(list.size() - 1).getNumber() + 1;
        }

        for (int i = 0; i < sendPersonZghs.length; i++)
        {
            if (!sendPersonZghs[i].equals(""))
            {
                Lc_workFlow workFlow = new Lc_workFlow();
                workFlow.setLc_defineID(lc_defineID);
                workFlow.setLc_jdID(lc.getLcjdID());
                workFlow.setLc_jdmc(lc.getJdmc());
                workFlow.setYwID(ywid);
                workFlow.setGroupID(Integer.parseInt(sendGroups[i]));

                String gname = sqlproc.ExecuteScalar("select groupname from GroupItem where id='" + workFlow.getGroupID() + "'");
                workFlow.setGroupName(gname);
                workFlow.setPersonZgh(sendPersonZghs[i]);
                workFlow.setPersonName(sendPersons[i]);
                workFlow.setFeed("");
                workFlow.setNumber(number);
                workFlow.setBXType(bxtype);
                workFlow.setPNO(pno);
                workFlow.setLcByRole(0);
                workFlow.setStartdate(DateUtil.toLocalDateTime(new Date()));
                workFlow.setTransdate(null);

                String strsql = DBHelper.GetInsertSQL(workFlow,"Lc_workFlow", Arrays.asList("ID"));
                if (!StringUtil.IsNullOrEmpty(strsql))
                {
                    sqlproc.ExecuteNoQuery(strsql);
                }
            }
        }
    }

    public void addNextLc_workFlowByRole(Lcjd lc, int lc_defineID, int ywid, String sendPersonGroup, String bxtype, String pno, SqlProc sqlproc) throws Exception {
        String[] sendGroups = StringUtil.deleteLastChar(sendPersonGroup,'~').split("~");//发送班组
        int number = 0;
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and feed!='' order by ID asc");
        if (list.size() > 0)
        {
            number = list.get(list.size() - 1).getNumber() + 1;
        }
        Lc_workFlow workFlow = new Lc_workFlow();
        workFlow.setLc_defineID(lc_defineID);
        workFlow.setLc_jdID(lc.getLcjdID());
        workFlow.setLc_jdmc(lc.getJdmc());
        workFlow.setYwID(ywid);
        workFlow.setGroupID(Integer.parseInt(sendGroups[0]));

        String gname = sqlproc.ExecuteScalar("select groupname from GroupItem where id='" + workFlow.getGroupID() + "'");
        workFlow.setGroupName(gname);
        workFlow.setPersonZgh("");
        workFlow.setPersonName("");
        workFlow.setFeed("");
        workFlow.setNumber(number);
        workFlow.setBXType(bxtype);
        workFlow.setPNO(pno);
        workFlow.setLcByRole(1);
        workFlow.setStartdate(DateUtil.toLocalDateTime(new Date()));

        //workFlowAccess.AddEntity(workFlow);
        String strsql = DBHelper.GetInsertSQL(workFlow,"Lc_workFlow", Arrays.asList("ID"));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    public Lcjd getCurrentLcjd(int lc_defineID, int lc_tojdid)
    {
        Lcjd currentLc=ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&& q.getLcjdID() == lc_tojdid);
        return currentLc;
    }

    public void updateCurrentLc_workFlow(int lc_defineID, int ywid, int lcjdid, String feed, String pno, String person, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' and personZgh='" + person + "' order by ID desc");
        if (lists.size() > 0)
        {
            Lc_workFlow workFlow = lists.get(0);
            workFlow.setTransdate(DateUtil.toLocalDateTime(new Date()));
            workFlow.setFeed(feed);
            //workFlowAccess.UpdateEntity(workFlow);

            String strsql = DBHelper.GetUpdateSQL(workFlow, "Lc_workFlow", Arrays.asList("ID"), Arrays.asList(workFlow.getID().toString()));
            if (!StringUtil.IsNullOrEmpty(strsql))
            {
                sqlproc.ExecuteNoQuery(strsql);
            }
        }
    }

    //用户撤回流程使用
    public void updateCurrentLc_workFlow(int lc_defineID, int ywid, int lcjdid, String pno, String person, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' and personZgh='" + person + "' order by ID desc");
        if (lists.size() > 0)
        {
            Lc_workFlow workFlow = lists.get(0);
            workFlow.setTransdate(null);
            workFlow.setFeed("");
            //workFlowAccess.UpdateEntity(workFlow);

            String strsql = "update Lc_workFlow set transdate=null,feed='' where ID=" + workFlow.getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    //用户撤回流程使用
    public void updateCurrentLc_workFlow(int lc_defineID, int ywid, int lcjdid, String pno, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            Lc_workFlow workFlow = lists.get(nIndex);
            workFlow.setTransdate(null);
            workFlow.setFeed("");
            //workFlowAccess.UpdateEntity(workFlow);

            String strsql = "update Lc_workFlow set transdate=null,feed='' where ID=" + workFlow.getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    private void xl_updateCurrentLc_workFlow(Lcjd lc, int lc_defineID, int ywid, String feed, SqlProc sqlproc) throws Exception {
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lc.getLcjdID().toString() + " order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            Lc_workFlow lco=lists.get(nIndex);
            if (lco.getFeed()==null||lco.getFeed().equals(""))
            {
                Lc_workFlow workFlow = lists.get(nIndex);
                workFlow.setTransdate(DateUtil.toLocalDateTime(new Date()));
                workFlow.setFeed(feed);
                //workFlowAccess.UpdateEntity(workFlow);

                String strsql = DBHelper.GetUpdateSQL(workFlow, "Lc_workFlow", Arrays.asList("ID"), Arrays.asList(workFlow.getID().toString()));
                if (!StringUtil.IsNullOrEmpty(strsql))
                {
                    sqlproc.ExecuteNoQuery(strsql);
                }
            }
        }
    }

    public void updateCurrentState(Lc_currentState currentState, Lcjd lc, Lcjd lcPrev, String sendPerson, String sendPersonZgh, SqlProc sqlproc) throws Exception {
        currentState.setLc_jdid(lc.getLcjdID());
        currentState.setLc_jdmc(lc.getJdmc());
        currentState.setSendPerson(deleteSamePerson(sendPerson));
        currentState.setSendPersonZgh(deleteSamePerson(sendPersonZgh));
        if (lc.getLcjdID()!= endLcjd)
        {
            currentState.setIsOtherAdd(0);
            currentState.setAllPersonZgh((currentState.getAllPersonZgh()+ currentState.getSendPersonZgh()+ "~").replace("~~", "~"));
        }
        else
        {
            currentState.setIsOtherAdd(lcPrev.getLcjdID());
            currentState.setLc_jdmc(lcPrev.getJdmc()+ "后流程等待");
        }
        currentState.setLc_tojdid(lcPrev.getLcjdID().toString());
        if (StringUtil.deleteLastChar(sendPerson,'~').indexOf("~") > 1)
        {
            currentState.setIsMany(1);
        }
        else
        {
            currentState.setIsMany(0);
        }
        currentState.setLc_isback(0);

        //获取最新流程信息的number
        currentState.setNumber(1);
        String pnostr = (currentState.getPNO()== null) ? "" : currentState.getPNO().toString();
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + currentState.getLc_defineID().toString() + " and ywID=" + currentState.getYwID().toString() + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (list.size() > 0)
        {
            if (list.get(list.size() - 1).getNumber()!= null)
            {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }
        //stateLinqAccess.UpdateEntity(currentState);
        String strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }


    //用于撤回流程使用
    public void updateCurrentState(Lc_currentState currentState, Lcjd lc, String sendPerson, String sendPersonZgh, SqlProc sqlproc) throws Exception {
        currentState.setLc_jdid(lc.getLcjdID());
        currentState.setLc_jdmc(lc.getJdmc());
        currentState.setIsOtherAdd(0);
        currentState.setSendPerson(deleteSamePerson(sendPerson));
        currentState.setSendPersonZgh(deleteSamePerson(sendPersonZgh));

        currentState.setLc_tojdid(lc.getNextID().toString());
        if (StringUtil.deleteLastChar(currentState.getSendPerson(),'~').indexOf("~") > 1)
        {
            currentState.setIsMany(1);
        }
        else
        {
            currentState.setIsMany(0);
        }
        currentState.setLc_isback(0);

        //获取最新流程信息的number
        currentState.setNumber(1);

        String pnostr = (currentState.getPNO() == null) ? "" : currentState.getPNO().toString();
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + currentState.getLc_defineID().toString() + " and ywID=" + currentState.getYwID().toString() + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (list.size() > 0)
        {
            if (list.get(list.size() - 1).getNumber()!= null)
            {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }

        //stateLinqAccess.UpdateEntity(currentState);
        String strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    public void updateCurrentState_OnNull(Lc_currentState currentState, Lcjd lcjd, String sendPerson, String sendPersonZgh, SqlProc sqlproc) throws Exception {
        currentState.setLc_jdmc(lcjd.getJdmc()+ "主线流程对应等待节点");
        currentState.setIsOtherAdd(lcjd.getLcjdID());   //各种等待节点时侯，隐射到对应主线节点，
        currentState.setSendPerson(deleteSamePerson(sendPerson));
        currentState.setSendPersonZgh(deleteSamePerson(sendPersonZgh));

        //stateLinqAccess.UpdateEntity(currentState);
        String strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    public void updateCurrentState_OnNull1(Lc_currentState currentState, Lcjd lcjd, String sendPerson, String sendPersonZgh, SqlProc sqlproc) throws Exception {
        currentState.setLc_jdmc(lcjd.getJdmc()+ "主线流程前置等待节点");
        currentState.setIsOtherAdd(lcjd.getLcjdID());   //各种等待节点时侯，隐射到对应主线节点，
        currentState.setSendPerson(deleteSamePerson(sendPerson));
        currentState.setSendPersonZgh(deleteSamePerson(sendPersonZgh));

        //stateLinqAccess.UpdateEntity(currentState);
        String strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }


    public void endCurrentState(Lc_currentState currentState, PersonEntity person, SqlProc sqlproc) throws Exception {
        currentState.setLc_jdid(0);
        currentState.setLc_jdmc("完成");
        currentState.setSendPerson("归档");
        currentState.setSendPersonZgh("归档");
        //获取最新流程信息的number
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + currentState.getLc_defineID().toString() + " and ywID=" + currentState.getYwID().toString() + " order by ID desc");
        if (list.size() > 0)
        {
            if (list.get(list.size() - 1).getNumber()!= null)
            {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }
        currentState.setBXType(currentState.getBXType());

        String strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }
    //终止
    public void finishCurrentState(Lc_currentState currentState, SqlProc sqlproc) throws Exception {
        currentState.setLc_jdid(0);
        currentState.setLc_jdmc("归档");
        currentState.setSendPerson("归档");
        currentState.setSendPersonZgh("归档");
        //获取最新流程信息的number
        List<Lc_workFlow> list = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + currentState.getLc_defineID().toString() + " and ywID=" + currentState.getYwID().toString() + " order by ID desc");
        if (list.size() > 0)
        {
            if (list.get(list.size() - 1).getNumber()!= null)
            {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }
        currentState.setBXType(currentState.getBXType());

        String strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    //撤回流程使用
    public void DeleteCurrentState(int lc_defineID, int ywid, String pno, List<Integer> pjd, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;

        String pjdstr = "";
        for (Integer tmpint : pjd)
        {
            if (pjdstr.equals("")) {
                pjdstr += tmpint.toString();
            } else {
                pjdstr += "," + tmpint.toString();
            }
        }
        List<Lc_currentState> lists = sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid in (" + pjdstr + ") and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            //stateLinqAccess.DeleteEntity(lists[nIndex]);

            String strsql = "delete from Lc_currentState where ID=" + lists.get(nIndex).getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    //撤回流程使用
    public void DeleteCurrentState2(int lc_defineID, int ywid, String pno, List<Integer> pjd, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;

        String pjdstr = "";
        for (Integer tmpint : pjd)
        {
            if (pjdstr.equals("")) {
                pjdstr += tmpint.toString();
            } else {
                pjdstr += "," + tmpint.toString();
            }
        }
        List<Lc_currentState> lists = sqlproc.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid in (" + pjdstr + ") and PNO like '%" + pnostr + "%' order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            //stateLinqAccess.DeleteEntity(lists[nIndex]);

            String strsql = "delete from Lc_currentState where ID=" + lists.get(nIndex).getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    public void DeleteCurrentLc_WorkFlow_InOnePass(int lc_defineID, int ywid, int lcjdid, String pno, String sendpeson, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID =" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            if (lists.get(nIndex).getFeed()== null||lists.get(nIndex).getFeed().equals(""))
            {
                //workFlowAccess.DeleteEntity(lists[nIndex]);

                String strsql = "delete from Lc_workFlow where ID=" + lists.get(nIndex).getID().toString();
                sqlproc.ExecuteNoQuery(strsql);
            }
        }
    }

    //撤回流程使用
    public void DeleteCurrentLc_WorkFlow_InOver(int lc_defineID, int ywid, String pno, List<Integer> pjd, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;

        String pjdstr = "";
        for (Integer tmpint : pjd)
        {
            if (pjdstr.equals("")) {
                pjdstr += tmpint.toString();
            } else {
                pjdstr += "," + tmpint.toString();
            }
        }
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID  in(" + pjdstr + ") and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            //workFlowAccess.DeleteEntity(lists[nIndex]);

            String strsql = "delete from Lc_workFlow where ID=" + lists.get(nIndex).getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    //撤回流程使用
    public void DeleteCurrentLc_WorkFlow_InOver2(int lc_defineID, int ywid, String pno, List<Integer> pjd, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        String pjdstr = "";
        for (Integer tmpint : pjd)
        {
            if (pjdstr.equals("")) {
                pjdstr += tmpint.toString();
            } else {
                pjdstr += "," + tmpint.toString();
            }
        }

        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID  in(" + pjdstr + ") and PNO like '%" + pnostr + "%' order by ID desc");
        for (int nIndex = 0; nIndex < lists.size(); nIndex++)
        {
            String strsql = "delete from Lc_workFlow where ID=" + lists.get(nIndex).getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    //撤回流程使用
    public void DeleteCurrentLc_WorkFlow_InMany(int lc_defineID, int ywid, String pno, int lcjd, SqlProc sqlproc) throws Exception {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID='" + lcjd + "' and isnull(PNO,'')='" + pnostr + "' order by ID asc");
        for (int nIndex = 1; nIndex < lists.size(); nIndex++)
        {
            //workFlowAccess.DeleteEntity(lists[nIndex]);

            String strsql = "delete from Lc_workFlow where ID=" + lists.get(nIndex).getID().toString();
            sqlproc.ExecuteNoQuery(strsql);
        }
    }

    public void Delete(int lc_defineID, int ywid, SqlProc sqlproc) throws Exception {
        sqlproc.ExecuteNoQuery("delete from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid);
        sqlproc.ExecuteNoQuery("delete from Lc_workFlow where Lc_defineID=" + lc_defineID + " and ywID=" + ywid);
    }

    public List<Lc_workFlow> GetWorkFlows(int lc_defineID, int ywid, SqlProc sqlproc)
    {
        List<Lc_workFlow> lists = null;
        lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + "  order by lc_jdID asc");

        return lists;
    }

    public List<Lc_workFlow> GetWorkFlows(int lc_defineID, int ywid, String pno, SqlProc sqlproc)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = null;
        lists = sqlproc.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "' order by lc_jdID asc");
        return lists;

    }

    public int InitWorkFlow(int lc_defineID, int ywid, String feed, PersonEntity person, int lc_jdid, SqlProc sqlproc) throws Exception {
        Lc_currentState currentState = getCurrentLcState(lc_defineID, ywid,sqlproc);
        if (currentState == null) //填报提交
        {
            Lcjd lc = getFirstLcjd(lc_defineID,lc_jdid);
            if (lc==null) {
                return -1;
            }

            addFirstLc_workFlow(lc, lc_defineID, ywid, person, feed, sqlproc);

            return 0;
        }
        else
        {
            return -1;
        }
    }


    //简单流程节点一个出口的流程节点提交
    public int SimpleSubmit(int lc_defineID, int ywid, String sendPerson, String sendPersonZgh, String sendPersonGroup, String feed, PersonEntity person, Boolean isOnePass, int lc_jdid, String bxtype, String pno, SqlProc sqlproc) throws Exception//isOnePass 几个人审核 有一个通过就过
    {
        int nextid = 0;
        Lc_currentState currentState = getCurrentLcState2(lc_defineID, ywid, lc_jdid, pno, sqlproc);
        if (currentState != null) //填报提交
        {
            Lcjd lc = getCurrentLcjd(lc_defineID, lc_jdid);
            Lcjd lcNext = getNextLcjd(lc_defineID, lc);

            updateCurrentLc_workFlow(lc_defineID, ywid, lc_jdid, feed, pno, person.getLoginName(), sqlproc);    //更新流程中的时间和审核时间

            if (lcNext != null)
            {
                if (isOnePass)
                {
                    DeleteCurrentLc_WorkFlow_InOnePass(lc_defineID, ywid, lc_jdid, currentState.getPNO(), person.getLoginName(), sqlproc);//删除其他人提交。
                    updateCurrentState(currentState, lcNext, lc, sendPerson, sendPersonZgh, sqlproc);
                }
                else
                {
                    updateCurrentState(currentState, lcNext, lc, sendPerson, sendPersonZgh, sqlproc);
                }
            }
            else //流程结束
            {
                if (isOnePass)
                {
                    DeleteCurrentLc_WorkFlow_InOnePass(lc_defineID, ywid, lc_jdid, currentState.getPNO(), person.getLoginName(), sqlproc);//删除其他人提交。
                }

                endCurrentState(currentState, person, sqlproc);
                nextid = 0;
            }

            if (lcNext != null)
            {
                if (lcNext.getLcjdID() != endLcjd)
                {
                    addNextLc_workFlow(lcNext, lc_defineID, ywid, sendPerson, sendPersonZgh, sendPersonGroup, bxtype, pno, sqlproc);
                }
                nextid = lcNext.getID();
            }
        }
        return nextid;
    }

    //简单按下一节点推进流程
    public int SubmitBranch(int lc_defineID, int ywid, String sendPerson, String sendPersonZgh, String sendPersonGroup, String feed, PersonEntity person, Boolean isOnePass, int lc_jdid, int nextjdid, String bxtype, String pno, SqlProc sqlproc) throws Exception//isOnePass 几个人审核 有一个通过就过
    {
        int nextid = 0;
        Lc_currentState currentState = getCurrentLcState2(lc_defineID, ywid, lc_jdid, pno, sqlproc);
        if (currentState != null) //填报提交
        {
            Lcjd lc = getCurrentLcjd(lc_defineID, lc_jdid);
            Lcjd lcNext = getCurrentLcjd(lc_defineID, nextjdid);

            updateCurrentLc_workFlow(lc_defineID, ywid, lc_jdid, feed, pno, person.getLoginName(), sqlproc); //更新流程中的时间和审核时间

            if (lcNext != null)
            {
                if (isOnePass)
                {
                    DeleteCurrentLc_WorkFlow_InOnePass(lc_defineID, ywid, lc_jdid, currentState.getPNO(), person.getLoginName(), sqlproc);//删除其他人提交。
                    updateCurrentState(currentState, lcNext, lc, sendPerson, sendPersonZgh, sqlproc);
                }
                else
                {
                    updateCurrentState(currentState, lcNext, lc, sendPerson, sendPersonZgh, sqlproc);
                }
            }
            else //流程结束
            {
                endCurrentState(currentState, person, sqlproc);
                nextid = 0;
            }

            if (lcNext != null)
            {
                if (lcNext.getLcjdID() != endLcjd)
                {
                    addNextLc_workFlow(lcNext, lc_defineID, ywid, sendPerson, sendPersonZgh, sendPersonGroup, bxtype, pno, sqlproc);
                }
                nextid = lcNext.getID();
            }
        }
        return nextid;
    }

    //中间结点简单流程节点一个出口的流程节点提交
    public int SimpleReback(int lc_defineID, int ywid, String feed, PersonEntity person, int backjdid, int lc_jdid, String pno, SqlProc sqlproc) throws Exception {
        int Ismany = 0;   //判断是否是多个人
        int jdid = 0;
        String sendPerson = "";     //人员名
        String sendPersonZgh = "";  //人员职工号
        String lc_jdmc = "";

        String strsql = "select * from Lc_currentState where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lc_jdid;
        if (pno != "") {
            strsql += " and PNO='" + pno + "'";
        }

        List<Lc_currentState> list = sqlproc.GetObjectList(Lc_currentState.class,strsql);
        if (list == null) {
            return -1;
        }

        Lc_currentState currentState = list.get(0);
        Lcjd lc = getCurrentLcjd(lc_defineID, lc_jdid);

        if (backjdid == 0)
        {
            backjdid = Integer.parseInt(lc.getBackjdID());
        }
        xl_updateCurrentLc_workFlow(lc, lc_defineID, ywid, feed, sqlproc);//修改当前流程信息


        //获取上一阶段的信息
        strsql = "select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + backjdid;
        if (pno != "") {
            strsql += " and PNO='" + pno + "'";
        }

        List<Lc_workFlow> last_list = sqlproc.GetObjectList(Lc_workFlow.class,strsql);
        if (last_list == null) {
            return -1;
        }

        for (int nIndex = 0; nIndex < last_list.size(); nIndex++)
        {
            if (nIndex == 0)
            {
                Lc_workFlow workFlow = new Lc_workFlow();
                workFlow.setGroupID(last_list.get(nIndex).getGroupID());
                workFlow.setGroupName(last_list.get(nIndex).getGroupName());
                workFlow.setLc_defineID(last_list.get(nIndex).getLc_defineID());
                workFlow.setLc_jdID(last_list.get(nIndex).getLc_jdID());
                workFlow.setLc_jdmc(last_list.get(nIndex).getLc_jdmc());
                workFlow.setPersonName(last_list.get(nIndex).getPersonName());
                workFlow.setPersonZgh(last_list.get(nIndex).getPersonZgh());
                //workFlow.transdate = null
                workFlow.setYwID(last_list.get(nIndex).getYwID());
                workFlow.setFeed("");
                workFlow.setID(0);
                workFlow.setNumber(last_list.get(nIndex).getNumber());
                workFlow.setBXType(last_list.get(nIndex).getBXType());
                workFlow.setPNO(last_list.get(nIndex).getPNO());
                workFlow.setStartdate(DateUtil.toLocalDateTime(new Date()));
                //workFlowAccess.AddEntity(workFlow);

                strsql = DBHelper.GetInsertSQL(workFlow,"Lc_workFlow", Arrays.asList("ID"));
                if (!StringUtil.IsNullOrEmpty(strsql))
                {
                    sqlproc.ExecuteNoQuery(strsql);
                }

                sendPerson = last_list.get(nIndex).getPersonName()+ "~";
                sendPersonZgh =last_list.get(nIndex).getPersonZgh()+ "~";
                lc_jdmc = last_list.get(nIndex).getLc_jdmc() + "/";

                jdid = last_list.get(nIndex).getLc_jdID();
            }
            else
            {
                if (sendPersonZgh.contains(last_list.get(nIndex).getPersonZgh()))
                {

                }
                else
                {
                    Lc_workFlow workFlow = new Lc_workFlow();
                    workFlow.setGroupID(last_list.get(nIndex).getGroupID());
                    workFlow.setGroupName(last_list.get(nIndex).getGroupName());
                    workFlow.setLc_defineID(last_list.get(nIndex).getLc_defineID());
                    workFlow.setLc_jdID(last_list.get(nIndex).getLc_jdID());
                    workFlow.setLc_jdmc(last_list.get(nIndex).getLc_jdmc());
                    workFlow.setPersonName(last_list.get(nIndex).getPersonName());
                    workFlow.setPersonZgh(last_list.get(nIndex).getPersonZgh());
                    //workFlow.transdate = null
                    workFlow.setYwID(last_list.get(nIndex).getYwID());
                    workFlow.setFeed("");
                    workFlow.setID(0);
                    workFlow.setNumber(last_list.get(nIndex).getNumber());
                    workFlow.setBXType(last_list.get(nIndex).getBXType());
                    workFlow.setPNO(last_list.get(nIndex).getPNO());
                    workFlow.setStartdate(DateUtil.toLocalDateTime(new Date()));

                    strsql = DBHelper.GetInsertSQL(workFlow,"Lc_workFlow", Arrays.asList("ID"));
                    if (!StringUtil.IsNullOrEmpty(strsql))
                    {
                        sqlproc.ExecuteNoQuery(strsql);
                    }

                    sendPerson = sendPerson+last_list.get(nIndex).getPersonName()+ "~";
                    sendPersonZgh =sendPersonZgh+last_list.get(nIndex).getPersonZgh()+ "~";
                    jdid = last_list.get(nIndex).getLc_jdID();
                    Ismany = 1;
                }
            }

        }
        sendPerson =StringUtil.deleteLastChar(sendPerson,'~');
        sendPersonZgh = StringUtil.deleteLastChar(sendPersonZgh,'~');
        lc_jdmc = StringUtil.deleteLastChar(lc_jdmc,'/');

        currentState.setSendPerson(sendPerson);
        currentState.setSendPersonZgh(sendPersonZgh);
        currentState.setIsMany(Ismany);
        currentState.setLc_tojdid(currentState.getLc_jdid().toString());
        currentState.setLc_jdmc(lc_jdmc);
        currentState.setLc_jdid(jdid);
        currentState.setLc_isback(1);
        currentState.setNumber(last_list.get(0).getNumber());
        //stateLinqAccess.UpdateEntity(currentState);

        strsql = DBHelper.GetUpdateSQL(currentState, "Lc_currentState", Arrays.asList("ID"), Arrays.asList(currentState.getID().toString()));
        if (!StringUtil.IsNullOrEmpty(strsql))
        {
            sqlproc.ExecuteNoQuery(strsql);
        }
        return currentState.getLc_jdid();
    }
}
