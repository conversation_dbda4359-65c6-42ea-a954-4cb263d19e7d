package com.soft.framework.job;

import com.soft.gcc.base.entity.sysjob;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;

/**
 * 定时任务处理（禁止并发执行）
 * 
 * <AUTHOR>
 *
 */
@DisallowConcurrentExecution
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob
{
    @Override
    protected void doExecute(JobExecutionContext context, sysjob sysJob) throws Exception
    {
        JobInvokeUtil.invokeMethod(sysJob);
    }
}
