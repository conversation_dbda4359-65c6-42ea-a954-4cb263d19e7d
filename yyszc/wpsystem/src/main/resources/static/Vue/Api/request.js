class Request {
	constructor({
		processAuthorizationHeader = defaultAuthorizationHeader,
		processHttpStatusCode = defaultHttpStatusCode,
		processResultHandle = defaultResultHandle,
		processNoAccess = empty, //默认处理401函数
		baseUrl = ""
	}) {
		this.processAuthorizationHeader = processAuthorizationHeader;
		this.processHttpStatusCode = processHttpStatusCode;
		this.processResultHandle = processResultHandle;
		this.processNoAccess = processNoAccess;
		this.baseUrl = baseUrl;
	}

	request(opts) {
		let {
			url,
			data = {},
			header = {},
			method = 'POST', // 有效值：OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
			dataType = 'json',
			responseType = 'text',
			access = false,
			complete = empty,  //不建议使用
			onSuccess = empty,
			onError = empty,
			showLoad = false, // 默认不显示loading
			mask = false, // 不显示遮罩
			loadingText,
		} = opts;
		const self = this;
		if (showLoad) {
			uni.showLoading({
				title: loadingText || '加载中…',
				icon:'loading',
				mask: mask
			})
		}
		// 请求头设置
		header = Object.assign({}, this.processAuthorizationHeader(access), header);
		if (!access && _.isEmpty(header['x-access-token'])) {
			return null;
		}
		return new Promise((resolve, reject) => {
			uni.request({
				url: self.baseUrl + url, 
				data,
				header,
				method,
				dataType,
				responseType,
				complete,
				success: (res) => {
					uni.hideLoading();
					// 请求状态码处理
					const httpStatusCode = res.statusCode;
					if (!self.processHttpStatusCode(httpStatusCode)) {
						return reject();
					}
					// 请求数据过滤(data默认为json格式)
					if (typeof res.data === 'string') {
						try {
							res.data = JSON.parse(res.data);
						} catch (ex) {
							uni.showToast({
								title: ex.message,
								icon: 'none',
								duration: 5000
							})
						}
					}
					// 统一添加请求处理
					if (self.processResultHandle(res)){
						onSuccess(res.data)
					}else{
						uni.showToast3s({
							title: res.data.message,
							icon:'none',
							duration: 1200
						});
						onError(res.data)
					} 
					resolve(res.data);
				},
				fail: (res) => {
					uni.hideLoading();
					uni.showToast3s({
						title: res.message || "网络异常，请检查网络是否通畅",
						icon:'none',
						duration: 1200
					});
					onError(res)
					reject(res);
				}
			});
		}).catch((e) => {console.log(e)});
	}
}

// 请求默认Header处理方法
function defaultAuthorizationHeader(access) {
	let token = uni.getStorageSync('token')
	if (_.isEmpty(token) && !access) {
		uni.showToast3s({
			title: '认证已过期，或在别处登录',
			icon: 'none',
			duration: 1000
		})
		this.processNoAccess()
		return {}
	} else {
		return {
			'x-access-token': token,
		}
	}
	return;
}
// 请求默认StatusCode处理方法
function defaultHttpStatusCode(statusCode) {
	if (statusCode === 200) {
		return true;
	} else if (statusCode === 500) {
		uni.showToast3s({
			title: '服务器错误',
			icon: 'none',
			duration: 2000
		})
	} else if (statusCode === 401) {
		uni.showToast3s({
			title: '认证已过期，或在别处登录',
			icon: 'none',
			duration: 1000
		})
		this.processNoAccess()
		return false;
	} else {
		uni.showToast3s({
			title: '服务器错误',
			icon: 'none',
			duration: 2000
		})
		return false;
	}
}
// 请求默认StatusCode处理方法
function defaultResultHandle(res) {
	if (+res.data.code === 200) {
		//处理分包 WEB 平台 token
		let fbtoken = res.header['token']
		if (fbtoken!=null && fbtoken !=''){
			uni.setStorageSync("fbtoken",fbtoken)
		}
		let bltoken = res.header['bltoken']
		if (bltoken!=null && bltoken !=''){
			uni.setStorageSync("bltoken",bltoken)
		}		
		let token = res.header['x-access-token']
		if (token!=null && token !=''){
			uni.setStorageSync("token",token)
		}
		return true;
	} else if (+res.data.code === 401) { // 重新登录
		this.processNoAccess()
		return false;
	} else { // 错误异常
		uni.showToast3s({
			title: res.data.message, icon: 'none', duration: 2000
		})
		return false;
	}
}

function empty(obj) {
	return true;
}
export default Request;
