function mDialog(url, width, height, title,logicpw) {
    var widths = width == "" ? document.body.clientWidth - 30 : width;
    var heights = height == "" ? document.body.clientHeight - 30 : height;
    var top = (document.body.clientHeight - heights) * 0.5;
    var left = (document.body.clientWidth - widths) * 0.5;
    var ismaximized = height == "" ? true : false;
    var isdrag = height == "" ? false : true;
    $j("#mDialog").find("iframe").attr("src", "");
    //alert($j("#mDialog").find("iframe")[0]);
    $j("#mDialog").window({
        modal: true,
        closed: false,
        width: widths,
        height: heights,
        title: title,
        top: top,
        left: left,
        collapsible: false,
        minimizable: false,
        maximizable: false,
        resizable: false,
        maximized: ismaximized,
        draggable: isdrag
    }).find("iframe").attr("height", heights - 46).attr("src", url);

    var iframe = $j("#mDialog").find("iframe")[0];
    if (iframe.attachEvent) {
        iframe.attachEvent("onload", function () {
            iframe.contentWindow.LogicPW = logicpw;
            $j('div.panel.window').find("div.panel-title").html(iframe.contentWindow.document.title);
            //alert(iframe.contentWindow.LogicPW);
        });
    } else {
        iframe.onload = function () {
            iframe.contentWindow.LogicPW = logicpw;
            $j('div.panel.window').find("div.panel-title").html(iframe.contentWindow.document.title);
            //alert(iframe.contentWindow.LogicPW);
        };
    }
}

//关闭层
function mDialogClose() {
    if ($j('#mDialog'))
    {
        $j("#mDialog").window('close');
    }
}

function InitMetaControl() {
    if ($j('#ffDialog'))
    {
        $j('#ffDialog').dialog().parent().appendTo("#form1");
        $j('#ffDialog').dialog('close');
    }

    if ($j('#foDialog'))
    {
        $j('#foDialog').dialog().parent().appendTo("#form1");
        $j('#foDialog').dialog('close');
    }

    if ($j('#fvDialog'))
    {
        $j('#fvDialog').dialog().parent().appendTo("#form1");
        $j('#fvDialog').dialog('close');
    }

    if ($j('#fvDialog')) {
        $j('#fvDialog').dialog().parent().appendTo("#form1");
        $j('#fvDialog').dialog('close');
    }

    if ($j('#fvoDialog')) {
        $j('#fvoDialog').dialog().parent().appendTo("#form1");
        $j('#fvoDialog').dialog('close');
    }

    if ($j('#mDialog')) {
        $j('#mDialog').dialog().parent().appendTo("#form1");
        $j('#mDialog').dialog('close');
    }
}