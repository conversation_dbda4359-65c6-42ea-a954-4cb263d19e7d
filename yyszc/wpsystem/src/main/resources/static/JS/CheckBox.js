// JScript 文件
//使名称为ControlID的网格内的checkbox全选或全不选
function CheckAll()
{
	var alltag=document.getElementsByTagName("input");
	for(var i=0;i<alltag.length;++i)
	{
		var temp_id=alltag.item(i).id;
		if(alltag.item(i).type=="checkbox" && temp_id.indexOf("gdselector")==0)
		{
			alltag.item(i).checked=document.all("allbox").checked;
		}
	}
}

//判断strControlID中选择框被选中了几个,如果没选中返回空字符串,否则返回选中的记录ID的字符串(用逗号分割)
function DeleteSelect()
{
	var alltag=document.getElementsByTagName("input");
	var pr="";
	for(var i=0;i<alltag.length;++i)
	{
		var temp_id=alltag.item(i).id;
		if(alltag.item(i).type=="checkbox"&&alltag.item(i).checked && temp_id.indexOf("gdselector")==0)
		{
			if(pr=="")
				pr=alltag.item(i).value;
			else
				pr=pr+","+alltag.item(i).value;
		}
	}
	return pr;
}


//判断datalist中选择框被选中了几个,0个返回0,一个则返回选中的ID,否则返回-1
function ModifySelect()
{
	var alltag=document.getElementsByTagName("INPUT");
	var pr="0";
	var j=0;
	for(var i=0;i<alltag.length;   ++i)
	{
		var temp_id=alltag.item(i).id;
		if(alltag.item(i).type=="checkbox"&&alltag.item(i).checked && temp_id.indexOf("gdselector")==0)
		{
			j=j+1;
			if (j>1) 
			{
			    break;
			}
			pr=alltag.item(i).value;
		}
	}
	if (j >1 )
	{
	    return "-1";
	}
	else
	{
		return pr;		
	}
}
