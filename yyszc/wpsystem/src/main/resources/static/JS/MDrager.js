function initMenu() {
    $j('#menu ul').hide();
    $j('#menu li ul:first').show();
    $j('#menu>li>a').click(
        function ()
        {
            var cidv = $j(this).attr("cid");
            var ulElement = $j("ul[cid='" + cidv + "']");
            if (ulElement!=undefined)
            {
                if(ulElement.is(':visible'))
                {
                    ulElement.slideUp('normal');
                    var childElement = $j(this).find('img[twomark]');
                    childElement.removeClass('ArrowUpImage');
                    childElement.addClass('ArrowDownImage');
                    return false;
                }
            }
            if (ulElement!=undefined)
            {
                if(!ulElement.is(':visible'))
                {
                    ulElement.slideDown('normal');
                    var childElement = $j(this).find('img[twomark]');
                    childElement.removeClass('ArrowUpImage');
                    childElement.addClass('ArrowDownImage');
                    return false;
                }
            }
        }
    );

    $j('#menu>li>ul>li>a').click(
        function () {
            var cidv = $j(this).attr("cid");
            var ulElement = $j("ul[cid='" + cidv + "']")
            if (ulElement!=undefined) {
                if(ulElement.is(':visible'))
                {
                    checkElement.slideUp('normal');
                    var childElement = $j(this).find('img[thirdmark]');
                    childElement.removeClass('ArrowUpImage');
                    childElement.addClass('ArrowDownImage');
                    return false;
                }
            }
            if (ulElement!=undefined) {
                if(ulElement.is(':visible'))
                {
                    $j('#menu li ul li ul:visible').slideUp('normal');
                    ulElement.slideDown('normal');
                    var childElement = $j(this).find('img[thirdmark]');
                    childElement.removeClass('ArrowUpImage');
                    childElement.addClass('ArrowDownImage');
                    return false;
                }
            }
        }
    );
}