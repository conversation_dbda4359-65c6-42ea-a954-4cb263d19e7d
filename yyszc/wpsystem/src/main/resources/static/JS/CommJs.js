String.prototype.replaceAll = stringReplaceAll;
function stringReplaceAll(AFindText, ARepText) {
    raRegExp = new RegExp(AFindText, "g");
    return this.replace(raRegExp, ARepText)
}
String.prototype.endWith = function (str) {
    if (str == null || str == "" || this.length == 0 || str.length > this.length)
        return false;
    if (this.substring(this.length - str.length) == str)
        return true;
    else
        return false;
    return true;
}
String.prototype.startWith = function (str) {
    if (str == null || str == "" || this.length == 0 || str.length > this.length)
        return false;
    if (this.substr(0, str.length) == str)
        return true;
    else
        return false;
    return true;
}
String.prototype.gblen = function () {
    var len = 0;
    for (var i = 0; i < this.length; i++) {
        if (this.charCodeAt(i) > 127 || this.charCodeAt(i) == 94) {
            len += 2;
        } else {
            len++;
        }
    }
    return len;
}
function getBrowserType() {
    var Sys = {};
    var ua = navigator.userAgent.toLowerCase();
    var s;
    (s = ua.match(/msie ([\d.]+)/)) ? Sys.ie = s[1] :
        (s = ua.match(/firefox\/([\d.]+)/)) ? Sys.firefox = s[1] :
            (s = ua.match(/chrome\/([\d.]+)/)) ? Sys.chrome = s[1] :
                (s = ua.match(/opera.([\d.]+)/)) ? Sys.opera = s[1] :
                    (s = ua.match(/version\/([\d.]+).*safari/)) ? Sys.safari = s[1] : 0;
    if (Sys.ie) {
        if (Sys.ie == '9.0') {
            return "ie9";
        } else if (Sys.ie == '8.0') {
            return "ie8";
        } else {
            return "ie";
        }
    }
    if (Sys.firefox) {
        return "firefox";
    }
    if (Sys.chrome) {//Js判断为谷歌chrome浏览器
        return "chrome";
    }
    if (Sys.opera) {//Js判断为opera浏览器
        return "opera";
    }
    if (Sys.safari) {//Js判断为苹果safari浏览器
        return "safari";
    }
    return "";
}


function getObjectURL(file) {
    var url = null;
    if (window.createObjectURL != undefined) {
        url = window.createObjectURL(file)
    } else if (window.URL != undefined) {
        url = window.URL.createObjectURL(file)
    } else if (window.webkitURL != undefined) {
        url = window.webkitURL.createObjectURL(file)
    }
    return url;
}

function sleep(n) {
    var start = new Date().getTime();
    while (true) if (new Date().getTime() - start > n) break;
}


function CheckIsIE()
{
    if (navigator.appName.toUpperCase() == 'MICROSOFT INTERNET EXPLORER') 
    { 
        return true;
    }else 
    { 
        return false; 
    }
}

// JScript 文件
function addfavorite(url,text)
{
    if(confirm("网站名称："+text+"\n网址："+url+"\n确定添加收藏?"))
    {
        var ua = navigator.userAgent.toLowerCase();
        if(ua.indexOf("msie 8")>-1)
        {
            external.AddToFavoritesBar(url,text,'IT有道');    //IE8
        }else
        {
            try 
            {
                window.external.addFavorite(url,text);
            }catch(e) 
            {
                try 
                {
                     window.sidebar.addPanel(text, url, "");    //firefox
                }catch(e) 
                {
                    alert("加入收藏失败，请使用Ctrl+D进行添加");
                }
            }
        }
    }
    return false;
}

function setHomepage(url) {
    if (document.all) {
        document.body.style.behavior = 'url(#default#homepage)';
        document.body.setHomePage(window.location.href);
    } else if (window.sidebar) {
        if (window.netscape) {
            try {
                netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect");
            } catch (e) {
                alert("该操作被浏览器拒绝，如果想启用该功能，请在地址栏内输入 about:config,然后将项 signed.applets.codebase_principal_support 值该为true");
            }
        }
        var prefs = Components.classes['@mozilla.org/preferences-service;1'].getService(Components.interfaces.nsIPrefBranch);
        prefs.setCharPref('browser.startup.homepage', window.location.href);
    } else {
        alert('您的浏览器不支持自动自动设置首页, 请使用浏览器菜单手动设置!');
    }
}

function NavFunction(urlstr, idstr, titstr) {
    displayCurLocPageProx(titstr);
    mainTabPanel.loadTab(urlstr, idstr, titstr);
}

function NavFunctionWithCallback(urlstr, idstr, titstr,succf) {
    displayCurLocPageProx(titstr);
    mainTabPanel.loadJsTabWithCallBack(urlstr, idstr, titstr,succf);
}

function getstat_root()
{
    var strurl = parent.topframe.location.href.replace("Page/Frame/top.html?v="+cpsVersion,"");
    //strurl = strurl + "index.aspx";
    return strurl;
}

function QueryString(key) 
{ 
    var url = window.location.search;
    var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
    var result = url.substr(1).match(reg);
    return result ? decodeURIComponent(result[2]) : null;
}

function WinClose() {
    try {
        var browserName = navigator.appName;
        if (browserName == "Netscape")
        {
            window.location.href = "about:blank"; 
            window.close();
        } else if (browserName == "Microsoft Internet Explorer")
        {
            window.opener = null;
            window.close();
        }
    } catch (e) {

    }
}

function CloseWindow(window) {
    try {
        var browserName = navigator.appName;
        if (browserName == "Netscape")
        {
            window.location.href = "about:blank"; 
            window.close();
        } else if (browserName == "Microsoft Internet Explorer")
        {
            window.opener = null;
            window.close();
        }
    } catch (e) {

    }
}

function FloatMainForm()
{
    if(parent.window.document.body.clientHeight<window.screen.availHeight-60)
    {
        CloseWindow(parent.window);
        OpenFullWin(parent.location.href);   
        
    }else
    {
        CloseWindow(parent.window);
        OpenDefWin(parent.location.href)
    }
}

function LogOutSys()
{
    $.ajax({
        type: "GET",
        url: "../../Logout",
        success: function (result) {
            if(_sysmark!=undefined)
            {
                var storage = window.sessionStorage;
                if(storage!=undefined)
                {
                    _xsystem=storage.getItem(_sysmark+"_xsystem");
                    if(_xsystem==true||_xsystem=="true")
                    {
                        CloseWindow(window);
                    }else {
                        window.location.href="../../Index";
                    }
                }
            }else {
                window.location.href="../../Index";
            }
        }
    });
}
function MTree_LogOutSys() 
{
    $.ajax({
        type: "GET",
        url: "../../Logout",
        success: function (result) {
            if(_sysmark!=undefined)
            {
                var storage = window.sessionStorage;
                if(storage!=undefined)
                {
                    _xsystem=storage.getItem(_sysmark+"_xsystem");
                    if(_xsystem==true||_xsystem=="true")
                    {
                        CloseWindow(window);
                    }else {
                        window.location.href="../../Index";
                    }
                }
            }else {
                window.location.href="../../Index";
            }
        }
    });
}

function ExitSys()
{
    CloseWindow(parent.window);
}
function MTree_ExitSys() 
{
    CloseWindow(parent.parent.window);
}
