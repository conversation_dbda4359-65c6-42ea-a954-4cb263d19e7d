//弹出窗体
function OpenWin(url,scol)
{
    var sFeatures = "dependent=yes,resizable=yes,toolbar=yes,status=yes,location=no,directories=yes,menubar=yes,";
	sFeatures+="scrollbars="+(scol?scol:"no");
	window.open(url, "_blank", sFeatures, false);
}

function OpenDefWin(url)
{
    var oW=window.screen.availWidth-10;
    var oH=window.screen.availHeight-120;
    var sFeatures="dependent=yes,resizable=yes,toolbar=yes,status=yes,location=yes,directories=yes,menubar=yes,scrollbars=yes,top=0,left=0,";
	sFeatures+="width="+oW+",";
	sFeatures+="height="+oH;
    window.open(url, "_blank", sFeatures, false);
}
function OpenWin(strContext,url,iW,iH)
{
    var oW=iW?iW:800;
    var oH=iH?iH:570;
    var wW=window.screen.availWidth;
    var wH=window.screen.availHeight;
    var left=(wW-oW)/2;
    var top=(wH-oH)/2;
    var sFeatures = "dependent=yes,resizable=no,toolbar=no,location=no,status=no,directories=no,menubar=no,scrollbars=no,";
	sFeatures+="width="+oW+",";
	sFeatures+="height="+oH+",";
	sFeatures+="top="+top+",";
	sFeatures+="left="+left;
    window.open(strContext+url, "_blank", sFeatures, false);
}

function ShowModalWin(strContext,strUrl,strWidth,strHeight)
{
	var dlgHeight,dlgWidth;
	dlgHeight=window.screen.availHeight;
	dlgWidth=window.screen.availWidth;
	if(arguments.length>=4)
	{
		dlgHeight=strHeight;
		dlgWidth=strWidth;
	}
    var dlgTop = (window.screen.availHeight - 20 - dlgHeight) / 2;
    var dlgLeft = (window.screen.availWidth - 10 - dlgWidth) / 2;

    var sFeatures = "dialogWidth:" + dlgWidth + "px;dialogHeight:" + dlgHeight + "px;dialogTop:" + dlgTop + "px;dialogLeft:" + dlgLeft + "px;center:yes;edge:sunken;scroll:no;location=no;resizable:no;status:yes;help:no";
	var url=strContext+strUrl;
    //alert(sFeatures);
	return window.showModalDialog(url,self,sFeatures);
}

function OpenFullWin(apppath,where,strScrollbars){
	//*** 弹出窗口的类型
	var h=window.screen.availHeight-60;
	var w=window.screen.availWidth-10;
	var ssb="yes";
	if(arguments.length=3)
	{
		ssb=strScrollbars;
	}
	var winType="toolbar=no,directories=no,status=yes,location=no,resizable=yes,alwaysRaised=yes,dependent=yes,";
	winType+="top=0,left=0,";
	winType+="scrollbars="+ssb+",menubar=no,width="+w+",";
	winType+="height="+h;
	var lWhere="";
	if(arguments.length==2)
		lWhere=where;
	return window.open(apppath,lWhere,winType);
}