/*
Easyui 的文本框验证 扩展方法 
<input class="easyui-textbox" name="IDCard"  data-options="required:true,validType:'idcard'" /> 进行身份证验证
by hey<PERSON><PERSON>
*/
var ps = {
		msg:'',
		age:0,
		sex:'',
		birthday:'',
		aCity:{11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",
			32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北",43:"湖南",44:"广东",
			45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",63:"青海",
			64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"
		},
		setNull:function(){
			$('#age').val('');
			 $('#birthday').datebox('setValue','');  
		}
};
$.extend($.fn.validatebox.defaults.rules, {
    maxNumberVal:{
		validator:function(value, param){
		return /^(100|[1-9]\d|\d)$/.test(value);
		},
		message : '请输入0~100之间的数字！'
	},
    idcard: {// 验证身份证
        validator: function (value) {
            return /^\d{15}(\d{2}[A-Za-z0-9])?$/i.test(value);
        },
        message: '身份证号码格式不正确'
    },
    idcards: {// 验证身份证 并且带身份证上的相关信息
        validator: function (ids) {
			   var iSum=0 ;  
			    if(!/^\d{17}(\d|x)$/i.test(ids)){
			    	ps.msg = "你输入的身份证长度或格式错误";
			    	ps.setNull();
			    	return false;
			    }
			    ids=ids.replace(/x$/i,"a");   
			    if(ps.aCity[parseInt(ids.substr(0,2))]==null) {
			    	ps.msg =  "你的身份证地区非法";  
			    	ps.setNull();
			    	return false;
			    }
			    sBirthday=ids.substr(6,4)+"-"+Number(ids.substr(10,2))+"-"+Number(ids.substr(12,2));   
			    var d=new Date(sBirthday.replace(/-/g,"/")) ;  
			    if(sBirthday!=(d.getFullYear()+"-"+ (d.getMonth()+1) + "-" + d.getDate())){
			    	ps.msg =  "身份证上的出生日期非法";   
			    	ps.setNull();
			    	return false;
			    }
			    for(var i = 17;i>=0;i --){
			    	iSum += (Math.pow(2,i) % 11) * parseInt(ids.charAt(17 - i),11) ;  
			    }
			    if(iSum%11!=1){
			    	ps.msg =  "你输入的身份证号非法";
			    	ps.setNull();
			    	return false;
			    }	
			    
		      ps.sex=ids.substr(16,1)%2?"男":"女";
		      
		   var sBirthday=ids.substr(6,4)+"-"+Number(ids.substr(10,2))+"-"+Number(ids.substr(12,2));
			    var d=new Date(sBirthday.replace(/-/g,"/")) ;
			    ps.birthday=d.getFullYear()+"-"+ (d.getMonth()+1) + "-" + d.getDate()
			   $('#birthday').datebox('setValue',ps.birthday);  
			    	  var myDate = new Date();
             var month = myDate.getMonth() + 1;
             var day = myDate.getDate();
             var age = myDate.getFullYear() - ids.substring(6, 10) - 1;
		        if (ids.substring(10, 12) < month || ids.substring(10, 12) == month && ids.substring(12, 14) <= day) {
		            age++;
		        }
		        ps.age=age;
		        $('#age').val(ps.age);
		        var d=$('#sex').combobox('getData');
		        for(var i=0;i<d.length;i++){
		        	   if(d[i].text==ps.sex){
		        		   $('#sex').combobox('select',d[i].id);
		        	   }
		        }
			    return true;
		
        },
        message: '身份证信息有错误'
    },
    minLength: {
        validator: function (value, param) {
            return value.length >= param[0];
        },
        message: '请输入至少（2）个字符'
    },
    length: {
        validator: function (value, param) {
            var len = $.trim(value).length+(value.split('\n')).length-1;//换行符\n在后台长度为2（\r\n）
            return len >= param[0] && len <= param[1];
        },
        message: '输入内容长度必须介于{0}和{1}之间'
    },
    number:{
        validator: function (value) {
      	  var reg = /^[0-9]*$/;  
            return reg.test(value);  
	    },
	    message: '输入内容只能是数字'    	
    },
	phone: {// 验证固话号码
	        validator: function (value) {
	            //return /^((\d2,3)|(\d{3}\-))?(0\d2,3|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$/i.test(value);
	        	 var reg = /^[0-9]*$/;
	              return reg.test(value);  
	        },
	        message: '格式不正确,电话号码只能是数字'
    },
    telephone:{
    	 validator: function (value) {
	        	  var reg = /^[0-9\-]{0,20}$/;
	              return reg.test(value);  
	        },
	        message: '输入内容只能包含数字或"-"'
    },
    mobile: {// 验证手机号码
        validator: function (value) {
            return /^(13|15|18)\d{9}$/i.test(value);
        },
        message: '手机号码格式不正确'
    },
    mobileNumber: {// 验证固话号码
        validator: function (value) {
        	  var reg = /^[0-9]*$/;  
              return reg.test(value);  
        },
        message: '格式不正确,手机号码只能是数字'
    },
    intOrFloat: {// 验证整数或小数
        validator: function (value) {
            return /^\d+(\.\d+)?$/i.test(value);
        },
        message: '请输入数字，并确保格式正确'
    },
    currency: {// 验证货币
        validator: function (value) {
            return /^\d+(\.\d+)?$/i.test(value);
        },
        message: '货币格式不正确'
    },
    qq: {// 验证QQ,从10000开始
        validator: function (value) {
            return /^[1-9]\d{4,9}$/i.test(value);
        },
        message: 'QQ号码格式不正确'
    },
    box: {
        validator: function (value) {
           if($.trim(value)==""){
        	   return false;
           }
           return true;
        }
    },
    combox:{
    	 validator: function (value) {
             if($.trim(value)==""){
          	   return false;
             }
             return true;
          }
    },
    fileUpload:{
    	 validator: function (value,param) {
	    		 if($.trim(value)==""){
	    			 return false;
	    		 }
	    	   var ext = value.substring(value.lastIndexOf(".")+1);
	           var fileType = param.split(",");
	           for(var i=0;i<fileType.length;i++){
	        	 if(ext.toLowerCase()==fileType[i].toLowerCase()){
	        		 return true;
	        	 }
	          }
	           return false;
          },
          message:'文件类型为：{0}'
    },
    datagridBox:{
    	 validator: function (value,param) {
    	     return false;
             if($.trim(value)==""){
          	   return false;
             }
             return true;
          }
    },
    integer: {// 验证整数 可正负数
        validator: function (value) {
            //return /^[+]?[1-9]+\d*$/i.test(value);
            //return /^([+]?[0-9])|([-]?[0-9])+\d*$/i.test(value);
        	return /^[0-9]*[1-9][0-9]*$/i.test(value);
        },
        message: '请输入大于0的整数'
    },
    age: {// 验证年龄
        validator: function (value) {
            return /^(?:[1-9][0-9]?|1[01][0-9]|120)$/i.test(value);
        },
        message: '年龄必须是0到120之间的整数'
    },
    chinese: {// 验证中文
        validator: function (value) {
            return /^[\Α-\￥]+$/i.test(value);
        },
        message: '请输入中文'
    },
    english: {// 验证英语
        validator: function (value) {
            return /^[A-Za-z]+$/i.test(value);
        },
        message: '请输入英文'
    },
    unnormal: {// 验证是否包含空格和非法字符
        validator: function (value) {
        	return !/((?=[\x21-\x7e]+)[^A-Za-z0-9])/i.test(value);
        },
        message: '输入值不能为空和包含其他非法字符'
    },
    username: {// 验证用户名
        validator: function (value) {
            return /^[a-zA-Z][a-zA-Z0-9_]{5,15}$/i.test(value);
        },
        message: '用户名不合法（字母开头，允许6-16字节，允许字母数字下划线）'
    },
    faxno: {// 验证传真
        validator: function (value) {
            //            return /^[+]{0,1}(\d){1,3}[ ]?([-]?((\d)|[ ]){1,12})+$/i.test(value);
            return /^((\d2,3)|(\d{3}\-))?(0\d2,3|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$/i.test(value);
        },
        message: '传真号码不正确'
    },
    zip: {// 验证邮政编码
        validator: function (value) {
            return /^[1-9]\d{5}$/i.test(value);
        },
        message: '邮政编码格式不正确'
    },
    ip: {// 验证IP地址
        validator: function (value) {
            return /^(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])$/i.test(value);
        },
        message: 'IP地址格式不正确'
    },
    name: {// 验证姓名，可以是中文或英文
        validator: function (value) {
            return /^[\Α-\￥]+$/i.test(value) | /^\w+[\w\s]+\w+$/i.test(value);
        },
        message: '请输入姓名'
    },
    date: {// 验证姓名，可以是中文或英文
        validator: function (value) {
            //格式yyyy-MM-dd或yyyy-M-d
            return /^(?:(?!0000)[0-9]{4}([-]?)(?:(?:0?[1-9]|1[0-2])\1(?:0?[1-9]|1[0-9]|2[0-8])|(?:0?[13-9]|1[0-2])\1(?:29|30)|(?:0?[13578]|1[02])\1(?:31))|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)([-]?)0?2\2(?:29))$/i.test(value);
        },
        message: '请输入合适的日期格式'
    },
    msn: {
        validator: function (value) {
        	
            return  /^(?=\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$).{0,50}$/.test(value);  
            //    /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(value);
        },
        message: '请输入有效的msn账号(例：abc@hotnail(msn/live).com)'
    },
    same: {
        validator: function (value, param) {
            if ($("#" + param[0]).val() != "" && value != "") {
                return $("#" + param[0]).val() == value;
            } else {
                return true;
            }
        },
        message: '两次输入的密码不一致！'
    },
    storageCode: {
        validator: function (value, param) {
        	if(!/^[0-9a-zA-Z]+$/.test(value) || value.length > 50){
        		return false;
        	}
           return true;
        },
        message: '编码长度必须小于50且只能由字母和数字组成！'
    }
});
            

//datagrid设置只读
$.extend(
	$.fn.datagrid.defaults.editors,
	{
		textReadonly : {
			init : function(container, options) {
				var input = $(
						'<input type="text" readonly="readonly" class="datagrid-editable-input">').appendTo(container);
			return input;
		},
		getValue : function(target) {
			return $(target).val();
		},
		setValue : function(target, value) {
			$(target).val(value);
		},
		resize : function(target, width) {
			var input = $(target);
			if ($.boxModel == true) {
				input.width(width- (input.outerWidth() - input.width()));
			} else {
				input.width(width);
			}
		}
	}
});
//动态添加ITEM
//$('#tt').datagrid("addToolbarItem",[{"text":"xxx"},"-",{"text":"xxxsss","iconCls":"icon-ok"}])
//$('#tt').datagrid("removeToolbarItem","GetChanges")//根据btn的text删除
//$('#tt').datagrid("removeToolbarItem",0)//根据下标删除
$.extend($.fn.datagrid.methods, {  
    addToolbarItem: function(jq, items){  
        return jq.each(function(){  
            var toolbar = $(this).parent().prev("div.datagrid-toolbar");
            for(var i = 0;i<items.length;i++){
                var item = items[i];
                if(item === "-"){
                    toolbar.append('<div class="datagrid-btn-separator"></div>');
                }else{
                   var btn=$("<a href=\"javascript:void(0)\"  onclick=\"test()\"></a>");
                   btn[0].onclick=eval(item.handler||function(){});
                   btn.css("float","left").appendTo(toolbar).linkbutton($.extend({},item,{plain:true}));
               }
           }
           toolbar = null;
       });  
   },
   removeToolbarItem: function(jq, param){  
       return jq.each(function(){  
           var btns = $(this).parent().prev("div.datagrid-toolbar").children("a");
           var cbtn = null;
           if(typeof param == "number"){
               cbtn = btns.eq(param);
           }else if(typeof param == "string"){
               var text = null;
               btns.each(function(){
                   text = $(this).data().linkbutton.options.text;
                   if(text == param){
                       cbtn = $(this);
                       text = null;
                       return;
                   }
               });
           } 
           if(cbtn){
               var prev = cbtn.prev()[0];
               if(prev && next && prev.nodeName == "DIV" && prev.nodeName == next.nodeName){
                   $(prev).remove();
               }else if(next && next.nodeName == "DIV"){
                   $(next).remove();
               }else if(prev && prev.nodeName == "DIV"){
                   $(prev).remove();
               }
               cbtn.remove();    
               cbtn= null;
           }                        
       });  
   }                 
});




