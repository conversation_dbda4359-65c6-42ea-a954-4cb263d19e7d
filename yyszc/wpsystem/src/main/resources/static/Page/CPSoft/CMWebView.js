function CMWebView(ownerStr) 
{
    var obj = this;

    var groupTree = new Ext.tree.TreePanel({
        title: '站点资源树',
        region: 'west',
        split: true,
        width: 326,
        minSize: 345,
        maxSize: 400,
        margins: '0 0 0 5',
        autoScroll: true,
        rootVisible: false,
        root: new Ext.tree.AsyncTreeNode({ id: 'root', text: '站点资源树', expanded: true }),
        loader: new Ext.tree.TreeLoader({
            dataUrl: "../../Service/CpSoft/CmWebView/GetWebTreeByPath",
            preloadChildren: true
        }),
        listeners:
        {
            'dblclick':function(node, e) {
                var furl = node.attributes.id;
                var dirf = node.attributes.dirf;
                if (dirf == "1") {
                    if (!node.expanded) {
                        node.expand();
                    }
                } else {
                    LoadFileFromWeb(furl);
                    var title = "web站点资源管理..." + furl;
                    CMWebView_Window.setTitle(title);
                }
            }
        }
    });

    var LoadFileFromWeb = function (furl) {
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmWebView/LoadFileFromWeb',
            params: { furl: furl},
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText != undefined) {
                    Ext.getCmp(ownerStr + ".CMWebView.IdCallBack").setValue(res.responseText);
                }
            }
        });
    }

    var groupPanel = new Ext.Panel({
        layout: 'border',
        items: [
            groupTree,
            {
                region: 'center',
                layout: 'fit',
                items: [
                    {
                        id: ownerStr + '.CMWebView.IdCallBack',
                        xtype: "textarea",
                        name: 'FileCallBack',
                        preventScrollbar: false,
                        grow: false,
                        height: 240,
                        allowBlank: true,
                        anchor: '100%'
                    }
                ]
            }
        ]
    });

    CMWebView_Window = new Ext.Window({
        width:1200,
        height:720,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: groupPanel,
        layout:'fit',
        title: 'web站点资源管理...',
        buttonAlign: 'center',
        buttons: [
        {
            text: '执行上传',
            height: 30,
            handler: function () {
                var oCMComfort = new CMComfort(ownerStr);
                oCMComfort.ShowWindow();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                CMWebView_Window.close();
            }
        }]
    });

    this.ShowWindow = function () {
        groupTree.getRootNode().expand();
        CMWebView_Window.show();
     }
}

CMWebView.prototype = {
    constructor: CMWebView
}