DownloadJSByParams('../../Page/CPSoft/common.js', function ()
{
    var FilesArray = [
        '../../Page/CPSoft/GrptProx.js?version='+_cps_js_version,
        '../../Page/CPSoft/ExtGridExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version='+_cps_js_version,
        '../../Page/CPSoft/GRPT_ImpInfo.js?version='+_cps_js_version,
    ];

    Import.LoadJsList(FilesArray, function () {
        DownloadJS('../../Page/CPSoft/GRPTI_ManageMain.js?version='+_cps_js_version);
    });

    document.getElementById('../../Page/CPSoft/GRPTI_ManageQueue.js').Destroy = function()
    {
        var element = document.getElementById('../../Page/CPSoft/GRPTI_ManageMain.js?version='+_cps_js_version);
        if(element!=undefined){
            element.parentNode.removeChild(element);
        }
        Ext.getCmp("CPS.GRPTI_ManageMain.IdGRPTI_FormWindow").destroy();
    }
    document.getElementById('../../Page/CPSoft/GRPTI_ManageQueue.js').DataReload = function() {

    }
});
