function GRPT_ImpInfo(ownerStr) 
{
    var obj = this;
    this.successfn = 0;
    this.failfn = 0;
    this.rptId = 0;
            
    //===================导入界面===========================start
    //上传表单定义
    this.UploadForm = new Ext.FormPanel({
        id: ownerStr + '.GRPT_ImpForm.IdUploadForm',
        labelWidth: 60,
        width: 600,
        height: 280,
        frame: true,
        fileUpload: true,
        bodyStyle: 'padding:10px',
        items:
        [
            {
                id: ownerStr + '.GRPT_ImpForm.IdFileName',
                xtype: 'fileuploadfield',
                emptyText: '请选择一个文件',
                fieldLabel: '模板文件',
                allowBlank: false,
                name: 'FileName',
                buttonText: '',
                anchor: '98%',
                blankText: '文件不能为空',
                editable: false,
                buttonCfg:
                {
                    iconCls: 'upload-icon'
                }
            }
        ],
        reader: new Ext.data.JsonReader({
            id: 'Id',
            root: 'data',
            fields: []
        })
    });

    //上传窗口提交函数
    this.ExecuteSubmit = function () {
         var fileType = "jrxml";
         if (obj.UploadForm.form.isValid()) {
             var formData = new FormData();
             //var ftag=Ext.getCmp(ownerStr + '.GRPT_ImpForm.IdFileName').getFileInputId();
             //var file = Ext.getDom(ftag).files.item(0);
             var file = Ext.getCmp(ownerStr + '.GRPT_ImpForm.IdFileName').fileInput.dom.files.item(0);
             formData.append('file', file);
             formData.append('rptId',obj.rptId);

             _ExtUploadFile(
                 '../../Service/CpSoft/GrptI/ImportGRPTInfo',
                 formData,
                 function(data){
                     var json0 = Ext.util.JSON.decode(data);
                     if(json0.text!=undefined)
                     {
                         Ext.MessageBox.alert("操作提示",json0.text);
                         if (obj.successfn != 0) obj.successfn();
                     }
                 },function () {
                     Ext.MessageBox.alert("操作提示","上传文模板件失败！");
                     if (obj.failfn != 0) obj.failfn();
                 }
             );
         } else {
             if (obj.failfn != 0) obj.failfn();
         }
     }

     this.InitView = function () {
         obj.UploadForm.form.reset();
     }
    //===================导入界面===========================end
}

GRPT_ImpInfo.prototype = {
    constructor: GRPT_ImpInfo
}