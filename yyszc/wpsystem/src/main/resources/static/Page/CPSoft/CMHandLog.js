function CMHandLog(ownerStr) {
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var HANDLOG_FormWindow_ttb = new Ext.Toolbar({
        items: [
            '操作标识',
            {
                id: ownerStr + '.toolbar.IdHANDLOG_MARK',
                xtype: "trigger",
                name: 'HANDLOG_MARK',
                triggerClass:'x-form-my-trigger',
                editable:false,
                width: 200,
                onTriggerClick:function (e) {
                    e.preventDefault();
                    e.stopEvent();

                    var strparams = "";
                    var oSmartCheck = new SmartCheck(ownerStr + ".SmartCheck", "CPS_T_HAND_LOG", "", "hl_mark", "300,200",
                        "操作标识", "hl_mark",
                        ownerStr + ".toolbar.IdHANDLOG_MARK",
                        0, "hl_mark", "hl_mark",800,600,"distinct","");

                    oSmartCheck.setTitle("操作选择列表");
                    oSmartCheck.Show(strparams);

                    return false;
                }
            }, '-', '->', '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    var mark = Ext.getCmp(ownerStr + '.toolbar.IdHANDLOG_MARK').getValue();
                    Ext.getCmp(ownerStr + '.toolbar.IdHANDLOG_VIEW').setValue("");

                    Ext.Ajax.request({
                        url: '../../Service/CpSoft/HandLog/GatherHandLog',
                        params:
                        {
                            sMark: mark
                        },
                        waitTitle: "请稍候",
                        waitMsg: '正在抓取操作日志...',
                        success: function (response, options) {
                            if (response.responseText == "" || response.responseText == undefined) return;
                            var responseArray = Ext.util.JSON.decode(response.responseText);
                            if (responseArray.success == true&&responseArray.text!=undefined) {
                                Ext.getCmp(ownerStr + '.toolbar.IdHANDLOG_VIEW').setValue(responseArray.text);
                            }
                        }
                    });
                }
            }
        ]
    });

    var HANDLOG_FormWindow_ftb = new Ext.Toolbar({
        height: 40,
        autoHeight: false,
        items: [{
            text: '关闭',
            width: 90,
            height: 30,
            handler: function () {
                HANDLOG_FormWindow.close();
            }
        }]
    });

    var HANDLOG_FormWindow = new Ext.Window({
        id: ownerStr + '.IdHANDLOG_FormWindow',
        height: 720,
        width: 1000,
        closeAction: 'close',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: false,
        items: [{
            id:ownerStr + '.toolbar.IdHANDLOG_VIEW',
            xtype: 'textarea',
            fieldLabel: '操作日志',
            labelWidth: 60,
            preventScrollbars: false,
            autoScroll:true,
            grow: false
        }],
        title: '表单',
        buttonAlign: 'center',
        tbar: HANDLOG_FormWindow_ttb,
        fbar: HANDLOG_FormWindow_ftb
    });

    this.ShowWindow=function() {
        HANDLOG_FormWindow.setTitle(ExtractTitleString("操作日志"));
        HANDLOG_FormWindow.show();
    }
}

CMHandLog.prototype = {
    constructor: CMHandLog
}