Ext.override(Ext.form.NumberField, {
    setValue: function (v) {
        if (this.allowDecimals) {
            if (this.initFormat != undefined && this.initFormat == true) {
                v = typeof v == 'number' ? v : parseFloat(String(v).replace(this.decimalSeparator, "."));
                v = isNaN(v) ? '' : v.toFixed(this.decimalPrecision).replace(".", this.decimalSeparator);
            }
        }
        return Ext.form.NumberField.superclass.setValue.call(this, v);
    }
});

var rmark1 = '';
var rmark2 = '';
var rmark3 = '';

function ExtractMark() {
   if (LoginPerson != null && LoginPerson != undefined) {
        rmark1 = LoginPerson.RealName;
        rmark2 = LoginPerson.TopGroupName;
        rmark3 = FormatDateStr(new Date(),'yyyy-MM-dd');
   }
}

Ext.override(Ext.grid.GridView, {
    afterRender: function () {
        if (!this.ds || !this.cm) {
            return;
        }

        this.mainBody.dom.innerHTML = this.renderBody() || '&#160;';
        this.processRows(0, true);

        if (this.deferEmptyText !== true) {
            this.applyEmptyText();
        }

        this.grid.fireEvent('viewready', this.grid);

        ExtractMark();
        var marko = this.mainBody.findParent("div.x-grid3-scroller", 2, true);
        if (marko != undefined) {
            if ($j(marko.dom) != null) {
                $j(marko.dom).watermark({
                    texts: [rmark1,rmark2,rmark3],
                    textColor: "#eeeeee",
                    textFont: '26px 微软雅黑',
                    textRotate: -30
                });
            }
        }
    }
});

Ext.override(Ext.FormPanel, {
    onRender: function (ct, position) {
        this.initFields();
        Ext.FormPanel.superclass.onRender.call(this,ct, position);
        this.form.initEl(this.body);

        ExtractMark();
        var marko = this.body;
        if (marko != undefined) {
            if ($j(marko.dom) != null) {
                $j(marko.dom).watermark({
                    texts: [rmark1,rmark2,rmark3],
                    textColor: "#DDDDDD",
                    textFont: '26px 微软雅黑',
                    textRotate: -30
                });
            }
        }
    }
});

Ext.override(Ext.menu.Menu, {
    hide : function(deep){
        if (!this.isDestroyed) {
            this.deepHide = deep;
            Ext.menu.Menu.superclass.hide.call(this);
            delete this.deepHide;

            //新增强制隐藏效果
            if(this.getEl()!=undefined)
            {
                this.getEl().addClass('x-hide-menu');
            }
        }
    },
    showAt : function(xy, parentMenu){
        if(this.fireEvent('beforeshow', this) !== false){
            this.parentMenu = parentMenu;
            if(!this.el){
                this.render();
            }
            if(this.enableScrolling){
                // set the position so we can figure out the constrain value.
                this.el.setXY(xy);
                //constrain the value, keep the y coordinate the same
                xy[1] = this.constrainScroll(xy[1]);
                xy = [this.el.adjustForConstraints(xy)[0], xy[1]];
            }else{
                //constrain to the viewport.
                xy = this.el.adjustForConstraints(xy);
            }
            //新增强制显示效果
            if(this.el!=undefined)
            {
                this.el.removeClass('x-hide-menu');
            }

            this.el.setXY(xy);
            this.el.show();
            Ext.menu.Menu.superclass.onShow.call(this);
            if(Ext.isIE){
                // internal event, used so we don't couple the layout to the menu
                this.fireEvent('autosize', this);
                if(!Ext.isIE8){
                    this.el.repaint();
                }
            }
            this.hidden = false;
            this.focus();
            this.fireEvent('show', this);
        }
    }
});

