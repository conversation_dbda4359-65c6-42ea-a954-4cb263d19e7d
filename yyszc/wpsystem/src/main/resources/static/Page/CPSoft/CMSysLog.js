function CMSysLog(ownerStr) {
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var SYSLOG_FormWindow_ttb = new Ext.Toolbar({
        items: [
        '开始日期',
        {
            id: ownerStr + '.toolbar.IdSYSLOG_SDATE',
            xtype: "datefield",
            name: 'SYSLOG_SDATE',
            width: 120,
            format: 'Y-m-d'
        },
        '结束日期',
        {
            id: ownerStr + '.toolbar.IdSYSLOG_EDATE',
            xtype: "datefield",
            name: 'SYSLOG_EDATE',
            width: 120,
            format: 'Y-m-d'
        },
        '匹配模式',
        {
            id: ownerStr + '.toolbar.IdSYSLOG_PATCH',
            xtype: "textfield",
            name: 'SYSLOG_PATCH',
            width: 120
        }, '-', '->', '-',
        {
            text: '查询',
            iconCls: 'ButtonFormLook',
            handler: function () {
                var sqrydate = Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_SDATE').getValue();
                var eqrydate = Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_EDATE').getValue();
                var sqrypatch = Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_PATCH').getValue();
                Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_VIEW').setValue("");

                Ext.Ajax.request({
                    url: '../../Service/CpSoft/SysLog/GatherSysLog',
                    params:
                    {
                        sQryDate: sqrydate,
                        eQryDate: eqrydate,
                        sQryPatch: sqrypatch
                    },
                    waitTitle: "请稍候",
                    waitMsg: '正在抓取系统日志...',
                    success: function (response, options) {
                        if (response.responseText == "" || response.responseText == undefined) return;
                        var responseArray = Ext.util.JSON.decode(response.responseText);
                        if (responseArray.success == true&&responseArray.text!=undefined) {
                            Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_VIEW').setValue(responseArray.text);
                        }
                    }
                });
            }
        },
        {
            text: '删除',
            iconCls: 'ButtonFormLook',
            handler: function () {
                Ext.MessageBox.show({
                    title: "操作确认",
                    msg: "确定要清除日志信息？",
                    icon: Ext.MessageBox.INFO,
                    buttons: Ext.MessageBox.OKCANCEL,
                    buttonText: { ok: '确认', cancel: '取消' },
                    fn: function (button) {
                        if (button == "ok") {
                            var sqrydate = Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_SDATE').getValue();
                            var eqrydate = Ext.getCmp(ownerStr + '.toolbar.IdSYSLOG_EDATE').getValue();

                            Ext.Ajax.request({
                                url: '../../Service/CpSoft/SysLog/DeleteSysLog',
                                params:
                                {
                                    sQryDate: sqrydate,
                                    eQryDate: eqrydate
                                },
                                waitTitle: "请稍候",
                                waitMsg: '正在删除系统日志...',
                                success: function (response, options) {
                                    if (response.responseText == "") return;
                                    var responseArray = Ext.util.JSON.decode(response.responseText);
                                    if (responseArray.text!=undefined) {
                                        Ext.MessageBox.alert("操作提示", responseArray.text);
                                    }
                                }
                            });
                        }
                    }
                });
            }
        }]
    });

    var SYSLOG_FormWindow_ftb = new Ext.Toolbar({
        height: 40,
        autoHeight: false,
        items: [{
            text: '关闭',
            width: 90,
            height: 30,
            handler: function () {
                SYSLOG_FormWindow.close();
            }
        }]
    });

    var SYSLOG_FormWindow = new Ext.Window({
        id: ownerStr + '.IdSYSLOG_FormWindow',
        height: 720,
        width: 1000,
        closeAction: 'close',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: false,
        items: [{
            id:ownerStr + '.toolbar.IdSYSLOG_VIEW',
            xtype: 'textarea',
            fieldLabel: '操作日志',
            labelWidth: 60,
            preventScrollbars: false,
            autoScroll:true,
            grow: false
        }],
        title: '表单',
        buttonAlign: 'center',
        tbar: SYSLOG_FormWindow_ttb,
        fbar: SYSLOG_FormWindow_ftb
    });

    this.ShowWindow=function() {
        SYSLOG_FormWindow.setTitle(ExtractTitleString("系统日志"));
        SYSLOG_FormWindow.show();
    }
}

CMSysLog.prototype = {
    constructor: CMSysLog
}