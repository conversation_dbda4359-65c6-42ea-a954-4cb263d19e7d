Ext.namespace('CPS.GRPTI_ManageMain');

//初始化函数
CPS.GRPTI_ManageMain.Initialize = function ()
{
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'CPS.GRPTI_ManageMain';
    var specKeyNum = 0;

    var oGRPT_ImpInfo = new GRPT_ImpInfo(funcMark);

    //===================导入信息界面===========================end
    var ImportWindow = new Ext.Window({
        id: funcMark + '.IdUploadWindow',
        width: 620,
        height: 360,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: true,
        items: oGRPT_ImpInfo.UploadForm,
        title: '文件上传',
        buttonAlign: 'center',
        buttons:
            [
                {
                    text: '提交',
                    height: 30,
                    handler: function ()
                    {
                        ExecuteImport();
                    }
                },
                {
                    text: '关闭',
                    height: 30,
                    handler: function ()
                    {
                        ImportWindow.hide();
                    }
                }
            ]
    });

    function ExecuteImport()
    {
        ImportWindow.buttons[0].disable();
        oGRPT_ImpInfo.successfn = function (data)
        {
            ImportWindow.buttons[0].enable();
            ImportWindow.hide();

            LoadData();
        }
        oGRPT_ImpInfo.failfn = function ()
        {
            //Ext.Msg.alert('提示', "导入失败！");
            obj.ImportWindow.buttons[0].enable();
        }
        oGRPT_ImpInfo.ExecuteSubmit();
    }

    function onImportButtonClick()
    {
        var grid = Ext.getCmp(funcMark + ".IdGRPTI_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined)
        {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else
        {
            var rptid = grid.getSelectionModel().getSelected().data.GRPT_ID;

            ImportWindow.setTitle(ExtractTitleString("导入或覆盖报表模板"));
            oGRPT_ImpInfo.rptId = rptid;
            oGRPT_ImpInfo.InitView();
            ImportWindow.show();
        }
    }
    //===================导入信息界面===========================end

    //===================报表界面===========================start
    var DEFGroup = new Ext.form.RadioGroup({
        id: funcMark + '.GRPTI_Form.IdGRPT_DEFN',
        name: 'GRPT_DEFN',
        fieldLabel: '当前状态',
        anchor: '50%',
        style: 'padding-top:3px;height:20px;',
        items:
        [
          {
              boxLabel: '<span style="color:blue;">默认</span>',
              name: 'GRPT_DEFN',
              inputValue: '1',
              checked: true
          },
          {
              boxLabel: '<span style="color:red;">备用</span>',
              name: 'GRPT_DEFN',
              inputValue: '0'
          }
        ],
        listeners:
        {
            change: function (rdgroup, checked)
            {
                var chval = DEFGroup.getValue();
                Ext.getCmp(funcMark + '.GRPTI_Form.IdGRPT_DEF').setValue(chval);
            }
        }
    });

    var GRPTTCombo = new Ext.form.ComboBox({
        id: funcMark + '.GRPTI_Form.IdGRPTT_NAME',
        name: 'GRPTT_NAME',
        fieldLabel: '所属类型',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'GRPTT_ID',
        displayField: 'GRPTT_NAME',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/GrptT/GetGRPTTList2',
            fields: [
                 'GRPTT_ID', 'GRPTT_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners:
        {
            afterRender: function (obj)
            {
            },
            'select': function (c, rec, idx)
            {
                Ext.getCmp(funcMark + '.GRPTI_Form.IdGRPTT_NAME').setValue(rec.data.GRPTT_NAME);
                Ext.getCmp(funcMark + '.GRPTI_Form.IdGRPTT_ID').setValue(rec.data.GRPTT_ID);
            }
        }
    });

    var GRPTI_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.GRPTI_Form.IdGRPT_ID',
                name: 'GRPT_ID',
                xtype: "hidden"
            },
            GRPTTCombo,
            {
                id: funcMark + '.GRPTI_Form.IdGRPTT_ID',
                name: 'GRPTT_ID',
                xtype: "hidden"
            },
            DEFGroup,
            {
                id: funcMark + '.GRPTI_Form.IdGRPT_DEF',
                name: 'GRPT_DEF',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.GRPTI_Form.IdGRPT_SMARK',
                xtype: "textfield",
                name: 'GRPT_SMARK',
                fieldLabel: "报表备注",
                maxLength: 1000,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'GRPT_ID', mapping: 'GRPT_ID', type: 'int' },
                { name: 'GRPTT_ID', mapping: 'GRPTT_ID', type: 'string' },
                { name: 'GRPTT_NAME', mapping: 'GRPTT_NAME', type: 'string' },
                { name: 'GRPT_DEF', mapping: 'GRPT_DEF', type: 'string' },
                { name: 'GRPT_DEFN', mapping: 'GRPT_DEFN', type: 'string' },
                { name: 'GRPT_NAME', mapping: 'GRPT_NAME', type: 'string' },
                { name: 'GRPT_SFILE', mapping: 'GRPT_SFILE', type: 'string' },
                { name: 'GRPT_SMARK', mapping: 'GRPT_SMARK', type: 'string' }
            ]
        })
    });

    var GRPTI_FormWindow = new Ext.Window({
        id: funcMark + '.IdGRPTI_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: GRPTI_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function ()
                {
                    GRPTI_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick()
    {
        GRPTI_Form.form.reset();

        DEFGroup.setValue(1);
        Ext.getCmp(funcMark + '.GRPTI_Form.IdGRPT_DEF').setValue(1);

        GRPTI_FormWindow.setTitle(ExtractTitleString("报表==新增报表=="));
        GRPTI_FormWindow.buttons[0].handler = function ()
        {
            var submitButton = this;
            submitButton.disable();
            if (GRPTI_Form.form.isValid())
            {
                GRPTI_Form.form.doAction('submit',
                {
                    url: '../../Service/CpSoft/GrptI/AddGRPTI',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action)
                    {
                        submitButton.enable();
                        GRPTI_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action)
                    {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else
            {
                submitButton.enable();
            }
        };
        GRPTI_FormWindow.buttons[0].enable();
        GRPTI_FormWindow.show();

    }

    function onEditButtonClick()
    {
        var grid = Ext.getCmp(funcMark + ".IdGRPTI_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined)
        {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else
        {
            var title = "报表==修改报表==";
            GRPTI_FormWindow.setTitle(ExtractTitleString(title));
            GRPTI_FormWindow.buttons[0].handler = function ()
            {
                var submitButton = this;
                submitButton.disable();

                var id = GRPTI_Form.form.reader.jsonData.data[0].GRPT_ID;
                if (GRPTI_Form.form.isValid())
                {
                    GRPTI_Form.form.doAction('submit',
                    {
                        url: '../../Service/CpSoft/GrptI/ModifyGRPTI',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action)
                        {
                            submitButton.enable();
                            GRPTI_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action)
                        {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else
                {
                    submitButton.enable();
                }
            };

            GRPTI_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.GRPT_ID;
            var url = '../../Service/CpSoft/GrptI/GetGRPTIById?ID=' + id;
            GRPTI_FormWindow.show();
            GRPTI_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action)
                {
                    var defv = form.reader.jsonData.data[0].GRPT_DEF;
                    DEFGroup.setValue(defv);

                    GRPTI_FormWindow.buttons[0].enable();
                },
                failure: function (form, action)
                {
                }
            });
        };
    }

    function onLookButtonClick()
    {
        var grid = Ext.getCmp(funcMark + ".IdGRPTI_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined)
        {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else
        {
            var title = "报表==查看报表==";
            GRPTI_FormWindow.setTitle(ExtractTitleString(title));
            GRPTI_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.GRPT_ID;
            var url = '../../Service/CpSoft/GrptI/GetGRPTIById?ID=' + id;
            GRPTI_FormWindow.show();
            GRPTI_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action)
                {
                    var defv = form.reader.jsonData.data[0].GRPT_DEF;
                    DEFGroup.setValue(defv);

                    GRPTI_FormWindow.buttons[0].disable();
                },
                failure: function (form, action)
                {
                }
            });
        };
    }

    function onDeleteButtonClick()
    {
        var grid = Ext.getCmp(funcMark + ".IdGRPTI_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined)
        {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else
        {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn)
            {
                if (btn == 'yes')
                {
                    AjaxRequest(
                    {
                        url: "../../Service/CpSoft/GrptI/DeleteGRPTI",
                        successfn: function (data)
                        {
                            RefreshData();
                        },
                        failurefn: function (data)
                        {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.GRPT_ID }
                    });
                }
            });
        }
    }
    //===================报表界面===========================end  

    //===================报表管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
             {
                 text: '模板',
                 iconCls: 'ButtonFormEdit',
                 handler: onImportButtonClick
             },
            {
                text: '测试报表',
                iconCls: 'ButtonFormLook',
                handler: function()
                {
                    ExecuteReport('测试报表打印', 1,"237");
                }
            },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function ()
                       {
                           RefreshData();
                       }
                   }
               },
                {
                    text: '导出XLS',
                    iconCls: 'ButtonFormLook',
                    handler: function()
                    {
                        ExportExcel();
                    }
                }
	       ]
    });

    function RefreshData()
    {
        specKeyNum = 0;
        Ext.getCmp(funcMark + ".IdGRPTI_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData()
    {
        start = 0;
        specKeyNum = 0;
        Ext.getCmp(funcMark + ".IdGRPTI_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/CpSoft/GrptI/ExportExcel", {
            seachtj: Ext.getCmp(funcMark+'.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/GrptI/GetGRPTIList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['GRPT_ID', 'GRPTT_ID', 'GRPTT_NAME', 'GRPTIT_REMARK', 'GRPT_NAME', 'GRPT_SFILE', 'GRPT_SMARK']
        }),
        sortInfo: { field: "GRPT_ID", direction: "ASC" },
        remoteSort: true
    });

    function ExportRptFile(rptid) {
        _ExtDownloadFile("../../Service/CpSoft/GrptI/RptFileDownload", {
            rptId: rptid
        });
    }

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdGRPTI_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "报表ID", width: 80, sortable: true, dataIndex: 'GRPT_ID' },
            { header: "所属类型", width: 176, sortable: true, dataIndex: 'GRPTT_NAME' },
            { header: "报表模板", width: 160, sortable: true, dataIndex: 'GRPT_SFILE',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return val;
                }
            },
            { header: "报表备注", width: 120, sortable: true, dataIndex: 'GRPT_SMARK' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e)
            {
                if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0 && LoginPerson.RoleNamesString.indexOf("平台管理角色") < 0) {
                    onEditButtonClick();
                }
            },
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var record = grid.store.getAt(rowIndex);
                var rptid = record.get("GRPT_ID");
                var sfile = record.get("GRPT_SFILE");
                if (columnIndex == 3 && sfile!="") {
                    ExportRptFile(rptid);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st)
            {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0 && LoginPerson.RoleNamesString.indexOf("平台管理角色") < 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================报表管理form===========================end
};
CPS.GRPTI_ManageMain.Initialize();
