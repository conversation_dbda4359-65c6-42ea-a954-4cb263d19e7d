function QueryString(lname){
    var name,
        value,
        i;
    var str = location.href;
    var num = str.indexOf("?")
    str = str.substr(num + 1);
    var arrtmp = str.split("&");
    for(i = 0; i < arrtmp.length; i ++ ){
        num = arrtmp[i].indexOf("=");
        if(num > 0){
            name = arrtmp[i].substring(0, num);
            value = arrtmp[i].substr(num + 1);
            if(name == lname)
                return value;
        }
    }
    return "";
}

var storage = window.sessionStorage;
var jtoken="";
var _sysmark=parent.window._sysmark;
if(storage!=undefined)
{
    jtoken = storage.getItem(_sysmark+"_token");
}

function getResultData(result) {
    if (result.data[0] != undefined) {
        return result.data[0];
    }else {
        return result.data;
    }
}

function getResultMeta(result) {
    if (result.data[0] != undefined) {
        return result.meta[0];
    }else {
        return result.meta;
    }
}

function isJsNullObj(tobj) {
    if (tobj==null||tobj==0||tobj==""||tobj==undefined) {
        return true;
    }else {
        return false;
    }
}

function CloseWindowToReLogin()
{
    var outw = null;
    if(window.top.opener!=null)
    {
        outw = window.top.opener.top;
    } else {
        outw = window.top;
    }

    if(window.top!=outw)
    {
        window.top.close();
        outw.location.href = "../../../Index.htm";
    }else if(window.parent!=undefined)
    {
        window.top.close();
        outw.location.href = "../../../Index.htm";
    }
    else {
        outw.location.href = "../../Index.html";
    }
}

function ShrgShowColse(msg, sec) {
    if (sec == null || sec == undefined) {
        sec = 3;
    }

    _ShowTimeMessageBox("提示",msg,sec);
}

function _ShowTimeMessageBox(title,msg,sec)
{
    if(sec==undefined) sec=5;
    var msgbox=Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.INFO
    });
    setTimeout(function(){
        msgbox.hide();
    }, sec*1000);
}
function _ShowMessageBox(title,msg)
{
    Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.INFO
    });
}
function _ShowWaringBox(title,msg)
{
    Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.WARNING
    });
}
function _ShowErrorBox(title,msg)
{
    Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.ERROR
    });
}

var ShrgShowBox = {
    error: function (str) {
        _ShowWaringBox("警告",str);
    },
    warning: function (str) {// 警告
        _ShowErrorBox("错误",str);
    },
    succeed: function (str) {// 成功
        ShrgShowColse(str, null);
    },
    showColse: function (str, se) {
        ShrgShowColse(str, se);
    },
    resultValidate: function (data,fflag) {
        if (data == null) {
            _ShowMessageBox('提示','返回的数据有异常');
            return false;
        }
        if (data.success=="true"||data.success==true) {
            if(fflag==true) ShrgShowColse("操作成功！", null);
        } else if (data.success == 'false') {
            if (robj.needrl == "true" || robj.needrl == true) {
                if (robj.text != undefined) {
                    ShrgShowColse(data.text);
                } else {
                    ShrgShowColse("您当前不在登录状态，请刷新重新登录后再尝试此操作！");
                }

                if(fflag==true)
                {
                    parent.window.CloseWindowToReLogin();
                }else
                {
                    CloseWindowToReLogin();
                }
            }else
            {
                if (robj.text != undefined) {
                    ShrgShowColse(data.text);
                } else {
                    ShrgShowColse("您当前不在登录状态，请刷新重新登录后再尝试此操作！");
                }
                return false;
            }
        }
        return true;
    }
}
