function CMUserRole(ownerStr) 
{
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var cmGroup = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属公司',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'COMP_ID',
        displayField: 'COMP_NAME',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetGroupList2',
            fields: [
                 'COMP_ID', 'COMP_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var cid = rec.data.COMP_ID;
                cmUser.store.reload({ params: { CompId: cid} });
            }
        }
    });

    var cmUser = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '公司用户',
        typeAhead: true,
        triggerAction: 'all',
        forceSelection: true,
        selectOnFocus: true,
        lazyRender: true,
        lazyInit: false,
        mode: 'local',
        valueField: 'Id',
        displayField: 'RealName',
        allowBlank: false,
        anchor: '98%',
        editable: true,
        enableKeyEvents: true,
        lastQuery: '',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CmmOpt/GetUserList',
            fields: [
                 'Id', 'RealName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'focus': function (e) {
                e.expand();
                this.doQuery(this.allQuery, true);
            },
            'beforequery': function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    var vcount = 0;
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        if (text.indexOf(value) != -1) {
                            vcount = vcount + 1;
                            return true;
                        }
                        return false;
                    });
                    combo.expand();
                    if (vcount > 0) {
                        //Ext.MessageBox.alert("操作提示", "vcount=" + vcount);
                    }
                    return false;
                }
            }
        }
    });

    var cmModule = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetModuleList',
            fields: [
                 'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var cmRoleMark = new Ext.form.TextField({
        xtype: "textfield",
        name: 'RoleMark',
        fieldLabel: "搜索角色",
        allowBlank: true,
        maxLength: 100,
        anchor: '98%'
    });

    var AddUserRight=function(person,role) {
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmUserRole/AddUserRight',
            params: {
                UserId: person,
                RoleId: role
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success ===true||resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var DeleteUserRight=function(person,role) {
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmUserRole/DeleteUserRight',
            params: {
                UserId: person,
                RoleId: role
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success === true || resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var yGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/CmUserRole/GetUserRoleList_Y"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'RoleName','RoleKind']
        })
    });

    var yEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: yGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "角色ID", width: 100, sortable: true, dataIndex: 'Id' },
            { header: "角色名称", width: 100, sortable: true, dataIndex: 'RoleName' },
            { header: "所属模块", width: 100, sortable: true, dataIndex: 'RoleKind' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'RoleName',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "取消权限";
                }
            }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var roleId = grid.store.getAt(rowIndex).get('Id');
                var UserId = cmUser.getValue();
                if (columnIndex == 3) {
                    DeleteUserRight(UserId, roleId);
                }
            }
        }
    });

    var wGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/CmUserRole/GetUserRoleList_W"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'RoleName', 'RoleKind']
        })
    });

    var wEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: wGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "角色ID", width: 100, sortable: true, dataIndex: 'Id' },
            { header: "角色名称", width: 100, sortable: true, dataIndex: 'RoleName' },
            { header: "所属模块", width: 100, sortable: true, dataIndex: 'RoleKind' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'RoleName',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "添加权限";
                }
            }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var roleId = grid.store.getAt(rowIndex).get('Id');
                var UserId = cmUser.getValue();
                if (columnIndex == 3) {
                    AddUserRight(UserId, roleId);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: wGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st)
            {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['UserId'] = cmUser.getValue();
                o['moduleid'] = cmModule.getValue();
                o['RoleMark'] = cmRoleMark.getValue();
                this.store.load({ params: o, callback: function (r, options, success)
                {
                    if (!success) 
                    {
                        Ext.MessageBox.alert('提示', "加载失败.....请稍候再试！");
                    }
                }});
            }
        })
    }); 

    var EditPanel = new Ext.TabPanel({
        activeTab: 0,
        deferredRender: false,
        autoDestroy: true,
        items:
        [
            {
                title: '已配置角色',
                isFixedTab: true,
                layout: 'fit',
                items: [yEditGrid]
            },
            {
                title: '未配置角色',
                isFixedTab: true,
                layout: 'fit',
                items: [wEditGrid]
            }
        ]
    });

    var cmForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 920,
        layout: 'form',
        autoScroll: false,
        items: [
            cmGroup,
            cmUser,
            cmModule,
            cmRoleMark,
            {
                xtype: 'fieldset',
                autoHeight: true,
                layout: 'fit',
                items: [EditPanel]
            }
        ]
     });

    var cmWindow = new Ext.Window({
        width: 920,
        height: 620,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: cmForm,
        title: '通用用户角色管理配置',
        buttonAlign: 'center',
        buttons: [
        {
            text: '查询',
            height: 30,
            handler: function () {
                obj.RefreshData();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                cmWindow.hide();
            }
        }]
    });

    this.ShowWindow = function () {
        cmForm.form.reset();
        cmWindow.show();
        yGridStore.removeAll();
        wGridStore.removeAll();
    }

    this.RefreshData = function () {
        var module = cmModule.getValue();
        var user = cmUser.getValue();
        var rmark = cmRoleMark.getValue();
        yGridStore.reload({ params: { RoleKind: module, UserId: user, RoleMark: rmark }, callback: function () {
            if (yEditGrid.store.getCount() > 0) {
                yEditGrid.getSelectionModel().selectAll();
            }
        } 
        });
        wGridStore.reload({ params: { start: 0, limit: limit, RoleKind: module, UserId: user, RoleMark: rmark} });
    }
}

CMUserRole.prototype = {
    constructor: CMUserRole
}