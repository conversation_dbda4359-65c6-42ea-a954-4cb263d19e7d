function CMOSSTest(ownerStr)
{
    var obj = this;

    this.CMOSSTest_Form = new Ext.FormPanel({
        LabelWidth:100,
        frame: true,
        labelAlign:'right',
        bodyStyle: 'padding:5px 5px 0',
        width: 480,
        height:320,
        layout: 'form',
        autoScroll: true,
        fileUpload: true,
        items: [
            {
                id: ownerStr + '.CMOSSTest.IdDownloadPath',
                xtype: "textfield",
                fieldLabel: '下载路径',
                name: 'DownloadPath',
                anchor: '98%',
            },
            {
                id: ownerStr + '.CMOSSTest.IdFileName',
                xtype: 'fileuploadfield',
                emptyText: '请选择一个文件',
                fieldLabel: '导入文件',
                allowBlaCMOSSTestnk: false,
                name: 'FileName',
                buttonText: '',
                anchor: '98%',
                blankText: '文件不能为空',
                editable: false,
                buttonCfg:
                {
                    iconCls: 'upload-icon'
                }
            }
        ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
            ]
        })
    });

    this.CMOSSTest_Window = new Ext.Window({
        id: ownerStr + '.CMOSSTest_Window',
        width: 500,
        height: 400,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: obj.CMOSSTest_Form,
        title: '通用不需要编译文件上传到指定目录下功能',
        buttonAlign: 'center',
        buttons: [
        {
            text: '执行上传',
            height: 30,
            handler: function () {
                obj.ExecuteUploadTest();
            }
        },
        {
            text: '执行下载',
            height: 30,
            handler: function () {
                obj.ExecuteDownloadTest();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                obj.CMOSSTest_Window.close();
            }
        }]
    });

    this.ExecuteUploadTest = function () {
        if (obj.CMOSSTest_Form.form.isValid()) {
            var formData = new FormData();
            var file = Ext.getCmp(ownerStr + '.CMOSSTest.IdFileName').fileInput.dom.files.item(0);
            formData.append('file', file);

            _ExtUploadFile(
                '../../Service/CpSoft/CMOSSTest/ExecuteUploadTest',
                formData,
                function(data){
                    var json0 = Ext.util.JSON.decode(data);
                    if(json0.text!=undefined)
                    {
                        Ext.MessageBox.alert("操作提示",json0.text);
                    }
                },function () {
                    Ext.MessageBox.alert("操作提示","上传文件失败！");
                }
            );
        }
     }

    this.ExecuteDownloadTest = function () {
        var dpath=Ext.getCmp(ownerStr + '.CMOSSTest.IdDownloadPath').getValue();
        if(dpath==""||dpath==undefined)
        {
            Ext.MessageBox.alert("操作提示","请选择要下载的文件路径！");
            return;
        }

        _ExtDownloadFile2("../../Service/CpSoft/CMOSSTest/ExecuteDownloadTest", {
            kname: dpath
        });
    }

     this.ShowWindow = function () {
         obj.CMOSSTest_Form.form.reset();
         obj.CMOSSTest_Window.show();
     }
}

CMOSSTest.prototype = {
    constructor: CMOSSTest
}