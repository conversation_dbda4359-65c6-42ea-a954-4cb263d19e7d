function CMQRYManage(ownerStr) 
{
    var obj = this;

    this.DefResultData = [['处理结果']];
    this.DefResultCMode = [{ header: "处理结果", width: 720, sortable: false, dataIndex: 'Result'}];
    this.DefResultCMode = new Ext.grid.ColumnModel(obj.DefResultCMode); 
    this.DefResultStore = new Ext.data.SimpleStore({
        fields: ['result'],
        data: obj.DefResultData
    });

    this.ResultGrid = new Ext.grid.EditorGridPanel({
        id: ownerStr + '.CMQry_Form.IDResultGrid',
        height: 310,
        width: 756,
        columns:obj.DefResultCMode,
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        autoScroll: true,
        store: obj.DefResultStore,
        enableColumnMove: false,
        enableHdMenu: false,
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true })
    });

    var modelRecord = Ext.data.Record.create([{ name: 'modelid', type: 'int' }, { name: 'modelname', type: 'string'}]);
    var modelaData = [[0, 'DML语句，仅返回处理结果'], [1, 'DDL语句，返回受影响条目'], [2, 'DCL语句，返回结果集']];
    var modelStore = new Ext.data.SimpleStore({
        fields: ['modelid', 'modelname'],
        data: modelaData
    });


    //查询模式选择框
    var ModelCombo = new Ext.form.ComboBox({
        id: ownerStr + '.CMQry_Form.IdQryMode',
        name: 'QRY_MODEL',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'modelid',
        displayField: 'modelname',
        allowBlank: false,
        editable: false,
        fieldLabel: '查询模式',
        store: modelStore,
        anchor: '98%',
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(ownerStr + '.CMQry_Form.IdQryMode').setValue(rec.data.modelid);
            }
        }
    });

    this.CMQry_Form = new Ext.FormPanel({
        LabelWidth:100,
        frame: true,
        labelAlign:'right',
        bodyStyle: 'padding:5px 5px 0',
        width: 930,
        layout: 'form',
        autoScroll: true,
        items: [
            {   //行七
                id: ownerStr + '.CMQry_Form.IdQrySM',
                xtype: "label",
                name: 'QUERY_SM',
                fieldLabel:'操作说明',
                html: '<span style="color:red">本查询仅支持简单查询（单记录集,前100条），更新语句，dml/ddl等更新语句可以多个以分号分割。</span>',
                anchor: '98%'
            },
            ModelCombo,
            {   //行七
                id: ownerStr + '.CMQry_Form.IdQryEdit',
                xtype: "textarea",
                name: 'QUERY_STR',
                preventScrollbar: false,
                grow: false,
                height: 220,
                fieldLabel: '查询语句',
                anchor: '98%'
            },
            {
                xtype: 'fieldset',
                id: ownerStr + '.CMQry_Form.IdFieldSet',
                fieldLabel: '查询结果',
                collapsible: false,
                height: 340,
                anchor: '98%',
                items: [obj.ResultGrid]
            }
        ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
            ]
        })
    });

    this.CheckValidS = function (strs) {
        var qcstr = strs.toLowerCase();
        if (qcstr.indexOf("drop") >= 0) return false;
        else if (qcstr.indexOf("alter") >= 0) return false;
        else if (qcstr.indexOf("update") >= 0) return false;
        else if (qcstr.indexOf("delete") >= 0) return false;
        return true;
    }

    this.ExecuteSubmit = function () {
        var qrystr = Ext.getCmp(ownerStr + '.CMQry_Form.IdQryEdit').getValue();
        var qrymode = Ext.getCmp(ownerStr + '.CMQry_Form.IdQryMode').getValue();

        if (!obj.CheckValidS(qrystr)) {
            Ext.MessageBox.alert("提示", "数据库更新功能受限！");
            return;
        }
        if (qrystr != "") qrystr = encryptByAES(qrystr,_enccode);

        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmQry/ExecuteQuery',
            params: {
                QueryMode: qrymode,
                QueryStr: qrystr
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "") {
                    Ext.MessageBox.alert("提示", "未知异常，返回空白");
                    return;
                }

                var json = Ext.decode(res.responseText);      //获得后台传递json 
                if (json != undefined) {
                    var tyobj = json.resultType;
                    if (tyobj == "grid") {
                        var fnobj = eval(json.fieldsNames);
                        var rdobj = eval(json.resultData);
                        var cmobj = eval(json.columModle);
                        var colModel = new Ext.grid.ColumnModel(cmobj);

                        for (var i = 0; i < rdobj.length; i++) {
                            rdobj[i] = eval(rdobj[i]);
                        }

                        var store = new Ext.data.SimpleStore(
                        {
                            fields: fnobj,
                            data: rdobj
                        });

                        Ext.getCmp(ownerStr + '.CMQry_Form.IDResultGrid').reconfigure(store, colModel);
                        Ext.getCmp(ownerStr + '.CMQry_Form.IDResultGrid').render();
                    } else {
                        var rdobj = json.resultData;
                        if(rdobj!=undefined)
                        {
                            Ext.MessageBox.alert("提示", rdobj);
                        }else{
                            Ext.MessageBox.alert("提示", "查询过程有错误产生！");
                        }
                    }
                }
            }
        });
    }

    this.ExportExcel=function()
    {
        var qrystr = Ext.getCmp(ownerStr + '.CMQry_Form.IdQryEdit').getValue();
        var qrymode = Ext.getCmp(ownerStr + '.CMQry_Form.IdQryMode').getValue();

        if (!obj.CheckValidS(qrystr)) {
            Ext.MessageBox.alert("提示", "数据库更新功能受限！");
            return;
        }
        if (qrystr != "") qrystr = encryptByAES(qrystr,_enccode);

        if (parseInt(qrymode) !== 2)
        {
            Ext.MessageBox.alert("提示", "非结果集查询不支持导出功能！");
            return;
        }
        _ExtDownloadFile("../../Service/CpSoft/CmQry/ExportExcel", {
            QueryMode: qrymode,
            QueryStr: qrystr
        });
    }
    

    this.InitView = function () {
        obj.CMQry_Form.form.reset();
        var firstValue = Ext.getCmp(ownerStr + '.CMQry_Form.IdQryMode').store.data.items[2].data.modelid;
        Ext.getCmp(ownerStr + '.CMQry_Form.IdQryMode').setValue(firstValue);
        ModelCombo.disable();
        Ext.getCmp(ownerStr + '.CMQry_Form.IDResultGrid').reconfigure(obj.DefResultStore, obj.DefResultCMode);
    }
 
}

CMQRYManage.prototype = {
    constructor: CMQRYManage
}