DownloadJS('../../Page/NoticeManage/LookNoticeManageMain.js');
document.getElementById('../../Page/NoticeManage/LookNoticeManageQueue.js').Destroy = function()
{
    var element = document.getElementById("../../Page/NoticeManage/LookNoticeManageMain.js");element.parentNode.removeChild(element);
    Ext.getCmp("NoticeManage.LookNoticeManageMain.IdYJFormWindow").destroy();}
    document.getElementById('../../Page/NoticeManage/LookNoticeManageQueue.js').DataReload = function(){
    Ext.getCmp("NoticeManage.LookNoticeManageMain.IdYJGrid").store.load({ params: { start: 0, limit: Ext.getCmp("NoticeManage.LookNoticeManageMain.IdYJGrid").getBottomToolbar().pageSize} });
}

