<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>任务清单</title>
    <script type="text/javascript" src="../../JSControl/JQuery/jquery-3.6.0.min.js"></script>
    <script type="text/javascript">var $j = $</script>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/ExtResources/css/ext-all.css" />
    <script type="text/javascript" src="../../JSControl/JQuery/jquery-migrate-1.4.1.min.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ext-base.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ext-all.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ext-lang-zh_CN.js"></script>
    <style type="text/css">
        .gray-bg {
            background-color: #f3f3f4;
            font-family:"open sans","Helvetica Neue",Helvetica,Arial,sans-serif;
            font-size: 13px;
         }
         .navul-title-cont {
             display: inline-block; 
             text-align: center;
             width:360px;
         }
         .navul-title {
             display: inline-block;
             background-color: #1ab394;
             border-radius: 2.5px;
             border:0px solid #1ab394;
             padding: 5px;
             width: 240px; 
             font-size: 16px;
             color: white;
         }
         .navul {
            color: white;
            display:block;
            font-family:"open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size:12px;
            line-height:18px;
            list-style-image:none;
            list-style-position:outside;
            list-style-type: disc;
            padding: 5px;
            margin: 15px;
            border: 0px solid red;
        }
        
        .navul li {
            height:32px;
            line-height:24px;
            padding: 5px;
            border-bottom: 1px solid #d3d3d3;
            list-style: none;
        }
        
        .navul li a{
            font-family:"open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size:13px;
            border: 0px solid red;
            color: #666;
        }
          
        .navul li a .leftspan{
            list-style-image:none;
            list-style-position:outside;
            list-style-type: disc;
            border: 1px solid #1ab394;
            display: inline-block;
            width: 6px;
            height: 6px;
            margin-right: 10px;
            color: #000;
        }
        
        .navul li a .rightspan{
            background-color: #1ab394;
            border-radius: 2.5px;
            display: block;
            float: right;
            padding: 3px 8px;
            text-align: center;
            text-shadow: none;
            vertical-align: baseline;
            white-space: nowrap;
            width: 36px;
            border: 0px solid red;
            color: #fff;
        }
        
        .list-header {
            height: 50px;
            width:100%;
        }
        
        .input-group-cont {
            height: 50px;
            width: 100%;
            display:block;
            text-align: right;
            vertical-align:bottom;
            border-bottom: 1px solid #d3d3d3;
            margin-right: 40px;
            margin-bottom: 10px;
        }
        
        .search-left{
            height: 30px;
            width: 300px;
            display:inline-block;
            border: 0px solid red;
            font-size: 18px;
            float: left;
            top: 10px;
            text-align: left;
        }
        
        .LcjdmcTitle{
            font-size: 22px;
            font-weight: bold;
        }
                
        .search-right{
            height: 40px;
            width: 400px;
            display:inline-block;
            border: 0px solid red;
        }
        
        .searchbox {
            height: 26px;
            border: 1px solid #d3d3d3;
            border-radius: 2.5px;
            width: 300px; 
        }
        
        .searchbutton {
            height: 30px;
            border-radius: 2.5px;
            width: 60px;
            background-color: #1ab394;
            border: 0px solid #1ab394;
            color: #fff;
        }
        
        .list-element-cont {
            height: 100%;
            width:100%;      
            border: 0px solid #d3d3d3;
        }
       
        .list-element-ibox {
            height: 100%;
            width:100%;     
            border: 0px solid #d3d3d3;       
        }
        
       .list-element-cont {
            height: 100%;
            width:100%;     
            border: 0px solid #d3d3d3;       
        }
        
       .list-element-ul {
            color: rgb(103, 106, 108);
            display:block;
            font-family:"open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size:13px;
            line-height:18px;
            list-style: none;
            list-style-image:none;
            list-style-position:outside;
            list-style-type: disc;
            padding: 5px;
            border: 0px solid red;
        }
        
        .list-element {
            height:50px;
            line-height:24px;
            padding: 5px;
            border: 1px solid #d3d3d3;
            border-left: 10px solid #1ab394;
            list-style: none;
            font-size:13px;
            margin-top: 8px;
            padding-left: 20px; 
        }
        
        .list-detail {
           display:block;
           font-size: 13px;
           width: 100%;
        }
        
        .list-days {
            font-size:13px;   
        }
        
        .list-handle {
            float: right;
            background-color: #1ab394;
            border: 0px solid #1ab394;
            border-radius: 2.5px;
            color: #fff;
            width: 50px;
            font-size: 12px;
            text-align: center;
        }
        
        .list-nav-btn {
            float: left;
            background-color: #1ab394;
            border: 0px solid #1ab394;
            border-radius: 2.5px;
            color: #fff;
            width: 24px;
            height: 24px;
            font-size: 12px;
            text-align: center;
            margin: 2px;            
        }
    </style>
    <script type="text/javascript">
        var _token="";
        var _sysmark="";

        function QueryString(lname){
            var name,
                value,
                i;
            var str = location.href;
            var num = str.indexOf("?")
            str = str.substr(num + 1);
            var arrtmp = str.split("&");
            for(i = 0; i < arrtmp.length; i ++ ){
                num = arrtmp[i].indexOf("=");
                if(num > 0){
                    name = arrtmp[i].substring(0, num);
                    value = arrtmp[i].substr(num + 1);
                    if(name == lname)
                        return value;
                }
            }
            return "";
        }

        Ext.onReady(function () {
            _sysmark=QueryString("sm");

            var storage = window.sessionStorage;
            if(storage!=undefined)
            {
                _token = storage.getItem(_sysmark+"_token");
                if(_token!=undefined)
                {
                    Ext.Ajax.defaultHeaders={
                        Authorization: _token
                    };
                }else {
                    Ext.MessageBox.alert("提示","系统异常，授权信息缺失！");
                }

                StateViewTab();
            }else {
                Ext.MessageBox.alert("提示","系统异常，授权信息缺失！");
            }
        });
    </script>
</head>
<body class="gray-bg" style="overflow:scroll;" onload="TimerTask()"> 
     <table cellspacing="0" cellpadding="0" style="width:100%; height:100%;">
        <tr>
            <td style="width:360px; padding: 20px; background-color: #F4F3F3; vertical-align: top;">
                <span class="navul-title-cont">
                    <span class="navul-title">待办事项</span>
                </span>
                <div style="display: inline-block; height: 10px;"></div>
                <div>
                    <ul class="navul" style="padding: 0;">
                        <span id="LabelWorkHZ"></span>
                    </ul>
                </div>
            </td>
            <td style="width:100%;padding: 20px; background-color: white;">
                <div class="list-header">
                    <div class="input-group-cont">
                        <div class="search-left">
                            <span id="LabelLcjdmc" class="LcjdmcTitle"></span>
                        </div>
                        <div class="search-right">
                            <input type="text" id="selectTJ" class="searchbox" name="selectTJ"/>
                            <input type="button" class="searchbutton" value="搜索" onclick="LcManageGetNext()"/>
                        </div>
                    </div>
                </div>
                <div class="list-element-div">
                    <div class="list-element-ibox">
                        <div class="list-element-cont">
                            <ul class="list-element-ul">
                                <span id="LabelList"></span>
                            </ul>
                        </div>
                
                        <div class="list-nav-btn-cont">
                            <span id="LabelButton"></span>
                        </div>
                    </div> 
                </div> 
            </td>
        </tr> 
    </table>
     <script type="text/javascript" src="nfIndex.js"></script>
</body>
</html>
