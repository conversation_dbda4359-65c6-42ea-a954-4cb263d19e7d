//用于全局控制整个框架，包括主界面的设计，运行方式
var GroupType = 0;
var SetLcJD = "";
//定时器
function TimerTask() {
    setInterval("HomeRefresh();", 3600000);
}

//首页刷新
function HomeRefresh() {
    //alert("5秒一次");
    //首页数据获取
    LcManageGet();
}


//首页数据获取
function LcManageGet() {
    Ext.Ajax.request(
        {
            url: "../../Service/Schdeule/SchdeuleMan/GetNowSchdeuleManage",
            success: function (response, options) {
                var responseArray = Ext.util.JSON.decode(response.responseText);
                if (responseArray.success == true) {
                    var data = responseArray.data;
                    if (data != undefined&&data[0]!=undefined) {
                        document.getElementById("LabelWorkHZ").innerHTML = data[0].hzContent;
                        document.getElementById("LabelList").innerHTML = data[0].schdeuleContent;
                        document.getElementById("LabelLcjdmc").innerHTML = data[0].titleContent;
                        document.getElementById("LabelButton").innerHTML = data[0].sgContent;
                    }
                }
            },
            failure: function (response, options) {

            },
            params: { setLcJD: SetLcJD, selectTJ: document.getElementById("selectTJ").value}
        }
    );
}


//首页数据获取
function LcManageGetNext(num, lcjdid) {
    Ext.Ajax.request(
        {
            url: "../../Service/Schdeule/SchdeuleMan/GetNowSchdeuleManageNext",
            success: function (response, options) {
                var responseArray = Ext.util.JSON.decode(response.responseText);
                if (responseArray.success == true) {
                    var data = responseArray.data;
                    if (data != undefined&&data[0]!=undefined) {
                        // document.getElementById("loadering").style.display = "none";
                        document.getElementById("LabelList").innerHTML = data[0].schdeuleContent;
                        document.getElementById("LabelLcjdmc").innerHTML = data[0].titleContent;
                        document.getElementById("LabelButton").innerHTML = data[0].sgContent;
                    }
                }
            },
            failure: function (response, options) {

            },
            params: { num: num, setLcJD: lcjdid, selectTJ: document.getElementById("selectTJ").value }
        }
    );
}

var StateViewTab = function() {
    //判断用户是否还处于登录状态
    IsPersonHaveLogined = function() {
        //return;
        Ext.Ajax.request({
            url: '../../Service/Base/User/GetCurrentPerson',
            success: function(response, options) {
                var responseArray = Ext.util.JSON.decode(response.responseText);
                if (responseArray.success == true) {
                    LcManageGet();
                }
                else {
                    Ext.MessageBox.alert('提示', '对不起，您当前登录时间超时，系统将自动重启！', function() {
                        window.parent.location.href = "../../index";
                    });
                }
            },
            failure: function (response, options) {
                //Ext.MessageBox.alert('提示', '对不起，您当前登录时间超时，系统将自动重启！', function() { window.location.href = "login.aspx"; });
            }
        });

        window.setTimeout("IsPersonHaveLogined()", 100000000);
    };
    IsPersonHaveLogined();
}

////////////////////////////////////功能按钮
//全局变量
var YJParentId = 0;
var YJTRUE = true;

//初始数据
var start = 0;
var limit = 50;
var pageSize = limit;

var dtime = new Date();
var LoginPerson;
//用于选择发送用户
//文件类别表单定义

//zz，2018年5月28日，用于显示功能模块
function onZZShowButtonClick(mA, mB, mC, mD) {
    clikcToLc = 1;
    this.parent.NavFunctionToDo(mA, mB, mC, mD);
}
