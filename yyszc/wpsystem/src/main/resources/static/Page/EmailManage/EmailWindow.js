IsMultiLevel = 0;
var T_permisson = "";
var csPerson = "";
var Stype = "";
var SdetailID = 0;
var SID = 0;
var csPerson = "";
var RpersonPerson = "";
var start = 0;
var limit = 25;
var pageSize = limit;
//组织单位选择弹出窗口定义
EmailSelectWindow = function() {

    var CarbonCopySelect = new Ext.form.ComboBox({
        id: 'EmailSelectWindow_CsPerson',
        width: 900,
        queryMode: 'local',
        queryParam: 'RealName',
        valueField: 'RealName',
        displayField: 'RealName',
        editable: true,
        minChars: 1,
        triggerAction: 'all',
        name: 'RealName',
        hideTrigger: false,
        fieldLabel: '抄送',
        store: new Ext.data.JsonStore({
            method: 'post',
            autoLoad: false,   //列表自动加载
            url: _netsvrurl+'Service/SystemManage/WorkflowService.ashx?Method=GetCSPerson',
            fields: [
                     'RealName'
                     ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            change: function() {
                if (this.value == "") {
                    csPerson = "";
                }
                else {
                    if (csPerson.indexOf(this.value) > -1) {
                        csPerson = this.value;
                    }
                    else {
                        if (csPerson == "") {
                            csPerson = this.value + ";";
                        }
                        else {

                            csPerson += this.value + ";";
                        }
                    }
                }
                Ext.getCmp("EmailSelectWindow_CsPerson").setValue(csPerson);
            }
        }
    });

    CarbonCopySelect.store.on('beforeload', function(store, options) {


        csPerson = Ext.getCmp("EmailSelectWindow_CsPerson").getValue();
        //extjs3.0的方法
        var baseParams = {};
        Ext.apply(options.params, baseParams);

    });

    var RpersonSelect = new Ext.form.ComboBox({
        id: 'EmailSelectWindow_Rperson',
        width: 900,
        queryMode: 'local',
        queryParam: 'RealName',
        valueField: 'RealName',
        displayField: 'RealName',
        editable: true,
        minChars: 1,
        triggerAction: 'all',
        name: 'RealName',
        hideTrigger: false,
        fieldLabel: '收件人',
        store: new Ext.data.JsonStore({
            method: 'post',
            autoLoad: false,   //列表自动加载
            url: _netsvrurl+'Service/SystemManage/WorkflowService.ashx?Method=GetCSPerson',
            fields: [
                     'RealName'
                     ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            change: function() {
                if (this.value == "") {
                    RpersonPerson = "";
                }
                else {
                    if (RpersonPerson.indexOf(this.value) > -1) {
                        RpersonPerson = this.value;
                    }
                    else {
                        if (RpersonPerson == "") {
                            RpersonPerson = this.value + ";";
                        }
                        else {

                            RpersonPerson += this.value + ";";
                        }
                    }
                }
                Ext.getCmp("EmailSelectWindow_Rperson").setValue(RpersonPerson);
            }
        }
    });

    RpersonSelect.store.on('beforeload', function(store, options) {


        RpersonPerson = Ext.getCmp("EmailSelectWindow_Rperson").getValue();
        //extjs3.0的方法
        var baseParams = {};
        Ext.apply(options.params, baseParams);

    });
    RpersonSelect.store.on('afteredit', function(store, options) {
        RpersonPerson = Ext.getCmp("EmailSelectWindow_Rperson").getValue();

    });

    var LXDForm = new Ext.FormPanel({
        id: 'EmailSelectWindow.LXDForm',
        labelWidth: 55,
        width: 1000,
        frame: true,
        fileUpload: true,
        bodyStyle: 'padding:5px 5px 0',
        items: [
    RpersonSelect,
         CarbonCopySelect,
         {
             id: 'LXDForm_Title',
             xtype: 'textfield',
             vtype: 'customtextfield',
             fieldLabel: '标题',
             name: 'Title',
             allowBlank: false,
             anchor: '98%'
         },
          {
              id: 'LXDForm_YWUrl',
              xtype: 'textfield',
              vtype: 'customtextfield',
              fieldLabel: '内容采用超链接',
              name: 'YWUrl',
              allowBlank: false,
              anchor: '98%'
          },
         {
             id: 'LXDForm_Cnt',
             xtype: 'htmleditor',
             vtype: 'customtextfield',
             fieldLabel: '内容',
             name: 'Content',
             allowBlank: false,
             height: 400,
             anchor: '98%'
         }
  ], buttons: [{
      text: '发送',
      handler: function() {
          var CsPerson = Ext.getCmp("EmailSelectWindow_CsPerson").getValue();
          var Rperson = Ext.getCmp("EmailSelectWindow_Rperson").getValue();

          var Title = Ext.getCmp("LXDForm_Title").getValue();
          var Cnt = Ext.getCmp("LXDForm_Cnt").getValue();
          var YWUrl = Ext.getCmp("LXDForm_YWUrl").getValue();
          if (Rperson == "" || Rperson == null) {
              Ext.Msg.alert('提示', "请选择收件人！");
              return false;
          }

          if (Title == "" || Title == null) {
              Ext.Msg.alert('提示', "标题不能为空！");
              return false;
          }

          Ext.Ajax.request({
              //                async: false,
              url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=AddEmail',
              params: {
                  CsPerson: CsPerson,
                  Rperson: Rperson,
                  Title: Title,
                  Cnt: Cnt,
                  YWUrl: YWUrl
              },
              waitTitle: "请稍候",
              waitMsg: '正在保存数据...',
              success: function(response, options) {
                  Ext.Msg.alert('提示', "发送成功！");
                  Ext.getCmp("EmailSelectWindow_CsPerson").setValue("");
                  Ext.getCmp("EmailSelectWindow_Rperson").setValue("");
                  Ext.getCmp("LXDForm_Title").setValue("");
                  Ext.getCmp("LXDForm_Cnt").setValue("");
                  Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                  Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                  Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();
              },
              failure: function(form, action) {

              }
          });

      }
  }, {
      text: '清空',
      handler: function() {
          Ext.getCmp("EmailSelectWindow_CsPerson").setValue("");
          Ext.getCmp("EmailSelectWindow_Rperson").setValue("");
          Ext.getCmp("LXDForm_Title").setValue("");
          Ext.getCmp("LXDForm_Cnt").setValue("");
      }
}],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
  { name: 'Rperson', mapping: 'Rperson', type: 'string' },
            { name: 'CsPerson', mapping: 'CsPerson', type: 'string' },
            { name: 'Title', mapping: 'Title', type: 'string' },
            { name: 'YWUrl', mapping: 'YWUrl', type: 'string' },
            { name: 'HisContent', mapping: 'HisContent', type: 'string'}]
        })
    });



    var LookLXDForm = new Ext.FormPanel({
        id: 'EmailSelectWindow.LookLXDForm',
        labelWidth: 55,
        width: 1000,
        frame: true,
        fileUpload: true,
        bodyStyle: 'padding:5px 5px 0',
        items: [
   {
       id: 'LookLXDForm_Rperson',
       xtype: 'textfield',
       vtype: 'customtextfield',
       fieldLabel: '收件人',
       name: 'Rperson',
       //       allowBlank: false,
       anchor: '98%'
   }, {
       id: 'LookLXDForm_Csperson',
       xtype: 'textfield',
       vtype: 'customtextfield',
       fieldLabel: '抄送',
       name: 'CsPerson',
       //       allowBlank: false,
       anchor: '98%'
   },
         {
             id: 'LookLXDForm_Title',
             xtype: 'textfield',
             vtype: 'customtextfield',
             fieldLabel: '标题',
             name: 'Title',
             allowBlank: false,
             readOnly: true,
             anchor: '98%'
         },
         {
             id: 'LookLXDForm_Cnt',
             xtype: 'htmleditor',
             vtype: 'customtextfield',
             fieldLabel: '内容',
             name: 'HisContent',
             allowBlank: false,
             height: 400,
             anchor: '98%'
         }
  ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
  { name: 'Rperson', mapping: 'Rperson', type: 'string' },
            { name: 'CsPerson', mapping: 'CsPerson', type: 'string' },
            { name: 'Title', mapping: 'Title', type: 'string' },
            { name: 'HisContent', mapping: 'HisContent', type: 'string'}]
        })
    });



    LookLXDFormWindow = new Ext.Window({
        id: 'EmailSelectWindow_LookLXDFormWindow',
        width: 1000,
        height: 610,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: true,
        items: LookLXDForm,
        title: '联系单',
        buttons: [{
            text: '回复',
            handler: function() {

                var Cnt = Ext.getCmp("LookLXDForm_Cnt").getValue();
                Ext.Ajax.request({
                    //                async: false,
                    url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=BackEmail',
                    params: {
                        Cnt: Cnt,
                        Stype: Stype,
                        SID: SID,
                        SDetailID: SdetailID
                    },
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function(response, options) {
                        Ext.Msg.alert('提示', "回复成功！");
                        LookLXDFormWindow.hide();
                        Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                        Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                        Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();

                        Ext.Ajax.request({
                            //        async: false,
                            url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetMsgNumByPerson',
                            success: function(response, options) {
                                var responseArray = Ext.util.JSON.decode(response.responseText).text;
                                Ext.getCmp("LXDTabPanel_fjx").setTitle("发件箱（" + responseArray.split('|')[1] + "）")
                                Ext.getCmp("LXDTabPanel_sjx").setTitle("收件箱（" + responseArray.split('|')[0] + "）")
                                Ext.getCmp("LXDTabPanel_xjx").setTitle("星件箱（" + responseArray.split('|')[2] + "）")

                            },
                            failure: function(form, action) {

                            }
                        });
                    },
                    failure: function(form, action) {

                    }
                });
            }
        },
                  {
                      text: '关闭',
                      handler: function() {
                          LookLXDFormWindow.hide();
                      }
                  }
          ]
    });


    var EmailPanel = new Ext.Panel({
        id: 'LookEmailSelectWindow.EmailPanel',
        collapsible: false,
        margins: '3 3 3 3',
        html: '<iframe id="Lookemail" name="Lookemail" src='+_netsvrurl+'JS/zdg/running.html?address=" width="100%" height="98%" frameborder=0 scrolling=auto></iframe>'
    });


    LookEmailPanelWindow = new Ext.Window({
        id: 'LookEmailPanelWindow',
        width: 1000,
        height: 620,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: true,
        items: EmailPanel,
        title: '通知公告',
        buttons: [
                  {
                      text: '关闭',
                      handler: function() {
                          LookEmailPanelWindow.hide();
                      }
                  }
          ]
    });

    function setLookLXD() {
        if (SLXDMgrid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {

            var ID = SLXDMgrid.getSelectionModel().getSelected().data.ID;
            var YWUrl = SLXDMgrid.getSelectionModel().getSelected().data.YWUrl;
            SID = ID;
            Stype = "2";
            if (YWUrl == "") {
                LookLXDForm.form.reset();
                var url = _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetEmailById&ID=' + ID + '&Type=' + 2 + '&DetailID=' + 0;
                LookLXDFormWindow.show();
                LookLXDForm.load({
                    url: url,
                    waitTitle: "请稍候",
                    waitMsg: '正在读取数据...',
                    success: function(form, action) {

                        Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                        Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                        Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();

                        Ext.Ajax.request({
                            //        async: false,
                            url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetMsgNumByPerson',
                            success: function(response, options) {
                                var responseArray = Ext.util.JSON.decode(response.responseText).text;
                                Ext.getCmp("LXDTabPanel_fjx").setTitle("发件箱（" + responseArray.split('|')[1] + "）")
                                Ext.getCmp("LXDTabPanel_sjx").setTitle("收件箱（" + responseArray.split('|')[0] + "）")
                                Ext.getCmp("LXDTabPanel_xjx").setTitle("星件箱（" + responseArray.split('|')[2] + "）")

                            },
                            failure: function(form, action) {

                            }
                        });
                    },
                    failure: function(form, action) {
                    }
                });
            }
            else {/// <reference path="email1218.html" />

                sgid = _netsvrurl+"EmailManage/" + YWUrl;
                LookEmailPanelWindow.show();
                document.getElementById("Lookemail").src = sgid;
            }
        }
    }

    function setLookFLXD() {
        if (FLXDMgrid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var ID = FLXDMgrid.getSelectionModel().getSelected().data.EmailID;
            var DetailID = FLXDMgrid.getSelectionModel().getSelected().data.ID;

            SID = ID;
            Stype = "0";
            SdetailID = DetailID;
            LookLXDForm.form.reset();
            var url = _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetEmailById&ID=' + ID + '&Type=' + 0 + '&DetailID=' + DetailID;
            LookLXDFormWindow.show();
            LookLXDForm.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在读取数据...',
                success: function(form, action) {

                    Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                    Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                    Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();

                    Ext.Ajax.request({
                        //        async: false,
                        url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetMsgNumByPerson',
                        success: function(response, options) {
                            var responseArray = Ext.util.JSON.decode(response.responseText).text;
                            Ext.getCmp("LXDTabPanel_fjx").setTitle("发件箱（" + responseArray.split('|')[1] + "）")
                            Ext.getCmp("LXDTabPanel_sjx").setTitle("收件箱（" + responseArray.split('|')[0] + "）")
                            Ext.getCmp("LXDTabPanel_xjx").setTitle("星件箱（" + responseArray.split('|')[2] + "）")

                        },
                        failure: function(form, action) {

                        }
                    });
                },
                failure: function(form, action) {
                }
            });
        }
    }


    function setLookStar() {
        if (Stargrid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var ID = Stargrid.getSelectionModel().getSelected().data.EmailID;
            var DetailID = Stargrid.getSelectionModel().getSelected().data.ID;

            SID = ID;
            Stype = "0";
            SdetailID = DetailID;
            LookLXDForm.form.reset();
            var url = _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetEmailById&ID=' + ID + '&Type=' + 0 + '&DetailID=' + DetailID;
            LookLXDFormWindow.show();
            LookLXDForm.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在读取数据...',
                success: function(form, action) {

                    Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                    Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                    Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();

                    Ext.Ajax.request({
                        //        async: false,
                        url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetMsgNumByPerson',
                        success: function(response, options) {
                            var responseArray = Ext.util.JSON.decode(response.responseText).text;
                            Ext.getCmp("LXDTabPanel_fjx").setTitle("发件箱（" + responseArray.split('|')[1] + "）")
                            Ext.getCmp("LXDTabPanel_sjx").setTitle("收件箱（" + responseArray.split('|')[0] + "）")
                            Ext.getCmp("LXDTabPanel_xjx").setTitle("星件箱（" + responseArray.split('|')[2] + "）")

                        },
                        failure: function(form, action) {

                        }
                    });
                },
                failure: function(form, action) {
                }
            });
        }
    }
    //设置Grid列表
    //设置数据源参数 

    var FSGridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: _netsvrurl+"Service/EmailManage/EmailManageService.ashx?Method=GetEmailBySpesonList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'Rperson', 'Title', 'Content', 'Sdate', 'Xsjhdh', 'Scjhdh', 'Xmmc', 'Zxmc', 'State', 'YWUrl']
        })
    });

    var SJGridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: _netsvrurl+"Service/EmailManage/EmailManageService.ashx?Method=GetEmailByRpesonList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'Sperson', 'Title', 'Content', 'Sdate', 'Xsjhdh', 'Scjhdh', 'Xmmc', 'Zxmc', 'State', 'StarLevel', 'EmailID', 'YWUrl']
        })
    });
    var StarGridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: _netsvrurl+"Service/EmailManage/EmailManageService.ashx?Method=GetEmailByStarRpesonList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'Sperson', 'Title', 'Content', 'Sdate', 'Xsjhdh', 'Scjhdh', 'Xmmc', 'Zxmc', 'State', 'StarLevel', 'EmailID']
        })
    });
    //联系单

    var record_start = 0;
    var SLXDMgrid = new Ext.grid.GridPanel({
        id: 'EmailSelectWindow_SLXDMgrid',
        height: 527,
        store: FSGridStore,
        columns: [new Ext.grid.RowNumberer({ renderer: function(v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
        { header: "", width: 50, sortable: false, dataIndex: 'State', renderer: function(value) { if (value == "新回复") { return "<img src=_netsvrurl+'Image/Button/email.png'>"; } else { return "<img src=_netsvrurl+'Image/Button/email_open_image.png'>"; } } },
           { header: "收件人", width: 180, sortable: false, dataIndex: 'Rperson' },
           { header: "主题", width: 240, sortable: false, dataIndex: 'Title' },
           { header: "时间", width: 120, sortable: false, dataIndex: 'Sdate', renderer: Ext.util.Format.dateRenderer('Y-m-d') }

     ], loadMask: { msg: "加载中..." },
        stripeRows: true,
        //        title: '列表',
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true }),
        //        tbar: toolbar,

        listeners:
    {
        "rowdblclick": function(grid, rowIndex, e) {
            setLookLXD();
        }
    },

        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: FSGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function(start) {
                record_start = start;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                this.store.load({ params: o });
            }
        })
    });
    var FLXDMtoolbar = new Ext.Toolbar({
        items: [
     {
         text: '标星',
         iconCls: 'ButtonFormAdd'
         ,
         handler: onStarButtonClick
     }

    ]
    });

    var Startoolbar = new Ext.Toolbar({
        items: [
     {
         text: '取消标星',
         iconCls: 'ButtonFormAdd'
         ,
         handler: onQXStarButtonClick
     }

    ]
    });
    function onStarButtonClick() {
        if (FLXDMgrid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var ID = FLXDMgrid.getSelectionModel().getSelected().data.EmailID;
            var DetailID = FLXDMgrid.getSelectionModel().getSelected().data.ID;

            var tip = "确实要标星所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function(btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                            {
                                url: _netsvrurl+"Service/EmailManage/EmailManageService.ashx?Method=StarEmail",
                                successfn: function(data) {
                                    Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                                    Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                                    Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();
                                },
                                failurefn: function(data) {
                                },
                                params: { ID: DetailID }
                            }
                );
                }
            });
        }
    }
    function onQXStarButtonClick() {
        if (Stargrid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var ID = Stargrid.getSelectionModel().getSelected().data.EmailID;
            var DetailID = Stargrid.getSelectionModel().getSelected().data.ID;

            var tip = "确实要标星所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function(btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                            {
                                url: _netsvrurl+"Service/EmailManage/EmailManageService.ashx?Method=QXStarEmail",
                                successfn: function(data) {
                                    Ext.getCmp("EmailSelectWindow_SLXDMgrid").store.reload();
                                    Ext.getCmp("EmailSelectWindow_FLXDMgrid").store.reload();
                                    Ext.getCmp("EmailSelectWindow_Stargrid").store.reload();
                                },
                                failurefn: function(data) {
                                },
                                params: { ID: DetailID }
                            }
                );
                }
            });
        }
    }

    var record_start = 0;
    var FLXDMgrid = new Ext.grid.GridPanel({
        id: 'EmailSelectWindow_FLXDMgrid',

        height: 527,
        store: SJGridStore,
        columns: [new Ext.grid.RowNumberer({ renderer: function(v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
      { header: "", width: 50, sortable: false, dataIndex: 'State', renderer: function(value) {

          if (value == "未查看" || value == "新回复") {
              return "<img src='"+_netsvrurl+"Image/Button/email.png'>";
          }
          else {
              return "<img src='"+_netsvrurl+"Image/Button/email_open_image.png'>";
          }
      }
      },
           { header: "发件人", width: 120, sortable: false, dataIndex: 'Sperson' },
           { header: "主题", width: 250, sortable: false, dataIndex: 'Title' },
           { header: "时间", width: 120, sortable: false, dataIndex: 'Sdate', renderer: Ext.util.Format.dateRenderer('Y-m-d') },
           { header: "星级", width: 50, sortable: false, dataIndex: 'StarLevel', renderer: function(value) {

               if (value == "1") {
                   return "<img src='"+_netsvrurl+"Image/Button/star.jpg'>";
               }
               else {
                   return "";
               }
           }
           }

     ], loadMask: { msg: "加载中..." },
        stripeRows: true,
        //        title: '列表',
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true }),
        tbar: FLXDMtoolbar,

        listeners:
    {
        "rowdblclick": function(grid, rowIndex, e) {
            setLookFLXD();
        }
    },

        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: SJGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function(start) {
                record_start = start;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                this.store.load({ params: o });
            }
        })
    });

    var record_start = 0;
    var Stargrid = new Ext.grid.GridPanel({
        id: 'EmailSelectWindow_Stargrid',
        height: 527,
        store: StarGridStore,
        columns: [new Ext.grid.RowNumberer({ renderer: function(v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
      { header: "", width: 50, sortable: false, dataIndex: 'State', renderer: function(value) {

          if (value == "未查看" || value == "新回复") {
              return "<img src='"+_netsvrurl+"Image/Button/email.png'>";
          }
          else {
              return "<img src='"+_netsvrurl+"Image/Button/email_open_image.png'>";
          }
      }
      },
           { header: "发件人", width: 120, sortable: false, dataIndex: 'Sperson' },
           { header: "主题", width: 250, sortable: false, dataIndex: 'Title' },
           { header: "时间", width: 120, sortable: false, dataIndex: 'Sdate', renderer: Ext.util.Format.dateRenderer('Y-m-d') },
           { header: "星级", width: 50, sortable: false, dataIndex: 'StarLevel', renderer: function(value) {

               if (value == "1") {
                   return "<img src='"+_netsvrurl+"Image/Button/star.jpg'>";
               }
               else {
                   return "";
               }
           }
           }

     ], loadMask: { msg: "加载中..." },
        stripeRows: true,
        //        title: '列表',
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true }),
        tbar: Startoolbar,

        listeners:
    {
        "rowdblclick": function(grid, rowIndex, e) {
            setLookStar();
        }
    },

        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: StarGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function(start) {
                record_start = start;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                this.store.load({ params: o });
            }
        })
    });
    EmailSelectWindow.superclass.constructor.call(this, {
        // id: 'ExtEmailSelectWindow',
        width: 1000,
        height: 600,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: true,
        items: {
            xtype: "tabpanel",
            id: "EmailSelectWindow.Tabpanel",
            activeTab: 1,
            //            deferredRender: false, 
            items: [{
                title: '联系单',
                id: 'LXDTabPanel_lxd',
                iconCls: 'ButtonFileEmailEdit',
                items: [LXDForm]
            },
				{
				    title: '发件箱',
				    id: 'LXDTabPanel_fjx',
				    iconCls: 'ButtonFileEmailGo',
				    items: [SLXDMgrid]
				},
				{
				    title: '收件箱',
				    id: 'LXDTabPanel_sjx',
				    iconCls: 'ButtonFileEmailOpen',
				    items: [FLXDMgrid]
				},
				{
				    title: '星件箱',
				    id: 'LXDTabPanel_xjx',
				    iconCls: 'ButtonFileEmailStar',
				    items: [Stargrid]
}]
        }
        //    ,
        //        //title: '组织单位选择',
        //        buttons: [ {
        //            text: '关闭',
        //            handler: function() {
        //                this.ownerCt.ownerCt.hide();
        //                //this.ownerCt.hide();
        //            }
        //}]
    });

};



    //扩展方法
Ext.extend(EmailSelectWindow, Ext.Window, {
    //显示Window
    Show: function() {


    var grid = this.items.items[0].items.items[1].items.items[0];
    var grid1 = this.items.items[0].items.items[2].items.items[0];
    var grid2 = this.items.items[0].items.items[3].items.items[0];
    grid.store.reload({ params: { start: 0, limit: 25} });
    grid1.store.reload({ params: { start: 0, limit: 25} });
    grid2.store.reload({ params: { start: 0, limit: 25} });


    this.show();
    Ext.Ajax.request({
//        async: false,
        url: _netsvrurl+'Service/EmailManage/EmailManageService.ashx?Method=GetMsgNumByPerson',
        success: function(response, options) {
            var responseArray = Ext.util.JSON.decode(response.responseText).text;
            Ext.getCmp("LXDTabPanel_fjx").setTitle("发件箱（"+responseArray.split('|')[1]+"）")
            Ext.getCmp("LXDTabPanel_sjx").setTitle("收件箱（" + responseArray.split('|')[0] + "）")
            Ext.getCmp("LXDTabPanel_xjx").setTitle("星件箱（" + responseArray.split('|')[2] + "）")

        },
        failure: function(form, action) {

        }
    });

    },
    checkData: function(permisson) { 
    },
    SetPositionId: function(id) {
        this.positionId = id;
    },
    //设置Window提交回调处理事件
    OnCallback: function(data) {
    }
});




    