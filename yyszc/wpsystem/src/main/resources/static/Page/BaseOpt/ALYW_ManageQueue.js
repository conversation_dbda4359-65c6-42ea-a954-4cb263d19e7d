DownloadJSByParams('../../Page/CPSoft/common.js', function () {
    var CssHead = [
        '../../Page/CPSoft/Spinner.css?version=' + _cps_js_version
      ];

    var FilesArray = [
        '../../Page/CPSoft/ExtGridExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version=' + _cps_js_version
     ];

    ImportCss.LoadCssList(CssHead, function () {
        Import.LoadJsList(FilesArray, function () {
            DownloadJS('../../Page/BaseOpt/ALYW_ManageMain.js?version='+_cps_js_version);
        });
    });

    document.getElementById('../../Page/BaseOpt/ALYW_ManageQueue.js').Destroy = function()
    {
        var element = document.getElementById('../../Page/BaseOpt/ALYW_ManageMain.js?version='+_cps_js_version);
        if (element!=undefined) element.parentNode.removeChild(element);
    }
    document.getElementById('../../Page/BaseOpt/ALYW_ManageQueue.js').DataReload = function () {
        if (Ext.getCmp('NF.ALYW_ManageMain.toolbar.IdREFRESH') != undefined) {
            Ext.getCmp('NF.ALYW_ManageMain.toolbar.IdREFRESH').fireEvent('click');
        }
    }
});
