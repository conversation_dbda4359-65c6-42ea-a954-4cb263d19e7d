Ext.namespace('BaseOpt.AS_ManageMain');

//初始化函数
BaseOpt.AS_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.AS_ManageMain';

    //===================模块自启动配置界面===========================start
    var ModuleCombo = new Ext.form.ComboBox({
        id: funcMark + '.AS_Form.Idmodule_name',
        name: 'module_name',
        fieldLabel: '功能模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/MDLMan/GetModuleList',
            fields: [
                'ID', 'module_name'
            ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.AS_Form.Idmodule_name').setValue(rec.data.module_name);
                Ext.getCmp(funcMark + '.AS_Form.IdLK_MID').setValue(rec.data.ID);
            }
        }
    });
    
    var AS_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.AS_Form.IdLK_ID',
                name: 'LK_ID',
                xtype: "hidden"
            },
            ModuleCombo,
            {
                id: funcMark + '.AS_Form.IdLK_MID',
                name: 'LK_MID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.AS_Form.IdLK_TMENU',
                xtype: "textfield",
                name: 'LK_TMENU',
                fieldLabel: "菜单目录",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.AS_Form.IdLK_TPATH',
                xtype: "textfield",
                name: 'LK_TPATH',
                fieldLabel: "菜单子目录",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.AS_Form.IdLK_TNODE',
                xtype: "textfield",
                name: 'LK_TNODE',
                fieldLabel: "菜单项",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.AS_Form.IdLK_SNO',
                xtype: "numberfield",
                name: 'LK_SNO',
                decimalPrecision: 0,
                fieldLabel: "菜单项",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'LK_ID', mapping: 'LK_ID', type: 'int' },
                { name: 'LK_MID', mapping: 'LK_MID', type: 'string' },
                { name: 'LK_TMENU', mapping: 'LK_TMENU', type: 'string' },
                { name: 'LK_TPATH', mapping: 'LK_TPATH', type: 'string' },
                { name: 'LK_TNODE', mapping: 'LK_TNODE', type: 'string' },
                { name: 'LK_SNO', mapping: 'LK_SNO', type: 'string' },
            ]
        })
    });

    var AS_FormWindow = new Ext.Window({
        id: funcMark + '.IdAS_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: AS_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    AS_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        AS_Form.form.reset();

        AS_FormWindow.setTitle(ExtractTitleString("模块自启动配置==新增模块自启动配置=="));
        AS_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (AS_Form.form.isValid()) {
                AS_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/AsMan/AddAS',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        AS_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        AS_FormWindow.buttons[0].enable();
        AS_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdAS_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "模块自启动配置==修改模块自启动配置==";
            AS_FormWindow.setTitle(ExtractTitleString(title));
            AS_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = AS_Form.form.reader.jsonData.data[0].LK_ID;
                if (AS_Form.form.isValid()) {
                    AS_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/AsMan/ModifyAS',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            AS_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            AS_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.LK_ID;
            var url = '../../Service/BaseOpt/AsMan/GetASById?ID=' + id;
            AS_FormWindow.show();
            AS_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    AS_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdAS_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "模块自启动配置==查看模块自启动配置==";
            AS_FormWindow.setTitle(ExtractTitleString(title));
            AS_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.LK_ID;
            var url = '../../Service/BaseOpt/AsMan/GetASById?ID=' + id;
            AS_FormWindow.show();
            AS_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    AS_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdAS_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/AsMan/DeleteAS",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.LK_ID }
                    });
                }
            });
        }
    }
    //===================模块自启动配置界面===========================end  

    //===================模块自启动配置管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdAS_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdAS_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/AsMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/AsMan/GetASList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['LK_ID', 'LK_MID', 'LK_LCID','LK_TMENU', 'LK_TPATH', 'LK_TNODE','module_num','module_name','LK_SNO']
        }),
        sortInfo: { field: "LK_SNO", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdAS_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "配置ID", width: 80, sortable: true, dataIndex: 'LK_ID' },
            { header: "所属模块", width: 120, sortable: true, dataIndex: 'module_name' },
            { header: "菜单目录", width: 120, sortable: true, dataIndex: 'LK_TMENU' },
            { header: "菜单子目录", width: 120, sortable: true, dataIndex: 'LK_TPATH' },
            { header: "菜单项", width: 120, sortable: true, dataIndex: 'LK_TNODE' },
            { header: "启动序号", width: 120, sortable: true, dataIndex: 'LK_SNO' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================模块自启动配置管理form===========================end
};
BaseOpt.AS_ManageMain.Initialize();
