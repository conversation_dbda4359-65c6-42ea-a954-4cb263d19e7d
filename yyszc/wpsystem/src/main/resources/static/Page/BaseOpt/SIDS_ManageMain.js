Ext.namespace('BaseOpt.SIDS_ManageMain');

//初始化函数
BaseOpt.SIDS_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.SIDS_ManageMain';

    //===================SQL注入检测串界面===========================start
    var modelRecord = Ext.data.Record.create([{ name: 'modelid', type: 'int' }, { name: 'modelname', type: 'string'}]);
    var modelaData = [[0, '加空格识别关键字'], [1, '不加空格'], [2, '文件格式']];
    var modelStore = new Ext.data.SimpleStore({
        fields: ['id', 'name'],
        data: modelaData
    });

    //查询模式选择框
    var ModelCombo = new Ext.form.ComboBox({
        id: funcMark + '.SIDS_Form.IdSIDTN',
        name: 'SIDTN',
        fieldLabel: '处理模式',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'id',
        displayField: 'name',
        allowBlank: false,
        editable: false,
        store: modelStore,
        anchor: '98%',
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.SIDS_Form.IdSIDT').setValue(rec.data.id);
            }
        }
    });

    var SIDS_Form = new Ext.FormPanel({
        labelWidth: 120,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.SIDS_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.SIDS_Form.IdIPW',
                xtype: "textfield",
                name: 'SIDS',
                fieldLabel: "注入串检测(|分隔)",
                maxLength: 1000,
                allowBlank: false,
                anchor: '98%'
            },
            ModelCombo,
            {
                id: funcMark + '.SIDS_Form.IdSIDT',
                name: 'SIDT',
                xtype: "hidden"
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'int' },
                { name: 'SIDS', mapping: 'SIDS', type: 'string' },
                { name: 'SIDT', mapping: 'SIDT', type: 'string' }
            ]
        })
    });

    var SIDS_FormWindow = new Ext.Window({
        id: funcMark + '.IdSIDS_FormWindow',
        width: 640,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: SIDS_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    SIDS_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        SIDS_Form.form.reset();

        SIDS_FormWindow.setTitle(ExtractTitleString("SQL注入检测串==新增SQL注入检测串=="));
        SIDS_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (SIDS_Form.form.isValid()) {
                SIDS_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/SidsMan/AddSIDS',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        SIDS_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        SIDS_FormWindow.buttons[0].enable();
        SIDS_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdSIDS_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "SQL注入检测串==修改SQL注入检测串==";
            SIDS_FormWindow.setTitle(ExtractTitleString(title));
            SIDS_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = SIDS_Form.form.reader.jsonData.data[0].ID;
                if (SIDS_Form.form.isValid()) {
                    SIDS_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/SidsMan/ModifySIDS',
                        method: 'post',
                        waitTitle: "请稍候",
                        params: { ID: id },
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            SIDS_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            SIDS_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/SidsMan/GetSIDSById?ID=' + id;
            SIDS_FormWindow.show();
            SIDS_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    ModelCombo.setValue(form.reader.jsonData.data[0].SIDT);
                    SIDS_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdSIDS_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "SQL注入检测串==查看SQL注入检测串==";
            SIDS_FormWindow.setTitle(ExtractTitleString(title));
            SIDS_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/SidsMan/GetSIDSById?ID=' + id;
            SIDS_FormWindow.show();
            SIDS_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    ModelCombo.setValue(form.reader.jsonData.data[0].SIDT);
                    SIDS_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdSIDS_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/SidsMan/DeleteSIDS",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.ID }
                    });
                }
            });
        }
    }
    //===================SQL注入检测串界面===========================end  

    //===================SQL注入检测串管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdSIDS_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdSIDS_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/SidsMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/SidsMan/GetSIDSList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'SIDS','SIDT']
        }),
        sortInfo: { field: "ID", direction: "ASC" },
        remoteSort: true
    });

    function setIntColumn(val, metadata, record) {
        if (val == null)return '';
        return val;
    }

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdSIDS_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "SQL注入检测串", width: 1000, sortable: true, dataIndex: 'SIDS' },
            { header: "SQL注入类型", width: 100, sortable: true, dataIndex: 'SIDT' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].enable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================SQL注入检测串管理form===========================end
};
BaseOpt.SIDS_ManageMain.Initialize();
