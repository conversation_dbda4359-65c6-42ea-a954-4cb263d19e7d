Ext.namespace('BaseOpt.LCW__ManageMain');

//初始化函数
BaseOpt.LCW__ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.LCW__ManageMain';

    var oCurrentStateMain = new CurrentStateMain(funcMark + ".CurrentStateMain.");
    var oWorkFlowMain = new WorkFlowMain(funcMark + ".WorkFlow.");

    var tabPanel = new Ext.TabPanel({
        activeTab: 0,
        deferredRender: false,
        autoDestroy: true,
        items:
        [
            {
                title: '流程状态表',
                isFixedTab: true,
                layout: 'fit',
                items: [oCurrentStateMain.grid]
            },
            {
                title: '流程历史表',
                isFixedTab: true,
                layout: 'fit',
                items: [oWorkFlowMain.grid]
            }
        ]
    });

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
            {
                region: 'center',
                layout: 'fit',
                items: [tabPanel]
            }
        ]
    }));
    Ext.QuickTips.init();

    //===================流程管理配置管理form===========================end
};
BaseOpt.LCW__ManageMain.Initialize();
