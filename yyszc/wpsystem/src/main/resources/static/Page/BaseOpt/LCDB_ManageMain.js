Ext.namespace('BaseOpt.LCDB_ManageMain');

//初始化函数
BaseOpt.LCDB_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.LCDB_ManageMain';

    //===================流程配置界面===========================start
    var ModuleCombo = new Ext.form.ComboBox({
        id: funcMark + '.LCDB_Form.Idmodule_name',
        name: 'module_name',
        fieldLabel: '功能模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/MDLMan/GetModuleList',
            fields: [
                'ID', 'module_name'
            ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.LCDB_Form.Idmodule_name').setValue(rec.data.module_name);
                Ext.getCmp(funcMark + '.LCDB_Form.IdLK_MID').setValue(rec.data.ID);
            }
        }
    });

    var LCCombo = new Ext.form.ComboBox({
        id: funcMark + '.LCDB_Form.IdlcName',
        name: 'lcName',
        fieldLabel: '流程名称',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'LcID',
        displayField: 'lcName',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/LCDBMan/GetLCList',
            fields: [
                 'LcID', 'lcName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            afterRender: function (obj) {
            },
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.LCDB_Form.IdlcName').setValue(rec.data.lcName);
                Ext.getCmp(funcMark + '.LCDB_Form.IdLK_LCID').setValue(rec.data.LcID);
            }
        }
    });

    var LCDB_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.LCDB_Form.IdLK_ID',
                name: 'LK_ID',
                xtype: "hidden"
            },
            ModuleCombo,
            {
                id: funcMark + '.LCDB_Form.IdLK_MID',
                name: 'LK_MID',
                xtype: "hidden"
            },
            LCCombo,
            {
                id: funcMark + '.LCDB_Form.IdLK_LCID',
                name: 'LK_LCID',
                xtype: "hidden"
            }, 
            {   //行六
                id: funcMark + '.LCDB_Form.IdLK_TMENU',
                xtype: "textfield",
                name: 'LK_TMENU',
                fieldLabel: "菜单目录",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.LCDB_Form.IdLK_TNODE',
                xtype: "textfield",
                name: 'LK_TNODE',
                fieldLabel: "菜单项",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'LK_ID', mapping: 'LK_ID', type: 'int' },
                { name: 'LK_MID', mapping: 'LK_MID', type: 'string' },
                { name: 'LK_LCID', mapping: 'LK_LCID', type: 'string' },
                { name: 'lcName', mapping: 'lcName', type: 'string' },
                { name: 'module_name', mapping: 'module_name', type: 'string' },
                { name: 'LK_TMENU', mapping: 'LK_TMENU', type: 'string' },
                { name: 'LK_TNODE', mapping: 'LK_TNODE', type: 'string' }
            ]
        })
    });

    var LCDB_FormWindow = new Ext.Window({
        id: funcMark + '.IdLCDB_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: LCDB_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    LCDB_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        LCDB_Form.form.reset();

        LCDB_FormWindow.setTitle(ExtractTitleString("功能关联流程配置==新增功能关联流程配置=="));
        LCDB_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (LCDB_Form.form.isValid()) {
                LCDB_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/LCDBMan/AddLCDB',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        LCDB_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        LCDB_FormWindow.buttons[0].enable();
        LCDB_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdLCDB_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "功能关联流程配置==修改功能关联流程配置==";
            LCDB_FormWindow.setTitle(ExtractTitleString(title));
            LCDB_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = LCDB_Form.form.reader.jsonData.data[0].LK_ID;
                if (LCDB_Form.form.isValid()) {
                    LCDB_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/LCDBMan/ModifyLCDB',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            LCDB_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            LCDB_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.LK_ID;
            var url = '../../Service/BaseOpt/LCDBMan/GetLCDBById?ID=' + id;
            LCDB_FormWindow.show();
            LCDB_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    LCDB_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdLCDB_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "功能关联流程配置==查看功能关联流程配置==";
            LCDB_FormWindow.setTitle(ExtractTitleString(title));
            LCDB_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.LK_ID;
            var url = '../../Service/BaseOpt/LCDBMan/GetLCDBById?ID=' + id;
            LCDB_FormWindow.show();
            LCDB_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    LCDB_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdLCDB_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/LCDBMan/DeleteLCDB",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.LK_ID }
                    });
                }
            });
        }
    }
    //===================功能关联流程配置界面===========================end  

    //===================功能关联流程配置管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdLCDB_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdLCDB_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/LCDBMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/LCDBMan/GetLCDBList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['LK_ID', 'LK_LCID', 'LK_MID','lcName', 'LcID', 'module_name', 'LK_TMENU', 'LK_TNODE']
        }),
        sortInfo: { field: "LK_LCID", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdLCDB_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "配置ID", width: 80, sortable: true, dataIndex: 'LK_ID' },
            { header: "功能模块", width: 170, sortable: true, dataIndex: 'module_name' },
            { header: "流程ID", width: 120, sortable: true, dataIndex: 'LcID' },
            { header: "流程名称", width: 120, sortable: true, dataIndex: 'lcName' },
            { header: "菜单目录", width: 120, sortable: true, dataIndex: 'LK_TMENU' },
            { header: "菜单项", width: 120, sortable: true, dataIndex: 'LK_TNODE' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn: function () {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================功能关联流程配置管理form===========================end
};
BaseOpt.LCDB_ManageMain.Initialize();
