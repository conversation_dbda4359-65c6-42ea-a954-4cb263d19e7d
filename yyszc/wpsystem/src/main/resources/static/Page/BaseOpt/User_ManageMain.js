Ext.namespace('BaseOpt.User_ManageMain');

//初始化函数
BaseOpt.User_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.User_ManageMain';

    //===================用户信息界面===========================start
    var groupTree = new Ext.tree.TreePanel({
        title: '单位树',
        width: 480,
        height: 400,
        minSize: 245,
        maxSize: 400,
        margins: '0 0 0 5',
        autoScroll: true,
        rootVisible: false,
        root: new Ext.tree.AsyncTreeNode({ id: 'root', text: '机构树', expanded: false }),
        loader: new Ext.tree.TreeLoader({
            dataUrl: "../../Service/BaseOpt/GroupMan/GetGroupTreeOneTime",
            preloadChildren: true
        }),
        listeners: {
            'click': function (node,obj) {
                groupid = node.id;
                groupname = node.groupname;
                Ext.getCmp(funcMark + '.User_Form.IdGroupId').setValue(groupid);
                Ext.getCmp(funcMark + '.User_Form.IdGroupName').setValue(groupname);
            }
        }
    });

    var SmartExpandFirst = function () {
        var node = groupTree.getRootNode();
        if (!node.expanded) {
            node.expand(false, false, function (a) {
                var childnodes = node.childNodes;
                if (childnodes != undefined) {
                    var childnode = childnodes[0];
                    if (childnode!=undefined&&!childnode.expanded) {
                        childnode.expand(false, false, function (a) { });
                    }
                }
            });
        } else {
            var childnodes = node.childNodes;
            if (childnodes != undefined) {
                var childnode = childnodes[0];
                if (childnode!=undefined&&!childnode.expanded) {
                    childnode.expand(false, false, function (a) { });
                }
            }
        }
    }

    var GroupMenu = new Ext.menu.Menu({
        items: [groupTree]
    });

    var User_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.User_Form.IdId',
                name: 'Id',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.User_Form.IdLoginName',
                xtype: "textfield",
                name: 'LoginName',
                fieldLabel: "登录名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.User_Form.IdRealName',
                xtype: "textfield",
                name: 'RealName',
                fieldLabel: "用户姓名",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {
                id: funcMark + '.User_Form.IdGroupName',
                xtype: 'trigger',
                triggerClass:'x-form-my-trigger',
                fieldLabel: "所属单位",
                editable:false,
                anchor: '98%',
                onTriggerClick:function (e)
                {
                    e.preventDefault();
                    e.stopEvent();

                    if (this.menu == undefined) {
                        this.menu = GroupMenu;
                        SmartExpandFirst();
                    }
                    this.menu.show(this.el);
                }
            },
            {
                id: funcMark + '.User_Form.IdGroupId',
                name: 'GroupId',
                xtype: "hidden"
            }, 
            {   //行六
                id: funcMark + '.User_Form.IdTelephone',
                xtype: "textfield",
                name: 'Telephone',
                fieldLabel: "电话号码",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.User_Form.IdMsgType',
                xtype: "textfield",
                name: 'MsgType',
                fieldLabel: "消息类型",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.User_Form.IdOA',
                xtype: "textfield",
                name: 'OA',
                fieldLabel: "OA编号",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.User_Form.Idtype',
                xtype: "numberfield",
                name: 'type',
                decimalPrecision: 0,
                fieldLabel: "用户类型",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.User_Form.IdP_XH',
                xtype: "numberfield",
                name: 'P_XH',
                decimalPrecision: 0,
                fieldLabel: "显示序号",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'Id', mapping: 'Id', type: 'string' },
                { name: 'LoginName', mapping: 'LoginName', type: 'string' },
                { name: 'RealName', mapping: 'RealName', type: 'string' },
                { name: 'GroupId', mapping: 'GroupId', type: 'string' },
                { name: 'GroupName', mapping: 'GroupName', type: 'string' },
                { name: 'GroupDesc', mapping: 'GroupDesc', type: 'string' },
                { name: 'Telephone', mapping: 'Telephone', type: 'string' },
                { name: 'MsgType', mapping: 'MsgType', type: 'string' },
                { name: 'OA', mapping: 'OA', type: 'string' },
                { name: 'type', mapping: 'type', type: 'string' },
                { name: 'P_XH', mapping: 'P_XH', type: 'string' } 
            ]
        })
    });

    var User_FormWindow = new Ext.Window({
        id: funcMark + '.IdUser_FormWindow',
        width: 800,
        height: 600,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: User_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    User_FormWindow.hide();
                }
            }
        ]
    });

    function onRoleButtonClick(record) {

        var id = record.get("Id");
        var login = record.get("LoginName");
        var name = record.get("RealName");
        var group = record.get("GroupName");
        var tgroup = record.get("TopGroupName");

        var oUserRoleList = new UserRoleList(funcMark);
        oUserRoleList.ShowWindow(id, login, name, tgroup,group);
    }

    function onAddButtonClick() {
        User_Form.form.reset();

        User_FormWindow.setTitle(ExtractTitleString("用户信息==新增用户信息=="));
        User_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (User_Form.form.isValid()) {
                User_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/UserMan/AddUser',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        User_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        User_FormWindow.buttons[0].enable();
        User_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "用户信息==修改用户信息==";
            User_FormWindow.setTitle(ExtractTitleString(title));
            User_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = User_Form.form.reader.jsonData.data[0].Id;
                if (User_Form.form.isValid()) {
                    User_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/UserMan/ModifyUser',
                        method: 'post',
                        params: { UserId: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            User_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            User_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/UserMan/GetUserById?UserId=' + id;
            User_FormWindow.show();
            User_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    var respjson=Ext.decode(action.response.responseText);
                    if(respjson!=undefined) {
                        var respdata = getResultData(respjson);
                        if(respdata!=undefined)
                        {
                            Ext.getCmp(funcMark + '.User_Form.IdGroupId').setValue(respdata.GroupId);
                            Ext.getCmp(funcMark + '.User_Form.IdGroupName').setValue(respdata.GroupName);

                        }
                    }

                    User_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "用户信息==查看用户信息==";
            User_FormWindow.setTitle(ExtractTitleString(title));
            User_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/UserMan/GetUserById?UserId=' + id;
            User_FormWindow.show();
            User_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    var respjson=Ext.decode(action.response.responseText);
                    if(respjson!=undefined) {
                        var respdata = getResultData(respjson);
                        if(respdata!=undefined)
                        {
                            Ext.getCmp(funcMark + '.User_Form.IdGroupId').setValue(respdata.GroupId);
                            Ext.getCmp(funcMark + '.User_Form.IdGroupName').setValue(respdata.GroupName);

                        }
                    }

                    User_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/UserMan/DeleteUser",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { UserId: grid.getSelectionModel().getSelected().data.Id }
                    });
                }
            });
        }
    }

    function onRSPwdButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要重置所选用户的密码？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    Ext.Ajax.request({
                        url: '../../Service/Cpsoft/userman/ResetPassword',
                        method: 'POST',
                        params: { UserId: grid.getSelectionModel().getSelected().data.Id },
                        success: function (res, opts) {
                            if (res.responseText == "" || res.responseText == undefined) return;
                            var resp = Ext.decode(res.responseText);

                            if (resp.success === true || resp.success === "true") {
                                Ext.MessageBox.alert("操作提示","密码重置成功！");
                            } else {
                                if (resp.text != undefined) {
                                    Ext.MessageBox.alert("操作提示", resp.text);
                                }
                            }
                            submitButton.enable();
                        }
                    });
                }
            });
        }
    }
    //===================用户信息界面===========================end  

    //===================用户信息管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
            {
                text: '新增',
                iconCls: 'ButtonFormAdd',
                handler: onAddButtonClick
            },
            {
                text: '修改',
                iconCls: 'ButtonFormEdit',
                handler: onEditButtonClick
            },
            {
                text: '删除',
                iconCls: 'ButtonFormDelete',
                handler: onDeleteButtonClick
            },
            {
                text: '查看',
                iconCls: 'ButtonFormLook',
                handler: onLookButtonClick
            },
            {
                text: '重置密码',
                iconCls: 'ButtonFormLook',
                handler: onRSPwdButtonClick
            }
	     ]
    });

    var toolbar2= new Ext.Toolbar({
        items:
        [
            '登录账号',
            {
                id: funcMark + '.toolbar.IdLoginName',
                xtype: 'textfield',
                width: 176
            },
            '用户名称',
            {
                id: funcMark + '.toolbar.IdRealName',
                xtype: 'textfield',
                width: 176
            },
            '所属公司',
            {
                id: funcMark + '.toolbar.IdCompName',
                xtype: 'textfield',
                width: 176
            },
            '所属部门',
            {
                id: funcMark + '.toolbar.IdGroupName',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: LoadData
            },
            {
                text: '刷新',
                id: funcMark + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
	    ]
     });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdUser_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            LoginName: Ext.getCmp(funcMark + '.toolbar.IdLoginName').getValue(),
            RealName: Ext.getCmp(funcMark + '.toolbar.IdRealName').getValue(),          
            CompName: Ext.getCmp(funcMark + '.toolbar.IdCompName').getValue(),
            GroupName: Ext.getCmp(funcMark + '.toolbar.IdGroupName').getValue()
        }
        });
    }

    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdUser_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            LoginName: Ext.getCmp(funcMark + '.toolbar.IdLoginName').getValue(),
            RealName: Ext.getCmp(funcMark + '.toolbar.IdRealName').getValue(),            
            CompName: Ext.getCmp(funcMark + '.toolbar.IdCompName').getValue(),
            GroupName: Ext.getCmp(funcMark + '.toolbar.IdGroupName').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/UserMan/ExportExcel", {
            LoginName: Ext.getCmp(funcMark + '.toolbar.IdLoginName').getValue(),
            RealName: Ext.getCmp(funcMark + '.toolbar.IdRealName').getValue(),            
            CompName: Ext.getCmp(funcMark + '.toolbar.IdCompName').getValue(),
            GroupName: Ext.getCmp(funcMark + '.toolbar.IdGroupName').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/UserMan/GetUserList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName']
        }),
        sortInfo: { field: "Id", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdUser_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "用户权限", width: 160, sortable: true, dataIndex: 'LoginName',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "用户角色";
                }
            },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'TopGroupName' },
            { header: "所属部门", width: 100, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1, toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            },
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var record = grid.store.getAt(rowIndex);
                if (columnIndex == 3) {
                    onRoleButtonClick(record);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['LoginName'] = Ext.getCmp(funcMark + '.toolbar.IdLoginName').getValue();
                o['RealName'] = Ext.getCmp(funcMark + '.toolbar.IdRealName').getValue();
                o['CompName'] = Ext.getCmp(funcMark + '.toolbar.IdCompName').getValue();
                o['GroupName'] = Ext.getCmp(funcMark + '.toolbar.IdGroupName').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].disable();
    toolbar1.items.items[4].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
        toolbar1.items.items[4].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================用户信息管理form===========================end
};
BaseOpt.User_ManageMain.Initialize();
