Ext.namespace('BaseOpt.NWP_ManageMain');

//初始化函数
BaseOpt.NWP_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.NWP_ManageMain';

    //===================内网认定项界面===========================start
    var NWP_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.NWP_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.NWP_Form.IdIPW',
                xtype: "textfield",
                name: 'IPW',
                fieldLabel: "网段名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.NWP_Form.IdIPS',
                xtype: "numberfield",
                name: 'IPS',
                fieldLabel: "起始地址",
                maxLength: 100,
                allowBlank: true,
                decimalPrecision: 0,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.NWP_Form.IdIPE',
                xtype: "numberfield",
                name: 'IPE',
                fieldLabel: "结束地址",
                maxLength: 100,
                llowBlank: true,
                decimalPrecision: 0,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'int' },
                { name: 'IPW', mapping: 'IPW', type: 'string' },
                { name: 'IPS', mapping: 'IPS', type: 'string' },
                { name: 'IPE', mapping: 'IPE', type: 'string' }
            ]
        })
    });

    var NWP_FormWindow = new Ext.Window({
        id: funcMark + '.IdNWP_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: NWP_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                disabled: true
            },
            {
                text: '关闭',
                handler: function () {
                    NWP_FormWindow.hide();
                }
            }
        ]
    });

    function CheckPSEValid() {
        var ips = Ext.getCmp(funcMark + '.NWP_Form.IdIPS').getValue();
        var ipe = Ext.getCmp(funcMark + '.NWP_Form.IdIPE').getValue();
        if (ips != "" && ipe != "") {
            if (parseInt(ips) > parseInt(ipe)) {
                Ext.MessageBox.alert('提示', '起始地址大于截至地址！');
                return false;
            }
        }
        return true;
    }

    function onAddButtonClick() {
        NWP_Form.form.reset();

        NWP_FormWindow.setTitle(ExtractTitleString("内网认定项==新增内网认定项=="));
        NWP_FormWindow.buttons[0].handler = function () {
            if (!CheckPSEValid()) return;
            var submitButton = this;
            submitButton.disable();
            if (NWP_Form.form.isValid()) {
                NWP_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/NWPMan/AddNWP',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        NWP_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        NWP_FormWindow.buttons[0].enable();
        NWP_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdNWP_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "内网认定项==修改内网认定项==";
            NWP_FormWindow.setTitle(ExtractTitleString(title));
            NWP_FormWindow.buttons[0].handler = function () {
                if (!CheckPSEValid()) return;
                var submitButton = this;
                submitButton.disable();

                var id = NWP_Form.form.reader.jsonData.data[0].ID;
                if (NWP_Form.form.isValid()) {
                    NWP_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/NWPMan/ModifyNWP',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            NWP_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            NWP_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/NWPMan/GetNWPById?ID=' + id;
            NWP_FormWindow.show();
            NWP_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    NWP_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdNWP_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "内网认定项==查看内网认定项==";
            NWP_FormWindow.setTitle(ExtractTitleString(title));
            NWP_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/NWPMan/GetNWPById?ID=' + id;
            NWP_FormWindow.show();
            NWP_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                },
                failure: function (form, action) {
                }
            });
            NWP_FormWindow.buttons[0].disable();
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdNWP_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/NWPMan/DeleteNWP",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.ID }
                    });
                }
            });
        }
    }
    //===================内网认定项界面===========================end  

    //===================内网认定项管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdNWP_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdNWP_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/NWPMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/NWPMan/GetNWPList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'IPW', 'IPS', 'IPE']
        }),
        sortInfo: { field: "IPW", direction: "ASC" },
        remoteSort: true
    });

    function setIntColumn(val, metadata, record) {
        if (val == null)return '';
        return val;
    }

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdNWP_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "内网网段", width: 120, sortable: true, dataIndex: 'IPW' },
            { header: "起始地址", width: 170, sortable: true, dataIndex: 'IPS', renderer: setIntColumn },
            { header: "结束地址", width: 170, sortable: true, dataIndex: 'IPE', renderer: setIntColumn }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================内网认定项管理form===========================end
};
BaseOpt.NWP_ManageMain.Initialize();
