DownloadJSByParams('../../Page/CPSoft/common.js', function ()
{
    var FilesArray = [
        '../../Page/CPSoft/ExtGridExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version=' + _cps_js_version,
        '../../Page/CPSoft/SmartCheck.js?version=' + _cps_js_version,
     ];

    Import.LoadJsList(FilesArray, function () {
        DownloadJS('../../Page/BaseOpt/MDL_ManageMain.js?version='+_cps_js_version);
    });

    document.getElementById('../../Page/BaseOpt/MDL_ManageQueue.js').Destroy = function()
    {
        var element = document.getElementById('../../Page/BaseOpt/MDL_ManageMain.js?version='+_cps_js_version); element.parentNode.removeChild(element);
        Ext.getCmp("BaseOpt.MDL_ManageMain.IdMDL_FormWindow").destroy();
    }
    document.getElementById('../../Page/BaseOpt/MDL_ManageQueue.js').DataReload = function () {
        if (Ext.getCmp('BaseOpt.MDL_ManageMain.toolbar.IdREFRESH')!= undefined) {
            Ext.getCmp('BaseOpt.MDL_ManageMain.toolbar.IdREFRESH').fireEvent('click');    
        }
    }
});
