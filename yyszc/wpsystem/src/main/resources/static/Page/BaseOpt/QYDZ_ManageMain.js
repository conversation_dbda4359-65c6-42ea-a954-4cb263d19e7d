Ext.namespace('BaseOpt.QYDZ_ManageMain');

//初始化函数
BaseOpt.QYDZ_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.QYDZ_ManageMain';

    //===================企业定制界面===========================start
    var QYDZ_Form = new Ext.FormPanel({
        labelWidth: 120,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.QYDZ_Form.IdQYDZ_ID',
                name: 'QYDZ_ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.QYDZ_Form.IdQYDZ_CID',
                xtype: "textfield",
                name: 'QYDZ_CID',
                fieldLabel: "QYDZ_CID",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.QYDZ_Form.IdQYDZ_MLIST',
                xtype: "textfield",
                name: 'QYDZ_MLIST',
                fieldLabel: "QYDZ_MLIST",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'QYDZ_ID', mapping: 'QYDZ_ID', type: 'int' },,
                { name: 'QYDZ_CID', mapping: 'QYDZ_CID', type: 'string' },
                { name: 'QYDZ_MLIST', mapping: 'QYDZ_MLIST', type: 'string' }
            ]
        })
    });

    var QYDZ_FormWindow = new Ext.Window({
        id: funcMark + '.IdQYDZ_FormWindow',
        width: 640,
        height: 420,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: QYDZ_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                disabled: true
            },
            {
                text: '关闭',
                handler: function () {
                    QYDZ_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        QYDZ_Form.form.reset();

        QYDZ_FormWindow.setTitle(ExtractTitleString("企业定制==新增企业定制=="));
        QYDZ_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (QYDZ_Form.form.isValid()) {
                QYDZ_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/QYDZMan/AddQYDZ',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        QYDZ_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        QYDZ_FormWindow.buttons[0].enable();
        QYDZ_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdQYDZ_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "企业定制==修改企业定制==";
            QYDZ_FormWindow.setTitle(ExtractTitleString(title));
            QYDZ_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = QYDZ_Form.form.reader.jsonData.data[0].QYDZ_ID;
                if (QYDZ_Form.form.isValid()) {
                    QYDZ_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/QYDZMan/ModifyQYDZ',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            QYDZ_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            QYDZ_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.QYDZ_ID;
            var url = '../../Service/BaseOpt/QYDZMan/GetQYDZById?ID=' + id;
            QYDZ_FormWindow.show();
            QYDZ_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    QYDZ_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdQYDZ_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "企业定制==查看企业定制==";
            QYDZ_FormWindow.setTitle(ExtractTitleString(title));
            QYDZ_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.QYDZ_ID;
            var url = '../../Service/BaseOpt/QYDZMan/GetQYDZById?ID=' + id;
            QYDZ_FormWindow.show();
            QYDZ_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    QYDZ_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdQYDZ_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/QYDZMan/DeleteQYDZ",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.QYDZ_ID }
                    });
                }
            });
        }
    }
    //===================企业定制界面===========================end  

    //===================企业定制管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdQYDZ_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdQYDZ_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/QYDZMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/QYDZMan/GetQYDZList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['QYDZ_ID', 'QYDZ_CID', 'QYDZ_MLIST']
        }),
        sortInfo: { field: "QYDZ_ID", direction: "ASC" },
        remoteSort: true
    });
    
    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdQYDZ_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "QYDZ_ID", width: 120, sortable: true, dataIndex: 'QYDZ_ID' },
            { header: "QYDZ_CID", width: 170, sortable: true, dataIndex: 'QYDZ_CID' },
            { header: "QYDZ_MLIST", width: 170, sortable: true, dataIndex: 'QYDZ_MLIST' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================企业定制管理form===========================end
};
BaseOpt.QYDZ_ManageMain.Initialize();
