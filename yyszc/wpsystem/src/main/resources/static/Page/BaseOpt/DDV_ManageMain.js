Ext.namespace('BaseOpt.DDV_ManageMain');

//初始化函数
BaseOpt.DDV_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.DDV_ManageMain';

    //===================字典值界面===========================start
    var DDTCombo = new Ext.form.ComboBox({
        id: funcMark + '.DDV_Form.IdTitle',
        name: 'Title',
        fieldLabel: '所属类型',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'Title',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/DDVMan/GetDDTList',
            fields: [
                 'ID', 'Title'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            afterRender: function (obj) {
            },
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.DDV_Form.IdTitle').setValue(rec.data.Title);
                Ext.getCmp(funcMark + '.DDV_Form.IdTitleID').setValue(rec.data.DDT_ID);
            }
        }
    });

    var UseGroup = new Ext.form.RadioGroup({
        id: funcMark + '.DDV_Form.IdIsUsed',
        name: 'IsUsed',
        fieldLabel: '是否使用',
        anchor: '50%',
        style: 'padding-top:3px;height:20px;',
        items:
            [
                {
                    boxLabel: '<span style="color:blue;">是</span>',
                    name: 'IsUsed',
                    inputValue: '1',
                    checked: true
                },
                {
                    boxLabel: '<span style="color:red;">否</span>',
                    name: 'IsUsed',
                    inputValue: '0'
                }
            ],
        listeners:
            {
                change: function (rdgroup, checked) {
                    var chval = obj.SBDGroup.getValue();
                    Ext.getCmp(funcMark + '.DDV_Form.IdUseGroup').setValue(chval);
                }
            }
    });

    var DDV_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.DDV_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            DDTCombo,
            {
                id: funcMark + '.DDV_Form.IdTitleID',
                name: 'TitleID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.DDV_Form.IdContent',
                xtype: "textfield",
                name: 'Content',
                fieldLabel: "字典值",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.DDV_Form.IdParameter',
                xtype: "textfield",
                name: 'Parameter',
                fieldLabel: "字典值参",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            UseGroup
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'int' },
                { name: 'TitleID', mapping: 'TitleID', type: 'string' },
                { name: 'Content', mapping: 'Content', type: 'string' },
                { name: 'Parameter', mapping: 'Parameter', type: 'string' },
                { name: 'IsUsed', mapping: 'IsUsed', type: 'string' }
            ]
        })
    });

    var DDV_FormWindow = new Ext.Window({
        id: funcMark + '.IdDDV_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: DDV_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    DDV_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        DDV_Form.form.reset();

        DDV_FormWindow.setTitle(ExtractTitleString("字典值==新增字典值=="));
        DDV_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (DDV_Form.form.isValid()) {
                DDV_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/DDVMan/AddDDV',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        DDV_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        DDV_FormWindow.buttons[0].enable();
        DDV_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdDDV_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "字典值==修改字典值==";
            DDV_FormWindow.setTitle(ExtractTitleString(title));
            DDV_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = DDV_Form.form.reader.jsonData.data[0].ID;
                if (DDV_Form.form.isValid()) {
                    DDV_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/DDVMan/ModifyDDV',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            DDV_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            DDV_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/DDVMan/GetDDVById?ID=' + id;
            DDV_FormWindow.show();
            DDV_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    var respjson=Ext.decode(action.response.responseText);
                    if(respjson!=undefined) {
                        var respdata = getResultData(respjson);
                        if(respdata!=undefined)
                        {
                            DDTCombo.setValue(respdata.TitleID);
                            UseGroup.setValue(respdata.IsUsed);
                        }
                    }
                    DDV_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdDDV_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "字典值==查看字典值==";
            DDV_FormWindow.setTitle(ExtractTitleString(title));
            DDV_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/DDVMan/GetDDVById?ID=' + id;
            DDV_FormWindow.show();
            DDV_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    var respjson=Ext.decode(action.response.responseText);
                    if(respjson!=undefined) {
                        var respdata = getResultData(respjson);
                        if(respdata!=undefined)
                        {
                            DDTCombo.setValue(respdata.TitleID);
                            UseGroup.setValue(respdata.IsUsed);
                        }
                    }
                    DDV_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdDDV_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/DDVMan/DeleteDDV",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.ID }
                    });
                }
            });
        }
    }
    //===================字典值界面===========================end  

    //===================字典值管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdDDV_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdDDV_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/DDVMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/DDVMan/GetDDVList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'Title', 'TitleID', 'Content', 'Parameter', 'IsUsed']
        }),
        sortInfo: { field: "TitleID", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdDDV_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "字典值ID", width: 80, sortable: true, dataIndex: 'ID' },
            { header: "所属类型", width: 120, sortable: true, dataIndex: 'Title' },
            { header: "字典值", width: 200, sortable: true, dataIndex: 'Content' },
            { header: "字典值参", width: 120, sortable: true, dataIndex: 'Parameter' },
            { header: "是否使用", width: 120, sortable: true, dataIndex: 'IsUsed' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: false,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================字典值管理form===========================end
};

BaseOpt.DDV_ManageMain.Initialize();

