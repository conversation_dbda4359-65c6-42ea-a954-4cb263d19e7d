function UpdatePassword(ownerStr) 
{
    var obj = this;

    var UPForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 420,
        layout: 'form',
        autoScroll: false,
        items: [
            {   //行六
                id: ownerStr + '.UPForm.IdOldPwd',
                xtype: "textfield",
                name: 'OldPwd',
                fieldLabel: "验证旧密码",
                maxLength: 100,
                inputType: 'password',
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.UPForm.IdFirstPwd',
                xtype: "textfield",
                name: 'DestCod',
                fieldLabel: "第一遍密码",
                maxLength: 100,
                inputType: 'password',
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.UPForm.IdSecondPwd',
                xtype: "textfield",
                name: 'SecondPwd',
                fieldLabel: "第二遍密码",
                maxLength: 100,
                inputType: 'password',
                allowBlank: false,
                anchor: '98%'
            }
        ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [

            ]
        })
     });

    function is_strong_real(password) {
        var regExpress = /^(?![^a-zA-Z]+$)(?!\D+$)(?![a-zA-Z0-9]+$).{8,}$/;
        if (regExpress.test(password)) {
            return 1;
        }
        else {
            return 0;
        }
    }

    var UPWindow = new Ext.Window({
        width: 420,
        height: 320,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: UPForm,
        layout: 'fit',
        title: '修改当前密码...',
        buttonAlign: 'center',
        buttons: [
        {
            text: '修改密码',
            height: 26,
            handler: function () {
                var oldPwd = Ext.getCmp(ownerStr + '.UPForm.IdOldPwd').getValue();
                var firstPwd = Ext.getCmp(ownerStr + '.UPForm.IdFirstPwd').getValue();
                var secondPwd = Ext.getCmp(ownerStr + '.UPForm.IdSecondPwd').getValue();
                if (oldPwd == "") {
                    Ext.MessageBox.alert('提示','提供老密码不能为空!');
                    return;
                }
                if (firstPwd == oldPwd) {
                    Ext.MessageBox.alert('提示', '新密码和旧密码不能一致！');
                    return;
                }
                if (firstPwd != secondPwd) {
                    Ext.MessageBox.alert('提示', '俩遍密码不一致！');
                    return;
                }
                if (firstPwd == "") {
                    Ext.MessageBox.alert('提示', '新密码不能为空！');
                    return;
                }
                if (is_strong_real(firstPwd)==0) {
                    Ext.MessageBox.alert('提示', '密码强度弱，请尽量使用“字母、数字、及控制字符”来设置您的密码！');
                    return;
                }

                var submitButton = this;
                submitButton.disable();

                var h5old = hex_md5(oldCode);
                var h5new = hex_md5(firstPwd);
                var yh5new = PassEnCode(firstPwd);
                Ext.Ajax.request({
                    url: '../../Service/BaseOpt/UserMan/CheckOldPassword',
                    params: {OldPwd: h5old,ypasswod:yh5new},
                    method: 'POST',
                    success: function (res, opts) {
                        if (res.responseText == "" || res.responseText == undefined) return;
                        var resp = Ext.decode(res.responseText);

                        if (resp.success === true || resp.success === "true") {
                            Ext.Ajax.request({
                                url: '../../Service/BaseOpt/UserMan/UpdatePassword',
                                params: {NewPwd: h5new },
                                method: 'POST',
                                success: function (res, opts) {
                                    if (res.responseText == "" || res.responseText == undefined) return;
                                    var resp0 = Ext.decode(res.responseText);

                                    if (resp0.success === true || resp0.success === "true") {
                                        Ext.MessageBox.alert("操作提示", "密码修改成功!", function () { UPWindow.close(); });
                                        {
                                            
                                        }
                                    } else {
                                        if (resp0.text != undefined) {
                                            Ext.MessageBox.alert("操作提示", resp0.text);
                                        }
                                    }
                                }
                            });
                        } else {
                            if (resp.text != undefined) {
                                Ext.MessageBox.alert("操作提示", resp.text);
                            }
                        }
                        submitButton.enable();
                    }
                });
            }
        },
        {
            text: '关闭',
            height: 26,
            handler: function () {
                UPWindow.close();
            }
        }]
    });

    this.ShowWindow = function () {
        UPForm.form.reset();
        UPWindow.show();
    }
}

UpdatePassword.prototype = {
    constructor: UpdatePassword
}