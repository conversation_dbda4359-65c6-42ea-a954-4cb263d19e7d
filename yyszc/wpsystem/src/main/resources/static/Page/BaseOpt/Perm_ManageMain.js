Ext.namespace('BaseOpt.Perm_ManageMain');

//初始化函数
BaseOpt.Perm_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.Perm_ManageMain';

    //===================操作权限界面===========================start
    var SubSystemCombo = new Ext.form.ComboBox({
        id: funcMark + '.Perm_Form.IdSubsystem',
        name: 'Subsystem',
        fieldLabel: '所属系统',
        typeAhead: true,
        forceSelection: false,
        selectOnFocus: true,
        triggerAction: 'all',
        editable: true,
        lazyRender: true,
        mode: 'local',
        valueField: 'Subsystem',
        displayField: 'Subsystem',
        allowBlank: false,
        anchor: '98%',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/BaseOpt/PermMan/GetSubsystemList',
            fields: [
                 'Subsystem', 'Subsystem'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var Perm_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {   //行六
                id: funcMark + '.Perm_Form.IdPermissionNo',
                xtype: "textfield",
                name: 'PermissionNo',
                fieldLabel: "权限编号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Perm_Form.IdOperationModule',
                xtype: "textfield",
                name: 'OperationModule',
                fieldLabel: "类型名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Perm_Form.IdOperation',
                xtype: "textfield",
                name: 'Operation',
                fieldLabel: "权限名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            SubSystemCombo
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'PermissionNo', mapping: 'PermissionNo', type: 'string' },
                { name: 'OperationModule', mapping: 'OperationModule', type: 'string' },
                { name: 'Operation', mapping: 'Operation', type: 'string' },
                { name: 'Subsystem', mapping: 'Subsystem', type: 'string' }
            ]
        })
    });

    var Perm_FormWindow = new Ext.Window({
        id: funcMark + '.IdPerm_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: Perm_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    Perm_FormWindow.hide();
                }
            }
        ]
    });

    function onRoleButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdPerm_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        } else {
            var record = grid.getSelectionModel().getSelections()[0];
            var no = record.get("PermissionNo");
            var module = record.get("OperationModule");
            var name = record.get("Operation");
            var system = record.get("Subsystem");

            var oPermRoleList = new PermRoleList(funcMark);
            oPermRoleList.ShowWindow(no, module, name, system);
        }
    }

    function onAddButtonClick() {
        Perm_Form.form.reset();

        Perm_FormWindow.setTitle(ExtractTitleString("操作权限==新增操作权限=="));
        Perm_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (Perm_Form.form.isValid()) {
                Perm_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/PermMan/AddPerm',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        Perm_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        Perm_FormWindow.buttons[0].enable();
        Perm_FormWindow.show();
        Ext.getCmp(funcMark + '.Perm_Form.IdPermissionNo').setReadOnly(false);

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdPerm_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "操作权限==修改操作权限==";
            Perm_FormWindow.setTitle(ExtractTitleString(title));
            Perm_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = Perm_Form.form.reader.jsonData.data[0].PermissionNo;
                if (Perm_Form.form.isValid()) {
                    Perm_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/PermMan/ModifyPerm',
                        method: 'post',
                        params: { PermId: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            Perm_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            Perm_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.PermissionNo;
            var url = '../../Service/BaseOpt/PermMan/GetPermById?PermId=' + id;
            Perm_FormWindow.show();
            Perm_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    Perm_FormWindow.buttons[0].enable();
                    Ext.getCmp(funcMark + '.Perm_Form.IdPermissionNo').setReadOnly(true);
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdPerm_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "操作权限==查看操作权限==";
            Perm_FormWindow.setTitle(ExtractTitleString(title));
            Perm_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.PermissionNo;
            var url = '../../Service/BaseOpt/PermMan/GetPermById?PermId=' + id;
            Perm_FormWindow.show();
            Perm_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    Perm_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdPerm_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/PermMan/DeletePerm",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { PermId: grid.getSelectionModel().getSelected().data.PermissionNo }
                    });
                }
            });
        }
    }
    //===================操作权限界面===========================end  

    //===================操作权限管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
             {
                 text: '角色',
                 iconCls: 'ButtonFormLook',
                 handler: onRoleButtonClick
             }
	       ]
    });

    var toolbar2 = new Ext.Toolbar({
        items:
        [
            '权限编号',
            {
                id: funcMark + '.toolbar.IdPermissionNo',
                xtype: 'textfield',
                width: 176
            },
            '权限名称',
            {
                id: funcMark + '.toolbar.IdOperation',
                xtype: 'textfield',
                width: 176
            },
            '所属系统',
            {
                id: funcMark + '.toolbar.IdSubsystem',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: LoadData
            },
            {
                text: '刷新',
                id: funcMark + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
	    ]
     });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdPerm_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            PermissionNo: Ext.getCmp(funcMark + '.toolbar.IdPermissionNo').getValue(),
            Operation: Ext.getCmp(funcMark + '.toolbar.IdOperation').getValue(),
            Subsystem: Ext.getCmp(funcMark + '.toolbar.IdSubsystem').getValue()
        }
        });
    }

    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdPerm_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            PermissionNo: Ext.getCmp(funcMark + '.toolbar.IdPermissionNo').getValue(),
            Operation: Ext.getCmp(funcMark + '.toolbar.IdOperation').getValue(),
            Subsystem: Ext.getCmp(funcMark + '.toolbar.IdSubsystem').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/PermMan/ExportExcel", {
            PermissionNo: Ext.getCmp(funcMark + '.toolbar.IdPermissionNo').getValue(),
            Operation: Ext.getCmp(funcMark + '.toolbar.IdOperation').getValue(),
            Subsystem: Ext.getCmp(funcMark + '.toolbar.IdSubsystem').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/PermMan/GetPermList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['PermissionNo', 'OperationModule', 'Operation', 'Subsystem']
        }),
        sortInfo: { field: "PermissionNo", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdPerm_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "权限编号", width: 80, sortable: true, dataIndex: 'PermissionNo' },
            { header: "权限模块", width: 120, sortable: true, dataIndex: 'OperationModule' },
            { header: "权限名称", width: 120, sortable: true, dataIndex: 'Operation' },
            { header: "所属系统", width: 120, sortable: true, dataIndex: 'Subsystem' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1, toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['PermissionNo'] = Ext.getCmp(funcMark + '.toolbar.IdPermissionNo').getValue();
                o['Operation'] = Ext.getCmp(funcMark + '.toolbar.IdOperation').getValue();
                o['Subsystem'] = Ext.getCmp(funcMark + '.toolbar.IdSubsystem').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].enable();
    toolbar1.items.items[4].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
        toolbar1.items.items[4].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================操作权限管理form===========================end
};
BaseOpt.Perm_ManageMain.Initialize();
