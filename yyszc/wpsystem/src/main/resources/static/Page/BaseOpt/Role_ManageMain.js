Ext.namespace('BaseOpt.Role_ManageMain');

//初始化函数
BaseOpt.Role_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.Role_ManageMain';

    //-------------------------------------------------------------------------------------------
    var MoudleCombo = new Ext.form.ComboBox({
        id: funcMark + '.Role_Form.IdRoleKindN',
        name: 'RoleKindN',
        fieldLabel: '所属模块',
        typeAhead: true,
        forceSelection: false,
        selectOnFocus: true,
        triggerAction: 'all',
        editable: true,
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: true,
        anchor: '98%',
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/MDLMan/GetModuleList',
            fields: [
                 'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.Role_Form.IdRoleKindN').setValue(rec.data.module_name);
                Ext.getCmp(funcMark + '.Role_Form.IdRoleKind').setValue(rec.data.ID);
            }
        }
    });

    var HideGroup = new Ext.form.RadioGroup({
        id: funcMark + '.Role_Form.IdIsHideN',
        name: 'IsHideN',
        fieldLabel: '是否隐藏',
        anchor: '98%',
        columns: 8,
        style: 'padding-top:3px;height:20px;',
        items:
        [
          {
              boxLabel: '<span style="color:red;">否</span>',
              name: 'IsHideN',
              inputValue: '0',
              labelStyle: 'color:red;',
              checked: true
          },
          {
              boxLabel: '<span style="color:blue;">是</span>',
              name: 'IsHideN',
              labelStyle: 'color:blue;',
              inputValue: '1'
          }
        ],
        listeners:
        {
            change: function (rdgroup, checked) {
                var chval = HideGroup.getValue();
                Ext.getCmp(funcMark + '.Role_Form.IdIsHide').setValue(chval);
            }
        }
    });

    //===================操作权限界面===========================start
    var Role_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.Role_Form.IdId',
                name: 'Id',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.Role_Form.IdRoleName',
                xtype: "textfield",
                name: 'RoleName',
                fieldLabel: "角色名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Role_Form.IdAdminGroupIds',
                xtype: "textfield",
                name: 'AdminGroupIds',
                fieldLabel: "管理组",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            HideGroup,
            {
                id: funcMark + '.Role_Form.IdIsHide',
                name: 'IsHide',
                xtype: "hidden"
            },
            MoudleCombo,
            {
                id: funcMark + '.Role_Form.IdRoleKind',
                name: 'RoleKind',
                xtype: "hidden"
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'Id', mapping: 'Id', type: 'string' },
                { name: 'RoleName', mapping: 'RoleName', type: 'string' },
                { name: 'AdminGroupIds', mapping: 'AdminGroupIds', type: 'string' },
                { name: 'IsHide', mapping: 'IsHide', type: 'string' },
                { name: 'RoleKind', mapping: 'RoleKind', type: 'string' }
            ]
        })
    });

    var Role_FormWindow = new Ext.Window({
        id: funcMark + '.IdRole_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: Role_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height:30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    Role_FormWindow.hide();
                }
            }
        ]
    });

    function onPermButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRole_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        } else {
            var record = grid.getSelectionModel().getSelections()[0];
            var id = record.get("Id");
            var name = record.get("RoleName");


            var oRolePermList = new RolePermList(funcMark);
            oRolePermList.ShowWindow(id, name);
        }
    }

    function onPersonButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRole_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        } else {
            var record = grid.getSelectionModel().getSelections()[0];
            var id = record.get("Id");
            var name = record.get("RoleName");

            var oRoleUserList = new RoleUserList(funcMark);
            oRoleUserList.ShowWindow(id, name);
        }
    }

    function onAddButtonClick() {
        Role_Form.form.reset();

        Role_FormWindow.setTitle(ExtractTitleString("操作权限==新增操作权限=="));
        Role_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (Role_Form.form.isValid()) {
                Role_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/RoleMan/AddRole',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        Role_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        Role_FormWindow.buttons[0].enable();
        Role_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRole_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "操作权限==修改操作权限==";
            Role_FormWindow.setTitle(ExtractTitleString(title));
            Role_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = Role_Form.form.reader.jsonData.data[0].Id;
                if (Role_Form.form.isValid()) {
                    Role_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/RoleMan/ModifyRole',
                        method: 'post',
                        params: { RoleId: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            Role_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            Role_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/RoleMan/GetRoleById?RoleId=' + id;
            Role_FormWindow.show();
            Role_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    Role_FormWindow.buttons[0].enable();

                    var ishide = form.reader.jsonData.data[0].IsHide;
                    if(ishide==false) {
                        HideGroup.setValue(0);
                        Ext.getCmp(funcMark + '.Role_Form.IdIsHide').setValue(0);
                    }
                    else{
                        HideGroup.setValue(1);
                        Ext.getCmp(funcMark + '.Role_Form.IdIsHide').setValue(1);
                    }

                    var rolekind = form.reader.jsonData.data[0].RoleKind;
                    MoudleCombo.setValue(rolekind);
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRole_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "操作权限==查看操作权限==";
            Role_FormWindow.setTitle(ExtractTitleString(title));
            Role_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/RoleMan/GetRoleById?RoleId=' + id;
            Role_FormWindow.show();
            Role_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    Role_FormWindow.buttons[0].disable();

                    var ishide = form.reader.jsonData.data[0].IsHide;
                    if(ishide==false) {
                        HideGroup.setValue(0);
                        Ext.getCmp(funcMark + '.Role_Form.IdIsHide').setValue(0);
                    }
                    else{
                        HideGroup.setValue(1);
                        Ext.getCmp(funcMark + '.Role_Form.IdIsHide').setValue(1);
                    }

                    var rolekind = form.reader.jsonData.data[0].RoleKind;
                    MoudleCombo.setValue(rolekind);
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRole_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/RoleMan/DeleteRole",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { RoleId: grid.getSelectionModel().getSelected().data.Id }
                    });
                }
            });
        }
    }
    //===================操作权限界面===========================end  

    //===================操作权限管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
             {
                 text: '资源',
                 iconCls: 'ButtonFormLook',
                 handler: onPermButtonClick
             },
             {
                 text: '用户',
                 iconCls: 'ButtonFormLook',
                 handler: onPersonButtonClick
             }
	     ]
    });

    var tbMoudleCombo = new Ext.form.ComboBox({
        id: funcMark + '.toolbar.IdRoleKindN',
        name: 'RoleKindN',
        fieldLabel: '所属模块',
        typeAhead: true,
        forceSelection: false,
        selectOnFocus: true,
        triggerAction: 'all',
        editable: true,
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: true,
        width:176,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/MDLMan/GetModuleList',
            fields: [
                'ID', 'module_name'
            ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(funcMark + '.toolbar.IdRoleKindN').setValue(rec.data.module_name);
                Ext.getCmp(funcMark + '.toolbar.IdRoleKind').setValue(rec.data.ID);
            }
        }
    });

    var toolbar2 = new Ext.Toolbar({
        items:
        [
            '角色名称',
            {
                id: funcMark + '.toolbar.IdRoleName',
                xtype: 'textfield',
                width: 176
            },
            '所属模块',
            tbMoudleCombo,
            {
                id: funcMark + '.toolbar.IdRoleKind',
                xtype: 'hidden',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: LoadData
            },
            {
                text: '刷新',
                id: funcMark + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
	    ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdRole_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            RoleName: Ext.getCmp(funcMark + '.toolbar.IdRoleName').getValue(),
            RoleKind: Ext.getCmp(funcMark + '.toolbar.IdRoleKind').getValue()
        }
        });
    }

    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdRole_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            RoleName: Ext.getCmp(funcMark + '.toolbar.IdRoleName').getValue(),
            RoleKind: Ext.getCmp(funcMark + '.toolbar.IdRoleKind').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/RoleMan/ExportExcel", {
            RoleName: Ext.getCmp(funcMark + '.toolbar.IdRoleName').getValue(),
            RoleKind: Ext.getCmp(funcMark + '.toolbar.IdRoleKind').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/RoleMan/GetRoleList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'RoleName', 'AdminGroupIds', 'IsHide','RoleKind']
        }),
        sortInfo: { field: "Id", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdRole_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "角色编号", width: 80, sortable: true, dataIndex: 'Id' },
            { header: "角色名称", width: 120, sortable: true, dataIndex: 'RoleName' },
            { header: "关联组名", width: 120, sortable: true, dataIndex: 'AdminGroupIds' },
            { header: "是否隐藏", width: 120, sortable: true, dataIndex: 'IsHide' },
            { header: "所属系统", width: 120, sortable: true, dataIndex: 'RoleKind' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1, toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['RoleName'] = Ext.getCmp(funcMark + '.toolbar.IdRoleName').getValue();
                o['RoleKind'] = Ext.getCmp(funcMark + '.toolbar.IdRoleKind').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].enable();
    toolbar1.items.items[4].disable();
    toolbar1.items.items[5].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
        toolbar1.items.items[4].enable();
        toolbar1.items.items[5].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================操作权限管理form===========================end
};
BaseOpt.Role_ManageMain.Initialize();
