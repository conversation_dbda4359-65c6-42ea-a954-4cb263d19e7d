Ext.namespace('BaseOpt.UCC_ManageMain');

//初始化函数
BaseOpt.UCC_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.UCC_ManageMain';

    //===================快速切换用户界面===========================start
    var UCC_Form = new Ext.FormPanel({
        labelWidth: 120,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.UCC_Form.IdREC_ID',
                name: 'REC_ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.UCC_Form.IdRU_USER',
                xtype: "textfield",
                name: 'RU_USER',
                fieldLabel: "快速切换用户账号",
                maxLength: 100,
                allowBlank: false,
                style: 'background-color: #ECFCF5;background-image:none;',
                anchor: '98%',
                listeners:
                {
                    specialkey: function (field, e) {
                        if (e.getKey() == e.UP || e.getKey() == e.DOWN || e.getKey() == e.ENTER) {
                            e.preventDefault();
                            e.stopEvent();

                            var strparams = Ext.getCmp(funcMark + '.UCC_Form.IdRU_USER').getValue();
                            var oSmartCheck = new SmartCheck(funcMark + ".PersonCheck", "vPerson", "", "LoginName,RealName,TopGroupId,TopGroupName", "200,200,200,200",
                                                "人员账号,人员姓名,所属公司ID,所属公司", "LoginName,RealName",
                                                funcMark + ".UCC_Form.IdRU_USER," + funcMark + ".UCC_Form.IdRU_USERN",
                                                0, "LoginName,RealName", "LoginName,RealName");

                            oSmartCheck.setTitle("用户选择列表");
                            oSmartCheck.Show(strparams);

                            return false;
                        }
                    },
                    change: function (field, newValue, oldValue) {
                        if (newValue == "") {
                            Ext.getCmp(funcMark + '.UCC_Form.IdRU_USERN').setValue("");
                        }
                    }
                }
            },
            {   //行六
                id: funcMark + '.UCC_Form.IdRU_USERN',
                xtype: "textfield",
                name: 'RU_USERN',
                fieldLabel: "快速切换用户名称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.UCC_Form.IdRC_USER',
                xtype: "textfield",
                name: 'RC_USER',
                fieldLabel: "切换目标用户账号",
                maxLength: 100,
                allowBlank: false,
                style: 'background-color: #ECFCF5;background-image:none;',
                anchor: '98%',
                listeners:
                {
                    specialkey: function (field, e) {
                        if (e.getKey() == e.UP || e.getKey() == e.DOWN || e.getKey() == e.ENTER) {
                            e.preventDefault();
                            e.stopEvent();

                            var strparams = Ext.getCmp(funcMark + '.UCC_Form.IdRC_USER').getValue();
                            var oSmartCheck = new SmartCheck(funcMark + ".PersonCheck", "vPerson", "", "LoginName,RealName,TopGroupId,TopGroupName", "200,200,200,200",
                                                "人员账号,人员姓名,所属公司ID,所属公司", "LoginName,RealName",
                                                funcMark + ".UCC_Form.IdRC_USER," + funcMark + ".UCC_Form.IdRC_USERN",
                                                0, "LoginName,RealName", "LoginName,RealName");

                            oSmartCheck.setTitle("用户选择列表");
                            oSmartCheck.Show(strparams);

                            return false;
                        }
                    },
                    change: function (field, newValue, oldValue) {
                        if (newValue == "") {
                            Ext.getCmp(funcMark + '.UCC_Form.IdRC_USERN').setValue("");
                        }
                    }
                }
            },
            {   //行六
                id: funcMark + '.UCC_Form.IdRC_USERN',
                xtype: "textfield",
                name: 'RC_USERN',
                fieldLabel: "切换目标用户名称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'REC_ID', mapping: 'REC_ID', type: 'int' },
                { name: 'RU_USER', mapping: 'RU_USER', type: 'string' },
                { name: 'RU_USERN', mapping: 'RU_USERN', type: 'string' },
                { name: 'RC_USER', mapping: 'RC_USER', type: 'string' },
                { name: 'RC_USERN', mapping: 'RC_USERN', type: 'string' }
            ]
        })
    });

    var UCC_FormWindow = new Ext.Window({
        id: funcMark + '.IdUCC_FormWindow',
        width: 540,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: UCC_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                disabled: true
            },
            {
                text: '关闭',
                handler: function () {
                    UCC_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        UCC_Form.form.reset();

        UCC_FormWindow.setTitle(ExtractTitleString("快速切换用户==新增快速切换用户=="));
        UCC_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (UCC_Form.form.isValid()) {
                UCC_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/UCCMan/AddUCC',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        UCC_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        UCC_FormWindow.buttons[0].enable();
        UCC_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUCC_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "快速切换用户==修改快速切换用户==";
            UCC_FormWindow.setTitle(ExtractTitleString(title));
            UCC_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = UCC_Form.form.reader.jsonData.data[0].REC_ID;
                if (UCC_Form.form.isValid()) {
                    UCC_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/UCCMan/ModifyUCC',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            UCC_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            UCC_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.REC_ID;
            var url = '../../Service/BaseOpt/UCCMan/GetUCCById?ID=' + id;
            UCC_FormWindow.show();
            UCC_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    UCC_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUCC_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "快速切换用户==查看快速切换用户==";
            UCC_FormWindow.setTitle(ExtractTitleString(title));
            UCC_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.REC_ID;
            var url = '../../Service/BaseOpt/UCCMan/GetUCCById?ID=' + id;
            UCC_FormWindow.show();
            UCC_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    UCC_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdUCC_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/UCCMan/DeleteUCC",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.REC_ID }
                    });
                }
            });
        }
    }
    //===================快速切换用户界面===========================end  

    //===================快速切换用户管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdUCC_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdUCC_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/UCCMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/UCCMan/GetUCCList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['REC_ID', 'RU_USER', 'RU_USERN', 'RC_USER', 'RC_USERN']
        }),
        sortInfo: { field: "RU_USER", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdUCC_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "快速功能用户账号", width: 120, sortable: true, dataIndex: 'RU_USER' },
            { header: "快速功能用户名称", width: 170, sortable: true, dataIndex: 'RU_USERN' },
            { header: "可切换用户账号", width: 170, sortable: true, dataIndex: 'RC_USER' },
            { header: "可切换用户名称", width: 200, sortable: true, dataIndex: 'RC_USERN' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================快速切换用户管理form===========================end
};
BaseOpt.UCC_ManageMain.Initialize();
