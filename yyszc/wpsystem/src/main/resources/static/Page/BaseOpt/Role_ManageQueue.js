DownloadJSByParams('../../Page/CPSoft/common.js', function ()
{
    var FilesArray = [
        '../../Page/CPSoft/ExtGridExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version=' + _cps_js_version,
        '../../Page/BaseOpt/MetaJs/RolePermList.js?version=' + _cps_js_version,
        '../../Page/BaseOpt/MetaJs/RoleUserList.js?version=' + _cps_js_version
     ];


    Import.LoadJsList(FilesArray, function () {
        DownloadJS('../../Page/BaseOpt/Role_ManageMain.js?version='+_cps_js_version);
    });

    document.getElementById('../../Page/BaseOpt/Role_ManageQueue.js').Destroy = function ()
    {
        var element = document.getElementById('../../Page/BaseOpt/Role_ManageMain.js?version='+_cps_js_version);
        if(element!=undefined){
            element.parentNode.removeChild(element);
        }
        Ext.getCmp("BaseOpt.Role_ManageMain.IdRole_FormWindow").destroy();
    }
    document.getElementById('../../Page/BaseOpt/Role_ManageQueue.js').DataReload = function() {

    }
});
