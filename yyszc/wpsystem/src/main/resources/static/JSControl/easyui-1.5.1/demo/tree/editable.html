<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Editable Tree - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Editable Tree</h2>
	<p>Click the node to begin edit, press enter key to stop edit or esc key to cancel edit.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="padding:5px">
		<ul id="tt" class="easyui-tree" data-options="
				url: 'tree_data1.json',
				method: 'get',
				animate: true,
				onClick: function(node){
					$(this).tree('beginEdit',node.target);
				}
			"></ul>
	</div>
</body>
</html>