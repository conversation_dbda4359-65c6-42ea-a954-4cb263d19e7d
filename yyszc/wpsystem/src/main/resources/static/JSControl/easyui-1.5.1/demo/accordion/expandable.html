<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Keep Expandable Panel in Accordion - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Keep Expandable Panel in Accordion</h2>
	<p>Keep a expandable panel and prevent it from collapsing.</p>
	<div style="margin:20px 0 10px 0;"></div>
	<div class="easyui-accordion" style="width:500px;height:300px;">
		<div title="Top Panel" data-options="iconCls:'icon-search',collapsed:false,collapsible:false" style="padding:10px;">
			<input class="easyui-searchbox" prompt="Enter something here" style="width:300px;">
		</div>
		<div title="About" data-options="selected:true" style="padding:10px;">
			<h3 style="color:#0099FF;">Accordion for jQuery</h3>
			<p>Accordion is a part of easyui framework for jQuery. It lets you define your accordion component on web page more easily.</p>
		</div>
		<div title="Title1" style="padding:10px">
			<p>Content1</p>
		</div>
		<div title="Title2" style="padding:10px">
			<p>Content2</p>
		</div>
	</div>

</body>
</html>