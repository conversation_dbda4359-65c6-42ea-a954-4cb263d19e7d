<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Fluid LinkButton - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Fluid LinkButton</h2>
	<p>This example shows how to set the width of LinkButton to a percentage of its parent container.</p>
	<div style="margin:10px 0 40px 0;"></div>
	<p>width: 15%</p>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add'" style="width:15%">Add</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-remove'" style="width:15%">Remove</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'" style="width:15%">Save</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cut',disabled:true" style="width:15%">Cut</a>
		<a href="#" class="easyui-linkbutton" style="width:15%">Text Button</a>
	</div>
	<p>width: 20%</p>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'" style="width:20%">Search</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-print'" style="width:20%">Print</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" style="width:20%">Reload</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-help'" style="width:20%">Help</a>
	</div>
	
</body>
</html>