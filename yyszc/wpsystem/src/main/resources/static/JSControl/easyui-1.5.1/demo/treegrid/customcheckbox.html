<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Custom CheckBox in TreeGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Custom CheckBox in TreeGrid</h2>
	<p>TreeGrid nodes with customized check boxes.</p>
	<div style="margin:20px 0;"></div>
	<table title="Folder Browser" class="easyui-treegrid" style="width:700px;height:250px"
			data-options="
				url: 'treegrid_data1.json',
				method: 'get',
				rownumbers: true,
				idField: 'id',
				treeField: 'name',
				checkbox: function(row){
					var names = ['Java','eclipse.exe','eclipse.ini'];
					if ($.inArray(row.name, names)>=0){
						return true;
					}
				}
			">
		<thead>
			<tr>
				<th data-options="field:'name'" width="220">Name</th>
				<th data-options="field:'size'" width="100" align="right">Size</th>
				<th data-options="field:'date'" width="150">Modified Date</th>
			</tr>
		</thead>
	</table>

</body>
</html>