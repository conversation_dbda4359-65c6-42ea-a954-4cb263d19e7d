<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TimePicker 24 Hour Format - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
	<script type="text/javascript" src="../../source/jquery.timepicker.js"></script>
</head>
<body>
	<h2>TimePicker 24 Hour Format</h2>
	<p>Click drop-down button to choose time on a clock.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-timepicker" label="Start Time:" value="08:10" labelPosition="top" style="width:100%;" data-options="panelWidth:350,panelHeight:350,hour24:true">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-timepicker" label="End Time:" value="20:30" labelPosition="top" style="width:100%;" data-options="panelWidth:350,panelHeight:350,hour24:true">
		</div>
	</div>
</body>
</html>