<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Custom TagBox Style - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Custom TagBox Style</h2>
	<p>This example shows how to apply different CSS styles to different tags.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<input class="easyui-tagbox" value="3" label="Add a tag" style="width:100%" data-options="
				url: 'tagbox_data1.json',
				method: 'get',
				value: '3,4',
				valueField: 'id',
				textField: 'text',
				limitToList: true,
				prompt: 'Select a Language',
				tagStyler: function(value){
					if (value == 3){
						return 'background:#ffd7d7;color:#c65353';
					} else if (value == 4){
						return 'background:#b8eecf;color:#45872c';
					}
				}
				">
	</div>
</body>
</html>