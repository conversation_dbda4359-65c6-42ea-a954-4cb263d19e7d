<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>AJAX Content</title>
</head>
<body>
	<p style="font-size:14px">Here is the content loaded via AJAX.</p>
	<ul>
		<li>easyui is a collection of user-interface plugin based on jQuery.</li>
		<li>easyui provides essential functionality for building modern, interactive, javascript applications.</li>
		<li>using easyui you don't need to write many javascript code, you usually defines user-interface by writing some HTML markup.</li>
		<li>complete framework for HTML5 web page.</li>
		<li>easyui save your time and scales while developing your products.</li>
		<li>easyui is very easy but powerful.</li>
	</ul>
</body>
</html>