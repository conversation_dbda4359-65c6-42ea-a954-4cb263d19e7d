<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TextBox with Icons - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TextBox with Icons</h2>
	<p>Click the icons on textbox to perform actions.</p>
	<div style="margin:20px 0 40px 0;"></div>
    <div style="margin:10px 0 20px 0">
        <span>Select Icon Align: </span>
        <select onchange="$('#tt').textbox({iconAlign:this.value})">
            <option value="right">Right</option>
            <option value="left">Left</option>
        </select>
    </div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="tt" class="easyui-textbox" style="width:100%;" data-options="
					label: 'Icons:',
					labelPosition: 'top',
					prompt: 'Input something here!',
					iconWidth: 22,
					icons: [{
						iconCls:'icon-add',
						handler: function(e){
							$(e.data.target).textbox('setValue', 'Something added!');
						}
					},{
						iconCls:'icon-remove',
						handler: function(e){
							$(e.data.target).textbox('clear');
						}
					},{
						iconCls:'icon-search',
						handler: function(e){
							var v = $(e.data.target).textbox('getValue');
							alert('The inputed value is ' + (v ? v : 'empty'));
						}
					}]
					">
		</div>
	</div>
</body>
</html>