<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Accept a Drop - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Accept a Drop</h2>
	<p>Some draggable object can not be accepted.</p>
	<div style="margin:20px 0;"></div>
	<div id="source" style="border:1px solid #ccc;width:300px;height:400px;float:left;margin:5px;">
		drag me!
		<div id="d1" class="drag">Drag 1</div>
		<div id="d2" class="drag">Drag 2</div>
		<div id="d3" class="drag">Drag 3</div>
	</div>
	<div id="target" style="border:1px solid #ccc;width:300px;height:400px;float:left;margin:5px;">
		drop here!
	</div>
	<div style="clear:both"></div>
	<style type="text/css">
		.drag{
			width:100px;
			height:50px;
			padding:10px;
			margin:5px;
			border:1px solid #ccc;
			background:#AACCFF;
		}
		.dp{
			opacity:0.5;
			filter:alpha(opacity=50);
		}
		.over{
			background:#FBEC88;
		}
	</style>
	<script>
		$(function(){
			$('.drag').draggable({
				proxy:'clone',
				revert:true,
				cursor:'auto',
				onStartDrag:function(){
					$(this).draggable('options').cursor='not-allowed';
					$(this).draggable('proxy').addClass('dp');
				},
				onStopDrag:function(){
					$(this).draggable('options').cursor='auto';
				}
			});
			$('#target').droppable({
				accept:'#d1,#d3',
				onDragEnter:function(e,source){
					$(source).draggable('options').cursor='auto';
					$(source).draggable('proxy').css('border','1px solid red');
					$(this).addClass('over');
				},
				onDragLeave:function(e,source){
					$(source).draggable('options').cursor='not-allowed';
					$(source).draggable('proxy').css('border','1px solid #ccc');
					$(this).removeClass('over');
				},
				onDrop:function(e,source){
					$(this).append(source)
					$(this).removeClass('over');
				}
			});
		});
	</script>

</body>
</html>