<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Snap Draggable - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Snap Draggable</h2>
	<p>This sample shows how to snap a draggable object to a 20x20 grid.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="position:relative;overflow:hidden;width:500px;height:300px">
		<div class="easyui-draggable" data-options="onDrag:onDrag" style="width:100px;height:100px;background:#fafafa;border:1px solid #ccc;">
		</div>
	</div>
	<script>
		function onDrag(e){
			var d = e.data;
			d.left = repair(d.left);
			d.top = repair(d.top);
			
			function repair(v){
				var r = parseInt(v/20)*20;
				if (Math.abs(v % 20) > 10){
					r += v > 0 ? 20 : -20;
				}
				return r;
			}
		}
	</script>

</body>
</html>