/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.ext-strict .ext-ie .x-tree .x-panel-bwrap{
    position:relative;
    overflow:hidden;
}

.x-tree-icon, .x-tree-ec-icon, .x-tree-elbow-line, .x-tree-elbow, .x-tree-elbow-end, .x-tree-elbow-plus, .x-tree-elbow-minus, .x-tree-elbow-end-plus, .x-tree-elbow-end-minus{
	border: 0 none;
	height: 18px;
	margin: 0;
	padding: 0;
	vertical-align: top;
	width: 16px;
    background-repeat: no-repeat;
}

.x-tree-node-collapsed .x-tree-node-icon, .x-tree-node-expanded .x-tree-node-icon, .x-tree-node-leaf .x-tree-node-icon{
	border: 0 none;
	height: 18px;
	margin: 0;
	padding: 0;
	vertical-align: top;
	width: 16px;
	background-position:center;
    background-repeat: no-repeat;
}

.ext-ie .x-tree-node-indent img, .ext-ie .x-tree-node-icon, .ext-ie .x-tree-ec-icon {
    vertical-align: middle !important;
}

.ext-strict .ext-ie8 .x-tree-node-indent img, .ext-strict .ext-ie8 .x-tree-node-icon, .ext-strict .ext-ie8 .x-tree-ec-icon {
    vertical-align: top !important;
}

/* checkboxes */

input.x-tree-node-cb {
    margin-left:1px;
    height: 19px;
	vertical-align: bottom;
}

.ext-ie input.x-tree-node-cb {
    margin-left:0;
    margin-top: 1px;
    width: 16px;
    height: 16px;
    vertical-align: middle;
}

.ext-strict .ext-ie8 input.x-tree-node-cb{
    margin: 1px 1px;
    height: 14px;
    vertical-align: bottom;
}

.ext-strict .ext-ie8 input.x-tree-node-cb + a{
    vertical-align: bottom;
}

.ext-opera input.x-tree-node-cb {
    height: 14px;
    vertical-align: middle;
}

.x-tree-noicon .x-tree-node-icon{
	width:0; height:0;
}

/* No line styles */
.x-tree-no-lines .x-tree-elbow{
	background:transparent;
}

.x-tree-no-lines .x-tree-elbow-end{
	background:transparent;
}

.x-tree-no-lines .x-tree-elbow-line{
	background:transparent;
}

/* Arrows */
.x-tree-arrows .x-tree-elbow{
	background:transparent;
}

.x-tree-arrows .x-tree-elbow-plus{
    background:transparent no-repeat 0 0;
}

.x-tree-arrows .x-tree-elbow-minus{
    background:transparent no-repeat -16px 0;
}

.x-tree-arrows .x-tree-elbow-end{
	background:transparent;
}

.x-tree-arrows .x-tree-elbow-end-plus{
    background:transparent no-repeat 0 0;
}

.x-tree-arrows .x-tree-elbow-end-minus{
    background:transparent no-repeat -16px 0;
}

.x-tree-arrows .x-tree-elbow-line{
	background:transparent;
}

.x-tree-arrows .x-tree-ec-over .x-tree-elbow-plus{
    background-position:-32px 0;
}

.x-tree-arrows .x-tree-ec-over .x-tree-elbow-minus{
    background-position:-48px 0;
}

.x-tree-arrows .x-tree-ec-over .x-tree-elbow-end-plus{
    background-position:-32px 0;
}

.x-tree-arrows .x-tree-ec-over .x-tree-elbow-end-minus{
    background-position:-48px 0;
}

.x-tree-elbow-plus, .x-tree-elbow-minus, .x-tree-elbow-end-plus, .x-tree-elbow-end-minus{
	cursor:pointer;
}

.ext-ie ul.x-tree-node-ct{
    font-size:0;
    line-height:0;
    zoom:1;
}

.x-tree-node{
	white-space: nowrap;
}

.x-tree-node-el {
    line-height:18px;
    cursor:pointer;
}

.x-tree-node a, .x-dd-drag-ghost a{
	text-decoration:none;
	-khtml-user-select:none;
	-moz-user-select:none;
    -webkit-user-select:ignore;
    -kthml-user-focus:normal;
    -moz-user-focus:normal;
    -moz-outline: 0 none;
    outline:0 none;
}

.x-tree-node a span, .x-dd-drag-ghost a span{
	text-decoration:none;
	padding:1px 3px 1px 2px;
}

.x-tree-node .x-tree-node-disabled .x-tree-node-icon{
	-moz-opacity: 0.5;
   opacity:.5;
   filter: alpha(opacity=50);
}

.x-tree-node .x-tree-node-inline-icon{
	background:transparent;
}

.x-tree-node a:hover, .x-dd-drag-ghost a:hover{
	text-decoration:none;
}

.x-tree-node div.x-tree-drag-insert-below{
 	 border-bottom:1px dotted;
}

.x-tree-node div.x-tree-drag-insert-above{
	 border-top:1px dotted;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-below{
 	 border-bottom:0 none;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-above{
	 border-top:0 none;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-below a{
 	 border-bottom:2px solid;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-above a{
	 border-top:2px solid;
}

.x-tree-node .x-tree-drag-append a span{
	 border:1px dotted;
}

.x-dd-drag-ghost .x-tree-node-indent, .x-dd-drag-ghost .x-tree-ec-icon{
	display:none !important;
}

/* Fix for ie rootVisible:false issue */
.x-tree-root-ct {
    zoom:1;
}
