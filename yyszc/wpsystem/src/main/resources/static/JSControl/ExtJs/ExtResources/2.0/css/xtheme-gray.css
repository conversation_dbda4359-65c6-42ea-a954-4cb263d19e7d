/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-panel {
    border-style: solid;
    border-color: #d0d0d0;
}
.x-panel-header {
    color:#333;
	border:1px solid #d0d0d0;
    background-image:url(../images/gray/panel/white-top-bottom.gif);
}

.x-panel-body {
    border-color:#d0d0d0;
}

.x-panel-bbar .x-toolbar {
    border-color:#d0d0d0;
}

.x-panel-tbar .x-toolbar {
    border-color:#d0d0d0;
}

.x-panel-tbar-noheader .x-toolbar, .x-panel-mc .x-panel-tbar .x-toolbar {
    border-color:#d0d0d0;
}
.x-panel-body-noheader, .x-panel-mc .x-panel-body {
    border-color:#d0d0d0;
}
.x-panel-tl .x-panel-header {
    color:#333;
}
.x-panel-tc {
	background-image:url(../images/gray/panel/top-bottom.gif);
}
.x-panel-tl {
	background-image:url(../images/gray/panel/corners-sprite.gif);
    border-color:#d0d0d0;
}
.x-panel-tr {
	background-image:url(../images/gray/panel/corners-sprite.gif);
}
.x-panel-bc {
	background-image:url(../images/gray/panel/top-bottom.gif);
}
.x-panel-bl {
	background-image:url(../images/gray/panel/corners-sprite.gif);
}
.x-panel-br {
	background-image:url(../images/gray/panel/corners-sprite.gif);
}
.x-panel-mc {
    background:#f1f1f1;
}
.x-panel-mc .x-panel-body {
    background:transparent;
    border: 0 none;
}
.x-panel-ml {
	background-image:url(../images/gray/panel/left-right.gif);
}
.x-panel-mr {
	background-image:url(../images/gray/panel/left-right.gif);
}

/* Tools */
.x-tool {
    background-image:url(../images/gray/panel/tool-sprites.gif);
}

/* Ghosting */
.x-panel-ghost {
    background:#e0e0e0;
}

.x-panel-ghost ul {
    border-color:#b0b0b0;
}

.x-grid-panel .x-panel-mc .x-panel-body {
    border:1px solid #d0d0d0;
}

/* Buttons */

.x-btn-left{
	background-image:url(../images/gray/button/btn-sprite.gif);
}
.x-btn-right{
	background-image:url(../images/gray/button/btn-sprite.gif);
}
.x-btn-center{
	background-image:url(../images/gray/button/btn-sprite.gif);
}

/* Layout classes */

.x-border-layout-ct {
    background:#f0f0f0;
}

.x-accordion-hd {
	background-image:url(../images/gray/panel/light-hd.gif);
}

.x-layout-collapsed{
    background-color:#eee;
    border-color:#e0e0e0;
}
.x-layout-collapsed-over{
	 background-color:#fbfbfb;
}


/* qtips */
.x-tip .x-tip-top {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-top-left {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-top-right {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-ft {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-ft-left {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-ft-right {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-bd-left {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}
.x-tip .x-tip-bd-right {
	background-image:url(../images/gray/qtip/tip-sprite.gif);
}

/* Toolbars */

.x-toolbar{
	border-color:#d0d0d0;
    background:#f0f4f5 url(../images/gray/toolbar/bg.gif) repeat-x top left;
}
.x-toolbar button {
    color:#444;
}
.x-toolbar .x-btn-menu-arrow-wrap .x-btn-center button {
    background-image:url(../images/gray/toolbar/btn-arrow.gif);
}
.x-toolbar .x-btn-text-icon .x-btn-menu-arrow-wrap .x-btn-center button {
    background-image:url(../images/gray/toolbar/btn-arrow.gif);
}
.x-toolbar .x-btn-over .x-btn-left{
	background-image:url(../images/gray/toolbar/tb-btn-sprite.gif);
}
.x-toolbar .x-btn-over .x-btn-right{
	background-image:url(../images/gray/toolbar/tb-btn-sprite.gif);
}
.x-toolbar .x-btn-over .x-btn-center{
	background-image:url(../images/gray/toolbar/tb-btn-sprite.gif);
}
.x-toolbar .x-btn-over button {
    color:#111;
}
.x-toolbar .x-btn-click .x-btn-left, .x-toolbar .x-btn-pressed .x-btn-left, .x-toolbar .x-btn-menu-active .x-btn-left{
	background-image:url(../images/gray/toolbar/tb-btn-sprite.gif);
}
.x-toolbar .x-btn-click .x-btn-right, .x-toolbar .x-btn-pressed .x-btn-right, .x-toolbar .x-btn-menu-active .x-btn-right{
	background-image:url(../images/gray/toolbar/tb-btn-sprite.gif);
}

.x-toolbar .x-btn-click .x-btn-center, .x-toolbar .x-btn-pressed .x-btn-center, .x-toolbar .x-btn-menu-active .x-btn-center{
	background-image:url(../images/gray/toolbar/tb-btn-sprite.gif);
}
.x-toolbar .ytb-sep {
	background-image: url(../images/default/grid/grid-split.gif);
}

/* Tabs */

.x-tab-panel-header, .x-tab-panel-footer {
	background: #EAEAEA;
	border-color:#d0d0d0;
}


.x-tab-panel-header {
	border-color:#d0d0d0;
}

.x-tab-panel-footer {
	border-color:#d0d0d0;
}

ul.x-tab-strip-top{
	background:#dbdbdb url(../images/gray/tabs/tab-strip-bg.gif) repeat-x left top;
	border-color:#d0d0d0;
    padding-top: 2px;
}

ul.x-tab-strip-bottom{
	background-image:url(../images/gray/tabs/tab-strip-btm-bg.gif);
	border-color:#d0d0d0;
}

.x-tab-strip span.x-tab-strip-text {
	color:#333;
}
.x-tab-strip-over span.x-tab-strip-text {
	color:#111;
}

.x-tab-strip-active span.x-tab-strip-text {
	color:#333;
}

.x-tab-strip-disabled .x-tabs-text {
	color:#aaaaaa;
}

.x-tab-strip-top .x-tab-right {
	background-image:url(../images/gray/tabs/tabs-sprite.gif);
}

.x-tab-strip-top .x-tab-left {
	background-image:url(../images/gray/tabs/tabs-sprite.gif);
}
.x-tab-strip-top .x-tab-strip-inner {
	background-image:url(../images/gray/tabs/tabs-sprite.gif);
}

.x-tab-strip-bottom .x-tab-right {
	background-image:url(../images/gray/tabs/tab-btm-inactive-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-left {
	background-image:url(../images/gray/tabs/tab-btm-inactive-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background-image:url(../images/gray/tabs/tab-btm-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background-image:url(../images/gray/tabs/tab-btm-left-bg.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
	background-image:url(../images/gray/tabs/tab-close.gif);
}
.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close:hover{
	background-image:url(../images/gray/tabs/tab-close.gif);
}

.x-tab-panel-body {
    border-color:#d0d0d0;
    background:#fff;
}
.x-tab-panel-bbar .x-toolbar {
    border-color: #d0d0d0;
}

.x-tab-panel-tbar .x-toolbar {
    border-color: #d0d0d0;
}

.x-tab-panel-header-plain .x-tab-strip-spacer {
    border-color:#d0d0d0;
    background: #eaeaea;
}

.x-tab-scroller-left {
    background-image: url(../images/gray/tabs/scroll-left.gif);
    border-color:#aeaeae;
}
.x-tab-scroller-right {
    background-image: url(../images/gray/tabs/scroll-right.gif);
    border-color:#aeaeae;
}

/* Window */

.x-window-proxy {
    background:#e0e0e0;
    border-color:#b0b0b0;
}

.x-window-tl .x-window-header {
    color:#555;
}
.x-window-tc {
	background-image:url(../images/gray/window/top-bottom.png);
}
.x-window-tl {
	background-image:url(../images/gray/window/left-corners.png);
}
.x-window-tr {
	background-image:url(../images/gray/window/right-corners.png);
}
.x-window-bc {
	background-image:url(../images/gray/window/top-bottom.png);
}
.x-window-bl {
	background-image:url(../images/gray/window/left-corners.png);
}
.x-window-br {
	background-image:url(../images/gray/window/right-corners.png);
}
.x-window-mc {
    border:1px solid #d0d0d0;
    background:#e8e8e8;
}

.x-window-ml {
	background-image:url(../images/gray/window/left-right.png);
}
.x-window-mr {
	background-image:url(../images/gray/window/left-right.png);
}
.x-panel-ghost .x-window-tl {
    border-color:#d0d0d0;
}
.x-panel-collapsed .x-window-tl {
    border-color:#d0d0d0;
}

.x-window-plain .x-window-mc {
    background: #e8e8e8;
    border-right:1px solid #eee;
    border-bottom:1px solid #eee;
    border-top:1px solid #d0d0d0;
    border-left:1px solid #d0d0d0;
}

.x-window-plain .x-window-body {
    border-left:1px solid #eee;
    border-top:1px solid #eee;
    border-bottom:1px solid #d0d0d0;
    border-right:1px solid #d0d0d0;
    background:transparent !important;
}

body.x-body-masked .x-window-mc, body.x-body-masked .x-window-plain .x-window-mc {
    background-color: #e4e4e4;
}


/* misc */
.x-html-editor-wrap {
    border-color:#d0d0d0;
}

/* Borders go last for specificity */
.x-panel-noborder .x-panel-body-noborder {
    border-width:0;
}

.x-panel-noborder .x-panel-header-noborder {
    border-width:0;
    border-bottom:1px solid #d0d0d0;
}

.x-panel-noborder .x-panel-tbar-noborder .x-toolbar {
    border-width:0;
    border-bottom:1px solid #d0d0d0;
}

.x-panel-noborder .x-panel-bbar-noborder .x-toolbar {
    border-width:0;
    border-top:1px solid #d0d0d0;
}

.x-window-noborder .x-window-mc {
    border-width:0;
}
.x-window-plain .x-window-body-noborder {
    border-width:0;
}

.x-tab-panel-noborder .x-tab-panel-body-noborder {
	border-width:0;
}

.x-tab-panel-noborder .x-tab-panel-header-noborder {
	border-top-width:0;
	border-left-width:0;
	border-right-width:0;
}

.x-tab-panel-noborder .x-tab-panel-footer-noborder {
	border-bottom-width:0;
	border-left-width:0;
	border-right-width:0;
}


.x-tab-panel-bbar-noborder .x-toolbar {
    border-width:0;
    border-top:1px solid #d0d0d0;
}

.x-tab-panel-tbar-noborder .x-toolbar {
    border-width:0;
    border-bottom:1px solid #d0d0d0;
}