/*!
 * Ext JS Library 3.1.1
 * Copyright(c) 2006-2010 Ext JS, LLC
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-form-spinner-proxy{
	/*background-color:#ff00cc;*/
}
.x-form-field-wrap .x-form-spinner-trigger {
    background:transparent url('../images/spinner.gif') no-repeat 0 0;
}
.x-form-field-wrap .x-form-spinner-overup{
    background-position:-17px 0;
}
.x-form-field-wrap .x-form-spinner-clickup{
    background-position:-34px 0;
}
.x-form-field-wrap .x-form-spinner-overdown{
    background-position:-51px 0;
}
.x-form-field-wrap .x-form-spinner-clickdown{
    background-position:-68px 0;
}


.x-trigger-wrap-focus .x-form-spinner-trigger{
    background-position:-85px 0;
}
.x-trigger-wrap-focus .x-form-spinner-overup{
    background-position:-102px 0;
}
.x-trigger-wrap-focus .x-form-spinner-clickup{
    background-position:-119px 0;
}
.x-trigger-wrap-focus .x-form-spinner-overdown{
    background-position:-136px 0;
}
.x-trigger-wrap-focus .x-form-spinner-clickdown{
    background-position:-153px 0;
}
.x-trigger-wrap-focus .x-form-trigger{
    border-bottom: 1px solid #7eadd9;
}

.x-form-field-wrap .x-form-spinner-splitter {
	line-height:1px;
	font-size:1px;
    background:transparent url('../images/spinner-split.gif') no-repeat 0 0;
	position:absolute;
	cursor: n-resize;
}
.x-trigger-wrap-focus .x-form-spinner-splitter{
    background-position:-14px 0;
}
