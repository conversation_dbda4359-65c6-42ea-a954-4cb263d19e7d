/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

/**
 * @class Ext.DatePicker
 * @extends Ext.Component
 * Simple date picker class.
 * @constructor
 * Create a new DatePicker
 * @param {Object} config The config object
 */
Ext.DatePicker = Ext.extend(Ext.Component, {
    /**
    * @cfg {String} todayText
    * The text to display on the button that selects the current date (defaults to "Today")
    */
    todayText: "Today",
    /**
    * @cfg {String} okText
    * The text to display on the ok button
    */
    okText: "&#160;OK&#160;", // &#160; to give the user extra clicking room
    /**
    * @cfg {String} cancelText
    * The text to display on the cancel button
    */
    cancelText: "Cancel",
    /**
    * @cfg {String} todayTip
    * The tooltip to display for the button that selects the current date (defaults to "{current date} (Spacebar)")
    */
    todayTip: "{0} (Spacebar)",
    /**
    * @cfg {Date} minDate
    * Minimum allowable date (JavaScript date object, defaults to null)
    */
    minDate: null,
    /**
    * @cfg {Date} maxDate
    * Maximum allowable date (JavaScript date object, defaults to null)
    */
    maxDate: null,
    /**
    * @cfg {String} minText
    * The error text to display if the minDate validation fails (defaults to "This date is before the minimum date")
    */
    minText: "This date is before the minimum date",
    /**
    * @cfg {String} maxText
    * The error text to display if the maxDate validation fails (defaults to "This date is after the maximum date")
    */
    maxText: "This date is after the maximum date",
    /**
    * @cfg {String} format
    * The default date format string which can be overriden for localization support.  The format must be
    * valid according to {@link Date#parseDate} (defaults to 'm/d/y').
    */
    format: "m/d/y",
    /**
    * @cfg {Array} disabledDays
    * An array of days to disable, 0-based. For example, [0, 6] disables Sunday and Saturday (defaults to null).
    */
    disabledDays: null,
    /**
    * @cfg {String} disabledDaysText
    * The tooltip to display when the date falls on a disabled day (defaults to "")
    */
    disabledDaysText: "",
    /**
    * @cfg {RegExp} disabledDatesRE
    * JavaScript regular expression used to disable a pattern of dates (defaults to null)
    */
    disabledDatesRE: null,
    /**
    * @cfg {String} disabledDatesText
    * The tooltip text to display when the date falls on a disabled date (defaults to "")
    */
    disabledDatesText: "",
    /**
    * @cfg {Boolean} constrainToViewport
    * True to constrain the date picker to the viewport (defaults to true)
    */
    constrainToViewport: true,
    /**
    * @cfg {Array} monthNames
    * An array of textual month names which can be overriden for localization support (defaults to Date.monthNames)
    */
    monthNames: Date.monthNames,
    /**
    * @cfg {Array} dayNames
    * An array of textual day names which can be overriden for localization support (defaults to Date.dayNames)
    */
    dayNames: Date.dayNames,
    /**
    * @cfg {String} nextText
    * The next month navigation button tooltip (defaults to 'Next Month (Control+Right)')
    */
    nextText: 'Next Month (Control+Right)',
    /**
    * @cfg {String} prevText
    * The previous month navigation button tooltip (defaults to 'Previous Month (Control+Left)')
    */
    prevText: 'Previous Month (Control+Left)',
    /**
    * @cfg {String} monthYearText
    * The header month selector tooltip (defaults to 'Choose a month (Control+Up/Down to move years)')
    */
    monthYearText: 'Choose a month (Control+Up/Down to move years)',
    /**
    * @cfg {Number} startDay
    * Day index at which the week should begin, 0-based (defaults to 0, which is Sunday)
    */
    startDay: 0,

    initComponent: function() {
        Ext.DatePicker.superclass.initComponent.call(this);

        this.value = this.value ?
                 this.value.clearTime() : new Date().clearTime();

        this.addEvents(
        /**
        * @event select
        * Fires when a date is selected
        * @param {DatePicker} this
        * @param {Date} date The selected date
        */
            'select'
        );

        if (this.handler) {
            this.on("select", this.handler, this.scope || this);
        }

        this.initDisabledDays();
    },

    // private
    initDisabledDays: function() {
        if (!this.disabledDatesRE && this.disabledDates) {
            var dd = this.disabledDates;
            var re = "(?:";
            for (var i = 0; i < dd.length; i++) {
                re += dd[i];
                if (i != dd.length - 1) re += "|";
            }
            this.disabledDatesRE = new RegExp(re + ")");
        }
    },

    /**
    * Sets the value of the date field
    * @param {Date} value The date to set
    */
    setValue: function(value) {
        var old = this.value;
        this.value = value.clearTime(true);
        if (this.el) {
            this.update(this.value);
        }
    },

    /**
    * Gets the current selected value of the date field
    * @return {Date} The selected date
    */
    getValue: function() {
        return this.value;
    },

    // private
    focus: function() {
        if (this.el) {
            this.update(this.activeDate);
        }
    },

    // private
    onRender: function(container, position) {
        var m = [
             '<table cellspacing="0">',
                '<tr><td class="x-date-left"><a href="#" title="', this.prevText, '">&#160;</a></td><td class="x-date-middle" align="center"></td><td class="x-date-right"><a href="#" title="', this.nextText, '">&#160;</a></td></tr>',
                '<tr><td colspan="3"><table class="x-date-inner" cellspacing="0"><thead><tr>'];
        var dn = this.dayNames;
        for (var i = 0; i < 7; i++) {
            var d = this.startDay + i;
            if (d > 6) {
                d = d - 7;
            }
            m.push("<th><span>", dn[d].substr(0, 1), "</span></th>");
        }
        m[m.length] = "</tr></thead><tbody><tr>";
        for (var i = 0; i < 42; i++) {
            if (i % 7 == 0 && i != 0) {
                m[m.length] = "</tr><tr>";
            }
            m[m.length] = '<td><a href="#" hidefocus="on" class="x-date-date" tabIndex="1"><em><span></span></em></a></td>';
        }
        m[m.length] = '</tr></tbody></table></td></tr><tr><td colspan="3" class="x-date-bottom" align="center"></td></tr></table><div class="x-date-mp"></div>';

        var el = document.createElement("div");
        el.className = "x-date-picker";
        el.innerHTML = m.join("");

        container.dom.insertBefore(el, position);

        this.el = Ext.get(el);
        this.eventEl = Ext.get(el.firstChild);

        new Ext.util.ClickRepeater(this.el.child("td.x-date-left a"), {
            handler: this.showPrevMonth,
            scope: this,
            preventDefault: true,
            stopDefault: true
        });

        new Ext.util.ClickRepeater(this.el.child("td.x-date-right a"), {
            handler: this.showNextMonth,
            scope: this,
            preventDefault: true,
            stopDefault: true
        });

        this.eventEl.on("mousewheel", this.handleMouseWheel, this);

        this.monthPicker = this.el.down('div.x-date-mp');
        this.monthPicker.enableDisplayMode('block');

        var kn = new Ext.KeyNav(this.eventEl, {
            "left": function(e) {
                e.ctrlKey ?
                    this.showPrevMonth() :
                    this.update(this.activeDate.add("d", -1));
            },

            "right": function(e) {
                e.ctrlKey ?
                    this.showNextMonth() :
                    this.update(this.activeDate.add("d", 1));
            },

            "up": function(e) {
                e.ctrlKey ?
                    this.showNextYear() :
                    this.update(this.activeDate.add("d", -7));
            },

            "down": function(e) {
                e.ctrlKey ?
                    this.showPrevYear() :
                    this.update(this.activeDate.add("d", 7));
            },

            "pageUp": function(e) {
                this.showNextMonth();
            },

            "pageDown": function(e) {
                this.showPrevMonth();
            },

            "enter": function(e) {
                e.stopPropagation();
                return true;
            },

            scope: this
        });

        this.eventEl.on("click", this.handleDateClick, this, { delegate: "a.x-date-date" });

        this.eventEl.addKeyListener(Ext.EventObject.SPACE, this.selectToday, this);

        this.el.unselectable();

        this.cells = this.el.select("table.x-date-inner tbody td");
        this.textNodes = this.el.query("table.x-date-inner tbody span");

        this.mbtn = new Ext.Button({
            text: "&#160;",
            tooltip: this.monthYearText,
            renderTo: this.el.child("td.x-date-middle", true)
        });

        this.mbtn.on('click', this.showMonthPicker, this);
        this.mbtn.el.child(this.mbtn.menuClassTarget).addClass("x-btn-with-menu");


        var today = (new Date()).dateFormat(this.format);
        this.todayBtn = new Ext.Button({
            renderTo: this.el.child("td.x-date-bottom", true),
            text: "清除",
            tooltip:"清除",
//            text: String.format(this.todayText, today),
//            tooltip: String.format(this.todayTip, today),
            handler: this.selectToday,
            scope: this
        });

        if (Ext.isIE) {
            this.el.repaint();
        }
        this.update(this.value);
    },

    createMonthPicker: function() {
        if (!this.monthPicker.dom.firstChild) {
            var buf = ['<table border="0" cellspacing="0">'];
            for (var i = 0; i < 6; i++) {
                buf.push(
                    '<tr><td class="x-date-mp-month"><a href="#">', this.monthNames[i].substr(0, 3), '</a></td>',
                    '<td class="x-date-mp-month x-date-mp-sep"><a href="#">', this.monthNames[i + 6].substr(0, 3), '</a></td>',
                    i == 0 ?
                    '<td class="x-date-mp-ybtn" align="center"><a class="x-date-mp-prev"></a></td><td class="x-date-mp-ybtn" align="center"><a class="x-date-mp-next"></a></td></tr>' :
                    '<td class="x-date-mp-year"><a href="#"></a></td><td class="x-date-mp-year"><a href="#"></a></td></tr>'
                );
            }
            buf.push(
                '<tr class="x-date-mp-btns"><td colspan="4"><button type="button" class="x-date-mp-ok">',
                    this.okText,
                    '</button><button type="button" class="x-date-mp-cancel">',
                    this.cancelText,
                    '</button></td></tr>',
                '</table>'
            );
            this.monthPicker.update(buf.join(''));
            this.monthPicker.on('click', this.onMonthClick, this);
            this.monthPicker.on('dblclick', this.onMonthDblClick, this);

            this.mpMonths = this.monthPicker.select('td.x-date-mp-month');
            this.mpYears = this.monthPicker.select('td.x-date-mp-year');

            this.mpMonths.each(function(m, a, i) {
                i += 1;
                if ((i % 2) == 0) {
                    m.dom.xmonth = 5 + Math.round(i * .5);
                } else {
                    m.dom.xmonth = Math.round((i - 1) * .5);
                }
            });
        }
    },

    showMonthPicker: function() {
        this.createMonthPicker();
        var size = this.el.getSize();
        this.monthPicker.setSize(size);
        this.monthPicker.child('table').setSize(size);

        this.mpSelMonth = (this.activeDate || this.value).getMonth();
        this.updateMPMonth(this.mpSelMonth);
        this.mpSelYear = (this.activeDate || this.value).getFullYear();
        this.updateMPYear(this.mpSelYear);

        this.monthPicker.slideIn('t', { duration: .2 });
    },

    updateMPYear: function(y) {
        this.mpyear = y;
        var ys = this.mpYears.elements;
        for (var i = 1; i <= 10; i++) {
            var td = ys[i - 1], y2;
            if ((i % 2) == 0) {
                y2 = y + Math.round(i * .5);
                td.firstChild.innerHTML = y2;
                td.xyear = y2;
            } else {
                y2 = y - (5 - Math.round(i * .5));
                td.firstChild.innerHTML = y2;
                td.xyear = y2;
            }
            this.mpYears.item(i - 1)[y2 == this.mpSelYear ? 'addClass' : 'removeClass']('x-date-mp-sel');
        }
    },

    updateMPMonth: function(sm) {
        this.mpMonths.each(function(m, a, i) {
            m[m.dom.xmonth == sm ? 'addClass' : 'removeClass']('x-date-mp-sel');
        });
    },

    selectMPMonth: function(m) {

    },

    onMonthClick: function(e, t) {
        e.stopEvent();
        var el = new Ext.Element(t), pn;
        if (el.is('button.x-date-mp-cancel')) {
            this.hideMonthPicker();
        }
        else if (el.is('button.x-date-mp-ok')) {
            this.update(new Date(this.mpSelYear, this.mpSelMonth, (this.activeDate || this.value).getDate()));
            this.hideMonthPicker();
        }
        else if (pn = el.up('td.x-date-mp-month', 2)) {
            this.mpMonths.removeClass('x-date-mp-sel');
            pn.addClass('x-date-mp-sel');
            this.mpSelMonth = pn.dom.xmonth;
        }
        else if (pn = el.up('td.x-date-mp-year', 2)) {
            this.mpYears.removeClass('x-date-mp-sel');
            pn.addClass('x-date-mp-sel');
            this.mpSelYear = pn.dom.xyear;
        }
        else if (el.is('a.x-date-mp-prev')) {
            this.updateMPYear(this.mpyear - 10);
        }
        else if (el.is('a.x-date-mp-next')) {
            this.updateMPYear(this.mpyear + 10);
        }
    },

    onMonthDblClick: function(e, t) {
        e.stopEvent();
        var el = new Ext.Element(t), pn;
        if (pn = el.up('td.x-date-mp-month', 2)) {
            this.update(new Date(this.mpSelYear, pn.dom.xmonth, (this.activeDate || this.value).getDate()));
            this.hideMonthPicker();
        }
        else if (pn = el.up('td.x-date-mp-year', 2)) {
            this.update(new Date(pn.dom.xyear, this.mpSelMonth, (this.activeDate || this.value).getDate()));
            this.hideMonthPicker();
        }
    },

    hideMonthPicker: function(disableAnim) {
        if (this.monthPicker) {
            if (disableAnim === true) {
                this.monthPicker.hide();
            } else {
                this.monthPicker.slideOut('t', { duration: .2 });
            }
        }
    },

    // private
    showPrevMonth: function(e) {
        this.update(this.activeDate.add("mo", -1));
    },

    // private
    showNextMonth: function(e) {
        this.update(this.activeDate.add("mo", 1));
    },

    // private
    showPrevYear: function() {
        this.update(this.activeDate.add("y", -1));
    },

    // private
    showNextYear: function() {
        this.update(this.activeDate.add("y", 1));
    },

    // private
    handleMouseWheel: function(e) {
        var delta = e.getWheelDelta();
        if (delta > 0) {
            this.showPrevMonth();
            e.stopEvent();
        } else if (delta < 0) {
            this.showNextMonth();
            e.stopEvent();
        }
    },

    // private
    handleDateClick: function(e, t) {
        e.stopEvent();
        if (t.dateValue && !Ext.fly(t.parentNode).hasClass("x-date-disabled")) {
            this.setValue(new Date(t.dateValue));
            this.fireEvent("select", this, this.value);
        }
    },

    // private
    selectToday: function() {
        //        this.setValue(new Date().clearTime());
        this.value = "";
        this.fireEvent("select", this, this.value);
    },

    // private
    update: function(date) {
        var vd = this.activeDate;
        this.activeDate = date;
        if (vd && this.el) {
            var t = date.getTime();
            if (vd.getMonth() == date.getMonth() && vd.getFullYear() == date.getFullYear()) {
                this.cells.removeClass("x-date-selected");
                this.cells.each(function(c) {
                    if (c.dom.firstChild.dateValue == t) {
                        c.addClass("x-date-selected");
                        setTimeout(function() {
                            try { c.dom.firstChild.focus(); } catch (e) { }
                        }, 50);
                        return false;
                    }
                });
                return;
            }
        }
        var days = date.getDaysInMonth();
        var firstOfMonth = date.getFirstDateOfMonth();
        var startingPos = firstOfMonth.getDay() - this.startDay;

        if (startingPos <= this.startDay) {
            startingPos += 7;
        }

        var pm = date.add("mo", -1);
        var prevStart = pm.getDaysInMonth() - startingPos;

        var cells = this.cells.elements;
        var textEls = this.textNodes;
        days += startingPos;

        // convert everything to numbers so it's fast
        var day = 86400000;
        var d = (new Date(pm.getFullYear(), pm.getMonth(), prevStart)).clearTime();
        var today = new Date().clearTime().getTime();
        var sel = date.clearTime().getTime();
        var min = this.minDate ? this.minDate.clearTime() : Number.NEGATIVE_INFINITY;
        var max = this.maxDate ? this.maxDate.clearTime() : Number.POSITIVE_INFINITY;
        var ddMatch = this.disabledDatesRE;
        var ddText = this.disabledDatesText;
        var ddays = this.disabledDays ? this.disabledDays.join("") : false;
        var ddaysText = this.disabledDaysText;
        var format = this.format;

        var setCellClass = function(cal, cell) {
            cell.title = "";
            var t = d.getTime();
            cell.firstChild.dateValue = t;
            if (t == today) {
                cell.className += " x-date-today";
                cell.title = cal.todayText;
            }
            if (t == sel) {
                cell.className += " x-date-selected";
                setTimeout(function() {
                    try { cell.firstChild.focus(); } catch (e) { }
                }, 50);
            }
            // disabling
            if (t < min) {
                cell.className = " x-date-disabled";
                cell.title = cal.minText;
                return;
            }
            if (t > max) {
                cell.className = " x-date-disabled";
                cell.title = cal.maxText;
                return;
            }
            if (ddays) {
                if (ddays.indexOf(d.getDay()) != -1) {
                    cell.title = ddaysText;
                    cell.className = " x-date-disabled";
                }
            }
            if (ddMatch && format) {
                var fvalue = d.dateFormat(format);
                if (ddMatch.test(fvalue)) {
                    cell.title = ddText.replace("%0", fvalue);
                    cell.className = " x-date-disabled";
                }
            }
        };

        var i = 0;
        for (; i < startingPos; i++) {
            textEls[i].innerHTML = (++prevStart);
            d.setDate(d.getDate() + 1);
            cells[i].className = "x-date-prevday";
            setCellClass(this, cells[i]);
        }
        for (; i < days; i++) {
            intDay = i - startingPos + 1;
            textEls[i].innerHTML = (intDay);
            d.setDate(d.getDate() + 1);
            cells[i].className = "x-date-active";
            setCellClass(this, cells[i]);
        }
        var extraDays = 0;
        for (; i < 42; i++) {
            textEls[i].innerHTML = (++extraDays);
            d.setDate(d.getDate() + 1);
            cells[i].className = "x-date-nextday";
            setCellClass(this, cells[i]);
        }

        this.mbtn.setText(this.monthNames[date.getMonth()] + " " + date.getFullYear());

        if (!this.internalRender) {
            var main = this.el.dom.firstChild;
            var w = main.offsetWidth;
            this.el.setWidth(w + this.el.getBorderWidth("lr"));
            Ext.fly(main).setWidth(w);
            this.internalRender = true;
            // opera does not respect the auto grow header center column
            // then, after it gets a width opera refuses to recalculate
            // without a second pass
            if (Ext.isOpera && !this.secondPass) {
                main.rows[0].cells[1].style.width = (w - (main.rows[0].cells[0].offsetWidth + main.rows[0].cells[2].offsetWidth)) + "px";
                this.secondPass = true;
                this.update.defer(10, this, [date]);
            }
        }
    },

    // private
    beforeDestroy: function() {
        this.mbtn.destroy();
        this.todayBtn.destroy();
    }

    /**
    * @cfg {String} autoEl @hide
    */
});
Ext.reg('datepicker', Ext.DatePicker);








Ext.ux.TimeField=function(config)
{
    //be true when reset

    this.dateTime=config.dateTime;
    Ext.ux.TimeField.superclass.constructor.call(this,config);
};

Ext.extend(Ext.ux.TimeField,Ext.form.TimeField, {
     
     /**
     * Clear any invalid styles/messages for this field
     */
    clearInvalid : function(){
        if(!this.rendered || this.preventMark){ // not rendered
            return;
        }
        this.el.removeClass(this.invalidClass);
        this.dateTime.clearInvalid();
        //checksys the other field for datetime
        //this.dateTime.df.isValid();
        
    },
    /**
     * Mark this field as invalid
     * @param {String} msg The validation message
     */
    markInvalid : function(msg){
        if(!this.rendered || this.preventMark){ // not rendered
            return;
        }
        this.el.addClass(this.invalidClass);
        msg = msg || this.invalidText;
        this.dateTime.markInvalid(msg);
    }
   
    
});


Ext.ux.DateField=function(config)
{
    //be true when reset
    this.isReset=false;
    this.dateTime=config.dateTime;
    Ext.ux.DateField.superclass.constructor.call(this,config);
};
 Ext.extend(Ext.ux.DateField,Ext.form.DateField,  {
     
     /**
     * Clear any invalid styles/messages for this field
     */
    clearInvalid : function(){
        if(!this.rendered || this.preventMark){ // not rendered
            return;
        }
        this.el.removeClass(this.invalidClass);
         this.dateTime.clearInvalid();
         //checksys the other field for datetime
        
        // this.dateTime.tf.isValid();
    },
    /**
     * Mark this field as invalid
     * @param {String} msg The validation message
     */
    markInvalid : function(msg){
        if(!this.rendered || this.preventMark){ // not rendered
            return;
        }
        this.el.addClass(this.invalidClass);
        msg = msg || this.invalidText;
         this.dateTime.markInvalid(msg);
    }
   
})



Ext.ux.DateTime = function(config) {

    // create DateField

    //message stack
    this.messages = new Array();
    var dateTime = this;
    var dateConfig = Ext.apply({}, {
        name: config.name + '-date'
            , format: config.dateFormat || Ext.form.DateField.prototype.format
            , width: config.dateWidth
            , readOnly:true
            , selectOnFocus: config.selectOnFocus
            , dateTime: dateTime
            , listeners: {
                blur: { scope: this, fn: this.onBlur }
                 , focus: { scope: this, fn: this.onFocus }
//                 , mouseover: { scope: this, fn: function() { alert('d'); } }
            }
    }, config.dateConfig);
    this.df = new Ext.ux.DateField(dateConfig);


    // create TimeField
    var timeConfig = Ext.apply({}, {
        name: config.name + '-time'
            , format: config.timeFormat || Ext.form.TimeField.prototype.format
            , width: config.timeWidth
            , selectOnFocus: config.selectOnFocus
            , dateTime: dateTime
            , listeners: {
                blur: { scope: this, fn: this.onBlur }
                 , focus: { scope: this, fn: this.onFocus }
            }
    }, config.timeConfig);
    this.tf = new Ext.ux.TimeField(timeConfig);
    Ext.ux.DateTime.superclass.constructor.call(this, config);


};



Ext.extend(Ext.ux.DateTime, Ext.form.Field, {
    /**
    * @cfg {String/Object} defaultAutoCreate DomHelper element spec
    * Let superclass to create hidden field instead of textbox. Hidden will be submittend to server
    */
    defaultAutoCreate: { tag: 'input', type: 'hidden' }
    /**
    * @cfg {Number} timeWidth Width of time field in pixels (defaults to 100)
    */
    , timeWidth: 100
    /**
    * @cfg {String} dtSeparator Date - Time separator. Used to split date and time (defaults to ' ' (space))
    */
    , dtSeparator: ' '
    /**
    * @cfg {String} hiddenFormat Format of datetime used to store value in hidden field
    * and submitted to server (defaults to 'Y-m-d H:i:s' that is mysql format)
    */
    , hiddenFormat: 'Y-m-d H:i:s'
    /**
    * @cfg {Boolean} otherToNow Set other field to now() if not explicly filled in (defaults to true)
    */
    , otherToNow: true
    /**
    * @cfg {Boolean} emptyToNow Set field value to now if on attempt to set empty value.
    * If it is true then setValue() sets value of field to current date and time (defaults to false)
    /**
    * @cfg {String} timePosition Where the time field should be rendered. 'right' is suitable for forms
    * and 'bellow' is suitable if the field is used as the grid editor (defaults to 'right')
    */
    , timePosition: 'right' // valid values:'bellow', 'right'
    /**
    * @cfg {String} dateFormat Format of DateField. Can be localized. (defaults to 'm/y/d')
    */
    , dateFormat: 'm/d/y'
    /**
    * @cfg {String} timeFormat Format of TimeField. Can be localized. (defaults to 'g:i A')
    */
    , timeFormat: 'g:i A'
    /**
    * @cfg {Object} dateConfig Config for DateField constructor.
    */
    /**
    * @cfg {Object} timeConfig Config for TimeField constructor.
    */

    // {{{
    /**
    * private
    * creates DateField and TimeField and installs the necessary event handlers
    */
    , initComponent: function() {
        // call parent initComponent
        Ext.ux.DateTime.superclass.initComponent.call(this);
        // relay events
        this.relayEvents(this.df, ['focus', 'specialkey', 'invalid', 'valid']);
        this.relayEvents(this.tf, ['focus', 'specialkey', 'invalid', 'valid']);


    } // eo function initComponent
    // }}}
    // {{{
    /**
    * private
    * Renders underlying DateField and TimeField and provides a workaround for side error icon bug
    */
    , onRender: function(ct, position) {
        // don't run more than once
        if (this.isRendered) {
            return;
        }

        // render underlying hidden field
        Ext.ux.DateTime.superclass.onRender.call(this, ct, position);

        // render DateField and TimeField
        // create bounding table
        var t;
        if ('bellow' === this.timePosition) {
            t = Ext.DomHelper.append(ct, { tag: 'table', style: 'border-collapse:collapse', children: [
                 { tag: 'tr', children: [{ tag: 'td', style: 'padding-bottom:1px', cls: 'ux-datetime-date'}] }
                , { tag: 'tr', children: [{ tag: 'td', cls: 'ux-datetime-time'}] }
            ]
            }, true);
        }
        else {
            t = Ext.DomHelper.append(ct, { tag: 'table', style: 'border-collapse:collapse', children: [
                { tag: 'tr', children: [
                    { tag: 'td', style: 'padding-right:4px', cls: 'ux-datetime-date' }, { tag: 'td', cls: 'ux-datetime-time' }
                ]
                }
            ]
            }, true);
        }

        this.tableEl = t;
        this.wrap = t.wrap({ cls: 'x-form-field-wrap' });
        this.wrap.on("mousedown", this.onMouseDown, this, { delay: 10 });

        // render DateField & TimeField
        this.df.render(t.child('td.ux-datetime-date'));
        this.tf.render(t.child('td.ux-datetime-time'));

        // workaround for IE trigger misalignment bug
        if (Ext.isIE && Ext.isStrict) {
            t.select('input').applyStyles({ top: 0 });
        }
        //        this.on('change', function() { alert("change"); }, this);
        this.on('specialkey', this.onSpecialKey, this);
        //        this.df.el.swallowEvent(['keydown', 'keypress']);
        //        this.tf.el.swallowEvent(['keydown', 'keypress']);
        var element = this;
//        AddEventListener(this.df.el.dom, "keyup", function(e) {
//            element.df.el.dom.value = e.srcElement.value;
//            element.updateDate();
//            element.updateHidden();
//        });
        AddEventListener(this.df.el.dom, "blur", function(e) {
            element.updateDate();
            element.updateHidden();
        });
        AddEventListener(this.tf.el.dom, "blur", function(e) {
            element.updateTime();
            element.updateHidden();
        });        

        // create icon for side invalid errorIcon
        if ('side' === this.msgTarget) {
            var elp = this.el.findParent('.x-form-element', 10, true);
            this.errorIcon = elp.createChild({ cls: 'x-form-invalid-icon' });

            this.df.errorIcon = this.errorIcon;
            this.tf.errorIcon = this.errorIcon;
        }

        // we're rendered flag
        this.isRendered = true;

    } // eo function onRender
    // }}}
    // {{{
    /**
    * private
    */
    , adjustSize: Ext.BoxComponent.prototype.adjustSize
    // }}}
    // {{{
    /**
    * private
    */
    , alignErrorIcon: function() {
        this.errorIcon.alignTo(this.tableEl, 'tl-tr', [2, 0]);
    }
    // }}}
    // {{{
    /**
    * private initializes internal dateValue
    */
    , initDateValue: function() {
        this.dateValue = this.otherToNow ? new Date() : new Date(1970, 0, 1, 0, 0, 0);
    }
    // }}}
    // {{{
    /**
    * Disable this component.
    * @return {Ext.Component} this
    */
    , disable: function() {
        if (this.isRendered) {
            this.df.disabled = this.disabled;
            this.df.onDisable();
            this.tf.onDisable();
        }
        this.disabled = true;
        this.df.disabled = true;
        this.tf.disabled = true;
        this.fireEvent("disable", this);
        return this;
    } // eo function disable
    // }}}
    // {{{
    /**
    * Enable this component.
    * @return {Ext.Component} this
    */
    , enable: function() {
        if (this.rendered) {
            this.df.onEnable();
            this.tf.onEnable();
        }
        this.disabled = false;
        this.df.disabled = false;
        this.tf.disabled = false;
        this.fireEvent("enable", this);
        return this;
    } // eo function enable
    // }}}
    // {{{
    /**
    * private Focus date filed
    */
    , focus: function() {
        this.df.focus();
    } // eo function focus
    // }}}
    // {{{
    /**
    * private
    */
    , getPositionEl: function() {
        return this.wrap;
    }
    // }}}
    // {{{
    /**
    * private
    */
    , getResizeEl: function() {
        return this.wrap;
    }
    // }}}
    // {{{
    /**
    * @return {Date/String} Returns value of this field
    */
    , getValue: function() {
        // create new instance of date
        return this.dateValue ? new Date(this.dateValue) : '';
    } // eo function getValue
    // }}}
    // {{{
    /**
    * @return {Boolean} true = valid, false = invalid
    * private Calls isValid methods of underlying DateField and TimeField and returns the result
    */
    , isValid: function() {
        return this.df.isValid() && this.tf.isValid();
    } // eo function isValid
    // }}}
    // {{{
    /**
    * Returns true if this component is visible
    * @return {boolean} 
    */
    , isVisible: function() {
        return this.df.rendered && this.df.getActionEl().isVisible();
    } // eo function isVisible
    // }}}
    // {{{
    /** 
    * private Handles blur event
    */
    , onBlur: function(f) {
        // called by both DateField and TimeField blur events
        //        alert("Blur");
        // revert focus to previous field if clicked in between
        if (this.wrapClick) {
            f.focus();
            this.wrapClick = false;
        }

        // update underlying value
        if (f === this.df) {
            this.updateDate();
        }
        else {
            this.updateTime();
        }
        this.updateHidden();

        // fire events later
        (function() {
            if (!this.df.hasFocus && !this.tf.hasFocus) {
                var v = this.getValue();
                if (String(v) !== String(this.startValue)) {
                    this.fireEvent("change", this, v, this.startValue);
                }
                this.hasFocus = false;
                this.fireEvent('blur', this);
            }
        }).defer(100, this);

    } // eo function onBlur
    // }}}
    // {{{
    /**
    * private Handles focus event
    */
    , onFocus: function() {
        if (!this.hasFocus) {
            this.hasFocus = true;
            this.startValue = this.getValue();
            this.fireEvent("focus", this);
        }
    }
    // }}}
    // {{{
    /**
    * private Just to prevent blur event when clicked in the middle of fields
    */
    , onMouseDown: function(e) {
        if (e.target.nodeName != undefined)
            this.wrapClick = 'td' === e.target.nodeName.toLowerCase();
    }
    // }}}
    // {{{
    /**
    * private
    * Handles Tab and Shift-Tab events
    */
    , onSpecialKey: function(t, e) {
        var key = e.getKey();
        if (key == e.TAB) {
            if (t === this.df && !e.shiftKey) {
                e.stopEvent();
                this.tf.focus();
            }
            if (t === this.tf && e.shiftKey) {
                e.stopEvent();
                this.df.focus();
            }
        }
        // otherwise it misbehaves in editor grid
        if (key == e.ENTER) {
            this.updateValue();
        }

        //        if (key != e.TAB && key != e.ENTER) {
        //            alert(key);
        //            this.updateValue();
        //        }

    } // eo function onSpecialKey
    // }}}
    // {{{
    /**
    * private Sets the value of DateField
    */
    , setDate: function(date) {
        this.df.setValue(date);
    } // eo function setDate
    // }}}
    // {{{
    /** 
    * private Sets the value of TimeField
    */
    , setTime: function(date) {
        this.tf.setValue(date);
    } // eo function setTime
    // }}}
    // {{{
    /**
    * private
    * Sets correct sizes of underlying DateField and TimeField
    * With workarounds for IE bugs
    */
    , setSize: function(w, h) {
        if (!w) {
            return;
        }
        if ('bellow' == this.timePosition) {
            this.df.setSize(w, h);
            this.tf.setSize(w, h);
            if (Ext.isIE) {
                this.df.el.up('td').setWidth(w);
                this.tf.el.up('td').setWidth(w);
            }
        }
        else {
            this.df.setSize(w - this.timeWidth - 4, h);
            this.tf.setSize(this.timeWidth, h);

            if (Ext.isIE) {
                this.df.el.up('td').setWidth(w - this.timeWidth - 4);
                this.tf.el.up('td').setWidth(this.timeWidth);
            }
        }
    } // eo function setSize
    // }}}
    // {{{
    /**
    * @param {Mixed} val Value to set
    * Sets the value of this field
    */
    , setValue: function(val) {
        //alert(val.time);
        if (!val && true === this.emptyToNow) {
            this.setValue(new Date());
            return;
        }
        else if (!val) {
            this.setDate('');
            this.setTime('');
            this.updateValue();
            return;
        }
        val = val ? val : new Date(1970, 0, 1, 0, 0, 0);
        var da, time;
        if (val instanceof Date) {
            this.setDate(val);
            this.setTime(val);
            this.dateValue = new Date(val);
        } else if (val instanceof Object && val.time) {
            var dtDate = new Date(parseInt(val.time));
            this.setDate(dtDate);
            this.setTime(dtDate);
            this.dateValue = new Date(dtDate);
        }
        else {
            da = val.split(this.dtSeparator);
            this.setDate(da[0]);
            if (da[1]) {
                this.setTime(da[1]);
            }
        }
        this.updateValue();
    } // eo function setValue
    // }}}
    // {{{
    /**
    * Hide or show this component by boolean
    * @return {Ext.Component} this
    */
    , setVisible: function(visible) {
        if (visible) {

            this.df.show();
            this.tf.show();


        } else {
            this.df.hide();
            this.tf.hide();
        }
        return this;
    } // eo function setVisible
    // }}}
    //{{{
    ,
    reset: function() {
        this.df.reset();
        this.tf.reset();
        this.clearInvalid();
    },
    show: function() {

        return this.setVisible(true);
    } // eo function show
    //}}}
    //{{{
    , hide: function() {

        return this.setVisible(false);
    } // eo function hide
    //}}}
    // {{{
    /**
    * private Updates the date part
    */
    , updateDate: function() {

        var d = this.df.getValue();
        if (d) {
            if (!(this.dateValue instanceof Date)) {
                this.initDateValue();
                if (!this.tf.getValue()) {
                    this.setTime(this.dateValue);
                }
            }
            this.dateValue.setMonth(0); // because of leap years
            this.dateValue.setFullYear(d.getFullYear());
            this.dateValue.setMonth(d.getMonth());
            this.dateValue.setDate(d.getDate());
        }
        else {
            this.dateValue = '';
            this.setTime('');
        }
    } // eo function updateDate
    // }}}
    // {{{
    /**
    * private
    * Updates the time part
    */
    , updateTime: function() {
        var t = this.tf.getValue();
        if (t && !(t instanceof Date)) {
            t = Date.parseDate(t, this.tf.format);
        }
        if (t && !this.df.getValue()) {
            this.initDateValue();
            this.setDate(this.dateValue);
        }
        if (this.dateValue instanceof Date) {
            if (t) {
                this.dateValue.setHours(t.getHours());
                this.dateValue.setMinutes(t.getMinutes());
                this.dateValue.setSeconds(t.getSeconds());
            }
            else {
                this.dateValue.setHours(0);
                this.dateValue.setMinutes(0);
                this.dateValue.setSeconds(0);
            }
        }
    } // eo function updateTime
    // }}}
    // {{{
    /**
    * private Updates the underlying hidden field value
    */
    , updateHidden: function() {
        if (this.isRendered) {
            var value = this.dateValue instanceof Date ? this.dateValue.format(this.hiddenFormat) : '';
            this.el.dom.value = value;
        }
    }
    // }}}
    // {{{
    /**
    * private Updates all of Date, Time and Hidden
    */
    , updateValue: function() {

        this.updateDate();
        this.updateTime();
        this.updateHidden();

        return;
    } // eo function updateValue
    // }}}
    // {{{
    /**
    * @return {Boolean} true = valid, false = invalid
    * callse validate methods of DateField and TimeField
    */
    , validate: function() {
        return this.df.validate() && this.tf.validate();
    } // eo function validate
    // }}}
    // {{{
    /**
    * Returns renderer suitable to render this field
    * @param {Object} Column model config
    */
    , renderer: function(field) {

        var format = field.editor.dateFormat || Ext.ux.DateTime.prototype.dateFormat;
        format += ' ' + (field.editor.timeFormat || Ext.ux.DateTime.prototype.timeFormat);
        var renderer = function(val) {
            var retval = Ext.util.Format.date(val, format);
            return retval;
        };
        return renderer;
    },
    markInvalid: function(msg) {
        this.messages.push(msg);
        Ext.ux.DateTime.superclass.markInvalid.call(this, msg);
    },
    clearInvalid: function() {
        this.messages.pop();
        if (this.messages.length == 0) {
            Ext.ux.DateTime.superclass.clearInvalid.call(this);
        }
        else {
            var msg = this.messages.pop();
            this.markInvalid(msg);
        }
    }


    // }}}

});          // eo extend

// register xtype
Ext.reg('xdatetime', Ext.ux.DateTime);








//服务端返回来的日期时间是以何种格式返回，例如java中的java.util.Date形式的，将使用下面的函数作为convert函数(可以在form的reader中设置)： 
//function dateTimeConvertor(sTimestamp) { 

//if (typeof sTimestamp == "string") 
//{ 
//sTimestamp = parseInt(sTimestamp); 
//} 
//else if(typeof sTimestamp=="object"&&sTimestamp&&sTimestamp.time) 
//{ 
//sTimestamp=parseInt(sTimestamp.time); 
//} 
//if(sTimestamp) 
//{ 
//var dtDate = new Date(sTimestamp); 
//return Ext.util.Format.date(dtDate,'Y-m-d H:i:s'); 
//} 
//else return ''; 
//}



//			            xtype: 'xdatetime',
//			            //vtype: 'customtextfield', //'alphanum',
//			            fieldLabel: '领用日期',
//			            name: 'TakeDate',
//			            blankText: '领用日期不能为空',
//			            emptyText: '必填',
//			            allowBlank: false,
//			            anchor: '98%',
//			            timeFormat:'H:i:s'
//                        ,timeConfig: {
//                             altFormats:'H:i:s'
//                            ,allowBlank:true    
//                        }
//                        ,dateFormat:'Y-n-d'
//                        ,dateConfig: {
//                             altFormats:'Y-m-d|Y-n-d'
//                            ,allowBlank:true    
//                        }