/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-date-picker {
    border: 1px solid;
    border-top:0 none;
	position:relative;
}

.x-date-picker a {
    -moz-outline:0 none;
    outline:0 none;
}

.x-date-inner, .x-date-inner td, .x-date-inner th{
    border-collapse:separate;
}

.x-date-middle,.x-date-left,.x-date-right {
	background: repeat-x 0 -83px;
	overflow:hidden;
}

.x-date-middle .x-btn-tc,.x-date-middle .x-btn-tl,.x-date-middle .x-btn-tr,
.x-date-middle .x-btn-mc,.x-date-middle .x-btn-ml,.x-date-middle .x-btn-mr,
.x-date-middle .x-btn-bc,.x-date-middle .x-btn-bl,.x-date-middle .x-btn-br{
	background:transparent !important;
    vertical-align:middle;
}

.x-date-middle .x-btn-mc em.x-btn-arrow {
    background:transparent no-repeat right 0;
}

.x-date-right, .x-date-left {
    width:18px;
}

.x-date-right{
    text-align:right;
}

.x-date-middle {
    padding-top:2px;
    padding-bottom:2px;
    width:130px; /* FF3 */
}

.x-date-right a, .x-date-left a{
    display:block;
    width:16px;
	height:16px;
	background-position: center;
	background-repeat: no-repeat;
	cursor:pointer;
    -moz-opacity: 0.6;
    opacity:.6;
    filter: alpha(opacity=60);
}

.x-date-right a:hover, .x-date-left a:hover{
    -moz-opacity: 1;
    opacity:1;
    filter: alpha(opacity=100);
}

.x-item-disabled .x-date-right a:hover, .x-item-disabled .x-date-left a:hover{
    -moz-opacity: 0.6;
    opacity:.6;
    filter: alpha(opacity=60);
}

.x-date-right a {
    margin-right:2px;
    text-decoration:none !important;
}

.x-date-left a{
    margin-left:2px;
    text-decoration:none !important;
}

table.x-date-inner {
    width: 100%;
    table-layout:fixed;
}

.ext-webkit table.x-date-inner{
    /* Fix for webkit browsers */
    width: 175px;
}


.x-date-inner th {
    width:25px;
}

.x-date-inner th {
    background: repeat-x left top;
    text-align:right !important;
	border-bottom: 1px solid;
	cursor:default;
    padding:0;
    border-collapse:separate;
}

.x-date-inner th span {
    display:block;
    padding:2px;
    padding-right:7px;
}

.x-date-inner td {
    border: 1px solid;
	text-align:right;
    padding:0;
}

.x-date-inner a {
    padding:2px 5px;
    display:block;
	text-decoration:none;
    text-align:right;
    zoom:1;
}

.x-date-inner .x-date-active{
	cursor:pointer;
	color:black;
}

.x-date-inner .x-date-selected a{
	background: repeat-x left top;
	border:1px solid;
    padding:1px 4px;
}

.x-date-inner .x-date-today a{
	border: 1px solid;
    padding:1px 4px;
}

.x-date-inner .x-date-prevday a,.x-date-inner .x-date-nextday a {
    text-decoration:none !important;
}

.x-date-bottom {
    padding:4px;
    border-top: 1px solid;
    background: repeat-x left top;
}

.x-date-inner a:hover, .x-date-inner .x-date-disabled a:hover{
    text-decoration:none !important;
}

.x-item-disabled .x-date-inner a:hover{
    background: none;
}

.x-date-inner .x-date-disabled a {
	cursor:default;
}

.x-date-menu .x-menu-item {
	padding:1px 24px 1px 4px;
	white-space: nowrap;
}

.x-date-menu .x-menu-item .x-menu-item-icon {
    width:10px;
    height:10px;
    margin-right:5px;
    background-position:center -4px !important;
}

.x-date-mp {
	position:absolute;
	left:0;
	top:0;
	display:none;
}

.x-date-mp td {
    padding:2px;
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

td.x-date-mp-month,td.x-date-mp-year,td.x-date-mp-ybtn {
    border: 0 none;
	text-align:center;
	vertical-align: middle;
	width:25%;
}

.x-date-mp-ok {
	margin-right:3px;
}

.x-date-mp-btns button {
	text-decoration:none;
	text-align:center;
	text-decoration:none !important;
	border:1px solid;
	padding:1px 3px 1px;
	cursor:pointer;
}

.x-date-mp-btns {
	background: repeat-x left top;
}

.x-date-mp-btns td {
	border-top: 1px solid;
    text-align:center;
}

td.x-date-mp-month a,td.x-date-mp-year a {
	display:block;
	padding:2px 4px;
	text-decoration:none;
	text-align:center;
}

td.x-date-mp-month a:hover,td.x-date-mp-year a:hover {
	text-decoration:none;
	cursor:pointer;
}

td.x-date-mp-sel a {
	padding:1px 3px;
	background: repeat-x left top;
	border:1px solid;
}

.x-date-mp-ybtn a {
    overflow:hidden;
    width:15px;
    height:15px;
    cursor:pointer;
    background:transparent no-repeat;
    display:block;
    margin:0 auto;
}

.x-date-mp-ybtn a.x-date-mp-next {
    background-position:0 -120px;
}

.x-date-mp-ybtn a.x-date-mp-next:hover {
    background-position:-15px -120px;
}

.x-date-mp-ybtn a.x-date-mp-prev {
    background-position:0 -105px;
}

.x-date-mp-ybtn a.x-date-mp-prev:hover {
    background-position:-15px -105px;
}

.x-date-mp-ybtn {
   text-align:center;
}

td.x-date-mp-sep {
   border-right:1px solid;
}