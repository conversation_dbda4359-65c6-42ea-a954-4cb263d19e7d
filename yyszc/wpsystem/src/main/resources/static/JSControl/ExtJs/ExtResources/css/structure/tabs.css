/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-tab-panel {
    overflow:hidden;
}

.x-tab-panel-header, .x-tab-panel-footer {
	border: 1px solid;
    overflow:hidden;
    zoom:1;
}

.x-tab-panel-header {
	border: 1px solid;
	padding-bottom: 2px;
}

.x-tab-panel-footer {
	border: 1px solid;
	padding-top: 2px;
}

.x-tab-strip-wrap {
	width:100%;
    overflow:hidden;
    position:relative;
    zoom:1;
}

ul.x-tab-strip {
	display:block;
    width:5000px;
    zoom:1;
}

ul.x-tab-strip-top{
	padding-top: 1px;
	background: repeat-x bottom;
	border-bottom: 1px solid;
}

ul.x-tab-strip-bottom{
	padding-bottom: 1px;
	background: repeat-x top;
	border-top: 1px solid;
	border-bottom: 0 none;
}

.x-tab-panel-header-plain .x-tab-strip-top {
    background:transparent !important;
    padding-top:0 !important;
}

.x-tab-panel-header-plain {
    background:transparent !important;
    border-width:0 !important;
    padding-bottom:0 !important;
}

.x-tab-panel-header-plain .x-tab-strip-spacer,
.x-tab-panel-footer-plain .x-tab-strip-spacer {
    border:1px solid;
    height:2px;
    font-size:1px;
    line-height:1px;
}

.x-tab-panel-header-plain .x-tab-strip-spacer {
    border-top: 0 none;
}

.x-tab-panel-footer-plain .x-tab-strip-spacer {
    border-bottom: 0 none;
}

.x-tab-panel-footer-plain .x-tab-strip-bottom {
    background:transparent !important;
    padding-bottom:0 !important;
}

.x-tab-panel-footer-plain {
    background:transparent !important;
    border-width:0 !important;
    padding-top:0 !important;
}

.ext-border-box .x-tab-panel-header-plain .x-tab-strip-spacer,
.ext-border-box .x-tab-panel-footer-plain .x-tab-strip-spacer {
    height:3px;
}

ul.x-tab-strip li {
    float:left;
    margin-left:2px;
}

ul.x-tab-strip li.x-tab-edge {
    float:left;
    margin:0 !important;
    padding:0 !important;
    border:0 none !important;
    font-size:1px !important;
    line-height:1px !important;
    overflow:hidden;
    zoom:1;
    background:transparent !important;
    width:1px;
}

.x-tab-strip a, .x-tab-strip span, .x-tab-strip em {
	display:block;
}

.x-tab-strip a {
	text-decoration:none !important;
	-moz-outline: none;
	outline: none;
	cursor:pointer;
}

.x-tab-strip-inner {
    overflow:hidden;
	text-overflow: ellipsis;
}

.x-tab-strip span.x-tab-strip-text {
	white-space: nowrap;
	cursor:pointer;
    padding:4px 0;
}

.x-tab-strip-top .x-tab-with-icon .x-tab-right {
    padding-left:6px;
}

.x-tab-strip .x-tab-with-icon span.x-tab-strip-text {
	padding-left:20px;
    background-position: 0 3px;
    background-repeat: no-repeat;
}

.x-tab-strip-active, .x-tab-strip-active a.x-tab-right {
    cursor:default;
}

.x-tab-strip-active span.x-tab-strip-text {
	cursor:default;
}

.x-tab-strip-disabled .x-tabs-text {
	cursor:default;
}

.x-tab-panel-body {
    overflow:hidden;
}

.x-tab-panel-bwrap {
    overflow:hidden;
}

.ext-ie .x-tab-strip .x-tab-right {
    position:relative;
}

.x-tab-strip-top .x-tab-strip-active .x-tab-right {
    margin-bottom:-1px;
}

/*
 * Horrible hack for IE8 in quirks mode
 */
.ext-ie8 ul.x-tab-strip li {
    position: relative;
}
.ext-ie8 .x-tab-strip .x-tab-right{
    margin-bottom: 0 !important;
    top: 1px;
}
.ext-ie8 ul.x-tab-strip-top {
    padding-top: 0;
}
.ext-ie8 .x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
    top:4px;
}
.ext-ie8 .x-tab-strip-bottom .x-tab-right{
    top:0;
}


.x-tab-strip-top .x-tab-strip-active .x-tab-right span.x-tab-strip-text {
    padding-bottom:5px;
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
    margin-top:-1px;
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right span.x-tab-strip-text {
    padding-top:5px;
}

.x-tab-strip-top .x-tab-right {
	background: transparent no-repeat 0 -51px;
    padding-left:10px;
}

.x-tab-strip-top .x-tab-left {
	background: transparent no-repeat right -351px;
    padding-right:10px;
}

.x-tab-strip-top .x-tab-strip-inner {
	background: transparent repeat-x 0 -201px;
}

.x-tab-strip-top .x-tab-strip-over .x-tab-right {
	 background-position:0 -101px;
}

.x-tab-strip-top .x-tab-strip-over .x-tab-left {
	 background-position:right -401px;
}

.x-tab-strip-top .x-tab-strip-over .x-tab-strip-inner {
	 background-position:0 -251px;
}

.x-tab-strip-top .x-tab-strip-active .x-tab-right {
	background-position: 0 0;
}

.x-tab-strip-top .x-tab-strip-active .x-tab-left {
	background-position: right -301px;
}

.x-tab-strip-top .x-tab-strip-active .x-tab-strip-inner {
	background-position: 0 -151px;
}

.x-tab-strip-bottom .x-tab-right {
	background: no-repeat bottom right;
}

.x-tab-strip-bottom .x-tab-left {
	background: no-repeat bottom left;
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background: no-repeat bottom right;
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background: no-repeat bottom left;
}

.x-tab-strip-bottom .x-tab-left {
    margin-right: 3px;
    padding:0 10px;
}

.x-tab-strip-bottom .x-tab-right {
    padding:0;
}

.x-tab-strip .x-tab-strip-close {
    display:none;
}

.x-tab-strip-closable {
    position:relative;
}

.x-tab-strip-closable .x-tab-left {
    padding-right:19px;
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
    opacity:.6;
    -moz-opacity:.6;
    background-repeat:no-repeat;
    display:block;
	width:11px;
    height:11px;
    position:absolute;
    top:3px;
    right:3px;
    cursor:pointer;
    z-index:2;
}

.x-tab-strip .x-tab-strip-active a.x-tab-strip-close {
    opacity:.8;
    -moz-opacity:.8;
}
.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close:hover{
    opacity:1;
    -moz-opacity:1;
}

.x-tab-panel-body {
    border: 1px solid;
}

.x-tab-panel-body-top {
    border-top: 0 none;
}

.x-tab-panel-body-bottom {
    border-bottom: 0 none;
}

.x-tab-scroller-left {
    background: transparent no-repeat -18px 0;
    border-bottom: 1px solid;
    width:18px;
    position:absolute;
    left:0;
    top:0;
    z-index:10;
    cursor:pointer;
}
.x-tab-scroller-left-over {
    background-position: 0 0;
}

.x-tab-scroller-left-disabled {
    background-position: -18px 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}

.x-tab-scroller-right {
    background: transparent no-repeat 0 0;
    border-bottom: 1px solid;
    width:18px;
    position:absolute;
    right:0;
    top:0;
    z-index:10;
    cursor:pointer;
}

.x-tab-scroller-right-over {
    background-position: -18px 0;
}

.x-tab-scroller-right-disabled {
    background-position: 0 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}

.x-tab-scrolling-bottom .x-tab-scroller-left, .x-tab-scrolling-bottom .x-tab-scroller-right{
    margin-top: 1px;
}

.x-tab-scrolling .x-tab-strip-wrap {
    margin-left:18px;
    margin-right:18px;
}

.x-tab-scrolling {
    position:relative;    
}

.x-tab-panel-bbar .x-toolbar {
    border:1px solid;
    border-top:0 none;
    overflow:hidden;
    padding:2px;
}

.x-tab-panel-tbar .x-toolbar {
    border:1px solid;
    border-top:0 none;
    overflow:hidden;
    padding:2px;
}