@CHARSET "UTF-8";

.ext-ux-uploaddialog-addbtn
{
	background: url('../images/file-add.gif') no-repeat left center !important;
}

.ext-ux-uploaddialog-removebtn
{
	background: url('../images/file-remove.gif') no-repeat left center !important;
}

.ext-ux-uploaddialog-resetbtn
{
	background: url('../images/reset.gif') no-repeat left center !important;
}

.ext-ux-uploaddialog-uploadstartbtn
{
	background: url('../images/upload-start.gif') no-repeat left center !important;
}

.ext-ux-uploaddialog-uploadstopbtn
{
	background: url('../images/upload-stop.gif') no-repeat left center !important;
}

.ext-ux-uploaddialog-indicator-stoped
{
	width: 16px;
	height: 16px;
	background: url('../images/done.gif') no-repeat center center;
}

.ext-ux-uploaddialog-indicator-processing
{
	width: 16px;
	height: 16px;	
	background: url('../images/loading.gif') no-repeat center center;
}

.ext-ux-uploaddialog-state
{
	text-align: center;
	background-position: center center;
	background-repeat: no-repeat;
}

/* Queued */
.ext-ux-uploaddialog-state-0
{
	background-image: url('../images/uncheck.gif');
}

/* Finished */
.ext-ux-uploaddialog-state-1
{
	background-image: url('../images/check.gif');
}

/* Failed */
.ext-ux-uploaddialog-state-2
{
	background-image: url('../images/failed.gif');
}

/* Processing */
.ext-ux-uploaddialog-state-3
{
	background-image: url('../images/file-uploading.gif');	
}

/* Progress bar fix */
.ext-ie7 .ext-ux-uploaddialog-dialog .x-progress-bar .x-progress-text div
{
	display: none;
}

.ext-ie7 .ext-ux-uploaddialog-dialog .x-progress-text-back
{
	position: absolute;
	left: 0px;
	right: 0px;
}

.ext-ie7 .ext-ux-uploaddialog-dialog .x-progress-text-back div
{
	width: auto !important;
	white-space: nowrap;
}