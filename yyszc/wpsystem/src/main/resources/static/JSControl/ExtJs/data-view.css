/*!
 * Ext JS Library 3.0.0
 * Copyright(c) 2006-2009 Ext JS, LLC
 * <EMAIL>
 * http://www.extjs.com/license
 */
#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .x-panel-body{
	background: white;
	font: 11px Arial, Helvetica, sans-serif;
}
#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .thumb{
	/*background: #dddddd;*/
	padding: 3px;
}
#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .thumb img{
	height: 60px;
	width: 60px;
}
#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .thumb-wrap{
	float: left;
	margin: 4px;
	margin-right: 0;
	padding: 5px;
}
#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .thumb-wrap span{
	display: block;
	overflow: hidden;
	text-align: center;
}

#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .x-view-over{
    border:1px solid #dddddd;
    /*background: #efefef url(../../ExtResources/images/default/grid/row-over.gif) repeat-x left top;*/
	padding: 4px;
}

#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .x-view-selected{
	background: #eff5fb url(../../Image/selected.gif) no-repeat right bottom;
	border:1px solid #99bbe8;
	padding: 4px;
}
#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .x-view-selected .thumb{
	background:transparent;
}

#SystemManage_DocumentDirectoryManage_IdDocumentDirectoryPanel .loading-indicator {
	font-size:11px;
	background-image:url('./ExtResources/images/default/grid/loading.gif');
	background-repeat: no-repeat;
	background-position: left;
	padding-left:20px;
	margin:10px; 
}
