/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-window-dlg .x-window-body {
    border:0 none !important;
    padding:5px 10px;
    overflow:hidden !important;
}

.x-window-dlg .x-window-mc {
    border:0 none !important;
}

.x-window-dlg .ext-mb-input {
    margin-top:4px;
    width:95%;
}

.x-window-dlg .ext-mb-textarea {
    margin-top:4px;
}

.x-window-dlg .x-progress-wrap {
    margin-top:4px;
}

.ext-ie .x-window-dlg .x-progress-wrap {
    margin-top:6px;
}

.x-window-dlg .x-msg-box-wait {
    background:transparent no-repeat left;
    display:block;
    width:300px;
    padding-left:18px;
    line-height:18px;
}

.x-window-dlg .ext-mb-icon {
    float:left;
    width:47px;
    height:32px;
}

.ext-ie .x-window-dlg .ext-mb-icon {
    width:44px; /* 3px IE margin issue */
}

.x-window-dlg .x-dlg-icon .ext-mb-content{
    zoom: 1; margin-left: 47px;
}

.x-window-dlg .ext-mb-info, .x-window-dlg .ext-mb-warning, .x-window-dlg .ext-mb-question, .x-window-dlg .ext-mb-error {
    background:transparent no-repeat top left;
}

.ext-gecko2 .ext-mb-fix-cursor {
    overflow:auto;
}