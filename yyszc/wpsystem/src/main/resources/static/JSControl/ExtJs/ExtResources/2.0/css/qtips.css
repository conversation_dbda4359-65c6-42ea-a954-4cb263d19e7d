/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-tip{
	position: absolute;
	top: 0;
    left:0;
    visibility: hidden;
	z-index: 20000;
    border:0 none;
}
.x-tip .x-tip-close{
	background-image: url(../images/default/qtip/close.gif);
	height: 15px;
	float:right;
	width: 15px;
    margin:0 0 2px 2px;
    cursor:pointer;
    display:none;
}
.x-tip .x-tip-tc {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -62px;
	padding-top:3px;
    overflow:hidden;
    zoom:1;
}
.x-tip .x-tip-tl {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 0;
	padding-left:6px;
    overflow:hidden;
    zoom:1;
}
.x-tip .x-tip-tr {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat right 0;
	padding-right:6px;
    overflow:hidden;
    zoom:1;
}
.x-tip .x-tip-bc {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -121px;
	height:3px;
    overflow:hidden;
}
.x-tip .x-tip-bl {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -59px;
	padding-left:6px;
    zoom:1;
}
.x-tip .x-tip-br {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat right -59px;
	padding-right:6px;
    zoom:1;
}
.x-tip .x-tip-mc {
    border:0 none;
    font: normal 11px tahoma,arial,helvetica,sans-serif;
}
.x-tip .x-tip-ml {
	background: #fff url(../images/default/qtip/tip-sprite.gif) no-repeat 0 -124px;
	padding-left:6px;
    zoom:1;
}
.x-tip .x-tip-mr {
	background: transparent url(../images/default/qtip/tip-sprite.gif) no-repeat right -124px;
	padding-right:6px;
    zoom:1;
}
.ext-ie .x-tip .x-tip-header,.ext-ie .x-tip .x-tip-tc {
    font-size:0;
    line-height:0;
}
.x-tip .x-tip-header-text {
    font: bold 11px tahoma,arial,helvetica,sans-serif;
    padding:0;
    margin:0 0 2px 0;
    color:#444;
}
.x-tip .x-tip-body {
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    margin:0 !important;
    line-height:14px;
    color:#444;
    padding:0;
}

.x-tip .x-tip-body .loading-indicator {
    margin:0;
}

.x-tip-draggable .x-tip-header,.x-tip-draggable .x-tip-header-text {
    cursor:move;
}

.x-form-invalid-tip {
}

.x-form-invalid-tip .x-tip-tc {
	background: url(../images/default/form/error-tip-corners.gif) repeat-x 0 -12px;
    padding-top:6px;
}
.x-form-invalid-tip .x-tip-tl {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-tr {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-bc {
	background: url(../images/default/form/error-tip-corners.gif) repeat-x 0 -18px;
    height:6px;
}
.x-form-invalid-tip .x-tip-bl {
	background: url(../images/default/form/error-tip-corners.gif) no-repeat 0 -6px;
}
.x-form-invalid-tip .x-tip-br {
	background: url(../images/default/form/error-tip-corners.gif) no-repeat right -6px;
}
.x-form-invalid-tip .x-tip-ml {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-mr {
	background-image: url(../images/default/form/error-tip-corners.gif);
}
.x-form-invalid-tip .x-tip-body {
    padding:2px;
}
.x-form-invalid-tip .x-tip-body {
    padding-left:24px;
    background:transparent url(../images/default/form/exclamation.gif) no-repeat 2px 2px;
}