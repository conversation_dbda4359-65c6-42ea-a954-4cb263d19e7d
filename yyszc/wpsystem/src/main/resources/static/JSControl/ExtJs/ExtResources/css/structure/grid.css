/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
/* Grid3 styles */
.x-grid3 {
	position:relative;
	overflow:hidden;
}

.x-grid-panel .x-panel-body {
    overflow:hidden !important;
}

.x-grid-panel .x-panel-mc .x-panel-body {
    border:1px solid;
}

.x-grid3 table {
    table-layout:fixed;
}

.x-grid3-viewport{
	overflow:hidden;
}

.x-grid3-hd-row td, .x-grid3-row td, .x-grid3-summary-row td{
    -moz-outline: none;
    outline: none;
	-moz-user-focus: normal;
}

.x-grid3-row td, .x-grid3-summary-row td {
    line-height:13px;
    vertical-align: top;
	padding-left:1px;
    padding-right:1px;
    -moz-user-select: none;
    -khtml-user-select:none;
    -webkit-user-select:ignore;
}

.x-grid3-cell{
    -moz-user-select: none;
    -khtml-user-select:none;
    -webkit-user-select:ignore;
}

.x-grid3-hd-row td {
    line-height:15px;
    vertical-align:middle;
    border-left:1px solid;
    border-right:1px solid;
}

.x-grid3-hd-row .x-grid3-marker-hd {
    padding:3px;
}

.x-grid3-row .x-grid3-marker {
    padding:3px;
}

.x-grid3-cell-inner, .x-grid3-hd-inner{
	overflow:hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
    padding:3px 3px 3px 5px;
    white-space: nowrap;
}

.x-grid3-hd-inner {
    position:relative;
	cursor:inherit;
	padding:4px 3px 4px 5px;
}

.x-grid3-row-body {
    white-space:normal;
}

.x-grid3-body-cell {
    -moz-outline:0 none;
    outline:0 none;
}

/* IE Quirks to clip */
.ext-ie .x-grid3-cell-inner, .ext-ie .x-grid3-hd-inner{
	width:100%;
}

/* reverse above in strict mode */
.ext-strict .x-grid3-cell-inner, .ext-strict .x-grid3-hd-inner{
	width:auto;
}

.x-grid-row-loading {
    background: no-repeat center center;
}

.x-grid-page {
    overflow:hidden;
}

.x-grid3-row {
	cursor: default;
    border: 1px solid;
    width:100%;
}

.x-grid3-row-over {
	border:1px solid;
    background: repeat-x left top;
}

.x-grid3-resize-proxy {
	width:1px;
    left:0;
	cursor: e-resize;
	cursor: col-resize;
	position:absolute;
	top:0;
	height:100px;
	overflow:hidden;
	visibility:hidden;
	border:0 none;
	z-index:7;
}

.x-grid3-resize-marker {
	width:1px;
	left:0;
	position:absolute;
	top:0;
	height:100px;
	overflow:hidden;
	visibility:hidden;
	border:0 none;
	z-index:7;
}

.x-grid3-focus {
	position:absolute;
	left:0;
	top:0;
	width:1px;
	height:1px;
    line-height:1px;
    font-size:1px;
    -moz-outline:0 none;
    outline:0 none;
    -moz-user-select: text;
    -khtml-user-select: text;
    -webkit-user-select:ignore;
}

/* header styles */
.x-grid3-header{
	background: repeat-x 0 bottom;
	cursor:default;
    zoom:1;
    padding:1px 0 0 0;
}

.x-grid3-header-pop {
    border-left:1px solid;
    float:right;
    clear:none;
}

.x-grid3-header-pop-inner {
    border-left:1px solid;
    width:14px;
    height:19px;
    background: transparent no-repeat center center;
}

.ext-ie .x-grid3-header-pop-inner {
    width:15px;
}

.ext-strict .x-grid3-header-pop-inner {
    width:14px; 
}

.x-grid3-header-inner {
    overflow:hidden;
    zoom:1;
    float:left;
}

.x-grid3-header-offset {
    width:auto;
}

td.x-grid3-hd-over, td.sort-desc, td.sort-asc, td.x-grid3-hd-menu-open {
    border-left:1px solid;
    border-right:1px solid;
}

td.x-grid3-hd-over .x-grid3-hd-inner, td.sort-desc .x-grid3-hd-inner, td.sort-asc .x-grid3-hd-inner, td.x-grid3-hd-menu-open .x-grid3-hd-inner {
    background: repeat-x left bottom;

}

.x-grid3-sort-icon{
	background-repeat: no-repeat;
	display: none;
	height: 4px;
	width: 13px;
	margin-left:3px;
	vertical-align: middle;
}

.sort-asc .x-grid3-sort-icon, .sort-desc .x-grid3-sort-icon {
	display: inline;
}

/* Header position fixes for IE strict mode */
.ext-strict .ext-ie .x-grid3-header-inner, .ext-strict .ext-ie6 .x-grid3-hd {
    position:relative;
}

.ext-strict .ext-ie6 .x-grid3-hd-inner{
    position:static;
}

/* Body Styles */
.x-grid3-body {
	zoom:1;
}

.x-grid3-scroller {
	overflow:auto;
    zoom:1;
    position:relative;
}

.x-grid3-cell-text, .x-grid3-hd-text {
	display: block;
	padding: 3px 5px 3px 5px;
	-moz-user-select: none;
	-khtml-user-select: none;
    -webkit-user-select:ignore;
}

.x-grid3-split {
	background-position: center;
	background-repeat: no-repeat;
	cursor: e-resize;
	cursor: col-resize;
	display: block;
	font-size: 1px;
	height: 16px;
	overflow: hidden;
	position: absolute;
	top: 2px;
	width: 6px;
	z-index: 3;
}

/* Column Reorder DD */
.x-dd-drag-proxy .x-grid3-hd-inner{
	background: repeat-x left bottom;
	width:120px;
	padding:3px;
	border:1px solid;
	overflow:hidden;
}

.col-move-top, .col-move-bottom{
	width:9px;
	height:9px;
	position:absolute;
	top:0;
	line-height:1px;
	font-size:1px;
	overflow:hidden;
	visibility:hidden;
	z-index:20000;
    background:transparent no-repeat left top;
}

/* Selection Styles */
.x-grid3-row-selected {
	border:1px dotted;
}

.x-grid3-locked td.x-grid3-row-marker, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker{
    background: repeat-x 0 bottom !important;
    vertical-align:middle !important;
    padding:0;
    border-top:1px solid;
    border-bottom:none !important;
    border-right:1px solid !important;
    text-align:center;
}

.x-grid3-locked td.x-grid3-row-marker div, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker div{
    padding:0 4px;
    text-align:center;
}

/* dirty cells */
.x-grid3-dirty-cell {
    background: transparent no-repeat 0 0;
}

/* Grid Toolbars */
.x-grid3-topbar, .x-grid3-bottombar{
    overflow:hidden;
	display:none;
	zoom:1;
    position:relative;
}

.x-grid3-topbar .x-toolbar{
	border-right:0 none;
}

.x-grid3-bottombar .x-toolbar{
	border-right:0 none;
	border-bottom:0 none;
	border-top:1px solid;
}

/* Props Grid Styles */
.x-props-grid .x-grid3-cell{
	padding:1px;
}

.x-props-grid .x-grid3-td-name .x-grid3-cell-inner{
	background:transparent repeat-y -16px !important;
    padding-left:12px;
}

.x-props-grid .x-grid3-body .x-grid3-td-name{
    padding:1px;
    padding-right:0;
    border:0 none;
    border-right:1px solid;
}

/* dd */
.x-grid3-col-dd {
    border:0 none;
    padding:0;
    background:transparent;
}

.x-dd-drag-ghost .x-grid3-dd-wrap {
    padding:1px 3px 3px 1px;
}

.x-grid3-hd {
    -moz-user-select:none;
    -khtml-user-select:none;
    -webkit-user-select:ignore;
}

.x-grid3-hd-btn {
    display:none;
    position:absolute;
    width:14px;
    background:no-repeat left center;
    right:0;
    top:0;
    z-index:2;
	cursor:pointer;
}

.x-grid3-hd-over .x-grid3-hd-btn, .x-grid3-hd-menu-open .x-grid3-hd-btn {
    display:block;
}

a.x-grid3-hd-btn:hover {
    background-position:-14px center;
}

/* Expanders */
.x-grid3-body .x-grid3-td-expander {
    background:transparent repeat-y right;
}

.x-grid3-body .x-grid3-td-expander .x-grid3-cell-inner {
    padding:0 !important;
    height:100%;
}

.x-grid3-row-expander {
    width:100%;
    height:18px;
    background-position:4px 2px;
    background-repeat:no-repeat;
    background-color:transparent;
}

.x-grid3-row-collapsed .x-grid3-row-expander {
    background-position:4px 2px;
}

.x-grid3-row-expanded .x-grid3-row-expander {
    background-position:-21px 2px;
}

.x-grid3-row-collapsed .x-grid3-row-body {
    display:none !important;
}

.x-grid3-row-expanded .x-grid3-row-body {
    display:block !important;
}

/* Checkers */
.x-grid3-body .x-grid3-td-checker {
    background:transparent repeat-y right;
}

.x-grid3-body .x-grid3-td-checker .x-grid3-cell-inner, .x-grid3-header .x-grid3-td-checker .x-grid3-hd-inner {
    padding:0 !important;
    height:100%;
}

.x-grid3-row-checker, .x-grid3-hd-checker {
    width:100%;
    height:18px;
    background-position:2px 2px;
    background-repeat:no-repeat;
    background-color:transparent;
}

.x-grid3-row .x-grid3-row-checker {
    background-position:2px 2px;
}

.x-grid3-row-selected .x-grid3-row-checker, .x-grid3-hd-checker-on .x-grid3-hd-checker,.x-grid3-row-checked .x-grid3-row-checker {
    background-position:-23px 2px;
}

.x-grid3-hd-checker {
    background-position:2px 1px;
}

.ext-border-box .x-grid3-hd-checker {
    background-position:2px 3px;
}

.x-grid3-hd-checker-on .x-grid3-hd-checker {
    background-position:-23px 1px;
}

.ext-border-box .x-grid3-hd-checker-on .x-grid3-hd-checker {
    background-position:-23px 3px;
}

/* Numberer */
.x-grid3-body .x-grid3-td-numberer {
    background:transparent repeat-y right;
}

.x-grid3-body .x-grid3-td-numberer .x-grid3-cell-inner {
    padding:3px 5px 0 0 !important;
    text-align:right;
}

/* Row Icon */

.x-grid3-body .x-grid3-td-row-icon {
    background:transparent repeat-y right;
    vertical-align:top;
    text-align:center;
}

.x-grid3-body .x-grid3-td-row-icon .x-grid3-cell-inner {
    padding:0 !important;
    background-position:center center;
    background-repeat:no-repeat;
    width:16px;
    height:16px;
    margin-left:2px;
    margin-top:3px;
}

/* All specials */
.x-grid3-body .x-grid3-row-selected .x-grid3-td-numberer,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-checker,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-expander {
	background:transparent repeat-y right;
}

.x-grid3-body .x-grid3-check-col-td .x-grid3-cell-inner {
    padding: 1px 0 0 0 !important;
}

.x-grid3-check-col {
    width:100%;
    height:16px;
    background-position:center center;
    background-repeat:no-repeat;
    background-color:transparent;
}

.x-grid3-check-col-on {
    width:100%;
    height:16px;
    background-position:center center;
    background-repeat:no-repeat;
    background-color:transparent;
}

/* Grouping classes */
.x-grid-group, .x-grid-group-body, .x-grid-group-hd {
    zoom:1;
}

.x-grid-group-hd {
    border-bottom: 2px solid;
    cursor:pointer;
    padding-top:6px;
}

.x-grid-group-hd div.x-grid-group-title {
    background:transparent no-repeat 3px 3px;
    padding:4px 4px 4px 17px;
}

.x-grid-group-collapsed .x-grid-group-body {
    display:none;
}

.ext-ie6 .x-grid3 .x-editor .x-form-text, .ext-ie7 .x-grid3 .x-editor .x-form-text {
    position:relative;
    top:-1px;
}

.ext-ie .x-props-grid .x-editor .x-form-text {
    position:static;
    top:0;
}

.x-grid-empty {
    padding:10px;
}

/* fix floating toolbar issue */
.ext-ie7 .x-grid-panel .x-panel-bbar {
    position:relative;
}


/* Reset position to static when Grid Panel has been framed */
/* to resolve 'snapping' from top to bottom behavior. */
/* @forumThread 86656 */
.ext-ie7 .x-grid-panel .x-panel-mc .x-panel-bbar {
    position: static;
}

.ext-ie6 .x-grid3-header {
    position: relative;
}

/* Fix WebKit bug in Grids */
.ext-webkit .x-grid-panel .x-panel-bwrap{
    -webkit-user-select:none;
}
.ext-webkit .x-tbar-page-number{
    -webkit-user-select:ignore;
}
/* end*/

/* column lines */
.x-grid-with-col-lines .x-grid3-row td.x-grid3-cell {
    padding-right:0;
    border-right:1px solid;
}
