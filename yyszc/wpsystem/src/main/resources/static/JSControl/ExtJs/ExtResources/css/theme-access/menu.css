/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-menu {
	border-color:#222;
    background-color:#414551;
	background-image:url(../images/access/menu/menu.gif);
}

.x-menu-nosep {
	background-image:none;
}

.x-menu-list-item{
	font:normal 14px tahoma,arial, sans-serif;
}

.x-menu-item-arrow{
	background-image:url(../images/access/menu/menu-parent.gif);
}

.x-menu-sep {
    background-color:#223;
	border-bottom-color:#666;
}

a.x-menu-item {
	color:#fffff6;
}

.x-menu-item-active {
	background-color: #f09134;
	background-image: none;
    border-color:#b36427;
}

.x-menu-item-active a.x-menu-item {
	border-color:#b36427;
}

.x-menu-check-item .x-menu-item-icon{
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/default/menu/checked.gif);
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background-image:url(../images/access/menu/group-checked.gif);
}

.x-menu-group-item .x-menu-item-icon{
    background-image:none;
}

.x-menu-plain {
	background-color:#fff !important;
}

.x-menu .x-date-picker{
    border-color:#a3bad9;
}

.x-cycle-menu .x-menu-item-checked {
    border-color:#a3bae9 !important;
    background-color:#def8f6;
}

.x-menu-scroller-top {
    background-image:url(../images/default/layout/mini-top.gif);
}

.x-menu-scroller-bottom {
    background-image:url(../images/default/layout/mini-bottom.gif);
}
