/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

#x-debug-browser .x-tree .x-tree-node a span {
    color:#222297;
    font-size:11px;
    padding-top:2px;
    font-family:"monotype","courier new",sans-serif;
    line-height:18px;
}
#x-debug-browser .x-tree a i {
    color:#FF4545;
    font-style:normal;
}
#x-debug-browser .x-tree a em {
    color:#999;
}
#x-debug-browser .x-tree .x-tree-node .x-tree-selected a span{
    background:#c3daf9;
}
#x-debug-browser  .x-tool-toggle {
    background-position:0 -75px;
}
#x-debug-browser  .x-tool-toggle-over {
    background-position:-15px -75px;
}
#x-debug-browser.x-panel-collapsed .x-tool-toggle {
    background-position:0 -60px;
}
#x-debug-browser.x-panel-collapsed .x-tool-toggle-over {
    background-position:-15px -60px;
}