/** vim: ts=4:sw=4:nu:fdc=4:nospell
 *
 * Ext.ux.form.LovCombo CSS File
 *
 * <AUTHOR>
 * @copyright (c) 2008, by Ing. <PERSON><PERSON>
 * @date      5. April 2008
 * @version   $Id: Ext.ux.form.LovCombo.Css 189 2008-04-16 21:01:06Z jozo $
 *
 * @license Ext.ux.form.LovCombo.Css is licensed under the terms of the Open Source
 * LGPL 3.0 license. Commercial use is permitted to the extent that the 
 * code/component(s) do NOT become part of another Open Source or Commercially
 * licensed development library or toolkit without explicit permission.
 * 
 * License details: http://www.gnu.org/licenses/lgpl.html
 */

.ux-lovcombo-icon {
	width:16px;
	height:16px;
	float:left;
	background-position: -1px -1px ! important;
	background-repeat:no-repeat ! important;
}
.ux-lovcombo-icon-checked {
	background: transparent url(../../../ExtResources/images/default/menu/checked.gif);
}
.ux-lovcombo-icon-unchecked {
	background: transparent url(../../../ExtResources/images/default/menu/unchecked.gif);
}
 
/* eof */
