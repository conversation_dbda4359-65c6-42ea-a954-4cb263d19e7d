/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-btn{
	cursor:pointer;
	white-space: nowrap;
}

.x-btn button{
    border:0 none;
    background:transparent;
    padding-left:3px;
    padding-right:3px;
    cursor:pointer;
    margin:0;
    overflow:visible;
    width:auto;
    -moz-outline:0 none;
    outline:0 none;
}

* html .ext-ie .x-btn button {
    width:1px;
}

.ext-gecko .x-btn button, .ext-webkit .x-btn button {
    padding-left:0;
    padding-right:0;
}

.ext-gecko .x-btn button::-moz-focus-inner {
    padding:0;
}

.ext-ie .x-btn button {
    padding-top:2px;
}

.x-btn td {
    padding:0 !important;
}

.x-btn-text {
    cursor:pointer;
	white-space: nowrap;
    padding:0;
}

/* icon placement and sizing styles */

/* Only text */
.x-btn-noicon .x-btn-small .x-btn-text{
	height: 16px;
}

.x-btn-noicon .x-btn-medium .x-btn-text{
    height: 24px;
}

.x-btn-noicon .x-btn-large .x-btn-text{
    height: 32px;
}

/* Only icons */
.x-btn-icon .x-btn-text{
    background-position: center;
	background-repeat: no-repeat;
}

.x-btn-icon .x-btn-small .x-btn-text{
	height: 16px;
	width: 16px;
}

.x-btn-icon .x-btn-medium .x-btn-text{
    height: 24px;
	width: 24px;
}

.x-btn-icon .x-btn-large .x-btn-text{
    height: 32px;
	width: 32px;
}

/* Icons and text */
/* left */
.x-btn-text-icon .x-btn-icon-small-left .x-btn-text{
    background-position: 0 center;
	background-repeat: no-repeat;
    padding-left:18px;
    height:16px;
}

.x-btn-text-icon .x-btn-icon-medium-left .x-btn-text{
    background-position: 0 center;
	background-repeat: no-repeat;
    padding-left:26px;
    height:24px;
}

.x-btn-text-icon .x-btn-icon-large-left .x-btn-text{
    background-position: 0 center;
	background-repeat: no-repeat;
    padding-left:34px;
    height:32px;
}

/* top */
.x-btn-text-icon .x-btn-icon-small-top .x-btn-text{
    background-position: center 0;
	background-repeat: no-repeat;
    padding-top:18px;
}

.x-btn-text-icon .x-btn-icon-medium-top .x-btn-text{
    background-position: center 0;
	background-repeat: no-repeat;
    padding-top:26px;
}

.x-btn-text-icon .x-btn-icon-large-top .x-btn-text{
    background-position: center 0;
	background-repeat: no-repeat;
    padding-top:34px;
}

/* right */
.x-btn-text-icon .x-btn-icon-small-right .x-btn-text{
    background-position: right center;
	background-repeat: no-repeat;
    padding-right:18px;
    height:16px;
}

.x-btn-text-icon .x-btn-icon-medium-right .x-btn-text{
    background-position: right center;
	background-repeat: no-repeat;
    padding-right:26px;
    height:24px;
}

.x-btn-text-icon .x-btn-icon-large-right .x-btn-text{
    background-position: right center;
	background-repeat: no-repeat;
    padding-right:34px;
    height:32px;
}

/* bottom */
.x-btn-text-icon .x-btn-icon-small-bottom .x-btn-text{
    background-position: center bottom;
	background-repeat: no-repeat;
    padding-bottom:18px;
}

.x-btn-text-icon .x-btn-icon-medium-bottom .x-btn-text{
    background-position: center bottom;
	background-repeat: no-repeat;
    padding-bottom:26px;
}

.x-btn-text-icon .x-btn-icon-large-bottom .x-btn-text{
    background-position: center bottom;
	background-repeat: no-repeat;
    padding-bottom:34px;
}

/* background positioning */
.x-btn-tr i, .x-btn-tl i, .x-btn-mr i, .x-btn-ml i, .x-btn-br i, .x-btn-bl i{
	font-size:1px;
    line-height:1px;
    width:3px;
    display:block;
    overflow:hidden;
}

.x-btn-tr i, .x-btn-tl i, .x-btn-br i, .x-btn-bl i{
	height:3px;
}

.x-btn-tl{
	width:3px;
	height:3px;
	background:no-repeat 0 0;
}
.x-btn-tr{
	width:3px;
	height:3px;
	background:no-repeat -3px 0;
}
.x-btn-tc{
	height:3px;
	background:repeat-x 0 -6px;
}

.x-btn-ml{
	width:3px;
	background:no-repeat 0 -24px;
}
.x-btn-mr{
	width:3px;
	background:no-repeat -3px -24px;
}

.x-btn-mc{
	background:repeat-x 0 -1096px;
    vertical-align: middle;
	text-align:center;
	padding:0 5px;
	cursor:pointer;
	white-space:nowrap;
}

/* Fixes an issue with the button height */
.ext-strict .ext-ie6 .x-btn-mc, .ext-strict .ext-ie7 .x-btn-mc {
    height: 100%;
}

.x-btn-bl{
	width:3px;
	height:3px;
	background:no-repeat 0 -3px;
}

.x-btn-br{
	width:3px;
	height:3px;
	background:no-repeat -3px -3px;
}

.x-btn-bc{
	height:3px;
	background:repeat-x 0 -15px;
}

.x-btn-over .x-btn-tl{
	background-position: -6px 0;
}

.x-btn-over .x-btn-tr{
	background-position: -9px 0;
}

.x-btn-over .x-btn-tc{
	background-position: 0 -9px;
}

.x-btn-over .x-btn-ml{
	background-position: -6px -24px;
}

.x-btn-over .x-btn-mr{
	background-position: -9px -24px;
}

.x-btn-over .x-btn-mc{
	background-position: 0 -2168px;
}

.x-btn-over .x-btn-bl{
	background-position: -6px -3px;
}

.x-btn-over .x-btn-br{
	background-position: -9px -3px;
}

.x-btn-over .x-btn-bc{
	background-position: 0 -18px;
}

.x-btn-click .x-btn-tl, .x-btn-menu-active .x-btn-tl, .x-btn-pressed .x-btn-tl{
	background-position: -12px 0;
}

.x-btn-click .x-btn-tr, .x-btn-menu-active .x-btn-tr, .x-btn-pressed .x-btn-tr{
	background-position: -15px 0;
}

.x-btn-click .x-btn-tc, .x-btn-menu-active .x-btn-tc, .x-btn-pressed .x-btn-tc{
	background-position: 0 -12px;
}

.x-btn-click .x-btn-ml, .x-btn-menu-active .x-btn-ml, .x-btn-pressed .x-btn-ml{
	background-position: -12px -24px;
}

.x-btn-click .x-btn-mr, .x-btn-menu-active .x-btn-mr, .x-btn-pressed .x-btn-mr{
	background-position: -15px -24px;
}

.x-btn-click .x-btn-mc, .x-btn-menu-active .x-btn-mc, .x-btn-pressed .x-btn-mc{
	background-position: 0 -3240px;
}

.x-btn-click .x-btn-bl, .x-btn-menu-active .x-btn-bl, .x-btn-pressed .x-btn-bl{
	background-position: -12px -3px;
}

.x-btn-click .x-btn-br, .x-btn-menu-active .x-btn-br, .x-btn-pressed .x-btn-br{
	background-position: -15px -3px;
}

.x-btn-click .x-btn-bc, .x-btn-menu-active .x-btn-bc, .x-btn-pressed .x-btn-bc{
	background-position: 0 -21px;
}

.x-btn-disabled *{
	cursor:default !important;
}


/* With a menu arrow */
/* right */
.x-btn-mc em.x-btn-arrow {
    display:block;
    background:transparent no-repeat right center;
	padding-right:10px;
}

.x-btn-mc em.x-btn-split {
    display:block;
    background:transparent no-repeat right center;
	padding-right:14px;
}

/* bottom */
.x-btn-mc em.x-btn-arrow-bottom {
    display:block;
    background:transparent no-repeat center bottom;
	padding-bottom:14px;
}

.x-btn-mc em.x-btn-split-bottom {
    display:block;
    background:transparent no-repeat center bottom;
	padding-bottom:14px;
}

/* height adjustment class */
.x-btn-as-arrow .x-btn-mc em {
    display:block;
    background:transparent;
	padding-bottom:14px;
}

/* groups */
.x-btn-group {
    padding:1px;
}

.x-btn-group-header {
    padding:2px;
    text-align:center;
}

.x-btn-group-tc {
	background: transparent repeat-x 0 0;
	overflow:hidden;
}

.x-btn-group-tl {
	background: transparent no-repeat 0 0;
	padding-left:3px;
    zoom:1;
}

.x-btn-group-tr {
	background: transparent no-repeat right 0;
	zoom:1;
    padding-right:3px;
}

.x-btn-group-bc {
	background: transparent repeat-x 0 bottom;
    zoom:1;
}

.x-btn-group-bc .x-panel-footer {
    zoom:1;
}

.x-btn-group-bl {
	background: transparent no-repeat 0 bottom;
	padding-left:3px;
    zoom:1;
}

.x-btn-group-br {
	background: transparent no-repeat right bottom;
	padding-right:3px;
    zoom:1;
}

.x-btn-group-mc {
    border:0 none;
    padding:1px 0 0 0;
    margin:0;
}

.x-btn-group-mc .x-btn-group-body {
    background:transparent;
    border: 0 none;
}

.x-btn-group-ml {
	background: transparent repeat-y 0 0;
	padding-left:3px;
    zoom:1;
}

.x-btn-group-mr {
	background: transparent repeat-y right 0;
	padding-right:3px;
    zoom:1;
}

.x-btn-group-bc .x-btn-group-footer {
    padding-bottom:6px;
}

.x-panel-nofooter .x-btn-group-bc {
	height:3px;
    font-size:0;
    line-height:0;
}

.x-btn-group-bwrap {
    overflow:hidden;
    zoom:1;
}

.x-btn-group-body {
    overflow:hidden;
    zoom:1;
}

.x-btn-group-notitle .x-btn-group-tc {
	background: transparent repeat-x 0 0;
	overflow:hidden;
    height:2px;
}