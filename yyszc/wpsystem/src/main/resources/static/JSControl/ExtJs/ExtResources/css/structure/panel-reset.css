/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
/**
 * W3C Suggested Default style sheet for HTML 4
 * http://www.w3.org/TR/CSS21/sample.html
 *
 * Resets for Ext.Panel @cfg normal: true
 */
.x-panel-reset .x-panel-body html,
.x-panel-reset .x-panel-body address,
.x-panel-reset .x-panel-body blockquote,
.x-panel-reset .x-panel-body body,
.x-panel-reset .x-panel-body dd,
.x-panel-reset .x-panel-body div,
.x-panel-reset .x-panel-body dl,
.x-panel-reset .x-panel-body dt,
.x-panel-reset .x-panel-body fieldset,
.x-panel-reset .x-panel-body form,
.x-panel-reset .x-panel-body frame, frameset,
.x-panel-reset .x-panel-body h1,
.x-panel-reset .x-panel-body h2,
.x-panel-reset .x-panel-body h3,
.x-panel-reset .x-panel-body h4,
.x-panel-reset .x-panel-body h5,
.x-panel-reset .x-panel-body h6,
.x-panel-reset .x-panel-body noframes,
.x-panel-reset .x-panel-body ol,
.x-panel-reset .x-panel-body p,
.x-panel-reset .x-panel-body ul,
.x-panel-reset .x-panel-body center,
.x-panel-reset .x-panel-body dir,
.x-panel-reset .x-panel-body hr,
.x-panel-reset .x-panel-body menu,
.x-panel-reset .x-panel-body pre 			  { display: block }
.x-panel-reset .x-panel-body li              { display: list-item }
.x-panel-reset .x-panel-body head            { display: none }
.x-panel-reset .x-panel-body table           { display: table }
.x-panel-reset .x-panel-body tr              { display: table-row }
.x-panel-reset .x-panel-body thead           { display: table-header-group }
.x-panel-reset .x-panel-body tbody           { display: table-row-group }
.x-panel-reset .x-panel-body tfoot           { display: table-footer-group }
.x-panel-reset .x-panel-body col             { display: table-column }
.x-panel-reset .x-panel-body colgroup        { display: table-column-group }
.x-panel-reset .x-panel-body td,
.x-panel-reset .x-panel-body th 	          { display: table-cell }
.x-panel-reset .x-panel-body caption         { display: table-caption }
.x-panel-reset .x-panel-body th              { font-weight: bolder; text-align: center }
.x-panel-reset .x-panel-body caption         { text-align: center }
.x-panel-reset .x-panel-body body            { margin: 8px }
.x-panel-reset .x-panel-body h1              { font-size: 2em; margin: .67em 0 }
.x-panel-reset .x-panel-body h2              { font-size: 1.5em; margin: .75em 0 }
.x-panel-reset .x-panel-body h3              { font-size: 1.17em; margin: .83em 0 }
.x-panel-reset .x-panel-body h4,
.x-panel-reset .x-panel-body p,
.x-panel-reset .x-panel-body blockquote,
.x-panel-reset .x-panel-body ul,
.x-panel-reset .x-panel-body fieldset,
.x-panel-reset .x-panel-body form,
.x-panel-reset .x-panel-body ol,
.x-panel-reset .x-panel-body dl,
.x-panel-reset .x-panel-body dir,
.x-panel-reset .x-panel-body menu            { margin: 1.12em 0 }
.x-panel-reset .x-panel-body h5              { font-size: .83em; margin: 1.5em 0 }
.x-panel-reset .x-panel-body h6              { font-size: .75em; margin: 1.67em 0 }
.x-panel-reset .x-panel-body h1,
.x-panel-reset .x-panel-body h2,
.x-panel-reset .x-panel-body h3,
.x-panel-reset .x-panel-body h4,
.x-panel-reset .x-panel-body h5,
.x-panel-reset .x-panel-body h6,
.x-panel-reset .x-panel-body b,
.x-panel-reset .x-panel-body strong          { font-weight: bolder }
.x-panel-reset .x-panel-body blockquote      { margin-left: 40px; margin-right: 40px }
.x-panel-reset .x-panel-body i,
.x-panel-reset .x-panel-body cite,
.x-panel-reset .x-panel-body em,
.x-panel-reset .x-panel-body var,
.x-panel-reset .x-panel-body address    	  { font-style: italic }
.x-panel-reset .x-panel-body pre,
.x-panel-reset .x-panel-body tt,
.x-panel-reset .x-panel-body code,
.x-panel-reset .x-panel-body kbd,
.x-panel-reset .x-panel-body samp       	  { font-family: monospace }
.x-panel-reset .x-panel-body pre             { white-space: pre }
.x-panel-reset .x-panel-body button,
.x-panel-reset .x-panel-body textarea,
.x-panel-reset .x-panel-body input,
.x-panel-reset .x-panel-body select   		  { display: inline-block }
.x-panel-reset .x-panel-body big             { font-size: 1.17em }
.x-panel-reset .x-panel-body small,
.x-panel-reset .x-panel-body sub,
.x-panel-reset .x-panel-body sup 			  { font-size: .83em }
.x-panel-reset .x-panel-body sub             { vertical-align: sub }
.x-panel-reset .x-panel-body sup             { vertical-align: super }
.x-panel-reset .x-panel-body table           { border-spacing: 2px; }
.x-panel-reset .x-panel-body thead,
.x-panel-reset .x-panel-body tbody,
.x-panel-reset .x-panel-body tfoot           { vertical-align: middle }
.x-panel-reset .x-panel-body td,
.x-panel-reset .x-panel-body th          	  { vertical-align: inherit }
.x-panel-reset .x-panel-body s,
.x-panel-reset .x-panel-body strike,
.x-panel-reset .x-panel-body del  			  { text-decoration: line-through }
.x-panel-reset .x-panel-body hr              { border: 1px inset }
.x-panel-reset .x-panel-body ol,
.x-panel-reset .x-panel-body ul,
.x-panel-reset .x-panel-body dir,
.x-panel-reset .x-panel-body menu,
.x-panel-reset .x-panel-body dd        	  { margin-left: 40px }
.x-panel-reset .x-panel-body ul, .x-panel-reset .x-panel-body menu, .x-panel-reset .x-panel-body dir { list-style-type: disc;}
.x-panel-reset .x-panel-body ol              { list-style-type: decimal }
.x-panel-reset .x-panel-body ol ul,
.x-panel-reset .x-panel-body ul ol,
.x-panel-reset .x-panel-body ul ul,
.x-panel-reset .x-panel-body ol ol    		  { margin-top: 0; margin-bottom: 0 }
.x-panel-reset .x-panel-body u,
.x-panel-reset .x-panel-body ins          	  { text-decoration: underline }
.x-panel-reset .x-panel-body br:before       { content: "\A" }
.x-panel-reset .x-panel-body :before, .x-panel-reset .x-panel-body :after { white-space: pre-line }
.x-panel-reset .x-panel-body center          { text-align: center }
.x-panel-reset .x-panel-body :link, .x-panel-reset .x-panel-body :visited { text-decoration: underline }
.x-panel-reset .x-panel-body :focus          { outline: invert dotted thin }

/* Begin bidirectionality settings (do not change) */
.x-panel-reset .x-panel-body BDO[DIR="ltr"]  { direction: ltr; unicode-bidi: bidi-override }
.x-panel-reset .x-panel-body BDO[DIR="rtl"]  { direction: rtl; unicode-bidi: bidi-override }
