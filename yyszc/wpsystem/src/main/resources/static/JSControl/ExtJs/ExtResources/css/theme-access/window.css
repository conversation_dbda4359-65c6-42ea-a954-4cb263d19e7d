/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-window-proxy {
    background-color:#1f2833;
    border-color:#18181a;
}

.x-window-tl .x-window-header {
    color:fff;
	font:bold 14px tahoma,arial,verdana,sans-serif;
}

.x-window-tc {
	background-image: url(../images/access/window/top-bottom.png);
}

.x-window-tl {
	background-image: url(../images/access/window/left-corners.png);
}

.x-window-tr {
	background-image: url(../images/access/window/right-corners.png);
}

.x-window-bc {
	background-image: url(../images/access/window/top-bottom.png);
}

.x-window-bl {
	background-image: url(../images/access/window/left-corners.png);
}

.x-window-br {
	background-image: url(../images/access/window/right-corners.png);
}

.x-window-mc {
    border-color:#18181a;
    font: normal 14px tahoma,arial,helvetica,sans-serif;
    background-color:#1f2833;
}

.x-window-ml {
	background-image: url(../images/access/window/left-right.png);
}

.x-window-mr {
	background-image: url(../images/access/window/left-right.png);
}

.x-window-maximized .x-window-tc {
    background-color:#fff;
}

.x-window-bbar .x-toolbar {
    border-top-color:#323945;
}

.x-panel-ghost .x-window-tl {
    border-bottom-color:#323945;
}

.x-panel-collapsed .x-window-tl {
    border-bottom-color:#323945;
}

.x-dlg-mask{
   background-color:#ccc;
}

.x-window-plain .x-window-mc {
    background-color: #464f61;
    border-color: #636778;
}

.x-window-plain .x-window-body {
    color: #fffff6;
    border-color: #464F61;
}

body.x-body-masked .x-window-plain .x-window-mc {
    background-color: #464f61;
}
