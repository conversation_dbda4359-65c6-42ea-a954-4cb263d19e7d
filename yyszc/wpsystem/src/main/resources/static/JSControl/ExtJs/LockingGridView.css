/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-grid3-locked, .x-grid3-unlocked {
	overflow: hidden;
	position: absolute;
}

.x-grid3-locked {
	border-right: 1px solid #99BBE8;
}

.x-grid3-locked .x-grid3-scroller {
	overflow: hidden;
}

.x-grid3-locked .x-grid3-row {
	border-right: 0;
}

.x-grid3-scroll-spacer {
	height: 19px;
}

.x-grid3-unlocked .x-grid3-header-offset {
	padding-left: 0;
}

.x-grid3-unlocked .x-grid3-row {
	border-left: 0;
}
