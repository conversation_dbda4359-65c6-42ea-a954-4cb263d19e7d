#class .loading-indicator{
	font-size:12px;
	height:18px;
}
#docs .x-layout-panel-north{
   border:0px none;
   background:#0000aa url(../../resources/images/default/basic-dialog/hd-sprite.gif) repeat-x 0px -83px;
   padding-top:3px;
   padding-left:3px;
}
#docs .x-layout-collapsed-west{
   background-image: url(collapse-bg.gif);
   background-repeat:no-repeat;
   background-position:center;
}
#header {
    font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;
    color:white;
}
.loading-indicator {
    font-size:8pt;
    background-image:url('../../resources/images/grid/loading.gif');
    background-repeat: no-repeat;
    background-position:top left;
    padding-left:20px;
	height:18px;
	text-align:left;
}
a#welcome-link{
	background:#fff url(docs.gif) no-repeat 0px 0px;
	padding-left:18px;
}
a#help-forums{
	background:#fff url(forum.gif) no-repeat 16px 0px;
	padding-left:34px;
	display:block
}
#loading{
	position:absolute;
	left:45%;
	top:40%;
	border:1px solid #6593cf;
	padding:2px;
	background:#c3daf9;
	width:150px;
	text-align:center;
	z-index:20001;
}
#loading .loading-indicator{
	border:1px solid #a3bad9;
	/*background:white url(block-bg.gif) repeat-x;*/
	background:white repeat-x;	
	color:#003366;
	font:bold 13px tahoma,arial,helvetica;
	padding:10px;
	margin:0;
}
#classes{
    overflow:auto;
    padding:5px;
}
#classes a span {
    font:normal 11px verdana,helvetica,tahoma,sans-serif;
}
#classes .cls a:hover span {
    text-decoration:underline;
    color:#003366;
}

#classes .x-tree-selected a span {
    background:#c3daf9;
    border:1px dashed #99bbe8;
    color:#000;
}
#classes .x-tree-selected a:hover span {
    text-decoration:none;
    color:#000;
}