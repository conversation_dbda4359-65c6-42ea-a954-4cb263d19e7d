/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-panel {
    border-color: #18181a;
    font-size: 14px;
}

.x-panel-header {
    color:#fff;
	font-weight:bold; 
    font-size: 14px;
    font-family: tahoma,arial,verdana,sans-serif;
    border-color:#18181a;
    background-image: url(../images/access/panel/white-top-bottom.gif);
}

.x-panel-body {
    color: #fffff6;
    border-color:#18181a;
    background-color:#232d38;
}

.x-tab-panel .x-panel-body {
    color: #fffff6;
    border-color:#18181a;
    background-color:#1f2730;
}

.x-panel-bbar .x-toolbar, .x-panel-tbar .x-toolbar {
    border-color:#223;
}

.x-panel-tbar-noheader .x-toolbar, .x-panel-mc .x-panel-tbar .x-toolbar {
    border-top-color:#223;
}

.x-panel-body-noheader, .x-panel-mc .x-panel-body {
    border-top-color:#223;
}

.x-panel-tl .x-panel-header {
    color:fff;
	font:bold 14px tahoma,arial,verdana,sans-serif;
}

.x-panel-tc {
	background-image: url(../images/access/panel/top-bottom.gif);
}

.x-panel-tl, .x-panel-tr, .x-panel-bl,  .x-panel-br{
	background-image: url(../images/access/panel/corners-sprite.gif);
    border-bottom-color:#222224;
}

.x-panel-bc {
	background-image: url(../images/access/panel/top-bottom.gif);
}

.x-panel-mc {
    font: normal 14px tahoma,arial,helvetica,sans-serif;
    background-color:#3f4757;
}

.x-panel-ml {
    background-image:url(../images/access/panel/left-right.gif);
}

.x-panel-mr {
	background-image: url(../images/access/panel/left-right.gif);
}

.x-tool {
    background-image:url(../images/access/panel/tool-sprites.gif);
}

.x-panel-ghost {
    background-color:#3f4757;
}

.x-panel-ghost ul {
    border-color:#18181a;
}

.x-panel-dd-spacer {
    border-color:#18181a;
}

.x-panel-fbar td,.x-panel-fbar span,.x-panel-fbar input,.x-panel-fbar div,.x-panel-fbar select,.x-panel-fbar label{
    font:normal 14px arial,tahoma, helvetica, sans-serif;
}
