
.x-grid3-td-topic .x-grid3-cell-inner {   
    white-space:normal;   
}   
.x-grid3-td-topic a {   
    color: #385F95;   
    text-decoration:none;   
}   
.x-grid3-td-topic a:hover {   
    text-decoration:underline;   
}   
.details .x-btn-text {   
    background-image: url(details.gif);   
}   

.x-resizable-pinned .x-resizable-handle-south{   
    background:url(../images/default/sizer/s-handle-dark.gif);   
    background-position: top;   
}   
/*******************************************/   
.Gray-row .x-grid3-cell-inner{   
    color:Gray;   
}   
.Maroon-row .x-grid3-cell-inner{   
    color:Maroon;   
}   
.red-row .x-grid3-cell-inner{   
    color:red;   
}   
.Navy-row .x-grid3-cell-inner{   
    color:Navy;   
} 
.Blue-row .x-grid3-cell-inner{   
    color:Blue;
}  

 /*---- 颜色-------*/
 .x-grid-record-red table
{
	color:#FF0000;
}
  
.x-grid-record-green table{
    color:Green;
}
  
.x-grid-record-orange table
{
    color:Orange;
}
  	
.x-grid-record-blue table
{
    background:#97d3b3;
}
