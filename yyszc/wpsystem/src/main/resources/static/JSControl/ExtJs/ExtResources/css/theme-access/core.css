/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
body {
	background-color:#16181a;
	color:#fcfcfc;
}

.ext-el-mask {
    background-color: #ccc;
}

.ext-el-mask-msg {
    border-color:#223;
    background-color:#3f4757;
    background-image:url(../images/access/box/tb-blue.gif);
}
.ext-el-mask-msg div {
    background-color: #232d38;
    border-color:#556;
    color:#fff;
    font:normal 14px tahoma, arial, helvetica, sans-serif;
}

.x-mask-loading div {
    background-color:#232d38;
    background-image:url(../images/access/grid/loading.gif);
}

.x-item-disabled {
    color: #ddd;
}

.x-item-disabled * {
    color: #ddd !important;
}

.x-splitbar-proxy {
    background-color: #aaa;
}

.x-color-palette a {
    border-color:#fff;
}

.x-color-palette a:hover, .x-color-palette a.x-color-palette-sel {
    border-color:#8bb8f3;
    background-color: #deecfd;
}

.x-color-palette em {
    border-color:#aca899;
}

.x-ie-shadow {
    background-color:#777;
}

.x-shadow .xsmc {
    background-image: url(../images/default/shadow-c.png);
}

.x-shadow .xsml, .x-shadow .xsmr {
    background-image: url(../images/default/shadow-lr.png);
}

.x-shadow .xstl, .x-shadow .xstc,  .x-shadow .xstr, .x-shadow .xsbl, .x-shadow .xsbc, .x-shadow .xsbr{
    background-image: url(../images/default/shadow.png);
}

.loading-indicator {
    font-size: 14px;
    background-image: url(../images/access/grid/loading.gif);
}

.x-spotlight {
    background-color: #ccc;
}