package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.AesHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.NFT_ModuleLCLink;
import com.yyszc.wpbase.entity.NFV_ModuleLCLink;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value="/WpService/MLCLink")
@Api(tags ="微服务框架接口->NFT_ModuleLCLink表操作接口")
public class NFT_ModuleLCLinkController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/AddNFT_ModuleLCLink",method ={RequestMethod.POST})
    @ApiOperation(value ="AddNFT_ModuleLCLink",notes="新增模块关联待办定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_ModuleLCLink", value = "NFT_ModuleLCLink", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Integer> AddNFT_ModuleLCLink(@RequestBody NFT_ModuleLCLink entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                //提交数据库
                String strsql = DBHelper.GetInsertSQL(entity,"NFT_ModuleLCLink", Arrays.asList("LK_ID"));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
                }
                String tmpstr=sqlhelper.ExecuteInsertWithObtainId(strsql);

                return ResponseEntity.ok().body(Integer.parseInt(tmpstr));
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
        }
    }

    @RequestMapping(value="/UpdateNFT_ModuleLCLink",method ={RequestMethod.POST})
    @ApiOperation(value ="UpdateNFT_ModuleLCLink",notes="修改模块关联待办定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "NFT_ModuleLCLink", value = "NFT_ModuleLCLink", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> UpdateNFT_ModuleLCLink(@RequestBody NFT_ModuleLCLink entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = DBHelper.GetUpdateSQL(entity,"NFT_ModuleLCLink", Arrays.asList("ID"), Arrays.asList(entity.getLK_ID().toString()));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
                }
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/DeleteNFT_ModuleLCLinkById",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteNFT_ModuleLCLinkById",notes="删除模块关联待办定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteNFT_ModuleLCLinkById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from NFT_ModuleLCLink where LK_ID='"+id+"'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/GetNFT_ModuleLCLinkById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNFT_ModuleLCLinkById",notes="获取模块关联待办定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<NFT_ModuleLCLink> GetNFT_ModuleLCLinkById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from NFT_ModuleLCLink where LK_ID='"+id+"'";
                NFT_ModuleLCLink entity=sqlhelper.GetObject(NFT_ModuleLCLink.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetNFV_ModuleLCLinkById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNFV_ModuleLCLinkById",notes="获取模块关联待办定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<NFV_ModuleLCLink> GetNFV_ModuleLCLinkById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from NFV_ModuleLCLink where LK_ID='"+id+"'";
                NFV_ModuleLCLink entity=sqlhelper.GetObject(NFV_ModuleLCLink.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetNFT_ModuleLCLinkList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNFT_ModuleLCLinkList",notes="获取角色列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<NFT_ModuleLCLink>> GetNFT_ModuleLCLinkList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<NFT_ModuleLCLink> plist=sqlhelper.GetObjectList(NFT_ModuleLCLink.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetNFV_ModuleLCLinkList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNFV_ModuleLCLinkList",notes="获取角色列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<NFV_ModuleLCLink>> GetNFV_ModuleLCLinkList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<NFV_ModuleLCLink> plist=sqlhelper.GetObjectList(NFV_ModuleLCLink.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetNFT_ModuleLCLinkByLc",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNFT_ModuleLCLinkByLc",notes="获取角色列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lcid", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<NFT_ModuleLCLink> GetNFT_ModuleLCLinkByLc(@RequestParam("lcid") String lcid, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                String strsql="select * from NFT_ModuleLCLink where LK_LCID='"+lcid+"'";
                NFT_ModuleLCLink plc=sqlhelper.GetObject(NFT_ModuleLCLink.class,strsql);
                return ResponseEntity.ok().body(plc);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }
}
