package com.soft.framework.controller;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.base.entity.NFV_NavInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Configuration
@RequestMapping(value="/Service/DownMan" )
@Api(tags ="微服务框架接口->下载中心基本接口")
public class DownController {
    @RequestMapping(value="/GetFileDataList",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetFileDataList",notes="用户DownCenter信息接口")
    public String GetFileDataList(HttpServletRequest request)
    {
        try {
            StringBuilder sb=new StringBuilder();
            GetFileData(sb);
            return sb.toString();
        }
        catch(Exception ex)
        {
            return "";
        }
    }

    private void GetFileData(StringBuilder sb)
    {
        SqlHelper sqlhelper=new SqlHelper();

        sb.append("<table width='100%' cellpadding='0' cellspacing='0' border='0' align='center'>");
        List<NFV_NavInfo> _navlist = sqlhelper.GetObjectList(NFV_NavInfo.class,"select * from nfv_navinfo where navstate=1 and navtype=4  order by NavOrder");
        for (int i = 0; i < _navlist.size(); i++)
        {
            NFV_NavInfo nav = _navlist.get(i);

            sb.append("<tr class='DownSpace'><td colspan='2'></td></tr><tr>");
            sb.append("<td class='DownLine' align='center'><span class='DownSpan'>" + (i + 1) + "</span></td><td><a class='DownText' href='javascript:void(0);' onclick='DownRec(" + nav.getNavId() + ")'>" + nav.getNavDisplay() + "</a></td>");
            sb.append("</tr>");
        }
        sb.append("</table>");
        return;
    }


    @RequestMapping(value="/DownFile", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="DownFile",notes="获取文件接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String DownFile(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String fileid=request.getParameter("fileid");
        SqlHelper sqlhelper=new SqlHelper();
        String furl = sqlhelper.ExecuteScalar("select navurl from nfv_navinfo where navtype=4 and navid=" + fileid);
        try
        {
            if(!StringUtil.IsNullOrEmpty(furl)) {
                String fullpath = ConfigHelper.getProfile() + furl;
                String filename = FileUtil.ExtractFileName(furl);

                if (FileUtil.FileExists(fullpath)) {
                    ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                    cfs.success = true;
                    cfs.text = "生成文件成功！";
                    cfs.fname = filename.toString();
                    cfs.data1 = ToolHelper.File2Bytes(fullpath);
                    cfs.data2 = null;

                    String jsonstr = JSON.toJSON(cfs).toString();
                    return jsonstr;
                } else {
                    ajaxResult = AjaxResult.error("定位文件失败!");
                    return JSON.toJSON(ajaxResult).toString();
                }
            }else
            {
                ajaxResult = AjaxResult.error("定位文件失败!");
                return JSON.toJSON(ajaxResult).toString();
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
