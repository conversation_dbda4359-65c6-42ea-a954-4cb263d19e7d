package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.AesHelper;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value="/WpService/Tool")
@Api(tags ="微服务框架接口->通用工具接口")
public class ToolController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/SendSms",method ={RequestMethod.GET})
    @ApiOperation(value ="SendSms",notes="获取当前用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "String", required = true),
            @ApiImplicitParam(name = "content", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> SendSms(@RequestParam("phone") String phone, @RequestBody String content, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(false);
        }

        if(tokenService.verifyToken(token))
        {
            DBHelper.SendSms(phone,content);
            return ResponseEntity.ok(true);
        }else{
            return ResponseEntity.ok(false);
        }
    }


    @RequestMapping(value="/GetDataTable",method ={RequestMethod.GET})
    @ApiOperation(value ="GetDataTable",notes="通用查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<DataTable> GetDataTable(@RequestParam("strsql") String strsql,  @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }

        if(tokenService.verifyToken(token))
        {
            SqlHelper sqlhelper=new SqlHelper();
            if(!strsql.equals("")) {
                strsql= AesHelper.aesDecodeCBC(strsql);
            }
            DataTable tmpdt=sqlhelper.GetDataTable(strsql);
            return ResponseEntity.ok(tmpdt);
        }else{
            return ResponseEntity.ok(null);
        }
    }


    @RequestMapping(value="/RecordExists",method ={RequestMethod.GET})
    @ApiOperation(value ="RecordExists",notes="通用记录存在确认接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> RecordExists(@RequestParam("strsql") String strsql,  @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper = new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                Boolean retval = sqlhelper.RecordExists(strsql);
                return ResponseEntity.ok(retval);
            } else {
                return ResponseEntity.ok(false);
            }
        }catch (Exception Ex)
        {
            return ResponseEntity.ok(false);
        }
    }

    @RequestMapping(value="/GetRecordCount",method ={RequestMethod.GET})
    @ApiOperation(value ="GetRecordCount",notes="通用获取记录条目接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<String> GetRecordCount(@RequestParam("strsql")  String strsql,  @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok("");
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper = new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                String retval = sqlhelper.ExecuteScalar(strsql);
                return ResponseEntity.ok(retval);
            } else {
                return ResponseEntity.ok("");
            }
        }catch (Exception Ex)
        {
            return ResponseEntity.ok("");
        }
    }

    @RequestMapping(value="/ExecuteScalar",method ={RequestMethod.GET})
    @ApiOperation(value ="ExecuteScalar",notes="通用获取记录条目接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<String> ExecuteScalar(@RequestParam("strsql")  String strsql,  @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok("");
        }

        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper = new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                String retval = sqlhelper.ExecuteScalar(strsql);
                return ResponseEntity.ok(retval);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/AddApplicationLog",method ={RequestMethod.GET})
    @ApiOperation(value ="AddApplicationLog",notes="通用登记AppliactionLog接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uname", value = "String", required = true),
            @ApiImplicitParam(name = "func", value = "String", required = true),
            @ApiImplicitParam(name = "module", value = "String", required = true),
            @ApiImplicitParam(name = "detail", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> AddApplicationLog(@RequestParam("uname")  String uname,@RequestParam("func")  String func,
        @RequestParam("module")  String module,@RequestParam("detail")  String detail,
         @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                DBHelper.AddApplicationLog(uname,func,module,detail);
                return ResponseEntity.ok(true);
            } else {
                return ResponseEntity.ok(false);
            }
        }catch (Exception Ex)
        {
            return ResponseEntity.ok(false);
        }
    }

    @RequestMapping(value="/AddApplicationLogYW",method ={RequestMethod.GET})
    @ApiOperation(value ="AddApplicationLogYW",notes="通用登记AppliactionLog接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uname", value = "String", required = true),
            @ApiImplicitParam(name = "func", value = "String", required = true),
            @ApiImplicitParam(name = "module", value = "String", required = true),
            @ApiImplicitParam(name = "detail", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> AddApplicationLogYW(@RequestParam("uname")  String uname,@RequestParam("func")  String func,
        @RequestParam("module")  String module,@RequestParam("detail")  String detail,
         @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                DBHelper.AddApplicationLogYW(uname,func,module,detail);
                return ResponseEntity.ok(true);
            } else {
                return ResponseEntity.ok(false);
            }
        }catch (Exception Ex)
        {
            return ResponseEntity.ok(false);
        }
    }
}
