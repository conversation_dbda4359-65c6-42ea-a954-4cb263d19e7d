package com.soft.framework.helper;

import com.soft.framework.common.utils.string.StringUtil;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;

public class AesHelper {
    private static byte[] key;

    static {
        try {
            key = "db2139561c9fe068".getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    public static String aesEncodeCBC(String srcstr)
    {
        try
        {
            byte[] data = srcstr.getBytes("UTF-8");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(key);
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ips);

            byte[] bOut = cipher.doFinal(data);
            String rets=new BASE64Encoder().encode(bOut);
            return rets;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    public static String aesDecodeCBC(String encstr)
    {
        try
        {
            byte[] data = new BASE64Decoder().decodeBuffer(encstr);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(key);
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ips);

            byte[] bOut = cipher.doFinal(data);
            String rets= StringUtil.deleteLastChar(new String(bOut,"UTF-8"),'\0');
            return rets;
        }catch(Exception Ex)
        {
            return null;
        }
    }
}
