package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.AesHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.Lcjd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value="/WpService/Lcjd")
@Api(tags ="微服务框架接口->Lcjd表操作接口")
public class LcjdController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/AddLcjd",method ={RequestMethod.POST})
    @ApiOperation(value ="AddLcjd",notes="新增流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lcjd", value = "Lcjd", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Integer> AddLcjd(@RequestBody Lcjd entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                //提交数据库
                String strsql = DBHelper.GetInsertSQL(entity,"Lcjd", Arrays.asList("ID"));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
                }
                String tmpstr=sqlhelper.ExecuteInsertWithObtainId(strsql);

                return ResponseEntity.ok().body(Integer.parseInt(tmpstr));
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
        }
    }

    @RequestMapping(value="/UpdateLcjd",method ={RequestMethod.POST})
    @ApiOperation(value ="UpdateLcjd",notes="修改流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lcjd", value = "Lcjd", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> UpdateLcjd(@RequestBody Lcjd entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = DBHelper.GetUpdateSQL(entity,"Lcjd", Arrays.asList("ID"), Arrays.asList(entity.getID().toString()));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
                }
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/DeleteLcjdById",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteLcjdById",notes="删除流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteLcjdById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from Lcjd where ID='"+id+"'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/GetLcjdById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLcjdById",notes="获取流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Lcjd> GetLcjdById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from Lcjd where id='"+id+"'";
                Lcjd entity=sqlhelper.GetObject(Lcjd.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetLcjdList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLcjdList",notes="获取流程节点定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<Lcjd>> GetLcjdList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<Lcjd> plist=sqlhelper.GetObjectList(Lcjd.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetLcjdListByLc",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLcjdListByLc",notes="获取指定流程所有节点定义列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lcId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<Lcjd>> GetLcjdListByLc(@RequestParam("lcId") int lcId, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                String strsql="select * from Lcjd where lc_defineID='"+lcId+"'";
                List<Lcjd> plist=sqlhelper.GetObjectList(Lcjd.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetLcjdByLcjdId",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLcjdByLcId",notes="获取流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "LcjdId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Lcjd> GetLcjdByLcId(@RequestParam("LcjdId") int LcjdId, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from Lcjd where lcjdID='"+LcjdId+"'";
                Lcjd entity=sqlhelper.GetObject(Lcjd.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetLcjdBySql",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLcjdBySql",notes="获取指定sql相关的流程节点定义信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Lcjd> GetLcjdBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                Lcjd entity=sqlhelper.GetObject(Lcjd.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }
}
