package com.soft.framework.controller;

import com.alibaba.fastjson.JSONObject;
import com.soft.framework.common.constant.Constants;
import com.soft.framework.common.exception.user.CaptchaException;
import com.soft.framework.common.exception.user.CaptchaExpireException;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.security.Base64Util;
import com.soft.framework.common.utils.security.Md5Util;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.manager.AsyncManager;
import com.soft.framework.manager.factory.AsyncFactory;
import com.soft.framework.redis.RedisCache;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.SysLoginService;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.base.entity.APP_SHAKE;
import com.soft.gcc.base.entity.ApplicationLog;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.EmailDetail;
import com.yyszc.wpbase.entity.EmailManage;
import com.yyszc.wpbase.entity.Person;
import com.yyszc.wpbase.entity.v$EmailDetail;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.servlet.ServletContext;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;

@Controller
@Configuration
@RequestMapping(value="/Service/User" )
@Api(tags ="微服务框架接口->登录基本接口")
public class LoginController {
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);

    @Autowired(required =true)
    private SysLoginService loginService;

    @Autowired(required =true)
    private TokenService tokenService;

    @Autowired(required =true)
    private RedisCache redisCache;

    @RequestMapping(value="/Login",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="Login",notes="登录接口")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true),
        @ApiImplicitParam(name = "response", value = "HttpServletResponse", required = true)
    })
    public AjaxResult Login(HttpServletRequest request,HttpServletResponse response)
    {
        AjaxResult ajax = null;
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();

        try {
            String skey=request.getParameter("Skey");
            String loginName=request.getParameter("LoginName");
            String code=request.getParameter("code");
            if (StringUtil.IsNullOrEmpty(loginName))
            {
                ajax=AjaxResult.error("传输参数有误");
                return ajax;
            }

            if (!loginName.equals("")&& !code.equals(""))
            {
                //获取登录名、密码
                String defpwd="zpepc001@@";
                String defmd5=Md5Util.getMD5Str(defpwd);

                if (!ConfigHelper.getRunMode().equals("prod") && code.equals(defpwd))
                {
                    strsql = "select count(*) from Person where loginName='" + loginName + "' and password='"+defmd5+"'";
                    String tmpc = sqlhelper.ExecuteScalar(strsql);
                    if(!tmpc.equals("0"))
                    {
                        return  ExecuteLogin(loginName,request,response);
                    }
                    else
                    {
                        ajax=AjaxResult.error("账号密码有误");
                        return ajax;
                    }
                }
                else
                {
                    Person person=DBHelper.GetPersonByLoginName_S(loginName);

                    if (person.getLoginName().equals("isoApp") && code.equals("159357") && person.getState()== 1)
                    {
                        strsql = "select count(*) from Person where loginName='" + loginName  + "'";
                        String tmpc = sqlhelper.ExecuteScalar(strsql);
                        if (!tmpc.equals("0"))
                        {
                            return ExecuteLogin(loginName, request, response);
                        }
                        else
                        {
                            ajax=AjaxResult.error("登录失败,账号密码有误!");
                            return ajax;
                        }
                    }else {

                        String userip = MetaHelper.GetCurrentIp(request);
                        String useragent = request.getHeader("HTTP_USER_AGENT");
                        String mobile = person.getTelephone();
                        logger.info("loginName="+loginName+",userip="+userip+",useragent="+useragent+",mobile="+mobile+",code="+code);
                        return ExecuteLogin(loginName, request, response);
//                        String ErrorMsg = "";
//                        qx_yzm qx = DBHelper.get_yzmLoginMessage(loginName, mobile, code, userip, useragent);
//                        if (qx != null) {
//                            if (qx.getMessage() == "" || qx.getMessage() == null || qx.getMessage() == "null") {
//                                return ExecuteLogin(loginName, request, response);
//                            } else {
//                                ErrorMsg = qx.getMessage();
//                                ajax = AjaxResult.error(ErrorMsg);
//                                return ajax;
//                            }
//                        } else {
//                            ajax = AjaxResult.error("登录失败，登录时未知异常！");
//                            return ajax;
//                        }
                    }
                }
            }
            else if (!skey.equals(""))
            {
                strsql = "select RES_USER from NFT_SCAN_RESULT where RES_DT>DATEADD(mi,-2,getdate()) and RES_KEY='" + skey + "'";
                loginName = sqlhelper.ExecuteScalar(strsql);
                if (!loginName.equals(""))
                {
                    return ExecuteLogin(loginName,request,response);
                }
                else
                {
                    ajax=AjaxResult.error("登录失败,传输参数有误!");
                    return ajax;
                }
            }
            else
            {
                ajax=AjaxResult.error("账号验证码不能为空!");
                return ajax;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("用户登录时产生异常！");
            return ajax;
        }
    }

    @RequestMapping(value="/ChangePostLogin",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ChangePostLogin",notes="快速用户切换接口")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true),
        @ApiImplicitParam(name = "response", value = "HttpServletResponse", required = true)
    })
    public AjaxResult ChangePostLogin(HttpServletRequest request,HttpServletResponse response)
    {
        AjaxResult ajax = null;
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();

        try {

            String loginName=request.getParameter("loginName");
            String kkey=request.getParameter("KKey");
            if (loginName.equals("")||loginName.equals("undefined"))
            {
                ajax=AjaxResult.error("传输参数有误");
                return ajax;
            }

            PersonEntity person = SessionHelper.getSessionPerson();
            if (person == null) {
                ajax=AjaxResult.error("非法的登录用户账户信息！");
                return ajax;
            }

            if (kkey.equals("true"))
            {
                strsql = "select count(*) from NFT_LOGIN_UC where RU_USER='" + person.getLoginName() + "' and RC_USER='" + loginName + "'";
                String tmpc = sqlhelper.ExecuteScalar(strsql);
                if(tmpc.equals("0"))
                {
                    ajax=AjaxResult.error("未知的切换用户！");
                    return ajax;
                }

                strsql = "select telephone from person where loginname='" + loginName + "'";
                String telephone = sqlhelper.ExecuteScalar(strsql);
                if (telephone==""|| telephone.length() != 11)
                {
                    ajax=AjaxResult.error("预留手机号码无效，请联系相关人员！");
                    return ajax;
                }

                return ExecuteLogin(loginName,request,response);
            }
            else
            {
                ajax=AjaxResult.error("传输参数有误！");
                return ajax;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("用户退出登录时产生异常！");
            return ajax;
        }
    }

    private AjaxResult ExecuteLogin(String loginname,HttpServletRequest request,HttpServletResponse response)
    {
        if(StringUtils.isEmpty(loginname)){
            return AjaxResult.error("参数错误");
        }

        //获取代码版本，控制各种静态页面自动刷新
        SqlHelper sqlhelper=new SqlHelper();
        String strsql="";
        /*if (!CacheLoginUser.isStongPass(password)){
            return AjaxResult.error("请使用强密码！");
        }*/

        AjaxResult ajax = null;
        try
        {
            strsql="select * from ApplicationLog where OperatorName like '%"+loginname+"%' and OperateDate>DATEADD(mi,-5,getdate()) order by OperateDate desc";
            List<ApplicationLog> maxlist = sqlhelper.GetObjectList(ApplicationLog.class,strsql);
//            if(maxlist.size() >= 5) {
//                if(maxlist.get(0).getOperateDetail().contains("登录失败") && maxlist.get(1).getOperateDetail().contains("登录失败") &&
//                        maxlist.get(2).getOperateDetail().contains("登录失败") && maxlist.get(3).getOperateDetail().contains("登录失败") &&
//                        maxlist.get(4).getOperateDetail().contains("登录失败")) {
//                    return AjaxResult.error("登录尝试次数过于频繁，请稍后再登录！");
//                }
//            }

            String  cpsversion =sqlhelper.ExecuteScalar("select vc_ver from cps_ver_ctrl");
            if (cpsversion.equals("")) {
                cpsversion = "0";
            }

            // 生成令牌
            PersonEntity sperson=DBHelper.GetPersonByLoginName(loginname);
            String token = loginService.loginPC(request,loginname,sperson.getPassword());
            ajax=AjaxResult.success();
            ajax.put(Constants.TOKEN, token);
            ajax.put("cpsversion",cpsversion);
            String smenc= Base64Util.encodeData(ConfigHelper.getSystemMark());
            ajax.put("sysmark",smenc);

            //记录登录日志
            LoginUser loginUser = tokenService.getLoginUser(token);
            if(loginUser==null||loginUser.getUser()==null)
            {
                ajax=AjaxResult.error("系统异常,登录失败！");
            }else
            {
                PersonEntity person=loginUser.getUser();
                if (person.getRoleIdsString().length() == 0)
                {
                    ajax=AjaxResult.error("您当前未分配任何权限，不能登录！");
                    return ajax;
                }
//                else if (person.getState() != 1)
//                {
//                    ajax=AjaxResult.error("您的账号已被禁用，请联系管理员！");
//                    return ajax;
//                }
                else
                {
                    //联合施工项目部配置
//                    strsql="select XmbID,XmbName from XSLH_XmbPersonManage where LoginName='"+loginname+"'";
//                    DataTable xmdt=sqlhelper.GetDataTable(strsql);
//                    if(xmdt.getTotalCount()>0)
//                    {
//                        person.setXmbID(Integer.parseInt(xmdt.getRow(0).getColValue("XmbID").toString()));
//                        person.setXmbName(xmdt.getRow(0).getColValue("XmbName").toString());
//                    }

                    //用户邮件
                    List<EmailDetail> list =sqlhelper.GetObjectList(EmailDetail.class,"select * from EmailDetail where State in('未查看','新回复') and personZgh='"+loginname+"'");
                    List<EmailManage> list1 = sqlhelper.GetObjectList(EmailManage.class,"select * from EmailManage where State='新回复' and SpersonZgh='"+loginname+"'");

                    List<EmailManage> list2 = sqlhelper.GetObjectList(EmailManage.class,"select * from EmailManage where State='新回复' and SpersonZgh='"+loginname+"' and Sdate>dateadd(ss,3,getdate())");
                    List<v$EmailDetail> list3 = sqlhelper.GetObjectList(v$EmailDetail.class,"select * from v$EmailDetail where State in('未查看','新回复')  and personZgh='"+loginname+"' and Sdate>dateadd(ss,3,getdate())");

                    if((list2!=null&&list2.size()>0)||(list3!=null&&list3.size()>0))
                    {
                        person.setNewEmailNum("1");
                    }else
                    {
                        person.setNewEmailNum("0");
                    }
                    int msgnum = 0;
                    if(list!=null&&list.size()>0) {
                        msgnum=msgnum+list.size();
                    }
                    if(list1!=null&&list1.size()>0) {
                        msgnum=msgnum+list1.size();
                    }
                    person.setEmailNum(String.valueOf(msgnum));

                    //内外网判断
                    int pwf=SecToolHelper.UserIsInNWWD(request);
                    person.setPwf(pwf);

                    person.setAdminGroupBHs(DBHelper.getPersonAdminGroup(person.getId()));

                    //联合施工项目部配置
                    if(person.getType()==2) {
                        //分包注册人员
                        strsql = "select  top 1 id,IsMain,unit,zzCode,ucontactname,ucontactid,uComapanyBH,id,uyearreg,utype,uloginnumber,uloginIP1,uloginIP2,uloginIP3 from T_UserData where PID="+person.getId();
                        DataTable udt=sqlhelper.GetDataTable(strsql);
                        if(udt.getTotalCount()>0)
                        {
                            person.setIsMain(udt.getRow(0).getColValue("IsMain").toString());
                            person.setUnit(udt.getRow(0).getColValue("unit").toString());
                            person.setZzCode(udt.getRow(0).getColValue("zzCode").toString());
                            person.setUcontactname(udt.getRow(0).getColValue("ucontactname").toString());
                            person.setUcontactid(udt.getRow(0).getColValue("ucontactid").toString());
                            person.setUComapanyBH(udt.getRow(0).getColValue("uComapanyBH").toString());
                            person.setUid(Integer.parseInt(udt.getRow(0).getColValue("id").toString()));
                            person.setChoseNF(udt.getRow(0).getColValue("uyearreg").toString());
                            person.setUType(udt.getRow(0).getColValue("utype").toString());
                            person.setIsOpen(String.valueOf(ConfigHelper.getIsOpen()));
                            person.setUyesrrge(udt.getRow(0).getColValue("uyearreg").toString());

                            //获取申报对象单位名称
                            String uComapanyBH=udt.getRow(0).getColValue("uComapanyBH").toString();
                            if(!StringUtil.IsNullOrEmpty(uComapanyBH))
                            {
                                strsql = "select GroupName,uComapanyJC from GroupItem where uComapanyBH='"+uComapanyBH+"'";
                                DataTable gdt=sqlhelper.GetDataTable(strsql);
                                if(gdt.getTotalCount()>0)
                                {
                                    person.setParentunit(gdt.getRow(0).getColValue("GroupName").toString());
                                    person.setUComapanyJC(gdt.getRow(0).getColValue("uComapanyJC").toString());
                                }
                            }

                            //获得申报单位用户状态
                            strsql = "select state,backid,IsHg from T_WorkFlow where userID='"+person.getId()+"'";
                            DataTable wdt=sqlhelper.GetDataTable(strsql);
                            if(wdt.getTotalCount()>0)
                            {
                                person.setWorkFlowBackId(Integer.parseInt(wdt.getRow(0).getColValue("backid").toString()));
                                person.setWorkFlowIsHg(Integer.parseInt(wdt.getRow(0).getColValue("IsHg").toString()));
                                person.setWorkFlowStart(Integer.parseInt(wdt.getRow(0).getColValue("state").toString()));
                            }

                            if (StringUtil.IsNullOrEmpty(person.getChoseNF()))
                            {
                                person.setChoseNF(String.valueOf(new Date().getYear()));
                            }


                            //更新用户登录信息
                            String did=udt.getRow(0).getColValue("id").toString();
                            ServletContext context= request.getServletContext();
                            String uip1= request.getHeader("HTTP_X_FORWARDED_FOR");
                            strsql="update T_UserData ulandingtime=getdate(),uloginnumber=isnll(uloginnumber,-1)+0,uloginIP3=isnull(uloginIP2,''),uloginIP2=isnull(uloginIP1,''),uloginIP1='"+uip1+"' set where id="+did;
                            sqlhelper.ExecuteNoQuery(strsql);
                        }
                    }

                    //保存session到数据库，和老代码兼容
                    SessionHelper.SessionAddPC(token,loginname);

                    //保存返回次数数据
                    strsql="update AccessCount set AccessCount=AccessCount+1 where Id=1";
                    sqlhelper.ExecuteNoQuery(strsql);

                    //登记日志
                    DBHelper.AddApplicationLog(person.getRealName()+"("+person.getLoginName()+")","用户管理","系统管理","登录成功");

                    //同步APP操作用cookie信息
                    if(ConfigHelper.getRunMode().equals("prod"))
                    {
                        SyncAppCookie(loginname,response);
                    }

                    //模拟登录netframe
                    //String utoken=SyncGetNetCookie(loginname);
                    //ConfigHelper.SaveUToken(utoken,loginUser.getToken());
                    //ajax.put("utoken",loginUser.getToken());
                }
                ajax.put("person",person);
            }
            return ajax;
        }catch(CaptchaExpireException ex)
        {
            DBHelper.AddApplicationLog(loginname,"登录异常","登录异常","登录失败,用户登录信息过期");
            LogHelper.WriteSysLog("用户登录信息过期！");
            ajax=AjaxResult.error("用户登录信息过期！");
        }catch(CaptchaException ex)
        {
            DBHelper.AddApplicationLog(loginname,"登录异常","登录异常","登录失败,登录Token异常！");
            LogHelper.WriteSysLog("系统异常，登录Token异常！");
            ajax=AjaxResult.error("系统异常，登录Token异常！");
        }catch(Exception ex)
        {
            DBHelper.AddApplicationLog(loginname,"登录异常","登录异常","登录失败,用户登录时产生异常");
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("用户登录时产生异常！");
        }
        return ajax;
    }


    private String SyncGetNetCookie(String loginName)
    {
        try
        {
            String contentUrl=ConfigHelper.getNetFramePath()+"/Service/NFrame/IML_Service.ashx?Method=GetToken";
            logger.info("SyncGetNetCookie,url="+contentUrl);

            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);

            CloseableHttpClient httpClient = HttpClientBuilder.create().setSSLSocketFactory(sslsf).build();
            HttpGet httpGet = new HttpGet(contentUrl);
            httpGet.setHeader("ContentType","text/javascript;charset=UTF-8");
            httpGet.setHeader("tp",loginName);

            HttpResponse httpResponse = httpClient.execute(httpGet);

            // 5、获取响应结果, 状态码 200 表示请求成功
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                logger.info("SyncGetNetCookie,statusCode=200");
                String line = null;
                JSONObject jsonObject = null;
                StringBuilder sb = new StringBuilder();

                BufferedReader b = new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(), "UTF-8"), 8 * 1024);
                while ((line = b.readLine()) != null) {
                    sb.append(line);
                }
                jsonObject = JSONObject.parseObject(sb.toString());
                if(jsonObject!=null)
                {
                    logger.info("jsonObject!=null");
                    String datastr=jsonObject.getString("data");
                    if(!StringUtil.IsNullOrEmpty(datastr))
                    {
                        APP_SHAKE shake=JSONObject.parseObject(datastr,APP_SHAKE.class);
                        String token=shake.getTOKEN();

                        return token;
                    }
                }
            }

            return "";
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            return "";
        }
    }

    private void SyncAppCookie(String loginName,HttpServletResponse response)
    {
        try
        {
            //调用java端登录，并获取cookie
            String contentUrl = ConfigHelper.getWetRoot()+"/app/sys/pcLogin/" + loginName + "?token=3ac15028c6abe0c64d43140531696a5";


            CloseableHttpClient httpClient = HttpClientBuilder.create().build();

            HttpGet httpGet = new HttpGet(contentUrl);
            httpGet.setHeader("ContentType","text/html;charset=UTF-8");

            HttpResponse httpResponse = httpClient.execute(httpGet);

            // 5、获取响应结果, 状态码 200 表示请求成功
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                List<String> list1 = new ArrayList<String>();
                if (httpResponse.getHeaders("Set-Cookie") != null)
                {
                    Header[] cooks=httpResponse.getHeaders("Set-Cookie");
                    for (Header hh:cooks) {
                        list1.add(hh.getName()+"="+hh.getValue());
                    }
                }

                String jtoken = "";
                for (int i = 0; i < list1.size(); i++)
                {
                    if (list1.get(i).contains("jtoken"))
                    {
                        jtoken =list1.get(i);
                    }
                }
                if (jtoken == "")
                {
                }
                else
                {
                    String jtoken1 = jtoken.split("=")[1].replace("; Path", "");

                    Cookie objCookie = new Cookie("jtoken", jtoken1);
                    objCookie.setPath("/");
                    response.addCookie(objCookie);
                }

                return;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            return;
        }
    }


    @RequestMapping(value="/LogOut",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="LogOut",notes="登录退出接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult LogOut(HttpServletRequest request)
    {
        AjaxResult ajax = null;
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (StringUtil.isNotNull(loginUser)) {
                int userId = loginUser.getUser().getId();
                String userName = loginUser.getUsername();
                tokenService.delLoginUser(loginUser.getToken());
                // 记录用户退出日志
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
            }
            ajax = AjaxResult.success("用户退出登录成功！");
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("用户退出登录时产生异常！");
        }
        return ajax;
    }


    @RequestMapping(value="/PostYzm",method ={RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="PostYzm",notes="发送验证码接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult PostYzm(HttpServletRequest request)
    {
        AjaxResult ajax = null;
        try {
            String loginName=request.getParameter("loginName");
            if(loginName==null&&loginName.equals("")||loginName.equals("undefined"))
            {
                ajax=AjaxResult.error("传输参数有误");
                return ajax;
            }

            PersonEntity person = DBHelper.GetPersonByLoginName(loginName);
            if (person == null) {
                ajax=AjaxResult.error("非法的登录用户账户信息！");
                return ajax;
            }

            String mobile = person.getTelephone();
            String userip = MetaHelper.GetCurrentIp(request);
            String useragent = request.getHeader("HTTP_USER_AGENT");

            String ErrorMsg = "";
            qx_yzm qx = DBHelper.get_yzmMessage(loginName,mobile, userip, useragent);
            if (qx != null)
            {
                if (qx.getMessage() == "" || qx.getMessage()  == null || qx.getMessage()  == "null")
                {
                    ErrorMsg = qx.getData();
                }
                else
                {

                    ErrorMsg = qx.getMessage();
                }
            }

            ajax=AjaxResult.success(ErrorMsg);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("发送验证码接口产生异常！");
            return ajax;
        }
    }

    @RequestMapping(value="/RegisterScanResult",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="RegisterScanResult",notes="-APP登记Scan结果接口")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true),
        @ApiImplicitParam(name = "params", value = "Map<String, String>", required = true)
    })
    public AjaxResult RegisterScanResult(HttpServletRequest request,@RequestParam Map<String, String> params)
    {
        AjaxResult ajax = null;
        String strsql = "";
        SqlHelper sqlhelper=new SqlHelper();

        try {

            String key=request.getParameter("key");
            if (key==null||key.equals("")||key.equals("undefined"))
            {
                ajax=AjaxResult.error("传输参数有误");
                return ajax;
            }

            //不需要像C#一样判断token头，token会在tokefliter中被解析处理
            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("传输参数有误,未携带有效token信息！");
                return ajax;
            }

            strsql = "insert into NFT_SCAN_RESULT(RES_DT,RES_KEY,RES_USER) values(getdate(),'"+key+"','"+person.getLoginName()+"')";
            sqlhelper.ExecuteNoQuery(strsql);

            ajax=AjaxResult.error("扫码结果登记完成！");
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("APP登记Scan结果接口产生系统异常！");
            return ajax;
        }
    }

    @RequestMapping(value="/CheckScanResult",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="CheckScanResult",notes="检测二维码扫描结果接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult CheckScanResult(HttpServletRequest request)
    {
        AjaxResult ajax = null;
        String strsql = "";
        SqlHelper sqlhelper=new SqlHelper();

        try {
            String key=request.getParameter("key");
            if (key==null||key.equals(""))
            {
                ajax=AjaxResult.error("传输参数有误");
                return ajax;
            }

            String fpath =ConfigHelper.getTempPath();
            String fname = key + ".png";
            if (FileUtil.FileExists(fpath + "/" + fname)) {
                FileUtil.Delete(fpath + "/" + fname);
            }

            strsql = "select top 1 RES_USER from NFT_SCAN_RESULT where RES_DT>DATEADD(mi,-2,getdate()) and RES_KEY='"+key+"'";
            String tmps=sqlhelper.ExecuteScalar(strsql);
            if (tmps.equals(""))
            {
                ajax=AjaxResult.error("获取扫码结果失败");
                return ajax;
            }
            else
            {
                ajax=AjaxResult.success("获取扫码结果完成!");
                return ajax;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("获取扫码结果产生系统异常！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetScanImage",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetScanImage",notes="获取二维码图片返回二进制流")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetScanImage(HttpServletRequest request){
        AjaxResult ajaxResult=null;
        try
        {
            String uuid = UUID.randomUUID().toString();
            String fpath = ConfigHelper.getTempPath();
            String fname = uuid + ".jpg";
            String filename = fpath +fname;
            if (FileUtil.FileExists(fpath + fname)) {
                FileUtil.Delete(fpath + fname);
            }

            if(!MetaHelper.GenerateQRCode(uuid, filename))
            {
                ajaxResult=AjaxResult.error("生成二维码失败!");
                return ajaxResult;
            }

            String fullfile = fpath + fname;
            if(FileUtil.FileExists(fullfile))
            {
                byte[] fb=ToolHelper.File2Bytes(fullfile);
                ajaxResult=AjaxResult.success("生成二维码成功!");
                ajaxResult.put("skey",uuid);
                ajaxResult.put("image",fb);
                return ajaxResult;
            }else
            {
                ajaxResult = AjaxResult.error("下载图片文件失败!");
                return ajaxResult;
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajaxResult =AjaxResult.error("下载图片文件失败产生系统异常！");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetToken",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetToken",notes="APP获取token接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetToken(HttpServletRequest request)
    {
        AjaxResult ajax = null;
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();

        try {
            String loginName=request.getHeader("tp");
            if(StringUtil.IsNullOrEmpty(loginName))
            {
                loginName=request.getHeader("Tp");
            }

            if (loginName.equals(""))
            {
                ajax= AjaxResult.error("握手失败，未携带有效信息！");
                ajax.put("status","-1");
                return ajax;
            }

            PersonEntity sperson=DBHelper.GetPersonByLoginName(loginName);
            if(sperson==null)
            {
                ajax= AjaxResult.error("握手失败，传输参数有误！");
                ajax.put("status","-2");
                return ajax;
            }
            String UId = sperson.getId().toString();
            String token = loginService.loginAPP(request,loginName,sperson.getPassword());

            //记录登录日志
            LoginUser loginUser = tokenService.getLoginUser(token);
            if(loginUser==null||loginUser.getUser()==null)
            {
                ajax= AjaxResult.error("握手失败，系统异常！");
                ajax.put("status","-3");
                return ajax;
            }

            PersonEntity person=loginUser.getUser();
            if (person.getState() != 1)
            {
                ajax= AjaxResult.error("握手失败，用户当前已经被禁用！");
                ajax.put("status","-4");
                return ajax;
            }else
            {
                //保存session到数据库，和老代码兼容
                SessionHelper.SessionAddAPP(token, loginName);

                //登记日志
                DBHelper.AddApplicationLog(person.getRealName() + "(" + person.getLoginName() + ")", "用户管理", "系统管理", "APP登录成功");

                sqlhelper.ExecuteNoQuery("update APP_SHAKE set state=1 where phone='"+loginName+"' and uid="+UId);
                int timeout=-1;

                //登记握手信息
                //int Timeout=ConfigHelper.getSessionTimeOut();
                APP_SHAKE shake=new APP_SHAKE();
                shake.setPHONE(loginName);
                shake.setTOKEN(token);
                shake.setUID(Integer.parseInt(UId));
                shake.setSHDT(DateUtil.formatDate(DateUtil.datetimeFormatf.toPattern()));
                shake.setEXPIRE(DateUtil.getCurrDateAddMinute(ConfigHelper.getSessionTimeOut()));
                shake.setSTATE(0);

                //提交数据库
                strsql = DBHelper.GetInsertSQL(shake,"APP_SHAKE", Arrays.asList("ID"));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    ajax= AjaxResult.error("握手失败，内部操作失败！");
                    ajax.put("status","-2");
                    return ajax;
                }
                sqlhelper.ExecuteScalar(strsql);

                ajax= AjaxResult.success("握手成功！");
                ajax.put("status","0");
                ajax.put("shake",shake);
                return ajax;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("获取Token信息产生异常！");
            return ajax;
        }
    }


    @RequestMapping(value="/ReleaseAppSession",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ReleaseAppSession",notes="重置APP Session 接口")
    public AjaxResult ReleaseAppSession()
    {
        AjaxResult ajax=null;
        if(ConfigHelper.getRunMode().equals("test"))
        {
            ajax=AjaxResult.success("测试模式下，不需要调有该项功能！");
            return ajax;
        }
        try
        {
            PersonEntity person=SessionHelper.getSessionPerson();
            //调用java端登录，并获取cookie
            String contentUrl = "https://szyy.yongyaokjit.com:4435/app/sys/pcLogin/" + person.getLoginName() + "?token=3ac15028c6abe0c64d43140531696a5";


            CloseableHttpClient httpClient = HttpClientBuilder.create().build();

            HttpGet httpGet = new HttpGet(contentUrl);
            httpGet.setHeader("ContentType","text/html;charset=UTF-8");

            HttpResponse httpResponse = httpClient.execute(httpGet);

            // 5、获取响应结果, 状态码 200 表示请求成功
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                ajax=AjaxResult.success("调用清除APP session成功！");
                return ajax;
            }else
            {
                ajax=AjaxResult.error("调用清除APP session失败！");
                return ajax;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("重置APP Session产生异常！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetCurrentPerson",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetCurrentPerson",notes="获取当前用户信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetCurrentPerson(HttpServletRequest request)
    {
        //LogHelper.WriteSysLog("检测点00!");
        AjaxResult ajax = null;
        try
        {
            if (tokenService == null || request == null) {
                ajax = AjaxResult.error("tokenService为空!");
                return ajax;
            }
            LoginUser loginUser = tokenService.getLoginUser(request);
            String strsql = "";
            SqlHelper sqlhelper = new SqlHelper();

            if (loginUser != null) {
                //LogHelper.WriteSysLog("检测点01!");
                PersonEntity person = loginUser.getUser();
                if (person != null) {
                    //LogHelper.WriteSysLog("检测点02!");
                    strsql = "select photo from app_person_msg_code where u_id=" + person.getId();
                    String ico = sqlhelper.ExecuteScalar(strsql);
                    if (StringUtil.IsNullOrEmpty(ico)) {
                        person.setHeadIco("");
                    } else {
                        //LogHelper.WriteSysLog("检测点03!");
                        String imgurl1 = ConfigHelper.getWpFrame() +"/"+ ConfigHelper.getOssRemotePath() + "FACE/" + ico;
                        String imgurls1 = imgurl1 + "?jtoken=" + loginUser.getToken();
                        person.setHeadIco(imgurls1);
                    }

                    //LogHelper.WriteSysLog("检测点04!");
                    ajax = AjaxResult.success("获取用户信息成功!", person);

                    //模拟登录netframe
                    if (ConfigHelper.getRunMode().equals("prod")) {
                        String utoken = SyncGetNetCookie(person.getLoginName());
                        ConfigHelper.SaveUToken(utoken, utoken);
                        ajax.put("utoken", utoken);
                        logger.info("netframe,utoken="+utoken);
                    }
                } else {
                    LogHelper.WriteSysLog("获取用户信息失败!");
                    ajax = AjaxResult.error("获取用户信息失败!");
                }
            } else {
                LogHelper.WriteSysLog("获取用户信息失败!");
                ajax = AjaxResult.error("获取用户信息失败!");
            }
            return ajax;
        }catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("获取用户信息产生异常！");
            return ajax;
        }
    }

    @RequestMapping(value="/AppLogOut",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="AppLogOut",notes="退出登录接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult AppLogOut(HttpServletRequest request)
    {
        AjaxResult ajax = null;
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (StringUtil.isNotNull(loginUser)) {
                int userId = loginUser.getUser().getId();
                String userName = loginUser.getUsername();
                tokenService.delAppLoginUser(loginUser.getToken());
                // 记录用户退出日志
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功"));
            }
            ajax = AjaxResult.success("用户退出登录成功！");
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            ajax=AjaxResult.error("用户退出登录时产生异常！");
        }
        return ajax;
    }
}
