package com.soft.framework.helper;

import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.nio.charset.StandardCharsets;

public class DesHelper {
    // 密钥
    final static String key = "www.y-dashi.com@qidefang";

    // 偏移量
    final static String iv = "www.y-dashi.com@qidefang";

    public static String decrypt(String message) {
        try {
            return new DesHelper().decrypt(message, key, iv);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String encrypt(String message) {
        try {
            byte[] b = new DesHelper().encrypt(message, key, iv);
            return toHexString(b).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 解密数据
    private String decrypt(String message, String key, String iv) throws Exception {
        iv = DigestUtils.md5Hex(key).toUpperCase().substring(0, 8);
        key = DigestUtils.md5Hex(key).toUpperCase().substring(0, 8);
        byte[] bytesrc = convertHexString(message);
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.US_ASCII));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        IvParameterSpec ivParameter = new IvParameterSpec(iv.getBytes(StandardCharsets.US_ASCII));
        cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameter);
        byte[] retByte = cipher.doFinal(bytesrc);
        return new String(retByte);
    }

    private byte[] encrypt(String message, String key, String iv) throws Exception {
        iv = DigestUtils.md5Hex(key).toUpperCase().substring(0, 8);
        key = DigestUtils.md5Hex(key).toUpperCase().substring(0, 8);
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.US_ASCII));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);
        IvParameterSpec ivParameter = new IvParameterSpec(iv.getBytes(StandardCharsets.US_ASCII));
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivParameter);
        return cipher.doFinal(message.getBytes(StandardCharsets.US_ASCII));
    }

    private static byte[] convertHexString(String ss) {
        if (ss.length() > 0) {
            ss = ss.replace("-", "");
        }
        byte digest[] = new byte[ss.length() / 2];
        for (int i = 0; i < digest.length; i++) {
            String byteString = ss.substring(2 * i, 2 * i + 2);
            int byteValue = Integer.parseInt(byteString, 16);
            digest[i] = (byte) byteValue;
        }
        return digest;
    }

    private static String toHexString(byte b[]) {
        StringBuffer hexString = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            String plainText = Integer.toHexString(0xff & b[i]);
            if (plainText.length() < 2) {
                plainText = "0" + plainText;
            }
            hexString.append("-").append(plainText);
        }
        String str = "";
        if (hexString.length() > 0) {
            str = hexString.toString().substring(1);
        }
        return str;
    }
}
