package com.soft.framework.controller;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.ConfigHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
@Configuration
@RequestMapping("/" )
@RefreshScope
public class MetaController {
    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    @RequestMapping("/wpstest")
    public String test(){
        return "redirect:./TStub01.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/WpsTest")
    public String Test(){
        return "redirect:./TStub01.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/")
    public String Default(){
        return "redirect:./Page/Frame/Frame.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/Index")
    public String Protal(){
        return "redirect:./Page/Frame/Frame.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/index")
    public String protal(){
        return "redirect:./Page/Frame/Frame.html?v="+String.valueOf(Math.random());
    }

    @RequestMapping("/Frame")
    public void Frame(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String uuid=request.getParameter("ut");
        if(!StringUtil.IsNullOrEmpty(uuid))
        {
            String utoken= ConfigHelper.GetUToken(uuid);
            Cookie cookie=new Cookie("access_token",utoken);
            cookie.setHttpOnly(true);
            cookie.setPath("/");
            response.addCookie(cookie);
            response.sendRedirect("./Frame.html?v="+String.valueOf(Math.random()));
        }
    }

    @RequestMapping("/frame")
    public void frame(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String uuid=request.getParameter("ut");
        if(!StringUtil.IsNullOrEmpty(uuid))
        {
            String utoken= ConfigHelper.GetUToken(uuid);
            Cookie cookie=new Cookie("access_token",utoken);
            cookie.setHttpOnly(true);
            cookie.setPath("/");
            response.addCookie(cookie);
            response.sendRedirect("./Frame.html?v="+String.valueOf(Math.random()));
        }
    }
}
