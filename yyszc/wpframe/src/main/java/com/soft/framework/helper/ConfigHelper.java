package com.soft.framework.helper;

import com.soft.framework.common.constant.Constants;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.config.AppConfig;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.config.OssConfig;
import com.soft.framework.redis.RedisCache;
import com.soft.framework.security.service.TokenService;
import org.springframework.context.ApplicationContext;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

public final class ConfigHelper {
    private static String TitleInfo;         //标题串
    private static String DBFeatures;        //数据库类型串
    private static String DBSchema;          //数据库标识（mysql适用）
    private static String StaticPath;        //静态资源根路径
    private static String ContextRealPath;   //上下文绝对路径
    private static String ContextPath;       //上下文

    public static String GetUToken(String uuid) {
        try {
            RedisCache redisCache=SpringUtil.getBean("redisCache", RedisCache.class);
            if(redisCache!=null) {
                String utokenkey=Constants.UTOKEN_KEY + uuid;
                String utoken=redisCache.getCacheObject(utokenkey);
                return utoken;
            }
            return null;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static void SaveUToken(String utoken,String uuid) {
        try {
            RedisCache redisCache=SpringUtil.getBean("redisCache", RedisCache.class);
            if(redisCache!=null) {
                String utokenkey=Constants.UTOKEN_KEY + uuid;
                redisCache.deleteObject(utokenkey);
                redisCache.setCacheObject(utokenkey,utoken,ConfigHelper.getSessionTimeOut(),TimeUnit.MINUTES);
                return;
            }
        }catch(Exception Ex)
        {
            return;
        }
    }

    public static void SaveShakeCode(String appmark,String shakecode) {
        try {
            RedisCache redisCache=SpringUtil.getBean("redisCache", RedisCache.class);
            if(redisCache!=null) {
                redisCache.deleteObject(Constants.SHAKE_CODE_KEY + shakecode);
                redisCache.setCacheObject(Constants.SHAKE_CODE_KEY+ shakecode,appmark,ConfigHelper.getSessionTimeOut(),TimeUnit.MINUTES);
            }
        }catch(Exception Ex)
        {

        }
    }

    public static String GetShakeCodeAppInfo(String shakecode) {
        try {
            RedisCache redisCache=SpringUtil.getBean("redisCache", RedisCache.class);
            if(redisCache!=null) {
                String appmark=redisCache.getCacheObject(Constants.SHAKE_CODE_KEY + shakecode);
                return appmark;
            }else
            {
                return "";
            }
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static String getCurrentToken() {
        String token="";
        HttpServletRequest req= ServletUtils.getRequest();
        TokenService tokenService=SpringUtil.getBean("tokenService",TokenService.class);
        if(tokenService!=null)
        {
            token=tokenService.getToken(req);
            return (token==null)?"":token;
        }
        return token;
    }

    public static String getTokenKey(String uuid)
    {
        return Constants.LOGIN_TOKEN_KEY + uuid;
    }

    public static String getAppTokenKey(String uuid)
    {
        return Constants.APP_LOGIN_TOKEN_KEY + uuid;
    }

    public static Integer getServerPort() {
        return AppConfig.getPort();
    }

    public static String getCachePath() {
        return BaseConfig.getCachePath();
    }

    public static String getDBFeatures() {
        return DBFeatures;
    }

    public static String getWhiteList() {
        return BaseConfig.getWhiteList();
    }

    public static String getTokenSecret() {
        return BaseConfig.getTokenSecret();
    }

    public static String getDBSchema() {
        return DBSchema;
    }

    public static String getSysLogPath() {
        return BaseConfig.getLogPath()+"SysLog/";
    }

    public static String getProfile() {
        return BaseConfig.getProfile();
    }

    public static String getSqlLogPath() {
        return BaseConfig.getLogPath()+"SqlLog/";
    }

    public static String getContextRealPath() {
        return ContextRealPath;
    }

    public static String getContextPath() {
        return ContextPath;
    }

    public static boolean isRecDBLog() {
        return (BaseConfig.getSqlTg()==1);
    }

    public static boolean isOSSOpen() {
        return OssConfig.getOpenFlag().equals("true");
    }

    public static String getOssLocalPath() {
        return BaseConfig.getOssLocalPath();
    }

    public static String getOssRemotePath() {
        return BaseConfig.getOssRemotePath();
    }

    public static String getWpFrame() {
        return BaseConfig.getWpFrame();
    }

    public static String getWetRoot() {
        return BaseConfig.getWebRoot();
    }

    public static boolean isLogFlag() {
        return (BaseConfig.getLogFlag()==1);
    }

    public static boolean isSmsFlag() {
        return (BaseConfig.getSmsFlag()==1);
    }

    public static boolean isNoticeSmsFlag() {
        return (BaseConfig.getSmsFlag()==1);
    }

    public static String getSystemMark() {
        return BaseConfig.getSystemMark();
    }

    public static boolean isLoadDBConf() {
        return (BaseConfig.getLoadDBConf()==1);
    }

    public static String getfSepChar() {
        return BaseConfig.getSeparator();
    }

    public static String getTitleInfo() {
        return TitleInfo;
    }

    public static String getUploadPath() {
        return BaseConfig.getUploadPath();
    }

    public static String getDownloadPath() {
        return BaseConfig.getDownloadPath();
    }

    public static String getTempPath() {
        return BaseConfig.getTemplatePath();
    }

    public static String getHotupdatePath() {
        return BaseConfig.getHotupdatePath();
    }

    public static String getReportPath() {
        return BaseConfig.getReportPath();
    }

    public static String getNetFramePath() {
        return BaseConfig.getNetFrame();
    }

    public static String getMobanPath() {
        return BaseConfig.getMobanPath();
    }

    public static String getStaticPath() {
        return StaticPath;
    }

    public static String getExtVersion() {
        return BaseConfig.getExtVersion();
    }

    public static int getSessionTimeOut() {
        return BaseConfig.getSessionTimeOut();
    }

    public static String getRunMode() {
        return BaseConfig.getRunMode();
    }

    public static int getIsOpen() {
        return BaseConfig.getIsOpen();
    }

    static {
        try {
            initProc();
        } catch (Exception ex) {
            String err=ex.getMessage();
        }
    }

    private static void initProc() {
        DBFeatures = "mssql";
        DBSchema = "JTSZHManage";
        FileUtil.CreateDir(getCachePath());
        FileUtil.CreateDir(getTempPath());
        FileUtil.CreateDir(getReportPath());
        FileUtil.CreateDir(getSysLogPath());
        FileUtil.CreateDir(getSqlLogPath());
        FileUtil.CreateDir(getDownloadPath());
        FileUtil.CreateDir(getHotupdatePath());
        FileUtil.CreateDir(getUploadPath());

        ApplicationContext context = SpringUtil.getApplicationContext();
        ContextPath = context.getApplicationName();
    }
}
