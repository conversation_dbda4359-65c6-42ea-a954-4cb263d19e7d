package com.soft.framework.controller;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.base.entity.NFT_LOGIN_UC;
import com.soft.gcc.base.ventity.vModule;
import com.yyszc.wpbase.entity.Module;
import com.yyszc.wpbase.entity.NFT_ModuleGroup;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Controller
@Configuration
@RequestMapping(value="/Service/Frame" )
@Api(tags ="微服务框架接口->框架页模块信息接口")
public class FrameController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/GetCurrentUccOptionList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetCurrentUccOptionList",notes="白名单设置接口")
    public AjaxResult GetCurrentUccOptionList(HttpServletRequest request)
    {
        AjaxResult ajax=null;
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();

        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("未发现登录信息！");
                return ajax;
            }

            strsql = "select * from NFT_LOGIN_UC where RU_USER='" + person.getLoginName() + "'";
            List<NFT_LOGIN_UC> list = sqlhelper.GetObjectList(NFT_LOGIN_UC.class,strsql);

            ajax=AjaxResult.extgrid(NFT_LOGIN_UC.class,list.size(),list);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetModuleGroupInfo",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetModuleGroupInfo",notes="用户Protal模块分组下辖模块接口")
    public AjaxResult GetModuleGroupInfo(HttpServletRequest request)
    {
        AjaxResult ajax=null;
        int pwf = SecToolHelper.UserIsInNWWD(request);
        SqlHelper sqlhelper=new SqlHelper();

        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("未发现登录信息！");
                return ajax;
            }

            if(person.getRoleNamesString().contains("集团审计"))
            {
                String strsql="elect distinct a.module_id from func_usr_func(" + person.getId() + "," + pwf + ") a,module b where a.module_Id=b.ID and b.is_show=1";
                String mdlist=sqlhelper.ExecuteScalarList(strsql);

                String wsql="";
                String[] mdarr=mdlist.split(",");
                for(int i=0;i<mdarr.length;i++)
                {
                    if(!wsql.equals("")) {
                        wsql+=" or gp_mlist like '%"+mdarr[i]+"%'";
                    } else {
                        wsql+=" gp_mlist like '%"+mdarr[i]+"%'";
                    }
                }

                strsql="select * from NFT_ModuleGroup where 1=1 and ("+wsql+") order by gp_id";
                List<NFT_ModuleGroup> _mglist=sqlhelper.GetObjectList(NFT_ModuleGroup.class,strsql);

                ajax=AjaxResult.success("获取信息成功!");
                ajax.put("mglist",_mglist);
            }else {
                String strsql="select * from NFT_ModuleGroup order by gp_id";
                List<NFT_ModuleGroup> _mglist=sqlhelper.GetObjectList(NFT_ModuleGroup.class,strsql);
                ajax=AjaxResult.success("获取信息成功!");
                ajax.put("mglist",_mglist);
            }

            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetUserProtalInfo",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetUserProtalInfo",notes="用户Protal信息接口")
    public AjaxResult GetUserProtalInfo(HttpServletRequest request)
    {
        AjaxResult ajax=null;
        SqlHelper sqlhelper=new SqlHelper();

        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("未发现登录信息！");
                return ajax;
            }

            ajax=AjaxResult.success("获取信息成功");
            ajax.put("sLogInfo",person.getTopGroupName() + "&nbsp;&nbsp;" + person.getRealName() + "&nbsp;&nbsp;你好!");

            String password = sqlhelper.ExecuteScalar("select password from person where id=" + person.getId());
            String defpwdf = (password == "w1A9rdnf1rwQJf0EZwwzdA==") ? "true" : "false";
            ajax.put("defpwdf",defpwdf);

            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetUserModuleInfo_ZDY",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetUserModuleInfo_ZDY",notes="用户Protal模块分组下辖自定义模块接口")
    public AjaxResult GetUserModuleInfo_ZDY(HttpServletRequest request)
    {
        AjaxResult ajax=null;
        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("未发现登录信息！");
                return ajax;
            }

            List<vModule> _nallist=new ArrayList<vModule>();
            GetGNData_ZDY(_nallist);
            for(vModule vo:_nallist)
            {
                vo.setModule_mix(ToolHelper.MixedMenuId(vo.getID()));
            }

            ajax=AjaxResult.success("获取信息成功！");
            ajax.put("navlist",_nallist);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetUserModuleInfo_QYDZ",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "token", value = "String", required = true)
    @ApiOperation(value ="GetUserModuleInfo_QYDZ",notes="用户Protal模块分组下辖企业定制模块接口")
    public AjaxResult GetUserModuleInfo_QYDZ(HttpServletRequest request)
    {
        AjaxResult ajax=null;

        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("未发现登录信息！");
                return ajax;
            }

            List<vModule> _nallist=new ArrayList<vModule>();
            GetGNData_QYDZ(_nallist);
            for(vModule vo:_nallist)
            {
                vo.setModule_mix(ToolHelper.MixedMenuId(vo.getID()));
            }

            ajax=AjaxResult.success("获取信息成功！");
            ajax.put("navlist",_nallist);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetUserModuleInfo",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetUserModuleInfo",notes="用户Protal模块分组下辖模块接口")
    public AjaxResult GetUserModuleInfo(HttpServletRequest request)
    {
        AjaxResult ajax=null;
        SqlHelper sqlhelper=new SqlHelper();

        try {
            int gpid=0;
            String sgpid=request.getParameter("gpid");
            if(!StringUtil.IsNullOrEmpty(sgpid)) {
                gpid=Integer.parseInt(sgpid);
            }

            PersonEntity person = SessionHelper.getSessionPerson();
            if(person==null)
            {
                ajax=AjaxResult.error("未发现登录信息！");
                return ajax;
            }

            String mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID="+gpid);
            if(StringUtil.IsNullOrEmpty(mlist))
            {
                ajax=AjaxResult.error("未发现定义信息！");
                return ajax;
            }

            List<vModule> _nallist=new ArrayList<vModule>();
            GetGNData_QT( mlist,_nallist);
            for(vModule vo:_nallist)
            {
                vo.setModule_mix(ToolHelper.MixedMenuId(vo.getID()));
            }

            ajax=AjaxResult.success("获取信息成功！");
            ajax.put("navlist",_nallist);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    private Boolean GetGNData_ZDY(List<vModule> _navlist)
    {
        int pwf = SecToolHelper.UserIsInNWWD(SessionHelper.getRequest());

        PersonEntity person = SessionHelper.getSessionPerson();
        SqlHelper sqlhelper=new SqlHelper();


        String mlist = sqlhelper.ExecuteScalarList("select lk_nid from nft_navulink where lk_uid=" + person.getId());
        try {
            String strsql = "";
            if(mlist != "") {
                strsql = "select distinct b.* from (select * from func_usr_func(" + person.getId() + "," + pwf + ")) a,module b where a.module_Id=b.ID and b.is_show=1 and b.id in(" + mlist + ") order by b.module_num ";
            } else {
                strsql = "select distinct b.* from (select * from func_usr_func(" + person.getId() + "," + pwf + ")) a,module b where a.module_Id=b.ID and b.is_show=1 and 1=0 order by b.module_num ";
            }
            List<Module> _list = sqlhelper.GetObjectList(Module.class,strsql);
            int rnum = _list.size();
            //if (rnum<= 0) return;

            //后期合并功能选项qidefang2021-03-19
            String modulestr = "";
            for(Module mobj : _list) {
                if(modulestr == "") {
                    modulestr = mobj.getID().toString();
                } else {
                    modulestr = modulestr + "," + mobj.getID().toString();
                }
            }

            String strsql1 = "";
            if(modulestr != "") {
                strsql1 = "select distinct b.* from module b where b.ID not in(" + modulestr + ") and b.is_show=1 order by b.module_num ";
            } else {
                strsql1 = "select distinct b.* from module b where 1=0 and b.is_show=1 order by b.module_num ";
            }

            List<vModule> _navlist_p = sqlhelper.GetObjectList(vModule.class,strsql1);
            _navlist.addAll(_navlist_p);

            return true;

        } catch(Exception Ex) {
            return false;
        }
    }

    //功能模块-企业定制
    private Boolean GetGNData_QYDZ(List<vModule> _navlist)
    {
        int pwf = SecToolHelper.UserIsInNWWD(SessionHelper.getRequest());

        PersonEntity person =SessionHelper.getSessionPerson();
        SqlHelper sqlhelper=new SqlHelper();
        
        String mlist = sqlhelper.ExecuteScalar("select QYDZ_MLIST from NFT_ModuleQydz where QYDZ_CID="+person.getTopGroupId());
        if(mlist=="")
        {
            return false;
        }

        try
        {
            String strsql = "select distinct b.* from (select * from func_usr_func(" + person.getId() + "," + pwf + ")) a,module b where a.module_Id=b.ID and b.is_show=1 and b.id in(" + mlist + ") order by b.module_num ";
            List<Module> _list = sqlhelper.GetObjectList(Module.class,strsql);
            int rnum = _list.size();
            if (rnum<= 0) {
                return false;
            }

            //后期合并功能选项qidefang2021-03-19
            String modulestr = "";
            for(Module mobj:_list) {
                if (modulestr == "") {
                    modulestr = mobj.getID().toString();
                } else {
                    modulestr = modulestr + "," + mobj.getID().toString();
                }
            }

            String strsql1 = "";
            if(modulestr != "") {
                strsql1 = "select distinct b.* from module b where b.ID  in(" + modulestr + ") and b.is_show=1 order by b.module_num ";
            } else {
                strsql1 = "select distinct b.* from module b where 1=0 and b.is_show=1 order by b.module_num ";
            }

            List<vModule> _navlist_p = sqlhelper.GetObjectList(vModule.class,strsql1);
            _navlist.addAll(_navlist_p);

            return true;

        } catch(Exception Ex){
            return false;
        }
    }

    //功能模块-其他模块
    private Boolean GetGNData_QT(String mlist, List<vModule> _navlist)
    {
        int pwf = SecToolHelper.UserIsInNWWD(SessionHelper.getRequest());
        PersonEntity person =SessionHelper.getSessionPerson();
        SqlHelper sqlhelper=new SqlHelper();

        try
        {
            String rmnum = sqlhelper.ExecuteScalar("select count(*) from module b where b.ID in(" + mlist + ") and b.is_show=1");
            if (rmnum == "0")
            {
                return false;
            }

            String strsql = "select distinct b.* from (select * from func_usr_func(" + person.getId() + "," + pwf + ")) a,module b where a.module_Id=b.ID and b.is_show=1 and b.id in(" + mlist + ") order by b.module_num ";
            List <Module> _list = sqlhelper.GetObjectList(Module.class,strsql);
            int rnum = _list.size();
            if(rnum <= 0) {
                return false;
            }

            //后期合并功能选项qidefang2021-03-19
            String modulestr = "";
            for(Module mobj:_list) {
                if(modulestr == "") {
                    modulestr = mobj.getID().toString();
                } else {
                    modulestr = modulestr + "," + mobj.getID().toString();
                }
            }

            String strsql1= "";
            if (modulestr != "")
            {
                strsql1 = "select distinct b.* from module b where b.ID in(" + modulestr + ") and b.is_show=1 order by b.module_num ";
            }
            else
            {
                strsql1 = "select distinct b.* from module b where 1=0 and b.is_show=1 order by b.module_num ";
            }

            List<vModule> _navlist_p = sqlhelper.GetObjectList(vModule.class,strsql1);
            _navlist.addAll(_navlist_p);

            return true;

        } catch(Exception Ex) {
            return false;
        }
    }
}
