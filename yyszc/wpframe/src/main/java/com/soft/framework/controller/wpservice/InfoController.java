package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.*;
import com.soft.framework.redis.RedisCache;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.base.entity.T_UserData;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.*;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping(value="/WpService/Info")
@Api(tags ="微服务框架接口->通用信息接口")
public class InfoController {
    @Autowired(required = true)
    private TokenService tokenService;

    @Autowired(required = true)
    private RedisCache redisCache;

    @Value("${server.port}")
    private Integer port;

    @RequestMapping(value="/Hello",method ={RequestMethod.GET})
    @ApiOperation(value ="Hello",notes="测试交互接口")
    @ApiImplicitParam(name = "name", value = "String", required = true)
    public String Hello( @RequestParam("name") String name)
    {
        String welstr=String.format("Hello from wpframe: %d", port);
        return welstr;
    }

    @RequestMapping(value="/TestDBEntity",method ={RequestMethod.GET})
    @ApiOperation(value ="TestDBEntity",notes="测试基本对象交互接口")
    @ApiImplicitParam(name = "name", value = "String", required = true)
    public ResponseEntity<Person> TestDBEntity(@RequestParam("name") String name,@RequestHeader(value = "token",required =true) String token)
    {
        SqlHelper sqlhelper=new SqlHelper();
        Person person=sqlhelper.GetObject(Person.class,"select * from person where loginname='"+name+"'");
        return ResponseEntity.ok(person);
    }

    @RequestMapping(value="/GetCurrentPerson",method ={RequestMethod.GET})
    @ApiOperation(value ="GetCurrentPerson",notes="获取当前用户信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<PersonEntity> GetCurrentPerson( @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        LoginUser loginUser = tokenService.getLoginUser(token);

        SqlHelper sqlhelper=new SqlHelper();
        try {
            if (loginUser != null) {
                PersonEntity person = loginUser.getUser();
                if (StringUtil.IsNullOrEmpty(person.getChoseNF())) {
                    person.setChoseNF(String.valueOf(new Date().getYear()));
                }

                //用户邮件
                List<EmailDetail> list = sqlhelper.GetObjectList(EmailDetail.class, "select * from EmailDetail where State in('未查看','新回复') and personZgh='" + person.getLoginName() + "'");
                List<EmailManage> list1 = sqlhelper.GetObjectList(EmailManage.class, "select * from EmailManage where State='新回复' and SpersonZgh='" + person.getLoginName() + "'");

                List<EmailManage> list2 = sqlhelper.GetObjectList(EmailManage.class, "select * from EmailManage where State='新回复' and SpersonZgh='" + person.getLoginName() + "' and Sdate>dateadd(ss,3,getdate())");
                List<v$EmailDetail> list3 = sqlhelper.GetObjectList(v$EmailDetail.class, "select * from v_EmailDetail where State in('未查看','新回复')  and personZgh='" + person.getLoginName() + "' and Sdate>dateadd(ss,3,getdate())");

                if((list2!=null&&list2.size()>0)||(list3!=null&&list3.size()>0))
                {
                    person.setNewEmailNum("1");
                }else
                {
                    person.setNewEmailNum("0");
                }
                int msgnum = 0;
                if(list!=null&&list.size()>0) {
                    msgnum=msgnum+list.size();
                }
                if(list1!=null&&list1.size()>0) {
                    msgnum=msgnum+list1.size();
                }
                person.setEmailNum(String.valueOf(msgnum));

                Date dtLast = getLastInputTimes(person.getId());
                String last = DateUtil.date2Str(dtLast,"yyyy/MM/dd HH:mm:ss");
                if(dtLast.getYear() == 2000) {
                    last = "stop";
                }
                person.setDateTimeString(DateUtil.date2Str(dtLast,"yyyy/MM/dd HH:mm:ss" + "|" + last));

                return ResponseEntity.ok().body(person);
            } else {
                return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).eTag("当前登录用户登录时间已经超时!").body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).eTag("获取用户信息异常!").body(null);
        }
    }

    @RequestMapping(value="/GetCurrentPerson_S",method ={RequestMethod.GET})
    @ApiOperation(value ="GetCurrentPerson_S",notes="获取当前用户信息接口(精简接口)")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<PersonEntity> GetCurrentPerson_S( @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        LoginUser loginUser = tokenService.getLoginUser(token);

        try {
            if (loginUser != null) {
                PersonEntity person = loginUser.getUser();
                return ResponseEntity.ok().body(person);
            } else {
                return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).eTag("当前登录用户登录时间已经超时!").body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).eTag("获取用户信息异常!").body(null);
        }
    }

    public Date getLastInputTimes(int userId)
    {
        Date dtreturn = new Date();
        SqlHelper sqlhelper=new SqlHelper();
        String strsql="";
        try {
            strsql="select * from T_UserData where PID="+userId;
            T_UserData userdata = sqlhelper.GetObject(T_UserData.class,strsql);
            if (userdata != null) {
                strsql ="select MAX(openEnd) as openEnd from (select openEnd From T_Open where AddYear ='" + userdata.getUyearreg() + "' and  opentime<=getdate() and openEnd>=getdate() union select openEnd From T_OpenPerson where userID ='" + userId + "' and opentime<=getdate() and openEnd>=getdate() union select t2.openEnd From T_UserData t1 inner join T_OpenGroup t2 on t1.uyearreg=t2.addyear and t1.uComapanyBH=t2.uComapanyBH and t2.opentime<=getdate() and t2.openEnd>=getdate() where t1.PID ='" + userId + "') t";
                DataTable dt = sqlhelper.GetDataTable(strsql);
                if (dt.getTotalCount() > 0) {
                    String dtstr=dt.getRow(0).getColValue("openEnd").toString();
                    if (!dtstr.equals("")) {
                        dtreturn = DateUtil.parseDate(dtstr,"yyyy-MM-dd HH:mm:ss.SSS");
                    } else {
                        dtreturn = DateUtil.parseDate("2000-01-01","yyyy-MM-dd");

                    }
                } else {
                    dtreturn = DateUtil.parseDate("2000-01-01","yyyy-MM-dd");
                }
            }
            return dtreturn;
        }catch(Exception Ex)
        {
            return null;
        }
    }


    @RequestMapping(value="/IsTokenValid",method ={RequestMethod.GET})
    @ApiOperation(value ="IsTokenValid",notes="获取当前token的有效性接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<Boolean> IsTokenValid( @RequestHeader(value = "token",required =true)String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(false);
        }
        LoginUser loginUser = tokenService.getLoginUser(token);

        if(loginUser!=null)
        {
            //判断是否被其他账户挤出
            if(SessionHelper.SessionVaild(token)) {
                return ResponseEntity.ok(true);
            }else{
                return ResponseEntity.ok(false);
            }
        }

        return ResponseEntity.ok(true);
    }

    @RequestMapping(value="/VerifyToken",method ={RequestMethod.GET})
    @ApiOperation(value ="VerifyToken",notes="刷新中心服务token信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<Boolean> VerifyToken(@RequestHeader(value = "token",required =true)String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            return ResponseEntity.ok(true);
        }else
        {
            return ResponseEntity.ok(false);
        }
    }

    @RequestMapping(value="/GetPreventSqlList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetPreventSqlList",notes="获取平台sql注入配置信息")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<List<NFT_SQL_IDSTR>> GetPreventSqlList(@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            return ResponseEntity.ok(DBHelper.GetPreventSqlList());
        }else
        {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetNftWhiteList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNftWhiteList",notes="获取平台sql注入配置信息")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<List<NFT_WhiteList>> GetNftWhiteList(@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            return ResponseEntity.ok(DBHelper.GetNftWhiteList());
        }else
        {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetNwIpdList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetNwIpdList",notes="获取平台sql注入配置信息")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<List<NFT_NWIPD>> GetNwIpdList(@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            return ResponseEntity.ok(DBHelper.GetNwIpdList());
        }else
        {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetUserNavTable",method ={RequestMethod.GET})
    @ApiOperation(value ="GetUserNavTable",notes="获取指定模块顶部菜单栏")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<DataTable> GetUserNavTable(@RequestParam("mid") String mid, @RequestParam("userid") int userid,@RequestParam("pwf") int pwf,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            DataTable tmpdt = DBHelper.GetUserNavTable(mid, userid, pwf);
            return ResponseEntity.ok(tmpdt);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetUserFunctionList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetUserFunctionList",notes="获取指定模块顶部菜单栏")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<vFunctionItem>> GetUserFunctionList(@RequestParam("mid") String mid, @RequestParam("userid") int userid,@RequestParam("pwf") int pwf,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            List<vFunctionItem> list = DBHelper.GetUserFunctionList(mid, userid, pwf);
            return ResponseEntity.ok(list);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetUserNavMenuGroup",method ={RequestMethod.GET})
    @ApiOperation(value ="GetUserNavMenuGroup",notes="获取指定模块顶部菜单栏中默认选择初始导航项，一般时第一个导航项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<String> GetUserNavMenuGroup(@RequestParam("mid") String mid, @RequestParam("userid") int userid,@RequestParam("pwf") int pwf,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            String tmps = DBHelper.GetUserNavMenuGroup(mid, userid, pwf);
            return ResponseEntity.ok(tmps);
        }else{
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetRolePersonList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetRolePersonList",notes="获取含有指定角色的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<PersonEntity>> GetRolePersonList(@RequestParam("roleid") int roleid,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            List<PersonEntity> retlist = DBHelper.GetRolePersonList(roleid);
            return ResponseEntity.ok(retlist);
        }else
        {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetCompRolePersonList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetCompRolePersonList",notes="获取指定单位含有指定角色的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleid", value = "int", required = true),
            @ApiImplicitParam(name = "topGroupId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<PersonEntity>> GetCompRolePersonList(@RequestParam("roleid") int roleid,@RequestParam("topGroupId") int topGroupId,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            List<PersonEntity> retlist = DBHelper.GetRolePersonList(roleid, topGroupId);
            return ResponseEntity.ok(retlist);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetPermissionPersonList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetPermissionPersonList",notes="获取拥有指定权限的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permission", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<PersonEntity>> GetPermissionPersonList(@RequestParam("permission") String permission,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            List<PersonEntity> retlist = DBHelper.GetPermissionPersonList(permission);
            return ResponseEntity.ok(retlist);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetCompPermissionPersonList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetCompPermissionPersonList",notes="获取指定单位拥有指定权限的用户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "permission", value = "String", required = true),
            @ApiImplicitParam(name = "topGroupId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<PersonEntity>> GetCompPermissionPersonList(@RequestParam("permission") String permission,@RequestParam("topGroupId") int topGroupId,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            List<PersonEntity> retlist = DBHelper.GetPermissionPersonList(permission, topGroupId);
            return ResponseEntity.ok(retlist);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetAllTopGroup",method ={RequestMethod.GET})
    @ApiOperation(value ="GetAllTopGroup",notes="获取指定模块操作单位（TopGroupId）列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mid", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<vComp>> GetAllTopGroup(@RequestParam("mid") int mid,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            List<vComp> retlist = DBHelper.GetAllTopGroup(mid);
            return ResponseEntity.ok(retlist);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetPersonByLoginName",method ={RequestMethod.GET})
    @ApiOperation(value ="GetPersonByLoginName",notes="据LogiinName获取用户基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<PersonEntity> GetPersonByLoginName(@RequestParam("loginName") String loginName,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if (tokenService.verifyToken(token)) {
            PersonEntity person = DBHelper.GetPersonByLoginName(loginName);
            return ResponseEntity.ok(person);
        } else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetPersonByUserId",method ={RequestMethod.GET})
    @ApiOperation(value ="GetPersonByUserId",notes="据userId获取用户基本信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<PersonEntity> GetPersonByUserId(@RequestParam("userid") int userid,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            PersonEntity person = DBHelper.GetPersonByUserId(userid);
            return ResponseEntity.ok(person);
        }else {
            return ResponseEntity.ok(null);
        }
    }


    @RequestMapping(value="/GetVPersonByLoginName_S",method ={RequestMethod.GET})
    @ApiOperation(value ="GetVPersonByLoginName_S",notes="据LogiinName获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<vPerson> GetVPersonByLoginName_S(@RequestParam("loginName") String loginName,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            vPerson person = DBHelper.GetVPersonByLoginName_S(loginName);
            return ResponseEntity.ok(person);
        }else
        {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetPersonByLoginName_S",method ={RequestMethod.GET})
    @ApiOperation(value ="GetPersonByLoginName_S",notes="据LogiinName获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "loginName", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Person> GetPersonByLoginName_S(@RequestParam("loginName") String loginName,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if (tokenService.verifyToken(token)) {
            Person person = DBHelper.GetPersonByLoginName_S(loginName);
            return ResponseEntity.ok(person);
        } else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetVPersonByUserId_S",method ={RequestMethod.GET})
    @ApiOperation(value ="GetVPersonByUserId_S",notes="据userId获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<vPerson> GetVPersonByUserId_S(@RequestParam("userid") int userid,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            vPerson person = DBHelper.GetVPersonByUserId_S(userid);
            return ResponseEntity.ok(person);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetPersonByUserId_S",method ={RequestMethod.GET})
    @ApiOperation(value ="GetPersonByUserId_S",notes="据userId获取用户基本信息(精简信息)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Person> GetPersonByUserId_S(@RequestParam("userid") int userid,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            Person person = DBHelper.GetPersonByUserId_S(userid);
            return ResponseEntity.ok(person);
        }else {
            return ResponseEntity.ok(null);
        }
    }

    @RequestMapping(value="/GetChildFuntionItemByParent",method ={RequestMethod.GET})
    @ApiOperation(value ="GetChildFuntionItemByParent",notes="据LoginName获取用户邮件信息信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userid", value = "int", required = true),
            @ApiImplicitParam(name = "pwf", value = "int", required = true),
            @ApiImplicitParam(name = "gid", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<DataTable> GetChildFuntionItemByParent(@RequestParam("userid") int userid,@RequestParam("pwf") int pwf,@RequestParam("gid") String gid,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            return ResponseEntity.ok(null);
        }
        if(tokenService.verifyToken(token)) {
            DataTable tmpdt = DBHelper.GetChildFuntionItemByParent(userid, pwf, gid);
            return ResponseEntity.ok(tmpdt);
        }else
        {
            return ResponseEntity.ok(null);
        }
    }
}
