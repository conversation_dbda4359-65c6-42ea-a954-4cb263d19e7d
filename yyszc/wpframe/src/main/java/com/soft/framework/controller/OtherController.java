package com.soft.framework.controller;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.SessionHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.base.entity.NFV_FBKB_DS;
import com.soft.gcc.base.entity.vFunctionItemM;
import com.yyszc.wpbase.entity.EmailDetail;
import com.yyszc.wpbase.entity.EmailManage;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Configuration
@RequestMapping(value="/Service/MetaMan" )
@Api(tags ="微服务框架接口->其他杂项服务接口")
public class OtherController {

    @RequestMapping(value="/GetFBKBData",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetFBKBData",notes="获取分包看板数据接口")
    public AjaxResult GetFBKBData(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        String strsql="";

        SqlHelper sqlhelper=new SqlHelper();
        try {
            strsql="select * from NFV_FBKB_DS";
            List<NFV_FBKB_DS> _list=sqlhelper.GetObjectList(NFV_FBKB_DS.class,strsql);
            int maxAge = _list.stream().mapToInt(NFV_FBKB_DS::getFbjs_AvgDay).max().getAsInt();

            for(NFV_FBKB_DS ds:_list)
            {
                if(ds.getFbjs_AvgDay()==0)
                {
                    ds.setFbjs_AvgDay((int)(maxAge*1.2));
                }
            }
            ajax=AjaxResult.extgrid(NFV_FBKB_DS.class,_list.size(),_list);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取分包看板数据产生异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetFunctionByOption",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetFunctionByOption",notes="获取功能导航搜索框搜索数据接口")
    public AjaxResult GetFunctionByOption(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        String strsql="";

        String searchp=request.getParameter("searchp");
        if (StringUtil.IsNullOrEmpty(searchp))
        {
            ajax=AjaxResult.error("传输参数有误");
            return ajax;
        }

        SqlHelper sqlhelper=new SqlHelper();
        try {
            strsql="select * from vFunctionItemM where 1=1 ";
            if(!StringUtil.IsNullOrEmpty(searchp))
            {
                strsql+=" and (module_name like '%"+searchp+"%' or Title like '%"+searchp+"%')";
            }
            strsql+=" order by Module_id,Title";

            List<vFunctionItemM> _list=sqlhelper.GetObjectList(vFunctionItemM.class,strsql);
            for(vFunctionItemM vo:_list)
            {
                vo.setUrl("../../Page/"+vo.getUrl());
                vo.setModule_mix(ToolHelper.MixedMenuId(vo.getModule_ID()));
            }

            ajax=AjaxResult.extgrid(vFunctionItemM.class,_list.size(),_list);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取功能导航搜索框搜索数据据异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetUserEmailList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetUserEmailList",notes="获取用户邮件列表接口")
    public AjaxResult GetUserEmailList(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        PersonEntity person= SessionHelper.getSessionPerson();
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();
        try {
            strsql="select * from EmailManage where State='新回复' and SpersonZgh='"+person.getLoginName()+"' or Id in(";
            strsql+="select EmailID from EmailDetail where State in('未查看','新回复') and personZgh='"+person.getLoginName()+"')";
            strsql+=" order by Sdate desc";

            List<EmailManage> _list =sqlhelper.GetObjectList(EmailManage.class,strsql);
            ajax=AjaxResult.extgrid(EmailManage.class,_list.size(),_list);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取用户邮件列表接口异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetUserNewEmailList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetUserNewEmailList",notes="获取用户新邮件列表接口")
    public AjaxResult GetUserNewEmailList(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        PersonEntity person= SessionHelper.getSessionPerson();
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();
        try {
            strsql="select * from EmailManage where State='新回复' and SpersonZgh='"+person.getLoginName()+"' and Sdate>dateadd(ss,3,getdate()) or Id in(";
            strsql+="select EmailID from v$EmailDetail where State in('未查看','新回复') and personZgh='"+person.getLoginName()+"' and Sdate>dateadd(ss,3,getdate()))";
            strsql+=" order by Sdate desc";

            List<EmailManage> _list =sqlhelper.GetObjectList(EmailManage.class,strsql);
            ajax=AjaxResult.extgrid(EmailManage.class,_list.size(),_list);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取用户新邮件列表接口异常！！");
            return ajax;
        }
    }

    @RequestMapping(value="/GetEmailDetailList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetEmailDetailList",notes="获取邮件明细列表接口")
    public AjaxResult GetEmailDetailList(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        String EmailId=request.getParameter("EmailId");

        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();
        try {
            strsql="select * from EmailDetail where EmailID="+EmailId;

            List<EmailDetail> _list =sqlhelper.GetObjectList(EmailDetail.class,strsql);
            ajax=AjaxResult.extgrid(EmailDetail.class,_list.size(),_list);
            return ajax;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取邮件明细列表接口异常！！");
            return ajax;
        }
    }
}
