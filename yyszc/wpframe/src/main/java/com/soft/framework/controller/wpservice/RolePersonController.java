package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value="/WpService/RolePerson")
@Api(tags ="微服务框架接口->RolePerson表操作接口")
public class RolePersonController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/DeleteRolePerson",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteRolePerson",notes="删除角色用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PersonId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteRolePerson(@RequestParam("RoleId") int RoleId,@RequestParam("PersonId") int PersonId,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from RolePerson where RoleId='" + RoleId + "' and PersonId='" + PersonId + "'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/AddRolePerson",method ={RequestMethod.GET})
    @ApiOperation(value ="AddRolePerson",notes="新增角色用户信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PersonId", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> AddRolePerson(@RequestParam("RoleId") int RoleId,@RequestParam("PersonId") int PersonId, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "insert into RolePerson(RoleId,PersonId) values(" + RoleId + ",'" + PersonId + "')";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

}
