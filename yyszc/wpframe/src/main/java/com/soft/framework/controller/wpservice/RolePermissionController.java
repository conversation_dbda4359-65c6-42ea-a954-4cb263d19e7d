package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value="/WpService/RolePermission")
@Api(tags ="微服务框架接口->RolePermission表操作接口")
public class RolePermissionController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/DeleteRolePermission",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteRolePermission",notes="删除角色权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PermissionNo", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteRolePermission(@RequestParam("RoleId") int RoleId,@RequestParam("PermissionNo") String PermissionNo,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from RolePermission where RoleId='" + RoleId + "' and PermissionNo='" + PermissionNo + "'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/AddRolePermission",method ={RequestMethod.GET})
    @ApiOperation(value ="AddRolePermission",notes="新增角色权限信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "RoleId", value = "int", required = true),
            @ApiImplicitParam(name = "PermissionNo", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> AddRolePermission(@RequestParam("RoleId") int RoleId,@RequestParam("PermissionNo") String PermissionNo, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "insert into RolePermission(RoleId,PermissionNo) values(" + RoleId + ",'" + PermissionNo + "')";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

}
