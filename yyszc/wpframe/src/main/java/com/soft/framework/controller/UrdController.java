package com.soft.framework.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.SqlHelper;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value="/Service/URDMan" )
@Api(tags ="微服务框架接口->用户使用热度基本接口")
public class UrdController {

    @Data
    public class URD_STATE
    {
        @JSONField(name="compName")
        private String compName;

        @JSONField(name="compUVal")
        private String compUVal;

        @JSONField(name="compRVal")
        private String compRVal;
    }

    @RequestMapping(value="/GetURDList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetURDList",notes="获取使用热度信息接口")
    public String GetURDList(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        String strsql="";

        SqlHelper sqlhelper=new SqlHelper();
        try {
            strsql="select top 21 URD_COMPN as compName,URD_RVAL as compUVal,URD_UVAL as compRVal from NFT_URD_STAT where URD_RVAL>0 order by cast(URD_UVAL as float)/URD_RVAL desc";
            DataTable tmpdt = sqlhelper.GetDataTable(strsql);

            String jsonstr=AjaxResult.extgrid(tmpdt,tmpdt.size());
            return jsonstr;
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("获取人员使用热度信息产生异常！！");
            return ajax.toString();
        }
    }
}
