package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.Module;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value="/WpService/Module")
@Api(tags ="微服务框架接口->Module表操作接口")
public class ModuleController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/GetModuleList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetModuleList",notes="获取模块信息接口")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<List<Module>> GetModuleList(@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                String strsql="select * from Module order by id";
                List<Module> plist=sqlhelper.GetObjectList(Module.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetModuleListInMode",method ={RequestMethod.GET})
    @ApiOperation(value ="GetModuleListInMode",notes="获取指定模组下辖模块信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mlist", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<Module>> GetModuleListInMode(@RequestParam("mlist") String mlist, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                String strsql="select * from Module where id in("+mlist+") order by id";
                List<Module> plist=sqlhelper.GetObjectList(Module.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }
}
