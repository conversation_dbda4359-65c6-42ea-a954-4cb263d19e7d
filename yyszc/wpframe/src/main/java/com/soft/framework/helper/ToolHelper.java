package com.soft.framework.helper;

import com.soft.framework.common.utils.JsonUtils;
import com.soft.framework.common.utils.UrlUtil;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import lombok.Data;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Predicate;

@SuppressWarnings("unchecked")
public class ToolHelper {
    @Data
    public static class Result_FileBytePack implements Serializable {
        public boolean success;
        public String text;
        public String fname;
        public String ftype;
        public byte[] data1;
        public byte[] data2;
    }

    @Data
    public static class ExtJS_Sort {
        public String property;
        public String direction;
    }

    @Data
    public static class ProgClass {
        public boolean success;
        public String percent;
        public String msginfo;
    }

    public static String ToJsonString(String strmsg)
    {
        String strReturn;
        strReturn = strmsg.replace("\n", "");
        strReturn = strReturn.replace("\r", "");
        strReturn = strReturn.replace("\b", "");
        strReturn = strReturn.replace("\f", "");
        strReturn = strReturn.replace("\t", "");
        strReturn = strReturn.replace("\"", "");
        strReturn = strReturn.replace("\'", "");
        strReturn = strReturn.replace(":", "");
        strReturn = strReturn.replace("（", "(");
        strReturn = strReturn.replace("）", ")");
        return strReturn;
    }

    public static int getRealDays(Date StartDate, Date endDate)
    {
        try
        {
            int Real_Days = 0;//实际天数
            SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");

            if(StartDate.compareTo(sdf.parse("2018-01-01"))<0)
            {
            } else
            {
                Real_Days= DateUtil.dateDiff('d', StartDate,endDate);
            }
            return Real_Days;
        }
        catch (Exception Ex)
        {
            return 0;
        }
    }

    public static String SmartLikeStr(String pname)
    {
        return " and (a.sendpersonZgh='" + pname + "' or a.sendpersonZgh like '" + pname + "~%' or a.sendpersonZgh like '%~" + pname + "~%' or a.sendpersonZgh like '%~" + pname + "' or a.sendpersonZgh like '%~" + pname + "~')";
    }


    public static boolean ReadFileToStr(String fstr, List<String> sb) {
        try {
            File file = new File(fstr);
            if (!file.exists()) {
                return false;
            }

            InputStreamReader isr=new InputStreamReader(new FileInputStream(file),"UTF-8");
            BufferedReader bReader = new BufferedReader(isr);
            String s = "";
            while ((s = bReader.readLine()) != null) {
                sb.add(s);
                //System.out.println(s);
            }
            bReader.close();

            return true;
        } catch (Exception err) {
            return false;
        }
    }

    private static boolean ReadFiletoStringList(String fstr, ArrayList slist) {
        try {
            File file = new File(fstr);
            if (!file.exists()) {
                return false;
            }
            slist.clear();

            FileReader reader = new FileReader(file);
            BufferedReader bReader = new BufferedReader(reader);
            StringBuilder sb = new StringBuilder();
            String s = "";
            while ((s = bReader.readLine()) != null) {
                slist.add(s);
            }
            bReader.close();

            return true;
        } catch (Exception err) {
            return false;
        }
    }

    public static String PathStrExtractFileExt(String path) {
        return path.substring(path.lastIndexOf(".") + 1);
    }

    public static boolean DeserializeExtJsSortInfo(String jsonstr, StringBuilder sortStr)
    {
        try
        {
            List<ExtJS_Sort> _list = JsonUtils.toArrayList(jsonstr,ExtJS_Sort.class);

            String tmpstr = "";
            for (int i = 0; i < _list.size(); i++)
            {
                String sortd = _list.get(i).direction;
                String sortf = _list.get(i).property;
                if (tmpstr == "") {
                    tmpstr = sortf + " " + sortd;
                } else {
                    tmpstr += ", " + sortf + " " + sortd;
                }
            }
            if (tmpstr != "")
            {
                sortStr.append(" order by " + tmpstr);
            }
        }
        catch (Exception err)
        {
            return false;
        }
        return true;
    }

    public static void DeleteTempFile(String fname)
    {
        try
        {
            String fullpath = ConfigHelper.getTempPath() + ConfigHelper.getfSepChar()+fname;
            if (FileUtil.FileExists(fullpath)) {
                FileUtil.Delete(fullpath);
            }
        }
        catch (Exception Ex)
        {

        }
    }

    public static void ResponseTempFile(String fname, HttpServletResponse Response)
    {
        String downloadName = "";
        String FullFileName = ConfigHelper.getTempPath() + ConfigHelper.getfSepChar()+ fname;
        File DownloadFile = new File(FullFileName);

        downloadName = fname;
        try
        {
            if (FileUtil.FileExists(FullFileName)) {
                Response.reset();
                Response.setCharacterEncoding("utf-8");
                Response.setContentType("application/octet-stream");
                Response.setHeader("content-type", "application/octet-stream");
                Response.addHeader("Content-Disposition", "attachment;filename=" + UrlUtil.encodeURL(downloadName));
                Response.addHeader("Content-Length", String.valueOf(DownloadFile.length()));

                InputStream in = new FileInputStream(FullFileName);
                int count = 0;
                byte[] by = new byte[1024];
                OutputStream out = Response.getOutputStream();
                while ((count = in.read(by)) != -1) {
                    out.write(by, 0, count);
                }
                in.close();
                out.flush();
                out.close();

                DeleteTempFile(fname);

                Response.flushBuffer();
            }
        }
        catch (Exception Ex)
        {
        }
    }

    public static void ResponseFile(String mark,String fname, HttpServletResponse Response)
    {
        String downloadName = "";
        String FullFileName = ConfigHelper.getUploadPath() +mark+ ConfigHelper.getfSepChar()+ fname;
        File DownloadFile = new File(FullFileName);

        downloadName = fname;
        try
        {
            if (FileUtil.FileExists(FullFileName)) {
                Response.reset();
                Response.setCharacterEncoding("utf-8");
                Response.setContentType("application/octet-stream");
                Response.setHeader("content-type", "application/octet-stream");
                Response.addHeader("Content-Disposition", "attachment;filename=" + UrlUtil.encodeURL(downloadName));
                Response.addHeader("Content-Length", String.valueOf(DownloadFile.length()));

                InputStream in = new FileInputStream(FullFileName);
                int count = 0;
                byte[] by = new byte[1024];
                OutputStream out = Response.getOutputStream();
                while ((count = in.read(by)) != -1) {
                    out.write(by, 0, count);
                }
                in.close();
                out.flush();
                out.close();

                DeleteTempFile(fname);

                Response.flushBuffer();
            }
        }
        catch (Exception Ex)
        {
        }
    }

    public static byte[] File2Bytes(String filename)
    {
        try {
            if (!FileUtil.FileExists(filename)) {
                return new byte[0];
            }

            File file = new File(filename);
            long fileSize = file.length();
            if (fileSize > Integer.MAX_VALUE) {
                System.out.println("file too big...");
                return null;
            }

            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();

            int len;
            byte[] buffer = new byte[1024];
            while ((len = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }

            byte[] data =baos.toByteArray();

            fis.close();
            baos.close();

            return data;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    public static <T> T getElement(List<T> objs, Predicate<T> predicate) {
        return objs.stream()
            .filter(predicate)
            .findAny()
            .orElse(null);
    }

    public static String GetLongString(Double dvalue)
    {
        try
        {
            String lval=String.valueOf(new Double(dvalue).longValue());
            return lval;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static String GetFloatString(Double dvalue)
    {
        try
        {
            String lval=String.valueOf(new Double(dvalue).floatValue());
            return lval;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static String getCurrentTokenKey(HttpServletRequest request)
    {
        try
        {
            TokenService tokenService=SpringUtil.getBean("tokenService");
            String uuid=tokenService.getRequestTokenKey(request);
            return uuid;
        }catch (Exception Ex)
        {
            return "";
        }
    }

    public static String MixedMenuId(int mid)
    {
        try
        {
            String rets = DesHelper.encrypt("fuckhack" + String.valueOf(mid) + "times");
            rets=rets.replaceAll("-","");
            return rets;
        }
        catch (Exception Ex)
        {
            return "";
        }
    }

    public static String UnMixedMenuId(String retstr)
    {
        try
        {
            String rets = DesHelper.decrypt(retstr);
            rets = rets.replaceAll("fuckhack", "").replaceAll("times","");
            return rets;
        }
        catch (Exception Ex)
        {
            return "";
        }
    }
}
