package com.soft.framework.helper;

import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.Person;
import com.yyszc.wpbase.ventity.PersonEntity;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

public final class SessionHelper {
    public static HttpServletRequest getRequest() {
        return ServletUtils.getRequest();
    }

    public static String getSessionId() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            if(loginUser!=null)
            {
                return loginUser.getToken();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static int getSessionUserID() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getId();
            }else
            {
                return -1;
            }
        }else
        {
            return -1;
        }
    }
    public static String getSessionUserName() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRealName();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionLoginName() { ;
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getLoginName();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static int getSessionDeptId() { ;
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getGroupId();
            }else
            {
                return -1;
            }
        }else
        {
            return -1;
        }
    }

    public static String getSessionDeptName() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getGroupName();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionRoleIdList() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRoleIdsString();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionRoleNameList() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRoleNamesString();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static String getSessionPermissionList() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity.getRolePermissionString();
            }else
            {
                return "";
            }
        }else
        {
            return "";
        }
    }

    public static PersonEntity getSessionPerson() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return personEntity;
            }else
            {
                return null;
            }
        }else
        {
            return null;
        }
    }

    public static int getSessionUserPwf() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                return loginUser.getPwf();
            }else
            {
                return 2;
            }
        }else
        {
            return 2;
        }
    }

    public static boolean Session_IsAdmin() {
        HttpServletRequest request=getRequest();
        if(request!=null)
        {
            TokenService tokenService=(TokenService) SpringUtil.getBean("tokenService");
            LoginUser loginUser = tokenService.getLoginUser(request);
            PersonEntity personEntity = loginUser.getUser();
            if(personEntity!=null)
            {
                String LoginName=personEntity.getLoginName();
                String RoleNameList=personEntity.getRoleNamesString();
                if(LoginName.equals("admin")||RoleNameList.indexOf("JAVA框架-平台管理角色")!=-1){
                    return true;
                }
                return false;
            }else
            {
                return false;
            }
        }else
        {
            return false;
        }
    }

    public static void SessionAddPC(String token,String loginname)
    {
        String strsql = "";
        try
        {
            SqlHelper sqlhelper = new SqlHelper();
            String datestr = MetaHelper.GetDateString(new Date(), false);
            Person person = DBHelper.GetPersonByLoginName_S(loginname);
            if (person.getType() == 2 && person.getLoginName2().equals("prod"))
            {

            }
            else
            {
                strsql = "update NFT_SESSION set LOGIN_NAME='XXXXX' where SESSION_DATE=" + datestr + " and LOGIN_NAME='" + loginname + "' and SRC_TYPE='PC' and SESSION_ID!='" + token + "'";
                sqlhelper.ExecuteNoQuery(strsql);
            }

            strsql = "delete from NFT_SESSION where SESSION_DATE=" + datestr + " and SESSION_ID='" + token + "'";
            sqlhelper.ExecuteNoQuery(strsql);
            strsql = "insert into NFT_SESSION(SESSION_DATE,SESSION_ID,LOGIN_NAME,SRC_TYPE) values(" + datestr + ",'" + token + "','" + loginname + "','PC')";
            sqlhelper.ExecuteNoQuery(strsql);

            strsql = "select 1 from NFT_URD_ULR where ULR_DATE=" + datestr + " and ULR_UNAME='" + loginname + "'";
            String strc = sqlhelper.ExecuteScalar(strsql);
            if(strc.equals("")) {
                strsql = "insert into NFT_URD_ULR(ULR_DATE,ULR_UNAME) values(" + datestr + ",'" + loginname + "')";
                sqlhelper.ExecuteNoQuery(strsql);
            }
        }
        catch (Exception Ex)
        {
            String msg=Ex.getMessage();
        }
    }

    public static void SessionAddAPP(String token,String loginname)
    {
        String strsql = "";
        try
        {
            SqlHelper sqlhelper = new SqlHelper();
            String datestr = MetaHelper.GetDateString(new Date(), false);
            Person person = DBHelper.GetPersonByLoginName_S(loginname);
            if (person.getType() == 2 && person.getLoginName2().equals("prod"))
            {

            }
            else
            {
                strsql = "update NFT_SESSION set LOGIN_NAME='XXXXX' where SESSION_DATE=" + datestr + " and LOGIN_NAME='" + loginname + "' and SRC_TYPE='APP' and SESSION_ID!='" + token + "'";
                sqlhelper.ExecuteNoQuery(strsql);
            }

            strsql = "delete from NFT_SESSION where SESSION_DATE=" + datestr + " and SESSION_ID='" + token + "'";
            sqlhelper.ExecuteNoQuery(strsql);
            strsql = "insert into NFT_SESSION(SESSION_DATE,SESSION_ID,LOGIN_NAME,SRC_TYPE) values(" + datestr + ",'" + token + "','" + loginname + "','APP')";
            sqlhelper.ExecuteNoQuery(strsql);

            strsql = "select 1 from NFT_URD_ULR where ULR_DATE=" + datestr + " and ULR_UNAME='" + loginname + "'";
            String strc = sqlhelper.ExecuteScalar(strsql);
            if (strc.equals(""))
            {
                strsql = "insert into NFT_URD_ULR(ULR_DATE,ULR_UNAME) values(" + datestr + ",'" + loginname + "')";
                sqlhelper.ExecuteNoQuery(strsql);
            }
        }
        catch (Exception Ex)
        {
            String msg=Ex.getMessage();
        }
    }

    public static Boolean SessionPD(String token)
    {
        Boolean flag = true;
        try
        {
            String datestr = MetaHelper.GetDateString(new Date(), false);
            String strsql = "";
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select LOGIN_NAME from NFT_SESSION where SESSION_DATE=" + datestr + " and SESSION_ID='" + token + "'";
            String strc = sqlhelper.ExecuteScalar(strsql);
            if (strc.equals("XXXXX"))
            {
                flag = true;
            }
            else
            {
                flag = false;
            }

            return flag;
        }
        catch (Exception Ex)
        {
            return true;
        }
    }

    //和net返回值相反，
    public static Boolean SessionVaild(String token)
    {
        return !SessionPD(token);
    }
}
