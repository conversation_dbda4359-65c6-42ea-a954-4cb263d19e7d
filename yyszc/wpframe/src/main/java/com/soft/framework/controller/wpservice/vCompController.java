package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.vComp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value="/WpService/vComp")
@Api(tags ="微服务框架接口->vComp表操作接口")
public class vCompController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/GetCompList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetCompList",notes="获取单位精简列表信息接口(多用于列表框显示数据)")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<List<vComp>> GetCompList(@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                String strsql = "select * from vComp order by COMP_ID asc";
                List<vComp> plist=sqlhelper.GetObjectList(vComp.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }
}
