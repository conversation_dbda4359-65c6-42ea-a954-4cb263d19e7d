package com.soft.framework.controller;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.NFV_WorkFlow;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@Configuration
@RequestMapping(value="/Service/DBLMan" )
@Api(tags ="微服务框架接口->框架页获取待办统计接口")
public class DblController {
    @Autowired(required =false)
    private TokenService tokenService;

    @Data
    public class DBState
    {
        public String lcid;
        public String lcname;
        public int lcnum;
    }

    @RequestMapping(value="/GetDBData",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetDBData",notes="用户待办数据")
    public AjaxResult GetDBStatData(HttpServletRequest request)
    {
        AjaxResult ajax=null;
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();

        String searchtj=SessionHelper.getRequest().getParameter("searchtj");
        if (searchtj == "undefined") {
            searchtj = "";
        }
        if(SecToolHelper.CheckWordHasSQLKQ(searchtj)) {
            ajax=AjaxResult.error("检测sql注入威胁！");
            return ajax;
        }

        try
        {
            PersonEntity person = SessionHelper.getSessionPerson();
            if (person == null) {
                ajax=AjaxResult.error("未授权的用户！");
                return ajax;
            }
            if (person.getRoleNamesString().contains("集团审计")){
                ajax=AjaxResult.error("未授权的用户！");
                return ajax;
            }

            String rpstr = person.getRoleNamesString();
            String gpstr = person.getGroupId().toString() + "~";

            String lpermstr = "0";
            String[] col_r = rpstr.split(",");
            for (int i = 0; i < col_r.length; i++)
            {
                if (MetaHelper.IsValidateInt(col_r[i]))
                {
                    if (lpermstr == "") {
                        lpermstr = col_r[i];
                    } else {
                        lpermstr = lpermstr + "," + col_r[i];
                    }
                }
            }

            final String permstr = lpermstr;

            String striso = "SET TRANSACTION ISOLATION LEVEL Read UnCommitted;";
            String strsql1 = "";
            String strsql2 = "";

            strsql1 = "select distinct a.lc_defineID,a.ywID,a.lc_jdid,a.lc_jdmc,isnull(c.startdate,GETDATE()) as startdate,isnull(c.wfexists,0) as wfexists,";
            strsql1 += "b.LcName,1111 as LcCount,b.ywb as LcProjParam,a.LcProjName,b.ywurl as LcProjUrl,a.sendPersonZgh ";
            strsql1 += "from FUNC_WORKFLOW(" + person.getTopGroupId() + "," + person.getType() + ",'" + person.getLoginName() + "','" + person.getRealName() + "') a outer apply dbo.FUNC_LCS_PECWR(a.lc_defineID,a.lc_jdid,a.ywID,'" + person.getLoginName() + "') c,Lcdefine b ";
            strsql1 += "where a.lc_defineID=b.lcid and isnull(a.lc_jdid,0)>0 and dbo.FUNC_LC_ISRUN(lc_defineID,lc_jdid,ywid)=1 ";
            strsql1 += " and dbo.FUNC_LC_WFNEED(a.lc_defineID,c.wfexists)=1 ";
            strsql2 = strsql1;
            strsql1 += ToolHelper.SmartLikeStr(person.getLoginName());
            strsql2 += " and a.sendGroupIDs like '%" + gpstr + "%' and a.sendPersonZgh=''";
            strsql1 += " order by lc_defineID,lc_jdid";
            strsql2 += " order by lc_defineID,lc_jdid";

            List<NFV_WorkFlow> list = new ArrayList<NFV_WorkFlow>();
            List<NFV_WorkFlow> _rlist = new ArrayList<NFV_WorkFlow>();

            List<NFV_WorkFlow> _list1 = sqlhelper.GetObjectList(NFV_WorkFlow.class,striso+strsql1);
            List<NFV_WorkFlow> _list2 = sqlhelper.GetObjectList(NFV_WorkFlow.class,striso+strsql2);

            if (_list1!=null&&_list1.size() > 0) {
                list.addAll(_list1);
            }
            if (_list2!=null&&_list2.size() > 0) {
                _list2 = _list2.stream().filter(u ->u.getLc_jdid()!=null&&permstr.contains(u.getLc_jdid().toString())).collect(Collectors.toList());
                list.addAll(_list2);
            }

            for (int i = 0; i < list.size(); i++)
            {
                NFV_WorkFlow wf = list.get(i);

                String tmpstr = wf.getLcProjParam();
                if (tmpstr!=null&& !tmpstr.equals("")&&(wf.getLcProjName()== null||wf.getLcProjName().equals("")))
                {
                    String[] arr = tmpstr.split("\\|");
                    if (arr.length >= 3)
                    {
                        String arr2str = arr[2].replace("^", "'");
                        arr2str = arr2str.replace("&1",wf.getLc_defineID().toString()).replace("&2", wf.getLc_jdid().toString()).replace("&3", wf.getYwID().toString());
                        strsql = "select " + arr2str + " from " + arr[0] + " where " + arr[1] + "=" + wf.getYwID();
                        String tmpc = sqlhelper.ExecuteScalar(strsql);
                        wf.setLcProjName(tmpc);
                    }
                }

                if (searchtj!=null&&!searchtj.equals(""))
                {
                    if (wf.getLcProjName().contains(searchtj))
                    {
                        _rlist.add(wf);
                    }
                }
                else
                {
                    _rlist.add(wf);
                }
            }

            List<DBState> dbslist=new ArrayList<DBState>();
            Map<String,List<NFV_WorkFlow>> flowMap = _rlist.stream().collect(Collectors.groupingBy(item -> buildKey(item)));
            for(String key : flowMap.keySet()){
                List<NFV_WorkFlow> llist= flowMap.get(key);
                String[] karr=key.split("#");
                if (llist.size() > 0)
                {
                    DBState dbs=new DBState();
                    dbs.setLcid(karr[0]);
                    dbs.setLcname(karr[1]);
                    dbs.setLcnum(llist.size());
                    dbslist.add(dbs);
                }
            }

            ajax=AjaxResult.success("获取待办统计数据成功！");
            ajax.put("dbslist",dbslist);
            return ajax;
        }
        catch (Exception Ex)
        {
            LogHelper.WriteSysLog(Ex.getMessage());
            ajax=AjaxResult.error("获取信息产生异常！！");
            return ajax;
        }
    }

    public static String buildKey(NFV_WorkFlow flow) {
        return flow.getLc_defineID() + "#" + flow.getLcName();
    }

}
