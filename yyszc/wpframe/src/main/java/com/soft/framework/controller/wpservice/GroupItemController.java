package com.soft.framework.controller.wpservice;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.AesHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.GroupItem;
import com.yyszc.wpbase.entity.vGroupItem;
import com.yyszc.wpbase.ventity.GroupTreeNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value="/WpService/GroupItem")
@Api(tags ="微服务框架接口->GroupItem表操作接口")
public class GroupItemController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/AddGroupItem",method ={RequestMethod.POST})
    @ApiOperation(value ="AddGroupItem",notes="新增单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "GroupItem", value = "GroupItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Integer> AddGroupItem(@RequestBody GroupItem entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                //提交数据库
                String strsql = DBHelper.GetInsertSQL(entity,"GroupItem", Arrays.asList("id"));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
                }
                String tmpstr=sqlhelper.ExecuteInsertWithObtainId(strsql);

                return ResponseEntity.ok().body(Integer.parseInt(tmpstr));
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
        }
    }

    @RequestMapping(value="/UpdateGroupItem",method ={RequestMethod.POST})
    @ApiOperation(value ="UpdateGroupItem",notes="修改单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "GroupItem", value = "GroupItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> UpdateGroupItem(@RequestBody GroupItem entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = DBHelper.GetUpdateSQL(entity,"GroupItem", Arrays.asList("id"), Arrays.asList(entity.getId().toString()));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
                }
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/DeleteGroupItemById",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteGroupItemById",notes="删除单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteGroupItemById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from GroupItem where id='"+id+"'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/GetGroupItemById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetGroupItemById",notes="获取单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<GroupItem> GetGroupItemById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from GroupItem where id='"+id+"'";
                GroupItem entity=sqlhelper.GetObject(GroupItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetGroupItemBySql",method ={RequestMethod.GET})
    @ApiOperation(value ="GetGroupItemBySql",notes="获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<GroupItem> GetGroupItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                GroupItem entity=sqlhelper.GetObject(GroupItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetVGroupItemById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetVGroupItemById",notes="获取单位信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<vGroupItem> GetVGroupItemById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from vGroupItem where id='"+id+"'";
                vGroupItem entity=sqlhelper.GetObject(vGroupItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetVGroupItemBySql",method ={RequestMethod.GET})
    @ApiOperation(value ="GetVGroupItemBySql",notes="获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<vGroupItem> GetVGroupItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                vGroupItem entity=sqlhelper.GetObject(vGroupItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetGroupItemList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetGroupItemList",notes="获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<GroupItem>> GetGroupItemList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<GroupItem> plist=sqlhelper.GetObjectList(GroupItem.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetVGroupItemList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetVGroupItemList",notes="获取当前单位列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<vGroupItem>> GetVGroupItemList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<vGroupItem> plist=sqlhelper.GetObjectList(vGroupItem.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetGroupList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetGroupList",notes="获取单位精简列表信息接口(多用于列表框显示数据)")
    @ApiImplicitParam(name = "token", value = "String", required = true)
    public ResponseEntity<List<vGroupItem>> GetGroupList(@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                String strsql = "select id,GroupDesc from vGroupItem where GroupDesc is not null order by topgroupid,plevel,XH asc";
                List<vGroupItem> plist=sqlhelper.GetObjectList(vGroupItem.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    private Boolean GetGroupChildTree(GroupTreeNode gtn)
    {
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();
        try
        {
            strsql = "select count(*) from vGroupItem where ParentId='" + gtn.getId()+ "'";
            String tmpc=sqlhelper.ExecuteScalar(strsql);
            if(!tmpc.equals("0")&&!tmpc.equals("")) {
                strsql = "select * from vGroupItem where ParentId='" + gtn.getId() + "'";
                List<GroupTreeNode> list = sqlhelper.GetObjectList(GroupTreeNode.class, strsql);
                for (GroupTreeNode gp:list)
                {
                    GetGroupChildTree(gp);
                }
                gtn.setChildList(list);
            }
            return true;
        }catch(Exception Ex)
        {
            return false;
        }
    }

    @RequestMapping(value="/GetGroupTree", produces={"text/plain;charset=UTF-8"})
    @ResponseBody
    @ApiOperation(value ="GetGroupTree",notes="获取树形单位机构列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "group", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    public String GetGroupTree(@RequestParam(value = "group",required =true) int group,@RequestHeader(value = "token",required =true) String token)
    {
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();
        try
        {
            if(group==0)
            {
                strsql="select * from vGroupItem where id='"+group+"'";
            }else
            {
                strsql="select * from vGroupItem where ParentId=0";
            }
            GroupTreeNode rn= sqlhelper.GetObject(GroupTreeNode.class,strsql);
            strsql = "select count(*) from vGroupItem where ParentId='" + rn.getId()+ "'";
            String tmpc=sqlhelper.ExecuteScalar(strsql);
            if(!tmpc.equals("0")&&!tmpc.equals("")) {
                strsql = "select * from vGroupItem where ParentId='" + rn.getId() + "'";
                List<GroupTreeNode> list = sqlhelper.GetObjectList(GroupTreeNode.class, strsql);
                for (GroupTreeNode vgp:list)
                {
                    GetGroupChildTree(vgp);
                }
                rn.setChildList(list);
            }
            return JSON.toJSONString(rn);
        }catch (Exception Ex)
        {
            return "";
        }
    }

    private Boolean SerailChildToTreeExtJs(List<GroupTreeNode> list,StringBuilder jsonData)
    {
        try
        {
            if (list.size() > 0)
            {
                for(int i=0;i<list.size();i++) {
                    GroupTreeNode gp = list.get(i);
                    String lnums =(gp.getChildList()!=null)?String.valueOf(gp.getChildList().size()):"0";
                    String leaf = (lnums.equals("0")) ? "true" : "false";
                    jsonData.append("{");
                    jsonData.append("\"id\":\"");
                    jsonData.append(gp.getId());
                    jsonData.append("\",\"text\":\"");
                    jsonData.append(gp.getGroupname());
                    jsonData.append("\",\"leaf\":");
                    jsonData.append(leaf);
                    if (gp.getChildList()!=null)
                    {
                        StringBuilder sbc=new StringBuilder();
                        SerailChildToTreeExtJs(gp.getChildList(),sbc);
                        jsonData.append(",\"children\":[");
                        jsonData.append(sbc.toString());
                        jsonData.append("]");
                    } else {
                        jsonData.append(",\"children\":[");
                        jsonData.append("]");
                    }

                    jsonData.append("}");
                    if(i<list.size()-1) {
                        jsonData.append(",");
                    }
                }
            }

            return true;
        }catch(Exception Ex)
        {
            return false;
        }
    }

    private Boolean SerailToTreeExtJs(GroupTreeNode rtn,StringBuilder jsonData)
    {
        try
        {
            jsonData.append("[");

            GroupTreeNode gp = rtn;
            String lnums =(rtn.getChildList()!=null)?String.valueOf(rtn.getChildList().size()):"0";
            String leaf = (lnums.equals("0")) ? "true" : "false";
            jsonData.append("{");
            jsonData.append("\"id\":\"");
            jsonData.append(gp.getId());
            jsonData.append("\",\"text\":\"");
            jsonData.append(gp.getGroupname());
            jsonData.append("\",\"leaf\":");
            jsonData.append(leaf);
            if (rtn.getChildList()!=null)
            {
                StringBuilder sbc=new StringBuilder();
                SerailChildToTreeExtJs(rtn.getChildList(),sbc);
                jsonData.append(",\"children\":[");
                jsonData.append(sbc.toString());
                jsonData.append("]");
            } else {
                jsonData.append(",\"children\":[");
                jsonData.append("]");
            }

            jsonData.append("}");
            jsonData.append("]");

            return true;
        }catch(Exception Ex)
        {
            return false;
        }
    }

    @RequestMapping(value="/GetGroupTreeExtJs", produces={"text/plain;charset=UTF-8"})
    @ResponseBody
    @ApiOperation(value ="GetGroupTreeExtJs",notes="获取树形单位机构列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "group", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    public String GetGroupTreeExtJs(@RequestParam(value = "group",required =true) int group,@RequestHeader(value = "token",required =true) String token)
    {
        String strsql="";
        SqlHelper sqlhelper=new SqlHelper();
        try
        {
            if(group!=0)
            {
                strsql="select * from vGroupItem where id='"+group+"'";
            }else
            {
                strsql="select * from vGroupItem where ParentId=0";
            }
            GroupTreeNode rn= sqlhelper.GetObject(GroupTreeNode.class,strsql);
            strsql = "select count(*) from vGroupItem where ParentId='" + rn.getId()+ "'";
            String tmpc=sqlhelper.ExecuteScalar(strsql);
            if(!tmpc.equals("0")&&!tmpc.equals("")) {
                strsql = "select * from vGroupItem where ParentId='" + rn.getId() + "'";
                List<GroupTreeNode> list = sqlhelper.GetObjectList(GroupTreeNode.class, strsql);
                for (GroupTreeNode vgp:list)
                {
                    GetGroupChildTree(vgp);
                }
                rn.setChildList(list);
            }

            StringBuilder jsonData=new StringBuilder();
            SerailToTreeExtJs(rn,jsonData);
            return jsonData.toString();
        }catch (Exception Ex)
        {
            return "";
        }
    }
}
