package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.AesHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.FunctionItem;
import com.yyszc.wpbase.entity.vFunctionItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value="/WpService/FunctionItem")
@Api(tags ="微服务框架接口->FunctionItem表操作接口")
public class FunctionItemController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/AddFunctionItem",method ={RequestMethod.POST})
    @ApiOperation(value ="AddFunctionItem",notes="新增资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "FunctionItem", value = "FunctionItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Integer> AddFunctionItem(@RequestBody FunctionItem entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                //提交数据库
                String strsql = DBHelper.GetInsertSQL(entity,"FunctionItem", Arrays.asList("Id"));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
                }
                String tmpstr=sqlhelper.ExecuteInsertWithObtainId(strsql);

                return ResponseEntity.ok().body(Integer.parseInt(tmpstr));
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
        }
    }

    @RequestMapping(value="/UpdateFunctionItem",method ={RequestMethod.POST})
    @ApiOperation(value ="UpdateFunctionItem",notes="修改资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "FunctionItem", value = "FunctionItem", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> UpdateFunctionItem(@RequestBody FunctionItem entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = DBHelper.GetUpdateSQL(entity,"FunctionItem", Arrays.asList("Id"), Arrays.asList(entity.getId().toString()));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
                }
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/DeleteFunctionItemById",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteFunctionItemById",notes="删除资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteFunctionItemById(@RequestParam("id") String id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from FunctionItem where id='"+id+"'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/GetFunctionItemById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetFunctionItemById",notes="获取资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<FunctionItem> GetFunctionItemById(@RequestParam("id") String id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from FunctionItem where id='"+id+"'";
                FunctionItem entity=sqlhelper.GetObject(FunctionItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetFunctionItemBySql",method ={RequestMethod.GET})
    @ApiOperation(value ="GetFunctionItemBySql",notes="获取资源列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<FunctionItem> GetFunctionItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                FunctionItem entity=sqlhelper.GetObject(FunctionItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetVFunctionItemBySql",method ={RequestMethod.GET})
    @ApiOperation(value ="GetVFunctionItemBySql",notes="获取资源列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<vFunctionItem> GetVFunctionItemBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                vFunctionItem entity=sqlhelper.GetObject(vFunctionItem.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetFunctionItemList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetFunctionItemList",notes="获取资源列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<FunctionItem>> GetFunctionItemList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<FunctionItem> plist=sqlhelper.GetObjectList(FunctionItem.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetFunctionItemByMark",method ={RequestMethod.GET})
    @ApiOperation(value ="GetFunctionItemByMark",notes="获取含有指定属性的资源信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "title", value = "String", required = true),
            @ApiImplicitParam(name = "parent", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<vFunctionItem> GetFunctionItemByMark(@RequestParam("title") String title,@RequestParam("parent") String parent,@RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from vFunctionItem where Title='" + title + "' and ParentName='" + parent + "'";
                List<vFunctionItem> list= sqlhelper.GetObjectList(vFunctionItem.class,strsql);
                if (list == null)
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
                }
                return ResponseEntity.ok().body(list.get(0));
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }
}
