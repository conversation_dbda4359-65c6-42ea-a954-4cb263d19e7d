package com.soft.framework.controller;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.yyszc.wpbase.entity.Module;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Controller
@Configuration
@RequestMapping(value="/Service/ZDYMan" )
@Api(tags ="微服务框架接口->自定义模块接口")
public class ZdyController {
    @Data
    public class ModuleTreeNode
    {
        public String id;
        public String label;
        public String parent;
        public String leaf;
        public String check;
        public String indeterminate;
        public List<ModuleTreeNode> children;
    }

    @RequestMapping(value="/SaveZDYSet",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="SaveZDYSet",notes="保存自定义模块接口")
    public AjaxResult SaveZDYSet(HttpServletRequest request)
    {
        AjaxResult ajax = null;

        String strsql="";

        String mlist=request.getParameter("mlist");
        if(StringUtil.IsNullOrEmpty(mlist))
        {
            ajax=AjaxResult.error("传输参数有误！");
            return ajax;
        }
        PersonEntity person = SessionHelper.getSessionPerson();
        if(person==null)
        {
            ajax=AjaxResult.error("未发现登录信息！");
            return ajax;
        }

        SqlProc sqlproc=new SqlProc();
        try {
            String[] idarr = mlist.split(",");

            //添加用户限制
            sqlproc.ExecuteNoQuery("delete from nft_navulink where lk_uid=" + person.getId());
            for (int i = 0; i < idarr.length; i++)
            {
                strsql = "insert into nft_navulink(lk_nid,lk_uid) values(" + idarr[i] + "," + person.getId() + ")";
                sqlproc.ExecuteNoQuery(strsql);
            }
            sqlproc.CommitTrans();

            ajax=AjaxResult.success("保存个人自定义模块配置成功！");
            return ajax;
        }
        catch(Exception ex)
        {
            sqlproc.RollbackTrans();
            LogHelper.WriteSysLog(ex.getMessage());
            ajax=AjaxResult.error("保存个人自定义模块配置产生异常！！");
            return ajax;
        }
    }

    private ModuleTreeNode GenerateModuleNode(int personId,int id,String title,String mlist)
    {
        SqlHelper sqlhelper=new SqlHelper();
        try
        {
            ModuleTreeNode tmpg=new ModuleTreeNode();
            tmpg.id = "g"+id;
            tmpg.leaf = "false";
            tmpg.label = title;
            tmpg.parent = "g0";
            tmpg.children=new ArrayList<ModuleTreeNode>();
            List<Module> _list = sqlhelper.GetObjectList(Module.class,"select * from module where id in("+mlist+")");
            String gnum= sqlhelper.ExecuteScalar("select count(*) from nft_navulink where lk_nid in("+mlist+") and lk_uid=" + personId);
            if (gnum==String.valueOf(_list.size()))
            {
                tmpg.check = "true";
                tmpg.indeterminate = "false";
            }
            else if (gnum == "0")
            {
                tmpg.check = "false";
                tmpg.indeterminate = "false";
            }
            else
            {
                tmpg.check = "false";
                tmpg.indeterminate = "true";
            }

            for (Module mo : _list)
            {
                ModuleTreeNode tmpm=new ModuleTreeNode();
                tmpm.id = mo.getID().toString();
                tmpm.label = mo.getModule_name();
                tmpm.leaf = "true";
                tmpm.parent = "g"+id;
                tmpm.indeterminate = "false";
                String rval = sqlhelper.ExecuteScalar("select count(*) from nft_navulink where lk_nid="+mo.getID().toString()+" and lk_uid=" + personId);
                if (rval.equals("0")) {
                    tmpm.check = "false";
                } else {
                    tmpm.check = "true";
                }
                tmpg.children.add(tmpm);
            }
            return tmpg;
        }
        catch (Exception Ex)
        {
            return null;
        }
    }

    private Boolean SerializeChildrenNode(List<ModuleTreeNode> _list, StringBuilder jsonData)
    {
        try
        {
            StringBuilder _jsonData = new StringBuilder();
            _jsonData.append(",\"children\":[");
            if (_list != null)
            {
                for (ModuleTreeNode fm : _list)
                {
                    _jsonData.append("{");
                    _jsonData.append("\"id\":\"");
                    _jsonData.append(fm.id);
                    _jsonData.append("\",\"text\":\"");
                    _jsonData.append(fm.label);
                    _jsonData.append("\",\"checked\":");
                    _jsonData.append(fm.check);
                    _jsonData.append(",\"leaf\":");
                    _jsonData.append(fm.leaf);
                    _jsonData.append(",\"indeterminate\":");
                    _jsonData.append(fm.indeterminate);
                    _jsonData.append("");
                    SerializeChildrenNode(fm.children, _jsonData);
                    _jsonData.append("},");
                }
                if (jsonData.charAt(jsonData.length() - 1) == ',')
                {
                    jsonData.deleteCharAt(jsonData.length() - 1);
                }
            }
            _jsonData.append("]");
            jsonData.append(_jsonData.toString());
        }
        catch (Exception Ex)
        {
            return false;
        }
        return true;
    }
    
    //按人资组织树
    public Boolean GetUserModuleTreeNode(int personId,StringBuilder jsonData)
    {
        SqlHelper sqlhelper=new SqlHelper();
        try
        {
            String strsql = "";
            String mlist = "";

            List<ModuleTreeNode> _list = new ArrayList<ModuleTreeNode>();
            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=2");
            ModuleTreeNode g2 = GenerateModuleNode(personId,2,"安全管控",mlist);
            if (g2 == null) {
                return false;
            }
            _list.add(g2);

            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=3");
            ModuleTreeNode g3 = GenerateModuleNode(personId,3,"经营管理",mlist);
            if (g3 == null) {
                return false;
            }
            _list.add(g3);

            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=4");
            ModuleTreeNode g4 = GenerateModuleNode(personId,4,"协同办公",mlist);
            if (g4 == null) {
                return false;
            }
            _list.add(g4);

            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=5");
            ModuleTreeNode g5 = GenerateModuleNode(personId,5,"物资管理",mlist);
            if (g5 == null) {
                return false;
            }
            _list.add(g5);

            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=7");
            ModuleTreeNode g7 = GenerateModuleNode(personId,7,"分包管理",mlist);
            if (g7 == null) {
                return false;
            }
            _list.add(g7);

            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=8");
            ModuleTreeNode g8 = GenerateModuleNode(personId,8,"系统管理",mlist);
            if (g8 == null) {
                return false;
            }
            _list.add(g8);

            mlist = sqlhelper.ExecuteScalar("select GP_MLIST from NFT_ModuleGroup where GP_ID=9");
            ModuleTreeNode g9 = GenerateModuleNode(personId,9,"其他管理",mlist);
            if (g9 == null) {
                return false;
            }
            _list.add(g9);

            if (_list.size() > 0)
            {
                jsonData.append("[");
                for (ModuleTreeNode fm :_list)
                {
                    jsonData.append("{");
                    jsonData.append("\"id\":\"");
                    jsonData.append(fm.id);
                    jsonData.append("\",\"text\":\"");
                    jsonData.append(fm.label);
                    jsonData.append("\",\"checked\":");
                    jsonData.append(fm.check);
                    jsonData.append(",\"leaf\":");
                    jsonData.append(fm.leaf);
                    jsonData.append(",\"indeterminate\":");
                    jsonData.append(fm.indeterminate);
                    jsonData.append("");
                    SerializeChildrenNode(fm.children, jsonData);
                    jsonData.append("},");
                }

                if (jsonData.charAt(jsonData.length() - 1) == ',')
                {
                    jsonData.deleteCharAt(jsonData.length() - 1);
                }

                jsonData.append("]");
            }

            return true;
        }
        catch (Exception Ex)
        {
            return false;
        }
    }

    @RequestMapping(value="/GetUserModuleTree",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @ApiOperation(value ="GetUserModuleTree",notes="获取自定义模块树接口")
    public String GetUserModuleTree()
    {
        String jsonstr = "";
        try
        {
            PersonEntity person = SessionHelper.getSessionPerson();
            StringBuilder jsonb0 = new StringBuilder();
            if (!GetUserModuleTreeNode(person.getId(),jsonb0))
            {
                jsonstr = JsonHelper.ToRequest(false, "获取人员模块树信息失败！","-2");
                return jsonstr;
            }

            jsonstr =jsonb0.toString();
            return jsonstr;
        }
        catch (Exception Ex)
        {
            jsonstr = JsonHelper.ToRequest(false, "获取人员模块树信息失败，系统异常！","-3");
            return jsonstr;
        }
    }
}
