package com.soft.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "jplat")
public class BaseConfig {
    public static int getIsOpen() {
        return IsOpen;
    }

    public void setIsOpen(int isOpen) {
        BaseConfig.IsOpen = isOpen;
    }

    /**
     * 是否记录日志
     */
    private static int IsOpen;
    /**
     * 是否记录日志
     */
    private static int LogFlag;
    /**
     * 是否记录SQL日志
     */
    private static int SqlTg;

    public static String getNetFrame() {
        return NetFrame;
    }

    public void setNetFrame(String netFrame) {
        BaseConfig.NetFrame = netFrame;
    }

    /**
     * 系统mark
     */
    private static String NetFrame;

    /**
     * 系统mark
     */
    private static String SystemMark;

    public static String getRunMode() {
        return RunMode;
    }

    public void setRunMode(String runMode) {
        BaseConfig.RunMode = runMode;
    }

    /**
     * 系统mark
     */
    private static String RunMode;
    /**
     * 是否加载数据库配置
     */
    private static int LoadDBConf;
    /**
     * 项目名称
     */
    private static String name;
    /**
     * ExtJS版本
     */
    private static String ExtVersion;
    /**
     * 版本
     */
    private static String version;
    /**
     * 实例演示开关
     */
    private static boolean demoEnabled;
    /**
     * 上传路径
     */
    private static String profile;
    /**
     * 短信总开关
     */
    private static int SmsFlag;
    /**
     * 通知短信总开关
     */
    private static int NoticeSmsFlag;

    public static String getOssLocalPath() {
        return OssLocalPath;
    }

    public void setOssLocalPath(String ossLocalPath) {
        BaseConfig.OssLocalPath = ossLocalPath;
    }

    public static String getOssRemotePath() {
        return OssRemotePath;
    }

    public void setOssRemotePath(String ossRemotePath) {
        BaseConfig.OssRemotePath = ossRemotePath;
    }

    /**
     * OssLocalPath
     */
    private static String OssLocalPath;
    /**
     * OSSRemoteRoot
     */
    private static String OssRemotePath;
    /**
     * 文件分隔符
     */
    private static String separator;

    public static String getTokenSecret() {
        return TokenSecret;
    }

    public void setTokenSecret(String tokenSecret) {
        BaseConfig.TokenSecret = tokenSecret;
    }

    /**
     * 文件分隔符
     */
    private static String TokenSecret;

    public static String getWhiteList() {
        return WhiteList;
    }

    public void setWhiteList(String whiteList) {
        BaseConfig.WhiteList = whiteList;
    }

    /**
     * 文件分隔符
     */
    private static String WhiteList;

    public static String getWpFrame() {
        return WpFrame;
    }

    public void setWpFrame(String wpFrame) {
        BaseConfig.WpFrame = wpFrame;
    }

    /**
     * 文件分隔符
     */
    private static String WebRoot;

    public static String getWebRoot() {
        return WebRoot;
    }

    public void setWebRoot(String webRoot) {
        BaseConfig.WebRoot = webRoot;
    }
    /**
     * 文件分隔符
     */
    private static String WpFrame;
    /**
     * 上传路径
     */
    private static int SessionTimeOut;
    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    public static int getSmsFlag() {
        return SmsFlag;
    }

    public void setSmsFlag(int smsFlag) {
        BaseConfig.SmsFlag = smsFlag;
    }

    public static int getNoticeSmsFlag() {
        return NoticeSmsFlag;
    }

    public void setNoticeSmsFlag(int noticeSmsFlag) {
        BaseConfig.NoticeSmsFlag = noticeSmsFlag;
    }

    public static String getSeparator() {
        return separator;
    }

    public void setSeparator(String separator) {
        BaseConfig.separator = separator;
    }

    public static int getLogFlag() {
        return LogFlag;
    }

    public void setLogFlag(int logFlag) {
        BaseConfig.LogFlag = logFlag;
    }

    public static int getSqlTg() {
        return SqlTg;
    }

    public void setSqlTg(int sqlTg) {
        BaseConfig.SqlTg = sqlTg;
    }

    public static String getSystemMark() {
        return SystemMark;
    }

    public void setSystemMark(String systemMark) {
        BaseConfig.SystemMark = systemMark;
    }

    public static int getLoadDBConf() {
        return LoadDBConf;
    }

    public void setLoadDBConf(int loadDBConf) {
        BaseConfig.LoadDBConf = loadDBConf;
    }

    public static String getName() {
        return name;
    }

    public void setName(String name) {
        BaseConfig.name = name;
    }

    public static String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        BaseConfig.version = version;
    }

    public static boolean isDemoEnabled() {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        BaseConfig.demoEnabled = demoEnabled;
    }

    public static String getProfile() {
        return (profile+"/").replace("//","/");
    }

    public void setProfile(String profile) {
        BaseConfig.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        BaseConfig.addressEnabled = addressEnabled;
    }

    public static String getExtVersion() {
        return ExtVersion;
    }

    public void setExtVersion(String extVersion) {
        BaseConfig.ExtVersion = extVersion;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return getProfile() + "download/";
    }

    /**
     * 获取上传路径
     */
    public static String getLogPath() {
        String baseUrl = getProfile();
        return baseUrl + "logs/";
    }

    public static String getHotupdatePath() {
        String baseUrl = getProfile();
        return baseUrl + "hotupdate/";
    }

    public static String getUploadPath() {
        String baseUrl = getProfile();
        return baseUrl + "upload/";
    }

    public static String getTemplatePath() {
        String baseUrl = getProfile();
        return baseUrl + "temp/";
    }

    public static String getReportPath() {
        String baseUrl = getProfile();
        return baseUrl + "report/";
    }

    public static String getMobanPath() {
        String baseUrl = getProfile();
        return baseUrl + "moban/";
    }

    public static String getCachePath() {
        String baseUrl = getProfile();
        return baseUrl + "cache/";
    }

    public static int getSessionTimeOut() {
        return SessionTimeOut;
    }

    public void setSessionTimeOut(int sessionTimeOut) {
        BaseConfig.SessionTimeOut = sessionTimeOut;
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public static String getAbsoluteFile(String filename) {
        String downloadPath = getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
}
