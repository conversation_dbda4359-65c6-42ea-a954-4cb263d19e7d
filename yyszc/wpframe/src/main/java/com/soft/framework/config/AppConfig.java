package com.soft.framework.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
public class AppConfig {
    public static Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        AppConfig.port = port;
    }

    @Value("${server.port}")
    private static Integer port;
}
