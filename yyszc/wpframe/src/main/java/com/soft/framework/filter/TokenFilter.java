package com.soft.framework.filter;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.SecurityUtils;
import com.soft.framework.common.utils.ip.IpUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.SessionHelper;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.TokenService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * token过滤器 验证token有效性
 *
 */
public class TokenFilter extends OncePerRequestFilter
{
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException
    {
        try
        {
            //LogHelper.WriteSysLog("TokenFilter检测点00");
            String surl=request.getRequestURL().toString();
            String sip= IpUtils.getIpAddr(request);

            if (IsWhiteListFunc(request)) {
                chain.doFilter(request, response);
                return;
            }

            TokenService tokenService= (TokenService)SpringUtil.getBean("tokenService");
            if(tokenService!=null)
            {
                String token=tokenService.getToken(request);
                LoginUser loginUser = StringUtil.IsNullOrEmpty(token)?null:tokenService.getLoginUser(token);

                if (loginUser!=null)
                {
                    //用户当前token有效，没有被其他用户挤出
                    if(SessionHelper.SessionVaild(token)) {
                        tokenService.verifyToken(loginUser);

                        if (SecurityUtils.getAuthentication() == null) {
                            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                        }
                    }else{
                        //被挤出的情况下，主动清除token
                        tokenService.delLoginUser(token);
                        ResponseInValidTokenInfo(response);
                        return;
                    }
                } else {
                    if(request.getParameter("jtoken")!=null)
                    {
                        String jtoken=request.getParameter("jtoken").toString();
                        String userkey= tokenService.getTokenKey(jtoken);
                        loginUser=tokenService.getLoginUserFromKey(userkey);
                        if (loginUser!=null) {
                            tokenService.verifyToken(loginUser);
                            if(SecurityUtils.getAuthentication()==null) {
                                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                            }
                        }else
                        {
                            ResponseNoTokenInfo(response);
                            return;
                        }
                    }else if(request.getParameter("jtokens")!=null)
                    {
                        String jtokens=request.getParameter("jtokens").toString();
                        loginUser=tokenService.getLoginUser(jtokens);
                        if (loginUser!=null) {
                            tokenService.verifyToken(loginUser);
                            if(SecurityUtils.getAuthentication()==null) {
                                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                            }
                        }else
                        {
                            ResponseNoTokenInfo(response);
                            return;
                        }
                    }else {
                        LogHelper.WriteSysLog("生成Token失败,未携带有效token标识,sip=" + sip);
                        if (!IsWhiteListFunc(request)) {
                            ResponseNoTokenInfo(response);
                            return;
                        }
                    }
                }
            }
            chain.doFilter(request, response);
        }catch (Exception Ex)
        {
            LogHelper.WriteSysLog("系统验证token时产生异常:"+Ex.getMessage());
            return;
        }
    }

    private boolean IsWhiteListFunc(HttpServletRequest request)
    {
        List<String> excludes = new ArrayList<String>();
        String whitelist=ConfigHelper.getWhiteList();
        if (StringUtil.isNotEmpty(whitelist))
        {
            String[] url = whitelist.split(",");
            for (int i = 0; url != null && i < url.length; i++)
            {
                excludes.add(url[i]);
            }
        }

        if (excludes == null || excludes.isEmpty())
        {
            return false;
        }
        String url = request.getServletPath();
        for (String pattern : excludes)
        {
            AntPathMatcher matcher = new AntPathMatcher();
            if(matcher.match(pattern,url))
            {
                return true;
            }
        }
        return false;
    }

    public void ResponseNoTokenInfo(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult=AjaxResult.error("检测到当前账户没有登录，终止操作",-1,true);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    public void ResponseInValidTokenInfo(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult=AjaxResult.error("检测到当前账户在其他地方登录！",-1,true);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
}
