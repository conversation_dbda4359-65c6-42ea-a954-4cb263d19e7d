package com.soft.framework.controller.wpservice;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.AesHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.Lc_currentState;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value="/WpService/CurrentState")
@Api(tags ="微服务框架接口->Lc_currentState表操作接口")
public class Lc_currentStateController {
    @Autowired(required =false)
    private TokenService tokenService;

    @RequestMapping(value="/AddLc_currentState",method ={RequestMethod.POST})
    @ApiOperation(value ="AddLc_currentState",notes="新增流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lc_currentState", value = "Lc_currentState", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Integer> AddLc_currentState(@RequestBody Lc_currentState entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                //提交数据库
                String strsql = DBHelper.GetInsertSQL(entity,"Lc_currentState", Arrays.asList("ID"));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
                }
                String tmpstr=sqlhelper.ExecuteInsertWithObtainId(strsql);

                return ResponseEntity.ok().body(Integer.parseInt(tmpstr));
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(-1);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(-1);
        }
    }

    @RequestMapping(value="/UpdateLc_currentState",method ={RequestMethod.POST})
    @ApiOperation(value ="UpdateLc_currentState",notes="修改流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Lc_currentState", value = "Lc_currentState", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> UpdateLc_currentState(@RequestBody Lc_currentState entity, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = DBHelper.GetUpdateSQL(entity,"Lc_currentState", Arrays.asList("ID"), Arrays.asList(entity.getID().toString()));
                if (StringUtil.IsNullOrEmpty(strsql))
                {
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
                }
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/DeleteLc_currentStateById",method ={RequestMethod.GET})
    @ApiOperation(value ="DeleteLc_currentStateById",notes="删除流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Boolean> DeleteLc_currentStateById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "delete from Lc_currentState where id='"+id+"'";
                sqlhelper.ExecuteNoQuery(strsql);

                return ResponseEntity.ok().body(true);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(false);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(false);
        }
    }

    @RequestMapping(value="/GetLc_currentStateById",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLc_currentStateById",notes="获取流程状态信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "int", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Lc_currentState> GetLc_currentStateById(@RequestParam("id") int id, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();

                String strsql = "select * from Lc_currentState where ID='"+id+"'";
                Lc_currentState entity=sqlhelper.GetObject(Lc_currentState.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetLc_currentStateBySql",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLc_currentStateBySql",notes="获取流程状态列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<Lc_currentState> GetLc_currentStateBySql(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                Lc_currentState entity=sqlhelper.GetObject(Lc_currentState.class,strsql);
                return ResponseEntity.ok().body(entity);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }

    @RequestMapping(value="/GetLc_currentStateList",method ={RequestMethod.GET})
    @ApiOperation(value ="GetLc_currentStateList",notes="获取流程状态列表信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "strsql", value = "String", required = true),
            @ApiImplicitParam(name = "token", value = "String", required = true)
    })
    public ResponseEntity<List<Lc_currentState>> GetLc_currentStateList(@RequestParam("strsql") String strsql, @RequestHeader(value = "token",required =true) String token)
    {
        if(StringUtil.IsNullOrEmpty(token)) {
            ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
        }

        try {
            if (tokenService.verifyToken(token)) {
                SqlHelper sqlhelper=new SqlHelper();
                if(!strsql.equals("")) {
                    strsql= AesHelper.aesDecodeCBC(strsql);
                }
                List<Lc_currentState> plist=sqlhelper.GetObjectList(Lc_currentState.class,strsql);
                return ResponseEntity.ok().body(plist);
            } else {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
        }catch(Exception Ex)
        {
            return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(null);
        }
    }
}
