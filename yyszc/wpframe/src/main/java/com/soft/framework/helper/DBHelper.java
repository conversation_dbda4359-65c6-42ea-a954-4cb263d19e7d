package com.soft.framework.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.soft.framework.common.features.MSSqlFeatures;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.date.DateUtils;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.gcc.base.entity.ApplicationLog;
import com.sun.jna.platform.win32.Guid;
import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.*;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class DBHelper {
    private static final Logger logger = LoggerFactory.getLogger(DBHelper.class);

    private static final String TabSchema = ConfigHelper.getDBSchema();

    public static List<NFT_SQL_IDSTR> GetPreventSqlList()
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<NFT_SQL_IDSTR> sqlids=sqlhelper.GetObjectList(NFT_SQL_IDSTR.class,"select * from NFT_SQL_IDSTR order by id");
        return sqlids;
    }

    public static List<NFT_WhiteList> GetNftWhiteList()
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<NFT_WhiteList> list=sqlhelper.GetObjectList(NFT_WhiteList.class,"select * from NFT_WhiteList order by id");
        return list;
    }

    public static List<NFT_NWIPD> GetNwIpdList()
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<NFT_NWIPD> ips=sqlhelper.GetObjectList(NFT_NWIPD.class,"select * from NFT_NWIPD order by id");
        return ips;
    }

    public static DataTable GetUserNavTable(String mid,int userid,int pwf)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";

        if(mid.equals(""))
        {
            strsql=" select Id,Title,OrderNumber from (";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "'))";
            strsql += " union ";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from functionItem where id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "')))";
            strsql+=") inntab order by OrderNumber";
        }else
        {
            strsql=" select Id,Title,OrderNumber from (";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + "))";
            strsql += " union ";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from functionItem where id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + ")))";
            strsql+=") inntab order by OrderNumber";
        }

        DataTable dsData = sqlhelper.GetDataTable(strsql);
        return dsData;
    }

    public static List<vFunctionItem> GetUserFunctionList(String mid,int userid,int pwf)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";

        strsql=" select Id,Title,ParentId,OrderNumber,Url,Parameter,DisplayName,module_ID from (";
        strsql += "select Id,Title,ParentId,OrderNumber,Url,Parameter,DisplayName,module_ID from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + ")";
        strsql += " union ";
        strsql += "select Id,Title,ParentId,OrderNumber,Url,Parameter,DisplayName,module_ID from functionItem where parentId='0' and id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + "))";
        strsql += " union ";
        strsql += "select Id,Title,ParentId,OrderNumber,Url,Parameter,DisplayName,module_ID from functionItem where parentId='0' and id in(select parentid from functionItem where id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + ")))";
        strsql+=") inntab order by ParentId,OrderNumber";

        List<vFunctionItem> _list=sqlhelper.GetObjectList(vFunctionItem.class,strsql);
        return _list;
    }

    public static String GetUserNavMenuGroup(String mid,int userid,int pwf)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "",retval="";

        if(mid.equals(""))
        {
            strsql =" select Id,Title,OrderNumber from (";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "'))";
            strsql += " union ";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from functionItem where id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "')))";
            strsql += " union ";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and IsPublic=99";
            strsql+=") inntab order by OrderNumber";
        }else
        {
            strsql =" select Id,Title,OrderNumber from (";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + "))";
            strsql += " union ";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and id in(select parentid from functionItem where id in(select parentid from dbo.FUNC_USR_FUNC('" + userid + "','" + pwf + "') where module_ID in(" + mid + ")))";
            strsql += " union ";
            strsql += "select Id,Title,OrderNumber from functionItem where parentId='0' and IsPublic=99";
            strsql+=") inntab order by OrderNumber";
        }

        DataTable dsData = sqlhelper.GetDataTable(strsql);
        if (dsData!=null&&dsData.getTotalCount()> 0)
        {
            retval = dsData.getRow(0).getColValue(0).toString();
        }else
        {
            return "";
        }

        return retval;
    }

    /// <summary>
    /// 获取实体的插入语句
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetInsertSQL(T obj, String tableName, List<String> idlist){
        String name="";
        try {
            StringBuilder strSQL = new StringBuilder();
            strSQL.append("insert into " + tableName + "(");

            String fields = "";
            String values = "";

            Field[] fieldArr = obj.getClass().getDeclaredFields();
            for (Field pi : fieldArr) {
                pi.setAccessible(true);
                name = pi.getName();
                if (name == "emptyChangingEventArgs") {
                    continue;
                }
                if (idlist.size() > 0 && idlist.indexOf(name) >= 0) {
                    continue;
                }

                if (!FieldIsExists(tableName, name.toString())) {
                    continue;
                }
                Object val = pi.get(obj);
                if(val==null) {
                    continue;
                }

                if (pi.getType().equals(String.class)) {
                    String value = ((val==null)?"":val.toString()).replace("'", "''");
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += "'" + value + "',";
                    }
                } else if (
                    pi.getType().equals(Integer.class)||pi.getType().equals(Boolean.class)||
                    pi.getType().equals(Long.class)) {
                    String value = (val==null)?"null":val.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                } else if (
                    pi.getType().equals(Float.class)||pi.getType().equals(Double.class)||
                    pi.getType().equals(BigDecimal.class)) {
                    BigDecimal bg=new BigDecimal(pi.get(obj)+"");
                    String value = (bg==null)?"null":bg.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                }else if (pi.getType().equals(Date.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str((Date) val, DateUtil.datetimeFormat);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDateTime.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str(DateUtil.toDate((LocalDateTime) val), DateUtil.datetimeFormatf);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDate.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.toDateStr((LocalDate) val);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }
            }

            // 去掉最后一个,
            fields = fields.substring(0, fields.length() - 1);
            values = values.substring(0, values.length() - 1);

            // 拼接Sql串
            strSQL.append(fields);
            strSQL.append(") values (");
            strSQL.append(values);
            strSQL.append(")");

            strSQL.append(";select dbo.FUNC_IDENT_CURR(SCOPE_IDENTITY(),'" + tableName + "');");

            return strSQL.toString();
        }catch(Exception Ex)
        {
            return "";
        }
    }

    /// <summary>
    /// 获取实体的插入语句
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetInsertSQL(T obj, String tableName, List<String> idlist, List<String> EFields){
        String name="";
        try {
            StringBuilder strSQL = new StringBuilder();
            strSQL.append("insert into " + tableName + "(");

            String fields = "";
            String values = "";

            Field[] fieldArr = obj.getClass().getDeclaredFields();
            for (Field pi : fieldArr) {
                pi.setAccessible(true);
                name = pi.getName();
                if (name == "emptyChangingEventArgs") {
                    continue;
                }
                if (idlist.size() > 0 && idlist.indexOf(name) >= 0) {
                    continue;
                }
                if (EFields!=null&&EFields.size()>0)
                {
                    if(EFields.indexOf(name)>=0) {
                        continue;
                    }
                }

                if (!FieldIsExists(tableName, name.toString())) {
                    continue;
                }
                Object val = pi.get(obj);
                if(val==null) {
                    continue;
                }

                if (pi.getType().equals(String.class)) {
                    String value = ((val==null)?"":val.toString()).replace("'", "''");
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += "'" + value + "',";
                    }
                } else if (
                        pi.getType().equals(Integer.class)||pi.getType().equals(Boolean.class)||
                                pi.getType().equals(Long.class)) {
                    String value = (val==null)?"null":val.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                } else if (
                        pi.getType().equals(Float.class)||pi.getType().equals(Double.class)||
                        pi.getType().equals(BigDecimal.class)) {
                    BigDecimal bg=new BigDecimal(pi.get(obj)+"");
                    String value = (bg==null)?"null":bg.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                }else if (pi.getType().equals(Date.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str((Date) val, DateUtil.datetimeFormat);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDateTime.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str(DateUtil.toDate((LocalDateTime) val), DateUtil.datetimeFormatf);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDate.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.toDateStr((LocalDate) val);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }
            }

            // 去掉最后一个,
            fields = fields.substring(0, fields.length() - 1);
            values = values.substring(0, values.length() - 1);

            // 拼接Sql串
            strSQL.append(fields);
            strSQL.append(") values (");
            strSQL.append(values);
            strSQL.append(")");

            strSQL.append(";select dbo.FUNC_IDENT_CURR(SCOPE_IDENTITY(),'" + tableName + "');");

            return strSQL.toString();
        }catch(Exception Ex)
        {
            return "";
        }
    }


    /// <summary>
    /// 获取实体的更新SQL串
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetUpdateSQL(T obj, String tableName, List<String> tableKey, List<String> keyValue) {
        return GetUpdateSQL(obj,tableName,tableKey,keyValue,null,null);
    }


    /// <summary>
    /// 获取实体的更新SQL串
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetUpdateSQL(T obj, String tableName, List<String> tableKey, List<String> keyValue, List<String> OFields, List<String> EFields) {
        String name="";
        try {
            StringBuilder strSQL = new StringBuilder();

            if (tableKey.size() <= 0 || keyValue.size() <= 0 || tableKey.size() != keyValue.size()) {
                return "";
            }

            String subSQL = "";
            String condition = "";
            strSQL.append("update " + tableName + " set ");
            for (int i = 0; i < tableKey.size(); i++) {
                if (condition == "") {
                    condition = tableKey.get(i).toString() + "='" + keyValue.get(i).toString().replace("'", "''") + "'";
                } else {
                    condition = condition + " and " + tableKey.get(i).toString() + "='" + keyValue.get(i).toString().replace("'", "''") + "'";
                }
            }
            condition = " where " + condition;

            Field[] fieldArr = obj.getClass().getDeclaredFields();
            for (Field pi : fieldArr) {
                pi.setAccessible(true);
                name = pi.getName();

                if (name == "emptyChangingEventArgs") {
                    continue;
                }
                if (tableKey.indexOf(name) >= 0) {
                    continue;
                }
                if (OFields!=null&&OFields.size()>0)
                {
                    if(OFields.indexOf(name)<0) {
                        continue;
                    }
                }
                if (EFields!=null&&EFields.size()>0)
                {
                    if(EFields.indexOf(name)>=0) {
                        continue;
                    }
                }

                if (!FieldIsExists(tableName, name.toString())) {
                    continue;
                }
                Object val = pi.get(obj);

                if (pi.getType().equals(String.class)) {
                    String value = ((val==null)?"":val.toString()).replace("'", "''");
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        subSQL += name + "='" + value + "',";
                    } else {
                        if (val == null) {
                            subSQL += name + "=null,";
                        } else {
                            subSQL += name + "='',";
                        }
                    }
                }else if (
                    pi.getType().equals(Integer.class)||pi.getType().equals(Boolean.class)||
                    pi.getType().equals(Long.class)){
                    String value = (val==null)?"":val.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        subSQL += name + "='" + value + "',";
                    } else {
                        subSQL += name + "=null,";
                    }
                }else if (
                    pi.getType().equals(Float.class)||pi.getType().equals(Double.class)||
                    pi.getType().equals(BigDecimal.class)) {
                    BigDecimal bg=null;
                    if(val!=null) {
                        bg=new BigDecimal(pi.get(obj)+"");
                    }
                    String value = (bg==null)?"":bg.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        subSQL += name + "='" + value + "',";
                    } else {
                        subSQL += name + "=null,";
                    }
                }else if (pi.getType().equals(Date.class)){
                    if(val!=null) {
                        String valstr = DateUtil.date2Str((Date) val, DateUtil.datetimeFormat);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            subSQL += name + "=" + value + ",";
                        } else {
                            subSQL += name + "=null,";
                        }
                    }
                }else if (pi.getType().equals(LocalDateTime.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str(DateUtil.toDate((LocalDateTime) val), DateUtil.datetimeFormatf);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            subSQL += name + "=" + value + ",";
                        } else {
                            subSQL += name + "=null,";
                        }
                    }
                }else if (pi.getType().equals(LocalDate.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.toDateStr((LocalDate) val);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            subSQL += name + "=" + value + ",";
                        } else {
                            subSQL += name + "=null,";
                        }
                    }
                }
            }

            // 去掉最后一个,
            subSQL = subSQL.substring(0, subSQL.length() - 1);

            // 拼接上更新子句
            strSQL.append(subSQL);

            // 加入更新条件
            strSQL.append(condition);

            return strSQL.toString();
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static Boolean FieldIsExists(String tableN, String fieldN) {
        SqlHelper sqlhelper = new SqlHelper();
        String strR = sqlhelper.GetColType(TabSchema, tableN, fieldN);
        if (strR == "" || strR == null) {
            return false;
        } else {
            return true;
        }
    }

    public static String ToRecordCountSql(String sql)
    {
        String strSql = "select count(*) from (" + sql + " ) tab ";
        return strSql;
    }

    public static void WriteHandLogInfo(String hlmark, String msgstr, int percent)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";
        try
        {
            strsql = "insert into CPS_T_HAND_LOG(HL_MARK,HL_INFO,HL_RATE,HL_DATE) values('" + hlmark + "','" + msgstr + "'," + percent + ",getdate())";
            sqlhelper.ExecuteNoQuery(strsql);
        }
        catch (Exception Ex)
        {
        }
        return;
    }

    public static void ClearHandLogInfo(String hlmark)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";
        try
        {
            strsql = "delete from CPS_T_HAND_LOG where HL_MARK='" + hlmark + "'";
            sqlhelper.ExecuteNoQuery(strsql);
        }
        catch (Exception Ex)
        {
        }
        return;
    }

    public static void GetHandLogInfo_ws(String hlmark, StringBuilder msginfo)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try
        {
            String strsql = "select * from CPS_T_HAND_LOG where HL_MARK='" + hlmark + "' order by HL_DATE asc;";
            DataTable tmpdt = sqlhelper.GetDataTable(strsql);
            for (DataRow tmpdr:tmpdt.getRowList()) {
                msginfo.append(tmpdr.getColumn("HL_INFO").toString() + "\n");
            }
        }
        catch (Exception Ex)
        {

        }
    }

    public static void GetHandLogInfo(String hlmark,StringBuilder msginfo,StringBuilder percent)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try
        {
            DataTable tmpdt = null;

            String strsql = "select * from CPS_T_HAND_LOG where HL_MARK='" + hlmark + "' order by HL_DATE asc;";
            tmpdt = sqlhelper.GetDataTable(strsql);

            if (tmpdt.getTotalCount()> 0)
            {
                percent.append(tmpdt.getRow(tmpdt.getTotalCount()-1).getColValue("HL_RATE").toString());
            }
            for (DataRow tmpdr:tmpdt.getRowList()) {
                String tmpstr = tmpdr.getColValue("HL_INFO").toString();
                if (!tmpstr.endsWith("\n")) {
                    msginfo.append(tmpstr + "\n");
                } else {
                    msginfo.append(tmpstr);
                }
            }
        }
        catch (Exception Ex)
        {

        }
    }

    public static List<PersonEntity> GetRolePersonList(int roleid)
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<PersonEntity> list = new ArrayList<PersonEntity>();

        DataTable dt = sqlhelper.GetDataTable("select a.PersonId from RolePerson a,person b where a.RoleId=" + roleid + " and a.personid=b.id");
        for (int i = 0; i < dt.getTotalCount(); i++)
        {
            DataRow dr = dt.getRow(i);
            int pid = Integer.parseInt(dr.getColValue("PersonId").toString());
            PersonEntity person = sqlhelper.GetObject(PersonEntity.class,"select * from vPerson where id=" + pid);
            if (person != null)
            {
                list.add(person);
            }
        }

        return list;
    }

    public static List<PersonEntity> GetRolePersonList(int roleid, int topGroupId)
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<PersonEntity> list = new ArrayList<PersonEntity>();

        DataTable dt = sqlhelper.GetDataTable("select a.PersonId from RolePerson a,person b where a.RoleId=" + roleid + " and a.personid=b.id");
        for (int i = 0; i < dt.getTotalCount(); i++)
        {
            DataRow dr = dt.getRow(i);
            int pid = Integer.parseInt(dr.getColValue("PersonId").toString());
            PersonEntity person = sqlhelper.GetObject(PersonEntity.class,"select * from vPerson where id=" + pid);
            if (person != null)
            {
                if (person.getTopGroupId()== topGroupId)
                {
                    list.add(person);
                }
            }
        }

        return list;
    }

    public static List<PersonEntity> GetPermissionPersonList(String permission)
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<PersonEntity> list = new ArrayList<PersonEntity>();

        DataTable dt = sqlhelper.GetDataTable("select distinct a.PersonId from RolePerson a,person b where a.RoleId in(select roleid from RolePermission where PermissionNo='" + permission + "') and a.PersonId=b.id");
        for (int i = 0; i < dt.getTotalCount(); i++)
        {
            DataRow dr = dt.getRow(i);
            int pid = Integer.parseInt(dr.getColValue("PersonId").toString());
            PersonEntity person = sqlhelper.GetObject(PersonEntity.class,"select * from vPerson where id=" + pid);
            list.add(person);
        }

        return list;
    }

    public static List<PersonEntity> GetPermissionPersonList(String permission, int topGroupId)
    {
        SqlHelper sqlhelper = new SqlHelper();
        List<PersonEntity> list = new ArrayList<PersonEntity>();

        DataTable dt = sqlhelper.GetDataTable("select distinct a.PersonId from RolePerson a,person b where a.RoleId in(select roleid from RolePermission where PermissionNo='" + permission + "') and a.PersonId=b.id");
        for (int i = 0; i < dt.getTotalCount(); i++)
        {
            DataRow dr = dt.getRow(i);
            int pid = Integer.parseInt(dr.getColValue("PersonId").toString());
            PersonEntity person = sqlhelper.GetObject(PersonEntity.class,"select * from vPerson where id=" + pid);
            if (person != null)
            {
                if (person.getTopGroupId() == topGroupId)
                {
                    list.add(person);
                }
            }
        }

        return list;
    }

    public static List<vComp> GetAllTopGroup()
    {
        SqlHelper sqlhelper = new SqlHelper();

        String sqlString = "Select * From vComp";
        List<vComp> list=sqlhelper.GetObjectList(vComp.class,sqlString);

        return list;
    }

    public static List<vComp> GetAllTopGroup(int mid)
    {
        SqlHelper sqlhelper = new SqlHelper();

        String sqlString = "Select * From vComp where COMP_ID in(select Group_Id from Module_Group where Module_id="+mid+")";
        List<vComp> list=sqlhelper.GetObjectList(vComp.class,sqlString);

        return list;
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static PersonEntity GetPersonByLoginName(String loginName)
    {
        SqlHelper sqlhelper = new SqlHelper();
        PersonEntity person = null;

        try {
            String strsql = "select * from vPerson where (LoginName='" + loginName + "' or LoginName2='"+loginName+"')";

            //获取一条用户数据（Reader格式），并转换为用户实体类型
            vPerson vp = sqlhelper.GetObject(vPerson.class, strsql);
            person = new PersonEntity();
            person.setId(vp.getId());
            person.setLoginName(vp.getLoginName());
            person.setLoginName2(vp.getLoginName2());
            person.setRealName(vp.getRealName());
            person.setPassword(vp.getPassword());
            person.setGroupId(vp.getGroupId());
            person.setGroupName(vp.getGroupName());
            person.setParentGroupId(vp.getParentId());
            person.setParentGroupName(vp.getParentName());
            person.setGroupType(vp.getGroupType());
            person.setTelephone(vp.getTelephone());
            person.setOA(vp.getOA());
            person.setMsgType(vp.getMsgType());

            strsql = "select * from person where id="+vp.getId();
            Person p = sqlhelper.GetObject(Person.class, strsql);
            person.setType(p.getType());
            person.setSphone(p.getSphone());
            person.setPhName(p.getPhName());
            person.setCertificateID(p.getCertificateID());
            person.setOfficePhone(p.getOfficePhone());
            person.setState(p.getState());
            person.setRoleId(p.getRoleId());

            if(person.getRoleId()!=0&&person.getRoleId()!=null)
            {
                strsql="select * from role where id="+person.getRoleId();
                Role role=sqlhelper.GetObject(Role.class,strsql);
                if(role!=null)
                {
                    person.setAdminGroupIds(role.getAdminGroupIds());
                    person.setRoleName(role.getRoleName());
                }
            }

            if(person.getGroupId()!=0&&person.getGroupId()!=null)
            {
                strsql="select * from GroupItem where id="+person.getGroupId();
                GroupItem groupItem=sqlhelper.GetObject(GroupItem.class,strsql);
                if(groupItem!=null)
                {
                    person.setUComapanyBH(groupItem.getUComapanyBH());
                    person.setUComapanyJC(groupItem.getUComapanyJC());
                    person.setUComapanyQC(groupItem.getUComapanyQC());
                }
            }

            String tmpstr = sqlhelper.ExecuteScalar("select TopGroupId from vGroupItem where id=" + person.getGroupId());
            if (!StringUtil.IsNullOrEmpty(tmpstr)) {
                person.setTopGroupId(Integer.parseInt(tmpstr));

                tmpstr = sqlhelper.ExecuteScalar("select GroupName from GroupItem where id=" + person.getGroupId());
                if (!StringUtil.IsNullOrEmpty(tmpstr)) {
                    person.setTopGroupName(tmpstr);
                }
            }

            tmpstr = sqlhelper.ExecuteScalar("select uComapanyQC from GroupItem where id=" + person.getGroupId());
            person.setGroupNameS(tmpstr);
            if (person.getParentGroupId() != null && person.getParentGroupId().intValue() != 0) {
                tmpstr = sqlhelper.ExecuteScalar("select uComapanyQC from GroupItem where id=" + person.getParentGroupId());
                person.setParentGroupNameS(tmpstr);
            }
            if (person.getTopGroupId() != null && person.getTopGroupId().intValue() != 0) {
                tmpstr = sqlhelper.ExecuteScalar("select uComapanyQC from GroupItem where id=" + person.getTopGroupId());
                person.setTopGroupNameS(tmpstr);
            }

            //获取角色
            tmpstr = sqlhelper.ExecuteScalarList("select RoleId from RolePerson where PersonId=" + person.getId());
            person.setRoleIdsString(tmpstr);

            tmpstr = sqlhelper.ExecuteScalarList("select RoleName from role where id in(select RoleId from RolePerson where PersonId=" + person.getId() + ")");
            person.setRoleNamesString(tmpstr);
            person.setRoleList(new ArrayList<String>(Arrays.asList(tmpstr.split(","))));

            tmpstr = sqlhelper.ExecuteScalarList("select distinct PermissionNo from RolePermission where RoleId in(select RoleId from RolePerson where PersonId=" + person.getId() + ")");
            person.setRolePermissionString(tmpstr);
            person.setPermissionList(new ArrayList<String>(Arrays.asList(tmpstr.split(","))));

            return person;
        }catch(Exception Ex)
        {
            return null;
        }
    }


    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static PersonEntity GetPersonByUserId(int uid)
    {
        SqlHelper sqlhelper = new SqlHelper();
        PersonEntity person = null;

        try {
            String strsql = "select * from vPerson where Id='" + uid + "'";

            //获取一条用户数据（Reader格式），并转换为用户实体类型
            vPerson vp = sqlhelper.GetObject(vPerson.class, strsql);
            person = new PersonEntity();
            person.setId(vp.getId());
            person.setLoginName(vp.getLoginName());
            person.setRealName(vp.getRealName());
            person.setPassword(vp.getPassword());
            person.setGroupId(vp.getGroupId());
            person.setGroupName(vp.getGroupName());
            person.setParentGroupId(vp.getParentId());
            person.setParentGroupName(vp.getParentName());
            //person.setGroupType(vp.getGroupType());
            person.setTelephone(vp.getTelephone());
            person.setOA(vp.getOA());
            person.setMsgType(vp.getMsgType());

            strsql = "select * from person where id="+vp.getId();
            Person p = sqlhelper.GetObject(Person.class, strsql);
            person.setType(p.getType());
            person.setSphone(p.getSphone());
            person.setPhName(p.getPhName());
            person.setCertificateID(p.getCertificateID());
            person.setOfficePhone(p.getOfficePhone());
            person.setState(p.getState());
            person.setRoleId(p.getRoleId());

            if(person.getRoleId()!=0&&person.getRoleId()!=null)
            {
                strsql="select * from role where id="+person.getRoleId();
                Role role=sqlhelper.GetObject(Role.class,strsql);
                if(role!=null)
                {
                    person.setAdminGroupIds(role.getAdminGroupIds());
                    person.setRoleName(role.getRoleName());
                }
            }

            if(person.getGroupId()!=0&&person.getGroupId()!=null)
            {
                strsql="select * from GroupItem where id="+person.getGroupId();
                GroupItem groupItem=sqlhelper.GetObject(GroupItem.class,strsql);
                if(groupItem!=null)
                {
                    person.setUComapanyBH(groupItem.getUComapanyBH());
                    person.setUComapanyJC(groupItem.getUComapanyJC());
                    person.setUComapanyQC(groupItem.getUComapanyQC());
                }
            }

            String tmpstr = sqlhelper.ExecuteScalar("select TopGroupId from vGroupItem where id=" + person.getGroupId());
            if (!StringUtil.IsNullOrEmpty(tmpstr)) {
                person.setTopGroupId(Integer.parseInt(tmpstr));

                tmpstr = sqlhelper.ExecuteScalar("select GroupName from GroupItem where id=" + person.getGroupId());
                if (!StringUtil.IsNullOrEmpty(tmpstr)) {
                    person.setTopGroupName(tmpstr);
                }
            }

            tmpstr = sqlhelper.ExecuteScalar("select uComapanyQC from GroupItem where id=" + person.getGroupId());
            person.setGroupNameS(tmpstr);
            if (person.getParentGroupId() != null && person.getParentGroupId().intValue() != 0) {
                tmpstr = sqlhelper.ExecuteScalar("select uComapanyQC from GroupItem where id=" + person.getParentGroupId());
                person.setParentGroupNameS(tmpstr);
            }
            if (person.getTopGroupId() != null && person.getTopGroupId().intValue() != 0) {
                tmpstr = sqlhelper.ExecuteScalar("select uComapanyQC from GroupItem where id=" + person.getTopGroupId());
                person.setTopGroupNameS(tmpstr);
            }

            //获取角色
            tmpstr = sqlhelper.ExecuteScalarList("select RoleId from RolePerson where PersonId=" + person.getId());
            person.setRoleIdsString(tmpstr);

            tmpstr = sqlhelper.ExecuteScalarList("select RoleName from role where id in(select RoleId from RolePerson where PersonId=" + person.getId() + ")");
            person.setRoleNamesString(tmpstr);
            person.setRoleList(new ArrayList<String>(Arrays.asList(tmpstr.split(","))));

            tmpstr = sqlhelper.ExecuteScalarList("select distinct PermissionNo from RolePermission where RoleId in(select RoleId from RolePerson where PersonId=" + person.getId() + ")");
            person.setRolePermissionString(tmpstr);
            person.setPermissionList(new ArrayList<String>(Arrays.asList(tmpstr.split(","))));

            return person;
        }catch (Exception Ex) {
            return  null;
        }
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static vPerson GetVPersonByLoginName_S(String loginName)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            String strsql = "select * from vPerson where LoginName='" + loginName + "'";
            vPerson vp = sqlhelper.GetObject(vPerson.class, strsql);
            return vp;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static Person GetPersonByLoginName_S(String loginName)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            String strsql = "select * from Person where LoginName='" + loginName + "'";
            Person vp = sqlhelper.GetObject(Person.class, strsql);
            return vp;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static vPerson GetVPersonByUserId_S(int uid)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            String strsql = "select * from vPerson where Id='" + uid + "'";
            vPerson vp = sqlhelper.GetObject(vPerson.class, strsql);
            return vp;
        }catch (Exception Ex) {
            return  null;
        }
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static Person GetPersonByUserId_S(int uid)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            String strsql = "select * from Person where Id='" + uid + "'";
            Person vp = sqlhelper.GetObject(Person.class, strsql);
            return vp;
        }catch (Exception Ex) {
            return  null;
        }
    }

    public static String getPersonAdminGroup(int pid)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            String strsql = "select uComapanyBH from GroupItem where id in(select fk_Group_id from Person_Group where fk_person_id=" + pid + ")";
            String vp = sqlhelper.ExecuteScalarList(strsql);
            return vp;
        }catch (Exception Ex) {
            return  "";
        }
    }

    public static Boolean AddApplicationLog(String usename,String func,String module,String detail)
    {
        try {
            ApplicationLog alog=new ApplicationLog();
            alog.setGuid(Guid.GUID.newGuid().toGuidString());
            alog.setOperateDetail(detail);
            alog.setOperateDate(DateUtil.toLocalDateTime());
            alog.setOperateFunction(func);
            alog.setOperateModule(module);
            alog.setOperatorName(usename);
            AddApplicationLog_Inner(alog);

            return true;
        }catch (Exception Ex) {
            return  false;
        }
    }

    public static Boolean AddApplicationLog_Inner(ApplicationLog alog)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            //提交数据库
            String strsql = DBHelper.GetInsertSQL(alog,"ApplicationLog", Arrays.asList());
            if (StringUtil.IsNullOrEmpty(strsql))
            {
                return false;
            }
            sqlhelper.ExecuteScalar(strsql);

            return true;
        }catch (Exception Ex) {
            return  false;
        }
    }

    public static Boolean AddApplicationLogYW(String usename,String func,String module,String detail)
    {
        try {
            ApplicationLog alog=new ApplicationLog();
            alog.setGuid(Guid.GUID.newGuid().toGuidString());
            alog.setOperateDetail(detail);
            alog.setOperateDate(DateUtil.toLocalDateTime());
            alog.setOperateFunction(func);
            alog.setOperateModule(module);
            alog.setOperatorName(usename);
            AddApplicationLogYW_inner(alog);

            return true;
        }catch (Exception Ex) {
            return  false;
        }
    }

    public static Boolean AddApplicationLogYW_inner(ApplicationLog alog)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try {
            //提交数据库
            String strsql = DBHelper.GetInsertSQL(alog,"ApplicationLog_YW", Arrays.asList());
            if (StringUtil.IsNullOrEmpty(strsql))
            {
                return false;
            }
            sqlhelper.ExecuteScalar(strsql);

            return true;
        }catch (Exception Ex) {
            return  false;
        }
    }

    public static void setValue(Field f,String coltype,String valtype, Object colvalue, Object row) {
        if (colvalue != null && !"".equals(colvalue)) {
            try {
                if ("String".equals(coltype)||"String".equals(coltype)) {
                    f.set(row,colvalue.toString());
                }else if ("Short".equals(coltype)||"short".equals(coltype)) {
                    f.set(row,Short.parseShort(colvalue.toString()));
                } else if ("int".equals(coltype) || "Integer".equals(coltype)) {
                    f.set(row,Integer.parseInt(colvalue.toString()));
                } else if ("double".equals(coltype) || "Double".equals(coltype)) {
                    f.set(row,Double.parseDouble(colvalue.toString()));
                } else if ("float".equals(coltype) || "Float".equals(coltype)) {
                    f.set(row,Float.parseFloat(colvalue.toString()));
                } else if ("long".equals(coltype) || "Long".equals(coltype)) {
                    f.set(row,Long.parseLong(colvalue.toString()));
                } else if ("Boolean".equals(coltype) || "Boolean".equals(coltype)) {
                    f.set(row,Boolean.parseBoolean(colvalue.toString()));
                } else if ("BigDecimal".equals(coltype)) {
                    f.set(row,new BigDecimal("" + colvalue));
                } else if ("Date".equals(coltype)|| "date".equals(coltype)) {
                    f.set(row, DateUtils.parseDate(colvalue.toString()));
                }else if ("TimeStamp".equals(coltype)|| "Timestamp".equals(coltype)) {
                    f.set(row, DateUtil.parseTimestamp(colvalue.toString(),"yyyy-MM-dd HH:mm:ss"));
                }else if("LocalDateTime".equals(coltype))
                {
                    if("TimeStamp".equals(valtype)|| "Timestamp".equals(valtype))
                    {
                        Timestamp time=(Timestamp)colvalue;
                        LocalDateTime ldt=time.toLocalDateTime();
                        f.set(row, ldt);
                    }else
                    {
                        f.set(row, LocalDateTime.parse(colvalue.toString()));
                    }
                }else if("LocalDate".equals(coltype))
                {
                    f.set(row, LocalDate.parse(colvalue.toString()));
                }else {
                    f.set(row, colvalue);
                }
            } catch (Exception ex) {
                LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            }
        }
    }

    public static void SendSms(String phone, String content) {
        SqlHelper sqlhelper=new SqlHelper();
        String strsql="";
        try
        {
            strsql = "insert into T_SMSSEND ([Source],[Phone],[DateTime],[Sequence],[Result],[Info]) select 'AQGL','" + phone + "',getdate(),isnull(max(sequence),0)+1,0,'" + content + "' From [T_SMSSend] where Source='AQGL' and Phone='" + phone + "' and [DateTime]=getdate()";
            int cont = sqlhelper.ExecuteNoQuery(strsql);
        }catch (Exception Ex)
        {
            return;
        }
    }

    public static int ExecuteSql(String strsql) {
        if(StringUtil.IsNullOrEmpty(strsql)) {
            return -1;
        }

        SqlHelper sqlhelper=new SqlHelper();
        int cont =-1;
        try
        {
            cont = sqlhelper.ExecuteNoQuery(strsql);
            return cont;
        }catch (Exception Ex)
        {
            return cont;
        }
    }

    public static DataTable GetChildFuntionItemByParent(int userid, int pwf,String gid)
    {
        try
        {
            String strsql = "select id as res_id, title as res_name,url as res_url,'' as img_url,ParentId as parent_id from FunctionItem where id in (select id from func_usr_func(" + userid + ","+pwf+"))";
            strsql += " and parentid='" + gid + "' and id not like '%APP%' order by OrderNumber";

            SqlHelper sqlhelper=new SqlHelper();
            DataTable tmpdt = sqlhelper.GetDataTable(strsql);
            return tmpdt;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    //调用邱雄用户名、密码验证端口，返回结果   mflag 参数，mflag=false 不进行在线测试
    public static qx_yzm get_yzmMessage(String loginName, String mobile,String userip, String useragent)
    {
        try
        {
            if (!ConfigHelper.getRunMode().equals("test"))
            {
                //**************
                String contentUrl = "https://ydyfwzx.yongyaokjit.com:8282/auth/oauth/api/verification-code";
                //String contentUrl = "https://ydyfwzx-slb.yongyaokjit.com:8282/auth/oauth/api/verification-code";

                CloseableHttpClient httpClient = HttpClientBuilder.create().build();

                HttpPost httpPost = new HttpPost(contentUrl);
                httpPost.setHeader("ContentType","application/x-www-form-urlencoded");
                httpPost.setHeader("Authorization", "Basic NTBjY2E0Y2EtOTQwNy00NWRhLWFiMGQtYTdjMDNlMDk2OTkyOkQzMFJJKk16X2tu");

                List<NameValuePair> parameters=new ArrayList<NameValuePair>();
                parameters.add(new BasicNameValuePair("mobile", mobile));
                parameters.add(new BasicNameValuePair("username", loginName));
                parameters.add(new BasicNameValuePair("userip", userip));
                parameters.add(new BasicNameValuePair("useragent", useragent));
                httpPost.setEntity(new UrlEncodedFormEntity(parameters,"UTF-8"));

                HttpResponse httpResponse = httpClient.execute(httpPost);

                // 5、获取响应结果, 状态码 200 表示请求成功
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    HttpEntity httpEntity = httpResponse.getEntity();
                    // 使用指定的字符编码解析响应消息实体
                    String feedback = EntityUtils.toString(httpEntity, "UTF-8");
                    if(feedback!=null){
                        logger.info(feedback);
                    }

                    JSONObject jsond=JSON.parseObject(feedback);
                    if(jsond!=null) {
                        int contentd2 = jsond.getInteger("error");
                        String contentd1 = jsond.getString("message");
                        String contentd = jsond.getString("data");

                        qx_yzm entity = new qx_yzm();
                        if(contentd!=null){
                            entity.setData(contentd.replaceAll("\"", ""));
                        }
                        entity.setError(contentd2);
                        if(contentd1!=null){
                            entity.setMessage(contentd1.replaceAll("\"", ""));
                        }

                        return entity;
                    }else{
                        return null;
                    }
                }else
                {
                    qx_yzm entity = new qx_yzm();
                    entity.setMessage("");
                    entity.setError(1);
                    entity.setData("");
                    return entity;
                }
            }
            else
            {
                qx_yzm entity = new qx_yzm();
                entity.setMessage("");
                entity.setError(1);
                entity.setData("");
                return entity;
            }
        }
        catch(Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            return null;
        }

    }

    //调用邱雄短信验证码登录认证
    public static qx_yzm get_yzmLoginMessage(String loginName, String mobile, String code, String userip, String useragent)
    {
        try
        {
            if (!ConfigHelper.getRunMode().equals("test"))
            {
                //**************
                String contentUrl = "https://ydyfwzx.yongyaokjit.com:8282/auth/oauth/api/verification-code-check";
                //String contentUrl = "https://ydyfwzx-slb.yongyaokjit.com:8282/auth/oauth/api/verification-code";

                CloseableHttpClient httpClient = HttpClientBuilder.create().build();

                HttpPost httpPost = new HttpPost(contentUrl);
                httpPost.setHeader("ContentType","application/x-www-form-urlencoded");
                httpPost.setHeader("Authorization", "Basic NTBjY2E0Y2EtOTQwNy00NWRhLWFiMGQtYTdjMDNlMDk2OTkyOkQzMFJJKk16X2tu");

                List<NameValuePair> parameters=new ArrayList<NameValuePair>();
                parameters.add(new BasicNameValuePair("mobile", mobile));
                parameters.add(new BasicNameValuePair("username", loginName));
                parameters.add(new BasicNameValuePair("code", code));
                parameters.add(new BasicNameValuePair("userip", userip));
                parameters.add(new BasicNameValuePair("useragent", useragent));
                httpPost.setEntity(new UrlEncodedFormEntity(parameters,"UTF-8"));

                HttpResponse httpResponse = httpClient.execute(httpPost);

                // 5、获取响应结果, 状态码 200 表示请求成功
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    HttpEntity httpEntity = httpResponse.getEntity();
                    // 使用指定的字符编码解析响应消息实体
                    String feedback = EntityUtils.toString(httpEntity, "UTF-8");
                    if(feedback!=null){
                        logger.info(feedback);
                    }

                    JSONObject jsond=JSON.parseObject(feedback);
                    if(jsond!=null) {
                        int contentd2 = jsond.getInteger("error");
                        String contentd1 = jsond.getString("message");
                        String contentd = jsond.getString("data");

                        qx_yzm entity = new qx_yzm();
                        if(contentd!=null){
                            entity.setData(contentd.replaceAll("\"", ""));
                        }
                        entity.setError(contentd2);
                        if(contentd1!=null){
                            entity.setMessage(contentd1.replaceAll("\"", ""));
                        }

                        return entity;
                    }else{
                        return null;
                    }
                }else
                {
                    qx_yzm entity = new qx_yzm();
                    entity.setMessage("");
                    entity.setError(1);
                    entity.setData("");
                    return entity;
                }
            }
           else
            {
                qx_yzm entity = new qx_yzm();
                entity.setMessage("");
                entity.setError(1);
                entity.setData("");
                return entity;
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteSysLog(MetaHelper.GetTrace(ex));
            return null;
        }

    }
}
