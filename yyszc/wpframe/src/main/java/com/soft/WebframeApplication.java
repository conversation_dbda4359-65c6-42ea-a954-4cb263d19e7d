package com.soft;

import com.alibaba.fastjson.util.TypeUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@MapperScan({"com.soft.gcc.**.mapper"})
public class WebframeApplication extends SpringBootServletInitializer implements CommandLineRunner {
    private static final Logger log = LoggerFactory.getLogger(WebframeApplication.class);

    public static void main(String[] args) {
        TypeUtils.compatibleWithJavaBean = true;
        SpringApplication.run(WebframeApplication.class, args);
        log.info("App start success!");
        log.info(":) Thanks buddy, we can make a lot together!");
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        TypeUtils.compatibleWithJavaBean = true;
        return application.sources(WebframeApplication.class);
    }

    public void run(String... args) throws Exception {

    }
}
