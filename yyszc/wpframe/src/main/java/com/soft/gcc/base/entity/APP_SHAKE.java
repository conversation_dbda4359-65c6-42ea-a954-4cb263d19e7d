package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * APP握手信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("APP_SHAKE")
@ApiModel(value="APP_SHAKE对象", description="APP握手信息表")
public class APP_SHAKE extends Model<APP_SHAKE> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编码")
    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Long ID;

    @ApiModelProperty(value = "电话号码")
    @JSONField(name="PHONE")
    private String PHONE;

    @ApiModelProperty(value = "用户TOKEN")
    @JSONField(name="TOKEN")
    private String TOKEN;

    @ApiModelProperty(value = "内部用户人员ID")
    @JSONField(name="UID")
    private Integer UID;

    @ApiModelProperty(value = "授予时间")
    @JSONField(name="SHDT")
    private String SHDT;

    @ApiModelProperty(value = "过期时间")
    @JSONField(name="EXPIRE")
    private String EXPIRE;

    @ApiModelProperty(value = "状态(0:当前，1:历史)")
    @JSONField(name="STATE")
    private Integer STATE;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
