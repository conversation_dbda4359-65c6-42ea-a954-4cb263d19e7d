package com.soft.gcc.base.ventity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vModule")
@ApiModel(value="vModule对象", description="")
public class vModule extends Model<vModule> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模块序号(用来排序)")
    @JSONField(name="module_num")
    private String module_num;

    @ApiModelProperty(value = "模块名称")
    @JSONField(name="module_name")
    private String module_name;

    @ApiModelProperty(value = "模块类型(1内部，2外部)")
    @JSONField(name="module_type")
    private Integer module_type;

    @ApiModelProperty(value = "是否显示（1显示，0不显示）")
    @JSONField(name="is_show")
    private Integer is_show;

    @ApiModelProperty(value = "PC端跳转地址，戚德芳用")
    @JSONField(name="url")
    private String url;

    @ApiModelProperty(value = "app的url地址，胡鹏飞用")
    @JSONField(name="app_url")
    private String app_url;

    @ApiModelProperty(value = "扩展字段1")
    @JSONField(name="define_f")
    private String define_f;

    @ApiModelProperty(value = "扩展字段2")
    @JSONField(name="define_s")
    private String define_s;

    @JSONField(name="ID")
    private Integer ID;

    @ApiModelProperty(value = "PC端跳转地址，戚德芳用")
    @JSONField(name="purl")
    private String purl;

    @ApiModelProperty(value = "混肴字串")
    @JSONField(name="module_mix")
    private String module_mix;

    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
