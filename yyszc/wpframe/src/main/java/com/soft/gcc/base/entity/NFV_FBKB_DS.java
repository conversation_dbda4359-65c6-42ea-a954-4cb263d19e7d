package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFV_FBKB_DS")
@ApiModel(value="NFV_FBKB_DS对象", description="")
public class NFV_FBKB_DS extends Model<NFV_FBKB_DS> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Group_Id")
    private Integer Group_Id;

    @JSONField(name="Fbht_AvgDay")
    private Integer Fbht_AvgDay;

    @JSONField(name="Group_Name")
    private String Group_Name;

    @JSONField(name="Fbjs_AvgDay")
    private Integer Fbjs_AvgDay;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
