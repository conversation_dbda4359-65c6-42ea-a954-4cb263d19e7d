package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("T_UserData")
@ApiModel(value="T_UserData对象", description="")
public class T_UserData extends Model<T_UserData> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "集体企业编号")
    @JSONField(name="uComapanyBH")
    private String uComapanyBH;

    @ApiModelProperty(value = "组织机构前8位")
    @JSONField(name="zzCode")
    private String zzCode;

    @ApiModelProperty(value = "PersonID")
    @JSONField(name="PID")
    private Integer PID;

    @ApiModelProperty(value = "单位名称")
    @JSONField(name="unit")
    private String unit;

    @ApiModelProperty(value = "入围联系人	")
    @JSONField(name="ucontactname")
    private String ucontactname;

    @ApiModelProperty(value = "身份证	")
    @JSONField(name="ucontactid")
    private String ucontactid;

    @ApiModelProperty(value = "电话")
    @JSONField(name="ucontactphone")
    private String ucontactphone;

    @ApiModelProperty(value = "虚拟号")
    @JSONField(name="ucontactvirtual")
    private String ucontactvirtual;

    @ApiModelProperty(value = "年	")
    @JSONField(name="uyearreg")
    private String uyearreg;

    @ApiModelProperty(value = "创建日期")
    @JSONField(name="ucreationtime")
    private LocalDateTime ucreationtime;

    @ApiModelProperty(value = "登录时间")
    @JSONField(name="ulandingtime")
    private LocalDateTime ulandingtime;

    @ApiModelProperty(value = "登录数")
    @JSONField(name="uloginnumber")
    private Integer uloginnumber;

    @ApiModelProperty(value = "登录IP1")
    @JSONField(name="uloginIP1")
    private String uloginIP1;

    @ApiModelProperty(value = "登录IP2")
    @JSONField(name="uloginIP2")
    private String uloginIP2;

    @ApiModelProperty(value = "登录IP3	")
    @JSONField(name="uloginIP3")
    private String uloginIP3;

    @ApiModelProperty(value = "状态	")
    @JSONField(name="state")
    private Integer state;

    @ApiModelProperty(value = "作业人数")
    @JSONField(name="unum")
    private Integer unum;

    @ApiModelProperty(value = "验证码	")
    @JSONField(name="strYzm")
    private String strYzm;

    @ApiModelProperty(value = "验证码生成时间")
    @JSONField(name="DateYzm")
    private LocalDateTime DateYzm;

    @ApiModelProperty(value = "是否从事高试")
    @JSONField(name="ISGS")
    private Integer ISGS;

    @ApiModelProperty(value = "是否从事电缆")
    @JSONField(name="ISDL")
    private Integer ISDL;

    @ApiModelProperty(value = "变动人数")
    @JSONField(name="changeNum")
    private Integer changeNum;

    @ApiModelProperty(value = "一天上传的最大流量")
    @JSONField(name="MaxLL")
    private Float MaxLL;

    @ApiModelProperty(value = "是否主账号（1、是，0、否）")
    @JSONField(name="IsMain")
    private Integer IsMain;

    @JSONField(name="uArea")
    private String uArea;

    @JSONField(name="DRNum")
    private Integer DRNum;

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name="id")
    private Integer id;

    @JSONField(name="FZRcertificateID")
    private String FZRcertificateID;

    @JSONField(name="utype")
    private String utype;

    @JSONField(name="FZRName")
    private String FZRName;

    @JSONField(name="IsUpload")
    private String IsUpload;

    @JSONField(name="IsOld")
    private Integer IsOld;

    @JSONField(name="zIntegval")
    private Integer zIntegval;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
