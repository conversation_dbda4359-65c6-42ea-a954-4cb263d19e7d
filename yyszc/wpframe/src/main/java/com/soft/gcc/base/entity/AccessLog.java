package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("AccessLog")
@ApiModel(value="AccessLog对象", description="")
public class AccessLog extends Model<AccessLog> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="UserAgent")
    private String UserAgent;

    @JSONField(name="AccessPage")
    private String AccessPage;

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Long Id;

    @JSONField(name="RecordDate")
    private LocalDateTime RecordDate;

    @JSONField(name="IP")
    private String IP;


    @Override
    protected Serializable pkVal() {
        return this.Id;
    }

}
