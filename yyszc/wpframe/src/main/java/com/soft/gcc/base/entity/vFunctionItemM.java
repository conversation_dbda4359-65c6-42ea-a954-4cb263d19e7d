package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vFunctionItemM")
@ApiModel(value="vFunctionItemM对象", description="")
public class vFunctionItemM extends Model<vFunctionItemM> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="Title")
    private String Title;

    @JSONField(name="app_icon")
    private String app_icon;

    @JSONField(name="module_type")
    private Integer module_type;

    @JSONField(name="module_url")
    private String module_url;

    @JSONField(name="ParentName")
    private String ParentName;

    @JSONField(name="app_module")
    private Integer app_module;

    @JSONField(name="OrderNumber")
    private Integer OrderNumber;

    @JSONField(name="DisplayName")
    private String DisplayName;

    @JSONField(name="module_name")
    private String module_name;

    @JSONField(name="module_ID")
    private Integer module_ID;

    @JSONField(name="app_type")
    private Integer app_type;

    @JSONField(name="IsPublic")
    private Integer IsPublic;

    @JSONField(name="Url")
    private String Url;

    @JSONField(name="ParentId")
    private String ParentId;

    @JSONField(name="Parameter")
    private Integer Parameter;

    @JSONField(name="app_url")
    private String app_url;

    @JSONField(name="Id")
    private String Id;

    @JSONField(name="module_mix")
    private String module_mix;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
