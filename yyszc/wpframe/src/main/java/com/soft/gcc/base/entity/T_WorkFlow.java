package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("T_WorkFlow")
@ApiModel(value="T_WorkFlow对象", description="")
public class T_WorkFlow extends Model<T_WorkFlow> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "退回阶段ID")
    @JSONField(name="backid")
    private Integer backid;

    @ApiModelProperty(value = "0:已注册,1:待审核，2：申报中，3：初审中，4：复审中，5：合格，6：不合格")
    @JSONField(name="state")
    private Integer state;

    @ApiModelProperty(value = "T_UserData中ID")
    @JSONField(name="userID")
    private Integer userID;

    @ApiModelProperty(value = "初审结果")
    @JSONField(name="FirstResult")
    private Integer FirstResult;

    @ApiModelProperty(value = "初审时间")
    @JSONField(name="FirstExamineDate")
    private LocalDateTime FirstExamineDate;

    @ApiModelProperty(value = "初审意见")
    @JSONField(name="FirstExamine")
    private String FirstExamine;

    @ApiModelProperty(value = "初审人")
    @JSONField(name="FirstExaminePerson")
    private String FirstExaminePerson;

    @ApiModelProperty(value = "复审结果")
    @JSONField(name="SecondResult")
    private Integer SecondResult;

    @ApiModelProperty(value = "复审时间")
    @JSONField(name="SecondExamineDate")
    private LocalDateTime SecondExamineDate;

    @ApiModelProperty(value = "不合格理由")
    @JSONField(name="SecondExamine")
    private String SecondExamine;

    @ApiModelProperty(value = "复审人")
    @JSONField(name="SecondExaminePerson")
    private String SecondExaminePerson;

    @ApiModelProperty(value = "曾经合格 0 不是 1是")
    @JSONField(name="IsHg")
    private Integer IsHg;

    @ApiModelProperty(value = "申报提交时间")
    @JSONField(name="SubmitDate")
    private LocalDateTime SubmitDate;

    @JSONField(name="ZJ3Examine")
    private String ZJ3Examine;

    @JSONField(name="ZJ4Examine")
    private String ZJ4Examine;

    @JSONField(name="ZJ3ExamineDate")
    private LocalDateTime ZJ3ExamineDate;

    @JSONField(name="ZJ1Name")
    private String ZJ1Name;

    @JSONField(name="XYS_LookTime")
    private LocalDateTime XYS_LookTime;

    @JSONField(name="ZJ2ExamineDate")
    private LocalDateTime ZJ2ExamineDate;

    @JSONField(name="ZJ1Examine")
    private String ZJ1Examine;

    @JSONField(name="ZJ2Examine")
    private String ZJ2Examine;

    @JSONField(name="FKPZ_Photo")
    private Integer FKPZ_Photo;

    @JSONField(name="XYS_Photo")
    private Integer XYS_Photo;

    @JSONField(name="ZJ1NameID")
    private Integer ZJ1NameID;

    @JSONField(name="ZJ4NameID")
    private Integer ZJ4NameID;

    @JSONField(name="ZJ2NameID")
    private Integer ZJ2NameID;

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name="ID")
    private Integer ID;

    @JSONField(name="ZJ3NameID")
    private Integer ZJ3NameID;

    @JSONField(name="ZJ3Name")
    private String ZJ3Name;

    @JSONField(name="ZJ2Name")
    private String ZJ2Name;

    @JSONField(name="ZJ1ExamineDate")
    private LocalDateTime ZJ1ExamineDate;

    @JSONField(name="ZJ4ExamineDate")
    private LocalDateTime ZJ4ExamineDate;

    @JSONField(name="ZJ4Name")
    private String ZJ4Name;


    @Override
    protected Serializable pkVal() {
        return this.ID;
    }

}
