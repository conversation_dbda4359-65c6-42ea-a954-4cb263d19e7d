package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 导航信息视图
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFV_NavInfo")
@ApiModel(value="NFV_NavInfo对象", description="导航信息视图")
public class NFV_NavInfo extends Model<NFV_NavInfo> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="NavId")
    private Integer NavId;

    @JSONField(name="NavType")
    private Integer NavType;

    @JSONField(name="NavDisplay")
    private String NavDisplay;

    @JSONField(name="NavState")
    private Integer NavState;

    @JSONField(name="NavImg")
    private String NavImg;

    @JSONField(name="NavTypeN")
    private String NavTypeN;

    @JSONField(name="NavName")
    private String NavName;

    @JSONField(name="NavOrder")
    private Integer NavOrder;

    @JSONField(name="NavUrl")
    private String NavUrl;

    @JSONField(name="NavStateN")
    private String NavStateN;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
