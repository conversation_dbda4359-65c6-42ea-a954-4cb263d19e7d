package com.soft.gcc.base.service.impl;

import com.soft.gcc.base.entity.vFunctionItemM;
import com.soft.gcc.base.mapper.vFunctionItemMMapper;
import com.soft.gcc.base.service.IvFunctionItemMService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * vFunctionItemM 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Service
public class vFunctionItemMServiceImpl extends ServiceImpl<vFunctionItemMMapper, vFunctionItemM> implements IvFunctionItemMService {

}
