# 项目相关配置
jplat:
  # 名称
  name: wpframe
  #文件分隔符
  separator: /
  # 版本
  version: 1.0.0
  # 实例演示开关
  demoEnabled: true
  # 文件路径 本机资源不能用户前台展示，不映射Upload路径
  profile: d:/profile/wpframe
  # 获取ip地址开关
  addressEnabled: true
  # Session保留时间长度
  SessionTimeOut: 30
  #ExtJs 版本
  ExtVersion: 3.2
  #是否记录日志
  LogFlag: 1
  #是否记录SQL日志
  SqlTg: 1
  #系统mark
  SystemMark: wpframe
  #是否加载数据库配置
  LoadDBConf: 0
  #短信总开关
  SmsFlag: 1
  #通知是否开启短信通知
  NoticeSmsFlag: 1
  #判断系统是否能手动输入身份证
  IsOpen: 1
  # 文件路径 联机资源，可用于前台展示下载等，测试时走本地，发布时统一走平台路径
  OssLocalPath: d:/profile/wpframe/upload/APP/
  #OSS远程路径
  OssRemotePath: Upload/APP/
  #判断系统test或其他
  RunMode: dev    #test/dev/prod
  #Security,excludes 要求token可以访问系统
  WhiteList: /Service/User/Login,/Service/User/GetToken,/Service/User/InitScanImage,/Service/User/GetScanImage,/Service/User/CheckScanResult,/Service/User/PostYzm,/WpService/**/
  #web根地址
  WebRoot: https://www.y-dashi.com:4435
  #net框架地址
  NetFrame: http://127.0.0.1/netframe
  #web框架地址
  WpFrame: http://127.0.0.1/wpframe

#是否开启swagger文档接口
mconfig:
  swagger-ui-open: true

#阿里云OSS配置
oconfig:
#  endPoint: http://oss-cn-ningbo-yongdian-d01-a.ops.yongdian-cloud.com/
#  accessKeyId: msCRKcKWU6anQKne
#  accessKeySecret: ******************************
#  bucketName: shuziyongyao
  endPoint: http://oss-cn-shanghai.aliyuncs.com/
  accessKeyId: LTAI5tCCPPvtGriTPpNw1wbp
  accessKeySecret: ******************************
  bucketName: shuziyongyao
  #开启标志
  openFlag: true

# 开发环境配置
server:
  port: 58010
  servlet:
    context-path: /wpframe
  tomcat:
    uri-encoding: UTF-8
    max-threads: 800
    min-spare-threads: 30

# 日志配置
logging:
  config:
    classpath: log4j2.xml

# Spring配置
spring:
  application:
    name: wpframe
  mvc:
    static-path-pattern: /**
  resources:
    static-locations: classpath:/static/,file:/static/
  http:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      # 单个文件的最大值
      max-file-size:  100MB
      # 上传文件总的最大值
      max-request-size:  200MB
  redis:
    host: **************
    port: 6379
    database: 6
    # 密码
    password: davice@252
    timeout: 10000
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 100
        max-wait: -1
      shutdown-timeout: 100
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  jmx:
    enable: false
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      master:
        url: ***********************************************************************************************************
        username: topdavice1
        password: topdavice@---
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 2000
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      query-timeout: 300
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      remove-abandoned: true
      remove-abandoned-timeout: 180
      log-abandoned: true
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        caffeine:
          spec: initialCapacity=500,expireAfterWrite=5s
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      eager: true
      enabled: true
      scg:
        fallback:
          mode: response
          response-status: 455
          response-body: The system is busy, please try again later!

#mybatis-plus 配置
mybatis-plus:
  mapper-locations: classpath*:com/**/xml/*.xml
  Configuration:
    mapUnderscoreToCamelCase: false
    cacheEnabled: false
  GlobalConfig:
    banner: false

management:
  endpoints:
    web:
      exposure:
        include: 'health,info' #暴露资源
        exclude:   #排除资源

token:
  # 令牌自定义标识
  header: Authorization
  # 令牌秘钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
