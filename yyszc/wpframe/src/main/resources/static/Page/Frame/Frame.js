function GetJSONObject(str) {
    if (str) {
        return Ext.util.JSON.decode(str);
    }
    else {
        return null;
    }
}

function GetJSONString(obj) {
    if (obj) {
        return Ext.util.JSON.encode(obj);
    }
    else {
        return null;
    }
}


function SetFormReadOnlyColor(form) {
    function setItemsReadOnlyColor(items) {
        items.each(function (item) {
            var childitems = item.items;
            if (!Ext.isEmpty(childitems, false)) {
                setItemsReadOnlyColor(childitems);
            }
            else {
                if (item.xtype != "button" && item.xtype != "gridview") {
                    if (item.readOnly) {
                        item.getEl().dom.style.background = ("rgb(230, 230, 230)");
                    }
                    else {
                        if (item.getEl().dom.style.background == "rgb(230, 230, 230)") {
                            item.getEl().dom.style.background = null;
                        }
                    }
                }
            }
        });
    }
    setItemsReadOnlyColor(form.items);
}

function isNumber(str) {
    if (parseFloat(str).toString() == "NaN") {
        return false;
    }
    else {
        return true;
    }
}


function OnDownloadJSSuccess() { }
function OnDownloadJSFailure() { }
var downloadtext = "";
function DownloadJS(url) {
    var ss = document.getElementsByTagName("script");
    for (var i = 0; i < ss.length; i++) {
        if (ss[i].id && ss[i].id.indexOf(url) != -1) {
            OnDownloadJSSuccess();
            return;
        }
    }
    var s = document.createElement("script");
    s.id = url;
    s.type = "text/javascript";
    s.src = url;
    var head = document.getElementsByTagName("head")[0];
    head.appendChild(s);

    s.onload = s.onreadystatechange = function () {
        if (this.readyState && this.readyState == "loading") return;
        downloadtext = downloadtext + "|" + url;
        OnDownloadJSSuccess();
    };

    s.onerror = function () {
        head.removeChild(s);
        OnDownloadJSFailure();
    };

}

function DownloadJSByParams(url, funSucc) {
    var ss = document.getElementsByTagName("script");
    for (var i = 0; i < ss.length; i++) {
        if (ss[i].id && ss[i].id.indexOf(url) != -1) {
            if (funSucc) funSucc();
            return;
        }
    }
    var s = document.createElement("script");
    s.id = url;
    s.type = "text/javascript";
    s.src = url;
    var head = document.getElementsByTagName("head")[0];
    head.appendChild(s);

    s.onload = s.onreadystatechange = function () {
        if (this.readyState && this.readyState == "loading") return;
        downloadtext = downloadtext + "|" + url;
        if (funSucc) funSucc();
    };

    s.onerror = function () {
        head.removeChild(s);
    };
}

function ajaxPage(sId, url) {
    var oXmlHttp = GetHttpRequest();
    oXmlHttp.onreadystatechange = function () {
        if (oXmlHttp.readyState == 4) {
            includeJS(sId, url, oXmlHttp.responseText);
        }
    }
    oXmlHttp.open('GET', url, false);
    //同步操作
    oXmlHttp.send(null);
}

function GetHttpRequest() {
    if (window.XMLHttpRequest)// Gecko
        return new XMLHttpRequest();
    else if (window.ActiveXObject)// IE
        return new ActiveXObject("MsXml2.XmlHttp");
}

function includeJS(sId, fileUrl, source) {
    if ((source != null) && (!document.getElementById(sId))) {
        var oHead = document.getElementsByTagName('HEAD').item(0);
        var oScript = document.createElement("script");
        oScript.type = "text/javascript";
        oScript.id = sId;
        oScript.text = source;
        oHead.appendChild(oScript);
    }
}

function SafeOpenFunction(win,fid,furl,ftitle)
{
    if(typeof(eval('win.NavFunction'))=="function")
    {
        win.NavFunction(furl,fid,ftitle);
    }
}

function OpenMoudleWindow(id,url,fid,furl,ftitle) {
    var urlstr ="";
    var loadflag=false;
    if (url.indexOf("?") > 0) {
        if (url.indexOf("?xml=true") > 0) {
            url = url.replace("?xml=true", "");
            urlstr = url + "?mid="+id+"&sm="+_sysmark;
        }else
        {
            urlstr = url + "&mt=m&mid=" + id+"&sm="+_sysmark;
        }
    }else
    {
        urlstr = url + "?mt=m&mid=" + id+"&sm="+_sysmark;
    }

    //alert(urlstr);
    if(fid!=undefined&&furl!=undefined&&ftitle!=undefined)
    {
        var cwfunctmp = cwMap0Func.get(id);
        if (cwfunctmp!=null&&cwfunctmp!=undefined) {
            cwMap0Func.delete(id);
            cwMap0Func.set(id, {fid: fid, furl: furl, ftitle: ftitle});
        } else {
            cwMap0Func.set(id, {fid: fid, furl: furl, ftitle: ftitle});
        }
    }

    var h = window.screen.availHeight;
    var w = window.screen.availWidth;

    var cwtmp = cwMap0.get(id);
    if (cwtmp == undefined || cwtmp==null) {
        cwtmp = window.open('about:blank');
        cwtmp.location = urlstr;
        cwMap0.set(id, cwtmp);
        loadflag=false;
    } else {
        if (cwtmp.closed) {
            cwtmp = window.open('about:blank');
            cwtmp.location = urlstr;
            cwMap0.set(id, cwtmp);
            loadflag=false;
        } else {
            cwtmp.focus();
            loadflag=true;
        }
    }

    if(cwtmp!=undefined&&fid!=undefined&&furl!=undefined&&ftitle!=undefined)
    {
        if(loadflag==true){
            SafeOpenFunction(cwtmp,fid,furl,ftitle);
        }
    }

    //ShowModalWin(urlstr, "", w, h);
}


function OpenFullWindow(funcid, url) {
    var urlstr = url;
    var h = window.screen.availHeight;
    var w = window.screen.availWidth;
    var cwtmp = cwMap0.get(funcid);
    if (cwtmp == undefined || cwtmp == null) {
        cwtmp = window.open('about:blank');
        cwtmp.location = urlstr;
        cwMap0.set(funcid, cwtmp);
    } else {
        if (cwtmp.closed) {
            cwtmp = window.open('about:blank');
            cwtmp.location = urlstr;
            cwMap0.set(funcid, cwtmp);
        } else {
            cwtmp.focus();
        }
    }
    //ShowModalWin(urlstr, "", w, h);
}

Ext.onReady(function () {
    Ext.QuickTips.init();

    Ext.apply(Ext.QuickTips.getQuickTip(), {
        maxWidth: 500,
        minWidth: 200,
        showDelay: 50,
        hideDelay: 10,
        trackMouse: false
    });

    window.addEventListener('message', function (event) {
        console.log(event.source == this.window);
        var jsonData = event.data;
        if(jsonData!=undefined&&jsonData!=null) {
            var mid=jsonData.mid;
            if (mid != "" && mid != undefined) {
                var cwt = cwMap0.get(mid);
                var cwf = cwMap0Func.get(mid);
                if (cwt != undefined && cwt != null && cwf != undefined && cwf != null) {
                    SafeOpenFunction(cwt, cwf.fid, cwf.furl, cwf.ftitle);
                }
            }
        }
    });
});

function StrTrim(str) {
    if (str == undefined) {
        return str;
    }
    else {
        return str.toString().replace(/(^\s*)|(\s*$)/g, "");
    }
}

window.onbeforeunload=function(){
    cwMap0.forEach(function (cwtmp, key, mapObj) {
        if (!cwtmp.closed)
        {
            cwtmp.close();
        }
    });
}

function RegisterDBFunc(dbf) {
    if (dbflist.indexOf(dbf) < 0) {
        dbflist.push(dbf);
    }
}

function OpenDBLCWindow(lcid, ywid, url) {
    SchID = ywid;
    SchlcID = lcid;
    functionPara = 0;
    if (url != "" && url != undefined) {
        DownloadJSByParams(url, function () {
            var sindex = url.lastIndexOf("\/");
            var vindex = url.lastIndexOf(".js");
            var funchead = url.substring(0, sindex);
            var functail = url.substring(sindex + 1, vindex);

            var sindex0 = funchead.lastIndexOf("\/");
            var functail0 = funchead.substring(sindex0 + 1);

            var funcp = functail0 + "_" + functail;
            if (dbflist.indexOf(funcp) >= 0) {
                var funcparm = functail + (Math.trunc(Math.random() * 1000000)).toString();
                eval("new " + funcp + "('" + funcparm + "')");
            }
        });
    }
}

function getCookie(c_name) {
    if (document.cookie.length > 0) {
        c_start = document.cookie.indexOf(c_name + "=")
        if (c_start != -1) {
            c_start = c_start + c_name.length + 1
            c_end = document.cookie.indexOf(";", c_start)
            if (c_end == -1) c_end = document.cookie.length
            return unescape(document.cookie.substring(c_start, c_end))
        }
    }
    return ""
}

function setCookie(c_name, value, expiredays) {
    var exdate = new Date()
    exdate.setDate(exdate.getDate() + expiredays)
    document.cookie = c_name + "=" + escape(value) +
        ((expiredays == null) ? "" : ";path=/;expires=" + exdate.toGMTString())
}

function saveNetFrameCookie(tokens) {
    setCookie("access_token", tokens, 365);
}