<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1">
    <title>数字化平台</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/ExtResources/css/ext-all.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/lovcombo/css/Ext.ux.form.LovCombo.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/Grid.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/Page.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/Loading.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/file-upload.css"/>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/animated-dataview.css"/>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/LockingGridView.css"/>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/data-view.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/ext_custom.css" />
    <link rel="stylesheet" type="text/css" href="../../Page/CPSoft/CustomUI.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/DateTimeField/css/Spinner.css"/>
	<script type="text/javascript" src="../../JSControl/ExtJs/ext-base.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/ext-all.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/ext-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/CheckColumn.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/ColumnHeaderGroup.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/PagingMemoryProxy.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/ExtDateTime.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/LockingGridView.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/DateTimeField/Spinner.js"></script>
	<script type="text/javascript" src="../../JSControl/ExtJs/DateTimeField/SpinnerField.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/DateTimeField/DateTimeField.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/lovcombo/LovCombo.js"></script>
	<script type="text/javascript" src="../../JS/md5.js"></script>
    <script type="text/javascript" src="../../JS/aes.js"></script>
    <script type="text/javascript" src="../../JS/CommJs.js"></script>
    <script src="../../JSControl/JQuery/jquery-3.6.0.min.js" type="text/javascript"></script>
    <script type="text/javascript">var $j = $;</script>
    <script type="text/javascript" src="../../JSControl/JQuery/jquery-migrate-1.4.1.min.js"></script>
    <script type="text/javascript">
        var vrandom = Math.random();
        var jspath = "../../Page/CPSoft/common.js?v=" + vrandom;
        document.write('<script src="' + jspath + '"><\/script>');
    </script>
    <script type="text/javascript">
        jspath = "../../Page/CPSoft/CommonFunc.js?v=" + vrandom;
        document.write('<script src="' + jspath + '"><\/script>');
    </script>
    <script type="text/javascript">
        var cwMap0 = new Map();
        var cwMap0Func = new Map();
        var dbflist = new Array();
        var _sysmark='wpframe';
        var _cps_js_version=Math.random();

        $(function(){
            var ifm = document.getElementById("mainframe");
            ifm.height = document.documentElement.clientHeight;
        });

        window.onresize=function(){
            var ifm = document.getElementById("mainframe");
            ifm.height = document.documentElement.clientHeight;
        }

        function ExecExtTest() {
            var Test_FormWindow = new Ext.Window({
                width: 560,
                height: 400,
                closeAction: 'hide',
                plain: true,
                modal: true,
                layout: "fit",
                resizable: false,
                autoScroll: true,
                title: '表单',
                maximized :true,
                buttonAlign: 'center',
                buttons: [
                    {
                        text: '保存',
                        height: 30,
                        disabled: true
                    },
                    {
                        text: '关闭',
                        height: 30,
                        handler: function () {
                            Test_FormWindow.hide();
                        }
                    }
                ]
            });

            Test_FormWindow.show();
        }
    </script>
    <script type="text/javascript" src="Frame.js"></script>
</head>
<body>
    <!--<iframe id="mainframe" width="100%" height="720" frameborder="0" scrolling="no" name="mainframe" src="http://*************:81/"></iframe>-->
    <iframe id="mainframe" width="100%" height="100%" frameborder="0" scrolling="no" name="mainframe" src="../../TStub02.html"></iframe>
</body>
</html>

