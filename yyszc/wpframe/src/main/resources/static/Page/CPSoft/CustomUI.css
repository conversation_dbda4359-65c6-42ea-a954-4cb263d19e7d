.x-form-textfield-disabled {
    background-color: #EEEEEE; 
    background-image: none;
}

.my_row_gray table {
   background-color: #EAEAEA;
} 

.x-grid-row-back-gray{
    background-color:#DCDCDC;
}

.x-grid-row-back-red{
    background-color: #dd7870;
}

.x-grid-rebeccapurple{
    background-color: lightsalmon;
}

.x-grid-row-back-goldenrod{
    background-color: bisque;
}

.x-grid-row-back-green{
   background-color:#F0FFF0;
}

.x-grid-row-back-blue{
   background-color:#B0E0E6;
}

.x-grid3-wz-row-selected .x-grid3-cell-inner {
    color: #FF0099;
    text-decoration:underline;
}

.x-grid-image-custom-height{
    height:40px !important;
    line-height:40px !important;
    vertical-align:center !important;
}

.x-grid-progress {
    overflow: hidden;
    height: 20px;
    margin: 5px;
    background-color: #f7f7f7;
    background-image: -moz-linear-gradient(top, #f5f5f5, #f9f9f9);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f5f5f5), to(#f9f9f9));
    background-image: -webkit-linear-gradient(top, #f5f5f5, #f9f9f9);
    background-image: -o-linear-gradient(top, #f5f5f5, #f9f9f9);
    background-image: linear-gradient(to bottom, #f5f5f5, #f9f9f9);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff5f5f5', endColorstr='#fff9f9f9', GradientType=0);
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    width:200px;
}

.x-grid-progress .per {
    width: 100%;
    height: 100%;
    margin-top:5px;
    margin-bottom:5px;
    float: left;
    font-size: 12px;
    text-align: center;
    background: transparent;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    color:#000000;
}

.x-grid-progress .bar {
    width: 0%;
    height: 100%;
    background-color: #0e90d2;
    background-image: -moz-linear-gradient(left, #149bdf, seagreen);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#149bdf), to(seagreen));
    background-image: -webkit-linear-gradient(left, #149bdf, seagreen);
    background-image: -o-linear-gradient(left, #149bdf, seagreen);
    background-image: linear-gradient(left, #149bdf, seagreen);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff149bdf', endColorstr='seagreen', GradientType=1);
    -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transition: width 0.6s ease;
    -moz-transition: width 0.6s ease;
    -o-transition: width 0.6s ease;
    transition: width 0.6s ease;
}

.x-grid-card {table-layout:fixed;border:1px #ccc solid;border-width:0px 1px 1px 0px;}
.x-grid-card tr{height:40px;}
.x-grid-card td input{zoom:140%;}
.x-grid-card tr td img{max-width:100%;max-height:260px;cursor:pointer;}
.x-grid-card tr td{padding: 5px 10px;vertical-align:middle;font-size:13px;line-height:20px;border:1px #ccc solid;border-width:1px 0px 0px 1px;word-wrap:break-word;}
.x-grid-card tr td a{line-height:20px;}
.x-grid-card tr td span{color:#666;}

.x-grid-card-editpic{position: relative;display:inline-block;}
.x-grid-card-editpic b{cursor:pointer; position: absolute;bottom:0px;width:100%;height:25px;display:block;background:rgba(101,134,116,.7);text-align:right;color:#fff;font-weight:normal;line-height:25px;}
.x-grid-card-editpic b img{vertical-align: middle;margin-right:3px;margin-top:-2px;}

.x-form-my-trigger{
    background-image: url(../../JSControl/ExtJs/ExtResources/images/default/form/search-trigger.gif) !important;
}

.tree-fold-R{
    background-image: url(../../Image/tree_fist_r.png) !important;
}
.tree-fold{
    background-image: url(../../Image/tree_fist.png) !important;
}
.tree-file-R{
    background-image: url(../../Image/tree_file_r.png) !important;
}
.tree-file{
    background-image: url(../../Image/tree_file.png) !important;
}
.tree-file-N{
    background-image: url(../../Image/upfalse.png) !important;
}
.tree-file-NR{
    background-image: url(../../Image/upfalse_r.png) !important;
}

.x-grid-column-btn{
    background: -webkit-linear-gradient(whitesmoke,#f39a00);
    background: -o-linear-gradient(whitesmoke,#f39a00);
    background: -moz-linear-gradient(whitesmoke,#f39a00);
    background: -mos-linear-gradient(whitesmoke,#f39a00);
    background: linear-gradient(whitesmoke,#f39a00);
    border:0px;
    font-size: 9pt;
    color: #1a1a1c;
}

.x-grid-column-btn:hover{
    background: -webkit-linear-gradient(whitesmoke,#f39a00);
    background: -o-linear-gradient(whitesmoke,#f39a00);
    background: -moz-linear-gradient(whitesmoke,#f39a00);
    background: -mos-linear-gradient(whitesmoke,#f39a00);
    background: linear-gradient(whitesmoke,#f39a00);
    border:1px solid #0e90d2;
    font-size: 9pt;
    color: #1a1a1c;
}

.imageview-panel-body{
    background: white;
    font: 11px Arial, Helvetica, sans-serif;
}
.imageview-thumb{
    background: #dddddd;
    padding: 3px;
}
.imageview-thumb img{
    height: 60px;
    width: 80px;
}
.imageview-thumb-wrap{
    float: left;
    margin: 4px;
    margin-right: 0;
    padding: 5px;
}

.imageview-thumb-wrap span{
    display: block;
    overflow: hidden;
    text-align: center;
}

.imageview-view-over{
    border:1px solid #dddddd;
    background: #efefef url(../../JSControl/ExtJs/ExtResources/images/default/grid/row-over.gif) repeat-x left top;
    padding: 4px;
}

.imageview-view-selected{
    background: #eff5fb url(../../Image/selected.gif) no-repeat right bottom;
    border:1px solid #99bbe8;
    padding: 4px;
}
.imageview-view-selected .thumb{
    background:transparent;
}

.imageview-mark
{
    font-size:9px !important;
}
.imageview-mark-hint
{
    font-size:9px !important;
    color: red !important;
}

.bg_imageview-panel-body{
    background: white;
    font: 11px Arial, Helvetica, sans-serif;
}

.bg_imageview-thumb{
    background: transparent;
    padding:10px 20px 10px 20px;
    border:0px solid red;
}

.bg_imageview-thumb img{
    width: 160px;
    height: 180px;
}

.bg_imageview-thumb-wrap{
    width: 200px;
    float: left;
    height: auto;
    margin: 20px;
    margin-bottom:10px;
    min-height: 160px;
    border:1px solid #9cbfee;
    padding:4px;
}

.bg_imageview-title{
    width:160px;
    margin:5px;
    margin-left:20px;
    margin-right:20px;
    display: block;
    text-align: center;
    white-space:nowrap;
    background: transparent;
    overflow:hidden;
    text-overflow:ellipsis;
}

.bg_imageview-hander{
    background-color: #fffff6;
}

.bg_imageview-hander table{
    margin:5px;
    padding:5px;
    font-size:10pt;
}

.bg_imageview-hander a{
    font-size:9pt;
    color:blue;
}

.bg_imageview-hander a:hover{
    font-size:9pt;
    color:blue;
    text-decoration: underline;
}

.bg_imageview-view-over{
    border:1px solid #dddddd;
    background: #efefef url(../../JSControl/ExtJs/ExtResources/images/default/grid/row-over.gif) repeat-x left top;
    padding:4px;
}

.bg_imageview-view-selected{
    background: #eff5fb url(../../Image/selected.gif) no-repeat right bottom;
    border:1px solid #99bbe8;
    padding:4px;
}

.bg_imageview-view-selected .thumb{
    background:transparent;
}

.bg_imageview-mark
{
    font-size:9px !important;
}
.bg_imageview-mark-hint
{
    font-size:9px !important;
    color: red !important;
}

.vbg_imageview-panel-body{
    background: white;
    font: 11px Arial, Helvetica, sans-serif;
}

.vbg_imageview-thumb{
    background: transparent;
    padding:10px 20px 10px 20px;
    border:0px solid red;
}

.vbg_imageview-thumb img{
    width: 200px;
    height: 360px;
}

.vbg_imageview-thumb-wrap{
    width: 240px;
    float: left;
    height: auto;
    margin: 20px;
    margin-bottom:10px;
    min-height: 360px;
    border:1px solid #9cbfee;
    padding:4px;
}

.vbg_imageview-view-over{
    border:1px solid #dddddd;
    background: #efefef url(../../JSControl/ExtJs/ExtResources/images/default/grid/row-over.gif) repeat-x left top;
    padding:4px;
}

.vbg_imageview-view-selected{
    background: #eff5fb url(../../Image/selected.gif) no-repeat right bottom;
    border:1px solid #99bbe8;
    padding:4px;
}

.vbg_imageview-view-selected .thumb{
    background:transparent;
}

.little-imageview-thumb{
    height: 60px !important;
    width: 60px !important;
}

.x-form-field-wrap .x-form-color-trigger {
    background:transparent url(../../JSControl/ExtJs/ExtResources/images/default/form/trigger.gif) no-repeat 0 0;
    cursor:pointer;
}

.x-selectable, .x-selectable * {
    user-select: text! important;
    -o-user-select: text! important;
    -moz-user-select: text! important;
    -khtml-user-select: text! important;
    -webkit-user-select: text! important;
}