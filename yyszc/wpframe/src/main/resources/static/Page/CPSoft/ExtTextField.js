Ext.ux.form.WMTextField = Ext.extend(Ext.form.TextField, {
    initComponent: function () {
        Ext.form.TextField.superclass.initComponent.call(this);
        this.addEvents('autosize', 'keydown', 'keyup', 'keypress', 'dblclick');
    },
    initEvents: function () {
        Ext.form.TextField.superclass.initEvents.call(this);
        if (this.validationEvent == 'keyup') {
            this.validationTask = new Ext.util.DelayedTask(this.validate, this);
            this.mon(this.el, 'keyup', this.filterValidation, this);
        } else if (this.validationEvent !== false) {
            this.mon(this.el, this.validationEvent, this.validate, this, {
                buffer: this.validationDelay
            });
        }
        if (this.selectOnFocus || this.emptyText) {
            this.on('focus', this.preFocus, this);

            this.mon(this.el, 'mousedown', function () {
                if (!this.hasFocus) {
                    this.el.on('mouseup', function (e) {
                        e.preventDefault();
                    }, this, {
                        single: true
                    });
                }
            }, this);

            if (this.emptyText) {
                this.on('blur', this.postBlur, this);
                this.applyEmptyText();
            }
        }
        if (this.maskRe || (this.vtype && this.disableKeyFilter !== true && (this.maskRe = Ext.form.VTypes[this.vtype + 'Mask']))) {
            this.mon(this.el, 'keypress', this.filterKeys, this);
        }
        if (this.grow) {
            this.mon(this.el, 'keyup', this.onKeyUpBuffered, this, {
                buffer: 50
            });
            this.mon(this.el, 'click', this.autoSize, this);
        }
        if (this.enableKeyEvents) {
            this.mon(this.el, 'keyup', this.onKeyUp, this);
            this.mon(this.el, 'keydown', this.onKeyDown, this);
            this.mon(this.el, 'keypress', this.onKeyPress, this);
        }
        this.mon(this.el, 'dblclick', this.onDblClick, this);
    },
    onDblClick: function (e) {
        this.fireEvent('dblclick', this, e);
    }
});

Ext.reg('wmtextfield', Ext.ux.form.WMTextField);