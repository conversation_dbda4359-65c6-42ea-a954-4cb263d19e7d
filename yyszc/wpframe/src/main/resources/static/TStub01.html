<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1">
    <title>数字化微服务平台-测试页面</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <script src="./JSControl/JQuery/jquery-3.6.0.min.js" type="text/javascript"></script>
    <script src="./JSControl/JQuery/jquery-migrate-1.4.1.min.js" type="text/javascript"></script>
    <style type="text/css">
        body{font-family:微软雅黑;}
        table{border:0px;}
        tr{border:0px;}
        td{border:0px;}
        img{border:0px;}
        a{ color:lightseagreen;text-decoration-line: none;}
        @media screen and (min-width: 800px) and (max-width: 1300px)
        {
            html{ font-size: 11px;}
        }
        @media screen and (min-width: 1301px) and (max-width: 1400px)
        {
            html{ font-size: 12px;}
        }
        @media screen and (min-width: 1401px) and (max-width: 1500px)
        {
            html{ font-size: 13px;}
        }
        @media screen and (min-width: 1501px) and (max-width: 1600px)
        {
            html{ font-size: 14px;}
        }
        @media screen and (min-width: 1601px) and (max-width: 1700px)
        {
            html{ font-size: 15px;}
        }
        @media screen and (min-width: 1701px) and (max-width: 1800px)
        {
            html{ font-size: 16px;}
        }
        @media screen and (min-width: 1801px) and (max-width: 1900px)
        {
            html{ font-size: 17px;}
        }
        @media screen and (min-width: 1901px) and (max-width: 2000px)
        {
            html{ font-size: 18px;}
        }
        @media screen and (min-width: 2001px) and (max-width: 2100px)
        {
            html{ font-size: 19px;}
        }
        @media screen and (min-width: 2101px) and (max-width: 2200px)
        {
            html{ font-size: 20px;}
        }
        @media screen and (min-width: 2201px)
        {
            html{ font-size: 21px;}
        }

        .handpanel{
            width:calc(40vw);
            margin-top:calc(20vh);
            background-color: azure;
            background-image: -moz-linear-gradient(top, #ebf3fd, #c7d9f7);
            background-image: -webkit-linear-gradient(top, #ebf3fd, #c7d9f7);
            background-image: -o-linear-gradient(top, #ebf3fd, #c7d9f7);
            background-image: linear-gradient(to bottom, #ebf3fd, #c7d9f7);
        }

        .handbtn{
            border-radius:0.5rem;
            border:0px;
            height:2.8rem;
            width:8rem;
            background-color:#2656D2;
            cursor: pointer;
            font-family: 微软雅黑;
            color:white;
            font-size: 1.1rem
        }
    </style>
    <script type="text/javascript">
        var vrandom = Math.random();
        var LoginPerson = null;
        var LoginToken = null;
        var cpsVersion='1.00';
        var _sysmark='wpframe';
        var cwMap0 = new Map();
        var cwMap0Func = new Map();

        function Login() {
            $("#btnLogIn").attr("disabled", "disabled");
            var loginName = document.getElementById("txtLoginName").value;
            var wcode = document.getElementById("txtWCode").value;

            $.ajax({
                timeout: 0,
                url: './Service/User/Login',
                type: 'post',
                processData: true,
                async: false,
                data: {"LoginName":loginName,"code":wcode},
                cache: false,
                dataType: 'json',
                contentType:'application/x-www-form-urlencoded',
                success: function (robj) {
                    if (robj.success == true) {
                        LoginPerson = robj.person;
                        cpsVersion = robj.cpsversion;
                        loginToken = robj.token;

                        var storage = window.sessionStorage;
                        if(storage!=undefined)
                        {
                            storage.setItem(_sysmark+"_xsystem","true");
                            storage.setItem(_sysmark+"_token",loginToken);
                            storage.setItem(_sysmark+"_cpsversion",cpsVersion);
                        }

                        RefreshView();
                        GetCurrentPerson();
                    }
                },
                error: function (req, textStatus, errorThrown) {
                    Alert("登录失败!");
                }
            });
        }

        function GetCurrentPerson() {
            $.ajax({
                timeout: 0,
                url: './Service/User/GetCurrentPerson',
                type: 'post',
                processData: true,
                async: false,
                cache: false,
                dataType: 'json',
                headers: {
                    Authorization: loginToken
                },
                contentType:'application/x-www-form-urlencoded',
                success: function (robj) {
                    if (robj.success!="true"&&robj.success != true) {
                        Alert("获取用户信息失败！");
                    }
                },
                error: function (req, textStatus, errorThrown) {
                    Alert("登录失败!");
                }
            });
        }

        function LogOut() {
            $.ajax({
                timeout: 0,
                url: './Service/User/LogOut',
                type: 'post',
                dataType: 'json',
                processData: true,
                async: false,
                cache: false,
                contentType:false,
                headers: {
                    Authorization: loginToken
                },
                success: function (robj) {
                    if (robj.success == true||robj.success == "true") {
                        LoginPerson = null;
                        cpsVersion = "";
                        loginToken = "";

                        var storage = window.sessionStorage;
                        if(storage!=undefined)
                        {
                            storage.setItem(_sysmark+"_xsystem","true");
                            storage.setItem(_sysmark+"_token",loginToken);
                            storage.setItem(_sysmark+"_cpsversion",cpsVersion);
                        }

                        $("#txtLoginName").val();
                        $("#txtWCode").val();

                        RefreshView();
                    }
                },
                error: function (req, textStatus, errorThrown) {
                    Alert("登录失败!");
                }
            });
        }

        function enterkey() {
            if (event.keyCode == 13) {
                Login();
            }
        }

        $(function (){
            $('#btnLogIn').on('click', function () { Login(); });
            $('#btnGoTo').on('click', function () { GoToUrl(); });
            $('#btnGoToFun').on('click', function () { GoToFuncUrl(); });
            $('#btnLogOut').on('click', function () { LogOut(); });
            RefreshView();

            window.addEventListener('message', function (event) {
                console.log(event.source == this.window);
                var jsonData = event.data;
                if(jsonData!=undefined&&jsonData!=null) {
                    var mid=jsonData.mid;
                    if (mid != "" && mid != undefined) {
                        var cwt = cwMap0.get(mid);
                        var cwf = cwMap0Func.get(mid);
                        if (cwt != undefined && cwt != null && cwf != undefined && cwf != null) {
                            SafeOpenFunction(cwt, cwf.fid, cwf.furl, cwf.ftitle);
                        }
                    }
                }
            });
        });

        function RefreshView()
        {
            if(LoginPerson==null||LoginPerson==undefined)
            {
                $("#loginInfo").css("display","block");
                $("#loginStatus").css("display","none");
            }else
            {
                $("#loginInfo").css("display","none");
                $("#loginStatus").css("display","block");
            }
        }

        function SafeOpenFunction(win,fid,furl,ftitle)
        {
            if(typeof(eval('win.NavFunction'))=="function")
            {
                win.NavFunction(furl,fid,ftitle);
            }
        }

        function OpenMoudleWindow(id,url,fid,furl,ftitle) {
            var urlstr ="";
            var loadflag=false;

            if (url.indexOf("?") > 0) {
                if (url.indexOf("?xml=true") > 0) {
                    url = url.replace("?xml=true", "");
                    urlstr = url + "?mid="+id+"&sm="+_sysmark;
                }else
                {
                    urlstr = url + "&mt=m&mid=" + id+"&sm="+_sysmark;
                }
            }else
            {
                urlstr = url + "?mt=m&mid=" + id+"&sm="+_sysmark;
            }

            //alert(urlstr);
            if(fid!=undefined&&furl!=undefined&&ftitle!=undefined)
            {
                var cwfunctmp = cwMap0Func.get(id);
                if (cwfunctmp!=null&&cwfunctmp!=undefined) {
                    cwMap0Func.delete(id);
                    cwMap0Func.set(id, {fid: fid, furl: furl, ftitle: ftitle});
                } else {
                    cwMap0Func.set(id, {fid: fid, furl: furl, ftitle: ftitle});
                }
            }

            var h = window.screen.availHeight;
            var w = window.screen.availWidth;

            var cwtmp = cwMap0.get(id);
            if (cwtmp == undefined || cwtmp==null) {
                cwtmp = window.open('about:blank');
                cwtmp.location = urlstr;
                cwMap0.set(id, cwtmp);
                loadflag=false;
            } else {
                if (cwtmp.closed) {
                    cwtmp = window.open('about:blank');
                    cwtmp.location = urlstr;
                    cwMap0.set(id, cwtmp);
                    loadflag=false;
                } else {
                    cwtmp.focus();
                    loadflag=true;
                }
            }

            if(cwtmp!=undefined&&fid!=undefined&&furl!=undefined&&ftitle!=undefined)
            {
                if(loadflag==true){
                    SafeOpenFunction(cwtmp,fid,furl,ftitle);
                }
            }

            //ShowModalWin(urlstr, "", w, h);
        }



        function GoToUrl() {
            var Id=$("#txtToId").val();
            var url=$("#txtToUrl").val();
            if(url!=""&&url!=undefined)
            {
                url="../../"+url;
                OpenMoudleWindow(Id,url);
            }else{
                alert("请输入用于调试的调整模块地址！");
            }
        }
    </script>
</head>
<body id="body1" onkeydown="enterkey()" style="margin:0px; padding:0px; overflow:auto;width:calc(100vw); height: calc(100vh); background-color: aliceblue;">
    <center>
    <fieldset class="handpanel">
        <LEGEND>微服务平台测试平台</LEGEND>
        <div id="loginInfo">
            <table style="width:38rem; height:27rem; background-color:transparent;border-radius:0.5rem;" align="center" cellpadding="0" cellspacing="0" border="0">
                <tr style="height:0.1rem"><td align="center" style="border-bottom:0px solid grey;" colspan="3"></td></tr>
                <tr style="height:1rem;"><td align="center" colspan="3"></td></tr>
                <tr style="height:3rem;">
                    <td align="center" style="width:6rem;padding-left: 1rem;">登录账号：</td><td colspan="2"><input type="text" id="txtLoginName" name="txtLoginName" style="padding-left: 0.5rem; border:1px solid gray;border-radius:0.5rem; height:2rem; width:27rem;font-size: 1.2rem;"/></td>
                </tr>
                <tr style="height:3rem;">
                    <td align="center" style="width:6rem;padding-left: 1rem;">登录密码：</td>
                    <td colspan="2">
                        <input type="text" id="txtWCode" name="txtWCode" style="padding-left: 0.5rem; border:1px solid gray;border-radius:0.5rem;height:2rem;width:27rem;font-size: 1.2rem;"/>
                    </td>
                </tr>
                <tr style="height:2rem;vertical-align:middle;">
                    <td align="center" style="padding:0.5rem" colspan="3">
                        <input type="button" id="btnLogIn" name="btnLogIn" style="border-radius:0.5rem;border:0px; height:2.8rem;width:24rem; background-color:#2656D2; cursor: pointer; font-family: 微软雅黑;color:white;font-size: 1.1rem" value="立即登录"/>
                    </td>
                </tr>
                <tr style="height:1.5rem;vertical-align:top;">
                    <td align="center" style="padding-left: 1rem;padding-right: 1rem;" colspan="2">
                    </td>
                </tr>
            </table>
        </div>
        <div id="loginStatus" style="display:none;">
            <table style="width:38rem; height:27rem; background-color:transparent;border-radius:0.5rem;" align="center" cellpadding="0" cellspacing="0" border="0">
                <tr style="height:0.1rem"><td align="center" style="border-bottom:0px solid grey;" colspan="3"></td></tr>
                <tr style="height:1rem;"><td align="center" colspan="3"></td></tr>
                <tr style="height:3rem;">
                    <td align="center" style="width:6rem;padding-left: 1rem;">模块ID：</td>
                    <td colspan="2"><input type="text" id="txtToId" name="txtToId" style="padding-left: 0.5rem; border:1px solid gray;border-radius:0.5rem; height:2rem; width:30rem;font-size: 1.2rem;"/></td>
                </tr>
                <tr style="height:3rem;">
                    <td align="center" style="width:6rem;padding-left: 1rem;">模块地址：</td>
                    <td colspan="2"><input type="text" id="txtToUrl" name="txtToUrl" style="padding-left: 0.5rem; border:1px solid gray;border-radius:0.5rem; height:2rem; width:30rem;font-size: 1.2rem;"/></td>
                </tr>
                <tr style="height:2rem;vertical-align:middle;">
                    <td align="center" style="padding:0.5rem" colspan="3">
                        <input type="button" id="btnGoTo" name="btnGoTo" class="handbtn" value="跳转测试"/>
                        <input type="button" id="btnLogOut" name="btnLogOut" class="handbtn" value="返回登录"/>
                    </td>
                </tr>
                <tr style="height:1.5rem;vertical-align:top;">
                    <td align="center" style="padding-left: 1rem;padding-right: 1rem;" colspan="2">
                    </td>
                </tr>
            </table>
        </div>
    </fieldset>
    </center>
</body>
</html>
