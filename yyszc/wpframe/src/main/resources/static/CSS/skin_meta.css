.toolbar-tool-button
{ 
    padding-left:1px;
    padding-right:1px;
    padding-top:1px;
    padding-bottom:1px;
 } 
 .navbar-nav-bar
 {
    width:100%;
    height:32px;
 }    
.rpttoolbar-tool-button
{ 
    padding-left:1px;
    padding-right:1px;
    padding-top:1px;
    padding-bottom:1px;
}
.titlebar-tool-button {
    zoom:1.2;
}
.menubar-tool-button
{
    padding-left:1px;
    padding-right:1px;
    padding-top:1px;
    padding-bottom:1px;
}
.menubar-tool-button div
{
    background-repeat:no-repeat;
    -moz-background-size:100% 100%;
    -o-background-size:100% 100%;
    -webkit-background-size:100% 100%;
    background-size:100% 100%; 
}
.menubar-tool-button:hover
{
    filter:progid:DXImageTransform.Microsoft.Shadow(color=#3366ff,direction=120,strength=3);/*ie*/
    -moz-box-shadow: 2px 2px 10px #3366ff;/*firefox*/
    -webkit-box-shadow: 2px 2px 10px #3366ff;/*safari或chrome*/
    box-shadow:2px 2px 10px #3366ff;/*opera或ie9*/
}
.rpttoolbar-tool-button:hover
{
    filter:progid:DXImageTransform.Microsoft.Shadow(color=#3366ff,direction=120,strength=3);/*ie*/
    -moz-box-shadow: 2px 2px 10px #3366ff;/*firefox*/
    -webkit-box-shadow: 2px 2px 10px #3366ff;/*safari或chrome*/
    box-shadow:2px 2px 10px #3366ff;/*opera或ie9*/
}
.titlebar-tool-button:hover
{
    filter:progid:DXImageTransform.Microsoft.Shadow(color=#3366ff,direction=120,strength=3);/*ie*/
    -moz-box-shadow: 2px 2px 2px #3366ff;/*firefox*/
    -webkit-box-shadow: 2px 2px 2px #3366ff;/*safari或chrome*/
    box-shadow:2px 2px 2px #3366ff;/*opera或ie9*/
}
.toolbar-tool-button:hover
{
    filter:progid:DXImageTransform.Microsoft.Shadow(color=#3366ff,direction=120,strength=3);/*ie*/
    -moz-box-shadow: 2px 2px 10px #3366ff;/*firefox*/
    -webkit-box-shadow: 2px 2px 10px #3366ff;/*safari或chrome*/
    box-shadow:2px 2px 10px #3366ff;/*opera或ie9*/
}