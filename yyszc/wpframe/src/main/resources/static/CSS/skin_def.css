.def-titlebar-main-bar
{
    width:100%;
    height:30px;
    background:url(../Image/skin/def-titlebar-gb.gif) repeat-x;
}  
.def-titlebar-span-title
{
    float:left; 
    color:Navy; 
    height:20px; 
    width :500px; 
    padding-left:4px; 
    padding-top:6px; 
    font-family:华文行楷,楷体_GB2312;
    font-size:22px;
}  
.def-toolbar-tool-bar
{
    width:100%;
    height:26px;
    background:url(../Image/skin/def-toolbar-gb.gif) repeat-x;
}   
.def-toolbar-span-title
{
    float:left; 
    color:Purple; 
    height:20px; 
    width :500px; 
    font-size:9pt; 
    padding-left:10px; 
    padding-top:6px; 
    font-weight:normal; 
    font-family:新宋体;
}      
.def-navbar-condiv
{
    position:relative;
    display:block;
    height:32px;
    font-size:14px;
    font-weight:bold;
    background:transparent url(../Image/skin/def-navbar-gb.gif) repeat-x top left;
    font-family:楷体, 宋体;
    overflow:hidden;
 }
 .def-navbar-condiv ul
{
    margin:0px;
    padding:0;
    list-style-type:none;
    width:auto;
 }
.def-navbar-condiv ul li
{
    display:block;
    float:left;
    margin: 0 1px 0 0;
 }
.def-navbar-condiv ul li a
{
    display:block;
    float:left;
    color:#005891;
    text-decoration:none;
    padding:10px 10px 8px 10px;
 }
.def-navbar-condiv ul li a:hover
{
    color:#ffffff;
    background:transparent url(../Image/skin/def-navbar-over.gif) no-repeat top center;
}
.def-navbar-condiv ul li a.current
{
    color:#ffffff;
    background:transparent url(../Image/skin/def-navbar-over.gif) no-repeat top center;
}
.def-rpttoolbar-main-bar
{
    width:100%;
    height:30px;
    background:url(../Image/skin/def-rpttoolbar-gb.gif) repeat-x;
}   
.def-rpttoolbar-span-title
{
    float:left; 
    color:Navy; 
    height:20px; 
    width :500px; 
    padding-left:4px; 
    padding-top:6px; 
    font-family:华文行楷,楷体_GB2312;
    font-size:22px;
}
.def-menubar-tool-bar
{
    border:0px solid #4F60FD;
    font-size:12px;
    color:#3366ff;
    height:26px;
    background-image:url(../Image/skin/def-menubar-gb.gif);
}
.def-menubar-span-title
{
    float:left; 
    color:Navy; 
    width:100%; 
    font-size:10pt; 
    padding-left:4px; 
    font-family:华文行楷,楷体_GB2312;
}
.def-menubar-tool-bar-left
{
    width:13px;
    display:inline-block;
    background-image:url(../Image/skin/def-menubar-left.gif);
}
.def-menubar-tool-bar-right
{
    width:13px;
    display:inline-block;
    background-image:url(../Image/skin/def-menubar-right.gif);
}
.def-flipbar-main-bar
{
    background:url(../Image/skin/def-flipbar-gb.gif) repeat-x;
}
.def-menudrager-ul
{
    list-style-type:none;
    margin: 0;
    padding: 0;
    font-size:12px;
    font-family:新宋体,宋体;
}
.def-menudrager-ul ul{
    list-style-type:none;
    margin: 0;
    padding: 0;
    font-size:12px;
    font-family:新宋体,宋体;
}
.def-menudrager-ul a 
{
    display: block;
    text-decoration: none;	
}
.def-menudrager-ul li{
    margin-top: 1px;
}
.def-menudrager-ul li a{
    background: rgb(232, 240, 254);
    color: #000000;
    padding:0.6em;
}
.def-menudrager-ul li a:hover{
    background: #B5E2F9;
}    
.def-menudrager-ul li ul li a{ 
    background-color:#DBEAF1;
    border:0px solid red;
    color: #000;
    padding-left: 20px;
}
.def-menudrager-ul li ul li a:hover{
    background: #B5E2F9;
    border-left: 5px #328DBE solid;
    padding-left: 15px;
}
.def-menudrager-ul li ul li ul
{
    padding:0px;
    margin:0px;
    list-style-type:none;
    background-color:Transparent;
    color: #000;
    border: 1px #328DBE solid;
}
.def-menudrager-ul li ul li ul li a{
    background-color:#DBEAF1;
    border:0px; 
    color: #000;
    padding-left: 30px;
    margin:0px;
}
.def-menudrager-ul li ul li ul li a:hover{
    background: #B5E2F9;
    border-left: 5px #328DBE solid;
    padding-left: 25px;
}
.def-menudrager-innertable
{
    background: Transparent;
    border:0px solid red;
    padding:0px;
    margin:0px;
    font-size:12px;
    font-family:新宋体,宋体;
    width:150px;
    text-decoration:none;
}
.def-tool-search
{
    font-size:12px;
    background-color:#b7caf5;
    width:100%;
}
.def-skinbody
{ 
	font-size: 9pt; 
	margin :20px 10px 10px 10px; 
	background-color:Transparent; 
	background:Transparent url(../Image/skin/def-popbg.gif) repeat-x;
}

