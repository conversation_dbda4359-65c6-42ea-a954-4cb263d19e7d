/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-form-field {
    font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-form-text, textarea.x-form-field{
    color: #ffffff;
    background-color:#33373d;
    background-image:url(../images/access/form/text-bg.gif);
    border-color:#737b8c;
    border-width:2px;
}

.ext-webkit .x-form-text, .ext-webkit textarea.x-form-field{
    border-width:2px;
}

.x-form-text, .ext-ie .x-form-file {
    height:26px;
}

.ext-strict .x-form-text {
    height:20px;
}

.x-form-select-one {
    background-color:#fff;
    border-color:#b5b8c8;
}

.x-form-check-group-label {
    border-bottom: 1px solid #99bbe8;
    color: #fff;
}

.x-editor .x-form-check-wrap {
    background-color:#fff;
}

.x-form-field-wrap .x-form-trigger{
    background-image:url(../images/access/form/trigger.gif);
    border-bottom-color:#737b8c;
    border-bottom-width:2px;
    height:24px;
    width:20px;
}

.x-form-field-wrap .x-form-trigger.x-form-trigger-over{
    border-bottom-color:#d97e27;
}

.x-form-field-wrap .x-form-trigger.x-form-trigger-click{
    border-bottom-color:#c86e19;
}

.x-small-editor .x-form-field-wrap .x-form-trigger {
    height:24px;
}

.x-form-field-wrap .x-form-trigger-over {
    background-position:-20px 0;
}

.x-form-field-wrap .x-form-trigger-click {
    background-position:-40px 0;
}

.x-trigger-wrap-focus .x-form-trigger {
    background-position:-60px 0;
}

.x-trigger-wrap-focus .x-form-trigger-over {
    background-position:-80px 0;
}

.x-trigger-wrap-focus .x-form-trigger-click {
    background-position:-100px 0;
}

.x-form-field-wrap .x-form-date-trigger{
    background-image: url(../images/access/form/date-trigger.gif);
}

.x-form-field-wrap .x-form-clear-trigger{
    background-image: url(../images/access/form/clear-trigger.gif);
}

.x-form-field-wrap .x-form-search-trigger{
    background-image: url(../images/access/form/search-trigger.gif);
}

.x-trigger-wrap-focus .x-form-trigger{
    border-bottom-color:#737b8c;
}

.x-item-disabled .x-form-trigger-over{
    border-bottom-color:#b5b8c8;
}

.x-item-disabled .x-form-trigger-click{
    border-bottom-color:#b5b8c8;
}

.x-form-focus, textarea.x-form-focus{
	border-color:#ff9c33;
}

.x-form-invalid, textarea.x-form-invalid,
.ext-webkit .x-form-invalid, .ext-webkit textarea.x-form-invalid{
    background-color:#15171a;
	background-image:url(../images/access/grid/invalid_line.gif);
	border-color:#c30;
}

/*
.ext-safari .x-form-invalid{
	background-color:#fee;
	border-color:#ff7870;
}
*/

.x-form-inner-invalid, textarea.x-form-inner-invalid{
    background-color:#fff;
	background-image:url(../images/access/grid/invalid_line.gif);
}

.x-form-grow-sizer {
	font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-form-item {
    font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-form-invalid-msg {
    color:#c0272b;
    font:normal 14px tahoma, arial, helvetica, sans-serif;
    background-image:url(../images/default/shared/warning.gif);
}

.x-form-empty-field {
    color:#dadadd;
}

.x-small-editor .x-form-text {
    height: 26px;
}

.x-small-editor .x-form-field {
    font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.ext-safari .x-small-editor .x-form-field {
    font:normal 15px arial, tahoma, helvetica, sans-serif;
}

.x-form-invalid-icon {
    background-image:url(../images/access/form/exclamation.gif);
    height:25px;
    width:19px;
    background-position:center right;
}

.x-fieldset {
    border-color:#737B8C;
}

.x-fieldset legend {
    font:bold 14px tahoma, arial, helvetica, sans-serif;
    color:#fff;
}
