/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-menu {
	border: 1px solid #718bb7;
	z-index: 15000;
	zoom: 1;
	background: #f0f0f0 url(../images/default/menu/menu.gif) repeat-y;
	padding: 2px;
}
.x-menu a {
    text-decoration: none !important;
}
.ext-ie .x-menu {
    zoom:1;
    overflow:hidden;
}
.x-menu-list{
	background:transparent;
	border:0 none;
}
.x-menu li{
	line-height:100%;
}
.x-menu li.x-menu-sep-li{
	font-size:1px;
	line-height:1px;
}
.x-menu-list-item{
	font:normal 11px tahoma,arial, sans-serif;
    white-space: nowrap;
    -moz-user-select: none;
	-khtml-user-select: none;
	display:block;
	padding:1px;
}
.x-menu-item-arrow{
	background:transparent url(../images/default/menu/menu-parent.gif) no-repeat right;
}
.x-menu-sep {
	display:block;
	font-size:1px;
	line-height:1px;
	margin: 2px 3px;
    background-color:#e0e0e0;
	border-bottom:1px solid #fff;
    overflow:hidden;
}
.x-menu-focus {
	position:absolute;
	left:0;
	top:-5px;
	width:0;
	height:0;
    line-height:1px;
}
.x-menu a.x-menu-item {
	display:block;
	line-height:16px;
	padding:3px 21px 3px 3px;
	white-space: nowrap;
	text-decoration:none;
	color:#222;
	-moz-outline: 0 none;
	outline: 0 none;
	cursor:pointer;
}
.x-menu-item-active {
	background: #ebf3fd url(../images/default/menu/item-over.gif) repeat-x left bottom;
	border:1px solid #aaccf6;
	padding: 0;
}
.x-menu-item-active a.x-menu-item {
    color: #233d6d;
}

.x-menu-item-icon {
	border: 0 none;
	height: 16px;
	padding: 0;
	vertical-align: top;
	width: 16px;
	margin: 0 8px 0 0;
   background-position:center;
}

.x-menu-check-item .x-menu-item-icon{
	background: transparent url(../images/default/menu/unchecked.gif) no-repeat center;
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/default/menu/checked.gif);
}
.x-menu-group-item .x-menu-item-icon{
	background: transparent;
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background: transparent url(../images/default/menu/group-checked.gif) no-repeat center;
}

.x-menu-plain {
	background:#fff !important;
}
.x-menu-date-item{
	padding:0;
}

.x-menu .x-color-palette, .x-menu .x-date-picker{
    margin-left: 26px;
	 margin-right:4px;
}
.x-menu .x-date-picker{
    border:1px solid #a3bad9;
    margin-top:2px;
    margin-bottom:2px;
}
.x-menu-plain .x-color-palette, .x-menu-plain .x-date-picker{
	 margin: 0;
	 border: 0 none;
}
.x-date-menu {
   padding:0 !important;
}

.x-cycle-menu .x-menu-item-checked {
    border:1px dotted #a3bae9 !important;
    background:#DFE8F6;
	padding:0;
}
