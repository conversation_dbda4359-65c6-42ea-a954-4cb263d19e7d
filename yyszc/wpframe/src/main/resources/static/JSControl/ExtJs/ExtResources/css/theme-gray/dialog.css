/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-window-dlg .ext-mb-text,
.x-window-dlg .x-window-header-text {
    font-size:12px;
}

.x-window-dlg .ext-mb-textarea {
    font:normal 12px tahoma,arial,helvetica,sans-serif;
}

.x-window-dlg .x-msg-box-wait {
    background-image:url(../images/default/grid/loading.gif);
}

.x-window-dlg .ext-mb-info {
    background-image:url(../images/gray/window/icon-info.gif);
}

.x-window-dlg .ext-mb-warning {
    background-image:url(../images/gray/window/icon-warning.gif);
}

.x-window-dlg .ext-mb-question {
    background-image:url(../images/gray/window/icon-question.gif);
}

.x-window-dlg .ext-mb-error {
    background-image:url(../images/gray/window/icon-error.gif);
}
