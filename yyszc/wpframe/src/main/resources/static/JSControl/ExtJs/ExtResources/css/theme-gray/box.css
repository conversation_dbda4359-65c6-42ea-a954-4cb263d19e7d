/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-box-tl {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-tc {
	background-image: url(../images/default/box/tb.gif);
}

.x-box-tr {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-ml {
	background-image: url(../images/default/box/l.gif);
}

.x-box-mc {
	background-color: #eee;
    background-image: url(../images/default/box/tb.gif);
	font-family: "Myriad Pro","Myriad Web","Tahoma","Helvetica","Arial",sans-serif;
	color: #393939;
	font-size: 12px;
}

.x-box-mc h3 {
	font-size: 14px;
	font-weight: bold;
}

.x-box-mr {
	background-image: url(../images/default/box/r.gif);
}

.x-box-bl {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-bc {
	background-image: url(../images/default/box/tb.gif);
}

.x-box-br {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-blue .x-box-bl, .x-box-blue .x-box-br, .x-box-blue .x-box-tl, .x-box-blue .x-box-tr {
	background-image: url(../images/default/box/corners-blue.gif);
}

.x-box-blue .x-box-bc, .x-box-blue .x-box-mc, .x-box-blue .x-box-tc {
	background-image: url(../images/default/box/tb-blue.gif);
}

.x-box-blue .x-box-mc {
	background-color: #c3daf9;
}

.x-box-blue .x-box-mc h3 {
	color: #17385b;
}

.x-box-blue .x-box-ml {
	background-image: url(../images/default/box/l-blue.gif);
}

.x-box-blue .x-box-mr {
	background-image: url(../images/default/box/r-blue.gif);
}
