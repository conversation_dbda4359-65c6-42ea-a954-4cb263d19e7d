/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-form-field {
    font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-form-text, textarea.x-form-field {
    background-color:#fff;
    background-image:url(../images/default/form/text-bg.gif);
    border-color:#b5b8c8;
}

.x-form-select-one {
    background-color:#fff;
    border-color:#b5b8c8;
}

.x-form-check-group-label {
    border-bottom: 1px solid #99bbe8;
    color: #15428b;
}

.x-editor .x-form-check-wrap {
    background-color:#fff;
}

.x-form-field-wrap .x-form-trigger {
    background-image:url(../images/default/form/trigger.gif);
    border-bottom-color:#b5b8c8;
}

.x-form-field-wrap .x-form-date-trigger {
    background-image: url(../images/default/form/date-trigger.gif);
}

.x-form-field-wrap .x-form-clear-trigger {
    background-image: url(../images/default/form/clear-trigger.gif);
}

.x-form-field-wrap .x-form-search-trigger {
    background-image: url(../images/default/form/search-trigger.gif);
}

.x-trigger-wrap-focus .x-form-trigger {
    border-bottom-color:#7eadd9;
}

.x-item-disabled .x-form-trigger-over {
    border-bottom-color:#b5b8c8;
}

.x-item-disabled .x-form-trigger-click {
    border-bottom-color:#b5b8c8;
}

.x-form-focus, textarea.x-form-focus {
	border-color:#7eadd9;
}

.x-form-invalid, textarea.x-form-invalid {
    background-color:#fff;
	background-image:url(../images/default/grid/invalid_line.gif);
	border-color:#c30;
}

.x-form-invalid.x-form-composite {
    border: none;
    background-image: none;
}

.x-form-invalid.x-form-composite .x-form-invalid {
    background-color:#fff;
	background-image:url(../images/default/grid/invalid_line.gif);
	border-color:#c30;
}

.x-form-inner-invalid, textarea.x-form-inner-invalid {
    background-color:#fff;
	background-image:url(../images/default/grid/invalid_line.gif);
}

.x-form-grow-sizer {
	font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-form-item {
    font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-form-invalid-msg {
    color:#c0272b;
    font:normal 11px tahoma, arial, helvetica, sans-serif;
    background-image:url(../images/default/shared/warning.gif);
}

.x-form-empty-field {
    color:gray;
}

.x-small-editor .x-form-field {
    font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.ext-webkit .x-small-editor .x-form-field {
    font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.x-form-invalid-icon {
    background-image:url(../images/default/form/exclamation.gif);
}

.x-fieldset {
    border-color:#b5b8c8;
}

.x-fieldset legend {
    font:bold 11px tahoma, arial, helvetica, sans-serif;
    color:#15428b;
}
