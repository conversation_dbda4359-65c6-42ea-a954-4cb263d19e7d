.x-tab-strip-inner
{
    padding-right: 20px;
    padding-left: 20px;
    display: block;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    zoom: 1;
}

.x-tab-strip-active { 
    background-image: -webkit-linear-gradient(top, #fff, #f5f9fe 25%, #fddee3 45%);
}

td.x-grid3-hd-checker {
    display:none;
}
td.ux-grid-hd-group-cell {
    background: url(../ExtJs/ExtResources/images/default/grid/grid3-hrow.gif) repeat-x bottom;
}
.col-grid td {
    -webkit-box-sizing:border-box;
    -moz-box-sizing:border-box;
    box-sizing:border-box;
}