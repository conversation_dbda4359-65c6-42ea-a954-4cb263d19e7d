/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-menu {
	z-index: 15000;
	zoom: 1;
	background: repeat-y;
}

.x-menu-floating{
    border: 1px solid;
}

.x-menu a {
    text-decoration: none !important;
}

.ext-ie .x-menu {
    zoom:1;
    overflow:hidden;
}

.x-menu-list{
    padding: 2px;
	background:transparent;
	border:0 none;
    overflow:hidden;
    overflow-y: hidden;
}

.ext-strict .ext-ie .x-menu-list{
    position: relative;
}

.x-menu li{
	line-height:100%;
}

.x-menu li.x-menu-sep-li{
	font-size:1px;
	line-height:1px;
}

.x-menu-list-item{
    white-space: nowrap;
	display:block;
	padding:1px;
}

.x-menu-item{
    -moz-user-select: none;
    -khtml-user-select:none;
    -webkit-user-select:ignore;
}

.x-menu-item-arrow{
	background:transparent no-repeat right;
}

.x-menu-sep {
	display:block;
	font-size:1px;
	line-height:1px;
	margin: 2px 3px;
	border-bottom:1px solid;
    overflow:hidden;
}

.x-menu-focus {
	position:absolute;
	left:-1px;
	top:-1px;
	width:1px;
	height:1px;
    line-height:1px;
    font-size:1px;
    -moz-outline:0 none;
    outline:0 none;
    -moz-user-select: none;
    -khtml-user-select:none;
    -webkit-user-select:ignore;
    overflow:hidden;
    display:block;
}

a.x-menu-item {
    cursor: pointer;
    display: block;
    line-height: 16px;
    outline-color: -moz-use-text-color;
    outline-style: none;
    outline-width: 0;
    padding: 3px 21px 3px 27px;
    position: relative;
    text-decoration: none;
    white-space: nowrap;
}

.x-menu-item-active {
    background-repeat: repeat-x;
    background-position: left bottom;
    border-style:solid;
    border-width: 1px 0;
    margin:0 1px;
	padding: 0;
}

.x-menu-item-active a.x-menu-item {
    border-style:solid;
    border-width:0 1px;
    margin:0 -1px;
}

.x-menu-item-icon {
	border: 0 none;
	height: 16px;
	padding: 0;
	vertical-align: top;
	width: 16px;
	position: absolute;
    left: 3px;
    top: 3px;
    margin: 0;
    background-position:center;
}

.ext-ie .x-menu-item-icon {
    left: -24px;
}
.ext-strict .x-menu-item-icon {
    left: 3px;
}

.ext-ie6 .x-menu-item-icon {
    left: -24px;
}

.ext-ie .x-menu-item-icon {
    vertical-align: middle;
}

.x-menu-check-item .x-menu-item-icon{
	background: transparent no-repeat center;
}

.x-menu-group-item .x-menu-item-icon{
	background: transparent;
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background: transparent no-repeat center;
}

.x-date-menu .x-menu-list{
    padding: 0;
}

.x-menu-date-item{
	padding:0;
}

.x-menu .x-color-palette, .x-menu .x-date-picker{
    margin-left: 26px;
	margin-right:4px;
}

.x-menu .x-date-picker{
    border:1px solid;
    margin-top:2px;
    margin-bottom:2px;
}

.x-menu-plain .x-color-palette, .x-menu-plain .x-date-picker{
	 margin: 0;
	 border: 0 none;
}

.x-date-menu {
   padding:0 !important;
}

/*
 * fixes separator visibility problem in IE 6
 */
.ext-strict .ext-ie6 .x-menu-sep-li {
    padding: 3px 4px;
}
.ext-strict .ext-ie6 .x-menu-sep {
    margin: 0;
    height: 1px;
}

/*
 * Ugly mess to remove the white border under the picker
 */
.ext-ie .x-date-menu{
    height: 199px;
}

.ext-strict .ext-ie .x-date-menu, .ext-border-box .ext-ie8 .x-date-menu{
    height: 197px;
}

.ext-strict .ext-ie7 .x-date-menu{
    height: 195px;
}

.ext-strict .ext-ie8 .x-date-menu{
    height: auto;
}

.x-cycle-menu .x-menu-item-checked {
    border:1px dotted !important;
	padding:0;
}

.x-menu .x-menu-scroller {
    width: 100%;
	background-repeat:no-repeat;
	background-position:center;
	height:8px;
    line-height: 8px;
	cursor:pointer;
    margin: 0;
    padding: 0;
}

.x-menu .x-menu-scroller-active{
    height: 6px;
    line-height: 6px;
}

.x-menu-list-item-indent{
    padding-left: 27px;
}