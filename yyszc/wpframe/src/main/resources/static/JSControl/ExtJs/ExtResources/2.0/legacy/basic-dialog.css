/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-dlg-proxy {
	background-image: url(../images/default/gradient-bg.gif);
	background-color:#c3daf9;
	border:1px solid #6593cf;
	z-index:10001;
	overflow:hidden;
	position:absolute;
	left:0;top:0;
}
.x-dlg-shadow{
	background:#aaaaaa;
	position:absolute;
	left:0;top:0;
}
.x-dlg-focus{
	-moz-outline:0 none;
	outline:0 none;
	width:0;
	height:0;
	overflow:hidden;
	position:absolute;
	top:0;
	left:0;
}
.x-dlg-mask{
	z-index:10000;   
   display:none;
   position:absolute;
   top:0;
   left:0;
   -moz-opacity: 0.5;
   opacity:.50;
   filter: alpha(opacity=50);
   background-color:#CCC;
}
body.x-body-masked select {
	visibility:hidden;
}
body.x-body-masked .x-dlg select {
	visibility:visible;
}
.x-dlg{
	z-index:10001;
	overflow:hidden;
	position:absolute;
	left:300;top:0;
}
.x-dlg .x-dlg-hd {
	background: url(../images/default/basic-dialog/hd-sprite.gif) repeat-x 0 -82px;
	background-color:navy;
	color:#FFF;
	font:bold 12px "sans serif", tahoma, verdana, helvetica;
	overflow:hidden;
	padding:5px;
    white-space: nowrap;
}
.x-dlg .x-dlg-hd-left {
	background: url(../images/default/basic-dialog/hd-sprite.gif) no-repeat 0 -41px;
	padding-left:3px;
	margin:0;
}
.x-dlg .x-dlg-hd-right {
	background: url(../images/default/basic-dialog/hd-sprite.gif) no-repeat right 0;
	padding-right:3px;
}
.x-dlg .x-dlg-dlg-body{
	background:url(../images/default/layout/gradient-bg.gif);
	border:1px solid #6593cf;
	border-top:0 none;
	padding:10px;
	position:absolute;
	top:24px;left:0;
	z-index:1;
	overflow:hidden;
}
.x-dlg-collapsed .x-resizable-handle{
    display:none;
}
.x-dlg .x-dlg-bd{
	overflow:hidden;
}
.x-dlg .x-dlg-ft{
	overflow:hidden;
	padding:5px;
	padding-bottom:0;
}

.x-dlg .x-tabs-body{
	background:white;
	overflow:auto;
}
.x-dlg .x-tabs-top .x-tabs-body{
	border:1px solid #6593cf;
	border-top:0 none;
}
.x-dlg .x-tabs-bottom .x-tabs-body{
	border:1px solid #6593cf;
	border-bottom:0 none;
}
.x-dlg .x-layout-container  .x-tabs-body{
	border:0 none;
}
.x-dlg .inner-tab{
	margin:5px;
}
.x-dlg .x-dlg-ft .x-btn{
	margin-right:5px;
	float:right;
	clear:none;
}
.x-dlg .x-dlg-ft .x-dlg-btns td {
	border:0;
	padding:0;
}
.x-dlg .x-dlg-ft .x-dlg-btns-right table{
	float:right;
	clear:none;
}
.x-dlg .x-dlg-ft .x-dlg-btns-left table{
	float:left;
	clear:none;
}
.x-dlg .x-dlg-ft .x-dlg-btns-center{
	text-align:center; /*ie*/
}
.x-dlg .x-dlg-ft .x-dlg-btns-center table{
	margin:0 auto; /*everyone else*/
}


.x-dlg .x-dlg-ft .x-dlg-btns .x-btn-focus .x-btn-left{
	background-position:0 -147px;
}
.x-dlg .x-dlg-ft .x-dlg-btns .x-btn-focus .x-btn-right{
	background-position:0 -168px;
}
.x-dlg .x-dlg-ft .x-dlg-btns .x-btn-focus .x-btn-center{
	background-position:0 -189px;
}

.x-dlg .x-dlg-ft .x-dlg-btns .x-btn-click .x-btn-center{
	background-position:0 -126px;
}
.x-dlg .x-dlg-ft .x-dlg-btns .x-btn-click .x-btn-right{
	background-position:0 -84px;
}
.x-dlg .x-dlg-ft .x-dlg-btns .x-btn-click .x-btn-left{
	background-position:0 -63px;
}

.x-dlg-draggable .x-dlg-hd{
	cursor:move;
}
.x-dlg-closable .x-dlg-hd{
	padding-right:22px;
}
.x-dlg-toolbox {
    position:absolute;
	top:4px;
	right:4px;
	z-index:6;
    width:40px;
    cursor:default;
    height:15px;
    background:transparent;
}
.x-dlg .x-dlg-close, .x-dlg .x-dlg-collapse {
    float:right;
    height:15px;
	width:15px;
	margin:0;
    margin-left:2px;
    padding:0;
	line-height:1px;
	font-size:1px;
	background-repeat:no-repeat;
	cursor:pointer;
	visibility:inherit;
}
.x-dlg .x-dlg-close {
    background-image:url(../images/default/basic-dialog/close.gif);
}
.x-dlg .x-dlg-collapse {
    background-image:url(../images/default/basic-dialog/collapse.gif);
}
.x-dlg-collapsed .x-dlg-collapse {
    background-image:url(../images/default/basic-dialog/expand.gif);
}
.x-dlg .x-dlg-close-over, .x-dlg .x-dlg-collapse-over {
    
}
.x-dlg div.x-resizable-handle-east{
	background-image:url(../images/default/basic-dialog/e-handle.gif);
	border:0;
	background-position:right;
	margin-right:0;
}
.x-dlg div.x-resizable-handle-south{
	background-image:url(../images/default/sizer/s-handle-dark.gif);
	border:0;
	height:6px;
}
.x-dlg div.x-resizable-handle-west{
	background-image:url(../images/default/basic-dialog/e-handle.gif);
	border:0;
	background-position:1px;
}
.x-dlg div.x-resizable-handle-north{
	background-image:url(../images/default/s.gif);
	border:0;
}
.x-dlg div.x-resizable-handle-northeast, .xtheme-gray .x-dlg div.x-resizable-handle-northeast{
	background-image:url(../images/default/s.gif);
	border:0;
}
.x-dlg div.x-resizable-handle-northwest, .xtheme-gray .x-dlg div.x-resizable-handle-northwest{
	background-image:url(../images/default/s.gif);
	border:0;
}
.x-dlg div.x-resizable-handle-southeast{
	background-image:url(../images/default/basic-dialog/se-handle.gif);
	background-position: bottom right;
	width:8px;
	height:8px;
	border:0;
}
.x-dlg div.x-resizable-handle-southwest{
	background-image:url(../images/default/sizer/sw-handle-dark.gif);
	background-position: top right;
	margin-left:1px;
	margin-bottom:1px;
	border:0;
}

#x-msg-box .x-dlg-ft .x-btn{
	float:none;
	clear:none;
	margin:0 3px;
}

#x-msg-box .x-dlg-bd {
	padding:5px;
	overflow:hidden !important;
	font:normal 13px verdana,tahoma,sans-serif;
}
#x-msg-box .ext-mb-input {
	margin-top:4px;
	width:95%;
}
#x-msg-box .ext-mb-textarea {
	margin-top:4px;
	font:normal 13px verdana,tahoma,sans-serif;
}
#x-msg-box .ext-mb-progress-wrap {
	margin-top:4px;
	border:1px solid #6593cf;
}
#x-msg-box .ext-mb-progress {
	height:18px;
	background: #e0e8f3 url(../images/default/qtip/bg.gif) repeat-x;
}
#x-msg-box .ext-mb-progress-bar {
	height:18px;
	overflow:hidden;
	width:0;
	background:#8BB8F3;
	border-top:1px solid #B2D0F7;
	border-bottom:1px solid #65A1EF;
	border-right:1px solid #65A1EF;
}

#x-msg-box .x-msg-box-wait {
	background: transparent url(../images/default/grid/loading.gif) no-repeat left;
    display:block;
    width:300px;
    padding-left:18px;
    line-height:18px;
}