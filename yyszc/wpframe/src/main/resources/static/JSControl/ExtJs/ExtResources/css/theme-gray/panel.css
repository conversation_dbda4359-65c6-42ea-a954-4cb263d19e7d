/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-panel {
    border-color: #d0d0d0;
}

.x-panel-header {
    color:#333;
	font-weight:bold; 
    font-size: 11px;
    font-family: tahoma,arial,verdana,sans-serif;
    border-color:#d0d0d0;
    background-image: url(../images/gray/panel/white-top-bottom.gif);
}

.x-panel-body {
    border-color:#d0d0d0;
    background-color:#fff;
}

.x-panel-bbar .x-toolbar, .x-panel-tbar .x-toolbar {
    border-color:#d0d0d0;
}

.x-panel-tbar-noheader .x-toolbar, .x-panel-mc .x-panel-tbar .x-toolbar {
    border-top-color:#d0d0d0;
}

.x-panel-body-noheader, .x-panel-mc .x-panel-body {
    border-top-color:#d0d0d0;
}

.x-panel-tl .x-panel-header {
    color:#333;
	font:bold 11px tahoma,arial,verdana,sans-serif;
}

.x-panel-tc {
	background-image: url(../images/gray/panel/top-bottom.gif);
}

.x-panel-tl, .x-panel-tr, .x-panel-bl,  .x-panel-br{
	background-image: url(../images/gray/panel/corners-sprite.gif);
    border-bottom-color:#d0d0d0;
}

.x-panel-bc {
	background-image: url(../images/gray/panel/top-bottom.gif);
}

.x-panel-mc {
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    background-color:#f1f1f1;
}

.x-panel-ml {
	background-color: #fff;
    background-image:url(../images/gray/panel/left-right.gif);
}

.x-panel-mr {
	background-image: url(../images/gray/panel/left-right.gif);
}

.x-tool {
    background-image:url(../images/gray/panel/tool-sprites.gif);
}

.x-panel-ghost {
    background-color:#f2f2f2;
}

.x-panel-ghost ul {
    border-color:#d0d0d0;
}

.x-panel-dd-spacer {
    border-color:#d0d0d0;
}

.x-panel-fbar td,.x-panel-fbar span,.x-panel-fbar input,.x-panel-fbar div,.x-panel-fbar select,.x-panel-fbar label{
    font:normal 11px arial,tahoma, helvetica, sans-serif;
}
