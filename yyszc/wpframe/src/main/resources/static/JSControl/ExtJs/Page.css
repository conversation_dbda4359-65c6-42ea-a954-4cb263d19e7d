body
{
	font:normal 12px verdana; margin:0; padding:0; border:0 none; overflow:hidden; height:100%; 
}

.ToolBarPassword { background-image: url(../../Image/changepasswd.gif) !important; }
.ToolBarLogout { background-image: url(../../Image/logout.gif) !important; }
.ToolBarHelp { background-image: url(../../Image/help.gif) !important; }
.ButtonAdd { background-image: url(../../Image/add.gif) !important; }
.ButtonEdit { background-image: url(../../Image/edit.gif) !important; }
.ButtonDetail { background-image: url(../../Image/detail.gif) !important; }
.ButtonDelete { background-image: url(../../Image/delete.gif) !important; }

.ButtonFormColor { background-image: url(../../Image/Button/color.gif) !important; }
.ButtonFormUpload { background-image: url(../../Image/Button/formupload.gif) !important; }
.ButtonFormAdd { background-image: url(../../Image/Button/formadd.gif) !important; }
.ButtonFormEdit { background-image: url(../../Image/Button/formedit.gif) !important; }
.ButtonFormDelete { background-image: url(../../Image/Button/formdelete.gif) !important; }
.ButtonFormLook { background-image: url(../../Image/Button/formlook.gif) !important; }
.ButtonSearch { background-image: url(../../Image/Button/search.gif) !important; }
.ButtonFind { background-image: url(../../Image/Button/find.png) !important; }
.ButtonLocation { background-image: url(../../Image/Button/location.png) !important; }
.ButtonLocus { background-image: url(../../Image/Button/locus.png) !important; }
.ButtonGridShow { background-image: url(../../Image/Button/gridshow.gif) !important; }
.ButtonDownload { background-image: url(../../Image/Button/down.gif) !important; }
.ButtonUp { background-image: url(../../Image/Button/up.gif) !important; }
.ButtonFlow { background-image: url(../../Image/Button/flow.gif) !important; }
.ButtonAppoint { background-image: url(../../Image/Button/appoint.gif) !important; }
.ButtonUserAdd { background-image: url(../../Image/Button/useradd.gif) !important; }
.ButtonUserEdit { background-image: url(../../Image/Button/useredit.gif) !important; }
.ButtonUserDelete { background-image: url(../../Image/Button/userdelete.gif) !important; }
.ButtonMultiLevel0 { background-image: url(../../Image/Button/MultiLevel0.gif) !important; }
.ButtonMultiLevel1 { background-image: url(../../Image/Button/MultiLevel1.gif) !important; }
.ButtonPerson { background-image: url(../../Image/Button/person.gif) !important; }
.ButtonComputer { background-image: url(../../Image/Button/computer.gif) !important; }
.ButtonNotepad { background-image: url(../../Image/Button/notepad.gif) !important; }
.ButtonRefresh { background-image: url(../../Image/Button/refresh.gif) !important; }
.ButtonLevelUp { background-image: url(../../Image/Button/level_up.gif) !important; }
.ButtonFolderAdd { background-image: url(../../Image/Button/folder_add.gif) !important; }
.ButtonFolderDelete { background-image: url(../../Image/Button/folder_delete.gif) !important; }
.ButtonFolderEdit { background-image: url(../../Image/Button/folder_edit.gif) !important; }
.ButtonFileAdd { background-image: url(../../Image/Button/file_add.gif) !important; }
.ButtonFileDelete { background-image: url(../../Image/Button/file_delete.gif) !important; }
.ButtonFileFB { background-image: url(../../Image/Button/formsubmit.gif) !important; }

.ButtonFileEmailEdit { background-image: url(../../Image/Button/email_edit.png) !important; }
.ButtonFileEmailGo { background-image: url(../../Image/Button/email_go.png) !important; }
.ButtonFileEmailOpen { background-image: url(../../Image/Button/email_open.png) !important; }
.ButtonFileEmailStar { background-image: url(../../Image/Button/star.jpg) !important; }

.ButtonFormConsult { background-image: url(../../Image/Button/eye.png) !important; }
.ButtonFormAccept { background-image: url(../../Image/Button/accept.png) !important; }
.ButtonFormAddTo { background-image: url(../../Image/Button/add.png) !important; }
.ButtonFormSubmit { background-image: url(../../Image/Button/submit.png) !important; }
.ButtonFormCancel { background-image: url(../../Image/Button/cancel.png) !important; }
.ButtonFormExit { background-image: url(../../Image/Button/Exit.gif) !important; }
.ButtonFormExtension { background-image: url(../../Image/Button/world.png) !important; }
.ButtonFormStop { background-image: url(../../Image/Button/stop.png) !important; }
.ButtonFormTranfer { background-image: url(../../Image/Button/tranfer.png) !important; }
.ButtonFormKey { background-image: url(../../Image/Button/key.png) !important; }
.ButtonFormFeed { background-image: url(../../Image/Button/feed.png) !important; }
.ButtonFormReback { background-image: url(../../Image/Button/reback.png) !important; }
.ButtonFormPackage { background-image: url(../../Image/Button/package.png) !important; }
.ButtonFormPicture { background-image: url(../../Image/Button/picture.png) !important; }
.ButtonFormPrint { background-image: url(../../Image/Button/printer.png) !important; }
.ButtonFormReport { background-image: url(../../Image/Button/report.png) !important; }
.ButtonFormFinish { background-image: url(../../Image/Button/finish.png) !important; }
.ButtonFormExport { background-image: url(../../Image/Button/expexcel.png) !important; }
.ButtonFormImport { background-image: url(../../Image/Button/import.png) !important; }

.TreeNodeDisable { background-image: url(../../Image/Button/disable.png) !important; }
.TreeNodeEnable { background-image: url(../../Image/Button/enable.png) !important; }
.TreeNodeDefault { background-image: url(../../Image/Button/camera.png) !important; }

.x-grid3-cell-text-visible .x-grid3-cell-inner{overflow:visible;padding:3px 3px 3px 5px;white-space:normal;}

.msg .x-box-mc {
    font-size:14px;
}

#msg-div {
    position:absolute;
    left:35%;
    top:10px;
    width:250px;
    z-index:20000;
}

