/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-panel {
    border-style: solid;
    border-width:0;
}

.x-panel-header {
    overflow:hidden;
    zoom:1;
    padding:5px 3px 4px 5px;
    border:1px solid;
    line-height: 15px;
    background: transparent repeat-x 0 -1px;
}

.x-panel-body {
    border:1px solid;
    border-top:0 none;
    overflow:hidden;
    position: relative; /* added for item scroll positioning */
}

.x-panel-bbar .x-toolbar, .x-panel-tbar .x-toolbar {
    border:1px solid;
    border-top:0 none;
    overflow:hidden;
    padding:2px;
}

.x-panel-tbar-noheader .x-toolbar, .x-panel-mc .x-panel-tbar .x-toolbar {
    border-top:1px solid;
    border-bottom: 0 none;
}

.x-panel-body-noheader, .x-panel-mc .x-panel-body {
    border-top:1px solid;
}

.x-panel-header {
    overflow:hidden;
    zoom:1;
}

.x-panel-tl .x-panel-header {
    padding:5px 0 4px 0;
    border:0 none;
    background:transparent;
}

.x-panel-tl .x-panel-icon, .x-window-tl .x-panel-icon {
    padding-left:20px !important;
    background-repeat:no-repeat;
    background-position:0 4px;
    zoom:1;
}

.x-panel-inline-icon {
    width:16px;
	height:16px;
    background-repeat:no-repeat;
    background-position:0 0;
	vertical-align:middle;
	margin-right:4px;
	margin-top:-1px;
	margin-bottom:-1px;
}

.x-panel-tc {
	background: transparent repeat-x 0 0;
	overflow:hidden;
}

/* fix ie7 strict mode bug */
.ext-strict .ext-ie7 .x-panel-tc {
    overflow: visible;
}

.x-panel-tl {
	background: transparent no-repeat 0 0;
	padding-left:6px;
    zoom:1;
    border-bottom:1px solid;
}

.x-panel-tr {
	background: transparent no-repeat right 0;
	zoom:1;
    padding-right:6px;
}

.x-panel-bc {
	background: transparent repeat-x 0 bottom;
    zoom:1;
}

.x-panel-bc .x-panel-footer {
    zoom:1;
}

.x-panel-bl {
	background: transparent no-repeat 0 bottom;
	padding-left:6px;
    zoom:1;
}

.x-panel-br {
	background: transparent no-repeat right bottom;
	padding-right:6px;
    zoom:1;
}

.x-panel-mc {
    border:0 none;
    padding:0;
    margin:0;
    padding-top:6px;
}

.x-panel-mc .x-panel-body {
    background:transparent;
    border: 0 none;
}

.x-panel-ml {
	background: repeat-y 0 0;
	padding-left:6px;
    zoom:1;
}

.x-panel-mr {
	background: transparent repeat-y right 0;
	padding-right:6px;
    zoom:1;
}

.x-panel-bc .x-panel-footer {
    padding-bottom:6px;
}

.x-panel-nofooter .x-panel-bc, .x-panel-nofooter .x-window-bc {
	height:6px;
    font-size:0;
    line-height:0;
}

.x-panel-bwrap {
    overflow:hidden;
    zoom:1;
    left:0;
    top:0;
}
.x-panel-body {
    overflow:hidden;
    zoom:1;
}

.x-panel-collapsed .x-resizable-handle{
    display:none;
}

.ext-gecko .x-panel-animated div {
    overflow:hidden !important;
}

/* Plain */
.x-plain-body {
    overflow:hidden;
}

.x-plain-bbar .x-toolbar {
    overflow:hidden;
    padding:2px;
}

.x-plain-tbar .x-toolbar {
    overflow:hidden;
    padding:2px;
}

.x-plain-bwrap {
    overflow:hidden;
    zoom:1;
}

.x-plain {
    overflow:hidden;
}

/* Tools */
.x-tool {
    overflow:hidden;
    width:15px;
    height:15px;
    float:right;
    cursor:pointer;
    background:transparent no-repeat;
    margin-left:2px;
}

/* expand / collapse tools */
.x-tool-toggle {
    background-position:0 -60px;
}

.x-tool-toggle-over {
    background-position:-15px -60px;
}

.x-panel-collapsed .x-tool-toggle {
    background-position:0 -75px;
}

.x-panel-collapsed .x-tool-toggle-over {
    background-position:-15px -75px;
}


.x-tool-close {
    background-position:0 -0;
}

.x-tool-close-over {
    background-position:-15px 0;
}

.x-tool-minimize {
    background-position:0 -15px;
}

.x-tool-minimize-over {
    background-position:-15px -15px;
}

.x-tool-maximize {
    background-position:0 -30px;
}

.x-tool-maximize-over {
    background-position:-15px -30px;
}

.x-tool-restore {
    background-position:0 -45px;
}

.x-tool-restore-over {
    background-position:-15px -45px;
}

.x-tool-gear {
    background-position:0 -90px;
}

.x-tool-gear-over {
    background-position:-15px -90px;
}

.x-tool-pin {
    background-position:0 -135px;
}

.x-tool-pin-over {
    background-position:-15px -135px;
}

.x-tool-unpin {
    background-position:0 -150px;
}

.x-tool-unpin-over {
    background-position:-15px -150px;
}

.x-tool-right {
    background-position:0 -165px;
}

.x-tool-right-over {
    background-position:-15px -165px;
}

.x-tool-left {
    background-position:0 -180px;
}

.x-tool-left-over {
    background-position:-15px -180px;
}

.x-tool-up {
    background-position:0 -210px;
}

.x-tool-up-over {
    background-position:-15px -210px;
}

.x-tool-down {
    background-position:0 -195px;
}

.x-tool-down-over {
    background-position:-15px -195px;
}

.x-tool-refresh {
    background-position:0 -225px;
}

.x-tool-refresh-over {
    background-position:-15px -225px;
}

.x-tool-minus {
    background-position:0 -255px;
}

.x-tool-minus-over {
    background-position:-15px -255px;
}

.x-tool-plus {
    background-position:0 -240px;
}

.x-tool-plus-over {
    background-position:-15px -240px;
}

.x-tool-search {
    background-position:0 -270px;
}

.x-tool-search-over {
    background-position:-15px -270px;
}

.x-tool-save {
    background-position:0 -285px;
}

.x-tool-save-over {
    background-position:-15px -285px;
}

.x-tool-help {
    background-position:0 -300px;
}

.x-tool-help-over {
    background-position:-15px -300px;
}

.x-tool-print {
    background-position:0 -315px;
}

.x-tool-print-over {
    background-position:-15px -315px;
}

/* Ghosting */
.x-panel-ghost {
    z-index:12000;
    overflow:hidden;
    position:absolute;
    left:0;top:0;
    opacity:.65;
    -moz-opacity:.65;
    filter:alpha(opacity=65);
}

.x-panel-ghost ul {
    margin:0;
    padding:0;
    overflow:hidden;
    font-size:0;
    line-height:0;
    border:1px solid;
    border-top:0 none;
    display:block;
}

.x-panel-ghost * {
    cursor:move !important;
}

.x-panel-dd-spacer {
    border:2px dashed;
}

/* Buttons */
.x-panel-btns {
    padding:5px;
    overflow:hidden;
}

.x-panel-btns td.x-toolbar-cell{
	padding:3px;
}

.x-panel-btns .x-btn-focus .x-btn-left{
	background-position:0 -147px;
}

.x-panel-btns .x-btn-focus .x-btn-right{
	background-position:0 -168px;
}

.x-panel-btns .x-btn-focus .x-btn-center{
	background-position:0 -189px;
}

.x-panel-btns .x-btn-over .x-btn-left{
	background-position:0 -63px;
}

.x-panel-btns .x-btn-over .x-btn-right{
	background-position:0 -84px;
}

.x-panel-btns .x-btn-over .x-btn-center{
	background-position:0 -105px;
}

.x-panel-btns .x-btn-click .x-btn-center{
	background-position:0 -126px;
}

.x-panel-btns .x-btn-click  .x-btn-right{
	background-position:0 -84px;
}

.x-panel-btns .x-btn-click .x-btn-left{
	background-position:0 -63px;
}

.x-panel-fbar td,.x-panel-fbar span,.x-panel-fbar input,.x-panel-fbar div,.x-panel-fbar select,.x-panel-fbar label{
	white-space: nowrap;
}
