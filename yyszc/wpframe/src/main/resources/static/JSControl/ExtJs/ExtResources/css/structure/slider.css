/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
/* Shared styles */
.x-slider {
    zoom:1;
}

.x-slider-inner {
    position:relative;
    left:0;
    top:0;
    overflow:visible;
    zoom:1;
}

.x-slider-focus {
	position:absolute;
	left:0;
	top:0;
	width:1px;
	height:1px;
    line-height:1px;
    font-size:1px;
    -moz-outline:0 none;
    outline:0 none;
    -moz-user-select: none;
    -khtml-user-select:none;
    -webkit-user-select:ignore;
	display:block;
	overflow:hidden;  
}

/* Horizontal styles */
.x-slider-horz {
    padding-left:7px;
    background:transparent no-repeat 0 -22px;
}

.x-slider-horz .x-slider-end {
    padding-right:7px;
    zoom:1;
    background:transparent no-repeat right -44px;
}

.x-slider-horz .x-slider-inner {
    background:transparent repeat-x 0 0;
    height:22px;
}

.x-slider-horz .x-slider-thumb {
    width:14px;
    height:15px;
    position:absolute;
    left:0;
    top:3px;
    background:transparent no-repeat 0 0;
}

.x-slider-horz .x-slider-thumb-over {
    background-position: -14px -15px;
}

.x-slider-horz .x-slider-thumb-drag {
    background-position: -28px -30px;
}

/* Vertical styles */
.x-slider-vert {
    padding-top:7px;
    background:transparent no-repeat -44px 0;
    width:22px;
}

.x-slider-vert .x-slider-end {
    padding-bottom:7px;
    zoom:1;
    background:transparent no-repeat -22px bottom;
}

.x-slider-vert .x-slider-inner {
    background:transparent repeat-y 0 0;
}

.x-slider-vert .x-slider-thumb {
    width:15px;
    height:14px;
    position:absolute;
    left:3px;
    bottom:0;
    background:transparent no-repeat 0 0;
}

.x-slider-vert .x-slider-thumb-over {
    background-position: -15px -14px;
}

.x-slider-vert .x-slider-thumb-drag {
    background-position: -30px -28px;
}