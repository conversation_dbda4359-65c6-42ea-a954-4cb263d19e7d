/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-list-header{
    background-color:#393d4e;
	background-image:url(../images/access/toolbar/bg.gif);
	background-position:0 top;
}

.x-list-header-inner div em {
    border-left-color:#667;
    font:normal 14px arial, tahoma, helvetica, sans-serif;
    line-height: 14px;
}

.x-list-body dt em {
    font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-list-over {
    background-color:#7E5530;
}

.x-list-selected {
    background-color:#E5872C;
}

.x-list-resizer {
    border-left-color:#555;
    border-right-color:#555;
}

.x-list-header-inner em.sort-asc, .x-list-header-inner em.sort-desc {
    background-image:url(../images/access/grid/sort-hd.gif);
    border-color: #3e4e6c;
}
