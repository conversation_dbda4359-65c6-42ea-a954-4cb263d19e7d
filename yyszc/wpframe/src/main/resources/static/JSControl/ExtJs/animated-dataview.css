/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
#phones {
    background-color: #fff;
    text-shadow: #fff 0 1px 0;
}

#phones ul {
    position: relative;
    display: block;
    height: auto;
    font-size: 85%;
}

#phones ul li img {
    margin-bottom: 1px;
}

#phones ul li {
    float: left;
    padding: 8px 17px;
    margin: 5px;
/*    margin: 10px 0 0 25px;*/
    text-align: center;
    line-height: 1.25em;
    color: #333;
    font-family: "Helvetica Neue",sans-serif;
    height: 113px;
    width: 112px;
    overflow: hidden;
    border-top: 1px solid transparent;
    cursor: pointer;
}

#phones ul li.phone-hover {
    background-color: #eee;
}

#phones ul li.x-view-selected {
    background-color: #ddf0ff;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border: 1px solid #99bbe8;
}

#phones ul li img {
/*    display: block;*/
}

#phones li strong {
    color: #000;
    display: block;
}

#phones li span {
    color: #999;
}

.x-slider-horz .x-slider-thumb {
    background-image: url(../../Image/slider-thumb.png);
}

.sort-desc {
    background-image: url("./ExtResources/images/default/grid/sort_desc.gif") !important;
    background-position: 4px 7px !important;
}

.sort-asc {
    background-image: url("./ExtResources/images/default/grid/sort_asc.gif") !important;
    background-position: 4px 7px !important;
}