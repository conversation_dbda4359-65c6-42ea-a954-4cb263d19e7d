/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-window-proxy {
    background-color:#c7dffc;
    border-color:#99bbe8;
}

.x-window-tl .x-window-header {
    color:#15428b;
	font:bold 11px tahoma,arial,verdana,sans-serif;
}

.x-window-tc {
	background-image: url(../images/default/window/top-bottom.png);
}

.x-window-tl {
	background-image: url(../images/default/window/left-corners.png);
}

.x-window-tr {
	background-image: url(../images/default/window/right-corners.png);
}

.x-window-bc {
	background-image: url(../images/default/window/top-bottom.png);
}

.x-window-bl {
	background-image: url(../images/default/window/left-corners.png);
}

.x-window-br {
	background-image: url(../images/default/window/right-corners.png);
}

.x-window-mc {
    border-color:#99bbe8;
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    background-color:#dfe8f6;
}

.x-window-ml {
	background-image: url(../images/default/window/left-right.png);
}

.x-window-mr {
	background-image: url(../images/default/window/left-right.png);
}

.x-window-maximized .x-window-tc {
    background-color:#fff;
}

.x-window-bbar .x-toolbar {
    border-top-color:#99bbe8;
}

.x-panel-ghost .x-window-tl {
    border-bottom-color:#99bbe8;
}

.x-panel-collapsed .x-window-tl {
    border-bottom-color:#84a0c4;
}

.x-dlg-mask{
   background-color:#ccc;
}

.x-window-plain .x-window-mc {
    background-color: #ccd9e8;
    border-color: #a3bae9 #dfe8f6 #dfe8f6 #a3bae9;
}

.x-window-plain .x-window-body {
    border-color: #dfe8f6 #a3bae9 #a3bae9 #dfe8f6;
}

body.x-body-masked .x-window-plain .x-window-mc {
    background-color: #ccd9e8;
}