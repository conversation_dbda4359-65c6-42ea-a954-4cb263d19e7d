/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-date-picker {
    border-color: #1b376c;
    background-color:#fff;
}

.x-date-middle,.x-date-left,.x-date-right {
	background-image: url(../images/default/shared/hd-sprite.gif);
	color:#fff;
	font:bold 11px "sans serif", tahoma, verdana, helvetica;
}

.x-date-middle .x-btn .x-btn-text {
    color:#fff;
}

.x-date-middle .x-btn-mc em.x-btn-arrow {
    background-image:url(../images/default/toolbar/btn-arrow-light.gif);
}

.x-date-right a {
    background-image: url(../images/default/shared/right-btn.gif);
}

.x-date-left a{
	background-image: url(../images/default/shared/left-btn.gif);
}

.x-date-inner th {
    background-color:#dfecfb;
    background-image:url(../images/default/shared/glass-bg.gif);
	border-bottom-color:#a3bad9;
    font:normal 10px arial, helvetica,tahoma,sans-serif;
	color:#233d6d;
}

.x-date-inner td {
    border-color:#fff;
}

.x-date-inner a {
    font:normal 11px arial, helvetica,tahoma,sans-serif;
    color:#000;
}

.x-date-inner .x-date-active{
	color:#000;
}

.x-date-inner .x-date-selected a{
    background-color:#dfecfb;
	background-image:url(../images/default/shared/glass-bg.gif);
	border-color:#8db2e3;
}

.x-date-inner .x-date-today a{
	border-color:darkred;
}

.x-date-inner .x-date-selected span{
    font-weight:bold;
}

.x-date-inner .x-date-prevday a,.x-date-inner .x-date-nextday a {
	color:#aaa;
}

.x-date-bottom {
    border-top-color:#a3bad9;
    background-color:#dfecfb;
    background-image:url(../images/default/shared/glass-bg.gif);
}

.x-date-inner a:hover, .x-date-inner .x-date-disabled a:hover{
    color:#000;
    background-color:#ddecfe;
}

.x-date-inner .x-date-disabled a {
	background-color:#eee;
	color:#bbb;
}

.x-date-mmenu{
    background-color:#eee !important;
}

.x-date-mmenu .x-menu-item {
	font-size:10px;
	color:#000;
}

.x-date-mp {
	background-color:#fff;
}

.x-date-mp td {
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns button {
	background-color:#083772;
	color:#fff;
	border-color: #3366cc #000055 #000055 #3366cc;
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns {
    background-color: #dfecfb;
	background-image: url(../images/default/shared/glass-bg.gif);
}

.x-date-mp-btns td {
	border-top-color: #c5d2df;
}

td.x-date-mp-month a,td.x-date-mp-year a {
	color:#15428b;
}

td.x-date-mp-month a:hover,td.x-date-mp-year a:hover {
	color:#15428b;
	background-color: #ddecfe;
}

td.x-date-mp-sel a {
    background-color: #dfecfb;
	background-image: url(../images/default/shared/glass-bg.gif);
	border-color:#8db2e3;
}

.x-date-mp-ybtn a {
    background-image:url(../images/default/panel/tool-sprites.gif);
}

td.x-date-mp-sep {
   border-right-color:#c5d2df;
}