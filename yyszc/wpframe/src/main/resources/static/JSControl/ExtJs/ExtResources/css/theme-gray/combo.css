/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-combo-list {
    border-color:#ccc;
    background-color:#ddd;
    font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-combo-list-inner {
    background-color:#fff;
}

.x-combo-list-hd {
    font:bold 11px tahoma, arial, helvetica, sans-serif;
    color:#333;
    background-image: url(../images/default/layout/panel-title-light-bg.gif);
    border-bottom-color:#BCBCBC;
}

.x-resizable-pinned .x-combo-list-inner {
    border-bottom-color:#BEBEBE;
}

.x-combo-list-item {
    border-color:#fff;
}

.x-combo-list .x-combo-selected{
	border-color:#777 !important;
    background-color:#f0f0f0;
}

.x-combo-list .x-toolbar {
    border-top-color:#BCBCBC;
}

.x-combo-list-small {
    font:normal 11px tahoma, arial, helvetica, sans-serif;
}