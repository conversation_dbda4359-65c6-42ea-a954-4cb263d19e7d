/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-combo-list {
    border:1px solid;
    zoom:1;
    overflow:hidden;
}

.x-combo-list-inner {
    overflow:auto;
    position:relative; /* for calculating scroll offsets */
    zoom:1;
    overflow-x:hidden;
}

.x-combo-list-hd {
    border-bottom:1px solid;
    padding:3px;
}

.x-resizable-pinned .x-combo-list-inner {
    border-bottom:1px solid;
}

.x-combo-list-item {
    padding:2px;
    border:1px solid;
    white-space: nowrap;
    overflow:hidden;
    text-overflow: ellipsis;
}

.x-combo-list .x-combo-selected{
	border:1px dotted !important;
    cursor:pointer;
}

.x-combo-list .x-toolbar {
    border-top:1px solid;
    border-bottom:0 none;
}