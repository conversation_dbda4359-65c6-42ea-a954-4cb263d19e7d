/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-progress-wrap {
    border:1px solid #6593cf;
    overflow:hidden;
}
.x-progress-inner {
    height:18px;
    background: #e0e8f3 url(../images/default/qtip/bg.gif) repeat-x;
    position:relative;
}
.x-progress-bar {
    height:18px;
    float:left;
    width:0;
    background:#9CBFEE url( ../images/default/progress/progress-bg.gif ) repeat-x left center;
    border-top:1px solid #D1E4FD;
    border-bottom:1px solid #7FA9E4;
    border-right:1px solid #7FA9E4;
}
.x-progress-text {
    font-size:11px;
    font-weight:bold;
    color:#fff;
    padding:1px 5px;
    overflow:hidden;
    position:absolute;
    left:0;
    text-align:center;
}
.x-progress-text-back {
    color:#396095;
    line-height:16px;
}
.ext-ie .x-progress-text-back {
    line-height:15px;
}