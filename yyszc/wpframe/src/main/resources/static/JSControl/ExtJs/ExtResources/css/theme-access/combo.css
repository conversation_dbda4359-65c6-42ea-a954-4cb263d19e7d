/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-combo-list {
    border:2px solid #232732;
    background-color:#555566;
    font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-combo-list-inner {
    background-color:#414551;
}

.x-combo-list-hd {
    font:bold 14px tahoma, arial, helvetica, sans-serif;
    color:fff;
    background-image: url(../images/default/layout/panel-title-light-bg.gif);
    border-bottom-color:#98c0f4;
}

.x-resizable-pinned .x-combo-list-inner {
    border-bottom-color:#98c0f4;
}

.x-combo-list-item {
    border-color:#556;
}

.x-combo-list .x-combo-selected {
	border-color:#e5872c !important;
    background-color:#e5872c;
}

.x-combo-list .x-toolbar {
    border-top-color:#98c0f4;
}

.x-combo-list-small {
    font:normal 14px tahoma, arial, helvetica, sans-serif;
}
