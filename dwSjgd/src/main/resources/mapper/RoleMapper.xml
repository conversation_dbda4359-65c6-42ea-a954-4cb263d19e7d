<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.role.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.role.entity.Role">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="roleName" column="RoleName" jdbcType="VARCHAR"/>
            <result property="adminGroupIds" column="AdminGroupIds" jdbcType="VARCHAR"/>
            <result property="isHide" column="IsHide" jdbcType="BIT"/>
            <result property="roleKind" column="RoleKind" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>
    </sql>
</mapper>
