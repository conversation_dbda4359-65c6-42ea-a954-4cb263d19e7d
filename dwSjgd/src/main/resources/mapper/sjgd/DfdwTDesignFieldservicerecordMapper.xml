<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignFieldservicerecordMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="designProjectId" column="designProjectId" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="designUnit" column="designUnit" jdbcType="VARCHAR"/>
            <result property="applyDate" column="applyDate" jdbcType="DATE"/>
            <result property="specialtyPersonId" column="specialtyPersonId" jdbcType="VARCHAR"/>
            <result property="engineeringPhase" column="engineeringPhase" jdbcType="VARCHAR"/>
            <result property="node" column="node" jdbcType="VARCHAR"/>
            <result property="fieldCondition" column="fieldCondition" jdbcType="VARCHAR"/>
            <result property="problemsAndMeasures" column="problemsAndMeasures" jdbcType="VARCHAR"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,designProjectId,applyNo,
        designUnit,applyDate,specialtyPersonId,
        engineeringPhase,node,fieldCondition,
        problemsAndMeasures,approveState,createTime,
        createId
    </sql>
    <select id="getPageList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord">
        <include refid="BaseSelect">
        </include>
        order by res.id desc
    </select>

    <select id="getDownLst" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord">
        WITH NodeSplit AS (
        SELECT
        id,
        CAST(LEFT(node, CHARINDEX(',', node + ',') - 1) AS VARCHAR(8000)) AS nodePart,
        STUFF(node, 1, CHARINDEX(',', node + ','), '') AS remainingNode
        FROM DFDW_T_Design_FieldServiceRecord
        WHERE node IS NOT NULL

        UNION ALL

        SELECT
        id,
        CAST(LEFT(remainingNode, CHARINDEX(',', remainingNode + ',') - 1) AS VARCHAR(8000)),
        STUFF(remainingNode, 1, CHARINDEX(',', remainingNode + ','), '')
        FROM NodeSplit
        WHERE remainingNode > ''
        ),
        NodeLabels AS (
        SELECT
        ns.id,
        ns.nodePart,
        d.remark,
        d.label,
        CASE
        WHEN CHARINDEX(':', ns.nodePart) > 0 THEN SUBSTRING(ns.nodePart, CHARINDEX(':', ns.nodePart) + 1, LEN(ns.nodePart))
        ELSE ''
        END AS extraInfo
        FROM
        NodeSplit ns
        LEFT JOIN
        DFDW_DICT_DATA d ON ns.nodePart LIKE '%' + d.value + '%'
        WHERE
        d.dict_type IN ('sjgd-line-node', 'sjgd-electricity-node')
        )
        select * from(
          SELECT
          t.*,p.projectName,p.designDirectorUserId, CASE t.approveState
          WHEN 0 THEN '未提交'
          WHEN 1 THEN '审批中'
          WHEN 2 THEN '已审批'
          WHEN 3 THEN '已驳回'
          WHEN 4 THEN '流程终止'
          ELSE '未知状态'
          END as approveStateStr,t.applyDate as applyDateStr,
          STUFF((
          SELECT '、' + '[' + nl.remark + ']' + nl.label +
          CASE
          WHEN nl.extraInfo != '' THEN ':' + nl.extraInfo
          ELSE ''
          END
          FROM NodeLabels nl
          WHERE nl.id = t.id
          FOR XML PATH(''), TYPE
          ).value('.', 'NVARCHAR(MAX)'), 1, 1, '') AS nodeLabels,
          ( select top 1 a.applyNo from DFDW_T_Design_ServiceAssociation sac
          left join DFDW_T_Design_SupervisorApply a on sac.supervisorApplyId = a.Id
          where serviceRecordId = t.Id) as supervisorApplyNo,
          ( select top 1 b.applyNo from DFDW_T_Design_ServiceAssociation sac
          left join DFDW_T_Design_FieldProblemFeedback b on sac.problemFeedbackId = b.Id
          where serviceRecordId = t.Id) as problemFeedbackNo
          FROM
          DFDW_T_Design_FieldServiceRecord t
          left join DFDW_T_Design_Project p on p.id = t.designProjectId
               <where><if test="query.ids != null">   AND t.id in
                       <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                           #{id,jdbcType=INTEGER}
                       </foreach>
                   </if>
                   <if test="query.designProjectId != null">
                       AND t.designProjectId = #{query.designProjectId}
                   </if>
                   <if test="query.applyNo != null and query.applyNo != ''">
                       AND t.applyNo LIKE CONCAT('%', #{query.applyNo}, '%')
                   </if>
                   <if test="query.designUnit != null and query.designUnit != ''">
                       AND t.designUnit LIKE CONCAT('%', #{query.designUnit}, '%')
                   </if>
                   <if test="query.specialtyPersonId != null">
                       AND t.specialtyPersonId = #{query.specialtyPersonId}
                   </if>
                   <if test="query.specialtyPersonName != null and query.specialtyPersonName != ''">
                       AND t.specialtyPersonName LIKE CONCAT('%', #{query.specialtyPersonName}, '%')
                   </if>
                   <if test="query.engineeringPhase != null and query.engineeringPhase != ''">
                       AND t.engineeringPhase = #{query.engineeringPhase}
                   </if>
                   <if test="query.node != null and query.node != ''">
                       AND t.node = #{query.node}
                   </if>
                   <if test="query.approveState != null">
                       AND t.approveState = #{query.approveState}
                   </if>
                   <if test="query.createId != null">
                       AND t.createId = #{query.createId}
                   </if>
                   <if test="query.projectName != null">
                       AND p.projectName like concat( '%',#{query.projectName},'%')
                   </if>
                   <if test="query.queryUserId != null">
                       AND  ( p.designDirectorUserId =#{query.queryUserId} and t.approveState=2
                       or
                       t.specialtyPersonId =#{query.queryUserId}
                       )
                   </if>
               </where>
        )res
        where 1=1
        <if test="query.supervisorApplyNo != null and query.supervisorApplyNo != ''">
            AND res.supervisorApplyNo LIKE CONCAT('%', #{query.supervisorApplyNo}, '%')
        </if>
        <if test="query.problemFeedbackNo != null and query.problemFeedbackNo != ''">
            AND res.problemFeedbackNo LIKE CONCAT('%', #{problemFeedbackNo}, '%')
        </if>

        ORDER BY res.id DESC
    </select>
    <select id="getAllList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord">
        <include refid="BaseSelect">
        </include>
        order by res.id desc
    </select>
    <sql id="BaseSelect">
        select res.* from (
        SELECT t.*,p.projectName,p.designDirectorUserId,
        (	select  top 1 a.applyNo from DFDW_T_Design_ServiceAssociation sac
        left join DFDW_T_Design_SupervisorApply a on sac.supervisorApplyId = a.Id
        where  serviceRecordId = t.Id) as supervisorApplyNo,
        (	select  top 1 b.applyNo from DFDW_T_Design_ServiceAssociation sac
        left join DFDW_T_Design_FieldProblemFeedback b on sac.problemFeedbackId = b.Id
        where  serviceRecordId = t.Id) as problemFeedbackNo
        FROM DFDW_T_Design_FieldServiceRecord t
        left join  DFDW_T_Design_Project p on p.id = t.designProjectId
        <where>
            <if test="query.ids != null">
                AND t.id in
                <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="query.designProjectId != null">
                AND t.designProjectId = #{query.designProjectId}
            </if>
            <if test="query.applyNo != null and query.applyNo != ''">
                AND t.applyNo LIKE CONCAT('%', #{query.applyNo}, '%')
            </if>
            <if test="query.designUnit != null and query.designUnit != ''">
                AND t.designUnit LIKE CONCAT('%', #{query.designUnit}, '%')
            </if>
            <if test="query.specialtyPersonId != null">
                AND t.specialtyPersonId = #{query.specialtyPersonId}
            </if>
            <if test="query.specialtyPersonName != null and query.specialtyPersonName != ''">
                AND t.specialtyPersonName LIKE CONCAT('%', #{query.specialtyPersonName}, '%')
            </if>
            <if test="query.engineeringPhase != null and query.engineeringPhase != ''">
                AND t.engineeringPhase = #{query.engineeringPhase}
            </if>
            <if test="query.node != null and query.node != ''">
                AND t.node = #{query.node}
            </if>
            <if test="query.approveState != null">
                AND t.approveState = #{query.approveState}
            </if>
            <if test="query.createId != null">
                AND t.createId = #{query.createId}
            </if>
            <if test="query.projectName != null">
                AND p.projectName like concat( '%',#{query.projectName},'%')
            </if>
            <if test="query.isShowAll == false">
                AND (
                 t.specialtyPersonId =#{query.currentLoginUser.id}
                or t.createId=#{query.currentLoginUser.id}
                <if test="query.DesignDirectorDesignProjectIds != null and query.DesignDirectorDesignProjectIds.size() >0">
                    or EXISTS (
                    SELECT 1
                    FROM (VALUES
                    <foreach collection="query.DesignDirectorDesignProjectIds" item="designProjectId" separator=",">
                        (#{designProjectId})
                    </foreach>
                    ) AS temp(id)
                    WHERE temp.id = t.designProjectId
                    )
                </if>
                <if test="query.applyServiceRecordIds != null and query.applyServiceRecordIds.size()>0 ">
                    or EXISTS (
                    SELECT 1
                    FROM (VALUES
                    <foreach collection="query.applyServiceRecordIds" item="id" separator=",">
                        (#{id})
                    </foreach>
                    ) AS temp(id)
                    WHERE temp.id = t.id
                    )
                </if>
                <if test="query.signServiceRecordIds != null and query.signServiceRecordIds.size()>0 ">
                    or EXISTS (
                    SELECT 1
                    FROM (VALUES
                    <foreach collection="query.signServiceRecordIds" item="id" separator=",">
                        (#{id})
                    </foreach>
                    ) AS temp(id)
                    WHERE temp.id = t.id
                    )
                </if>
                )
            </if>
        </where>
        ) res
        where 1=1
        <if test="query.supervisorApplyNo != null and query.supervisorApplyNo != ''">
            AND res.supervisorApplyNo LIKE CONCAT('%', #{query.supervisorApplyNo}, '%')
        </if>
        <if test="query.problemFeedbackNo != null and query.problemFeedbackNo != ''">
            AND res.problemFeedbackNo LIKE CONCAT('%', #{query.problemFeedbackNo}, '%')
        </if>
    </sql>

    <select id="getMaxApplyNo" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord">
        SELECT MAX(applyNo) applyNo from DFDW_T_Design_FieldServiceRecord  where applyNo like '%' + #{applyNo} + '%'
    </select>
    <select id="getFwjlAssociationList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord">
        SELECT t.*, p.projectName, p.designDirectorUserId
        FROM DFDW_T_Design_FieldServiceRecord t
        left join DFDW_T_Design_Project p on p.id = t.designProjectId
        left join DFDW_T_Design_ServiceAssociation sa on sa.serviceRecordId = t.id
        where t.designProjectId = #{query.designProjectId}
          and (sa.supervisorApplyId is null <if test="query.id != null">or sa.supervisorApplyId = #{query.id}</if>)
        <if test="query.specialtyPersonId != null">
            AND t.specialtyPersonId = #{query.specialtyPersonId}
        </if>
        <if test="query.queryUserId != null">
            AND ((p.designDirectorUserId =#{query.queryUserId} and t.approveState=2)
            or t.specialtyPersonId =#{query.queryUserId})
        </if>
        and t.specialtyPersonId in (select designUserId from DFDW_T_Design_SupervisorApplySpecialty where supervisorApplyId = #{query.id})
        order by t.id desc
    </select>

    <select id="getSignServiceRecordIds" resultType="java.lang.Integer">
        SELECT
            serviceRecordId
        FROM
            ( SELECT * FROM DFDW_T_Design_FieldProblemFeedback WHERE signerUserId = #{userId} ) AS feedback
                LEFT JOIN DFDW_T_Design_ServiceAssociation ass ON ass.problemFeedbackId = feedback.id
        WHERE
            serviceRecordId IS NOT NULL
    </select>
</mapper>
