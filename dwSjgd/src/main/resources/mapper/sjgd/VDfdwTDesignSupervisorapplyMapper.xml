<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.VDfdwTDesignSupervisorapplyMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignSupervisorapply">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="designProjectId" column="designProjectId" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="designUnit" column="designUnit" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="specialty" column="specialty" jdbcType="VARCHAR"/>
            <result property="applyArriveDate" column="applyArriveDate" jdbcType="DATE"/>
            <result property="applyUserId" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyUserName" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="replyArriveDays" column="replyArriveDays" jdbcType="INTEGER"/>
            <result property="signerUserId" column="signerUserId" jdbcType="INTEGER"/>
            <result property="signerUserName" column="signerUserName" jdbcType="VARCHAR"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
            <result property="updaterId" column="updaterId" jdbcType="INTEGER"/>
            <result property="lcDefineID" column="lcDefineID" jdbcType="INTEGER"/>
            <result property="lcName" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="isMany" column="isMany" jdbcType="INTEGER"/>
            <result property="lcJdmc" column="lcJdmc" jdbcType="VARCHAR"/>
            <result property="lcJdid" column="lcJdid" jdbcType="INTEGER"/>
            <result property="lcIsback" column="lcIsback" jdbcType="INTEGER"/>
            <result property="lcTojdid" column="lcTojdid" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,designProjectId,applyNo,
        designUnit,content,specialty,
        applyArriveDate,applyUserId,applyUserName,
        replyArriveDays,signerUserId,signerUserName,
        approveState,createTime,createId,
        creator,updater,updateTime,
        deleted,updaterId,lcDefineID,
        lcName,ywID,sendPerson,
        sendPersonZgh,AllPersonZgh,isMany,
        lcJdmc,lcJdid,lcIsback,
        lcTojdid,number,BXType
    </sql>
</mapper>
