<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignSupervisorapplyspecialtyMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="supervisorApplyId" column="supervisorApplyId" jdbcType="INTEGER"/>
            <result property="specialty" column="specialty" jdbcType="VARCHAR"/>
            <result property="designUserId" column="designUserId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supervisorApplyId,specialty,
        designUserId,createTime,createId
    </sql>

    <select id="getSpecialtyPersonListByYwId" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty">
        select s.*,p.telephone from DFDW_T_Design_SupervisorApplySpecialty s
        left join DFDW_T_Design_Person p on s.designUserId = p.srcUserId and p.deleted = '0'
        where p.state =1 and s.supervisorApplyId = #{ywId}
    </select>


    <select id="getPersonListBySupervisorApplyId"
            resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty">
        select t2.label as specialtyName,t3.*, t.designUserId
        from DFDW_T_Design_SupervisorApplySpecialty t
        left join (select  * from DFDW_DICT_DATA where dict_type = 'sjgd-specialty') t2 on t2.value = t.specialty
        LEFT JOIN DFDW_T_Design_Person t3 on t3.srcUserId = t.designUserId
        <where>
             t3.deleted = '0'
            <if test="query.supervisorApplyId != null">
                and t.supervisorApplyId = #{query.supervisorApplyId}
            </if>
        </where>
        order by t.createTime desc



    </select>


</mapper>
