<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignSupervisorapplyMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="designProjectId" column="designProjectId" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="designUnit" column="designUnit" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="specialty" column="specialty" jdbcType="VARCHAR"/>
            <result property="applyArriveDate" column="applyArriveDate" jdbcType="DATE"/>
            <result property="applyUserId" column="applyUserId" jdbcType="INTEGER"/>
            <result property="applyUserName" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="replyArriveDays" column="replyArriveDays" jdbcType="INTEGER"/>
            <result property="signerUserId" column="signerUserId" jdbcType="INTEGER"/>
            <result property="signerUserName" column="signerUserName" jdbcType="VARCHAR"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,designProjectId,applyNo,
        designUnit,content,specialty,
        applyArriveDate,applyUserId,applyUserName,
        replyArriveDays,signerUserId,signerUserName,
        approveState,createTime,createId
    </sql>

    <select id="listPage" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
        SELECT t2.projectName,t.* ,(select count(serviceRecordId) from DFDW_T_Design_ServiceAssociation where supervisorApplyId = t.id) as serviceRecordCount
        FROM DFDW_T_Design_SupervisorApply t
        LEFT JOIN DFDW_T_Design_Project t2 on  t2.id = t.designProjectId

        where
            t.deleted = 0

            <if test="query.projectName != '' and query.projectName != null">
                and t2.projectName like concat('%',#{query.projectName},'%')
            </if>

            <if test="query.applyNo !='' and query.applyNo !=null ">
                and t.applyNo like concat('%',#{query.applyNo},'%')
            </if>
            <if test="query.approveState != -1 ">
                and t.approveState = #{query.approveState}
            </if>
            <if test="query.isShowAll == false ">
                and (
                t.applyUserId = #{query.currentLoginUser.id}
                or t.createId=#{query.currentLoginUser.id}
                OR EXISTS (
                SELECT 1
                FROM DFDW_T_Design_SupervisorApplySpecialty t3
                WHERE t3.supervisorApplyId = t.id  AND t3.designUserId = #{query.currentLoginUser.id} and t.approveState = 2
                )
                OR (t.signerUserId = #{query.currentLoginUser.id} AND t.approveState = 2 )
                <if test="query.DesignDirectorDesignProjectIds != null and query.DesignDirectorDesignProjectIds.size() >0">
                    or EXISTS (
                    SELECT 1
                    FROM (VALUES
                    <foreach collection="query.DesignDirectorDesignProjectIds" item="designProjectId" separator=",">
                        (#{designProjectId})
                    </foreach>
                    ) AS temp(id)
                    WHERE temp.id = t.designProjectId
                    )
                </if>
                )
            </if>
        order by t.id desc
    </select>

    <select id="selectInfoById" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
        SELECT t2.projectName,t.* FROM DFDW_T_Design_SupervisorApply t
        LEFT JOIN DFDW_T_Design_Project t2 on  t2.id = t.designProjectId
        where t.id =#{id}
    </select>

    <select id="getMaxApplyNo" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
        SELECT MAX(applyNo) applyNo from DFDW_T_Design_SupervisorApply  where applyNo like '%' + #{applyNo} + '%'

    </select>
    <select id="queryList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
        SELECT
            t.id,
            t.applyNo,
            t2.projectName,
            t.designUnit,
            t.applyArriveDate as applyArriveDateStr,
            t.applyUserName,
            t.signerUserName,
            t.replyArriveDays,
            t.content,
            CASE t.approveState
                WHEN 0 THEN '未提交'
                WHEN 1 THEN '审批中'
                WHEN 2 THEN '已审批'
                WHEN 3 THEN '已驳回'
                WHEN 4 THEN '流程终止'
                ELSE '未知状态'
                END as approveStateStr,

            STUFF((
                      SELECT '；' + t5.label + '-' + t4.userName
                      FROM DFDW_T_Design_SupervisorApplySpecialty t3_inner
                               LEFT JOIN DFDW_T_Design_Person t4
                                         ON t4.srcUserId = t3_inner.designUserId
                               LEFT JOIN DFDW_DICT_DATA t5
                                         ON t5.[value] = t3_inner.specialty
                                             AND t5.dict_type = 'sjgd-specialty'
                      WHERE t3_inner.supervisorApplyId = t.id and t4.deleted = '0'
                      FOR XML PATH('')
                  ), 1, 1, '') as specialtyUsers,
            STUFF((
                      SELECT '、' + d.label
                      FROM DFDW_DICT_DATA d
                      WHERE d.dict_type = 'sjgd-specialty'
                        AND t.specialty LIKE '%' + d.value + '%'
                      FOR XML PATH('')
                  ), 1, 1, '') as specialtyLabels
        ,(select count(serviceRecordId) from DFDW_T_Design_ServiceAssociation where supervisorApplyId = t.id) as serviceRecordCount
        FROM DFDW_T_Design_SupervisorApply t
                 LEFT JOIN DFDW_T_Design_Project t2
                           ON t2.id = t.designProjectId
        where
        t.deleted = 0

        <if test="query.projectName != '' and query.projectName != null">
            and t2.projectName like concat('%',#{query.projectName},'%')
        </if>

        <if test="query.applyNo !='' and query.applyNo !=null ">
            and t.applyNo like concat('%',#{query.applyNo},'%')
        </if>
        <if test="query.approveState != -1 ">
            and t.approveState = #{query.approveState}
        </if>
        <if test="query.applyUserId != null ">
            and (
            t.applyUserId = #{query.applyUserId}
            OR EXISTS (
            SELECT 1
            FROM DFDW_T_Design_SupervisorApplySpecialty t6
            WHERE t3.supervisorApplyId = t.id  AND t6.designUserId = #{query.applyUserId} and t.approveState = 2
            )
            or
            (   t2.projectDirectorUserId = #{query.applyUserId}  and t.approveState = 2)
            OR (t.signerUserId = #{query.applyUserId} AND t.approveState = 2 )
            )
        </if>

        GROUP BY
            t.id,
            t.applyNo,
            t2.projectName,
            t.designUnit,
            t.applyArriveDate,
            t.applyUserName,
            t.signerUserName,
            t.replyArriveDays,
            t.content,
            t.approveState,
            t.specialty
        ORDER BY t.id desc



    </select>
    <select id="getFwsqAssociationList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
        select res.* from (
             SELECT t2.projectName,t.* , (select count(*) from DFDW_T_Design_SupervisorApplySpecialty where supervisorApplyId = t.Id and designUserId = #{query.fieldServiceRecordDesignUserId} ) designUserCount
             FROM DFDW_T_Design_SupervisorApply t
             LEFT JOIN DFDW_T_Design_Project t2 on t2.id = t.designProjectId
             where t.deleted = 0 and t.designProjectId = #{query.designProjectId}
             <if test="query.applyUserId != null ">
                 and (t.applyUserId = #{query.applyUserId}
                 or EXISTS (SELECT 1 FROM DFDW_T_Design_SupervisorApplySpecialty t3 where t3.supervisorApplyId = t.id AND
                 t3.designUserId = #{query.applyUserId} and t.approveState = 2)
                 or (t2.projectDirectorUserId = #{query.applyUserId} and t.approveState = 2)
                 or (t.signerUserId = #{query.applyUserId} AND t.approveState = 2)
                 )
             </if>
        )res
        where res.designUserCount >0
        order by res.id desc
    </select>

    <select id="selectPersonUserPhoneById" resultType="java.lang.String">
        SELECT
            Telephone AS telephone
        FROM Person
        WHERE Id = #{id}
    </select>
</mapper>
