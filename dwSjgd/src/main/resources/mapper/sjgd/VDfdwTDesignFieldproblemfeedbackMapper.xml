<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.VDfdwTDesignFieldproblemfeedbackMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignFieldproblemfeedback">
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="designProjectId" column="designProjectId" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="designUnit" column="designUnit" jdbcType="VARCHAR"/>
            <result property="applyDate" column="applyDate" jdbcType="DATE"/>
            <result property="specialtyPersonId" column="specialtyPersonId" jdbcType="VARCHAR"/>
            <result property="engineeringPhase" column="engineeringPhase" jdbcType="VARCHAR"/>
            <result property="node" column="node" jdbcType="VARCHAR"/>
            <result property="fieldCondition" column="fieldCondition" jdbcType="VARCHAR"/>
            <result property="problemsAndMeasures" column="problemsAndMeasures" jdbcType="VARCHAR"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
            <result property="lcDefineID" column="lcDefineID" jdbcType="INTEGER"/>
            <result property="lcName" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="isMany" column="isMany" jdbcType="INTEGER"/>
            <result property="lcJdmc" column="lcJdmc" jdbcType="VARCHAR"/>
            <result property="lcJdid" column="lcJdid" jdbcType="INTEGER"/>
            <result property="lcIsback" column="lcIsback" jdbcType="INTEGER"/>
            <result property="lcTojdid" column="lcTojdid" jdbcType="VARCHAR"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,designProjectId,applyNo,
        designUnit,applyDate,specialtyPersonId,
        engineeringPhase,node,fieldCondition,
        problemsAndMeasures,approveState,createTime,
        createId,lcDefineID,lcName,
        ywID,sendPerson,sendPersonZgh,
        AllPersonZgh,isMany,lcJdmc,
        lcJdid,lcIsback,lcTojdid,
        number,BXType
    </sql>
</mapper>
