<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.VDfdwTDesignflowMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignflow">
            <result property="ywType" column="ywType" jdbcType="INTEGER"/>
            <result property="id" column="id" jdbcType="INTEGER"/>
            <result property="designProjectId" column="designProjectId" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="personId" column="personId" jdbcType="INTEGER"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="lcDefineID" column="lcDefineID" jdbcType="INTEGER"/>
            <result property="lcName" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="lcJdmc" column="lcJdmc" jdbcType="VARCHAR"/>
            <result property="lcJdid" column="lcJdid" jdbcType="INTEGER"/>
            <result property="lcIsback" column="lcIsback" jdbcType="INTEGER"/>
            <result property="projectName" column="projectName" jdbcType="VARCHAR"/>
            <result property="userName" column="userName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ywType,id,designProjectId,
        applyNo,personId,approveState,
        lcDefineID,lcName,ywID,
        sendPerson,sendPersonZgh,AllPersonZgh,
        lcJdmc,lcJdid,lcIsback,
        projectName,userName
    </sql>
</mapper>
