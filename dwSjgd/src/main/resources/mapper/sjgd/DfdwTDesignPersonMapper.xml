<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignPersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userName" column="userName" jdbcType="VARCHAR"/>
            <result property="specialty" column="specialty" jdbcType="VARCHAR"/>
            <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
            <result property="srcUserId" column="srcUserId" jdbcType="INTEGER"/>
            <result property="deptId" column="deptId" jdbcType="INTEGER"/>
            <result property="deptName" column="deptName" jdbcType="VARCHAR"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,userName,specialty,
        telephone,srcUserId,deptId,
        deptName,state,createTime,
        createId
    </sql>
    <select id="selectAllList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson">
        select t2.label as specialtyName , t.* from DFDW_T_Design_Person t
        left join (select * from  DFDW_DICT_DATA where dict_type ='sjgd-specialty') t2 on t2.value = t.specialty
        <where>
            t.deleted = '0'
            <if test="designPerson.userName != null">
                AND t.userName LIKE CONCAT('%', #{designPerson.userName}, '%')
            </if>
            <if test="designPerson.specialty != null">
                AND t.specialty = #{designPerson.specialty}
            </if>
            <if test="designPerson.telephone != null">
                AND t.telephone LIKE CONCAT('%', #{designPerson.telephone}, '%')
            </if>
            <if test="designPerson.hasSrcUserId != null">
                <choose>
                    <when test="designPerson.hasSrcUserId">
                        AND t.srcUserId IS NOT NULL
                    </when>
                    <otherwise>
                        AND t.srcUserId IS NULL
                    </otherwise>
                </choose>
            </if>
            <if test="designPerson.deptId != null">
                AND t.deptId = #{designPerson.deptId}
            </if>
            <if test="designPerson.state != null">
                AND t.state = #{designPerson.state}
            </if>
        </where>
        ORDER BY t.userName ASC
    </select>
    <select id="selectPersonList" resultType="com.soft.gcc.common.person.entity.Vperson">
        select p.*
        from vPerson p
        where (p.TopGroupId = 474 or p.Id in (select rp.PersonId from RolePerson rp left join Role r on rp.RoleId = r.Id where r.RoleName = N'协同办公-设计工代-设计工代人员'))
            <if test="userName != null">
                and p.RealName = #{userName}
            </if>
            <if test="telephone != null">
                and p.Telephone = #{telephone}
            </if>
    </select>
    <select id="getPersonPageList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson">
        select * from (
            select p.*
            from DFDW_T_Design_Person p
            left join RolePerson rp on p.srcUserId = rp.PersonId
            left join Role r on rp.RoleId = r.Id
            <where>
                p.deleted = '0'
                <if test="designPerson.roleName != null and designPerson.roleName != ''">
                    and r.RoleName = #{designPerson.roleName}
                </if>
                <if test="designPerson.userName != null and designPerson.userName != ''">
                    and p.userName LIKE CONCAT('%', #{designPerson.userName}, '%')
                </if>
                <if test="designPerson.specialty != null and designPerson.specialty != ''">
                    and p.specialty = #{designPerson.specialty}
                </if>
            </where>
            <if test="designPerson.roleName == null or designPerson.roleName == ''">
                group by p.id, p.userName, p.specialty, p.telephone, p.srcUserId, p.deptId, p.deptName, p.state, p.createTime,
                p.createId, p.updateTime, p.updateId, p.deleted, p.updater, p.creator
            </if>

        ) a
        order by a.userName
    </select>
    <select id="selectPersonRoleById" resultType="com.soft.gcc.common.role.entity.Role">
        select r.* from Role r
        right join RolePerson p on p.RoleId = r.Id and p.PersonId = #{designPerson.srcUserId}
        where r.RoleName like N'%协同办公-设计工代%'
        group by r.Id, r.RoleName, r.AdminGroupIds, r.IsHide, r.RoleKind
    </select>
    <select id="selectRoleBySjgd" resultType="com.soft.gcc.common.role.entity.Role">
        select * from Role
        where RoleName like N'%协同办公-设计工代%'
          and RoleName != N'协同办公-设计工代-总管理'
        order by RoleName
    </select>

    <select id="selectPersonListByIds" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson">
        SELECT srcUserId, userName, MAX(telephone) as telephone, MAX(specialtyName) as specialtyName
        FROM (
        SELECT t.srcUserId, t.userName, t.telephone, t2.label as specialtyName
        FROM DFDW_T_Design_Person t
        LEFT JOIN (SELECT * FROM DFDW_DICT_DATA WHERE dict_type ='sjgd-specialty') t2
        ON t2.value = t.specialty
        WHERE srcUserId in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        UNION ALL

        SELECT
        Id AS srcUserId,
        RealName AS userName,
        Telephone AS telephone,
        '' as specialtyName
        FROM Person
        WHERE Id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ) temp
        GROUP BY srcUserId, userName



    </select>
</mapper>
