<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignProjectMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="projectId" column="projectId" jdbcType="INTEGER"/>
            <result property="projectName" column="projectName" jdbcType="VARCHAR"/>
            <result property="projectDirectorUserId" column="projectDirectorUserId" jdbcType="INTEGER"/>
            <result property="projectDirectorUserName" column="projectDirectorUserName" jdbcType="VARCHAR"/>
            <result property="projectSpecialOverseeUserId" column="projectSpecialOverseeUserId" jdbcType="INTEGER"/>
            <result property="projectSpecialOverseeUserName" column="projectSpecialOverseeUserName" jdbcType="VARCHAR"/>
            <result property="designDirectorUserId" column="designDirectorUserId" jdbcType="INTEGER"/>
            <result property="designDirectorUserName" column="designDirectorUserName" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
    </resultMap>

<!--    <resultMap id="VProjectTMap" type="com.soft.gcc.xtbg.sjgd.domain.VProjectT">-->
<!--        <id property="projectId" column="project_id" jdbcType="BIGINT"/>-->
<!--        <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>-->
<!--        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>-->
<!--        <result property="projectShortName" column="project_short_name" jdbcType="VARCHAR"/>-->
<!--        <result property="projectPlace" column="project_place" jdbcType="VARCHAR"/>-->
<!--        <result property="survey" column="survey" jdbcType="VARCHAR"/>-->
<!--        <result property="supervisionScopen" column="supervision_scopen" jdbcType="VARCHAR"/>-->
<!--        <result property="supervisorCompanyId" column="supervisor_company_id" jdbcType="BIGINT"/>-->
<!--        <result property="projectGroupLeaderId" column="project_group_leader_id" jdbcType="BIGINT"/>-->
<!--        <result property="projectGroupLeaderIdTo" column="project_group_leader_id_to" jdbcType="VARCHAR"/>-->
<!--        <result property="directorId" column="director_id" jdbcType="BIGINT"/>-->
<!--        <result property="directorIdTo" column="director_id_to" jdbcType="VARCHAR"/>-->
<!--        <result property="ownerUnitId" column="owner_unit_id" jdbcType="BIGINT"/>-->
<!--        <result property="designUnitId" column="design_unit_id" jdbcType="BIGINT"/>-->
<!--        <result property="constructionUnitId" column="construction_unit_id" jdbcType="BIGINT"/>-->
<!--        <result property="isSubPackage" column="is_sub_package" jdbcType="VARCHAR"/>-->
<!--        <result property="constructionSubUnitId" column="construction_sub_unit_id" jdbcType="BIGINT"/>-->
<!--        <result property="constructionSubUnitIdTo" column="construction_sub_unit_id_to" jdbcType="VARCHAR"/>-->
<!--        <result property="planStartDate" column="plan_start_date" jdbcType="TIMESTAMP"/>-->
<!--        <result property="planFinishDate" column="plan_finish_date" jdbcType="TIMESTAMP"/>-->
<!--        <result property="physicalStartDate" column="physical_start_date" jdbcType="TIMESTAMP"/>-->
<!--        <result property="physicalFinishDate" column="physical_finish_date" jdbcType="TIMESTAMP"/>-->
<!--        <result property="remark" column="remark" jdbcType="VARCHAR"/>-->
<!--        <result property="status" column="status" jdbcType="VARCHAR"/>-->
<!--        <result property="createBy" column="create_by" jdbcType="BIGINT"/>-->
<!--        <result property="creationDate" column="creation_date" jdbcType="TIMESTAMP"/>-->
<!--        <result property="lastUpdateBy" column="last_update_by" jdbcType="BIGINT"/>-->
<!--        <result property="lastUpdateDate" column="last_update_date" jdbcType="TIMESTAMP"/>-->
<!--        <result property="versionNumber" column="version_number" jdbcType="BIGINT"/>-->
<!--        <result property="enable" column="enable" jdbcType="VARCHAR"/>-->
<!--        <result property="projectLongitude" column="project_longitude" jdbcType="DOUBLE"/>-->
<!--        <result property="projectLatitude" column="project_latitude" jdbcType="DOUBLE"/>-->
<!--        <result property="itudeFlag" column="itudeFlag" jdbcType="VARCHAR"/>-->
<!--        <result property="statusStop" column="statusStop" jdbcType="INTEGER"/>-->
<!--        <result property="network" column="network" jdbcType="INTEGER"/>-->
<!--        <result property="projectGroupCode" column="project_group_code" jdbcType="VARCHAR"/>-->
<!--    </resultMap>-->



    <sql id="Base_Column_List">
        id,projectId,projectName,
        projectDirectorUserId,projectDirectorUserName,projectSpecialOverseeUserId,
        projectSpecialOverseeUserName,designDirectorUserId,designDirectorUserName,
        createTime,createId
    </sql>
    <select id="getPageList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject">
        <include refid="BaseSelect"></include>
        ORDER BY t.id DESC
    </select>
    <select id="getVProjectPageList" resultType="com.soft.gcc.xtbg.sjgd.domain.VProjectT">
      <include refid="projectBaseSql"></include>
    </select>
    <select id="getVProjectAllList" resultType="com.soft.gcc.xtbg.sjgd.domain.VProjectT">
        <include refid="projectBaseSql"></include>
    </select>
    <select id="getVProjectTypeList" resultType="com.soft.gcc.xtbg.sjgd.domain.ProjectTypeT">
        SELECT
            project_type_id AS projectTypeId,
            project_id AS projectId,
            project_type AS projectType,
            project_name AS projectName,
            construction_unit_id AS constructionUnitId,
            profession_supervisor_id AS professionSupervisorId,
            profession_supervisor_id_to AS professionSupervisorIdTo,
            construction_id AS constructionId,
            supervisor_id AS supervisorId,
            supervisor_id_to AS supervisorIdTo,
            full_time_person_id AS fullTimePersonId,
            full_time_person_id_to AS fullTimePersonIdTo,
            start_date AS startDate,
            finish_date AS finishDate,
            is_last_finish AS isLastFinish,
            status AS status,
            create_by AS createBy,
            creation_date AS creationDate,
            last_update_by AS lastUpdateBy,
            last_update_date AS lastUpdateDate,
            version_number AS versionNumber,
            enable AS enable,
            old_status AS oldStatus,
            overtime_modify_flag AS overtimeModifyFlag,
            subpackage_unit_id AS subpackageUnitId,
            subpackage_unit_id_to AS subpackageUnitIdTo,
            is_project_start AS isProjectStart,
            project_start_begin_date AS projectStartBeginDate,
            project_start_end_date AS projectStartEndDate,
            project_schedule AS projectSchedule
        FROM project_type_t
        <if test="project_id!=null">
            WHERE project_id = #{query.projectId}
        </if>
    </select>
    <select id="getLeaderList" resultType="com.soft.gcc.xtbg.sjgd.domain.LeaderPerson">
        SELECT p.realname,p.loginname,p.id, 'professionSupervisorId' as leaderType
        FROM person p
        WHERE id in (
            SELECT distinct src_user_id
            FROM V_GCJL_SUPERVISOR_T
            WHERE supervisor_id in (
                SELECT profession_Supervisor_Id
                FROM v_project_type_t
                WHERE project_id = #{project.projectId}
            )
        )
        UNION ALL
        SELECT p.realname,p.loginname,p.id, 'directorId' as leaderType
        FROM person p
        WHERE id in (
            SELECT distinct src_user_id
            FROM V_GCJL_SUPERVISOR_T
            WHERE supervisor_id in (
                SELECT director_id
                FROM v_project_t
                WHERE project_id = #{project.projectId}
            )
        )
    </select>
    <select id="hasDesignProjectRole" resultType="java.lang.Boolean">
        SELECT CASE
                   WHEN EXISTS (
                       SELECT 1
                       FROM [dbo].[RolePerson] rp
        LEFT JOIN Role r ON rp.RoleId = r.id
        WHERE rp.PersonId = #{currentUserId}
        AND r.rolename = #{roleName}
                   )
                       THEN 'true'
                   ELSE 'false'
                   END AS HasRole
    </select>
    <select id="getPageListUserId" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject">
        SELECT t.*
        FROM DFDW_T_Design_Project t
        <where>
            t.deleted = 0
            <if test="query.projectId != null">
                AND t.projectId = #{query.projectId}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND t.projectName LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.projectDirectorUserId != null">
                AND t.projectDirectorUserId = #{query.projectDirectorUserId}
            </if>
            <if test="query.projectDirectorUserName != null and query.projectDirectorUserName != ''">
                AND t.projectDirectorUserName LIKE CONCAT('%', #{query.projectDirectorUserName}, '%')
            </if>
            <if test="query.designDirectorUserId != null">
                AND t.designDirectorUserId = #{query.designDirectorUserId}
            </if>
            <if test="query.projectSpecialOverseeUserId != null">
                AND t.projectSpecialOverseeUserId = #{query.projectSpecialOverseeUserId}
            </if>
            <if test="query.createId != null">
                AND t.createId = #{query.createId}
            </if>
            <if test="userId != null">
                AND (EXISTS (
                SELECT 1 FROM DFDW_T_Design_ProjectSpecialty dps
                WHERE dps.designProjectId = t.id
                AND dps.designUserId = #{userId}
                )
                OR t.createId = #{userId}
                OR t.projectDirectorUserId =#{userId}
                or t.projectSpecialOverseeUserId = #{userId}
                OR t.designDirectorUserId = #{userId}
                OR CHARINDEX(',' + CAST(#{userId} AS VARCHAR) + ',', ',' + t.memberIds + ',') > 0
                OR CHARINDEX(',' + CAST(#{userId} AS VARCHAR) + ',', ',' + t.constructionUnitUserIds + ',') > 0
                )
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="getDownLoadList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject">
        SELECT t.*,
        STUFF((
        SELECT '；' + t5.label + '-' + t4.userName
        FROM DFDW_T_Design_ProjectSpecialty t3_inner
        LEFT JOIN DFDW_T_Design_Person t4  ON t4.srcUserId = t3_inner.designUserId
        LEFT JOIN DFDW_DICT_DATA t5 ON t5.[value] = t3_inner.specialty AND t5.dict_type = 'sjgd-specialty'
        WHERE t3_inner.designProjectId = t.id and t4.deleted = '0'
        FOR XML PATH('')
        ), 1, 1, '') as specialtyUsers
        FROM DFDW_T_Design_Project t
        <where>
            t.deleted = 0
            <if test="query.projectId != null">
                AND t.projectId = #{query.projectId}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND t.projectName LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.projectDirectorUserId != null">
                AND t.projectDirectorUserId = #{query.projectDirectorUserId}
            </if>
            <if test="query.projectDirectorUserName != null and query.projectDirectorUserName != ''">
                AND t.projectDirectorUserName LIKE CONCAT('%', #{query.projectDirectorUserName}, '%')
            </if>
            <if test="query.designDirectorUserId != null">
                AND t.designDirectorUserId = #{query.designDirectorUserId}
            </if>
            <if test="query.projectSpecialOverseeUserId != null">
                AND t.projectSpecialOverseeUserId = #{query.projectSpecialOverseeUserId}
            </if>
            <if test="query.createId != null">
                AND t.createId = #{query.createId}
            </if>
            <if test="query.designUserId != null">
                AND EXISTS (
                SELECT 1 FROM DFDW_T_Design_ProjectSpecialty dps
                WHERE dps.designProjectId = t.id
                AND dps.designUserId = #{query.designUserId }
                )
            </if>

            <if test="userId != null">
                AND (EXISTS (
                SELECT 1 FROM DFDW_T_Design_ProjectSpecialty dps
                WHERE dps.designProjectId = t.id
                AND dps.designUserId = #{userId}
                )
                OR t.createId = #{userId}
                OR t.projectDirectorUserId =#{userId}
                or t.projectSpecialOverseeUserId = #{userId}
                OR t.designDirectorUserId = #{userId}
                )
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <sql id="BaseSelect">
        SELECT t.*
        FROM DFDW_T_Design_Project t
        <where>
            <if test="query.projectId != null">
                AND t.projectId = #{query.projectId}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND t.projectName LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.projectDirectorUserId != null">
                AND t.projectDirectorUserId = #{query.projectDirectorUserId}
            </if>
            <if test="query.projectDirectorUserName != null and query.projectDirectorUserName != ''">
                AND t.projectDirectorUserName LIKE CONCAT('%', #{query.projectDirectorUserName}, '%')
            </if>
            <if test="query.designDirectorUserId != null">
                AND t.designDirectorUserId = #{query.designDirectorUserId}
            </if>
            <if test="query.projectSpecialOverseeUserId != null">
                AND t.projectSpecialOverseeUserId = #{query.projectSpecialOverseeUserId}
            </if>
            <if test="query.createId != null">
                AND t.createId = #{query.createId}
            </if>
        </where>
    </sql>
    <sql id="projectBaseSql">
        SELECT
        project_id AS projectId,
        project_code AS projectCode,
        project_name AS projectName,
        project_short_name AS projectShortName,
        project_place AS projectPlace,
        survey AS survey,
        supervision_scopen AS supervisionScopen,
        supervisor_company_id AS supervisorCompanyId,
        project_group_leader_id AS projectGroupLeaderId,
        project_group_leader_id_to AS projectGroupLeaderIdTo,
        director_id AS directorId,
        director_id_to AS directorIdTo,
        owner_unit_id AS ownerUnitId,
        design_unit_id AS designUnitId,
        construction_unit_id AS constructionUnitId,
        is_sub_package AS isSubPackage,
        construction_sub_unit_id AS constructionSubUnitId,
        construction_sub_unit_id_to AS constructionSubUnitIdTo,
        plan_start_date AS planStartDate,
        plan_finish_date AS planFinishDate,
        physical_start_date AS physicalStartDate,
        physical_finish_date AS physicalFinishDate,
        remark AS remark,
        status AS status,
        create_by AS createBy,
        creation_date AS creationDate,
        last_update_by AS lastUpdateBy,
        last_update_date AS lastUpdateDate,
        version_number AS versionNumber,
        enable AS enable,
        project_longitude AS projectLongitude,
        project_latitude AS projectLatitude,
        itudeFlag AS itudeFlag,
        statusStop AS statusStop,
        network AS network,
        project_group_code AS projectGroupCode
        FROM v_project_t
        WHERE
        status&lt;&gt;'PRODUCTION'
        AND (statusStop&lt;&gt;1 or statusStop is null)
        <if test="query.projectName != null">
            AND( project_name LIKE CONCAT( '%',#{query.projectName},'%') or  project_code LIKE CONCAT( '%',#{query.projectName},'%'))
        </if>
        ORDER BY project_id desc
    </sql>

    <select id="checkUse" resultType="com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignflow">
        select res.* ,pro.projectName from (
               SELECT
                   1 as ywType,
                   a.id,a.designProjectId,a.applyNo
               FROM
                   dbo.DFDW_T_Design_SupervisorApply AS a
               where a.deleted=0
               UNION
               SELECT
                   2 as ywType,
                   a.id,a.designProjectId,a.applyNo
               FROM
                   dbo.DFDW_T_Design_FieldServiceRecord AS a
               UNION
               SELECT
                   3 as ywType,
                   a.id,a.designProjectId,a.applyNo
               FROM
                   dbo.DFDW_T_Design_FieldProblemFeedback AS a
               where a.deleted = 0
        ) res
        left join DFDW_T_Design_Project pro on res.designProjectId = pro.Id
        WHERE
         res.designProjectId in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>
