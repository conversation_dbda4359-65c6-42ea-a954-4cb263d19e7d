<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignServiceassociationMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="supervisorApplyId" column="supervisorApplyId" jdbcType="INTEGER"/>
            <result property="serviceRecordId" column="serviceRecordId" jdbcType="INTEGER"/>
            <result property="problemFeedbackId" column="problemFeedbackId" jdbcType="INTEGER"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supervisorApplyId,serviceRecordId,
        problemFeedbackId,createId,createTime
    </sql>
    <delete id="deleteFwsqByIds">
        delete from DFDW_T_Design_ServiceAssociation
        where id in <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">#{item}</foreach>
        and problemFeedbackId is null;
    </delete>
    <update id="updateFwsqByIds">
        update  DFDW_T_Design_ServiceAssociation set supervisorApplyId = null
        where id in <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">#{item}</foreach>
        and problemFeedbackId is not null;
    </update>
    <delete id="deleteFwjlByIds">
        delete from DFDW_T_Design_ServiceAssociation
        where id in <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">#{item}</foreach>
        and supervisorApplyId is null;
    </delete>
    <update id="updateFwjlByIds">
        update  DFDW_T_Design_ServiceAssociation set problemFeedbackId = null
        where id in <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">#{item}</foreach>
        and supervisorApplyId is not null;
    </update>
</mapper>
