<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignFieldproblemfeedbackMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="designProjectId" column="designProjectId" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="proprietorProjectdepartment" column="proprietorProjectdepartment" jdbcType="VARCHAR"/>
            <result property="fieldCondition" column="fieldCondition" jdbcType="VARCHAR"/>
            <result property="problemsAndMeasures" column="problemsAndMeasures" jdbcType="VARCHAR"/>
            <result property="specialtyPersonId" column="specialtyPersonId" jdbcType="VARCHAR"/>
            <result property="feedbackDate" column="feedbackDate" jdbcType="DATE"/>
            <result property="signerUserId" column="signerUserId" jdbcType="INTEGER"/>
            <result property="signerOpinion" column="signerOpinion" jdbcType="VARCHAR"/>
            <result property="signerDate" column="signerDate" jdbcType="DATE"/>
            <result property="supervisorNotice" column="supervisorNotice" jdbcType="VARCHAR"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="createId" column="createId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,designProjectId,applyNo,
        proprietorProjectdepartment,fieldCondition,problemsAndMeasures,
        specialtyPersonId,feedbackDate,signerUserId,
        signerOpinion,signerDate,supervisorNotice,
        approveState,createTime,createId
    </sql>
    <select id="listPage" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback">
        SELECT t2.projectName,t.feedbackDate as feedbackDateStr,t.signerDate as signerDateStr,
        CASE t.approveState
        WHEN 0 THEN '未提交'
        WHEN 1 THEN '审批中'
        WHEN 2 THEN '已审批'
        WHEN 3 THEN '已驳回'
        WHEN 4 THEN '流程终止'
        ELSE '未知状态'
        END as approveStateStr,
        t.*,
        (	select top 1 a.applyNo from DFDW_T_Design_ServiceAssociation sac
        left join DFDW_T_Design_FieldServiceRecord a on sac.serviceRecordId = a.Id
        where  problemFeedbackId = t.Id) as serviceRecordNo
        FROM DFDW_T_Design_FieldProblemFeedback t
        LEFT JOIN DFDW_T_Design_Project t2 on  t2.id = t.designProjectId
        where t.deleted = '0'
        <if test="query.projectName != '' and query.projectName != null">
            and t2.projectName like concat('%',#{query.projectName},'%')
        </if>

        <if test="query.applyNo !='' and query.applyNo !=null ">
            and t.applyNo like concat('%',#{query.applyNo},'%')
        </if>
        <if test="query.approveState != -1 ">
            and t.approveState = #{query.approveState}
        </if>
        <if test="query.isShowAll ==false ">
            AND (
            t.specialtyPersonId = #{query.currentLoginUser.id}
            OR
            (t.signerUserId = #{query.currentLoginUser.id} AND t.approveState = 2)
            or t.createId= #{query.currentLoginUser.id}
            <if test="query.DesignDirectorDesignProjectIds != null and query.DesignDirectorDesignProjectIds.size() >0">
                or EXISTS (
                SELECT 1
                FROM (VALUES
                <foreach collection="query.DesignDirectorDesignProjectIds" item="designProjectId" separator=",">
                    (#{designProjectId})
                </foreach>
                ) AS temp(id)
                WHERE temp.id = t.designProjectId
                )
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                or t.id in
                <foreach collection="query.ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        order by t.id desc
    </select>

    <select id="getMaxApplyNo" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback">
        SELECT MAX(applyNo) applyNo from DFDW_T_Design_FieldProblemFeedback  where applyNo like '%' + #{applyNo} + '%'

    </select>
    <select id="selectInfoById" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback">
        SELECT t2.projectName,t.* FROM DFDW_T_Design_FieldProblemFeedback t
                                           LEFT JOIN DFDW_T_Design_Project t2 on  t2.id = t.designProjectId
        where t.id =#{id}
    </select>
    <select id="getWtfkAssociationList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback">
        SELECT t2.projectName,t.feedbackDate as feedbackDateStr,t.signerDate as signerDateStr,
               t.*
        FROM DFDW_T_Design_FieldProblemFeedback t
        LEFT JOIN DFDW_T_Design_Project t2 on  t2.id = t.designProjectId
        left join DFDW_T_Design_ServiceAssociation sa on sa.problemFeedbackId = t.id
        where t.deleted = '0' and t.designProjectId = #{query.designProjectId}
        and (sa.id is null <if test="query.id != null">or sa.serviceRecordId = #{query.id}</if>)
        <if test="query.specialtyPersonId != null ">
            AND ( t.specialtyPersonId = #{query.specialtyPersonId} OR (t.signerUserId = #{query.specialtyPersonId} AND t.approveState = 2))
        </if>
        order by t.id desc
    </select>
    <select id="getApplyList" resultType="com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply">
        SELECT t2.projectName,t.* ,(select count(serviceRecordId) from DFDW_T_Design_ServiceAssociation where supervisorApplyId = t.id) as serviceRecordCount
        ,feedback.id as problemFeedbackId
        FROM DFDW_T_Design_SupervisorApply t
        LEFT JOIN DFDW_T_Design_Project t2 on  t2.id = t.designProjectId
        left join DFDW_T_Design_ServiceAssociation service on service.supervisorApplyId = t.id
        left join DFDW_T_Design_FieldProblemFeedback feedback on feedback.id = service.problemFeedbackId
        where
        t.deleted = 0
        and   feedback.id is not null
        <if test="query.isShowAll == false ">
            and (
            t.applyUserId = #{query.currentLoginUser.id}
            or t.createId=#{query.currentLoginUser.id}
            OR EXISTS (
            SELECT 1
            FROM DFDW_T_Design_SupervisorApplySpecialty t3
            WHERE t3.supervisorApplyId = t.id  AND t3.designUserId = #{query.currentLoginUser.id} and t.approveState = 2
            )
            OR (t.signerUserId = #{query.currentLoginUser.id} AND t.approveState = 2 )
            <if test="query.DesignDirectorDesignProjectIds != null">
                or EXISTS (
                SELECT 1
                FROM (VALUES
                <foreach collection="query.DesignDirectorDesignProjectIds" item="designProjectId" separator=",">
                    (#{designProjectId})
                </foreach>
                ) AS temp(id)
                WHERE temp.id = t.designProjectId
                )
            </if>
            )
        </if>
        order by t.id desc
    </select>
</mapper>
