<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.role.mapper.RolepersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.role.entity.Roleperson">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="roleId" column="RoleId" jdbcType="INTEGER"/>
            <result property="personId" column="PersonId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,RoleId,PersonId
    </sql>
</mapper>
