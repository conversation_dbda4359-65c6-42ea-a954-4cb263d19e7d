<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.person.mapper.VpersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.person.entity.Vperson">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="loginname" column="LoginName" jdbcType="VARCHAR"/>
            <result property="loginname2" column="LoginName2" jdbcType="VARCHAR"/>
            <result property="realname" column="RealName" jdbcType="VARCHAR"/>
            <result property="password" column="Password" jdbcType="VARCHAR"/>
            <result property="roleid" column="RoleId" jdbcType="INTEGER"/>
            <result property="telephone" column="Telephone" jdbcType="VARCHAR"/>
            <result property="msgtype" column="MsgType" jdbcType="VARCHAR"/>
            <result property="oa" column="OA" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="pXh" column="P_XH" jdbcType="INTEGER"/>
            <result property="groupid" column="GroupId" jdbcType="INTEGER"/>
            <result property="parentid" column="ParentId" jdbcType="INTEGER"/>
            <result property="parentname" column="ParentName" jdbcType="VARCHAR"/>
            <result property="grouptype" column="GroupType" jdbcType="INTEGER"/>
            <result property="groupname" column="GroupName" jdbcType="VARCHAR"/>
            <result property="groupdesc" column="GroupDesc" jdbcType="VARCHAR"/>
            <result property="topgroupid" column="TopGroupId" jdbcType="INTEGER"/>
            <result property="topgroupname" column="TopGroupName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,LoginName,LoginName2,
        RealName,Password,RoleId,
        Telephone,MsgType,OA,
        type,P_XH,GroupId,
        ParentId,ParentName,GroupType,
        GroupName,GroupDesc,TopGroupId,
        TopGroupName
    </sql>
</mapper>
