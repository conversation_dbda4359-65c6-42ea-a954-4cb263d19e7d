package com.soft.framework.helper;

import com.soft.framework.common.utils.string.StringUtil;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class AesHelper {
    private static byte[] key;

    static {
        try {
            key = "db2139561c9fe068".getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    public static String aesEncodeCBC(String srcstr)
    {
        try
        {
            byte[] data = srcstr.getBytes("UTF-8");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(key);
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ips);

            byte[] bOut = cipher.doFinal(data);
            String rets=new BASE64Encoder().encode(bOut);
            return rets;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    public static String aesDecodeCBC(String encstr)
    {
        try
        {
            byte[] data = new BASE64Decoder().decodeBuffer(encstr);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(key);
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ips);

            byte[] bOut = cipher.doFinal(data);
            String rets= StringUtil.deleteLastChar(new String(bOut,"UTF-8"),'\0');
            return rets;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    public static String assDecrypt(String encstr, String key, String ivString) throws NoSuchPaddingException, InvalidAlgorithmParameterException, NoSuchAlgorithmException, IOException, BadPaddingException, IllegalBlockSizeException, InvalidKeyException {
        try {
            byte[] byteKEY = key.getBytes("UTF-8");
            byte[] byteIV = ivString.getBytes("UTF-8");

            byte[] data = new BASE64Decoder().decodeBuffer(encstr);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(byteIV);
            SecretKeySpec keySpec = new SecretKeySpec(byteKEY, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ips);

            byte[] bOut = cipher.doFinal(data);
            String rets= StringUtil.deleteLastChar(new String(bOut,"UTF-8"),'\0');
            return rets;
        } catch(Exception ex) {
            throw ex;
        }
    }
}
