package com.soft.framework.helper;

import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.AppConfig;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.config.OssConfig;
import com.soft.framework.security.service.TokenService;
import org.springframework.context.ApplicationContext;

import javax.servlet.http.HttpServletRequest;

public final class ConfigHelper {
    private static String TitleInfo;         //标题串
    private static String DBFeatures;        //数据库类型串
    private static String DBSchema;          //数据库标识（mysql适用）
    private static String StaticPath;        //静态资源根路径
    private static String ContextRealPath;   //上下文绝对路径
    private static String ContextPath;       //上下文

    public static String getCurrentToken() {
        String token="";
        HttpServletRequest req= ServletUtils.getRequest();
        TokenService tokenService=SpringUtil.getBean("tokenService",TokenService.class);
        if(tokenService!=null)
        {
            token=tokenService.getToken(req);
            if(StringUtil.IsNullOrEmpty(token))
            {
                token=getCurrentUUToken();
            }
            return (token==null)?"":token;
        }
        return token;
    }

    public static String getCurrentUUTokenString() {
        String ctoken="";
        HttpServletRequest request= ServletUtils.getRequest();
        ctoken = request.getHeader("ctoken");
        if (ctoken == "" || ctoken == null) {
            ctoken = request.getParameter("ctoken");
        }
        if (ctoken == "" || ctoken == null) {
            ctoken = request.getHeader("token");
        }
        if (ctoken == "" || ctoken == null) {
            ctoken = request.getParameter("token");
        }
        return ctoken;
    }

    public static String getCurrentUUToken() {
        String utoken=getCurrentUUTokenString();
        return utoken;
    }

    public static Integer getServerPort() {
        return AppConfig.getPort();
    }

    public static String getWhiteList() {
        return BaseConfig.getWhiteList();
    }

    public static String getCachePath() {
        return BaseConfig.getCachePath();
    }

    public static String getReportConnStr() {
        return BaseConfig.getReportConnStr();
    }

    public static String getDBFeatures() {
        return DBFeatures;
    }

    public static String getDBSchema() {
        return DBSchema;
    }

    public static String getSysLogPath() {
        return BaseConfig.getLogPath()+"SysLog/";
    }

    public static String getProfile() {
        return BaseConfig.getProfile();
    }

    public static String getSqlLogPath() {
        return BaseConfig.getLogPath()+"SqlLog/";
    }

    public static String getOcciLogPath() {
        return BaseConfig.getLogPath()+"OcciLog/";
    }

    public static String getContextRealPath() {
        return ContextRealPath;
    }

    public static String getContextPath() {
        return ContextPath;
    }

    public static boolean isOSSOpen() {
        return OssConfig.getOpenFlag().equals("true");
    }

    public static boolean isRecDBLog() {
        return (BaseConfig.getSqlTg()==1);
    }

    public static boolean isLogFlag() {
        return (BaseConfig.getLogFlag()==1);
    }

    public static boolean isSmsFlag() {
        return (BaseConfig.getSmsFlag()==1);
    }

    public static boolean isNoticeSmsFlag() {
        return (BaseConfig.getNoticeSmsFlag()==1);
    }

    public static String getSystemMark() {
        return BaseConfig.getSystemMark();
    }

    public static boolean isUIWMask() {
        return (BaseConfig.getUIWMask()==1);
    }

    public static boolean isEPWMask() {
        return (BaseConfig.getEPWMask()==1);
    }

    public static boolean isLoadDBConf() {
        return (BaseConfig.getLoadDBConf()==1);
    }

    public static boolean isOcciLog() {
        return (BaseConfig.getOcciLog()==1);
    }

    public static int getOcciSecond() {
        return BaseConfig.getOcciSecond();
    }

    public static String getfSepChar() {
        return BaseConfig.getSeparator();
    }

    public static String getRunMode() {
        return BaseConfig.getRunMode();
    }

    public static String getTitleInfo() {
        return TitleInfo;
    }

    public static String getUploadPath() {
        return BaseConfig.getUploadPath();
    }

    public static String getDownloadPath() {
        return BaseConfig.getDownloadPath();
    }

    public static String getTempPath() {
        return BaseConfig.getTemplatePath();
    }

    public static String getHotupdatePath() {
        return BaseConfig.getHotupdatePath();
    }

    public static String getReportPath() {
        return BaseConfig.getReportPath();
    }

    public static String getModuleId() {
        return BaseConfig.getModuleId();
    }

    public static String getGroupId() {
        return BaseConfig.getGroupId();
    }

    public static String getMobanPath() {
        return BaseConfig.getMobanPath();
    }

    public static String getStaticPath() {
        return StaticPath;
    }

    public static String getExtVersion() {
        return BaseConfig.getExtVersion();
    }

    public static String getOssLocalPath() {
        return BaseConfig.getOssLocalPath();
    }

    public static String getOssRemotePath() {
        return BaseConfig.getOssRemotePath();
    }

    public static int getSessionTimeOut() {
        return BaseConfig.getSessionTimeOut();
    }

    public static int getCompileReport() {
        return BaseConfig.getCompileReport();
    }

    static {
        try {
            initProc();
        } catch (Exception ex) {

        }
    }

    private static void initProc() {
        DBFeatures = "mssql";
        DBSchema = "JTSZHManage";

        FileUtil.CreateDir(getCachePath());
        FileUtil.CreateDir(getTempPath());
        FileUtil.CreateDir(getReportPath());
        FileUtil.CreateDir(getSysLogPath());
        FileUtil.CreateDir(getSqlLogPath());
        FileUtil.CreateDir(getOcciLogPath());
        FileUtil.CreateDir(getDownloadPath());
        FileUtil.CreateDir(getHotupdatePath());

        ApplicationContext context = SpringUtil.getApplicationContext();
        ContextPath = context.getApplicationName();
    }
}
