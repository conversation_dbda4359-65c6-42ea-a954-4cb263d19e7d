package com.soft.framework.helper;

import com.yyszc.extend.DataColumn;
import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataSet;
import com.yyszc.extend.DataTable;
import com.soft.framework.common.features.IDBFeatures;
import com.soft.framework.common.features.MSSqlFeatures;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.config.datasource.DynamicDataSource;
import com.sun.rowset.CachedRowSetImpl;

import javax.sql.RowSet;
import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.*;

/***
 * mysql操作类，其他数据库可以参照修改
 * **/
@SuppressWarnings("unchecked")
public class SqlHelper {
    private IDBFeatures dbFeatures;
    private DynamicDataSource ds=null;

    public SqlHelper() {
        dbFeatures = new MSSqlFeatures();
    }

    public Connection getConnection() {
        Connection conn = null;
        try {
            if(ds==null) {
                ds=(DynamicDataSource) SpringUtil.getBean("dynamicDataSource");
            }
            conn = ds.getConnection();
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
        return conn;
    }

    public boolean closeConnection(Connection conn) {
        try {
            if(conn!=null&&!conn.isClosed()) {
                conn.close();
            }
        } catch (Exception ex) {
            return false;
        }
        return true;
    }

    public RowSet GerRowSet(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            CachedRowSetImpl rowset = new CachedRowSetImpl();
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            rowset.populate(rs);
            rs.close();
            statement.close();
            connection.close();
            return rowset;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public <T> List<T> GetObjectList(Class<T> cl, String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        List<T> list = new ArrayList<T>();
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            ResultSetMetaData rsMeta = rs.getMetaData();
            ArrayList colnamelist = new ArrayList();
            int columnCount = rsMeta.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMeta.getColumnLabel(i);
                colnamelist.add(columnName);
            }
            while (rs.next()) {
                T row = cl.newInstance();

                for (int i = 0; i <colnamelist.size(); i++) {
                    String columnName = colnamelist.get(i).toString();
                    if(columnName.equals("row_num")) {
                        continue;
                    }

                    try{
                        Field f = row.getClass().getDeclaredField(columnName);
                        if(f!=null)
                        {
                            f.setAccessible(true);
                            Object colvalue = rs.getObject(i + 1);

                            if(colvalue==null)
                            {
                                f.set(row, colvalue);
                                continue;
                            }

                            DBHelper.setValue(f, f.getType().getSimpleName(), colvalue.getClass().getSimpleName(), colvalue, row);
                        }
                    }catch(Exception Ex)
                    {

                    }
                }

                list.add(row);
            }
            statement.close();
            connection.close();
            return list;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public <T> List<T> GetObjectList(Class<T> cl, String sql, String orderStr, int pageSize, int currentPage) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        String strsql = dbFeatures.ToPageSql(sql, orderStr, pageSize, currentPage);
        return GetObjectList(cl, strsql);
    }

    public <T> T GetObject(Class<T> cl, String sql) {
        List<T> alist = GetObjectList(cl, sql);
        if (alist!=null&&!alist.isEmpty()) {
            return alist.get(0);
        }
        return null;
    }

    public RowSet GerRowSet(String sql, String orderStr, int pageSize, int currentPage) {
        sql = dbFeatures.ToPageSql(sql, orderStr, pageSize, currentPage);
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            CachedRowSetImpl rowset = new CachedRowSetImpl();
            Statement statement = connection.createStatement(ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = statement.executeQuery(sql);
            rowset.setPageSize(pageSize);
            rowset.populate(rs);
            rs.close();
            statement.close();
            connection.close();
            return rowset;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public Map<String,Object> GetMapForResult(String strsql) {
        Map<String,Object> map = new HashMap<String,Object>();

        try {
            DataTable tmpdt=GetDataTable(strsql);
            if(tmpdt.getTotalCount()>0)
            {
                DataRow dr=tmpdt.getRow(0);
                for(int i=0;i<dr.getColumnList().size();i++)
                {
                    map.put(dr.getColumn(i).getColumnName(),dr.getColValue(i));
                }
            }

            return map;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public List<Map<String,Object>> GetListMapForResult(String strsql) {
        List<Map<String,Object>> _list=new ArrayList<Map<String,Object>>();
        try {
            DataTable tmpdt=GetDataTable(strsql);
            for(int i=0;i<tmpdt.getTotalCount();i++)
            {
                Map<String,Object> locmap = new HashMap<String,Object>();
                DataRow dr=tmpdt.getRow(i);
                for(int j=0;j<dr.getColumnList().size();j++)
                {
                    locmap.put(dr.getColumn(j).getColumnName(),dr.getColValue(j));
                }
                _list.add(locmap);
            }

            return _list;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public DataTable GetDataTable(String sql) {
        if(ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            DataTable dt=ConvertResultSetToDataTable(rs);
            statement.close();
            connection.close();
            return dt;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public DataSet GetDataSet(ArrayList<String> sqllist) {
        if(ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sqllist.toString());
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            DataSet ds=new DataSet();
            for (String sql:sqllist) {
                Statement statement = connection.createStatement();
                ResultSet rs = statement.executeQuery(sql);
                DataTable dt=ConvertResultSetToDataTable(rs);
                statement.close();
                ds.addTable(dt);
            }
            connection.close();

            return ds;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    private DataTable ConvertResultSetToDataTable(ResultSet rs)  {
        try
        {
            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();
            DataTable dt = new DataTable();

            for (int i = 1; i <= columnCount; i++) {
                DataColumn dc = new DataColumn(rsmd.getColumnLabel(i),rsmd.getColumnType(i),null);
                dt.getColList().add(dc);
            }

            while (rs.next()) {
                DataRow dr = new DataRow();
                for (int i = 1; i <= columnCount; i++) {
                    DataColumn dc = new DataColumn(rsmd.getColumnLabel(i),rsmd.getColumnType(i),rs.getObject(i));
                    dr.getColumnList().add(dc);
                }
                dt.addRow(dr);
            }
            return dt;
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return null;
        }
    }

    public String ExecuteInsertWithObtainId(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                retval = rs.getString(1);
            }
            rs.close();
            statement.close();
            connection.close();
            return retval;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public String ExecuteScalar(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                retval = rs.getString(1);
            }
            rs.close();
            statement.close();
            connection.close();
            return retval;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public boolean ExecuteScalarPair(String sql, StringBuilder  s1, StringBuilder  s2) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return false;
        }
        try {
            String retval = "";
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                MetaHelper.StringBuilderEmpty(s1);
                MetaHelper.StringBuilderEmpty(s2);
                s1.append(rs.getString(1));
                s2.append(rs.getString(2));
            }

            rs.close();
            statement.close();
            connection.close();
            return true;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }

    public boolean ExecuteScalarThree(String sql, StringBuilder  s1, StringBuilder  s2, StringBuilder  s3) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return false;
        }
        try {
            String retval = "";
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                MetaHelper.StringBuilderEmpty(s1);
                MetaHelper.StringBuilderEmpty(s2);
                MetaHelper.StringBuilderEmpty(s3);
                s1.append(rs.getString(1));
                s2.append(rs.getString(2));
                s3.append(rs.getString(3));
            }

            rs.close();
            statement.close();
            connection.close();
            return true;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }

    public boolean RecordExists(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return false;
        }
        try {
            boolean retval = false;
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                String rets = rs.getString(1);
                if (Integer.parseInt(rets) >= 1) {
                    retval = true;
                }
            }
            rs.close();
            statement.close();
            connection.close();
            return retval;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }

    public String ExecuteScalarList(String sql){
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                if (retval.equals("")) {
                    retval += rs.getString(1);
                } else {
                    retval += "," + rs.getString(1);
                }
            }
            rs.close();
            statement.close();
            connection.close();
            return retval;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public String ExecuteScalarList(String sql,String dels) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                if (retval.equals("")) {
                    retval += rs.getString(1);
                } else {
                    retval += dels + rs.getString(1);
                }
            }
            rs.close();
            statement.close();
            connection.close();
            return retval;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public ArrayList<String> ExecuteScalarArrayList(String sql){
        ArrayList<String> relist = new ArrayList<String>();
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return null;
        }
        try {
            Statement statement = connection.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                relist.add(rs.getString(1));
            }
            rs.close();
            statement.close();
            connection.close();
            return relist;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public int ExecuteNoQuery(String sql) throws Exception{
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        Connection connection = getConnection();
        if (connection == null) {
            return -1;
        }
        try {
            Statement statement = connection.createStatement();
            int retint = statement.executeUpdate(sql);
            statement.close();
            connection.close();
            return retint;
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            throw ex;
        }
    }

    public boolean ExecuteNoQuery(List<String> sqllist) throws Exception{
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sqllist.toString());
        }
        Connection connection = getConnection();
        if (connection == null) {
            return false;
        }
        try {
            Statement statement = null;
            connection.setAutoCommit(false);
            try {
                if (sqllist.size() > 0) {
                    for (int i = 0; i < sqllist.size(); i++) {
                        statement = connection.createStatement();
                        statement.executeUpdate((String) sqllist.get(i));
                        statement.close();
                    }
                    connection.commit();
                }
                connection.close();
                return true;
            } catch (Exception ex) {
                connection.rollback();
                LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
                return false;
            }
        } catch (Exception ex) {
            closeConnection(connection);
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            throw ex;
        }
    }

    public String FormatDateToStr(int strlen, String colname) {
        return dbFeatures.FormatDateToStr(strlen, colname);
    }

    public String FormatStrToDate(String strval) {
        return dbFeatures.FormatStrToDate(strval);
    }

    public String GetColType(String schema, String strtab, String colname) {
        String strsql = dbFeatures.GetColType(schema, strtab, colname);
        String strColType = null;
        try {
            strColType = ExecuteScalar(strsql);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return strColType;
    }

    public String ToPageSql(String strSql, String orderStr, int pageSize, int currentPage) {
        return dbFeatures.ToPageSql(strSql, orderStr, pageSize, currentPage);
    }

    public String ToLimitSql(String strSql, String orderStr, int start, int end) {
        return dbFeatures.ToLimitSql(strSql, orderStr, start, end);
    }

    public String PackFunc(String funcname) {
        return dbFeatures.PackFunc(funcname);
    }

    public String PackProc(String procname, String valstr) {
        return dbFeatures.PackProc(procname, valstr);
    }

    public String PackMetaQry(String sqlstr) {
        return dbFeatures.PackMetaQry(sqlstr);
    }

    public String PackTreeOrder(String orderstr) {
        return dbFeatures.PackTreeOrder(orderstr);
    }

    public String GetDefaultDT() {
        return dbFeatures.GetDefaultDT();
    }
}
