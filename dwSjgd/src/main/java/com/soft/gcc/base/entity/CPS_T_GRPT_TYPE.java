package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 报表类型定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CPS_T_GRPT_TYPE")
@ApiModel(value="CPS_T_GRPT_TYPE对象", description="报表类型定义表")
public class CPS_T_GRPT_TYPE extends Model<CPS_T_GRPT_TYPE> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报表类型ID（建议使用单据类型编号）")
    @JSONField(name="GRPTT_ID")
    private Integer GRPTT_ID;

    @ApiModelProperty(value = "报表类型名称")
    @JSONField(name="GRPTT_NAME")
    private String GRPTT_NAME;

    @ApiModelProperty(value = "报表类型说明")
    @JSONField(name="GRPTT_REMARK")
    private String GRPTT_REMARK;

    @ApiModelProperty(value = "报表类型编码")
    @JSONField(name="GRPTT_CODE")
    private String GRPTT_CODE;


    @Override
    protected Serializable pkVal() {
        return this.GRPTT_ID;
    }

}
