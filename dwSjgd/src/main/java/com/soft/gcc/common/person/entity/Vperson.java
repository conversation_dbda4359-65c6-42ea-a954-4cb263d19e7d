package com.soft.gcc.common.person.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName vPerson
 */
@TableName(value ="vPerson")
@Data
public class Vperson implements Serializable {
    /**
     * 
     */
    @TableField(value = "Id")
    private Integer id;

    /**
     * 
     */
    @TableField(value = "LoginName")
    private String loginname;

    /**
     * 
     */
    @TableField(value = "LoginName2")
    private String loginname2;

    /**
     * 
     */
    @TableField(value = "RealName")
    private String realname;

    /**
     * 
     */
    @TableField(value = "Password")
    private String password;

    /**
     * 
     */
    @TableField(value = "RoleId")
    private Integer roleid;

    /**
     * 
     */
    @TableField(value = "Telephone")
    private String telephone;

    /**
     * 
     */
    @TableField(value = "MsgType")
    private String msgtype;

    /**
     * 
     */
    @TableField(value = "OA")
    private String oa;

    /**
     * 
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 
     */
    @TableField(value = "P_XH")
    private Integer pXh;

    /**
     * 
     */
    @TableField(value = "GroupId")
    private Integer groupid;

    /**
     * 
     */
    @TableField(value = "ParentId")
    private Integer parentid;

    /**
     * 
     */
    @TableField(value = "ParentName")
    private String parentname;

    /**
     * 
     */
    @TableField(value = "GroupType")
    private Integer grouptype;

    /**
     * 
     */
    @TableField(value = "GroupName")
    private String groupname;

    /**
     * 
     */
    @TableField(value = "GroupDesc")
    private String groupdesc;

    /**
     * 
     */
    @TableField(value = "TopGroupId")
    private Integer topgroupid;

    /**
     * 
     */
    @TableField(value = "TopGroupName")
    private String topgroupname;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}