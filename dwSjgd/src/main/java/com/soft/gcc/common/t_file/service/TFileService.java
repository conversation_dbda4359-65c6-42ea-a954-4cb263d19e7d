package com.soft.gcc.common.t_file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.common.t_file.entity.TFile;

/**
* <AUTHOR>
* @description 针对表【T_File】的数据库操作Service
* @createDate 2023-02-23 21:23:38
*/
public interface TFileService extends IService<TFile> {

    /**
     * 文件上传之后操作file表
     * @param ywId
     * @param type
     * @param Hjid
     * @param functionId
     * @param filePaths
     */
    void saveOrUpdateFile(String ywId,String type,String Hjid,String functionId,String filePaths);



}
