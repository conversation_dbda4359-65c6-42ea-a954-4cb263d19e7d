package com.soft.gcc.common.role.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName Role
 */
@TableName(value ="Role")
@Data
public class Role implements Serializable {
    /**
     * 
     */
    @TableField(value = "Id")
    private Integer id;

    /**
     * 
     */
    @TableField(value = "RoleName")
    private String roleName;

    /**
     * 
     */
    @TableField(value = "AdminGroupIds")
    private String adminGroupIds;

    /**
     * 
     */
    @TableField(value = "IsHide")
    private Boolean isHide;

    /**
     * 
     */
    @TableField(value = "RoleKind")
    private String roleKind;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}