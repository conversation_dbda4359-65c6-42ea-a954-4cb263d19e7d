package com.soft.gcc.common.dfdw_dict.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictType;
import com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictDataMapper;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 */
@Service
public class DfdwTDictDataServiceImpl extends ServiceImpl<DfdwTDictDataMapper, DfdwTDictData>  implements DfdwTDictDataService {

    @Resource
    DfdwTDictDataMapper dfdwTDictDataMapper;


    /**
     * 新增字典数据
     * @return
     */
    @Override
    public Result<Object> createDictData(DfdwTDictData dfdwTDictData) {
        //检验键值是否重复
        Result<Object> result =  validateDictDataForCreateOrUpdate(null, dfdwTDictData.getValue(),dfdwTDictData.getDictType());
        if (result.getCode() != 200) {
            return result;
        }
        // 创建时间
        dfdwTDictData.setCreateTime(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //用户id
        dfdwTDictData.setCreator(user.getId()+"");
        dfdwTDictData.setDeleted("0");
        //更新用户
        dfdwTDictData.setUpdater(user.getId()+"");
        //更新时间
        dfdwTDictData.setUpdateTime(new Date());
        //新增
        dfdwTDictDataMapper.insert(dfdwTDictData);
        return result;
    }

    private Result<Object> validateDictDataForCreateOrUpdate(Integer id, String value,String dictType) {
        //查询字典类型
        List<DfdwTDictData> dictData = dfdwTDictDataMapper.selectList(new LambdaQueryWrapper<DfdwTDictData>()
                .eq(value != null , DfdwTDictData::getValue,value)
                .eq( dictType!= null , DfdwTDictData::getDictType,dictType)
                .eq(DfdwTDictData::getDeleted,0));
        if (id == null){
            if (CollectionUtils.isNotEmpty(dictData)) {
                return Result.error("字典键值已存在");
            }
        } else {
            if (CollectionUtils.isNotEmpty(dictData) && id != dictData.get(0).getId()) {
                return Result.error("字典键值已存在");
            }
        }
        return Result.ok();
    }

    /**
     * 删除字典数据
     * @return
     */
    @Override
    public Result<Object> deleteDictData(Long id) {
        //根据id查询数据
        DfdwTDictData dfdwTDictData = this.dfdwTDictDataMapper.selectById(id);
        //删除标识
        dfdwTDictData.setDeleted("1");
        this.dfdwTDictDataMapper.deleteById(dfdwTDictData);
        return Result.ok();
    }

    /**
     * 修改字典数据
     * @return
     */
    @Override
    public Result<Object>  updateDictData(DfdwTDictData dfdwTDictData) {
        //检验键值是否重复
        Result<Object> result = this.validateDictDataForCreateOrUpdate(dfdwTDictData.getId(), dfdwTDictData.getValue(), dfdwTDictData.getDictType());
        if (result.getCode() != 200) {
            return result;
        }
        //修改时间
        dfdwTDictData.setUpdateTime(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        dfdwTDictData.setUpdater(user.getId()+"");
        //修改
        dfdwTDictDataMapper.updateById(dfdwTDictData);
        return result;
    }

    /**
     * 字典数据分页
     * @param dfdwTDictData
     * @return
     */
    @Override
    public IPage<DfdwTDictData> dicDataByType(DfdwTDictData dfdwTDictData) {
        IPage<DfdwTDictData> list = new Page<>();
        list.setCurrent(dfdwTDictData.getPageNum());
        list.setSize(dfdwTDictData.getPageSize());
        //查询分页
        list = this.page(list, new LambdaQueryWrapper<DfdwTDictData>()
                .like(dfdwTDictData.getLabel() != null,DfdwTDictData::getLabel,dfdwTDictData.getLabel())
                .eq(dfdwTDictData.getDictType() != null,DfdwTDictData::getDictType,dfdwTDictData.getDictType())
                .eq(dfdwTDictData.getStatus() != null,DfdwTDictData::getStatus,dfdwTDictData.getStatus())
                .eq(DfdwTDictData::getDeleted,0)
                .orderByAsc(DfdwTDictData::getId));
        return list;
    }

    /**
     * 根据类型获取字典数据
     * @param dfdwTDictType
     * @return
     */
    @Override
    public List<DfdwTDictData> getDataByType(DfdwTDictType dfdwTDictType) {
        //根据字典类型查询字典数据
        List<DfdwTDictData> dictData = dfdwTDictDataMapper.selectList(new LambdaQueryWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType,dfdwTDictType.getType())
                .eq(DfdwTDictData::getDeleted,0));
        return dictData;
    }

    /**
     * 根据类型查询字典数据且状态0-正常
     */
    @Override
    public List<DfdwTDictData> getDictDataByType(String dictType) {
        //根据字典类型查询字典数据
        List<DfdwTDictData> dictData = dfdwTDictDataMapper.selectList(new LambdaQueryWrapper<DfdwTDictData>()
                .likeRight(DfdwTDictData::getDictType,dictType)
                .eq(DfdwTDictData::getStatus,0)
                .eq(DfdwTDictData::getDeleted,0));
        return dictData;
    }
}




