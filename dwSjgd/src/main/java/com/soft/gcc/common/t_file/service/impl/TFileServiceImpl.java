package com.soft.gcc.common.t_file.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.mapper.TFileMapper;
import com.soft.gcc.common.t_file.service.TFileService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【T_File】的数据库操作Service实现
* @createDate 2023-02-23 21:23:38
*/
@Service
public class TFileServiceImpl extends ServiceImpl<TFileMapper, TFile>
    implements TFileService{

    @Override
    public void saveOrUpdateFile(String ywId, String type, String Hjid, String functionId, String filePaths) {
        if (StringUtils.checkValNotNull(filePaths)){
            List<String> newFileList = Arrays.stream(filePaths.split(","))
                    .collect(Collectors.toList());
            //获取数据库中的老数据
            List<TFile> nowFileList = this.list(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getProjectid, ywId)
                    .eq(StringUtils.checkValNotNull(functionId),TFile::getFunctionid, functionId)
                    .eq(TFile::getHjid,Hjid)
                    .eq(TFile::getType, type)
            );
            List<String> oldFileList = nowFileList.stream().map(TFile::getFilepath).collect(Collectors.toList());
            //获取需要删除的数据
            List<String> deleted = oldFileList.stream()
                    .filter(item -> !newFileList.contains(item))
                    .collect(Collectors.toList());

            //获取需要update 的数据
            List<String> updateData = newFileList.stream()
                    .filter(item -> !oldFileList.contains(item))
                    .collect(Collectors.toList());
            if(!deleted.isEmpty()){
                this.remove(new LambdaQueryWrapper<TFile>()
                        .eq(TFile::getProjectid, ywId)
                        .eq(StringUtils.checkValNotNull(functionId),TFile::getFunctionid, functionId)
                        .eq(TFile::getType, type)
                        .eq(TFile::getHjid,Hjid)
                        .in(TFile::getFilepath, deleted));
            }
            if (!updateData.isEmpty()){
                this.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid,ywId)
                        .eq(TFile::getType, type)
                        .eq(StringUtils.checkValNotNull(functionId),TFile::getFunctionid, functionId)
                        .eq(TFile::getHjid,Hjid)
                        .in(TFile::getFilepath, updateData)
                );
                List<String> fileList = this.list(new LambdaQueryWrapper<TFile>()
                        .eq(TFile::getProjectid, ywId)
                        .eq(StringUtils.checkValNotNull(functionId),TFile::getFunctionid, functionId)
                        .eq(TFile::getType, type)
                ).stream().map(TFile::getFilepath).collect(Collectors.toList());
                for (String filePath: updateData) {
                    if (!fileList.contains(filePath)){
                        List<TFile> tFileList = this.list(new LambdaQueryWrapper<TFile>()
                                .eq(TFile::getFilepath, filePath)
                                .eq(StringUtils.checkValNotNull(functionId),TFile::getFunctionid, functionId)
                                .eq(TFile::getType, type)
                                .eq(TFile::getHjid,Hjid)
                        );
                        if (!tFileList.isEmpty()){
                            TFile tFile = new TFile();
                            tFile.setProjectid(Integer.valueOf(ywId));
                            tFile.setFunctionid(Integer.parseInt(functionId));
                            tFile.setType(type);
                            tFile.setHjid(Hjid);
                            tFile.setFilepath(filePath);
                            tFile.setFilename(tFileList.get(0).getFilename());
                            tFile.setUploaddate(new Date());
                            if (!nowFileList.isEmpty()) {
                                tFile.setPersonname(nowFileList.get(0).getPersonname());
                                tFile.setPersonzgh(nowFileList.get(0).getPersonzgh());
                            }
                            this.save(tFile);
                        }
                    }
                }
            }
        }else {
            this.remove(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getProjectid, ywId)
                    .eq(StringUtils.checkValNotNull(functionId),TFile::getFunctionid, functionId)
                    .eq(TFile::getType, type)
                    .eq(TFile::getHjid,Hjid)
            );
        }

    }
}




