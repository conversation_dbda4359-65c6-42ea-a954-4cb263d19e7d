package com.soft.gcc.common.t_smssend.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.common.t_smssend.entity.TSmssend;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【T_SMSSend】的数据库操作Mapper
* @createDate 2024-12-25 15:30:15
* @Entity com.soft.gcc.common.t_smssend.entity.TSmssend
*/
public interface TSmssendMapper extends BaseMapper<TSmssend> {

    Integer sendSms(@Param("phone")String phone, @Param("content") String content);
}




