package com.soft.gcc.common.role.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName RolePerson
 */
@TableName(value ="RolePerson")
@Data
public class Roleperson implements Serializable {
    /**
     * 
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "RoleId")
    private Integer roleId;

    /**
     * 
     */
    @TableField(value = "PersonId")
    private Integer personId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}