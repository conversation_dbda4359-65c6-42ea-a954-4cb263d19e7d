package com.soft.gcc.xtbg.sjgd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_FieldServiceRecord(设计工代-现场服务记录)】的数据库操作Service
* @createDate 2024-12-30 16:40:28
*/
public interface DfdwTDesignFieldservicerecordService extends IService<DfdwTDesignFieldservicerecord> {
    IPage<DfdwTDesignFieldservicerecord> getPageList(Page<DfdwTDesignFieldservicerecord> page,
                                                     DfdwTDesignFieldservicerecord serviceRecord);
    void addOrEdit(DfdwTDesignFieldservicerecord serviceRecord);
    DfdwTDesignFieldservicerecord getDetail(Integer id);
    void addProjectFile(DfdwTDesignFieldservicerecord serviceRecord);
    void updateProjectFile(DfdwTDesignFieldservicerecord serviceRecord);
    void exportPdf(List<Integer> ids, HttpServletResponse response, DzccPersonEntity user);
    List<DfdwTDesignFieldservicerecord> getAllList(@Param("query")DfdwTDesignFieldservicerecord serviceRecord);

    String serviceAssociation(DfdwTDesignFieldservicerecord serviceRecord, PersonEntity person);

    IPage<DfdwTDesignFieldservicerecord> getFwjlAssociationList(DfdwTDesignFieldservicerecord fieldservicerecord, PersonEntity person);

    void downloadExcel(DfdwTDesignFieldservicerecord serviceRecord, HttpServletResponse response);

    List<DfdwTDesignServiceassociation> getAssociationList(DfdwTDesignServiceassociation serviceassociation, PersonEntity person);
    //反馈是签收人关联的服务记录可见
    List<Integer> getSignServiceRecordIds(Integer userId);
}
