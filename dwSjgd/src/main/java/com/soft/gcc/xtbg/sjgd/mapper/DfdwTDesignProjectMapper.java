package com.soft.gcc.xtbg.sjgd.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_Project(设计工代-项目管理)】的数据库操作Mapper
* @createDate 2024-12-30 16:40:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject
*/
public interface DfdwTDesignProjectMapper extends BaseMapper<DfdwTDesignProject> {
    IPage<DfdwTDesignProject> getPageList(@Param("page") Page<DfdwTDesignProject> page,
                                          @Param("query") DfdwTDesignProject project);
    IPage<DfdwTDesignProject> getPageListUserId(@Param("page") Page<DfdwTDesignProject> page,
                                                @Param("userId") Integer userId,
                                          @Param("query") DfdwTDesignProject project);
    List<DfdwTDesignProject> getDownLoadList( @Param("userId") Integer userId,
                                              @Param("query") DfdwTDesignProject project);


    IPage<VProjectT> getVProjectPageList(@Param("page") Page<VProjectT> page, @Param("query") VProjectT project);
    List<VProjectT> getVProjectAllList(@Param("query") VProjectT project);

    List<ProjectTypeT> getVProjectTypeList(@Param("query") VProjectT project);
    List<LeaderPerson> getLeaderList(@Param("project")VProjectT project);

    //查权限
    Boolean hasDesignProjectRole(@Param("currentUserId") Integer currentUserId,@Param("roleName")String roleName);

    List<VDfdwTDesignflow> checkUse(@Param("ids") List<Integer> ids);
}


