package com.soft.gcc.xtbg.sjgd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_Project(设计工代-项目管理)】的数据库操作Service
* @createDate 2024-12-30 16:40:28
*/
public interface DfdwTDesignProjectService extends IService<DfdwTDesignProject> {
    IPage<DfdwTDesignProject> getPageList( Page<DfdwTDesignProject> page, DfdwTDesignProject project);
    IPage<DfdwTDesignProject> getPageListUserId( Page<DfdwTDesignProject> page, Integer userId,DfdwTDesignProject project);
    Boolean hasDesignProjectRole( Integer currentUserId,String roleName);

    /**
     * 项目列表
     * @param project
     * @return
     */
    IPage<VProjectT> getVProjectPageList(Page<VProjectT> page, VProjectT project);
    List<LeaderPerson> getLeaderList(VProjectT person);
    void addProjectSpecialty(DfdwTDesignProject project);
    void updateProjectSpecialty(DfdwTDesignProject project);
    void getProjectSpecialtyList(List<DfdwTDesignProject> list);
    void addProjectFile(DfdwTDesignProject project);
    void updateProjectFile(DfdwTDesignProject project);
    DfdwTDesignProject detail(Integer id);
    void exportProjectPdf(List<Integer> ids, HttpServletResponse response,  DzccPersonEntity user) throws Exception;

    /**
     * 校验项目使用情况
     * @param ids
     * @return
     */
    List<VDfdwTDesignflow> checkUse(List<Integer> ids);

    void downloadExcel(DfdwTDesignProject project, HttpServletResponse response);
    /**
     * userId如果是设总，获取设总的项目
     */
    List<Integer> getDesignDirectorProject(Integer userId);
}
