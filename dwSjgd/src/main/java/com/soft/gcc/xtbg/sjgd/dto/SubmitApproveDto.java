package com.soft.gcc.xtbg.sjgd.dto;

import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 审批提交实体
 * @date 2024-12-17 09:48:23
 */
@Data
public class SubmitApproveDto {
    private Integer lcDefineId;
    //当前流程节点Id
    private Integer lcJdId;

    //下一步流程节点信息
    private Integer lcNextJdId;

    //业务Id
    private Integer ywId;
    //提交选中的审批人信息 (如果是最后一步，传入 -1 和 完成)
    private Integer sendPersonId;
    private String sendPersonName;

    private String feeds;

    //审批方式
    private String approvalOpinion;

    //服务申请，第二步提交保存时用
    private Integer replyArriveDays;
    private List<DfdwTDesignSupervisorapplyspecialty> designUser;


}
