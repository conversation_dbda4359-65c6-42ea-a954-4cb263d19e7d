package com.soft.gcc.xtbg.sjgd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import com.yyszc.wpbase.ventity.PersonEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_FieldProblemFeedback(设计工代-现场问题反馈)】的数据库操作Service
* @createDate 2024-12-30 16:40:28
*/
public interface DfdwTDesignFieldproblemfeedbackService extends IService<DfdwTDesignFieldproblemfeedback> {


    IPage<DfdwTDesignFieldproblemfeedback> listPage(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback);

    DfdwTDesignFieldproblemfeedback detail(Integer id);

    Integer addOrEdit(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletRequest request);

    /**
     * 签收并且短信通知
     * @param dfdwTDesignFieldproblemfeedback
     * @return
     */
    Integer signFor(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback);

    void downloadPdf(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletResponse response, DzccPersonEntity user);
    void downloadExcel(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletResponse response);

    IPage<DfdwTDesignFieldproblemfeedback> getWtfkAssociationList(DfdwTDesignFieldproblemfeedback problemfeedback, PersonEntity person);
}
