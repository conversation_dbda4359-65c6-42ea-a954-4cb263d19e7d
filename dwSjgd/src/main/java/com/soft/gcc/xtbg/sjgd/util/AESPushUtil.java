package com.soft.gcc.xtbg.sjgd.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
public class AESPushUtil {

    private static String KEY = "nbszyynuanbaobao"; // 长度必须是 16

    private static String IV = "nuanbaobao123456"; // 长度必须是 16

    /**
     * 加密返回的数据转换成 String 类型
     * @param content 明文
     * @return
     * @throws Exception
     */
    public static String encrypt(String content){
        // 将返回的加密过的 byte[] 转换成Base64编码字符串 ！！！！很关键
        return base64ToString(AES_CBC_Encrypt(content.getBytes(), KEY.getBytes(), IV.getBytes()));
    }

    /**
     * 将解密返回的数据转换成 String 类型
     * @param content Base64编码的密文
     * @return
     * @throws Exception
     */
    public static String decrypt(String content) {
        // stringToBase64() 将 Base64编码的字符串转换成 byte[] !!!与base64ToString(）配套使用
        return new String(AES_CBC_Decrypt(stringToBase64(content), KEY.getBytes(), IV.getBytes()));
    }

    private static byte[] AES_CBC_Encrypt(byte[] content, byte[] keyBytes, byte[] iv){
        try {
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE,key, new IvParameterSpec(iv));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            log.error("解密失败,{}", e.getMessage());
            return null;
        }
    }

    private static byte[] AES_CBC_Decrypt(byte[] content, byte[] keyBytes, byte[] iv){
        try {
            SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE,key, new IvParameterSpec(iv));
            byte[] result = cipher.doFinal(content);
            return result;
        } catch (Exception e) {
            log.error("加密失败,{}", e.getMessage());
            return null;
        }
    }

    /**
     * 字符串装换成 Base64
     */

    public static byte[] stringToBase64(String key){
        return Base64.decodeBase64(key.getBytes());
    }

    /**
     * Base64装换成字符串
     */
    public static String base64ToString(byte[] key) {
        return new Base64().encodeToString(key);
    }
}
