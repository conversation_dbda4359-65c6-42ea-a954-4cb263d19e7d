package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 设计工代-项目-专业人员
 * @TableName DFDW_T_Design_ProjectSpecialty
 */
@TableName(value ="DFDW_T_Design_ProjectSpecialty")
@Data
public class DfdwTDesignProjectspecialty implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设计工代项目Id
     */
    @TableField(value = "designProjectId")
    private Integer designProjectId;

    /**
     * 专业
     */
    @TableField(value = "specialty")
    private String specialty;

    /**
     * 设计工代人员
     */
    @TableField(value = "designUserId")
    private Integer designUserId;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}