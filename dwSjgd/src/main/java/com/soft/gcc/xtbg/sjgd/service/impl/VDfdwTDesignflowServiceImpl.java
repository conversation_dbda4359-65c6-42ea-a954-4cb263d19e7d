package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignflow;
import com.soft.gcc.xtbg.sjgd.dto.FlowDto;
import com.soft.gcc.xtbg.sjgd.service.VDfdwTDesignflowService;
import com.soft.gcc.xtbg.sjgd.mapper.VDfdwTDesignflowMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【V_DFDW_T_DesignFlow】的数据库操作Service实现
 * @createDate 2024-12-31 13:29:50
 */
@Service
public class VDfdwTDesignflowServiceImpl extends ServiceImpl<VDfdwTDesignflowMapper, VDfdwTDesignflow>
        implements VDfdwTDesignflowService {

    @Override
    public Result<Object> getFlowList(DzccPersonEntity person, FlowDto flowDto) {

        IPage<VDfdwTDesignflow> list = new Page<>();
        list.setCurrent(flowDto.getPageNum());
        list.setSize(flowDto.getPageSize());
        String loginName = person.getLoginName();
        list = this.page(list, new LambdaQueryWrapper<VDfdwTDesignflow>()
                        .eq(flowDto.getType() != -1, VDfdwTDesignflow::getYwType, flowDto.getType())
                        .eq(flowDto.getApproveState() != -1, VDfdwTDesignflow::getApproveState, flowDto.getApproveState())
                        .like(StringUtils.isNoneEmpty(flowDto.getProjectName()), VDfdwTDesignflow::getProjectName, flowDto.getProjectName())
                        .like(StringUtils.isNoneEmpty(flowDto.getApplyNo()), VDfdwTDesignflow::getApplyNo, flowDto.getApplyNo())
                        .eq(flowDto.getIsHistory() == 0, VDfdwTDesignflow::getSendPersonZgh, loginName + "~")
                        .like(flowDto.getIsHistory() == 1, VDfdwTDesignflow::getAllPersonZgh, loginName + "~")
                        .orderByDesc(VDfdwTDesignflow::getCreateTime));

        return Result.ok(list);
    }
}




