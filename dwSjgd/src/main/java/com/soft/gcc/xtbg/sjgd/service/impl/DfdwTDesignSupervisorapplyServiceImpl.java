package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.common.t_smssend.service.TSmssendService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty;
import com.soft.gcc.xtbg.sjgd.service.*;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignSupervisorapplyMapper;
import com.soft.gcc.xtbg.sjgd.util.PdfUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_SupervisorApply(设计工代-服务申请)】的数据库操作Service实现
* @createDate 2024-12-30 16:40:28
*/
@Service
public class DfdwTDesignSupervisorapplyServiceImpl extends ServiceImpl<DfdwTDesignSupervisorapplyMapper, DfdwTDesignSupervisorapply>
    implements DfdwTDesignSupervisorapplyService{
    @Resource
    private TFileService fileService;
    @Resource
    private DfdwTDesignSupervisorapplyspecialtyService dfdwTDesignSupervisorapplyspecialtyService;
    @Resource
    DfdwTDesignProjectService projectService;
    @Resource
    DfdwTDesignServiceassociationService serviceassociationService;

    @Resource
    DfdwTDesignFieldservicerecordService dfdwTDesignFieldservicerecordService;

    @Resource
    TSmssendService smssendService;
    @Resource
    private DfdwTDesignSupervisorapplyService dfdwTDesignSupervisorapplyService;

    @Override
    public IPage<DfdwTDesignSupervisorapply> listPage(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply) {
        Boolean  zongGuanLi= projectService.hasDesignProjectRole(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId(), "协同办公-设计工代-总管理");
        dfdwTDesignSupervisorapply.setIsShowAll(false);
        if( zongGuanLi ){
            dfdwTDesignSupervisorapply.setIsShowAll(true);
        }
        //1、设总可以查看本项目的所有内容；
        List<Integer> designProjectList = projectService.getDesignDirectorProject(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId());
        dfdwTDesignSupervisorapply.setDesignDirectorDesignProjectIds(designProjectList);
        return this.baseMapper.listPage(new Page<>(dfdwTDesignSupervisorapply.getPageNum(), dfdwTDesignSupervisorapply.getPageSize()),dfdwTDesignSupervisorapply);
    }

    @Override
    public DfdwTDesignSupervisorapply detail(Integer id) {
        DfdwTDesignSupervisorapply res = this.baseMapper.selectInfoById(id);
        List<TFile> files = fileService.list(new LambdaQueryWrapper<TFile>()
                .eq(TFile::getProjectid,id).eq(TFile::getFunctionid,20033)
                .eq(TFile::getType,"jlsq")
        );
        for (TFile file : files) {
            if ("1".equals(file.getHjid())) {
                if (StringUtils.checkValNull(res.getSiteProblemFile())){
                    res.setSiteProblemFile(file.getFilepath());
                }else{
                    res.setSiteProblemFile(res.getSiteProblemFile()+","+file.getFilepath());
                }
            }

            if ("2".equals(file.getHjid())){
                if (StringUtils.checkValNull(res.getContactFile())){
                    res.setContactFile(file.getFilepath());
                }else{
                    res.setContactFile(res.getContactFile()+","+file.getFilepath());
                }
            }
        }
        List<DfdwTDesignServiceassociation> serviceassociationList = serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                .eq(DfdwTDesignServiceassociation::getSupervisorApplyId,id)
                .isNotNull(DfdwTDesignServiceassociation::getServiceRecordId)
        );
        res.setServiceassociationList(serviceassociationList);
        return res;
    }

    @Override
    public Integer addOrEdit(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply) {
        if (dfdwTDesignSupervisorapply.getId() == null ){
            dfdwTDesignSupervisorapply.setCreateId(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId());
            dfdwTDesignSupervisorapply.setCreator(dfdwTDesignSupervisorapply.getCurrentLoginUser().getLoginName());
            dfdwTDesignSupervisorapply.setCreateTime(new Date());
            dfdwTDesignSupervisorapply.setApproveState(0);
            dfdwTDesignSupervisorapply.setDeleted("0");
            //生成单据编号
            dfdwTDesignSupervisorapply.setApplyNo(getBh());
        }else {
            dfdwTDesignSupervisorapply.setUpdaterId(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId());
            dfdwTDesignSupervisorapply.setUpdater(dfdwTDesignSupervisorapply.getCurrentLoginUser().getLoginName());
            dfdwTDesignSupervisorapply.setUpdateTime(new Date());
        }
        this.saveOrUpdate(dfdwTDesignSupervisorapply);

        fileService.saveOrUpdateFile(String.valueOf(dfdwTDesignSupervisorapply.getId()),"jlsq","1","20033",dfdwTDesignSupervisorapply.getSiteProblemFile());
        fileService.saveOrUpdateFile(String.valueOf(dfdwTDesignSupervisorapply.getId()),"jlsq","2","20033",dfdwTDesignSupervisorapply.getContactFile());

        return dfdwTDesignSupervisorapply.getId();
    }

    /**
     * 获取编号
     * @return
     */
    public String getBh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        //dateStr = dateStr.substring(2, dateStr.length());  //保留格式yyMM

        String gcBH = "JLSQ-" + dateStr;
        DfdwTDesignSupervisorapply maxEn = baseMapper.getMaxApplyNo(gcBH);
        if (maxEn == null) {
            gcBH = gcBH + "0001";
        } else {
            //从12位截取后面
            String maxReturnNo = maxEn.getApplyNo();
            String noStr = maxReturnNo.substring(13);
            int no = Integer.parseInt(noStr) + 1;
            if (no % 10 == 0) {
                no = no + 1;
            }
            noStr = String.valueOf(no);
            noStr = padLeft(noStr, 4, '0');
            gcBH = gcBH + noStr;
        }


        return gcBH;
    }

    public String padLeft(String src, int len, char ch) {
        int diff = len - src.length();
        if (diff <= 0) {
            return src;
        }

        char[] charr = new char[len];
        System.arraycopy(src.toCharArray(), 0, charr, diff, src.length());
        for (int i = 0; i < diff; i++) {
            charr[i] = ch;
        }
        return new String(charr);

    }

    @Override
    public void downloadExcel(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, HttpServletResponse response)  {
        Boolean  gongDai= projectService.hasDesignProjectRole(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId(), "协同办公-设计工代-设计工代人员");
        Boolean  zongGuanLi= projectService.hasDesignProjectRole(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId(), "协同办公-设计工代-总管理");
        if(gongDai){
            dfdwTDesignSupervisorapply.setApplyUserId(dfdwTDesignSupervisorapply.getCurrentLoginUser().getId());
        }
        if( zongGuanLi ){
            dfdwTDesignSupervisorapply.setApplyUserId(null);
        }
        List<DfdwTDesignSupervisorapply> list = this.baseMapper.queryList(dfdwTDesignSupervisorapply);
//        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
//        //转换时间
//        for (int i = 0; i < list.size(); i++) {
//            if (list.get(i).getApplyArriveDate() != null) {
//                list.get(i).setApplyArriveDateStr(ymd.format(list.get(i).getApplyArriveDate()));
//            }
//        }

        try {
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
            cmlist.add(new ToolHelper.ExportColumnMode("applyNo", "单据编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("projectName", "项目名称", 50));
            cmlist.add(new ToolHelper.ExportColumnMode("designUnit", "设计单位", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("applyArriveDateStr", "申请到位时间", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("applyUserName", "发起人", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("signerUserName", "签收人", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("replyArriveDays", "答复到位时间(天)", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("content", "内容", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("approveStateStr", "审批状态", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("specialtyLabels", "设计工代专业", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("specialtyUsers", "设计工代人员", 50));
            cmlist.add(new ToolHelper.ExportColumnMode("serviceRecordCount", "关联服务记录数", 50));
            ToolHelper.ExportExcelList(list, "服务申请","服务申请信息", cmlist,false,response );
        }catch (Exception e){
           throw new RuntimeException(e.getMessage());
        }

    }


    @Override
    public void downloadPdf(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply,
                            HttpServletResponse response,
                            DzccPersonEntity user) {
        try {
            List<Integer> ids = dfdwTDesignSupervisorapply.getIds();

            // 判断是否有数据要导出
            if (ids == null || ids.isEmpty()) {
                throw new RuntimeException("没有可导出的数据");
            }

            // 单个文件直接导出PDF
            if (ids.size() == 1) {
                dfdwTDesignSupervisorapply.setId(dfdwTDesignSupervisorapply.getIds().get(0));

                generateSinglePdf(dfdwTDesignSupervisorapply, response,user);
                return;
            }

            // 多个文件打包成ZIP
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode("服务联系单.zip", "UTF-8"));

            try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
                for (Integer id : ids) {
                    // 获取每个id的详细信息
                    DfdwTDesignSupervisorapply detail = detail(id);
                    // 生成PDF并添加到ZIP
                    byte[] pdfBytes = generatePdfBytes(detail,user);

                    // 创建ZIP条目
                    String fileName = detail.getApplyNo() + "_服务联系单.pdf";
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zos.putNextEntry(zipEntry);
                    zos.write(pdfBytes);
                    zos.closeEntry();
                }
                zos.flush();
            }

        } catch (Exception e) {
            log.error("文件生成失败", e);
            throw new RuntimeException("文件生成失败: " + e.getMessage());
        }
    }

    @Override
    public String serviceAssociation(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, PersonEntity person) {
        List<DfdwTDesignServiceassociation> newList = dfdwTDesignSupervisorapply.getServiceassociationList();
        List<Integer> serviceRecordIdList = newList.stream().map(DfdwTDesignServiceassociation::getServiceRecordId).collect(Collectors.toList());
        // 服务申请与服务记录关联
        List<DfdwTDesignServiceassociation> oldList = serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                .eq(DfdwTDesignServiceassociation::getSupervisorApplyId, dfdwTDesignSupervisorapply.getId())
        );
        List<Integer> serviceRecordOldIdList = oldList.stream().map(DfdwTDesignServiceassociation::getServiceRecordId).collect(Collectors.toList());
        List<DfdwTDesignServiceassociation> serviceRecordList = new ArrayList<>();
        if (!serviceRecordIdList.isEmpty()) {
            serviceRecordList = serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                    .in(DfdwTDesignServiceassociation::getServiceRecordId, serviceRecordIdList)
            );
        }
        // 删除数据
        List<Integer> deleteIdList = oldList.stream()
                .filter(item -> !serviceRecordIdList.contains(item.getServiceRecordId()))
                .map(DfdwTDesignServiceassociation::getId)
                .collect(Collectors.toList());
        serviceassociationService.deleteFwsqByIds(deleteIdList);

        List<DfdwTDesignServiceassociation> editList = newList.stream()
                .filter(item -> !serviceRecordOldIdList.contains(item.getServiceRecordId()))
                .collect(Collectors.toList());
        //发短信用
        List<Integer> ids = new ArrayList<>();

        for (DfdwTDesignServiceassociation edit: editList) {
            ids.add(edit.getServiceRecordId());

            DfdwTDesignServiceassociation finalEdit = edit;
            Optional<DfdwTDesignServiceassociation> first = serviceRecordList.stream().filter(item -> item.getServiceRecordId().equals(finalEdit.getServiceRecordId())).findFirst();
            if (first.isPresent()) {
                edit = first.get();
                edit.setSupervisorApplyId(dfdwTDesignSupervisorapply.getId());
                edit.setUpdaterId(person.getId());
                edit.setUpdateTime(new Date());
                serviceassociationService.updateById(edit);
            } else {
                edit.setCreateId(person.getId());
                edit.setCreateTime(new Date());
                serviceassociationService.save(edit);
            }
        }
        if (!ids.isEmpty()){
            DfdwTDesignSupervisorapply info = this.baseMapper.selectById(dfdwTDesignSupervisorapply.getId());
            String phoneNumber = this.baseMapper.selectPersonUserPhoneById(info.getApplyUserId());
            //给发起人发送短信
            List<DfdwTDesignFieldservicerecord> list = dfdwTDesignFieldservicerecordService.list(new LambdaQueryWrapper<DfdwTDesignFieldservicerecord>()
                    .eq(DfdwTDesignFieldservicerecord::getApproveState,2)
                    .in(DfdwTDesignFieldservicerecord::getId,ids)
            );
            if (!list.isEmpty()){
                // 提取并拼接 applyNo
                String joinedApplyNos = list.stream()
                        .map(DfdwTDesignFieldservicerecord::getApplyNo)
                        .filter(Objects::nonNull) // 过滤空值
                        .collect(Collectors.joining(","));

                smssendService.sendSms(phoneNumber,"您已成功关联服务记录，服务记录编号："+joinedApplyNos+"，详情请登录平台查看");
            }

        }


        return "success";
    }

    @Override
    public List<DfdwTDesignServiceassociation> getAssociationList(DfdwTDesignServiceassociation serviceassociation, PersonEntity person) {
        return serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>().eq(DfdwTDesignServiceassociation::getSupervisorApplyId, serviceassociation.getSupervisorApplyId()));
    }

    @Override
    public IPage<DfdwTDesignSupervisorapply> getFwsqAssociationList(DfdwTDesignSupervisorapply supervisorapply, PersonEntity person) {
        Boolean gongDai= projectService.hasDesignProjectRole(person.getId(), "协同办公-设计工代-设计工代人员");
        Boolean zongGuanLi= projectService.hasDesignProjectRole(person.getId(), "协同办公-设计工代-总管理");
        if(gongDai){
            supervisorapply.setApplyUserId(person.getId());
        }
        if(zongGuanLi){
            supervisorapply.setApplyUserId(null);
        }

        IPage<DfdwTDesignFieldservicerecord> page = new Page<>(supervisorapply.getPageNum(), supervisorapply.getPageSize());
        return baseMapper.getFwsqAssociationList(page, supervisorapply);
    }

    // 生成单个PDF文件
    private void generateSinglePdf(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, HttpServletResponse response,DzccPersonEntity user) throws Exception {
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment;filename=" +
                URLEncoder.encode(dfdwTDesignSupervisorapply.getApplyNo() + "_服务联系单.pdf", "UTF-8"));

        byte[] pdfBytes = generatePdfBytes(dfdwTDesignSupervisorapply,user);
        response.getOutputStream().write(pdfBytes);
        response.getOutputStream().flush();
    }

    // 生成PDF字节数组的核心方法
    private byte[] generatePdfBytes(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, DzccPersonEntity user) throws Exception {
        PdfReader reader = null;
        PdfStamper stamper = null;
        ByteArrayOutputStream bos = null;

        try {
            // 读取模板
            String templatePath = "/static/sjgd/fuwulianxidan.pdf";
            InputStream inputStream = getClass().getResourceAsStream(templatePath);
            if (inputStream == null) {
                throw new IOException("模板文件不存在: " + templatePath);
            }

            reader = new PdfReader(inputStream);
            bos = new ByteArrayOutputStream();
            stamper = new PdfStamper(reader, bos);

            // 使用中文字体
            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            ArrayList<BaseFont> fontList = new ArrayList<>();
            fontList.add(bf);

            DfdwTDesignSupervisorapply detail = detail(dfdwTDesignSupervisorapply.getId());

            // 填充数据
            AcroFields fields = stamper.getAcroFields();
            fields.setSubstitutionFonts(fontList);

            fields.setField("projectName", detail.getProjectName());
            fields.setField("applyNo", detail.getApplyNo());
            fields.setField("designUnit", detail.getDesignUnit());

            float fontSize = 7f;
            // 创建带下划线的内容部分
            Chunk underlinedContent = new Chunk(detail.getContent(), new Font(bf, fontSize));
            underlinedContent.setUnderline(0.1f, -2f);  // 设置下划线

            // 获取Text1字段的位置
            List<AcroFields.FieldPosition> positions = fields.getFieldPositions("Text1");
            if (positions != null && !positions.isEmpty()) {
                AcroFields.FieldPosition pos = positions.get(0);

                // 创建一个新的内容
                PdfContentByte canvas = stamper.getOverContent(pos.page);
                Rectangle rect = pos.position;

                // 设置字体
                canvas.beginText();
                canvas.setFontAndSize(bf, fontSize);  // 使用之前创建的BaseFont

                // 写入文本并添加下划线
                Phrase phrase = new Phrase();
//                Chunk chunk = new Chunk(content, new Font(bf, 6));
                phrase.add(new Chunk("由于 ", new Font(bf, fontSize)));
                phrase.add(underlinedContent);  // 添加带下划线的内容
                LocalDate applyArriveDate = detail.getApplyArriveDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                String ymd = applyArriveDate.getYear()+"年"+applyArriveDate.getMonthValue()+"月"+applyArriveDate.getDayOfMonth()+"日";
                phrase.add(new Chunk("原因，兹提出设计工代赴现场解决，请收到服务联系单后"+ymd+"（时间）内到现场服务。附件：现场疑难问题等情况相关附件", new Font(bf, fontSize)));
//                chunk.setUnderline(0.1f, -2f);  // 设置下划线（粗细，位置）
//                phrase.add(chunk);

                // 使用ColumnText来放置文本
                ColumnText ct = new ColumnText(canvas);
                ct.setSimpleColumn(rect);
                ct.addText(phrase);
                ct.go();

                canvas.endText();
            }
           // fields.setField("Text1", content);

            fields.setField("specialtyName", detail.getSignerUserName());
            fields.setField("replyArriveDays", String.valueOf(detail.getReplyArriveDays()));
            fields.setField("signUserName", detail.getSignerUserName());

            if( detail.getApprovalTime() != null){
                LocalDate approvalTime = detail.getApprovalTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                fields.setField("signDateY", String.valueOf(approvalTime.getYear()));
                fields.setField("signDateM", String.valueOf(approvalTime.getMonthValue()));
                fields.setField("signDateD", String.valueOf(approvalTime.getDayOfMonth()));
            }

            fields.setField("applyUserName", detail.getApplyUserName());
            LocalDate applyDate = detail.getCreateTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            fields.setField("applyDateY", String.valueOf(applyDate.getYear()));
            fields.setField("applyDateM", String.valueOf(applyDate.getMonthValue()));
            fields.setField("applyDateD", String.valueOf(applyDate.getDayOfMonth()));

            //获取专业人员
            List<DfdwTDesignSupervisorapplyspecialty> list = dfdwTDesignSupervisorapplyspecialtyService.getPersonListBySupervisorApplyId(new DfdwTDesignSupervisorapplyspecialty() {{
                setSupervisorApplyId(detail.getId());
            }});

            String userNames = "";
            for (DfdwTDesignSupervisorapplyspecialty item : list) {
                userNames += item.getUserName()+", ";
            }
            if(!userNames.isEmpty()){
                userNames = userNames.substring(0,userNames.length()-1);
            }

            fields.setField("qsyj","联系单已签收，拟派"+userNames+"设计工地代表于"+ detail.getReplyArriveDays()+"日内现场解决。" );





            //若当前文本域无法满足
            if (checkAddrLength(fields.getField("Text1"), "Text1", fields)) {
                /*获取当前文本框的尺寸，返回的数据依次为左上右下（0,1,2,3）*/
                PdfArray rect1 = fields.getFieldItem("Text1").getValue(0).getAsArray(PdfName.RECT);
                rect1.set(1, new PdfNumber(rect1.getAsNumber(1).intValue() - 16));
            }

            //若当前文本域无法满足
            if (checkAddrLength(fields.getField("qsyj"), "qsyj", fields)) {
                /*获取当前文本框的尺寸，返回的数据依次为左上右下（0,1,2,3）*/
                PdfArray rect1 = fields.getFieldItem("qsyj").getValue(0).getAsArray(PdfName.RECT);
                rect1.set(1, new PdfNumber(rect1.getAsNumber(1).intValue() - 16));
            }

            // 设置表单平面化
            stamper.setFormFlattening(true);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<String> watermark = Arrays.asList(
                    user.getRealName(),
                    user.getGroupName(),
                    sdf.format(new Date())
            );


            // 完成 PDF 生成
            stamper.close();

            return PdfUtil.PDFAddWatermark(bos.toByteArray(),watermark);

        } finally {
            // 关闭资源
            try {
                if (bos != null) {
                    bos.close();
                }
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }


    public static boolean checkAddrLength(String addr, String fileName, AcroFields form) throws IOException, DocumentException {
        float fontSize = 8f;
        boolean flag = false;
        BaseFont baseFont = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        float textWidth = baseFont.getWidthPoint(addr, fontSize);
        Rectangle position = form.getFieldPositions(fileName).get(0).position;
        float textBoxWidth = position.getWidth();
        if (textWidth > textBoxWidth) {
            flag = true;
        }
        return flag;
    }

    @Override
    public List<Integer> getApplyServiceRecordIds(Integer userId) {
        List<DfdwTDesignSupervisorapply> applyList = dfdwTDesignSupervisorapplyService.list(new LambdaQueryWrapper<DfdwTDesignSupervisorapply>()
                .eq(DfdwTDesignSupervisorapply::getCreateId, userId)
                .eq(DfdwTDesignSupervisorapply::getDeleted, 0));
        //获取申请单关联的服务记录
        if(applyList!=null && applyList.size()>0) {
            List<Integer> collect = serviceassociationService.list(new QueryWrapper<DfdwTDesignServiceassociation>().lambda()
                            .in(DfdwTDesignServiceassociation::getSupervisorApplyId
                                    , applyList.stream().map(DfdwTDesignSupervisorapply::getId).collect(Collectors.toList())))
                    .stream().map(s -> s.getServiceRecordId()).collect(Collectors.toList());
            return collect;
        }else{
            return new ArrayList<>();
        }
    }

}




