package com.soft.gcc.xtbg.sjgd.service;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignflow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.sjgd.dto.FlowDto;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_T_DesignFlow】的数据库操作Service
* @createDate 2024-12-31 13:29:50
*/
public interface VDfdwTDesignflowService extends IService<VDfdwTDesignflow> {

    Result<Object> getFlowList(DzccPersonEntity person, FlowDto flowDto);
}
