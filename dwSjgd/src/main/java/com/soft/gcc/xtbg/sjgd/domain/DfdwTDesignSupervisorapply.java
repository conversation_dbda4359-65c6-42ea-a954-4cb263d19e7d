package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import com.soft.gcc.xtbg.base.entity.SjgdBaseEntity;
import lombok.Data;

/**
 * 设计工代-服务申请
 * @TableName DFDW_T_Design_SupervisorApply
 */
@TableName(value ="DFDW_T_Design_SupervisorApply")
@Data
public class DfdwTDesignSupervisorapply extends SjgdBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 设计工代项目Id
     */
    @TableField(value = "designProjectId")
    private Integer designProjectId;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;

    /**
     * 单据编号（自动生成）
     */
    @TableField(value = "applyNo")
    private String applyNo;

    /**
     * 设计单位（默认设计院，读取字典）
     */
    @TableField(value = "designUnit")
    private String designUnit;

    /**
     * 内容（申请原因）
     */
    @TableField(value = "content")
    private String content;

    /**
     * 设计工代专业（多个之间,分割）
     */
    @TableField(value = "specialty")
    private String specialty;

    /**
     * 申请到位时间
     */
    @TableField(value = "applyArriveDate")
    private Date applyArriveDate;

    /**
     * 发起人
     */
    @TableField(value = "applyUserId")
    private Integer applyUserId;

    /**
     * 发起人姓名
     */
    @TableField(value = "applyUserName")
    private String applyUserName;

    /**
     * 答复到位时间（天）
     */
    @TableField(value = "replyArriveDays")
    private Integer replyArriveDays;

    /**
     * 签收人（该项目总设Id）
     */
    @TableField(value = "signerUserId")
    private Integer signerUserId;

    /**
     * 签收人（该项目总设Name）
     */
    @TableField(value = "signerUserName")
    private String signerUserName;

    /**
     * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(value = "approveState")
    private Integer approveState;


    /**
     * 审批通过时间
     */
    @TableField(value = "approvalTime")
    private Date approvalTime;

//    /**
//     * 创建时间
//     */
//    @TableField(value = "createTime")
//    private Date createTime;

    /**
     * 导出excel用  审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(exist = false)
    private String approveStateStr;
    /**
     * 导出excel用 设计工代人员
     */
    @TableField(exist = false)
    private String specialtyUsers;
    /**
     * 导出excel用 设计工代专业
     */
    @TableField(exist = false)
    private String specialtyLabels;
    /**
     * 导出excel用 申请时间
     */
    @TableField(exist = false)
    private String applyArriveDateStr;


    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;
    /**
     * 现场问题处理附件
     */
    @TableField(exist = false)
    private String siteProblemFile;
    /**
     * 联系单附件
     */
    @TableField(exist = false)
    private String contactFile;
    /**
     * 服务申请-服务记录关联表
     */
    @TableField(exist = false)
    private List<DfdwTDesignServiceassociation> serviceassociationList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 关联服务记录数量
     */
    @TableField(exist = false)
    private Integer serviceRecordCount;

    @TableField(exist = false)
    private Integer fieldServiceRecordId;

    @TableField(exist = false)
    private Integer fieldServiceRecordDesignUserId;
    /**
     * 设总的项目Ids
     */
    @TableField(exist = false)
    private List<Integer> DesignDirectorDesignProjectIds;

    @TableField(exist = false)
    private Boolean isShowAll;
    /**
     * 关联的反馈id
     */
    @TableField(exist = false)
    private Integer problemFeedbackId;

}
