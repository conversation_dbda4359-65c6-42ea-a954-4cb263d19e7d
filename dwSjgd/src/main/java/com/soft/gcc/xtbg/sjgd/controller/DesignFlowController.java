package com.soft.gcc.xtbg.sjgd.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.mapper.PersonMapper;
import com.soft.gcc.common.t_smssend.service.TSmssendService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.soft.gcc.xtbg.sjgd.dto.FlowDto;
import com.soft.gcc.xtbg.sjgd.dto.SubmitApproveDto;
import com.soft.gcc.xtbg.sjgd.dto.TreeNode;
import com.soft.gcc.xtbg.sjgd.dto.TreeNodeChild;
import com.soft.gcc.xtbg.sjgd.service.*;
import com.soft.gcc.xtbg.sjgd.util.HttpClientUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024-12-31 13:30:51
 */
@RequestMapping("/Sjgd/DesignFlow")
@RestController
public class DesignFlowController extends BaseController {

    @Autowired
    private VDfdwTDesignflowService dfdwTDesignflowService;
    @Autowired
    private DfdwTDesignSupervisorapplyService dfdwTDesignSupervisorapplyService;
    @Autowired
    private DfdwTDesignSupervisorapplyspecialtyService dfdwTDesignSupervisorapplyspecialtyService;
    @Autowired
    private DfdwTDesignFieldservicerecordService dfdwTDesignFieldservicerecordService;
    @Autowired
    private DfdwTDesignFieldproblemfeedbackService dfdwTDesignFieldproblemfeedbackService;
    @Autowired
    private TSmssendService smssendService;
    @Autowired
    private PersonMapper personMapper;
    @Autowired
    private DfdwTDesignPersonService designPersonService;
    @Autowired
    private DfdwTDictDataService dfdwTDictDataService;
    @Autowired
    private DfdwTDesignProjectService projectService;

    @Autowired
    private DfdwTDesignServiceassociationService dfdwTDesignServiceassociationService;
    @Autowired
    private DfdwTDesignProjectspecialtyService projectSpecialtyService;

    /**
     * 获取审批列表记录
     *
     * @param flowDto
     * @return
     */
    @RequestMapping("/getFlowList")
    @PreAuthorize("@ss.hasAnyPermi('JDWSJ01AP01QX01,JDWSJ01AP01QX02')")
    public Result<Object> getFlowList(@RequestBody FlowDto flowDto) {
        DzccPersonEntity person = user();
        return dfdwTDesignflowService.getFlowList(person, flowDto);
    }

    /**
     * 提交
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/submitLc")
    @ResponseBody
    public Result<Object> submitLc(@RequestBody SubmitApproveDto submitApproveDto, HttpServletRequest request) {
        try {
            Integer ywId = submitApproveDto.getYwId();
            //当前流程节点Id
            Integer lcJdId = submitApproveDto.getLcJdId();
            Integer lcDefineId = submitApproveDto.getLcDefineId();

            if (lcDefineId == 20033) {
                //服务申请
                DfdwTDesignSupervisorapply entity = dfdwTDesignSupervisorapplyService.getById(ywId);
                if (entity == null) {
                    return Result.error("获取当前业务数据失败，请刷新后重试");
                }
                if (lcJdId == 200331) {
                    entity.setApproveState(1);
                    dfdwTDesignSupervisorapplyService.updateById(entity);
                }

                //短信
                if (submitApproveDto.getSendPersonId() == -1 && submitApproveDto.getSendPersonName().equals("完成")) {
                    //修改审批状态
                    entity.setApproveState(2);
                    entity.setApprovalTime(new Date());
                    dfdwTDesignSupervisorapplyService.updateById(entity);

                    //审批完结-发送短信通知申请人
                    Person person = personMapper.selectById(entity.getApplyUserId());
                    String msg = "您发起设计工代-服务申请已审批完成，详情请登录平台查看。";
                    smssendService.sendSms(person.getTelephone(), msg);

                    //审批完结-发短信通知设计工代人员
                    List<DfdwTDesignSupervisorapplyspecialty> personList = dfdwTDesignSupervisorapplyspecialtyService.getSpecialtyPersonListByYwId(ywId);
                    for (int i = 0; i < personList.size(); i++) {
                        String msg2 = entity.getApplyUserName() + "发起设计工代-服务申请已审批完成，详情请登录平台查看。";
                        smssendService.sendSms(personList.get(i).getTelephone(), msg2);
                    }

                } else {
                    //审批过程中-发送短信提醒审批人
                    Person person = personMapper.selectById(submitApproveDto.getSendPersonId());
                    String msg = entity.getApplyUserName() + "向您发起设计工代-服务申请审批，请给予办理。";
                    smssendService.sendSms(person.getTelephone(), msg);
                }

            } else if (lcDefineId == 20034) {
                //现场服务记录
                DfdwTDesignFieldservicerecord entity = dfdwTDesignFieldservicerecordService.getById(ywId);
                if (entity == null) {
                    return Result.error("获取当前业务数据失败，请刷新后重试");
                }
                if (lcJdId == 200341) {
                    entity.setApproveState(1);
                    dfdwTDesignFieldservicerecordService.updateById(entity);
                }


                //短信
                if (submitApproveDto.getSendPersonId() == -1 && submitApproveDto.getSendPersonName().equals("完成")) {
                    //修改审批状态
                    entity.setApproveState(2);
                    dfdwTDesignFieldservicerecordService.updateById(entity);


                    //如果有对应关联的问题反馈，则自动完成反馈流程
                    List<DfdwTDesignServiceassociation> dfdwTDesignServiceassociationList = dfdwTDesignServiceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                            .eq(DfdwTDesignServiceassociation::getServiceRecordId, ywId).isNotNull(DfdwTDesignServiceassociation::getProblemFeedbackId));
                    if (dfdwTDesignServiceassociationList.size() > 0) {
                        Integer backId = dfdwTDesignServiceassociationList.get(0).getProblemFeedbackId();
                        DfdwTDesignFieldproblemfeedback entityBack = dfdwTDesignFieldproblemfeedbackService.getById(backId);
                        if (entityBack != null) {
                          if(entityBack.getApproveState() == 0){
                              entityBack.setApproveState(2);
                              dfdwTDesignFieldproblemfeedbackService.updateById(entityBack);
                              if(entityBack.getIsSigner() ==1){
                                  //需要签收，则需要copy审批流程
                                  try{
                                      Map<String, Object> map = new HashMap<>();

                                      HashMap<String, String> headers = new HashMap<>();
                                      headers.put("Authorization", request.getHeader("Authorization"));
                                      String cookie = request.getHeader("Cookie");
                                      String url = "/dwFlow/flow/dfdwApprove/lcCopy";
                                      map.put("lcDefineId","20034");
                                      map.put("ywId",ywId);
                                      map.put("newLcDefineId","20035");
                                      map.put("newYwId",backId);
                                      Result r =  HttpClientUtils.doPostFlow(url, map, headers, cookie);
                                      System.out.println(r);
                                  }catch (Exception ex){
                                      ex.printStackTrace();

                                  }
                              }
                          }

                        }


                    }




                    //如果又对应关联的申请记录，给服务申请发送短信通知
                    List<DfdwTDesignServiceassociation> dfdwTDesignApply = dfdwTDesignServiceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                            .eq(DfdwTDesignServiceassociation::getServiceRecordId, ywId).isNotNull(DfdwTDesignServiceassociation::getSupervisorApplyId));
                    if(dfdwTDesignApply.size() >0){
                        Integer applyId = dfdwTDesignApply.get(0).getSupervisorApplyId();
                        DfdwTDesignSupervisorapply applyEntity = dfdwTDesignSupervisorapplyService.getById(applyId);
                        if (applyEntity!= null) {
                            //审批完结-发送短信通知申请人
                            Person person = personMapper.selectById(applyEntity.getApplyUserId());
                            String msg = entity.getSpecialtyPersonName()+"关联的服务申请已审批完成,服务记录编号："+entity.getApplyNo()+"，详情请登录平台查看。";
                            smssendService.sendSms(person.getTelephone(), msg);
                        }

                    }


                    //审批完结-发送短信通知申请人
                    Person person = personMapper.selectById(entity.getSpecialtyPersonId());
                    String msg = "您发起设计工代-现场服务记录已审批完成，详情请登录平台查看。";
                    smssendService.sendSms(person.getTelephone(), msg);
                } else {

                    //审批过程中-发送短信提醒审批人
                    Person person = personMapper.selectById(submitApproveDto.getSendPersonId());
                    String msg = entity.getSpecialtyPersonName() + "向您发起设计工代-现场服务记录审批，请给予办理。";
                    smssendService.sendSms(person.getTelephone(), msg);
                }

            } else if (lcDefineId == 20035) {
                //现场问题反馈
                DfdwTDesignFieldproblemfeedback entity = dfdwTDesignFieldproblemfeedbackService.getById(ywId);
                if (entity == null) {
                    return Result.error("获取当前业务数据失败，请刷新后重试");
                }
                if (lcJdId == 200351) {
                    entity.setApproveState(1);
                    dfdwTDesignFieldproblemfeedbackService.updateById(entity);
                }

                //短信
                if (submitApproveDto.getSendPersonId() == -1 && submitApproveDto.getSendPersonName().equals("完成")) {
                    //修改审批状态
                    entity.setApproveState(2);
//                    entity.setSignerDate(new Date());
//                    entity.setSignerOpinion(submitApproveDto.getFeeds());
                    dfdwTDesignFieldproblemfeedbackService.updateById(entity);

                    //审批完结-发送短信通知申请人
                    Person person = personMapper.selectById(entity.getSpecialtyPersonId());
                    String msg = "您发起设计工代-现场问题反馈已审批完成，详情请登录平台查看。";
                    smssendService.sendSms(person.getTelephone(), msg);

                    //审批完结-发送短信通知签收人
                    Person person2 = personMapper.selectById(entity.getSignerUserId());
                    String msg2 = entity.getSpecialtyPersonName() + "发起设计工代-现场问题反馈已审批完成，请及时签收，详情请登录平台查看。";
                    smssendService.sendSms(person2.getTelephone(), msg2);
                } else {

                    //审批过程中-发送短信提醒审批人
                    Person person = personMapper.selectById(submitApproveDto.getSendPersonId());
                    String msg = entity.getSpecialtyPersonName() + "向您发起设计工代-现场问题反馈审批，请给予办理。";
                    smssendService.sendSms(person.getTelephone(), msg);
                }
            }


        } catch (Exception ex) {
            return Result.error("提交审批失败：" + ex.getMessage());
        }
        return Result.ok("提交审批成功");
    }


    /**
     * 驳回-退回到申报
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/rollBackForApply")
    @ResponseBody
    public Result rollBackForApply(@RequestBody SubmitApproveDto submitApproveDto) {
        try {
            Integer ywId = submitApproveDto.getYwId();
            Integer lcDefineId = submitApproveDto.getLcDefineId();
            if (lcDefineId == 20033) {
                //服务申请
                DfdwTDesignSupervisorapply entity = dfdwTDesignSupervisorapplyService.getById(ywId);
                if (entity == null) {
                    return Result.error("获取当前业务数据失败，请刷新后重试");
                }

                entity.setApproveState(3);
                dfdwTDesignSupervisorapplyService.updateById(entity);

                //审批完结-发送短信通知申请人
                Person person = personMapper.selectById(entity.getApplyUserId());
                String msg = "您发起设计工代-服务申请已被驳回，请及时查看。";
                smssendService.sendSms(person.getTelephone(), msg);
            } else if (lcDefineId == 20034) {
                //现场服务记录
                DfdwTDesignFieldservicerecord entity = dfdwTDesignFieldservicerecordService.getById(ywId);
                if (entity == null) {
                    return Result.error("获取当前业务数据失败，请刷新后重试");
                }
                entity.setApproveState(3);
                dfdwTDesignFieldservicerecordService.updateById(entity);

                //审批完结-发送短信通知申请人
                Person person = personMapper.selectById(entity.getSpecialtyPersonId());
                String msg = "您发起设计工代-现场服务记录已被驳回，请及时查看。";
                smssendService.sendSms(person.getTelephone(), msg);
            } else if (lcDefineId == 20035) {
                //现场问题反馈
                DfdwTDesignFieldproblemfeedback entity = dfdwTDesignFieldproblemfeedbackService.getById(ywId);
                if (entity == null) {
                    return Result.error("获取当前业务数据失败，请刷新后重试");
                }
                entity.setApproveState(3);
                dfdwTDesignFieldproblemfeedbackService.updateById(entity);

                //审批完结-发送短信通知申请人
                Person person = personMapper.selectById(entity.getSpecialtyPersonId());
                String msg = "您发起设计工代-场问题反馈已被驳回，请及时查看。";
                smssendService.sendSms(person.getTelephone(), msg);
            }


        } catch (Exception ex) {
            return Result.error("提交审批失败：" + ex.getMessage());
        }
        return Result.ok("提交审批成功");
    }


    /**
     * 根据服务申请所选的专业，获取人员信息
     *
     * @param ywId
     * @return
     */
    @RequestMapping("/getSpecialtyUser")
    @ResponseBody
    public Result getSpecialtyUser(@RequestParam(value = "ywId") Integer ywId) {
        DfdwTDesignSupervisorapply designSupervisorapply = dfdwTDesignSupervisorapplyService.getById(ywId);
        //获取专业
        String specialty = designSupervisorapply.getSpecialty();
        specialty = specialty.replaceAll("\\[", "").replaceAll("\\]", "").replaceAll("\"", "");
        List<String> resultList = Arrays.stream(specialty.split(","))
                .collect(Collectors.toList());
        //获取专业对应名称信息
        List<DfdwTDictData> specialtyList = dfdwTDictDataService.list(new LambdaQueryWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "sjgd-specialty").in(DfdwTDictData::getValue, resultList));


        List<TreeNode> treeNodeList = new ArrayList<>();
        List<DfdwTDesignProjectspecialty> list = projectSpecialtyService.list(new LambdaQueryWrapper<DfdwTDesignProjectspecialty>()
                .eq(DfdwTDesignProjectspecialty::getDesignProjectId, designSupervisorapply.getDesignProjectId()));
        List<Integer> collect = list.stream().map(s -> s.getDesignUserId()).collect(Collectors.toList());
        //获取各专业下的人员信息
        for (int i = 0; i < specialtyList.size(); i++) {
            TreeNode treeNode = new TreeNode();
            String specialtyVal = specialtyList.get(i).getValue();
            treeNode.setValue(specialtyVal);
            treeNode.setLabel(specialtyList.get(i).getLabel());

            List<TreeNodeChild> children = new ArrayList<>();
            //获取人员并拼接
            List<DfdwTDesignPerson> personList = designPersonService.list(new LambdaQueryWrapper<DfdwTDesignPerson>()
                    .eq(DfdwTDesignPerson::getSpecialty, specialtyVal).eq(DfdwTDesignPerson::getState, 1)
                    .in(DfdwTDesignPerson::getSrcUserId,collect));
            for (int j = 0; j < personList.size(); j++) {
                TreeNodeChild child = new TreeNodeChild();
                child.setValue(personList.get(j).getSrcUserId() + "");
                child.setLabel(personList.get(j).getUserName());
                children.add(child);
            }
            treeNode.setChildren(children);
            treeNodeList.add(treeNode);
        }
        return Result.ok(treeNodeList);
    }


    /**
     * 前置校验
     *
     * @return
     */
    @RequestMapping("/checkSubmit")
    @ResponseBody
    public Result checkSubmit(@RequestBody SubmitApproveDto submitApproveDto) {
        Integer ywId = submitApproveDto.getYwId();
        //当前流程
        Integer lcDefineId = submitApproveDto.getLcDefineId();
        String approvalOpinion = submitApproveDto.getApprovalOpinion();
        if (lcDefineId == 20033 && approvalOpinion.equals("agree")) {
            //服务申请
            //保存主表基本信息
            DfdwTDesignSupervisorapply entity = dfdwTDesignSupervisorapplyService.getById(ywId);
            entity.setReplyArriveDays(submitApproveDto.getReplyArriveDays());
            //获取项目设总
            DfdwTDesignProject project = projectService.getById(entity.getDesignProjectId());
            if (project == null) {
                return Result.error("获取项目信息失败");
            }
            //签收人 = 自动带入项目设总
            entity.setSignerUserId(project.getDesignDirectorUserId());
            entity.setSignerUserName(project.getDesignDirectorUserName());
            dfdwTDesignSupervisorapplyService.updateById(entity);


            //保存设计工代人员
            List<DfdwTDesignSupervisorapplyspecialty> designUser = submitApproveDto.getDesignUser();
            if (designUser.size() == 0) {
                return Result.error("请至少有一条设计工代人员");
            }
            for (DfdwTDesignSupervisorapplyspecialty design : designUser) {
                design.setId(null);
                design.setSupervisorApplyId(ywId);
                design.setCreateId(user().getId());
                design.setCreateTime(new Date());
                dfdwTDesignSupervisorapplyspecialtyService.save(design);
            }
        }
        return Result.ok();
    }
}
