package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName V_DFDW_T_Design_SupervisorApply
 */
@TableName(value ="V_DFDW_T_Design_SupervisorApply")
@Data
public class VDfdwTDesignSupervisorapply implements Serializable {
    /**
     * 
     */
    @TableField(value = "id")
    private Integer id;

    /**
     * 
     */
    @TableField(value = "designProjectId")
    private Integer designProjectId;

    /**
     * 
     */
    @TableField(value = "applyNo")
    private String applyNo;

    /**
     * 
     */
    @TableField(value = "designUnit")
    private String designUnit;

    /**
     * 
     */
    @TableField(value = "content")
    private String content;

    /**
     * 
     */
    @TableField(value = "specialty")
    private String specialty;

    /**
     * 
     */
    @TableField(value = "applyArriveDate")
    private Date applyArriveDate;

    /**
     * 
     */
    @TableField(value = "applyUserId")
    private Integer applyUserId;

    /**
     * 
     */
    @TableField(value = "applyUserName")
    private String applyUserName;

    /**
     * 
     */
    @TableField(value = "replyArriveDays")
    private Integer replyArriveDays;

    /**
     * 
     */
    @TableField(value = "signerUserId")
    private Integer signerUserId;

    /**
     * 
     */
    @TableField(value = "signerUserName")
    private String signerUserName;

    /**
     * 
     */
    @TableField(value = "approveState")
    private Integer approveState;

    /**
     * 
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "createId")
    private Integer createId;

    /**
     * 
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 
     */
    @TableField(value = "updateTime")
    private Date updateTime;

    /**
     * 
     */
    @TableField(value = "deleted")
    private String deleted;

    /**
     * 
     */
    @TableField(value = "updaterId")
    private Integer updaterId;

    /**
     * 
     */
    @TableField(value = "lcDefineID")
    private Integer lcDefineID;

    /**
     * 
     */
    @TableField(value = "lcName")
    private String lcName;

    /**
     * 
     */
    @TableField(value = "ywID")
    private Integer ywID;

    /**
     * 
     */
    @TableField(value = "sendPerson")
    private String sendPerson;

    /**
     * 
     */
    @TableField(value = "sendPersonZgh")
    private String sendPersonZgh;

    /**
     * 
     */
    @TableField(value = "AllPersonZgh")
    private String allPersonZgh;

    /**
     * 
     */
    @TableField(value = "isMany")
    private Integer isMany;

    /**
     * 
     */
    @TableField(value = "lcJdmc")
    private String lcJdmc;

    /**
     * 
     */
    @TableField(value = "lcJdid")
    private Integer lcJdid;

    /**
     * 
     */
    @TableField(value = "lcIsback")
    private Integer lcIsback;

    /**
     * 
     */
    @TableField(value = "lcTojdid")
    private String lcTojdid;

    /**
     * 
     */
    @TableField(value = "number")
    private Integer number;

    /**
     * 
     */
    @TableField(value = "BXType")
    private String BXType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}