package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value ="v_project_t")
public class VProjectT extends DfdwBaseEntity implements Serializable {
        @TableId(value = "project_id", type = IdType.AUTO)
        private Long projectId;                    // ID
        @TableField("project_code")
        private String projectCode;                // 项目编号（XMBH2016082701）

        @TableField("project_name")
        private String projectName;                // 项目名称

        @TableField("project_short_name")
        private String projectShortName;           // 项目缩写

        @TableField("project_place")
        private String projectPlace;               // 工程地点

        @TableField("survey")
        private String survey;                     // 工程概况

        @TableField("supervision_scopen")
        private String supervisionScopen;          // 监理范围

        @TableField("supervisor_company_id")
        private Long supervisorCompanyId;          // 监理公司ID

        @TableField("project_group_leader_id")
        private Long projectGroupLeaderId;         // 项目组长

        @TableField("project_group_leader_id_to")
        private String projectGroupLeaderIdTo;     // 项目组长(多选用户,逗号隔开)

        @TableField("director_id")
        private Long directorId;                   // 监理

        @TableField("director_id_to")
        private String directorIdTo;               // 总监监理(多选用户,逗号隔开)

        @TableField("owner_unit_id")
        private Long ownerUnitId;                  // 业主单位ID

        @TableField("design_unit_id")
        private Long designUnitId;                 // 设计单位

        @TableField("construction_unit_id")
        private Long constructionUnitId;           // 施工单位

        @TableField("is_sub_package")
        private String isSubPackage;               // 是否分包

        @TableField("construction_sub_unit_id")
        private Long constructionSubUnitId;        // 施工分包单位

        @TableField("construction_sub_unit_id_to")
        private String constructionSubUnitIdTo;    // 施工分包单位(多选)

        @TableField("plan_start_date")
        private Date planStartDate;                // 计划开工日期

        @TableField("plan_finish_date")
        private Date planFinishDate;               // 计划竣工日期

        @TableField("physical_start_date")
        private Date physicalStartDate;            // 实际开始日期

        @TableField("physical_finish_date")
        private Date physicalFinishDate;           // 实际竣工日期

        @TableField("remark")
        private String remark;                     // 备注

        @TableField("status")
        private String status;                     // 状态

        @TableField("create_by")
        private Long createBy;                     // 创建人

        @TableField("creation_date")
        private Date creationDate;                 // 创建时间

        @TableField("last_update_by")
        private Long lastUpdateBy;                 // 修改人

        @TableField("last_update_date")
        private Date lastUpdateDate;               // 修改时间

        @TableField("version_number")
        private Long versionNumber;                // 版本号

        @TableField("enable")
        private String enable;                     // 删除标志

        @TableField("project_longitude")
        private Double projectLongitude;           // 经度

        @TableField("project_latitude")
        private Double projectLatitude;            // 纬度

        @TableField("itudeFlag")
        private String itudeFlag;                  // 经纬度标志

        @TableField("statusStop")
        private Integer statusStop;                // 是否停工 1.是 2.否

        @TableField("network")
        private Integer network;                   // 1.主网 2.配网

        @TableField("project_group_code")
        private String projectGroupCode;           // 项目班组key
        @TableField(exist = false)
        private String keyword;
    }

