package com.soft.gcc.xtbg.sjgd.mapper;

import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_ServiceAssociation(现场服务关联表)】的数据库操作Mapper
* @createDate 2025-02-17 16:51:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation
*/
public interface DfdwTDesignServiceassociationMapper extends BaseMapper<DfdwTDesignServiceassociation> {

    void deleteFwsqByIds(@Param("idList") List<Integer> idList);

    void updateFwsqByIds(@Param("idList") List<Integer> idList);

    void deleteFwjlByIds(@Param("idList") List<Integer> idList);

    void updateFwjlByIds(@Param("idList") List<Integer> idList);
}




