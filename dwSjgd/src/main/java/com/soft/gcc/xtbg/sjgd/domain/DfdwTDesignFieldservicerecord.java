package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.entity.PageEntity;
import lombok.Data;

/**
 * 设计工代-现场服务记录
 *
 * @TableName DFDW_T_Design_FieldServiceRecord
 */
@TableName(value = "DFDW_T_Design_FieldServiceRecord")
@Data
public class DfdwTDesignFieldservicerecord extends PageEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设计工代项目Id
     */
    @TableField(value = "designProjectId")
    private Integer designProjectId;

    /**
     * 单据编号（自动生成）
     */
    @TableField(value = "applyNo")
    private String applyNo;

    /**
     * 设计单位
     */
    @TableField(value = "designUnit")
    private String designUnit;

    /**
     * 填报日期
     */
    @TableField(value = "applyDate")
    private Date applyDate;

    /**
     * 设计工代专业人员（自动带入操作人员）
     */
    @TableField(value = "specialtyPersonId")
    private Integer specialtyPersonId;

    @TableField(value = "specialtyPersonName")
    private String specialtyPersonName;

    /**
     * 工程阶段
     */
    @TableField(value = "engineeringPhase")
    private String engineeringPhase;

    /**
     * 节点（字典）
     */
    @TableField(value = "node")
    private String node;

    /**
     * 现场情况描述
     */
    @TableField(value = "fieldCondition")
    private String fieldCondition;

    /**
     * 发现问题及解决措施
     */
    @TableField(value = "problemsAndMeasures")
    private String problemsAndMeasures;

    /**
     * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(value = "approveState")
    private Integer approveState;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 设计工代项目名称
     */
    @TableField(exist = false)
    private String projectName;

    @TableField(exist = false)
    private List<String> nodes;
    @TableField(exist = false)
    private String recordFiles;

    /**
     * Id列表
     */
    @TableField(exist = false)
    private List<Integer> ids;
    /**
     * 下载类型
     */
    @TableField(exist = false)
    private String downloadType;
    //项目的设总Id
    @TableField(exist = false)
    private Integer queryUserId;
    /**
     * 服务申请-服务记录-问题发聩关联表
     */
    @TableField(exist = false)
    private List<DfdwTDesignServiceassociation> serviceassociationList;

    /**
     * 服务申请-服务记录-问题发聩关联表
     */
    @TableField(exist = false)
    private List<DfdwTDesignServiceassociation> supervisorApplyList;

    /**
     * 当前登录用户
     */
    @TableField(exist = false)
    private DzccPersonEntity currentLoginUser;

    /**
     * 导出excel用 填报日期
     */
    @TableField(exist = false)
    private String applyDateStr;
    /**
     * 导出excel用 审批状态
     */
    @TableField(exist = false)
    private String approveStateStr;
    /**
     * 导出excel用 节点
     */
    @TableField(exist = false)
    private String nodeLabels;

    /**
     * 关联服务申请编号
     */
    @TableField(exist = false)
    private String supervisorApplyNo;
    /**
     * 关联问题反馈编号
     */
    @TableField(exist = false)
    private String problemFeedbackNo;

    @TableField(exist = false)
    private String type;
    /**
     * 设总的项目Ids
     */
    @TableField(exist = false)
    private List<Integer> DesignDirectorDesignProjectIds;

    @TableField(exist = false)
    private Boolean isShowAll;
    /**
     * 申请单Ids
     */
    @TableField(exist = false)
    private List<Integer> applyServiceRecordIds;

    /**
     * 反馈是签收人关联的服务记录可见
     */
    @TableField(exist = false)
    private List<Integer> signServiceRecordIds;
}
