package com.soft.gcc.xtbg.sjgd.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignSupervisorapplyspecialtyService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/sjgd/supervisorApplySpecialty")
public class DfdwTDesignSupervisorapplyspecialtyController  extends BaseController {


    @Resource
    DfdwTDesignSupervisorapplyspecialtyService dfdwTDesignSupervisorapplyspecialtyService;

    /**
     * 根据服务申请id 获取设计工代人员列表
     * @param dfdwTDesignSupervisorapplyspecialty
     * @return
     */
    @PostMapping("getPersonList")
    public Result<Object> getPersonList(@RequestBody DfdwTDesignSupervisorapplyspecialty dfdwTDesignSupervisorapplyspecialty){
        if (dfdwTDesignSupervisorapplyspecialty.getSupervisorApplyId() == null){
            return Result.error("参数为空!");
        }
        return Result.ok(dfdwTDesignSupervisorapplyspecialtyService.getPersonListBySupervisorApplyId(dfdwTDesignSupervisorapplyspecialty));
    }
}
