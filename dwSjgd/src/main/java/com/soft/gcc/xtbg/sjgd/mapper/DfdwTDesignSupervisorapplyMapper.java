package com.soft.gcc.xtbg.sjgd.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_SupervisorApply(设计工代-服务申请)】的数据库操作Mapper
* @createDate 2024-12-30 16:40:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply
*/
public interface DfdwTDesignSupervisorapplyMapper extends BaseMapper<DfdwTDesignSupervisorapply> {
    IPage<DfdwTDesignSupervisorapply> listPage(@Param("page") Page<DfdwTDesignSupervisorapply> page, @Param("query") DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply);

    DfdwTDesignSupervisorapply selectInfoById(@Param("id") Integer Id);

    DfdwTDesignSupervisorapply getMaxApplyNo( @Param("applyNo") String applyNo);

    List<DfdwTDesignSupervisorapply> queryList(@Param("query") DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply);

    IPage<DfdwTDesignSupervisorapply> getFwsqAssociationList(
            @Param("page") IPage<DfdwTDesignFieldservicerecord> page,
            @Param("query") DfdwTDesignSupervisorapply supervisorapply
    );

    String selectPersonUserPhoneById(@Param("id") Integer id);

}




