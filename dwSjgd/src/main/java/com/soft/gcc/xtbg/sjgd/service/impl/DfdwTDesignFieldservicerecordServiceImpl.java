package com.soft.gcc.xtbg.sjgd.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.mapper.PersonMapper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.common.t_smssend.service.TSmssendService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignFieldservicerecordService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignFieldservicerecordMapper;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignServiceassociationService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignSupervisorapplyService;
import com.soft.gcc.xtbg.sjgd.util.PdfUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.catalina.User;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.apache.fop.util.CharUtilities.padLeft;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_Design_FieldServiceRecord(设计工代-现场服务记录)】的数据库操作Service实现
 * @createDate 2024-12-30 16:40:28
 */
@Service
public class DfdwTDesignFieldservicerecordServiceImpl extends ServiceImpl<DfdwTDesignFieldservicerecordMapper, DfdwTDesignFieldservicerecord>
        implements DfdwTDesignFieldservicerecordService {

    private static final Logger log = LoggerFactory.getLogger(DfdwTDesignFieldservicerecordServiceImpl.class);

    @Autowired
    private DfdwTDesignFieldservicerecordService serviceRecordService;
    @Autowired
    private TFileService fileService;
    @Resource
    private DfdwTDictDataService dfdwTDictDataService;
    @Resource
    private DfdwTDesignProjectService designProjectService;
    @Resource
    DfdwTDesignServiceassociationService serviceassociationService;
    @Resource
    private DfdwTDesignProjectService projectService;

    @Resource
    private DfdwTDesignSupervisorapplyService applyservice;
    @Autowired
    private TSmssendService smssendService;
    @Autowired
    private PersonMapper personMapper;

    @Override
    public IPage<DfdwTDesignFieldservicerecord> getPageList(Page<DfdwTDesignFieldservicerecord> page, DfdwTDesignFieldservicerecord serviceRecord) {
        return baseMapper.getPageList(page, serviceRecord);
    }

    @Override
    public void addOrEdit(DfdwTDesignFieldservicerecord serviceRecord) {
        if (serviceRecord.getId() == null) {
            serviceRecord.setCreateTime(new Date());
            serviceRecord.setApproveState(0);
            //生成单据编号
            serviceRecord.setApplyNo(getBh());
        }
        serviceRecord.setNode(String.join(",", serviceRecord.getNodes()));
        this.saveOrUpdate(serviceRecord);
    }

    @Override
    public DfdwTDesignFieldservicerecord getDetail(Integer id) {
        DfdwTDesignFieldservicerecord record = getById(id);
        if (record != null) {
            DfdwTDesignProject project = designProjectService.getById(record.getDesignProjectId());
            record.setProjectName(project.getProjectName());
            List<TFile> files = fileService.list(
                    new LambdaQueryWrapper<TFile>()
                            .eq(TFile::getProjectid, id)
                            .eq(TFile::getFunctionid, 20034)
                            .eq(TFile::getType, "fwjl"));
            List<String> list = new ArrayList<>();
            files.forEach(s -> {
                list.add(s.getFilepath());
            });
            record.setRecordFiles(String.join(",", list));
            List<DfdwTDesignServiceassociation> serviceRecordList = serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                    .eq(DfdwTDesignServiceassociation::getServiceRecordId, id)
                    .isNotNull(DfdwTDesignServiceassociation::getProblemFeedbackId)
            );
            record.setServiceassociationList(serviceRecordList);

            List<DfdwTDesignServiceassociation> supervisorApplyList = serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                    .eq(DfdwTDesignServiceassociation::getServiceRecordId, id)
                    .isNotNull(DfdwTDesignServiceassociation::getSupervisorApplyId)
            );
            record.setSupervisorApplyList(supervisorApplyList);
        }


        return record;
    }

    public String getBh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());

        String gcBH = "FWJL-" + dateStr;
        DfdwTDesignFieldservicerecord maxEn = baseMapper.getMaxApplyNo(gcBH);
        if (maxEn == null) {
            gcBH = gcBH + "0001";
        } else {
            //从12位截取后面
            String maxReturnNo = maxEn.getApplyNo();
            String noStr = maxReturnNo.substring(13);
            Integer no = Integer.parseInt(noStr) + 1;
            noStr = String.valueOf(no);
            if (no > 9 && no < 100) {
                noStr = "00" + noStr;
            } else if (no > 99 && no < 1000) {
                noStr = "0" + noStr;
            } else if (no > 999 && no < 10000) {
                noStr = noStr;
            } else {
                noStr = "000" + noStr;
            }

            gcBH = gcBH + noStr;
        }
        return gcBH;
    }

    @Override
    public void addProjectFile(DfdwTDesignFieldservicerecord serviceRecord) {
        //获取文件列表
        if (StringUtils.checkValNotNull(serviceRecord.getRecordFiles())) {
            List<String> fileList = Arrays.stream(serviceRecord.getRecordFiles().split(","))
                    .collect(Collectors.toList());
            fileService.update(new LambdaUpdateWrapper<TFile>()
                    .set(TFile::getProjectid, serviceRecord.getId())
                    .eq(TFile::getFunctionid, 20034) //服务记录
                    .in(TFile::getFilepath, fileList)
            );
        }
    }

    @Override
    public void updateProjectFile(DfdwTDesignFieldservicerecord serviceRecord) {
        //获取文件列表
        if (StringUtils.checkValNotNull(serviceRecord.getRecordFiles())) {
            //当前需要update 的文件
            List<String> newFileList = Arrays.stream(serviceRecord.getRecordFiles().split(","))
                    .collect(Collectors.toList());

            //获取数据库中的老数据
            List<TFile> nowFileList = fileService.list(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getProjectid, serviceRecord.getId())
                    .eq(TFile::getFunctionid, 20034)
                    .eq(TFile::getHjid, 1)
                    .eq(TFile::getType, "fwjl")
            );
            List<String> oldFileList = nowFileList.stream().map(s -> s.getFilepath()).collect(Collectors.toList());

            //获取需要删除的数据
            List<String> deleted = oldFileList.stream()
                    .filter(item -> !newFileList.contains(item))
                    .collect(Collectors.toList());

            //获取需要update 的数据
            List<String> updateData = newFileList.stream()
                    .filter(item -> !oldFileList.contains(item))
                    .collect(Collectors.toList());
            if (deleted.size() > 0) {
                fileService.remove(new LambdaQueryWrapper<TFile>()
                        .eq(TFile::getProjectid, serviceRecord.getId())
                        .eq(TFile::getFunctionid, 20034)
                        .eq(TFile::getType, "fwjl")
                        .in(TFile::getFilepath, deleted));
            }
            if (updateData.size() > 0) {
                fileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid, serviceRecord.getId())
                        .eq(TFile::getType, "fwjl")
                        .eq(TFile::getFunctionid, 20034)
                        .in(TFile::getFilepath, updateData)
                );
            }

        } else {
            fileService.remove(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getProjectid, serviceRecord.getId())
                    .eq(TFile::getFunctionid, 20034)
                    .eq(TFile::getType, "fwjl")
            );

        }
    }

    @Override
    public void exportPdf(List<Integer> ids, HttpServletResponse response, DzccPersonEntity user) {
        // 创建临时文件目录
        String localPath = System.getProperty("java.io.tmpdir");
        File tempDir = new File(localPath + "/design/pdf/");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        // 水印PDF目录
        File watermarkDir = new File(localPath + "/yykj/dzcc/downloads/water/jtsjqx/");
        if (!watermarkDir.exists()) {
            watermarkDir.mkdirs();
        }

        List<String> pdfPaths = new ArrayList<>();
        List<String> watermarkPaths = new ArrayList<>();
        Map<String, Integer> fileNameCounter = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        DfdwTDesignFieldservicerecord queryRecord = new DfdwTDesignFieldservicerecord();
        queryRecord.setIds(ids);
        List<DfdwTDesignFieldservicerecord> allList = serviceRecordService.getAllList(queryRecord);

        try (ServletOutputStream servletOutputStream = response.getOutputStream()) {
            for (DfdwTDesignFieldservicerecord record : allList) {
                if (record == null) continue;

                long time = System.currentTimeMillis();
                String pdfPath = localPath + "/design/pdf/" + "fwjl" + "_" + time + ".pdf";
                String watermarkPath = localPath + "/yykj/dzcc/downloads/water/jtsjqx/" + "fwjl" + "_" + time + ".pdf";
                pdfPaths.add(pdfPath);
                watermarkPaths.add(watermarkPath);
                LocalDate applyDate = record.getApplyDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                // 生成PDF
                try (FileOutputStream fos = new FileOutputStream(pdfPath)) {
                    Document document = new Document(PageSize.A4);
                    PdfWriter.getInstance(document, fos);

                    // 设置中文字体
                    BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                    Font normalFont = new Font(baseFont, 12, Font.NORMAL);
                    Font smallFont = new Font(baseFont, 9, Font.NORMAL);
                    document.open();

                    // 添加第一页
                    Paragraph firstPageTitle = new Paragraph("设计工代现场服务记录", new Font(baseFont, 18, Font.BOLD));
                    firstPageTitle.setAlignment(Element.ALIGN_CENTER);
                    firstPageTitle.setSpacingAfter(50f);
                    document.add(firstPageTitle);

                    // 添加工程名称和本册编号
                    PdfPTable topTable = new PdfPTable(1);
                    topTable.setWidthPercentage(80);
                    topTable.setHorizontalAlignment(Element.ALIGN_CENTER);
                    topTable.setSpacingBefore(30f);

                    addFormField(topTable, "工程名称：", record.getProjectName(), normalFont);
                    addFormField(topTable, "本册编号：", "____________________", normalFont);
                    document.add(topTable);

                    // 添加垂直空间
                    document.add(new Paragraph("\n\n\n\n\n\n\n\n\n\n\n\n"));

                    // 添加设计单位和日期
                    PdfPTable middleTable = new PdfPTable(1);
                    middleTable.setWidthPercentage(80);
                    middleTable.setHorizontalAlignment(Element.ALIGN_CENTER);

                    addFormField(middleTable, "设计单位：", record.getDesignUnit(), normalFont);
                    addFormField(middleTable, "日    期：", applyDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")), normalFont);
                    document.add(middleTable);

                    // 添加分页符
                    document.newPage();

                    // 添加标题
                    Paragraph title = new Paragraph("设计工代服务记录", new Font(baseFont, 16, Font.BOLD));
                    title.setAlignment(Element.ALIGN_CENTER);
                    document.add(title);
                    document.add(new Paragraph("\n"));

                    // 添加基本信息表格
                    PdfPTable infoTable = new PdfPTable(4);
                    infoTable.setWidthPercentage(100);

                    addTableCell(infoTable, "工代人员：", normalFont);
                    addTableCell(infoTable, record.getSpecialtyPersonName(), normalFont);
                    addTableCell(infoTable, "日期：", normalFont);
                    addTableCell(infoTable, applyDate.toString(), normalFont);

                    addTableCell(infoTable, "工程阶段：", normalFont);
                    addTableCell(infoTable, record.getEngineeringPhase(), normalFont, 3);

                    document.add(infoTable);

                    // 服务节点表格
                    PdfPTable serviceTable = new PdfPTable(1);
                    serviceTable.setWidthPercentage(100);

                    PdfPCell subCell = new PdfPCell();
                    subCell.setPadding(5);

                    // 变电工程服务节点
                    Paragraph substationTitle = new Paragraph("变电工程服务节点：", normalFont);
                    subCell.addElement(substationTitle);

                    PdfPTable checkboxTable1 = new PdfPTable(3);
                    checkboxTable1.setWidthPercentage(100);

                    List<String> substationItems = getPdfNodes("sjgd-electricity-node", record);
                    for (String item : substationItems) {
                        PdfPCell checkCell = new PdfPCell(new Phrase(item, smallFont));
                        checkCell.setBorder(Rectangle.NO_BORDER);
                        checkboxTable1.addCell(checkCell);
                    }
                    // 如果项目数不能被3整除，添加空单元格填充最后一行
                    int remainder = substationItems.size() % 3;
                    if (remainder > 0) {
                        for (int i = 0; i < (3 - remainder); i++) {
                            PdfPCell emptyCell = new PdfPCell();
                            emptyCell.setBorder(Rectangle.NO_BORDER);
                            checkboxTable1.addCell(emptyCell);
                        }
                    }
                    subCell.addElement(checkboxTable1);

                    // 线路工程服务节点
                    Paragraph lineTitle = new Paragraph("\n线路工程服务节点：", normalFont);
                    subCell.addElement(lineTitle);

                    PdfPTable checkboxTable2 = new PdfPTable(3);
                    checkboxTable2.setWidthPercentage(100);

                    List<String> lineItems = getPdfNodes("sjgd-line-node", record);
                    for (String item : lineItems) {
                        PdfPCell checkCell = new PdfPCell(new Phrase(item, smallFont));
                        checkCell.setBorder(Rectangle.NO_BORDER);
                        checkboxTable2.addCell(checkCell);
                    }
                    // 如果项目数不能被3整除，添加空单元格填充最后一行
                    int remainder2 = lineItems.size() % 3;
                    if (remainder2 > 0) {
                        for (int i = 0; i < (3 - remainder2); i++) {
                            PdfPCell emptyCell = new PdfPCell();
                            emptyCell.setBorder(Rectangle.NO_BORDER);
                            checkboxTable2.addCell(emptyCell);
                        }
                    }
                    subCell.addElement(checkboxTable2);

                    serviceTable.addCell(subCell);
                    document.add(serviceTable);

                    // 现场情况描述模块
                    PdfPTable situationTable = new PdfPTable(1);
                    situationTable.setWidthPercentage(100);

                    PdfPCell situationCell = new PdfPCell();
                    situationCell.setPadding(5);
                    situationCell.setFixedHeight(200f);
                    situationCell.setNoWrap(false);

                    Paragraph situationContent = new Paragraph();
                    situationContent.add(new Phrase("现场情况描述：\n", normalFont));
                    if (record.getFieldCondition() != null) {
                        situationContent.add(new Phrase(record.getFieldCondition(), smallFont));
                    }
                    situationCell.addElement(situationContent);

                    situationTable.addCell(situationCell);
                    document.add(situationTable);

                    // 发现问题及解决措施模块
                    PdfPTable problemTable = new PdfPTable(1);
                    problemTable.setWidthPercentage(100);

                    PdfPCell problemCell = new PdfPCell();
                    problemCell.setPadding(5);
                    problemCell.setFixedHeight(200f);
                    problemCell.setNoWrap(false);

                    Paragraph problemContent = new Paragraph();
                    problemContent.add(new Phrase("发现问题及解决措施（如有）：\n", normalFont));
                    if (record.getProblemsAndMeasures() != null) {
                        problemContent.add(new Phrase(record.getProblemsAndMeasures(), smallFont));
                    }
                    problemCell.addElement(problemContent);

                    problemTable.addCell(problemCell);
                    document.add(problemTable);

                    document.add(new Paragraph("\n"));

                    // 添加注释
                    Paragraph note = new Paragraph("注：本表一式四份（施工、监理、业主项目部和设计单位各一份，由设计单位存档）。", normalFont);
                    document.add(note);
                    document.close();
                }

                // 添加水印
                List<String> watermark = Arrays.asList(
                        user.getRealName(),
                        user.getGroupName(),
                        sdf.format(new Date())
                );
                PdfUtil.pdfAddWaterMark(pdfPath, watermarkPath, watermark);
            }
            if (watermarkPaths.size() == 1) {
                byte[] buffer = new byte[1024];
                File file = new File(watermarkPaths.get(0));
                if (!file.exists()) {
                    log.info("设计工代服务记录文件不存在：" + watermarkPaths.get(0));
                }
                try (FileInputStream fis = new FileInputStream(file); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    int i = bis.read(buffer);
                    while (i != -1) {
                        servletOutputStream.write(buffer, 0, i);
                        i = bis.read(buffer);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("设计工代服务记录单文件下载失败：" + e.getMessage());
                }
            } else {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(servletOutputStream)) {
                    for (int i = 0; i < watermarkPaths.size(); i++) {
                        // 处理ZIP文件名
                        String baseFileName = allList.get(i).getProjectName() + "服务记录" + "(" + allList.get(i).getApplyNo() + ").pdf";
                        String filePath = watermarkPaths.get(i);
                        // 创建ZIP条目
                        ZipEntry zipEntry = new ZipEntry(baseFileName);
                        zipOutputStream.putNextEntry(zipEntry);

                        // 读取文件并写入到ZIP输出流
                        byte[] pdfFile = FileUtil.readBytes(filePath);
                        zipOutputStream.write(pdfFile);
                        zipOutputStream.closeEntry();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new IOException("电子出车详情多文件压缩失败：" + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("生成PDF文件失败", e);
            throw new RuntimeException("生成PDF文件失败：" + e.getMessage());
        } finally {
            // 清理临时文件
            for (String path : pdfPaths) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
            for (String path : watermarkPaths) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
        }
    }

    @Override
    public List<DfdwTDesignFieldservicerecord> getAllList(DfdwTDesignFieldservicerecord serviceRecord) {
        return baseMapper.getAllList(serviceRecord);
    }

    @Override
    public String serviceAssociation(DfdwTDesignFieldservicerecord serviceRecord, PersonEntity person) {
        List<DfdwTDesignServiceassociation> newList = serviceRecord.getServiceassociationList();
        List<DfdwTDesignServiceassociation> oldList = serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                .eq(DfdwTDesignServiceassociation::getServiceRecordId, serviceRecord.getId())
        );
        if (!newList.isEmpty() && !oldList.isEmpty()) {
            // 新老不空则更新数据
            DfdwTDesignServiceassociation entity = newList.get(0);
            entity.setId(oldList.get(0).getId());
            entity.setUpdaterId(person.getId());
            entity.setUpdateTime(new Date());
            serviceassociationService.updateById(entity);
        } else if (newList.isEmpty() && !oldList.isEmpty()) {
            // 新空老不空则删除数据
            if ("fwsq".equals(serviceRecord.getType())) {
                if (oldList.get(0).getProblemFeedbackId() == null) {
                    serviceassociationService.removeById(oldList.get(0).getId());
                } else {
                    serviceassociationService.update(new LambdaUpdateWrapper<DfdwTDesignServiceassociation>()
                            .set(DfdwTDesignServiceassociation::getSupervisorApplyId, null)
                            .set(DfdwTDesignServiceassociation::getUpdaterId, person.getId())
                            .set(DfdwTDesignServiceassociation::getUpdateTime, new Date())
                            .eq(DfdwTDesignServiceassociation::getId, oldList.get(0).getId())
                    );
                }
            } else if ("wtfk".equals(serviceRecord.getType())) {
                if (oldList.get(0).getSupervisorApplyId() == null) {
                    serviceassociationService.removeById(oldList.get(0).getId());
                } else {
                    serviceassociationService.update(new LambdaUpdateWrapper<DfdwTDesignServiceassociation>()
                            .set(DfdwTDesignServiceassociation::getProblemFeedbackId, null)
                            .set(DfdwTDesignServiceassociation::getUpdaterId, person.getId())
                            .set(DfdwTDesignServiceassociation::getUpdateTime, new Date())
                            .eq(DfdwTDesignServiceassociation::getId, oldList.get(0).getId())
                    );
                }
            }

        } else if (!newList.isEmpty()) {
            // 新不空老空则添加数据
            DfdwTDesignServiceassociation entity = newList.get(0);
            entity.setCreateId(person.getId());
            entity.setCreateTime(new Date());
            serviceassociationService.save(entity);
        }
        try {
            Integer state = 2;
            DfdwTDesignFieldservicerecord serviceRecordServiceById = serviceRecordService
                    .getById(serviceRecord.getServiceassociationList()
                            .get(0).getServiceRecordId());
            DfdwTDesignSupervisorapply applyById = applyservice
                    .getById(serviceRecord.getServiceassociationList()
                            .get(0).getSupervisorApplyId());
            if (serviceRecordServiceById != null && state.equals(serviceRecordServiceById.getApproveState())
                    && applyById != null && applyById.getApplyUserId() != null) {
                Person fsPerson = personMapper.selectById(applyById.getApplyUserId());
                Person gdPerson = personMapper.selectById(serviceRecordServiceById.getSpecialtyPersonId());
                String msg = gdPerson.getRealname() + "已关联服务申请并完结，记录编号：" + serviceRecordServiceById.getApplyNo() + "，详情请登录平台查看";
                smssendService.sendSms(fsPerson.getTelephone(), msg);
            }
        } catch (Exception e) {
            log.error("关联服务申请短信发送失败，" + e.getMessage());
        }
        return "success";
    }

    @Override
    public IPage<DfdwTDesignFieldservicerecord> getFwjlAssociationList(DfdwTDesignFieldservicerecord fieldservicerecord, PersonEntity person) {
        Integer userId = person.getId();
        Boolean gongDai = designProjectService.hasDesignProjectRole(userId, "协同办公-设计工代-设计工代人员");
        Boolean zongGuanLi = designProjectService.hasDesignProjectRole(userId, "协同办公-设计工代-总管理");
        fieldservicerecord.setQueryUserId(userId);
        if (gongDai) {
            fieldservicerecord.setSpecialtyPersonId(userId);
        }
        if (zongGuanLi) {
            fieldservicerecord.setQueryUserId(null);
            fieldservicerecord.setSpecialtyPersonId(null);
        }
        IPage<DfdwTDesignFieldservicerecord> page = new Page<>(fieldservicerecord.getPageNum(), fieldservicerecord.getPageSize());
        return baseMapper.getFwjlAssociationList(page, fieldservicerecord);
    }

    @Override
    public void downloadExcel(DfdwTDesignFieldservicerecord serviceRecord, HttpServletResponse response) {
        Integer userId = serviceRecord.getCurrentLoginUser().getId();
//        Boolean  sheZong= projectService.hasDesignProjectRole(userId, "协同办公-设计工代-项目设总");
        Boolean gongDai = projectService.hasDesignProjectRole(userId, "协同办公-设计工代-设计工代人员");
        Boolean zongGuanLi = projectService.hasDesignProjectRole(userId, "协同办公-设计工代-总管理");
        serviceRecord.setQueryUserId(userId);
        if (gongDai) {
            serviceRecord.setSpecialtyPersonId(userId);
        }
        if (zongGuanLi) {
            serviceRecord.setQueryUserId(null);
            serviceRecord.setSpecialtyPersonId(null);
        }
        List<DfdwTDesignFieldservicerecord> list = baseMapper.getDownLst(serviceRecord);
        try {
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
            cmlist.add(new ToolHelper.ExportColumnMode("applyNo", "单据编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("projectName", "项目名称", 50));
            cmlist.add(new ToolHelper.ExportColumnMode("designUnit", "设计单位", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("applyDateStr", "填报日期", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("specialtyPersonName", "设计工代人员", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("engineeringPhase", "工程阶段", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("nodeLabels", "节点", 50));
            cmlist.add(new ToolHelper.ExportColumnMode("supervisorApplyNo", "关联服务申请", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("problemFeedbackNo", "关联问题反馈", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("approveStateStr", "审批状态", 10));
            ToolHelper.ExportExcelList(list, "服务记录", "服务记录信息", cmlist, false, response);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<DfdwTDesignServiceassociation> getAssociationList(DfdwTDesignServiceassociation serviceassociation, PersonEntity person) {
        return serviceassociationService.list(new LambdaQueryWrapper<DfdwTDesignServiceassociation>().eq(DfdwTDesignServiceassociation::getServiceRecordId, serviceassociation.getServiceRecordId()));
    }

    // 辅助方法
    private void addTableCell(PdfPTable table, String text, Font font) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        cell.setPadding(5);
        table.addCell(cell);
    }

    private void addTableCell(PdfPTable table, String text, Font font, int colspan) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        cell.setColspan(colspan);
        cell.setPadding(5);
        table.addCell(cell);
    }

    private void addServiceCheckboxes(Document document, Font font) throws DocumentException {
        // 变电工程服务节点
        document.add(new Paragraph("变电工程服务节点：", font));
        // ... 添加复选框列表
        document.add(new Paragraph("□你好哦", font));
        document.add(new Paragraph("□你好哦", font));
        document.add(new Paragraph("□你好哦", font));
        // 线路工程服务节点
        document.add(new Paragraph("线路工程服务节点：", font));
        document.add(new Paragraph("□你好哦", font));
        document.add(new Paragraph("□你好哦", font));
        // ... 添加复选框列表
    }

    private Paragraph createEmptyLines(int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append("\n");
        }
        return new Paragraph(sb.toString());
    }

    // 添加辅助方法来创建表单字段
// 修改 addFormField 方法
    private void addFormField(PdfPTable table, String label, String value, Font font) {
        PdfPCell cell = new PdfPCell();
        cell.setBorder(Rectangle.NO_BORDER);

        // 创建一个包含标签和值的段落
        Paragraph p = new Paragraph();
        p.setAlignment(Element.ALIGN_CENTER); // 设置段落居中对齐

        // 添加标签和值
        Chunk labelChunk = new Chunk(label, font);
        Chunk valueChunk = new Chunk(value, font);
        p.add(labelChunk);
        p.add(valueChunk);

        cell.addElement(p);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER); // 设置单元格居中对齐
        table.addCell(cell);
    }

    private List<String> getPdfNodes(String type, DfdwTDesignFieldservicerecord record) {
        List<DfdwTDictData> dictDataList = dfdwTDictDataService.getDictDataByType(type);
        List<String> strList = new ArrayList<>();
        dictDataList.forEach(dict -> {
            String label = dict.getLabel();
            String value = dict.getValue();
            List<String> list = new ArrayList<>(Arrays.asList(record.getNode().split(",")));
            if (list.contains(value)) {
                strList.add("√" + label);
            } else if ("其它".equals(label)) {
                boolean flag = true;
                for (String s : list) {
                    if (s.startsWith(value)) {
                        String ss = s.replace(value, label);
                        strList.add("√" + ss);
                        flag = false;
                    }
                }
                if (flag) {
                    strList.add("□" + label);
                }
            } else {
                strList.add("□" + label);
            }
        });
        return strList;
    }

    @Override
    public List<Integer> getSignServiceRecordIds(Integer userId) {
        return  baseMapper.getSignServiceRecordIds(userId);
    }
}




