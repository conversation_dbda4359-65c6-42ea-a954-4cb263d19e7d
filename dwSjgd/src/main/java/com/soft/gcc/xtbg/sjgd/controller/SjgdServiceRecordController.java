package com.soft.gcc.xtbg.sjgd.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.common.lc_workFlow.entity.LcWorkflow;
import com.soft.gcc.common.lc_workFlow.service.LcWorkflowService;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.soft.gcc.xtbg.sjgd.service.*;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/Sjgd/serviceRecord")
@RestController
public class SjgdServiceRecordController extends BaseController {
    @Autowired
    private DfdwTDesignFieldservicerecordService serviceRecordService;
    @Resource
    TFileService fileService;
    @Resource
    LcWorkflowService lcWorkflowService;
    @Resource
    private DfdwTDesignProjectService projectService;
    @Resource
    DfdwTDesignServiceassociationService serviceassociationService;
    @Resource
    DfdwTDesignSupervisorapplyService supervisorapplyService;
    @Resource
    DfdwTDesignFieldproblemfeedbackService problemfeedbackService;


    @PostMapping("/getPageList")
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX01')")
    public Result<Object> getPageList(@RequestBody DfdwTDesignFieldservicerecord serviceRecord){
        Page<DfdwTDesignFieldservicerecord> page = new Page<>(serviceRecord.getPageNum(), serviceRecord.getPageSize());
        serviceRecord.setCurrentLoginUser(user());
        Boolean  zongGuanLi= projectService.hasDesignProjectRole(serviceRecord.getCurrentLoginUser().getId(),
                "协同办公-设计工代-总管理");
        serviceRecord.setIsShowAll(false);
        if(zongGuanLi){
            serviceRecord.setIsShowAll(true);
        }
        //1、设总可以查看本项目的所有内容；
        if(projectService.hasDesignProjectRole(serviceRecord.getCurrentLoginUser().getId(), "协同办公-设计工代-项目设总")) {
            List<Integer> designProjectList = projectService.getDesignDirectorProject(serviceRecord.getCurrentLoginUser().getId());
            serviceRecord.setDesignDirectorDesignProjectIds(designProjectList);
        }
        //总监、专监、施工单位：
        //服务记录： 能见申请单所关联的服务记录
        if(projectService.hasDesignProjectRole(serviceRecord.getCurrentLoginUser().getId(), "协同办公-设计工代-监理项目专监/施工单位")
                || projectService.hasDesignProjectRole(serviceRecord.getCurrentLoginUser().getId(), "协同办公-设计工代-监理项目总监")
        ){
            List<Integer> applyServiceRecordIds = supervisorapplyService.getApplyServiceRecordIds(serviceRecord.getCurrentLoginUser().getId());
            serviceRecord.setApplyServiceRecordIds(applyServiceRecordIds);
        }
        //反馈是签收人关联的服务记录可见
        List<Integer> signServiceRecordIds = serviceRecordService.getSignServiceRecordIds(serviceRecord.getCurrentLoginUser().getId());
        if(signServiceRecordIds!=null && signServiceRecordIds.size()>0){
            serviceRecord.setSignServiceRecordIds(signServiceRecordIds);
        }
        return Result.ok( serviceRecordService.getPageList(page,serviceRecord));
    }
    @GetMapping("/getDetail")
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX01')")
    public Result<Object> getDetail(@RequestParam("id") Integer id){
        DfdwTDesignFieldservicerecord detail = serviceRecordService.getDetail(id);
        return Result.ok(detail);
    }
    @PostMapping("addOrEdit")
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX03')")
    public Result<Object> addOrEdit(@RequestBody DfdwTDesignFieldservicerecord serviceRecord){
        Integer userId = user().getId();
        Boolean isAdd=false;
        if (serviceRecord.getId() == null ){
            serviceRecord.setCreateId(userId);
            isAdd=true;
        }
        serviceRecordService.addOrEdit(serviceRecord);
        if(isAdd){
//            serviceRecordService.addProjectFile(serviceRecord);
            fileService.saveOrUpdateFile(String.valueOf(serviceRecord.getId()),"fwjl","1","20034",serviceRecord.getRecordFiles());
        }else{
//            serviceRecordService.updateProjectFile(serviceRecord);
            fileService.saveOrUpdateFile(String.valueOf(serviceRecord.getId()),"fwjl","1","20034",serviceRecord.getRecordFiles());
        }
        return Result.ok(serviceRecord.getId());
    }
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX04')")//设计工代-服务-删除
    public Result<Object> delete(@RequestBody DfdwTDesignFieldservicerecord serviceRecord) {
        serviceassociationService.remove(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                .in(DfdwTDesignServiceassociation::getServiceRecordId, serviceRecord.getIds())
        );
        return Result.ok( serviceRecordService.removeByIds(serviceRecord.getIds()));
    }
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX05')")//设计工代-服务 导出
    public void exportPdf(@RequestBody DfdwTDesignFieldservicerecord serviceRecord, HttpServletResponse response) {
        try {
            DzccPersonEntity user = user();
            serviceRecordService.exportPdf( serviceRecord.getIds(), response,user);
        } catch (Exception e) {
//            log.error("导出PDF失败", e);
            throw new RuntimeException("导出PDF失败");
        }
    }
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX01')")
    @GetMapping("getLcListById/{id}")
    public Result<Object> GetLcListById(@PathVariable("id") Integer id) {
        // 流程定义ID
        List<LcWorkflow> list = getLcList(id);
        return Result.ok(list);
    }

    /**
     * 服务申请、服务记录关联
     * @param serviceRecord 实体类
     * @return
     */
    @PreAuthorize("@ss.hasAnyPermi('JDWSJ01SR01QX02,JDWSJ01SR01QX03,JDWSJ01SR01QX08')")
    @PostMapping("serviceAssociation")
    public Result<Object> serviceAssociation(@RequestBody DfdwTDesignFieldservicerecord serviceRecord){
        PersonEntity person = user();
        return Result.ok(serviceRecordService.serviceAssociation(serviceRecord, person));
    }

    private List<LcWorkflow> getLcList(Integer id) {
        Integer lcDefineID = 20034;
        List<LcWorkflow> list = lcWorkflowService.list(new LambdaQueryWrapper<LcWorkflow>()
                .eq(LcWorkflow::getYwid, id)
                .eq(LcWorkflow::getLcDefineid, lcDefineID)
                .orderByAsc(LcWorkflow::getYwid, LcWorkflow::getStartdate)
        );
        return list;
    }

    /**
     * 导出服务记录Excel
     * @param serviceRecord
     * @param response
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX07')")
    @PostMapping("downloadExcel")
    public Result<Object> downloadExcel(@RequestBody DfdwTDesignFieldservicerecord serviceRecord, HttpServletResponse response){
        serviceRecord.setCurrentLoginUser(user());
        serviceRecordService.downloadExcel(serviceRecord,response);
        return Result.ok();
    }

    /**
     * 得到服务申请未关联数据
     * @param supervisorapply 实体类
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX08')")
    @PostMapping("getFwsqAssociationList")
    public Result<Object> getFwjlAssociationList(@RequestBody DfdwTDesignSupervisorapply supervisorapply){
        PersonEntity person = user();
        DfdwTDesignFieldservicerecord serviceById = serviceRecordService.getById(supervisorapply.getFieldServiceRecordId());
        Integer DesignUserId = serviceById.getSpecialtyPersonId();
        supervisorapply.setFieldServiceRecordDesignUserId(DesignUserId);
        return Result.ok(supervisorapplyService.getFwsqAssociationList(supervisorapply, person));
    }

    /**
     * 得到问题反馈未关联数据
     * @param problemfeedback 实体类
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX08')")
    @PostMapping("getWtfkAssociationList")
    public Result<Object> getWtfkAssociationList(@RequestBody DfdwTDesignFieldproblemfeedback problemfeedback){
        PersonEntity person = user();
        return Result.ok(problemfeedbackService.getWtfkAssociationList(problemfeedback, person));
    }

    /**
     * 得到关联数据
     * @param serviceassociation 实体类
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01SR01QX08')")
    @PostMapping("getAssociationList")
    public Result<Object> getAssociationList(@RequestBody DfdwTDesignServiceassociation serviceassociation){
        PersonEntity person = user();
        return Result.ok(serviceRecordService.getAssociationList(serviceassociation, person));
    }

}
