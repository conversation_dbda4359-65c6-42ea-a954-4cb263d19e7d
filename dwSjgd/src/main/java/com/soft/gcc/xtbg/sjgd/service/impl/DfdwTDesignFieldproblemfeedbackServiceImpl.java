package com.soft.gcc.xtbg.sjgd.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.common.t_smssend.service.TSmssendService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignPersonMapper;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignSupervisorapplyMapper;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignFieldproblemfeedbackService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignFieldproblemfeedbackMapper;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignPersonService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectService;
import com.soft.gcc.xtbg.sjgd.util.HttpClientUtils;
import com.soft.gcc.xtbg.sjgd.util.PdfUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.apache.fop.util.CharUtilities.padLeft;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_FieldProblemFeedback(设计工代-现场问题反馈)】的数据库操作Service实现
* @createDate 2024-12-30 16:40:28
*/
@Service
public class DfdwTDesignFieldproblemfeedbackServiceImpl extends ServiceImpl<DfdwTDesignFieldproblemfeedbackMapper, DfdwTDesignFieldproblemfeedback>
    implements DfdwTDesignFieldproblemfeedbackService{
    @Resource
    TFileService fileService;
    @Resource
    DfdwTDesignProjectService projectService;

    @Resource
    TSmssendService smssendService;
    @Resource
    DfdwTDesignPersonMapper dfdwTDesignPersonMapper;

    @Resource
    DfdwTDesignSupervisorapplyMapper dfdwTDesignSupervisorapplyMapper;
    @Resource
    private DfdwTDesignFieldproblemfeedbackMapper dfdwTDesignFieldproblemfeedbackMapper;

    @Override
    public IPage<DfdwTDesignFieldproblemfeedback> listPage(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback) {
        Boolean  zongGuanLi= projectService.hasDesignProjectRole(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId(), "协同办公-设计工代-总管理");
        dfdwTDesignFieldproblemfeedback.setIsShowAll(false);
        if( zongGuanLi ){
            dfdwTDesignFieldproblemfeedback.setIsShowAll(true);
        }
        //1、设总可以查看本项目的所有内容；
        if(projectService.hasDesignProjectRole(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId(), "协同办公-设计工代-项目设总")) {
            List<Integer> designProjectList = projectService.getDesignDirectorProject(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId());
            dfdwTDesignFieldproblemfeedback.setDesignDirectorDesignProjectIds(designProjectList);
        }
//      总监、专监、施工单位：
//      问题反馈： 能见服务记录所关联的问题反馈
        DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply=new DfdwTDesignSupervisorapply();
        dfdwTDesignSupervisorapply.setIsShowAll(  dfdwTDesignFieldproblemfeedback.getIsShowAll());
        dfdwTDesignSupervisorapply.setCurrentLoginUser(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser());
        List<DfdwTDesignSupervisorapply> applyList = dfdwTDesignFieldproblemfeedbackMapper.getApplyList(dfdwTDesignSupervisorapply);
        if(CollectionUtil.isNotEmpty(applyList)){
            List<Integer> collect = applyList.stream().map(s -> s.getProblemFeedbackId()).collect(Collectors.toList());
            dfdwTDesignFieldproblemfeedback.setIds(collect);
        }
        return this.baseMapper.listPage(new Page<>(dfdwTDesignFieldproblemfeedback.getPageNum(), dfdwTDesignFieldproblemfeedback.getPageSize()),dfdwTDesignFieldproblemfeedback);
    }

    @Override
    public DfdwTDesignFieldproblemfeedback detail(Integer id) {
        DfdwTDesignFieldproblemfeedback res = this.baseMapper.selectInfoById(id);
        List<TFile> files = fileService.list(new LambdaQueryWrapper<TFile>().eq(TFile::getProjectid,id).eq(TFile::getFunctionid,20035).eq(TFile::getType,"wtfk"));
        for (TFile file : files) {
            if (StringUtils.checkValNull(res.getSiteProblemFile())){
                res.setSiteProblemFile(file.getFilepath());
            }else{
                res.setSiteProblemFile(res.getSiteProblemFile()+","+file.getFilepath());
            }

        }
        return res;
    }

    @Override
    public Integer addOrEdit(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletRequest request) {
        if (dfdwTDesignFieldproblemfeedback.getId() == null ){
            dfdwTDesignFieldproblemfeedback.setCreateId(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId());
            dfdwTDesignFieldproblemfeedback.setCreateTime(new Date());
            if (dfdwTDesignFieldproblemfeedback.getApproveState() != null){
                dfdwTDesignFieldproblemfeedback.setApproveState(dfdwTDesignFieldproblemfeedback.getApproveState());
            }else {
                dfdwTDesignFieldproblemfeedback.setApproveState(0);
            }
            //生成单据编号
            dfdwTDesignFieldproblemfeedback.setApplyNo(getBh());
        }
        this.saveOrUpdate(dfdwTDesignFieldproblemfeedback);

        if(dfdwTDesignFieldproblemfeedback.getIsSigner() == 0){
            // 使用UpdateWrapper强制更新这些字段
            UpdateWrapper<DfdwTDesignFieldproblemfeedback> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("signerUserName", null)
                    .set("signerUserId", null)
                    .eq("id", dfdwTDesignFieldproblemfeedback.getId());
            this.update(updateWrapper);

            try{
                Map<String, Object> map = new HashMap<>();

                HashMap<String, String> headers = new HashMap<>();
                headers.put("Authorization", request.getHeader("Authorization"));
                String cookie = request.getHeader("Cookie");
                String url = "/dwFlow/flow/dfdwApprove/endLc";
                map.put("lcDefineId","20034");
                map.put("ywId",dfdwTDesignFieldproblemfeedback.getId());
                Result r =  HttpClientUtils.doPostFlow(url, map, headers, cookie);
                System.out.println(r);
            }catch (Exception ex){
                ex.printStackTrace();

            }

        }
        fileService.saveOrUpdateFile(String.valueOf(dfdwTDesignFieldproblemfeedback.getId()),"wtfk","1","20035",dfdwTDesignFieldproblemfeedback.getSiteProblemFile());
        return dfdwTDesignFieldproblemfeedback.getId();
    }

    @Override
    public Integer signFor(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback) {
        if (dfdwTDesignFieldproblemfeedback.getSignerDate() == null || StringUtils.checkValNull(dfdwTDesignFieldproblemfeedback.getSignerOpinion())){
           throw new RuntimeException("签收意见、签收时间不能为空！");
        }
        this.saveOrUpdate(dfdwTDesignFieldproblemfeedback);
        DfdwTDesignFieldproblemfeedback detail = this.getById(dfdwTDesignFieldproblemfeedback.getId());
        String phoneNumber = dfdwTDesignSupervisorapplyMapper.selectPersonUserPhoneById(detail.getSignerUserId());

       // List<DfdwTDesignPerson> list = dfdwTDesignPersonMapper.selectList(new LambdaQueryWrapper<DfdwTDesignPerson>().eq(DfdwTDesignPerson::getSrcUserId,detail.getSignerUserId()));
        if (!phoneNumber.isEmpty()){
            smssendService.sendSms(phoneNumber,"您有一条问题反馈单编号【"+detail.getApplyNo()+"】已签收，详情请登录app查看");
        }
        return dfdwTDesignFieldproblemfeedback.getId();
    }

    /**
     * 获取编号
     * @return
     */
    public String getBh() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        //dateStr = dateStr.substring(2, dateStr.length());  //保留格式yyMM

        String gcBH = "WTFK-" + dateStr;
        DfdwTDesignFieldproblemfeedback maxEn = baseMapper.getMaxApplyNo(gcBH);
        if (maxEn == null) {
            gcBH = gcBH + "0001";
        } else {
            //从12位截取后面
            String maxReturnNo = maxEn.getApplyNo();
            String noStr = maxReturnNo.substring(13);
            int no = Integer.parseInt(noStr) + 1;
            if (no % 10 == 0) {
                no = no + 1;
            }
            noStr = String.valueOf(no);
            noStr = padLeft(noStr, 4, '0');
            gcBH = gcBH + noStr;
        }


        return gcBH;
    }

    public String padLeft(String src, int len, char ch) {
        int diff = len - src.length();
        if (diff <= 0) {
            return src;
        }

        char[] charr = new char[len];
        System.arraycopy(src.toCharArray(), 0, charr, diff, src.length());
        for (int i = 0; i < diff; i++) {
            charr[i] = ch;
        }
        return new String(charr);

    }


    @Override
    public void downloadExcel(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletResponse response) {
        Boolean  gongDai= projectService.hasDesignProjectRole(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId(), "协同办公-设计工代-设计工代人员");
        Boolean  zongGuanLi= projectService.hasDesignProjectRole(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId(), "协同办公-设计工代-总管理");
        if(gongDai){
            dfdwTDesignFieldproblemfeedback.setSpecialtyPersonId(dfdwTDesignFieldproblemfeedback.getCurrentLoginUser().getId());
        }
        if( zongGuanLi ){
            dfdwTDesignFieldproblemfeedback.setSpecialtyPersonId(null);
        }
        IPage<DfdwTDesignFieldproblemfeedback> page = this.baseMapper.listPage(new Page<>(1,-1 ),dfdwTDesignFieldproblemfeedback);

        try {
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
            cmlist.add(new ToolHelper.ExportColumnMode("applyNo", "单据编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("projectName", "项目名称", 50));
            cmlist.add(new ToolHelper.ExportColumnMode("proprietorProjectdepartment", "业主项目部", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("specialtyPersonName", "设计工代人员", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("feedbackDateStr", "反馈日期", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("signerUserName", "签收人", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("signerOpinion", "签收意见", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("signerDateStr", "签收日期", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("serviceRecordNo", "关联服务记录", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("approveStateStr", "审批状态", 10));
            ToolHelper.ExportExcelList(page.getRecords(), "问题反馈", "问题反馈信息",cmlist,false,response );
        }catch (Exception e){
            System.out.println(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public IPage<DfdwTDesignFieldproblemfeedback> getWtfkAssociationList(DfdwTDesignFieldproblemfeedback problemfeedback, PersonEntity person) {
        Boolean gongDai= projectService.hasDesignProjectRole(person.getId(), "协同办公-设计工代-设计工代人员");
        Boolean zongGuanLi= projectService.hasDesignProjectRole(person.getId(), "协同办公-设计工代-总管理");
        if(gongDai){
            problemfeedback.setSpecialtyPersonId(person.getId());
        }
        if(zongGuanLi){
            problemfeedback.setSpecialtyPersonId(null);
        }
        return baseMapper.getWtfkAssociationList(new Page<>(problemfeedback.getPageNum(), problemfeedback.getPageSize()), problemfeedback);
    }

    @Override
    public void downloadPdf(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback,
                            HttpServletResponse response,
                            DzccPersonEntity user) {
        try {
            List<Integer> ids = dfdwTDesignFieldproblemfeedback.getIds();

            // 判断是否有数据要导出
            if (ids == null || ids.isEmpty()) {
                throw new RuntimeException("没有可导出的数据");
            }

            // 单个文件直接导出PDF
            if (ids.size() == 1) {
                dfdwTDesignFieldproblemfeedback.setId(dfdwTDesignFieldproblemfeedback.getIds().get(0));
                generateSinglePdf(dfdwTDesignFieldproblemfeedback, response,user);
                return;
            }

            // 多个文件打包成ZIP
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode("服务联系单.zip", "UTF-8"));

            try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
                for (Integer id : ids) {
                    // 获取每个id的详细信息
                    DfdwTDesignFieldproblemfeedback detail = detail(id);
                    // 生成PDF并添加到ZIP
                    byte[] pdfBytes = generatePdfBytes(detail,user);

                    // 创建ZIP条目
                    String fileName = detail.getApplyNo() + "_服务联系单.pdf";
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zos.putNextEntry(zipEntry);
                    zos.write(pdfBytes);
                    zos.closeEntry();
                }
                zos.flush();
            }

        } catch (Exception e) {
            log.error("文件生成失败", e);
            throw new RuntimeException("文件生成失败: " + e.getMessage());
        }
    }

    // 生成单个PDF文件
    private void generateSinglePdf(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletResponse response,DzccPersonEntity user) throws Exception {
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment;filename=" +
                URLEncoder.encode(dfdwTDesignFieldproblemfeedback.getApplyNo() + "_服务联系单.pdf", "UTF-8"));

        byte[] pdfBytes = generatePdfBytes(dfdwTDesignFieldproblemfeedback,user);
        response.getOutputStream().write(pdfBytes);
        response.getOutputStream().flush();
    }

    // 生成PDF字节数组的核心方法
    private byte[] generatePdfBytes(DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback,DzccPersonEntity user) throws Exception {
        PdfReader reader = null;
        PdfStamper stamper = null;
        ByteArrayOutputStream bos = null;

        try {
            // 读取模板
            String templatePath = "/static/sjgd/wtfk.pdf";
            InputStream inputStream = getClass().getResourceAsStream(templatePath);
            if (inputStream == null) {
                throw new IOException("模板文件不存在: " + templatePath);
            }

            reader = new PdfReader(inputStream);
            bos = new ByteArrayOutputStream();
            stamper = new PdfStamper(reader, bos);

            // 使用中文字体
            BaseFont bf = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            ArrayList<BaseFont> fontList = new ArrayList<>();
            fontList.add(bf);

            DfdwTDesignFieldproblemfeedback detail = detail(dfdwTDesignFieldproblemfeedback.getId());

            // 填充数据
            AcroFields fields = stamper.getAcroFields();
            fields.setSubstitutionFonts(fontList);

            fields.setField("projectName", detail.getProjectName());
            fields.setField("applyNo", detail.getApplyNo());
            fields.setField("proprietorProjectdepartment", detail.getProprietorProjectdepartment());
            fields.setField("fieldCondition", detail.getFieldCondition());
            fields.setField("problemsAndMeasures", detail.getProblemsAndMeasures());
            fields.setField("specialtyPersonName", detail.getSpecialtyPersonName());
            fields.setField("signerOpinion", StringUtils.checkValNull(detail.getSignerOpinion())?"":detail.getSignerOpinion());

            LocalDate feedbackDate = detail.getFeedbackDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            fields.setField("feedbackDateY", String.valueOf(feedbackDate.getYear()));
            fields.setField("feedbackDateM", String.valueOf(feedbackDate.getMonthValue()));
            fields.setField("feedbackDateD", String.valueOf(feedbackDate.getDayOfMonth()));

            fields.setField("signerUserName", detail.getSignerUserName());
            if(detail.getSignerDate() != null){
                LocalDate signerDate = detail.getSignerDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                fields.setField("signerDateY", String.valueOf(signerDate.getYear()));
                fields.setField("signerDateM", String.valueOf(signerDate.getMonthValue()));
                fields.setField("signerDateD", String.valueOf(signerDate.getDayOfMonth()));
            }


            //若当前文本域无法满足
            if (checkAddrLength(fields.getField("fieldCondition"), "fieldCondition", fields)) {
                /*获取当前文本框的尺寸，返回的数据依次为左上右下（0,1,2,3）*/
                PdfArray rect1 = fields.getFieldItem("fieldCondition").getValue(0).getAsArray(PdfName.RECT);
                rect1.set(1, new PdfNumber(rect1.getAsNumber(1).intValue() - 16));
            }
            if (checkAddrLength(fields.getField("problemsAndMeasures"), "problemsAndMeasures", fields)) {
                /*获取当前文本框的尺寸，返回的数据依次为左上右下（0,1,2,3）*/
                PdfArray rect1 = fields.getFieldItem("problemsAndMeasures").getValue(0).getAsArray(PdfName.RECT);
                rect1.set(1, new PdfNumber(rect1.getAsNumber(1).intValue() - 16));
            }
            if(StringUtils.checkValNotNull(detail.getSignerOpinion())){
                if (checkAddrLength(fields.getField("signerOpinion"), "signerOpinion", fields)) {
                    PdfArray rect1 = fields.getFieldItem("signerOpinion").getValue(0).getAsArray(PdfName.RECT);

                    // 获取文本框当前高度
                    float currentHeight = rect1.getAsNumber(1).floatValue() - rect1.getAsNumber(3).floatValue();
                    // 调试代码
                    System.out.println("文本框位置信息：top={"+rect1.getAsNumber(1).floatValue()+"}, " +
                                    "bottom={"+rect1.getAsNumber(3).floatValue()+"}, " +
                                    "left={"+rect1.getAsNumber(0).floatValue()+"}, " +
                                    "right={"+rect1.getAsNumber(2).floatValue()+"}"
                    );

                    // 计算需要的额外空间
                    float extraSpace = 32; // 可以根据实际需求调整这个值

                    // 同时调整上下边距，保持文本居中
                    rect1.set(1, new PdfNumber(rect1.getAsNumber(1).floatValue() + extraSpace/2));  // 上边距
                    rect1.set(3, new PdfNumber(rect1.getAsNumber(3).floatValue() - extraSpace/2));  // 下边距

                    // 设置文本对齐方式（如果需要的话）
                    AcroFields.Item item = fields.getFieldItem("signerOpinion");
                    PdfDictionary widget = item.getWidget(0);
                    PdfNumber flags = new PdfNumber(PdfFormField.FF_MULTILINE);  // 允许多行
                    widget.put(PdfName.FF, flags);
                }
            }


            // 设置表单平面化
            stamper.setFormFlattening(true);


            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<String> watermark = Arrays.asList(
                    user.getRealName(),
                    user.getGroupName(),
                    sdf.format(new Date())
            );
            // 完成 PDF 生成
            stamper.close();

            return PdfUtil.PDFAddWatermark(bos.toByteArray(),watermark);

        } finally {
            // 关闭资源
            try {
                if (bos != null) {
                    bos.close();
                }
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }


    public static boolean checkAddrLength(String addr, String fileName, AcroFields form) throws IOException, DocumentException {
        float fontSize = 8f;
        boolean flag = false;
        BaseFont baseFont = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        float textWidth = baseFont.getWidthPoint(addr, fontSize);
        Rectangle position = form.getFieldPositions(fileName).get(0).position;
        float textBoxWidth = position.getWidth();
        if (textWidth > textBoxWidth) {
            flag = true;
        }
        return flag;
    }









}




