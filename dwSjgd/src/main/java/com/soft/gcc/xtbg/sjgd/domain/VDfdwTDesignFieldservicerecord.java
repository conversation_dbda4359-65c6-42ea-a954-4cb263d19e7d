package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName V_DFDW_T_Design_FieldServiceRecord
 */
@TableName(value ="V_DFDW_T_Design_FieldServiceRecord")
@Data
public class VDfdwTDesignFieldservicerecord extends DfdwTDesignFieldservicerecord implements Serializable {

    /**
     *
     */
    @TableField(value = "lcDefineID")
    private Integer lcDefineID;

    /**
     *
     */
    @TableField(value = "lcName")
    private String lcName;

    /**
     *
     */
    @TableField(value = "ywID")
    private Integer ywID;

    /**
     *
     */
    @TableField(value = "sendPerson")
    private String sendPerson;

    /**
     *
     */
    @TableField(value = "sendPersonZgh")
    private String sendPersonZgh;

    /**
     *
     */
    @TableField(value = "AllPersonZgh")
    private String allPersonZgh;

    /**
     *
     */
    @TableField(value = "isMany")
    private Integer isMany;

    /**
     *
     */
    @TableField(value = "lcJdmc")
    private String lcJdmc;

    /**
     *
     */
    @TableField(value = "lcJdid")
    private Integer lcJdid;

    /**
     *
     */
    @TableField(value = "lcIsback")
    private Integer lcIsback;

    /**
     *
     */
    @TableField(value = "lcTojdid")
    private String lcTojdid;

    /**
     *
     */
    @TableField(value = "number")
    private Integer number;

    /**
     *
     */
    @TableField(value = "BXType")
    private String BXType;

}
