package com.soft.gcc.xtbg.sjgd.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.service.PersonService;
import com.soft.gcc.common.person.service.VpersonService;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignProjectMapper;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectspecialtyService;
import com.soft.gcc.xtbg.sjgd.util.PdfUtil;
import org.apache.xpath.operations.Bool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_Design_Project(设计工代-项目管理)】的数据库操作Service实现
 * @createDate 2024-12-30 16:40:28
 */
@Service
public class DfdwTDesignProjectServiceImpl extends ServiceImpl<DfdwTDesignProjectMapper, DfdwTDesignProject>
        implements DfdwTDesignProjectService {
    @Autowired
    private DfdwTDesignProjectService designProjectService;
    @Autowired
    private DfdwTDesignProjectspecialtyService projectSpecialtyService;
    @Autowired
    private PersonService personService;
    @Autowired
    private TFileService fileService;
    private Set<String> specialties;
    @Resource
    private DfdwTDictDataService dfdwTDictDataService;

    @Override
    public IPage<DfdwTDesignProject> getPageList(Page<DfdwTDesignProject> page, DfdwTDesignProject project) {
        IPage<DfdwTDesignProject> pageList = baseMapper.getPageList(page, project);
        return pageList;
    }
    @Override
    public IPage<DfdwTDesignProject> getPageListUserId(Page<DfdwTDesignProject> page,
                                                       Integer userId,
                                                       DfdwTDesignProject project) {
        IPage<DfdwTDesignProject> pageList = baseMapper.getPageListUserId(page, userId,project);
        return pageList;
    }

    @Override
    public Boolean hasDesignProjectRole(Integer currentUserId, String roleName) {
        return baseMapper.hasDesignProjectRole(currentUserId,roleName);
    }

    /**
     * 项目列表
     *
     * @param page
     * @param project
     * @return
     */
    @Override
    public IPage<VProjectT> getVProjectPageList(Page<VProjectT> page, VProjectT project) {
        IPage<VProjectT> pageList = baseMapper.getVProjectPageList(page, project);
        return pageList;
    }

    @Override
    public List<LeaderPerson> getLeaderList(VProjectT project) {
        List<LeaderPerson> leaderList = baseMapper.getLeaderList(project);
        return leaderList;
    }

    @Override
    public void addProjectSpecialty(DfdwTDesignProject project) {
        List<SpecialtyPerson> fromSpecialtyPersonList = project.getFromSpecialtyPersonList();
        List<DfdwTDesignProjectspecialty> addDPList = new ArrayList<>();
        fromSpecialtyPersonList.stream().forEach(s -> {
            List<String> personId = Stream.of(s.getPersonIds().split(",")).collect(Collectors.toList());
            personId.stream().forEach(id -> {
                DfdwTDesignProjectspecialty entity = new DfdwTDesignProjectspecialty();
                entity.setDesignProjectId(project.getId());
                entity.setDesignUserId(Integer.valueOf(id));
                entity.setSpecialty(s.getSpecialtyName());
                addDPList.add(entity);
            });
        });
//        projectSpecialtyService.saveBatch(addDPList);
        for (DfdwTDesignProjectspecialty d : addDPList) {
            projectSpecialtyService.save(d);
        }
    }
    public void deleteProjectSpecialty(DfdwTDesignProject project) {
        projectSpecialtyService.remove(new QueryWrapper<DfdwTDesignProjectspecialty>()
                .lambda()
                .eq(DfdwTDesignProjectspecialty::getDesignProjectId,project.getId())
        );
    }

    @Override
    public void getProjectSpecialtyList(List<DfdwTDesignProject> list) {
        if(CollectionUtil.isEmpty(list)){
            return ;
        }
        List<Integer> ids = list.stream().map(s -> s.getId()).collect(Collectors.toList());
        List<DfdwTDesignProjectspecialty> specialtyList = projectSpecialtyService.list(
                new QueryWrapper<DfdwTDesignProjectspecialty>()
                        .lambda().in(DfdwTDesignProjectspecialty::getDesignProjectId, ids));
        //数据重组
        Map<Integer, Map<String, List<Integer>>> result = specialtyList.stream()
                .collect(Collectors.groupingBy(
                        DfdwTDesignProjectspecialty::getDesignProjectId,
                        Collectors.groupingBy(
                                DfdwTDesignProjectspecialty::getSpecialty,
                                Collectors.mapping(
                                        DfdwTDesignProjectspecialty::getDesignUserId,
                                        Collectors.toList()
                                )
                        )
                ));
        Boolean cal=result!=null && result.size()>0;
        if(!cal){
            return;
        }
        //人员姓名
        Map<Integer, String> PersonMap = personService
                .list(new QueryWrapper<Person>().lambda().select(Person::getId, Person::getRealname,Person::getTelephone))
                .stream()
                .collect(Collectors.toMap(Person::getId, p->p.getRealname()+","+p.getTelephone()));
        list.stream().forEach(project -> {
            // 获取某个项目ID的所有专业
            if( result.get(project.getId())!=null){
                Set<String> specialties = result.get(project.getId()).keySet();
                List<SpecialtyPerson> specialtyPersonList = new ArrayList<>();
                // 获取某个项目某个专业下的所有设计人员ID
                specialties.stream().forEach(specialty->{
                    List<Integer> designUserIds = result.get(project.getId()).get(specialty);
                    SpecialtyPerson specialtyPerson = new SpecialtyPerson();
                    specialtyPerson.setSpecialtyName(specialty);
                    specialtyPerson.setPersonIds(designUserIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(",")));
                    List<String> names=new ArrayList<>();
                    List<String> phones=new ArrayList<>();
                    designUserIds.stream().forEach(id->{
                        if(PersonMap.get(id)!=null){
                            String[] split = PersonMap.get(id).split(",");
                            if(split!=null && split.length>0){
                                String name = PersonMap.get(id).split(",")[0];
                                names.add(name);
                                if(split.length>1){
                                    String phone =PersonMap.get(id).split(",")[1];
                                    phones.add(phone);
                                }
                            }
                        }
                    });
                    specialtyPerson.setPersonNames(String.join(",",names));
                    specialtyPerson.setPhones(String.join(",",phones));
                    specialtyPersonList.add(specialtyPerson);
                });
                project.setFromSpecialtyPersonList(specialtyPersonList);
            }
        });
    }


    @Override
    public void updateProjectSpecialty(DfdwTDesignProject project) {
        deleteProjectSpecialty(project);
        addProjectSpecialty(project);
    }

    @Override
    public void addProjectFile(DfdwTDesignProject project) {
        //获取文件列表
        if(StringUtils.checkValNotNull(project.getProjectFiles())){
            List<String> fileList = Arrays.stream(project.getProjectFiles().split(","))
                    .collect(Collectors.toList());
            fileService.update(new LambdaUpdateWrapper<TFile>()
                    .set(TFile::getProjectid,project.getId())
                    .in(TFile::getFilepath, fileList)
            );
        }
    }

    @Override
    public void updateProjectFile(DfdwTDesignProject project) {
        //获取文件列表
        if(StringUtils.checkValNotNull(project.getProjectFiles())){
            //当前需要update 的文件
            List<String> newFileList = Arrays.stream(project.getProjectFiles().split(","))
                    .collect(Collectors.toList());

            //获取数据库中的老数据
            List<TFile> nowFileList = fileService.list(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getProjectid, project.getId())
                    .eq(TFile::getFunctionid, 20036)
                    .eq(TFile::getHjid,1)
                    .eq(TFile::getType, "xmgl")
            );
            List<String> oldFileList = nowFileList.stream().map(s -> s.getFilepath()).collect(Collectors.toList());

            //获取需要删除的数据
            List<String> deleted = oldFileList.stream()
                    .filter(item -> !newFileList.contains(item))
                    .collect(Collectors.toList());

            //获取需要update 的数据
            List<String> updateData = newFileList.stream()
                    .filter(item -> !oldFileList.contains(item))
                    .collect(Collectors.toList());
            if(deleted.size() >0){
                fileService.remove(new LambdaQueryWrapper<TFile>()
                        .eq(TFile::getProjectid, project.getId())
                        .eq(TFile::getFunctionid, 20036)
                        .eq(TFile::getType, "xmgl")
                        .in(TFile::getFilepath, deleted));
            }
            if (updateData.size() > 0){
                fileService.update(new LambdaUpdateWrapper<TFile>()
                        .set(TFile::getProjectid,project.getId())
                        .eq(TFile::getType, "xmgl")
                        .eq(TFile::getFunctionid, 20036)
                        .in(TFile::getFilepath, updateData)
                );
            }

        }else {
            fileService.remove(new LambdaQueryWrapper<TFile>()
                    .eq(TFile::getProjectid, project.getId())
                    .eq(TFile::getFunctionid, 20036)
                    .eq(TFile::getType, "xmgl")
            );

        }
    }

    @Override
    public DfdwTDesignProject detail(Integer id) {
        DfdwTDesignProject project = designProjectService.getById(id);
        List<TFile> files = fileService.list(
                new LambdaQueryWrapper<TFile>()
                        .eq(TFile::getProjectid,id)
                        .eq(TFile::getFunctionid,20036)
                        .eq(TFile::getType,"xmgl"));
        List<String> list=new ArrayList<>();
        files.forEach(s-> {
            list.add(s.getFilepath());
        });
        project.setProjectFiles(String.join(",", list));
        return project;
    }
    @Override
    public void exportProjectPdf(List<Integer> ids, HttpServletResponse response,  DzccPersonEntity user) throws Exception {
        // 设置响应头为zip
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("设计工代派驻函.zip", "UTF-8"));

        // 创建临时文件目录
        String localPath = System.getProperty("java.io.tmpdir");
        File tempDir = new File(localPath + "/design/pdf/");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        // 水印PDF目录
        File watermarkDir = new File(localPath + "/yykj/dzcc/downloads/water/jtsjqx/");
        if (!watermarkDir.exists()) {
            watermarkDir.mkdirs();
        }
        List<String> pdfPaths = new ArrayList<>();
        List<String> watermarkPaths = new ArrayList<>();
        long time = System.currentTimeMillis();
        Map<String, Integer> fileNameCounter = new HashMap<>();

        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            // 循环处理每个项目
            for (Integer id : ids) {
                DfdwTDesignProject project = designProjectService.getById(id);
                if (project == null) {
                    continue;
                }

                // 获取专业人员信息
                List<DfdwTDesignProject> list = new ArrayList<>();
                list.add(project);
                designProjectService.getProjectSpecialtyList(list);
                List<SpecialtyPerson> specialtyPersonList = list.get(0).getFromSpecialtyPersonList();

                // 生成临时PDF文件路径
                String pdfPath = localPath + "/design/pdf/" + project.getProjectName() + "_" + System.currentTimeMillis() + ".pdf";
                pdfPaths.add(pdfPath);
                String watermarkPath = localPath + "/yykj/dzcc/downloads/water/jtsjqx/" + "xmgl" + "_" + time + ".pdf";
                watermarkPaths.add(watermarkPath);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 生成单个PDF
                try (FileOutputStream fos = new FileOutputStream(pdfPath)) {
                    Document document = new Document(PageSize.A4);
                    PdfWriter.getInstance(document, fos);

                    // 设置中文字体
                    BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                    Font titleFont = new Font(baseFont, 18, Font.BOLD);
                    Font subTitleFont = new Font(baseFont, 14, Font.BOLD);
                    Font normalFont = new Font(baseFont, 12, Font.NORMAL);
                    Font boldFont = new Font(baseFont, 12, Font.BOLD);

                    document.open();

                    // 添加标题
                    Paragraph title = new Paragraph("设计工代派驻函", titleFont);
                    title.setAlignment(Element.ALIGN_CENTER);
                    document.add(title);
                    document.add(new Paragraph("\n"));

                    // 添加子标题
                    Chunk chunk1 = new Chunk("关于明确", subTitleFont);
//                    Chunk chunk2 = new Chunk("                                                     ", subTitleFont); // 使用下划线
                    Chunk chunk2 = new Chunk(project.getProjectName(), subTitleFont); // 使用下划线
//                    chunk2.setUnderline(0.1f, -2f); // 设置下划线
                    Chunk chunk3 = new Chunk("输变电工程设计工代人员的通知", subTitleFont);

                    Paragraph subTitle = new Paragraph();
                    subTitle.add(chunk1);
                    subTitle.add(chunk2);
                    subTitle.add(chunk3);
                    subTitle.setAlignment(Element.ALIGN_CENTER);
                    document.add(subTitle);
                    document.add(new Paragraph("\n"));

                    // 添加正文
                    document.add(new Paragraph("建设管理单位：", normalFont));
                    document.add(new Paragraph("\n"));

                    String content = "我院设计的" + project.getProjectName() +
                            "工程将进入施工阶段，为更好地做好设计服务工作，现将设计工代人员明确如下：";
                    document.add(new Paragraph(content, normalFont));
                    document.add(new Paragraph("\n"));

                    // 输出专业人员
                    if (specialtyPersonList != null && !specialtyPersonList.isEmpty()) {
                        Map<String, String> dictMap = dfdwTDictDataService.getDictDataByType("sjgd-specialty")
                                .stream()
                                .collect(Collectors.toMap(
                                        DfdwTDictData::getValue,
                                        DfdwTDictData::getLabel
                                ));

                        for (SpecialtyPerson sp : specialtyPersonList) {
                            String phones = sp.getPhones();
                            String specialtyName = sp.getSpecialtyName();
                            String personNames = sp.getPersonNames();

                            List<String> personNameList = Arrays.asList(personNames.split(","));
                            List<String> phonesList = Arrays.asList(phones.split(","));

                            for (int index = 0; index < personNameList.size(); index++) {
                                document.add(new Paragraph(
                                        dictMap.get(specialtyName) + "工地代表："
                                                + personNameList.get(index)
                                                + "，联系电话："
                                                + phonesList.get(index),
                                        normalFont
                                ));
                            }
                        }
                    }

                    document.add(new Paragraph("\n"));

                    // 添加底部说明
                    String footer = "工地代表应与建设、监理、施工等单位密切配合，确保设计服务工作满足现场需求，" +
                            "共同确保工程建设的的质量和工期，为工程如期投运、达标投产及工程创优尽责尽力。";
                    document.add(new Paragraph(footer, normalFont));
                    document.add(new Paragraph("\n\n\n"));

                    // 添加落款
                    Paragraph signature = new Paragraph();
                    signature.add(new Chunk("设计单位：", normalFont));
                    signature.add(new Chunk("（盖章）", normalFont));
                    signature.setAlignment(Element.ALIGN_RIGHT);
                    document.add(signature);

                    Paragraph date = new Paragraph();
                    date.add(new Chunk("日    期：    年    月    日", normalFont));
                    date.setAlignment(Element.ALIGN_RIGHT);
                    document.add(date);

                    document.close();
                }
                // 添加水印
                List<String> watermark = Arrays.asList(
                        user.getRealName(),
                        user.getGroupName(),
                        sdf.format(new Date())
                );
                PdfUtil.pdfAddWaterMark(pdfPath, watermarkPath, watermark);


                // 处理ZIP文件名
                String baseFileName = project.getProjectName() + ".pdf";
                Integer count = fileNameCounter.getOrDefault(baseFileName, 0);
                String zipFileName;
                if (count > 0) {
                    zipFileName = project.getProjectName() + "(" + count + ").pdf";
                } else {
                    zipFileName = baseFileName;
                }
                fileNameCounter.put(baseFileName, count + 1);

                // 添加到ZIP
                File watermarkFile = new File(watermarkPath);
                ZipEntry zipEntry = new ZipEntry(zipFileName);
                zipOutputStream.putNextEntry(zipEntry);

                // 写入ZIP
                byte[] buffer = new byte[1024];
                try (FileInputStream fis = new FileInputStream(watermarkFile);
                     BufferedInputStream bis = new BufferedInputStream(fis)) {
                    int bytesRead;
                    while ((bytesRead = bis.read(buffer)) != -1) {
                        zipOutputStream.write(buffer, 0, bytesRead);
                    }
                }
                zipOutputStream.closeEntry();
            }
        } catch (Exception e) {
            log.error("生成PDF文件失败", e);
            throw new RuntimeException("生成PDF文件失败：" + e.getMessage());
        } finally {
            // 清理临时文件
            for (String path : pdfPaths) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
            for (String path : watermarkPaths) {
                File file = new File(path);
                if (file.exists()) {
                    file.delete();
                }
            }
        }
    }

    /**
     * 校验项目使用情况
     * @param ids
     * @return
     */
    @Override
    public List<VDfdwTDesignflow> checkUse(List<Integer> ids) {
        return baseMapper.checkUse(ids);
    }

    @Override
    public void downloadExcel(DfdwTDesignProject project, HttpServletResponse response) {
        Integer userId = project.getCurrentLoginUser().getId();
        Page<DfdwTDesignProject> page = new Page<>(project.getPageNum(), project.getPageSize());
        //Boolean  sheZong= projectService.hasDesignProjectRole(userId, "协同办公-设计工代-项目设总");
        Boolean gongDai = this.hasDesignProjectRole(userId, "协同办公-设计工代-设计工代人员");
        Boolean zongGuanLi = this.hasDesignProjectRole(userId, "协同办公-设计工代-总管理");
        List<DfdwTDesignProject> list = null;
        if (zongGuanLi) {
            list =  baseMapper.getDownLoadList(null, project);
        } else if (gongDai) {
            list =  baseMapper.getDownLoadList( userId, project);
        }

        try {
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
            cmlist.add(new ToolHelper.ExportColumnMode("projectName", "项目名称", 50));
            cmlist.add(new ToolHelper.ExportColumnMode("designDirectorUserName", "设总人员", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("projectDirectorUserName", "项目总监", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("projectSpecialOverseeUserName", "项目专监", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("memberNames", "项目成员", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("specialtyUsers", "设计专业人员", 50));
            ToolHelper.ExportExcelList(list, "项目信息", "项目信息",cmlist,false,response );
        }catch (Exception e){
            System.out.println(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }


    }
    @Override
    public List<Integer> getDesignDirectorProject(Integer userId) {
        List<DfdwTDesignProject> list = designProjectService.list(new LambdaQueryWrapper<DfdwTDesignProject>()
                .eq(DfdwTDesignProject::getDesignDirectorUserId, userId)
                .eq(DfdwTDesignProject::getDeleted, 0));
        if(CollectionUtil.isNotEmpty(list)){
            List<Integer> collect = list.stream().map(s -> s.getId()).collect(Collectors.toList());
            return collect;
        }else{
            return  new ArrayList<>();
        }
    }
}




