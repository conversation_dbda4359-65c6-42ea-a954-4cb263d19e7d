package com.soft.gcc.xtbg.sjgd.service;

import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_ServiceAssociation(现场服务关联表)】的数据库操作Service
* @createDate 2025-02-17 16:51:28
*/
public interface DfdwTDesignServiceassociationService extends IService<DfdwTDesignServiceassociation> {

    void deleteFwsqByIds(List<Integer> idList);

    void deleteFwjlByIds(List<Integer> idList);
}
