package com.soft.gcc.xtbg.sjgd.dto;

import com.soft.gcc.xtbg.base.entity.PageEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 审批查询条件
 * @date 2024-12-31 13:38:54
 */
@Data
public class FlowDto  extends PageEntity {

    /**
     * 类型 1服务申请 ，2现场服务记录， 3现场问题反馈
     */
    private Integer type;

    /**
     * 申请人
     */
    private String userName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 编号
     */
    private String applyNo;

    /**
     * 审批状态 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    private Integer approveState;

    /**
     * 是否查看历史 0否 1是
     */
    private Integer isHistory;

}
