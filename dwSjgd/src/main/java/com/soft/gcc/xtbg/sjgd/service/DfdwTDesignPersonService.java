package com.soft.gcc.xtbg.sjgd.service;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_Person(设计工代-人员管理)】的数据库操作Service
* @createDate 2024-12-30 16:40:28
*/
public interface DfdwTDesignPersonService extends IService<DfdwTDesignPerson> {

    Result<Object> getPage(DfdwTDesign<PERSON>erson designPerson, PersonEntity person);

    Result<Object> getList(DfdwTDesign<PERSON>erson designPerson, PersonEntity person);

    Result<Object> add(DfdwTDesign<PERSON>erson designPerson, PersonEntity person);

    Result<Object> edit(DfdwTDesign<PERSON>erson designPerson, PersonEntity person);

    Result<Object> deleteByIds(DfdwTDesignPerson designPerson, PersonEntity person);

    Result<Object> upload(MultipartFile file, DfdwTDesignPerson designPerson, PersonEntity person) throws IOException;

    Result<Object> linkPerson(List<DfdwTDesignPerson> designPersonList, PersonEntity person);

    List<DfdwTDesignPerson> selectList(DfdwTDesignPerson designPerson);

    Result<Object> getPermissonList(DfdwTDesignPerson designPerson, PersonEntity person);

    Result<Object> getPersonPageList(DfdwTDesignPerson designPerson, PersonEntity person);

    Result<Object> getPersonRoleById(DfdwTDesignPerson designPerson, PersonEntity person);

    Result<Object> getRole();

    Result<Object> editRoleById(DfdwTDesignPerson designPerson);

    List<DfdwTDesignPerson> getPersonListByProjectId(Integer projectId);
}
