package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import lombok.Data;

/**
 * 设计工代-现场问题反馈
 * @TableName DFDW_T_Design_FieldProblemFeedback
 */
@TableName(value ="DFDW_T_Design_FieldProblemFeedback")
@Data
public class DfdwTDesignFieldproblemfeedback implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 设计工代项目Id
     */
    @TableField(value = "designProjectId")
    private Integer designProjectId;
    @TableField(exist = false)
    private String projectName;

    /**
     * 单据编号（自动生成）
     */
    @TableField(value = "applyNo")
    private String applyNo;

    /**
     * 业务项目部
     */
    @TableField(value = "proprietorProjectdepartment")
    private String proprietorProjectdepartment;

    /**
     * 现场情况描述
     */
    @TableField(value = "fieldCondition")
    private String fieldCondition;

    /**
     * 发现问题及解决措施
     */
    @TableField(value = "problemsAndMeasures")
    private String problemsAndMeasures;

    /**
     * 设计工代专业人员（自动带入操作人员）
     */
    @TableField(value = "specialtyPersonId")
    private Integer specialtyPersonId;

    @TableField(value = "specialtyPersonName")
    private String specialtyPersonName;

    /**
     * 反馈日期（默认填报日期）
     */
    @TableField(value = "feedbackDate")
    private Date feedbackDate;

    /**
     * 签收人（项目总监自动带入）
     */
    @TableField(value = "signerUserId")
    private Integer signerUserId;

    /**
     * 是否签收 1是0否
     */
    @TableField(value = "isSigner")
    private Integer isSigner;

    /**
     * 签收人姓名
     */
    @TableField(value = "signerUserName")
    private String signerUserName;

    /**
     * 签收意见（未填写默认”已收到”）
     */
    @TableField(value = "signerOpinion")
    private String signerOpinion;

    /**
     * 签收日期（默认签收操作日期）
     */
    @TableField(value = "signerDate")
    private Date signerDate;

    /**
     * 关联监理通知单
     */
    @TableField(value = "supervisorNotice")
    private String supervisorNotice;

    /**
     * 审批状态(0未提交，1审批中，2已审批，3已驳回，4流程终止)
     */
    @TableField(value = "approveState")
    private Integer approveState;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;

    /**
     * 是否已经被删除 0否 1是
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "1")
    private String deleted;
    /**
     * 附件
     */
    @TableField(exist = false)
    private String siteProblemFile;

    /**
     * 导出excel用 签收日期（默认签收操作日期）
     */
    @TableField(exist = false)
    private String signerDateStr;
    /**
     * 导出excel用 反馈日期
     */
    @TableField(exist = false)
    private String feedbackDateStr;
    /**
     * 导出excel用 审批状态
     */
    @TableField(exist = false)
    private String approveStateStr;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    @TableField(exist = false)
    private Integer pageSize;

    @TableField(exist = false)
    private Integer pageNum;
    /**
     * 当前登录用户
     */
    @TableField(exist = false)
    private DzccPersonEntity currentLoginUser;

    /**
     * 关联服务记录
     */
    @TableField(exist = false)
    private String serviceRecordNo;
    /**
     * 设总的项目Ids
     */
    @TableField(exist = false)
    private List<Integer> DesignDirectorDesignProjectIds;
    @TableField(exist = false)
    private Boolean isShowAll;
}
