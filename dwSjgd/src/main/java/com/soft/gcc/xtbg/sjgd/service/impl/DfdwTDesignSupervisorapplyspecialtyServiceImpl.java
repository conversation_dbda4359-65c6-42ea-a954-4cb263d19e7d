package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignSupervisorapplyspecialtyService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignSupervisorapplyspecialtyMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_SupervisorApplySpecialty(设计工代-服务申请-设计工代人员)】的数据库操作Service实现
* @createDate 2024-12-30 16:40:28
*/
@Service
public class DfdwTDesignSupervisorapplyspecialtyServiceImpl extends ServiceImpl<DfdwTDesignSupervisorapplyspecialtyMapper, DfdwTDesignSupervisorapplyspecialty>
    implements DfdwTDesignSupervisorapplyspecialtyService{

    @Override
    public List<DfdwTDesignSupervisorapplyspecialty> getSpecialtyPersonListByYwId(Integer ywId) {
        return baseMapper.getSpecialtyPersonListByYwId(ywId);
    }

    @Override
    public List<DfdwTDesignSupervisorapplyspecialty> getPersonListBySupervisorApplyId(DfdwTDesignSupervisorapplyspecialty dfdwTDesignSupervisorapplyspecialty) {
        return baseMapper.getPersonListBySupervisorApplyId(dfdwTDesignSupervisorapplyspecialty);
    }
}




