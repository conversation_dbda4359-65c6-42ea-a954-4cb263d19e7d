package com.soft.gcc.xtbg.sjgd.service;

import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_SupervisorApplySpecialty(设计工代-服务申请-设计工代人员)】的数据库操作Service
* @createDate 2024-12-30 16:40:28
*/
public interface DfdwTDesignSupervisorapplyspecialtyService extends IService<DfdwTDesignSupervisorapplyspecialty> {

    /**
     * 获取服务申请的设计工代人员相关记录
     * @param ywId
     * @return
     */
    List<DfdwTDesignSupervisorapplyspecialty> getSpecialtyPersonListByYwId(Integer ywId);


    /**
     * 根据服务申请id 获取设计工代人员列表
     * @param dfdwTDesignSupervisorapplyspecialty
     * @return
     */
    List<DfdwTDesignSupervisorapplyspecialty> getPersonListBySupervisorApplyId(DfdwTDesignSupervisorapplyspecialty dfdwTDesignSupervisorapplyspecialty);
}
