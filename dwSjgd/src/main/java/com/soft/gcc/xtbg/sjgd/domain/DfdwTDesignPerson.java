package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设计工代-人员管理
 * <AUTHOR>
 * @TableName DFDW_T_Design_Person
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="DFDW_T_Design_Person")
@Data
public class DfdwTDesignPerson extends DfdwBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名称
     */
    @TableField(value = "userName")
    private String userName;

    /**
     * 专业
     */
    @TableField(value = "specialty")
    private String specialty;
    @TableField(exist = false)
    private String specialtyName;

    /**
     * 手机号
     */
    @TableField(value = "telephone")
    private String telephone;

    /**
     * 数字化平台用户Id
     */
    @TableField(value = "srcUserId", updateStrategy = FieldStrategy.IGNORED)
    private Integer srcUserId;

    /**
     * 部门Id
     */
    @TableField(value = "deptId")
    private Integer deptId;

    /**
     * 部门名称
     */
    @TableField(value = "deptName")
    private String deptName;

    /**
     * 状态(1启用 0 禁用)
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;

    /**
     * 修改人
     */
    @TableField(value = "updateId")
    private Integer updateId;

    @TableField(exist = false)
    private List<Integer> ids;

    @TableField(exist = false)
    private Boolean hasSrcUserId;

    @TableField(exist = false)
    private String roleName;

    @TableField(exist = false)
    private List<Integer> roleIds;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}