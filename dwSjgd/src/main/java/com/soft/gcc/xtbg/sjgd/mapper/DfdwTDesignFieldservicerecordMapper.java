package com.soft.gcc.xtbg.sjgd.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_FieldServiceRecord(设计工代-现场服务记录)】的数据库操作Mapper
* @createDate 2024-12-30 16:40:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldservicerecord
*/
public interface DfdwTDesignFieldservicerecordMapper extends BaseMapper<DfdwTDesignFieldservicerecord> {
    IPage<DfdwTDesignFieldservicerecord> getPageList(@Param("page")Page<DfdwTDesignFieldservicerecord> page,
                                                     @Param("query")DfdwTDesignFieldservicerecord serviceRecord);

    List<DfdwTDesignFieldservicerecord> getDownLst(@Param("query")DfdwTDesignFieldservicerecord serviceRecord);
    DfdwTDesignFieldservicerecord getMaxApplyNo(@Param("applyNo") String applyNo);
    List<DfdwTDesignFieldservicerecord> getAllList(@Param("query")DfdwTDesignFieldservicerecord serviceRecord);

    IPage<DfdwTDesignFieldservicerecord> getFwjlAssociationList(
            @Param("page") IPage<DfdwTDesignFieldservicerecord> page,
            @Param("query") DfdwTDesignFieldservicerecord fieldservicerecord
    );
    List<Integer> getSignServiceRecordIds(@Param("userId")Integer userId);
}




