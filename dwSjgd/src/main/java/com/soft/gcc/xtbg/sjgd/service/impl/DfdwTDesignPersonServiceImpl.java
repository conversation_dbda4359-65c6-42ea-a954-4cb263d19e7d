package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictDataMapper;
import com.soft.gcc.common.person.entity.Vperson;
import com.soft.gcc.common.role.entity.Role;
import com.soft.gcc.common.role.entity.Roleperson;
import com.soft.gcc.common.role.mapper.RoleMapper;
import com.soft.gcc.common.role.mapper.RolepersonMapper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProject;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignProjectMapper;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignPersonService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignPersonMapper;
import com.soft.gcc.xtbg.sjgd.util.ReadPatientExcelUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_Person(设计工代-人员管理)】的数据库操作Service实现
* @createDate 2024-12-30 16:40:28
*/
@Service
public class DfdwTDesignPersonServiceImpl extends ServiceImpl<DfdwTDesignPersonMapper, DfdwTDesignPerson>
    implements DfdwTDesignPersonService{
    @Resource
    private DfdwTDictDataMapper dfdwTDictDataMapper;
    @Resource
    private DfdwTDesignProjectMapper dfdwTDesignProjectMapper;
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private RolepersonMapper rolepersonMapper;

    @Override
    public Result<Object> getPage(DfdwTDesignPerson designPerson, PersonEntity person) {
        IPage<DfdwTDesignPerson> page = this.page(new Page<>(designPerson.getPageNum(), designPerson.getPageSize()),
                new LambdaQueryWrapper<DfdwTDesignPerson>()
                        .like(StringUtils.isNotBlank(designPerson.getUserName()), DfdwTDesignPerson::getUserName, designPerson.getUserName())
                        .eq(StringUtils.isNotBlank(designPerson.getSpecialty()), DfdwTDesignPerson::getSpecialty, designPerson.getSpecialty())
                        .like(StringUtils.isNotBlank(designPerson.getTelephone()), DfdwTDesignPerson::getTelephone, designPerson.getTelephone())
                        .isNotNull(designPerson.getHasSrcUserId() != null && designPerson.getHasSrcUserId(), DfdwTDesignPerson::getSrcUserId)
                        .isNull(designPerson.getHasSrcUserId() != null && !designPerson.getHasSrcUserId(), DfdwTDesignPerson::getSrcUserId)
                        .eq(designPerson.getDeptId() != null, DfdwTDesignPerson::getDeptId, designPerson.getDeptId())
                        .like(StringUtils.isNotBlank(designPerson.getDeptName()), DfdwTDesignPerson::getDeptName, designPerson.getDeptName())
                        .eq(designPerson.getState() != null, DfdwTDesignPerson::getState, designPerson.getState())
                        .orderByAsc(DfdwTDesignPerson::getDeptName)
                        .orderByAsc(DfdwTDesignPerson::getUserName)
        );
        return Result.ok(page);
    }

    @Override
    public Result<Object> getList(DfdwTDesignPerson designPerson, PersonEntity person) {
        List<DfdwTDesignPerson> list = this.list(new LambdaQueryWrapper<DfdwTDesignPerson>()
                .like(designPerson.getUserName() != null, DfdwTDesignPerson::getUserName, designPerson.getUserName())
                .eq(designPerson.getSpecialty() != null, DfdwTDesignPerson::getSpecialty, designPerson.getSpecialty())
                .like(designPerson.getTelephone() != null, DfdwTDesignPerson::getTelephone, designPerson.getTelephone())
                .isNotNull(designPerson.getHasSrcUserId() != null && designPerson.getHasSrcUserId(), DfdwTDesignPerson::getSrcUserId)
                .isNull(designPerson.getHasSrcUserId() != null && !designPerson.getHasSrcUserId(), DfdwTDesignPerson::getSrcUserId)
                .eq(designPerson.getDeptId() != null, DfdwTDesignPerson::getDeptId, designPerson.getDeptId())
                .like(designPerson.getDeptName() != null, DfdwTDesignPerson::getDeptName, designPerson.getDeptName())
                .eq(designPerson.getState() != null, DfdwTDesignPerson::getState, designPerson.getState())
                .orderByAsc(DfdwTDesignPerson::getDeptName)
                .orderByAsc(DfdwTDesignPerson::getUserName)
        );
        return Result.ok(list);
    }

    @Override
    public Result<Object> add(DfdwTDesignPerson designPerson, PersonEntity person) {
        List<DfdwTDesignPerson> list = this.list(new LambdaQueryWrapper<DfdwTDesignPerson>()
                .eq(DfdwTDesignPerson::getUserName, designPerson.getUserName())
                .eq(DfdwTDesignPerson::getTelephone, designPerson.getTelephone())
        );
        if (!list.isEmpty()) {
            return Result.error("人员已存在");
        }
        List<Vperson> personList = baseMapper.selectPersonList(designPerson);
        if (!personList.isEmpty()) {
            int count = baseMapper.selectCount(new LambdaQueryWrapper<DfdwTDesignPerson>()
                    .eq(DfdwTDesignPerson::getSrcUserId, personList.get(0).getId())
            );
            if (count == 0) {
                designPerson.setSrcUserId(personList.get(0).getId());
            }
            List<Role> roles = roleMapper.selectList(new LambdaQueryWrapper<Role>()
                    .eq(Role::getRoleName, "协同办公-设计工代-设计工代人员")
            );
            if (!roles.isEmpty()) {
                List<Roleperson> rolePersons = rolepersonMapper.selectList(new LambdaQueryWrapper<Roleperson>()
                        .eq(Roleperson::getPersonId, personList.get(0).getId())
                        .eq(Roleperson::getRoleId, roles.get(0).getId())
                );
                if (rolePersons.isEmpty()) {
                    Roleperson rolePerson = new Roleperson();
                    rolePerson.setRoleId(roles.get(0).getId());
                    rolePerson.setPersonId(personList.get(0).getId());
                    rolepersonMapper.insert(rolePerson);
                }
            }
        }
        designPerson.setCreateId(person.getId());
        designPerson.setCreator(person.getRealName());
        designPerson.setCreateTime(new Date());
        designPerson.setUpdateId(person.getId());
        designPerson.setUpdater(person.getRealName());
        designPerson.setUpdateTime(new Date());
        this.save(designPerson);
        return Result.ok();
    }

    @Override
    public Result<Object> edit(DfdwTDesignPerson designPerson, PersonEntity person) {
        int count = this.count(new LambdaQueryWrapper<DfdwTDesignPerson>()
                .eq(DfdwTDesignPerson::getUserName, designPerson.getUserName())
                .eq(DfdwTDesignPerson::getTelephone, designPerson.getTelephone())
                .ne(DfdwTDesignPerson::getId, designPerson.getId())
        );
        if (count > 0) {
            return Result.error("姓名手机号已存在");
        }
        DfdwTDesignPerson oldDesignPerson = this.getById(designPerson.getId());
        if (oldDesignPerson == null) {
            return Result.error("人员不存在");
        }
        if (designPerson.getSrcUserId() == null) {
            List<Vperson> personList = baseMapper.selectPersonList(designPerson);
            if (!personList.isEmpty()) {
                int personCount = baseMapper.selectCount(new LambdaQueryWrapper<DfdwTDesignPerson>()
                        .eq(DfdwTDesignPerson::getSrcUserId, personList.get(0).getId())
                );
                if (personCount == 0) {
                    designPerson.setSrcUserId(personList.get(0).getId());
                }
                List<Role> roles = roleMapper.selectList(new LambdaQueryWrapper<Role>()
                        .eq(Role::getRoleName, "协同办公-设计工代-设计工代人员")
                );
                if (!roles.isEmpty()) {
                    List<Roleperson> rolePersons = rolepersonMapper.selectList(new LambdaQueryWrapper<Roleperson>()
                            .eq(Roleperson::getPersonId, personList.get(0).getId())
                            .eq(Roleperson::getRoleId, roles.get(0).getId())
                    );
                    if (rolePersons.isEmpty()) {
                        Roleperson rolePerson = new Roleperson();
                        rolePerson.setRoleId(roles.get(0).getId());
                        rolePerson.setPersonId(personList.get(0).getId());
                        rolepersonMapper.insert(rolePerson);
                    }
                }
            }
        }
        designPerson.setUpdateId(person.getId());
        designPerson.setUpdater(person.getRealName());
        designPerson.setUpdateTime(new Date());
        this.updateById(designPerson);
        return Result.ok();
    }

    @Override
    public Result<Object> deleteByIds(DfdwTDesignPerson designPerson, PersonEntity person) {
        List<DfdwTDesignPerson> list = this.list(new LambdaQueryWrapper<DfdwTDesignPerson>()
                .in(DfdwTDesignPerson::getId, designPerson.getIds())
        );
        for (DfdwTDesignPerson designPerson1 : list) {
            int count = dfdwTDesignProjectMapper.selectCount(new LambdaQueryWrapper<DfdwTDesignProject>()
                    .eq(DfdwTDesignProject::getProjectDirectorUserId, designPerson1.getSrcUserId())
                    .or()
                    .eq(DfdwTDesignProject::getProjectSpecialOverseeUserId, designPerson1.getSrcUserId())
                    .or()
                    .eq(DfdwTDesignProject::getDesignDirectorUserId, designPerson1.getSrcUserId())
            );
            if (count > 0) {
                return Result.error(designPerson1.getUserName() + "已关联项目，无法删除！");
            }
        }
        this.removeByIds(designPerson.getIds());
        return Result.ok();
    }

    @Override
    public Result<Object> upload(MultipartFile file, DfdwTDesignPerson designPerson, PersonEntity person) throws IOException {
        //获取文件名
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return Result.error("文件名不能为空");
        }
        // 验证文件名是否合格
        if (!ReadPatientExcelUtil.validateExcel(fileName)) {
            return null;
        }
        // 根据文件名判断文件是2003版本还是2007版本
        boolean isExcel2003 = true;
        if (ReadPatientExcelUtil.isExcel2007(fileName)) {
            isExcel2003 = false;
        }
        Workbook wb = null;
        InputStream is = file.getInputStream();
        // 当excel是2003时,创建excel2003
        if (isExcel2003) {
            wb = new HSSFWorkbook(is);
        } else {// 当excel是2007时,创建excel2007
            wb = new XSSFWorkbook(is);
        }
        List<DfdwTDesignPerson> list = new ArrayList<>();
        // 读取Excel里面客户的信息
        // 默认会跳过第一行标题
        // 得到第一个shell
        Sheet sheet = wb.getSheetAt(0);
        // 得到Excel的行数
        int totalRows = sheet.getPhysicalNumberOfRows();
        int totalCells = 0;
        // 得到Excel的列数(前提是有行数)
        if (totalRows > 1 && sheet.getRow(0) != null) {
            totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
        }
        // 循环Excel行数
        for (int r = 1; r < totalRows; r++) {
            Row row = sheet.getRow(r);
            if (row == null) {
                continue;
            }
            DfdwTDesignPerson user = new DfdwTDesignPerson();
            // 循环Excel的列
            for (int c = 0; c < totalCells; c++) {
                Cell cell = row.getCell(c);
                if (null != cell) {
                    if (cell.getCellType() == CellType.NUMERIC) {
                        cell.setCellType(CellType.STRING);
                    }
                    if (c == 0) {
                        user.setUserName(cell.getStringCellValue());
                    } else if (c == 1) {
                        user.setDeptName(cell.getStringCellValue());
                    } else if (c == 2) {
                        user.setSpecialtyName(cell.getStringCellValue());
                    } else if (c == 3) {
                        user.setTelephone(cell.getStringCellValue());
                    }
                }
            }
            list.add(user);
        }
        List<DfdwTDictData> dictDataList = dfdwTDictDataMapper.selectList(new LambdaQueryWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "sjgd-specialty")
        );
        List<String> nameList = new ArrayList<>();
        for (DfdwTDesignPerson dfdwTDesignPerson : list) {
            List<DfdwTDesignPerson> filterList = this.list(new LambdaQueryWrapper<DfdwTDesignPerson>()
                    .eq(DfdwTDesignPerson::getUserName, dfdwTDesignPerson.getUserName())
                    .eq(DfdwTDesignPerson::getTelephone, dfdwTDesignPerson.getTelephone())
            );
            if (filterList.isEmpty()) {
                List<Vperson> personList = baseMapper.selectPersonList(dfdwTDesignPerson);
                if (!personList.isEmpty()) {
                    int count = baseMapper.selectCount(new LambdaQueryWrapper<DfdwTDesignPerson>()
                            .eq(DfdwTDesignPerson::getSrcUserId, personList.get(0).getId())
                    );
                    if (count == 0) {
                        dfdwTDesignPerson.setSrcUserId(personList.get(0).getId());
                    }
                }
            } else {
                nameList.add(dfdwTDesignPerson.getUserName());
            }
            if (dfdwTDesignPerson.getSpecialtyName() != null) {
                for (DfdwTDictData dictData : dictDataList) {
                    if (dfdwTDesignPerson.getSpecialtyName().equals(dictData.getLabel())) {
                        dfdwTDesignPerson.setSpecialty(dictData.getValue());
                        break;
                    }
                }
            }
            dfdwTDesignPerson.setState(1);
            dfdwTDesignPerson.setCreateId(person.getId());
            dfdwTDesignPerson.setCreator(person.getRealName());
            dfdwTDesignPerson.setCreateTime(new Date());
            dfdwTDesignPerson.setUpdateId(person.getId());
            dfdwTDesignPerson.setUpdater(person.getRealName());
            dfdwTDesignPerson.setUpdateTime(new Date());
            this.save(dfdwTDesignPerson);
        }
        if (!nameList.isEmpty()) {
            return Result.ok("导入成功，部门人员已存在：" + String.join(",", nameList) + ",如果不要请手动删除！");
        }
        return Result.ok();
    }

    @Override
    public Result<Object> linkPerson(List<DfdwTDesignPerson> designPersonList, PersonEntity person) {
        if (designPersonList.isEmpty()) {
            return Result.error("请选择人员");
        }
        List<Integer> ids = designPersonList.stream().map(DfdwTDesignPerson::getId).collect(Collectors.toList());
        List<DfdwTDesignPerson> otherList = this.list(new LambdaQueryWrapper<DfdwTDesignPerson>()
                .select(DfdwTDesignPerson::getId, DfdwTDesignPerson::getSrcUserId)
                .isNotNull(DfdwTDesignPerson::getSrcUserId)
                .notIn(DfdwTDesignPerson::getId, ids)
        );
        List<Integer> srcUserIds = otherList.stream().map(DfdwTDesignPerson::getSrcUserId).collect(Collectors.toList());

        List<DfdwTDesignPerson> list = this.list(new LambdaQueryWrapper<DfdwTDesignPerson>().in(DfdwTDesignPerson::getId, ids));
        List<DfdwTDesignProject> designProjectList = dfdwTDesignProjectMapper.selectList(new LambdaQueryWrapper<DfdwTDesignProject>()
                .in(DfdwTDesignProject::getProjectDirectorUserId, list.stream().map(DfdwTDesignPerson::getSrcUserId).collect(Collectors.toList()))
                .or()
                .in(DfdwTDesignProject::getProjectSpecialOverseeUserId, list.stream().map(DfdwTDesignPerson::getSrcUserId).collect(Collectors.toList()))
                .or()
                .in(DfdwTDesignProject::getDesignDirectorUserId, list.stream().map(DfdwTDesignPerson::getSrcUserId).collect(Collectors.toList()))
        );
        for (int i = 0; i < designPersonList.size(); i++) {
            int finalI = i;
            DfdwTDesignPerson before = list.stream().filter(d -> d.getId().equals(designPersonList.get(finalI).getId())).findFirst().orElse(null);
            if (before == null) {
                return Result.error("第" + (i + 1) + "条人员不存在！");
            }
            List<DfdwTDesignProject> projectList = designProjectList.stream().filter(d -> d.getProjectDirectorUserId().equals(before.getSrcUserId())
                    || d.getProjectSpecialOverseeUserId().equals(before.getSrcUserId())
                    || d.getDesignDirectorUserId().equals(before.getSrcUserId())
            ).collect(Collectors.toList());
            if (!projectList.isEmpty()) {
                return Result.error("第" + (i + 1) + "条人员已关联项目无法修改！");
            }
            if (srcUserIds.contains(designPersonList.get(i).getSrcUserId())) {
                return Result.error( "第" + (i + 1) + "条用户已被其他人员关联！");
            }
            for (int j = i + 1; j < designPersonList.size(); j++) {
                if (designPersonList.get(i).getSrcUserId().equals(designPersonList.get(j).getSrcUserId())) {
                    return Result.error("第" + (i + 1) + "条和第" + (j + 1) + "条关联人员重复");
                }
            }
        }
        List<Role> roles = roleMapper.selectList(new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleName, "协同办公-设计工代-设计工代人员")
        );
        for (DfdwTDesignPerson dfdwTDesignPerson : designPersonList) {
            if (!roles.isEmpty()) {
                List<Roleperson> rolePersons = rolepersonMapper.selectList(new LambdaQueryWrapper<Roleperson>()
                        .eq(Roleperson::getPersonId, dfdwTDesignPerson.getSrcUserId())
                        .eq(Roleperson::getRoleId, roles.get(0).getId())
                );
                if (rolePersons.isEmpty()) {
                    Roleperson rolePerson = new Roleperson();
                    rolePerson.setRoleId(roles.get(0).getId());
                    rolePerson.setPersonId(dfdwTDesignPerson.getSrcUserId());
                    rolepersonMapper.insert(rolePerson);
                }
            }
            this.updateById(dfdwTDesignPerson);
        }
        return Result.ok();
    }

    @Override
    public List<DfdwTDesignPerson> selectList(DfdwTDesignPerson designPerson) {
        return this.baseMapper.selectAllList(designPerson);
    }

    @Override
    public Result<Object> getPermissonList(DfdwTDesignPerson designPerson, PersonEntity person) {
        List<Vperson> personList = baseMapper.selectPersonList(designPerson);
        return Result.ok(personList);
    }

    @Override
    public Result<Object> getPersonPageList(DfdwTDesignPerson designPerson, PersonEntity person) {
        IPage<DfdwTDesignPerson> page = baseMapper.getPersonPageList(new Page<>(designPerson.getPageNum(), designPerson.getPageSize()),
                designPerson,
                person
        );
        return Result.ok(page);
    }

    @Override
    public Result<Object> getPersonRoleById(DfdwTDesignPerson designPerson, PersonEntity person) {
        return Result.ok(baseMapper.selectPersonRoleById(designPerson));
    }

    @Override
    public Result<Object> getRole() {
        List<Role> roleList = baseMapper.selectRoleBySjgd();
        return Result.ok(roleList);
    }

    @Override
    public Result<Object> editRoleById(DfdwTDesignPerson designPerson) {
        List<Role> roleList = baseMapper.selectRoleBySjgd();
        List<Integer> roleIds = roleList.stream().map(Role::getId).collect(Collectors.toList());
        rolepersonMapper.delete(new LambdaQueryWrapper<Roleperson>().eq(Roleperson::getPersonId, designPerson.getSrcUserId()).in(Roleperson::getRoleId, roleIds));
        for (Integer roleId : designPerson.getRoleIds()) {
            if (roleIds.contains(roleId)) {
                Roleperson roleperson = new Roleperson();
                roleperson.setPersonId(designPerson.getSrcUserId());
                roleperson.setRoleId(roleId);
                rolepersonMapper.insert(roleperson);
            }
        }
        return Result.ok("角色编辑成功");
    }

    @Override
    public List<DfdwTDesignPerson> getPersonListByProjectId(Integer projectId) {
        DfdwTDesignProject project = dfdwTDesignProjectMapper.selectById(projectId);
        if (project == null){
            throw new RuntimeException("项目信息不存在");
        }
        List<Integer> srcUserIds = new ArrayList<>();
        //总监id
        srcUserIds.add(project.getProjectDirectorUserId());
        //监理项目专监id
        srcUserIds.add(project.getProjectSpecialOverseeUserId());
        //项目成员
        if (StringUtils.isNotBlank(project.getMemberIds())){
            List<Integer> memberIds = Arrays.stream(project.getMemberIds().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            srcUserIds.addAll(memberIds);
        }
        //设总人员
        srcUserIds.add(project.getDesignDirectorUserId());
        //施工单位人员
        if (StringUtils.isNotBlank(project.getConstructionUnitUserIds())){
            List<Integer> constructionUnitUserIds = Arrays.stream(project.getConstructionUnitUserIds().split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            srcUserIds.addAll(constructionUnitUserIds);
        }

//        List<DfdwTDesignPerson> designPersonList = this.baseMapper.selectList(new LambdaQueryWrapper<DfdwTDesignPerson>()
//                .in(DfdwTDesignPerson::getSrcUserId,srcUserIds)
//        );



        return this.baseMapper.selectPersonListByIds(srcUserIds);
    }
}




