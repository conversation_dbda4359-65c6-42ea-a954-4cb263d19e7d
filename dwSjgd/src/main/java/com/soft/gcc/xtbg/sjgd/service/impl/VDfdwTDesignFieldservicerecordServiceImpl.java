package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignFieldservicerecord;
import com.soft.gcc.xtbg.sjgd.service.VDfdwTDesignFieldservicerecordService;
import com.soft.gcc.xtbg.sjgd.mapper.VDfdwTDesignFieldservicerecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_T_Design_FieldServiceRecord】的数据库操作Service实现
* @createDate 2024-12-31 13:29:50
*/
@Service
public class VDfdwTDesignFieldservicerecordServiceImpl extends ServiceImpl<VDfdwTDesignFieldservicerecordMapper, VDfdwTDesignFieldservicerecord>
    implements VDfdwTDesignFieldservicerecordService{

}




