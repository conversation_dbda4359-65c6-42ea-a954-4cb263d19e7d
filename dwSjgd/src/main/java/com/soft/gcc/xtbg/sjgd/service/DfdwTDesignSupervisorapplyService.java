package com.soft.gcc.xtbg.sjgd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_SupervisorApply(设计工代-服务申请)】的数据库操作Service
* @createDate 2024-12-30 16:40:28
*/
public interface DfdwTDesignSupervisorapplyService extends IService<DfdwTDesignSupervisorapply> {
    IPage<DfdwTDesignSupervisorapply> listPage(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply);

    /**
     * 获取详情 并且带上文件地址
     * @param id
     * @return
     */
    DfdwTDesignSupervisorapply detail(Integer id);

    Integer addOrEdit(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply);

    void downloadPdf(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, HttpServletResponse response, DzccPersonEntity user) throws IOException;

    void downloadExcel(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, HttpServletResponse response);

    /**
     * 服务申请、服务记录关联
     * @return
     */
    String serviceAssociation(DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, PersonEntity person);

    IPage<DfdwTDesignSupervisorapply> getFwsqAssociationList(DfdwTDesignSupervisorapply supervisorapply, PersonEntity person);

    List<DfdwTDesignServiceassociation> getAssociationList(DfdwTDesignServiceassociation serviceassociation, PersonEntity person);
    /**
     *总监、专监、施工单位：
     *服务记录： 能见申请单所关联的服务记录
     */
    List<Integer> getApplyServiceRecordIds(Integer userId);
}
