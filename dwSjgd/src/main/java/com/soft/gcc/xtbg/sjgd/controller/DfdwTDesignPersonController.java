package com.soft.gcc.xtbg.sjgd.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignPersonService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/2
 */
@RequestMapping("/sjgd/designPerson")
@RestController
public class DfdwTDesignPersonController extends BaseController {
    @Resource
    private DfdwTDesignPersonService dfdwTDesignPersonService;

    /**
     * 设计工代-PC-人员管理-分页
     * */
    @RequestMapping("/page")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX01')")
    public Result<Object> page(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.getPage(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-列表
     * */
    @RequestMapping("/list")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX01')")
    public Result<Object> list(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.getList(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-新增
     * */
    @RequestMapping("/add")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX02')")
    public Result<Object> add(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.add(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-修改
     * */
    @RequestMapping("/edit")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX03')")
    public Result<Object> edit(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.edit(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-删除
     * */
    @RequestMapping("/deleteByIds")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX04')")
    public Result<Object> deleteByIds(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.deleteByIds(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-导入
     * */
    @RequestMapping("/upload")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX05')")
    public Result<Object> upload(MultipartFile file, DfdwTDesignPerson designPerson) throws IOException {
        PersonEntity person = user();
        return dfdwTDesignPersonService.upload(file, designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-关联用户表
     * */
    @RequestMapping("/linkPerson")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX07')")
    public Result<Object> linkPerson(@RequestBody List<DfdwTDesignPerson> designPersonList) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.linkPerson(designPersonList, person);
    }

    /**
     * 服务申请 获取人员列表
     * @return
     */
    @PostMapping("getList")
    public Result<Object> getList(@RequestBody DfdwTDesignPerson designPerson){
        return Result.ok(dfdwTDesignPersonService.selectList(designPerson));
    }

    /**
     * 设计工代-PC-人员管理-查询所有有权限的人
     * */
    @RequestMapping("/getPermissonList")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX07')")
    public Result<Object> getPermissonList(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.getPermissonList(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-根据id查询角色
     * */
    @RequestMapping("/getPersonRoleById")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX08')")
    public Result<Object> getPersonRoleById(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.getPersonRoleById(designPerson, person);
    }

    /**
     * 设计工代-PC-人员管理-查询角色
     * */
    @GetMapping("/getRole")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX08')")
    public Result<Object> getRole() {
        return dfdwTDesignPersonService.getRole();
    }

    /**
     * 设计工代-PC-人员管理-编辑id角色
     * */
    @PostMapping("/editRoleById")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM01QX08')")
    public Result<Object> editRoleById(@RequestBody DfdwTDesignPerson designPerson) {
        return dfdwTDesignPersonService.editRoleById(designPerson);
    }

    @PostMapping("/getPersonListByProjectId/{projectId}")
    public Result<Object> getPersonListByProjectId(@PathVariable Integer projectId){
        return Result.ok(dfdwTDesignPersonService.getPersonListByProjectId(projectId));
    }



}
