package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignProjectspecialty;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectspecialtyService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignProjectspecialtyMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_ProjectSpecialty(设计工代-项目-专业人员)】的数据库操作Service实现
* @createDate 2024-12-30 16:40:28
*/
@Service
public class DfdwTDesignProjectspecialtyServiceImpl extends ServiceImpl<DfdwTDesignProjectspecialtyMapper, DfdwTDesignProjectspecialty>
    implements DfdwTDesignProjectspecialtyService{

}




