package com.soft.gcc.xtbg.sjgd.mapper;

import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_SupervisorApplySpecialty(设计工代-服务申请-设计工代人员)】的数据库操作Mapper
* @createDate 2024-12-30 16:40:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapplyspecialty
*/
public interface DfdwTDesignSupervisorapplyspecialtyMapper extends BaseMapper<DfdwTDesignSupervisorapplyspecialty> {

    List<DfdwTDesignSupervisorapplyspecialty> getSpecialtyPersonListByYwId(@Param("ywId") Integer ywId);

    List<DfdwTDesignSupervisorapplyspecialty> getPersonListBySupervisorApplyId(@Param("query") DfdwTDesignSupervisorapplyspecialty dfdwTDesignSupervisorapplyspecialty);
}




