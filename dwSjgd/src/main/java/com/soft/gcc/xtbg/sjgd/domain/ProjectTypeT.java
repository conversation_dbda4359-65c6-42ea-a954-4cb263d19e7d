package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目类型实体类
 * <AUTHOR>
 */
@Data
@TableName(value ="project_type_t")
public class ProjectTypeT extends DfdwBaseEntity implements Serializable {

    @TableId(value = "project_type_id", type = IdType.AUTO)
    private Long projectTypeId;                    // 项目类型ID

    @TableField("project_id")
    private Long projectId;                        // 项目ID

    @TableField("project_type")
    private String projectType;                    // 项目类型

    @TableField("project_name")
    private String projectName;                    // 项目名称

    @TableField("construction_unit_id")
    private Long constructionUnitId;               // 施工单位ID

    @TableField("profession_supervisor_id")
    private Long professionSupervisorId;           // 专业监理工程师ID

    @TableField("profession_supervisor_id_to")
    private String professionSupervisorIdTo;       // 专业监理工程师(多选)

    @TableField("construction_id")
    private String constructionId;                 // 施工ID

    @TableField("supervisor_id")
    private Long supervisorId;                     // 监理ID

    @TableField("supervisor_id_to")
    private String supervisorIdTo;                 // 监理(多选)

    @TableField("full_time_person_id")
    private Long fullTimePersonId;                 // 专职安全员ID

    @TableField("full_time_person_id_to")
    private String fullTimePersonIdTo;             // 专职安全员(多选)

    @TableField("start_date")
    private Date startDate;                        // 开始日期

    @TableField("finish_date")
    private Date finishDate;                       // 结束日期

    @TableField("is_last_finish")
    private String isLastFinish;                   // 是否最后完成

    @TableField("status")
    private String status;                         // 状态

    @TableField("create_by")
    private Long createBy;                         // 创建人

    @TableField("creation_date")
    private Date creationDate;                     // 创建时间

    @TableField("last_update_by")
    private Long lastUpdateBy;                     // 最后更新人

    @TableField("last_update_date")
    private Date lastUpdateDate;                   // 最后更新时间

    @TableField("version_number")
    private Long versionNumber;                    // 版本号

    @TableField("enable")
    private String enable;                         // 是否启用

    @TableField("old_status")
    private String oldStatus;                      // 旧状态

    @TableField("overtime_modify_flag")
    private String overtimeModifyFlag;             // 超时修改标志

    @TableField("subpackage_unit_id")
    private Long subpackageUnitId;                 // 分包单位ID

    @TableField("subpackage_unit_id_to")
    private String subpackageUnitIdTo;             // 分包单位(多选)

    @TableField("is_project_start")
    private String isProjectStart;                 // 是否项目开始

    @TableField("project_start_begin_date")
    private Date projectStartBeginDate;            // 项目开始时间

    @TableField("project_start_end_date")
    private Date projectStartEndDate;              // 项目结束时间

    @TableField("project_schedule")
    private Long projectSchedule;                  // 项目进度
}
