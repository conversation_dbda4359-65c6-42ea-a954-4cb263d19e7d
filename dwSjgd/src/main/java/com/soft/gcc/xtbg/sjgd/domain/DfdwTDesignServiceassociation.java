package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 现场服务关联表
 * @TableName DFDW_T_Design_ServiceAssociation
 */
@TableName(value ="DFDW_T_Design_ServiceAssociation")
@Data
public class DfdwTDesignServiceassociation implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 监理申请id
     */
    @TableField(value = "supervisorApplyId")
    private Integer supervisorApplyId;

    /**
     * 服务记录id
     */
    @TableField(value = "serviceRecordId")
    private Integer serviceRecordId;

    /**
     * 问题反馈id
     */
    @TableField(value = "problemFeedbackId")
    private Integer problemFeedbackId;

    /**
     * 创建人
     */
    @TableField(value = "createId")
    private Integer createId;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "updaterId")
    private Integer updaterId;

    /**
     * 修改时间
     */
    @TableField(value = "updateTime")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}