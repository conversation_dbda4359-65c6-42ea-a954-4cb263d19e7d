package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import com.soft.gcc.xtbg.base.entity.PageEntity;
import lombok.Data;

/**
 *
 * @TableName V_DFDW_T_DesignFlow
 */
@TableName(value ="V_DFDW_T_DesignFlow")
@Data
public class VDfdwTDesignflow extends PageEntity implements Serializable {
    /**
     *
     */
    @TableField(value = "ywType")
    private Integer ywType;

    /**
     *
     */
    @TableField(value = "id")
    private Integer id;

    /**
     *
     */
    @TableField(value = "designProjectId")
    private Integer designProjectId;

    /**
     *
     */
    @TableField(value = "applyNo")
    private String applyNo;

    /**
     *
     */
    @TableField(value = "personId")
    private Integer personId;

    /**
     *
     */
    @TableField(value = "approveState")
    private Integer approveState;

    @TableField(value = "createTime")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "lcDefineID")
    private Integer lcDefineID;

    /**
     * 第一个流程节点Id
     */
    @TableField(value = "firstLcJd")
    private Integer firstLcJd;

    /**
     *
     */
    @TableField(value = "lcName")
    private String lcName;

    /**
     *
     */
    @TableField(value = "ywID")
    private Integer ywID;

    /**
     *
     */
    @TableField(value = "sendPerson")
    private String sendPerson;

    /**
     *
     */
    @TableField(value = "sendPersonZgh")
    private String sendPersonZgh;

    /**
     *
     */
    @TableField(value = "AllPersonZgh")
    private String allPersonZgh;

    /**
     *
     */
    @TableField(value = "lcJdmc")
    private String lcJdmc;

    /**
     *
     */
    @TableField(value = "lcJdid")
    private Integer lcJdid;

    /**
     *
     */
    @TableField(value = "lcIsback")
    private Integer lcIsback;

    /**
     *
     */
    @TableField(value = "projectName")
    private String projectName;

    /**
     *
     */
    @TableField(value = "userName")
    private String userName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
