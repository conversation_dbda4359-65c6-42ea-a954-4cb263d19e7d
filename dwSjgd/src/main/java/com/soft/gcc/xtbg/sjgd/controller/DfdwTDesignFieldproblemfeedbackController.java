package com.soft.gcc.xtbg.sjgd.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.soft.gcc.common.lc_workFlow.entity.LcWorkflow;
import com.soft.gcc.common.lc_workFlow.service.LcWorkflowService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignFieldproblemfeedbackService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignServiceassociationService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 现场问题反馈controller
 */
@RestController
@RequestMapping("/sjgd/fieldproblemfeedback")
public class DfdwTDesignFieldproblemfeedbackController extends BaseController {

    @Resource
    DfdwTDesignFieldproblemfeedbackService dfdwTDesignFieldproblemfeedbackService;
    @Resource
    LcWorkflowService lcWorkflowService;
    @Resource
    DfdwTDesignServiceassociationService serviceassociationService;


    @PreAuthorize("@ss.hasPermi('JDWSJ01PF01QX01')")
    @PostMapping("/listPage")
    public Result<Object> listPage(@RequestBody DfdwTDesignFieldproblemfeedback dfdwTDesignSupervisorapply){
        dfdwTDesignSupervisorapply.setCurrentLoginUser(user());
        return Result.ok(dfdwTDesignFieldproblemfeedbackService.listPage(dfdwTDesignSupervisorapply));
    }

    @PreAuthorize("@ss.hasPermi('JDWSJ01PF01QX01')")
    @GetMapping("detail/{id}")
    public Result<Object> detail(@PathVariable("id") Integer id){
        return Result.ok(dfdwTDesignFieldproblemfeedbackService.detail( id));
    }


    @PreAuthorize("@ss.hasAnyPermi('JDWSJ01PF01QX02,JDWSJ01PF01QX03')")
    @PostMapping("addOrEdit")
    public Result<Object> addOrEdit(@RequestBody DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback,HttpServletRequest request){
        dfdwTDesignFieldproblemfeedback.setCurrentLoginUser(user());
        return Result.ok(dfdwTDesignFieldproblemfeedbackService.addOrEdit(dfdwTDesignFieldproblemfeedback,request));
    }


    @PostMapping("signFor")
    public Result<Object> signFor(@RequestBody DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback){
        dfdwTDesignFieldproblemfeedback.setCurrentLoginUser(user());
        return Result.ok(dfdwTDesignFieldproblemfeedbackService.signFor(dfdwTDesignFieldproblemfeedback));
    }


    @PreAuthorize("@ss.hasPermi('JDWSJ01PF01QX04')")
    @DeleteMapping("delete/{ids}")
    public Result<Object> addOrEdit(@PathVariable("ids") List<Integer> ids){
        PersonEntity user = user();
        serviceassociationService.remove(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                .in(DfdwTDesignServiceassociation::getProblemFeedbackId, ids)
                .isNull(DfdwTDesignServiceassociation::getSupervisorApplyId)
        );
        serviceassociationService.update(new LambdaUpdateWrapper<DfdwTDesignServiceassociation>()
                .set(DfdwTDesignServiceassociation::getProblemFeedbackId, null)
                .set(DfdwTDesignServiceassociation::getUpdaterId, user.getId())
                .set(DfdwTDesignServiceassociation::getUpdateTime, new Date())
                .in(DfdwTDesignServiceassociation::getProblemFeedbackId, ids)
                .isNotNull(DfdwTDesignServiceassociation::getSupervisorApplyId)
        );
        return Result.ok(dfdwTDesignFieldproblemfeedbackService.removeByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('JDWSJ01PF01QX01')")
    @GetMapping("getLcListById/{id}")
    public Result<Object> GetLcListById(@PathVariable("id") Integer id) {
        // 流程定义ID
        Integer lcDefineID = 20035;
        List<LcWorkflow> list = lcWorkflowService.list(new LambdaQueryWrapper<LcWorkflow>()
                .eq(LcWorkflow::getYwid, id)
                .eq(LcWorkflow::getLcDefineid, lcDefineID)
                .orderByAsc(LcWorkflow::getYwid, LcWorkflow::getStartdate)
        );
        return Result.ok(list);
    }


    @PreAuthorize("@ss.hasPermi('JDWSJ01PF01QX05')")
    @PostMapping("downloadPdf")
    public Result<Object> downloadPdf(@RequestBody DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletResponse response) throws IOException {
        DzccPersonEntity user = user();
        dfdwTDesignFieldproblemfeedbackService.downloadPdf(dfdwTDesignFieldproblemfeedback,response,user);
        return Result.ok();
    }

    @PreAuthorize("@ss.hasPermi('JDWSJ01PF01QX08')")
    @PostMapping("downloadExcel")
    public Result<Object> downloadExcel(@RequestBody DfdwTDesignFieldproblemfeedback dfdwTDesignFieldproblemfeedback, HttpServletResponse response){
        dfdwTDesignFieldproblemfeedback.setCurrentLoginUser(user());
        dfdwTDesignFieldproblemfeedbackService.downloadExcel(dfdwTDesignFieldproblemfeedback,response);
        return Result.ok();
    }


}
