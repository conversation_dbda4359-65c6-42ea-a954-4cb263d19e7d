package com.soft.gcc.xtbg.sjgd.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignSupervisorapply;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_FieldProblemFeedback(设计工代-现场问题反馈)】的数据库操作Mapper
* @createDate 2024-12-30 16:40:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignFieldproblemfeedback
*/
public interface DfdwTDesignFieldproblemfeedbackMapper extends BaseMapper<DfdwTDesignFieldproblemfeedback> {

    IPage<DfdwTDesignFieldproblemfeedback> listPage(@Param("page") Page<DfdwTDesignFieldproblemfeedback> page, @Param("query") DfdwTDesignFieldproblemfeedback query);


    DfdwTDesignFieldproblemfeedback getMaxApplyNo( @Param("applyNo") String applyNo);
    DfdwTDesignFieldproblemfeedback selectInfoById( @Param("id") Integer id);

    IPage<DfdwTDesignFieldproblemfeedback> getWtfkAssociationList(Page<Object> objectPage, @Param("query") DfdwTDesignFieldproblemfeedback problemfeedback);
    List<DfdwTDesignSupervisorapply> getApplyList(@Param("query") DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply ) ;
}




