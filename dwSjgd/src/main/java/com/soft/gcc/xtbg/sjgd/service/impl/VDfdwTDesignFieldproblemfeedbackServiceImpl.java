package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjgd.domain.VDfdwTDesignFieldproblemfeedback;
import com.soft.gcc.xtbg.sjgd.service.VDfdwTDesignFieldproblemfeedbackService;
import com.soft.gcc.xtbg.sjgd.mapper.VDfdwTDesignFieldproblemfeedbackMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【V_DFDW_T_Design_FieldProblemFeedback】的数据库操作Service实现
* @createDate 2024-12-31 13:29:50
*/
@Service
public class VDfdwTDesignFieldproblemfeedbackServiceImpl extends ServiceImpl<VDfdwTDesignFieldproblemfeedbackMapper, VDfdwTDesignFieldproblemfeedback>
    implements VDfdwTDesignFieldproblemfeedbackService{

}




