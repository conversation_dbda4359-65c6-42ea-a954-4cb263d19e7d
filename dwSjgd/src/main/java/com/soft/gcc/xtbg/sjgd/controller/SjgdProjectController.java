package com.soft.gcc.xtbg.sjgd.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignPersonService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignSupervisorapplyService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@RequestMapping("/Sjgd/project")
@RestController
public class SjgdProjectController extends BaseController {
    @Autowired
    private DfdwTDesignProjectService projectService;
    @Resource
    private DfdwTDesignPersonService dfdwTDesignPersonService;
    @Resource
    TFileService fileService;
    @Autowired
    private DfdwTDesignSupervisorapplyService dfdwTDesignSupervisorapplyService;

    /**
     * 设计工代-项目管理-分页列表
     *
     * @param project
     * @return
     */
    @PostMapping("/getPageList")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX01')")//设计工代-项目管理-查看
    public Result<Object> getList(@RequestBody DfdwTDesignProject project) {
        Integer userId = user().getId();
        Page<DfdwTDesignProject> page = new Page<>(project.getPageNum(), project.getPageSize());
        Boolean zongGuanLi = projectService.hasDesignProjectRole(userId, "协同办公-设计工代-总管理");
        if (zongGuanLi) {
            projectService.getPageListUserId(page, null, project);
        } else {
            projectService.getPageListUserId(page, userId, project);
        }
        projectService.getProjectSpecialtyList(page.getRecords());
        return Result.ok(page);
    }

    /**
     * 项目列表
     *
     * @param project
     * @return
     */
    @PostMapping("/getVProjectPageList")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX01')")//设计工代-项目管理-查看
    public Result<Object> getVProjectPageList(@RequestBody VProjectT project) {
        Page<VProjectT> page = new Page<>(project.getPageNum(), project.getPageSize());
        IPage<VProjectT> pageList = projectService.getVProjectPageList(page, project);
        return Result.ok(pageList);
    }

    /**
     * 项目总监，专监，人员的选择
     *
     * @param project
     * @return
     */
    @PostMapping("/getLeaderList")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX01')")//设计工代-项目管理-查看
    public Result<Object> getLeaderList(@RequestBody VProjectT project) {
        List<LeaderPerson> pageList = projectService.getLeaderList(project);
        return Result.ok(pageList);
    }


    @PostMapping("getList")
    //@PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX01')")//设计工代-项目管理-查看
    public Result<Object> getAllList(@RequestBody DfdwTDesignProject project) {
        Page<DfdwTDesignProject> page = new Page<>(project.getPageNum(), -1);
        Integer userId = user().getId();
        //Boolean  sheZong= projectService.hasDesignProjectRole(userId, "协同办公-设计工代-项目设总");
        Boolean gongDai = projectService.hasDesignProjectRole(userId, "协同办公-设计工代-设计工代人员");
        Boolean zongGuanLi = projectService.hasDesignProjectRole(userId, "协同办公-设计工代-总管理");

        if (zongGuanLi) {
            //总管理查询所有数据
            project.setDesignUserId(null);
            projectService.getPageListUserId(page, null, project);
        } else if (gongDai) {
            projectService.getPageListUserId(page, userId, project);
        }
        return Result.ok(page);
    }

    @PostMapping("/addProject")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX02')")//设计工代-项目管理-新增
    public Result<Object> add(@RequestBody DfdwTDesignProject project) {
        project.setCreateId(user().getId());
        project.setCreateTime(new Date());
        String projectName = project.getProjectName();
        List<DfdwTDesignProject> list = projectService.list(new QueryWrapper<DfdwTDesignProject>().lambda()
                .eq(DfdwTDesignProject::getProjectName, projectName)
               .eq(DfdwTDesignProject::getDeleted, 0));
        if (CollectionUtil.isNotEmpty(list)) {
            return Result.error("项目名称已存在");
        }
        if (CollectionUtil.isNotEmpty(project.getFromSpecialtyPersonList())) {
            projectService.save(project);
            projectService.addProjectSpecialty(project);
//            projectService.addProjectFile(project);
            fileService.saveOrUpdateFile(String.valueOf(project.getId()), "xmgl", "1", "20036", project.getProjectFiles());
        } else {
            return Result.error("请至少添加一个设计专业");
        }
        return Result.ok();
    }

    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX03')")//设计工代-项目管理-编辑
    public Result<Object> update(@RequestBody DfdwTDesignProject project) {
        DfdwTDesignProject beforeProject = projectService.getById(project.getId());
        Integer sheZongUserId = beforeProject.getDesignDirectorUserId();
        Integer createId = beforeProject.getCreateId();
        Integer currentId = user().getId();
        if (!currentId.equals(sheZongUserId) && !currentId.equals(createId)) {
            return Result.error("只有项目设总和记录创建人可以编辑项目信息");
        }
        String projectName = project.getProjectName();
        List<DfdwTDesignProject> list = projectService.list(new QueryWrapper<DfdwTDesignProject>().lambda()
                .eq(DfdwTDesignProject::getProjectName, projectName)
                .eq(DfdwTDesignProject::getDeleted, 0)
                .ne(DfdwTDesignProject::getId, project.getId()));
        if (CollectionUtil.isNotEmpty(list)) {
            return Result.error("项目名称已存在");
        }
        if (project.getFromSpecialtyPersonList() != null && project.getFromSpecialtyPersonList().size() > 0) {
            projectService.update(project, new QueryWrapper<DfdwTDesignProject>().lambda()
                    .eq(DfdwTDesignProject::getId, project.getId()));
            projectService.updateProjectSpecialty(project);
//            projectService.updateProjectFile(project);
            fileService.saveOrUpdateFile(String.valueOf(project.getId()), "xmgl", "1", "20036", project.getProjectFiles());
        } else {
            return Result.error("请至少添加一个设计专业");
        }
        return Result.ok();
    }

    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX04')")//设计工代-项目管理-删除
    public Result<Object> delete(@RequestBody DfdwTDesignProject project) {
        List<Integer> ids = project.getIds();
        if (ids.size() == 0) {
            return Result.error("请至少选择一条记录");
        }
        List<VDfdwTDesignflow> useList = projectService.checkUse(ids);
        if(useList.size() >0){
            return Result.error("该项目已被使用，无法删除！");
        }
        for (int i = 0; i < ids.size(); i++) {
            //判断是否存在已关联的项目数据
            DfdwTDesignProject entity = projectService.getById(ids.get(i));
            if (entity != null) {
                entity.setDeleted(1);
                projectService.updateById(entity);
            }
        }
        return Result.ok();
    }

    @GetMapping("/detail")
    public Result<Object> detail(@RequestParam("id") Integer id) {
        return Result.ok(projectService.detail(id));
    }

    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX05')")//设计工代-项目管理-导出
    public void exportPdf(@RequestBody DfdwTDesignProject project, HttpServletResponse response) {
        try {
            DzccPersonEntity user = user();
            projectService.exportProjectPdf(project.getIds(), response, user);
        } catch (Exception e) {
//            log.error("导出PDF失败", e);
            throw new RuntimeException("导出PDF失败");
        }
    }

    /**
     * 设计工代-项目管理-新增人员分页列表
     *
     * @param designPerson
     * @return
     */
    @PostMapping("/getPersonPageList")
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX01')")
    public Result<Object> getPersonPageList(@RequestBody DfdwTDesignPerson designPerson) {
        PersonEntity person = user();
        return dfdwTDesignPersonService.getPersonPageList(designPerson, person);
    }


    /**
     * 导出项目信息Excel
     * @param project
     * @param response
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01PM02QX06')")
    @PostMapping("downloadExcel")
    public Result<Object> downloadExcel(@RequestBody DfdwTDesignProject project, HttpServletResponse response){
        project.setCurrentLoginUser(user());
        projectService.downloadExcel(project,response);
        return Result.ok();
    }
}
