package com.soft.gcc.xtbg.sjgd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignServiceassociation;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignServiceassociationService;
import com.soft.gcc.xtbg.sjgd.mapper.DfdwTDesignServiceassociationMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_ServiceAssociation(现场服务关联表)】的数据库操作Service实现
* @createDate 2025-02-17 16:51:28
*/
@Service
public class DfdwTDesignServiceassociationServiceImpl extends ServiceImpl<DfdwTDesignServiceassociationMapper, DfdwTDesignServiceassociation>
    implements DfdwTDesignServiceassociationService{

    @Override
    public void deleteFwsqByIds(List<Integer> idList) {
        if (!idList.isEmpty()) {
            baseMapper.deleteFwsqByIds(idList);
            baseMapper.updateFwsqByIds(idList);
        }
    }

    @Override
    public void deleteFwjlByIds(List<Integer> idList) {
        if (!idList.isEmpty()) {
            baseMapper.deleteFwjlByIds(idList);
            baseMapper.updateFwjlByIds(idList);
        }
    }
}




