package com.soft.gcc.xtbg.sjgd.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.common.person.entity.Vperson;
import com.soft.gcc.common.role.entity.Role;
import com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_Design_Person(设计工代-人员管理)】的数据库操作Mapper
* @createDate 2024-12-30 16:40:28
* @Entity com.soft.gcc.xtbg.sjgd.domain.DfdwTDesignPerson
*/
public interface DfdwTDesignPersonMapper extends BaseMapper<DfdwTDesignPerson> {

    List<DfdwTDesignPerson> selectAllList(@Param("designPerson") DfdwTDesignPerson designPerson );

    List<Vperson> selectPersonList(DfdwTDesignPerson designPerson);

    IPage<DfdwTDesignPerson> getPersonPageList(Page<Object> objectPage, @Param("designPerson") DfdwTDesignPerson designPerson, @Param("person") PersonEntity person);

    List<Role> selectPersonRoleById(@Param("designPerson") DfdwTDesignPerson designPerson);

    List<Role> selectRoleBySjgd();

    List<DfdwTDesignPerson> selectPersonListByIds(List<Integer> ids);
}




