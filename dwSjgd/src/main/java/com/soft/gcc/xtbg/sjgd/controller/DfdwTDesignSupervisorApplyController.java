package com.soft.gcc.xtbg.sjgd.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.common.lc_workFlow.entity.LcWorkflow;
import com.soft.gcc.common.lc_workFlow.service.LcWorkflowService;
import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.sjgd.domain.*;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignFieldservicerecordService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignProjectService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignServiceassociationService;
import com.soft.gcc.xtbg.sjgd.service.DfdwTDesignSupervisorapplyService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设计工代-服务申请
 */
@RequestMapping("/sjgd/designSupervisorApply")
@RestController
public class DfdwTDesignSupervisorApplyController extends BaseController {
    @Resource
    DfdwTDesignSupervisorapplyService dfdwTDesignSupervisorapplyService;
    @Resource
    DfdwTDictDataService dfdwTDictDataService;
    @Resource
    LcWorkflowService lcWorkflowService;
    @Resource
    DfdwTDesignServiceassociationService serviceassociationService;
    @Resource
    DfdwTDesignFieldservicerecordService fieldservicerecordService;
    @Resource
    private DfdwTDesignProjectService designProjectService;

    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX01')")
    @PostMapping("/listPage")
    public Result<Object> listPage(@RequestBody DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply){
        dfdwTDesignSupervisorapply.setCurrentLoginUser(user());
        return Result.ok(dfdwTDesignSupervisorapplyService.listPage(dfdwTDesignSupervisorapply));
    }

    /**
     * 获取详情 带文件地址
     * @param id
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX01')")
    @GetMapping("detail/{id}")
    public Result<Object> detail(@PathVariable("id") Integer id){
        return Result.ok(dfdwTDesignSupervisorapplyService.detail( id));
    }

    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX01')")
    @GetMapping("getLcListById/{id}")
    public Result<Object> GetLcListById(@PathVariable("id") Integer id) {
        // 流程定义ID
        Integer lcDefineID = 20033;
        List<LcWorkflow> list = lcWorkflowService.list(new LambdaQueryWrapper<LcWorkflow>()
                .eq(LcWorkflow::getYwid, id)
                .eq(LcWorkflow::getLcDefineid, lcDefineID)
                .orderByAsc(LcWorkflow::getYwid, LcWorkflow::getStartdate)
        );
        return Result.ok(list);
    }

    @PreAuthorize("@ss.hasAnyPermi('JDWSJ01FS01QX02,JDWSJ01FS01QX03')")
    @PostMapping("addOrEdit")
    public Result<Object> addOrEdit(@RequestBody DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply){
        dfdwTDesignSupervisorapply.setCurrentLoginUser(user());
        return Result.ok(dfdwTDesignSupervisorapplyService.addOrEdit(dfdwTDesignSupervisorapply));
    }


    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX04')")
    @DeleteMapping("delete/{ids}")
    public Result<Object> addOrEdit(@PathVariable("ids") List<Integer> ids){
        PersonEntity user = user();
        serviceassociationService.remove(new LambdaQueryWrapper<DfdwTDesignServiceassociation>()
                .in(DfdwTDesignServiceassociation::getSupervisorApplyId, ids)
                .isNull(DfdwTDesignServiceassociation::getProblemFeedbackId)
        );
        serviceassociationService.update(new LambdaUpdateWrapper<DfdwTDesignServiceassociation>()
                .set(DfdwTDesignServiceassociation::getSupervisorApplyId, null)
                .set(DfdwTDesignServiceassociation::getUpdaterId, user.getId())
                .set(DfdwTDesignServiceassociation::getUpdateTime, new Date())
                .in(DfdwTDesignServiceassociation::getSupervisorApplyId, ids)
                .isNotNull(DfdwTDesignServiceassociation::getProblemFeedbackId)
        );
        return Result.ok(dfdwTDesignSupervisorapplyService.removeByIds(ids));
    }


    /**
     * 通用方法 根据字典type 获取所有的字段
     * @param dictType
     * @return
     */
    @GetMapping("getDictList/{dictType}")
    public Result<Object> getDictList(@PathVariable("dictType") String dictType){
        return Result.ok(dfdwTDictDataService.list(new LambdaQueryWrapper<DfdwTDictData>()
                .select(DfdwTDictData::getLabel,DfdwTDictData::getValue)
                .eq(DfdwTDictData::getDictType,dictType)
        ));
    }


    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX05')")
    @PostMapping("downloadPdf")
    public Result<Object> downloadPdf(@RequestBody DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, HttpServletResponse response) throws IOException {
        DzccPersonEntity user=user();
        dfdwTDesignSupervisorapplyService.downloadPdf(dfdwTDesignSupervisorapply,response,user);
        return Result.ok();
    }

    /**
     * 服务申请、服务记录关联
     * @param dfdwTDesignSupervisorapply 实体类
     * @return
     */
    @PreAuthorize("@ss.hasAnyPermi('JDWSJ01FS01QX02,JDWSJ01FS01QX03,JDWSJ01FS01QX08')")
    @PostMapping("serviceAssociation")
    public Result<Object> serviceAssociation(@RequestBody DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply){
        PersonEntity person = user();
        return Result.ok(dfdwTDesignSupervisorapplyService.serviceAssociation(dfdwTDesignSupervisorapply, person));
    }

    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX07')")
    @PostMapping("downloadExcel")
    public Result<Object> downloadExcel(@RequestBody DfdwTDesignSupervisorapply dfdwTDesignSupervisorapply, HttpServletResponse response) {
        dfdwTDesignSupervisorapply.setCurrentLoginUser(user());
        dfdwTDesignSupervisorapplyService.downloadExcel(dfdwTDesignSupervisorapply,response);
        return Result.ok();
    }

    /**
     * 得到服务记录未关联数据
     * @param fieldservicerecord 实体类
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX08')")
    @PostMapping("getFwjlAssociationList")
    public Result<Object> getFwjlAssociationList(@RequestBody DfdwTDesignFieldservicerecord fieldservicerecord){
        PersonEntity person = user();
        return Result.ok(fieldservicerecordService.getFwjlAssociationList(fieldservicerecord, person));
    }

    /**
     * 得到关联数据
     * @param serviceassociation 实体类
     * @return
     */
    @PreAuthorize("@ss.hasPermi('JDWSJ01FS01QX08')")
    @PostMapping("getAssociationList")
    public Result<Object> getAssociationList(@RequestBody DfdwTDesignServiceassociation serviceassociation){
        PersonEntity person = user();
        return Result.ok(dfdwTDesignSupervisorapplyService.getAssociationList(serviceassociation, person));
    }

    @GetMapping("getFilterSpecialtyDict")
    public Result<Object> getFilterSpecialtyDict(@RequestParam("id") Integer id){
        PersonEntity person = user();
        DfdwTDesignProject detail = designProjectService.detail(id);
        List<DfdwTDesignProject> list=new ArrayList<>();
        list.add(detail);
        designProjectService.getProjectSpecialtyList(list);
        List<SpecialtyPerson> fromSpecialtyPersonList = list.get(0).getFromSpecialtyPersonList();
        List<String> collect = fromSpecialtyPersonList.stream().map(s -> s.getSpecialtyName()).collect(Collectors.toList());
        return Result.ok(collect);
    }
}
