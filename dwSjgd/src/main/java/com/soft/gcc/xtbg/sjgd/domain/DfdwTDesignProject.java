package com.soft.gcc.xtbg.sjgd.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.soft.gcc.xtbg.base.DzccPersonEntity;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import com.soft.gcc.xtbg.base.entity.PageEntity;
import lombok.Data;

/**
 * 设计工代-项目管理
 * @TableName DFDW_T_Design_Project
 */
@TableName(value ="DFDW_T_Design_Project")
@Data
public class DfdwTDesignProject extends PageEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 监理宝项目Id
     */
    @TableField(value = "projectId")
    private Integer projectId;

    /**
     * 项目名称
     */
    @TableField(value = "projectName")
    private String projectName;

    /**
     * 监理项目总监Id
     */
    @TableField(value = "projectDirectorUserId")
    private Integer projectDirectorUserId;

    /**
     * 监理项目总监名称
     */
    @TableField(value = "projectDirectorUserName")
    private String projectDirectorUserName;

    /**
     * 监理项目专监
     */
    @TableField(value = "projectSpecialOverseeUserId")
    private Integer projectSpecialOverseeUserId;

    /**
     * 监理项目专监名称
     */
    @TableField(value = "projectSpecialOverseeUserName")
    private String projectSpecialOverseeUserName;

    /**
     * 设总人员（默认项目创建人）
     */
    @TableField(value = "designDirectorUserId")
    private Integer designDirectorUserId;

    /**
     * 设总人员名称（默认项目创建人）
     */
    @TableField(value = "designDirectorUserName")
    private String designDirectorUserName;

    /**
     * 创建时间（申请时间）
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 创建人（冗余字段）
     */
    @TableField(value = "createId")
    private Integer createId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<SpecialtyPerson> fromSpecialtyPersonList;
    @TableField(exist = false)
    private String projectFiles;
    /**
     * Id列表
     */
    @TableField(exist = false)
    private List<Integer> ids;
    /**
     * 下载类型
     */
    @TableField(exist = false)
    private String downloadType;

    /**
     * 设计工代人员id
     */
    @TableField(exist = false)
    private Integer designUserId;

    /**
     * 删除标识
     */
    @TableField(value = "deleted")
    private Integer deleted;
    @TableField(value = "memberIds")
    private String memberIds;
    @TableField(value = "memberNames")
    private String memberNames;

    /**
     * 当前登录用户
     */
    @TableField(exist = false)
    private DzccPersonEntity currentLoginUser;

    /**
     * 导出使用-设计工代专业人员
     */
    @TableField(exist = false)
    private String specialtyUsers;

    /**
     * 施工单位ids
     */

    @TableField(value = "constructionUnitUserIds")
    private String  constructionUnitUserIds;
    /**
     * 施工单位names
     */
    @TableField(value = "constructionUnitNames")
    private String  constructionUnitNames;

}
