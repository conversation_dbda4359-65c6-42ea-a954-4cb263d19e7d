<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlLcjdMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.Lcjd">
            <id property="ID" column="ID" jdbcType="INTEGER"/>
            <result property="lcjdID" column="lcjdID" jdbcType="INTEGER"/>
            <result property="lc_defineID" column="lc_defineID" jdbcType="INTEGER"/>
            <result property="jdmc" column="jdmc" jdbcType="VARCHAR"/>
            <result property="nextID" column="nextID" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="shgr" column="shgr" jdbcType="INTEGER"/>
            <result property="groupID" column="GroupID" jdbcType="VARCHAR"/>
            <result property="isBX" column="IsBX" jdbcType="INTEGER"/>
            <result property="formType" column="FormType" jdbcType="VARCHAR"/>
            <result property="checkFile" column="CheckFile" jdbcType="VARCHAR"/>
            <result property="checkData" column="CheckData" jdbcType="VARCHAR"/>
            <result property="canBack" column="canBack" jdbcType="INTEGER"/>
            <result property="backjdID" column="BackjdID" jdbcType="VARCHAR"/>
            <result property="lookFileJdID" column="LookFileJdID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,lcjdID,lc_defineID,
        jdmc,nextID,type,
        shgr,GroupID,IsBX,
        FormType,CheckFile,CheckData,
        canBack,BackjdID,LookFileJdID
    </sql>
</mapper>
