<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTPersonlimitlogMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="srcPersonId" column="SrcPersonId" jdbcType="INTEGER"/>
            <result property="limit" column="Limit" jdbcType="DECIMAL"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,Src<PERSON>ersonId,Limit,
        CreateTime
    </sql>
    <select id="getPersonLimitLog" resultType="com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog"
            parameterType="java.lang.Integer">
        SELECT
            log.*,
            p.RealName
        FROM
            GCJL_T_PersonLimitLog log
                LEFT JOIN GCJL_T_Person gp ON log.SrcPersonId =  gp.SrcUserId
                LEFT JOIN Person p ON p.Id = gp.SrcUserId
        WHERE
            log.SrcPersonId = #{srcPersonId} ORDER BY CreateTime DESC

    </select>

</mapper>
