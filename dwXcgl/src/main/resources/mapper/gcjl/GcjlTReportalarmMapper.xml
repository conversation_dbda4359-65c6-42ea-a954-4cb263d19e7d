<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTReportalarmMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTReportalarm">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="deptId" column="DeptId" jdbcType="INTEGER"/>
            <result property="alarmType" column="AlarmType" jdbcType="VARCHAR"/>
            <result property="alarmTime" column="AlarmTime" jdbcType="TIMESTAMP"/>
            <result property="usersId" column="UsersId" jdbcType="VARCHAR"/>
            <result property="usersName" column="UsersName" jdbcType="VARCHAR"/>
            <result property="alarmNote" column="AlarmNote" jdbcType="VARCHAR"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,DeptId,AlarmType,
        AlarmTime,UsersId,UsersName,
        AlarmNote,CreateTime
    </sql>
</mapper>
