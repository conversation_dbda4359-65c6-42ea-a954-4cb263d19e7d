<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTProjectpointMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTProjectpoint">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="pointName" column="PointName" jdbcType="VARCHAR"/>
            <result property="deptId" column="DeptId" jdbcType="INTEGER"/>
            <result property="latitude" column="Latitude" jdbcType="DECIMAL"/>
            <result property="longitude" column="Longitude" jdbcType="DECIMAL"/>
            <result property="state" column="State" jdbcType="INTEGER"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="locationName" column="LocationName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,PointName,DeptId,
        Latitude,Longitude,State,
        CreateTime,LocationName
    </sql>
</mapper>
