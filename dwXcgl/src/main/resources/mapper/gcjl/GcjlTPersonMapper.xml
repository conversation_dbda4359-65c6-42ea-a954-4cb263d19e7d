<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTPersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="srcUserId" column="SrcUserId" jdbcType="INTEGER"/>
            <result property="topDeptId" column="TopDeptId" jdbcType="INTEGER"/>
            <result property="deptId" column="DeptId" jdbcType="INTEGER"/>
            <result property="userFullPath" column="UserFullPath" jdbcType="VARCHAR"/>
            <result property="state" column="State" jdbcType="INTEGER"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="costTopLimit" column="CostTopLimit" jdbcType="DECIMAL"/>
            <result property="endMileage" column="EndMileage" jdbcType="DECIMAL"/>
            <result property="endCost" column="EndCost" jdbcType="DECIMAL"/>
            <result property="isNetPerson" column="IsNetPerson" jdbcType="INTEGER"/>
            <result property="jlId" column="JlId" jdbcType="INTEGER"/>
            <result property="endArrivedTime" column="EndArrivedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,SrcUserId,TopDeptId,
        DeptId,UserFullPath,State,
        CreateTime,CostTopLimit,EndMileage,
        EndCost,IsNetPerson,JlId,
        EndArrivedTime
    </sql>



    <insert id="batchInsert" parameterType="com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson">
        INSERT INTO GCJL_T_PERSON (TopDeptId, SrcUserId, DeptId, UserFullPath, State,CreateTime,CostTopLimit)
        VALUES
        <foreach collection="list" index="" item="item" separator=",">
            (#{item.TopDeptId,jdbcType=INTEGER},
            #{item.SrcUserId,jdbcType=INTEGER},
            #{item.DeptId,jdbcType=INTEGER},
            #{item.UserFullPath,jdbcType=VARCHAR},
            #{item.State,jdbcType=INTEGER},
            #{item.CreateTime,jdbcType=TIMESTAMP},
            #{item.CostTopLimit,jdbcType =DECIMAL})
        </foreach>


    </insert>

    <update id="updateCostTopLimit">
        update GCJL_T_Person set CostTopLimit = #{CostTopLimit} where SrcUserId = #{srcUserId}
    </update>


    <update id="cleanEndData">
        update GCJL_T_Person set EndMileage = null, EndCost =null, JlId = null,EndArrivedTime = null ,EndArrivedJlId = null where Id = #{id}
    </update>


    <select id="getPersonList" resultType="com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson">
        SELECT
            gp.Id,
            gp.SrcUserId,
            p.RealName ,
            d.DeptName,
            p.Sphone,
            p.Telephone,
            gp.CostTopLimit,
            gp.State,
            gp.UserFullPath,
            gp.TopDeptId,
            gp.DeptId
        FROM
            GCJL_T_Person gp
                LEFT JOIN Person p ON gp.SrcUserId = p.Id
                LEFT JOIN GCJL_T_Department d on gp.DeptId = d.Id
        WHERE
            1 = 1
            <if test="deptId != 0 ">
              and gp.DeptId = #{deptId}
            </if>
            <if test="realName != null and realName !='' ">
                and p.RealName like concat('%',concat(#{realName},'%'))
            </if>
        ORDER BY gp.CreateTime DESC
    </select>



    <select id="exportExcel" resultType="com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson"
            parameterType="java.lang.Integer">
        SELECT
            p.RealName ,
            d.DeptName,
            p.Sphone,
            p.Telephone,
            CASE

                WHEN gp.State = 1 THEN
                    '启用'
                WHEN gp.State = 0 THEN
                    '停用'
                END
                AS StateStr
        FROM
            GCJL_T_Person gp
                LEFT JOIN Person p ON gp.SrcUserId = p.Id
                LEFT JOIN GCJL_T_Department d ON gp.DeptId = d.Id
        WHERE
        1 = 1
        <if test="deptId != 0 ">
            and gp.DeptId = #{deptId}
        </if>
        <if test="realName != null and realName !='' ">
            and p.RealName like concat('%',concat(#{realName},'%'))
        </if>
         ORDER BY gp.CreateTime DESC
    </select>
</mapper>
