<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTLcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTLc">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="bizNo" column="BizNo" jdbcType="VARCHAR"/>
            <result property="userId" column="UserId" jdbcType="INTEGER"/>
            <result property="userName" column="UserName" jdbcType="VARCHAR"/>
            <result property="userFullPath" column="UserFullPath" jdbcType="VARCHAR"/>
            <result property="planTime" column="PlanTime" jdbcType="DATE"/>
            <result property="note" column="Note" jdbcType="VARCHAR"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="planMileage" column="PlanMileage" jdbcType="DECIMAL"/>
            <result property="planCost" column="PlanCost" jdbcType="DECIMAL"/>
            <result property="performMileage" column="PerformMileage" jdbcType="DECIMAL"/>
            <result property="performCost" column="PerformCost" jdbcType="DECIMAL"/>
            <result property="bizType" column="BizType" jdbcType="VARCHAR"/>
            <result property="approvingState" column="ApprovingState" jdbcType="INTEGER"/>
            <result property="approveUserId" column="ApproveUserId" jdbcType="INTEGER"/>
            <result property="approveNote" column="ApproveNote" jdbcType="VARCHAR"/>
            <result property="approveTime" column="ApproveTime" jdbcType="TIMESTAMP"/>
            <result property="approveDeptId" column="ApproveDeptId" jdbcType="INTEGER"/>
            <result property="topDeptId" column="TopDeptId" jdbcType="INTEGER"/>
            <result property="deptId" column="DeptId" jdbcType="INTEGER"/>
            <result property="deptName" column="DeptName" jdbcType="VARCHAR"/>
            <result property="phone" column="Phone" jdbcType="VARCHAR"/>
            <result property="performState" column="PerformState" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,BizNo,UserId,
        UserName,UserFullPath,PlanTime,
        Note,CreateTime,PlanMileage,
        PlanCost,PerformMileage,PerformCost,
        BizType,ApprovingState,ApproveUserId,
        ApproveNote,ApproveTime,ApproveDeptId,
        TopDeptId,DeptId,DeptName,
        Phone,PerformState
    </sql>


    <select id="selectAllList" resultType="com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto">
        select t.*,t.ArrivedTime as ArrivedTimeStr ,t2.deptName,t2.UserName,t2.PlanTime AS planTimeStr,
        CASE
            WHEN t.PointState = 0 THEN '未到'
            WHEN t.PointState = 1 THEN '已到'
        ELSE '未知状态' END AS PointStateStr,
        CASE
        WHEN t.IsGoCompany = 0 THEN '否'
        WHEN t.IsGoCompany = 1 THEN '是'
        ELSE '未知状态' END AS IsGoCompanyStr,

        CASE
        WHEN t2.ApprovingState = 0 THEN '未提交'
        WHEN t2.ApprovingState = 1 THEN '审核中'
        WHEN t2.ApprovingState = 2 THEN '已驳回'
        WHEN t2.ApprovingState = 3 THEN '已审核'
        WHEN t2.ApprovingState = 4 THEN '已过期'
        WHEN t2.ApprovingState = 5 THEN '已作废'
        ELSE '未知状态' END AS approvingStateDescription,

        CASE
        WHEN t2.NotarizeState = 0 THEN '未提交'
        WHEN t2.NotarizeState = 1 THEN '审核中'
        WHEN t2.NotarizeState = 2 THEN '已驳回'
        WHEN t2.NotarizeState = 3 THEN '确认通过'
        WHEN t2.NotarizeState = 4 THEN '已作废'
        ELSE '未知状态' END AS notarizeStateDescription,

        CASE
        WHEN t2.PerformState = 0 THEN '未执行'
        WHEN t2.PerformState = 1 THEN '执行中'
        WHEN t2.PerformState = 2 THEN '已执行'
        WHEN t2.PerformState = 3 THEN '已取消'
        ELSE '未知状态' END AS performStateDescription

        from GCJL_T_Plan t
        left join GCJL_T_LC t2 on t.LcId = t2.Id
        where  1 = 1
        <if test="dto.planStartDate != null and dto.planEndDate != null">
            AND t2.PlanTime between #{dto.planStartDate} and #{dto.planEndDate}
        </if>
        <if test="dto.userName !=null and dto.userName !='' ">
            and t2.UserName like concat('%',#{dto.userName}, '%')
        </if>
        <if test="dto.deptFullPath !=null">
            AND t2.UserFullPath LIKE concat(#{dto.deptFullPath}, '%')
        </if>
        <if test="dto.ApprovingState != -1">
            and t2.ApprovingState = #{dto.ApprovingState}
        </if>
        <if test="dto.PerformState != -1">
            and t2.PerformState = #{dto.PerformState}
        </if>
        <if test="dto.NotarizeState != -1">
            and t2.NotarizeState = #{dto.NotarizeState}
        </if>

        order by t.LcId desc ,t.Sort asc

    </select>

</mapper>
