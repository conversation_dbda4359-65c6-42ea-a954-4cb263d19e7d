<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTSubsidyMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTSubsidy">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="sysUserId" column="SysUserId" jdbcType="INTEGER"/>
            <result property="allCost" column="AllCost" jdbcType="DECIMAL"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,SysUserId,AllCost,
        CreateTime,UpdateTime
    </sql>
</mapper>
