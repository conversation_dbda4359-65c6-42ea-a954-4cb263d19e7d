<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTPlanMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTPlan">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="lcId" column="LcId" jdbcType="INTEGER"/>
            <result property="planName" column="PlanName" jdbcType="VARCHAR"/>
            <result property="latitude" column="Latitude" jdbcType="DECIMAL"/>
            <result property="longitude" column="Longitude" jdbcType="DECIMAL"/>
            <result property="mileage" column="Mileage" jdbcType="DECIMAL"/>
            <result property="cost" column="Cost" jdbcType="DECIMAL"/>
            <result property="sort" column="Sort" jdbcType="INTEGER"/>
            <result property="pointState" column="PointState" jdbcType="INTEGER"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="arrivedTime" column="ArrivedTime" jdbcType="TIMESTAMP"/>
            <result property="departTime" column="DepartTime" jdbcType="TIMESTAMP"/>
            <result property="residenceTime" column="ResidenceTime" jdbcType="INTEGER"/>
            <result property="planProjectId" column="PlanProjectId" jdbcType="INTEGER"/>
            <result property="isGoCompany" column="IsGoCompany" jdbcType="INTEGER"/>
            <result property="locationName" column="LocationName" jdbcType="VARCHAR"/>
            <result property="provinces" column="Provinces" jdbcType="INTEGER"/>
            <result property="city" column="City" jdbcType="INTEGER"/>
            <result property="area" column="Area" jdbcType="INTEGER"/>
            <result property="areaText" column="AreaText" jdbcType="VARCHAR"/>
            <result property="arrivedLongitude" column="ArrivedLongitude" jdbcType="DECIMAL"/>
            <result property="arrivedLatitude" column="ArrivedLatitude" jdbcType="DECIMAL"/>
            <result property="arrivedLocationName" column="ArrivedLocationName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,LcId,PlanName,
        Latitude,Longitude,Mileage,
        Cost,Sort,PointState,
        CreateTime,ArrivedTime,DepartTime,
        ResidenceTime,PlanProjectId,IsGoCompany,
        LocationName,Provinces,City,
        Area,AreaText,ArrivedLongitude,
        ArrivedLatitude,ArrivedLocationName
    </sql>
    <select id="getXchzList" resultType="com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto">
        SELECT p.LcId,
               p.PlanName,
                lc.UserId,
               p.Sort,
               p.ArrivedTime,
               p.DepartTime,
               lc.UserName,
               lc.DeptName,
               lc.Note,
               lc.PerformMileage,
               lc.PerformCost,
               p.Latitude,
               p.Longitude,
               p.ArrivedLocationName,
               lc.PlanTime,
               p.CreateTime,
               lc.ApproveUserId as approveUserId,
               per.RealName as approveUserName,
        lc.NotarizeState ,
        lc.ApprovingState  ,
        lc.ApplyPostponeDays,
        lc.ApprovePostponeDays
        FROM GCJL_T_Plan AS p
        LEFT JOIN GCJL_T_LC lc ON p.LcId = lc.Id
        left join Person per on lc.ApproveUserId = per.Id
        WHERE p.PointState = 1
        <if test="startDate != null">
             AND lc.PlanTime &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND lc.PlanTime &lt; #{endDate}
        </if>
        GROUP BY p.LcId,
                 p.PlanName,
                 p.Sort,
                 p.ArrivedTime,
                 p.DepartTime,
                lc.UserId,
                 lc.UserName,
                 lc.DeptName,
                 lc.Note,
                 lc.PerformMileage,
                 lc.PerformCost,
                 p.Latitude,
                 p.Longitude,
                 p.ArrivedLocationName,
                 lc.ApprovingState,
                 lc.PerformState,
                 lc.UserFullPath,
                 lc.PlanTime,
                 p.CreateTime,
                lc.ApproveUserId,
                per.RealName,
        lc.NotarizeState ,
        lc.ApprovingState  ,
        lc.ApplyPostponeDays,
        lc.ApprovePostponeDays

        HAVING lc.ApprovingState = 3
           AND lc.PerformState = 2
           AND lc.NotarizeState = 3
           AND lc.UserFullPath LIKE concat(#{deptfullpath}, '%')
        ORDER BY lc.PlanTime DESC , p.CreateTime desc;

    </select>


    <select id="getXchzListByLcId" resultType="com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto"
            parameterType="java.lang.Integer">
        SELECT
            p.LcId,
            p.PlanName,
            p.Sort,
            p.ArrivedTime,
            p.Latitude,
            p.Longitude,
            p.ArrivedLocationName,
            p.CreateTime,
            p.clockImgId  ,p.mileage,p.cost,
            p.Mileage,p.Cost,p.IsGoCompany,p.PointState,
            files.FilePath
        FROM
            GCJL_T_Plan AS p
            left join T_File files on files.id = p.clockImgId
        WHERE
            p.PointState = 1
          and
            p.LcId = #{lcId}
        ORDER BY
            p.Sort asc,
            p.CreateTime DESC;



    </select>

    <select id="selectJoinPage" resultType="com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto">
        SELECT  t2.UserName,t3.FilePath, t.ArrivedTime,t.ArrivedLocationName,t.clockImgId  FROM GCJL_T_Plan t
          LEFT JOIN GCJL_T_LC t2 on t.LcId = t2.Id
          LEFT JOIN T_file t3 on t3.ID = t.clockImgId
        where
            t.clockImgId is not null
            <if test="dto.planStartDate != null and dto.planEndDate != null">
                AND t2.PlanTime between #{dto.planStartDate} and #{dto.planEndDate}
            </if>

            <if test="dto.userName !=null and dto.userName !='' ">
                and t2.UserName like concat('%',#{dto.userName}, '%')
            </if>
            <if test="dto.deptFullPath !=null">
                AND t2.UserFullPath LIKE concat(#{dto.deptFullPath}, '%')
            </if>


        order by t.ArrivedTime

    </select>
    <select id="selectList" resultType="com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto">
        SELECT  t2.UserName,t3.FilePath, t.ArrivedTime,t.ArrivedLocationName,t.clockImgId,t3.SubTName,t3.FileName FROM GCJL_T_Plan t
        LEFT JOIN GCJL_T_LC t2 on t.LcId = t2.Id
        LEFT JOIN T_file t3 on t3.ID = t.clockImgId
        where
        t.clockImgId is not null
        <if test="dto.planStartDate != null and dto.planEndDate != null">
            AND t2.PlanTime between #{dto.planStartDate} and #{dto.planEndDate}
        </if>
        <if test="dto.userName !=null and dto.userName !='' ">
            and t2.UserName like concat('%',#{dto.userName}, '%')
        </if>
        <if test="dto.deptFullPath !=null">
            AND t2.UserFullPath LIKE concat(#{dto.deptFullPath}, '%')
        </if>

        order by t.ArrivedTime
    </select>
</mapper>
