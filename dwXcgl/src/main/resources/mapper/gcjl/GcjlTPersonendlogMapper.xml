<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTPersonendlogMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonendlog">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="srcUserId" column="SrcUserId" jdbcType="INTEGER"/>
            <result property="endMileage" column="EndMileage" jdbcType="DECIMAL"/>
            <result property="endCost" column="EndCost" jdbcType="DECIMAL"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="currDate" column="CurrDate" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,SrcUserId,EndMileage,
        EndCost,CreateTime,CurrDate
    </sql>
</mapper>
