<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTDepartmentMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="parentid" column="ParentId" jdbcType="INTEGER"/>
            <result property="deptname" column="DeptName" jdbcType="VARCHAR"/>
            <result property="deptfullpath" column="DeptFullPath" jdbcType="VARCHAR"/>
            <result property="latitude" column="Latitude" jdbcType="DECIMAL"/>
            <result property="longitude" column="Longitude" jdbcType="DECIMAL"/>
            <result property="state" column="State" jdbcType="INTEGER"/>
            <result property="createtime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="sort" column="Sort" jdbcType="INTEGER"/>
            <result property="addresslimit" column="AddressLimit" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,ParentId,DeptName,
        DeptFullPath,Latitude,Longitude,
        State,CreateTime,Sort,
        AddressLimit
    </sql>

    <select id="getDepartmentById" resultType="com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment">
        select Id ,ParentId, DeptName,Latitude,Longitude,cast(Longitude as VARCHAR) +','+ cast(Latitude as VARCHAR) as GPS,State,Sort,DeptFullPath
        from GCJL_T_Department
        where Id =#{id}
    </select>
    <select id="getTjcxDtoDept" resultType="com.soft.gcc.xtbg.xcgl.dto.TjcxDto">
        SELECT lc.DeptName, SUM ( lc.PerformMileage ) PerformMileage,SUM ( lc.PerformCost ) PerformCost
        FROM GCJL_T_LC  lc
        LEFT JOIN GCJL_T_Department d ON lc.DeptName = d.DeptName
        WHERE lc.DeptName is not null
        AND lc.PerformState = 2
        AND lc.ApprovingState = 3
        AND lc.NotarizeState = 3
        AND d.State = 1
        and lc.DeptId in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test=" deptName != null and deptName !=''  ">
            AND lc.UserFullPath like '%'+  #{deptName} + '%'
        </if>
        <if test=" startTime != null and startTime !=''  ">
            AND lc.PlanTime &gt;=  #{startTime}
        </if>
        <if test=" endTime != null and endTime !=''  ">
            AND lc.PlanTime &lt;=   #{endTime}
        </if>
        GROUP BY lc.DeptName ORDER BY PerformCost DESC
    </select>



    <select id="getTjcxDtoPerson" resultType="com.soft.gcc.xtbg.xcgl.dto.TjcxDto">
       SELECT lc.DeptName,lc.UserName ,
              SUM ( lc.PerformMileage ) PerformMileage,
              SUM ( lc.PerformCost ) PerformCost  FROM   GCJL_T_LC  lc
          LEFT JOIN GCJL_T_Department d ON lc.DeptName = d.DeptName WHERE
            lc.DeptName IS NOT NULL
            AND lc.UserName IS NOT NULL
            AND lc.PerformState = 2
            AND lc.ApprovingState = 3
            AND lc.NotarizeState = 3
            AND d.State = 1
            and lc.DeptId in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test=" deptName != null and deptName !=''  ">
         AND lc.UserFullPath like '%' + #{deptName} + '%'
        </if>
        <if test=" startTime != null and startTime !=''  ">
          AND lc.PlanTime &gt;=   #{startTime}
        </if>
        <if test=" endTime != null and endTime !=''  ">
         AND  lc.PlanTime &lt;= #{endTime}
        </if>
        GROUP BY lc.UserName,  lc.DeptName   ORDER BY  PerformCost  DESC

    </select>





    <select id="getTjcxDtoAlarm" resultType="com.soft.gcc.xtbg.xcgl.dto.TjcxDto">
        SELECT de.DeptName,re.UsersName, re.AlarmTime,
        CASE WHEN re.AlarmType = 1 THEN  '监理时长未达标' WHEN
        re.AlarmType = 2   THEN    '监理人数过多'     END
        AS AlarmTypeString,    re.AlarmNote FROM
        GCJL_T_ReportAlarm re
        LEFT JOIN GCJL_T_Department de ON re.DeptId = de.Id
        WHERE  1 =1
        and re.DeptId in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test=" deptName != null and deptName !=''  ">
        AND  de.DeptFullPath like '%'  +#{deptName}+  '%'
        </if>
        <if test=" startTime != null and startTime !=''  ">
         AND  re.AlarmTime &gt;= #{startTime}
        </if>
        <if test=" endTime != null and endTime !=''  ">
         AND   re.AlarmTime  &lt;=  #{endTime}
        </if>
        ORDER BY  re.AlarmTime DESC,de.DeptName,re.UsersName
    </select>



</mapper>
