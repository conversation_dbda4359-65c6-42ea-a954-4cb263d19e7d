<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlTSqlagentplanconfigMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.GcjlTSqlagentplanconfig">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="code" column="Code" jdbcType="VARCHAR"/>
            <result property="createTime" column="CreateTime" jdbcType="TIMESTAMP"/>
            <result property="value" column="Value" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,Code,CreateTime,
        Value
    </sql>
</mapper>
