<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.xcgl.mapper.GcjlLcCurrentstateMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.xcgl.entity.LcCurrentstate">
            <id property="ID" column="ID" jdbcType="INTEGER"/>
            <result property="lc_defineID" column="Lc_defineID" jdbcType="INTEGER"/>
            <result property="lc_Name" column="Lc_Name" jdbcType="VARCHAR"/>
            <result property="ywID" column="ywID" jdbcType="INTEGER"/>
            <result property="sendPerson" column="sendPerson" jdbcType="VARCHAR"/>
            <result property="sendPersonZgh" column="sendPersonZgh" jdbcType="VARCHAR"/>
            <result property="allPersonZgh" column="AllPersonZgh" jdbcType="VARCHAR"/>
            <result property="isMany" column="isMany" jdbcType="INTEGER"/>
            <result property="lc_jdmc" column="lc_jdmc" jdbcType="VARCHAR"/>
            <result property="lc_jdid" column="lc_jdid" jdbcType="INTEGER"/>
            <result property="lc_isback" column="lc_isback" jdbcType="INTEGER"/>
            <result property="lc_tojdid" column="lc_tojdid" jdbcType="VARCHAR"/>
            <result property="isOtherAdd" column="isOtherAdd" jdbcType="INTEGER"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="BXType" column="BXType" jdbcType="VARCHAR"/>
            <result property="PNO" column="PNO" jdbcType="VARCHAR"/>
            <result property="sendGroupIDs" column="sendGroupIDs" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,Lc_defineID,Lc_Name,
        ywID,sendPerson,sendPersonZgh,
        AllPersonZgh,isMany,lc_jdmc,
        lc_jdid,lc_isback,lc_tojdid,
        isOtherAdd,number,BXType,
        PNO,sendGroupIDs
    </sql>
</mapper>
