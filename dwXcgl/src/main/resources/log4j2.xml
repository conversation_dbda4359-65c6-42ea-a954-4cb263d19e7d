<!--设置log4j2的自身log级别为warn-->
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<configuration status="OFF" monitorInterval="30">
    <Properties>
        <Property name="pattern">%d{yyyy-MM-dd HH:mm:ss,SSS}|%p|%c|%L|%m%n</Property>
        <Property name="logPath">/temp/logs/wpsystem</Property>
    </Properties>
    <Appenders>
        <!--设置在控制台打印日志-->
        <Console name="console" target="SYSTEM_OUT">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
        </Console>

        <!--设置级别为 DEBUG 日志输出到 debug.log 中-->
        <RollingRandomAccessFile name="debug" immediateFlush="true" fileName="${logPath}/debug.log" filePattern="${logPath}/debug-%d{yyyy-MM-dd}.log">
            <Filters>
                <ThresholdFilter level="DEBUG"/>
                <ThresholdFilter level="INFO" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout charset="UTF-8"  pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingRandomAccessFile>

        <!--设置级别为 INFO 日志输出到 info.log 中-->
        <RollingRandomAccessFile name="info" immediateFlush="true" fileName="${logPath}/info.log" filePattern="${logPath}/info-%d{yyyy-MM-dd}.log">
            <Filters>
                <ThresholdFilter level="INFO"/>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout charset="UTF-8"  pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy max="60"/>
        </RollingRandomAccessFile>

        <!--设置级别为 WARN 日志输出到 warn.log 中-->
        <RollingRandomAccessFile name="warn" immediateFlush="true" fileName="${logPath}/warn.log" filePattern="${logPath}/warn-%d{yyyy-MM-dd}.log">
            <Filters>
                <ThresholdFilter level="WARN"/>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy max="60"/>
        </RollingRandomAccessFile>

        <!--设置级别为 ERROR 日志输出到 error.log 中-->
        <RollingRandomAccessFile name="error" immediateFlush="true" fileName="${logPath}/error.log" filePattern="${logPath}/error-%d{yyyy-MM-dd}.log">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Filters>
                <ThresholdFilter level="ERROR"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy max="60"/>
        </RollingRandomAccessFile>

        <!--设置级别为 ERROR 日志输出到 sql.log 中-->
        <RollingRandomAccessFile name="sql" immediateFlush="true" fileName="${logPath}/sql.log" filePattern="${logPath}/sql-%d{yyyy-MM-dd}.log">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Filters>
                <ThresholdFilter level="DEBUG"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <DefaultRolloverStrategy max="60"/>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="console"/>
        </Root>

        <!-- 2：自己的程序包，输出所有有用信息 -->
        <Logger name="com.soft" level="INFO">
            <AppenderRef ref="debug"/>
            <AppenderRef ref="info"/>
            <AppenderRef ref="warn"/>
            <AppenderRef ref="error"/>
        </Logger>

        <Logger name="feign" level="DEBUG">
            <AppenderRef ref="console"/>
        </Logger>

        <!-- 3：设置spring和mybatis的输出级别为INFO，过滤掉一些无用的DEBUG信息 -->
        <Logger name="org.springframework" level="INFO"/>
        <Logger name="org.mybatis" level="INFO"/>
        <Logger name="com.alibaba.fastjson" level="INFO"/>
    </Loggers>
</configuration>
