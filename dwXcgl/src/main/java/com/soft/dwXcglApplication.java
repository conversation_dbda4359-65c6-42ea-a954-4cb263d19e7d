package com.soft;

import com.alibaba.fastjson.util.TypeUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.web.context.request.RequestContextListener;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients(basePackages ={"com.soft.framework.helper"})
@MapperScan({"com.soft.gcc.**.mapper"})
public class dwXcglApplication extends SpringBootServletInitializer implements CommandLineRunner {
    private static final Logger log = LoggerFactory.getLogger(dwXcglApplication.class);

    public static void main(String[] args) {
        TypeUtils.compatibleWithJavaBean = true;
        SpringApplication.run(dwXcglApplication.class, args);
        log.info("App start success!");
        log.info(":) Thanks buddy, we can make a lot together!");
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        TypeUtils.compatibleWithJavaBean = true;
        return application.sources(dwXcglApplication.class);
    }

    @Override
    public void run(String... args) throws Exception {

    }

    @Bean
    public RequestContextListener requestContextListener(){
        return new RequestContextListener();
    }
}
