package com.soft.gcc.common.person.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.xtbg.base.controller.Result;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【Person】的数据库操作Service
* @createDate 2023-03-28 14:23:59
*/
public interface PersonService extends IService<Person> {


    Result<Object> selectPageList(Map<String,String> param);

}
