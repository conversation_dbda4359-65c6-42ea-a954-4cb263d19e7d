package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 流程定义
* @TableName Lcdefine
*/
@TableName(value ="Lcdefine")
@Data
public class Lcdefine implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    *
    */
    @TableField(value = "LcID")
    @JSONField(name = "LcId")

    private Integer LcId;
    /**
    *
    */
    @TableField(value = "lcName")
    @JSONField(name = "lcName")
    private String lcName;
    /**
    *
    */
    @TableField(value = "ywb")
    @JSONField(name = "ywb")
    private String ywb;
    /**
    *
    */
    @TableField(value = "ywUrl")
    @JSONField(name = "ywUrl")
    private String ywUrl;
    /**
    *
    */
    @TableField(value = "xszd")

    private String xszd;
    /**
    *
    */
    @TableField(value = "isUse")
    @JSONField(name = "isUse")
    private Integer isUse;
    /**
    *
    */
    @TableField(value = "app_url")
    @JSONField(name = "appUrl")
    private String appUrl;
    /**
    *
    */
    @TableField(value = "app_ywb")
    @JSONField(name = "appYwb")
    private String appYwb;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
