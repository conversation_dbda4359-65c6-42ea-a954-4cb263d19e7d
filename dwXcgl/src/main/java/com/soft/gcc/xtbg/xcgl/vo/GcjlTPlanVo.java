package com.soft.gcc.xtbg.xcgl.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createDate 2023/8/2
 */
@Data
public class GcjlTPlanVo  implements Serializable {
    private Integer lcId;
    private String deptName;
    private String userName;
    private String tripDate;
    private String note;
    private String performMileage;
    private String performCost;
    private String startSite;
    private String startTime;
    private String endSite;
    private String endTime;
    private String middleSite;
    private String isTab;
    private String arrivedLocationName;
    private String isSpecialItinerary;
    private Date planTime;
    private String approveUserName;
    private Integer approveUserId;
    /**
     * 确认状态（0未提交、1审批中、2已驳回、3确认通过）
     */
    private Integer notarizeState;
    /**
     * 审核状态（0 未提交、1 审核中、2 已驳回、3 已审核、4已过期、5已作废
     */
    private Integer approvingState;
    /**
     * 申请延期天数：行程确认提交日期-行程计划日期
     */
    private Integer applyPostponeDays;
    /**
     * 审批延期天数：行程确认审批提交日期- 一级审批人审批结束日期
     */
    private Integer approvePostponeDays;
}
