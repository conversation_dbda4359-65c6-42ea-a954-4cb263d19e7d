package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcbbDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTXcbb;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTXcbbMapper;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTXcbbService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.SimpleFormatter;

@Service
public class GcjlTXcbbServiceImpl extends ServiceImpl<GcjlTXcbbMapper, GcjlTXcbb> implements IGcjlTXcbbService {

    @Autowired
    GcjlTDepartmentService departmentService;

    @Override
    public Result<Object> selectPageList(GcjlTXcbbDto gcjlTXcbb) {
        int pageNum = ParseUtil.tryParseInt(gcjlTXcbb.getPageNum().toString());
        int pageSize = ParseUtil.tryParseInt(gcjlTXcbb.getPageSize().toString(), 1);
        Page<GcjlTXcbb> page = new Page<>(pageNum,pageSize);
        QueryWrapper<GcjlTXcbb> queryWrapper = new QueryWrapper();

        //查询部门全路径
        List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, gcjlTXcbb.getDeptId()));
        if (CollectionUtils.isEmpty(departments)){
            return Result.error("该部门或许已不存在，请重新获取!");
        }

        String deptfullpath = departments.get(0).getDeptfullpath();

        queryWrapper.lambda().like(gcjlTXcbb.getDeptId() != null,GcjlTXcbb::getUserFullPath,deptfullpath);

        queryWrapper.lambda().like(StringUtils.isNotEmpty(gcjlTXcbb.getUserName()),GcjlTXcbb::getUserName,gcjlTXcbb.getUserName());
        if (gcjlTXcbb.getPlanStartDate()!=null || gcjlTXcbb.getPlanEndDate()!=null){
            queryWrapper.lambda()
                    .and(wp-> wp.ge(gcjlTXcbb.getPlanStartDate()!=null,GcjlTXcbb::getPlanStartDate,gcjlTXcbb.getPlanStartDate())
                            .le(gcjlTXcbb.getPlanStartDate()!=null,GcjlTXcbb::getPlanStartDate,gcjlTXcbb.getPlanEndDate())
                    );


            queryWrapper.lambda()
                    .or(wp->wp.ge(gcjlTXcbb.getPlanEndDate()!=null,GcjlTXcbb::getPlanEndDate,gcjlTXcbb.getPlanStartDate())
                            .le(gcjlTXcbb.getPlanEndDate()!=null,GcjlTXcbb::getPlanEndDate,gcjlTXcbb.getPlanEndDate())

                    );
        }


        queryWrapper.lambda().orderByDesc(GcjlTXcbb::getPlanStartDate);
        return Result.ok(this.baseMapper.selectPage(page,queryWrapper));
    }

    @Override
    public Result<Object> exportExcel(GcjlTXcbbDto gcjlTXcbb, HttpServletResponse response)  {
        QueryWrapper<GcjlTXcbb> queryWrapper = new QueryWrapper();

        //查询部门全路径
        List<GcjlTDepartment> departments = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().eq(GcjlTDepartment::getId, gcjlTXcbb.getDeptId()));
        if (CollectionUtils.isEmpty(departments)){
            return Result.error("该部门或许已不存在，请重新获取!");
        }

        String deptfullpath = departments.get(0).getDeptfullpath();

        queryWrapper.lambda().like(gcjlTXcbb.getDeptId() != null,GcjlTXcbb::getUserFullPath,deptfullpath);

        queryWrapper.lambda().like(StringUtils.isNotEmpty(gcjlTXcbb.getUserName()),GcjlTXcbb::getUserName,gcjlTXcbb.getUserName());
        if (gcjlTXcbb.getPlanStartDate()!=null || gcjlTXcbb.getPlanEndDate()!=null){
            queryWrapper.lambda()
                    .and(wp-> wp.ge(gcjlTXcbb.getPlanStartDate()!=null,GcjlTXcbb::getPlanStartDate,gcjlTXcbb.getPlanStartDate())
                            .le(gcjlTXcbb.getPlanStartDate()!=null,GcjlTXcbb::getPlanStartDate,gcjlTXcbb.getPlanEndDate())
                    );


            queryWrapper.lambda()
                    .or(wp->wp.ge(gcjlTXcbb.getPlanEndDate()!=null,GcjlTXcbb::getPlanEndDate,gcjlTXcbb.getPlanStartDate())
                            .le(gcjlTXcbb.getPlanEndDate()!=null,GcjlTXcbb::getPlanEndDate,gcjlTXcbb.getPlanEndDate())

                    );
        }
        queryWrapper.lambda().orderByDesc(GcjlTXcbb::getPlanStartDate);

        List<GcjlTXcbb> list = this.baseMapper.selectList(queryWrapper);
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        //转换时间
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setPlanStartDateStr(ymd.format(list.get(i).getPlanStartDate()));
            list.get(i).setPlanEndDateStr(ymd.format(list.get(i).getPlanEndDate()));
        }

        try {
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
            cmlist.add(new ToolHelper.ExportColumnMode("userName", "名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("deptName", "部门", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("planStartDateStr", "开始日期", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("planEndDateStr", "结束日期", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("note", "出行事由", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("address", "地址", 30));
            ToolHelper.ExportExcelList(list, "行程报备", cmlist,false,response );
        }catch (Exception e){
            return Result.error(e.getMessage());
        }
        return Result.ok();
    }
}
