package com.soft.gcc.xtbg.xcgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcbbDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTXcbb;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IGcjlTXcbbService extends IService<GcjlTXcbb> {

    Result<Object> selectPageList(GcjlTXcbbDto gcjlTXcbb);

    Result<Object> exportExcel(GcjlTXcbbDto gcjlTXcbb, HttpServletResponse response);

}
