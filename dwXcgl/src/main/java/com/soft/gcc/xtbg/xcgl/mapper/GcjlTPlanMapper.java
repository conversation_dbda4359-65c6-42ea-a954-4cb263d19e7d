package com.soft.gcc.xtbg.xcgl.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcdkzphzDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_Plan(工程监理--计划行程子表)】的数据库操作Mapper
* @createDate 2023-07-12 10:59:27
* @Entity com.soft.gcc.xtbg.xcgl.entity.GcjlTPlan
*/
public interface GcjlTPlanMapper extends BaseMapper<GcjlTPlan> {

    List<GcjlPlanDto> getXchzList(@Param("startDate") Date startDate,@Param("endDate") Date realEndDate,@Param("deptfullpath") String deptfullpath);

    List<GcjlPlanDto> getXchzListByLcId(@Param("lcId")Integer lcId);


    IPage<GcjlPlanDto> selectJoinPage(Page<GcjlTPlan> page, @Param("dto")GcjlTXcdkzphzDto dto);

    List<GcjlPlanDto> selectList(@Param("dto")GcjlTXcdkzphzDto dto);
}




