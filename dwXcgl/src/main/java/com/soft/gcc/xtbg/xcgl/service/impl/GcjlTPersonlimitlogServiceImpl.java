package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTPersonlimitlogService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTPersonlimitlogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_PersonLimitLog】的数据库操作Service实现
* @createDate 2023-07-12 10:59:16
*/
@Service
public class GcjlTPersonlimitlogServiceImpl extends ServiceImpl<GcjlTPersonlimitlogMapper, GcjlTPersonlimitlog>
    implements IGcjlTPersonlimitlogService{

    @Override
    public IPage<GcjlTPersonlimitlog> getPersonLimitLogBySrcPersonId(Page<GcjlTPersonlimitlog> page, Integer srcPersonId) {
        return this.baseMapper.getPersonLimitLog(page,srcPersonId);
    }
}




