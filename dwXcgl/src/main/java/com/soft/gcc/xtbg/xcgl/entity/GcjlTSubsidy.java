package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理--补贴费用表
* @TableName GCJL_T_Subsidy
*/
@TableName(value ="GCJL_T_Subsidy")
@Data
public class GcjlTSubsidy implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 对应用户id
    */
    @TableField(value = "SysUserId")
    @JSONField(name = "SysUserId")

    private Integer SysUserId;
    /**
    * 补贴总费用
    */
    @TableField(value = "AllCost")
    @JSONField(name = "AllCost")

    private BigDecimal AllCost;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 更新时间
    */
    @TableField(value = "UpdateTime")
    @JSONField(name = "UpdateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date UpdateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
