package com.soft.gcc.xtbg.xcgl.service;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcjhDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_LC(工程监理--计划主表-流程表)】的数据库操作Service
* @createDate 2023-07-12 10:55:55
*/
public interface IGcjlTLcService extends IService<GcjlTLc> {

    Result<Object> selectPageList(GcjlTXcjhDto xcjhDto);

    void exportExcel(GcjlTXcjhDto xcjhDto , HttpServletResponse response);
    void exportExcelChildPlan(GcjlTXcjhDto xcjhDto , HttpServletResponse response);
}
