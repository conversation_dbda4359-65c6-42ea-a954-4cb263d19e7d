package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理--计划主表-流程表
* @TableName GCJL_T_LC
*/
@TableName(value ="GCJL_T_LC")
@Data
public class GcjlTLc implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 业务编号
    */
    @TableField(value = "BizNo")
    @JSONField(name = "BizNo")

    private String BizNo;
    /**
    * 申请用户id
    */
    @TableField(value = "UserId")
    @JSONField(name = "UserId")

    private Integer UserId;
    /**
    * 申请人名称
    */
    @TableField(value = "UserName")
    @JSONField(name = "UserName")

    private String UserName;
    /**
    * 申请人全路径，宁波yy\yinzyongn\yinzjlz\张三
    */
    @TableField(value = "UserFullPath")
    @JSONField(name = "UserFullPath")

    private String UserFullPath;
    /**
    * 计划时间
    */
    @TableField(value = "PlanTime")
    @JSONField(name = "PlanTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date PlanTime;
    /**
    * 备注
    */
    @TableField(value = "Note")
    @JSONField(name = "Note")

    private String Note;
    /**
    * 申请时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 计划里程
    */
    @TableField(value = "PlanMileage")
    @JSONField(name = "PlanMileage")

    private BigDecimal PlanMileage;
    /**
    * 计划费用
    */
    @TableField(value = "PlanCost")
    @JSONField(name = "PlanCost")

    private BigDecimal PlanCost;
    /**
    * 执行里程
    */
    @TableField(value = "PerformMileage")
    @JSONField(name = "PerformMileage")

    private BigDecimal PerformMileage;
    /**
    * 执行费用
    */
    @TableField(value = "PerformCost")
    @JSONField(name = "PerformCost")

    private BigDecimal PerformCost;
    /**
    * 业务类型，备用
    */
    @TableField(value = "BizType")
    @JSONField(name = "BizType")

    private String BizType;
    /**
    * 审核状态（0 未提交、1 审核中、2 已驳回、3 已审核、4已过期  5、已作废）
    */
    @TableField(value = "ApprovingState")
    @JSONField(name = "ApprovingState")

    private Integer ApprovingState;
    /**
    * 审批负责人
    */
    @TableField(value = "ApproveUserId")
    @JSONField(name = "ApproveUserId")

    private Integer ApproveUserId;
    /**
    * 审批意见
    */
    @TableField(value = "ApproveNote")
    @JSONField(name = "ApproveNote")

    private String ApproveNote;
    /**
    * 审批意见确认时间
    */
    @TableField(value = "ApproveTime")
    @JSONField(name = "ApproveTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ApproveTime;
    /**
    * 审批负责人部门ID
    */
    @TableField(value = "ApproveDeptId")
    @JSONField(name = "ApproveDeptId")

    private Integer ApproveDeptId;
    /**
    * 申请用户组织ID，备用
    */
    @TableField(value = "TopDeptId")
    @JSONField(name = "TopDeptId")

    private Integer TopDeptId;
    /**
    * 申请用户部门ID
    */
    @TableField(value = "DeptId")
    @JSONField(name = "DeptId")

    private Integer DeptId;
    /**
    * 申请用户部门名称
    */
    @TableField(value = "DeptName")
    @JSONField(name = "DeptName")

    private String DeptName;
    /**
    * 申请用户手机号
    */
    @TableField(value = "Phone")
    @JSONField(name = "Phone")

    private String Phone;
    /**
    * 执行状态（0 未执行、1 执行中、2 已执行、3 已取消）
    */
    @TableField(value = "PerformState")
    @JSONField(name = "PerformState")

    private Integer PerformState;

    private Integer NotarizeState;
    private Integer ApplyPostponeDays;
    private Integer ApprovePostponeDays;
    @TableField(value = "SystemCancellationTime")
    @JSONField(name = "SystemCancellationTime")
    private Date SystemCancellationTime;

    @TableField(exist = false)
    private String approveUserName;

    @TableField(exist = false)
    private String approvingStateDescription;
    @TableField(exist = false)
    private String notarizeStateDescription;
    @TableField(exist = false)
    private String performStateDescription;
    @TableField(exist = false)
    private String planTimeStr;




    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
