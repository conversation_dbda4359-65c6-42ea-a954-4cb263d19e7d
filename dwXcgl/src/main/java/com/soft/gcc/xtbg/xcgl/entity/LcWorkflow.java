package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName Lc_workFlow
*/
@TableName(value ="Lc_workFlow")
@Data
public class LcWorkflow implements Serializable {


    /**
    *
    */
    @TableField(value = "ID")
    @JSONField(name = "id")
    private Integer id;
    /**
    *
    */
    @TableField(value = "lc_defineID")
    @JSONField(name = "lcDefineId")
    private Integer lcDefineId;
    /**
    *
    */
    @TableField(value = "ywID")
    @JSONField(name = "ywId")
    private Integer ywId;
    /**
    *
    */
    @TableField(value = "lc_jdID")
    @JSONField(name = "lcJdId")
    private Integer lcJdId;
    /**
    *
    */
    @TableField(value = "lc_jdmc")
    @JSONField(name = "lcJdmc")
    private String lcJdmc;
    /**
    *
    */
    @TableField(value = "groupID")
    @JSONField(name = "groupId")
    private Integer groupId;
    /**
    *
    */
    @TableField(value = "groupName")
    @JSONField(name = "groupName")
    private String groupName;
    /**
    *
    */
    @TableField(value = "personZgh")
    @JSONField(name = "personZgh")
    private String personZgh;
    /**
    *
    */
    @TableField(value = "personName")
    @JSONField(name = "personName")
    private String personName;
    /**
    *
    */
    @TableField(value = "transdate")
    @JSONField(name = "transdate")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transdate;
    /**
    *
    */
    @TableField(value = "feed")
    @JSONField(name = "feed")
    private String feed;
    /**
    *
    */
    @TableField(value = "number")
    @JSONField(name = "number")
    private Integer number;
    /**
    *
    */
    @TableField(value = "BXType")
    @JSONField(name = "bXType")
    private String bXType;
    /**
    *
    */
    @TableField(value = "PNO")
    @JSONField(name = "pNO")
    private String pNO;
    /**
    *
    */
    @TableField(value = "startdate")
    @JSONField(name = "startdate")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startdate;
    /**
    *
    */
    @TableField(value = "LcByRole")
    @JSONField(name = "lcByRole")
    private Integer lcByRole;
    /**
    *
    */
    @TableField(value = "isback")
    @JSONField(name = "isback")
    private Integer isback;
    /**
    *
    */
    @TableField(value = "useback")
    @JSONField(name = "useback")
    private Integer useback;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
