package com.soft.gcc.xtbg.xcgl.Controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Joiner;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.DeptCascader;
import com.soft.gcc.xtbg.xcgl.dto.TjcxDto;
import com.soft.gcc.xtbg.xcgl.dto.TreeMenu;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.xtbg.xcgl.vo.GcjlTPlanVo;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.awt.*;
import java.math.BigDecimal;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.logging.SimpleFormatter;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import javax.tools.Tool;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.stream.Collectors;

import static com.soft.framework.helper.ToolHelper.SetWaterMark;

@RequestMapping(value = "/tjcx")
@RestController
@Component
public class XcglTjcxController  extends BaseController {

    @Autowired
    GcjlTDepartmentService gcjlTDepartmentService;
    @Autowired
    XcglDeptController xcglDeptController;




    @RequestMapping(value = "/getStatisticsInfo")
    @PreAuthorize("@ss.hasRole('协同办公-行程管理-统计查询')")
    public Result<Object> getStatisticsInfo(@RequestBody HashMap<String,Object> map) throws ParseException {
        TjcxPageData tjcxPageData = getTjcxPageData(map,false);
        return Result.ok(tjcxPageData.data, tjcxPageData.size +","+ tjcxPageData.pageCount);
    }

    @NotNull
    private TjcxPageData getTjcxPageData(HashMap<String, Object> map,Boolean exportData) {
        Integer type = (Integer) map.get("statisticsType");
        Integer deptId=null;
        String deptname=null;
        Integer pageSize =  (Integer) map.get("pageSize");
        Integer currentPage =  (Integer) map.get("currentPage");
        Result<Object> result = xcglDeptController.getDeptList();
        TreeMenu treeMenu = (TreeMenu)result.getResult();
        Set<Integer> childrenIdsSet = treeMenu.getChildrenIdsSet(treeMenu);
        if(map.get("deptId")!=null){
            deptId = (Integer)  map.get("deptId");
            GcjlTDepartment dept = gcjlTDepartmentService.getById(deptId);
            deptname= dept.getDeptname();
        }
        JSONArray searchTime=null;
        String startTime=null;
        String endTime =null;
        if(map.get("searchTime")!=null &&  !map.get("searchTime").equals("")){
             searchTime = (JSONArray) map.get("searchTime");
             startTime = searchTime.get(0).toString()+" 00:00:00";
             endTime = searchTime.get(1).toString()+" 23:59:59";
        }
        String sql = "";
        BigDecimal PerformMileageCount=BigDecimal.valueOf(0);
        BigDecimal PerformCostCount=BigDecimal.valueOf(0);
        int size=0;
        List<TjcxDto> tjcxDtoList=null;
        Temp temp=null;
        switch (type)
        {
            case 1:
                tjcxDtoList = gcjlTDepartmentService.getTjcxDtoDept(deptname, startTime,endTime,childrenIdsSet);
                temp= getCountAndAverage(PerformMileageCount, PerformCostCount, size, tjcxDtoList);
                PerformMileageCount=temp.getPerformMileageCount();
                PerformCostCount=temp.getPerformCostCount();
                break;
            case 2:
                tjcxDtoList = gcjlTDepartmentService.getTjcxDtoPerson(deptname, startTime, endTime,childrenIdsSet);
                temp= getCountAndAverage(PerformMileageCount, PerformCostCount, size, tjcxDtoList);
                PerformMileageCount=temp.getPerformMileageCount();
                PerformCostCount=temp.getPerformCostCount();
                break;
            case 3:
                tjcxDtoList = gcjlTDepartmentService.getTjcxDtoAlarm(deptname,startTime,endTime,childrenIdsSet);
                break;
        }
        if(tjcxDtoList.size()==0){
          return  new TjcxPageData(0,0,null);
        }
        size=tjcxDtoList.size();
        int pageCount = (int) Math.ceil((double)  size / pageSize);//总页数
        int startIndex=currentPage*pageSize-pageSize;
        int endIndex=currentPage*pageSize;
        if(endIndex> size){
            endIndex= size;
        }
        List<TjcxDto> data=new ArrayList<>();
        if(exportData==false){
           data = tjcxDtoList.subList(startIndex, endIndex);
        }else if(exportData==true){
          data = tjcxDtoList;
        }
        if(type==1 || type==2 || temp!=null){
            data.add(new TjcxDto("合计", PerformMileageCount.toString(), PerformCostCount.toString()));
            data.add(new TjcxDto("平均值", PerformMileageCount.divide(BigDecimal.valueOf(size),2).toString()
                    , PerformCostCount.divide(BigDecimal.valueOf(size),2).toString()
            ));
        }
        TjcxPageData tjcxPageData = new TjcxPageData(size, pageCount, data);
        return tjcxPageData;
    }

    private static class TjcxPageData {
        public final int size;
        public final int pageCount;
        public final List<TjcxDto> data;

        public TjcxPageData(int size, int pageCount, List<TjcxDto> data) {
            this.size = size;
            this.pageCount = pageCount;
            this.data = data;
        }
    }

    private Temp getCountAndAverage(BigDecimal PerformMileageCount, BigDecimal PerformCostCount, int size, List<TjcxDto> tjcxDtoList) {
        if (tjcxDtoList !=null && tjcxDtoList.size()>0){
           size = tjcxDtoList.size();
           for(TjcxDto tjcxDto: tjcxDtoList){
               PerformMileageCount = PerformMileageCount.add(tjcxDto.getPerformMileage());
               PerformCostCount = PerformCostCount.add(tjcxDto.getPerformCost());
           }
        }
        return new Temp(size, PerformMileageCount, PerformCostCount);
    }


    @RequestMapping(value = "/export")
    @PreAuthorize("@ss.hasRole('协同办公-行程管理-统计查询')")
    public Result<Object> export(@RequestBody HashMap<String,Object> map,HttpServletResponse response){
        TjcxPageData tjcxPageData = getTjcxPageData(map,true);
//        String desktopPath = System.getProperty("user.home") + "\\Desktop\\";
        String fileNames = getFileNames(map);
//        String excelFilePath = desktopPath + fileNames+".xlsx";

        // 假设你已经有一个名为JLList的List对象，这里只是一个示例
        List<TjcxDto> JLList = tjcxPageData.data;
        if(JLList!=null && JLList.size()>0){

        }else{
            return Result.error("无可查询数据");
        }
//        try (Workbook workbook = new XSSFWorkbook()) {
//            Sheet sheet = workbook.createSheet("Sheet1");
//            CellStyle tcs = null;
//            CellStyle ccs=null;
//            //添加水印
//            if (ConfigHelper.isEPWMask()) {
//                ToolHelper.SetWaterMark((XSSFWorkbook) sheet.getWorkbook());
//                 tcs = ToolHelper.createCellStyleXSSF(workbook, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true, 10, true);
//                 ccs = ToolHelper.createCellStyleXSSF(workbook, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);
//            }
//            // 设置列宽
//            sheet.setColumnWidth(0, 20 * 256); // 设置第1列（索引为0）的宽度为20个字符的宽度
//            sheet.setColumnWidth(1, 20 * 256); // 设置第2列（索引为1）的宽度为30个字符的宽度
//            sheet.setColumnWidth(2, 20 * 256); // 设置第3列（索引为2）的宽度为15个字符的宽度
//            sheet.setColumnWidth(3, 20 * 256); // 设置第3列（索引为2）的宽度为15个字符的宽度
//            sheet.setColumnWidth(4, 40 * 256); // 设置第3列（索引为2）的宽度为15个字符的宽度
//
//            // 创建第一行并设置字段名
//            Row headerRow = sheet.createRow(0);
//            String[] fieldNames = getFieldNames(map);
//            for (int i = 0; i < fieldNames.length; i++) {
//                Cell cell = headerRow.createCell(i);
//                cell.setCellValue(fieldNames[i]);
//                cell.setCellStyle(tcs);
//            }
//
//            // 将TjcxDto对象列表中的数据添加到Excel
//            int rowIndex = 1;
//            for (TjcxDto dto : JLList) {
//                Row dataRow = sheet.createRow(rowIndex++);
//                setCellValues(dto, dataRow,map);
//            }
//
//            // 保存Excel文件
//            try (FileOutputStream fileOut = new FileOutputStream(excelFilePath)) {
//                workbook.write(fileOut);
//                System.out.println("Excel文件成功导出到桌面：" + excelFilePath);
//            } catch (IOException e) {
//                System.err.println("保存Excel文件时出现错误：" + e.getMessage());
//            }
//        } catch (IOException e) {
//            System.err.println("创建Excel文件时出现错误：" + e.getMessage());
//        }
//
//        // 打开Excel文件
//        try {
//            File excelFile = new File(excelFilePath);
//            if (excelFile.exists()) {
//                ProcessBuilder processBuilder = new ProcessBuilder("cmd.exe", "/c", excelFilePath);
//                processBuilder.start();
//            }
//        } catch (IOException e) {
//            System.err.println("打开Excel文件时出现错误：" + e.getMessage());
//        }
        Integer type = (Integer) map.get("statisticsType");
        List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
        if(type==1){
            cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "监理站", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("PerformMileage", "监理公里数(km)", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("PerformCost", "监理补贴费用(人民币)", 10));
        }else if(type==2){
            cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "监理站", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("UserName", "姓名", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("PerformMileage", "监理公里数(km)", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("PerformCost", "监理补贴费用(人民币)", 10));
        }else if(type==3){
            cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "监理站", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("UsersName", "姓名", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("AlarmTime", "告警时间", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("AlarmTypeString", "告警类型", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("AlarmNote", "告警内容", 10));
        }
        ToolHelper.ExportExcelList(JLList, fileNames, cmlist,false,response );
        return Result.ok();
    }


//    @RequestMapping(value = "/export")
//    public void export(@RequestBody HashMap<String,Object> map, HttpServletResponse response){
//        TjcxPageData tjcxPageData = getTjcxPageData(map);
//        String fileNames = getFileNames(map);
//        List<ToolHelper.ExportColumnMode> cmlist = getHeadRow(map);
//        ToolHelper.ExportExcelList(tjcxPageData.data, fileNames, cmlist,false,response );
//
//    }

    @NotNull
    private static List<ToolHelper.ExportColumnMode> getHeadRow(HashMap<String,Object> map) {
        List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
        Integer type = (Integer) map.get("statisticsType");
        switch (type) {
            case 1 :
            cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "监理站", 10));
            cmlist.add(new ToolHelper.ExportColumnMode("PerformMileage", "监理公里数(km)", 15));
            cmlist.add(new ToolHelper.ExportColumnMode("PerformCost", "监理补贴费用(人民币)", 10));
            break;
            case 2 :
                cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "监理站", 10));
                cmlist.add(new ToolHelper.ExportColumnMode("UserName", "姓名", 10));
                cmlist.add(new ToolHelper.ExportColumnMode("PerformMileage", "监理公里数(km)", 15));
                cmlist.add(new ToolHelper.ExportColumnMode("PerformCost", "监理补贴费用(人民币)", 10));
                break;
            case 3 :
                cmlist.add(new ToolHelper.ExportColumnMode("DeptName", "监理站", 10));
                cmlist.add(new ToolHelper.ExportColumnMode("UsersName", "姓名", 10));
                cmlist.add(new ToolHelper.ExportColumnMode("AlarmTime", "告警时间", 15));
                cmlist.add(new ToolHelper.ExportColumnMode("AlarmTypeString", "告警类型", 10));
                cmlist.add(new ToolHelper.ExportColumnMode("AlarmNote", "告警内容", 10));
                break;
        }
        return cmlist;
    }

    private static void setCellValues(TjcxDto dto, Row dataRow,HashMap<String,Object> map) {
        Integer type = (Integer) map.get("statisticsType");
        switch (type){
            case 1 :
                dataRow.createCell(0).setCellValue(dto.getDeptName());
                dataRow.createCell(1).setCellValue(dto.getPerformMileage().toString());
                dataRow.createCell(2).setCellValue(dto.getPerformCost().toString());
                break;
            case 2 :
                dataRow.createCell(0).setCellValue(dto.getDeptName());
                dataRow.createCell(1).setCellValue(dto.getUserName());
                dataRow.createCell(2).setCellValue(dto.getPerformMileage().toString());
                dataRow.createCell(3).setCellValue(dto.getPerformCost().toString());
                break;
            case 3 :
                dataRow.createCell(0).setCellValue(dto.getDeptName());
                dataRow.createCell(1).setCellValue(dto.getUsersName());
                dataRow.createCell(2).setCellValue(dto.getAlarmTime());
                dataRow.createCell(3).setCellValue(dto.getAlarmTypeString());
                dataRow.createCell(4).setCellValue(dto.getAlarmNote());
                break;
        }
    }

    public String[] getFieldNames(HashMap<String,Object> map){
        Integer type = (Integer) map.get("statisticsType");
        String[] fieldNames=null;
        switch (type){
            case 1 :  fieldNames = new String[]{"监理站", "监理公里数(km)", "监理补贴费用(人民币)"};   break;
            case 2 :  fieldNames = new String[]{"监理站","姓名", "监理公里数(km)", "监理补贴费用(人民币)"};   break;
            case 3 :  fieldNames = new String[]{"监理站","姓名", "告警时间", "告警类型","告警内容"};   break;
        }
        return fieldNames;
    }



    public String getFileNames(HashMap<String,Object> map){
        Integer type = (Integer) map.get("statisticsType");
        String fileNames=null;
        Date date=new Date();
        SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
        String dateStr=sdf.format(date);
        switch (type){
            case 1 :  fileNames =  "行程管理-统计查询-监理站"+dateStr;   break;
            case 2 :  fileNames =  "行程管理-统计查询-人员"+dateStr;   break;
            case 3 :  fileNames =  "行程管理-统计查询-告警"+dateStr;  break;
        }
        return fileNames;
    }



    @Data
    class Temp{
       int size;
       BigDecimal PerformMileageCount;
       BigDecimal PerformCostCount;

        public Temp(int size, BigDecimal performMileageCount, BigDecimal performCostCount) {
            this.size = size;
            PerformMileageCount = performMileageCount;
            PerformCostCount = performCostCount;
        }
    }
}
