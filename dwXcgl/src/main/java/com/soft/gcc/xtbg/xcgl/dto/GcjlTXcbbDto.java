package com.soft.gcc.xtbg.xcgl.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
@Data
public class GcjlTXcbbDto extends PageBaseEntity {
    private Integer deptId;

    private String userName;
    /**
     * 行程开始日期
     */
    @JSONField(name = "planStartDate")
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planStartDate;

    /**
     * 行程结束日期
     */
    @JSONField(name = "planEndDate")
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;


}
