package com.soft.gcc.xtbg.xcgl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.xcgl.dto.TjcxDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
*
*/
public interface GcjlTDepartmentService extends IService<GcjlTDepartment> {

    GcjlTDepartment getDepartmentById(Integer id);
    List<TjcxDto> getTjcxDtoDept(String deptName,
                                 String startTime , String endTime,  Set<Integer> childrenIdsSet);
    List<TjcxDto> getTjcxDtoPerson(String deptName,
                                   String startTime , String endTime,  Set<Integer> childrenIdsSet);
    List<TjcxDto> getTjcxDtoAlarm(String deptName,
                                   String startTime , String endTime,  Set<Integer> childrenIdsSet);
}
