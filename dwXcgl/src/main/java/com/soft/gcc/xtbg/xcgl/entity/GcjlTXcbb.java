package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 行程管理-行程报备
 */
@Data
@TableName("GCJL_T_Xcbb")
public class GcjlTXcbb implements Serializable {
    /**
     * 主键：
     */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;


    /**
     * 业务编号
     */
    @JSONField(name = "bizNo")
    private String bizNo;

    /**
     * 申请用户id
     */
    @JSONField(name = "userId")
    private Integer userId;

    /**
     * 申请人名称
     */
    @JSONField(name = "userName")
    private String userName;

    /**
     * 申请人全路径，宁波yy\yinzyongn\yinzjlz\张三
     */
    @JSONField(name = "userFullPath")
    private String userFullPath;


    /**
     * 申请用户部门ID
     */
    @JSONField(name = "deptId")
    private Integer deptId;

    /**
     * 申请用户部门名称
     */
    @JSONField(name = "deptName")
    private String deptName;

    /**
     * 申请用户组织ID，备用
     */
    @JSONField(name = "topDeptId")
    private Integer topDeptId;

    /**
     * 行程开始日期
     */
    @JSONField(name = "planStartDate",format = "yyyy-MM-dd")
//    @JsonFormat( pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planStartDate;

    /**
     * 行程结束日期
     */
    @JSONField(name = "planEndDate",format = "yyyy-MM-dd")
//    @JsonFormat( pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;

    /**
     *  出行事由
     */
    @JSONField(name = "note")
    private String note;

    /**
     * 申请时间
     */
    @JSONField(name = "createTime")
    private Date createTime;

    /**
     * 地址
     */
    @JSONField(name = "address")
    private String address;

    @TableField(exist = false)
    public String planStartDateStr;
    @TableField(exist = false)
    public String planEndDateStr;


}
