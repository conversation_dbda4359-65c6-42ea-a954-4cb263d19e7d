package com.soft.gcc.xtbg.xcgl.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.xcgl.dto.TjcxDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
* @Entity generator.domain.GcjlTDepartment
*/
public interface GcjlTDepartmentMapper extends BaseMapper<GcjlTDepartment> {


    GcjlTDepartment getDepartmentById(@Param("id") Integer id);

    List<TjcxDto> getTjcxDtoDept(@Param("deptName")String deptName,
                       @Param("startTime") String startTime ,
                       @Param("endTime")String endTime,
                       @Param("ids") Set<Integer> ids);


    List<TjcxDto> getTjcxDtoPerson(@Param("deptName")String deptName,
                                   @Param("startTime") String startTime ,
                                   @Param("endTime")String endTime,
                                   @Param("ids")   Set<Integer> ids);

    List<TjcxDto> getTjcxDtoAlarm(@Param("deptName")String deptName,
                                   @Param("startTime") String startTime ,
                                   @Param("endTime")String endTime,
                                  @Param("ids")   Set<Integer> ids);
}
