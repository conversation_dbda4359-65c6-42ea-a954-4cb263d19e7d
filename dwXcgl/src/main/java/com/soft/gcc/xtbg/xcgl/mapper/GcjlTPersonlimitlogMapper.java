package com.soft.gcc.xtbg.xcgl.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPerson;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_PersonLimitLog】的数据库操作Mapper
* @createDate 2023-07-12 10:59:16
* @Entity com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog
*/
public interface GcjlTPersonlimitlogMapper extends BaseMapper<GcjlTPersonlimitlog> {


    /**
     * 根据工程监理人员表Id 获取人员补贴费用上限
     */
    IPage<GcjlTPersonlimitlog> getPersonLimitLog(Page<GcjlTPersonlimitlog> page, @Param("srcPersonId") Integer srcPersonId);

}




