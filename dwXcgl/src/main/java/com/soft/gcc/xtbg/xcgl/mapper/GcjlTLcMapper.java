package com.soft.gcc.xtbg.xcgl.mapper;

import com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcjhDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_LC(工程监理--计划主表-流程表)】的数据库操作Mapper
* @createDate 2023-07-12 10:55:55
* @Entity com.soft.gcc.xtbg.xcgl.entity.GcjlTLc
*/
public interface GcjlTLcMapper extends BaseMapper<GcjlTLc> {


    List<GcjlPlanDto> selectAllList(@Param("dto") GcjlTXcjhDto gcjlTLc);

}




