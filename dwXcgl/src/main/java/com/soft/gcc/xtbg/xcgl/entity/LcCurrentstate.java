package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName Lc_currentState
*/
@TableName(value ="Lc_currentState")
@Data
public class LcCurrentstate implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    *
    */
    @TableField(value = "Lc_defineID")
    @JSONField(name = "LcDefineID")
    private Integer LcDefineID;
    /**
    *
    */
    @TableField(value = "Lc_Name")
    @JSONField(name = "LcName")

    private String LcName;
    /**
    *
    */
    @TableField(value = "ywID")
    @JSONField(name = "ywId")
    private Integer ywId;
    /**
    *
    */
    @TableField(value = "sendPerson")
    @JSONField(name = "sendPerson")
    private String sendPerson;
    /**
    *
    */
    @TableField(value = "sendPersonZgh")
    @JSONField(name = "sendPersonZgh")
    private String sendPersonZgh;
    /**
    *
    */
    @TableField(value = "AllPersonZgh")
    @JSONField(name = "allPersonZgh")
    private String allPersonZgh;
    /**
    *
    */
    @TableField(value = "isMany")
    @JSONField(name = "isMany")

    private Integer isMany;
    /**
    *
    */
    @TableField(value = "lc_jdmc")
    @JSONField(name = "lcJdmc")
    private String lcJdmc;
    /**
    *
    */
    @TableField(value = "lc_jdid")
    @JSONField(name = "lcJdid")

    private Integer lcJdid;
    /**
    *
    */
    @TableField(value = "lc_isback")
    @JSONField(name = "lcIsback")

    private Integer lcIsback;
    /**
    *
    */
    @TableField(value = "lc_tojdid")
    @JSONField(name = "lcTojdid")

    private String lcTojdid;
    /**
    *
    */
    @TableField(value = "isOtherAdd")
    @JSONField(name = "isOtherAdd")
    private Integer isOtherAdd;
    /**
    *
    */
    @TableField(value = "number")
    @JSONField(name = "number")
    private Integer number;
    /**
    *
    */
    @TableField(value = "BXType")
    @JSONField(name = "bXType")
    private String bXType;
    /**
    *
    */
    @TableField(value = "PNO")
    @JSONField(name = "pNO")
    private String pNO;
    /**
    *
    */
    @TableField(value = "sendGroupIDs")
    @JSONField(name = "sendGroupIds")
    private String sendGroupIds;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
