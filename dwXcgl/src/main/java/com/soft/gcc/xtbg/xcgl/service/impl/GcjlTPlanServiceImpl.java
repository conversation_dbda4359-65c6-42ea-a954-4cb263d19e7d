package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.AliyunOSSUtils;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.base.util.WbAliyunOSSUtils;
import com.soft.gcc.xtbg.xcgl.dto.GcjlPlanDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcdkzphzDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPlan;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTXcbb;
import com.soft.gcc.xtbg.xcgl.service.GcjlTDepartmentService;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTLcService;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTPlanService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTPlanMapper;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTXcbbService;
import com.soft.gcc.xtbg.xcgl.vo.GcjlTPlanVo;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【GCJL_T_Plan(工程监理--计划行程子表)】的数据库操作Service实现
 * @createDate 2023-07-12 10:59:27
 */
@Service
public class GcjlTPlanServiceImpl extends ServiceImpl<GcjlTPlanMapper, GcjlTPlan>
        implements IGcjlTPlanService {
    @Autowired
    private IGcjlTLcService gcjlTLcService;
    @Autowired
    private GcjlTDepartmentService departmentService;
    @Autowired
    private IGcjlTXcbbService xcbbService;

    @Override
    public IPage<GcjlTPlanVo> getXchzShowList(String deptfullpath, Date startDate, Date endDate, Integer pageNum, Integer pageSize) {

        List<GcjlTPlanVo> xchzList = getXchzList(deptfullpath, startDate, endDate);
        IPage<GcjlTPlanVo> page = new Page<>();
        page.setTotal(xchzList.size());
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        if (pageNum * pageSize > xchzList.size()) {
            page.setRecords(xchzList.subList((pageNum - 1) * pageSize, xchzList.size()));
        } else {
            page.setRecords(xchzList.subList((pageNum - 1) * pageSize, pageNum * pageSize));
        }
        return page;
    }

    @Override
    public List<GcjlPlanDto> getInfo(Integer lcId, PersonEntity user) {
        List<GcjlPlanDto> list = baseMapper.getXchzListByLcId(lcId);
        //处理图片
//        for (int i = 0; i < list.size(); i++) {
//
//            if (list.get(i).getClockImgId() != null){
//                String base64 = "data:image/png;base64,";
//                 base64 += Base64.encodeBase64String(AliyunOSSUtils.downloadFileStream(list.get(i).getFilePath()));
//                System.out.println(base64);
//                list.get(i).setClockImgFileUrl(base64);
//            }
//        }
        if (list == null || list.size() == 0) {
            return null;
        }

        list.forEach(p -> {
            //合并经纬度
            p.setLongitudeLatitude(p.getLongitude() + "," + p.getLatitude());
            //获取图片地址
            if (p.getClockImgId() != null) {
                String base64 = "data:image/png;base64,";
                //20250408 新增需求，有国网建设分公司管理角色读取另外一个oss数据上的打卡照片
                if(user.getRoleList().contains("协同办公-行程管理-国网建设分公司管理")){
                    base64 += Base64.encodeBase64String(WbAliyunOSSUtils.downloadFileStream(p.getFilePath()));
                }else {
                    base64 += Base64.encodeBase64String(AliyunOSSUtils.downloadFileStream(p.getFilePath()));
                }
               // base64 += Base64.encodeBase64String(AliyunOSSUtils.downloadFileStream(p.getFilePath()));
                p.setClockImgFileUrl(base64);
            }
        });

        return list;
    }


    public static void main(String[] args) {
        List<Integer> list =null;
        list.forEach(p->{
            System.out.println("执行； ");
        });
    }

    @Override
    public void exportExcel(String deptfullpath, Date startDate, Date endDate, HttpServletResponse response) {
        List<GcjlTPlanVo> xchzList = getXchzList(deptfullpath, startDate, endDate);
        List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<>();
        cmlist.add(new ToolHelper.ExportColumnMode("lcId", "内部编号", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("deptName", "部门", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("userName", "姓名", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("tripDate", "出行日期", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("note", "出行事由", 15));
        cmlist.add(new ToolHelper.ExportColumnMode("performMileage", "里程", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("performCost", "费用", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("startSite", "起点", 30));
        cmlist.add(new ToolHelper.ExportColumnMode("startTime", "起点时间", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("endSite", "终点", 30));
        cmlist.add(new ToolHelper.ExportColumnMode("endTime", "终点时间", 10));
        cmlist.add(new ToolHelper.ExportColumnMode("middleSite", "途径站", 40));
        cmlist.add(new ToolHelper.ExportColumnMode("arrivedLocationName", "实际到达地gps定位地名称", 40));
        cmlist.add(new ToolHelper.ExportColumnMode("approveUserName", "审批人", 40));
        ToolHelper.ExportExcelList(xchzList, "行程汇总", cmlist, true, response);
    }

    /**
     * 获取行程汇总总数据的方法
     *
     * @param deptfullpath
     * @param startDate
     * @param endDate
     * @return
     */
    private List<GcjlTPlanVo> getXchzList(String deptfullpath, Date startDate, Date endDate) {
        List<GcjlTPlanVo> showList = new ArrayList<>();
        SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat hms = new SimpleDateFormat("HH:mm:ss");
        try {
            Date realEndDate = null;
            if (endDate != null) {
                //先转成localdate加一天 再转回date
                LocalDate realEndLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1);
                realEndDate = Date.from(realEndLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            }
            List<GcjlPlanDto> list = baseMapper.getXchzList(startDate, realEndDate, deptfullpath);
            List<GcjlTXcbb> xcbbList = xcbbService.list();
            Map<Integer, List<GcjlPlanDto>> planGroupMap = list.stream().collect(Collectors.groupingBy(GcjlPlanDto::getLcId));
            List<Integer> lcIds = new ArrayList<>();
            List<Integer> departmentIds = new ArrayList<>();
            planGroupMap.forEach((k, v) -> {
                lcIds.add(k);
            });

            //获取gcjltlc数据 为了不让in超出数量限制 在里面每1000条查一下
            List<GcjlTLc> gcjlTLcList = getGcjlLcList(lcIds);
            if (CollectionUtils.isNotEmpty(gcjlTLcList)) {
                gcjlTLcList.forEach(p -> departmentIds.add(p.getDeptId()));
            }

            //去重
            List<Integer> departmentIdsNew = new ArrayList<>(new HashSet<>(departmentIds));
            if(departmentIdsNew.size()==0){
                departmentIdsNew.add(-1);
            }
            List<GcjlTDepartment> departmentList = departmentService.list(new LambdaQueryWrapper<GcjlTDepartment>().in(GcjlTDepartment::getId, departmentIdsNew));

            planGroupMap.forEach((key, value) -> {
                GcjlTDepartment dept = departmentList.stream().filter(p -> p.getId().equals(key)).findFirst().orElse(null);
                StringBuilder middleSite = new StringBuilder();
                StringBuilder arrivedLocationNameStr = new StringBuilder();
                //把list再排序一次
                List<GcjlPlanDto> listGroup = value.stream().sorted(Comparator.comparing(GcjlPlanDto::getSort)).collect(Collectors.toList());
                GcjlTPlanVo planVo = new GcjlTPlanVo();
                planVo.setLcId(key);
                planVo.setDeptName(listGroup.get(0).getDeptName());
                planVo.setUserName(listGroup.get(0).getUserName());
                planVo.setNote(listGroup.get(0).getNote());
                planVo.setPlanTime(listGroup.get(0).getPlanTime());
                planVo.setPerformCost(String.valueOf(listGroup.get(0).getPerformCost()));
                planVo.setPerformMileage(String.valueOf(listGroup.get(0).getPerformMileage()));
                planVo.setApproveUserId(listGroup.get(0).getApproveUserId());
                planVo.setApproveUserName(listGroup.get(0).getApproveUserName());
                planVo.setTripDate(ymd.format(listGroup.get(0).getPlanTime()));
                //todo:赋值
                planVo.setNotarizeState(listGroup.get(0).getNotarizeState());
                planVo.setApprovingState(listGroup.get(0).getApprovingState());
                planVo.setApplyPostponeDays(listGroup.get(0).getApplyPostponeDays());
                planVo.setApprovePostponeDays(listGroup.get(0).getApprovePostponeDays());

                if (listGroup.get(0).getArrivedTime() != null) {
                  //  planVo.setTripDate(ymd.format(listGroup.get(0).getArrivedTime()));
                    planVo.setStartTime(hms.format(listGroup.get(0).getArrivedTime()));
                }
                planVo.setIsTab("0");
                planVo.setStartSite(listGroup.get(0).getPlanName());
                if (StringUtils.isNotEmpty(listGroup.get(0).getPlanName()) ) {
                    planVo.setStartSite(listGroup.get(0).getPlanName().replace("浙江省", ""));
                }
                planVo.setIsSpecialItinerary("0");
                //判断是否是特殊行程
                //取出当前用户id的所有特殊行程记录
                if (CollectionUtils.isNotEmpty(xcbbList)) {
                    if (StringUtils.isNotEmpty(planVo.getTripDate())) {
                        try {
                            Date trip = ymd.parse(planVo.getTripDate());
                            long count = xcbbList.stream()
                                    .filter(p -> p.getUserId().equals(listGroup.get(0).getUserId()) && p.getPlanStartDate().getTime() <= trip.getTime() && p.getPlanEndDate().getTime() >= trip.getTime())
                                    .count();
                            if (count > 0) {
                                planVo.setIsSpecialItinerary("1");
                            }
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }

                for (int i = 0; i < listGroup.size(); i++) {
                    if (StringUtils.isNotEmpty(listGroup.get(i).getArrivedLocationName())) {
                        if (listGroup.get(i).getArrivedLocationName().contains("浙江省宁波市")) {
                            arrivedLocationNameStr.append(listGroup.get(i).getArrivedLocationName().replace("浙江省宁波市", "")).append(";");
                        } else {
                            arrivedLocationNameStr.append(listGroup.get(i).getArrivedLocationName()).append(";");
                        }
                    }
                }
                if (arrivedLocationNameStr.length() > 0) {
                    planVo.setArrivedLocationName(arrivedLocationNameStr.substring(0, arrivedLocationNameStr.length() - 1));
                } else {
                    planVo.setArrivedLocationName(arrivedLocationNameStr.toString());
                }


                if (listGroup.size() > 1) {
                    if (listGroup.size() == 2) {
                        if (StringUtils.isNotEmpty(listGroup.get(1).getPlanName()) ) {
                            planVo.setEndSite(listGroup.get(1).getPlanName().replace("浙江省", ""));
                        }

                        if (listGroup.get(1).getArrivedTime() != null) {
                            planVo.setEndTime(hms.format(listGroup.get(1).getArrivedTime()));
                        }
                        getXchzIsTab(dept, listGroup, planVo);
                    } else {
//                        if (listGroup.get(listGroup.size() - 1).getPlanName().contains("浙江省")) {
                            listGroup.get(listGroup.size() - 1).setPlanName(listGroup.get(listGroup.size() - 1).getPlanName().replace("浙江省", ""));
//                        }
                        planVo.setEndSite(listGroup.get(listGroup.size() - 1).getPlanName());
                        if (listGroup.get(listGroup.size() - 1).getArrivedTime() != null) {
                            planVo.setEndTime(hms.format(listGroup.get(listGroup.size() - 1).getArrivedTime()));
                        }
                        for (int i = 1; i < listGroup.size() - 1; i++) {
                            GcjlPlanDto plan = listGroup.get(i);
                            //if (plan.getPlanName().contains("浙江省")) {
                                listGroup.get(i).setPlanName(plan.getPlanName().replace("浙江省", ""));
                          //  }

                            if (plan.getArrivedTime() != null) {
                                middleSite.append(plan.getPlanName()).append("(").append(hms.format(plan.getArrivedTime())).append(");");
                            }
                            getXchzIsTab(dept, listGroup, planVo);
                        }
                        if (middleSite.length() > 0) {
                            planVo.setMiddleSite(middleSite.substring(0, middleSite.length() - 1));
                        }

                    }
                }
                showList.add(planVo);
            });
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        List<GcjlTPlanVo> showListSort = showList.stream()
                .sorted(Comparator.comparing(GcjlTPlanVo::getPlanTime,Comparator.reverseOrder())
                        .thenComparing(GcjlTPlanVo::getStartTime,Comparator.reverseOrder()))
                 .collect(Collectors.toList());
        return showListSort;
    }

    private void getXchzIsTab(GcjlTDepartment dept, List<GcjlPlanDto> listGroup, GcjlTPlanVo planVo) {
        if (dept != null) {
            double startLon = (Math.PI / 180) * dept.getLongitude().doubleValue();
            double startLat = (Math.PI / 180) * dept.getLatitude().doubleValue();
            if (dept.getDeptfullpath().contains("主网监理部")) {
                double endLon = (Math.PI / 180) * listGroup.get(1).getLongitude().doubleValue();
                double endLat = (Math.PI / 180) * listGroup.get(1).getLatitude().doubleValue();
                double a = startLat - endLat;
                double b = startLon - endLon;
                double result = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(startLat) * Math.cos(endLat) * Math.pow(Math.sin(b / 2), 2))) * 6378137;
                if (result <= 1) {
                    //针对主网项目部人员，行程汇总界面中，行程管理中途径点包含监理公司的行程，在数据查询时进行标红，并导出  逻辑：途径点的gps距离监理公司1公里范围内的都算是监理公司。
                    planVo.setIsTab("1");
                }
            }
        }
    }

    /**
     *分批查询工程监理流程
     */
    private List<GcjlTLc> getGcjlLcList(List<Integer> ids){
        List<GcjlTLc> allList = new ArrayList<>();
        int batchCount = 1000;//每批查询的个数
        int batchLastIndex = batchCount - 1;// 每批最后一个的下标
        for (int index = 0; index < ids.size() - 1; ) {
            if (batchLastIndex > ids.size() - 1) {
                batchLastIndex = ids.size() - 1;
                List<GcjlTLc> list = gcjlTLcService.list(new LambdaQueryWrapper<GcjlTLc>().in(GcjlTLc::getId, ids.subList(index, batchLastIndex + 1)));
                allList.addAll(list);
                break;// 数据插入完毕,退出循环
            } else {
                List<GcjlTLc> list = gcjlTLcService.list(new LambdaQueryWrapper<GcjlTLc>().in(GcjlTLc::getId, ids.subList(index, batchLastIndex + 1)));
                allList.addAll(list);
                index = batchLastIndex + 1;// 设置下一批下标
                batchLastIndex = index + (batchCount - 1);
            }
        }
        return allList;
    }


    @Override
    public IPage<GcjlPlanDto> selectJoinPage(GcjlTXcdkzphzDto xcdkzphzDto, PersonEntity user){


        int pageNum = ParseUtil.tryParseInt(xcdkzphzDto.getPageNum().toString());
        int pageSize = ParseUtil.tryParseInt(xcdkzphzDto.getPageSize().toString(), 1);
        Page<GcjlTPlan> page = new Page<>(pageNum, pageSize);
        IPage<GcjlPlanDto> resPgae = this.baseMapper.selectJoinPage(page,xcdkzphzDto);
        resPgae.getRecords().forEach(e->{
            if (e.getClockImgId() != null) {
                String base64 = "data:image/png;base64,";
                //20250408 新增需求，有国网建设分公司管理角色读取另外一个oss数据上的打卡照片
                if(user.getRoleList().contains("协同办公-行程管理-国网建设分公司管理")){
                    base64 += Base64.encodeBase64String(WbAliyunOSSUtils.downloadFileStream(e.getFilePath()));
                }else {
                    base64 += Base64.encodeBase64String(AliyunOSSUtils.downloadFileStream(e.getFilePath()));
                }
                e.setClockImgFileUrl(base64);
            }
        });
        return resPgae;
    }

    @Override
    public List<GcjlPlanDto> selectList(GcjlTXcdkzphzDto xcdkzphzDto) {
        return this.baseMapper.selectList(xcdkzphzDto);
    }
}




