package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理--常用工程点表
* @TableName GCJL_T_ProjectPoint
*/
@TableName(value ="GCJL_T_ProjectPoint")
@Data
public class GcjlTProjectpoint implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 工程名称点
    */
    @TableField(value = "PointName")
    @JSONField(name = "PointName")

    private String PointName;
    /**
    * 所属组织Id
    */
    @TableField(value = "DeptId")
    @JSONField(name = "DeptId")

    private Integer DeptId;
    /**
    * 纬度
    */
    @TableField(value = "Latitude")
    @JSONField(name = "Latitude")

    private BigDecimal Latitude;
    /**
    * 经度
    */
    @TableField(value = "Longitude")
    @JSONField(name = "Longitude")

    private BigDecimal Longitude;
    /**
    * 状态(1启用 0 禁用)
    */
    @TableField(value = "State")
    @JSONField(name = "State")

    private Integer State;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 位置名称
    */
    @TableField(value = "LocationName")
    @JSONField(name = "LocationName")

    private String LocationName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
