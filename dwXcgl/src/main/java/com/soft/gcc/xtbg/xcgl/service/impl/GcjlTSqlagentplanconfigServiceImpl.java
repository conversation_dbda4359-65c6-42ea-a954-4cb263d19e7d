package com.soft.gcc.xtbg.xcgl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTSqlagentplanconfig;
import com.soft.gcc.xtbg.xcgl.service.IGcjlTSqlagentplanconfigService;
import com.soft.gcc.xtbg.xcgl.mapper.GcjlTSqlagentplanconfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_SqlAgentPlanConfig(工程监理_Sql计划任务参数配置表)】的数据库操作Service实现
* @createDate 2023-07-12 10:59:57
*/
@Service
public class GcjlTSqlagentplanconfigServiceImpl extends ServiceImpl<GcjlTSqlagentplanconfigMapper, GcjlTSqlagentplanconfig>
    implements IGcjlTSqlagentplanconfigService{

}




