package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName GCJL_T_PersonLimitLog
*/
@TableName(value ="GCJL_T_PersonLimitLog")
@Data
public class GcjlTPersonlimitlog implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    * 工程监理人员表Id
    */
    @TableField(value = "SrcPersonId")
    @JSONField(name = "SrcPersonId")

    private Integer SrcPersonId;
    /**
    * 补贴费用上限
    */
    @TableField(value = "Limit")
    @JSONField(name = "Limit")

    private BigDecimal Limit;
    /**
    * 修改时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String RealName;
}
