package com.soft.gcc.xtbg.xcgl.dto;

import com.soft.gcc.xtbg.xcgl.entity.GcjlTDepartment;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTReportalarm;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class TjcxDto implements Serializable {

    String DeptName;
    String UserName;
    String UsersName;
    String PerformMileage;
    String PerformCost;
    String AlarmTime;
    String AlarmTypeString;
    String AlarmNote;

    public TjcxDto(String deptName, String performMileageCount, String performCostCount) {
        this.PerformMileage = performMileageCount;
        this.PerformCost = performCostCount;
        this.DeptName=deptName;
    }
    @Override
    public String toString() {
        return "[TjcxDto(PerformMileage=" + PerformMileage + ", PerformCost=" + PerformCost + ", DeptName=" + getDeptName() + ")]";
    }
    public BigDecimal getPerformCost(){
        return new BigDecimal(this.PerformCost);
    }
    public BigDecimal getPerformMileage(){
        return new BigDecimal(this.PerformMileage);
    }
    public String getAlarmTime(){
        return this.AlarmTime;
    }
    public String getAlarmTypeString(){
        return this.AlarmTypeString;
    }
    public String getAlarmNote(){
        return this.AlarmNote;
    }
}
