package com.soft.gcc.xtbg.xcgl.Controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcbbDto;
import com.soft.gcc.xtbg.xcgl.dto.GcjlTXcjhDto;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTCancellationLog;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTLc;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPlan;
import com.soft.gcc.xtbg.xcgl.entity.LcWorkflow;
import com.soft.gcc.xtbg.xcgl.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.w3c.dom.stylesheets.LinkStyle;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 行程报备
 */
@Slf4j
@RestController
@RequestMapping("xcjh")
public class XcglXcjhController extends BaseController {

    @Autowired
    private IGcjlTLcService gcjlTLcService;
    @Autowired
    private IGcjlTPlanService planService;
    @Autowired
    private IGcjlLcWorkflowService workflowService;
    @Resource
    private IGcjlTCancellationLogService cancellationLogService;

    /**
     * 获取行程报备列表
     */
    @PostMapping("selectPageList")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    public Result<Object> selectPageList(@RequestBody GcjlTXcjhDto xcjhDto) {
        return gcjlTLcService.selectPageList(xcjhDto);
    }


    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    @PostMapping("getInfoByLcId")
    public Result<Object> getInfo(@RequestBody Integer lcId) {
        if (lcId == null) {
            Result.error("参数错误");
        }
//        List<GcjlTPlan> planList = planService.list(new LambdaQueryWrapper<GcjlTPlan>().eq(GcjlTPlan::getLcId, lcId).orderByAsc(GcjlTPlan::getSort));
        return Result.ok(planService.getInfo(lcId,user()));
    }


    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    @PostMapping("getWorkFlowList")
    public Result<Object> getWorkFlowList(@RequestBody Integer lcId) {
        if (lcId == null) {
            Result.error("参数错误");
        }
       List<LcWorkflow> list =  workflowService.list(new LambdaQueryWrapper<LcWorkflow>()
                .eq(LcWorkflow::getLcDefineId, 9136).eq(LcWorkflow::getYwId, lcId)
                .orderByAsc(LcWorkflow::getStartdate).orderByAsc(LcWorkflow::getNumber));
        return Result.ok(list);
    }

    /**
     * 确认流程日志
     * @param lcId
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    @PostMapping("getConfirmProcessLogList")
    public Result<Object> getConfirmProcessLogList(@RequestBody Integer lcId) {
        if (lcId == null) {
            Result.error("参数错误");
        }
        List<LcWorkflow> list =  workflowService.list(new LambdaQueryWrapper<LcWorkflow>()
                .eq(LcWorkflow::getLcDefineId, 9139).eq(LcWorkflow::getYwId, lcId)
                .orderByAsc(LcWorkflow::getStartdate).orderByAsc(LcWorkflow::getNumber));
        return Result.ok(list);
    }

    /**
     * 获取 手动作废 时间
     * @param cancellationLog
     * @return
     */
    @PostMapping("getCalcellationLog")
    public Result<Object> getCalcellationLog(@RequestBody GcjlTCancellationLog cancellationLog){
        return Result.ok(cancellationLogService.getInfo(cancellationLog));
    }


    /**
     * 导出excel
     */
    @PostMapping("exportExcel")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    public void exportExcel(@RequestBody GcjlTXcjhDto xcjhDto , HttpServletResponse response){
        try{
            gcjlTLcService.exportExcel(xcjhDto,response);
        }catch (Exception ex){
            log.error(ex.getMessage());
            ex.printStackTrace();
        }
    }

    /**
     * 导出excel
     */
    @PostMapping("exportExcelChildPlan")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
    public void exportExcelChildPlan(@RequestBody GcjlTXcjhDto xcjhDto , HttpServletResponse response){
        try{
            gcjlTLcService.exportExcelChildPlan(xcjhDto,response);
        }catch (Exception ex){
            log.error(ex.getMessage());
            ex.printStackTrace();
        }
    }










//    /**
//     * 导出excel
//     * @param gcjlTXcbb
//     * @param response
//     * @return
//     */
//    @PostMapping("exportExcel")
//    @PreAuthorize("@ss.hasAnyRoles('协同办公-行程管理-统计查询,协同办公-行程管理-组织架构,协同办公-行程管理-计划上报,协同办公-行程管理-行程汇总,协同办公-行程管理,协同办公-行程管理-监理站站长,协同办公-行程管理-工程管理,协同办公-行程管理-计划审批,协同办公-行程管理-人员管理')")
//    public Result<Object> exportExcel(@RequestBody GcjlTXcbbDto gcjlTXcbb, HttpServletResponse response){
//        return gcjlTXcbbService.exportExcel(gcjlTXcbb,response);
//    }
}
