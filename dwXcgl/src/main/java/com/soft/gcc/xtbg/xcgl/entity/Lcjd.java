package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 流程节点
* @TableName Lcjd
*/
@TableName(value ="Lcjd")
@Data
public class Lcjd implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "ID", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    *
    */
    @TableField(value = "lcjdID")
    @JSONField(name = "lcjdId")
    private Integer lcjdId;
    /**
    *
    */
    @TableField(value = "lc_defineID")
    @JSONField(name = "lcDefineId")
    private Integer lcDefineId;
    /**
    *
    */
    @TableField(value = "jdmc")
    @JSONField(name = "jdmc")
    private String jdmc;
    /**
    *
    */
    @TableField(value = "nextID")
    @JSONField(name = "nextId")
    private Integer nextId;
    /**
    *
    */
    @TableField(value = "type")
    @JSONField(name = "type")
    private Integer type;
    /**
    *
    */
    @TableField(value = "shgr")
    @JSONField(name = "shgr")
    private Integer shgr;
    /**
    *
    */
    @TableField(value = "GroupID")
    @JSONField(name = "groupId")
    private String groupId;
    /**
    *
    */
    @TableField(value = "IsBX")
    @JSONField(name = "isBx")
    private Integer isBx;
    /**
    *
    */
    @TableField(value = "FormType")
    @JSONField(name = "formType")
    private String formType;
    /**
    *
    */
    @TableField(value = "CheckFile")
    @JSONField(name = "checkFile")
    private String checkFile;
    /**
    *
    */
    @TableField(value = "CheckData")
    @JSONField(name = "checkData")
    private String checkData;
    /**
    *
    */
    @TableField(value = "canBack")
    @JSONField(name = "canBack")
    private Integer canBack;
    /**
    *
    */
    @TableField(value = "BackjdID")
    @JSONField(name = "backjdId")
    private String backjdId;
    /**
    *
    */
    @TableField(value = "LookFileJdID")
    @JSONField(name = "lookFileJdID")
    private String lookFileJdID;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
