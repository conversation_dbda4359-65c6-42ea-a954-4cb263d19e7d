package com.soft.gcc.xtbg.xcgl.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
* 工程监理--人员表（当天最后点距离公司里程）日志
* @TableName GCJL_T_PersonEndLog
*/
@TableName(value ="GCJL_T_PersonEndLog")
@Data
public class GcjlTPersonendlog implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name = "Id")
    private Integer Id;

    /**
    *
    */
    @TableField(value = "SrcUserId")
    @JSONField(name = "SrcUserId")

    private Integer SrcUserId;
    /**
    * 当天最后点距离公司里程
    */
    @TableField(value = "EndMileage")
    @JSONField(name = "EndMileage")

    private BigDecimal EndMileage;
    /**
    * 当天最后点距离公司费用
    */
    @TableField(value = "EndCost")
    @JSONField(name = "EndCost")

    private BigDecimal EndCost;
    /**
    * 创建时间
    */
    @TableField(value = "CreateTime")
    @JSONField(name = "CreateTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreateTime;
    /**
    * 当天时间
    */
    @TableField(value = "CurrDate")
    @JSONField(name = "CurrDate")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CurrDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
