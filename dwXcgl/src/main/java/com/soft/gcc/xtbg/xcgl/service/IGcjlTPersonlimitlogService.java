package com.soft.gcc.xtbg.xcgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.xcgl.entity.GcjlTPersonlimitlog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GCJL_T_PersonLimitLog】的数据库操作Service
* @createDate 2023-07-12 10:59:16
*/
public interface IGcjlTPersonlimitlogService extends IService<GcjlTPersonlimitlog> {

    /**
     * 根据工程监理人员表Id 获取人员补贴费用上限
     * @param srcPersonId
     * @return
     */
    IPage<GcjlTPersonlimitlog> getPersonLimitLogBySrcPersonId(Page<GcjlTPersonlimitlog> page, Integer srcPersonId);
}
