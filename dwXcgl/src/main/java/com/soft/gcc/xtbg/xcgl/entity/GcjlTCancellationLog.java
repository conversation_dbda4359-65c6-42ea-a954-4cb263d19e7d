package com.soft.gcc.xtbg.xcgl.entity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户手动作废日志
 */
@Data
@TableName("GCJL_T_CalcellationLog")
public class GcjlTCancellationLog implements Serializable {

    /**
     * 主键Id
     */
    @TableField("Id")
    private int id;

    /**
     * 用户Id
     */
    @TableField("UserId")
    private Integer userId;

    /**
     * 用户名称
     */
    @TableField("UserName")
    private String userName;

    /**
     * 作废时间
     */
    @TableField("CancellationTime")
    private Date cancellationTime;

    /**
     * 业务Id
     */
    @TableField("YwId")
    private Integer ywId;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    private Date createTime;


}



