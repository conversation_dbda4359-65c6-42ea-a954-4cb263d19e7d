package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("CPS_T_HAND_LOG")
@ApiModel(value="CPS_T_HAND_LOG对象", description="")
public class CPS_T_HAND_LOG extends Model<CPS_T_HAND_LOG> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="HL_INFO")
    private String HL_INFO;

    @JSONField(name="HL_RATE")
    private Integer HL_RATE;

    @JSONField(name="HL_DATE")
    private LocalDateTime HL_DATE;

    @JSONField(name="HL_MARK")
    private String HL_MARK;


    @Override
    protected Serializable pkVal() {
        return this.HL_RATE;
    }

}
