package com.soft.gcc.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户能够切换的账户
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("NFT_LOGIN_UC")
@ApiModel(value="NFT_LOGIN_UC对象", description="用户能够切换的账户")
public class NFT_LOGIN_UC extends Model<NFT_LOGIN_UC> {

    private static final long serialVersionUID = 1L;

    @JSONField(name="RC_USERN")
    private String RC_USERN;

    @JSONField(name="RU_USER")
    private String RU_USER;

    @JSONField(name="RU_USERN")
    private String RU_USERN;

    @JSONField(name="RC_USER")
    private String RC_USER;

    @TableId(value = "REC_ID", type = IdType.AUTO)
    @JSONField(name="REC_ID")
    private Integer REC_ID;


    @Override
    protected Serializable pkVal() {
        return this.REC_ID;
    }

}
