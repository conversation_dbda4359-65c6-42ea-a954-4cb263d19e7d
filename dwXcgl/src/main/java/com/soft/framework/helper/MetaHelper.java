package com.soft.framework.helper;

import com.soft.framework.common.utils.date.DateUtil;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.regex.Pattern;

public class MetaHelper {
    public static String ExtractStrQuot(String instr) {
        return "'" + instr + "'";
    }

    public static String ExtractStrBracket(String instr) {
        return "(" + instr + ")";
    }

    public static String ArrayList2String(List<String> arrList) {
        String retstr = "";
        for (String tmpstr : arrList) {
            retstr += tmpstr + ",";
        }
        return retstr;
    }

    public static String ArrayList2String2(List<String> arrList) {
        String retstr = "";
        for (String tmpstr : arrList) {
            retstr += tmpstr + "\n";
        }
        return retstr;
    }

    public static String GetDateString(Date date, boolean delimit) {
        String dstr = "";
        if (delimit) {
            dstr = DateUtil.formatDate(date, "yyyyMMdd");
        } else {
            dstr = DateUtil.formatDate(date, "yyyyMMdd");
        }
        return dstr;
    }

    public static boolean IsEmptyStr(String pstr) {
        if (pstr == null || pstr.isEmpty() || pstr.equals("")) {
            return true;
        }
        return false;
    }

    public static String SafeStringValue(Object pobj) {
        if (pobj == null) {
            return "";
        }
        String pstr = String.valueOf(pobj);
        if (pstr.isEmpty()) {
            return "";
        } else {
            return pstr;
        }
    }

    public static String ToJsonString(String msgstr) {
        if(msgstr==null) {
            return "";
        }
        msgstr=msgstr.replace("\"","").replace("\'","").replace("\r\n","");
        msgstr=msgstr.replace("\n","").replace("{","").replace("}","");
        msgstr=msgstr.replace("[","").replace("]","");
        return msgstr;
    }

    public static boolean IsValidateDate(String in) {
        try {
            if(in.indexOf("-")>0)
            {
                DateUtil.parseDate(in,"yyyy-MM-dd");
            }else if(in.indexOf("/")>0)
            {
                DateUtil.parseDate(in,"yyyy/MM/dd");
            }else {
                DateUtil.parseDate(in, "yyyyMMdd");
            }
            return true;
        } catch (ParseException ex) {
            return false;
        }
    }

    public static boolean IsValidateInt(String in) {
        try {
            Integer.parseInt(in);
            return true;
        } catch (NumberFormatException ex) {
            return false;
        }
    }

    public static String StrExtQuote(String pstr) {
        if (pstr != null && !pstr.isEmpty() && !pstr.equals("")) {
            return "'" + pstr + "'";
        } else {
            return pstr;
        }
    }

    public static long ExecDateDiff(Date dateBegin, Date dateEnd)
    {
        long millis = DateUtil.getMillis(dateEnd) - DateUtil.getMillis(dateBegin);
        return millis;
    }

    public static boolean IsValidateShortDate(String datestr)
    {
        try
        {
            String ldstr = "";
            String ystr = datestr.substring(0, 4);
            String mstr = datestr.substring(4, 2);
            String dstr = datestr.substring(6, 2);
            ldstr = ystr + "-" + mstr + "-" + dstr;
            return IsValidateDate(ldstr);
        }
        catch (Exception err)
        {
            return false;
        }
    }

    public static boolean IsValidateYMStr(String datestr)
    {
        try
        {
            return IsValidateShortDate(datestr + "01");
        }
        catch (Exception err)
        {
            return false;
        }
    }

    public static boolean IsValidateFloat(String fstr)
    {
        try
        {
            float f = Float.parseFloat(fstr);
        }
        catch (Exception err)
        {
            return false;
        }
        return true;
    }

    public static boolean IsValidateDouble(String dstr)
    {
        try
        {
            double d = Double.parseDouble(dstr);
        }
        catch (Exception err)
        {
            return false;
        }
        return true;
    }

    public static  boolean IsValidateNumStr(String str)
    {
        try
        {
            Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
            return pattern.matcher(str).matches();
        }
        catch (Exception err)
        {
            return false;
        }
    }

    public static String GetChDateString(Date date)
    {
        return DateUtil.date2Str(date,DateUtil.date_sdf_wz);
    }

    public static String ShortDateToLong(String pstr) {
        Date tmpdt = DateUtil.str2Date(pstr, "yyyyMMdd");
        return DateUtil.date2Str(tmpdt, "yyyy-MM-dd");
    }

    public static String LongDateToShort(String pstr) {
        Date tmpdt = DateUtil.str2Date(pstr, "yyyy-MM-dd");
        return DateUtil.date2Str(tmpdt, "yyyyMMdd");
    }

    public static String GetTimeString(Date date) {
        return DateUtil.date2Str(date,DateUtil.datetimeFormat);
    }

    public static String GetSimpTimeString(Date date) {
        return DateUtil.date2Str(date,DateUtil.yyyymmddhhmmss);
    }

    public static String GetTmString(Date date) {
        return DateUtil.formatDate(date, "HH:mm");
    }

    public static String GetRunCPInfo(String msgstr) {
        String clazz = Thread.currentThread().getStackTrace()[3].getClassName();
        String method = Thread.currentThread().getStackTrace()[3].getMethodName();
        String logstr = msgstr + ",出错位置， Class：" + clazz + "，Method：" + method;
        return logstr;
    }

    public static String GetRandString(int len) {
        Random r1 = new Random();
        int seed = r1.nextInt(100000000);
        Random ro = new Random(seed);
        int ival = (int) Math.pow(10, len);
        int reti = ro.nextInt(ival);
        return String.valueOf(reti);
    }

    public static String GetRand8String() {
        int len = 8;
        Random r1 = new Random();
        int seed = r1.nextInt(100000000);
        Random ro = new Random(seed);
        int ival = (int) Math.pow(10, len);
        int reti = ro.nextInt(ival);
        return String.valueOf(reti);
    }

    public static String getString(String s, String srcEncoding, String destEncoding) {
        try {
            return new String(s.getBytes(srcEncoding), destEncoding);
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return s;
        }
    }

    public static String getString(byte[] b, String encoding) {
        try {
            return new String(b, encoding);
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return new String(b);
        }
    }

    public static String LPadString(String instr, int length) {
        int strlen = instr.length();
        if (strlen > length) {
            return instr;
        } else {
            String tmpstr = StringOfChar(length - strlen, ' ');
            tmpstr += instr;
            return tmpstr;
        }
    }

    public static String LPadString(String instr, int length,char pchar) {
        int strlen = instr.length();
        if (strlen > length) {
            return instr;
        } else {
            String tmpstr = StringOfChar(length - strlen, pchar);
            tmpstr += instr;
            return tmpstr;
        }
    }

    public static String StringOfChar(int len, char pchar) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < len; i++) {
            sb.append(pchar);
        }
        return sb.toString();
    }

    public static String LRPadString(String instr, int length) {
        int strlen = instr.length();
        if (strlen > length) {
            return instr;
        } else {
            String tmpstr = "";
            String ltmpstr = StringOfChar((length - strlen) / 2, ' ');
            String rtmpstr = StringOfChar((length - strlen) - (length - strlen) / 2, ' ');
            tmpstr = ltmpstr + instr + rtmpstr;
            return tmpstr;
        }
    }

    public static void StringBuilderEmpty(StringBuilder sb) {
        sb.delete(0, sb.length());
    }

    public static String CustomUrlEncode(String imps)
    {
        try {
            String imgss = URLEncoder.encode(imps, "UTF-8");
            imgss = imgss.replaceAll("%2F", "/");
            return imgss;
        }catch (Exception Ex)
        {
            return "";
        }
    }

    public static String GetCurrentIp(HttpServletRequest request)
    {
        try
        {
            String userip = "";
            userip = request.getHeader("X-Forwarded-For");
            if (userip.equals("")) {
                userip = request.getHeader("X-Real-IP");
            }
            if (userip.equals("")) {
                userip = request.getHeader("Remote_Addr");
            }
            return userip;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static String GetTrace(Throwable t) {
        StringWriter stringWriter= new StringWriter();
        PrintWriter writer= new PrintWriter(stringWriter);
        t.printStackTrace(writer);
        StringBuffer buffer= stringWriter.getBuffer();
        return buffer.toString();
    }
}
