package com.soft.framework.helper;

import com.soft.framework.common.utils.date.DateUtils;

import java.io.*;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 处理并记录日志文件
 * 
 * <AUTHOR>
 */
public class LogHelper
{
    private static final Logger logger = LoggerFactory.getLogger(LogHelper.class);

    public static String getBlock(Object msg)
    {
        if (msg == null)
        {
            msg = "";
        }
        return "[" + msg.toString() + "]";
    }

    public static void WriteSysLog(String msgstr)
    {
        logger.info(msgstr);
        if (ConfigHelper.getSysLogPath().equals("")) {
            return;
        }
        String filePath = ConfigHelper.getSysLogPath();
        String fileName = DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate())+".log";
        String fullname=filePath+"/"+fileName;

        try
        {
            File file = new File(fullname);
            if (!file.exists()) {
                File dir = new File(file.getParent());
                dir.mkdirs();
                file.createNewFile();
            }

            FileOutputStream fos = new FileOutputStream(fullname,true);
            String timestr = MetaHelper.GetTimeString(new Date());
            fos.write((timestr+":"+msgstr).getBytes("UTF-8"));
            String newLine = System.getProperty("line.separator");
            fos.write(newLine.getBytes());
            fos.close();
        }catch(Exception Ex)
        {

        }
    }

    public static void WriteSqlLog(String msgstr)
    {
        if (ConfigHelper.getSqlLogPath().equals("")) {
            return;
        }
        String filePath = ConfigHelper.getSqlLogPath();
        String fileName = DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate())+".log";
        String fullname=filePath+"/"+fileName;

        try
        {
            File file = new File(fullname);
            if (!file.exists()) {
                File dir = new File(file.getParent());
                dir.mkdirs();
                file.createNewFile();
            }

            FileOutputStream fos = new FileOutputStream(fullname,true);
            String timestr = MetaHelper.GetTimeString(new Date());
            fos.write((timestr+":"+msgstr).getBytes("UTF-8"));
            String newLine = System.getProperty("line.separator");
            fos.write(newLine.getBytes());
            fos.close();
        }catch(Exception Ex)
        {

        }
    }

    public static void WriteOCCILog(String msgstr)
    {
        if (ConfigHelper.getOcciLogPath().equals("")) {
            return;
        }
        String filePath = ConfigHelper.getOcciLogPath();
        String fileName = DateUtils.parseDateToStr("yyyyMMdd", DateUtils.getNowDate())+".log";
        String fullname=filePath+"/"+fileName;

        try
        {
            File file = new File(fullname);
            if (!file.exists()) {
                File dir = new File(file.getParent());
                dir.mkdirs();
                file.createNewFile();
            }

            FileOutputStream fos = new FileOutputStream(fullname,true);
            String timestr = MetaHelper.GetTimeString(new Date());
            fos.write((timestr+":"+msgstr).getBytes("UTF-8"));
            String newLine = System.getProperty("line.separator");
            fos.write(newLine.getBytes());
            fos.close();
        }catch(Exception Ex)
        {

        }
    }

    public static String ReadSysLog(String dtstr) {
        if (ConfigHelper.getSysLogPath().equals("")) {
            return "";
        }
        String filePath = ConfigHelper.getSysLogPath();
        String fileName = dtstr+".log";
        String fullname=filePath+"/"+fileName;
        StringBuilder sb=new StringBuilder();

        File file = new File(fullname);
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            while ((tempString = reader.readLine()) != null) {
                sb.append(tempString);
                line++;
            }
            reader.close();

            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
    }

    public static String ReadSqlLog(String dtstr) {
        if (ConfigHelper.getSqlLogPath().equals("")) {
            return "";
        }
        String filePath = ConfigHelper.getSqlLogPath();
        String fileName = dtstr+".log";
        String fullname=filePath+"/"+fileName;
        StringBuilder sb=new StringBuilder();

        File file = new File(fullname);
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            while ((tempString = reader.readLine()) != null) {
                sb.append(tempString);
                line++;
            }
            reader.close();

            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
    }

    public static String ReadOCCILog(String dtstr) {
        if (ConfigHelper.getOcciLogPath().equals("")) {
            return "";
        }
        String filePath = ConfigHelper.getOcciLogPath();
        String fileName = dtstr+".log";
        String fullname=filePath+"/"+fileName;
        StringBuilder sb=new StringBuilder();

        File file = new File(fullname);
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            int line = 1;
            while ((tempString = reader.readLine()) != null) {
                sb.append(tempString);
                line++;
            }
            reader.close();

            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
    }
}
