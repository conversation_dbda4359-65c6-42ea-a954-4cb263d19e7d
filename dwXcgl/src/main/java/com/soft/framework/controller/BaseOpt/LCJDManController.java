package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.Lcjd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/LCJDMan")
@Api(tags = "基本框架接口->流程节点管理接口")
public class LCJDManController {
    private Boolean GatherParams2Obj(Map<String, String> params, Lcjd entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("jdmc"))) {
                entity.setJdmc(params.get("jdmc"));
            }
            if (!StringUtil.IsNull(params.get("GroupID"))) {
                entity.setGroupID(params.get("GroupID"));
            }
            if (!StringUtil.IsNull(params.get("FormType"))) {
                entity.setFormType(params.get("FormType"));
            }
            if (!StringUtil.IsNull(params.get("CheckFile"))) {
                entity.setCheckFile(params.get("CheckFile"));
            }
            if (!StringUtil.IsNull(params.get("CheckData"))) {
                entity.setCheckData(params.get("CheckData"));
            }
            if (!StringUtil.IsNull(params.get("BackjdID"))) {
                entity.setBackjdID(params.get("BackjdID"));
            }
            if (!StringUtil.IsNull(params.get("LookFileJdID"))) {
                entity.setLookFileJdID(params.get("LookFileJdID"));
            }

            if (!StringUtil.IsNullOrEmpty(params.get("LcjdID"))) {
                entity.setLcjdID(Integer.parseInt(params.get("LcjdID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("lc_defineID"))) {
                entity.setLc_defineID(Integer.parseInt(params.get("lc_defineID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("nextID"))) {
                entity.setNextID(Integer.parseInt(params.get("nextID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("type"))) {
                entity.setType(Integer.parseInt(params.get("type")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("shgr"))) {
                entity.setShgr(Integer.parseInt(params.get("shgr")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("IsBX"))) {
                entity.setIsBX(Integer.parseInt(params.get("IsBX")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("canBack"))) {
                entity.setCanBack(Integer.parseInt(params.get("canBack")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddLCJD", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddLCJD", notes = "新增流程节点定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddLCJD(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            Lcjd entity = new Lcjd();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Integer rid = -1;
            rid = WpServiceHelper.AddLcjd(entity);
            if (rid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增流程信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(rid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyLCJD", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyLCJD", notes = "修改流程节点定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyLCJD(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            Integer iid = Integer.parseInt(Id);

            Lcjd entity = null;
            entity = WpServiceHelper.GetLcjdById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程节点信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateLcjd(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改流程节点信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteLCJD", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteLCJD", notes = "删除流程节点定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteLCJD(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String Id = request.getParameter("ID");
            int iid = Integer.parseInt(Id);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteLcjdById(iid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除流程节点定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删流程节点定义信息成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String LcId = request.getParameter("LcId");

            strsql.append("select * from LCJD where 1=1 ");
            if (!StringUtil.IsNullOrEmpty(LcId)) {
                strsql.append(" and lc_defineID=" + LcId);
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetLCJDList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetLCJDList", notes = "获取当前流程节点定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetLCJDList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<Lcjd> list = null;

            list = WpServiceHelper.GetLcjdList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程节点定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(Lcjd.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetLCJDById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetLCJDById", notes = "获取指定具有指定ID的流程节点定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetLCJDById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer iid = Integer.parseInt(Id);

            Lcjd entity = null;

            entity = WpServiceHelper.GetLcjdById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程节点定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(Lcjd.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前模块自启动项信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程节点定义信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("LCJDID", "流程节点", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("lc_defineID", "流程编号", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("jdmc", "节点名称", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("nextID", "下一节点", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("type", "节点步骤", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("shgr", "审核标志", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GroupID", "所属机构", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("IsBX", "是否主线", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("FormType", "上一步骤", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("CheckFile", "检测FILE", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("CheckData", "检测数据", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("canBack", "能否回退", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("BackjdID", "回退节点", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("LookFileJdID", "可查看文件节点", 30));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "模块配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
