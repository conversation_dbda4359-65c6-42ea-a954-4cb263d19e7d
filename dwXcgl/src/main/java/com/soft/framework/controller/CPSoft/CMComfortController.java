package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.annotation.JSONField;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SessionHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.yyszc.wpbase.ventity.PersonEntity;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CMComfort" )
public class CMComfortController {
    ToolHelper cptl = new ToolHelper();
    SqlHelper sqlhelper = new SqlHelper();

    @Data
    class CPSOFT_CHILD_DIR
    {
        @JSONField(name="id")
        public int id;

        @JSONField(name="name")
        public String name;
    }

    @RequestMapping(value="/ExecuteFileCopy",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteFileCopy(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        String RootP = request.getParameter("RootP");
        String FirstP = request.getParameter("FirstP");
        String SecondP = request.getParameter("SecondP");
        String ThirdP = request.getParameter("ThirdP");
        if (StringUtil.IsNullOrEmpty(RootP)||StringUtil.IsNullOrEmpty(FirstP)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        PersonEntity person =SessionHelper.getSessionPerson();
        if (!person.getLoginName().equals("qidf"))
        {
            ajaxResult =AjaxResult.error("非超级管理用户禁止使用该管理功能!");
            return ajaxResult;
        }

        try
        {
            String destd = ConfigHelper.getStaticPath()+"/"+RootP+"/"+FirstP;
            if (SecondP != "")
            {
                if (ThirdP != "") {
                    destd = destd + "/" + SecondP+"/"+ThirdP;
                } else {
                    destd = destd + "/" + SecondP;
                }
            }
            String destpath=destd;

            String cachepath = ConfigHelper.getProfile()+"/Temp";

            List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
            if(files.size()>0) {
                MultipartFile file = files.get(0);
                if (file.isEmpty()) {
                    ajaxResult = AjaxResult.error("上传文件失败");
                    return ajaxResult;
                }

                String ofileName = file.getOriginalFilename();
                String fext =".tmp";
                String frext = FileUtil.ExtractFileExt(ofileName);
                String fname = FileUtil.ExtractFileName(ofileName);

                File dest = new File(cachepath +"/"+ fname+fext);
                file.transferTo(dest);
                if (FileUtil.FileExists(cachepath +"/"+ fname + fext))
                {
                    FileUtil.CopyFile(cachepath + "/" + fname + fext, destpath + "/" + fname);
                    FileUtil.Delete(cachepath + "/" + fname + fext);
                }
            }else
            {
                ajaxResult = AjaxResult.error("上传文件失败");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("文件上传成功");
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    //获取一级目录
    @RequestMapping(value="/GetFirstPath",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult GetFirstPath(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        String RootP = request.getParameter("RootP");
        if (StringUtil.IsNullOrEmpty(RootP)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        List<CPSOFT_CHILD_DIR> list = new ArrayList<CPSOFT_CHILD_DIR>();

        try
        {
            String destpath=ConfigHelper.getStaticPath()+"/"+RootP+"/";
            List<String> dirs=FileUtil.GetDirectories(destpath);
            for (int i = 0; i < dirs.size(); i++)
            {
                String dname = dirs.get(i).replace("\\","/");
                CPSOFT_CHILD_DIR tmpd=new CPSOFT_CHILD_DIR();
                tmpd.id = i;
                tmpd.name = dname.replace(destpath+"\\","").toString();
                list.add(tmpd);
            }

            ajaxResult=AjaxResult.extgrid(CPSOFT_CHILD_DIR.class,list.size(),list);
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    //获取二级目录
    @RequestMapping(value="/GetSecondPath",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult GetSecondPath(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        String RootP = request.getParameter("RootP");
        String FirstP = request.getParameter("FirstP");
        if (StringUtil.IsNullOrEmpty(RootP)||StringUtil.IsNullOrEmpty(FirstP)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        List<CPSOFT_CHILD_DIR> list = new ArrayList<CPSOFT_CHILD_DIR>();

        try
        {
            String destpath=ConfigHelper.getStaticPath()+"/"+RootP+"/"+FirstP+"/";
            List<String> dirs=FileUtil.GetDirectories(destpath);
            for (int i = 0; i < dirs.size(); i++)
            {
                String dname = dirs.get(i).replace("\\","/");
                CPSOFT_CHILD_DIR tmpd=new CPSOFT_CHILD_DIR();
                tmpd.id = i;
                tmpd.name = dname.replace(destpath+"\\","").toString();
                list.add(tmpd);
            }

            CPSOFT_CHILD_DIR opt = new CPSOFT_CHILD_DIR() { };
            opt.id = -1;
            opt.name = "";
            list.add(0, opt);

            ajaxResult=AjaxResult.extgrid(CPSOFT_CHILD_DIR.class,list.size(),list);
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    //获取三级目录
    @RequestMapping(value="/GetThirdPath",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult GetThirdPath(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        String RootP = request.getParameter("RootP");
        String FirstP = request.getParameter("FirstP");
        String SecondP = request.getParameter("SecondP");
        if (StringUtil.IsNullOrEmpty(RootP)||StringUtil.IsNullOrEmpty(FirstP)||StringUtil.IsNullOrEmpty(SecondP)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        List<CPSOFT_CHILD_DIR> list = new ArrayList<CPSOFT_CHILD_DIR>();

        try
        {
            String destpath=ConfigHelper.getStaticPath()+"/"+RootP+"/"+FirstP+"/"+SecondP+"/";
            List<String> dirs=FileUtil.GetDirectories(destpath);
            for (int i = 0; i < dirs.size(); i++)
            {
                String dname = dirs.get(i).replace("\\","/");
                CPSOFT_CHILD_DIR tmpd=new CPSOFT_CHILD_DIR();
                tmpd.id = i;
                tmpd.name = dname.replace(destpath+"\\","").toString();
                list.add(tmpd);
            }

            CPSOFT_CHILD_DIR opt = new CPSOFT_CHILD_DIR() { };
            opt.id = -1;
            opt.name = "";
            list.add(0, opt);

            ajaxResult=AjaxResult.extgrid(CPSOFT_CHILD_DIR.class,list.size(),list);
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
