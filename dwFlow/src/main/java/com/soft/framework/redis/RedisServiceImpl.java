package com.soft.framework.redis;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
 
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
 
@Service
public class RedisServiceImpl implements IRedisService {
	
	@Autowired
	RedisTemplate<Object, Object> redisTemplate;
 
	@Override
	public void addValue(String key, Object value, Long timeout) throws Exception {
		redisTemplate.boundValueOps(key).set(value, timeout, TimeUnit.DAYS);
	}
 
	@Override
	public void addValueSecond(String key, Object value, Long timeout) throws Exception {
		redisTemplate.boundValueOps(key).set(value, timeout, TimeUnit.SECONDS);
	}
	
	@Override
	public Object getValue(String key) throws Exception {
		return redisTemplate.boundValueOps(key).get();
	}
 
	@Override
	public void addList(String key, List<Object> list, Long timeout) throws Exception {
		redisTemplate.boundValueOps(key).set(list, timeout, TimeUnit.DAYS);
	}
 
	@SuppressWarnings("unchecked")
	@Override
	public List<Object> getList(String key) throws Exception {
		return (List<Object>) redisTemplate.boundValueOps(key).get();
	}
 
	@Override
	public void addMap(String key, Map<String, Object> map, Long timeout) throws Exception {
		redisTemplate.boundValueOps(key).set(map, timeout, TimeUnit.DAYS);
	}
 
	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> getMap(String key) throws Exception {
		return (Map<String, Object>) redisTemplate.boundValueOps(key).get();
	}
	
	@Override
	public Long getExpire(String key){
		return redisTemplate.getExpire(key);
	}
	
	@Override
	public boolean hasKey(String key){
		return redisTemplate.hasKey(key);
	}
	
}
