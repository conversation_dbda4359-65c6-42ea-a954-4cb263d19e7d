package com.soft.framework.filter;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.SecurityUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.SysLoginService;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.entity.Person;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * token过滤器 验证token有效性
 * 
 * <AUTHOR>
 */
public class TokenFilter extends OncePerRequestFilter
{
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException
    {
        String token="";
        String surl=request.getRequestURL().toString();
        try
        {
            if (IsWhiteListFunc(request)) {
                chain.doFilter(request, response);
                return;
            }

            TokenService tokenService= (TokenService)SpringUtil.getBean("tokenService");
            if(tokenService!=null)
            {
                //判断框架授予的token是否有效的标志是，本地resdis是否合适的配置，LoginUser!=null
                LoginUser loginUser = tokenService.getLoginUser(request);
                if(loginUser==null) {
                    String tokens = ConfigHelper.getCurrentUUToken();
                    if(!StringUtil.IsNullOrEmpty(tokens)) {
                        loginUser = tokenService.getLoginUser(tokens);
                    }
                }

                if (loginUser!=null)
                {
                    token=tokenService.getToken(request);
                    if(SessionHelper.SessionVaild(token))
                    {
                        tokenService.verifyToken(token);

                        if(SecurityUtils.getAuthentication()==null) {
                            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                        }
                    }else
                    {
                        //用户在其他地方登录，本次session主动登出
                        if(loginUser.getLpf().equals("pc")) {
                            tokenService.delLoginUser(loginUser.getToken());
                        }else{
                            tokenService.delAppLoginUser(loginUser.getToken());
                        }
                        ResponseInValidTokenInfo(response);
                        return;
                    }
                }else if(request.getParameter("jtoken")!=null)
                {
                    String jtoken=request.getParameter("jtoken").toString();
                    String userkey= tokenService.getTokenKey(jtoken);
                    loginUser=tokenService.getLoginUserFromKey(userkey);
                    if (loginUser!=null) {
                        tokenService.verifyToken(loginUser);
                        if(SecurityUtils.getAuthentication()==null) {
                            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                        }
                    }else
                    {
                        ResponseNoTokenInfo(response);
                        return;
                    }
                }else if(request.getParameter("jtokens")!=null)
                {
                    String jtokens=request.getParameter("jtokens").toString();
                    loginUser=tokenService.getLoginUser(jtokens);
                    if (loginUser!=null) {
                        tokenService.verifyToken(loginUser);
                        if(SecurityUtils.getAuthentication()==null) {
                            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                        }
                    }else
                    {
                        ResponseNoTokenInfo(response);
                        return;
                    }
                }else if(surl.indexOf("/Service")>0 && surl.indexOf("/Service/Base/User/Login")<0 && surl.indexOf("/Service/Base/User/MLogin")<0) {
                    String utoken=ConfigHelper.getCurrentUUTokenString();
                    if (!StringUtil.IsNullOrEmpty(utoken)) {
                        StringBuffer sb = new StringBuffer();
                        token = ImplicateAutoLogin(request,utoken,sb);
                        if (!StringUtil.IsNullOrEmpty(token)) {
                            loginUser = tokenService.getLoginUser(token);

                            if (loginUser != null) {
                                tokenService.verifyToken(token);

                                if(SecurityUtils.getAuthentication() == null) {
                                    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                                }
                            }else{
                                LogHelper.WriteSysLog("生成Token非法,ctoken="+utoken+",surl="+surl);
                                ResponseWithTokenWithErrInfo(response,"生成Token非法！！");
                                return;
                            }
                        }else{
                            LogHelper.WriteSysLog("生成Token失败,ctoken="+utoken+",sb="+sb.toString()+",surl="+surl);
                            ResponseWithTokenWithErrInfo(response,sb.toString());
                            return;
                        }
                    } else {
                        LogHelper.WriteSysLog("生成Token失败,未携带有效ctoken标识,surl="+surl);
                        if(!IsWhiteListFunc(request)) {
                            ResponseNoTokenInfo(response);
                            return;
                        }
                    }
                }
            }
            chain.doFilter(request, response);
        }catch (Exception Ex)
        {
            LogHelper.WriteSysLog("系统验证token时产生异常:"+Ex.getMessage()+",surl="+surl);
            return;
        }
    }

    private boolean IsWhiteListFunc(HttpServletRequest request)
    {
        List<String> excludes = new ArrayList<String>();
        String whitelist=ConfigHelper.getWhiteList();
        if (StringUtil.isNotEmpty(whitelist))
        {
            String[] url = whitelist.split(",");
            for (int i = 0; url != null && i < url.length; i++)
            {
                excludes.add(url[i]);
            }
        }

        if (excludes == null || excludes.isEmpty())
        {
            return true;
        }
        String url = request.getServletPath();
        for (String pattern : excludes)
        {
            AntPathMatcher matcher = new AntPathMatcher();
            if(matcher.match(pattern,url))
            {
                return true;
            }
        }
        return false;
    }


    //隐含自动登录，适合手机APP登录情况
    public String ImplicateAutoLogin(HttpServletRequest request,String utoken,StringBuffer sb)
    {
        SysLoginService loginService=null;
        TokenService tokenService=null;
        String token="";
        try {
            if(loginService==null)
            {
                loginService=(SysLoginService)SpringUtil.getBean("sysLoginService");
            }
            if(tokenService==null)
            {
                tokenService=(TokenService) SpringUtil.getBean("tokenService");
            }

            if(loginService==null||tokenService==null)
            {
                sb.append("未能定位授权对象！");
                return null;
            }

            token=utoken;
            //String shalecode=ConfigHelper.getShakeCode();
            //token=wppService.GetRealToken(shalecode,utoken);
            if(StringUtil.IsNullOrEmpty(utoken))
            {
                sb.append("未携带有效token信息！");
                return null;
            }

            String dtstr= MetaHelper.GetDateString(new Date(),false);
            String strsql="select * from Person a inner join NFT_SESSION b on b.LOGIN_NAME=a.LoginName where b.SESSION_DATE='"+dtstr+"' and b.SESSION_ID='"+utoken+"'";
            if(!StringUtil.IsNullOrEmpty(strsql)) {
                strsql= AesHelper.aesEncodeCBC(strsql);
            }
            Person person=WpServiceHelper.GetPersonBySql(strsql);
            if(person==null)
            {
                sb.append("获取用户信息失败！");
                return null;
            }

            String loginname=person.getLoginName().toString();
            String md5pwd=person.getPassword().toString();
            if(StringUtils.isEmpty(loginname) || StringUtils.isEmpty(md5pwd)){
                sb.append("携带参数异常！");
                return null;
            }else if(loginname=="XXXXX")
            {
                sb.append("用户已经退出登录！");
                return null;
            }

            loginService.loginAPP(request,token,loginname,md5pwd);

            return token;
        }catch(Exception ex)
        {
            sb.append("系统异常:"+ex.getMessage());
            return null;
        }
    }

    public void ResponseNoTokenInfo(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult=AjaxResult.error("检测到当前账户没有登录，终止操作",-1,true);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    public void ResponseWithTokenWithErrInfo(HttpServletResponse response,String err) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult=AjaxResult.error(err,-1,true);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }

    public void ResponseInValidTokenInfo(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter out = null;
        try {
            out = response.getWriter();
            AjaxResult ajaxResult=AjaxResult.error("检测到当前账户在其他地方登录！",-1,true);
            out.append(JSON.toJSON(ajaxResult).toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
}
