package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.wpbase.entity.Role;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmUserRole")
public class CMUserRoleController {

    @RequestMapping("/GetUserRoleList_Y")
    @ResponseBody
    public AjaxResult GetUserRoleList_Y(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String UserId = request.getParameter("UserId");
        String RoleKind = request.getParameter("RoleKind");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select a.* from Role a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(RoleKind)&&!RoleKind.equals("0")){
                strsql += " and a.RoleKind='"+RoleKind+"'";
            }
            if (!StringUtil.IsNullOrEmpty(RoleMark)) {
                strsql += " and a.RoleName like '"+RoleMark+"'";
            }
            strsql += " and a.id in(select RoleId from RolePerson where personId='"+UserId+"')";

            List<Role> _plist = sqlhelper.GetObjectList(Role.class, strsql);
            ajaxResult = AjaxResult.extgrid(Role.class, _plist.size(), _plist);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/GetUserRoleList_W")
    @ResponseBody
    public AjaxResult GetUserRoleList_W(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = starts.equals("") ? 0 : Integer.parseInt(starts);
        int limit = starts.equals("") ? 20 : Integer.parseInt(limits);

        String UserId = request.getParameter("UserId");
        String RoleKind = request.getParameter("RoleKind");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        String basesql = "";
        String orderstr = "";
        String msgstr = "";
        int rcount = 0;

        try {
            SqlHelper sqlhelper = new SqlHelper();

            basesql = "select a.* from Role a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(RoleKind)&&!RoleKind.equals("0")){
                basesql += " and a.RoleKind='"+RoleKind+"'";
            }
            if (!StringUtil.IsNullOrEmpty(RoleMark)) {
                basesql += " and a.RoleName like '"+RoleMark+"'";
            }
            basesql += " and a.id not in(select RoleId from RolePerson where personId='"+UserId+"')";
            orderstr = " order by a.RoleName asc";

            String rcsql = DBHelper.ToRecordCountSql(basesql);
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql, orderstr, limit, currpage);

            List<Role> _plist = sqlhelper.GetObjectList(Role.class, strsql);
            ajaxResult = AjaxResult.extgrid(Role.class, rcount, _plist);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/AddUserRight")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult AddUserRight(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId)|| StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "insert into RolePerson(RoleId,personId) values(" + RoleId + "," + UserId + ")";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("权限设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/DeleteUserRight")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult DeleteUserRight(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId)|| StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "delete from RolePerson where RoleId='" + RoleId + "' and personId='" + UserId + "'";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("权限删除成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
