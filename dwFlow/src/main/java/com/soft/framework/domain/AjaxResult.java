package com.soft.framework.domain;

import com.soft.framework.common.constant.HttpStatus;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.MetaHelper;
import com.yyszc.extend.DataTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 操作消息提醒
 * 
 * <AUTHOR>
 */
public class AjaxResult extends HashMap<String, Object>
{
    private static final Logger logger = LoggerFactory.getLogger(AjaxResult.class);

    private static final long serialVersionUID = 1L;

    /** 状态码 */
    public static final String CODE_TAG = "code";

    /** 状态码 */
    public static final String STATUS_TAG = "success";

    /** 返回内容 */
    public static final String MSG_TAG = "text";

    /** 数据对象 */
    public static final String DATA_TAG = "data";

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    public AjaxResult()
    {
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     * 
     * @param code 状态码
     * @param msg 返回内容
     */
    public AjaxResult(int code, String msg)
    {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     * 
     * @param code 状态码
     * @param msg 返回内容
     * @param data 数据对象
     */
    public AjaxResult(int code, String msg, Object data)
    {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
        if (StringUtil.isNotNull(data))
        {
            super.put(DATA_TAG, data);
        }
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param status 状态
     * @param msg 返回内容
     * @param data 数据对象
     */
    public AjaxResult(int code,boolean status, String msg, Object data)
    {
        super.put(CODE_TAG, code);
        super.put(STATUS_TAG, status);
        super.put(MSG_TAG, msg);
        if (StringUtil.isNotNull(data))
        {
            super.put(DATA_TAG, data);
        }
    }

    /**
     * 返回成功消息
     * 
     * @return 成功消息
     */
    public static AjaxResult success()
    {
        return AjaxResult.success("操作成功");
    }

    /**
     * 返回成功数据
     * 
     * @return 成功消息
     */
    public static AjaxResult success(Object data)
    {
        return AjaxResult.success("操作成功", data);
    }

    /**
     * 返回成功消息
     * 
     * @param msg 返回内容
     * @return 成功消息
     */
    public static AjaxResult success(String msg)
    {
        return AjaxResult.success(msg, null);
    }

    /**
     * 返回成功消息
     * 
     * @param msg 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static AjaxResult success(String msg, Object data)
    {
        if(msg==null){
            return AjaxResult.success("处理成功！", null);
        }else {
            logger.info(msg);
            return new AjaxResult(HttpStatus.SUCCESS,true,msg, data);
        }
    }

    /**
     * 返回错误消息
     * 
     * @return
     */
    public static AjaxResult error()
    {
        return AjaxResult.error("操作失败");
    }

    /**
     * 返回错误消息
     * 
     * @param msg 返回内容
     * @return 警告消息
     */
    public static AjaxResult error(String msg)
    {
        if(msg==null){
            return AjaxResult.error("未知错误！", null);
        }else {
            logger.info(msg);
            msg = MetaHelper.ToJsonString(msg);
            return AjaxResult.error(msg, null);
        }
    }

    /**
     * 返回错误消息
     * 
     * @param msg 返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static AjaxResult error(String msg, Object data)
    {
        if(msg==null){
            return AjaxResult.error("未知错误！", null);
        }else {
            msg= MetaHelper.ToJsonString(msg);
            logger.info(msg);
            return new AjaxResult(HttpStatus.ERROR,false, msg, data);
        }
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @param status 状态标志
     * @return 警告消息
     */
    public static AjaxResult error(String msg,int status)
    {
        if(msg==null){
            return AjaxResult.error("未知错误！", null);
        }else {
            msg= MetaHelper.ToJsonString(msg);
            logger.info(msg);
            AjaxResult ajaxResult=new AjaxResult(HttpStatus.ERROR,msg);
            ajaxResult.put("status",status);
            return ajaxResult;
        }
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @param status 状态标志
     * @return 警告消息
     */
    public static AjaxResult error(String msg,int status,boolean needrl)
    {
        if(msg==null){
            return AjaxResult.error("未知错误！", null);
        }else {
            msg= MetaHelper.ToJsonString(msg);
            logger.info(msg);
            AjaxResult ajaxResult=new AjaxResult(HttpStatus.ERROR,msg);
            ajaxResult.put("status",status);
            ajaxResult.put("needrl",needrl);
            return ajaxResult;
        }
    }


    /**
     * 返回Grid消息
     *
     * @param recordcount 记录条目
     * @param list 列表内容
     * @return 警告消息
     */
    public static <T> AjaxResult extgrid(Class<T> cl,int recordcount, List<T> list)
    {
        AjaxResult ajax=new AjaxResult(HttpStatus.SUCCESS,true,"获取数据成功","");
        ajax.put("RecordCount",recordcount);
        ajax.put("Table",list);
        return ajax;
    }

    /**
     * 返回Grid消息
     *
     * @param recordcount 记录条目
     * @param list 列表内容
     * @return 警告消息
     */
    public static <T> AjaxResult extgridEasyUI(Class<T> cl,int recordcount, List<T> list)
    {
        AjaxResult ajax=new AjaxResult(HttpStatus.SUCCESS,true,"获取数据成功","");
        ajax.put("total",recordcount);
        ajax.put("rows",list);
        return ajax;
    }

    /**
     * 返回Grid消息
     *
     * @param dt 数据库表
     * @return 警告消息
     */
    public static String extgrid(DataTable dt)
    {
        StringBuilder json=new StringBuilder();
        json.append("{");
        json.append("\""+CODE_TAG+"\":200,");
        json.append("\""+STATUS_TAG+"\":true,");
        json.append("\""+MSG_TAG+"\":\"获取数据成功\",");
        json.append("\"RecordCount\":"+dt.getRowList().size()+",");

        StringBuilder jsonData=new StringBuilder();
        int rlen=dt.getRowList().size();
        int clen=dt.getColList().size();

        jsonData.append("[");
        for (int i = 0; i < rlen; i++)
        {
            jsonData.append("{");
            for (int j = 0; j < clen; j++)
            {
                jsonData.append("\"");
                jsonData.append(dt.getRow(i).getColumn(j).getColumnName());
                jsonData.append("\":\"");
                jsonData.append(dt.getCell(i,j).toString());
                if(j!=clen-1)
                {
                    jsonData.append("\",");
                }else
                {
                    jsonData.append("\"");
                }
            }
            if(i!=rlen-1){
                jsonData.append("},");
            }else
            {
                jsonData.append("}");
            }

        }
        jsonData.append("]");

        json.append("\"Table\":"+jsonData.toString());
        json.append("}");

        return json.toString();
    }

    /**
     * 返回Grid消息
     *
     * @param dt 数据库表
     * @return 警告消息
     */
    public static String extgrid(DataTable dt,int recount)
    {
        StringBuilder json=new StringBuilder();
        json.append("{");
        json.append("\""+CODE_TAG+"\":200,");
        json.append("\""+STATUS_TAG+"\":true,");
        json.append("\""+MSG_TAG+"\":\"获取数据成功\",");
        json.append("\"RecordCount\":"+recount+",");

        StringBuilder jsonData=new StringBuilder();
        int rlen=dt.getRowList().size();
        int clen=dt.getColList().size();

        jsonData.append("[");
        for (int i = 0; i < rlen; i++)
        {
            jsonData.append("{");
            for (int j = 0; j < clen; j++)
            {
                jsonData.append("\"");
                jsonData.append(dt.getRow(i).getColumn(j).getColumnName());
                jsonData.append("\":\"");
                jsonData.append(dt.getCell(i,j).toString());
                if(j!=clen-1)
                {
                    jsonData.append("\",");
                }else
                {
                    jsonData.append("\"");
                }
            }
            if(i!=rlen-1){
                jsonData.append("},");
            }else
            {
                jsonData.append("}");
            }

        }
        jsonData.append("]");

        json.append("\"Table\":"+jsonData.toString());
        json.append("}");

        return json.toString();
    }

    /**
     * 返回Form消息
     *
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T> AjaxResult extform(Class<T> cl,String msg,T data)
    {
        AjaxResult ajax=new AjaxResult(HttpStatus.SUCCESS,true,msg,null);
        if(ConfigHelper.getExtVersion().equals("3.2"))
        {
            List<T> list=new ArrayList<T>();
            list.add(data);

            ajax.put("data",list);
        }else
        {
            ajax.put("data",data);
        }
        return ajax;
    }

    /**
     * 返回Form消息
     *
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T,L> AjaxResult extform(Class<T> ct,Class<L> cl,String msg,T data,L meta)
    {
        AjaxResult ajax=new AjaxResult(HttpStatus.SUCCESS,true,msg,null);
        if(ConfigHelper.getExtVersion().equals("3.2"))
        {
            List<T> list=new ArrayList<T>();
            list.add(data);
            ajax.put("data",list);

            List<L> _list=new ArrayList<L>();
            _list.add(meta);
            ajax.put("meta",_list);
        }else
        {
            ajax.put("data",data);
            ajax.put("meta",meta);
        }
        return ajax;
    }


    /**
     * 返回Form消息
     *
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T,L,K> AjaxResult extform(Class<T> ct,Class<L> cl,Class<K> ck,String msg,T data,L meta,K peta)
    {
        AjaxResult ajax=new AjaxResult(HttpStatus.SUCCESS,true,msg,null);
        if(ConfigHelper.getExtVersion().equals("3.2"))
        {
            List<T> list=new ArrayList<T>();
            list.add(data);
            ajax.put("data",list);

            List<L> _list=new ArrayList<L>();
            _list.add(meta);
            ajax.put("meta",_list);

            List<K> __list=new ArrayList<K>();
            __list.add(peta);
            ajax.put("peta",__list);
        }else
        {
            ajax.put("data",data);
            ajax.put("meta",meta);
            ajax.put("peta",peta);
        }
        return ajax;
    }

    /**
     * 返回Form消息
     *
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T,L,K> AjaxResult extform(Class<T> ct,Class<L> cl,Class<K> ck,String msg,T data,L meta,List<K> peta)
    {
        AjaxResult ajax=new AjaxResult(HttpStatus.SUCCESS,true,msg,null);
        if(ConfigHelper.getExtVersion().equals("3.2"))
        {
            List<T> list=new ArrayList<T>();
            list.add(data);
            ajax.put("data",list);

            List<L> _list=new ArrayList<L>();
            _list.add(meta);
            ajax.put("meta",_list);

            ajax.put("peta",peta);
        }else
        {
            ajax.put("data",data);
            ajax.put("meta",meta);
            ajax.put("peta",peta);
        }
        return ajax;
    }

    /**
     * 返回Form消息
     *
     * @param dt 数据库表
     * @return 警告消息
     */
    public static String extform(DataTable dt)
    {
        StringBuilder json=new StringBuilder();
        json.append("{");
        json.append("\""+CODE_TAG+"\":200,");
        json.append("\""+STATUS_TAG+"\":true,");
        json.append("\""+MSG_TAG+"\":\"获取数据成功\",");

        StringBuilder jsonData=new StringBuilder();
        int rlen=dt.getRowList().size();
        int clen=dt.getColList().size();

        for (int i = 0; i < rlen; i++)
        {
            jsonData.append("{");
            for (int j = 0; j < clen; j++)
            {
                jsonData.append("\"");
                jsonData.append(dt.getRow(i).getColumn(j).getColumnName());
                jsonData.append("\":\"");
                jsonData.append(dt.getCell(i,j).toString());
                if(j!=clen-1)
                {
                    jsonData.append("\",");
                }else
                {
                    jsonData.append("\"");
                }
            }
            if(i!=rlen-1){
                jsonData.append("},");
            }else
            {
                jsonData.append("}");
            }
        }

        if(ConfigHelper.getExtVersion().equals("3.2"))
        {
            json.append("\"data\":"+"["+jsonData.toString()+"]");
        }else
        {
            json.append("\"data\":"+jsonData.toString());
        }
        json.append("}");

        return json.toString();
    }

    /**
     * 返回错误消息
     * 
     * @param code 状态码
     * @param msg 返回内容
     * @return 警告消息
     */
    public static AjaxResult error(int code, String msg)
    {
        return new AjaxResult(code, msg, null);
    }
}
