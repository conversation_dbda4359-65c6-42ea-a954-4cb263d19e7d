package com.soft.framework.helper;

import com.yyszc.extend.DataTable;

public class ResToolHelper {
    public static String ToGridPanel(DataTable dt)
    {
        return ToGridPanel(dt,-1);
    }

    public static String ToGridPanel(DataTable dt,int recordCount)
    {
        StringBuilder jsonData = new StringBuilder();

        if (recordCount < 0)
        {
            recordCount = dt.getRowList().size();
        }

        jsonData.append("{\"RecordCount\":\"");
        jsonData.append(recordCount);
        jsonData.append("\",\"Table\":"+dt.toJsonString());
        jsonData.append("}");

        return jsonData.toString();

    }
}
