package com.soft.framework.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.soft.framework.common.features.MSSqlFeatures;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.date.DateUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.*;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class DBHelper {
    private static final Logger logger = LoggerFactory.getLogger(DBHelper.class);

    private static final String TabSchema = ConfigHelper.getDBSchema();

    public static List<NFT_SQL_IDSTR> GetPreventSqlList() {
        return WpServiceHelper.GetPreventSqlList();
    }

    public static List<NFT_NWIPD> GetNwIpdList() {
        return WpServiceHelper.GetNwIpdList();
    }

    public static List<NFT_WhiteList> GetNftWhiteList() {
        return WpServiceHelper.GetNftWhiteList();
    }

    public static DataTable GetUserNavTable(String mid,int userid,int pwf) {
        return WpServiceHelper.GetUserNavTable(mid,userid,pwf);
    }

    public static List<vFunctionItem> GetUserFunctionList(String mid, int userid, int pwf) {
        return WpServiceHelper.GetUserFunctionList(mid,userid,pwf);
    }

    public static String GetUserNavMenuGroup(String mid,int userid,int pwf) {
        return WpServiceHelper.GetUserNavMenuGroup(mid,userid,pwf);
    }

    /// <summary>
    /// 获取实体的插入语句
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetInsertSQL(T obj, String tableName, List<String> idlist){
        String name="";
        try {
            StringBuilder strSQL = new StringBuilder();
            strSQL.append("insert into " + tableName + "(");

            String fields = "";
            String values = "";

            Field[] fieldArr = obj.getClass().getDeclaredFields();
            for (Field pi : fieldArr) {
                pi.setAccessible(true);
                name = pi.getName();
                if (name == "emptyChangingEventArgs") {
                    continue;
                }
                if (idlist.size() > 0 && idlist.indexOf(name) >= 0) {
                    continue;
                }

                if (!FieldIsExists(tableName, name.toString())) {
                    continue;
                }
                Object val = pi.get(obj);
                if(val==null) {
                    continue;
                }

                if (pi.getType().equals(String.class)) {
                    String value = ((val==null)?"":val.toString()).replace("'", "''");
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += "'" + value + "',";
                    }
                } else if (
                        pi.getType().equals(Integer.class)||pi.getType().equals(Boolean.class)||
                                pi.getType().equals(Long.class)) {
                    String value = (val==null)?"null":val.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                } else if (
                        pi.getType().equals(Float.class)||pi.getType().equals(Double.class)||
                                pi.getType().equals(BigDecimal.class)) {
                    BigDecimal bg=new BigDecimal(pi.get(obj)+"");
                    String value = (bg==null)?"null":bg.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                }else if (pi.getType().equals(Date.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str((Date) val, DateUtil.datetimeFormat);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDateTime.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str(DateUtil.toDate((LocalDateTime) val), DateUtil.datetimeFormatf);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDate.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.toDateStr((LocalDate) val);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }
            }

            // 去掉最后一个,
            fields = fields.substring(0, fields.length() - 1);
            values = values.substring(0, values.length() - 1);

            // 拼接Sql串
            strSQL.append(fields);
            strSQL.append(") values (");
            strSQL.append(values);
            strSQL.append(")");

            strSQL.append(";select dbo.FUNC_IDENT_CURR(SCOPE_IDENTITY(),'" + tableName + "');");

            return strSQL.toString();
        }catch(Exception Ex)
        {
            return "";
        }
    }

    /// <summary>
    /// 获取实体的插入语句
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetInsertSQL(T obj, String tableName, List<String> idlist, List<String> EFields){
        String name="";
        try {
            StringBuilder strSQL = new StringBuilder();
            strSQL.append("insert into " + tableName + "(");

            String fields = "";
            String values = "";

            Field[] fieldArr = obj.getClass().getDeclaredFields();
            for (Field pi : fieldArr) {
                pi.setAccessible(true);
                name = pi.getName();
                if (name == "emptyChangingEventArgs") {
                    continue;
                }
                if (idlist.size() > 0 && idlist.indexOf(name) >= 0) {
                    continue;
                }
                if (EFields!=null&&EFields.size()>0)
                {
                    if(EFields.indexOf(name)>=0) {
                        continue;
                    }
                }

                if (!FieldIsExists(tableName, name.toString())) {
                    continue;
                }
                Object val = pi.get(obj);
                if(val==null) {
                    continue;
                }

                if (pi.getType().equals(String.class)) {
                    String value = ((val==null)?"":val.toString()).replace("'", "''");
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += "'" + value + "',";
                    }
                } else if (
                        pi.getType().equals(Integer.class)||pi.getType().equals(Boolean.class)||
                                pi.getType().equals(Long.class)) {
                    String value = (val==null)?"null":val.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                } else if (
                        pi.getType().equals(Float.class)||pi.getType().equals(Double.class)||
                                pi.getType().equals(BigDecimal.class)) {
                    BigDecimal bg=new BigDecimal(pi.get(obj)+"");
                    String value = (bg==null)?"null":bg.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        fields += name + ",";
                        values += value + ",";
                    }
                }else if (pi.getType().equals(Date.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str((Date) val, DateUtil.datetimeFormat);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDateTime.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str(DateUtil.toDate((LocalDateTime) val), DateUtil.datetimeFormatf);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }else if (pi.getType().equals(LocalDate.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.toDateStr((LocalDate) val);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            fields += name + ",";
                            values += value + ",";
                        }
                    }
                }
            }

            // 去掉最后一个,
            fields = fields.substring(0, fields.length() - 1);
            values = values.substring(0, values.length() - 1);

            // 拼接Sql串
            strSQL.append(fields);
            strSQL.append(") values (");
            strSQL.append(values);
            strSQL.append(")");

            strSQL.append(";select dbo.FUNC_IDENT_CURR(SCOPE_IDENTITY(),'" + tableName + "');");

            return strSQL.toString();
        }catch(Exception Ex)
        {
            return "";
        }
    }


    /// <summary>
    /// 获取实体的更新SQL串
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetUpdateSQL(T obj, String tableName, List<String> tableKey, List<String> keyValue) {
        return GetUpdateSQL(obj,tableName,tableKey,keyValue,null,null);
    }


    /// <summary>
    /// 获取实体的更新SQL串
    /// </summary>
    /// <typeparam name="T">泛型</typeparam>
    /// <param name="obj">实体对象</param>
    /// <returns>返回插入语句</returns>
    public static <T> String GetUpdateSQL(T obj, String tableName, List<String> tableKey, List<String> keyValue, List<String> OFields, List<String> EFields) {
        String name="";
        try {
            StringBuilder strSQL = new StringBuilder();

            if (tableKey.size() <= 0 || keyValue.size() <= 0 || tableKey.size() != keyValue.size()) {
                return "";
            }

            String subSQL = "";
            String condition = "";
            strSQL.append("update " + tableName + " set ");
            for (int i = 0; i < tableKey.size(); i++) {
                if (condition == "") {
                    condition = tableKey.get(i).toString() + "='" + keyValue.get(i).toString().replace("'", "''") + "'";
                } else {
                    condition = condition + " and " + tableKey.get(i).toString() + "='" + keyValue.get(i).toString().replace("'", "''") + "'";
                }
            }
            condition = " where " + condition;

            Field[] fieldArr = obj.getClass().getDeclaredFields();
            for (Field pi : fieldArr) {
                pi.setAccessible(true);
                name = pi.getName();

                if (name == "emptyChangingEventArgs") {
                    continue;
                }
                if (tableKey.indexOf(name) >= 0) {
                    continue;
                }
                if (OFields!=null&&OFields.size()>0)
                {
                    if(OFields.indexOf(name)<0) {
                        continue;
                    }
                }
                if (EFields!=null&&EFields.size()>0)
                {
                    if(EFields.indexOf(name)>=0) {
                        continue;
                    }
                }

                if (!FieldIsExists(tableName, name.toString())) {
                    continue;
                }
                Object val = pi.get(obj);

                if (pi.getType().equals(String.class)) {
                    String value = ((val==null)?"":val.toString()).replace("'", "''");
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        subSQL += name + "='" + value + "',";
                    } else {
                        if (val == null) {
                            subSQL += name + "=null,";
                        } else {
                            subSQL += name + "='',";
                        }
                    }
                }else if (
                        pi.getType().equals(Integer.class)||pi.getType().equals(Boolean.class)||
                                pi.getType().equals(Long.class)){
                    String value = (val==null)?"":val.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        subSQL += name + "='" + value + "',";
                    } else {
                        subSQL += name + "=null,";
                    }
                }else if (
                        pi.getType().equals(Float.class)||pi.getType().equals(Double.class)||
                                pi.getType().equals(BigDecimal.class)) {
                    BigDecimal bg=null;
                    if(val!=null) {
                        bg=new BigDecimal(pi.get(obj)+"");
                    }
                    String value = (bg==null)?"":bg.toString();
                    if (!StringUtil.IsNullOrEmpty(value)) {
                        subSQL += name + "='" + value + "',";
                    } else {
                        subSQL += name + "=null,";
                    }
                }else if (pi.getType().equals(Date.class)){
                    if(val!=null) {
                        String valstr = DateUtil.date2Str((Date) val, DateUtil.datetimeFormat);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            subSQL += name + "=" + value + ",";
                        } else {
                            subSQL += name + "=null,";
                        }
                    }
                }else if (pi.getType().equals(LocalDateTime.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.date2Str(DateUtil.toDate((LocalDateTime) val), DateUtil.datetimeFormatf);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            subSQL += name + "=" + value + ",";
                        } else {
                            subSQL += name + "=null,";
                        }
                    }
                }else if (pi.getType().equals(LocalDate.class)) {
                    if(val!=null) {
                        String valstr = DateUtil.toDateStr((LocalDate) val);
                        MSSqlFeatures dbFeatures = new MSSqlFeatures();
                        String value = dbFeatures.FormatStrToDate(valstr);

                        if (!StringUtil.IsNullOrEmpty(value)) {
                            subSQL += name + "=" + value + ",";
                        } else {
                            subSQL += name + "=null,";
                        }
                    }
                }
            }

            // 去掉最后一个,
            subSQL = subSQL.substring(0, subSQL.length() - 1);

            // 拼接上更新子句
            strSQL.append(subSQL);

            // 加入更新条件
            strSQL.append(condition);

            return strSQL.toString();
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static boolean FieldIsExists(String tableN, String fieldN) {
        SqlHelper sqlhelper = new SqlHelper();
        String strR = sqlhelper.GetColType(TabSchema, tableN, fieldN);
        if (strR == "" || strR == null) {
            return false;
        } else {
            return true;
        }
    }

    public static String ToRecordCountSql(String sql)
    {
        String strSql = "select count(*) from (" + sql + " ) tab ";
        return strSql;
    }

    public static void WriteHandLogInfo(String hlmark, String msgstr, int percent)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";
        try
        {
            strsql = "insert into CPS_T_HAND_LOG(HL_MARK,HL_INFO,HL_RATE,HL_DATE) values('" + hlmark + "','" + msgstr + "'," + percent + ",getdate())";
            sqlhelper.ExecuteNoQuery(strsql);
        }
        catch (Exception Ex)
        {
        }
        return;
    }

    public static void ClearHandLogInfo(String hlmark)
    {
        SqlHelper sqlhelper = new SqlHelper();
        String strsql = "";
        try
        {
            strsql = "delete from CPS_T_HAND_LOG where HL_MARK='" + hlmark + "'";
            sqlhelper.ExecuteNoQuery(strsql);
        }
        catch (Exception Ex)
        {
        }
        return;
    }

    public static void GetHandLogInfo_ws(String hlmark, StringBuilder msginfo)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try
        {
            String strsql = "select * from CPS_T_HAND_LOG where HL_MARK='" + hlmark + "' order by HL_DATE asc;";
            DataTable tmpdt = sqlhelper.GetDataTable(strsql);
            for (DataRow tmpdr:tmpdt.getRowList()) {
                msginfo.append(tmpdr.getColumn("HL_INFO").toString() + "\n");
            }
        }
        catch (Exception Ex)
        {

        }
    }

    public static void GetHandLogInfo(String hlmark,StringBuilder msginfo,StringBuilder percent)
    {
        SqlHelper sqlhelper = new SqlHelper();
        try
        {
            DataTable tmpdt = null;

            String strsql = "select * from CPS_T_HAND_LOG where HL_MARK='" + hlmark + "' order by HL_DATE asc;";
            tmpdt = sqlhelper.GetDataTable(strsql);

            if (tmpdt.getTotalCount()> 0)
            {
                percent.append(tmpdt.getRow(tmpdt.getTotalCount()-1).getColValue("HL_RATE").toString());
            }
            for (DataRow tmpdr:tmpdt.getRowList()) {
                String tmpstr = tmpdr.getColValue("HL_INFO").toString();
                if (!tmpstr.endsWith("\n")) {
                    msginfo.append(tmpstr + "\n");
                } else {
                    msginfo.append(tmpstr);
                }
            }
        }
        catch (Exception Ex)
        {

        }
    }

    public static List<PersonEntity> GetRolePersonList(int roleid)
    {
        return WpServiceHelper.GetRolePersonList(roleid);
    }

    public static List<PersonEntity> GetRolePersonList(int roleid, int topGroupId)
    {
        return WpServiceHelper.GetCompRolePersonList(roleid, topGroupId);
    }

    public static List<PersonEntity> GetPermissionPersonList(String permission)
    {
        return WpServiceHelper.GetPermissionPersonList(permission);
    }

    public static List<PersonEntity> GetPermissionPersonList(String permission, int topGroupId)
    {
        return WpServiceHelper.GetCompPermissionPersonList(permission,topGroupId);
    }

    public static List<vComp> GetAllTopGroup(int mid)
    {
        return WpServiceHelper.GetAllTopGroup(mid);
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static PersonEntity GetPersonByLoginName(String loginName)
    {
        return WpServiceHelper.GetPersonByLoginName(loginName);
    }


    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static PersonEntity GetPersonByUserId(int uid)
    {
        return WpServiceHelper.GetPersonByUserId(uid);
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static vPerson GetVPersonByLoginName_S(String loginName)
    {
        return WpServiceHelper.GetVPersonByLoginName_S(loginName);
    }


    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static vPerson GetVPersonByUserId_S(int uid)
    {
        return WpServiceHelper.GetVPersonByUserId_S(uid);
    }

    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static Person GetPersonByLoginName_S(String loginName)
    {
        return WpServiceHelper.GetPersonByLoginName_S(loginName);
    }


    /// <summary>
    /// 根据用户LoginName获取一条用户数据
    /// </summary>
    /// <param name="Id">用户Id</param>
    public static Person GetPersonByUserId_S(int uid)
    {
        return WpServiceHelper.GetPersonByUserId_S(uid);
    }


    public static void setValue(Field f,String coltype,String valtype, Object colvalue, Object row) {
        if (colvalue != null && !"".equals(colvalue)) {
            try {
                if ("String".equals(coltype)||"string".equals(coltype)) {
                    f.set(row,colvalue.toString());
                }else if ("Short".equals(coltype)||"short".equals(coltype)) {
                    f.set(row,Short.parseShort(colvalue.toString()));
                } else if ("int".equals(coltype) || "Integer".equals(coltype)) {
                    f.set(row,Integer.parseInt(colvalue.toString()));
                } else if ("double".equals(coltype) || "Double".equals(coltype)) {
                    f.set(row,Double.parseDouble(colvalue.toString()));
                } else if ("float".equals(coltype) || "Float".equals(coltype)) {
                    f.set(row,Float.parseFloat(colvalue.toString()));
                } else if ("long".equals(coltype) || "Long".equals(coltype)) {
                    f.set(row,Long.parseLong(colvalue.toString()));
                } else if ("boolean".equals(coltype) || "Boolean".equals(coltype)) {
                    f.set(row,Boolean.parseBoolean(colvalue.toString()));
                } else if ("BigDecimal".equals(coltype)) {
                    f.set(row,new BigDecimal("" + colvalue));
                } else if ("Date".equals(coltype)|| "date".equals(coltype)) {
                    f.set(row, DateUtils.parseDate(colvalue.toString()));
                }else if ("TimeStamp".equals(coltype)|| "Timestamp".equals(coltype)) {
                    f.set(row, DateUtil.parseTimestamp(colvalue.toString(),"yyyy-MM-dd HH:mm:ss"));
                }else if("LocalDateTime".equals(coltype))
                {
                    if("TimeStamp".equals(valtype)|| "Timestamp".equals(valtype))
                    {
                        Timestamp time=(Timestamp)colvalue;
                        LocalDateTime ldt=time.toLocalDateTime();
                        f.set(row, ldt);
                    }else
                    {
                        f.set(row, LocalDateTime.parse(colvalue.toString()));
                    }
                }else if("LocalDate".equals(coltype))
                {
                    f.set(row, LocalDate.parse(colvalue.toString()));
                }else {
                    f.set(row, colvalue);
                }
            } catch (Exception ex) {
                LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            }
        }
    }

    public static void SendSms(String phone, String content)
    {
        WpServiceHelper.SendSms(phone,content);
    }

    public static int ExecuteSql(String strsql) {
        if(StringUtil.IsNullOrEmpty(strsql)) {
            return -1;
        }

        SqlHelper sqlhelper=new SqlHelper();
        int cont =-1;
        try
        {
            cont = sqlhelper.ExecuteNoQuery(strsql);
            return cont;
        }catch (Exception Ex)
        {
            return cont;
        }
    }

    public static DataTable GetChildFuntionItemByParent(int userid, int pwf,String gid)
    {
        return WpServiceHelper.GetChildFuntionItemByParent(userid,pwf,gid);
    }

    //调用邱雄用户名、密码验证端口，返回结果   mflag 参数，mflag=false 不进行在线测试
    public static qx_info get_yzMessage(String loginName, String password,String userip, String useragent)
    {
        try
        {
            if (!ConfigHelper.getRunMode().equals("dev"))
            {
                password = AesHelper.assDecrypt(password, "nbsbdjsyxgsyykjf", "sbdjsyxgsyykjfgs");

                //**************
                String contentUrl = "https://ydyfwzx.yongyaokjit.com:8282/auth/oauth/api/check-login";
                CloseableHttpClient httpClient = HttpClientBuilder.create().build();

                HttpPost httpPost = new HttpPost(contentUrl);
                httpPost.setHeader("Authorization", "Basic NTBjY2E0Y2EtOTQwNy00NWRhLWFiMGQtYTdjMDNlMDk2OTkyOkQzMFJJKk16X2tu");

                List<NameValuePair> parameters=new ArrayList<NameValuePair>();
                parameters.add(new BasicNameValuePair("username", loginName));
                parameters.add(new BasicNameValuePair("password", password));
                parameters.add(new BasicNameValuePair("userip", userip));
                parameters.add(new BasicNameValuePair("useragent", useragent));
                httpPost.setEntity(new UrlEncodedFormEntity(parameters,"UTF-8"));

                HttpResponse httpResponse = httpClient.execute(httpPost);

                // 5、获取响应结果, 状态码 200 表示请求成功
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                System.out.println("响应状态码：" + statusCode);
                if (statusCode == 200) {
                    HttpEntity httpEntity = httpResponse.getEntity();
                    // 使用指定的字符编码解析响应消息实体
                    String feedback = EntityUtils.toString(httpEntity, "UTF-8");
                    logger.info(feedback);

                    JSONObject jsond= JSON.parseObject(feedback);

                    String contentd = jsond.getString("data");
                    qx_info entity = JSONObject.parseObject(contentd,qx_info.class);
                    return entity;
                }else
                {
                    qx_info entity = new qx_info();
                    entity.setPasswordError("");
                    entity.setUsernameSuccess(true);
                    entity.setUsernameError("");
                    return entity;
                }
            }
            else
            {
                qx_info entity = new qx_info();
                entity.setPasswordError("");
                entity.setUsernameSuccess(true);
                entity.setUsernameError("");
                return entity;
            }
        }
        catch(Exception Ex)
        {
            return null;
        }
    }
}
