package com.soft.framework.helper;

import com.yyszc.wpbase.entity.Lc_currentState;
import com.yyszc.wpbase.entity.Lc_workFlow;
import com.yyszc.wpbase.entity.Lcdefine;
import com.yyszc.wpbase.entity.Lcjd;

import java.util.ArrayList;
import java.util.List;

public class WrokHelpHelper {
    private SqlHelper sqlhelper = new SqlHelper();
    public List<Lcjd> listLcjd = new ArrayList<Lcjd>();
    public List<Lcdefine> listLcDefine = new ArrayList<Lcdefine>();
    int endLcjd = 0;

    public WrokHelpHelper(int lcid, int endlcjd)
    {
        listLcDefine = sqlhelper.GetObjectList(Lcdefine.class,"select * from Lcdefine where lcID='"+lcid+"'");
        listLcjd = sqlhelper.GetObjectList(Lcjd.class,"select * from Lcjd where lc_defineID='"+lcid+"'");
        endLcjd = endlcjd;
    }

    public Lcjd getFirstLcjd(int lc_defineID)
    {
        Lcjd lc = ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&&q.getType()==1);
        return lc;
    }

    public Lcjd getFirstLcjd(int lc_defineID,int lcjdId)
    {
        Lcjd lc = ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&&q.getLcjdID()==lcjdId&&q.getType()==1);
        return lc;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid)
    {
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState2(int lc_defineID, int ywid, int lcjdid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and isnull(PNO,'')='" + pnostr + "' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and isnull(PNO,'')='" + pnostr + "' and sendPersonZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState2(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and isnull(PNO,'')='" + pnostr + "' and AllPersonZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, int lcjdid, String pesonZgh)
    {
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ "  and sendPersonZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, int lcjdid, String pesonZgh)
    {
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and personZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow2(int lc_defineID, int ywid, int lcjdid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "'  order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow3(int lc_defineID, int ywid, int lcjdid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and and PNO like '%" + pnostr + "%'   order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "'  order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }


    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' and personZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_workFlow getWorkFlow(int lc_defineID, int ywid, int lcjdid)
    {
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + "  order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Lc_currentState getCurrentLcState(int lc_defineID, int ywid, int lcjdid)
    {
        List<Lc_currentState> lists =sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where Lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdid=" + lcjdid+ " order by ID desc");
        if (lists.size() > 0)
        {
            return lists.get(0);
        }
        return null;
    }

    public Boolean checkWorkFlowExists(int lc_defineID, int ywid, int lcjdid)
    {
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkWorkFlowExists(int lc_defineID, int ywid, int lcjdid, String pesonZgh)
    {
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and personZgh like '%" + pesonZgh + "%'  order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkWorkFlowExists2(int lc_defineID, int ywid, int lcjdid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "'  order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkWorkFlowExists(int lc_defineID, int ywid, int lcjdid, String pno, String pesonZgh)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " and isnull(PNO,'')='" + pnostr + "' and personZgh like '%" + pesonZgh + "%' order by ID desc");
        if (lists.size() > 0)
        {
            if (lists.get(0).getTransdate()!= null) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    public Boolean checkStateExpPnoIsFinish(int lc_defineID, int ywid, int lcjdid, String pno)
    {
        Boolean retint = true;
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_currentState> lists = sqlhelper.GetObjectList(Lc_currentState.class,"select * from Lc_currentState where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and BXType='子线' and isnull(PNO,'')!='" + pnostr + "' order by ID desc");
        if (lists.size() > 0)
        {
            for (int i = 0; i < lists.size(); i++)
            {
                Lc_currentState cs = lists.get(i);
                if (cs.getLc_jdid()== lcjdid)
                {
                    retint = false;
                    break;
                }
            }
        }
        return retint;
    }


    public Lcjd getNextLcjd(int lc_defineID, Lcjd currentLcjd)
    {
        int nextID = currentLcjd.getNextID();
        Lcjd nextLc =ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&&q.getLcjdID()==nextID);
        return nextLc;
    }

    public Lcjd getCurrentLcjd(int lc_defineID, int lc_tojdid)
    {
        Lcjd currentLc=ToolHelper.getElement(listLcjd, q -> q.getLc_defineID() == lc_defineID&& q.getLcjdID() == lc_tojdid);
        return currentLc;
    }

    public List<Lc_workFlow> GetWorkFlows(int lc_defineID, int ywid)
    {
        List<Lc_workFlow> lists = null;
        lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + "  order by lc_jdID asc");

        return lists;
    }

    public List<Lc_workFlow> GetWorkFlows(int lc_defineID, int ywid, String pno)
    {
        String pnostr = (pno == null) ? "" : pno;
        List<Lc_workFlow> lists = null;
        lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and isnull(PNO,'')='" + pnostr + "' order by lc_jdID asc");
        return lists;
    }

    public List<Lc_workFlow> GetWorkFlows(int lc_defineID, int ywid, int lcjdid)
    {
        List<Lc_workFlow> lists = null;
        lists = sqlhelper.GetObjectList(Lc_workFlow.class,"select * from Lc_workFlow where lc_defineID=" + lc_defineID + " and ywID=" + ywid + " and lc_jdID=" + lcjdid + " order by id asc");
        return lists;
    }
}
