package com.soft.framework.config;

import com.soft.framework.filter.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.DispatcherType;
import java.util.HashMap;
import java.util.Map;

/**
 * Filter配置
 *
 * <AUTHOR>
 */
@Configuration
public class FilterConfig
{
    @Value("${xss.enabled}")
    private String xss_enabled;

    @Value("${xss.excludes}")
    private String xss_excludes;

    @Value("${xss.urlPatterns}")
    private String xss_urlPatterns;

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Bean
    public FilterRegistrationBean xssFilterRegistration()
    {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new XssFilter());
        registration.addUrlPatterns(StringUtils.split(xss_urlPatterns, ","));
        registration.setName("xssFilter");
        registration.setDispatcherTypes(DispatcherType.REQUEST,DispatcherType.ASYNC);
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        Map<String, String> initParameters = new HashMap<String, String>();
        initParameters.put("excludes", xss_excludes);
        initParameters.put("enabled", xss_enabled);
        registration.setInitParameters(initParameters);
        return registration;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Bean
    public FilterRegistrationBean RepeatableFilterRegistration()
    {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns("/Service/*");
        registration.setName("repeatableFilter");
        registration.setDispatcherTypes(DispatcherType.REQUEST,DispatcherType.ASYNC);
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }


    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Bean
    public FilterRegistrationBean ProxyFilterRegistration()
    {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new ProxyFilter());
        registration.addUrlPatterns("/Upload/*");
        registration.setName("proxyFilter");
        registration.setDispatcherTypes(DispatcherType.REQUEST,DispatcherType.ASYNC);
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    public FilterRegistrationBean filterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new CorsFilter());
        registration.addUrlPatterns("/*");
        registration.setName("corsFilter");
        registration.setDispatcherTypes(DispatcherType.REQUEST,DispatcherType.ASYNC);
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }
}
