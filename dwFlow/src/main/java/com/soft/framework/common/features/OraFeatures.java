package com.soft.framework.common.features;

/**
 * Created by Administrator on 2016/1/26.
 */
public class OraFeatures implements IDBFeatures {
    public String FormatDateToStr(int strlen, String colname) {
        String strret = colname;
        if (strlen == 8) {
            strret = "to_char(" + colname + ",'yyyymmdd') as " + colname;
        } else if (strlen == 10) {
            strret = "to_char(" + colname + ",'yyyy-mm-dd') as " + colname;
        } else if (strlen == 16) {
            strret = "to_char(" + colname + ",'yyyy-mm-dd hh24:mi') as " + colname;
        } else if (strlen == 19) {
            strret = "to_char(" + colname + ",'yyyy-mm-dd hh24:mi:ss') as " + colname;
        }
        return strret;
    }

    public String FormatStrToDate(String strval) {
        String strret = "null";
        int strlen=strval.length();
        if (strlen== 8) {
            strret = "to_date('" + strval + "','yyyymmdd')";
        } else if (strlen== 10) {
            strret = "to_date('" + strval + "','yyyy-mm-dd')";
        } else if (strlen== 16) {
            strret = "to_date('" + strval + "','yyyy-mm-dd hh24:mi')";
        } else if (strlen== 19) {
            strret = "to_date('" + strval + "','yyyy-mm-dd hh24:mi:ss')";
        }
        return strret;
    }

    public String GetColType(String schema,String strtab, String colname) {
        return "select data_type from user_tab_columns where table_name=upper('" + strtab + "') and column_name=upper('" + colname + "')";
    }

    public String ToPageSql(String strSql,String orderStr, int intPageSize, int intCurrentPage) {
        return "select * from (select rownum as row_num,a.* from (" + strSql+" "+orderStr+ ") a ) where row_num>" + ((intCurrentPage-1) * intPageSize) + " and row_num<=" + (intCurrentPage * intPageSize) + " ";
    }

    public String ToLimitSql(String strSql,String orderStr, int start, int end) {
        return "select * from (select rownum as row_num,a.* from (" + strSql+" "+orderStr+ ") a ) where row_num>" + start + " and row_num<=" + end + " ";
    }

    public String PackFunc(String funcname) {
        return funcname;
    }

    public String PackProc(String procname, String valstr) {
        return "begin " + procname + "(" + valstr + "); end;";
    }

    public String PackMetaQry(String sqlstr) {
        return sqlstr + " from dual";
    }

    public String PackTreeOrder(String orderstr) {
        return "";
    }

    public String GetDefaultDT() {
        return "sysdate";
    }
}
