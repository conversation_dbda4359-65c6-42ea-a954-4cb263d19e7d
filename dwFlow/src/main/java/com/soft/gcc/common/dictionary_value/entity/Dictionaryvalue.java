package com.soft.gcc.common.dictionary_value.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName DictionaryValue
 */
@TableName(value ="DictionaryValue")
@Data
public class Dictionaryvalue implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "TitleID")
    private Integer titleid;

    /**
     * 
     */
    @TableField(value = "Content")
    private String content;

    /**
     * 
     */
    @TableField(value = "Parameter")
    private String parameter;

    /**
     * 
     */
    @TableField(value = "IsUsed")
    private Integer isused;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Dictionaryvalue other = (Dictionaryvalue) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTitleid() == null ? other.getTitleid() == null : this.getTitleid().equals(other.getTitleid()))
            && (this.getContent() == null ? other.getContent() == null : this.getContent().equals(other.getContent()))
            && (this.getParameter() == null ? other.getParameter() == null : this.getParameter().equals(other.getParameter()))
            && (this.getIsused() == null ? other.getIsused() == null : this.getIsused().equals(other.getIsused()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTitleid() == null) ? 0 : getTitleid().hashCode());
        result = prime * result + ((getContent() == null) ? 0 : getContent().hashCode());
        result = prime * result + ((getParameter() == null) ? 0 : getParameter().hashCode());
        result = prime * result + ((getIsused() == null) ? 0 : getIsused().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", titleid=").append(titleid);
        sb.append(", content=").append(content);
        sb.append(", parameter=").append(parameter);
        sb.append(", isused=").append(isused);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
