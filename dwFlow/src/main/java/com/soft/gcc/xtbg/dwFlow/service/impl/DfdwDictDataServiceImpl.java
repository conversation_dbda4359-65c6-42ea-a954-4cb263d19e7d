package com.soft.gcc.xtbg.dwFlow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dwFlow.domain.DfdwDictData;
import com.soft.gcc.xtbg.dwFlow.service.DfdwDictDataService;
import com.soft.gcc.xtbg.dwFlow.mapper.DfdwDictDataMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DICT_DATA(东方多维-字典数据表)】的数据库操作Service实现
* @createDate 2024-12-16 15:53:07
*/
@Service
public class DfdwDictDataServiceImpl extends ServiceImpl<DfdwDictDataMapper, DfdwDictData>
    implements DfdwDictDataService{

}




