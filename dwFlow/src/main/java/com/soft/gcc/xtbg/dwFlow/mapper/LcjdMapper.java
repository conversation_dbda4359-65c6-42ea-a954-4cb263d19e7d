package com.soft.gcc.xtbg.dwFlow.mapper;

import com.soft.gcc.xtbg.dwFlow.domain.Lcjd;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dwFlow.dto.LcApprovePerson;
import com.yyszc.wpbase.entity.vPerson;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【Lcjd(流程节点)】的数据库操作Mapper
* @createDate 2024-12-16 10:39:35
* @Entity com.soft.gcc.xtbg.dwFlow.domain.Lcjd
*/
public interface LcjdMapper extends BaseMapper<Lcjd> {

    List<LcApprovePerson> getPerson(@Param("isShowAll") Integer isShowAll, @Param("applyUserId")Integer applyUserId
            , @Param("roleName")String roleName, @Param("chooseUse") Integer chooseUse);

    List<LcApprovePerson> getPersonByUserId( @Param("userId")Integer userId);

    PersonEntity getPersonById(@Param("sendPersonId") Integer sendPersonId);
}




