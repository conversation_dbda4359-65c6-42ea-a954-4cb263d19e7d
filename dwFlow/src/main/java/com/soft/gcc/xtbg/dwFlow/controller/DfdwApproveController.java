package com.soft.gcc.xtbg.dwFlow.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dwFlow.domain.DfdwDictData;
import com.soft.gcc.xtbg.dwFlow.domain.LcCondition;
import com.soft.gcc.xtbg.dwFlow.domain.LcCurrentstate;
import com.soft.gcc.xtbg.dwFlow.domain.Lcjd;
import com.soft.gcc.xtbg.dwFlow.dto.LcApprovePerson;
import com.soft.gcc.xtbg.dwFlow.dto.SubmitApproveDto;
import com.soft.gcc.xtbg.dwFlow.mapper.PublicSqlMapper;
import com.soft.gcc.xtbg.dwFlow.service.*;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @description 公共审批相关
 * @date 2024-12-16 10:24:22
 */
@RequestMapping("/flow/dfdwApprove")
@RestController
public class DfdwApproveController extends BaseController {


    @Autowired
    private LcdefineService lcdefineService;
    @Autowired
    private LcConditionService lcConditionService;
    @Autowired
    private LcCurrentstateService lcCurrentstateService;
    @Autowired
    private LcWorkflowService lcWorkflowService;
    @Autowired
    private LcjdService lcjdService;
    @Autowired
    private PublicSqlMapper publicSqlMapper;
    @Autowired
    private DfdwDictDataService dfdwDictDataService;
    @Autowired
    private IDwlcApproveService dwlcApproveService;


    /**
     * 获取审批人
     *
     * @param lcDefineID  流程定义Id
     * @param lcJdId      流程节点Id
     * @param ywId        业务id
     * @param applyUserId 申请人用户Id
     * @param isShowAll   部门权限控制，-1不做控制，0当前部门 ,1TopDeptId
     * @param initChoose  是否能选择自己 0不可以,  1可以
     * @return
     */
    @RequestMapping("/getNextPerson")
    @ResponseBody
    public Result<Object> getNextPersonForApp(@RequestParam("lcDefineID") Integer lcDefineID,
                                              @RequestParam("lcJdId") Integer lcJdId, @RequestParam("ywId") Integer ywId,
                                              @RequestParam("applyUserId") Integer applyUserId,
                                              @RequestParam(value = "isShowAll", required = false, defaultValue = "-1") Integer isShowAll,
                                              @RequestParam(value = "initChoose", required = false, defaultValue = "0") Integer initChoose) {
        PersonEntity person = user();
        if (person == null) {
            return Result.error("获取用户信息失败");
        }
        Map<String, Object> map = new HashMap<>();
        try {
            String lcJdmc = "";

            Integer nextJdid = 0;
            //得到当前流程信息
            List<Lcjd> currLcJdList = lcjdService.list(new LambdaQueryWrapper<Lcjd>().eq(Lcjd::getLcDefineID, lcDefineID).eq(Lcjd::getLcjdID, lcJdId));
            if (currLcJdList.size() > 0) {
                Lcjd currLcJd = currLcJdList.get(0);
                nextJdid = currLcJd.getNextID();
                lcJdmc = currLcJd.getJdmc();
            }

            // ↓↓↓↓↓↓流程分支，如果查到了就走分支↓↓↓↓↓↓
            if (lcDefineID == 20032 && lcJdId == 200321) {
                // 电子出车-特殊信息流程（这里不满足条件无法进行设置，所以特殊处理）
                //判断当前登录用户是否具有 协同办公-电子出车-特殊信息-审核 权限，有的话，下一步是200323
                Integer qxSize = lcConditionService.checkQx(person.getId(), "协同办公-电子出车-特殊信息-审核");
                if (qxSize > 0) {
                    nextJdid = 200323;
                } else {
                    nextJdid = 200322;
                }
            } else {
                List<LcCondition> lc_conditionList = lcConditionService.getLcCondition(lcDefineID, lcJdId);
                if (lc_conditionList != null && lc_conditionList.size() > 0 && ywId != null && ywId != 0) {
                    String conditionSql = lc_conditionList.get(0).getCondition() + ywId;
                    Map<String, Object> sqlMap = new HashMap<>();
                    sqlMap.put("sql", conditionSql);
                    List<T> sqlQueryList = publicSqlMapper.select(sqlMap);
                    nextJdid = sqlQueryList.size() > 0 ? lc_conditionList.get(0).getNextJDID() : nextJdid;
                }
            }

            // ↑↑↑↑↑↑↑流程分支，如果查到了就走分支↑↑↑↑↑

            //得到下一个流程节点信息
            String nextJdmc = "";
            //根据获取的下一个流程节点Id，查出对应信息
            List<Lcjd> nextLcJdList = lcjdService.list(new LambdaQueryWrapper<Lcjd>().eq(Lcjd::getLcDefineID, lcDefineID).eq(Lcjd::getLcjdID, nextJdid));
            if (nextLcJdList.size() > 0) {
                Lcjd nextLcJd = nextLcJdList.get(0);
                nextJdmc = nextLcJd.getJdmc();
            }

            //获取字典配置信息
            List<DfdwDictData> dictDataList = dfdwDictDataService.list(new LambdaQueryWrapper<DfdwDictData>().eq(DfdwDictData::getDictType, "dfdw-lc").eq(DfdwDictData::getValue, nextJdid));

            List<LcApprovePerson> personList = new ArrayList<>();

            //设计工代流程获取审批人特殊处理
            if (lcDefineID == 20033 || lcDefineID == 20034 || lcDefineID == 20035) {
                //获取项目的设总人员
                if (dictDataList.size() > 0) {
                    //获取字典的sql
                    String conditionSql = dictDataList.get(0).getRemark() + ywId;
                    Map<String, Object> sqlMap = new HashMap<>();
                    sqlMap.put("sql", conditionSql);
                    Integer userId = publicSqlMapper.selectInt(sqlMap);
                    personList = dwlcApproveService.getPersonByUserId(userId);
                }
            } else {
                //根据角色获取对应的审批人信息
                String roleName = "";
                if (dictDataList.size() > 0) {
                    roleName = dictDataList.get(0).getLabel();
                }
                personList = dwlcApproveService.getPerson(isShowAll, applyUserId, roleName, initChoose);
            }

            map.put("personList", personList);
            map.put("lcDefineID", lcDefineID);
            map.put("lcJdId", lcJdId);
            map.put("lcJdmc", lcJdmc);
            map.put("lcNextJdId", nextJdid);
            map.put("lcNextJdmc", nextJdmc);

            return Result.ok(map);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("获取审批人失败：" + ex.getMessage());
        }
    }


    /**
     * 提交
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/submitLc")
    @ResponseBody
    public Result<Object> submitLc(@RequestBody SubmitApproveDto submitApproveDto) {
        PersonEntity person = user();
        if (person == null) {
            return Result.error("获取用户信息失败");
        }
        Integer ywId = submitApproveDto.getYwId();
        try {
            Integer lcDefineId = submitApproveDto.getLcDefineId();
            Integer lcJdId = submitApproveDto.getLcJdId();


            String sendPerson = submitApproveDto.getSendPersonName();
            String feed = submitApproveDto.getFeeds();


            if (StringUtils.isEmpty(sendPerson)) {
                return Result.error("发送人不能为空！");
            }
            if (feed == null || "".equals(feed)) {
                submitApproveDto.setFeeds("同意");
            }

            List<LcCurrentstate> currentStateList = lcCurrentstateService.list(new LambdaQueryWrapper<LcCurrentstate>()
                    .eq(LcCurrentstate::getLcDefineID, lcDefineId).eq(LcCurrentstate::getYwID, ywId));
            LcCurrentstate currentState = null;
            if (currentStateList.size() > 0) {
                currentState = currentStateList.get(0);
            }
            if (currentState == null) {
                currentState = new LcCurrentstate();
                currentState.setLcDefineID(lcDefineId);
                //获取第一步流程节点信息
                List<Lcjd> lcjdList = lcjdService.list(new LambdaQueryWrapper<Lcjd>().eq(Lcjd::getLcDefineID, lcDefineId).orderByAsc(Lcjd::getType));
                currentState.setLcJdid(lcjdList.get(0).getLcjdID());
                submitApproveDto.setFirstLcJd(lcjdList.get(0));
            }
            if (!lcJdId.equals(currentState.getLcJdid())) {
                return Result.error("当前流程异常，请联系相关技术人员或刷新页面再次尝试。");
            }
            //执行审批
            dwlcApproveService.submitLc(submitApproveDto, person);
            return Result.ok();
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("提交审批失败：" + ex.getMessage());
        }
    }


    /**
     * 退回到申报
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/rollBackForApply")
    @ResponseBody
    public Result rollBackForApply(@RequestBody SubmitApproveDto submitApproveDto) {
        PersonEntity person = user();
        if (person == null) {
            return Result.error("获取用户信息失败");
        }
        Result result = dwlcApproveService.rollBackForApply(submitApproveDto, person);
        return result;
    }


    /**
     * 退回流程终止
     *
     * @param submitApproveDto
     * @return
     */
    @RequestMapping("/rollBackForEnd")
    @ResponseBody
    public Result rollBackForEnd(@RequestBody SubmitApproveDto submitApproveDto) {
        PersonEntity person = user();
        if (person == null) {
            return Result.error("获取用户信息失败");
        }
        Result result = dwlcApproveService.rollBackForEnd(submitApproveDto, person);
        return result;
    }



    /**
     * 流程终止
     * @return
     */
    @RequestMapping("/endLc")
    @ResponseBody
    public Result endLc(@RequestBody SubmitApproveDto submitApproveDto) {
        dwlcApproveService.endLc(submitApproveDto.getYwId(),submitApproveDto.getLcDefineId());
        return Result.ok();
    }


    /**
     * 流程终止
     * @return
     */
    @RequestMapping("/lcCopy")
    @ResponseBody
    public Result lcCopy(@RequestBody SubmitApproveDto submitApproveDto) {
        dwlcApproveService.lcCopy(submitApproveDto);
        return Result.ok();
    }

}
