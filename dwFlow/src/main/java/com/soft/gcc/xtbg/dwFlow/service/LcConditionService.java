package com.soft.gcc.xtbg.dwFlow.service;

import com.soft.gcc.xtbg.dwFlow.domain.LcCondition;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【Lc_Condition】的数据库操作Service
* @createDate 2024-12-16 10:39:35
*/
public interface LcConditionService extends IService<LcCondition> {

    List<LcCondition> getLcCondition(Integer lcDefineID, Integer lcid);

    Integer checkQx(Integer userId, String roleName);
}
