package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 东方多维-字典类型表
 * @TableName DFDW_DICT_TYPE
 */
@TableName(value ="DFDW_DICT_TYPE")
@Data
public class DfdwDictType implements Serializable {
    /**
     * 字典主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 字典类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 状态（0正常 1停用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date create_time;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date update_time;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private String deleted;

    /**
     * 删除时间
     */
    @TableField(value = "deleted_time")
    private Date deleted_time;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}