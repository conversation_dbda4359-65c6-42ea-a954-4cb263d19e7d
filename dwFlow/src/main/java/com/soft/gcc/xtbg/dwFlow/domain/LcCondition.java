package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName Lc_Condition
 */
@TableName(value ="Lc_Condition")
@Data
public class LcCondition implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer ID;

    /**
     * 
     */
    @TableField(value = "PID")
    private Integer PID;

    /**
     * 
     */
    @TableField(value = "LC_DefineID")
    private Integer LC_DefineID;

    /**
     * 
     */
    @TableField(value = "JDID")
    private Integer JDID;

    /**
     * 
     */
    @TableField(value = "JDMC")
    private String JDMC;

    /**
     * 
     */
    @TableField(value = "NextJDID")
    private Integer nextJDID;

    /**
     * 
     */
    @TableField(value = "NextJDMC")
    private String nextJDMC;

    /**
     * 
     */
    @TableField(value = "YWB")
    private String YWB;

    /**
     * 
     */
    @TableField(value = "Condition")
    private String condition;

    /**
     * 
     */
    @TableField(value = "L_Condition_Code")
    private String l_Condition_Code;

    /**
     * 
     */
    @TableField(value = "L_Condition")
    private String l_Condition;

    /**
     * 
     */
    @TableField(value = "Z_Condition")
    private String z_Condition;

    /**
     * 
     */
    @TableField(value = "R_Condition")
    private String r_Condition;

    /**
     * 
     */
    @TableField(value = "FromJScdt")
    private String fromJScdt;

    /**
     * 
     */
    @TableField(value = "ISBX")
    private Integer ISBX;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}