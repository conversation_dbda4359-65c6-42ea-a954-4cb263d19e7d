package com.soft.gcc.xtbg.dwFlow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dwFlow.domain.LcCurrentstate;
import com.soft.gcc.xtbg.dwFlow.service.LcCurrentstateService;
import com.soft.gcc.xtbg.dwFlow.mapper.LcCurrentstateMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【Lc_currentState】的数据库操作Service实现
* @createDate 2024-12-16 10:39:35
*/
@Service
public class LcCurrentstateServiceImpl extends ServiceImpl<LcCurrentstateMapper, LcCurrentstate>
    implements LcCurrentstateService{

}




