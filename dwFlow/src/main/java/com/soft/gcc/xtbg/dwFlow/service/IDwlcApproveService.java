package com.soft.gcc.xtbg.dwFlow.service;

import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dwFlow.dto.LcApprovePerson;
import com.soft.gcc.xtbg.dwFlow.dto.SubmitApproveDto;
import com.yyszc.wpbase.ventity.PersonEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description 流程审批service
 * @date 2022/10/14 10:14:10
 */
public interface IDwlcApproveService {

    /**
     * 获取下一步审批人

     * @param applyUserId 申 z请人用户Id
     * @param isShowAll   部门权限控制，-1不做控制，0当前部门 ,1TopDeptId
     * @param roleName  角色名称
     * @param chooseUse  是否能选择自己 0不可以,  1可以
     * @return
     */
    List<LcApprovePerson> getPerson(Integer isShowAll, Integer applyUserId, String roleName, Integer chooseUse);

    List<LcApprovePerson> getPersonByUserId(Integer userId);


    /**
     * 执行审批
     * @param submitApproveDto
     * @param person
     * @return
     */
    Integer submitLc(SubmitApproveDto submitApproveDto, PersonEntity person);

    /**
     * 退回到申报
     * @param submitApproveDto
     * @return
     */
    Result rollBackForApply(SubmitApproveDto submitApproveDto, PersonEntity person );

    /**
     * 流程终止
     * @param submitApproveDto
     * @param person
     * @return
     */
    Result rollBackForEnd(SubmitApproveDto submitApproveDto, PersonEntity person);


    /**
     * 终止流程
     * @param ywId
     * @param lcDefineId
     */
    void endLc(Integer ywId,Integer lcDefineId);


    void lcCopy(SubmitApproveDto submitApproveDto);
}
