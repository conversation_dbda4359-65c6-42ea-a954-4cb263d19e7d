package com.soft.gcc.xtbg.dwFlow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dwFlow.domain.DfdwDictType;
import com.soft.gcc.xtbg.dwFlow.service.DfdwDictTypeService;
import com.soft.gcc.xtbg.dwFlow.mapper.DfdwDictTypeMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_DICT_TYPE(东方多维-字典类型表)】的数据库操作Service实现
* @createDate 2024-12-16 15:53:07
*/
@Service
public class DfdwDictTypeServiceImpl extends ServiceImpl<DfdwDictTypeMapper, DfdwDictType>
    implements DfdwDictTypeService{

}




