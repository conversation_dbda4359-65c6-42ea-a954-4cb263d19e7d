package com.soft.gcc.xtbg.dwFlow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.gcc.common.person.mapper.PersonMapper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.dwFlow.domain.*;
import com.soft.gcc.xtbg.dwFlow.dto.LcApprovePerson;
import com.soft.gcc.xtbg.dwFlow.dto.SubmitApproveDto;
import com.soft.gcc.xtbg.dwFlow.mapper.LcCurrentstateMapper;
import com.soft.gcc.xtbg.dwFlow.mapper.LcWorkflowMapper;
import com.soft.gcc.xtbg.dwFlow.mapper.LcdefineMapper;
import com.soft.gcc.xtbg.dwFlow.mapper.LcjdMapper;
import com.soft.gcc.xtbg.dwFlow.service.IDwlcApproveService;

import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/14 10:14:17
 */
@Service
public class DwlcApproveServiceImpl implements IDwlcApproveService {

    @Autowired
    private LcjdMapper lcjdMapper;
    @Autowired
    private LcCurrentstateMapper lcCurrentstateMapper;
    @Autowired
    private LcWorkflowMapper lcWorkflowMapper;
    @Autowired
    private PersonMapper personMapper;
    @Autowired
    private LcdefineMapper lcdefineMapper;


    /**
     * 选择审批人
     *
     * @param isShowAll   是否展示全部
     * @param applyUserId 申请人userId
     * @param roleName    角色名称
     * @param chooseUse   是否可以选择自己 (0不可以，1可以)
     * @return
     */
    @Override
    public List<LcApprovePerson> getPerson(Integer isShowAll, Integer applyUserId, String roleName, Integer chooseUse) {
        //根据用户id和角色名称获取具有这个角色的人
        return lcjdMapper.getPerson(isShowAll, applyUserId, roleName, chooseUse);
    }

    @Override
    public List<LcApprovePerson> getPersonByUserId(Integer userId) {
        return lcjdMapper.getPersonByUserId(userId);
    }


    /**
     * 执行审批
     *
     * @param submitApproveDto
     * @param person           当前登录用户信息
     * @return
     */
    @Override
    public Integer submitLc(SubmitApproveDto submitApproveDto, PersonEntity person) {
        Integer nextId = 0;

        Integer lcDefineId = submitApproveDto.getLcDefineId();
        Integer lcJdId = submitApproveDto.getLcJdId();
        Integer ywId = submitApproveDto.getYwId();
        String feeds = submitApproveDto.getFeeds();
        Integer lcNextJdId = submitApproveDto.getLcNextJdId();


        //获取提交给下一步的审批人信息
        PersonEntity sendPersonInfo = lcjdMapper.getPersonById(submitApproveDto.getSendPersonId());
        //获取下一步流程节点信息
        Lcjd nextJd = null;
        List<Lcjd> lcjdList = lcjdMapper.selectList(new LambdaQueryWrapper<Lcjd>().eq(Lcjd::getLcDefineID, lcDefineId).eq(Lcjd::getLcjdID, lcNextJdId));
        if (lcjdList.size() > 0) {
            nextJd = lcjdList.get(0);
        }

        //当前流程节点
        Lcjd lc = submitApproveDto.getFirstLcJd();

        List<LcCurrentstate> currentStateList = lcCurrentstateMapper.selectList(new QueryWrapper<LcCurrentstate>().lambda()
                .eq(LcCurrentstate::getLcJdid, lcJdId)
                .eq(LcCurrentstate::getYwID, ywId).eq(LcCurrentstate::getLcDefineID, lcDefineId));

        LcCurrentstate currentState = null;
        if (currentStateList != null && currentStateList.size() > 0) {
            currentState = currentStateList.get(0);
        }

        //为NULL表示走第一个流程
        if (currentState == null) {

            //插入初始化workFlow
            addFirstLc_workFlow(lc, lcDefineId, ywId, person, feeds);

            //插入待办
            addCurrentState(nextJd, lcDefineId, ywId, sendPersonInfo, person);

            //插入下一步审批日志
            addNextLc_workFlow(nextJd, lcDefineId, ywId, sendPersonInfo);
            nextId = nextJd.getNextID();
        } else {
            //更新workFlow日志
            updateCurrentLc_workFlow(lcDefineId, ywId, lcJdId, feeds, person);
            //更新待办
            if (submitApproveDto.getSendPersonId() == -1 && submitApproveDto.getSendPersonName().equals("完成")) {
                //如果当前选择人为空，那么当前流程不需要流转
                endCurrentState(currentState);
                nextId = 0;
            } else {
                //修改待办审批信息
                updateCurrentState(currentState, nextJd, sendPersonInfo);
                //插入下一步审批日志
                addNextLc_workFlow(nextJd, lcDefineId, ywId, sendPersonInfo);
                nextId = nextJd.getNextID();
            }
        }
        return nextId;
    }


    /**
     * 退回到申报
     *
     * @param submitApproveDto
     * @return
     */
    @Override
    public Result rollBackForApply(SubmitApproveDto submitApproveDto, PersonEntity person) {
        try {
            Integer lcDefineID = submitApproveDto.getLcDefineId();
            Integer ywId = submitApproveDto.getYwId();
            Integer lcJdId = submitApproveDto.getLcJdId();
            String feed = submitApproveDto.getFeeds();

            List<LcCurrentstate> currentStateList = lcCurrentstateMapper.selectList(new QueryWrapper<LcCurrentstate>().lambda()
                    .eq(LcCurrentstate::getLcJdid, lcJdId)
                    .eq(LcCurrentstate::getYwID, ywId).eq(LcCurrentstate::getLcDefineID, lcDefineID));

            LcCurrentstate currentState = null;
            if (currentStateList != null && currentStateList.size() > 0) {
                currentState = currentStateList.get(0);
            }

            //这里根据lcDefineID + ywid 获取流程日志里面为空的一个流程意见
            List<LcWorkflow> n_number = lcWorkflowMapper.getFeedNull(lcDefineID, ywId);
            if (n_number.size() > 0) {
                for (int j = 0; j < n_number.size(); j++) {
                    if (n_number.get(j).getPersonZgh().contains(person.getLoginName())) {
                        n_number.get(j).setFeed(feed);
                    } else {
                        n_number.get(j).setFeed("自动退回");
                    }
                    n_number.get(j).setIsback(1);
                    n_number.get(j).setTransdate(new Date());
                    n_number.get(j).setUseback(1);
                    lcWorkflowMapper.updateById(n_number.get(j));
                }
            }

            //获取第一个流程节点
            Lcjd lc_first = lcjdMapper.selectList(new QueryWrapper<Lcjd>().lambda().eq(Lcjd::getLcDefineID, lcDefineID).orderByAsc(Lcjd::getType)).get(0);

            LcWorkflow firstWorkFlow = lcWorkflowMapper.selectList(new LambdaQueryWrapper<LcWorkflow>()
                    .eq(LcWorkflow::getLcDefineID, lcDefineID).eq(LcWorkflow::getYwID, ywId).orderByAsc(LcWorkflow::getID)).get(0);
            try {
                LcWorkflow workFlow = new LcWorkflow();
                workFlow.setLcDefineID(lcDefineID);
                workFlow.setLcJdID(firstWorkFlow.getLcJdID());
                workFlow.setLcJdmc(firstWorkFlow.getLcJdmc());
                workFlow.setYwID(ywId);
                workFlow.setGroupID(firstWorkFlow.getGroupID());
                workFlow.setGroupName(firstWorkFlow.getGroupName());
                workFlow.setStartdate(new Date());
                workFlow.setPersonZgh(firstWorkFlow.getPersonZgh());
                workFlow.setPersonName(firstWorkFlow.getPersonName());
                workFlow.setFeed("");
                workFlow.setNumber(1);
                lcWorkflowMapper.insert(workFlow);


                currentState.setSendPerson(firstWorkFlow.getPersonName());
                currentState.setSendPersonZgh(firstWorkFlow.getPersonZgh() + "~");
                currentState.setIsMany(0);
                currentState.setLcTojdid(lc_first.getNextID().toString());
                currentState.setLcJdmc(firstWorkFlow.getLcJdmc());
                currentState.setLcJdid(firstWorkFlow.getLcJdID());
                currentState.setLcIsback(1);
                currentState.setNumber(1);
                lcCurrentstateMapper.updateById(currentState);

            } catch (Exception e) {
                e.printStackTrace();
                return Result.error(e.getMessage());
            }

        } catch (Exception ex) {
            return Result.error("退回失败：" + ex.getMessage());
        }
        return Result.ok();
    }

    /**
     * 退回流程终止
     *
     * @param submitApproveDto
     * @param person
     * @return
     */
    @Override
    public Result rollBackForEnd(SubmitApproveDto submitApproveDto, PersonEntity person) {
        try {


            Integer lcDefineID = submitApproveDto.getLcDefineId();
            Integer ywId = submitApproveDto.getYwId();
            Integer lcJdId = submitApproveDto.getLcJdId();
            String feed = submitApproveDto.getFeeds();

            List<LcCurrentstate> currentStateList = lcCurrentstateMapper.selectList(new QueryWrapper<LcCurrentstate>().lambda()
                    .eq(LcCurrentstate::getLcJdid, lcJdId)
                    .eq(LcCurrentstate::getYwID, ywId).eq(LcCurrentstate::getLcDefineID, lcDefineID));

            LcCurrentstate currentState = null;
            if (currentStateList != null && currentStateList.size() > 0) {
                currentState = currentStateList.get(0);
            }

            //这里根据lcDefineID + ywid 获取流程日志里面为空的一个流程意见
            List<LcWorkflow> n_number = lcWorkflowMapper.getFeedNull(lcDefineID, ywId);
            if (n_number.size() > 0) {
                for (int j = 0; j < n_number.size(); j++) {
                    if (n_number.get(j).getPersonZgh().contains(person.getLoginName())) {
                        n_number.get(j).setFeed(feed);
                    } else {
                        n_number.get(j).setFeed("流程终止");
                    }
                    n_number.get(j).setIsback(1);
                    n_number.get(j).setTransdate(new Date());
                    n_number.get(j).setUseback(1);
                    lcWorkflowMapper.updateById(n_number.get(j));
                }
            }


            //修改待审批信息
            currentState.setSendPerson("流程终止");
            currentState.setSendPersonZgh("流程终止");
            currentState.setIsMany(0);
            currentState.setLcTojdid("0");
            currentState.setLcJdmc("流程终止");
            currentState.setLcJdid(-1);
            currentState.setNumber(1);
            currentState.setLcIsback(0);

            lcCurrentstateMapper.updateById(currentState);

        } catch (Exception ex) {
            return Result.error("流程终止失败：" + ex.getMessage());
        }
        return Result.ok();
    }


    /**
     * 终止流程信息
     *
     * @param ywId
     * @param lcDefineId
     */
    @Override
    public void endLc(Integer ywId, Integer lcDefineId) {
        List<LcCurrentstate> list = lcCurrentstateMapper.selectList(new LambdaQueryWrapper<LcCurrentstate>()
                .eq(LcCurrentstate::getLcDefineID, lcDefineId)
                .eq(LcCurrentstate::getYwID, ywId).ne(LcCurrentstate::getLcJdid, 0));
        if (list.size() > 0) {
            LcCurrentstate currentstate = list.get(0);
            currentstate.setSendPerson("流程终止");
            currentstate.setSendPersonZgh("流程终止");
            currentstate.setLcJdmc("完成");
            currentstate.setLcJdid(0);
            lcCurrentstateMapper.updateById(currentstate);
        }

        //这里根据lcDefineID + ywid 获取流程日志里面为空的一个流程意见
        List<LcWorkflow> n_number = lcWorkflowMapper.getFeedNull(lcDefineId, ywId);
        if (n_number.size() > 0) {
            for (int j = 0; j < n_number.size(); j++) {
                n_number.get(j).setFeed("流程终止(无需签收人审批)");
                n_number.get(j).setIsback(1);
                n_number.get(j).setTransdate(new Date());
                n_number.get(j).setUseback(1);
                lcWorkflowMapper.updateById(n_number.get(j));
            }
        }
    }

    @Override
    public void lcCopy(SubmitApproveDto submitApproveDto) {
        Integer lcDefineId = submitApproveDto.getLcDefineId();
        Integer ywId = submitApproveDto.getYwId();
        Integer newYwId = submitApproveDto.getNewYwId();
        Integer newLcDefineId = submitApproveDto.getNewLcDefineId();
        List<LcCurrentstate> list = lcCurrentstateMapper.selectList(new LambdaQueryWrapper<LcCurrentstate>().eq(LcCurrentstate::getLcDefineID, lcDefineId).eq(LcCurrentstate::getYwID, ywId));
        if (list.size() > 0) {
            LcCurrentstate currentstate = list.get(0);
            currentstate.setID(null);
            currentstate.setLcDefineID(newLcDefineId);
            currentstate.setYwID(newYwId);
            currentstate.setLcName("设计工代-现场问题反馈流程");
            currentstate.setSendPerson("归档");
            currentstate.setSendPersonZgh("归档");
            currentstate.setLcJdid(0);
            currentstate.setLcJdmc("完成");
            lcCurrentstateMapper.insert(currentstate);
        }


        List<LcWorkflow> workflowList = lcWorkflowMapper.selectList(new LambdaQueryWrapper<LcWorkflow>().eq(LcWorkflow::getLcDefineID, lcDefineId).eq(LcWorkflow::getYwID, ywId).orderByAsc(LcWorkflow::getStartdate));
        if(workflowList.size() >0){
            for (LcWorkflow workflow :workflowList){
                workflow.setID(null);
                workflow.setLcDefineID(newLcDefineId);
                workflow.setYwID(newYwId);
                if(workflow.getLcJdID().equals(200341)){
                    workflow.setLcJdID(200351);
                    workflow.setLcJdmc("现场问题反馈申请");
                }
                if(workflow.getLcJdID().equals(200342)){
                    workflow.setLcJdID(200352);
                    workflow.setLcJdmc("设总一级审批");
                }
                lcWorkflowMapper.insert(workflow);
            }
        }
    }


    /**
     * 插入第一步审批流程
     *
     * @param lc
     * @param lc_defineID
     * @param ywid
     * @param person
     * @param feed
     */
    public void addFirstLc_workFlow(Lcjd lc, int lc_defineID, int ywid, PersonEntity person, String feed) {
        LcWorkflow workFlow = new LcWorkflow();
        workFlow.setLcDefineID(lc_defineID);
        workFlow.setLcJdID(lc.getLcjdID());
        workFlow.setLcJdmc(lc.getJdmc());
        workFlow.setYwID(ywid);
        workFlow.setGroupID(person.getGroupId());
        workFlow.setGroupName(person.getGroupName());
        workFlow.setTransdate(new Date());
        workFlow.setStartdate(new Date());
        workFlow.setPersonZgh(person.getLoginName());
        workFlow.setPersonName(person.getRealName());
        workFlow.setFeed(feed);
        workFlow.setNumber(1);
        lcWorkflowMapper.insert(workFlow);
    }


    /**
     * 插入下一步审批日志
     *
     * @param lcjd
     * @param lcDefineId
     * @param ywId
     */
    public void addNextLc_workFlow(Lcjd lcjd, Integer lcDefineId, Integer ywId, PersonEntity sendPersonInfo) {
        LcWorkflow workFlow = new LcWorkflow();
        workFlow.setLcDefineID(lcDefineId);
        workFlow.setLcJdID(lcjd.getLcjdID());
        workFlow.setLcJdmc(lcjd.getJdmc());
        workFlow.setYwID(ywId);
        workFlow.setGroupID(sendPersonInfo.getGroupId());
        workFlow.setGroupName(sendPersonInfo.getGroupName());
        workFlow.setPersonZgh(sendPersonInfo.getLoginName());
        workFlow.setPersonName(sendPersonInfo.getRealName());
        workFlow.setFeed("");

        int number = 0;
        List<LcWorkflow> list = lcWorkflowMapper.selectList(new QueryWrapper<LcWorkflow>().lambda()
                .eq(LcWorkflow::getLcDefineID, lcDefineId)
                .eq(LcWorkflow::getYwID, ywId)
                .ne(LcWorkflow::getFeed, "")
                .orderByAsc(LcWorkflow::getID)
        );
        if (list.size() > 0) {
            number = list.get(list.size() - 1).getNumber() + 1;
        }
        workFlow.setNumber(number);
        workFlow.setLcByRole(0);
        workFlow.setStartdate(new Date());
        workFlow.setTransdate(null);

        lcWorkflowMapper.insert(workFlow);
    }

    /**
     * 更新流程日志
     *
     * @param lc_defineID
     * @param ywId
     * @param lcJdId
     * @param feed
     * @param person
     */
    public void updateCurrentLc_workFlow(int lc_defineID, int ywId, int lcJdId, String feed, PersonEntity person) {
        List<LcWorkflow> lists = lcWorkflowMapper.getFeedNullByPerson(lc_defineID, lcJdId, ywId, person.getLoginName());
        if (lists.size() > 0) {
            LcWorkflow workFlow = lists.get(0);
            workFlow.setTransdate(new Date());
            workFlow.setFeed(feed);

            lcWorkflowMapper.updateById(workFlow);
        }
    }


    /**
     * 插入待办信息
     *
     * @param lc             流程信息 （下一步的）
     * @param lc_defineID
     * @param ywId
     * @param sendPersonInfo 发送给下一步的审批人信息（待办信息）
     * @param person         当前登录用户
     */
    public void addCurrentState(Lcjd lc, int lc_defineID, int ywId, PersonEntity sendPersonInfo, PersonEntity person) {
        LcCurrentstate currentState = new LcCurrentstate();
        currentState.setYwID(ywId);
        currentState.setLcDefineID(lc_defineID);
        currentState.setLcJdid(lc.getLcjdID());
        currentState.setLcJdmc(lc.getJdmc());
        Lcdefine lcdefine = lcdefineMapper.selectOne(new QueryWrapper<Lcdefine>().lambda().eq(Lcdefine::getLcID, lc_defineID));
        currentState.setLcName(lcdefine.getLcName());
        currentState.setSendPerson(sendPersonInfo.getRealName() + "~");
        currentState.setSendPersonZgh(sendPersonInfo.getLoginName() + "~");
        currentState.setAllPersonZgh(person.getLoginName() + "~" + currentState.getSendPersonZgh());
        currentState.setLcTojdid(lc.getNextID().toString());
        currentState.setIsOtherAdd(0);
        currentState.setIsMany(0);
        //获取最新流程信息的number
        List<LcWorkflow> list = lcWorkflowMapper.selectList(new QueryWrapper<LcWorkflow>().lambda()
                .eq(LcWorkflow::getLcDefineID, lc_defineID).eq(LcWorkflow::getYwID, ywId));//用于获取上一级的number
        if (list.size() > 0) {
            if (list.get(list.size() - 1).getNumber() != null) {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }
        currentState.setBXType("主线");
        lcCurrentstateMapper.insert(currentState);
    }


    public void updateCurrentState(LcCurrentstate currentState, Lcjd lc, PersonEntity sendPersonInfo) {
        currentState.setLcJdid(lc.getLcjdID());
        currentState.setLcJdmc(lc.getJdmc());
        currentState.setSendPerson(sendPersonInfo.getRealName() + "~");
        currentState.setSendPersonZgh(sendPersonInfo.getLoginName() + "~");

        String aghStr = currentState.getAllPersonZgh();

        if (!"~".equals(aghStr.substring(aghStr.length() - 1, aghStr.length()))) {
            aghStr = aghStr + "~~";
        }

        currentState.setAllPersonZgh(aghStr + currentState.getSendPersonZgh() + "~");

        currentState.setLcTojdid(lc.getNextID().toString());

        currentState.setIsMany(0);
        currentState.setIsOtherAdd(0);
        currentState.setLcIsback(0);

        //获取最新流程信息的number
        List<LcWorkflow> list = lcWorkflowMapper.selectList(new QueryWrapper<LcWorkflow>().lambda()
                .eq(LcWorkflow::getLcDefineID, currentState.getLcDefineID()).eq(LcWorkflow::getYwID, currentState.getYwID()));//用于获取上一级的number

        if (list.size() > 0) {
            if (list.get(list.size() - 1).getNumber() != null) {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }

        currentState.setBXType("主线");
        lcCurrentstateMapper.updateById(currentState);
    }


    /**
     * 结束待办
     *
     * @param currentState
     */
    public void endCurrentState(LcCurrentstate currentState) {
        currentState.setLcJdid(0);
        currentState.setLcJdmc("完成");
        currentState.setSendPerson("归档");
        currentState.setSendPersonZgh("归档");
        //获取最新流程信息的number
        List<LcWorkflow> list = lcWorkflowMapper.selectList(new QueryWrapper<LcWorkflow>().lambda()
                .eq(LcWorkflow::getLcDefineID, currentState.getLcDefineID())
                .eq(LcWorkflow::getYwID, currentState.getYwID())
                .orderByDesc(LcWorkflow::getID)
        );
        if (list.size() > 0) {
            if (list.get(list.size() - 1).getNumber() != null) {
                currentState.setNumber(list.get(list.size() - 1).getNumber());
            }
        }
        currentState.setBXType(currentState.getBXType());
        lcCurrentstateMapper.updateById(currentState);
    }

}
