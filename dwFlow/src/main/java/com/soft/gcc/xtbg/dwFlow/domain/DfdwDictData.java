package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 东方多维-字典数据表
 * @TableName DFDW_DICT_DATA
 */
@TableName(value ="DFDW_DICT_DATA")
@Data
public class DfdwDictData implements Serializable {
    /**
     * 字典编码
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典类型
     */
    @TableField(value = "dict_type")
    private String dictType;

    /**
     * 字典标签
     */
    @TableField(value = "label")
    private String label;

    /**
     * 字典键值
     */
    @TableField(value = "value")
    private String value;

    /**
     * 字典排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private String deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
