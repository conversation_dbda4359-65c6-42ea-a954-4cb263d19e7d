package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 流程定义
 * @TableName Lcdefine
 */
@TableName(value ="Lcdefine")
@Data
public class Lcdefine implements Serializable {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer ID;

    /**
     *
     */
    @TableField(value = "LcID")
    private Integer lcID;

    /**
     *
     */
    @TableField(value = "lcName")
    private String lcName;

    /**
     *
     */
    @TableField(value = "ywb")
    private String ywb;

    /**
     *
     */
    @TableField(value = "ywUrl")
    private String ywUrl;

    /**
     *
     */
    @TableField(value = "xszd")
    private String xszd;

    /**
     *
     */
    @TableField(value = "isUse")
    private Integer isUse;

    /**
     *
     */
    @TableField(value = "app_url")
    private String appUrl;

    /**
     *
     */
    @TableField(value = "app_ywb")
    private String appYwb;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
