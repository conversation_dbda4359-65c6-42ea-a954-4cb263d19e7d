package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 *
 * @TableName Lc_currentState
 */
@TableName(value ="Lc_currentState")
@Data
public class LcCurrentstate implements Serializable {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer ID;

    /**
     *
     */
    @TableField(value = "Lc_defineID")
    private Integer lcDefineID;

    /**
     *
     */
    @TableField(value = "Lc_Name")
    private String lcName;

    /**
     *
     */
    @TableField(value = "ywID")
    private Integer ywID;

    /**
     *
     */
    @TableField(value = "sendPerson")
    private String sendPerson;

    /**
     *
     */
    @TableField(value = "sendPersonZgh")
    private String sendPersonZgh;

    /**
     *
     */
    @TableField(value = "AllPersonZgh")
    private String allPersonZgh;

    /**
     *
     */
    @TableField(value = "isMany")
    private Integer isMany;

    /**
     *
     */
    @TableField(value = "lc_jdmc")
    private String lcJdmc;

    /**
     *
     */
    @TableField(value = "lc_jdid")
    private Integer lcJdid;

    /**
     *
     */
    @TableField(value = "lc_isback")
    private Integer lcIsback;

    /**
     *
     */
    @TableField(value = "lc_tojdid")
    private String lcTojdid;

    /**
     *
     */
    @TableField(value = "isOtherAdd")
    private Integer isOtherAdd;

    /**
     *
     */
    @TableField(value = "number")
    private Integer number;

    /**
     *
     */
    @TableField(value = "BXType")
    private String BXType;

    /**
     *
     */
    @TableField(value = "PNO")
    private String PNO;

    /**
     *
     */
    @TableField(value = "sendGroupIDs")
    private String sendGroupIDs;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
