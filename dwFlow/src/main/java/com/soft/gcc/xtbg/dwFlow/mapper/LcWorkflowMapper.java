package com.soft.gcc.xtbg.dwFlow.mapper;

import com.soft.gcc.xtbg.dwFlow.domain.LcWorkflow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【Lc_workFlow】的数据库操作Mapper
* @createDate 2024-12-16 10:39:35
* @Entity com.soft.gcc.xtbg.dwFlow.domain.LcWorkflow
*/
public interface LcWorkflowMapper extends BaseMapper<LcWorkflow> {

    List<LcWorkflow> getFeedNull(@Param("lcDefineId") Integer lcDefineId, @Param("ywId")  Integer ywId);

    List<LcWorkflow> getFeedNullByPerson(@Param("lcDefineId") Integer lcDefineId,@Param("lcJdId") Integer lcJdId,
                                         @Param("ywId")  Integer ywId,@Param("personZgh")  String personZgh);
}




