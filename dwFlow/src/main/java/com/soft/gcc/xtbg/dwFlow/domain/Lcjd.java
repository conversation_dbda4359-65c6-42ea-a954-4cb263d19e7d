package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 流程节点
 * @TableName Lcjd
 */
@TableName(value ="Lcjd")
@Data
public class Lcjd implements Serializable {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer ID;

    /**
     *
     */
    @TableField(value = "lcjdID")
    private Integer lcjdID;

    /**
     *
     */
    @TableField(value = "lc_defineID")
    private Integer lcDefineID;

    /**
     *
     */
    @TableField(value = "jdmc")
    private String jdmc;

    /**
     *
     */
    @TableField(value = "nextID")
    private Integer nextID;

    /**
     *
     */
    @TableField(value = "type")
    private Integer type;

    /**
     *
     */
    @TableField(value = "shgr")
    private Integer shgr;

    /**
     *
     */
    @TableField(value = "GroupID")
    private String groupID;

    /**
     *
     */
    @TableField(value = "IsBX")
    private Integer isBX;

    /**
     *
     */
    @TableField(value = "FormType")
    private String formType;

    /**
     *
     */
    @TableField(value = "CheckFile")
    private String checkFile;

    /**
     *
     */
    @TableField(value = "CheckData")
    private String checkData;

    /**
     *
     */
    @TableField(value = "canBack")
    private Integer canBack;

    /**
     *
     */
    @TableField(value = "BackjdID")
    private String backjdID;

    /**
     *
     */
    @TableField(value = "LookFileJdID")
    private String lookFileJdID;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
