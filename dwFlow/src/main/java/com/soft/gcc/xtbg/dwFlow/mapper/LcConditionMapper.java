package com.soft.gcc.xtbg.dwFlow.mapper;

import com.soft.gcc.xtbg.dwFlow.domain.LcCondition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【Lc_Condition】的数据库操作Mapper
* @createDate 2024-12-16 10:39:35
* @Entity com.soft.gcc.xtbg.dwFlow.domain.LcCondition
*/
public interface LcConditionMapper extends BaseMapper<LcCondition> {

    List<LcCondition> getLcCondition(@Param("lcDefineID") Integer lcDefineID, @Param("lcid") Integer lcid);

    Integer checkQx(@Param("userId") Integer userId,@Param("roleName") String roleName);
}




