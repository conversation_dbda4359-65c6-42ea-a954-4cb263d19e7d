package com.soft.gcc.xtbg.dwFlow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dwFlow.domain.LcCondition;
import com.soft.gcc.xtbg.dwFlow.service.LcConditionService;
import com.soft.gcc.xtbg.dwFlow.mapper.LcConditionMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【Lc_Condition】的数据库操作Service实现
* @createDate 2024-12-16 10:39:35
*/
@Service
public class LcConditionServiceImpl extends ServiceImpl<LcConditionMapper, LcCondition>
    implements LcConditionService{

    @Override
    public List<LcCondition> getLcCondition(Integer lcDefineID, Integer lcid) {
        //根据lcDefineID + lcid 获取分支条条件
        return baseMapper.getLcCondition(lcDefineID, lcid);
    }

    @Override
    public Integer checkQx(Integer userId, String roleName) {
        return baseMapper.checkQx(userId,roleName);
    }
}




