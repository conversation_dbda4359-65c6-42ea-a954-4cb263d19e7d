package com.soft.gcc.xtbg.dwFlow.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName Lc_workFlow
 */
@TableName(value ="Lc_workFlow")
@Data
public class LcWorkflow implements Serializable {
    /**
     *
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer ID;

    /**
     *
     */
    @TableField(value = "lc_defineID")
    private Integer lcDefineID;

    /**
     *
     */
    @TableField(value = "ywID")
    private Integer ywID;

    /**
     *
     */
    @TableField(value = "lc_jdID")
    private Integer lcJdID;

    /**
     *
     */
    @TableField(value = "lc_jdmc")
    private String lcJdmc;

    /**
     *
     */
    @TableField(value = "groupID")
    private Integer groupID;

    /**
     *
     */
    @TableField(value = "groupName")
    private String groupName;

    /**
     *
     */
    @TableField(value = "personZgh")
    private String personZgh;

    /**
     *
     */
    @TableField(value = "personName")
    private String personName;

    /**
     *
     */
    @TableField(value = "transdate")
    private Date transdate;

    /**
     *
     */
    @TableField(value = "feed")
    private String feed;

    /**
     *
     */
    @TableField(value = "number")
    private Integer number;

    /**
     *
     */
    @TableField(value = "BXType")
    private String BXType;

    /**
     *
     */
    @TableField(value = "PNO")
    private String PNO;

    /**
     *
     */
    @TableField(value = "startdate")
    private Date startdate;

    /**
     *
     */
    @TableField(value = "LcByRole")
    private Integer lcByRole;

    /**
     *
     */
    @TableField(value = "isback")
    private Integer isback;

    /**
     *
     */
    @TableField(value = "useback")
    private Integer useback;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
