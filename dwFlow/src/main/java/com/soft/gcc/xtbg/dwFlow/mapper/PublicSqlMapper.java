package com.soft.gcc.xtbg.dwFlow.mapper;

import com.soft.gcc.xtbg.dwFlow.domain.LcCondition;
import org.apache.ibatis.annotations.*;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;
import java.util.Map;

@Mapper
public interface PublicSqlMapper {
    /**
     * 通用查询
     * @return
     *
     * 查询示例
     * map：
     * {
     * 	"sql": "select * from article where author=#{author} and title like concat('%', #{title}, '%')",
     * 	"author": "zs",
     * 	"title": "领导"
     * }
     *
     * return：
     * {
    * 			"id": 2,
    * 			"title": "分区领导视察",
    * 			"sub_title": null,
    * 			"author": "zs",
    * 			"content": "慰问员工",
    * 			"department_id": 1,
    * 			"check_status": "0",
    * 			"create_time": "2021-04-30 12:00:00",
    * 			"update_time": "2021-04-21 22:04:44"
     * }
     */
    @Select("${sql}")
    List<T> select(Map<String, Object> map);

    @Select("${sql}")
    Integer selectInt(Map<String, Object> map);

    @Select("${sql}")
    List<LcCondition> selectLcCondition(Map<String, Object> map);

    /**
     * 新增
     * @param map
     * @return
     */
    @Insert("${sql}")
    int insert(Map<String, Object> map);

    /**
     * 修改
     * @param map
     * @return
     */
    @Update("${sql}")
    int update(Map<String, Object> map);

    /**
     * 删除
     * @param map
     * @return
     */
    @Delete("${sql}")
    int delete(Map<String, Object> map);
}
