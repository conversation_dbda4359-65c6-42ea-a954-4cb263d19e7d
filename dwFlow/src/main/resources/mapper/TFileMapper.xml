<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.t_file.mapper.TFileMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.t_file.entity.TFile">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="filename" column="FileName" jdbcType="VARCHAR"/>
            <result property="filepath" column="FilePath" jdbcType="VARCHAR"/>
            <result property="projectid" column="ProjectID" jdbcType="INTEGER"/>
            <result property="functionid" column="FunctionID" jdbcType="INTEGER"/>
            <result property="type" column="Type" jdbcType="VARCHAR"/>
            <result property="hjid" column="hjID" jdbcType="VARCHAR"/>
            <result property="uploaddate" column="UploadDate" jdbcType="TIMESTAMP"/>
            <result property="issecret" column="IsSecret" jdbcType="INTEGER"/>
            <result property="personname" column="PersonName" jdbcType="VARCHAR"/>
            <result property="personzgh" column="PersonZgh" jdbcType="VARCHAR"/>
            <result property="subtname" column="SubTName" jdbcType="VARCHAR"/>
            <result property="subtid" column="SubTID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,FileName,FilePath,
        ProjectID,FunctionID,Type,
        hjID,UploadDate,IsSecret,
        PersonName,PersonZgh,SubTName,
        SubTID
    </sql>
</mapper>
