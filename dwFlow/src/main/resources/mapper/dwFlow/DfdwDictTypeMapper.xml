<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dwFlow.mapper.DfdwDictTypeMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dwFlow.domain.DfdwDictType">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="update_time" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
            <result property="deleted_time" column="deleted_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,type,
        status,remark,creator,
        create_time,updater,update_time,
        deleted,deleted_time
    </sql>
</mapper>
