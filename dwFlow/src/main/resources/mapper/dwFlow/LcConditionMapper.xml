<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dwFlow.mapper.LcConditionMapper">



    <sql id="Base_Column_List">
        ID,PID,LC_DefineID,
        JDID,JDMC,NextJDID,
        NextJDMC,YWB,Condition,
        L_Condition_Code,L_Condition,Z_Condition,
        R_Condition,FromJScdt,ISBX
    </sql>

    <select id="getLcCondition" resultType="com.soft.gcc.xtbg.dwFlow.domain.LcCondition">
        SELECT * from Lc_Condition WHERE LC_DefineID = #{lcDefineID} AND JDID = #{lcid}
    </select>

    <select id="checkQx" resultType="java.lang.Integer">
        select count(*) from <PERSON><PERSON><PERSON>  rp
        left join Role r  on rp.RoleId = r.Id
        where r.RoleName = #{roleName} and rp.PersonId = #{userId}
    </select>
</mapper>
