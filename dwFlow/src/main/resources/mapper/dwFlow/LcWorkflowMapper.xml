<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dwFlow.mapper.LcWorkflowMapper">



    <sql id="Base_Column_List">
        ID,lc_defineID,ywID,
        lc_jdID,lc_jdmc,groupID,
        groupName,personZgh,personName,
        transdate,feed,number,
        BXType,PNO,startdate,
        LcByRole,isback,useback
    </sql>

    <select id="getFeedNull" resultType="com.soft.gcc.xtbg.dwFlow.domain.LcWorkflow">
        SELECT *  FROM Lc_workFlow WHERE lc_defineID = #{lcDefineId} AND ywID = #{ywId} and  (feed =' ' or feed is null )
    </select>

    <select id="getFeedNullByPerson" resultType="com.soft.gcc.xtbg.dwFlow.domain.LcWorkflow">
        SELECT
           *
        FROM
            Lc_workFlow
        WHERE
            lc_defineID = #{lcDefineId} AND ywID = #{ywId} AND lc_jdID = #{lcJdId} AND personZgh = #{personZgh}
            AND (feed =' ' or feed is null )
        ORDER BY
            ID DESC
    </select>
</mapper>
