<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dwFlow.mapper.LcjdMapper">



    <sql id="Base_Column_List">
        ID,lcjdID,lc_defineID,
        jdmc,nextID,type,
        shgr,GroupID,IsBX,
        FormType,CheckFile,CheckData,
        canBack,BackjdID,LookFileJdID
    </sql>


    <select id="getPerson" resultType="com.soft.gcc.xtbg.dwFlow.dto.LcApprovePerson">
        SELECT  p.Id,p.LoginName,p.RealName,p.Telephone,p.GroupId,p.GroupName,p.TopGroupId,p.TopGroupName  FROM vPerson p
        left join RolePerson rp on rp.PersonId = p.Id
        left join Role  r on r.Id = rp.RoleId
        where r.RoleName = #{roleName}
        <if test="isShowAll==0">
            and p.GroupId =  (select p.GroupId FROM vPerson p  where   p.Id =#{applyUserId}  )
        </if>
        <if test="isShowAll==1">
            and  p.TopGroupId =  (select p.TopGroupId FROM vPerson p  where   p.Id =#{applyUserId} )
        </if>
        <if test="chooseUse ==0">
            and  p.Id != #{applyUserId}
        </if>
        order by p.P_XH
    </select>

    <select id="getPersonByUserId"  resultType="com.soft.gcc.xtbg.dwFlow.dto.LcApprovePerson">
        SELECT  p.Id,p.LoginName,p.RealName,p.Telephone,p.GroupId,p.GroupName,p.TopGroupId,p.TopGroupName  FROM vPerson p
        where p.Id = #{userId}
    </select>

    <select id="getPersonById" resultType="com.yyszc.wpbase.ventity.PersonEntity">
        SELECT  *  FROM vPerson  where Id = #{sendPersonId}
    </select>
</mapper>
