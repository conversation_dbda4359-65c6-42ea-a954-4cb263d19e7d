# 项目相关配置
jplat:
  # 名称
  name: dwGcssb
  #文件分隔符
  separator: /
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 本机资源不能用户前台展示，不映射Upload路径
  profile: d:/profile/dwGcssb
  # 获取ip地址开关
  addressEnabled: true
  # Session保留时间长度
  SessionTimeOut: 5
  #报表工具数据库链接
  ReportConnStr:
  #ExtJs 版本
  ExtVersion: 3.2
  #是否记录日志
  LogFlag: 1
  #是否记录SQL日志
  SqlTg: 1
  #系统mark
  SystemMark: dwGcssb
  #是否记录OCCI日志
  OcciLog: 1
  #OCCI记录时限
  OcciSecond: 5
  #导出水印标志
  EPWMask: 1
  #界面水印
  UIWMask: 1
  #是否加载数据库配置
  LoadDBConf: 0
  #模块标志
  ModuleId: 68
  #限定GroupId标志
  GroupId: 0
  #短信总开关
  SmsFlag: 1
  #通知是否开启短信通知
  NoticeSmsFlag: 1
  # 文件路径 联机资源，可用于前台展示下载等，测试时走本地，发布时统一走平台路径
  OssLocalPath: d:/profile/dwGcssb/upload/APP/
  #OSS远程路径
  OssRemotePath: Upload/APP/
  #运行模式 test/formal
  RunMode: test
  #Security,excludes 要求token可以访问系统
  WhiteList: /Service/**/MLogin

#是否开启swagger文档接口
mconfig:
  swagger-ui-open: true

#阿里云OSS配置
oconfig:
  endPoint: http://oss-cn-shanghai.aliyuncs.com/
  accessKeyId: LTAI5tCCPPvtGriTPpNw1wbp
  accessKeySecret: ******************************
  bucketName: shuziyongyao
  #开启标志
  openFlag: false

# 开发环境配置
server:
  port: 8060
  # ssl:
  #    enabled: true
  #    protocol: TLS
  #    key-store: classpath:cert/3688256__yongyaokjit.com.pfx
  #    key-store-password: G30HL74F
  #    key-store-type: pkcs12
  #    key-alias: alias
  #    key-password: G30HL74F
  #    ciphers: [ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE]

  servlet:
    context-path: /dwGcssb
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  tomcat:
    uri-encoding: UTF-8
    max-threads: 800
    min-spare-threads: 30

# 日志配置
logging:
  level:
    root: info
  config:
    classpath: log4j2.xml

# Spring配置
spring:
  application:
    name: dwGcssb
  mvc:
    static-path-pattern: /**
  resources:
    static-locations: classpath:/static/,file:/static/
  thymeleaf:
    check-template: true
    check-template-location: true
    cache: true
    servlet:
      content-type: text/html
    suffix: .tml
    enabled: true
    encoding: UTF-8
    prefix: classpath:/templates/
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      # 单个文件的最大值
      max-file-size: 100MB
      # 上传文件总的最大值
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  redis:
    host: **************
    port: 6379
    database: 6
    # 密码
    password: davice@252
    timeout: 10000
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 100
        max-wait: -1
      shutdown-timeout: 100
  jmx:
    enable: false
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      master:
        url: ***********************************************************************************************************
        username: topdavice1
        password: topdavice@---
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      query-timeout: 300
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username:
        login-password:
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
      remove-abandoned: true
      remove-abandoned-timeout: 180
      log-abandoned: true
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
      cache:
        enabled: true
        caffeine:
          spec: initialCapacity=500,expireAfterWrite=5s
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      eager: true
      enabled: true
      scg:
        fallback:
          mode: response
          response-status: 455
          response-body: The system is busy, please try again later!
#mybatis-plus 配置
mybatis-plus:
  #  mapper-locations: classpath*:com/**/xml/*.xml
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/*/*.xml #实体类所要映射sql的xml文件
  Configuration:
    mapUnderscoreToCamelCase: false
    cacheEnabled: false
  GlobalConfig:
    banner: false

feign:
  compression:
    request:
      enable: true
      min-request-size: 2048
      mime-types: text/xml,application/xml,application/json
    response:
      enable: true
  hystrix:
    enabled: false
  httpclient:
    enable: false
  okhttp:
    enable: true
    max-connections: 200 # 默认值
    max-connections-per-route: 50 # 默认值
  sentinel:
    enabled: true
  client:
    config:
      wpframe:
        url: http://127.0.0.1:8010/wpframe
        #请求日志级别
        loggerLevel: BASIC
        # 连接超时时间，默认2s，设置单位为毫秒
        connectTimeout: 20000
        # 请求处理超时时间，默认5s，设置单位为毫秒。
        readTimeout: 20000

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes:
  # 匹配链接
  urlPatterns: /Service/*


token:
  # 令牌自定义标识
  header: Authorization
  # 令牌秘钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30


InfluxDBConfig:
  ipAddress: http://**************:8086
  token: ZCV98IFBLe6qioNqwf-Lx51aY1la6k_n2sKlkX2OdvDjZxIJVvfTFwzzo6xViZhXrA57TNR0ezOsw6b_4_Qghg==
  bucket: dfdw
  org: dfdw
  measurement: vehicle_position
