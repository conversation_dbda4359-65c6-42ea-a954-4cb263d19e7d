<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbMainMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameFullPath" column="nameFullPath" jdbcType="VARCHAR"/>
            <result property="parentId" column="parentId" jdbcType="INTEGER"/>
            <result property="topId" column="topId" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="isEnable" column="isEnable" jdbcType="INTEGER"/>
            <result property="isVisible" column="isVisible" jdbcType="INTEGER"/>
            <result property="advancePageNum" column="advancePageNum" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="level" column="level" jdbcType="INTEGER"/>
            <result property="createBy" column="createBy" jdbcType="INTEGER"/>
            <result property="creationDate" column="creationDate" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateBy" column="lastUpdateBy" jdbcType="INTEGER"/>
            <result property="lastUpdateDate" column="lastUpdateDate" jdbcType="TIMESTAMP"/>
            <result property="createByDeptId" column="createByDeptId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,nameFullPath,
        parentId,topId,sort,
        isEnable,isVisible,advancePageNum,
        remark,type,level,
        createBy,creationDate,lastUpdateBy,
        lastUpdateDate,createByDeptId
    </sql>
    <select id="selectImgTFile" resultType="com.soft.gcc.common.t_file.entity.TFile">
        select  * from T_File where id = #{fileId}
    </select>
</mapper>
