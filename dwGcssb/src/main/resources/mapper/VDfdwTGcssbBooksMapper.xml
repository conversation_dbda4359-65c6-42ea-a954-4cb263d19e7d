<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.gcssb.mapper.VDfdwTGcssbBooksMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.gcssb.domain.VDfdwTGcssbBooks">
            <result property="id" column="Id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="categoryId" column="categoryId" jdbcType="INTEGER"/>
            <result property="isEnable" column="isEnable" jdbcType="INTEGER"/>
            <result property="isVisible" column="isVisible" jdbcType="INTEGER"/>
            <result property="advancePageNum" column="advancePageNum" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="fileId" column="fileId" jdbcType="INTEGER"/>
            <result property="originalFilename" column="originalFilename" jdbcType="VARCHAR"/>
            <result property="filePath" column="FilePath" jdbcType="VARCHAR"/>
            <result property="hz" column="hz" jdbcType="VARCHAR"/>
            <result property="totalPages" column="totalPages" jdbcType="INTEGER"/>
            <result property="splitCatalog" column="splitCatalog" jdbcType="VARCHAR"/>
            <result property="fileSize" column="fileSize" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,name,categoryId,
        isEnable,isVisible,advancePageNum,
        remark,fileId,originalFilename,
        FilePath,hz,totalPages,
        splitCatalog,fileSize
    </sql>
</mapper>
