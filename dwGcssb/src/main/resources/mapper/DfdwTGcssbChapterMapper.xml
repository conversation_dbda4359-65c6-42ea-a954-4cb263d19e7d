<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbChapterMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nameFullPath" column="nameFullPath" jdbcType="VARCHAR"/>
            <result property="parentId" column="parentId" jdbcType="INTEGER"/>
            <result property="topId" column="topId" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="pageNum" column="pageNum" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createBy" column="createBy" jdbcType="INTEGER"/>
            <result property="creationDate" column="creationDate" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateBy" column="lastUpdateBy" jdbcType="INTEGER"/>
            <result property="lastUpdateDate" column="lastUpdateDate" jdbcType="TIMESTAMP"/>
            <result property="createByDeptId" column="createByDeptId" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,nameFullPath,
        parentId,topId,sort,
        pageNum,remark,createBy,
        creationDate,lastUpdateBy,lastUpdateDate,
        createByDeptId,deleted
    </sql>
    <insert id="batchInsert">
        INSERT INTO
            DFDW_T_GCSSB_CHAPTER
            (name, nameFullPath, parentId, topId, sort,pageNum,remark,createBy,creationDate,createByDeptId,deleted,booksId)
        VALUES
        <foreach collection="list" index="" item="item" separator=",">
            (#{item.name,jdbcType=VARCHAR},
            #{item.nameFullPath,jdbcType=VARCHAR},
            #{item.parentId,jdbcType=INTEGER},
            #{item.topId,jdbcType=INTEGER},
            #{item.sort,jdbcType=INTEGER},
            #{item.pageNum,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=INTEGER},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.createByDeptId,jdbcType=INTEGER},
            '0',
            #{item.booksId,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>
