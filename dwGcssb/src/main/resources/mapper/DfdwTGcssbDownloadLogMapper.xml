<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbDownloadLogMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="userId" jdbcType="INTEGER"/>
            <result property="userName" column="userName" jdbcType="VARCHAR"/>
            <result property="booksId" column="booksId" jdbcType="INTEGER"/>
            <result property="booksName" column="booksName" jdbcType="VARCHAR"/>
            <result property="downloadTime" column="downloadTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,userId,userName,
        booksId,booksName,downloadTime,downloadType
    </sql>
    <select id="selecDownloadLogPage" resultType="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog">
        select Id,userId,userName,booksId,booksName,downloadTime,downloadType from DFDW_T_GCSSB_DOWNLOAD_LOG
        <where>
            1=1
            <if test="downloadLog.downloadType != null and downloadLog.downloadType !='' ">
                and downloadType like CONCAT('%',#{downloadLog.downloadType},'%')
            </if>
            <if test="downloadLog.userName != null and downloadLog.userName !='' ">
                and userName like CONCAT('%',#{downloadLog.userName},'%')
            </if>
            <if test="downloadLog.booksName != null and downloadLog.booksName !='' ">
                and booksName like CONCAT('%',#{downloadLog.booksName},'%')
            </if>
            <if test="downloadLog.downloadTimeStart != null and downloadLog.downloadTimeStart !=''  and downloadLog.downloadTimeEnd!= null and downloadLog.downloadTimeEnd !=''">
                and downloadTime >= #{downloadLog.downloadTimeStart} and downloadTime  &lt;= #{downloadLog.downloadTimeEnd}
            </if>
        </where>
        order by id DESC
    </select>
</mapper>
