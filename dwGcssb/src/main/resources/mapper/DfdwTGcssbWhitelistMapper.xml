<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbWhitelistMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="userId" jdbcType="INTEGER"/>
            <result property="userName" column="userName" jdbcType="VARCHAR"/>
            <result property="whitelistTag" column="whitelistTag" jdbcType="INTEGER"/>
            <result property="whitelistPermission" column="whitelistPermission" jdbcType="VARCHAR"/>
            <result property="editTag" column="editTag" jdbcType="INTEGER"/>
            <result property="editPermission" column="editPermission" jdbcType="VARCHAR"/>
            <result property="createBy" column="createBy" jdbcType="INTEGER"/>
            <result property="creationDate" column="creationDate" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateBy" column="lastUpdateBy" jdbcType="INTEGER"/>
            <result property="lastUpdateDate" column="lastUpdateDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,userId,userName,
        whitelistTag,whitelistPermission,editTag,
        editPermission,createBy,creationDate,
        lastUpdateBy,lastUpdateDate
    </sql>

    <select id="selecWhitetPage" resultType="com.soft.gcc.xtbg.gcssb.entity.Person">
        select Id,LoginName,RealName,Sphone,Telephone from Person
        <where>
            1=1
            <if test="loginName != null and loginName != ''">
                and LoginName like  concat('%',#{loginName},'%')
            </if>
            <if test="realName != null and realName != ''">
                and RealName like  concat('%',#{realName},'%')
            </if>
        </where>
        order by id DESC
    </select>
</mapper>
