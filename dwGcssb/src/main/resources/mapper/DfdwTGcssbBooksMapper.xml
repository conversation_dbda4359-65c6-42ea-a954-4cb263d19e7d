<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbBooksMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="categoryId" column="categoryId" jdbcType="INTEGER"/>
            <result property="isEnable" column="isEnable" jdbcType="INTEGER"/>
            <result property="isVisible" column="isVisible" jdbcType="INTEGER"/>
            <result property="advancePageNum" column="advancePageNum" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createBy" column="createBy" jdbcType="INTEGER"/>
            <result property="creationDate" column="creationDate" jdbcType="TIMESTAMP"/>
            <result property="lastUpdateBy" column="lastUpdateBy" jdbcType="INTEGER"/>
            <result property="lastUpdateDate" column="lastUpdateDate" jdbcType="TIMESTAMP"/>
            <result property="createByDeptId" column="createByDeptId" jdbcType="INTEGER"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,categoryId,
        isEnable,isVisible,advancePageNum,
        remark,createBy,creationDate,
        lastUpdateBy,lastUpdateDate,createByDeptId,
        deleted
    </sql>

    <insert id="batchInsert">
        INSERT INTO
        DFDW_T_GCSSB_BOOKS
        (name, categoryId, isEnable, isVisible, advancePageNum,remark,createBy,creationDate,createByDeptId,deleted)
        VALUES
        <foreach collection="list" index="" item="item" separator=",">
            (#{item.name,jdbcType=VARCHAR},
            #{item.categoryId,jdbcType=INTEGER},
            #{item.isEnable,jdbcType=INTEGER},
            #{item.isVisible,jdbcType=INTEGER},
            #{item.advancePageNum,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=INTEGER},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.createByDeptId,jdbcType=INTEGER},
            '0')
        </foreach>
    </insert>

    <select id="selectBooksPage" resultType="com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks">
        select Id,name,categoryId,isEnable,isVisible,advancePageNum,remark from DFDW_T_GCSSB_BOOKS
        <where>
            1=1
            <if test="name != null and name != ''">
                and name like  concat('%',#{name},'%')
            </if>
        </where>
        order by id DESC
    </select>
</mapper>
