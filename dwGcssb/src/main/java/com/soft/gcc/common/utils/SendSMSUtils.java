package com.soft.gcc.common.utils;

import com.soft.framework.helper.SqlHelper;

public class SendSMSUtils {

    public static void sendSms(String phone, String content) {
        SqlHelper sqlhelper=new SqlHelper();
        String strsql="";
        try
        {
            strsql = "insert into T_SMSSEND ([Source],[Phone],[DateTime],[Sequence],[Result],[Info]) select 'AQGL','" + phone + "',getdate(),isnull(max(sequence),0)+1,0,'" + content + "' From [T_SMSSend] where Source='AQGL' and Phone='" + phone + "' and [DateTime]=getdate()";
            int cont = sqlhelper.ExecuteNoQuery(strsql);
        }catch (Exception Ex)
        {
            return;
        }
    }

}
