package com.soft.gcc.base.ventity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class vMenuItem {
    private static final long serialVersionUID = 1L;

    @J<PERSON><PERSON>ield(name="Id")
    private String Id;

    @JSO<PERSON>ield(name="Parameter")
    private Integer Parameter;

    @JSONField(name="Title")
    private String Title;

    @J<PERSON><PERSON>ield(name="DisplayName")
    private String DisplayName;

    @J<PERSON><PERSON>ield(name="Url")
    private String Url;

    @J<PERSON><PERSON>ield(name="imageUrl")
    private String imageUrl;

    @J<PERSON>NField(name="childList")
    List<vMenuItem> childList;
}
