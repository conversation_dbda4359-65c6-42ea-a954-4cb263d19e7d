package com.soft.gcc.xtbg.gcssb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbBooksService;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbChapterService;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@RequestMapping("/gcssb/chapter")
@RestController
@Validated
public class DfdwTGcssbChapterController extends BaseController {

    @Resource
    private DfdwTGcssbChapterService dfdwTGcssbChapterService;


    /**
     * 新增
     * @return
     */
    @PostMapping("/create")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> createChapter( @RequestBody DfdwTGcssbChapter dfdwTGcssbChapter) {
        int id =  dfdwTGcssbChapterService.createChapter(dfdwTGcssbChapter);
        return Result.ok(id) ;
    }

    /**
     * 删除
     * @return
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX04')")
    public Result<Object> deleteChapter(@RequestParam("id") Long id) {
        dfdwTGcssbChapterService.deleteChapter(id);
        return Result.ok();
    }


    /**
     * 修改
     * @return
     */
    @PutMapping("/update")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> updateChapter(@RequestBody DfdwTGcssbChapter dfdwTGcssbChapter) {
        dfdwTGcssbChapterService.updateChapter(dfdwTGcssbChapter);
        return Result.ok();
    }


    /**
     * 根据书籍id获取章节数据page
     * @return
     */
    @PostMapping("/getListByBooksId")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> getListByBooksId(@RequestBody DfdwTGcssbChapter dfdwTGcssbChapter) {
        IPage<DfdwTGcssbChapter>  chapterIPage =  dfdwTGcssbChapterService.getListByBooksId(dfdwTGcssbChapter);
        return Result.ok(chapterIPage) ;
    }

    /**
     * 根据书籍id获取章节数据
     * @return
     */
    @PostMapping("/getAllList")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> getAllList(@RequestBody DfdwTGcssbChapter dfdwTGcssbChapter) {
        List<DfdwTGcssbChapter> chapterList =  dfdwTGcssbChapterService.getAllList(dfdwTGcssbChapter);
        return Result.ok(chapterList) ;
    }


    /**
     * 导入excel模板
     * @return
     */
    @PostMapping("importExcelTemplate")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> exportExcelTemplate( HttpServletResponse response){
        try {
            dfdwTGcssbChapterService.exportExcelTemplate(response);
        }  catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.ok();
    }

    /**
     * 章节导入
     */
    @PostMapping("importExcel/{booksId}")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> importExcel(MultipartFile file,@PathVariable Integer booksId){
        return dfdwTGcssbChapterService.importExcel(file ,booksId);
    }




}
