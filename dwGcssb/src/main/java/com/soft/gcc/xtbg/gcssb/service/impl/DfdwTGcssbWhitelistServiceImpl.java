package com.soft.gcc.xtbg.gcssb.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist;
import com.soft.gcc.xtbg.gcssb.entity.Person;
import com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbWhitelistMapper;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbMainService;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbWhitelistService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_WHITELIST(工程随身宝-白名单)】的数据库操作Service实现
* @createDate 2024-06-14 11:03:04
*/
@Service
public class DfdwTGcssbWhitelistServiceImpl extends ServiceImpl<DfdwTGcssbWhitelistMapper, DfdwTGcssbWhitelist>
    implements DfdwTGcssbWhitelistService{

    @Resource
    DfdwTGcssbWhitelistMapper dfdwTGcssbWhitelistMapper;
    @Resource
    DfdwTGcssbMainService dfdwTGcssbMainService;


    /**
     * 新增
     * @param whitelist 白名单
     */
    @Override
    public Result<Object> create(DfdwTGcssbWhitelist whitelist) {
        //校验用户是否重复
        Result<Object> result =  verifyDoubleName(whitelist);
        if (result.getCode() != 200) {
            return result;
        }
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //新增用户id
        whitelist.setCreateBy(user.getId());
        //新增时间
        whitelist.setCreationDate(new Date());
        //更新时间
        whitelist.setLastUpdateDate(new Date());
        //更新用户id
        whitelist.setLastUpdateBy(user.getId());
        //新增
        dfdwTGcssbWhitelistMapper.insert(whitelist);
        return result;
    }

    /**
     * 校验用户是否重复
     * @param whitelist 白名单
     */
    private Result<Object> verifyDoubleName(DfdwTGcssbWhitelist whitelist) {
        List<DfdwTGcssbWhitelist> whiteDataById = this.getWhiteDataById(whitelist.getUserId());
        //如果是新增
        if (whitelist.getId() == null) {
            if (CollectionUtils.isNotEmpty(whiteDataById)) {
                return Result.error("该用户已存在");
            }
        }
        //如果是修改
        if (CollectionUtils.isNotEmpty(whiteDataById) && whiteDataById.get(0).getId() != whitelist.getId()) {
            return Result.error("该用户已存在");
        }
        return Result.ok();
    }

    /**
     * 删除
     * @param id 白名单id
     */
    @Override
    public void deleteWhitelist(Long id) {
        dfdwTGcssbWhitelistMapper.deleteById(id);
    }

    /**
     * 修改
     * @param whitelist 白名单
     */
    @Override
    public Result<Object> updateWhitelist(DfdwTGcssbWhitelist whitelist) {
        //校验用户是否重复
        Result<Object> result =  verifyDoubleName(whitelist);
        if (result.getCode() != 200) {
            return result;
        }
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        whitelist.setLastUpdateBy(user.getId());
        //修改时间
        whitelist.setLastUpdateDate(new Date());
        //白名单标识为0-否
        if (whitelist.getWhitelistTag() == 0) {
            whitelist.setWhitelistPermission(null);
        }
        //白名单编辑标识0-否
        if (whitelist.getEditTag() == 0) {
            whitelist.setEditPermission(null);
        }
        //修改
        dfdwTGcssbWhitelistMapper.updateById(whitelist);
        return result;
    }

    /**
     * 获取白名单数据
     */
    @Override
    public IPage<DfdwTGcssbWhitelist> getList(DfdwTGcssbWhitelist whitelist) {
        IPage<DfdwTGcssbWhitelist> list = new Page<>();
        list.setCurrent(whitelist.getPageNum());
        list.setSize(whitelist.getPageSize());
        //查询分页
        list = this.page(list, new LambdaQueryWrapper<DfdwTGcssbWhitelist>()
                .like(whitelist.getUserName() != null,DfdwTGcssbWhitelist::getUserName,whitelist.getUserName())
                .in(whitelist.getWhitelistTag() != null,DfdwTGcssbWhitelist::getWhitelistTag,whitelist.getWhitelistTag())
                .in(whitelist.getEditTag() != null,DfdwTGcssbWhitelist::getEditTag,whitelist.getEditTag())
                .in(whitelist.getUserId() != null,DfdwTGcssbWhitelist::getUserId,whitelist.getUserId())
                .eq(DfdwTGcssbWhitelist::getDeleted,0)
                .orderByAsc(DfdwTGcssbWhitelist::getId));
        //更换枚举信息
        for (DfdwTGcssbWhitelist gcssbWhitelist :list.getRecords()) {
            //获取所有类别
            List<DfdwTGcssbMain> allMain = dfdwTGcssbMainService.getAllMain();
            //白名单权限
            String[] whitelistPermissionArray = gcssbWhitelist.getWhitelistPermission().split(",");
            //白名单编辑权限
            String[] editPermissionArray = gcssbWhitelist.getEditPermission().split(",");
            //白名单权限文本
            String whitelistPermissionText = "";
            //编辑权限文本
            String editPermissionText = "";
            for (String permission : whitelistPermissionArray) {
                if (StringUtils.isBlank(permission)) {
                    continue;
                }
                //过滤
                Optional<DfdwTGcssbMain> optionalMain = allMain.stream() .filter(data -> Integer.parseInt(permission.trim()) == data.getId()) .findFirst();
                //是否为空
                if (optionalMain.isPresent()) {
                    DfdwTGcssbMain main = optionalMain.get();
                    whitelistPermissionText += main.getName() + ",";
                }
            }
            for (String permission : editPermissionArray) {
                if (StringUtils.isBlank(permission)) {
                    continue;
                }
                //过滤
                Optional<DfdwTGcssbMain> optionalMain = allMain.stream() .filter(data ->  Integer.parseInt(permission.trim()) == data.getId()) .findFirst();
                //是否为空
                if (optionalMain.isPresent()) {
                    DfdwTGcssbMain main = optionalMain.get();
                    editPermissionText += main.getName() + ",";
                }
            }

            //去除最后一个逗号
            if (whitelistPermissionText.endsWith(",")) {
                whitelistPermissionText = whitelistPermissionText.substring(0, whitelistPermissionText.length() - 1);
            }
            if (editPermissionText.endsWith(",")) {
                editPermissionText = editPermissionText.substring(0, editPermissionText.length() - 1);
            }
            //白名单标识权限文本
            gcssbWhitelist.setWhitelistPermissionText(whitelistPermissionText);
            //白名单编辑标识权限文本
            gcssbWhitelist.setEditPermissionText(editPermissionText);
            //白名单编辑标识
            if (gcssbWhitelist.getEditTag() != null) {
                if(gcssbWhitelist.getEditTag()==1){
                    gcssbWhitelist.setEditTagText("是");
                }else {
                    gcssbWhitelist.setEditTagText("否");
                }
            }
            //白名单标识
            if (gcssbWhitelist.getWhitelistTag() != null){
                if(gcssbWhitelist.getWhitelistTag()==1){
                    gcssbWhitelist.setWhitelistTagText("是");
                }else {
                    gcssbWhitelist.setWhitelistTagText("否");
                }
            }
        }

        return list;
    }


    /**
     * 获取原有人员信息
     * @param param 查询参数
     */
    @Override
    public Result<Object> selectPageList(Map<String, String> param) {
        try{
            int pageNum = ParseUtil.tryParseInt(param.get("pageNum"));
            int pageSize = ParseUtil.tryParseInt(param.get("pageSize"),1);
            //工号
            String loginName = ParseUtil.tryParseString(param.get("loginName"));
            //名称
            String realName = ParseUtil.tryParseString(param.get("userRealName"));
            Page<Person> pageParam = new Page<>(pageNum, pageSize);
            //分页查询
            IPage<Person> personIPage = this.baseMapper.selecWhitetPage(pageParam,loginName,realName);
            return Result.ok(personIPage);
        }catch (Exception ex){
            String s = "程序产生异常:" + ex.getMessage() + "!";
            return Result.error(s);
        }
    }

    /**
     * 根据用户id获取白名单数据
     * @param id id
     * @return whiteDataList
     */
    @Override
    public List<DfdwTGcssbWhitelist> getWhiteDataById(Integer id) {
        List<DfdwTGcssbWhitelist> whiteDataList = this.dfdwTGcssbWhitelistMapper
                .selectList(new LambdaQueryWrapper<DfdwTGcssbWhitelist>()
                        .eq(DfdwTGcssbWhitelist::getDeleted,0)
                        .eq(DfdwTGcssbWhitelist::getUserId,id));
        if (CollectionUtils.isEmpty(whiteDataList)) {
            return null;
        }
        return whiteDataList;
    }

    /**
     * 判断登录用户是否有白名单编辑权限
     */
    @Override
    public Result<Object> isEditPermission() {
        //获取当前登录人id
        Integer id = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser().getId();
        //根据id查询白名单
        List<DfdwTGcssbWhitelist> whiteDataById = this.getWhiteDataById(id);
        if (whiteDataById == null) {
            return Result.ok(false);
        }
        DfdwTGcssbWhitelist editData = whiteDataById.get(0);
        //白名单编辑标识
        Integer editTag = editData.getEditTag();
        //是否有白名单编辑权限
        if (1 != editTag) {
            return Result.ok(false);
        }
        return Result.ok(true);
    }
}




