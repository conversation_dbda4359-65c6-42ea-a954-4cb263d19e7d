package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.gcssb.entity.GcssbBaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工程随身宝-字典数据
 * @TableName DFDW_DICT_DATA
 */
@TableName(value ="DFDW_DICT_DATA")
@Data
public class DfdwTDictData extends GcssbBaseEntity implements Serializable {
    /**
     * 字典编码
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字典排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 字典标签
     */
    @TableField(value = "label")
    private String label;

    /**
     * 字典键值
     */
    @TableField(value = "value")
    private String value;

    /**
     * 字典类型
     */
    @TableField(value = "dict_Type")
    private String dictType;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * 更新时间
     */
    @TableField(value = "updateTime")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private String deleted;

}