package com.soft.gcc.xtbg.gcssb.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMainTree;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist;
import com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbMainMapper;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbMainService;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbWhitelistService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_GCSSB_MAIN(工程随身宝-主表)】的数据库操作Service实现
 * @createDate 2024-06-14 11:02:58
 */
@Service
public class DfdwTGcssbMainServiceImpl extends ServiceImpl<DfdwTGcssbMainMapper, DfdwTGcssbMain>  implements DfdwTGcssbMainService{

    @Resource
    DfdwTGcssbMainMapper dfdwTGcssbMainMapper;
    @Resource
    DfdwTGcssbWhitelistService dfdwTGcssbWhitelistService;

    /**
     * 类别新增
     * @return
     */
    @Override
    @Transactional
    public int createMain( DfdwTGcssbMain dfdwTGcssbMain) {
        //如果ParentId为空则为第一级
        if (dfdwTGcssbMain.getParentId() == null){
            dfdwTGcssbMain.setParentId(0);
        }
        //计算全路径/topid/层级
        this.calculatePath(dfdwTGcssbMain);
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //创建时间
        dfdwTGcssbMain.setCreationDate(new Date());
        //创建用户id
        dfdwTGcssbMain.setCreateBy(user.getId());
        //创建人部门
        dfdwTGcssbMain.setCreateByDeptId(user.getGroupId());
        //更新时间
        dfdwTGcssbMain.setLastUpdateDate(new Date());
        //更新用户id
        dfdwTGcssbMain.setLastUpdateBy(user.getId());
        //新增
        int id = dfdwTGcssbMainMapper.insert(dfdwTGcssbMain);
        return id;
    }

    private void calculatePath(DfdwTGcssbMain dfdwTGcssbMain) {
        //查询所有类别名称
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>().eq(DfdwTGcssbMain::getDeleted,0));
        //全路径
        DfdwTGcssbMain fullPath = new DfdwTGcssbMain();
        fullPath.setNameFullPath(dfdwTGcssbMain.getName());
        //递归获取topid和全路径
        this.recursiveAddMain(fullPath, dfdwTGcssbMain.getParentId(),mainList);
        //获取层级
        int level = this.calculateDepth(fullPath.getNameFullPath()) -1;
        dfdwTGcssbMain.setLevel(level);
        //topid
        dfdwTGcssbMain.setTopId(fullPath.getTopId());
        //全路径
        dfdwTGcssbMain.setNameFullPath(fullPath.getNameFullPath());
    }

    /**
     * 递归获取全路径
     */
    private void recursiveAddMain(DfdwTGcssbMain fullPath, Integer parentId, List<DfdwTGcssbMain> mainList) {
        for (DfdwTGcssbMain parent : mainList) {
            if (Objects.equals(parentId, parent.getId())) {
                if (Objects.equals(parent.getParentId(), 0)) {
                    fullPath.setTopId(parent.getId());
                    fullPath.setNameFullPath(parent.getName() + "/" + fullPath.getNameFullPath());
                    return;
                } else {
                    fullPath.setNameFullPath(parent.getName() + "/" + fullPath.getNameFullPath());
                    recursiveAddMain(fullPath, parent.getParentId(), mainList);
                    return; // 递归完成后退出循环
                }
            }
        }
    }

    // 计算层级深度
    private int calculateDepth(String fullPath) {
        if (fullPath == null || fullPath.isEmpty()) {
            return 0;
        }
        return fullPath.split("/").length ;
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void deleteMain(Long id) {
        dfdwTGcssbMainMapper.deleteById(id);
    }

    /**
     * 类别修改
     * @return
     */
    @Override
    public void updateMation(DfdwTGcssbMain dfdwTGcssbMain) {
        //修改时间
        dfdwTGcssbMain.setLastUpdateDate(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        dfdwTGcssbMain.setLastUpdateBy(user.getId());
        //如果ParentId为空则为第一级
        if (dfdwTGcssbMain.getParentId() == null){
            dfdwTGcssbMain.setParentId(0);
        }
        //计算全路径/topid/层级
        this.calculatePath(dfdwTGcssbMain);
        //修改
        dfdwTGcssbMainMapper.updateById(dfdwTGcssbMain);
        //查询是否有子集,有子集都要修改
        updateSon(dfdwTGcssbMain,user.getId());
    }

    private void updateSon(DfdwTGcssbMain dfdwTGcssbMain,Integer userId) {
        //查询所有类别信息
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>().eq(DfdwTGcssbMain::getDeleted,0));
        for (DfdwTGcssbMain  main : mainList) {
            if (Objects.equals(dfdwTGcssbMain.getId(), main.getParentId())) {
                this.calculatePath(main); //修改路径
                main.setLastUpdateDate(new Date());//修改时间
                main.setLastUpdateBy(userId);//修改用户
                //修改
                dfdwTGcssbMainMapper.updateById(main);
                updateSon(main,userId);
                return;
            }
        }
    }

    /**
     * 获取类别名称树
     * @return
     */
    @Override
    public List<DfdwTGcssbMainTree> getMainNameList() {
        //查询所有类别名称
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>().eq(DfdwTGcssbMain::getDeleted,0));
        //返回树
        List<DfdwTGcssbMainTree> mainTrees = new ArrayList<>();
        // 1、获取第一级节点
        for (DfdwTGcssbMain main : mainList) {
            //如果ParentId=0,为第一级
            if (0 == main.getParentId()) {
                DfdwTGcssbMainTree mainTree = new DfdwTGcssbMainTree();
                mainTree.setId(main.getId()); // id
                mainTree.setIsEnable(main.getIsEnable()); // 是否可见
                mainTree.setLabel(main.getName()); //名称
                //放入返回的树中
                mainTrees.add(mainTree);
            }
        }
        // 2、递归获取子节点
        for (DfdwTGcssbMainTree contentVo : mainTrees) {
            recursiveMenuTree(contentVo,mainList);
        }

        return mainTrees;
    }

    /**
     * 获取一级类别树
     * @return
     */
    @Override
    public List<DfdwTGcssbMainTree> getMainFirstNameList() {
        //返回列表树
        List<DfdwTGcssbMainTree> treeList = new ArrayList<>();
        //查询所有类别名称
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>().eq(DfdwTGcssbMain::getDeleted,0));
        //查询父id是0的数据-0为第一级
        List<DfdwTGcssbMain> topMains = mainList.stream().filter(data -> data.getParentId() == 0).collect(Collectors.toList());
        //根据sort排序
        Collections.sort(topMains, Comparator.comparing(DfdwTGcssbMain::getSort));
        //遍历
        for (DfdwTGcssbMain topMain : topMains ) {
            DfdwTGcssbMainTree tree = new DfdwTGcssbMainTree();
            tree.setId(topMain.getId()); // id
            tree.setLabel(topMain.getName()); //名称
            //查询该数据子类
            List<DfdwTGcssbMain> childMains = mainList.stream().filter(data -> data.getParentId() == topMain.getId()).collect(Collectors.toList());
            //返回列表树
            List<DfdwTGcssbMainTree> childTreeList = new ArrayList<>();
            //根据sort排序
            Collections.sort(childMains, Comparator.comparing(DfdwTGcssbMain::getSort));
            //遍历
            for (DfdwTGcssbMain childMain : childMains) {
                DfdwTGcssbMainTree childTree = new DfdwTGcssbMainTree();
                childTree.setId(childMain.getId()); // id
                childTree.setLabel(childMain.getName()); //名称
                childTreeList.add(childTree);
            }
            //子树放入
            tree.setChildren(childTreeList);
            treeList.add(tree);
        }
        return treeList;
    }

    /**
     * 根据权限获取类别管理名称树
     * 如果没有白名单权限返回所有
     */
    @Override
    public Result<Object> getMainListByPermission() {
        //查询所有类别名称
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>().eq(DfdwTGcssbMain::getDeleted,0));
        //获取当前登录人id
        Integer id = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser().getId();
        //根据id查询白名单
        List<DfdwTGcssbWhitelist> whiteDataById = dfdwTGcssbWhitelistService.getWhiteDataById(id);
        if (whiteDataById == null) {
            return Result.ok(this.getMainNameList());
        }
        DfdwTGcssbWhitelist editData = whiteDataById.get(0);
        //白名单编辑标识
        Integer editTag = editData.getEditTag();
        //是否有白名单编辑权限
        if (1 != editTag) {
            return Result.ok(this.getMainNameList());
        }
        //返回树
        List<DfdwTGcssbMainTree> mainTrees = new ArrayList<>();
        String[] mainIds = editData.getEditPermission().split(",");
        for (String mainId : mainIds) {
            //根据id查询类别
            DfdwTGcssbMain main = dfdwTGcssbMainMapper.selectById(mainId);
            DfdwTGcssbMainTree mainTree = new DfdwTGcssbMainTree();
            mainTree.setId(main.getId()); // id
            mainTree.setLabel(main.getName()); //名称
            mainTree.setIsEnable(main.getIsEnable()); //是否启用
            //放入返回的树中
            mainTrees.add(mainTree);
        }
        // 2、递归获取子节点
        for (DfdwTGcssbMainTree contentVo : mainTrees) {
            recursiveMenuTree(contentVo,mainList);
        }
        return Result.ok(mainTrees);
    }


    /**
     * 根据是否启用获取类别管理名称树
     * @return
     */
    @Override
    public List<DfdwTGcssbMainTree> getMainNameListByIsEnable() {
        // 通过权限筛选
        List<DfdwTGcssbMainTree> treeList = (List<DfdwTGcssbMainTree>) this.getMainListByPermission().getResult();
        //根据是否启用筛选
        treeList = removeDisabledNodes(treeList);
        return treeList;
    }

    private List<DfdwTGcssbMainTree> removeDisabledNodes(List<DfdwTGcssbMainTree> nodes) {
        List<DfdwTGcssbMainTree> enabledNodes = new ArrayList<>();
        if (nodes != null) {
            for (DfdwTGcssbMainTree node : nodes) {
                if (node.getIsEnable() != 0) {
                    // 递归处理子节点，并返回处理后的子节点列表
                    List<DfdwTGcssbMainTree> children = removeDisabledNodes(node.getChildren());
                    node.setChildren(children); // 更新子节点列表
                    enabledNodes.add(node); // 添加处理后的节点到新的列表中
                }
            }
        }
        return enabledNodes;
    }

    /**
     * 根据categoryId获取类别数据
     * @param categoryId
     * @return
     */
    @Override
    public DfdwTGcssbMain getMainCategoryId(int categoryId) {
        //查询类别名称
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>()
                .eq(DfdwTGcssbMain::getId,categoryId)
                .eq(DfdwTGcssbMain::getDeleted,0));

        return mainList.get(0);
    }

    /**
     * 获取所有类别
     * @return
     */
    @Override
    public List<DfdwTGcssbMain> getAllMain() {
        //查询所有类别名称
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbMain>()
                .eq(DfdwTGcssbMain::getDeleted,0));
        return mainList;
    }

    /**
     * 获取类别名称递归
     * @param parent
     * @param list
     */
    private void recursiveMenuTree(DfdwTGcssbMainTree parent, List<DfdwTGcssbMain> list) {
        List<DfdwTGcssbMainTree> childList = new ArrayList<>();
        for (DfdwTGcssbMain child : list) {
            if (Objects.equals(parent.getId(), child.getParentId())) {
                DfdwTGcssbMainTree mainTree = new DfdwTGcssbMainTree();
                mainTree.setId(child.getId()); // id
                mainTree.setLabel(child.getName()); //名称
                mainTree.setIsEnable(child.getIsEnable()); //是否启用
                mainTree.setSort(child.getSort()); //排序
                recursiveMenuTree(mainTree, list);
                childList.add(mainTree);
                //根据sort排序
                Collections.sort(childList, Comparator.comparing(DfdwTGcssbMainTree::getSort));
                parent.setChildren(childList);
            }

        }
    }


    /**
     * 根据父id获取数据
     * @return
     */
    @Override
    public IPage<DfdwTGcssbMain>  getListByParentId(DfdwTGcssbMain dfdwTGcssbMain) {
        IPage<DfdwTGcssbMain> list = new Page<>();
        list.setCurrent(dfdwTGcssbMain.getPageNum());
        list.setSize(dfdwTGcssbMain.getPageSize());
        //查询分页
        list = this.page(list, new LambdaQueryWrapper<DfdwTGcssbMain>()
                .eq(DfdwTGcssbMain::getDeleted,0)
                .eq(DfdwTGcssbMain::getParentId,dfdwTGcssbMain.getParentId())
                .orderByAsc(DfdwTGcssbMain::getSort));
        for (DfdwTGcssbMain main :list.getRecords() ) {
            //是否启用
            if (main.getIsEnable() != null) {
                if(main.getIsEnable()==1){
                    main.setIsEnableText("启用");
                }else {
                    main.setIsEnableText("不启用");
                }
            }
            //是否可见
            if (main.getIsVisible() != null){
                if(main.getIsVisible()==1){
                    main.setIsVisibleText("可见");
                }else {
                    main.setIsVisibleText("不可见");
                }
            }
            //类型
            if (main.getType() != null){
                if(main.getType()==1){
                    main.setTypeText("类别");
                } else if (main.getType()==2){
                    main.setTypeText("书籍");
                } else {
                    main.setTypeText("章节");
                }
            }
        }
        return list;
    }

    /**
     * 根据id获取文件Path
     * @param id
     * @return
     */
    @Override
    public TFile getImagePath(Integer id) {
        DfdwTGcssbMain main = this.dfdwTGcssbMainMapper.selectById(id);
        TFile file = this.dfdwTGcssbMainMapper.selectImgTFile(main.getFileId());
        return file;
    }
}




