package com.soft.gcc.xtbg.gcssb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_WHITELIST(工程随身宝-白名单)】的数据库操作Service
* @createDate 2024-06-14 11:03:04
*/
public interface DfdwTGcssbWhitelistService extends IService<DfdwTGcssbWhitelist> {

    /**
     * 新增
     * @param whitelist
     * @return
     */
    Result<Object> create(DfdwTGcssbWhitelist whitelist);

    /**
     * 删除
     * @param id
     */
    void deleteWhitelist(Long id);

    /**
     * 获取白名单数据
     * @return
     */
    IPage<DfdwTGcssbWhitelist> getList(DfdwTGcssbWhitelist whitelist);


    /**
     * 修改
     *
     * @return
     */
    Result<Object> updateWhitelist(DfdwTGcssbWhitelist whitelist);

    /**
     * 获取原有人员信息
     * @param param
     * @return
     */
    Result<Object> selectPageList(Map<String, String> param);

    /**
     * 根据用户id获取白名单数据
     * @param id
     * @return
     */
    List<DfdwTGcssbWhitelist> getWhiteDataById (Integer id);


    /**
     * 判断登录用户是否有白名单编辑权限
     */
    Result<Object> isEditPermission();

}
