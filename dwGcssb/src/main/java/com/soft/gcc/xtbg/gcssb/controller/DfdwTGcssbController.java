package com.soft.gcc.xtbg.gcssb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMainTree;
import com.soft.gcc.xtbg.gcssb.dto.UploadInfoDto;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbMainService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RequestMapping("/gcssb/main")
@RestController
@Validated
public class DfdwTGcssbController extends BaseController {

    @Resource
    private DfdwTGcssbMainService dfdwTGcssbMainService;


    /**
     * 新增
     * @param dfdwTGcssbMain
     * @return
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX02')")
    public Result<Object> createMain( @RequestBody DfdwTGcssbMain dfdwTGcssbMain) {
        int id =  dfdwTGcssbMainService.createMain(dfdwTGcssbMain);
        return Result.ok(id) ;
    }

    /**
     * 删除
     * @return
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX04')")
    public Result<Object> deleteMain(@RequestParam("id") Long id) {
        dfdwTGcssbMainService.deleteMain(id);
        return Result.ok();
    }

    /**
     * 修改
     * @return
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX03')")
    public Result<Object> updateMation(@RequestBody DfdwTGcssbMain dfdwTGcssbMain) {
        dfdwTGcssbMainService.updateMation(dfdwTGcssbMain);
        return Result.ok();
    }


    /**
     * 获取类别管理名称树
     * @return
     */
    @GetMapping("/getMainNameList")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX01')")
    public Result<Object> getMainNameList() {
        List<DfdwTGcssbMainTree> treeList =  dfdwTGcssbMainService.getMainNameList();
        return Result.ok(treeList) ;
    }

    /**
     * 获取一级类别管理名称树
     * @return
     */
    @GetMapping("/getMainFirstNameList")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX01')")
    public Result<Object> getMainFirstNameList() {
        List<DfdwTGcssbMainTree> treeList =  dfdwTGcssbMainService.getMainFirstNameList();
        return Result.ok(treeList) ;
    }

    /**
     * 根据权限获取类别管理名称树
     * @return
     */
    @GetMapping("/getMainListByPermission")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX01')")
    public Result<Object> getMainListByPermission() {
        Result<Object> treeList =  dfdwTGcssbMainService.getMainListByPermission();
        return treeList ;
    }

    /**
     * 根据是否启用获取类别管理名称树
     * @return
     */
    @GetMapping("/getMainNameListByIsEnable")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX03')")
    public Result<Object> getMainNameListByIsEnable() {
        List<DfdwTGcssbMainTree> treeList =  dfdwTGcssbMainService.getMainNameListByIsEnable();
        return Result.ok(treeList) ;
    }

    /**
     * 根据父id获取数据
     * @return
     */
    @PostMapping("/getListByParentId")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX01')")
    public Result<Object> getListByParentId(@RequestBody DfdwTGcssbMain dfdwTGcssbMain) {
        IPage<DfdwTGcssbMain>  mainList =  dfdwTGcssbMainService.getListByParentId(dfdwTGcssbMain);
        return Result.ok(mainList) ;
    }


    /**
     * 更新文件信息
     * @return
     */
    @PostMapping("/updCategoryFile")
    public Result<Object> updCategoryFile(@RequestBody UploadInfoDto uploadInfo){
        DfdwTGcssbMain dfdwTGcssbMain = dfdwTGcssbMainService.getById(uploadInfo.getYwId());
        if(dfdwTGcssbMain !=null){
            dfdwTGcssbMain.setFileId(uploadInfo.getFileId());
            dfdwTGcssbMain.setFilePath(uploadInfo.getFilePath());
            dfdwTGcssbMainService.updateById(dfdwTGcssbMain);
            return Result.ok("成功");
        }else{
            return Result.error("获取数据失败！");
        }
    }

    /**
     * 根据id获取图片文件
     * @param id
     * @return
     */
    @RequestMapping("/image")
    public Result<Object> image(Integer id){
        TFile imageTFile = dfdwTGcssbMainService.getImagePath(id);
        return Result.ok(imageTFile);
    }
}
