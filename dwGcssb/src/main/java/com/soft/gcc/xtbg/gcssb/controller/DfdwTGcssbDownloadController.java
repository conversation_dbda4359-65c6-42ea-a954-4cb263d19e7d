package com.soft.gcc.xtbg.gcssb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbDownloadLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@RequestMapping("/gcssb/download")
@RestController
@Validated
public class DfdwTGcssbDownloadController extends BaseController {

    @Resource
    private DfdwTGcssbDownloadLogService dfdwTGcssbDownloadLogService;


    /**
     * 新增
     * @return
     */
    @PostMapping("/create")
//    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX01')")
    public Result<Object> createDownload( @RequestBody DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog) {
        int id =  dfdwTGcssbDownloadLogService.createDownload(dfdwTGcssbDownloadLog);
        return Result.ok(id) ;
    }


    /**
     * 删除
     * @return
     */
    @DeleteMapping("/delete")
//    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX01')")
    public Result<Object> deleteDownload(@RequestParam("id") Long id) {
        dfdwTGcssbDownloadLogService.deleteDownload(id);
        return Result.ok();
    }

    /**
     * 修改
     * @return
     */
    @PutMapping("/update")
//    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX01')")
    public Result<Object> updateDownload(@RequestBody DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog) {
        dfdwTGcssbDownloadLogService.updateDownload(dfdwTGcssbDownloadLog);
        return Result.ok();
    }


    /**
     * 获取下载日志据page
     * @return
     */
    @PostMapping("/getListPage")
    @PreAuthorize("@ss.hasPermi('JDWGB01ZX01QX01')")
    public Result<Object> getListPage(@RequestBody DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog) {
        IPage<DfdwTGcssbDownloadLog>  downloadIPage =  dfdwTGcssbDownloadLogService.getListPage(dfdwTGcssbDownloadLog);
        return Result.ok(downloadIPage) ;
    }


    /**
     * 日志导出
     * @param dfdwTGcssbDownloadLog
     * @param response
     */
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermi('JDWGB01ZX01QX02')")
    public void exportDownloadLoExcel(@RequestBody DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog,HttpServletResponse response)  {
        try {
            dfdwTGcssbDownloadLogService.exportDownloadLog(dfdwTGcssbDownloadLog,response);
        }catch (Exception ex){
            Result.error(ex.getMessage());
        }
    }




}
