package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.gcssb.entity.GcssbBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 工程随身宝-下载日志
 * @TableName DFDW_T_GCSSB_DOWNLOAD_LOG
 */
@TableName(value ="DFDW_T_GCSSB_DOWNLOAD_LOG")
@Data
public class DfdwTGcssbDownloadLog extends GcssbBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户Id
     */
    @TableField(value = "userId")
    private Integer userId;

    /**
     * 用户名称
     */
    @TableField(value = "userName")
    private String userName;

    /**
     * 书籍Id
     */
    @TableField(value = "booksId")
    private Integer booksId;

    /**
     * 书籍名称
     */
    @TableField(value = "booksName")
    private String booksName;

    /**
     * 下载时间
     */
    @TableField(value = "downloadTime")
    private Date downloadTime;

    /**
     * 手机下载保存路径
     */
    @TableField(value = "downloadPath")
    private String downloadPath;

    /**
     * PC/APP
     */
    @TableField(value = "downloadType")
    private String downloadType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 开始下载时间
     */
    @TableField(exist = false)
    private String  downloadTimeStart;

    /**
     * 结束下载时间
     */
    @TableField(exist = false)
    private String downloadTimeEnd;
}

