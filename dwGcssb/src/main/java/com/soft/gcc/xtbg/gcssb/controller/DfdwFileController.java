package com.soft.gcc.xtbg.gcssb.controller;

import com.aspose.words.Document;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.dto.SplitInfo;
import com.soft.gcc.xtbg.gcssb.service.IGcssbFileService;
import com.soft.gcc.xtbg.gcssb.util.AliyunOSSUtils;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @description pdf切分
 * @date 2024-06-14 11:05:23
 */
@RequestMapping("/gcssb/File")
@RestController
public class DfdwFileController extends BaseController {

    @Autowired
    private IGcssbFileService gcssbFileService;

    private static final String UPLOAD_DIR = "D:\\Save\\directory\\"; // 设置分片保存目录

    @PostMapping("/uploadChunk")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX05')")
    public Result<Object> uploadChunk(@RequestParam(value = "chunk",required = false) MultipartFile chunk,
                                      @RequestParam("fileName") String fileNameStr,
                                      @RequestParam("chunkIndex") int chunkIndex,
                                      @RequestParam("totalChunks") int totalChunks,
                                      @RequestParam("ywId") int ywId) {
        PersonEntity person = user();
        return gcssbFileService.uploadChunk(chunk, fileNameStr, chunkIndex, totalChunks, ywId, person);
    }


    /**
     * 分割上传
     * @return
     */
    @PostMapping("/uploadSplit")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX05')")
    public Result<Object> uploadSplit(@RequestBody SplitInfo splitInfo) {
        String hz = splitInfo.getHz();
        if("pdf".equals(hz) ){
            int ywId = splitInfo.getYwId();
            String newName = splitInfo.getNewName();
            String fileNameChunk = splitInfo.getFileNameChunk();
            Integer fileId = splitInfo.getFileId();
            return gcssbFileService.uploadSplit(hz,ywId,newName,fileNameChunk);
        }else{
            return Result.ok("其他文件分割");
        }

    }




    /**
     * 上传
     * @param file    file文件
     * @param request
     * @param ywId    ywId
     * @return
     */
    @RequestMapping("/upload")
    public Result<Object> upload(MultipartFile file, HttpServletRequest request, Integer ywId,
                                 @RequestParam(value = "hjID",required = false,defaultValue = "")  String hjID) {
        PersonEntity person = user();
        if (null == file) {
            file = ((StandardMultipartHttpServletRequest) request).getMultiFileMap().get("uploadFile").get(0);
        }
        return gcssbFileService.upload(file, ywId, hjID,person);

    }





    /**
     * 上传类型文件
     * @param file    file文件
     * @param request
     * @param ywId    书籍Id
     * @return
     */
    @RequestMapping("/uploadImg")
    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX05')")
    public Result<Object> uploadImg(MultipartFile file, HttpServletRequest request, Integer ywId){
        PersonEntity person = user();
        return gcssbFileService.uploadImg(file,person,ywId);
    }



    public static void splitPDF(String pdfFilePath, String outputDir) throws IOException {
        try (PDDocument document = PDDocument.load(new File(pdfFilePath))) {
            int pageCount = document.getNumberOfPages();
            for (int i = 0; i < pageCount; i++) {
                PDPage page = document.getPage(i);
                PDDocument outputDoc = new PDDocument();
                outputDoc.addPage(page);

                String outputPath = outputDir + "/page-" + (i + 1) + ".pdf";
                outputDoc.save(outputPath);
                outputDoc.close();
            }
        }
    }


    public static void TextWordToPdf(String wordPath, String pdfPath) throws Exception {

        FileOutputStream os = null;
        try {
            long old = System.currentTimeMillis();
            File file = new File(pdfPath);
            os = new FileOutputStream(file);
            Document doc = new Document(wordPath);
            doc.save(os, com.aspose.words.SaveFormat.PDF);
            long now = System.currentTimeMillis();
            System.out.println("共耗时：" + ((now - old) / 1000.0) + "秒"); // 转化用时
        } finally {
            if (os != null) {
                os.close();
            }
        }
    }

    /**
     * 分片下载书籍
     *
     * @return
     */
    @RequestMapping("/downloadChunk")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX06')")
    public void downloadChunk(@RequestParam("ywId") Integer ywId,
                              @RequestParam("filePath") String filePath,
                              @RequestParam("fileName") String fileName,
                              @RequestParam("chunkSize") Integer chunkSize,
                              @RequestParam("chunkTotal") Integer chunkTotal,
                              @RequestParam("index")Integer index,
                              HttpServletResponse response) {
        PersonEntity person = user();
        gcssbFileService.downloadChunk(ywId, filePath, fileName, chunkSize, chunkTotal, index, response, person);
    }

    public static void main(String[] args) {
        try {
            String path = "Upload/FileManage/dfdwGcssb/Books/aaa.pdf";
            String url =  AliyunOSSUtils.uploadLocalFile(path, "aaa.pdf", "D:\\Save\\yykj\\dfdwGcssb\\4.pdf");

            System.out.println(url);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
