package com.soft.gcc.xtbg.gcssb.mapper;

import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_MAIN(工程随身宝-主表)】的数据库操作Mapper
* @createDate 2024-06-14 11:02:58
* @Entity com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain
*/
public interface DfdwTGcssbMainMapper extends BaseMapper<DfdwTGcssbMain> {

    /**
     * 根据fileId获取图片文件
     * @param fileId
     * @return
     */
    TFile selectImgTFile(Integer fileId);
}




