package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.soft.gcc.xtbg.gcssb.entity.GcssbBaseEntity;
import lombok.Data;

/**
 * 工程随身宝-书籍
 * @TableName DFDW_T_GCSSB_BOOKS
 */
@TableName(value ="DFDW_T_GCSSB_BOOKS")
@Data
public class DfdwTGcssbBooks extends GcssbBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 类别Id
     */
    @TableField(value = "categoryId")
    private Integer categoryId;

    /**
     * 是否启用(0不启用，1启用)
     */
    @TableField(value = "isEnable")
    private Integer isEnable;

    /**
     * 是否可见(0不可见，1可见)
     */
    @TableField(value = "isVisible")
    private Integer isVisible;

    /**
     * 前置页码
     */
    @TableField(value = "advancePageNum")
    private Integer advancePageNum;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "createBy")
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(value = "creationDate")
    private Date creationDate;

    /**
     * 更新人
     */
    @TableField(value = "lastUpdateBy")
    private Integer lastUpdateBy;

    /**
     * 更新时间
     */
    @TableField(value = "lastUpdateDate")
    private Date lastUpdateDate;

    /**
     * 创建人所在部门
     */
    @TableField(value = "createByDeptId")
    private Integer createByDeptId;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "1")
    private String deleted;

    //-1代表还没分片成功，0代表不需要统计页数的
    @TableField(value = "totalPages")
    private Integer totalPages;

    @TableField(value = "fileId")
    private Integer fileId;

    @TableField(value = "uploadMsg")
    private String uploadMsg;

    @TableField(value = "fileSize")
    private Long fileSize;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 是否启用文本
     */
    @TableField(exist = false)
    private String isEnableText;

    /**
     * 是否可见文本
     */
    @TableField(exist = false)
    private String isVisibleText;

    /**
     * 类别名称
     */
    @TableField(exist = false)
    private String categoryText;
}
