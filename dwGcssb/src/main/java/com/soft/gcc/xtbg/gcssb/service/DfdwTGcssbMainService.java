package com.soft.gcc.xtbg.gcssb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMainTree;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_MAIN(工程随身宝-主表)】的数据库操作Service
* @createDate 2024-06-14 11:02:58
*/
public interface DfdwTGcssbMainService extends IService<DfdwTGcssbMain> {

    /**
     * 类别新增
     * @return
     */
    int createMain( DfdwTGcssbMain dfdwTGcssbMain);

    /**
     * 获取类别名称树
     * @return
     */
    List<DfdwTGcssbMainTree> getMainNameList();

    /**
     * 获取一级类别管理名称树
     * @return
     */
    List<DfdwTGcssbMainTree> getMainFirstNameList();


    /**
     * 根据父id获取数据
     * @return
     */
    IPage<DfdwTGcssbMain> getListByParentId(DfdwTGcssbMain dfdwTGcssbMain);


    /**
     * 类别修改
     * @return
     */
    void updateMation(DfdwTGcssbMain dfdwTGcssbMain);


    /**
     * 删除
     * @return
     */
    void deleteMain(Long id);

    /**
     * 根据是否启用获取类别管理名称树
     * @return
     */
    List<DfdwTGcssbMainTree> getMainNameListByIsEnable();


    /**
     * 根据权限获取类别管理名称树
     * @return
     */
    Result<Object> getMainListByPermission();

    /**
     * 根据categoryId获取类别数据
     * @param categoryId
     * @return
     */
    DfdwTGcssbMain getMainCategoryId(int categoryId);

    /**
     * 获取所有类别
     * @return
     */
    List<DfdwTGcssbMain> getAllMain();

    /**
     * 根据id获取图片文件
     * @param id
     * @return
     */
    TFile getImagePath(Integer id);
}
