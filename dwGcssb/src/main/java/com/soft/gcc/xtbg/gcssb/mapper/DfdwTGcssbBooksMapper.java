package com.soft.gcc.xtbg.gcssb.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_BOOKS(工程随身宝-书籍)】的数据库操作Mapper
* @createDate 2024-06-19 15:19:36
* @Entity com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks
*/
public interface DfdwTGcssbBooksMapper extends BaseMapper<DfdwTGcssbBooks> {

    /**
     * 分页查询
     * @param pageParam
     * @param name
     * @return
     */
    IPage<DfdwTGcssbBooks> selectBooksPage(Page<DfdwTGcssbBooks> pageParam,@Param("name") String name);

    /**
     * 批量新增
     * @param booksList
     */
    void batchInsert(List<DfdwTGcssbBooks> booksList);
}




