package com.soft.gcc.xtbg.gcssb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_DOWNLOAD_LOG(工程随身宝-下载日志)】的数据库操作Service
* @createDate 2024-06-14 11:01:46
*/
public interface DfdwTGcssbDownloadLogService extends IService<DfdwTGcssbDownloadLog> {

    /**
     * 新增
     * @return
     */
    int createDownload(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog);


    /**
     * 修改
     * @return
     */
    void updateDownload(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog);

    /**
     * 获取下载日志据page
     * @return
     */
    IPage<DfdwTGcssbDownloadLog> getListPage(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog);

    /**
     * 删除
     * @return
     */
    void deleteDownload(Long id);

    /**
     * 日志导出
     * @param dfdwTGcssbDownloadLog
     * @param response
     */
    void exportDownloadLog(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog, HttpServletResponse response);
}
