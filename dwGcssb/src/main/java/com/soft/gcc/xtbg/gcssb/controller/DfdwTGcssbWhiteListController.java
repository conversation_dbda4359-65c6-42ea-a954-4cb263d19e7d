package com.soft.gcc.xtbg.gcssb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbWhitelistService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;


@RequestMapping("/gcssb/whiteList")
@RestController
@Validated
public class DfdwTGcssbWhiteListController extends BaseController {

    @Resource
    private DfdwTGcssbWhitelistService dfdwTGcssbWhitelistService;


    /**
     * 新增
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX02')")
    public Result<Object> createWhitelist(@RequestBody DfdwTGcssbWhitelist whitelist) {
        return dfdwTGcssbWhitelistService.create(whitelist);
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX04')")
    public Result<Object> deleteWhitelist(@RequestParam("id") Long id) {
        dfdwTGcssbWhitelistService.deleteWhitelist(id);
        return Result.ok();
    }

    /**
     * 修改
     *
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX03')")
    public Result<Object> update(@RequestBody DfdwTGcssbWhitelist whitelist) {
        return  dfdwTGcssbWhitelistService.updateWhitelist(whitelist);
    }

    /**
     * 获取白名单数据
     */
    @PostMapping("/getList")
    @PreAuthorize("@ss.hasPermi('JDWGB01BM01QX01')")
    public Result<Object> getList(@RequestBody DfdwTGcssbWhitelist whitelist) {
        IPage<DfdwTGcssbWhitelist> whitelistIPage =  dfdwTGcssbWhitelistService.getList(whitelist);
        return Result.ok(whitelistIPage);
    }

    /**
     * 获取原有人员信息
     * @param param 分页数据
     */
    @PostMapping("/getPersonEntityList")
    public Result<Object> getPersonEntityList(@RequestBody Map<String, String> param){
        return dfdwTGcssbWhitelistService.selectPageList(param);
    }

    /**
     * 判断登录用户是否有白名单编辑权限
     */
    @GetMapping("/isEditPermission")
    public Result<Object> isEditPermission(){
        return dfdwTGcssbWhitelistService.isEditPermission();
    }
}
