package com.soft.gcc.xtbg.gcssb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog;
import com.soft.gcc.xtbg.gcssb.entity.Person;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbDownloadLogService;
import com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbDownloadLogMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_DOWNLOAD_LOG(工程随身宝-下载日志)】的数据库操作Service实现
* @createDate 2024-06-14 11:01:46
*/
@Service
public class DfdwTGcssbDownloadLogServiceImpl extends ServiceImpl<DfdwTGcssbDownloadLogMapper, DfdwTGcssbDownloadLog>
    implements DfdwTGcssbDownloadLogService{

    @Resource
    DfdwTGcssbDownloadLogMapper dfdwTGcssbDownloadLogMapper;

    /**
     * 新增
     * @param dfdwTGcssbDownloadLog
     * @return
     */
    @Override
    public int createDownload(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog) {
        int id = dfdwTGcssbDownloadLogMapper.insert(dfdwTGcssbDownloadLog);
        return id;
    }

    /**
     * 修改
     * @param dfdwTGcssbDownloadLog
     * @return
     */
    @Override
    public void updateDownload(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog) {
        dfdwTGcssbDownloadLogMapper.updateById(dfdwTGcssbDownloadLog);
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void deleteDownload(Long id) {
        dfdwTGcssbDownloadLogMapper.deleteById(id);
    }

    /**
     * 获取下载日志据page
     * @return
     */
    @Override
    public IPage<DfdwTGcssbDownloadLog> getListPage(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog) {
        //分页参数
        Page<DfdwTGcssbDownloadLog> pageParam = new Page<>(dfdwTGcssbDownloadLog.getPageNum(), dfdwTGcssbDownloadLog.getPageSize());
        //分页查询
        IPage<DfdwTGcssbDownloadLog> downloadLogIPage = this.baseMapper.selecDownloadLogPage(pageParam,dfdwTGcssbDownloadLog);
        return downloadLogIPage;
    }


    /**
     * 日志导出
     * @param dfdwTGcssbDownloadLog
     * @param response
     */
    @Override
    public void exportDownloadLog(DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog, HttpServletResponse response) {
        //查询日期
        List<DfdwTGcssbDownloadLog> queryList = this.list(
                new LambdaQueryWrapper<DfdwTGcssbDownloadLog>()
                        .like(dfdwTGcssbDownloadLog.getUserName() !=null ,DfdwTGcssbDownloadLog::getUserName, dfdwTGcssbDownloadLog.getUserName())
                        .like(dfdwTGcssbDownloadLog.getBooksName() !=null ,DfdwTGcssbDownloadLog::getBooksName, dfdwTGcssbDownloadLog.getBooksName())
                        .between(dfdwTGcssbDownloadLog.getDownloadTimeStart() != null && dfdwTGcssbDownloadLog.getDownloadTimeEnd() !=null,DfdwTGcssbDownloadLog::getDownloadTime,dfdwTGcssbDownloadLog.getDownloadTimeStart(),dfdwTGcssbDownloadLog.getDownloadTimeEnd())
        );
        //解析日期,转化为可视化时间
        dataParse(queryList);
        List<ToolHelper.ExportColumnMode> downloadList = new ArrayList<>();
        downloadList.add(new ToolHelper.ExportColumnMode("userId", "用户ID", 20));
        downloadList.add(new ToolHelper.ExportColumnMode("userName", "用户名", 30));
        downloadList.add(new ToolHelper.ExportColumnMode("booksId", "书籍ID", 20));
        downloadList.add(new ToolHelper.ExportColumnMode("booksName", "书籍名称", 30));
        downloadList.add(new ToolHelper.ExportColumnMode("downloadTimeStart", "下载时间", 30));
        ToolHelper.ExportExcelList(queryList, "下载日志", downloadList,false,response );

    }

    private static void dataParse(List<DfdwTGcssbDownloadLog> queryList) {
        for (DfdwTGcssbDownloadLog downloadLog : queryList) {
            // 定义原始日期格式
            DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
            // 定义目标日期时间格式
            DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            // 解析原始日期字符串
            LocalDateTime dateTime = LocalDateTime.parse( downloadLog.getDownloadTime()+"", originalFormatter);
            // 格式化为目标日期时间字符串
            String formattedDate = dateTime.format(targetFormatter);
            //暂存下载日期
            downloadLog.setDownloadTimeStart(formattedDate);
        }
    }


}




