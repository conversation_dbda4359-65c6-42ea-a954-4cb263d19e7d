package com.soft.gcc.xtbg.gcssb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_BOOKS(工程随身宝-书籍)】的数据库操作Service
* @createDate 2024-06-19 15:19:36
*/
public interface DfdwTGcssbBooksService extends IService<DfdwTGcssbBooks> {

    /**
     * 新增
     * @return
     */
    int createBooks(DfdwTGcssbBooks dfdwTGcssbBooks);

    /**
     * 修改
     * @return
     */
    void updateBooks(DfdwTGcssbBooks dfdwTGcssbBooks);


    /**
     * 删除
     * @return
     */
    void deleteBooks(Long id);

    /**
     * 根据类别id获取数据
     * @return
     */
    IPage<DfdwTGcssbBooks> getListByCategoryId(DfdwTGcssbBooks dfdwTGcssbBook);


    /**
     * 获取书籍数据
     * @return
     */
    IPage<DfdwTGcssbBooks> getBooksPageList(Map<String, String> param);

    /**
     * 导入书籍excel模板
     * @return
     */
    Result<Object> dfdwTGcssbBooksService(HttpServletResponse response) throws IOException;

    /**
     * 书籍导入
     */
    Result<Object> importExcelBooks(MultipartFile file, Integer categoryId);
}
