package com.soft.gcc.xtbg.gcssb.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbChapterService;
import com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbChapterMapper;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_CHAPTER(工程随身宝-章节)】的数据库操作Service实现
* @createDate 2024-06-19 15:19:36
*/
@Service
public class DfdwTGcssbChapterServiceImpl extends ServiceImpl<DfdwTGcssbChapterMapper, DfdwTGcssbChapter>
    implements DfdwTGcssbChapterService{

    @Resource
    DfdwTGcssbChapterMapper dfdwTGcssbChapterMapper;

    /**
     * 新增
     * @return
     */
    @Override
    public int createChapter(DfdwTGcssbChapter dfdwTGcssbChapter) {
        //如果为空则为第一级
        if (dfdwTGcssbChapter.getParentId() ==null) {
            dfdwTGcssbChapter.setParentId(0);
        }
        //删除标识-0-可见
        dfdwTGcssbChapter.setDeleted("0");
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //新增用户id
        dfdwTGcssbChapter.setCreateBy(user.getId());
        //新增时间
        dfdwTGcssbChapter.setCreationDate(new Date());
        //创建人部门
        dfdwTGcssbChapter.setCreateByDeptId(user.getGroupId());
        //更新用户id
        dfdwTGcssbChapter.setLastUpdateBy(user.getId());
        //更新时间
        dfdwTGcssbChapter.setLastUpdateDate(new Date());
        //计算全路径/topid/层级
        this.calculatePath(dfdwTGcssbChapter);
        //新增
        int id = dfdwTGcssbChapterMapper.insert(dfdwTGcssbChapter);
        return id;
    }

    //计算全路径/topid/层级
    private void calculatePath(DfdwTGcssbChapter dfdwTGcssbChapter) {
        //查询所有章节名称
        List<DfdwTGcssbChapter> mainList = dfdwTGcssbChapterMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbChapter>().eq(DfdwTGcssbChapter::getDeleted,0));
        //全路径
        DfdwTGcssbChapter fullPath = new DfdwTGcssbChapter();
        fullPath.setNameFullPath(dfdwTGcssbChapter.getName());
        //递归获取topid和全路径
        this.recursiveAddMain(fullPath, dfdwTGcssbChapter.getParentId(),mainList);
        //topid
        dfdwTGcssbChapter.setTopId(fullPath.getTopId());
        //全路径
        dfdwTGcssbChapter.setNameFullPath(fullPath.getNameFullPath());
    }

    /**
     * 递归获取全路径
     */
    private void recursiveAddMain(DfdwTGcssbChapter fullPath, Integer parentId, List<DfdwTGcssbChapter> mainList) {
        for (DfdwTGcssbChapter parent : mainList) {
            if (Objects.equals(parentId, parent.getId())) {
                if (Objects.equals(parent.getParentId(), 0)) {
                    fullPath.setTopId(parent.getId());
                    fullPath.setNameFullPath(parent.getName() + "/" + fullPath.getNameFullPath());
                    return;
                } else {
                    fullPath.setNameFullPath(parent.getName() + "/" + fullPath.getNameFullPath());
                    recursiveAddMain(fullPath, parent.getParentId(), mainList);
                    return; // 递归完成后退出循环
                }
            }
        }
    }


    /**
     * 删除
     */
    @Override
    public void deleteChapter(Long id) {
        this.dfdwTGcssbChapterMapper.deleteById(id);
    }

    /**
     * 修改
     * @return
     */
    @Override
    public void updateChapter(DfdwTGcssbChapter dfdwTGcssbChapter) {
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        dfdwTGcssbChapter.setLastUpdateBy(user.getId());
        //修改时间
        dfdwTGcssbChapter.setLastUpdateDate(new Date());
        //如果ParentId为空则为第一级
        if (dfdwTGcssbChapter.getParentId() == null){
            dfdwTGcssbChapter.setParentId(0);
        }
        //计算全路径/topid/层级
        this.calculatePath(dfdwTGcssbChapter);
        //修改
        dfdwTGcssbChapterMapper.updateById(dfdwTGcssbChapter);
        //查询是否有子集,有子集都要修改
        updateSon(dfdwTGcssbChapter,user.getId());
    }

    private void updateSon(DfdwTGcssbChapter dfdwTGcssbChapter,Integer userId) {
        //查询所有类别信息
        List<DfdwTGcssbChapter> mainList = dfdwTGcssbChapterMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbChapter>().eq(DfdwTGcssbChapter::getDeleted,0));
        for (DfdwTGcssbChapter  main : mainList) {
            if (Objects.equals(dfdwTGcssbChapter.getId(), main.getParentId())) {
                this.calculatePath(main); //修改路径
                main.setLastUpdateDate(new Date());//修改时间
                main.setLastUpdateBy(userId);//修改用户
                //修改
                dfdwTGcssbChapterMapper.updateById(main);
                updateSon(main,userId);
                return;
            }
        }
    }


    /**
     * 根据书籍id获取数据page
     * @return
     */
    @Override
    public IPage<DfdwTGcssbChapter> getListByBooksId(DfdwTGcssbChapter dfdwTGcssbChapter) {
        IPage<DfdwTGcssbChapter> list = new Page<>();
        list.setCurrent(dfdwTGcssbChapter.getPageNum());
        list.setSize(dfdwTGcssbChapter.getPageSize());
        //查询分页
        list = this.page(list, new LambdaQueryWrapper<DfdwTGcssbChapter>()
                .eq(DfdwTGcssbChapter::getDeleted,0)
                .eq(DfdwTGcssbChapter::getParentId,dfdwTGcssbChapter.getParentId())
                .orderByAsc(DfdwTGcssbChapter::getSort));
        return list;
    }


    /**
     * 根据书籍id获取数据
     * @return
     */
    @Override
    public List<DfdwTGcssbChapter> getAllList(DfdwTGcssbChapter dfdwTGcssbChapter) {
        // 查询章节信息
        List<DfdwTGcssbChapter> chapterList = dfdwTGcssbChapterMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbChapter>()
                .eq(DfdwTGcssbChapter::getDeleted, 0)
                .eq(DfdwTGcssbChapter::getBooksId, dfdwTGcssbChapter.getBooksId())
                .like(dfdwTGcssbChapter.getName()!= null,DfdwTGcssbChapter::getName, dfdwTGcssbChapter.getName())
                .orderByAsc(DfdwTGcssbChapter::getSort));
        return chapterList;
    }

    /**
     * 下载导入excel模板
     * @param response
     */
    @Override
    public Result<Object> exportExcelTemplate(HttpServletResponse response) throws IOException{
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("章节导入模板");
        //sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        // 创建字体样式
        Font headerFont = workbook.createFont();
        headerFont.setBold(true); // 设置字体加粗
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        // 创建单元格样式
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置居中对齐
        //放入模版数据
        XSSFRow row = sheet.createRow(0);
        row = sheet.createRow(0);
        row.createCell(0).setCellValue("章节编号");
        row.createCell(1).setCellValue("章节名称");
        row.createCell(2).setCellValue("章节所在页码");
        row.createCell(3).setCellValue("备注");
        row.createCell(4).setCellValue("排序");

        // 应用样式到第一行每个单元格
        for (int i = 0; i <= 4; i++) {
            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            cell.setCellStyle(headerCellStyle);
        }
        //设置列宽
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 4000);
        sheet.setColumnWidth(3, 6000);
        sheet.setColumnWidth(4, 3000);
        //=================生成word到设置浏览默认下载地址=================
        response.setHeader("Content-disposition", "章节导入模板.xlsx");
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        response.setContentType("multipart/form-data;charset=UTF-8");
        workbook.write(response.getOutputStream());
        return Result.ok();
    }


    /**
     * 导入
     * @param multipartFile
     * @param booksId
     * @return
     */
    @Override
    public Result<Object> importExcel(MultipartFile multipartFile,Integer booksId) {
        String fileName = multipartFile.getOriginalFilename();
//        System.out.println(fileName);
        if (!".xlsx".equals(fileName.substring(fileName.lastIndexOf("."))) &&
                !".xls".equals(fileName.substring(fileName.lastIndexOf(".")))
        ){
            return Result.error("不是excel文件!");
        }
        try {
            //章节导入列表
            List<DfdwTGcssbChapter> chapterList = new ArrayList<>();

            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            XSSFRow row = null;
            int one = 0;

            //循环sesheet页中数据从第二行开始，第一行是标题
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                //获取每一行数据
                row = sheet.getRow(i);

                DfdwTGcssbChapter entity = new DfdwTGcssbChapter();

                if (row.getCell(0) == null || row.getCell(1) == null || row.getCell(2) == null || row.getCell(4) == null) {
                    one = i + 1;
                    return Result.error("有数据为空，请检查第" + one + "行数据！");
                }

                //全部设置成string 在读取
                row.getCell(0).setCellType(CellType.STRING); //章节编号
                row.getCell(1).setCellType(CellType.STRING); //章节名称
                row.getCell(2).setCellType(CellType.STRING); //章节所在页码
                if ( row.getCell(3) != null ){
                    row.getCell(3).setCellType(CellType.STRING); //备注
                    String remark = row.getCell(3).getStringCellValue().trim(); //备注
                    entity.setRemark(remark); //备注
                }
                row.getCell(4).setCellType(CellType.STRING); //排序
                String chapterNumber = row.getCell(0).getStringCellValue().trim(); //章节编号
                String name = row.getCell(1).getStringCellValue().trim(); //章节名称
                String pageNum = row.getCell(2).getStringCellValue().trim(); //章节所在页码

                String sort = row.getCell(4).getStringCellValue().trim(); //排序
                // 获取当前的用户
                PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
                entity.setCreateBy(user.getId()); //新增用户id
                entity.setCreationDate(new Date());//新增时间
                entity.setCreateByDeptId(user.getGroupId()); //创建人部门
                entity.setName(name); //名称
                entity.setChapterNumber(chapterNumber); //章节编号
                entity.setBooksId(booksId);//书籍id
                if (StringUtils.isNotBlank(pageNum)) {
                    entity.setPageNum(Integer.parseInt(pageNum));//章节所在页码
                }
                if (StringUtils.isNotBlank(sort)) {
                    entity.setSort(Integer.parseInt(sort));//排序
                }
                chapterList.add(entity);
            }

            //筛选出没有.的(纯数字为第一级)
            List<DfdwTGcssbChapter> topChapters = chapterList.stream().filter(data -> !data.getChapterNumber().equals(".") && !data.getChapterNumber().contains(".")).collect(Collectors.toList());

            for (DfdwTGcssbChapter topChapter : topChapters) {
                topChapter.setParentId(0); // 第一级父id为0
                topChapter.setNameFullPath(topChapter.getName()); // 设置全路径
                // 插入顶层章节
                this.dfdwTGcssbChapterMapper.insert(topChapter);
                // 递归处理子章节
                processChapters(chapterList, topChapter,topChapter.getId());
            }

        }catch (Exception ex){
            log.error("导入失败:" + ex.getMessage());
            return Result.error("导入失败！");
        }
        return Result.ok("导入成功!");
    }


    private void processChapters(List<DfdwTGcssbChapter> chapterList, DfdwTGcssbChapter parentChapter,int topId) {
        // 查找当前父章节的子章节
        List<DfdwTGcssbChapter> childChapters = chapterList.stream()
                .filter(data -> isChildChapter(data.getChapterNumber(), parentChapter.getChapterNumber()))
                .collect(Collectors.toList());

        // 遍历子章节
        for (DfdwTGcssbChapter childChapter : childChapters) {
            childChapter.setParentId(parentChapter.getId()); // 设置父id
            childChapter.setNameFullPath(parentChapter.getNameFullPath() + "/" + childChapter.getName()); // 设置全路径
            childChapter.setTopId(topId); // 设置topID
            // 插入子章节
            this.dfdwTGcssbChapterMapper.insert(childChapter);
            // 递归处理子章节的子章节
            processChapters(chapterList, childChapter,topId);
        }
    }

    // 判断是否为指定父章节的子章节
    private boolean isChildChapter(String chapterNumber, String parentChapterNumber) {
        //是否是父number开头
        if (!chapterNumber.startsWith(parentChapterNumber)) {
            return false;
        }
        // 确保子章节的点的个数比父章节多一个
        int parentDotCount = countOccurrences(parentChapterNumber, '.');
        int childDotCount = countOccurrences(chapterNumber, '.');
        return childDotCount == parentDotCount + 1;
    }

    // 计算字符串中指定字符的出现次数
    private int countOccurrences(String text, char character) {
        int count = 0;
        for (char c : text.toCharArray()) {
            if (c == character) {
                count++;
            }
        }
        return count;
    }


}




