package com.soft.gcc.xtbg.gcssb.mapper;

import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_CHAPTER(工程随身宝-章节)】的数据库操作Mapper
* @createDate 2024-06-19 15:19:36
* @Entity com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter
*/
public interface DfdwTGcssbChapterMapper extends BaseMapper<DfdwTGcssbChapter> {

    void batchInsert(List<DfdwTGcssbChapter> list);

}




