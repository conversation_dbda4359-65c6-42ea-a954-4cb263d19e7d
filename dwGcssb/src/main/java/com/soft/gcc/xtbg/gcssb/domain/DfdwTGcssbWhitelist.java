package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.soft.gcc.xtbg.gcssb.entity.GcssbBaseEntity;
import lombok.Data;

/**
 * 工程随身宝-白名单
 * @TableName DFDW_T_GCSSB_WHITELIST
 */
@TableName(value ="DFDW_T_GCSSB_WHITELIST")
@Data
public class DfdwTGcssbWhitelist extends GcssbBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户Id
     */
    @TableField(value = "userId")
    private Integer userId;

    /**
     * 用户名称
     */
    @TableField(value = "userName")
    private String userName;

    /**
     * 白名单标识（0否 1是）
     */
    @TableField(value = "whitelistTag")
    private Integer whitelistTag;

    /**
     * 白名单权限(多个之间逗号分割)
     */
    @TableField(value = "whitelistPermission")
    private String whitelistPermission;

    /**
     * 编辑标识（0否 1是）
     */
    @TableField(value = "editTag")
    private Integer editTag;

    /**
     * 编辑权限(多个之间逗号分割)
     */
    @TableField(value = "editPermission")
    private String editPermission;

    /**
     * 创建人
     */
    @TableField(value = "createBy")
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(value = "creationDate")
    private Date creationDate;

    /**
     * 更新人
     */
    @TableField(value = "lastUpdateBy")
    private Integer lastUpdateBy;

    /**
     * 更新时间
     */
    @TableField(value = "lastUpdateDate")
    private Date lastUpdateDate;

    /**
     * 删除标识
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "1")
    private String deleted;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    //白名单标识文本
    @TableField(exist = false)
    private String whitelistTagText;

    //白名单编辑标识文本
    @TableField(exist = false)
    private String editTagText;

    //白名单标识权限文本
    @TableField(exist = false)
    private String whitelistPermissionText;

    //白名单编辑标识权限文本
    @TableField(exist = false)
    private String editPermissionText;
}