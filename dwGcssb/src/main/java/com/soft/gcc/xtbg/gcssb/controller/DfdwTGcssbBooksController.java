package com.soft.gcc.xtbg.gcssb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.domain.VDfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbBooksService;
import com.soft.gcc.xtbg.gcssb.service.VDfdwTGcssbBooksService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


@RequestMapping("/gcssb/books")
@RestController
@Validated
public class DfdwTGcssbBooksController extends BaseController {

    @Resource
    private DfdwTGcssbBooksService dfdwTGcssbBooksService;
    @Resource
    private VDfdwTGcssbBooksService vDfdwTGcssbBooksService;


    /**
     * 新增
     * @return
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX02')")
    public Result<Object> createBooks( @RequestBody DfdwTGcssbBooks dfdwTGcssbBooks) {
        int id =  dfdwTGcssbBooksService.createBooks(dfdwTGcssbBooks);
        return Result.ok(id) ;
    }


    /**
     * 修改
     * @return
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX03')")
    public Result<Object> updateBooks(@RequestBody DfdwTGcssbBooks dfdwTGcssbBooks) {
        dfdwTGcssbBooksService.updateBooks(dfdwTGcssbBooks);
        return Result.ok();
    }

    /**
     * 删除
     * @return
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX04')")
    public Result<Object> deleteBooks(@RequestParam("id") Long id) {
        dfdwTGcssbBooksService.deleteBooks(id);
        return Result.ok();
    }


    /**
     * 根据类别id获取数据
     * @return
     */
    @PostMapping("/getListByCategoryId")
    @PreAuthorize("@ss.hasPermi('JDWGB01SJ01QX01')")
    public Result<Object> getListByCategoryId(@RequestBody DfdwTGcssbBooks dfdwTGcssbBook) {
        IPage<DfdwTGcssbBooks>  mainList =  dfdwTGcssbBooksService.getListByCategoryId(dfdwTGcssbBook);
        return Result.ok(mainList) ;
    }


    /**
     * 获取书籍数据
     * @return
     */
    @PostMapping("/getBooksPageList")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX01')")
    public Result<Object> getBooksPageList(@RequestBody Map<String, String> param) {
        IPage<DfdwTGcssbBooks> booksIPage =  dfdwTGcssbBooksService.getBooksPageList(param);
        return Result.ok(booksIPage);
    }

    /**
     * 根据id获取书籍视图
     * @return
     */
    @PostMapping("/getVBooksById")
//    @PreAuthorize("@ss.hasPermi('JDWGB01LB01QX01')")
    public Result<Object> getVBooksById(@RequestBody VDfdwTGcssbBooks vDfdwTGcssbBooks) {
        return Result.ok(vDfdwTGcssbBooksService.getById(vDfdwTGcssbBooks));
    }

    /**
     * 导入书籍excel模板
     * @return
     */
    @PostMapping("importExcelTemplateBooks")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> importExcelTemplateBooks( HttpServletResponse response){
        try {
            dfdwTGcssbBooksService.dfdwTGcssbBooksService(response);
        }  catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.ok();
    }

    /**
     * 书籍导入
     */
    @PostMapping("importExcelBooks/{categoryId}")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> importExcelBooks(MultipartFile file, @PathVariable Integer categoryId){
        return dfdwTGcssbBooksService.importExcelBooks(file ,categoryId);
    }
}
