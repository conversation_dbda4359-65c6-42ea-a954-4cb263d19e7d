package com.soft.gcc.xtbg.gcssb.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.gcssb.entity.Person;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_WHITELIST(工程随身宝-白名单)】的数据库操作Mapper
* @createDate 2024-06-14 11:03:04
* @Entity com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbWhitelist
*/
public interface DfdwTGcssbWhitelistMapper extends BaseMapper<DfdwTGcssbWhitelist> {

    IPage<Person> selecWhitetPage(Page<Person> page, @Param("loginName") String loginName,@Param("realName") String realName);
}




