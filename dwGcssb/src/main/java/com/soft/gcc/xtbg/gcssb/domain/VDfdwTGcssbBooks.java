package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 
 * @TableName V_DFDW_T_GCSSB_BOOKS
 */
@TableName(value ="V_DFDW_T_GCSSB_BOOKS")
@Data
public class VDfdwTGcssbBooks implements Serializable {
    /**
     * 
     */
    @TableField(value = "Id")
    private Integer id;

    /**
     * 
     */
    @TableField(value = "name")
    private String name;

    /**
     * 
     */
    @TableField(value = "categoryId")
    private Integer categoryId;

    /**
     * 
     */
    @TableField(value = "isEnable")
    private Integer isEnable;

    /**
     * 
     */
    @TableField(value = "isVisible")
    private Integer isVisible;

    /**
     * 
     */
    @TableField(value = "advancePageNum")
    private Integer advancePageNum;

    /**
     * 
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 
     */
    @TableField(value = "fileId")
    private Integer fileId;

    /**
     * 
     */
    @TableField(value = "originalFilename")
    private String originalFilename;

    /**
     * 
     */
    @TableField(value = "FilePath")
    private String filePath;

    /**
     * 
     */
    @TableField(value = "hz")
    private String hz;

    /**
     * 
     */
    @TableField(value = "totalPages")
    private Integer totalPages;

    /**
     * 
     */
    @TableField(value = "splitCatalog")
    private String splitCatalog;

    /**
     * 
     */
    @TableField(value = "fileSize")
    private Long fileSize;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}