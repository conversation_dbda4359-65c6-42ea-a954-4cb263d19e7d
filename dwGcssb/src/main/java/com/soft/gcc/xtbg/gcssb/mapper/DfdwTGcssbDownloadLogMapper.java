package com.soft.gcc.xtbg.gcssb.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_DOWNLOAD_LOG(工程随身宝-下载日志)】的数据库操作Mapper
* @createDate 2024-06-14 11:01:46
* @Entity com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog
*/
public interface DfdwTGcssbDownloadLogMapper extends BaseMapper<DfdwTGcssbDownloadLog> {

    IPage<DfdwTGcssbDownloadLog> selecDownloadLogPage(Page<DfdwTGcssbDownloadLog> pageParam,@Param("downloadLog")  DfdwTGcssbDownloadLog dfdwTGcssbDownloadLog);

}




