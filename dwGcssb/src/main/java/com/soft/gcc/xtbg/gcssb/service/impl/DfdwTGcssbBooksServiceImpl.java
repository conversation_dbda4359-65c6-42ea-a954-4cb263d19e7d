package com.soft.gcc.xtbg.gcssb.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.soft.gcc.xtbg.gcssb.mapper.DfdwTGcssbBooksMapper;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbBooksService;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbMainService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_BOOKS(工程随身宝-书籍)】的数据库操作Service实现
* @createDate 2024-06-19 15:19:36
*/
@Service
public class DfdwTGcssbBooksServiceImpl extends ServiceImpl<DfdwTGcssbBooksMapper, DfdwTGcssbBooks>
    implements DfdwTGcssbBooksService{

    @Resource
    DfdwTGcssbBooksMapper dfdwTGcssbBooksMapper;
    @Resource
    DfdwTGcssbMainService dfdwTGcssbMainService;

    /**
     * 新增
     * @param dfdwTGcssbBooks
     * @return
     */
    @Override
    public int createBooks(DfdwTGcssbBooks dfdwTGcssbBooks) {
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //新增用户id
        dfdwTGcssbBooks.setCreateBy(user.getId());
        //新增时间
        dfdwTGcssbBooks.setCreationDate(new Date());
        //更新用户id
        dfdwTGcssbBooks.setLastUpdateBy(user.getId());
        //更新时间
        dfdwTGcssbBooks.setLastUpdateDate(new Date());
        //逻辑删除0-可见
        dfdwTGcssbBooks.setDeleted("0");
        //新增
        int id = dfdwTGcssbBooksMapper.insert(dfdwTGcssbBooks);
        return id;
    }

    /**
     * 修改
     * @param dfdwTGcssbBooks
     */
    @Override
    public void updateBooks(DfdwTGcssbBooks dfdwTGcssbBooks) {
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        dfdwTGcssbBooks.setLastUpdateBy(user.getId());
        //修改时间
        dfdwTGcssbBooks.setLastUpdateDate(new Date());
        //修改
        dfdwTGcssbBooksMapper.updateById(dfdwTGcssbBooks);
    }

    /**
     * 删除
     * @param id
     */
    @Override
    public void deleteBooks(Long id) {
        dfdwTGcssbBooksMapper.deleteById(id);
    }

    /**
     * 根据类别id获取数据
     * @return
     */
    @Override
    public IPage<DfdwTGcssbBooks> getListByCategoryId(DfdwTGcssbBooks dfdwTGcssbBook) {
        IPage<DfdwTGcssbBooks> list = new Page<>();
        list.setCurrent(dfdwTGcssbBook.getPageNum());
        list.setSize(dfdwTGcssbBook.getPageSize());
        // 查询该子集书籍的具体书籍信息
        List<DfdwTGcssbBooks> booksList = dfdwTGcssbBooksMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbBooks>()
                .eq(dfdwTGcssbBook.getCategoryId() != null , DfdwTGcssbBooks::getCategoryId,dfdwTGcssbBook.getCategoryId())
                .like(dfdwTGcssbBook.getName() != null, DfdwTGcssbBooks::getName,dfdwTGcssbBook.getName())
                .eq(DfdwTGcssbBooks::getDeleted,0)
                .orderByAsc(DfdwTGcssbBooks::getId));
        //添加的子集
        List<DfdwTGcssbBooks> addList = new ArrayList<>();
        //获取类别信息
        List<DfdwTGcssbMain> mainList = dfdwTGcssbMainService.getAllMain();
        //递归获取书籍的所有子集书籍
        getSonBooks(dfdwTGcssbBook.getCategoryId(), mainList, addList,dfdwTGcssbBook.getName());
        //将addList中的书籍信息添加到records中
        booksList.addAll(addList);
        list.setRecords(booksList);
        // 更新实际的总记录数
        list.setTotal(booksList.size());
        // 根据当前页和每页大小，重新计算当前页的记录列表
        Integer fromIndex = Math.min((dfdwTGcssbBook.getPageNum() - 1) * dfdwTGcssbBook.getPageSize(), booksList.size());
        Integer toIndex = Math.min(fromIndex + dfdwTGcssbBook.getPageSize(), booksList.size());
        // 使用 subList 截取长整型索引范围内的子列表
        list.setRecords(booksList.subList( fromIndex, toIndex));
        //书籍枚举字典转化
        booksDictionaries(list);
        return list;
    }

    private void booksDictionaries(IPage<DfdwTGcssbBooks> list) {
        for (DfdwTGcssbBooks books : list.getRecords() ) {
            //根据CategoryId获取类别信息
            DfdwTGcssbMain mainCategoryId = dfdwTGcssbMainService.getMainCategoryId(books.getCategoryId());
            //类别名称
            books.setCategoryText(mainCategoryId.getName());
            //是否启用
            if (books.getIsEnable() != null) {
                if(books.getIsEnable()==1){
                    books.setIsEnableText("启用");
                }else {
                    books.setIsEnableText("不启用");
                }
            }
            //是否可见
            if (books.getIsVisible() != null){
                if(books.getIsVisible()==1){
                    books.setIsVisibleText("可见");
                }else {
                    books.setIsVisibleText("不可见");
                }
            }
        }
    }

    private void getSonBooks(Integer booksId, List<DfdwTGcssbMain> mainList, List<DfdwTGcssbBooks> addList ,String name) {
        // 获取当前书籍的子集书籍
        List<DfdwTGcssbMain> sonMainList = mainList.stream().filter(o -> Objects.equals(o.getParentId(), booksId)).collect(Collectors.toList());
        for (DfdwTGcssbMain sonMain : sonMainList) {
            // 查询该子集书籍的具体书籍信息
            List<DfdwTGcssbBooks> sonBooksList = dfdwTGcssbBooksMapper.selectList(new LambdaQueryWrapper<DfdwTGcssbBooks>()
                    .eq(DfdwTGcssbBooks::getDeleted, 0)
                    .like(name != null,DfdwTGcssbBooks::getName, name)
                    .eq(DfdwTGcssbBooks::getCategoryId, sonMain.getId()));
            if (CollectionUtils.isNotEmpty(sonBooksList)) {
                // 将子集书籍加入到addList中
                addList.addAll(sonBooksList);
            }
            // 递归调用获取子集书籍的子集书籍
            getSonBooks(sonMain.getId(), mainList, addList,name);
        }
    }


    /**
     * 获取书籍数据
     * @return
     */
    @Override
    public IPage<DfdwTGcssbBooks> getBooksPageList(Map<String, String> param) {
        int pageNum = ParseUtil.tryParseInt(param.get("pageNum"));
        int pageSize = ParseUtil.tryParseInt(param.get("pageSize"),1);
        //名称
        String name = ParseUtil.tryParseString(param.get("name"));
        Page<DfdwTGcssbBooks> pageParam = new Page<>(pageNum, pageSize);
        //分页查询
        IPage<DfdwTGcssbBooks> booksIPage = this.baseMapper.selectBooksPage(pageParam,name);
        //数据字典转化
        this.booksDictionaries(booksIPage);
        return booksIPage;
    }

    /**
     * 导入书籍excel模板
     * @return
     */
    @Override
    public Result<Object> dfdwTGcssbBooksService(HttpServletResponse response) throws IOException{
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("书籍导入模板");
        //sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        // 创建字体样式
        Font headerFont = workbook.createFont();
        headerFont.setBold(true); // 设置字体加粗
        headerFont.setFontHeightInPoints((short) 12); // 设置字体大小
        // 创建单元格样式
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置居中对齐+
        //放入模版数据
        XSSFRow row ;
        row = sheet.createRow(0);
        row.createCell(0).setCellValue("书籍名称");
        row.createCell(1).setCellValue("前置页码");
        row.createCell(2).setCellValue("是否启用");
        row.createCell(3).setCellValue("是否可见");
        row.createCell(4).setCellValue("备注");

        // 应用样式到第一行每个单元格
        for (int i = 0; i <= 4; i++) {
            Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            cell.setCellStyle(headerCellStyle);
        }
        //设置列宽
        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 4000);
        sheet.setColumnWidth(2, 3000);
        sheet.setColumnWidth(3, 3000);
        sheet.setColumnWidth(4, 5000);
        //=================生成word到设置浏览默认下载地址=================
        response.setHeader("Content-disposition", "书籍导入模板.xlsx");
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        response.setContentType("multipart/form-data;charset=UTF-8");
        workbook.write(response.getOutputStream());
        return Result.ok();
    }

    /**
     * 书籍导入
     */
    @Override
    public Result<Object> importExcelBooks(MultipartFile multipartFile, Integer categoryId) {
        String fileName = multipartFile.getOriginalFilename();
        if (!".xlsx".equals(fileName.substring(fileName.lastIndexOf("."))) &&
                !".xls".equals(fileName.substring(fileName.lastIndexOf(".")))
        ){
            return Result.error("不是excel文件!");
        }
        try {
            //书籍导入列表
            List<DfdwTGcssbBooks> booksList = new ArrayList<>();

            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(multipartFile.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            XSSFRow row = null;
            int one = 0;

            //循环sesheet页中数据从第二行开始，第一行是标题
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                //获取每一行数据
                row = sheet.getRow(i);

                DfdwTGcssbBooks entity = new DfdwTGcssbBooks();

                if (row.getCell(0) == null || row.getCell(1) == null || row.getCell(2) == null || row.getCell(3) == null) {
                    one = i + 1;
                    return Result.error("有数据为空，请检查第" + one + "行数据！");
                }

                //全部设置成string 在读取
                row.getCell(0).setCellType(CellType.STRING); //书籍名称
                row.getCell(1).setCellType(CellType.STRING); //前置页码
                row.getCell(2).setCellType(CellType.STRING); //是否启用
                row.getCell(3).setCellType(CellType.STRING); //是否可见
                if ( row.getCell(4) != null ){
                    row.getCell(4).setCellType(CellType.STRING); //备注
                    String remark = row.getCell(4).getStringCellValue().trim(); //备注
                    entity.setRemark(remark); //备注
                }
                String name = row.getCell(0).getStringCellValue().trim(); //书籍名称
                String advancePageNum = row.getCell(1).getStringCellValue().trim(); //前置页码
                String isEnable = row.getCell(2).getStringCellValue().trim(); //是否启用
                String isVisible = row.getCell(3).getStringCellValue().trim(); //是否可见
                // 获取当前的用户
                PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
                entity.setCreateBy(user.getId()); //新增用户id
                entity.setCreationDate(new Date());//新增时间
                entity.setCreateByDeptId(user.getGroupId()); //创建人部门
                entity.setName(name); //名称
                entity.setAdvancePageNum(Integer.parseInt(advancePageNum)); //前置页码
                entity.setCategoryId(categoryId); //类别id
                //是否启用
                if ("启用".equals(isEnable)) {
                    entity.setIsEnable(1);
                } else {
                    entity.setIsEnable(0);
                }
                //是否可见
                if ("可见".equals(isVisible)) {
                    entity.setIsVisible(1);
                } else {
                    entity.setIsVisible(0);
                }
                booksList.add(entity);
            }
            //批量新增
            this.dfdwTGcssbBooksMapper.batchInsert(booksList);

        }catch (Exception ex){
            log.error("导入失败:" + ex.getMessage());
            return Result.error("导入失败！");
        }
        return Result.ok("导入成功!");
    }
}




