package com.soft.gcc.xtbg.gcssb.service;

import com.soft.gcc.xtbg.base.controller.Result;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 工程随身宝文件操作接口
 * @date 2024-06-17 13:51:50
 */
public interface IGcssbFileService {


    /**
     * 文件切片上传
     * @param chunk 文件
     * @param fileNameStr 文件名称 （电缆工程分册.pdf）
     * @param chunkIndex 分片当前index
     * @param totalChunks 总分片数
     * @param ywId 业务Id
     * @return
     */
    Result<Object> uploadChunk(MultipartFile chunk, String fileNameStr, int chunkIndex, int totalChunks, int ywId,PersonEntity person);


    /**
     * 文件分割保存
     *
     * @param newName  新的文件名称不带后缀 （d7edebe6-bf0f-48d9-ad72-f1306657ee4f）
     * @param ywId        业务Id
     * @param hz  文件后缀
     * @param fileNameChunk  本地临时保存的文件名称（1.pdf）
     * @return
     */
     Result<Object> uploadSplit(String hz, int ywId,String newName,String fileNameChunk);


    Result<Object> upload(MultipartFile file, Integer ywId,String hjID, PersonEntity person);


    void downloadChunk(Integer ywId, String filePath, String fileName, Integer chunkSize, Integer chunkTotal, Integer index, HttpServletResponse response, PersonEntity person);


    /**
     * 上传img图片
     * @param file
     * @param person
     * @param ywId
     * @return
     */
    Result<Object> uploadImg(MultipartFile file, PersonEntity person, Integer ywId);
}
