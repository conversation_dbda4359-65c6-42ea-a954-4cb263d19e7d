package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.soft.gcc.xtbg.gcssb.entity.GcssbBaseEntity;
import lombok.Data;

/**
 * 工程随身宝-章节
 * @TableName DFDW_T_GCSSB_CHAPTER
 */
@TableName(value ="DFDW_T_GCSSB_CHAPTER")
@Data
public class DfdwTGcssbChapter extends GcssbBaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 全路径
     */
    @TableField(value = "nameFullPath")
    private String nameFullPath;

    /**
     * 父Id
     */
    @TableField(value = "parentId")
    private Integer parentId;

    /**
     * topId
     */
    @TableField(value = "topId")
    private Integer topId;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 页码
     */
    @TableField(value = "pageNum")
    private Integer pageNum;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField(value = "createBy")
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(value = "creationDate")
    private Date creationDate;

    /**
     * 更新人
     */
    @TableField(value = "lastUpdateBy")
    private Integer lastUpdateBy;

    /**
     * 更新时间
     */
    @TableField(value = "lastUpdateDate")
    private Date lastUpdateDate;

    /**
     * 创建人所在部门
     */
    @TableField(value = "createByDeptId")
    private Integer createByDeptId;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "1")
    private String deleted;

    /**
     * 章节编号
     */
    @TableField(value = "chapterNumber")
    private String chapterNumber;

    /**
     * 书籍id
     */
    @TableField(value = "booksId")
    private Integer booksId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}