package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 工程随身宝类别名称树
 * @TableName DFDW_T_GCSSB_MAIN
 */
@TableName(value ="DFDW_T_GCSSB_MAIN")
@Data
public class DfdwTGcssbMainTree implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField(value = "label")
    private String label;

    /**
     * 是否启用
     */
    @TableField(value = "isEnable")
    private Integer isEnable;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 下级
     */
    @TableField(value = "children")
    private List<DfdwTGcssbMainTree> children;


}