package com.soft.gcc.xtbg.gcssb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbChapter;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_GCSSB_CHAPTER(工程随身宝-章节)】的数据库操作Service
* @createDate 2024-06-19 15:19:36
*/
public interface DfdwTGcssbChapterService extends IService<DfdwTGcssbChapter> {

    /**
     * 新增
     * @return
     */
    int createChapter(DfdwTGcssbChapter dfdwTGcssbChapter);

    /**
     * 修改
     * @return
     */
    void updateChapter(DfdwTGcssbChapter dfdwTGcssbChapter);

    /**
     * 根据书籍id获取数据Page
     * @return
     */
    IPage<DfdwTGcssbChapter> getListByBooksId(DfdwTGcssbChapter dfdwTGcssbChapter);

    /**
     * 根据书籍id获取章节数据
     * @return
     */
    List<DfdwTGcssbChapter> getAllList(DfdwTGcssbChapter dfdwTGcssbChapter);

    /**
     * 导入excel
     * @param file
     * @return
     */
    Result<Object> importExcel(MultipartFile file ,Integer booksId);

    /**
     * 导入excel模板
     * @return
     */
    Result<Object> exportExcelTemplate(HttpServletResponse response) throws Exception;

    /**
     * 删除
     * @param id
     */
    void deleteChapter(Long id);
}
