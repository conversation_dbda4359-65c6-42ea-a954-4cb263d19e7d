package com.soft.gcc.xtbg.gcssb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTDictData;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTDictType;

import java.util.List;

/**
* <AUTHOR>
*/
public interface DfdwTDictDataService extends IService<DfdwTDictData> {

    /**
     * 新增字典数据
     * @param dfdwTDictData
     * @return
     */
    Result<Object> createDictData(DfdwTDictData dfdwTDictData);

    /**
     * 删除字典数据
     * @return
     */
    Result<Object> deleteDictData(Long id);

    /**
     * 修改字典数据
     * @return
     */
    Result<Object> updateDictData(DfdwTDictData dfdwTDictData);

    /**
     * 字典数据分页
     * @param dfdwTDictData
     * @return
     */
    IPage<DfdwTDictData> dicDataByType(DfdwTDictData dfdwTDictData);

    /**
     * 根据类型查询字典数据
     */
    List<DfdwTDictData> getDataByType(DfdwTDictType dfdwTDictType);

    /**
     * 根据类型查询字典数据且状态0-正常
     */
    List<DfdwTDictData> getDictDataByType(String dictType);
}
