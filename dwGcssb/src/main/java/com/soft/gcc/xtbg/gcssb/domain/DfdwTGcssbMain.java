package com.soft.gcc.xtbg.gcssb.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.soft.gcc.xtbg.gcssb.entity.GcssbBaseEntity;
import lombok.Data;

/**
 * 工程随身宝-主表
 * @TableName DFDW_T_GCSSB_MAIN
 */
@TableName(value ="DFDW_T_GCSSB_MAIN")
@Data
public class DfdwTGcssbMain extends GcssbBaseEntity implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 全路径
     */
    @TableField(value = "nameFullPath")
    private String nameFullPath;

    /**
     * 父Id
     */
    @TableField(value = "parentId")
    private Integer parentId;

    /**
     * topId
     */
    @TableField(value = "topId")
    private Integer topId;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 是否启用(0不启用，1启用)
     */
    @TableField(value = "isEnable")
    private Integer isEnable;

    /**
     * 是否可见(0不可见，1可见)
     */
    @TableField(value = "isVisible")
    private Integer isVisible;

    /**
     * 前置页码（书籍专用）
     */
    @TableField(value = "advancePageNum")
    private Integer advancePageNum;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 类型(1类别、2书籍、3章节)
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 级别
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 创建人
     */
    @TableField(value = "createBy")
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(value = "creationDate")
    private Date creationDate;

    /**
     * 更新人
     */
    @TableField(value = "lastUpdateBy")
    private Integer lastUpdateBy;

    /**
     * 更新时间
     */
    @TableField(value = "lastUpdateDate")
    private Date lastUpdateDate;

    /**
     * 创建人所在部门
     */
    @TableField(value = "createByDeptId")
    private Integer createByDeptId;

    /**
     * 删除标识
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "1")
    private String deleted;

    @TableField(value = "fileId")
    private Integer fileId;
    @TableField(value = "filePath")
    private String filePath;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 是否启用文本
     */
    @TableField(exist = false)
    private String isEnableText;

    /**
     * 是否可见文本
     */
    @TableField(exist = false)
    private String isVisibleText;

    /**
     * 类型文本
     */
    @TableField(exist = false)
    private String typeText;

}
