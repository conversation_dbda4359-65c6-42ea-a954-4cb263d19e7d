package com.soft.gcc.xtbg.gcssb.dto;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description
 * @date 2024-06-21 15:59:20
 */
@Data
public class SplitInfo {

    //开始页
    Integer splitStartIndex;
    //结束页
    Integer splitEndIndex;
    //后缀
    String hz;
    //业务Id
    Integer ywId;
    //保存oss的文件名（不带后缀）
    String newName;
    //上传包保存本地的临时名称
    String fileNameChunk;
    //文件Id
    Integer fileId;
    //总页数
    private Integer pageCount;
}
