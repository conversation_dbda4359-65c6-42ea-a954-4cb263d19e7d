package com.soft.gcc.xtbg.gcssb.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aspose.words.Document;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbBooks;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbDownloadLog;
import com.soft.gcc.xtbg.gcssb.domain.DfdwTGcssbMain;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbBooksService;
import com.soft.gcc.xtbg.gcssb.service.DfdwTGcssbDownloadLogService;
import com.soft.gcc.xtbg.gcssb.service.IGcssbFileService;
import com.soft.gcc.xtbg.gcssb.util.AliyunOSSUtils;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

/**
 * <AUTHOR>
 * @description 工程随身宝文件操作接口实现类
 * @date 2024-06-17 13:52:02
 */
@Service
public class GcssbFileServiceImpl implements IGcssbFileService {
    @Autowired
    private TFileService tFileService;
    @Autowired
    private DfdwTGcssbBooksService gcssbBooksService;
    @Autowired
    private DfdwTGcssbDownloadLogService gcssbDownloadLogService;


    /**
     * 文件切片上传
     *
     * @param chunk       文件
     * @param fileNameStr 文件名称 （电缆工程分册.pdf）
     * @param chunkIndex  分片当前index
     * @param totalChunks 总分片数
     * @param ywId        业务Id
     * @return
     */
    @Override
    public Result<Object> uploadChunk(MultipartFile chunk, String fileNameStr, int chunkIndex, int totalChunks, int ywId, PersonEntity person) {
        try {

            String[] splits = fileNameStr.split("\\.");
            String hz = splits[splits.length - 1].toLowerCase();
            //临时路径
            String filePath = System.getProperty("java.io.tmpdir") + "/yykj/dfdwGcssb/temp/";
            // String filePath = "D:\\Save" + "/yykj/dfdwGcssb/temp/";

            //拆分的文件名称 ywId标识
            String fileNameChunk = ywId + "." + hz;
            String tempPath = filePath + fileNameChunk;
            File file = new File(tempPath);
            if (!file.exists()) {
                file.getParentFile().mkdirs();
                file.createNewFile();
            } else {
                if (chunkIndex == 0) {
                    file.delete();
                    file.createNewFile();
                }
            }
            //以追加的形式写入文件
            if (chunkIndex != totalChunks && chunk != null) {
                String filePathPart = filePath + fileNameChunk + "_part_" + chunkIndex;
                File chunkFile = new File(filePathPart);
                chunk.transferTo(chunkFile);
                // Files.write(Paths.get(tempPath), chunk.getBytes(), StandardOpenOption.APPEND);
            }
            //最后一次，合并处理
            if (chunkIndex == totalChunks - 1) {
                File outputFile = new File(filePath + fileNameChunk);
                OutputStream outputStream = new FileOutputStream(outputFile);

                for (int i = 0; i < totalChunks; i++) {
                    String chunkFilePath = filePath + fileNameChunk + "_part_" + i;
                    FileInputStream inputStream = new FileInputStream(chunkFilePath);
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    inputStream.close();
                }

                outputStream.close();

                for (int i = 0; i < totalChunks; i++) {
                    File chunkFile = new File(filePath + fileNameChunk + "_part_" + i);
                    chunkFile.delete();
                }
            }
            Map<String, Object> map = new HashMap<>();
            //如果是最后一次
            if (chunkIndex == totalChunks) {
                //按照oss规则改名
                String path = "Upload/FileManage/dfdwGcssb/Books/";
                String newName = UUID.randomUUID() + "";
                String fileName = newName + "." + hz;

                int pageCount = 0;
//                File file2= new File(tempPath);
//                try (PDDocument document = PDDocument.load( new File(tempPath))) {
//                    pageCount = document.getNumberOfPages();
//
//                }catch (Exception e){
//                    e.printStackTrace();
//                }


                //保存至oss
                String url = path + AliyunOSSUtils.uploadLocalFile(path + fileName, fileName, tempPath);
                //保存file文件表
                TFile tFile = saveFile(fileNameStr, url, "gcssb", "books", hz, ywId, person);

                map.put("newName", newName);
                map.put("hz", hz);
                map.put("fileId", tFile.getId());
                map.put("pageCount", pageCount);
                map.put("fileNameChunk", fileNameChunk);
                JSONObject json = new JSONObject(map);


                //更新书籍信息
                DfdwTGcssbBooks entity = gcssbBooksService.getById(ywId);
                if (entity != null) {
                    if("pdf".equals(hz)){
                        entity.setTotalPages(-1);
                    }else {
                        entity.setTotalPages(0);
                    }

                    entity.setFileId(tFile.getId());
                    entity.setUploadMsg(json.toJSONString());
                    gcssbBooksService.updateById(entity);
                }

                //返回标识
                return Result.ok(map);

            } else {
                map.put("hz", hz);
                map.put("fileNameChunk", fileNameChunk);
                return Result.ok(map);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("文件上传失败：" + ex.getMessage());
        }
    }


    /**
     * 文件分割保存
     *
     * @param newName       新的文件名称不带后缀 （d7edebe6-bf0f-48d9-ad72-f1306657ee4f）
     * @param ywId          业务Id
     * @param hz            文件后缀
     * @param fileNameChunk 本地临时保存的文件名称（1.pdf）
     * @return
     */
    @Override
    public Result<Object> uploadSplit(String hz, int ywId, String newName, String fileNameChunk) {
        try {
            DfdwTGcssbBooks entity = gcssbBooksService.getById(ywId);
            if (entity == null) {
                return Result.error("获取书籍信息失败");
            }
            //兼容处理，兼容手动分割上传
            if (StringUtils.isEmpty(fileNameChunk) && StringUtils.isEmpty(entity.getUploadMsg())) {
                JSONObject json = JSONObject.parseObject(entity.getUploadMsg());
                Map<String, Object> map = (Map<String, Object>) json;
                fileNameChunk = map.get("fileNameChunk").toString();
                newName = map.get("newName").toString();
                hz = map.get("hz").toString();
            }


            //文件临时路径
            String tempPath = System.getProperty("java.io.tmpdir") + "/yykj/dfdwGcssb/temp/";

            //oss路径
            String path = "Upload/FileManage/dfdwGcssb/Books/";

            File file = new File(tempPath + fileNameChunk);


            //按照oss规则改名
            String parentPath = path + newName + "/";


            //pdf处理
            int pageCount = 0;
            try (PDDocument document = PDDocument.load(file)) {
                pageCount = document.getNumberOfPages();
                for (int i = 0; i < pageCount; i++) {
                    PDPage page = document.getPage(i);
                    PDDocument outputDoc = new PDDocument();
                    outputDoc.addPage(page);

                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    outputDoc.save(baos);
                    outputDoc.close();

                    String pageName = "page-" + (i + 1) + ".pdf";
                    String outputPath = parentPath + pageName;

                    // 将字节数组转换为Base64字符串，或者直接使用字节数组上传
                    AliyunOSSUtils.uploadBytesFile(outputPath, pageName, baos.toByteArray());


                    if (i == pageCount - 1) {
                        //如果是最后一次，更新数据表，总页数【同时用这个字段来标识，分页切分成功了】
                        //更新书籍信息
                        entity.setTotalPages(pageCount);
                        gcssbBooksService.updateById(entity);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                //全部处理完毕，删除历史文件
                if (file != null && file.exists()) {
                    file.delete();
                }
            }


        } catch (Exception ex) {
            return Result.error("分页切割失败：" + ex.getMessage());
        }
        return Result.ok("分页切割成功");
    }


    /**
     * 普通文件上传
     *
     * @param multipartFile file文件
     * @param ywId          业务Id
     * @param person
     * @return
     */
    @Override
    public Result<Object> upload(MultipartFile multipartFile, Integer ywId,String hjID, PersonEntity person) {
        try {
            String path = "Upload/FileManage/dfdwGcssb/other/";
            String hz = FilenameUtils.getExtension(multipartFile.getOriginalFilename()).toLowerCase();
            String newName = UUID.randomUUID() + "";
            String fileName = newName + "." + hz;
            //保存至oss
            String url = path + AliyunOSSUtils.uploadFile(path, fileName, multipartFile);
            //保存file文件表
            TFile tFile = saveFile(multipartFile.getOriginalFilename(), url, "gcssb", hjID, hz, ywId, person);
            return Result.ok(tFile);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("上传失败：" + ex.getMessage());
        }
    }

    @Override
    public void downloadChunk(Integer ywId, String filePath, String fileName, Integer chunkSize, Integer chunkTotal, Integer index, HttpServletResponse response, PersonEntity person) {
        try (OutputStream outputStream = response.getOutputStream()) {
            String localPath = System.getProperty("java.io.tmpdir") + "/yykj/gcssb/temp/" + filePath.split("/")[filePath.split("/").length - 1];
            File file = new File(localPath);
            byte[] buffer = null;
            if (file.exists()) {
                buffer = fileTobyte(file);
            } else {
                file.getParentFile().mkdirs();
                file.createNewFile();
                byte[] bytes = AliyunOSSUtils.downloadFileStream(filePath);
                try (FileOutputStream fos = new FileOutputStream(localPath)) {
                    fos.write(bytes);
                    file = new File(localPath);
                    buffer = fileTobyte(file);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (index.equals(chunkTotal)) {
                file.delete();
                DfdwTGcssbDownloadLog downloadLog = new DfdwTGcssbDownloadLog();
                downloadLog.setUserId(person.getId());
                downloadLog.setUserName(person.getRealName());
                downloadLog.setBooksId(ywId);
                downloadLog.setBooksName(fileName);
                downloadLog.setDownloadTime(new Date());
                downloadLog.setDownloadPath(filePath);
                downloadLog.setDownloadType("PC");
                gcssbDownloadLogService.save(downloadLog);
            }
            int offset = chunkSize * (index - 1);
            int end = 0;
            byte[] chunk = null;
            if (buffer != null) {
                end = Math.min(offset + chunkSize, buffer.length);
                chunk = Arrays.copyOfRange(buffer, offset, end);
                response.addHeader("Content-Length", "" + (chunk.length));
                // 写入数据
                outputStream.write(chunk);
            } else {
                throw new RuntimeException("工程随身宝下载书籍发生错误：未找到文件");
            }

            response.addHeader("Content-Disposition", "attachment;filename=" + fileName + ".pdf");
            response.setHeader("filename", fileName + ".pdf");
            response.setContentType("application/octet-stream");
        } catch (IOException e) {
            throw new RuntimeException("工程随身宝下载书籍发生错误", e);
        }
    }

    @Override
    public Result<Object> uploadImg(MultipartFile multipartFile, PersonEntity person, Integer ywId) {
        try {
            String path = "Upload/FileManage/dfdwGcssb/Books/";
            String hz = FilenameUtils.getExtension(multipartFile.getOriginalFilename()).toLowerCase();
            String newName = UUID.randomUUID() + "";
            String fileName = newName + "." + hz;
            //保存至oss
            String url = path + AliyunOSSUtils.uploadFile(path, fileName, multipartFile);
            //保存file文件表
            TFile tFile = saveFile(multipartFile.getOriginalFilename(), url, "gcssb", "books", hz, ywId, person);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Result.error("上传失败：" + ex.getMessage());
        }
        return Result.ok();
    }


    /**
     * pdf分割
     *
     * @param file       源文件
     * @param parentPath 分割后要保存的路径
     * @throws IOException
     */
    private int pdfSplit(File file, String parentPath) throws IOException {
        //  String parentPath = path+newName+"/";
        int totalPages = 0;
        //分割
        try (PDDocument document = PDDocument.load(file)) {
            int pageCount = document.getNumberOfPages();
            totalPages = pageCount;
            for (int i = 0; i < pageCount; i++) {
                PDPage page = document.getPage(i);
                PDDocument outputDoc = new PDDocument();
                outputDoc.addPage(page);

                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                outputDoc.save(baos);
                outputDoc.close();

                String pageName = "page-" + (i + 1) + ".pdf";
                String outputPath = parentPath + pageName;
                // 将字节数组转换为Base64字符串，或者直接使用字节数组上传
                // String base64Content = Base64.getEncoder().encodeToString(baos.toByteArray());

                AliyunOSSUtils.uploadBytesFile(outputPath, pageName, baos.toByteArray());
            }
        } finally {
            // 删除临时文件
            if (file != null && file.exists()) {
                file.delete();
            }
        }
        return totalPages;
    }

    /**
     * 保存file表数据
     *
     * @param name   原始名称
     * @param path   保存后的oss路径
     * @param type
     * @param hjID
     * @param hz     后缀
     * @param ywId   业务Id
     * @param person
     */
    private TFile saveFile(String name, String path, String type, String hjID, String hz, Integer ywId, PersonEntity person) {
       if("books".equals(hjID)){
           List<TFile> fileList = tFileService.list(new LambdaQueryWrapper<TFile>().eq(TFile::getProjectid, ywId)
                   .eq(TFile::getFunctionid, 20019).eq(TFile::getType, type).eq(TFile::getHjid,hjID));
           if (fileList.size() > 0) {
               for (int i = 0; i < fileList.size(); i++) {
                   tFileService.removeById(fileList.get(i));
               }
           }
       }
        TFile sf = new TFile();
        sf.setFilename(name);
        sf.setFilepath(path);
        sf.setProjectid(ywId);
        sf.setFunctionid(20019);
        sf.setType(type);
        sf.setUploaddate(new Date());
        sf.setHjid(hjID);
        sf.setPersonname(person.getRealName());
        sf.setPersonzgh(person.getLoginName());
        sf.setSubtname(hz);
        tFileService.save(sf);
        return sf;
    }

    /**
     * file转byte
     */
    public static byte[] fileTobyte(File file) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }
}
