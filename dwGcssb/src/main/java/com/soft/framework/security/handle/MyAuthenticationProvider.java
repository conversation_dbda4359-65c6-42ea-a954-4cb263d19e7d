package com.soft.framework.security.handle;

import com.soft.framework.helper.DBHelper;
import com.soft.framework.security.LoginUser;
import com.soft.framework.security.service.SysPermissionService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

@Component
public class MyAuthenticationProvider implements AuthenticationProvider {
    @Autowired
    private SysPermissionService permissionService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = (String)authentication.getCredentials();
        UserDetails userDeatils = null;
        // 根据用户名获取用户信息
        PersonEntity user = DBHelper.GetPersonByLoginName(username);
        if (user==null) {
            throw new BadCredentialsException("用户名不存在");
        } else {
            userDeatils = createLoginUser(user);
            // 自定义的加密规则，用户名、输的密码和数据库保存的盐值进行加密
            //String encodedPassword = Md5Util.getMD5Str(password);
            if (authentication.getCredentials() == null) {
                throw new BadCredentialsException("登录名或密码错误");
            } else if (!password.equals(userDeatils.getPassword())) {
                throw new BadCredentialsException("登录名或密码错误");
            } else {
                UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDeatils, authentication.getCredentials(),userDeatils.getAuthorities());
                result.setDetails(authentication.getDetails());
                return result;
            }
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return true;
    }

    public UserDetails createLoginUser(PersonEntity user)
    {
        return new LoginUser(user,permissionService.getMenuPermission(user));
    }
}
