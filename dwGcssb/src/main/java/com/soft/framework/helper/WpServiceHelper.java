package com.soft.framework.helper;

import com.soft.framework.common.utils.spring.SpringUtil;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.*;
import com.yyszc.wpbase.ventity.PersonEntity;

import java.util.ArrayList;
import java.util.List;

public final class WpServiceHelper {
    public static WpService getWpService() {
        WpService wpService = SpringUtil.getBean(WpService.class);
        if (wpService!= null) {
            return wpService;
        } else {
            return null;
        }
    }

    //-----------------Info信息-------------------------------------------------------
    public static String Hello(String name)
    {
        WpService wpService = getWpService();
        if (wpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String obj=wpService.Hello(name);
            return obj;
        } else {
            return null;
        }
    }

    public static Person TestDBEntity(String name)
    {
        WpService wpService = getWpService();
        if (wpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Person obj=wpService.TestDBEntity(name,token);
            return  obj;
        } else {
            return null;
        }
    }


    public static PersonEntity GetCurrentPerson() {
        WpService wpService = getWpService();
        if (wpService != null) {
            String token = ConfigHelper.getCurrentToken();
            return wpService.GetCurrentPerson(token);
        } else {
            return null;
        }
    }

    public static PersonEntity GetCurrentPerson_S() {
        WpService wpService = getWpService();
        if (wpService != null) {
            String token = ConfigHelper.getCurrentToken();
            return wpService.GetCurrentPerson_S(token);
        } else {
            return null;
        }
    }

    public static Boolean IsTokenValid() {
        WpService wpService = getWpService();
        if (wpService != null) {
            String token = ConfigHelper.getCurrentToken();
            return wpService.IsTokenValid(token);
        } else {
            return false;
        }
    }

    public static Boolean VerifyToken() {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean retf = wppService.VerifyToken(token);
            return retf;
        } else {
            return false;
        }
    }

    public static List<NFT_SQL_IDSTR> GetPreventSqlList() {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<NFT_SQL_IDSTR> obj = wppService.GetPreventSqlList(token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFT_NWIPD> GetNwIpdList() {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<NFT_NWIPD> obj = wppService.GetNwIpdList(token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFT_WhiteList> GetNftWhiteList() {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<NFT_WhiteList> obj = wppService.GetNftWhiteList(token);
            return obj;
        } else {
            List<NFT_WhiteList> obj = new ArrayList<NFT_WhiteList>();
            return obj;
        }
    }

    public static DataTable GetUserNavTable(String mid, int userid, int pwf) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            DataTable obj = wppService.GetUserNavTable(mid, userid, pwf, token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<vFunctionItem> GetUserFunctionList(String mid, int userid, int pwf) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<vFunctionItem> obj = wppService.GetUserFunctionList(mid, userid, pwf, token);
            return obj;
        } else {
            return null;
        }
    }

    public static String GetUserNavMenuGroup(String mid, int userid, int pwf) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String tmpstr = wppService.GetUserNavMenuGroup(mid, userid, pwf, token);
            return tmpstr;
        } else {
            return "";
        }
    }

    public static List<PersonEntity> GetRolePersonList(int roleid) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<PersonEntity> obj = wppService.GetRolePersonList(roleid, token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<PersonEntity> GetCompRolePersonList(int roleid, int topGroupId) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<PersonEntity> obj = wppService.GetCompRolePersonList(roleid, topGroupId, token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<PersonEntity> GetPermissionPersonList(String permission) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<PersonEntity> obj = wppService.GetPermissionPersonList(permission, token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<PersonEntity> GetCompPermissionPersonList(String permission, int topGroupId) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<PersonEntity> obj = wppService.GetCompPermissionPersonList(permission, topGroupId, token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<vComp> GetAllTopGroup(int mid) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<vComp> obj = wppService.GetAllTopGroup(mid, token);
            return obj;
        } else {
            return null;
        }
    }

    public static PersonEntity GetPersonByLoginName(String loginName) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            PersonEntity person = wppService.GetPersonByLoginName(loginName, token);
            return person;
        } else {
            return null;
        }
    }

    public static PersonEntity GetPersonByUserId(int userid) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            PersonEntity person = wppService.GetPersonByUserId(userid, token);
            return person;
        } else {
            return null;
        }
    }

    public static vPerson GetVPersonByLoginName_S(String loginName) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            vPerson person = wppService.GetVPersonByLoginName_S(loginName, token);
            return person;
        } else {
            return null;
        }
    }

    public static Person GetPersonByLoginName_S(String loginName) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Person person = wppService.GetPersonByLoginName_S(loginName, token);
            return person;
        } else {
            return null;
        }
    }

    public static vPerson GetVPersonByUserId_S(int userid) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            vPerson person = wppService.GetVPersonByUserId_S(userid, token);
            return person;
        } else {
            return null;
        }
    }

    public static Person GetPersonByUserId_S(int userid) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Person person = wppService.GetPersonByUserId_S(userid, token);
            return person;
        } else {
            return null;
        }
    }

    public static DataTable GetChildFuntionItemByParent(int userid, int pwf, String gid) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            DataTable tmpdt = wppService.GetChildFuntionItemByParent(userid, pwf, gid, token);
            return tmpdt;
        } else {
            return null;
        }
    }
    //-----------------------Info信息----------------------------------------------

    //--------------------Tool工具集---------------------------------------------------
    public static boolean SendSms(String phone, String content) {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean retb = wppService.SendSms(phone, content, token);
            return retb;
        } else {
            return false;
        }
    }

    public static  Boolean AddApplicationLog(String uname,  String func, String module,  String detail)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean retb = wppService.AddApplicationLog(uname, func,module,detail,token);
            return retb;
        } else {
            return false;
        }
    }

    public static  Boolean AddApplicationLogYW(String uname,  String func, String module,  String detail)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean retb = wppService.AddApplicationLog(uname, func,module,detail,token);
            return retb;
        } else {
            return false;
        }
    }
    //---------------------Tool工具集----------------------------------------------------


    //---------------------lcdb工具集----------------------------------------------------
    public static  List<NFV_WorkFlow> GetWorkFlowList(String strsql){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<NFV_WorkFlow> obj = wppService.GetWorkFlowList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------lcdb工具集----------------------------------------------------

    //---------------------Person表操作接口------------------------------------------------
    public static Person GetPersonById(int id){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Person person = wppService.GetPersonById(id,token);
            return person;
        } else {
            return null;
        }
    }

    public static vPerson GetVPersonById(int id){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            vPerson person = wppService.GetVPersonById(id,token);
            return person;
        } else {
            return null;
        }
    }

    public static Person GetPersonBySql(String strsql){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            Person person = wppService.GetPersonBySql(bstrsql,token);
            return person;
        } else {
            return null;
        }
    }

    public static vPerson GetVPersonBySql(String strsql){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            vPerson person = wppService.GetVPersonBySql(bstrsql,token);
            return person;
        } else {
            return null;
        }
    }

    public static List<Person> GetPersonList(String strsql){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Person> obj = wppService.GetPersonList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<vPerson> GetVPersonList(String strsql){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<vPerson> obj = wppService.GetVPersonList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Person表操作接口------------------------------------------------

    //---------------------Role表操作接口------------------------------------------------
    public static Role GetRoleById(int id){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Role role = wppService.GetRoleById(id,token);
            return role;
        } else {
            return null;
        }
    }

    public static List<Role> GetRoleList(String strsql){
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Role> obj = wppService.GetRoleList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Role表操作接口------------------------------------------------

    //---------------------Permission表操作接口------------------------------------------------
    public static Permission GetPermissionById(String Id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Permission permission = wppService.GetPermissionById(Id,token);
            return permission;
        } else {
            return null;
        }
    }

    public static List<Permission> GetPermissionList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Permission> permission = wppService.GetPermissionList(bstrsql,token);
            return permission;
        } else {
            return null;
        }
    }
    //---------------------Permission表操作接口------------------------------------------------

    //---------------------FunctionItem表操作接口------------------------------------------------
    public static FunctionItem GetFunctionItemById(String id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            FunctionItem functionItem = wppService.GetFunctionItemById(id,token);
            return functionItem;
        } else {
            return null;
        }
    }

    public static FunctionItem GetFunctionItemBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            FunctionItem functionItem = wppService.GetFunctionItemBySql(bstrsql,token);
            return functionItem;
        } else {
            return null;
        }
    }

    public static vFunctionItem GetVFunctionItemBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            vFunctionItem functionItem = wppService.GetVFunctionItemBySql(bstrsql,token);
            return functionItem;
        } else {
            return null;
        }
    }

    public static List<FunctionItem> GetFunctionItemList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<FunctionItem> obj = wppService.GetFunctionItemList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static vFunctionItem GetFunctionItemByMark(String title,String parent)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            vFunctionItem functionItem = wppService.GetFunctionItemByMark(title,parent,token);
            return functionItem;
        } else {
            return null;
        }
    }
    //---------------------FunctionItem表操作接口------------------------------------------------


    //---------------------Module表操作接口------------------------------------------------
    public static List<Module> GetModuleList()
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<Module> obj= wppService.GetModuleList(token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<Module> GetModuleListInMode(String mlist)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<Module> obj= wppService.GetModuleListInMode(mlist,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Module表操作接口------------------------------------------------

    //---------------------GroupItem表操作接口------------------------------------------------
    public static GroupItem GetGroupItemById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            GroupItem groupItem = wppService.GetGroupItemById(id,token);
            return groupItem;
        } else {
            return null;
        }
    }

    public static GroupItem GetGroupItemBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            GroupItem groupItem = wppService.GetGroupItemBySql(bstrsql,token);
            return groupItem;
        } else {
            return null;
        }
    }

    public static vGroupItem GetVGroupItemById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            vGroupItem groupItem = wppService.GetVGroupItemById(id,token);
            return groupItem;
        } else {
            return null;
        }
    }

    public static vGroupItem GetVGroupItemBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            vGroupItem groupItem = wppService.GetVGroupItemBySql(bstrsql,token);
            return groupItem;
        } else {
            return null;
        }
    }

    public static List<GroupItem> GetGroupItemList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<GroupItem> obj = wppService.GetGroupItemList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<vGroupItem> GetVGroupItemList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<vGroupItem> obj = wppService.GetVGroupItemList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<vGroupItem> GetGroupList()
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<vGroupItem> obj = wppService.GetGroupList(token);
            return obj;
        } else {
            return null;
        }
    }

    public static String GetGroupTreeExtJs(int group)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String obj = wppService.GetGroupTreeExtJs(group,token);
            return obj;
        } else {
            return null;
        }
    }

    public static String GetGroupTree(int group)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String obj = wppService.GetGroupTree(group,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------GroupItem表操作接口------------------------------------------------

    //---------------------vComp视图操作接口-------------------------------------------
    public static List<vComp> GetCompList()
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<vComp> obj = wppService.GetCompList(token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------vComp视图操作接口--------------------------------------------


    //---------------------Lcdefine表操作接口-------------------------------------------
    public static Lcdefine GetLcdefineById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Lcdefine obj = wppService.GetLcdefineById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<Lcdefine> GetLcdefineList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Lcdefine> obj = wppService.GetLcdefineList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static Lcdefine GetLcdefineByLcId(String lcId)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Lcdefine obj = wppService.GetLcdefineByLcId(lcId,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Lcdefine表操作接口--------------------------------------------

    //---------------------Lcjd表操作接口--------------------------------------------
    public static Lcjd GetLcjdById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Lcjd obj = wppService.GetLcjdById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<Lcjd> GetLcjdList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Lcjd> obj = wppService.GetLcjdList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    
    public static List<Lcjd> GetLcjdListByLc(int lcId)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<Lcjd> obj = wppService.GetLcjdListByLc(lcId,token);
            return obj;
        } else {
            return null;
        }
    }

    public static Lcjd GetLcjdByLcId(int LcjdId)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Lcjd obj = wppService.GetLcjdByLcId(LcjdId,token);
            return obj;
        } else {
            return null;
        }
    }        

    public static Lcjd GetLcjdBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            Lcjd obj = wppService.GetLcjdBySql(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Lcjd表操作接口--------------------------------------------

    //---------------------Lc_currentState表操作接口--------------------------------------------
    public static Lc_currentState GetLc_currentStateById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Lc_currentState obj = wppService.GetLc_currentStateById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static Lc_currentState GetLc_currentStateBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            Lc_currentState obj = wppService.GetLc_currentStateBySql(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }      

    public static List<Lc_currentState> GetLc_currentStateList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Lc_currentState> obj = wppService.GetLc_currentStateList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Lc_currentState表操作接口--------------------------------------------

    //---------------------Lc_workFlow表操作接口--------------------------------------------
    public static Lc_workFlow GetLc_workFlowById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            Lc_workFlow obj = wppService.GetLc_workFlowById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static Lc_workFlow GetLc_workFlowBySql(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            Lc_workFlow obj = wppService.GetLc_workFlowBySql(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<Lc_workFlow> GetLc_workFlowList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<Lc_workFlow> obj = wppService.GetLc_workFlowList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------Lc_workFlow表操作接口--------------------------------------------

    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------
    public static NFT_ModuleAMLink GetNFT_ModuleAMLinkById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            NFT_ModuleAMLink obj = wppService.GetNFT_ModuleAMLinkById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFT_ModuleAMLink> GetNFT_ModuleAMLinkListByMId(int mid)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<NFT_ModuleAMLink> obj = wppService.GetNFT_ModuleAMLinkListByMId(mid,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFT_ModuleAMLink> GetNFT_ModuleAMLinkList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<NFT_ModuleAMLink> obj = wppService.GetNFT_ModuleAMLinkList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------

    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------
    public static NFT_ModuleLCLink GetNFT_ModuleLCLinkById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            NFT_ModuleLCLink obj = wppService.GetNFT_ModuleLCLinkById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static NFV_ModuleLCLink GetNFV_ModuleLCLinkById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            NFV_ModuleLCLink obj = wppService.GetNFV_ModuleLCLinkById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFT_ModuleLCLink> GetNFT_ModuleLCLinkList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<NFT_ModuleLCLink> obj = wppService.GetNFT_ModuleLCLinkList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFV_ModuleLCLink> GetNFV_ModuleLCLinkList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<NFV_ModuleLCLink> obj = wppService.GetNFV_ModuleLCLinkList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }

    public static NFT_ModuleLCLink GetNFT_ModuleLCLinkByLc(String lcid)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            NFT_ModuleLCLink obj = wppService.GetNFT_ModuleLCLinkByLc(lcid,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------

    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------
    public static NFT_SQL_IDSTR GetNFT_SQL_IDSTRById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            NFT_SQL_IDSTR obj = wppService.GetNFT_SQL_IDSTRById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<NFT_SQL_IDSTR> GetNFT_SQL_IDSTRList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<NFT_SQL_IDSTR> obj = wppService.GetNFT_SQL_IDSTRList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------

    //---------------------DictionaryDefine表操作接口-------------------------------------------
    public static DictionaryDefine GetDictionaryDefineById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            DictionaryDefine obj = wppService.GetDictionaryDefineById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<DictionaryDefine> GetDictionaryDefineList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<DictionaryDefine> obj = wppService.GetDictionaryDefineList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------DictionaryDefine表操作接口--------------------------------------------


    //---------------------DictionaryValue表操作接口-------------------------------------------
    public static DictionaryValue GetDictionaryValueById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            DictionaryValue obj = wppService.GetDictionaryValueById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static vDictionaryValue GetVDictionaryValueById(int id)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            vDictionaryValue obj = wppService.GetVDictionaryValueById(id,token);
            return obj;
        } else {
            return null;
        }
    }

    public static List<vDictionaryValue> GetDictionaryValueList(String strsql)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            List<vDictionaryValue> obj = wppService.GetDictionaryValueList(bstrsql,token);
            return obj;
        } else {
            return null;
        }
    }
    
    public static List<DictionaryValue> GetDictionaryValueListByType(String type)
    {
        WpService wppService = getWpService();
        if (wppService != null) {
            String token = ConfigHelper.getCurrentToken();
            List<DictionaryValue> obj = wppService.GetDictionaryValueListByType(type,token);
            return obj;
        } else {
            return null;
        }
    }
    //---------------------DictionaryValue表操作接口--------------------------------------------


    //-------------------操作根棍工具集------------------------------------------------------------
    //--------------------Tool工具集------------------------------------------------------------
    public static DataTable GetDataTable(String strsql) {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            DataTable obj = WpService.GetDataTable(bstrsql, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean RecordExists(String strsql)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            Boolean obj = WpService.RecordExists(bstrsql, token);
            return obj;
        } else {
            return false;
        }
    }

    public static String GetRecordCount(String strsql)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            String obj = WpService.GetRecordCount(bstrsql, token);
            return obj;
        } else {
            return null;
        }
    }

    public static String ExecuteScalar(String strsql)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String bstrsql = AesHelper.aesEncodeCBC(strsql);
            String obj = WpService.ExecuteScalar(bstrsql, token);
            return obj;
        } else {
            return null;
        }
    }

    //---------------------Tool工具集----------------------------------------------------

    //---------------------Person表操作接口------------------------------------------------
    public static Integer AddPerson(Person entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddPerson(entity, token);
            return obj;
        } else {
            return null;
        }
    }


    public static Boolean UpdatePerson(Person entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdatePerson(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeletePersonById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeletePersonById(id, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean ResetPassword(int id, String defpwd)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.ResetPassword(id,defpwd, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean UpdatePassword(int id,String newpwd)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdatePassword(id,newpwd, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Person表操作接口------------------------------------------------

    //---------------------Role表操作接口------------------------------------------------
    public static Integer AddRole(Role entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddRole(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateRole(Role entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateRole(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteRoleById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteRoleById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Role表操作接口------------------------------------------------

    //---------------------Permission表操作接口------------------------------------------------
    public static String AddPermission(Permission entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String obj = WpService.AddPermission(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdatePermission(Permission entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdatePermission(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeletePermissionById(String id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeletePermissionById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Permission表操作接口------------------------------------------------

    //---------------------FunctionItem表操作接口------------------------------------------------
    public static String AddFunctionItem(FunctionItem entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            String obj = WpService.AddFunctionItem(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateFunctionItem(FunctionItem entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateFunctionItem(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteFunctionItemById(String id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteFunctionItemById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------FunctionItem表操作接口------------------------------------------------


    //---------------------GroupItem表操作接口------------------------------------------------
    public static Integer AddGroupItem(GroupItem entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddGroupItem(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateGroupItem(GroupItem entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateGroupItem(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteGroupItemById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteGroupItemById(id, token);
            return obj;
        } else {
            return false;
        }
    }

    //---------------------GroupItem表操作接口------------------------------------------------


    //---------------------RolePermission表操作接口--------------------------------------------
    public static Boolean DeleteRolePermission(int RoleId,String PermissionNo)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteRolePermission(RoleId,PermissionNo, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean AddRolePermission(int RoleId,String PermissionNo)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.AddRolePermission(RoleId,PermissionNo, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------RolePermission表操作接口--------------------------------------------

    //---------------------RolePerson表操作接口--------------------------------------------
    public static Boolean DeleteRolePerson(int RoleId,int PersonId)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteRolePerson(RoleId,PersonId, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean AddRolePerson(int RoleId,int PersonId)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.AddRolePerson(RoleId,PersonId, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------RolePerson表操作接口--------------------------------------------


    //---------------------Lcdefine表操作接口-------------------------------------------
    public static Integer AddLcdefine(Lcdefine entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddLcdefine(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateLcdefine(Lcdefine entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateLcdefine(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteLcdefineById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteLcdefineById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Lcdefine表操作接口--------------------------------------------

    //---------------------Lcjd表操作接口--------------------------------------------
    public static Integer AddLcjd(Lcjd entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddLcjd(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateLcjd(Lcjd entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateLcjd(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteLcjdById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteLcjdById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Lcjd表操作接口--------------------------------------------

    //---------------------Lc_currentState表操作接口--------------------------------------------
    public static Integer AddLc_currentState(Lc_currentState entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddLc_currentState(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static  Boolean UpdateLc_currentState(Lc_currentState entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateLc_currentState(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteLc_currentStateById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteLc_currentStateById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Lc_currentState表操作接口--------------------------------------------

    //---------------------Lc_workFlow表操作接口--------------------------------------------
    public static Integer AddLc_workFlow(Lc_workFlow entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddLc_workFlow(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateLc_workFlow(Lc_workFlow entity){
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateLc_workFlow(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteLc_workFlowById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteLc_workFlowById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------Lc_workFlow表操作接口--------------------------------------------

    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------
    public static Integer AddNFT_ModuleAMLink(NFT_ModuleAMLink entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddNFT_ModuleAMLink(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateNFT_ModuleAMLink(NFT_ModuleAMLink entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateNFT_ModuleAMLink(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteNFT_ModuleAMLinkById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteNFT_ModuleAMLinkById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------NFT_ModuleAMLink表操作接口--------------------------------------------

    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------
    public static Integer AddNFT_ModuleLCLink(NFT_ModuleLCLink entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddNFT_ModuleLCLink(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateNFT_ModuleLCLink(NFT_ModuleLCLink entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateNFT_ModuleLCLink(entity, token);
            return obj;
        } else {
            return false;
        }
    }


    public static Boolean DeleteNFT_ModuleLCLinkById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteNFT_ModuleLCLinkById(id, token);
            return obj;
        } else {
            return false;
        }
    }

    //---------------------NFT_ModuleLCLink表操作接口--------------------------------------------

    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------
    public static Integer AddNFT_SQL_IDSTR(NFT_SQL_IDSTR entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddNFT_SQL_IDSTR(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateNFT_SQL_IDSTR(NFT_SQL_IDSTR entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateNFT_SQL_IDSTR(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    Boolean DeleteNFT_SQL_IDSTRById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteNFT_SQL_IDSTRById(id, token);
            return obj;
        } else {
            return false;
        }
    }
    //---------------------NFT_SQL_IDSTR表操作接口--------------------------------------------

    //---------------------DictionaryDefine表操作接口-------------------------------------------
    public static Integer AddDictionaryDefine(DictionaryDefine entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddDictionaryDefine(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateDictionaryDefine(DictionaryDefine entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateDictionaryDefine(entity, token);
            return obj;
        } else {
            return false;
        }
    }

    public static Boolean DeleteDictionaryDefineById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteDictionaryDefineById(id, token);
            return obj;
        } else {
            return false;
        }
    }

    //---------------------DictionaryDefine表操作接口--------------------------------------------


    //---------------------DictionaryValue表操作接口-------------------------------------------
    public static Integer AddDictionaryValue(DictionaryValue entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Integer obj = WpService.AddDictionaryValue(entity, token);
            return obj;
        } else {
            return null;
        }
    }

    public static Boolean UpdateDictionaryValue(DictionaryValue entity)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.UpdateDictionaryValue(entity, token);
            return obj;
        } else {
            return false;
        }
    }


    public static Boolean DeleteDictionaryValueById(int id)
    {
        WpService WpService = getWpService();
        if (WpService != null) {
            String token = ConfigHelper.getCurrentToken();
            Boolean obj = WpService.DeleteDictionaryValueById(id, token);
            return obj;
        } else {
            return false;
        }
    }

    //---------------------DictionaryValue表操作接口--------------------------------------------}
}
