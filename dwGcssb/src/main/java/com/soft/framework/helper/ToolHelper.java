package com.soft.framework.helper;

import com.yyszc.extend.DataTable;
import com.soft.framework.common.utils.JsonUtils;
import com.soft.framework.common.utils.UrlUtil;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.security.service.TokenService;
import com.yyszc.wpbase.ventity.PersonEntity;
import lombok.Data;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRelation;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.Color;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Predicate;

@SuppressWarnings("unchecked")
public class ToolHelper {
    @Data
    public static class NowSchdeuleManage {
        public String success;
        public String hzContent;
        public String titleContent;
        public String sgContent;
        public String schdeuleContent;
    }

    public static class ExportColumnMode {
        public String col_code;
        public String col_name;
        public int col_size;
        public String col_sum_cell;
        public int col_type;

        public ExportColumnMode(String code, String name, int size) {
            col_code = code;
            col_name = name;
            col_size = size;
            col_sum_cell = null;
            col_type = 0;
        }

        public ExportColumnMode(String code, String name, int size, String sum_cell) {
            col_code = code;
            col_name = name;
            col_size = size;
            col_sum_cell = sum_cell;
            col_type = 0;
        }

        public ExportColumnMode(String code, String name, int size, String sum_cell, int type) {
            col_code = code;
            col_name = name;
            col_size = size;
            col_sum_cell = sum_cell;
            col_type = type;
        }
    }

    public class ImpExchangeData {
        public String imp_jsonf;

        public ImpExchangeData(String jsonf) {
            imp_jsonf = jsonf;
        }
    }

    @Data
    public static class Result_FileBytePack implements Serializable {
        public boolean success;
        public String text;
        public String fname;
        public String ftype;
        public byte[] data1;
        public byte[] data2;
    }

    @Data
    public static class ExtJS_Sort {
        public String property;
        public String direction;
    }

    @Data
    public static class ProgClass {
        public boolean success;
        public String percent;
        public String msginfo;
    }

    public static String ToJsonString(String strmsg)
    {
        String strReturn;
        strReturn = strmsg.replace("\n", "");
        strReturn = strReturn.replace("\r", "");
        strReturn = strReturn.replace("\b", "");
        strReturn = strReturn.replace("\f", "");
        strReturn = strReturn.replace("\t", "");
        strReturn = strReturn.replace("\"", "");
        strReturn = strReturn.replace("\'", "");
        strReturn = strReturn.replace(":", "");
        strReturn = strReturn.replace("（", "(");
        strReturn = strReturn.replace("）", ")");
        return strReturn;
    }

    public static int getRealDays(Date StartDate, Date endDate)
    {
        try
        {
            int Real_Days = 0;//实际天数
            SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");

            if(StartDate.compareTo(sdf.parse("2018-01-01"))<0)
            {
            } else
            {
                Real_Days= DateUtil.dateDiff('d', StartDate,endDate);
            }
            return Real_Days;
        }
        catch (Exception Ex)
        {
            return 0;
        }
    }

    public static String SmartLikeStr(String pname)
    {
        return " and (a.sendpersonZgh='" + pname + "' or a.sendpersonZgh like '" + pname + "~%' or a.sendpersonZgh like '%~" + pname + "~%' or a.sendpersonZgh like '%~" + pname + "' or a.sendpersonZgh like '%~" + pname + "~')";
    }


    public static boolean ReadFileToStr(String fstr, List<String> sb) {
        try {
            File file = new File(fstr);
            if (!file.exists()) {
                return false;
            }

            InputStreamReader isr=new InputStreamReader(new FileInputStream(file),"UTF-8");
            BufferedReader bReader = new BufferedReader(isr);
            String s = "";
            while ((s = bReader.readLine()) != null) {
                sb.add(s);
                //System.out.println(s);
            }
            bReader.close();

            return true;
        } catch (Exception err) {
            return false;
        }
    }

    private static boolean ReadFiletoStringList(String fstr, ArrayList slist) {
        try {
            File file = new File(fstr);
            if (!file.exists()) {
                return false;
            }
            slist.clear();

            FileReader reader = new FileReader(file);
            BufferedReader bReader = new BufferedReader(reader);
            StringBuilder sb = new StringBuilder();
            String s = "";
            while ((s = bReader.readLine()) != null) {
                slist.add(s);
            }
            bReader.close();

            return true;
        } catch (Exception err) {
            return false;
        }
    }

    public static String PathStrExtractFileExt(String path) {
        return path.substring(path.lastIndexOf(".") + 1);
    }

    public static CellStyle createCellStyleXSSF(Workbook wb, HorizontalAlignment hAlignment, VerticalAlignment vAlignment, boolean fontBold, int fontPoint, boolean isBorder) {
        CellStyle cellStyle = wb.createCellStyle(); //创建列样式
        cellStyle.setAlignment(hAlignment); //水平居中
        cellStyle.setVerticalAlignment(vAlignment); //垂直居中
        cellStyle.setWrapText(true);
        if (isBorder) {
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderTop(BorderStyle.THIN);
        }
        XSSFFont cellStyleFont = (XSSFFont) wb.createFont(); //创建字体
        cellStyleFont.setBold(fontBold); //字体加粗
        cellStyleFont.setFontHeightInPoints((short) fontPoint); //字体大小
        cellStyle.setFont(cellStyleFont); //将字体绑定到样式
        return cellStyle;
    }

    //重载
    public static boolean ExportDS2XlsFile(DataTable dt, String titstr, List<ExportColumnMode> cmlist, StringBuilder retstr, StringBuilder fname) {
        String path = ConfigHelper.getTempPath();
        try {
            Workbook wk = new XSSFWorkbook();
            Sheet sheet = wk.createSheet(titstr);

            CellStyle tcs = createCellStyleXSSF(wk, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true, 10, true);
            CellStyle ccs = createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);

            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                //设置标题行
                tmpRow = sheet.createRow(0);
                if (tmpRow == null) {
                    retstr.append("程序获取XLS文件行失败!\\n");
                    return false;
                }
                tmpRow.setHeightInPoints(30);

                for (int j = 0; j < cmlist.size(); j++) {
                    ExportColumnMode ecm = cmlist.get(j);
                    sheet.setColumnWidth(j, ecm.col_size * 256);
                    tmpCell = tmpRow.createCell(j);
                    tmpCell.setCellStyle(tcs);
                    if (tmpCell != null) {
                        tmpCell.setCellValue(ecm.col_name);
                    }
                }

                if(dt!=null)
                {
                    for (int i = 0; i < dt.getTotalCount(); i++) {
                        tmpRow = sheet.createRow(i + 1);
                        if (tmpRow == null) {
                            retstr.append("程序获取XLS文件行失败!\\n");
                            return false;
                        }
                        tmpRow.setHeightInPoints(30);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (tmpCell != null) {
                                Object colobj = dt.getRow(i).getColValue(ecm.col_code);
                                if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                    String colval = (colobj == null || colobj.toString() == "") ? "" : colobj.toString();
                                    tmpCell.setCellValue(colval);
                                } else {
                                    double colval = (colobj == null || colobj.toString() == "") ? 0 : Double.parseDouble(colobj.toString());
                                    tmpCell.setCellValue(colval);
                                }
                            }
                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(dt.getTotalCount() + 1);
                    if (tmpRow == null) {
                        retstr.append("程序获取XLS文件行失败!\\n");
                        return false;
                    }
                    tmpRow.setHeightInPoints(30);
                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (dt.getTotalCount() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + "2:" + ecm.col_sum_cell + (dt.getTotalCount() + 1) + ")");
                            }
                        }
                    }
                }
            }

            if (ConfigHelper.isEPWMask()) {
                SetWaterMark((XSSFWorkbook) sheet.getWorkbook());
            }

            String filename = titstr + MetaHelper.GetSimpTimeString(new Date()) + ".xlsx";
            String fullname = path + ConfigHelper.getfSepChar() + filename;
            if (FileUtil.FileExists(fullname)) {
                FileUtil.Delete(fullname);
            }

            FileOutputStream filess = new FileOutputStream(fullname);
            wk.write(filess);
            //filess.Flush();
            filess.close();

            fname.append(filename);

            return true;
        } catch (Exception Ex) {
            retstr.append("程序产生异常" + Ex.getMessage() + "!\\n");
            return false;
        }
    }
    //重载
    public static boolean ExportDS2XlsFile(Workbook wk,DataTable dt, String titstr, List<ExportColumnMode> cmlist, StringBuilder retstr, StringBuilder fname) {
        String path = ConfigHelper.getTempPath();
        try {
            Sheet sheet = wk.createSheet(titstr);

            CellStyle tcs = createCellStyleXSSF(wk, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true, 10, true);
            CellStyle ccs = createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);

            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                //设置标题行
                tmpRow = sheet.createRow(0);
                if (tmpRow == null) {
                    retstr.append("程序获取XLS文件行失败!\\n");
                    return false;
                }
                tmpRow.setHeightInPoints(30);

                for (int j = 0; j < cmlist.size(); j++) {
                    ExportColumnMode ecm = cmlist.get(j);
                    sheet.setColumnWidth(j, ecm.col_size * 256);
                    tmpCell = tmpRow.createCell(j);
                    tmpCell.setCellStyle(tcs);
                    if (tmpCell != null) {
                        tmpCell.setCellValue(ecm.col_name);
                    }
                }

                if(dt!=null)
                {
                    for (int i = 0; i < dt.getTotalCount(); i++) {
                        tmpRow = sheet.createRow(i + 1);
                        if (tmpRow == null) {
                            retstr.append("程序获取XLS文件行失败!\\n");
                            return false;
                        }
                        tmpRow.setHeightInPoints(30);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (tmpCell != null) {
                                Object colobj = dt.getRow(i).getColValue(ecm.col_code);
                                if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                    String colval = (colobj == null || colobj.toString() == "") ? "" : colobj.toString();
                                    tmpCell.setCellValue(colval);
                                } else {
                                    double colval = (colobj == null || colobj.toString() == "") ? 0 : Double.parseDouble(colobj.toString());
                                    tmpCell.setCellValue(colval);
                                }
                            }
                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(dt.getTotalCount() + 1);
                    if (tmpRow == null) {
                        retstr.append("程序获取XLS文件行失败!\\n");
                        return false;
                    }
                    tmpRow.setHeightInPoints(30);
                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (dt.getTotalCount() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + "2:" + ecm.col_sum_cell + (dt.getTotalCount() + 1) + ")");
                            }
                        }
                    }
                }
            }

            if (ConfigHelper.isEPWMask()) {
                SetWaterMark((XSSFWorkbook) sheet.getWorkbook(),(XSSFSheet) sheet);
            }

            String filename = titstr + MetaHelper.GetSimpTimeString(new Date()) + ".xlsx";
            String fullname = path + ConfigHelper.getfSepChar() + filename;
            if (FileUtil.FileExists(fullname)) {
                FileUtil.Delete(fullname);
            }

            FileOutputStream filess = new FileOutputStream(fullname);
            wk.write(filess);
            //filess.Flush();
            filess.close();

            fname.append(filename);

            return true;
        } catch (Exception Ex) {
            retstr.append("程序产生异常" + Ex.getMessage() + "!\\n");
            return false;
        }
    }

    public static <T> boolean ExportList2XlsFile(List<T> _list, String titstr, List<ExportColumnMode> cmlist, StringBuilder retstr, StringBuilder fname) {
        String path = ConfigHelper.getTempPath();
        try {
            Workbook wk = new XSSFWorkbook();
            Sheet sheet = wk.createSheet(titstr);

            CellStyle tcs = createCellStyleXSSF(wk, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true, 10, true);
            CellStyle ccs = createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);

            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                //设置标题行
                tmpRow = sheet.createRow(0);
                if (tmpRow == null) {
                    retstr.append("程序获取XLS文件行失败!\\n");
                    return false;
                }
                tmpRow.setHeightInPoints(30);

                for (int j = 0; j < cmlist.size(); j++) {
                    ExportColumnMode ecm = cmlist.get(j);
                    sheet.setColumnWidth(j, ecm.col_size * 256);
                    tmpCell = tmpRow.createCell(j);
                    tmpCell.setCellStyle(tcs);
                    if (tmpCell != null) {
                        tmpCell.setCellValue(ecm.col_name);
                    }
                }

                if(_list!=null) {
                    for (int i = 0; i < _list.size(); i++) {
                        T obj = _list.get(i);
                        tmpRow = sheet.createRow(i + 1);
                        if (tmpRow == null) {
                            retstr.append("程序获取XLS文件行失败!\\n");
                            return false;
                        }
                        tmpRow.setHeightInPoints(30);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (tmpCell != null) {
                                Field f = obj.getClass().getDeclaredField(ecm.col_code);
                                if (f != null) {
                                    f.setAccessible(true);
                                    Object colobj = f.get(obj);
                                    if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                        String colval = (colobj == null) ? "" : colobj.toString();
                                        tmpCell.setCellValue(colval);
                                    } else {
                                        double colval = (colobj == null) ? 0 : Double.parseDouble(colobj.toString());
                                        tmpCell.setCellValue(colval);
                                    }
                                }
                            }
                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(_list.size() + 1);
                    if (tmpRow == null) {
                        retstr.append("程序获取XLS文件行失败!\\n");
                        return false;
                    }
                    tmpRow.setHeightInPoints(30);
                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (_list.size() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + "2:" + ecm.col_sum_cell + (_list.size() + 1) + ")");
                            }
                        }
                    }
                }
            }

            if (ConfigHelper.isEPWMask()) {
                SetWaterMark((XSSFWorkbook) sheet.getWorkbook());
            }

            String filename = titstr + MetaHelper.GetSimpTimeString(new Date()) + ".xlsx";
            String fullname = path + ConfigHelper.getfSepChar() + filename;
            if (FileUtil.FileExists(fullname)) {
                FileUtil.Delete(fullname);
            }

            FileOutputStream filess = new FileOutputStream(fullname);
            wk.write(filess);
            //filess.Flush();
            filess.close();

            fname.append(filename);

            return true;
        } catch (Exception Ex) {
            retstr.append("程序产生异常" + Ex.getMessage() + "!\\n");
            return false;
        }
    }

    public static <T> boolean ExportList2XlsFile(Workbook wk,List<T> _list, String titstr, List<ExportColumnMode> cmlist, StringBuilder retstr, StringBuilder fname) {
        String path = ConfigHelper.getTempPath();
        try {
            Sheet sheet = wk.createSheet(titstr);

            CellStyle tcs = createCellStyleXSSF(wk, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true, 10, true);
            CellStyle ccs = createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);

            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                //设置标题行
                tmpRow = sheet.createRow(0);
                if (tmpRow == null) {
                    retstr.append("程序获取XLS文件行失败!\\n");
                    return false;
                }
                tmpRow.setHeightInPoints(30);

                for (int j = 0; j < cmlist.size(); j++) {
                    ExportColumnMode ecm = cmlist.get(j);
                    sheet.setColumnWidth(j, ecm.col_size * 256);
                    tmpCell = tmpRow.createCell(j);
                    tmpCell.setCellStyle(tcs);
                    if (tmpCell != null) {
                        tmpCell.setCellValue(ecm.col_name);
                    }
                }

                if(_list!=null) {
                    for (int i = 0; i < _list.size(); i++) {
                        T obj = _list.get(i);
                        tmpRow = sheet.createRow(i + 1);
                        if (tmpRow == null) {
                            retstr.append("程序获取XLS文件行失败!\\n");
                            return false;
                        }
                        tmpRow.setHeightInPoints(30);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (tmpCell != null) {
                                Field f = obj.getClass().getDeclaredField(ecm.col_code);
                                if (f != null) {
                                    f.setAccessible(true);
                                    Object colobj = f.get(obj);
                                    if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                        String colval = (colobj == null) ? "" : colobj.toString();
                                        tmpCell.setCellValue(colval);
                                    } else {
                                        double colval = (colobj == null) ? 0 : Double.parseDouble(colobj.toString());
                                        tmpCell.setCellValue(colval);
                                    }
                                }
                            }
                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(_list.size() + 1);
                    if (tmpRow == null) {
                        retstr.append("程序获取XLS文件行失败!\\n");
                        return false;
                    }
                    tmpRow.setHeightInPoints(30);
                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (_list.size() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + "2:" + ecm.col_sum_cell + (_list.size() + 1) + ")");
                            }
                        }
                    }
                }
            }

            if (ConfigHelper.isEPWMask()) {
                SetWaterMark((XSSFWorkbook) sheet.getWorkbook(),(XSSFSheet) sheet);
            }

            String filename = titstr + MetaHelper.GetSimpTimeString(new Date()) + ".xlsx";
            String fullname = path + ConfigHelper.getfSepChar() + filename;
            if (FileUtil.FileExists(fullname)) {
                FileUtil.Delete(fullname);
            }

            FileOutputStream filess = new FileOutputStream(fullname);
            wk.write(filess);
            //filess.Flush();
            filess.close();

            fname.append(filename);

            return true;
        } catch (Exception Ex) {
            retstr.append("程序产生异常" + Ex.getMessage() + "!\\n");
            return false;
        }
    }

    public static boolean AppendDS2XlsFile(DataTable dt, List<ExportColumnMode> cmlist, int startr, int heightPoint, Sheet sheet, CellStyle ccs, StringBuilder retstr, String fname) {
        String path = ConfigHelper.getTempPath();
        try {
            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                if(dt!=null) {
                    for (int i = 0; i < dt.getTotalCount(); i++) {
                        tmpRow = sheet.createRow(startr + i);
                        if (tmpRow == null) {
                            retstr.append("程序获取XLS文件行失败!\\n");
                            return false;
                        }
                        tmpRow.setHeightInPoints(heightPoint);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (tmpCell != null) {
                                if (ecm.col_type == 1) {
                                    tmpCell.setCellFormula(ecm.col_code.replace("?", String.valueOf(startr + i + 1)));
                                    continue;
                                }

                                Object colobj = dt.getRow(i).getColValue(ecm.col_code);
                                if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                    String colval = (colobj == null || colobj == "") ? "" : colobj.toString();
                                    tmpCell.setCellValue(colval);
                                } else {
                                    double colval = (colobj == null || colobj == "") ? 0 : Double.parseDouble(colobj.toString());
                                    tmpCell.setCellValue(colval);
                                }

                            }
                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(startr + dt.getTotalCount());
                    if (tmpRow == null) {
                        retstr.append("程序获取XLS文件行失败!\\n");
                        return false;
                    }
                    tmpRow.setHeightInPoints(heightPoint);

                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (dt.getTotalCount() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + (startr + 1) + ":" + ecm.col_sum_cell + (startr + dt.getTotalCount()) + ")");
                            }
                        }
                    }
                }

                if (ConfigHelper.isEPWMask()) {
                    SetWaterMark((XSSFWorkbook) sheet.getWorkbook());
                }

                String fullname=path + ConfigHelper.getfSepChar() + fname;
                if (FileUtil.FileExists(fullname)) {
                    FileUtil.Delete(fullname);
                }

                FileOutputStream filess = new FileOutputStream(fullname);
                sheet.getWorkbook().write(filess);
                //filess.Flush();
                filess.close();
            }

            return true;
        } catch (Exception Ex) {
            retstr.append("程序产生异常" + Ex.getMessage() + "!\\n");
            return false;
        }
    }

    public static <T> boolean AppendDS2XlsFile(List<T> _list, List<ExportColumnMode> cmlist, int startr,int heightPoint, Sheet sheet, CellStyle ccs, StringBuilder retstr, String fname) {
        String path = ConfigHelper.getTempPath();
        try {
            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                if(_list!=null) {
                    for (int i = 0; i < _list.size(); i++) {
                        T obj = _list.get(i);
                        tmpRow = sheet.createRow(startr + i);
                        if (tmpRow == null) {
                            retstr.append("程序获取XLS文件行失败!\\n");
                            return false;
                        }
                        tmpRow.setHeightInPoints(30);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (tmpCell != null) {
                                Field f = obj.getClass().getDeclaredField(ecm.col_code);
                                if (f != null) {
                                    f.setAccessible(true);
                                    Object colobj = f.get(obj);
                                    if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                        String colval = (colobj == null) ? "" : colobj.toString();
                                        tmpCell.setCellValue(colval);
                                    } else {
                                        double colval = (colobj == null) ? 0 : Double.parseDouble(colobj.toString());
                                        tmpCell.setCellValue(colval);
                                    }
                                }
                            }
                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(startr + _list.size());
                    if (tmpRow == null) {
                        retstr.append("程序获取XLS文件行失败!\\n");
                        return false;
                    }
                    tmpRow.setHeightInPoints(heightPoint);

                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (_list.size() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + (startr + 1) + ":" + ecm.col_sum_cell + (startr + _list.size()) + ")");
                            }
                        }
                    }
                }

                if (ConfigHelper.isEPWMask()) {
                    SetWaterMark((XSSFWorkbook) sheet.getWorkbook());
                }

                String fullname=path + ConfigHelper.getfSepChar() + fname;
                if (FileUtil.FileExists(fullname)) {
                    FileUtil.Delete(fullname);
                }

                FileOutputStream filess = new FileOutputStream(fullname);
                sheet.getWorkbook().write(filess);
                //filess.Flush();
                filess.close();
            }

            return true;
        } catch (Exception Ex) {
            retstr.append("程序获取XLS文件行失败!\\n");
            return false;
        }
    }

    private static boolean SetWaterMark(XSSFWorkbook workbook) {
        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            String username = person.getRealName();
            String gpname = person.getTopGroupName();

            XSSFSheet sheet = workbook.getSheetAt(0);
            Color wmColor = new Color(229, 229, 229); //LavenderBlush 颜色
            java.awt.Font wmFont = new java.awt.Font("microsoft-yahei", Font.ANSI_CHARSET, 30);
            BufferedImage image = createWatermarkImage(username, gpname, wmFont, wmColor);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, "png", os);

            int pictureIdx = workbook.addPicture(os.toByteArray(), Workbook.PICTURE_TYPE_PNG);
            String rID = sheet.addRelation(null, XSSFRelation.IMAGES, workbook.getAllPictures().get(pictureIdx)).getRelationship().getId();
            sheet.getCTWorksheet().addNewPicture().setId(rID);

            return true;
        } catch (Exception Ex) {
            return false;
        }
    }

    private static boolean SetWaterMark(XSSFWorkbook workbook,XSSFSheet sheet) {
        try {
            PersonEntity person = SessionHelper.getSessionPerson();
            String username = person.getRealName();
            String gpname = person.getTopGroupName();

            Color wmColor = new Color(229, 229, 229); //LavenderBlush 颜色
            java.awt.Font wmFont = new java.awt.Font("microsoft-yahei", Font.ANSI_CHARSET, 30);
            BufferedImage image = createWatermarkImage(username, gpname, wmFont, wmColor);

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, "png", os);

            int pictureIdx = workbook.addPicture(os.toByteArray(), Workbook.PICTURE_TYPE_PNG);
            String rID = sheet.addRelation(null, XSSFRelation.IMAGES, workbook.getAllPictures().get(pictureIdx)).getRelationship().getId();
            sheet.getCTWorksheet().addNewPicture().setId(rID);

            return true;
        } catch (Exception Ex) {
            return false;
        }
    }

    public static BufferedImage createWatermarkImage(String fstr, String sstr, java.awt.Font font, Color textColor) {
        String[] textArray = fstr.split("\n");
        Integer width = 500;
        Integer height = 200;

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        image = g.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
        g.dispose();
        g = image.createGraphics();
        g.setColor(textColor);// 设定画笔颜色
        g.setFont(font);// 设置画笔字体
        g.shear(0.1, -0.26);// 设定倾斜度

        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        int y = 50;
        for (int i = 0; i < textArray.length; i++) {
            g.drawString(textArray[i], 0, y);
            y = y + font.getSize();
        }
        g.drawString(sstr, 0, y);// 画出字符串

        g.dispose();// 释放画笔
        return image;
    }

    public static boolean DeserializeExtJsSortInfo(String jsonstr, StringBuilder sortStr)
    {
        try
        {
            List<ExtJS_Sort> _list = JsonUtils.toArrayList(jsonstr,ExtJS_Sort.class);

            String tmpstr = "";
            for (int i = 0; i < _list.size(); i++)
            {
                String sortd = _list.get(i).direction;
                String sortf = _list.get(i).property;
                if (tmpstr == "") {
                    tmpstr = sortf + " " + sortd;
                } else {
                    tmpstr += ", " + sortf + " " + sortd;
                }
            }
            if (tmpstr != "")
            {
                sortStr.append(" order by " + tmpstr);
            }
        }
        catch (Exception err)
        {
            return false;
        }
        return true;
    }

    public static void DeleteTempFile(String fname)
    {
        try
        {
            String fullpath = ConfigHelper.getTempPath() + ConfigHelper.getfSepChar()+fname;
            if (FileUtil.FileExists(fullpath)) {
                FileUtil.Delete(fullpath);
            }
        }
        catch (Exception Ex)
        {

        }
    }

    public static void ResponseTempFile(String fname, HttpServletResponse Response)
    {
        String downloadName = "";
        String FullFileName = ConfigHelper.getTempPath() + ConfigHelper.getfSepChar()+ fname;
        File DownloadFile = new File(FullFileName);

        downloadName = fname;
        try
        {
            if (FileUtil.FileExists(FullFileName)) {
                Response.reset();
                Response.setCharacterEncoding("utf-8");
                Response.setContentType("application/octet-stream");
                Response.setHeader("content-type", "application/octet-stream");
                Response.addHeader("Content-Disposition", "attachment;filename=" + UrlUtil.encodeURL(downloadName));
                Response.addHeader("Content-Length", String.valueOf(DownloadFile.length()));

                InputStream in = new FileInputStream(FullFileName);
                int count = 0;
                byte[] by = new byte[1024];
                OutputStream out = Response.getOutputStream();
                while ((count = in.read(by)) != -1) {
                    out.write(by, 0, count);
                }
                in.close();
                out.flush();
                out.close();

                DeleteTempFile(fname);

                Response.flushBuffer();
            }
        }
        catch (Exception Ex)
        {
        }
    }

    public static void ResponseFile(String mark,String fname, HttpServletResponse Response)
    {
        String downloadName = "";
        String FullFileName = ConfigHelper.getUploadPath() +mark+ ConfigHelper.getfSepChar()+ fname;
        File DownloadFile = new File(FullFileName);

        downloadName = fname;
        try
        {
            if (FileUtil.FileExists(FullFileName)) {
                Response.reset();
                Response.setCharacterEncoding("utf-8");
                Response.setContentType("application/octet-stream");
                Response.setHeader("content-type", "application/octet-stream");
                Response.addHeader("Content-Disposition", "attachment;filename=" + UrlUtil.encodeURL(downloadName));
                Response.addHeader("Content-Length", String.valueOf(DownloadFile.length()));

                InputStream in = new FileInputStream(FullFileName);
                int count = 0;
                byte[] by = new byte[1024];
                OutputStream out = Response.getOutputStream();
                while ((count = in.read(by)) != -1) {
                    out.write(by, 0, count);
                }
                in.close();
                out.flush();
                out.close();

                DeleteTempFile(fname);

                Response.flushBuffer();
            }
        }
        catch (Exception Ex)
        {
        }
    }

    public static byte[] File2Bytes(String filename)
    {
        try {
            if (!FileUtil.FileExists(filename)) {
                return new byte[0];
            }

            File file = new File(filename);
            long fileSize = file.length();
            if (fileSize > Integer.MAX_VALUE) {
                System.out.println("file too big...");
                return null;
            }

            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();

            int len;
            byte[] buffer = new byte[1024];
            while ((len = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }

            byte[] data =baos.toByteArray();

            fis.close();
            baos.close();

            return data;
        }catch(Exception Ex)
        {
            return null;
        }
    }

    public static <T> T getElement(List<T> objs, Predicate<T> predicate) {
        return objs.stream()
            .filter(predicate)
            .findAny()
            .orElse(null);
    }

    public static String GetLongString(Double dvalue)
    {
        try
        {
            String lval=String.valueOf(new Double(dvalue).longValue());
            return lval;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static String GetFloatString(Double dvalue)
    {
        try
        {
            String lval=String.valueOf(new Double(dvalue).floatValue());
            return lval;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    public static String getCurrentTokenKey(HttpServletRequest request)
    {
        try
        {
            TokenService tokenService=SpringUtil.getBean("tokenService");
            String uuid=tokenService.getRequestTokenKey(request);
            return uuid;
        }catch (Exception Ex)
        {
            return "";
        }
    }
    public static <T> boolean ExportExcelList(List<T> _list, String titstr, List<ExportColumnMode> cmlist,Boolean isXchz, HttpServletResponse response) {
        String path = ConfigHelper.getTempPath();
        try {
            Workbook wk = new XSSFWorkbook();
            Sheet sheet = wk.createSheet(titstr);

            CellStyle tcs = createCellStyleXSSF(wk, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true, 10, true);
            CellStyle ccs = createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);
            CellStyle redCss=createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);
            redCss.setFillForegroundColor(IndexedColors.RED.index);
            redCss.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //粉红色
            CellStyle rossCss=createCellStyleXSSF(wk, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, false, 10, true);
            rossCss.setFillForegroundColor(IndexedColors.ROSE.index);
            rossCss.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            if (sheet != null) {
                Row tmpRow = null;
                Cell tmpCell = null;
                boolean flag = true;

                //设置标题行
                tmpRow = sheet.createRow(0);
                if (tmpRow == null) {
                    return false;
                }
                tmpRow.setHeightInPoints(30);

                for (int j = 0; j < cmlist.size(); j++) {
                    ExportColumnMode ecm = cmlist.get(j);
                    sheet.setColumnWidth(j, ecm.col_size * 256);
                    tmpCell = tmpRow.createCell(j);
                    tmpCell.setCellStyle(tcs);
                    if (tmpCell != null) {
                        tmpCell.setCellValue(ecm.col_name);
                    }
                }

                if(_list!=null) {
                    for (int i = 0; i < _list.size(); i++) {
                        T obj = _list.get(i);
                        tmpRow = sheet.createRow(i + 1);
                        if (tmpRow == null) {
                            return false;
                        }
                        tmpRow.setHeightInPoints(30);

                        for (int j = 0; j < cmlist.size(); j++) {
                            ExportColumnMode ecm = cmlist.get(j);
                            tmpCell = tmpRow.createCell(j);
                            tmpCell.setCellStyle(ccs);
                            if (isXchz){
                            }
                            if (tmpCell != null) {
                                Field f = obj.getClass().getDeclaredField(ecm.col_code);
                                if (f != null) {
                                    f.setAccessible(true);
                                    Object colobj = f.get(obj);
                                    if (StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                                        String colval = (colobj == null) ? "" : colobj.toString();
                                        tmpCell.setCellValue(colval);
                                    } else {
                                        double colval = (colobj == null) ? 0 : Double.parseDouble(colobj.toString());
                                        tmpCell.setCellValue(colval);
                                    }
                                }
                            }

                        }
                    }

                    //合计行
                    tmpRow = sheet.createRow(_list.size() + 1);
                    if (tmpRow == null) {
                        return false;
                    }
                    tmpRow.setHeightInPoints(30);
                    for (int j = 0; j < cmlist.size(); j++) {
                        ExportColumnMode ecm = cmlist.get(j);
                        tmpCell = tmpRow.createCell(j);
                        tmpCell.setCellStyle(ccs);

                        if (tmpCell != null && !StringUtil.IsNullOrEmpty(ecm.col_sum_cell)) {
                            if (flag) {
                                tmpRow.getCell(0).setCellValue("合计：");
                                flag = false;
                            }
                            if (_list.size() > 0) {
                                tmpCell.setCellFormula("sum(" + ecm.col_sum_cell + "2:" + ecm.col_sum_cell + (_list.size() + 1) + ")");
                            }
                        }
                    }
                }
            }

            if (ConfigHelper.isEPWMask()) {
                SetWaterMark((XSSFWorkbook) sheet.getWorkbook());
            }

            String filename = titstr + MetaHelper.GetSimpTimeString(new Date()) + ".xlsx";
            response.setHeader("Content-disposition", filename);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            //response.setContentType("multipart/form-data;charset=UTF-8");
            wk.write(response.getOutputStream());
            return true;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            return false;
        }
    }
}
