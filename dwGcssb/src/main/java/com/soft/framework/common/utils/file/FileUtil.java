package com.soft.framework.common.utils.file;

import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.MetaHelper;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("unchecked")
public class FileUtil {
    /**
     * 获取文件size
     *
     * @param fname
     * @return
     */
    public static long GetFileSize(File fname) //取得文件大小
    {
        try {
            long s = 0;
            if (fname.exists()) {
                FileInputStream fis = null;
                fis = new FileInputStream(fname);
                s = fis.available();
            }
            return s;
        } catch (Exception Ex) {
            return 0;
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filename
     * @return
     */
    public static String ExtractFileExt(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int i = filename.lastIndexOf('.');

            if ((i > 0) && (i < (filename.length() - 1))) {
                return filename.substring(i);
            }
        }
        return "";
    }

    /**
     * 获取文件名称[不含后缀名]
     *
     * @param
     * @return String
     */
    public static String ExtractFileDir(String fileName) {
        fileName = fileName.replace("/", "\\");
        int splitIndex = fileName.lastIndexOf("\\");
        if (splitIndex != -1) {
            return fileName.substring(0, splitIndex).replaceAll("\\s*", "");
        } else {
            return fileName;
        }
    }

    /**
     * 获取文件名称[不含后缀名]
     *
     * @param
     * @return String
     */
    public static String ExtractFileNameNoExt(String fileName) {
        if ((fileName != null) && (fileName.length() > 0)) {
            int i = fileName.lastIndexOf('.');

            if ((i > 0) && (i < (fileName.length() - 1))) {
                return fileName.substring(0,i);
            }
        }
        return "";
    }

    /**
     * 获取文件名称
     *
     * @param
     * @return String
     */
    public static String ExtractFileName(String fileName) {

        fileName = fileName.replace("/", "\\");
        int splitIndex = fileName.lastIndexOf("\\");
        if (splitIndex != -1) {
            return fileName.substring(splitIndex + 1).replaceAll("\\s*", "");
        } else {
            return fileName;
        }
    }

    /**
     * 文件复制
     * 方法摘要：这里一句话描述方法的用途
     *
     * @param
     * @return void
     */
    public static void CopyFile(String inputFile, String outputFile){
        try
        {
            File sFile = new File(inputFile);
            File tFile = new File(outputFile);
            FileUtils.copyFile(sFile,tFile);
        }catch(Exception Ex)
        {
            return;
        }
    }

    /**
     * 删除指定的文件
     *
     * @param strFileName 指定绝对路径的文件名
     * @return 如果删除成功true否则false
     */
    public static boolean Delete(String strFileName) {
        File fileDelete = new File(strFileName);

        if (!fileDelete.exists() || !fileDelete.isFile()) {

            return false;
        }

        return fileDelete.delete();
    }

    /**
     * 创建新目录
     *
     * @param path
     */
    public static void CreateDir(String path) {
        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * 创建新文件
     *
     * @param path
     */
    public static void CreateFile(String path) {
        String dir = ExtractFileDir(path);
        if (!DirectoryExists(dir)) {
            CreateDir(dir);
        }

        File file = new File(path);
        try {
            file.createNewFile();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public static void CutTo(String source, String target) {
        File sourFile = new File(source);
        File tarFile = new File(target);
        if (sourFile.isFile()) {
            if (tarFile.isDirectory()) {
                sourFile.renameTo(tarFile);
            }
        } else {
            CopyFile(source, target);
            Delete(source);
        }
    }

    public static boolean FileExists(String fname) {
        try {
            File file = new File(fname);
            if (file.exists()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return false;
        }
    }

    public static boolean DirectoryExists(String pname) {
        try {
            File file = new File(pname);
            if (file.exists()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return false;
        }
    }

    public static boolean DeleteDir(File dir) {
        try {
            if (dir.isDirectory()) {
                String[] children = dir.list(); //递归删除目录中的子目录下
                for (int i = 0; i < children.length; i++) {
                    boolean success = DeleteDir(new File(dir, children[i]));
                    if (!success) {
                        return false;
                    }
                }
            }
            // 目录此时为空，可以删除
            return dir.delete();
        }catch(Exception Ex)
        {
            return false;
        }
    }

    public static boolean DeleteDir(String sdir) {
        try {
            File dir=new File(sdir);
            if (dir.isDirectory()) {
                String[] children = dir.list(); //递归删除目录中的子目录下
                for (int i = 0; i < children.length; i++) {
                    boolean success = DeleteDir(new File(dir, children[i]));
                    if (!success) {
                        return false;
                    }
                }
            }
            // 目录此时为空，可以删除
            return dir.delete();
        }catch(Exception Ex)
        {
            return false;
        }
    }

    public static List<String> GetDirectories(String path) {
        ArrayList<String> dirs = new ArrayList<String>();
        File file = new File(path);

        File[] tempList = file.listFiles();
        if(tempList!=null)
        {
            for (int i = 0; i < tempList.length; i++) {
                if (tempList[i].isDirectory()) {
                    String dirName = tempList[i].getName();
                    dirs.add(dirName);
                }
            }
        }

        return dirs;
    }

    /**
     *
     * 获取某个目录下所有直接文件
     */
    public static List<String> GetFiles(String path) {
        ArrayList<String> files = new ArrayList<String>();
        File file = new File(path);

        File[] tempList = file.listFiles();
        if(tempList!=null)
        {
            for (int i = 0; i < tempList.length; i++) {
                if (tempList[i].isFile()) {
                    String fileName = tempList[i].getName();
                    files.add(fileName);
                }
            }
        }
        return files;
    }
}
