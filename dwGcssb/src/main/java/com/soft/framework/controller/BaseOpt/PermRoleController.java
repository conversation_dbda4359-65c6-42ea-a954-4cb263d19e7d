package com.soft.framework.controller.BaseOpt;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/PermRole")
@Api(tags = "基本框架接口->权限角色操作接口")
public class PermRoleController {
    @RequestMapping(value = "/GetPermRoleList_Y", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetPermRoleList_Y", notes = "获取当前对指定权限具有操作权限的角色接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetPermRoleList_Y(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String PermissionNo = request.getParameter("PermissionNo");
        String Module = request.getParameter("Module");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(PermissionNo)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select a.Id,a.RoleName,a.RoleKind from Role a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(Module) && !Module.equals("0")) {
                strsql += " and a.RoleKind='" + Module + "'";
            }
            if (!StringUtil.IsNullOrEmpty(RoleMark)) {
                strsql += " and a.RoleName like '" + RoleMark + "'";
            }
            strsql += " and a.id in(select RoleId from RolePermission where PermissionNo='" + PermissionNo + "')";

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/GetPermRoleList_W", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetPermRoleList_W", notes = "获取当前对指定权限不具有操作权限的角色接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetPermRoleList_W(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);


        String PermissionNo = request.getParameter("PermissionNo");
        String Module = request.getParameter("Module");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(PermissionNo)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        String basesql = "";
        String orderstr = "";
        String msgstr = "";
        int rcount = 0;

        basesql = "select a.Id,a.RoleName,a.RoleKind from Role a where 1=1 ";
        if (!StringUtil.IsNullOrEmpty(Module) && !Module.equals("0")) {
            basesql += " and a.RoleKind='" + Module + "'";
        }
        if (!StringUtil.IsNullOrEmpty(RoleMark)) {
            basesql += " and a.RoleName like '" + RoleMark + "'";
        }
        basesql += " and a.id not in(select RoleId from RolePermission where PermissionNo='" + PermissionNo + "')";
        orderstr += "order by a.RoleName asc";

        try {
            SqlHelper sqlhelper = new SqlHelper();

            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult.toString();
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt, rcount);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/AddPermRole", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddPermRole", notes = "权限添加归属角色接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult AddPermRole(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String PermissionNo = request.getParameter("PermissionNo");

        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(PermissionNo)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);

            Boolean uflag = false;
            uflag = WpServiceHelper.AddRolePermission(iroleid, PermissionNo);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("添加角色权限成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeletePermRole", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeletePermRole", notes = "权限删除归属角色接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeletePermRole(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String PermissionNo = request.getParameter("PermissionNo");

        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(PermissionNo)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteRolePermission(iroleid, PermissionNo);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("去除角色权限成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
