package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.gcc.base.entity.NFT_LOGIN_UC;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/UCCMan")
@Api(tags = "基本框架接口->快速用户切换管理接口")
public class UCCManController {

    private Boolean GatherParams2Obj(Map<String, String> params, NFT_LOGIN_UC entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("RU_USER"))) {
                entity.setRC_USER(params.get("RU_USER"));
            }
            if (!StringUtil.IsNull(params.get("RC_USER"))) {
                entity.setRC_USER(params.get("RC_USER"));
            }
            if (!StringUtil.IsNull(params.get("RU_USERN"))) {
                entity.setRC_USERN(params.get("RU_USERN"));
            }
            if (!StringUtil.IsNull(params.get("RC_USERN"))) {
                entity.setRU_USERN(params.get("RC_USERN"));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddUCC", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddUCC", notes = "新增快速用户切换配置接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddUCC(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            NFT_LOGIN_UC entity = new NFT_LOGIN_UC();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            //提交数据库
            String strsql = DBHelper.GetInsertSQL(entity, "NFT_LOGIN_UC", Arrays.asList("REC_ID"));
            if (StringUtil.IsNullOrEmpty(strsql)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            String tmpstr = sqlhelper.ExecuteInsertWithObtainId(strsql);

            ajaxResult = AjaxResult.success(tmpstr);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyUCC", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyUCC", notes = "修改快速用户切换配置接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyUCC(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            String strsql = "select * from NFT_LOGIN_UC where REC_ID='" + Id + "'";
            NFT_LOGIN_UC entity = sqlhelper.GetObject(NFT_LOGIN_UC.class, strsql);
            if (entity == null) {
                ajaxResult = AjaxResult.error("此记录在数据库中已经被删除，请刷新主界面，重新加载数据！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            //提交数据库
            strsql = DBHelper.GetUpdateSQL(entity, "NFT_LOGIN_UC", Arrays.asList("REC_ID"), Arrays.asList(Id.toString()));
            if (StringUtil.IsNullOrEmpty(strsql)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success(entity.getREC_ID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteUCC", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteUCC", notes = "删除快速用户切换配置接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteUCC(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();

            String Id = request.getParameter("ID");

            String strsql = "delete from NFT_LOGIN_UC where ID='" + Id + "'";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("删除默认启动设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String seachtj = request.getParameter("seachtj");

            strsql.append("select * from NFT_LOGIN_UC where 1=1 ");

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetUCCList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetUCCList", notes = "获取当前快速用户切换配置列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetUCCList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<NFT_LOGIN_UC> list = sqlhelper.GetObjectList(NFT_LOGIN_UC.class, strsql);
            ajaxResult = AjaxResult.extgrid(NFT_LOGIN_UC.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetUCCById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetUCCById", notes = "获取指定具有指定ID的快速用户切换配置接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetUCCById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();
        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            String strsql = "select * from NFT_LOGIN_UC where ID='" + Id + "'";
            NFT_LOGIN_UC obj = sqlhelper.GetObject(NFT_LOGIN_UC.class, strsql);
            ajaxResult = AjaxResult.extform(NFT_LOGIN_UC.class, "获取信息成功！", obj);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前快速用户切换配置信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            DataTable dt = sqlhelper.GetDataTable(basesql.toString() + " " + orderstr.toString());
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("RC_USER", "用户编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("RC_USERN", "用户名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("RU_USER", "切换目标编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("RU_USERN", "切换目标名称", 20));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "模块配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
