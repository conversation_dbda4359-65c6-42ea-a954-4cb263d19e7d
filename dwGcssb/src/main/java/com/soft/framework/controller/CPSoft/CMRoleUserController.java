package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.wpbase.entity.vComp;
import com.yyszc.wpbase.entity.vPerson;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Hashtable;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmRoleUser")
@SuppressWarnings("unchecked")
public class CMRoleUserController {

    @RequestMapping("/GetRoleUserList_Y")
    @ResponseBody
    public AjaxResult GetRoleUserList_Y(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String CompId = request.getParameter("CompId");
        String RoleId = request.getParameter("RoleId");
        String UserMark = request.getParameter("UserMark");
        if (StringUtil.IsNullOrEmpty(RoleId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select * from vComp order by COMP_ID asc";
            List<vComp> _clist = sqlhelper.GetObjectList(vComp.class, strsql);
            Hashtable chash = new Hashtable();
            chash.put(1, "集团");
            for (vComp cobj : _clist) {
                chash.put(cobj.getCOMP_ID(), cobj.getCOMP_NAME());
            }

            strsql = "select * from vPerson where Id in(select PersonId from RolePerson where RoleId=" + RoleId + ")";
            if (!StringUtil.IsNullOrEmpty(CompId) && !CompId.equals("0")) {
                strsql += " and TopGroupId=" + CompId;
            }

            if (!StringUtil.IsNullOrEmpty(UserMark)){
                strsql += " and  (RealName like '%" + UserMark + "%' or LoginName like '%" + UserMark + "%')";
            }
            strsql += " order by GroupId asc";

            List<vPerson> _plist = sqlhelper.GetObjectList(vPerson.class, strsql);
            for (vPerson pe : _plist) {
                if (chash.get(pe.getTopGroupId()) != null) {
                    pe.setTopGroupName((String) chash.get(pe.getTopGroupId()));
                }
            }

            ajaxResult = AjaxResult.extgrid(vPerson.class, _plist.size(), _plist);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/GetRoleUserList_W")
    @ResponseBody
    public AjaxResult GetRoleUserList_W(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = starts.equals("") ? 0 : Integer.parseInt(starts);
        int limit = starts.equals("") ? 20 : Integer.parseInt(limits);

        String CompId = request.getParameter("CompId");
        String RoleId = request.getParameter("RoleId");
        String UserMark = request.getParameter("UserMark");
        if (StringUtil.IsNullOrEmpty(RoleId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        String basesql = "";
        String orderstr = "";
        String msgstr = "";
        int rcount = 0;

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select * from vComp order by COMP_ID asc";
            List<vComp> _clist = sqlhelper.GetObjectList(vComp.class, strsql);
            Hashtable chash = new Hashtable();
            chash.put(1, "集团");
            for (vComp cobj : _clist) {
                chash.put(cobj.getCOMP_ID(), cobj.getCOMP_NAME());
            }

            basesql = "select * from vPerson where id not in(select PersonId from RolePerson where RoleId=" + RoleId + ")";
            if (!StringUtil.IsNullOrEmpty(CompId)&&!CompId.equals("0")) {
                basesql += " and TopGroupId=" + CompId;
            }
            if (!StringUtil.IsNullOrEmpty(UserMark)) {
                basesql += " and  (RealName like '%" + UserMark + "%' or LoginName like '%" + UserMark + "%')";
            }
            orderstr = " order by GroupId asc";

            String rcsql = DBHelper.ToRecordCountSql(basesql);
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql, orderstr, limit, currpage);

            List<vPerson> _plist = sqlhelper.GetObjectList(vPerson.class, strsql);
            for (vPerson pe : _plist) {
                if (chash.get(pe.getTopGroupId()) != null) {
                    pe.setTopGroupName((String) chash.get(pe.getTopGroupId()));
                }
            }

            ajaxResult = AjaxResult.extgrid(vPerson.class, rcount, _plist);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/AddRoleRight")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult AddRoleRight(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId)|| StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "insert into RolePerson(RoleId,PersonId) values(" + RoleId + "," + UserId + ")";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("权限设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/DeleteRoleRight")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult DeleteRoleRight(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId)|| StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "delete from RolePerson where RoleId='" + RoleId + "' and PersonId='" + UserId + "'";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("权限删除成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
