package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.GroupItem;
import com.yyszc.wpbase.entity.vGroupItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/GroupMan")
@Api(tags = "基本框架接口->单位管理接口")
public class GroupManController {

    private Boolean GatherParams2Obj(Map<String, String> params, GroupItem entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("groupname"))) {
                entity.setGroupname(params.get("groupname"));
            }
            if (!StringUtil.IsNull(params.get("UComapanyQC"))) {
                entity.setUComapanyQC(params.get("UComapanyQC"));
            }
            if (!StringUtil.IsNull(params.get("shortpinyin"))) {
                entity.setShortpinyin(params.get("shortpinyin"));
            }
            if (!StringUtil.IsNull(params.get("dydj"))) {
                entity.setDydj(params.get("dydj"));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("parentid"))) {
                entity.setParentid(Integer.parseInt(params.get("parentid")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("type"))) {
                entity.setType(Integer.parseInt(params.get("type")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("XH"))) {
                entity.setXH(Integer.parseInt(params.get("XH")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("IsShow"))) {
                entity.setIsShow(Integer.parseInt(params.get("IsShow")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("Category"))) {
                entity.setCategory(Integer.parseInt(params.get("Category")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddGroup", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddGroup", notes = "新增单位机构接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddGroup(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            GroupItem entity = new GroupItem();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，获取传输参数失败！");
                return ajaxResult;
            }

            Integer gid = -1;
            gid = WpServiceHelper.AddGroupItem(entity);
            if (gid == null || gid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增角色信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(gid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyGroup", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyGroup", notes = "修改单位机构接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyGroup(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String GroupId = params.get("GroupId");
            Integer iid = Integer.parseInt(GroupId);

            GroupItem entity = null;
            entity = WpServiceHelper.GetGroupItemById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取单位信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateGroupItem(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改单位信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getId().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteGroup", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteGroup", notes = "修改单位机构接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteGroup(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String GroupId = request.getParameter("GroupId");
            int igroupid = Integer.parseInt(GroupId);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteGroupItemById(igroupid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除角色信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除单位信息成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String GroupName = request.getParameter("GroupName");
            String ParentId = request.getParameter("ParentId");
            String sGroupId = ConfigHelper.getGroupId();

            if (!sGroupId.equals("0")) {
                strsql.append("select * from vGroupItem where 1=1 and TopGroupId='" + sGroupId + "' ");
            } else {
                strsql.append("select * from vGroupItem where 1=1 ");
            }

            if (!StringUtil.IsNullOrEmpty(ParentId)) {
                strsql.append(" and ParentId=" + ParentId);
            }
            if (!StringUtil.IsNullOrEmpty(GroupName)) {
                strsql.append(" and GroupName like '%" + GroupName + "%'");
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetGroupList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetGroupList", notes = "获取当前单位机构列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGroupList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<vGroupItem> list = null;
            list = WpServiceHelper.GetVGroupItemList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取角色信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(vGroupItem.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    @RequestMapping(value = "/GetParentList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetParentList", notes = "获取上级单位机构列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetParentList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        try {
            String strsql = "select Id,GroupDesc from vGroupItem order by topgroupid,plevel,XH asc";
            List<vGroupItem> list = WpServiceHelper.GetVGroupItemList(strsql);

            ajaxResult = AjaxResult.extgrid(vGroupItem.class, list.size(), list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetGroupById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetGroupById", notes = "获取指定ID的单位机构接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGroupById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String GroupId = request.getParameter("GroupId");
        if (StringUtil.IsNullOrEmpty(GroupId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer igroupid = Integer.parseInt(GroupId);

            vGroupItem entity = null;

            entity = WpServiceHelper.GetVGroupItemById(igroupid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(vGroupItem.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出单位机构列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取单位信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("GroupName", "单位名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("uComapanyQC", "单位全称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("shortpinyin", "单位简称", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("type", "单位类型", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("dydj", "附加标识", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("XH", "序号", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("IsShow", "是否显示", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("Category", "类别", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "单位信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetGroupTreeByParent", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    @ApiOperation(value = "GetGroupTreeByParent", notes = "获取树形单位机构列表接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    public String GetGroupTreeByParent(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;

        String strsql = "";
        String node = params.get("node");
        if (node.equals("root")) {
            node = "0";
        }

        try {
            int togGroupId = SessionHelper.getSessionTopGroupId();
            if (!node.equals("0")) {
                strsql = "select * from vGroupItem where ParentId=" + node;
            } else {
                if (togGroupId != 1) {
                    strsql = "select * from vGroupItem where id=" + togGroupId;
                } else {
                    strsql = "select * from vGroupItem where ParentId=" + node;
                }
            }
            List<vGroupItem> list = WpServiceHelper.GetVGroupItemList(strsql);
            StringBuilder jsonData = new StringBuilder();
            if (list.size() > 0) {
                jsonData.append("[");

                for (int i = 0; i < list.size(); i++) {
                    vGroupItem gp = list.get(i);
                    strsql = "select count(*) from vGroupItem where ParentId='" + gp.getId() + "'";
                    String lnums = WpServiceHelper.GetRecordCount(strsql);
                    String leaf = (lnums.equals("0")) ? "true" : "false";
                    jsonData.append("{");
                    jsonData.append("\"id\":\"");
                    jsonData.append(gp.getId());
                    jsonData.append("\",\"text\":\"");
                    jsonData.append(gp.getGroupname());
                    jsonData.append("\",\"leaf\":");
                    jsonData.append(leaf);
                    if (i <= list.size() - 1) {
                        jsonData.append("},");
                    } else {
                        jsonData.append("}");
                    }
                }
                jsonData.append("]");

                return jsonData.toString();
            } else {
                return "";
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetGroupTreeOneTime", produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    @ApiOperation(value = "GetGroupTreeOneTime", notes = "获取树形单位机构列表接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    public String GetGroupTreeOneTime(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;

        int togGroupId = SessionHelper.getSessionTopGroupId();
        int rgroup = 0;
        if (togGroupId != 1) {
            rgroup = togGroupId;
        }

        try {
            String jsonstr = WpServiceHelper.GetGroupTreeExtJs(rgroup);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
