package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.NFT_ModuleAMLink;
import com.yyszc.wpbase.entity.vFunctionItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/AsMan")
@Api(tags = "基本框架接口->模块自启动功能管理接口")
public class AsManController {
    private Boolean GatherParams2Obj(Map<String, String> params, NFT_ModuleAMLink entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("LK_TMENU"))) {
                entity.setLK_TMENU(params.get("LK_TMENU"));
            }
            if (!StringUtil.IsNull(params.get("LK_TPATH"))) {
                entity.setLK_TPATH(params.get("LK_TPATH"));
            }
            if (!StringUtil.IsNull(params.get("LK_TNODE"))) {
                entity.setLK_TNODE(params.get("LK_TNODE"));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("LK_MID"))) {
                entity.setLK_MID(Integer.parseInt(params.get("LK_MID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("LK_SNO"))) {
                entity.setLK_MID(Integer.parseInt(params.get("LK_SNO")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddAS", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddAS", notes = "新增模块启动项接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddAS(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            NFT_ModuleAMLink entity = new NFT_ModuleAMLink();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }

            Integer rid = -1;
            rid = WpServiceHelper.AddNFT_ModuleAMLink(entity);
            if (rid == null || rid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增流程信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(rid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyAS", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyAS", notes = "修改模块启动项接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyAS(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            Integer iid = Integer.parseInt(Id);

            NFT_ModuleAMLink entity = null;

            entity = WpServiceHelper.GetNFT_ModuleAMLinkById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取模块自启动定义信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateNFT_ModuleAMLink(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改模块自启动定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getLK_ID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteAS", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteAS", notes = "删除模块启动项接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteAS(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String Id = request.getParameter("ID");
            int iid = Integer.parseInt(Id);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteNFT_ModuleAMLinkById(iid);
            if (uflag == null || uflag == false) {
                ajaxResult = AjaxResult.error("操作失败，删除模块自启动定义信息失败！");
                return ajaxResult;
            }
            ajaxResult = AjaxResult.success("删除默认启动设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String seachtj = request.getParameter("seachtj");

            strsql.append("select * from NFV_ModuleAMLink where 1=1 ");

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetASList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetASList", notes = "获取当前模块启动项列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetASList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<NFT_ModuleAMLink> list = null;
            list = WpServiceHelper.GetNFT_ModuleAMLinkList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取模块自启动定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(NFT_ModuleAMLink.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetASById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetASById", notes = "获取指定具有指定ID的模块启动项接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetASById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer iid = Integer.parseInt(Id);

            NFT_ModuleAMLink entity = null;
            entity = WpServiceHelper.GetNFT_ModuleAMLinkById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取模块自启动定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(NFT_ModuleAMLink.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前模块自启动项信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;
            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取模块自启动定义信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("LK_ID", "导航项ID", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("LK_TMENU", "菜单目录", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("LK_TPATH", "菜单子目录", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("LK_TNODE", "菜单项", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("LK_SNO", "启动序号", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "模块自动启动配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetStartPList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetStartPList", notes = "导出当前模块自启动项信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetStartPList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String mid = request.getParameter("mid");
        Integer imid = Integer.parseInt(mid);

        String jsonstr = "";
        String strsql = "";
        String strPath = "../../Page/";
        try {
            List<NFT_ModuleAMLink> list = WpServiceHelper.GetNFT_ModuleAMLinkListByMId(imid);
            if (list == null) {
                ajaxResult = AjaxResult.error("获取数据失败！", list);
                return ajaxResult;
            }
            List<vFunctionItem> flist = new ArrayList<vFunctionItem>();
            for (NFT_ModuleAMLink obj : list) {
                String mRoot = obj.getLK_TMENU();
                String mPath = obj.getLK_TPATH();
                String mMenu = obj.getLK_TNODE();
                if (StringUtil.IsNullOrEmpty(mRoot) || StringUtil.IsNullOrEmpty(mMenu)) {
                    continue;
                }
                ;
                if (StringUtil.IsNullOrEmpty(mPath)) {
                    strsql = "select * from vFunctionItem where Title='" + mMenu + "' and ParentName='" + mRoot + "'";
                } else {
                    strsql = "select id from vFunctionItem where Title='" + mPath + "' and ParentName='" + mRoot + "'";
                    String parentid = WpServiceHelper.ExecuteScalar(strsql);
                    if (parentid == null || parentid == "") {
                        continue;
                    }
                    strsql = "select * from vFunctionItem where Title='" + mMenu + "' and ParentId='" + parentid + "'";
                }

                if (StringUtil.IsNullOrEmpty(strsql)) {
                    strsql = AesHelper.aesEncodeCBC(strsql);
                }
                vFunctionItem entity = WpServiceHelper.GetVFunctionItemBySql(strsql);
                if (entity == null) {
                    continue;
                }
                entity.setUrl(strPath + entity.getUrl());
                flist.add(entity);
            }

            ajaxResult = AjaxResult.success("获取数据成功！", list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
