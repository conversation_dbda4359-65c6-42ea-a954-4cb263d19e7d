package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SessionHelper;
import com.soft.framework.helper.ToolHelper;
import com.yyszc.wpbase.ventity.PersonEntity;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmProView")
public class CMProViewController {

    @Data
    class CPSOFT_CHILD_DIR
    {
        @JSONField(name="id")
        public int id;
        
        @JSONField(name="name")
        public String name;

        @JSONField(name="realp")
        public String realp;

        @JSONField(name="dirf")
        public String dirf;      //目录标志

        @JSONField(name="leaf")
        public String leaf;
    }

    @RequestMapping(value="/GetWebTreeByPath",produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String GetWebTreeByPath(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String node = request.getParameter("node");
        if (StringUtil.IsNullOrEmpty(node)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return JSON.toJSON(ajaxResult).toString();
        }

        PersonEntity person= SessionHelper.getSessionPerson();
        if(!person.getLoginName().equals("qidf"))
        {
            ajaxResult = AjaxResult.error("非超级管理用户禁止使用该管理功能!");
            return JSON.toJSON(ajaxResult).toString();
        }

        String fullPath = "";
        String realPath = "";
        String rootPath = ConfigHelper.getProfile();
        String upload= ConfigHelper.getUploadPath();

        List<CPSOFT_CHILD_DIR> list = new ArrayList<CPSOFT_CHILD_DIR>();
        if (node.equals("root"))
        {
            fullPath = rootPath;
        }
        else
        {
            realPath = node;
            fullPath = rootPath +"/"+ realPath;
        }
        if (fullPath.lastIndexOf("/")!=fullPath.length())
        {
            fullPath = fullPath + "/";
        }

        try {
            List<String> dirs=FileUtil.GetDirectories(fullPath);
            for (int i = 0; i < dirs.size(); i++)
            {
                String dname = dirs.get(i).replace("\\","/").replace(fullPath, "").toString();
                CPSOFT_CHILD_DIR tmpd = new CPSOFT_CHILD_DIR();
                tmpd.id = i;
                tmpd.name = dname;
                if (StringUtil.IsNullOrEmpty(realPath))
                {
                    tmpd.realp = tmpd.name;
                }
                else
                {
                    tmpd.realp = realPath+"/"+tmpd.name;
                }
                tmpd.dirf = "1";
                tmpd.leaf = "false";
                list.add(tmpd);
            }

            List<String> files = FileUtil.GetFiles(fullPath);
            for (int i = 0; i < files.size(); i++)
            {
                String fname = files.get(i).replace("\\","/").replace(fullPath,"").toString();
                CPSOFT_CHILD_DIR tmpd = new CPSOFT_CHILD_DIR();
                tmpd.id = i;
                tmpd.name =fname;
                if (StringUtil.IsNullOrEmpty(realPath))
                {
                    tmpd.realp = tmpd.name;
                }
                else
                {
                    tmpd.realp = realPath+"/"+tmpd.name;
                }
                tmpd.dirf = "0";
                tmpd.leaf = "true";
                list.add(tmpd);
            }

            StringBuilder jsonData = new StringBuilder();
            if (list.size()> 0) {
                jsonData.append("[");

                for (int i = 0; i < list.size(); i++) {
                    String leaf = "";
                    CPSOFT_CHILD_DIR ccdDir = list.get(i);
                    if (ccdDir.dirf.equals("1")) {
                        String fpath = rootPath +"/"+ ccdDir.realp;
                        List<String> fd = FileUtil.GetDirectories(fpath);
                        List<String> ff = FileUtil.GetFiles(fpath);
                        int lnums = fd.size() + ff.size();
                        leaf = (lnums==0) ? "true" : "false";
                    } else {
                        leaf = "true";
                    }
                    jsonData.append("{");
                    jsonData.append("id:'");
                    jsonData.append(ccdDir.realp);
                    jsonData.append("',realp:'");
                    jsonData.append(ccdDir.realp);
                    jsonData.append("',dirf:'");
                    jsonData.append(ccdDir.dirf);
                    jsonData.append("',text:'");
                    jsonData.append(ccdDir.name);
                    //jsonData.append("'");
                    jsonData.append("',leaf:");
                    jsonData.append(leaf);
                    if (i != list.size() - 1) {
                        jsonData.append("},");
                    } else {
                        jsonData.append("}");
                    }
                }
                jsonData.append("]");

                return jsonData.toString();
            }
            else
            {
                ajaxResult = AjaxResult.error("未找到资源!");
                return JSON.toJSON(ajaxResult).toString();
            }

        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value="/LoadFileFromWeb",produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    public String LoadFileFromWeb(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String jsonstr="";

        String furl = request.getParameter("furl");
        if (StringUtil.IsNullOrEmpty(furl)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return JSON.toJSON(ajaxResult).toString();
        }

        PersonEntity person= SessionHelper.getSessionPerson();
        if(!person.getLoginName().equals("qidf"))
        {
            ajaxResult = AjaxResult.error("非超级管理用户禁止使用该管理功能!");
            return JSON.toJSON(ajaxResult).toString();
        }

        String fname = ConfigHelper.getProfile()+"/"+furl;
        try {
            File fi = new File(fname);
            if (fi.exists())
            {
                List<String> msglist =new ArrayList<String>();
                String fext=FileUtil.ExtractFileExt(fname);
                if (fext.contains(".js") || fext.contains(".css") || fext.contains(".html")||fext.contains(".xml")||fext.contains(".yml")||fext.contains(".htm")||fext.contains(".properties") || fext.contains(".log") )
                {
                    ToolHelper.ReadFileToStr(fname,msglist);
                    StringBuilder sb=new StringBuilder();
                    for(String ls:msglist)
                    {
                        sb.append(ls + "\n");
                    }
                    return sb.toString();
                }
                else
                {
                    ajaxResult = AjaxResult.error("非文本文件，禁止读取操作!");
                    return JSON.toJSON(ajaxResult).toString();
                }
            }
            else
            {
                ajaxResult = AjaxResult.error("文件不存在!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
