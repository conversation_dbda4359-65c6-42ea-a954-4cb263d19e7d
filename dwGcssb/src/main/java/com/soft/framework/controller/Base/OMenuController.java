package com.soft.framework.controller.Base;

import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataTable;
import com.soft.framework.helper.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value="/Service/Base/OMenu" )
@Api(tags ="基本框架接口->菜单基本接口")
public class OMenuController {
    private String strImgPath = "../../Image/Menus/";
    private String strPath="../../Page/";

    @RequestMapping(value="/RefreshMenu",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="RefreshMenu",notes="获取子菜单接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String RefreshMenu(HttpServletRequest request){
        String mid= request.getParameter("mid");
        String gid= request.getParameter("gid");

        SqlHelper sqlhelper = new SqlHelper();
        int userID= SessionHelper.getSessionUserID();
        int pwf= SessionHelper.getSessionUserPwf();

        if (gid.equals(""))
        {
            gid =new DBHelper().GetUserNavMenuGroup(mid,userID,pwf);
            if(gid.equals(""))
            {
                 return "";
            }
        }

        String menustr = "";
        String strSqlParent = "", strSqlChild = "", strTempChild = "",UrlStr="";
        String strImg = "Page.gif", ResName = "", ResId = "",cResId="",cResName = "";
        try
        {
            if (userID!= -1)
            {
                DataTable dtData1 = DBHelper.GetChildFuntionItemByParent(userID,pwf,gid);
                for(int i=0;i<dtData1.getTotalCount();i++)
                {
                    DataRow drData1=dtData1.getRow(i);
                    ResId = drData1.getColValue(0).toString();
                    ResName = drData1.getColValue(1).toString();
                    if (!drData1.getColValue(3).toString().equals(""))
                    {
                        strImg = drData1.getColValue(3).toString();
                    }
                    else
                    {
                        strImg = "b" +ResId+ ".png";
                    }

                    UrlStr = drData1.getColValue(2).toString();
                    if (!UrlStr.equals(""))
                    {
                        if (UrlStr.indexOf("javascript::") == -1)
                        {
                            UrlStr = "javascript:NavFunction('" + strPath + UrlStr + "','"+ResId+"','"+ResName+"');";
                        }
                        else
                        {
                            UrlStr = UrlStr.replace("javascript::", "javascript:");
                        }
                    }

                    DataTable dtData2 = DBHelper.GetChildFuntionItemByParent(userID,pwf,ResId);
                    if (dtData2.getTotalCount()> 0)
                    {
                        menustr += "<li><a href='javascript:void(0);' cid='" + ResId + "'><center><table class='def-menudrager-innertable'><tr><td align='center'><img src=\"" + strImgPath + strImg + "\" border=0></td><td align='right' style='width:1.5em;'><img src=\"" + strImgPath + "arrow_down.gif\"  twomark='true' border=0/></td></tr><tr><td align='center'>" + ResName + "</td></tr></table></center></a></li>";
                        menustr += "<ul cid='" + ResId + "' style='border:1px solid #ccccff;'>";
                        for(int j=0;j<dtData2.getTotalCount();j++)
                        {
                            DataRow drData2=dtData2.getRow(j);
                            cResId = drData2.getColValue(0).toString();
                            cResName = drData2.getColValue(1).toString();
                            if (!drData2.getColValue(3).toString().equals(""))
                            {
                                strImg = drData2.getColValue(3).toString();
                            }
                            else
                            {
                                strImg = "b" + cResId + ".png";
                            }

                            UrlStr = drData2.getColValue(2).toString();
                            if (!UrlStr.equals(""))
                            {
                                if (UrlStr.indexOf("javascript::") == -1)
                                {
                                    UrlStr = "javascript:NavFunction('" + strPath + UrlStr + "','" + cResId + "','" +cResName + "');";
                                }
                                else
                                {
                                    UrlStr = UrlStr.replace("javascript::", "javascript:");
                                }
                            }

                            DataTable dtData3 = DBHelper.GetChildFuntionItemByParent(userID,pwf,cResId);
                            if (dtData3.getTotalCount()> 0)
                            {
                                String tvstr = LoadThreeChild(dtData3,UrlStr, ResName, strImg, ResId);
                                menustr += tvstr;
                            }
                            else
                            {
                                menustr += "<li><a href='javascript:void(0);' onclick=\"" + UrlStr + "\"><center><table class='def-menudrager-innertable'><tr><td align='center'><img src=\"" + strImgPath + strImg + "\" border=0></td></tr><tr><td align='center'>" + ResName + "</td></tr></table></center></a></li>";
                            }
                        } //end foreach (DataRow drData2 in dtData2.Rows)
                        menustr += "</ul>";
                    }
                    else
                    {
                        menustr += "<li><a href='javascript:void(0);' onclick=\"" + UrlStr + "\"><center><table class='def-menudrager-innertable'><tr><td align='center'><img src=\"" + strImgPath + strImg + "\" border=0></td></tr><tr><td align='center'>" + ResName + "</td></tr></table></center></a></li>";
                    }
                    menustr += "</li>";
                } //end foreach (DataRow drData1 in dtData1.Rows)
            }

            return menustr;
        }
        catch (Exception Ex)
        {
            LogHelper.WriteSysLog("系统产生异常：" + Ex.getMessage() + "!");
            return "";
        }
    }

    private String LoadThreeChild(DataTable dtData,String parurl,String parname,String parimg,String parid)
    {
        String itemstr="";
        try
        {
            String UrlStr = "", strImg = "", ResId = "", ResName = "";
            itemstr += "<li><a onclick=\"" + parurl + "\" cid='" + parid + "'><table class='def-menudrager-innertable'><tr><td align='center'><img src=\"" + strImgPath + parimg + "\" border=0></td><td align='right' style='width:1.5em;'><img src=\"" + strImgPath + "arrow_down.gif\"  thirdmark='true' border=0/></td></tr><tr><td align='center'>" + parname + "</td></tr></table></a>";
            itemstr += "<ul cid='" + parid + "' style='border:1px solid #ccccff;'>";
            for(int k=0;k<dtData.getTotalCount();k++)
            {
                DataRow drData=dtData.getRow(k);
                ResId = drData.getColValue(0).toString();
                ResName = drData.getColValue(1).toString();
                if (!drData.getColValue(3).toString().equals(""))
                {
                    strImg = drData.getColValue(3).toString();
                }
                else
                {
                    strImg = "b" + ResId + ".png";
                }

                UrlStr = drData.getColValue(2).toString();
                if (!UrlStr.equals(""))
                {
                    if (UrlStr.indexOf("javascript::") == -1)
                    {
                        UrlStr = "javascript:NavFunction('" + strPath + UrlStr + "','" + ResId + "','" + ResName + "');";
                    }
                    else
                    {
                        UrlStr = UrlStr.replace("javascript::", "javascript:");
                    }
                }
                itemstr += "<li><a onclick=\"" + UrlStr + "\"><img src=\"" + strImgPath + strImg + "\" border=0>&nbsp;" + ResName + "</a></li>";
            } //end foreach (DataRow drData3 in dtData3.Rows)
            itemstr += "</ul></li>";
            return itemstr;
        }catch(Exception Ex)
        {
            return "";
        }
    }

    @RequestMapping(value="/GetMenuGroup",produces = {"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetMenuGroup",notes="获取顶部菜单分组接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetMenuGroup(HttpServletRequest request){
        String mid= ConfigHelper.getModuleId();

        SqlHelper sqlhelper = new SqlHelper();
        int userID= SessionHelper.getSessionUserID();
        int pwf= SessionHelper.getSessionUserPwf();

        String tmpstr = "[";
        String groupName = "", groupId = "";
        try
        {
            DataTable dtable = DBHelper.GetUserNavTable(mid,userID,pwf);
            int gcount=dtable.getTotalCount();
            for(int iter=0;iter<gcount;iter++) {
                DataRow drData1 = dtable.getRow(iter);
                groupId = drData1.getColValue(0).toString();
                groupName = drData1.getColValue(1).toString();

                StringBuilder sb=new StringBuilder();
                sb.append("{");
                sb.append("\"id\":\"");
                sb.append(groupId);
                sb.append("\",\"text\":\"");
                sb.append(groupName);
                sb.append("\",\"DisplayName\":\"");
                sb.append(groupName);
                sb.append("\"}");

                if(iter<gcount-1)
                {
                    tmpstr+=sb.toString()+",";
                }else
                {
                    tmpstr+=sb.toString();
                }
            }
            tmpstr+="]";

            return tmpstr;
        }
        catch (Exception Ex)
        {
            return "";
        }
    }
}
