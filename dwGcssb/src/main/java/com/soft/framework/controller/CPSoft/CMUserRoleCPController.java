package com.soft.framework.controller.CPSoft;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.wpbase.entity.Role;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmUserRoleCp")
public class CMUserRoleCPController {

    @RequestMapping("/GetUserRoleList")
    @ResponseBody
    public AjaxResult GetUserRoleList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String UserId = request.getParameter("UserId");
        String RoleMark = request.getParameter("RoleMark");
        if (StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select a.* from Role a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(RoleMark)&&!RoleMark.equals("0")){
                strsql += " and a.RoleKind='" + RoleMark + "'";
            }
            strsql += " and a.id in(select RoleId from RolePerson where personId='"+UserId+"')";

            List<Role> _plist = sqlhelper.GetObjectList(Role.class, strsql);
            ajaxResult = AjaxResult.extgrid(Role.class, _plist.size(), _plist);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/CopyUserRight")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult CopyUserRight(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String PersonId0 = request.getParameter("PersonId0");
        String PersonId1 = request.getParameter("PersonId1");
        String ModuleId = request.getParameter("ModuleId");

        if (StringUtil.IsNullOrEmpty(PersonId0)|| StringUtil.IsNullOrEmpty(PersonId1)){
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "insert into RolePerson(RoleId,personId) select RoleId,"+PersonId1+" from RolePerson a where personId="+PersonId0;
            if(!ModuleId.equals("0")&&!ModuleId.equals(""))
            {
                strsql += " and RoleId in(select Id from Role where RoleKind='"+ModuleId+"')";
            }
            strsql += " and not exists(select * from RolePerson where personId="+PersonId1+" and RoleId=a.RoleId)";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("特定用户权限复刻至指定用户成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
