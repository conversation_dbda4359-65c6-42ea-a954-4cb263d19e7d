package com.soft.framework.config;

import com.soft.framework.filter.TokenFilter;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.security.handle.AuthenticationEntryPointImpl;
import com.soft.framework.security.handle.LogoutSuccessHandlerImpl;
import com.soft.framework.security.handle.MyAuthenticationProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * spring security配置
 *
 * <AUTHOR>
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
@Configuration
public class SecurityConfig extends WebSecurityConfigurerAdapter
{
    @Autowired(required =false)
    private MyAuthenticationProvider myAuthenticationProvider;
    /**
     * 认证失败处理类
     */
    @Autowired(required =false)
    private AuthenticationEntryPointImpl unauthorizedHandler;

    /**
     * 退出处理类
     */
    @Autowired(required =false)
    private LogoutSuccessHandlerImpl logoutSuccessHandler;

    /**
     * 解决 无法直接注入 AuthenticationManager
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception
    {
        return super.authenticationManagerBean();
    }

    /**
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception
    {
        httpSecurity
                .csrf().disable()
                .exceptionHandling().authenticationEntryPoint(unauthorizedHandler)
                .and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests()
                .antMatchers(ConfigHelper.getWhiteList().split(",")).permitAll()
                .antMatchers(loadAnonymous()).anonymous()
                .anyRequest().authenticated()
                .and()
                .headers().frameOptions().disable();
        httpSecurity.logout().logoutUrl("/Logout").logoutSuccessHandler(logoutSuccessHandler);
        // 添加JWT filter
        httpSecurity.addFilterBefore(new TokenFilter(),UsernamePasswordAuthenticationFilter.class);
    }

    /**
     * 身份认证接口
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception
    {
        auth.authenticationProvider(myAuthenticationProvider);
    }

    @Override
    public void configure(WebSecurity web) throws Exception {
        //解决静态资源被拦截的问题
        web.ignoring().antMatchers(HttpMethod.GET,loadExclude());
    }

    private String[] loadExclude() {
        return new String[]{
            "/**/index","/**/Index",
            "/**/mIndex","/**/MIndex",
            "/**/test","/**/Test",
            "/**/*.html","/**/*.HTML",
            "/**/*.css","/**/*.CSS",
            "/**/*.js","/**/*.JS",
            "/**/*.gif", "/**/*.GIF",
            "/**/*.jpg","/**/*.JPG",
            "/**/*.jpeg","/**/*.JPEG",
            "/**/*.png","/**/*.PNG"
        };
    }

    private String[] loadAnonymous() {
        return new String[]{
                "/dev-api/**",
                "/doc.html",
                "/v2/*",
                "/swagger-resources",
                "/swagger-resources/**",
        };
    }
}
