<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglClockMapper">


    <select id="getRoleByPersonId" resultType="java.lang.Integer">
        SELECT
            count(rl.id)
        FROM
            RolePerson rp
                LEFT JOIN Role rl ON rp.RoleId = rl.Id
        WHERE
            rp.PersonId=#{personId} and rl.RoleName=#{roleName}
    </select>

    <select id="selectAllDep" resultType="com.soft.gcc.common.groupitem.entity.Groupitem">
        with cte as
                 (
                     select id,parentid,groupname, 0 as lvl from GroupItem
                     where id = #{depId}
                     union all
                     select d.id,d.parentid,d.groupname,lvl + 1 from cte c inner join GroupItem d
                                                                                      on c.id = d.parentid
                 )
        select * from cte
    </select>

    <select id="getListMytj" resultType="com.soft.gcc.xtbg.kqgl.dto.MykqDto">
        select res.*,(res.sjts- res.dkts) kgts from (
        select p.realName,p.groupId,g.groupname ,r.*,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth}  and clock_time &lt;= #{endMonth}  and apply_user = p.Id ) dkts,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth} and clock_time &lt;= #{endMonth}  and apply_user =  p.Id
        and ( (type  not like '%0.9%' and type !=',' ) or  ( (clock_start_phone_status =0 or clock_end_phone_status = 0) and  type  not like '%0.9%') )) ycts,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth} and clock_time &lt;= #{endMonth}  and apply_user = p.Id  and type like '%2%' and type NOT LIKE '%0.9%') cdts,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth} and clock_time &lt;= #{endMonth}  and apply_user = p.Id  and type like '%3%' and type NOT LIKE '%0.9%') ztts,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth} and clock_time &lt;=  #{endMonth}  and apply_user = p.Id  and type like '%4%' and type NOT LIKE '%0.9%') yddkts,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth} and clock_time &lt;=  #{endMonth}  and apply_user = p.Id  and type like '%5%' and type NOT LIKE '%0.9%') dwycts,
        (select count(*) from dfdw_t_kqgl_clock where clock_time  &gt;= #{startMonth} and clock_time &lt;=  #{endMonth}  and apply_user = p.Id
        and (clock_start_phone_status =0 or clock_end_phone_status = 0) and type NOT LIKE '%0.9%' ) sjhyc,
        ROW_NUMBER() OVER(Order by p.Id ) AS RowId
        from (
        SELECT userId ,count(userId) sjts
        FROM dfdw_t_kqgl_roledate  where role_date &gt;= #{startMonth} and role_date &lt;=  #{endMonth}
        group by userId
        )r
        left join person p on r.userId = p.Id
        left join GroupItem g on p.groupId = g.Id
        where p.realName is not null
        <if test="depList!= null">
            and p.groupId in
            <foreach item="item" index="index" collection="depList" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="userName !=null and userName !=''">
            and p.realName like '%${userName}%'
        </if>
        ) res
        where 1=1

    </select>

    <select id="getListMrTj" resultType="com.soft.gcc.xtbg.kqgl.dto.MrdkDto">
        select res.* from (
                              SELECT
                                  rd.userId AS applyUser,
                                  p.RealName AS applyUserName,
                                  rd.role_date AS clockTime,
                                  IIF(clock.clock_start_time is null, ',1,', clock.type) as type,
                                  (case when  clock.clock_start_time is not null then  clock.clock_start_time else '空' END) as clockStartTime,
                                  (case when  clock.clock_end_time is not null then  clock.clock_end_time else '空' END) as clockEndTime,
                                  clock_start_phone_status as clockStartPhoneStatus,
                                  clock_end_phone_status as clockEndPhoneStatus,
                                  clock_start_address as clockStartAddress,
                                  clock_end_address as clockEndAddress,
                                  p.GroupID AS applyUserDept,
                                  g.groupname as applyUserDeptName,
                                  kqcc.user_id as bmrkUserId,
                                  reP.RealName as bmrkUserName,
                                  kqcc.note as bmrkNote,
                                  clock.clock_start_place as clockStartPlace,
                                  clock.clock_start_place_note as clockStartPlaceNote,
                                  clock.clock_end_place as clockEndPlace,
                                  clock.clock_end_place_note as clockEndPlaceNote,
                                  ROW_NUMBER() OVER(Order by  rd.userId ) AS RowId
                              FROM dfdw_t_kqgl_roledate rd
                                       LEFT JOIN dfdw_t_kqgl_clock clock ON clock.apply_user = rd.userId AND clock.clock_time = rd.role_date
                                       LEFT JOIN dfdw_t_kqgl_clock_check kqcc ON rd.userId = kqcc.user_id AND rd.role_date = kqcc.clock_time
                                       left join Person p on p.Id = rd.userId
                                       left join GroupItem g on  p.groupId = g.Id
                                       left join Person reP on reP.Id = kqcc.user_id
                              where  p.RealName is not null and rd.role_date &gt;= #{startDate} and rd.role_date &lt;= #{endDate}

                             <if test="userName !=null and userName !=''">
                                 and p.RealName like '%${userName}%'
                             </if>
                             <if test="applyUser !=0">
                                 and rd.userId = #{applyUser}
                             </if>
          )	res
            where 1=1
            <if test="type != -1  and type != 7 ">
                and res.type like '%${type}%' and res.type not like '%0.9%'
            </if>
            <if test="type == 7 ">
                and (res.clockStartPhoneStatus = 0 OR res.clockEndPhoneStatus = 0) and res.type not like '%0.9%'
            </if>
            order by res.clockTime asc
    </select>
    <select id="getMonthListById" resultType="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock">
        select
            IIF(res.clock_end_time is null and getdate() > concat ( cast(res.clock_time as varchar), ' ',
                                                                    (select RIGHT((case when r.x1 is not null then r.x1 when r.x2 is not null then r.x2 else r.Parameter end ),8)
                                                                     from (
                                                                              select
                                                                                  Parameter,
                                                                                  (select top 1 clock_end_time from dfdw_t_kqgl_position where user_id = res.applyUser) x1,
                                                                                  (select top 1 clock_end_time from dfdw_t_kqgl_position where group_id = res.apply_user_dept) x2
                                                                              from DictionaryValue where TitleId = '970226' and Content = N'下午'
                                                                          ) r
                                                                    )
                ), concat(res.type, '6,'), res.type) as type,
            res.applyUser,
            res.apply_user_name,
            res.clock_time,
            res.clock_start_time,
            res.clock_end_time,
            res.startClock,
            res.endClock,
            res.clock_start_place,
            res.clock_end_place,
            res.apply_user_dept
        from (
                 SELECT
                     rd.userId AS applyUser,
                     p.RealName AS apply_user_name,
                     rd.role_date AS clock_time,
                     IIF(clock.clock_start_time is null, ',1,', clock.type) as type,
                     clock.clock_start_time ,
                     clock.clock_end_time,
                     ( CASE WHEN clock.clock_start_time IS NOT NULL THEN ( cast(rd.role_date as varchar) + ' ' + clock.clock_start_time ) ELSE N'无' END ) AS startClock,
                     ( CASE WHEN clock.clock_end_time IS NOT NULL THEN ( cast(rd.role_date as varchar) + ' ' + clock.clock_end_time ) ELSE N'无' END ) AS endClock,
                     clock.clock_start_place,
                     clock.clock_end_place,
                     p.GroupID AS apply_user_dept
                 FROM dfdw_t_kqgl_roledate rd
                          LEFT JOIN dfdw_t_kqgl_clock clock ON clock.apply_user = rd.userId AND clock.clock_time = rd.role_date
                          LEFT JOIN dfdw_t_kqgl_clock_check kqcc ON rd.userId = kqcc.user_id AND rd.role_date = kqcc.clock_time
                          left join Person p on p.Id = rd.userId
             ) res
        where res.applyUser = #{applyUser}
          and res.clock_time >= #{timeStart}
          and res.clock_time &lt;= #{timeEnd}
    </select>


    <select id="dwppList" resultType="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock">
        select c.* ,c.clock_time as clockTimeStr,g.groupname from dfdw_t_kqgl_clock c
       left join GroupItem g on c.apply_user_dept = g.id
        where c.clock_time = #{date}
        <if test="depList!= null">
            and c.apply_user_dept  in
            <foreach item="item" index="index" collection="depList" open="(" separator="," close=")">#{item}</foreach>
        </if>
        <if test="userName !=null and userName !=''">
            and c.apply_user_name like '%${userName}%'
        </if>
    </select>
    <insert id="sendSms">
        INSERT INTO T_SMSSEND (Source,Phone,DateTime,Sequence,Result,Info)
        SELECT
            'AQGL',
            #{phone},
            getdate(),
            isnull(MAX ( sequence ), 0) + 1,
            0,
            #{content}
        FROM
            [T_SMSSend]
        WHERE
            Source = 'AQGL'
          AND Phone = #{phone}
          AND DateTime = getdate()
    </insert>
</mapper>
