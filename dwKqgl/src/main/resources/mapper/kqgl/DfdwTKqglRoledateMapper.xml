<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglRoledateMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglRoledate">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="userId" jdbcType="INTEGER"/>
            <result property="role_date" column="role_date" jdbcType="DATE"/>
            <result property="group_id" column="group_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,userId,role_date,
        group_id
    </sql>
</mapper>
