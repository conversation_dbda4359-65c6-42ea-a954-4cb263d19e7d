<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwKqglDictionaryvalueMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.kqgl.domain.DfdwKqglDictionaryvalue">
            <result property="ID" column="ID" jdbcType="INTEGER"/>
            <result property="titleID" column="TitleID" jdbcType="INTEGER"/>
            <result property="content" column="Content" jdbcType="VARCHAR"/>
            <result property="parameter" column="Parameter" jdbcType="VARCHAR"/>
            <result property="isUsed" column="IsUsed" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,TitleID,Content,
        Parameter,IsUsed
    </sql>
</mapper>
