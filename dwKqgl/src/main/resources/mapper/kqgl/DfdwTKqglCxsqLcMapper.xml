<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglCxsqLcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglCxsqLc">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="applyNo" column="applyNo" jdbcType="VARCHAR"/>
            <result property="applyUser" column="applyUser" jdbcType="INTEGER"/>
            <result property="applyUserName" column="applyUserName" jdbcType="VARCHAR"/>
            <result property="applyUserDeptId" column="applyUserDeptId" jdbcType="INTEGER"/>
            <result property="planDate" column="planDate" jdbcType="DATE"/>
            <result property="openTime" column="openTime" jdbcType="VARCHAR"/>
            <result property="endTime" column="endTime" jdbcType="VARCHAR"/>
            <result property="travelHour" column="travelHour" jdbcType="FLOAT"/>
            <result property="note" column="note" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="approveState" column="approveState" jdbcType="INTEGER"/>
            <result property="approveUserId" column="approveUserId" jdbcType="INTEGER"/>
            <result property="approveName" column="approveName" jdbcType="VARCHAR"/>
            <result property="applyUserDeptName" column="applyUserDeptName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,applyNo,applyUser,
        applyUserName,applyUserDeptId,planDate,
        openTime,endTime,travelHour,
        note,createTime,approveState,
        approveUserId,approveName,applyUserDeptName
    </sql>
</mapper>
