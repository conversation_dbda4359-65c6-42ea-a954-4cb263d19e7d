<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglUserPointSizeMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglUserPointSize">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="user_id" column="user_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id
    </sql>
</mapper>
