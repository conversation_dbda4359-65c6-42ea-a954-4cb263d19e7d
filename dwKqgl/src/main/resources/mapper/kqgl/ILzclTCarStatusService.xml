<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglPositionMapper">


    <sql id="Base_Column_List">
        id,group_id,longitude,
        latitude,status,create_by,
        creation_date,last_update_by,last_update_date,
        company_address,top_group_Id,user_id,
        clock_start_time,clock_end_time
    </sql>


    <select id="selectPositionById" resultType="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition">
        SELECT GroupItem.groupname as deptName ,Person.RealName as userName,

        <if test="type ==2">
            GroupItem.id as deptId,
        </if>
        dfdw_t_kqgl_position.*

        FROM dfdw_t_kqgl_position
        LEFT JOIN Person on dfdw_t_kqgl_position.user_id = Person.Id
        <if test="type ==1">
            LEFT JOIN GroupItem on GroupItem.id = dfdw_t_kqgl_position.group_id
        </if>

        <if test="type ==2">
            LEFT JOIN GroupItem on GroupItem.id = Person.GroupID
        </if>
        where
        dfdw_t_kqgl_position.id = #{id}


        order by dfdw_t_kqgl_position.creation_date desc
    </select>


    <select id="getPageList" resultType="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition">

        SELECT GroupItem.groupname as deptName ,Person.RealName as userName,

        <if test="type ==2">
            GroupItem.id as deptId,
        </if>
               dfdw_t_kqgl_position.*,
        dfdw_t_kqgl_position.company_address as companyAddress,
        dfdw_t_kqgl_position.clock_start_time as clockStartTime,
        dfdw_t_kqgl_position.clock_end_time as clockEndTime

        FROM dfdw_t_kqgl_position
              LEFT JOIN Person on dfdw_t_kqgl_position.user_id = Person.Id
        <if test="type ==1">
            LEFT JOIN GroupItem on GroupItem.id = dfdw_t_kqgl_position.group_id
        </if>

        <if test="type ==2">
            LEFT JOIN GroupItem on GroupItem.id = Person.GroupID
        </if>
        where 1=1
        <if test="type ==1">
            and dfdw_t_kqgl_position.group_id is not null
        </if>
        <if test="type ==2">
            and dfdw_t_kqgl_position.user_id is not null
        </if>

        <if test="userName != null and userName !=''">
           and Person.RealName like CONCAT('%',CONCAT(#{userName},'%'))
        </if>

        <if test="companyAddress != null and companyAddress !=''">
           and  dfdw_t_kqgl_position.company_address like CONCAT('%',CONCAT(#{companyAddress},'%'))
        </if>

        <if test="depList!= null and type ==1">
            and dfdw_t_kqgl_position.group_id   in
            <foreach item="item" index="index" collection="depList" open="(" separator="," close=")">#{item}</foreach>
        </if>

        <if test="depList!= null and type ==2">
            and Person.GroupId    in
            <foreach item="item" index="index" collection="depList" open="(" separator="," close=")">#{item}</foreach>
        </if>

        order by dfdw_t_kqgl_position.creation_date desc


    </select>



</mapper>
