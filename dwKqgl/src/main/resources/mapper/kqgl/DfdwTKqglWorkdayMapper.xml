<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglWorkdayMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglWorkday">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="week" column="week" jdbcType="INTEGER"/>
            <result property="date" column="date" jdbcType="DATE"/>
            <result property="day" column="day" jdbcType="INTEGER"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,month,status,
        week,date,day,
        year
    </sql>
</mapper>
