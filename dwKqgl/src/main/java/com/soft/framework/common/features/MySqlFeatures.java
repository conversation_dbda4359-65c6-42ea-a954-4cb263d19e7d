package com.soft.framework.common.features;

/**
 * Created by Administrator on 2016/1/26.
 */
public class MySqlFeatures implements IDBFeatures {
    public String FormatDateToStr(int strlen, String colname) {
        String strret = colname;
        if (strlen == 8) {
            strret = "date_format(" + colname + ",'%Y%m%d') as " + colname;
        } else if (strlen == 10) {
            strret = "date_format(" + colname + ",'%Y-%m-%d') as " + colname;
        } else if (strlen == 16) {
            strret = "date_format(" + colname + ",'%Y-%m-%d %H:%i') as " + colname;
        } else if (strlen == 19) {
            strret = "date_format(" + colname + ",'%Y-%m-%d %H:%i:%s') as " + colname;
        } else if (strlen>20) {
            strret = "date_format(" + colname + ",'%Y-%m-%d %H:%i:%s.%f') as " + colname;
        }
        return strret;
    }

    public String FormatStrToDate(String strval) {
        String strret = "null";
        int slen=strval.length();
        if (slen == 8) {
            strret = "str_to_date('" + strval + "','%Y%m%d')";
        } else if (slen == 10) {
            strret = "str_to_date('" + strval + "','%Y-%m-%d')";
        } else if (slen== 16) {
            strret = "str_to_date('" + strval + "','%Y-%m-%d %H:%i')";
        } else if (slen== 19) {
            strret = "str_to_date('" + strval + "','%Y-%m-%d %H:%i:%s')";
        } else if (slen>20) {
            strret = "str_to_date('" + strval + "','%Y-%m-%d %H:%i:%s.%f')";
        }
        return strret;
    }

    public String GetColType(String schema,String strtab, String colname) {
        String strSel = "select data_type from information_schema.columns where table_schema='" + schema + "' and table_name='" + strtab + "' and column_name = '" + colname + "'";
        return strSel;
    }

    public String ToPageSql(String strSql,String orderStr, int intPageSize, int intCurrentPage) {
        String strReturn = strSql +" "+orderStr+ " limit " + intCurrentPage * intPageSize + "," + intPageSize;
        return strReturn;
    }

    public String ToLimitSql(String strSql,String orderStr, int start, int end) {
        String strReturn = strSql +" "+orderStr+ " limit " + start + "," + String.valueOf(end-start);
        return strReturn;
    }

    public String PackFunc(String funcname) {
        return funcname;
    }

    public String PackProc(String procname, String valstr) {
        return "call " + procname +"("+ valstr + ");";
    }

    public String PackMetaQry(String sqlstr) {
        return sqlstr;
    }

    public String PackTreeOrder(String orderstr) {
        return "";
    }

    public String GetDefaultDT() {
        return "now()";
    }
}
