package com.soft.framework.helper;

import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataTable;
import com.soft.framework.common.utils.JsonUtils;

import java.util.List;

@SuppressWarnings("unchecked")
public class JsonHelper {
    public static <T> String ToGridPanel(List<T> list) {
        return ToGridPanel(list, -1);
    }

    public static <T> String ToGridPanel(List<T> list, int recordCount) {
        StringBuilder jsonData = new StringBuilder();
        if (recordCount < 0) {
            recordCount = list.size();
        }
        jsonData.append("{\"RecordCount\":\"");
        jsonData.append(recordCount);
        jsonData.append("\",\"Table\":[");
        if (list != null) {
            String jsonGroup = JsonUtils.toJSONString(list);
        }
        jsonData.append("]}");

        return jsonData.toString();
    }

    public static String ToGridPanel(DataTable dt) {
        return ToGridPanel(dt, -1);
    }

    public static String ToGridPanel(DataTable dt, int recordCount) {
        StringBuilder jsonData = new StringBuilder();
        if (recordCount < 0) {
            recordCount = dt.getTotalCount();
        }

        jsonData.append("{\"RecordCount\":\"");
        jsonData.append(recordCount);
        jsonData.append("\",\"Table\":[");

        for (int i = 0; i < recordCount; i++) {
            jsonData.append("{");
            DataRow dr = dt.getRow(i);
            for (int j = 0; j < dr.size(); j++) {
                jsonData.append("\"");
                jsonData.append(dr.getColumn(j).getColumnName());
                jsonData.append("\":\"");
                jsonData.append(dr.getColValue(j).toString());
                jsonData.append("\",");
            }

            jsonData.delete(jsonData.length() - 1, 1);
            jsonData.append("},");
        }

        //去掉末尾“,”号
        if (recordCount > 0) {
            jsonData = jsonData.delete(jsonData.length() - 1, 1);
        }

        jsonData.append("]}");

        return jsonData.toString();
    }

    public static <T> String ToFormPanel(T entity) {
        return ToFormPanel(entity, true, "");
    }

    public static <T> String ToFormPanel(T entity, Boolean success, String text) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(success.toString().toLowerCase());
        jsonData.append(",\"text\":\"");
        jsonData.append(text);
        jsonData.append("\",\"data\":[");

        if (entity != null) {
            jsonData.append(JsonUtils.bean2json(entity));
        } else {
            jsonData.append("{}");
        }

        jsonData.append("]}");

        return jsonData.toString();
    }

    public static String ToFormPanel(DataTable dt) {
        return ToFormPanel(dt, true, "");
    }

    public static String ToFormPanel(DataTable dt, Boolean success, String text) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(success.toString().toLowerCase());
        jsonData.append(",\"text\":\"");
        jsonData.append(text);
        jsonData.append("\",\"data\":[");

        int recordCount = dt.getTotalCount();
        if (dt != null && recordCount > 0) {
            jsonData.append("{");
            DataRow dr = dt.getRow(0);

            for (int j = 0; j < dr.size(); j++) {
                jsonData.append("\"");
                jsonData.append(dr.getColumn(j).getColumnName());
                jsonData.append("\":\"");
                jsonData.append(dr.getColValue(j).toString());
                jsonData.append("\",");
            }

            jsonData.delete(jsonData.length() - 1, 1);
            jsonData.append("},");
        } else {
            jsonData.append("{}");
        }

        if (recordCount > 0) {
            jsonData = jsonData.delete(jsonData.length() - 1, 1);
        }
        jsonData.append("]}");

        return jsonData.toString();
    }

    public static <T> String ToRequest(T entity, Boolean success, String text) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(success.toString().toLowerCase());
        jsonData.append(",\"text\":\"");
        jsonData.append(text.replace("\r\n", "\\r\\n").replace("'", "\\'"));
        jsonData.append("\",\"data\":");

        if (entity != null) {
            jsonData.append(JsonUtils.bean2json(entity));
        } else {
            jsonData.append("{}");
        }

        jsonData.append("}");

        return jsonData.toString();
    }

    public static String ToRequest(Boolean success, String text) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(success.toString().toLowerCase());
        jsonData.append(",\"text\":\"");
        jsonData.append(text.replace("\r\n", "\\r\\n").replace("'", "\\'"));
        jsonData.append("\"");
        jsonData.append("}");

        return jsonData.toString();
    }

    public static String ToRequest(Boolean success, String text, String status) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(success.toString().toLowerCase());
        jsonData.append(",\"text\":\"");
        jsonData.append(text.replace("\r\n", "\\r\\n").replace("'", "\\'"));
        jsonData.append(",\"status\":\"");
        jsonData.append(status.replace("\r\n", "\\r\\n").replace("'", "\\'"));
        jsonData.append("\"");
        jsonData.append("}");

        return jsonData.toString();
    }

    public static String ToJsonString(DataTable dt) {
        return ToGridPanel(dt);
    }

    public static String ToMixDataList(List<DataTable> dlist) {
        StringBuilder jsonData = new StringBuilder();

        jsonData.append("{");
        for (int idx = 0; idx < dlist.size(); idx++) {
            DataTable dt = dlist.get(idx);
            jsonData.append("\"RecordCount" + idx + "\":\"");
            jsonData.append(dt.getTotalCount());
            jsonData.append("\",\"Table" + idx + "\":[");
            for (int i = 0; i < dt.getTotalCount(); i++) {
                jsonData.append("{");

                DataRow dr = dt.getRow(i);
                for (int j = 0; j < dr.size(); j++) {
                    jsonData.append("\"");
                    jsonData.append(dr.getColumn(j).getColumnName());
                    jsonData.append("\":\"");
                    jsonData.append(dr.getColValue(j).toString());
                    jsonData.append("\",");
                }

                jsonData.delete(jsonData.length() - 1, 1);
                jsonData.append("},");
            }

            if (dt.getTotalCount() > 0) {
                jsonData = jsonData.delete(jsonData.length() - 1, 1);
            }

            if (idx < dlist.size() - 1) {
                jsonData.append("],");
            } else {
                jsonData.append("]");
            }
        }

        jsonData.append("}");

        return jsonData.toString();
    }
}
