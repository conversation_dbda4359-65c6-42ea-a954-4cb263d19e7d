package com.soft.framework.helper;

import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.config.OssConfig;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;

public class FileUploadHelper {
    private String path = null;
    private String fileName = null;
    private String fileType = null;
    private HttpServletRequest request = null;
    private int sizes = 0;
    private MultipartFile postedFile = null;
    private Boolean useOSS=false;
    public String errorText;

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public int getSizes() {
        return sizes;
    }

    public void setSizes(int sizes) {
        this.sizes = sizes*1024;
    }

    public MultipartFile getPostedFile() {
        return postedFile;
    }

    public void setPostedFile(MultipartFile postedFile) {
        this.postedFile = postedFile;
    }

    public String getErrorText() {
        return errorText;
    }

    public void setErrorText(String errorText) {
        this.errorText = errorText;
    }



    /// <summary>
    /// 初始化变量
    /// </summary>
    public FileUploadHelper(HttpServletRequest req)
    {
        this.request = req;
        if (OssConfig.getOpenFlag().equals("true"))
        {
            useOSS = true;
        }
    }

    public FileUploadHelper()
    {
        if (OssConfig.getOpenFlag().equals("true"))
        {
            useOSS = true;
        }
    }

    public String PathToName(String path)
    {
        int pos = path.lastIndexOf("\\");
        return path.substring(pos + 1);
    }

    /// <summary>
    /// 上传
    /// </summary>
    public String Upload()
    {
        try
        {
            String filePath = null;

            String modifyFileName = getFileName();

            //获得站点的物理路径
            String uploadFilePath = null;
            uploadFilePath = getPath();

            //获得文件的上传的路径
            String sourcePath="";
            if(postedFile!= null)
            {
                sourcePath = PathToName(postedFile.getOriginalFilename());
            }

            //判断上传文件是否为空
            if (sourcePath == "" || sourcePath == null ||postedFile==null)
            {
                message("您没有上传数据呀！");
                return null;
            }

            //获得文件扩展名
            String tFileType = sourcePath.substring(sourcePath.lastIndexOf(".") + 1);
            //分解允许上传文件的格式
            if(!getFileType().contains(tFileType))
            {
                message("支持的文件格式为:" + fileType);
                return null;
            }

            //获得上传文件的大小
            long strLen = postedFile.getSize();
            //判断上传文件大小
            /*if (strLen >= getSizes())
            {
                message("上传的文件不能大于" + sizes / 1024.0 /1024.0 + "兆。");
                return null;
            }*/

            if (!FileUtil.DirectoryExists(uploadFilePath))
            {
                FileUtil.CreateDir(uploadFilePath);
            }

            filePath = uploadFilePath + modifyFileName +"." + tFileType;

            //删除已存在的旧文件
            if (FileUtil.FileExists(filePath)) {
                FileUtil.Delete(filePath);
            }

            File fi = new File(filePath);
            postedFile.transferTo(fi);
            filePath = getPath() + modifyFileName +"." + tFileType;
            fileName = modifyFileName +"." + tFileType;

            if (useOSS)
            {
                String fullfilename = fi.getAbsolutePath();
                String ossLocalRoot = ConfigHelper.getOssLocalPath();
                String ossRemoteRoot = ConfigHelper.getOssRemotePath();
                //文件替换本地OSSLocalRoot映射到OSSRemoteRoot获取键值
                String kname=fullfilename.replace("\\", "/").replace(ossLocalRoot,ossRemoteRoot);
                OSSHelper ossHelper = new OSSHelper();
                ossHelper.UploadFileProx(kname, fi, false);
            }

            return filePath;
        }
        catch (Exception e) {
            e.printStackTrace();

            message("出现未知错误！");
            return null;
        }
    }

    public String UploadTemp()
    {
        try
        {
            String filePath = null;

            String modifyFileName = getFileName();

            //获得站点的物理路径
            String uploadFilePath = null;
            uploadFilePath = ConfigHelper.getProfile() + getPath();

            //获得文件的上传的路径
            String sourcePath="";
            if(postedFile!= null)
            {
                sourcePath = PathToName(postedFile.getOriginalFilename());
            }

            //判断上传文件是否为空
            if (sourcePath == "" || sourcePath == null ||postedFile==null)
            {
                message("您没有上传数据呀！");
                return null;
            }

            //获得文件扩展名
            String tFileType = sourcePath.substring(sourcePath.lastIndexOf(".") + 1);
            //获得上传文件的大小

            long strLen = postedFile.getSize();
            //分解允许上传文件的格式
            if(!getFileType().contains(tFileType))
            {
                message("支持的文件格式为:" + fileType);
                return null;
            }

            //判断上传文件大小
            /*if (strLen >= getSizes())
            {
                message("上传的文件不能大于" + sizes / 1024.0 /1024.0 + "兆。");
                return null;
            }*/

            if (!FileUtil.DirectoryExists(uploadFilePath))
            {
                FileUtil.CreateDir(uploadFilePath);
            }

            filePath = uploadFilePath + modifyFileName +"." + tFileType;

            //删除已存在的旧文件
            if (FileUtil.FileExists(filePath)) {
                FileUtil.Delete(filePath);
            }

            File fi = new File(filePath);
            postedFile.transferTo(fi);
            filePath = getPath() + modifyFileName +"." + tFileType;
            fileName = modifyFileName +"." + tFileType;

            return filePath;
        }
        catch (Exception e) {
            //异常
            message("出现未知错误！");
            return null;
        }
    }

    private void message(String s) {
        setErrorText(s);
    }
}
