package com.soft.framework.controller.Base;

import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.LogHelper;
import com.soft.framework.helper.OSSHelper;
import com.soft.framework.helper.ToolHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

@Controller
@Configuration
@RequestMapping(value="/Service/Base/UploadMan" )
@Api(tags ="基本框架接口->Upload附件接口")
public class UploadController {
    @RequestMapping(value="/getByteStream", produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE}, method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="getByteStream",notes="获取OSS资源,返回二进制流")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public byte[] getByteStream(HttpServletRequest request){
        AjaxResult ajaxResult=null;
        try
        {
            String kname= request.getParameter("rfurl");
            if (StringUtil.IsNullOrEmpty(kname)) {
                return null;
            }

            String fname= FileUtil.ExtractFileName(kname);
            String ftype=FileUtil.ExtractFileExt(kname);
            String ossLocalPath=ConfigHelper.getOssLocalPath();
            
            String fullfile="";
            String localf=kname.replaceAll(ConfigHelper.getOssRemotePath(),ConfigHelper.getOssLocalPath());
            if(!FileUtil.FileExists(localf))
            {
                if(ConfigHelper.isOSSOpen()) {
                    fullfile = localf;
                    OSSHelper ossHelper = new OSSHelper();
                    if (FileUtil.FileExists(fullfile)) {
                        FileUtil.Delete(fullfile);
                    }
                    ossHelper.DownloadFile(kname, fullfile);
                }
            }else
            {
                fullfile=localf;
            }

            if(FileUtil.FileExists(fullfile))
            {
                byte[] fb=ToolHelper.File2Bytes(fullfile);
                //使用springmvc框架的ResponseEntity对象封装返回数据
                return fb;
            }else
            {
                ajaxResult = AjaxResult.error("下载OSS文件失败!");
                return null;
            }
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return null;
        }
    }

    @RequestMapping(value="/getPdf",produces = {MediaType.APPLICATION_PDF_VALUE},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="getPdf",notes="获取OSS资源,PDF,inline形式返回文件资源")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public void getPdf(HttpServletRequest request, HttpServletResponse response){
        AjaxResult ajaxResult=null;
        try
        {
            String kname= request.getParameter("rfurl");
            if (StringUtil.IsNullOrEmpty(kname)) {
                return;
            }

            String fname= FileUtil.ExtractFileName(kname);
            String ftype=FileUtil.ExtractFileExt(kname);
            String ossLocalPath=ConfigHelper.getOssLocalPath();


            String fullfile="";
            String localf=kname.replaceAll(ConfigHelper.getOssRemotePath(),ConfigHelper.getOssLocalPath());
            if(!FileUtil.FileExists(localf))
            {
                if(ConfigHelper.isOSSOpen()) {
                    fullfile = localf;
                    OSSHelper ossHelper = new OSSHelper();
                    if (FileUtil.FileExists(fullfile)) {
                        FileUtil.Delete(fullfile);
                    }
                    ossHelper.DownloadFile(kname, fullfile);
                }
            }else
            {
                fullfile=localf;
            }

            if(FileUtil.FileExists(fullfile))
            {
                byte[] data = ToolHelper.File2Bytes(fullfile);
                response.reset();
                response.setContentType("application/pdf;charset=UTF-8");
                response.setHeader("Content-Disposition", "inline");
                response.addHeader("Content-Length", "" + data.length);

                //用于处理字符流数据 response.getWriter()
                //用于输出字符流数据或者二进制的字节流数据   response.getOutputStream()
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();
            }else
            {
                ajaxResult = AjaxResult.error("下载OSS文件失败!");
                return ;
            }
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return;
        }
    }

    @RequestMapping(value="/getImage",produces =
            {MediaType.IMAGE_JPEG_VALUE,MediaType.IMAGE_GIF_VALUE,MediaType.IMAGE_PNG_VALUE},
            method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="getImage",notes="获取OSS资源,返回二进制流")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public byte[] getImage(HttpServletRequest request){
        AjaxResult ajaxResult=null;
        try
        {
            String kname= request.getParameter("rfurl");
            LogHelper.WriteSysLog("kname="+kname);
            if (StringUtil.IsNullOrEmpty(kname)) {
                return null;
            }

            String fname= FileUtil.ExtractFileName(kname);
            String ftype=FileUtil.ExtractFileExt(kname);
            String ossLocalPath=ConfigHelper.getOssLocalPath();

            String fullfile="";
            String localf=kname.replaceAll(ConfigHelper.getOssRemotePath(),ConfigHelper.getOssLocalPath());
            if(!FileUtil.FileExists(localf))
            {
                if(ConfigHelper.isOSSOpen()) {
                    fullfile = localf;
                    OSSHelper ossHelper = new OSSHelper();
                    if (FileUtil.FileExists(fullfile)) {
                        FileUtil.Delete(fullfile);
                    }
                    ossHelper.DownloadFile(kname, fullfile);
                }
            }else
            {
                fullfile=localf;
            }

            if(FileUtil.FileExists(fullfile))
            {
                byte[] fb=ToolHelper.File2Bytes(fullfile);
                //使用springmvc框架的ResponseEntity对象封装返回数据
                return fb;
            }else
            {
                LogHelper.WriteSysLog("下载OSS文件失败，或者缺少本地文件,fullfile="+fullfile);
                ajaxResult = AjaxResult.error("下载OSS文件失败，或者缺少本地文件,fullfile="+fullfile);
                return null;
            }
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return null;
        }
    }

    @RequestMapping(value="/getAttach",produces = {MediaType.APPLICATION_OCTET_STREAM_VALUE},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="getAttach",notes="获取OSS资源,PDF,inline形式返回文件资源")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public void getAttach(HttpServletRequest request, HttpServletResponse response){
        AjaxResult ajaxResult=null;
        try
        {
            String kname= request.getParameter("rfurl");
            if (StringUtil.IsNullOrEmpty(kname)) {
                return;
            }

            String fname= FileUtil.ExtractFileName(kname);
            String ftype=FileUtil.ExtractFileExt(kname);
            String ossLocalPath=ConfigHelper.getOssLocalPath();

            String fullfile="";
            String localf=kname.replaceAll(ConfigHelper.getOssRemotePath(),ConfigHelper.getOssLocalPath());
            if(!FileUtil.FileExists(localf))
            {
                if(ConfigHelper.isOSSOpen()) {
                    fullfile = localf;
                    OSSHelper ossHelper = new OSSHelper();
                    if (FileUtil.FileExists(fullfile)) {
                        FileUtil.Delete(fullfile);
                    }
                    ossHelper.DownloadFile(kname, fullfile);
                }
            }else
            {
                fullfile=localf;
            }

            if(FileUtil.FileExists(fullfile))
            {
                byte[] data = ToolHelper.File2Bytes(fullfile);
                response.reset();
                response.setContentType("application/pdf;charset=UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fname,"UTF-8"));
                response.addHeader("Content-Length", "" + data.length);

                //用于处理字符流数据 response.getWriter()
                //用于输出字符流数据或者二进制的字节流数据   response.getOutputStream()
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();
            }else
            {
                ajaxResult = AjaxResult.error("下载OSS文件失败!");
                return ;
            }
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return;
        }
    }
}
