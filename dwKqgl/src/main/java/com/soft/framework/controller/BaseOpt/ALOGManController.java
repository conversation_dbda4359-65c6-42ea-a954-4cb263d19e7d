package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.gcc.base.entity.ApplicationLog;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.NFT_ModuleAMLink;
import com.yyszc.wpbase.entity.vFunctionItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/ALOGMan")
@Api(tags = "基本框架接口->模块自启动功能管理接口")
public class ALOGManController {
    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String Remark = request.getParameter("Remark");
            String StartT = request.getParameter("StartT");
            String EndT = request.getParameter("EndT");
            String PDate = request.getParameter("PDate");


            String dstart = "";
            String dend = "";
            if(!StringUtil.IsNullOrEmpty(PDate)){
                PDate=PDate.substring(0,10);
            }
            else
            {
                PDate = MetaHelper.GetDateString(new Date(),true);
            }

            if (StartT != "")
            {
                dstart = PDate + " " + StartT;
            }
            else
            {
                dstart = PDate + " 00:00:00";
            }

            if (EndT != "")
            {
                dend = PDate + " " + EndT;
            }
            else
            {
                dend = PDate + " 23:59:59";
            }

            strsql.append("select * from ApplicationLog where 1=1 ");
            if (!dstart.equals("")) {
                strsql.append(" and OperateDate>='" + dstart+ "'");
            }
            if (!dend.equals("")) {
                strsql.append(" and OperateDate<='" + dend+ "'");
            }
            if (!Remark.equals(""))
            {
                strsql.append(" and (OperateModule like '%" + Remark + "%' or OperateFunction like '%" + Remark + "%' or  OperateDetail like '%" + Remark + "%')");
            }


            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetALOGList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetALOGList", notes = "获取当前模块启动项列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetALOGList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<ApplicationLog> list = sqlhelper.GetObjectList(ApplicationLog.class, strsql);
            ajaxResult = AjaxResult.extgrid(ApplicationLog.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
    
    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出平台登录日志信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;
            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取平台登录日志信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("OperatorName", "操作名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("OperateDate", "操作日期", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("OperateModule", "操作模块", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("OperateFunction", "操作功能", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("OperateDetail", "操作说明", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "平台登录日志信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetStartPList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetStartPList", notes = "导出平台登录日志信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetStartPList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String mid = request.getParameter("mid");
        Integer imid = Integer.parseInt(mid);

        String jsonstr = "";
        String strsql = "";
        String strPath = "../../Page/";
        try {
            List<NFT_ModuleAMLink> list = WpServiceHelper.GetNFT_ModuleAMLinkListByMId(imid);
            if (list == null) {
                ajaxResult = AjaxResult.error("获取数据失败！", list);
                return ajaxResult;
            }
            List<vFunctionItem> flist = new ArrayList<vFunctionItem>();
            for (NFT_ModuleAMLink obj : list) {
                String mRoot = obj.getLK_TMENU();
                String mPath = obj.getLK_TPATH();
                String mMenu = obj.getLK_TNODE();
                if (StringUtil.IsNullOrEmpty(mRoot) || StringUtil.IsNullOrEmpty(mMenu)) {
                    continue;
                }
                ;
                if (StringUtil.IsNullOrEmpty(mPath)) {
                    strsql = "select * from vFunctionItem where Title='" + mMenu + "' and ParentName='" + mRoot + "'";
                } else {
                    strsql = "select id from vFunctionItem where Title='" + mPath + "' and ParentName='" + mRoot + "'";
                    String parentid = WpServiceHelper.ExecuteScalar(strsql);
                    if (parentid == null || parentid == "") {
                        continue;
                    }
                    strsql = "select * from vFunctionItem where Title='" + mMenu + "' and ParentId='" + parentid + "'";
                }

                if (StringUtil.IsNullOrEmpty(strsql)) {
                    strsql = AesHelper.aesEncodeCBC(strsql);
                }
                vFunctionItem entity = WpServiceHelper.GetVFunctionItemBySql(strsql);
                if (entity == null) {
                    continue;
                }
                entity.setUrl(strPath + entity.getUrl());
                flist.add(entity);
            }

            ajaxResult = AjaxResult.success("获取数据成功！", list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
