package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.*;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmmOpt" )
public class CMMOptController {
    @GetMapping("/GetUIWMaskOption")
    @ResponseBody
    public AjaxResult GetUIWMaskOption(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            if (ConfigHelper.isEPWMask())
            {
                ajaxResult=AjaxResult.success("开启界面水印!");
                return ajaxResult;
            }
            else
            {
                ajaxResult=AjaxResult.error("关闭界面水印!");
                return ajaxResult;
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @GetMapping("/GetSessionTimeOut")
    @ResponseBody
    public AjaxResult GetSessionTimeOut(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            HttpSession session=request.getSession();
            int sessionTimeOut =session.getMaxInactiveInterval();
            ajaxResult=AjaxResult.success(String.valueOf(sessionTimeOut));
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @GetMapping("/GetSystemVerContrl")
    @ResponseBody
    public AjaxResult GetSystemVerContrl(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();
            String  jsversion =sqlhelper.ExecuteScalar("select vc_ver from cps_ver_ctrl");
            if (jsversion.equals("")) {
                jsversion = "0";
            }
            ajaxResult=AjaxResult.success(jsversion);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @GetMapping("/GetGroupList")
    @ResponseBody
    public AjaxResult GetGroupList(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            List<vComp> list = null;

            list= WpServiceHelper.GetCompList();
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取单位信息失败！");
                return ajaxResult;
            }

            ajaxResult=AjaxResult.extgrid(vComp.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @GetMapping("/GetGroupList2")
    @ResponseBody
    public AjaxResult GetGroupList2(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            List<vComp> list = null;
            list=WpServiceHelper.GetCompList();
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取单位信息失败！");
                return ajaxResult;
            }

            vComp opt = new vComp();
            opt.setCOMP_ID(0);
            opt.setCOMP_NAME("==所有公司==");
            list.add( 0, opt );

            ajaxResult=AjaxResult.extgrid(vComp.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @GetMapping("/GetModuleList")
    @ResponseBody
    public AjaxResult GetModuleList(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            List<Module> list = null;
            list=WpServiceHelper.GetModuleList();
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取模块信息失败！");
                return ajaxResult;
            }

            ajaxResult=AjaxResult.extgrid(Module.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @GetMapping("/GetLCList")
    @ResponseBody
    public AjaxResult GetLCList(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            List<Lcdefine> list = null;
            String strsql="select * from Lcdefine order by id asc";
            list=WpServiceHelper.GetLcdefineList(strsql);
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取流程定义信息失败！");
                return ajaxResult;
            }

            ajaxResult=AjaxResult.extgrid(Lcdefine.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @PostMapping("/GetLCJDList")
    @ResponseBody
    public AjaxResult GetLCJDList(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            String LcID=request.getParameter("LcID");
            if (StringUtil.IsNullOrEmpty(LcID)) {
                ajaxResult =AjaxResult.error("传输参数有误!");
                return ajaxResult;
            }

            List<Lcjd> list = null;
            String strsql="select * from Lcjd where lc_defineID='"+LcID+"' order by LcjdID asc";
            list=WpServiceHelper.GetLcjdList(strsql);
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取流程节点定义信息失败！");
                return ajaxResult;
            }

            ajaxResult=AjaxResult.extgrid(Lcjd.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @PostMapping("/GetRoleList")
    @ResponseBody
    public AjaxResult GetRoleList(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            String RoleKind=request.getParameter("RoleKind");
            if (StringUtil.IsNullOrEmpty(RoleKind)) {
                ajaxResult =AjaxResult.error("传输参数有误!");
                return ajaxResult;
            }

            List<Role> list = null;
            String strsql="select * from Role where RoleKind='"+RoleKind+"' order by RoleName asc";
            list=WpServiceHelper.GetRoleList(strsql);
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取角色定义信息失败！");
                return ajaxResult;
            }

            ajaxResult=AjaxResult.extgrid(Role.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @PostMapping("/GetUserList")
    @ResponseBody
    public AjaxResult GetUserList(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            String CompId=request.getParameter("CompId");
            if (StringUtil.IsNullOrEmpty(CompId)) {
                ajaxResult =AjaxResult.error("传输参数有误!");
                return ajaxResult;
            }
            String gstr = sqlhelper.ExecuteScalar("select dbo.FUNC_GRP_CLIST(" + CompId + ")");

            List<Person> list = null;
            String strsql="select Id,LoginName+'|'+RealName as RealName from vPerson where GroupId in("+gstr+") order by GroupId asc";
            list=WpServiceHelper.GetPersonList(strsql);
            if(list==null)
            {
                ajaxResult=AjaxResult.error("操作失败，获取角色定义信息失败！");
                return ajaxResult;
            }

            ajaxResult=AjaxResult.extgrid(Person.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @Data
    private class FunctionMenuClass
    {
        @JSONField(name="id")
        public String id ;
        @JSONField(name="url")
        public String url ;
        @JSONField(name="title")
        public String title ;
    }

    @PostMapping("/GetResGroupName")
    @ResponseBody
    public AjaxResult GetResGroupName(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String gname = request.getParameter("gname");
        String fname = request.getParameter("fname");

        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            String fstr = "";
            String gid=sqlhelper.ExecuteScalar("select id from functionitem where parentid=0 and title='"+gname+"'");
            DataTable tmpdt=sqlhelper.GetDataTable("select id,url,title from functionitem where parentid="+gid+" and title='"+fname+"'");
            if (tmpdt.getTotalCount()>0)
            {
                ajaxResult=AjaxResult.success(gid);
                FunctionMenuClass fmc=new FunctionMenuClass();
                fmc.id=tmpdt.getRow(0).getColValue("id").toString();
                fmc.url=tmpdt.getRow(0).getColValue("url").toString();
                fmc.title=tmpdt.getRow(0).getColValue("title").toString();
                fstr= JSON.toJSON(fmc).toString();
                ajaxResult.put("status",fstr);
                return ajaxResult;
            }else
            {
                ajaxResult=AjaxResult.error("未找到有效资源!");
                return ajaxResult;
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
