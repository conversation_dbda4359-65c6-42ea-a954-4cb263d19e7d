package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.yyszc.wpbase.entity.Lc_workFlow;
import com.yyszc.wpbase.ventity.PersonEntity;
import lombok.Data;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmManGt")
public class CMManToolController {
    ToolHelper cptl = new ToolHelper();
    MetaHelper cpmtl = new MetaHelper();
    SqlHelper sqlhelper = new SqlHelper();
    SqlProc sqlproc = new SqlProc();

    @Data
    class CPSOFT_CHILD_DIR
    {
        @JSONField(name="id")
        public int id;

        @JSONField(name="name")
        public String name;
    }


    @RequestMapping(value="/ExecuteCreateDir",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteCreateDir(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        
        String DirMark = request.getParameter("DirMark");
        if (StringUtil.IsNullOrEmpty(DirMark)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try
        {
            String createp=ConfigHelper.getProfile()+"/"+DirMark+"/";
            if (!FileUtil.DirectoryExists(createp))
            {
                FileUtil.CreateDir(createp);
                ajaxResult =AjaxResult.success("创建目录成功!");
            }
            else
            {
                ajaxResult =AjaxResult.error("目录已经存在!");
            }

            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    //获取一级目录
    @RequestMapping(value="/GetUploadChildPath",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult GetUploadChildPath(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        List<CPSOFT_CHILD_DIR> list = new ArrayList<CPSOFT_CHILD_DIR>();

        try
        {
            String destpath=ConfigHelper.getProfile();
            List<String> dirs=FileUtil.GetDirectories(destpath);
            for (int i = 0; i < dirs.size(); i++)
            {
                String dname = dirs.get(i).replace("\\","/");
                CPSOFT_CHILD_DIR tmpd=new CPSOFT_CHILD_DIR();
                tmpd.id = i;
                tmpd.name = dname;
                list.add(tmpd);
            }

            ajaxResult=AjaxResult.extgrid(CPSOFT_CHILD_DIR.class,list.size(),list);
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExecuteCopyFileTo",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteCopyFileTo(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        String SPath = request.getParameter("SPath");
        String SFile = request.getParameter("SFile");
        String DPath = request.getParameter("DPath");
        if (StringUtil.IsNullOrEmpty(SPath)||StringUtil.IsNullOrEmpty(SFile)||StringUtil.IsNullOrEmpty(DPath)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try
        {
            String spath = ConfigHelper.getProfile()+"/"+SPath+"/";
            String dpath = ConfigHelper.getProfile()+"/"+DPath+"/";
            File fi0 = new File(spath+"\\"+SFile);
            File fi1 = new File(dpath+"\\"+SFile);

            if (fi0.exists())
            {
                if (fi1.exists())
                {
                    ajaxResult =AjaxResult.success("目标文件已经存在!");
                }
                else
                {
                    FileUtil.CopyFile(spath+"\\"+SFile,dpath+"\\"+SFile);
                    ajaxResult =AjaxResult.success("复制文件已经成功!");
                }
            }
            else
            {
                ajaxResult =AjaxResult.error("源文件不存在!");
            }

            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExecuteGrabSource", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String ExecuteGrabSource(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String SPath = request.getParameter("SPath");
        if (StringUtil.IsNullOrEmpty(SPath)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            String tpath = ConfigHelper.getProfile() + "/Temp/";
            String spath = ConfigHelper.getStaticPath() + "/" + SPath + "/";

            File fi = new File(spath);
            if (fi.exists()) {
                String filename = "GrabSource_" + MetaHelper.GetSimpTimeString(new Date()) + ".zip";
                if (FileUtil.FileExists(tpath + "\\" + filename)) {
                    FileUtil.Delete(tpath + "\\" + filename);
                }

                ZipHelper.ZipCompress(fi.getAbsolutePath(), tpath + "\\" + filename);

                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = filename.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + filename.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("参数指定的文件不存在!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value="/ExecuteCleanApplication",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteCleanApplication(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        try
        {
            ServletContext application= request.getServletContext();

            application.removeAttribute("SQL_IDSTR_LIST");
            application.removeAttribute("ProjName");
            application.removeAttribute("GropName");
            application.removeAttribute("SystemMark");
            application.removeAttribute("SqlTg");
            application.removeAttribute("LogFlag");
            application.removeAttribute("UIWMask");
            application.removeAttribute("EPWMask");
            application.removeAttribute("OcciLog");
            application.removeAttribute("OcciSecond");
            application.removeAttribute("LoadDBConf");
            application.removeAttribute("ExtVersion");

            ajaxResult =AjaxResult.success("清理Application成功!");

            return ajaxResult;
        }
       catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExecuteQueryLcNum",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteQueryLcNum(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;
        String lcComp = request.getParameter("lcComp");
        String lcId = request.getParameter("lcId");
        String lcjdId = request.getParameter("lcjdId");
        String srcName = request.getParameter("srcName");
        if (StringUtil.IsNullOrEmpty(lcComp)||StringUtil.IsNullOrEmpty(lcId)||StringUtil.IsNullOrEmpty(lcjdId)||StringUtil.IsNullOrEmpty(srcName)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try
        {
            String strsql = "";

            String gstr = sqlhelper.ExecuteScalar("select dbo.FUNC_GRP_CLIST(" + lcComp + ")");

            strsql = "select count(*) from lc_workflow where 1=1 ";
            if (!lcId.equals("")) {
                strsql += " and lc_defineID in(" + lcId.replace("、", ",") + ")";
            }
            if (!lcjdId.equals("")) {
                strsql+=" and lc_jdid='"+lcjdId+"'";
            }
            strsql +=" and personZgh='"+srcName+"' and groupid in("+gstr+") and transdate is null ";

            String strn=sqlhelper.ExecuteScalar(strsql);
            ajaxResult =AjaxResult.success("获取流程数据成功!",strn);

            return ajaxResult;
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExecuteLcTranfer",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteLcTranfer(HttpServletRequest request) throws Exception {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String lcComp = request.getParameter("lcComp");
        String lcId = request.getParameter("lcId");
        String lcjdId = request.getParameter("lcjdId");
        String srcName = request.getParameter("srcName");
        String destName = request.getParameter("destName");
        if (StringUtil.IsNullOrEmpty(lcComp)||StringUtil.IsNullOrEmpty(lcId)||StringUtil.IsNullOrEmpty(lcjdId)||StringUtil.IsNullOrEmpty(srcName)||StringUtil.IsNullOrEmpty(destName)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        PersonEntity person2 = DBHelper.GetPersonByLoginName(destName);
        if (person2 == null)
        {
            ajaxResult =AjaxResult.error("获取接收人信息失败,请确认!");
            return ajaxResult;
        }
        if (!person2.getTopGroupId().equals(lcComp))
        {
            ajaxResult =AjaxResult.error("接收人目前不是所选公司账户！");
            return ajaxResult;
        }
        String gstr = sqlhelper.ExecuteScalar("select dbo.FUNC_GRP_CLIST(" + lcComp + ")");

        String strwhere = "";
        if (!lcId.equals("")) {
            strwhere += " and lc_defineID in(" + lcId.replace("、", ",") + ")";
        }
        if (!lcjdId.equals("")) {
            strwhere+=" and lc_jdid='"+lcjdId+"'";
        }
        strwhere +=" and personZgh='"+srcName+"' and groupid in("+gstr+") and transdate is null ";

        strsql = "select distinct lc_defineID,ywID,lc_jdID,BXType,PNO from lc_workflow where 1=1 "+strwhere;
        List<Lc_workFlow> _list = sqlhelper.GetObjectList(Lc_workFlow.class,strsql);


        sqlproc.BeginTrans();
        try
        {
            for (Lc_workFlow tmpo:_list)
            {
                if (tmpo.getBXType()== null) {
                    tmpo.setBXType("");
                }
                if (tmpo.getPNO()== null) {
                    tmpo.setPNO("");
                }
                strsql = "update Lc_currentState set sendPersonZgh=sendPersonZgh+'~" + person2.getLoginName()+ "~' " +
                        "where lc_defineID='" + tmpo.getLc_defineID() + "' and ywID='" + tmpo.getYwID() + "'" +
                        "and lc_jdID='" + tmpo.getLc_jdID() + "' and BXType='" + tmpo.getBXType() + "'" +
                        "and PNO='" + tmpo.getPNO() + "'";
                sqlproc.ExecuteNoQuery(strsql);
            }

            strsql = "update lc_workflow set personZgh='"+person2.getLoginName()+"',personName='"+person2.getRealName()+"',groupId='"+person2.getGroupId()+"',groupName='"+person2.getGroupName()+ "' where 1=1 "+strwhere;
            sqlproc.ExecuteNoQuery(strsql);

            sqlproc.CommitTrans();
            ajaxResult = AjaxResult.success("让渡流程数据成功!");
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            sqlproc.RollbackTrans();
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExecuteLcCopyTo",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public AjaxResult ExecuteLcCopyTo(HttpServletRequest request) throws Exception {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String lcComp = request.getParameter("lcComp");
        String lcId = request.getParameter("lcId");
        String lcjdId = request.getParameter("lcjdId");
        String srcName = request.getParameter("srcName");
        String destName = request.getParameter("destName");
        if (StringUtil.IsNullOrEmpty(lcComp)||StringUtil.IsNullOrEmpty(lcId)||StringUtil.IsNullOrEmpty(lcjdId)||StringUtil.IsNullOrEmpty(srcName)||StringUtil.IsNullOrEmpty(destName)) {
            ajaxResult =AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        PersonEntity person2 = DBHelper.GetPersonByLoginName(destName);
        if (person2 == null)
        {
            ajaxResult =AjaxResult.error("获取接收人信息失败,请确认!");
            return ajaxResult;
        }
        if (!person2.getTopGroupId().equals(lcComp))
        {
            ajaxResult =AjaxResult.error("接收人目前不是所选公司账户！");
            return ajaxResult;
        }
        String gstr = sqlhelper.ExecuteScalar("select dbo.FUNC_GRP_CLIST(" + lcComp + ")");

        String strwhere = "";
        if (!lcId.equals("")) {
            strwhere += " and lc_defineID in(" + lcId.replace("、", ",") + ")";
        }
        if (!lcjdId.equals("")) {
            strwhere+=" and lc_jdid='"+lcjdId+"'";
        }
        strwhere +=" and personZgh='"+srcName+"' and groupid in("+gstr+") and transdate is null ";

        strsql = "select distinct lc_defineID,ywID,lc_jdID,BXType,PNO from lc_workflow where 1=1 "+strwhere;
        List<Lc_workFlow> _list = sqlhelper.GetObjectList(Lc_workFlow.class,strsql);

        sqlproc.BeginTrans();
        try
        {
            for (Lc_workFlow tmpo:_list)
            {
                if (tmpo.getBXType()== null) {
                    tmpo.setBXType("");
                }
                if (tmpo.getPNO()== null) {
                    tmpo.setPNO("");
                }
                strsql = "update Lc_currentState set sendPersonZgh=sendPersonZgh+'~" + person2.getLoginName()+ "~' " +
                        "where lc_defineID='" + tmpo.getLc_defineID() + "' and ywID='" + tmpo.getYwID() + "'" +
                        "and lc_jdID='" + tmpo.getLc_jdID() + "' and BXType='" + tmpo.getBXType() + "'" +
                        "and PNO='" + tmpo.getPNO() + "'";
                sqlproc.ExecuteNoQuery(strsql);
            }

            strsql = "insert into lc_workflow(lc_defineID,ywID,lc_jdID,lc_jdmc,groupID,groupName,personZgh,personName,transdate,feed,number,BXType,PNO,startdate,LcByRole,isback,useback) ";
            strsql += " select lc_defineID,ywID,lc_jdID,lc_jdmc,'"+person2.getGroupId()+"','"+person2.getGroupName()+"','"+person2.getLoginName()+"','"+person2.getRealName()+"',transdate,feed,number,BXType,PNO,startdate,LcByRole,isback,useback from lc_workflow ";
            strsql += " where 1=1 "+strwhere;
            sqlproc.ExecuteNoQuery(strsql);

            sqlproc.CommitTrans();
            ajaxResult = AjaxResult.success("复刻流程数据成功!");
            return ajaxResult;
        }
        catch (Exception Ex)
        {
            sqlproc.RollbackTrans();
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
