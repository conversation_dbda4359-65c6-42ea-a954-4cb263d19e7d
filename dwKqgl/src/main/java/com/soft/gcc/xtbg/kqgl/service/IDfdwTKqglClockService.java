package com.soft.gcc.xtbg.kqgl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock;
import com.soft.gcc.xtbg.kqgl.dto.CalendarDto;
import com.soft.gcc.xtbg.kqgl.dto.MrdkDto;
import com.soft.gcc.xtbg.kqgl.dto.MykqDto;
import com.soft.gcc.xtbg.kqgl.dto.MykqParm;
import com.yyszc.wpbase.entity.Person;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_clock(考勤管理-考勤列表)】的数据库操作Service
* @createDate 2023-05-25 13:20:50
*/
public interface IDfdwTKqglClockService extends IService<DfdwTKqglClock> {
    List<MykqDto> getListMytj(MykqParm ent, PersonEntity person );

    List<MrdkDto> getListMrTj(MykqParm ent, PersonEntity person);

    MrdkDto getClockInfo(Integer applyUser, String time);

    Result getUserHistoryLocationNew(Integer userId, String date);


    Result getClickList(Map<String,String> param);


    List<CalendarDto> getCalenDarList(String time, Integer applyUser);

    public Result<Object> dwppList(MykqParm ent,PersonEntity person);


    public List<DfdwTKqglClock> getDwppList(MykqParm ent,PersonEntity person);

    Integer sendSms(String phone, String content);
}
