package com.soft.gcc.xtbg.kqgl.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @description 每月考勤参数
 * @date 2023/5/25 15:23:56
 */
@Data
public class MykqParm extends PageBaseEntity {
    //每月打卡信息-月份筛选
    private String month;

    private Integer start;
    private Integer end;
    private Integer groupId;
    private String userName;
    private Integer type;  //异常类型

    //每日打卡信息- 日期筛选
    private String date;

    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startDate;

    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String endDate;
}
