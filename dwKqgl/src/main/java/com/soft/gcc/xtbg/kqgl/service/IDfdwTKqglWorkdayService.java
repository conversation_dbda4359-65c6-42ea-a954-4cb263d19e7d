package com.soft.gcc.xtbg.kqgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglWorkday;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_workday(考勤管理-工作日设置)】的数据库操作Service
* @createDate 2023-05-29 09:30:46
*/
public interface IDfdwTKqglWorkdayService extends IService<DfdwTKqglWorkday> {

    IPage<DfdwTKqglWorkday> selectPage(DfdwTKqglWorkday workday);

    Integer addWorkDay(DfdwTKqglWorkday workday);


    List<DfdwTKqglWorkday> httpApihubs(RestTemplate restTemplate, String url, Integer status) throws Exception;

}
