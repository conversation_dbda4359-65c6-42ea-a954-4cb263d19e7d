/*
 * @(#) ImageCheckAPIDemo.java 2016年3月15日
 *
 * Copyright 2010 NetEase.com, Inc. All rights reserved.
 */
package com.soft.gcc.xtbg.kqgl.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.client.HttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * 实人认证 接口调用示例,该示例依赖以下jar包：
 * 1. httpclient，用于发送http请求,详细见HttpClient4Utils.java
 * 2. commons-codec，使用md5算法生成签名信息，详细见SignatureUtils.java
 * 3. gson，用于做json解析
 *
 * <AUTHOR>
 */
public class RealPersonCheck {
    private static final Logger log = LoggerFactory.getLogger(RealPersonCheck.class);
    /**
     * 产品密钥ID，产品标识
     */
    private final static String SECRET_ID = "304dc76ff494d01ffb91fc6178f02722";
    /**
     * 产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露
     */
    private final static String SECRET_KEY = "7804139f0367898155992e6a2d9fbc4a";
    /**
     * 业务ID，易盾根据产品业务特点分配
     */
    private final static String BUSINESS_ID = "13d139bfe121499a9fb6cf18ed354c14";
    /**
     * 易盾身份认证服务身份证实人认证在线检测接口地址
     */
    private final static String API_URL = "https://verify.dun.163.com/v1/rp/check";


    /**
     * 实例化HttpClient，发送http请求使用，可根据需要自行调参
     */
    private static HttpClient httpClient = HttpClient4Utils.createHttpClient(100, 100, 5000, 2000, 2000);

    public static void main(String[] args) throws Exception {
        String name = "马梦娜";
        String cardno = "******************";
        String avatar = "";
        String dataid = UUID.randomUUID().toString();
        postCheck(SECRET_ID, SECRET_KEY, BUSINESS_ID, API_URL, name, cardno, avatar, dataid);
    }

    /**
     * 实人认证接口示例代码
     * 接口文档: https://support.dun.163.com/documents/391676076156063744?docId=391677733860962304
     *
     * @param secretId        产品密钥ID，产品标识
     * @param secretKey       产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露
     * @param businessId      业务ID，易盾根据产品业务特点分配
     * @param apiUrl          验证接口地址
     * @param name            姓名
     * @param cardNo          身份证号，字母大写
     * @param avatarBase64Str 头像base64串，小于180K，可支持PNG、JPG、BMP图片文件
     * @param dataId          随机跟踪id
     * @throws Exception
     */
    public static String postCheck(String secretId, String secretKey, String businessId, String apiUrl
            , String name, String cardNo, String avatarBase64Str, String dataId) throws Exception {

        log.info("人脸识别参数1："+cardNo+"    "+name +"    图片大小：" +avatarBase64Str.length());

        Map<String, String> params = new HashMap<>(12);
        // 1.设置公共参数
        params.put("secretId", secretId);
        params.put("businessId", businessId);
        params.put("version", "v1");
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("nonce", String.valueOf(new Random().nextInt()));
        params.put("signatureMethod", "MD5"); // 可选参数，可选值为MD5、SHA1、SHA256、SM3。如果不传该字段，默认采用MD5算法生成签名。

        // 2.设置私有参数
        params.put("name", name);
        params.put("cardNo", cardNo.toUpperCase());
        params.put("picType", "2");
        params.put("avatar", avatarBase64Str);
        params.put("dataId", dataId);
        params.put("callback", dataId);

        // 3.生成签名信息
        String signature = SignatureUtils.genSignature(secretKey, params);
        params.put("signature", signature);

//        // 4.1拼装请求参数
//        String postParamsStr = "";
//        Iterator<Map.Entry<String, String>> itr = params.entrySet().iterator();
//        while (itr.hasNext()) {
//            Map.Entry<String, String> entry = itr.next();
//            postParamsStr += String.format("&%s=%s", entry.getKey(), entry.getValue());
//        }
//        postParamsStr = postParamsStr.substring(1);

        // 4.2发送HTTP请求，这里使用的是HttpClient工具包，产品可自行选择自己熟悉的工具包发送请求
        // httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        String response = HttpClient4Utils.sendPost(httpClient,apiUrl, params);
        log.info("人脸识别结果1："+response);
        //5.解析报文返回
        JSONObject jObject = JSON.parseObject(response);


        System.out.printf("response=%s%n", jObject);
        int code =  StringUtilsT.cast(jObject.get("code"));// jObject.get("code") .getAsInt();
        if (code == 200) {
            JSONObject resultObject = JSON.parseObject(jObject.get("result").toString());
            int status = StringUtilsT.cast(resultObject.get("status"));
            String taskId = StringUtilsT.cast(resultObject.get("taskId"));
            if (status == 0) {
                System.out.printf("taskId=%s，姓名身份证认证结果待定，具体原因：%s%n", taskId, StringUtilsT.cast(resultObject.get("reasonType")));
            } else if (status == 1) {
                BigDecimal similarityScore = StringUtilsT.cast(resultObject.get("similarityScore"));
                System.out.printf("taskId=%s，姓名身份证认证通过, 头像相似度得分 = %s%n", taskId, similarityScore);
            } else if (status == 2) {
                int reasonType = StringUtilsT.cast(resultObject.get("reasonType"));
                System.out.printf("taskId=%s，姓名身份证认证不通过，不通过原因：%s%n", taskId, reasonType);
            } else {
                System.out.printf("taskId=%s，status=%s，具体见接口文档说明%n", taskId, status);
            }
        } else {
            System.out.printf("ERROR: response=%s%n", jObject);
        }
        return response;
    }
}
