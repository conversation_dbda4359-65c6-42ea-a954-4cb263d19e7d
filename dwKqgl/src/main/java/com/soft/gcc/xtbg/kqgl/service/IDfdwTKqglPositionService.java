package com.soft.gcc.xtbg.kqgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import com.yyszc.wpbase.ventity.PersonEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_position】的数据库操作Service
* @createDate 2023-05-25 13:29:39
*/
public interface IDfdwTKqglPositionService extends IService<DfdwTKqglPosition> {

    List<Groupitem> getGroupListAll(PersonEntity person);

    IPage<DfdwTKqglPosition> selectPage(IPage<DfdwTKqglPosition> page , DfdwTKqglPosition position,PersonEntity person);

    DfdwTKqglPosition selectPositionById(Integer id);


}
