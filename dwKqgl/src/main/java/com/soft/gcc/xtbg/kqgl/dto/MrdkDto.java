package com.soft.gcc.xtbg.kqgl.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 每日打卡信息
 * @date 2023/5/25 16:46:13
 */
@Data
public class MrdkDto {
    private Integer applyUser;
    private String applyUserName;
    /**
     * 打卡日期
     */
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String clockTime;

    private String type;

    //上下班打卡时间
    private String clockStartTime;
    private String clockEndTime;

    //手机号不匹配信息
    private Integer clockStartPhoneStatus;
    private Integer clockEndPhoneStatus;

    private String clockStartAddress;
    private String clockEndAddress;
    private Integer clockStartPlace;
    private Integer clockEndPlace;

    private Integer applyUserDept;
    private String applyUserDeptName;


    //部门确认信息
    private Integer bmrkUserId;
    private String bmrkUserName;
    private String bmrkNote;

    //上下班异地打卡说明
    private String clockStartPlaceStr;
    private String clockEndPlaceStr;
    private String clockStartPlaceNote;
    private String clockEndPlaceNote;

    //异常信息拼接
    private String errorInfo;



}
