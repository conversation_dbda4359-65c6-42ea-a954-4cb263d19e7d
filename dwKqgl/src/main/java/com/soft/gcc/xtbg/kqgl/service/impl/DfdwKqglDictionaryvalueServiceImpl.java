package com.soft.gcc.xtbg.kqgl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.kqgl.domain.DfdwKqglDictionaryvalue;
import com.soft.gcc.xtbg.kqgl.service.IDfdwKqglDictionaryvalueService;
import com.soft.gcc.xtbg.kqgl.mapper.DfdwKqglDictionaryvalueMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DictionaryValue】的数据库操作Service实现
* @createDate 2023-10-07 13:28:21
*/
@Service
public class DfdwKqglDictionaryvalueServiceImpl extends ServiceImpl<DfdwKqglDictionaryvalueMapper, DfdwKqglDictionaryvalue>
    implements IDfdwKqglDictionaryvalueService {

}




