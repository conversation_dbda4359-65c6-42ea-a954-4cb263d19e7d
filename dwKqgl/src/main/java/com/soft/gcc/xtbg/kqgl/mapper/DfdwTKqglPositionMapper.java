package com.soft.gcc.xtbg.kqgl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_position】的数据库操作Mapper
* @createDate 2023-05-25 13:29:39
* @Entity com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition
*/
public interface DfdwTKqglPositionMapper extends BaseMapper<DfdwTKqglPosition> {


    IPage<DfdwTKqglPosition> getPageList(IPage<DfdwTKqglPosition> page,@Param("type") String type,
                                         @Param("userName") String userName,
                                         @Param("companyAddress") String companyAddress,
                                         @Param("depList") List<Integer> integerList
    );

    DfdwTKqglPosition selectPositionById(@Param("type") String type,@Param("id") Integer id);

}




