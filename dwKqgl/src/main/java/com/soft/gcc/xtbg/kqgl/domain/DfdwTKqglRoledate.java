package com.soft.gcc.xtbg.kqgl.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 考勤管理-角色日期
* @TableName dfdw_t_kqgl_roledate
*/
@TableName(value ="dfdw_t_kqgl_roledate")
@Data
public class DfdwTKqglRoledate implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 用户id
    */
    @TableField(value = "userId")
    @JSONField(name = "userId")

    private Integer userId;
    /**
    * 角色日期
    */
    @TableField(value = "role_date")
    @JSONField(name = "roleDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date roleDate;
    /**
    * 单位id
    */
    @TableField(value = "group_id")
    @JSONField(name = "groupId")

    private Integer groupId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
