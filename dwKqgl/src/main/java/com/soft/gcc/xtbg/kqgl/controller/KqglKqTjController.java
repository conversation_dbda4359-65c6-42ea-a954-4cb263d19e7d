package com.soft.gcc.xtbg.kqgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.influxdb.client.WriteApi;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.InfluxDBUtil;
import com.soft.gcc.xtbg.kqgl.domain.DfdwKqglDictionaryvalue;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglCxsqLc;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import com.soft.gcc.xtbg.kqgl.dto.*;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglClockService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglCxsqLcService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglPositionService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwKqglDictionaryvalueService;
import com.soft.gcc.xtbg.kqgl.util.DateUtil;
import com.soft.gcc.xtbg.kqgl.util.KqglInfluxDBUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 考勤统计
 * @date 2023/5/25 14:11:53
 */
@RequestMapping("/kqtj")
@RestController
public class KqglKqTjController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(KqglKqTjController.class);
    @Autowired
    private IDfdwTKqglClockService clockService;
    @Autowired
    private IDfdwTKqglCxsqLcService cxsqLcService;
    @Autowired
    private IDfdwTKqglPositionService positionService;
    @Autowired
    private IDfdwKqglDictionaryvalueService dictionaryvalueService;


    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyy-MM");


    /**
     * 每月考勤统计
     *
     * @param ent
     * @return
     */
    @RequestMapping("/getListMytj")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    public Result<Object> getListMytj(@RequestBody MykqParm ent) {
        PersonEntity person = user();

        //分页
        Integer start = (ent.getPageNum() - 1) * ent.getPageSize();
        Integer end = ent.getPageNum() * ent.getPageSize();
        ent.setStart(start);
        ent.setEnd(end);

        List<MykqDto> records = clockService.getListMytj(ent, person);

        IPage<MykqDto> list = new Page<>();
        list.setCurrent(ent.getPageNum());
        list.setSize(ent.getPageSize());

        List<MykqDto> zbbList = new ArrayList<>();
        if (records.size() > 0) {
            int fromIndex = (ent.getPageNum() - 1) * ent.getPageSize();
            int toIndex = ent.getPageNum() * ent.getPageSize();
            if (toIndex > records.size()) {
                toIndex = records.size();
            }
            zbbList = records.subList(fromIndex, toIndex);
        }
        list.setRecords(zbbList);
        list.setTotal(records.size());

        return Result.ok(list);
    }


    /**
     * 考勤管理-pc-每日考勤
     *
     * @param ent
     * @return
     */
    @RequestMapping("/getListMrTj")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    public Result<Object> getListMrTj(@RequestBody MykqParm ent) {
        PersonEntity person = user();
        System.out.println(person);
        //分页
        Integer start = (ent.getPageNum() - 1) * ent.getPageSize();
        Integer end = ent.getPageNum() * ent.getPageSize();
        ent.setStart(start);
        ent.setEnd(end);

        List<MrdkDto> records = clockService.getListMrTj(ent, person);

        IPage<MrdkDto> list = new Page<>();
        list.setCurrent(ent.getPageNum());
        list.setSize(ent.getPageSize());

        List<MrdkDto> zbbList = new ArrayList<>();
        if (records.size() > 0) {
            int fromIndex = (ent.getPageNum() - 1) * ent.getPageSize();
            int toIndex = ent.getPageNum() * ent.getPageSize();
            if (toIndex > records.size()) {
                toIndex = records.size();
            }
            zbbList = records.subList(fromIndex, toIndex);
        }


        for (MrdkDto mrdkDto : zbbList) {
            String errorMsg = "";
            if (mrdkDto.getType().contains("0.9")) {
                errorMsg = "无";
                mrdkDto.setErrorInfo(errorMsg);
                continue;
            }
            if (mrdkDto.getType().contains("1")) {
                errorMsg += "旷工、";
            }
            if (mrdkDto.getType().contains("2")) {
                errorMsg += "迟到、";
            }
            if (mrdkDto.getType().contains("3")) {
                errorMsg += "早退、";
            }
            if (mrdkDto.getType().contains("4")) {
                errorMsg += "异地打卡、";
            }
            if (mrdkDto.getType().contains("5")) {
                errorMsg += "点位异常、";
            }
            if (mrdkDto.getType().contains("6")) {
                errorMsg += "缺卡、";
            }
            if ((mrdkDto.getClockStartPhoneStatus() != null && mrdkDto.getClockStartPhoneStatus() == 0) || (mrdkDto.getClockEndPhoneStatus() != null && mrdkDto.getClockEndPhoneStatus() == 0)) {
                errorMsg += "手机号不匹配、";
            }
            if (errorMsg == "") {
                errorMsg = "无";
            }
            if (errorMsg.endsWith("、")) {
                errorMsg = errorMsg.substring(0, errorMsg.length() - 1);
            }

            mrdkDto.setErrorInfo(errorMsg);
        }

        list.setRecords(zbbList);
        list.setTotal(records.size());

        return Result.ok(list);
    }

    /**
     * 考勤管理-pc-考勤详情
     *
     * @param applyUser
     * @param time
     * @return
     */
    @RequestMapping("/getClockInfo")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    public Result<Object> getClockInfo(@RequestParam(value = "applyUser") Integer applyUser,
                                       @RequestParam(value = "time") String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (applyUser.equals(0) || applyUser == null) {
            return Result.error("获取用户信息失败");
        }

        MrdkDto clock = clockService.getClockInfo(applyUser, time);
        return Result.ok(clock);
    }


    /**
     * 查询用户当天已审批的出行记录 (获取出行申请信息列表)
     *
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping({"/getTjList"})
    @ResponseBody
    public Result getTjList(@RequestParam(value = "userId") Integer userId,
                            @RequestParam(value = "date") String date) {

        String starTime = "";
        String endTime = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        starTime = date + " 00:00:00";
        endTime = date + " 23:59:59";
        Date stTime = null;
        Date edTime = null;
        try {
            stTime = sdf.parse(starTime);
            edTime = sdf.parse(endTime);

        } catch (ParseException ps) {
            return Result.error("时间转换异常");
        }

        // 根据状态查询申请记录
        List<DfdwTKqglCxsqLc> cclcList = cxsqLcService.list(new LambdaQueryWrapper<DfdwTKqglCxsqLc>()
                .eq(DfdwTKqglCxsqLc::getApplyUser, userId)
                .eq(DfdwTKqglCxsqLc::getApproveState, 2)  //默认已审批
                .ge(DfdwTKqglCxsqLc::getPlanDate, stTime)
                .le(DfdwTKqglCxsqLc::getPlanDate, edTime)
                .orderByDesc(DfdwTKqglCxsqLc::getCreateTime)
        );

        return Result.ok(cclcList);
    }

    /**
     * 获取人员轨迹列表
     *
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping(value = "/getUserHistoryLocationNew")
    @ResponseBody
    @ApiOperation(value = "getUserHistoryLocationNew", notes = "获取人员轨迹列表")
    public Result getUserHistoryLocation(@RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "date") String date) {
        Result r = clockService.getUserHistoryLocationNew(userId, date);
        return r;
    }

    /**
     * 获取考勤日历
     *
     * @param time
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping(value = "/getCalenDarList")
    @ResponseBody
    @ApiOperation(value = "getCalenDarList", notes = "获取考勤日历")
    public Result getCalenDarList(@RequestParam(value = "time") String time, @RequestParam(value = "applyUser") Integer applyUser) {

        if (applyUser.equals(0) || applyUser == null) {
            applyUser = user().getId();
        }

        List<CalendarDto> list = clockService.getCalenDarList(time, applyUser);

        return Result.ok(list);

    }

    /**
     * 考勤管理-轨迹信息
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     * @throws ParseException
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/getPolylinePoints")
    public Result<Object> getPolylinePoints(Integer userId, String startDate, String endDate) throws ParseException {
        if (userId == 0) {
            return Result.error(10001, "用户id不能为空");
        }

        int length = 16;
        if (startDate.length() == length) {
            startDate += ":00";
        }
        if (endDate.length() == length) {
            endDate += ":00";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date startDateValue = dateFormat.parse(startDate);
        Date endDateValue = dateFormat.parse(endDate);
        if ((endDateValue.getTime() - startDateValue.getTime()) > 604800000) {
            return Result.error(10001, "查询时间跨度不能超过七天");
        } else if (startDate.compareTo(endDate) > 0) {
            return Result.error(10001, "开始时间不能超过结束时间");
        }

        SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
        startDate = zdateFormat.format(startDateValue);
        endDate = zdateFormat.format(endDateValue);
        String query = String.format(
                "import \"influxdata/influxdb/schema\" "
                        + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                        + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                        + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                        + " and r[\"userId\"] == \"%s\")"
                        + " |> schema.fieldsAsCols() "
                        + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"uploadTime\",\"ywId\",\"accuracy\",\"type\",\"simInfo\"])"
                , InfluxDBUtil.bucket, startDate, endDate, "kqgl_new_position", userId);

        List<FluxTable> tables = null;
        try {
            tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
        } catch (Exception e) {
            InfluxDBUtil.reloadInfluxDBClinet();
        }

        ArrayList<Map> carLocationList = new ArrayList();
        if (tables != null) {
            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    HashMap map = new HashMap();
                    map.put("longitude", record.getValues().get("longitude"));
                    map.put("latitude", record.getValues().get("latitude"));
                    map.put("loc_time", zdateFormat.parse(record.getValues().get("_time").toString().substring(0, 19) + "Z").getTime());
                    carLocationList.add(map);
                }
            }
        }
        HashMap hashMap = new HashMap();
        hashMap.put("carLocationList", carLocationList);

        return Result.ok(hashMap);
    }

    @RequestMapping("/getDzccPoints")
    public Result<Object> getDzccPoints(String GPSNum, String startDate, String endDate) throws ParseException {
        SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));

        String query = String.format(
                "import \"influxdata/influxdb/schema\" "
                        + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                        + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                        + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                        + " and r[\"deviceId\"] == \"%s\")"
                        + " |> schema.fieldsAsCols() "
                        + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"speed\"])"
                , InfluxDBUtil.bucket, startDate, endDate, "vehicle_position", GPSNum);

        List<FluxTable> tables = null;
        try {
            tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
        } catch (Exception e) {
            InfluxDBUtil.reloadInfluxDBClinet();
            log.error("电子出车查询轨迹数据异常", e);
            return Result.error("获取轨迹数据出错" + e.getMessage());
        }

        ArrayList<HashMap<String, Object>> carLocationList = new ArrayList<>();
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("speed", record.getValues().get("speed"));
                map.put("longitude", record.getValues().get("longitude"));
                map.put("latitude", record.getValues().get("latitude"));
//                map.put("loc_time",record.getValues().get("_time").toString());
//                map.put("loc_time",zdateFormat.parse(record.getValues().get("_time").toString().substring(0,19)+"Z").getTime()/1000);
                map.put("loc_time", zdateFormat.parse(record.getValues().get("_time").toString().substring(0, 19) + "Z").getTime());
//                map.put("coord_type_input","bd09ll");
                carLocationList.add(map);
            }
        }
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("carLocationList", carLocationList);

        return Result.ok(hashMap);
    }

    /**
     * 考勤管理-pc-每月考勤导出
     *
     * @param ent
     * @param response
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/mytjDownLoad")
    public void mytjDownLoad(@RequestBody MykqParm ent, HttpServletResponse response) {
        PersonEntity person = user();
        try {
            //获取数据
            List<MykqDto> list = clockService.getListMytj(ent, person);


            String fileName = sdfMonth.format(sdf.parse(ent.getMonth())) + "打卡情况";

            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet(fileName);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(fileName);
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("姓名");
            row.createCell(1).setCellValue("部门");
            row.createCell(2).setCellValue("实际打卡天数");
            row.createCell(3).setCellValue("旷工");
            row.createCell(4).setCellValue("打卡异常天数");
            row.createCell(5).setCellValue("迟到");
            row.createCell(6).setCellValue("早退");
            row.createCell(7).setCellValue("异地打卡");
            row.createCell(8).setCellValue("点位数不够");
            row.createCell(9).setCellValue("手机号不匹配");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getRealName());
                row.createCell(1).setCellValue(list.get(i).getGroupname());
                row.createCell(2).setCellValue(list.get(i).getSjts());
                row.createCell(3).setCellValue(list.get(i).getKgts());
                row.createCell(4).setCellValue(list.get(i).getYcts());
                row.createCell(5).setCellValue(list.get(i).getCdts());
                row.createCell(6).setCellValue(list.get(i).getZtts());
                row.createCell(7).setCellValue(list.get(i).getYddkts());
                row.createCell(8).setCellValue(list.get(i).getDwycts());
                row.createCell(9).setCellValue(list.get(i).getSjhyc());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());
        } catch (Exception ex) {

        }

    }


    /**
     * 考勤管理-pc-每日考勤导出
     *
     * @param ent
     * @param response
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/mrTjDownLoad")
    public void mrTjDownLoad(@RequestBody MykqParm ent, HttpServletResponse response) {
        try {
            PersonEntity person = user();
            //分页

            List<MrdkDto> list = clockService.getListMrTj(ent, person);

            for (MrdkDto mrdkDto : list) {
                String errorMsg = "";
                if (mrdkDto.getType().contains("0.9")) {
                    errorMsg = "无";
                    mrdkDto.setErrorInfo(errorMsg);
                    continue;
                }
                if (mrdkDto.getType().contains("1")) {
                    errorMsg += "旷工、";
                }
                if (mrdkDto.getType().contains("2")) {
                    errorMsg += "迟到、";
                }
                if (mrdkDto.getType().contains("3")) {
                    errorMsg += "早退、";
                }
                if (mrdkDto.getType().contains("4")) {
                    errorMsg += "异地打卡、";
                }
                if (mrdkDto.getType().contains("5")) {
                    errorMsg += "点位异常、";
                }
                if (mrdkDto.getType().contains("6")) {
                    errorMsg += "缺卡、";
                }
                if ((mrdkDto.getClockStartPhoneStatus() != null && mrdkDto.getClockStartPhoneStatus() == 0) || (mrdkDto.getClockEndPhoneStatus() != null && mrdkDto.getClockEndPhoneStatus() == 0)) {
                    errorMsg += "手机号不匹配、";
                }
                if (errorMsg == "") {
                    errorMsg = "无";
                }
                if (errorMsg.endsWith("、")) {
                    errorMsg = errorMsg.substring(0, errorMsg.length() - 1);
                }

                mrdkDto.setErrorInfo(errorMsg);
            }

            String fileName = "";
            if (ent.getStartDate().equals(ent.getEndDate())) {
                fileName = ent.getStartDate().replace('-', '.') + "打卡情况";
            } else {
                fileName = ent.getStartDate().replace('-', '.') + "-" + ent.getEndDate().replace('-', '.') + "打卡情况";

            }

            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet(fileName);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中 titleStyle.getVerticalAlignmentEnum().CENTER
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(fileName);
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("姓名");
            row.createCell(1).setCellValue("部门");
            row.createCell(2).setCellValue("日期");
            row.createCell(3).setCellValue("上班打卡时间");
            row.createCell(4).setCellValue("上班是否异地打卡");
            row.createCell(5).setCellValue("上班异地打卡说明");
            row.createCell(6).setCellValue("下班打卡时间");
            row.createCell(7).setCellValue("下班是否异地打卡");
            row.createCell(8).setCellValue("下班异地打卡说明");
            row.createCell(9).setCellValue("异常情况");
            row.createCell(10).setCellValue("部门确认人");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getApplyUserName());
                row.createCell(1).setCellValue(list.get(i).getApplyUserDeptName());
                row.createCell(2).setCellValue(list.get(i).getClockTime());
                row.createCell(3).setCellValue(list.get(i).getClockStartTime());

                //转换
                if(list.get(i).getClockStartPlace() != null){
                    if (list.get(i).getClockStartPlace() == 1){
                        list.get(i).setClockStartPlaceStr("是");
                    }
                    if (list.get(i).getClockStartPlace() == 0){
                        list.get(i).setClockStartPlaceStr("否");
                    }
                }

                row.createCell(4).setCellValue(list.get(i).getClockStartPlaceStr());

                row.createCell(5).setCellValue(list.get(i).getClockStartPlaceNote());
                row.createCell(6).setCellValue(list.get(i).getClockEndTime());

                //转换
                if(list.get(i).getClockEndPlace() != null){
                    if (list.get(i).getClockEndPlace() == 1){
                        list.get(i).setClockEndPlaceStr("是");
                    }
                    if (list.get(i).getClockEndPlace() == 0){
                        list.get(i).setClockEndPlaceStr("否");
                    }
                }

                row.createCell(7).setCellValue(list.get(i).getClockEndPlaceStr());
                row.createCell(8).setCellValue(list.get(i).getClockEndPlaceNote());
                row.createCell(9).setCellValue(list.get(i).getErrorInfo());
                row.createCell(10).setCellValue(list.get(i).getBmrkUserName());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());


        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    /**
     * 考勤管理-pc-新增部门或个人办公地址
     *
     * @param ent
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addUserPoint")
    public Result<Object> addUserPoint(@RequestBody UserPointDto ent) {
        PersonEntity person = user();
        try {
            //判断当日是否存在打卡记录
            List<DfdwTKqglClock> clockList = clockService.list(new LambdaQueryWrapper<DfdwTKqglClock>().eq(DfdwTKqglClock::getApplyUser, ent.getUserId()).eq(DfdwTKqglClock::getClockTime, ent.getClockDate()));
            if (clockList.size() == 0) {
                return Result.error("未找到当日打卡数据");
            }
            //获取上下班时间

            //定位点采集，只采集8：30-11：30、13：30-17：00之间的轨迹信息
            //11:30 - 13:30 之间无需上传
            //判断当前时间是否在设置的时间点内 HH24小时制
            SimpleDateFormat sdfhms = new SimpleDateFormat("HH:mm:ss");

            //先获取字典设置
            List<DfdwKqglDictionaryvalue> dictionaryValueList = dictionaryvalueService.list(new LambdaQueryWrapper<DfdwKqglDictionaryvalue>()
                    .eq(DfdwKqglDictionaryvalue::getTitleID, "970226"));
            //上午上班时间
            String shangwu = dictionaryValueList.stream().filter(o -> o.getContent().equals("上午")).collect(Collectors.toList()).get(0).getParameter();
            //下午上班时间
            String xiawu = dictionaryValueList.stream().filter(o -> o.getContent().equals("下午")).collect(Collectors.toList()).get(0).getParameter();

            //上午下班时间
            String shangwuEnd = shangwu.split("\\|")[1];
            //下午上班时间
            String xiawuStar = xiawu.split("\\|")[0];

            //判断是否设置了个人的上下班时间
            List<DfdwTKqglPosition> list = positionService.list(new LambdaQueryWrapper<DfdwTKqglPosition>().eq(DfdwTKqglPosition::getUserId, ent.getUserId()));
            if (list != null && list.size() > 0) {
                //取第一条
                DfdwTKqglPosition position = list.get(0);
                shangwuEnd = StringUtil.isEmpty(position.getClockStartTime()) ? shangwu.split("\\|")[1] : position.getClockStartTime().split("\\|")[1];
                xiawuStar = StringUtil.isEmpty(position.getClockEndTime()) ? xiawu.split("\\|")[0] : position.getClockEndTime().split("\\|")[0];
            } else {
                //判断是否设置了部门的上下班时间
                List<DfdwTKqglPosition> groupList = positionService.list(new LambdaQueryWrapper<DfdwTKqglPosition>().eq(DfdwTKqglPosition::getGroupId, ent.getGroupId()));
                if (groupList != null && groupList.size() > 0) {
                    shangwuEnd = StringUtil.isEmpty(groupList.get(0).getClockStartTime()) ? shangwu.split("\\|")[1] : groupList.get(0).getClockStartTime().split("\\|")[1];
                    xiawuStar = StringUtil.isEmpty(groupList.get(0).getClockEndTime()) ? xiawu.split("\\|")[0] : groupList.get(0).getClockEndTime().split("\\|")[0];

                }
            }


            Date shangwuEndD = sdfhms.parse(shangwuEnd);
            Date xiawuStarD = sdfhms.parse(xiawuStar);


            //获取字典设置的定位点采集频率（毫秒）
            String cirTime = dictionaryValueList.stream().filter(o -> o.getContent().equals("自动采集点位频率")).collect(Collectors.toList()).get(0).getParameter();
            //转化为分钟
            long seconds = TimeUnit.MILLISECONDS.toMinutes(Long.valueOf(cirTime));

            //根据时间段 ，每5分钟生成一个定位点
            String start = ent.getClockDate() + " " + ent.getClockStartTime();
            String end = ent.getClockDate() + " " + ent.getClockEndTime();
            SimpleDateFormat sdfx = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startDate = DateUtil.asLocalDateTime(sdfx.parse(start));
            LocalDateTime endDate = DateUtil.asLocalDateTime(sdfx.parse(end));


            String lon = String.valueOf(ent.getLongitude());
            String lat = String.valueOf(ent.getLatitude());
            if (lon.length() > 8) {
                lon = lon.substring(0, 8);
            }
            if (lat.length() > 7) {
                lat = lat.substring(0, 7);
            }


            //当开始时间不大于结束时间，循环执行
            Integer size = 0;
            Integer otherSize = 0;
            ArrayList<Point> influxdbPoints = new ArrayList<>();
            Random random = new Random();
            while (!startDate.isAfter(endDate)) {

                String tempS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(startDate);
                String now = sdfhms.format(sdfx.parse(tempS));
                Date nowTime = sdfhms.parse(now);
                if (!isEffectiveDate(nowTime, shangwuEndD, xiawuStarD)) {
                    //经纬度随机最后2位，（保持一致，至保留6位小数） //0-100如果不足两位，前面补0
                    String lonNew = lon + String.format("%02d", random.nextInt(99));
                    String latNew = lat + String.format("%02d", random.nextInt(99));


                    System.out.println(startDate);
                    size++;
                    //当前时间不在11：30 - 13：00 区间
                    long time = startDate.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();  //毫秒级时间戳
                    Point point = Point
                            .measurement(KqglInfluxDBUtil.measurement)
                            .addTag("userId", ent.getUserId().toString())
                            .addField("ywId", clockList.get(0).getId())
                            .addField("latitude", Double.valueOf(latNew))
                            .addField("longitude", Double.valueOf(lonNew))
                            .addField("uploadTime", time)
                            .addField("type", "4")   //标识是接口上传点位
                            .addField("gpsUuid", "")
                            .addField("accuracy", "0")
                            //.addField("simInfo", "")
                            .addField("locationSource", "1")
                            .time(time, WritePrecision.MS);
                    influxdbPoints.add(point);
                } else {
                    otherSize++;
                }


                //日期+1，继续执行
                //随机生成1分半随机数 .plusSeconds(randomLong) 90秒内随机
                long min = 1;
                long max = 90;
                long randomLong = min + ((long) (random.nextDouble() * (max - min)));
                startDate = startDate.plusMinutes(seconds).plusSeconds(randomLong);
            }


            //插入时序库
            if (influxdbPoints.size() > 0) {
                try (WriteApi writeApi = KqglInfluxDBUtil.getInfluxDBClient().getWriteApi()) {
                    writeApi.writePoints(KqglInfluxDBUtil.bucket, KqglInfluxDBUtil.org, influxdbPoints);
                } catch (Exception e1) {
                    KqglInfluxDBUtil.reloadInfluxDBClinet();
                    return Result.error("增加点位信息出错");
                }
            } else {
                return Result.error("无可插入点位");
            }

            //发送短信提醒
//            String content = "考勤打卡执行点位插入操作：账号：" + person.getLoginName() + ",用户名：" + person.getRealName() + "，插入人员：" + ent.getUserName() + "，插入时间段：[" + start + "," + end + "]" + "，插入经纬度：[" + ent.getLongitude() + "," + ent.getLatitude() + "]";
//            clockService.sendSms("13586683463", content);

            return Result.ok("成功插入" + size + "个点位,不包含" + otherSize + "个午休点位");
        } catch (Exception ex) {
            ex.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Result.error("插入失败：" + ex.getMessage());
        }
    }


    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/deladdUserPoint")
    public Result<Object> deladdUserPoint(@RequestBody UserPointDto ent) {
        PersonEntity person = user();
        try {
            String startDate = ent.getClockDate() + " " + ent.getClockStartTime() + ":000";
            String endDate = ent.getClockDate() + " " + ent.getClockEndTime() + ":000";
            DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS");
            //删除两点之间的路径信息
            KqglInfluxDBUtil.getInfluxDBClient().getDeleteApi().delete(OffsetDateTime.of(LocalDateTime.parse(startDate, FORMATTER), OffsetDateTime.now().getOffset()),
                    OffsetDateTime.of(LocalDateTime.parse(endDate, FORMATTER), OffsetDateTime.now().getOffset())
                    , "userId=\"" + ent.getUserId() + "\" and _measurement=\"" + KqglInfluxDBUtil.measurement + "\"", KqglInfluxDBUtil.bucket, KqglInfluxDBUtil.org);

            //发送短信提醒
//            String content = "考勤打卡执行删除点位操作：账号：" + person.getLoginName() + ",用户名：" + person.getRealName() + "，删除人员：" + ent.getUserName() + "，删除时间段：[" + startDate + "," + endDate + "]";
//            clockService.sendSms("13586683463", content);
            return Result.ok("删除成功");
        } catch (Exception ex) {
            return Result.error("删除失败：" + ex.getMessage());
        }

    }


    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意三个参数的时间格式要一致
     *
     * @param nowTime
     * @param startTime
     * @param endTime
     * @return 在时间段内返回true，不在返回false
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        return date.after(begin) && date.before(end);
    }

    public static void main(String[] args) throws ParseException {
        Random random = new Random();
        String x = String.format("%02d", random.nextInt(10));//如果不足两位，前面补0
        String x2 = String.format("%02d", random.nextInt(99));//如果不足两位，前面补0
        String x3 = String.format("%02d", random.nextInt(99));//如果不足两位，前面补0
        String x4 = String.format("%02d", random.nextInt(99));//如果不足两位，前面补0
        System.out.println(x);
        System.out.println(x2);
        System.out.println(x3);
        System.out.println(x4);
//
//        String longitude = "121.5725";
//        String lat = "29.8538318";
//        System.out.println(longitude.length());
//        longitude = longitude.substring(0,8);
//        lat = lat.substring(0,7);
//        System.out.println(longitude);
//        System.out.println(lat);
//
//
//        Random random = new Random();
//        long min = 1;
//        long max = 90;
//        long randomLong = min + ((long) (random.nextDouble() * (max - min)));
//        System.out.println(randomLong);
//
//
//        long seconds = TimeUnit.MILLISECONDS.toMinutes(300000);
//        System.out.println(seconds);
//        String start = "2023-10-01 00:00:02";
//        String end = "2023-10-01 01:02:01";
//        SimpleDateFormat sdfx = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
//        SimpleDateFormat s = new SimpleDateFormat("HH:mm:ss");
//        String now = s.format(sdfx.parse(start));
//        Date xx = s.parse(now);
//        System.out.println(xx);
//        Date nowTime = s.parse(
//                "12:07:00");
//        System.out.println(nowTime);


//        LocalDateTime startDate = DateUtil.asLocalDateTime(sdfx.parse(start));
//        LocalDateTime endDate = DateUtil.asLocalDateTime(sdfx.parse(end));
//        //当开始时间不大于结束时间，循环执行
//        while (!startDate.isAfter(endDate)) {
//            System.out.println(startDate);
//            //日期+1，继续执行
//            startDate = startDate.plusMinutes(5).plusSeconds(70);
//        }

    }


}
