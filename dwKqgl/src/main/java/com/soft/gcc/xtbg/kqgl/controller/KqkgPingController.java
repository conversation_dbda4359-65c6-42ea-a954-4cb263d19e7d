package com.soft.gcc.xtbg.kqgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglActivityPing;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglUserPointSize;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglActivityPingService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglUserPointSizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/1 10:35:01
 */
@RequestMapping("/ping")
@RestController
public class KqkgPingController {
    @Autowired
    private IDfdwTKqglActivityPingService pingService;
    @Autowired
    private IDfdwTKqglUserPointSizeService userPointSizeService;
    @Autowired
    private RedisTemplate<String,String>  redisTemplate;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat ymd = new SimpleDateFormat("yyyyMMdd");


    /**
     * 考勤管理-pc-获取ping记录
     * @param ent
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/getList")
    public Result<Object> getList(@RequestBody DfdwTKqglActivityPing ent) {
        Map<String, Object> map = new HashMap<>();
        try {
            List<DfdwTKqglActivityPing> list = new ArrayList<>();

            String startTime = ent.getDate() + " 00:00:00";
            String endTime = ent.getDate() + " 23:29:29";
            //获取ping数据
            list = pingService.list(new LambdaQueryWrapper<DfdwTKqglActivityPing>()
                    .eq(DfdwTKqglActivityPing::getUid, ent.getUid())
                    .ge(DfdwTKqglActivityPing::getCreateTime, startTime)
                    .le(DfdwTKqglActivityPing::getCreateTime, endTime)
                    .orderByAsc(DfdwTKqglActivityPing::getCreateTime)
            );
            map.put("pingList", list);

            String currDate = sdf.format(new Date());
            if (ent.getDate().equals(currDate)) {
                map.put("isShow", 1);
                //获取数据库中存入的数量
                List<DfdwTKqglUserPointSize> userPointSizes = userPointSizeService.list(new LambdaQueryWrapper<DfdwTKqglUserPointSize>()
                        .eq(DfdwTKqglUserPointSize::getUserId, ent.getUid()));
                map.put("pointSize", userPointSizes.size());

                //获取redis中存在的数量
                String currentymd = ymd.format(sdf.parse(ent.getDate()));
                String kqgl_new_pointSizeKey = "kqgl_new_pointSize_" + ent.getUid() + "_" + currentymd;
                String sizeStr = redisTemplate.opsForValue().get(kqgl_new_pointSizeKey);
                if (sizeStr != null) {
                    Integer residsSize = Integer.valueOf(sizeStr.toString());
                    map.put("residsSize", residsSize);
                }else{
                    map.put("residsSize", -1);
                }
            } else {
                map.put("isShow", 0);
                map.put("pointSize",-1);
                map.put("residsSize", -1);
            }
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }

        return Result.ok(map);
    }

}
