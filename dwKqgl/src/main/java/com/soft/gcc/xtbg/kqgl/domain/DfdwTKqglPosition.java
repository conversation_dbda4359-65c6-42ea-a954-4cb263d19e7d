package com.soft.gcc.xtbg.kqgl.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.kqgl.dto.PageBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName dfdw_t_kqgl_position
*/
@TableName(value ="dfdw_t_kqgl_position")
@Data
public class DfdwTKqglPosition  extends PageBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 部门id
    */
    @TableField(value = "group_id")
    @JSONField(name = "groupId")
    private Integer groupId;
    /**
    * 经度
    */
    @TableField(value = "longitude")
    @JSONField(name = "longitude")
    private Double longitude;
    /**
    * 纬度
    */
    @TableField(value = "latitude")
    @JSONField(name = "latitude")
    private Double latitude;
    /**
    * 状态，1启用，0停用
    */
    @TableField(value = "status")
    @JSONField(name = "status")

    private String status;
    /**
    *
    */
    @TableField(value = "create_by")
    @JSONField(name = "createBy")
    private Long createBy;
    /**
    *
    */
    @TableField(value = "creation_date")
    @JSONField(name = "createDate")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
    *
    */
    @TableField(value = "last_update_by")
    @JSONField(name = "lastUpdateBy")
    private Long lastUpdateBy;
    /**
    *
    */
    @TableField(value = "last_update_date")
    @JSONField(name = "lastUpdateDate")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;
    /**
    * 详细地址
    */
    @TableField(value = "company_address")
    @JSONField(name = "companyAddress")
    private String companyAddress;
    /**
    * 单位id
    */
    @TableField(value = "top_group_Id")
    @JSONField(name = "topGroupId")
    private Integer topGroupId;
    /**
    * 用户id
    */
    @TableField(value = "user_id")
    @JSONField(name = "userId")
    private Integer userId;
    /**
    * 上班打卡时间
    */
    @TableField(value = "clock_start_time")
    @JSONField(name = "clockStartTime")
    private String clockStartTime;
    /**
    * 下班打卡时间
    */
    @TableField(value = "clock_end_time")
    @JSONField(name = "clockEndTime")
    private String clockEndTime;

    /**
     * 判断查部门还是人员  1 部门 2 人员
     */
    @TableField(exist = false)
    private String type;
    /**
     * \
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String deptName;
    @TableField(exist = false)
    private Integer deptId;
    @TableField(exist = false)
    private String groupName;

}
