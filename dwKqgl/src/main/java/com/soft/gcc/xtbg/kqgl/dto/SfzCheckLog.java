package com.soft.gcc.xtbg.kqgl.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 身份证人证核验-日志表
 */
@Data
public class SfzCheckLog implements Serializable {

    /**
     * 主键：id
     */

    @JSONField(name = "Id")
    private Integer Id;

    /**
     * 身份证号码
     */
    @JSONField(name = "certificateId")

    private String certificateId;
    /**
     * 姓名
     */
    @JSONField(name = "name")

    private String name;
    /**
     * 头像路径
     */
    @JSONField(name = "facePath")

    private String facePath;
    /**
     * 用户类型，默认为0分包模块用户
     */
    @JSONField(name = "userType")

    private String userType;
    /**
     * 核验时间
     */
    @JSONField(name = "checkTime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    /**
     * 数据标识，可传入用户标识等数据，便于数据查询
     */
    @JSONField(name = "dataId")

    private String dataId;
    /**
     * 返回结果：代码+说明
     */
    @JSONField(name = "resutMsg")

    private String resutMsg;
    /**
     * 姓名身份证号认证结果，1-通过 2-不通过（原因见reasonType) 0-待定
     */
    @JSONField(name = "sfzMatched")

    private Integer sfzMatched;
    /**
     * 权威数据源人脸照和身份证照片比对，1-同一个人 2-非同一人 0-不确定 （status为1时存在，该字段为1最终结果才通过）
     */
    @JSONField(name = "faceMatched")

    private Integer faceMatched;
    /**
     * 认证结果：2输入姓名和身份证号不一致；3查无此身份证；7其他错误；8人脸比对分数低于默认阈值；9库中无此身份证照片；10人像照过大180K；11人像照不合规；
     */
    @JSONField(name = "reasonType")

    private Integer reasonType;
    /**
     * 权威数据源人脸照与身份证头像的相似度得分，未检测为-1，正常检测取值范围为0-1，相似度越高，分值越大。默认判断阈值为0.851（faceMatched字段即采用此阈值，可自定义该阀值）
     */
    @JSONField(name = "similarityScore")

    private Double similarityScore;
    /**
     * 本次请求是否收费标识，1代表收费，0代表不收费
     */
    @JSONField(name = "isPayed")

    private Integer isPayed;

}
