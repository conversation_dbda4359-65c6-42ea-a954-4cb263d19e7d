package com.soft.gcc.xtbg.kqgl.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.groupitem.mapper.GroupitemMapper;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglClockMapper;
import com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglPositionMapper;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglPositionService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_position】的数据库操作Service实现
* @createDate 2023-05-25 13:29:39
*/
@Service
public class DfdwTKqglPositionServiceImpl extends ServiceImpl<DfdwTKqglPositionMapper, DfdwTKqglPosition>
    implements IDfdwTKqglPositionService{

    @Autowired
    private DfdwTKqglClockMapper clockMapper;
    @Autowired
    private GroupitemMapper groupitemMapper;
    @Override
    public List<Groupitem> getGroupListAll(PersonEntity person) {
        List<Integer> integerList = null;
        Integer dwRole = clockMapper.getRoleByPersonId(person.getId(), "协同办公-考勤管理-单位管理员");
        Integer bmRole = clockMapper.getRoleByPersonId(person.getId(), "协同办公-考勤管理-部门管理员");
        Integer userIdTemp = 0;
        List<Groupitem> groupList = new ArrayList<>();
        Groupitem groupitem = new Groupitem();
        groupitem.setId(-1);
        groupitem.setGroupname("全部");
        groupList.add(groupitem);

        if (dwRole > 0) {
            // 单位管理员 top获取用户单位及以下部门信息
            Integer groupTemp =  person.getTopGroupId();
            List<Groupitem> depListTemp = clockMapper.selectAllDep(groupTemp);
            groupList.addAll(depListTemp);

        } else if (bmRole > 0) {
            //部门管理员 获取传入的部门及以下所有部门
            List<Groupitem> depListTemp = clockMapper.selectAllDep(person.getGroupId());
            groupList.addAll(depListTemp);
        }else{
            Groupitem depListTemp =groupitemMapper.selectById(person.getGroupId());
            groupList.add(depListTemp);
        }
        return groupList;
    }

    @Override
    public IPage<DfdwTKqglPosition> selectPage(IPage<DfdwTKqglPosition> page, DfdwTKqglPosition position,PersonEntity person) {

        List<Integer> integerList = integerList(person, position.getGroupId());

        return  this.baseMapper.getPageList(page,position.getType(),position.getUserName(),position.getCompanyAddress(),integerList);
    }

    @Override
    public DfdwTKqglPosition selectPositionById(Integer id) {
        DfdwTKqglPosition kqglPosition = this.baseMapper.selectById(id);
        String type = "1";
        if (kqglPosition.getUserId() != null){
            type = "2";
        }
        DfdwTKqglPosition kqglPosition1 =this.baseMapper.selectPositionById(type,id);
        kqglPosition.setGroupName(kqglPosition1.getDeptName());
        kqglPosition.setDeptName(kqglPosition1.getDeptName());
        kqglPosition.setUserName(kqglPosition1.getUserName());
        return kqglPosition;
    }

    private List<Integer> integerList(PersonEntity person, Integer groupId) {
        List<Integer> integerList = new ArrayList<>();
        Integer dwRole = clockMapper.getRoleByPersonId(person.getId(), "协同办公-考勤管理-单位管理员");
        Integer bmRole = clockMapper.getRoleByPersonId(person.getId(), "协同办公-考勤管理-部门管理员");
        if (dwRole > 0) {
            // 单位管理员 top获取用户单位及以下部门信息
            Integer groupTemp = groupId == -1 ? person.getTopGroupId() : groupId;
            List<Groupitem> depList = clockMapper.selectAllDep(groupTemp);
            integerList = depList.stream().map(Groupitem::getId).distinct().collect(Collectors.toList());
        } else if (bmRole > 0) {
            //部门管理员 获取传入的部门及以下所有部门
            Integer groupTemp = groupId == -1 ? person.getGroupId() : groupId;
            List<Groupitem> depList = clockMapper.selectAllDep(groupTemp);
            integerList = depList.stream().map(Groupitem::getId).distinct().collect(Collectors.toList());
        } else {
            System.out.println(person.getGroupId());
            //只查看自己的
            integerList.add(person.getGroupId());
        }
        return integerList;
    }

}




