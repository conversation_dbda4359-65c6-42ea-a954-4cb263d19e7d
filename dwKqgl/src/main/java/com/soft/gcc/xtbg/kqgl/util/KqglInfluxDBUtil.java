package com.soft.gcc.xtbg.kqgl.util;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class KqglInfluxDBUtil {

    public static String ipAddress;

    public static String token;
    public static String bucket;
    public static String org;
    public static String measurement;

    private static InfluxDBClient influxDBClient = null;

    @Value("${kqglInfluxDBConfig.ipAddress}")
    public void setIpAddress(String ipAddress) {
        KqglInfluxDBUtil.ipAddress = ipAddress;
    }
    @Value("${kqglInfluxDBConfig.token}")
    public void setToken(String token) {
        KqglInfluxDBUtil.token = token;
    }
    @Value("${kqglInfluxDBConfig.bucket}")
    public void setBucket(String bucket) {
        KqglInfluxDBUtil.bucket = bucket;
    }
    @Value("${kqglInfluxDBConfig.org}")
    public void setOrg(String org) {
        KqglInfluxDBUtil.org = org;
    }
    @Value("${kqglInfluxDBConfig.measurement}")
    public void setMeasurement(String measurement) {
        KqglInfluxDBUtil.measurement = measurement;
    }

    //连接
    public static InfluxDBClient getInfluxDBClient(){
        if(influxDBClient==null) {
            influxDBClient = InfluxDBClientFactory.create(ipAddress, token.toCharArray());
        }
        return influxDBClient;
    }

    //重连
    public static void reloadInfluxDBClinet() {
        try {
            influxDBClient.close();
        } catch (Exception e1) {
        }
        influxDBClient = null;
        getInfluxDBClient();
    }

}
