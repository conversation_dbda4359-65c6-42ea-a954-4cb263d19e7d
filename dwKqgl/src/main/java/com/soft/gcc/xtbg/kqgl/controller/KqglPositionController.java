package com.soft.gcc.xtbg.kqgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.influxdb.query.FluxTable;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.service.impl.PersonServiceImpl;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.InfluxDBUtil;
import com.soft.gcc.xtbg.base.util.ParseUtil;

import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import com.soft.gcc.xtbg.kqgl.dto.MykqParm;
import com.soft.gcc.xtbg.kqgl.dto.UserPointDto;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglClockService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglPositionService;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 部门/个人定位点设置
 * @date 2023/5/25 13:33:50
 */
@RequestMapping("/position")
@RestController
public class KqglPositionController extends BaseController {

    @Autowired
    private IDfdwTKqglPositionService positionService;

    @Autowired
    private PersonServiceImpl personService;

    @Autowired
    private IDfdwTKqglClockService dfdwTKqglClockService;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


    /**
     * 考勤管理-pc-部门定位点
     * @param ent
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/getListBm")
    public Result<Object> getListBm(@RequestBody DfdwTKqglPosition ent) {
        IPage<DfdwTKqglPosition> list = new Page<>();
        list.setCurrent(ent.getPageNum());
        list.setSize(ent.getPageSize());

        if (StringUtil.isNullOrEmpty(ent.getType())){
            return Result.error("必传参数为空");
        }
        PersonEntity person = user();
        list = positionService.selectPage(list,ent,person);
        return Result.ok(list);
    }

    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/getPositionById")
    public Result<Object> getPositionById(Integer id){
        return Result.ok( positionService.selectPositionById(id));
    }


    /**
     * 考勤管理-pc-部门下拉
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/getGroupListAll")
    public Result<Object> getGroupListAll(){
        PersonEntity person  =user();
        List<Groupitem> groupitems =  positionService.getGroupListAll(person);
        return Result.ok(groupitems);
    }


    /**
     * 考勤管理-pc-新增部门或个人办公地址
     * @param ent
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addPosition")
    public Result<Object> addPosition(@RequestBody DfdwTKqglPosition ent){
        PersonEntity person  =user();
        ent.setCreateBy(Long.parseLong(person.getId()+""));
        ent.setCreateDate(new Date());
        ent.setStatus("1");

        LambdaQueryWrapper<DfdwTKqglPosition> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ent.getUserId() != null,DfdwTKqglPosition::getUserId,ent.getUserId());
        lambdaQueryWrapper.eq(ent.getGroupId() != null, DfdwTKqglPosition::getGroupId,ent.getGroupId());
        DfdwTKqglPosition ds = positionService.getOne(lambdaQueryWrapper);
        if (ds != null){
            String msg = "";
            msg += ent.getGroupName() == null?"":ent.getGroupName();
            msg += ent.getUserName() == null?"":ent.getUserName();

            return  Result.error(msg+"已经设置过上下班时间,不可重复添加");
        }

        return Result.ok(positionService.save(ent));
    }

    /**
     * 考勤管理-pc-编辑部门或个人办公地址
     * @param ent
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/editPosition")
    public Result<Object> editPosition(@RequestBody DfdwTKqglPosition ent){
        PersonEntity person  =user();
        ent.setLastUpdateBy(Long.parseLong(person.getId()+""));
        ent.setLastUpdateDate(new Date());
        ent.setStatus("1");
        //positionService.save(ent);
        return Result.ok(positionService.updateById(ent));
    }


    /**
     * 考勤管理-pc-删除部门或个人办公地址
     * @param map
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/delPosition")
    public Result<Object> delPosition(@RequestParam Map<String, String> map){
        PersonEntity person  =user();
        int id = ParseUtil.tryParseInt(map.get("id"));
        //positionService.removeByIds(ids);
        return Result.ok(positionService.removeById(id));
    }


    /***
     * 根据部门id 获取当前部门下面的所有用户
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/getDeptUsers")
    public Result<Object> getDeptUsers(@RequestParam Map<String, String> map){
        //Person person = user();
        if (map.get("deptId") == null){
            return Result.error("请选择部门");
        }
        QueryWrapper<Person> personQueryWrapper = new QueryWrapper<>();
        personQueryWrapper.lambda().eq(Person::getGroupid,map.get("deptId"));
        //personService.list(personQueryWrapper);
      return Result.ok(personService.list(personQueryWrapper));
    }


    /***
     * 判断当前用户上下班打卡时间是否和时序库中的打卡采集定位时间一致
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/dwppList")
    public Result<Object> dwppList(@RequestBody MykqParm ent){

        PersonEntity person = user();
        Result r =  dfdwTKqglClockService.dwppList(ent,person);
        return r;
    }


    /**
     * "考勤管理-pc-点位匹配导出
     * @param ent
     * @param response
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/dwppDownLoad")
    public void dwppDownLoad(@RequestBody MykqParm ent, HttpServletResponse response) {
        try{
            PersonEntity person = user();
            //分页

            List<DfdwTKqglClock> listOld = dfdwTKqglClockService.getDwppList(ent,person);
            List<DfdwTKqglClock> list = getNewList(listOld,ent);


            String fileName = ent.getDate() +"点位匹配信息";

            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet(fileName);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue(fileName);
            sheet.setDefaultColumnWidth(23);

            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("姓名");
            row.createCell(1).setCellValue("部门");
            row.createCell(2).setCellValue("日期");
            row.createCell(3).setCellValue("上班打卡时间");
            row.createCell(4).setCellValue("下班打卡时间");
            row.createCell(5).setCellValue("点位采集开始时间");
            row.createCell(6).setCellValue("点位采集结束时间");

            for (int i = 0; i < list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                row.createCell(0).setCellValue(list.get(i).getApplyUserName());
                row.createCell(1).setCellValue(list.get(i).getGroupName());
                row.createCell(2).setCellValue(sdf.format(list.get(i).getClockTime()));
                row.createCell(3).setCellValue(list.get(i).getClockStartTime());
                row.createCell(4).setCellValue(list.get(i).getClockEndTime());
                row.createCell(5).setCellValue(list.get(i).getInfluxStartTime());
                row.createCell(6).setCellValue(list.get(i).getInfluxEndTime());
                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" +fileName+ ".xlsx");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");

            workbook.write(response.getOutputStream());

        }catch (Exception ex){

        }

    }


    private   List<DfdwTKqglClock> getNewList(List<DfdwTKqglClock> listOld,MykqParm ent) throws ParseException {

        List<DfdwTKqglClock> listNew = new ArrayList<>();

        if (listOld!= null && listOld.size() > 0) {

            //每10个分段
            List<List<DfdwTKqglClock> >  result = new ArrayList<>();

            int  n = 10;
            int remainder = listOld.size() % n;//余数
            int size = (listOld.size() / n);//商 不算余数 要分多少组。有余数的话下面有单独处理余数数据的
            for (int i = 0; i < size; i++) {//循环要分多少组
                List<DfdwTKqglClock> subset = null;
                subset = listOld.subList(i * n, (i + 1) * n);//截取list
                result.add(subset);
            }
            if (remainder > 0) {//有余数的情况下把余数得到的数据再添加到list里面
                List<DfdwTKqglClock> subset = null;
                subset = listOld.subList(size * n, size * n + remainder);
                result.add(subset);
            }


            // 输出分割后的子list集合
            for (int k = 0; k < result.size(); k++) {
                List<DfdwTKqglClock> list = result.get(k);
                //获取用户id,不足10个补充
                List<Integer> userIds = list.stream().map(DfdwTKqglClock::getApplyUser).collect(Collectors.toList());
                if (userIds.size() < 10) {
                    Integer defaultId = 0;
                    Integer cha = 10 - userIds.size();
                    for (int i = 0; i < cha; i++) {
                        userIds.add(defaultId);
                        defaultId--;
                    }
                }


                //批量获取时序库数据
                String startDate = ent.getDate() + "T00:00:00";
                String endDate = ent.getDate() + "T23:59:59";
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                Date startDateValue = null;
                Date endDateValue = null;
                try {
                    startDateValue = dateFormat.parse(startDate);
                    endDateValue = dateFormat.parse(endDate);
                } catch (ParseException e) {
                }

                SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
                zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
                startDate = zdateFormat.format(startDateValue);
                endDate = zdateFormat.format(endDateValue);
                String query = String.format(
                        "import \"influxdata/influxdb/schema\" "
                                + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                                + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                                + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                                + " and r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\")"
                                + " |> schema.fieldsAsCols() "
                                + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"uploadTime\",\"ywId\",\"userId\"])"
                        , InfluxDBUtil.bucket, startDate, endDate, "kqgl_new_position", userIds.get(0), userIds.get(1), userIds.get(2), userIds.get(3), userIds.get(4), userIds.get(5), userIds.get(6), userIds.get(7), userIds.get(8), userIds.get(9));

                List<FluxTable> tables = null;
                try {
                    tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
                } catch (Exception e) {
                    InfluxDBUtil.reloadInfluxDBClinet();
                }

                for (FluxTable table : tables) {
                    String startTime = "";
                    String endTime = "";
                    Integer userId = 0;
                    if (table.getRecords().size() >= 0) {
                        userId = Integer.valueOf(table.getRecords().get(0).getValues().get("userId").toString()) ;
                        //第一个点
                        Long longTime = zdateFormat.parse(table.getRecords().get(0).getValues().get("_time").toString().substring(0, 19) + "Z").getTime();
                        startTime = new SimpleDateFormat("HH:mm:ss").format(longTime);
                    }
                    if (table.getRecords().size() >= 1) {
                        //最后一个点
                        Long longTime = zdateFormat.parse(table.getRecords().get(table.getRecords().size()-1).getValues().get("_time").toString().substring(0, 19) + "Z").getTime();
                        endTime = new SimpleDateFormat("HH:mm:ss").format(longTime);
                    }

                    //给list赋值
                    for(DfdwTKqglClock clock : list){
                        if(clock.getApplyUser().equals(userId)){
                            clock.setInfluxStartTime(startTime);
                            clock.setInfluxEndTime(endTime);
                        }

                    }
                    listNew.addAll(list);
                }

            }

        }

        return listNew;
    }


    public static void main(String[] args) {
        // 原始List集合
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        list.add(4);
        list.add(5);
        list.add(6);
        list.add(7);
        list.add(8);
        list.add(9);
        list.add(10);



        List<List<Integer> >  result = new ArrayList<>();

        int  n = 10;
        int remainder = list.size() % n;//余数
        int size = (list.size() / n);//商 不算余数 要分多少组。有余数的话下面有单独处理余数数据的
        for (int i = 0; i < size; i++) {//循环要分多少组
            List<Integer> subset = null;
            subset = list.subList(i * n, (i + 1) * n);//截取list
            result.add(subset);
        }
        if (remainder > 0) {//有余数的情况下把余数得到的数据再添加到list里面
            List<Integer> subset = null;
            subset = list.subList(size * n, size * n + remainder);
            result.add(subset);
        }

// 输出分割后的子list集合
        for (int i = 0; i < result.size(); i++) {
            System.out.println("Sub-list " + (i+1) + ": " + result.get(i));
        }
    }






}
