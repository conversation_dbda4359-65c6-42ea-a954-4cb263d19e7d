package com.soft.gcc.xtbg.kqgl.service;

import com.soft.gcc.xtbg.kqgl.dto.SfzCheckLog;

/**
 * <AUTHOR>
 * @description
 * @date 2024-07-25 14:22:18
 */
public interface ISfzCheckService {

    /**
     * 提交身份证认证
     * @param cardNo 身份证号码
     * @param name 姓名
     * @param faceBase64 头像 BASE64格式，小于180K
     * @param userType 用户标签
     * @return
     */
    public SfzCheckLog sfzCheck(String cardNo, String name, String faceBase64, String userType);

}
