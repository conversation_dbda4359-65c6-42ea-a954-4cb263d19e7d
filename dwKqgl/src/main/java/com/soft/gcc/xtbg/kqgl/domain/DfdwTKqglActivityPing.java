package com.soft.gcc.xtbg.kqgl.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.kqgl.dto.PageBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
*
* @TableName dfdw_t_kqgl_activity_ping
*/
@TableName(value ="dfdw_t_kqgl_activity_ping")
@Data
public class DfdwTKqglActivityPing extends PageBaseEntity implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * uuid
    */
    @TableField(value = "uuid")
    @JSONField(name = "uuid")

    private String uuid;
    /**
    * 用户登录名
    */
    @TableField(value = "login_name")
    @JSONField(name = "loginName")
    private String loginName;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @JSONField(name = "createTime")
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * uid
    */
    @TableField(value = "uid")
    @JSONField(name = "uid")

    private Integer uid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String date;
}
