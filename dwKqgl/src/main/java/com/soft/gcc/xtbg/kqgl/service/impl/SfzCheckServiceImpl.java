package com.soft.gcc.xtbg.kqgl.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.soft.gcc.xtbg.kqgl.dto.SfzCheckLog;
import com.soft.gcc.xtbg.kqgl.service.ISfzCheckService;
import com.soft.gcc.xtbg.kqgl.util.IdCardUtil;
import com.soft.gcc.xtbg.kqgl.util.RealPersonCheck;
import com.soft.gcc.xtbg.kqgl.util.StringUtilsT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2024-07-25 14:24:02
 */
@Service
public class SfzCheckServiceImpl  implements ISfzCheckService {
    private static final Logger log = LoggerFactory.getLogger(SfzCheckServiceImpl.class);
    @Value("${neteaseconfig.secretId}")
    private String SecretId;
    @Value("${neteaseconfig.secretKey}")
    private String SecretKey;
    @Value("${neteaseconfig.realperson.businessId}")
    private String BusinessId;
    @Value("${neteaseconfig.realperson.url}")
    private String ApiUrl;

    @Value("${neteaseconfig.liveperson.businessId}")
    private String liveId;
    @Value("${neteaseconfig.liveperson.url}")
    private String liveUrl;


    @Override
    public SfzCheckLog sfzCheck(String cardNo, String name, String faceBase64, String userType) {
        if(StringUtilsT.isNotEmpty(faceBase64) && faceBase64.startsWith("data:")) {
            faceBase64 =faceBase64.substring(faceBase64.indexOf(",")+1);
        }
        //用于提交给百度的实际头头像
        String realFaceBase64 = faceBase64;
        if(StringUtilsT.isEmpty(faceBase64)){
            //为兼容二要素
            realFaceBase64 = "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";
        }
        if(StringUtilsT.isEmpty(userType)){
            userType = "fb";
        }


        if(StringUtilsT.isNotEmpty(cardNo)){
            cardNo = cardNo.toUpperCase();
        }

        SfzCheckLog checkResult = new SfzCheckLog();
        checkResult.setCertificateId(cardNo);
        checkResult.setName(name);
        checkResult.setCheckTime(new Date());
        checkResult.setUserType(userType);
        checkResult.setSfzMatched(2);

        if(IdCardUtil.isIdcard(cardNo)== false){
            checkResult.setResutMsg("身份证号不合法");
        }



        String dataid = UUID.randomUUID().toString();
        checkResult.setDataId(dataid);
        try {
            log.info("人脸识别参数："+cardNo+"    "+name+"    图片大小：" +realFaceBase64.length());
            String checkstr = RealPersonCheck.postCheck(SecretId,SecretKey,BusinessId,ApiUrl,name,cardNo,realFaceBase64,dataid+"("+cardNo+")");
            log.info("人脸识别结果："+cardNo+"    "+checkstr);
            JSONObject checkJson = JSON.parseObject(checkstr);
            int code = StringUtilsT.cast(checkJson.get("code"));
            String msg = checkJson.get("msg").toString();
            checkResult.setResutMsg(String.format("%d,%s",code,msg));
            if (code == 200) {
                checkResult = formatSfzCheckLog(checkResult,checkJson,code,msg);
            }
            else
            {
                checkResult.setReasonType(code);
            }

        } catch (Exception e) {
//            e.printStackTrace();
            log.error("人脸识别报错 "+e.getMessage());
        }

        return checkResult;
    }

    private SfzCheckLog formatSfzCheckLog(SfzCheckLog checkResult,JSONObject checkJson,int code,String msg)
    {
        JSONObject resultJson = JSON.parseObject(checkJson.get("result").toString());
        String taskId = StringUtilsT.cast(resultJson.get("taskId"));
        //status 姓名身份证号认证结果，1-通过 2-不通过（原因见reasonType) 0-待定
        int status = StringUtilsT.cast(resultJson.get("status"));
        checkResult.setSfzMatched(status);
        //faceMatched 权威数据源人脸照和身份证照片比对，1-同一个人 2-非同一人 0-不确定 （status为1时存在，该字段为1最终结果才通过）
        int faceMatched = StringUtilsT.cast(resultJson.get("faceMatched"));
        checkResult.setFaceMatched(faceMatched);
        //similarityScore 权威数据源人脸照与身份证头像的相似度得分，未检测为-1，正常检测取值范围为0-1，相似度越高，分值越大。默认判断阈值为0.851（faceMatched字段即采用此阈值，可自定义该阀值）
        BigDecimal similarityScore = StringUtilsT.cast(resultJson.get("similarityScore"));
        checkResult.setSimilarityScore(similarityScore.doubleValue());
        //isPayed 本次请求是否收费标识，1代表收费，0代表不收费
        int isPayed = StringUtilsT.cast(resultJson.get("isPayed"));
        checkResult.setIsPayed(isPayed);
        if (status == 0 || status == 2) {
            //未通过（2-不通过，0-待定），见reasonType
            int reasonType = StringUtilsT.cast(resultJson.get("reasonType"));
            checkResult.setReasonType(reasonType);
            checkResult.setResutMsg(getResonTypeValue(reasonType));
        } else if (status == 1) {
            checkResult.setResutMsg(String.format("%d,%s",code,msg));
        } else {
            checkResult.setResutMsg(String.format("taskId=%s，status=%s，具体见接口文档说明", taskId, status));
        }
        return checkResult;
    }

    private String getResonTypeValue(int reasonType){
        switch (reasonType){
            case 2:
                return "2:输入姓名和身份证号不一致:可能用户的信息有误或假信息，建议用户确认后重新操作";
            case 3:
                return "3:查无此身份证:可能是户口更名、户口迁移、军人等特殊状态导致，建议人工介入";
            case 7:
                return "7:其他错误:调用失败，请联系客服处理";
            case 8:
                return "8:人脸比对分数低于默认阈值:特殊场景下，建议您根据similarityScore字段自定义阈值";
            case 9:
                return "9:库中无此身份证照片:可能是权威库数据问题导致，若是初次办理身份证，同步时间可能较长，常见于18岁以下的用户。建议客服介入";
            case 10:
                return "10:人像照过大:BASE64限制为大小180K，请压缩图片后上传";
            case 11:
                return "11:人像照不合规:若照片中出现多人脸、非活体脸等特殊情况会返回此项。该项表明用户照片存在恶意破解行为，建议人工复审";
            default:
                return reasonType+":未知原因:联系客服";
        }
    }
}
