package com.soft.gcc.xtbg.kqgl.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglWorkday;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglWorkdayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 节假日设置
 */
@RequestMapping("/workDay")
@RestController
public class KqglWorkdayController extends BaseController {
    private final Logger log = LoggerFactory.getLogger(KqglWorkdayController.class);


    @Resource
    public IDfdwTKqglWorkdayService dfdwTKqglWorkdayService;


    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @RequestMapping("/list")
    public Result<Object> workDayList(@RequestBody DfdwTKqglWorkday workday){
        return Result.ok(dfdwTKqglWorkdayService.selectPage(workday));
    }

    /**
     * 新增
     * @param workday
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @PostMapping("/add")
    public Result<Object> addWorkDay(@RequestBody DfdwTKqglWorkday workday){
        return Result.ok(dfdwTKqglWorkdayService.addWorkDay(workday));
    }

    /**
     * 删除
     * @param
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/delWorkDay/{id}")
    public Result<Object> removeWorkDay(@PathVariable("id") Integer id) {
        return Result.ok(dfdwTKqglWorkdayService.removeById(id));
    }

    /**
     * 修改
     * @param workday
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @Transactional(rollbackFor = Exception.class)
    @PutMapping
    public Result<Object> updateWorkDay(@RequestBody DfdwTKqglWorkday workday){
        //校验是否存在相同的数据
        Integer count = dfdwTKqglWorkdayService.count(new LambdaQueryWrapper<DfdwTKqglWorkday>()
                .ne(DfdwTKqglWorkday::getId,workday.getId())
                .eq(DfdwTKqglWorkday::getDate,workday.getDate())
        );
        if (count > 0){
            return Result.error("当前日期已经存在！");
        }
        return Result.ok(dfdwTKqglWorkdayService.updateById(workday));
    }


    /**
     * 查看详情
     * @param id
     * @return
     */
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-打卡,协同办公-考勤管理-部门管理员,协同办公-考勤管理-统计,协同办公-考勤管理-单位管理员')")
    @GetMapping(value = "/{id}")
    public Result<Object> workDayInfo(@PathVariable("id") Integer id){
        return Result.ok(dfdwTKqglWorkdayService.getById(id));
    }

    /**
     * 执行获取工作日
     * @return
     */
    @GetMapping(value = "/runGetWorkDay")
    public Result<Object> runGetWorkDay(){
        try {
            //获取当前时间
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            QueryWrapper<DfdwTKqglWorkday> queryWrapper=new QueryWrapper<>();
            queryWrapper.eq("status",2).eq("year",year);
            List<DfdwTKqglWorkday> hisList = dfdwTKqglWorkdayService.list(queryWrapper);

            RestTemplate restTemplate = new RestTemplate();
            //节假日-放假时间 网址 http://www.apihubs.cn/
            String url11 = "https://api.apihubs.cn/holiday/get?api_key=defc1c5a0890604b3d184dc13792ab11ff3c&field=date,week,holiday&year="+year+"&holiday=22,11,88,44,55,66&cn=1&size=366";
            //节假日-调休工作时间
            String url22 = "https://api.apihubs.cn/holiday/get?api_key=defc1c5a0890604b3d184dc13792ab11ff3c&field=date,week&year="+year+"&week=6,7&workday=1&cn=1&size=366";
            List<DfdwTKqglWorkday> workdayList0 = dfdwTKqglWorkdayService.httpApihubs(restTemplate,url11,2);
            List<DfdwTKqglWorkday> workdayList1 = dfdwTKqglWorkdayService.httpApihubs(restTemplate, url22,1);

            log.info("获取今年节假日返回数据(放假时间)==="+ JSON.toJSONString(workdayList0));
            log.info("获取今年节假日返回数据(调休工作时间)==="+ JSON.toJSONString(workdayList1));

            List<DfdwTKqglWorkday> workdayList = new ArrayList<>();
            if (hisList.size() == 0){
                workdayList.addAll(workdayList0);
                workdayList.addAll( workdayList1);
            }
            if (hisList.size() != 0){
                for (int i = 0; i < workdayList0.size(); i++) {
                    DfdwTKqglWorkday dw = workdayList0.get(i);
                    DfdwTKqglWorkday hisEntity = hisList.stream().filter(p -> p.getDate()== dw.getDate()).findFirst().orElse(null);
                    if (hisEntity != null){
                        dw.setId(hisEntity.getId());
                        workdayList.add(dw);
                    }else {
                        workdayList.add(dw);
                    }

                }
                for (int i = 0; i < workdayList1.size(); i++) {
                    DfdwTKqglWorkday dw = workdayList1.get(i);
                    DfdwTKqglWorkday hisEntity = hisList.stream().filter(p -> p.getDate()== dw.getDate()).findFirst().orElse(null);
                    if (hisEntity != null){
                        dw.setId(hisEntity.getId());
                        workdayList.add(dw);
                    }else {
                        workdayList.add(dw);
                    }
                }
            }

            for (int i = 0; i < workdayList.size(); i++) {
                DfdwTKqglWorkday wd = workdayList.get(i);
                if (wd.getId() != null){
                    dfdwTKqglWorkdayService.updateById(wd);
                }else {
                    dfdwTKqglWorkdayService.save(wd);
                }
            }

        }catch (Exception e){
            return Result.error("执行【获取今年节假日】任务报错，错误信息："+e.getMessage());
        }
        return Result.ok();
    }

}
