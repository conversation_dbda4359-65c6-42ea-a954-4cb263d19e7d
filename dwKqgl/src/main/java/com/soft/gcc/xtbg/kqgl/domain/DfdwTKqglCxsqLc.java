package com.soft.gcc.xtbg.kqgl.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
* <AUTHOR>
* 考勤管理-出行申请
* @TableName dfdw_t_kqgl_cxsq_lc
*/
@TableName(value ="dfdw_t_kqgl_cxsq_lc")
@Data
public class DfdwTKqglCxsqLc implements Serializable {

    /**
    * 主键：
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    * 编号
    */
    @TableField(value = "applyNo")
    @JSONField(name = "applyNo")

    private String applyNo;
    /**
    * 申请人Id
    */
    @TableField(value = "applyUser")
    @JSONField(name = "applyUser")

    private Integer applyUser;
    /**
    * 申请人名称
    */
    @TableField(value = "applyUserName")
    @JSONField(name = "applyUserName")

    private String applyUserName;
    /**
    * 申请人部门Id
    */
    @TableField(value = "applyUserDeptId")
    @JSONField(name = "applyUserDeptId")

    private Integer applyUserDeptId;
    /**
    * 出行日期
    */
    @TableField(value = "planDate")
    @JSONField(name = "planDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planDate;
    /**
    * 开始时间
    */
    @TableField(value = "openTime")
    @JSONField(name = "openTime")
    private String openTime;
    /**
    * 结束时间
    */
    @TableField(value = "endTime")
    @JSONField(name = "endTime")

    private String endTime;
    /**
    * 出行时长
    */
    @TableField(value = "travelHour")
    @JSONField(name = "travelHour")
    private Double travelHour;
    /**
    * 出行事由
    */
    @TableField(value = "note")
    @JSONField(name = "note")
    private String note;
    /**
    * 创建时间（申请时间）
    */
    @TableField(value = "createTime")
    @JSONField(name = "createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
    * 审批状态(0未提交，1审批中，2已审批，3已驳回)
    */
    @TableField(value = "approveState")
    @JSONField(name = "approveState")
    private Integer approveState;
    /**
    * 审批人Id
    */
    @TableField(value = "approveUserId")
    @JSONField(name = "approveUserId")
    private Integer approveUserId;
    /**
    * 审批人名称
    */
    @TableField(value = "approveName")
    @JSONField(name = "approveName")
    private String approveName;
    /**
    * 申请部门名称
    */
    @TableField(value = "applyUserDeptName")
    @JSONField(name = "applyUserDeptName")
    private String applyUserDeptName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
