package com.soft.gcc.xtbg.kqgl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock;
import com.soft.gcc.xtbg.kqgl.dto.MrdkDto;
import com.soft.gcc.xtbg.kqgl.dto.MykqDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【dfdw_t_kqgl_clock(考勤管理-考勤列表)】的数据库操作Mapper
 * @createDate 2023-05-25 13:20:50
 * @Entity com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock
 */
public interface DfdwTKqglClockMapper extends BaseMapper<DfdwTKqglClock> {

    List<Groupitem> selectAllDep(@Param("depId") Integer depId);

    Integer getRoleByPersonId(@Param("personId") Integer personId, @Param("roleName") String roleName);

    List<MykqDto> getListMytj(@Param("startMonth") String startMonth, @Param("endMonth") String endMonth,
                              @Param("start") Integer start, @Param("end") Integer end,
                              @Param("depList") List <Integer> depList, @Param("userName") String userName);

    List<MrdkDto> getListMrTj(@Param("startDate")  String startDate, @Param("endDate")  String endDate, @Param("start")  Integer start, @Param("end")  Integer end,
                              @Param("depList") List<Integer> integerList, @Param("userName")  String userName,
                              @Param("applyUser") Integer applyUser,@Param("type") Integer type);

    List<DfdwTKqglClock> getMonthListById(@Param("applyUser")  Integer applyUser, @Param("timeStart")  Date timeStart, @Param("timeEnd")  Date timeEnd,
                                          @Param("sbsj") String sbsj, @Param("xbsj")  String xbsj);


    List<DfdwTKqglClock> dwppList(@Param("date") String date,@Param("userName") String userName,
                                  @Param("depList") List<Integer> integerList);

    Integer sendSms(@Param("phone")String phone,@Param("content") String content);
}




