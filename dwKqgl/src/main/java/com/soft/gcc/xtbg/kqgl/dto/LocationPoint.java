package com.soft.gcc.xtbg.kqgl.dto;


//import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

@Data
public class LocationPoint {
    @Data
    public static class Address {
        private String country;
        private String province;
        private String city;
        private String district;
        private String street;
        private String streetNum;
        private String poiName;
        private String cityCode;
    }

    private String userId;
    private double latitude;
    private double longitude;
    //定位时间
    private Long time;
    //上传时间
    private Long uploadTime;
    private String addresses;
    private Address address;
    private MobileInfo mobileInfo;
    private SimInfo simInfo;
    private String gpsUuid;
    /**
     * 定位类型 1 上班打卡/  2 抓取定位  3下班卡
     */
    private String type;


    /**
     *  位置准确度
     */
    private String accuracy;
    private String isxt;
    private String imsi;

    /**
     * 手机信息
     */
    @Data
    public static class MobileInfo {
        private String userInfoPhoneNumber;
        private String phoneNumber;
        private String imei;
        private String imsi;
        private String gpsUuid;
        private String isxt;
        private String xytTime;
    }

    @Data
    public static class SimInfo{
        private String simStateDeviceId;
        private String simOperatorName;
        private String line1Number;
        private String subscriberId;
        private String cellLocationAllCellInfo;
        private String cid;
        private String lac;
        private String mnc;
        private String mcc;

    }

}


