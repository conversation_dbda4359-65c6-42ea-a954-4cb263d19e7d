package com.soft.gcc.xtbg.kqgl.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
*
* @TableName dfdw_t_kqgl_user_point_size
*/
@TableName(value ="dfdw_t_kqgl_user_point_size")
@Data
public class DfdwTKqglUserPointSize implements Serializable {

    /**
    * 主键：id
    */

    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;

    /**
    *
    */
    @TableField(value = "user_id")
    @JSONField(name = "userId")
    private Integer userId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
