package com.soft.gcc.xtbg.kqgl.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.ParseUtil;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglWorkday;
import com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglWorkdayMapper;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglWorkdayService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_workday(考勤管理-工作日设置)】的数据库操作Service实现
* @createDate 2023-05-29 09:30:46
*/
@Service
public class IDfdwTKqglWorkdayServiceImpl extends ServiceImpl<DfdwTKqglWorkdayMapper, DfdwTKqglWorkday>
    implements IDfdwTKqglWorkdayService {


    @Override
    public IPage<DfdwTKqglWorkday> selectPage(DfdwTKqglWorkday workday) {
        try{

            QueryWrapper<DfdwTKqglWorkday> queryWrapper = new QueryWrapper<>();
            //只查询指定字段
            //queryWrapper.select("Id","LoginName","RealName","Sphone","Telephone");

//            queryWrapper.lambda().like(StringUtils.isNotEmpty(workday.getYear()),DfdwTKqglWorkday::getRealname,realName);
//            queryWrapper.lambda().like(!"".equals(loginName) ,DfdwTKqglWorkday::getLoginname,loginName);
            queryWrapper.lambda().eq(workday.getStatus() != null,DfdwTKqglWorkday::getStatus,workday.getStatus());
            queryWrapper.lambda().eq(workday.getYear() != null,DfdwTKqglWorkday::getYear,workday.getYear());
            queryWrapper.lambda().eq(workday.getMonth() != null,DfdwTKqglWorkday::getMonth,workday.getMonth());
            queryWrapper.lambda().between((workday.getBeginDate() !=null && workday.getOverDate() != null),DfdwTKqglWorkday::getDate,workday.getBeginDate(),workday.getOverDate());
            queryWrapper.lambda().orderByDesc(DfdwTKqglWorkday::getYear);
            queryWrapper.lambda().orderByDesc(DfdwTKqglWorkday::getDate);
            return this.baseMapper.selectPage(new Page<>(workday.getPageNum(),workday.getPageSize()),queryWrapper);

        }catch (Exception ex){
            String s = "程序产生异常:" + ex.getMessage() + "!";
            throw new RuntimeException(s);
            //return Result.error(s);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addWorkDay(DfdwTKqglWorkday workday) {
        //校验 当前日期是否已经存在
        Integer count = this.count(new LambdaQueryWrapper<DfdwTKqglWorkday>()
                .eq(DfdwTKqglWorkday::getDate,workday.getDate())
                .eq(DfdwTKqglWorkday::getYear,workday.getYear())
        );
        if (count >0){
            throw new RuntimeException("当前日期已经存在,请勿重复添加！");
        }
        //获取当前日期的年份
        workday.setYear(workday.getDate().getYear()+1900);
        //获取当前日期的日期
        workday.setDay(workday.getDate().getDay());
        //获取当前日期月份
        workday.setMonth(workday.getDate().getMonth());
        //判断当前日期是星期几
        workday.setWeek(dateToWeek(workday.getDate()));

        return this.save(workday) ? 1 : 0;
    }

    /**
     * 日期转 星期
     * @param date
     * @return
     */
    public Integer dateToWeek(Date date){
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        String dateStr = sdf.format(date);
        String[] weekDays = {"7","1","2","3","4","5","6"};
        Calendar cal = Calendar.getInstance(); // 获得一个日历
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
        if (w < 0){
            w = 0;
        }
        return Integer.valueOf(weekDays[w]);
    }


    @Override
    public List<DfdwTKqglWorkday> httpApihubs(RestTemplate restTemplate, String url, Integer status) throws Exception {
        ResponseEntity<JSONObject> responseEntity = restTemplate.getForEntity(url, JSONObject.class);
        JSONObject obj = responseEntity.getBody();
        JSONObject data = obj.getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        if (list == null){
            throw new RuntimeException("暂时未请求到数据，请稍后再试");
        }
        List<DfdwTKqglWorkday> workdayList=new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            DfdwTKqglWorkday workday = new DfdwTKqglWorkday();
            JSONObject jsonObject = list.getJSONObject(i);
            Integer week = jsonObject.getInt("week");
            String date = jsonObject.getStr("date");
            workday.setStatus(status);
            workday.setWeek(week);
            workday.setYear(Integer.valueOf(date.substring(0,4)));
            workday.setMonth(Integer.valueOf(date.substring(4,6)));
            workday.setDay(Integer.valueOf(date.substring(6,8)));
            StringBuffer stringBuffer=new StringBuffer(date);
            stringBuffer.insert(4,"-");
            stringBuffer.insert(7,"-");
            Date newDate = new SimpleDateFormat("yyyy-MM-dd").parse(stringBuffer.toString());
            workday.setDate(newDate);
            workdayList.add(workday);
        }
        return workdayList;
    }
}




