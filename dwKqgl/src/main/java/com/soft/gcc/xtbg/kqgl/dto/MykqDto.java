package com.soft.gcc.xtbg.kqgl.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/25 14:29:20
 */
@Data
public class MykqDto  {
    private Integer userId;
    private String realName;
    private String groupId;
    private String groupname;

    //实际应打卡天数
    private Integer sjts;
    //打卡天数
    private Integer dkts;
    //异常天数
    private Integer ycts;
    //旷工
    private Integer kgts;
    //迟到
    private Integer cdts;
    //早退
    private Integer ztts;
    //异地打卡
    private Integer yddkts;
    //点位异常
    private Integer dwycts;
    //手机号不匹配异常
    private Integer sjhyc;
}
