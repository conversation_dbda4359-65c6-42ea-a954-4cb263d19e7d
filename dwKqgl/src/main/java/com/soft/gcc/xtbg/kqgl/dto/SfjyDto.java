package com.soft.gcc.xtbg.kqgl.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024-07-25 15:15:13
 */
@Data
public class SfjyDto {
    private String userName;
    private String cardNo;
    /**
     * 姓名身份证号认证结果，1-通过 2-不通过（原因见reasonType) 0-待定
     */
    private Integer sfzMatched;
    /**
     * 返回结果：代码+说明
     */
    private String resutMsg;
   // private SfzCheckLog sfzCheckLog;

}
