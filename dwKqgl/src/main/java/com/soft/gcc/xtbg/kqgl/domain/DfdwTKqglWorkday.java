package com.soft.gcc.xtbg.kqgl.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.kqgl.dto.PageBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考勤管理-工作日设置
 * @TableName dfdw_t_kqgl_workday
 */
@TableName(value ="dfdw_t_kqgl_workday")
@Data
public class DfdwTKqglWorkday  extends PageBaseEntity implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 月份
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 是否是工作日（0休息日 1工作日 2节假日）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 星期
     */
    @TableField(value = "week")
    private Integer week;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "date")
    private Date date;

    /**
     * 日
     */
    @TableField(value = "day")
    private Integer day;

    /**
     * 年
     */
    @TableField(value = "year")
    private Integer year;

    @TableField(exist = false)
    private List<String> dates;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
