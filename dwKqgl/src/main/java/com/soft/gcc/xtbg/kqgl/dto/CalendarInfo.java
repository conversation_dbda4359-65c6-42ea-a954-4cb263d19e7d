package com.soft.gcc.xtbg.kqgl.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 日历 详细信息
 * @date 2022/6/6 13:50:28
 */
@Data
public class CalendarInfo {
    //日期
    private Integer day;
    //当日在岗情况说明
    private String dayInfo;
    //年月日
    private String date;
    //是否有打卡记录
    private Integer haveClock;

    public CalendarInfo(Integer day, String dayInfo,String date,Integer haveClock) {
        this.day = day;
        this.dayInfo = dayInfo;
        this.date = date;
        this.haveClock = haveClock;
    }

    public CalendarInfo() {
    }
}
