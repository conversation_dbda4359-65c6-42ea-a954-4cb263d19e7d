package com.soft.gcc.xtbg.kqgl.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2023-10-07 10:21:38
 */
@Data
public class UserPointDto {
    /**
     * 部门id
     */
    private Integer groupId;
    private String groupName;
    private String deptName;

    private String unitGPS;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 详细地址
     */
    private String companyAddress;

    /**
     * 用户id
     */
    private Integer userId;
    private String userName;


    private String clockDate;
    /**
     * 开始时间
     */
    private String clockStartTime;
    /**
     * 结束时间
     */
    private String clockEndTime;


}
