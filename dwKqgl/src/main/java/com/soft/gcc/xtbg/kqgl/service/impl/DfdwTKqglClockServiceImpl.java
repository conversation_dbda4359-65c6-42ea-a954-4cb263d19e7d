package com.soft.gcc.xtbg.kqgl.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.influxdb.query.FluxTable;
import com.soft.gcc.common.dictionary_value.service.impl.DictionaryvalueServiceImpl;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.person.service.impl.PersonServiceImpl;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.base.util.InfluxDBUtil;

import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglClock;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglPosition;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglWorkday;
import com.soft.gcc.xtbg.kqgl.dto.*;
import com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglClockMapper;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglClockService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglPositionService;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglWorkdayService;

import com.soft.gcc.common.dictionary_value.entity.Dictionaryvalue;
import com.soft.gcc.common.person.entity.Person;
import com.yyszc.wpbase.ventity.PersonEntity;
import io.netty.util.internal.StringUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【dfdw_t_kqgl_clock(考勤管理-考勤列表)】的数据库操作Service实现
 * @createDate 2023-05-25 13:20:50
 */
@Service
public class DfdwTKqglClockServiceImpl extends ServiceImpl<DfdwTKqglClockMapper, DfdwTKqglClock>
        implements IDfdwTKqglClockService {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    @Autowired
    private PersonServiceImpl personService;
    @Autowired
    private DictionaryvalueServiceImpl dictionaryValueService;
    @Autowired
    private IDfdwTKqglPositionService iKqglPositionService;
    @Autowired
    private IDfdwTKqglWorkdayService workdayService;

    /**
     * 每月考勤统计
     *
     * @param ent
     * @return
     */
    @Override
    public List<MykqDto> getListMytj(MykqParm ent, PersonEntity person) {
        try {
            String startMonth = ent.getMonth();  //月份第一天
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(startMonth));
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));  //本月最后一天

            String endMonth = sdf.format(calendar.getTime());  //月份最后一天

            List<Integer> integerList = integerList(person, ent.getGroupId());

            List<MykqDto> list = baseMapper.getListMytj(startMonth, endMonth, ent.getStart(), ent.getEnd(), integerList, ent.getUserName());
            return list;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private List<Integer> integerList(PersonEntity person, Integer groupId) {
        List<Integer> integerList = new ArrayList<>();
        Integer dwRole = baseMapper.getRoleByPersonId(person.getId(), "协同办公-考勤管理-单位管理员");
        Integer bmRole = baseMapper.getRoleByPersonId(person.getId(), "协同办公-考勤管理-部门管理员");
        if (dwRole > 0) {
            // 单位管理员 top获取用户单位及以下部门信息
            Integer groupTemp = groupId == -1 ? person.getTopGroupId() : groupId;
            List<Groupitem> depList = baseMapper.selectAllDep(groupTemp);
            integerList = depList.stream().map(Groupitem::getId).distinct().collect(Collectors.toList());
        } else if (bmRole > 0) {
            //部门管理员 获取传入的部门及以下所有部门
            Integer groupTemp = groupId == -1 ? person.getGroupId() : groupId;
            List<Groupitem> depList = baseMapper.selectAllDep(groupTemp);
            integerList = depList.stream().map(Groupitem::getId).distinct().collect(Collectors.toList());
        } else {
            System.out.println(person.getGroupId());
            //只查看自己的
            integerList.add(person.getGroupId());
        }
        return integerList;
    }

    /**
     * 每日考勤统计
     *
     * @param ent
     * @param person
     * @return
     */
    @Override
    public List<MrdkDto> getListMrTj(MykqParm ent, PersonEntity person) {
        List<Integer> integerList = integerList(person, ent.getGroupId());
        List<MrdkDto> list = baseMapper.getListMrTj(ent.getStartDate(),ent.getEndDate(), ent.getStart(), ent.getEnd(), integerList, ent.getUserName(), 0,ent.getType());
        return list;
    }


    /**
     * 获取用户指定日期的打卡详情
     *
     * @param applyUser
     * @param time
     * @return
     */
    @Override
    public MrdkDto getClockInfo(Integer applyUser, String time) {
        List<MrdkDto> list = baseMapper.getListMrTj(time, time,null, null, null, "", applyUser,-1);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 点位匹配
     *
     * @return
     */
    @Override
    public Result getClickList(Map<String, String> param) {
        //时间 。。。 部门。。。。姓名
        QueryWrapper<DfdwTKqglClock> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(!StringUtil.isNullOrEmpty(param.get("userName")), DfdwTKqglClock::getApplyUserName, param.get("userName"));
        queryWrapper.lambda().eq(!StringUtil.isNullOrEmpty(param.get("groupId")), DfdwTKqglClock::getApplyUserDept, param.get("groupId"));
        queryWrapper.lambda().ge(!StringUtil.isNullOrEmpty(param.get("clockTimeStar")), DfdwTKqglClock::getClockTime, param.get("clockTimeStar"));
        queryWrapper.lambda().le(!StringUtil.isNullOrEmpty(param.get("clockTimeEnd")), DfdwTKqglClock::getClockTime, param.get("clockTimeEnd"));
        queryWrapper.lambda().orderByDesc(DfdwTKqglClock::getClockTime);

        IPage<DfdwTKqglClock> list = new Page<>();
        list.setCurrent(Long.parseLong(param.get("pageNum")));
        list.setSize(Long.parseLong(param.get("pageSize")));

        list = this.page(list, queryWrapper);
        try {
            checkTime(list.getRecords().get(0).getClockTime().toString(), "", "");

        } catch (Exception e) {

        }

        for (int i = 0; i < list.getRecords().size(); i++) {
            //查询时序库 获取最早点和最晚点   type = 1 上班卡   type =3 下班卡

            Date clockTime = list.getRecords().get(i).getClockTime();


        }


        return null;
    }

    @Override
    public List<CalendarDto> getCalenDarList(String time, Integer applyUser) {
        List<CalendarDto> list = new ArrayList<>();
        List<DfdwTKqglClock> clockList = new ArrayList<>(); //存放本人本月打卡记录
        List<DfdwTKqglWorkday> workdayList = new ArrayList<>(); //存放该月的工作节假日设置
        //修改日历的指定时间
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdf.parse(time));

            //获取这个月的总天数
            int totalDays = cal.getActualMaximum(Calendar.DATE);
            //获得传来时间的该月第一天和最后一天
            String timeMon = time.substring(0, time.length() - 2);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            // 往前补几天
            cal.add(Calendar.DATE, cal.get(Calendar.DAY_OF_WEEK) > 1 ? (2 - cal.get(Calendar.DAY_OF_WEEK)) : 6);
            Date timeStart = cal.getTime();
            // 往后补几天
            cal.add(Calendar.DATE, 41);
            Date timeEnd = cal.getTime();
            //获取本人本月打卡记录
            // 查询上下时间
            //获取字典
            Person person = personService.getById(applyUser);
            List<Dictionaryvalue> dictionaryValueList1 = dictionaryValueService.list(new LambdaQueryWrapper<Dictionaryvalue>()
                    .eq(Dictionaryvalue::getTitleid, "970226"));
            //新增校验  允许在一定时间内 重复上班打卡。下班打卡只允许一次
            String morning = dictionaryValueList1.stream().filter(o -> "上午".equals(o.getContent())).collect(Collectors.toList()).get(0).getParameter();
            String afternoon = dictionaryValueList1.stream().filter(o -> "下午".equals(o.getContent())).collect(Collectors.toList()).get(0).getParameter();
            //得到上班时间
            String sbsj = morning.split("\\|")[0];
            String xbsj = afternoon.split("\\|")[1];
            //获取单独设置的上班时间
            //2023-5-16新增  部分人员或者部门可以提前下班或者晚下班 此时定位需要继续采集
            QueryWrapper<DfdwTKqglPosition> positionQueryWrapper = new QueryWrapper<>();
            positionQueryWrapper.lambda()
                    .eq(DfdwTKqglPosition::getUserId, person.getId())
                    .orderByDesc(DfdwTKqglPosition::getId);
            List<DfdwTKqglPosition> kqglPositionList = iKqglPositionService.list(positionQueryWrapper);
            if (kqglPositionList != null && kqglPositionList.size() != 0) {
                //取第一条
                DfdwTKqglPosition position = kqglPositionList.get(0);
                sbsj = StringUtils.isEmpty(position.getClockStartTime()) ? sbsj : position.getClockStartTime().split("\\|")[0];
                xbsj = StringUtils.isEmpty(position.getClockEndTime()) ? xbsj : position.getClockEndTime().split("\\|")[0];
            } else {
                //取部门的上班时间
                QueryWrapper<DfdwTKqglPosition> positionQueryWrapper2 = new QueryWrapper<>();
                positionQueryWrapper2.lambda()
                        .eq(DfdwTKqglPosition::getGroupId, person.getGroupid())
                        .orderByDesc(DfdwTKqglPosition::getId);
                List<DfdwTKqglPosition> groupList = iKqglPositionService.list(positionQueryWrapper);
                if (groupList != null && groupList.size() != 0) {
                    sbsj = StringUtils.isEmpty(groupList.get(0).getClockStartTime()) ? sbsj : groupList.get(0).getClockStartTime().split("\\|")[0];
                    xbsj = StringUtils.isEmpty(groupList.get(0).getClockEndTime()) ? xbsj : groupList.get(0).getClockEndTime().split("\\|")[0];
                }
            }
            clockList = baseMapper.getMonthListById(applyUser, timeStart, timeEnd, sbsj, xbsj);
            //获取设置的本月工作日节假日设置的记录
            workdayList = workdayService.list(
                    new QueryWrapper<DfdwTKqglWorkday>().lambda()
                            .ge(DfdwTKqglWorkday::getDate, timeStart)
                            .le(DfdwTKqglWorkday::getDate, timeEnd)
            );
            //设置日期到第一天
            cal.setTime(sdf.parse(time));
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.add(Calendar.DATE, cal.get(Calendar.DAY_OF_WEEK) > 1 ? (2 - cal.get(Calendar.DAY_OF_WEEK)) : 6);
            CalendarDto calendarDto = new CalendarDto();
            int num = 0;
            String date = "";
            Date dateNowTime = sdf.parse(sdf.format(new Date()));
            for (int i = 1; i <= 42; i++) {
                date = sdf.format(cal.getTime());
                Date dateDayTime = sdf.parse(date);
                num++;
                CalendarInfo calendarInfo = new CalendarInfo(cal.get(Calendar.DAY_OF_MONTH), "", "", 0);

                if (num % 7 == 1) {
                    calendarDto = new CalendarDto();
                    calendarInfo.setDayInfo("在岗");
                    CalendarInfo calendarInfoNew = workDay(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    calendarDto.setMonday(calendarInfoNew);
                    //获取当天情况
                } else if (num % 7 == 2) {
                    calendarInfo.setDayInfo("在岗");
                    CalendarInfo calendarInfoNew = workDay(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    calendarDto.setTuesday(calendarInfoNew);
                } else if (num % 7 == 3) {
                    calendarInfo.setDayInfo("在岗");
                    CalendarInfo calendarInfoNew = workDay(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    calendarDto.setWednesday(calendarInfoNew);
                } else if (num % 7 == 4) {
                    calendarInfo.setDayInfo("在岗");
                    CalendarInfo calendarInfoNew = workDay(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    calendarDto.setThursday(calendarInfoNew);
                } else if (num % 7 == 5) {
                    calendarInfo.setDayInfo("在岗");
                    CalendarInfo calendarInfoNew = workDay(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    calendarDto.setFriday(calendarInfoNew);
                } else if (num % 7 == 6) {
                    calendarInfo.setDayInfo("公休");
                    List<DfdwTKqglClock> satOnDuty = baseMapper.selectList(
                            new QueryWrapper<DfdwTKqglClock>().lambda()
                                    .eq(DfdwTKqglClock::getApplyUser, applyUser)
                                    .eq(DfdwTKqglClock::getClockStartStatus, 1)
                                    .eq(DfdwTKqglClock::getClockTime, date)
                                    //.eq(Clock::getClockEndStatus,1)
                                    // .eq(Clock::getIsDelete,0)
                                    .ge(DfdwTKqglClock::getClockTime, timeStart)
                                    .le(DfdwTKqglClock::getClockTime, timeEnd)
                    );

                    CalendarInfo calendarInfoNew = weekend(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    if (satOnDuty.size() > 0) {
                        calendarInfoNew.setDayInfo("在岗");
                        calendarInfoNew.setHaveClock(1);
                    }
                    calendarDto.setSaturday(calendarInfoNew);
                } else if (num % 7 == 0) {
                    calendarInfo.setDayInfo("公休");
                    List<DfdwTKqglClock> sunOnDuty = baseMapper.selectList(
                            new QueryWrapper<DfdwTKqglClock>().lambda()
                                    .eq(DfdwTKqglClock::getApplyUser, applyUser)
                                    .eq(DfdwTKqglClock::getClockStartStatus, 1)
                                    .eq(DfdwTKqglClock::getClockTime, date)
                                    //.eq(Clock::getClockEndStatus,1)
                                    // .eq(Clock::getIsDelete,0)
                                    .ge(DfdwTKqglClock::getClockTime, timeStart)
                                    .le(DfdwTKqglClock::getClockTime, timeEnd)
                    );

                    CalendarInfo calendarInfoNew = weekend(calendarInfo, date, clockList, workdayList, sdf);
                    if (dateDayTime.compareTo(dateNowTime) > 0 && ifLeave(calendarInfoNew.getDayInfo())) {
                        calendarInfoNew.setDayInfo("");
                    }
                    if (sunOnDuty.size() > 0) {
                        calendarInfoNew.setDayInfo("在岗");
                        calendarInfoNew.setHaveClock(1);
                    }
                    calendarDto.setSunday(calendarInfoNew);
                    list.add(calendarDto);
//                    System.out.println();
                }

                if (i == totalDays && num % 7 != 0) {
                    calendarInfo = new CalendarInfo(0, "", "", 0);
                    //解决结尾非本月数据
                    if (num % 7 == 1) {
                        //补充 2，3，4，5，6,6
                        calendarDto.setTuesday(calendarInfo);
                        calendarDto.setWednesday(calendarInfo);
                        calendarDto.setThursday(calendarInfo);
                        calendarDto.setFriday(calendarInfo);
                        calendarDto.setSaturday(calendarInfo);
                        calendarDto.setSunday(calendarInfo);
                    } else if (num % 7 == 2) {
                        //补充 3，4，5，6,7
                        calendarDto.setWednesday(calendarInfo);
                        calendarDto.setThursday(calendarInfo);
                        calendarDto.setFriday(calendarInfo);
                        calendarDto.setSaturday(calendarInfo);
                        calendarDto.setSunday(calendarInfo);
                    } else if (num % 7 == 3) {
                        //补充 4，5，6,7
                        calendarDto.setThursday(calendarInfo);
                        calendarDto.setFriday(calendarInfo);
                        calendarDto.setSaturday(calendarInfo);
                        calendarDto.setSunday(calendarInfo);
                    } else if (num % 7 == 4) {
                        //补充 5，6,7
                        calendarDto.setFriday(calendarInfo);
                        calendarDto.setSaturday(calendarInfo);
                        calendarDto.setSunday(calendarInfo);
                    } else if (num % 7 == 5) {
                        //补充 6,7
                        calendarDto.setSaturday(calendarInfo);
                        calendarDto.setSunday(calendarInfo);
                    } else if (num % 7 == 6) {
                        //补充 7
                        calendarDto.setSunday(calendarInfo);
                    }
                    list.add(calendarDto);
                }
                cal.add(Calendar.DATE, 1);
            }


        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }


    public Result checkTime(String date, String type, String userId) {
        date = "2023-05-25";
        type = "~/^1|3$/";
        userId = "~/^16702|29471$/";
//        String  startDate =date+ " 00:00:00";
//        String  endDate = date+ " 23:59:59";
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
//        Date startDateValue = null;
//        Date endDateValue = null;
//        try {
//            startDateValue = dateFormat.parse(startDate);
//            endDateValue = dateFormat.parse(endDate);
//        } catch (ParseException e) {
//            return Result.error("时间格式错误");
//        }


        SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
//        startDate = zdateFormat.format(startDateValue);
//        endDate = zdateFormat.format(endDateValue);

        String startDate = "2023-05-25T00:00:43Z";
        String endDate = "2023-05-25T23:00:43Z";


        String query = String.format(
                "import \"influxdata/influxdb/schema\" "
                        + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                        + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                        + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                        + " and r[\"userId\"] == \"%s\""
                        + " and r[\"type\"] == \"%s\")"
                        + " |> schema.fieldsAsCols() "
                        + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"uploadTime\",\"ywId\",\"accuracy\",\"type\"])"
                , InfluxDBUtil.bucket, startDate, endDate, "kqgl_new_position", userId, type);

        List<FluxTable> tables = null;
        try {
            tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
        } catch (Exception e) {
            InfluxDBUtil.reloadInfluxDBClinet();
            return Result.error("获取点位信息出错");
        }
        for (FluxTable table : tables) {
            if (table.getRecords().size() != 0) {
                int index = table.getRecords().size() - 1;
                try {
                    Long longTime = zdateFormat.parse(table.getRecords().get(index).getValues().get("_time").toString().substring(0, 19) + "Z").getTime();
                    String time = new SimpleDateFormat("HH:mm:ss").format(longTime);
                } catch (ParseException ex) {

                }


            }
        }

        return null;

    }


    /**
     * 获取人员定位点数据
     *
     * @param userId
     * @param date
     * @return
     */
    @Override
    public Result getUserHistoryLocationNew(Integer userId, String date) {

        String startDate = date + "T00:00:00";
        String endDate = date + "T23:59:59";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date startDateValue = null;
        Date endDateValue = null;
        try {
            startDateValue = dateFormat.parse(startDate);
            endDateValue = dateFormat.parse(endDate);
        } catch (ParseException e) {
            return Result.error("时间格式错误");
        }

        if ((endDateValue.getTime() - startDateValue.getTime()) > 604800000) {
            return Result.error("查询时间跨度不能超过七天");
        } else if (startDate.compareTo(endDate) > 0) {
            return Result.error("开始时间不能超过结束时间");
        }

        SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
        startDate = zdateFormat.format(startDateValue);
        endDate = zdateFormat.format(endDateValue);
        String query = String.format(
                "import \"influxdata/influxdb/schema\" "
                        + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                        + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                        + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                        + " and r[\"userId\"] == \"%s\")"
                        + " |> schema.fieldsAsCols() "
                        + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"uploadTime\",\"ywId\",\"accuracy\",\"type\",\"simInfo\"])"
                , InfluxDBUtil.bucket, startDate, endDate, "kqgl_new_position", userId);

        List<FluxTable> tables = null;
        try {
            tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
        } catch (Exception e) {
            InfluxDBUtil.reloadInfluxDBClinet();
            return Result.error("获取点位信息出错");
        }

        //获取当前用户信息
        QueryWrapper<Person> personQueryWrapper = new QueryWrapper();
        personQueryWrapper.lambda().eq(Person::getId, userId);
        Person person = personService.getOne(personQueryWrapper);
        if (person == null) {
            return Result.error("未查询到用户信息！");
        }

        ArrayList<Map> locationList = new ArrayList();


        Integer reportCount = 0;
        for (FluxTable table : tables) {

            reportCount = table.getRecords().size();
            for (int i = 0; i < table.getRecords().size(); i++) {
                HashMap map = new HashMap();
                String uploadTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(table.getRecords().get(i).getValues().get("uploadTime"));
                map.put("uploadTime", uploadTime);
                map.put("longitude", table.getRecords().get(i).getValues().get("longitude"));
                map.put("latitude", table.getRecords().get(i).getValues().get("latitude"));
                map.put("ywId", table.getRecords().get(i).getValues().get("ywId"));
                map.put("type", table.getRecords().get(i).getValues().get("type"));
                map.put("accuracy", table.getRecords().get(i).getValues().get("accuracy"));
                try {
                    Long longTime = zdateFormat.parse(table.getRecords().get(i).getValues().get("_time").toString().substring(0, 19) + "Z").getTime();
                    map.put("loc_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(longTime));
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                //* 轨迹列表界面，根据采集时间正序展示， ok
                //* 如果定位点正常展示绿“√”，定位点异常展示红“x”
                //* 并展示异常情况最早点与最晚点，检测时间与打卡范围    其它点，检测手机号是否匹配
                //  ios 取到的手机号 带 +86
                LocationPoint.SimInfo simInfo =null;
                if( table.getRecords().get(i).getValues().get("simInfo") != null){
                    String simInfoStr = table.getRecords().get(i).getValues().get("simInfo").toString();
                     simInfo = JSON.parseObject(simInfoStr, LocationPoint.SimInfo.class);
                }

                map.put("isAbnormal", 0);
                String abnormalMsg = "";
                map.put("abnormalMsg", abnormalMsg);
                //校验 是否是异常打卡
                if (simInfo == null) {
                    //abnormalMsg = abnormalMsg +"未获取到sim卡信息,";
//                    map.put("isAbnormal",1);
                    map.put("Line1Number", "");
                    map.put("Telephone", person.getTelephone());

                } else {
                    map.put("Line1Number", simInfo.getLine1Number());
                    map.put("Telephone", person.getTelephone());

                }

                if (abnormalMsg != "") {
                    abnormalMsg = abnormalMsg.substring(0, abnormalMsg.length() - 1);
                }

                map.put("abnormalMsg", abnormalMsg);

                locationList.add(map);

            }

        }



        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("reportCount", reportCount);


        Collections.sort(locationList, new Comparator<Map>() {
            @Override
            public int compare(Map o1, Map o2) {
                String str1 = o1.get("loc_time").toString();
                String str2 = o2.get("loc_time").toString();
                //str2在前，str1在后，默认降序
                return str2.compareTo(str1);
            }
        });
        resultMap.put("locationList", locationList);
        return Result.ok(resultMap);
    }

    private CalendarInfo workDay(CalendarInfo calendarInfo, String timeNow,
                                 List<DfdwTKqglClock> clockList, List<DfdwTKqglWorkday> workdayList, SimpleDateFormat sdf
    ) {
        try {
            Date nowDate = sdf.parse(timeNow);
            calendarInfo.setDate(timeNow);
            //查找打卡记录
            QueryClock queryClock = queryClock(clockList, nowDate);
            calendarInfo.setDayInfo(queryClock.getInfo());
            calendarInfo.setHaveClock(queryClock.haveClock);

            //查找节假日参数记录
            if (workdayList.size() > 0) {
                List<DfdwTKqglWorkday> workday = workdayList.stream().filter(p -> p.getDate().equals(nowDate)).collect(Collectors.toList());
                if (workday.size() > 0) {
                    int type = workday.get(0).getStatus();
                    if (type != 1) {
                        if (type == 0) {
                            calendarInfo.setDayInfo("休息日");
                        }
                        if (type == 2) {
                            calendarInfo.setDayInfo("节假日");
                        }
                    }
                }
            }
            return calendarInfo;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return calendarInfo;
    }

    @Data
    static class QueryClock {
        String info;
        Integer haveClock;
    }

    private QueryClock queryClock(List<DfdwTKqglClock> clockList, Date nowDate) {
        QueryClock queryClock = new QueryClock();
        String info = "";
        int haveClock = 0;
        if (clockList.size() > 0) {
            AtomicReference<DfdwTKqglClock> clockAtomicReference = new AtomicReference<>(new DfdwTKqglClock());
            clockList.stream().filter(p -> p.getClockTime().equals(nowDate)).findFirst().ifPresent(clockAtomicReference::set);
            DfdwTKqglClock clock = clockAtomicReference.get();
            if (clock.getApplyUser() == null) {// 如果不在需要打卡的日期内
                info = "正常";
            } else {// 如果在需要打卡的日期内
                String type = "";
                if (clock.getType() != null && !"".equals(clock.getType())) {
                    type = clock.getType();
                }
                if (type.contains("0.9")) {
                    haveClock = 1;
                    info = "在岗";
                } else if (type.contains("1")) {
                    info = "旷工";
                } else if (type.contains("2")) {
                    haveClock = 1;
                    info = "迟到";
                } else if (type.contains("3")) {
                    haveClock = 1;
                    info = "早退";
                } else if (type.contains("4")) {
                    haveClock = 1;
                    info = "异地";
                } else if (type.contains("5")) {
                    haveClock = 1;
                    info = "点位异常";
                } else if (type.contains("6")) {
                    haveClock = 1;
                    info = "缺卡";
                } else {
                    haveClock = 1;
                    info = "在岗";
                }
            }
        } else { //没有打卡记录
            info = "正常";
        }
        queryClock.setInfo(info);
        queryClock.setHaveClock(haveClock);
        return queryClock;
    }

    private Boolean ifLeave(String info) {
        // （1年假、2事假、3病假、4调休、5产假、6陪产假、7婚假、8丧假）
        return !info.contains("年假") &&
                !info.contains("事假") &&
                !info.contains("病假") &&
                !info.contains("调休") &&
                !info.contains("产假") &&
                !info.contains("陪产假") &&
                !info.contains("婚假") &&
                !info.contains("丧假");
    }

    private CalendarInfo weekend(CalendarInfo calendarInfo, String timeNow,
                                 List<DfdwTKqglClock> clockList, List<DfdwTKqglWorkday> workdayList, SimpleDateFormat sdf
    ) {
        try {
            Date nowDate = sdf.parse(timeNow);
            calendarInfo.setDate(timeNow);
            Integer haveClock = 0;
            calendarInfo.setHaveClock(haveClock);
            //查找节假日参数记录
            if (workdayList.size() > 0) {
                List<DfdwTKqglWorkday> workday = workdayList.stream().filter(p -> p.getDate().equals(nowDate)).collect(Collectors.toList());
                if (workday.size() > 0) {
                    int type = workday.get(0).getStatus();
                    if (type == 1) {
                        //如果设置成工作日查询是否有打卡记录 如果缺卡就查找是否有请假记录
                        //查找打卡记录
                        QueryClock queryClock = queryClock(clockList, nowDate);
                        calendarInfo.setDayInfo(queryClock.getInfo());
                        calendarInfo.setHaveClock(queryClock.haveClock);

                    } else if (type == 2) {
                        calendarInfo.setDayInfo("节假日");
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return calendarInfo;
    }

    @Override
    public List<DfdwTKqglClock> getDwppList(MykqParm ent, PersonEntity person) {
        List<Integer> integerList = integerList(person, ent.getGroupId());
        List<DfdwTKqglClock> records = baseMapper.dwppList(ent.getDate(), ent.getUserName(),integerList);
        return records;
    }

    @Override
    public Integer sendSms(String phone, String content) {
        return baseMapper.sendSms(phone,content);
    }

    /**
     * 点位匹配
     *
     * @param ent
     * @return
     */
    @Override
    public Result<Object> dwppList(MykqParm ent, PersonEntity person) {
        try {
            //获取用户部门信息
            List<DfdwTKqglClock> records =getDwppList(ent,person);
            IPage<DfdwTKqglClock> list = new Page<>();
            list.setCurrent(ent.getPageNum());
            list.setSize(ent.getPageSize());

            List<DfdwTKqglClock> zbbList = new ArrayList<>();
            if (records.size() > 0) {
                int fromIndex = (ent.getPageNum() - 1) * ent.getPageSize();
                int toIndex = ent.getPageNum() * ent.getPageSize();
                if (toIndex > records.size()) {
                    toIndex = records.size();
                }
                zbbList = records.subList(fromIndex, toIndex);
            }
            list.setRecords(zbbList);
            list.setTotal(records.size());

            if (list.getRecords() != null && list.getRecords().size() > 0) {
                //获取用户id,不足10个补充
                List<Integer> userIds = list.getRecords().stream().map(DfdwTKqglClock::getApplyUser).collect(Collectors.toList());
                if (userIds.size() < 10) {
                    Integer defaultId = 0;
                    Integer cha = 10 - userIds.size();
                    for (int i = 0; i < cha; i++) {
                        userIds.add(defaultId);
                        defaultId--;
                    }
                }

                //批量获取时序库数据
                String startDate = ent.getDate() + "T00:00:00";
                String endDate = ent.getDate() + "T23:59:59";
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                Date startDateValue = null;
                Date endDateValue = null;
                try {
                    startDateValue = dateFormat.parse(startDate);
                    endDateValue = dateFormat.parse(endDate);
                } catch (ParseException e) {
                    return Result.error("时间格式错误");
                }

                SimpleDateFormat zdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
                zdateFormat.setTimeZone(TimeZone.getTimeZone("Z"));
                startDate = zdateFormat.format(startDateValue);
                endDate = zdateFormat.format(endDateValue);
                String query = String.format(
                        "import \"influxdata/influxdb/schema\" "
                                + "from(bucket: \"%s\") " // bucket就代表当前的influxdb1.8中的database
                                + " |> range(start: %s,stop: %s) " // 必须要有起始时间和结束时间
                                + " |> filter(fn: (r) => r[\"_measurement\"] == \"%s\""
                                + " and r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\" or r[\"userId\"] == \"%s\")"
                                + " |> schema.fieldsAsCols() "
                                + " |> keep(columns:[\"_time\",\"longitude\",\"latitude\",\"uploadTime\",\"ywId\",\"userId\"])"
                        , InfluxDBUtil.bucket, startDate, endDate, "kqgl_new_position", userIds.get(0), userIds.get(1), userIds.get(2), userIds.get(3), userIds.get(4), userIds.get(5), userIds.get(6), userIds.get(7), userIds.get(8), userIds.get(9));

                List<FluxTable> tables = null;
                try {
                    tables = InfluxDBUtil.getInfluxDBClient().getQueryApi().query(query, InfluxDBUtil.org);
                } catch (Exception e) {
                    InfluxDBUtil.reloadInfluxDBClinet();
                    return Result.error("获取点位信息出错");
                }

                for (FluxTable table : tables) {
                    String startTime = "";
                    String endTime = "";
                    Integer userId = 0;
                    if (table.getRecords().size() >= 0) {
                        userId = Integer.valueOf(table.getRecords().get(0).getValues().get("userId").toString()) ;
                        //第一个点
                        Long longTime = zdateFormat.parse(table.getRecords().get(0).getValues().get("_time").toString().substring(0, 19) + "Z").getTime();
                        startTime = new SimpleDateFormat("HH:mm:ss").format(longTime);
                    }
                    if (table.getRecords().size() >= 1) {
                        //最后一个点
                        Long longTime = zdateFormat.parse(table.getRecords().get(table.getRecords().size()-1).getValues().get("_time").toString().substring(0, 19) + "Z").getTime();
                        endTime = new SimpleDateFormat("HH:mm:ss").format(longTime);
                    }

                    //给list赋值
                    for(DfdwTKqglClock clock : list.getRecords()){
                        if(clock.getApplyUser().equals(userId)){
                            clock.setInfluxStartTime(startTime);
                            clock.setInfluxEndTime(endTime);
                        }
                    }

                }


            }
            return Result.ok(list);
        } catch (Exception ex) {
            return Result.error("失败：" + ex.getMessage());
        }


    }


}




