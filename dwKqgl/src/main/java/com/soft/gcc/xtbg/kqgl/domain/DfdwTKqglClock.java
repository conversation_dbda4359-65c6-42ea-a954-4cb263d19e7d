package com.soft.gcc.xtbg.kqgl.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.kqgl.dto.PageBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* <AUTHOR>
* 考勤管理-考勤列表
* @TableName dfdw_t_kqgl_clock
*/
@TableName(value ="dfdw_t_kqgl_clock")
@Data
public class DfdwTKqglClock  extends PageBaseEntity implements Serializable {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**
     * 申请人
     */
    @TableField(value = "apply_user")
    private Integer applyUser;

    /**
     * 申请人名称
     */
    @TableField(value = "apply_user_name")
    private String applyUserName;

    /**
     * 申请人所在部门
     */
    @TableField(value = "apply_user_dept")
    private Integer applyUserDept;

    /**
     * 上班打卡时间
     */
    @TableField(value = "clock_start_time")
    private String clockStartTime;

    /**
     * 上班打卡状态 （0未打卡、1已打卡）
     */
    @TableField(value = "clock_start_status")
    private Integer clockStartStatus;

    /**
     * 上班打卡地点
     */
    @TableField(value = "clock_start_address")
    private String clockStartAddress;

    /**
     * 上班是否是异地打卡（0否 、1是）
     */
    @TableField(value = "clock_start_place")
    private Integer clockStartPlace;

    /**
     * 上班异地打卡说明
     */
    @TableField(value = "clock_start_place_note")
    private String clockStartPlaceNote;

    /**
     * 下班打卡时间
     */
    @TableField(value = "clock_end_time")
    private String clockEndTime;

    /**
     * 下班打卡状态 （0未打卡、1已打卡）
     */
    @TableField(value = "clock_end_status")
    private Integer clockEndStatus;


    /**
     * 下班打卡地点
     */
    @TableField(value = "clock_end_address")
    private String clockEndAddress;

    /**
     * 下班是否是异地打卡（0否 、1是）
     */
    @TableField(value = "clock_end_place")
    private Integer clockEndPlace;

    /**
     * 下班异地打卡说明
     */
    @TableField(value = "clock_end_place_note")
    private String clockEndPlaceNote;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 打卡日期
     */
    @TableField(value = "clock_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date clockTime;

    /**
     * 最后一个点位时间
     */
    @TableField(value = "last_point_time")
    private Date lastPointTime;

    /**
     * 在线时长
     */
    @TableField(value = "online_duration")
    private Long onlineDuration;

    /**
     * 上班打卡采集图片base64
     */
//    @TableField(value = "clock_img")
    @TableField(exist = false)
    private String clockImg;

    /**
     * 上班经纬度
     */
    @TableField(value = "clock_start_latitude")
    private BigDecimal clockStartLatitude;

    /**
     * 上班经纬度
     */
    @TableField(value = "clock_start_longitude")
    private BigDecimal clockStartLongitude;

    /**
     * 下班经纬度
     */
    @TableField(value = "clock_end_latitude")
    private BigDecimal clockEndLatitude;

    /**
     * 下班经纬度
     */
    @TableField(value = "clock_end_longitude")
    private BigDecimal clockEndLongitude;

    @TableField(value = "clock_start_auto_status")
    private  Integer clockStartAutoStatus;

    @TableField(value = "clock_end_auto_status")
    private  Integer clockEndAutoStatus;

    @TableField(value = "clock_longest_distance")
    private  Integer clockLongestDistance;

    @TableField(value = "clock_longest_distance_address")
    private  String clockLongestDistanceAddress;

    @TableField(value = "clock_longest_distance_latitude")
    private  BigDecimal clockLongestDistanceLatitude;

    @TableField(value = "clock_longest_distance_longitude")
    private  BigDecimal clockLongestDistanceLongitude;
    @TableField(value = "clock_longest_distance_place_status")
    private  Integer clockLongestDistancePlaceStatus;

    @TableField(value = "IsConfrim")
    private  Integer isConfrim;

    @TableField(value = "IsDelete")
    private  Integer isDelete;

    @TableField(value = "gpsUuid")
    private  String gpsUuid;

    @TableField(value = "project_punch_t_id")
    private  Long projectPunchTId;

    @TableField(value = "clock_img_id")
    private  Long clockImgId;

    @TableField(value = "bill_type")
    private  String billType;

    //审批状态 0:未审批 1:审批中 2 审批通过  3 驳回
    @TableField(value = "approval_status")
    private  Integer approvalStatus;

    @TableField(value = "approval_user_id")
    private  Long approvalUserId;

    @TableField(value = "approval_time")
    private  Date approvalTime;

    /**
     * 类型 0 正常上班
     * 异常类型（1 缺卡、2迟到、3早退、4异地打卡、5点位异常）间隔以逗号隔开
     */
    @TableField(value = "type")
    private  String type;

    @TableField(value = "clock_end_task_status")
    private  Integer clockEndTaskStatus;

    @TableField(value = "clock_start_phone_status")
    private  Integer clockStartPhoneStatus;

    @TableField(value = "clock_end_phone_status")
    private  Integer clockEndPhoneStatus;


    /**
     * 打卡最早点是否匹配
     */
    @TableField(exist = false)
    private Integer clockEndStatusEarliest;

    /**
     * 打卡最晚点是否匹配
     */
    @TableField(exist = false)
    private Integer clockStarStatusLatest;

    @TableField(exist = false)
    private String influxStartTime;
    @TableField(exist = false)
    private String influxEndTime;
    @TableField(exist = false)
    private String groupName;

    @TableField(exist = false)
    @JsonFormat( pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String clockTimeStr;

}
