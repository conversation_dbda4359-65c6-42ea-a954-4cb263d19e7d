package com.soft.gcc.xtbg.kqgl.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.framework.helper.ToolHelper;
import com.soft.gcc.common.dictionary_value.entity.Dictionaryvalue;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.kqgl.dto.SfjyDto;
import com.soft.gcc.xtbg.kqgl.dto.SfzCheckLog;
import com.soft.gcc.xtbg.kqgl.service.ISfzCheckService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 身份证校验
 * @date 2024-07-25 14:30:39
 */

@RequestMapping("/sfzCheck")
@RestController
public class SfzCheckController {


    @Autowired
    private ISfzCheckService sfzCheckService;
    @RequestMapping("/check")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-身份校验管理员')")
    public Result<Object> check(@RequestBody SfjyDto ent) {
        SfzCheckLog checklog = sfzCheckService.sfzCheck(ent.getCardNo(), ent.getUserName(), "", "kqgl");
       // ent.setSfzCheckLog(checklog);
        ent.setSfzMatched(checklog.getSfzMatched());
        ent.setResutMsg(checklog.getResutMsg());
        List<SfjyDto> list  = new ArrayList<>();
        list.add(ent);
        return Result.ok(list);
    }


    /**
     * 上传
     */
    @PostMapping("importExcel")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-身份校验管理员')")
    public Result<Object> importExcel(MultipartFile file){
        String fileName = file.getOriginalFilename();
        System.out.println(fileName);
        if (!".xlsx".equals(fileName.substring(fileName.lastIndexOf("."))) &&
                !".xls".equals(fileName.substring(fileName.lastIndexOf(".")))
        ){
            return Result.error("不是excel文件!");
        }

        List<SfjyDto> list = new ArrayList<>();
        try {

            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            XSSFRow row = null;
            int one = 0;

            //循环sesheet页中数据从第二行开始，第一行是标题
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                //获取每一行数据
                row = sheet.getRow(i);
                if(row.getCell(0) == null || row.getCell(1) == null ){
                    one = i+1;
                    return Result.error("有数据为空，请检查第" + one + "行数据！");
                }

                //数字会导致读取错误，故先转换成string 再读取，可解决问题
                XSSFCell cell = row.getCell(0);
                //设置单元格类型
                cell.setCellType(CellType.STRING);
                //全部设置成string 在读取
                row.getCell(0).setCellType(CellType.STRING);
                row.getCell(1).setCellType(CellType.STRING);

                String userName = row.getCell(0).getStringCellValue().trim();
                String cardNo = row.getCell(1).getStringCellValue().trim();

                SfjyDto entity = new SfjyDto();
                entity.setCardNo(cardNo);
                entity.setUserName(userName);
                try{

                    //校验身份证
                    SfzCheckLog checklog = sfzCheckService.sfzCheck(cardNo,userName, "", "kqgl");
                    //  entity.setSfzCheckLog(checklog);
                    entity.setSfzMatched(checklog.getSfzMatched());
                    entity.setResutMsg(checklog.getResutMsg());

                }catch (Exception ex){
                    entity.setSfzMatched(2);
                    entity.setResutMsg("异常："+ex.getMessage());
                    ex.printStackTrace();
                }
                list.add(entity);


            }


           return Result.ok(list);

        }catch (Exception ex){
            ex.printStackTrace();
          //  log.error("导入失败:" + ex.getMessage());
            return Result.error("导入失败！:"+ex.getMessage());
        }
    }

    /**
     * 导出excel
     * @param list
     * @param response
     * @return
     */
    @PostMapping("exportExcel")
    @PreAuthorize("@ss.hasAnyRoles('协同办公-考勤管理-身份校验管理员')")
    public Result<Object> exportExcel(@RequestBody List<SfjyDto> list, HttpServletResponse response){
        try (XSSFWorkbook workbook = new XSSFWorkbook()){
            XSSFSheet sheet = workbook.createSheet("身份核验结果");
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            //第一行数据
            XSSFRow row = sheet.createRow(0);
            //设置一个标题样式
            XSSFCellStyle titleStyle = workbook.createCellStyle();

            XSSFFont titleFont = workbook.createFont();
            //字体大小
            titleFont.setFontHeightInPoints((short) 12);
            //什么字体
            titleFont.setFontName("Arial");
            //是不倾斜
            titleFont.setItalic(false);
            //是不是划掉
            titleFont.setStrikeout(false);
            //字体加粗
            titleFont.setBold(true);
            titleFont.setColor((short) 255);
            titleStyle.setFont(titleFont);
            //添加前景色,内容看的清楚
            titleStyle.setFillForegroundColor((short) 11);
            //垂直居中
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //水平居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFCell titleCell = row.createCell(0);
            titleCell.setCellStyle(titleStyle);
            titleCell.setCellValue("身份核验");
            sheet.setDefaultColumnWidth(23);


            //第二行数据
            row = sheet.createRow(1);
            row.createCell(0).setCellValue("姓名");
            row.createCell(1).setCellValue("身份证号");
            row.createCell(2).setCellValue("认证结果");
            row.createCell(3).setCellValue("核验信息");

            for (int i = 0; i< list.size(); i++) {
                //第3+i行数据
                row = sheet.createRow(2 + i);
                //设置一个标题样式
                XSSFCellStyle cellStyle = workbook.createCellStyle();
                //垂直居中
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                //水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);

                row.createCell(0).setCellValue(list.get(i).getUserName());
                row.createCell(1).setCellValue(list.get(i).getCardNo());


                if(list.get(i).getSfzMatched()   == 1){
                    row.createCell(2).setCellValue("通过");
                }else if(list.get(i).getSfzMatched()   == 2){
                    row.createCell(2).setCellValue("不通过");
                }else if(list.get(i).getSfzMatched()   == 3){
                    row.createCell(2).setCellValue("待定");
                }else{
                    row.createCell(2).setCellValue("未知...");
                }
                row.createCell(3).setCellValue(list.get(i).getResutMsg());

                row.setRowStyle(cellStyle);
            }

            //=================生成word到设置浏览默认下载地址=================
            response.setHeader("Content-disposition", "attachment;filename=" + new String("身份核验结果".getBytes("gb2312"), "ISO8859-1") + ".pdf");
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setContentType("multipart/form-data;charset=UTF-8");
            workbook.write(response.getOutputStream());
            return null;
        } catch (Exception Ex) {
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            return Result.error(s);
        }
    }

}
