package com.soft.gcc.xtbg.kqgl.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.kqgl.domain.DfdwTKqglCxsqLc;
import com.soft.gcc.xtbg.kqgl.mapper.DfdwTKqglCxsqLcMapper;
import com.soft.gcc.xtbg.kqgl.service.IDfdwTKqglCxsqLcService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【dfdw_t_kqgl_cxsq_lc(考勤管理-出行申请)】的数据库操作Service实现
* @createDate 2023-05-26 09:10:19
*/
@Service
public class DfdwTKqglCxsqLcServiceImpl extends ServiceImpl<DfdwTKqglCxsqLcMapper, DfdwTKqglCxsqLc>
    implements IDfdwTKqglCxsqLcService{

}




