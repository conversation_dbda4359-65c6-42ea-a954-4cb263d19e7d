package com.soft.gcc.xtbg.kqgl.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.*;
import lombok.Data;

/**
* <AUTHOR>
*
* @TableName DictionaryValue
*/
@TableName(value ="DictionaryValue")
@Data
public class DfdwKqglDictionaryvalue implements Serializable {


    /**
    *
    */
    @TableField(value = "ID")
    @JSONField(name = "ID")

    private Integer ID;
    /**
    *
    */
    @TableField(value = "TitleID")
    @JSONField(name = "TitleID")

    private Integer TitleID;
    /**
    *
    */
    @TableField(value = "Content")
    @JSONField(name = "Content")

    private String Content;
    /**
    *
    */
    @TableField(value = "Parameter")
    @JSONField(name = "Parameter")

    private String Parameter;
    /**
    *
    */
    @TableField(value = "IsUsed")
    @JSONField(name = "IsUsed")

    private Integer IsUsed;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
