package com.soft.gcc.common.person.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.mapper.PersonMapper;
import com.soft.gcc.common.person.service.PersonService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【Person】的数据库操作Service实现
* @createDate 2023-03-28 14:23:59
*/
@Service
public class PersonServiceImpl extends ServiceImpl<PersonMapper, Person>
    implements PersonService{

}




