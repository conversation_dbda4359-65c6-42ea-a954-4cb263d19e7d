package com.soft.gcc.common.person.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName Person
 */
@TableName(value ="Person")
@Data
public class Person implements Serializable {
    /**
     * 
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "LoginName")
    private String loginname;

    /**
     * 
     */
    @TableField(value = "RealName")
    private String realname;

    /**
     * 
     */
    @TableField(value = "Password")
    private String password;

    /**
     * 
     */
    @TableField(value = "GroupID")
    private Integer groupid;

    /**
     * 
     */
    @TableField(value = "RoleId")
    private Integer roleid;

    /**
     * 
     */
    @TableField(value = "Telephone")
    private String telephone;

    /**
     * 
     */
    @TableField(value = "MsgType")
    private String msgtype;

    /**
     * 
     */
    @TableField(value = "OA")
    private String oa;

    /**
     * 物资系统对应用户ID
     */
    @TableField(value = "WZ_UserID")
    private Integer wzUserid;

    /**
     * 用于判断是集团企业还是施工单位(管理员1，其他是2)
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 短号
     */
    @TableField(value = "Sphone")
    private String sphone;

    /**
     * 专家次数
     */
    @TableField(value = "ZJ_CS")
    private Integer zjCs;

    /**
     * 是否有签名照
     */
    @TableField(value = "PhName")
    private Integer phname;

    /**
     * 身份证号码
     */
    @TableField(value = "CertificateID")
    private String certificateid;

    /**
     * 办公室电话
     */
    @TableField(value = "OfficePhone")
    private String officephone;

    /**
     * 
     */
    @TableField(value = "BFLoginName")
    private String bfloginname;

    /**
     * 
     */
    @TableField(value = "P_XH")
    private Integer pXh;

    /**
     * 
     */
    @TableField(value = "LoginName2")
    private String loginname2;

    /**
     * 
     */
    @TableField(value = "oldID")
    private Integer oldid;

    /**
     * 
     */
    @TableField(value = "state")
    private Integer state;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Person other = (Person) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getLoginname() == null ? other.getLoginname() == null : this.getLoginname().equals(other.getLoginname()))
            && (this.getRealname() == null ? other.getRealname() == null : this.getRealname().equals(other.getRealname()))
            && (this.getPassword() == null ? other.getPassword() == null : this.getPassword().equals(other.getPassword()))
            && (this.getGroupid() == null ? other.getGroupid() == null : this.getGroupid().equals(other.getGroupid()))
            && (this.getRoleid() == null ? other.getRoleid() == null : this.getRoleid().equals(other.getRoleid()))
            && (this.getTelephone() == null ? other.getTelephone() == null : this.getTelephone().equals(other.getTelephone()))
            && (this.getMsgtype() == null ? other.getMsgtype() == null : this.getMsgtype().equals(other.getMsgtype()))
            && (this.getOa() == null ? other.getOa() == null : this.getOa().equals(other.getOa()))
            && (this.getWzUserid() == null ? other.getWzUserid() == null : this.getWzUserid().equals(other.getWzUserid()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getSphone() == null ? other.getSphone() == null : this.getSphone().equals(other.getSphone()))
            && (this.getZjCs() == null ? other.getZjCs() == null : this.getZjCs().equals(other.getZjCs()))
            && (this.getPhname() == null ? other.getPhname() == null : this.getPhname().equals(other.getPhname()))
            && (this.getCertificateid() == null ? other.getCertificateid() == null : this.getCertificateid().equals(other.getCertificateid()))
            && (this.getOfficephone() == null ? other.getOfficephone() == null : this.getOfficephone().equals(other.getOfficephone()))
            && (this.getBfloginname() == null ? other.getBfloginname() == null : this.getBfloginname().equals(other.getBfloginname()))
            && (this.getPXh() == null ? other.getPXh() == null : this.getPXh().equals(other.getPXh()))
            && (this.getLoginname2() == null ? other.getLoginname2() == null : this.getLoginname2().equals(other.getLoginname2()))
            && (this.getOldid() == null ? other.getOldid() == null : this.getOldid().equals(other.getOldid()))
            && (this.getState() == null ? other.getState() == null : this.getState().equals(other.getState()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getLoginname() == null) ? 0 : getLoginname().hashCode());
        result = prime * result + ((getRealname() == null) ? 0 : getRealname().hashCode());
        result = prime * result + ((getPassword() == null) ? 0 : getPassword().hashCode());
        result = prime * result + ((getGroupid() == null) ? 0 : getGroupid().hashCode());
        result = prime * result + ((getRoleid() == null) ? 0 : getRoleid().hashCode());
        result = prime * result + ((getTelephone() == null) ? 0 : getTelephone().hashCode());
        result = prime * result + ((getMsgtype() == null) ? 0 : getMsgtype().hashCode());
        result = prime * result + ((getOa() == null) ? 0 : getOa().hashCode());
        result = prime * result + ((getWzUserid() == null) ? 0 : getWzUserid().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getSphone() == null) ? 0 : getSphone().hashCode());
        result = prime * result + ((getZjCs() == null) ? 0 : getZjCs().hashCode());
        result = prime * result + ((getPhname() == null) ? 0 : getPhname().hashCode());
        result = prime * result + ((getCertificateid() == null) ? 0 : getCertificateid().hashCode());
        result = prime * result + ((getOfficephone() == null) ? 0 : getOfficephone().hashCode());
        result = prime * result + ((getBfloginname() == null) ? 0 : getBfloginname().hashCode());
        result = prime * result + ((getPXh() == null) ? 0 : getPXh().hashCode());
        result = prime * result + ((getLoginname2() == null) ? 0 : getLoginname2().hashCode());
        result = prime * result + ((getOldid() == null) ? 0 : getOldid().hashCode());
        result = prime * result + ((getState() == null) ? 0 : getState().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", loginname=").append(loginname);
        sb.append(", realname=").append(realname);
        sb.append(", password=").append(password);
        sb.append(", groupid=").append(groupid);
        sb.append(", roleid=").append(roleid);
        sb.append(", telephone=").append(telephone);
        sb.append(", msgtype=").append(msgtype);
        sb.append(", oa=").append(oa);
        sb.append(", wzUserid=").append(wzUserid);
        sb.append(", type=").append(type);
        sb.append(", sphone=").append(sphone);
        sb.append(", zjCs=").append(zjCs);
        sb.append(", phname=").append(phname);
        sb.append(", certificateid=").append(certificateid);
        sb.append(", officephone=").append(officephone);
        sb.append(", bfloginname=").append(bfloginname);
        sb.append(", pXh=").append(pXh);
        sb.append(", loginname2=").append(loginname2);
        sb.append(", oldid=").append(oldid);
        sb.append(", state=").append(state);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}