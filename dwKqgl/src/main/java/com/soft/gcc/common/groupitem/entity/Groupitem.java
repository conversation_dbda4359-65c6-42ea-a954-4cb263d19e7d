package com.soft.gcc.common.groupitem.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName GroupItem
 */
@TableName(value ="GroupItem")
@Data
public class Groupitem implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "groupname")
    private String groupname;

    /**
     * 
     */
    @TableField(value = "parentid")
    private Integer parentid;

    /**
     * 
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 
     */
    @TableField(value = "shortpinyin")
    private String shortpinyin;

    /**
     * 
     */
    @TableField(value = "dydj")
    private String dydj;

    /**
     * 单位编号
     */
    @TableField(value = "uComapanyBH")
    private String ucomapanybh;

    /**
     * 单位简称
     */
    @TableField(value = "uComapanyJC")
    private String ucomapanyjc;

    /**
     * 序号
     */
    @TableField(value = "XH")
    private Integer xh;

    /**
     * 是否（分包）
     */
    @TableField(value = "IsShow")
    private Integer isshow;

    /**
     * 类型（1：单位；2：部门；3：班组；4：其它）
     */
    @TableField(value = "Category")
    private Integer category;

    /**
     * 
     */
    @TableField(value = "parentidRz")
    private Integer parentidrz;

    /**
     * 
     */
    @TableField(value = "uComapanyQC")
    private String ucomapanyqc;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Groupitem other = (Groupitem) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGroupname() == null ? other.getGroupname() == null : this.getGroupname().equals(other.getGroupname()))
            && (this.getParentid() == null ? other.getParentid() == null : this.getParentid().equals(other.getParentid()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getShortpinyin() == null ? other.getShortpinyin() == null : this.getShortpinyin().equals(other.getShortpinyin()))
            && (this.getDydj() == null ? other.getDydj() == null : this.getDydj().equals(other.getDydj()))
            && (this.getUcomapanybh() == null ? other.getUcomapanybh() == null : this.getUcomapanybh().equals(other.getUcomapanybh()))
            && (this.getUcomapanyjc() == null ? other.getUcomapanyjc() == null : this.getUcomapanyjc().equals(other.getUcomapanyjc()))
            && (this.getXh() == null ? other.getXh() == null : this.getXh().equals(other.getXh()))
            && (this.getIsshow() == null ? other.getIsshow() == null : this.getIsshow().equals(other.getIsshow()))
            && (this.getCategory() == null ? other.getCategory() == null : this.getCategory().equals(other.getCategory()))
            && (this.getParentidrz() == null ? other.getParentidrz() == null : this.getParentidrz().equals(other.getParentidrz()))
            && (this.getUcomapanyqc() == null ? other.getUcomapanyqc() == null : this.getUcomapanyqc().equals(other.getUcomapanyqc()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGroupname() == null) ? 0 : getGroupname().hashCode());
        result = prime * result + ((getParentid() == null) ? 0 : getParentid().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getShortpinyin() == null) ? 0 : getShortpinyin().hashCode());
        result = prime * result + ((getDydj() == null) ? 0 : getDydj().hashCode());
        result = prime * result + ((getUcomapanybh() == null) ? 0 : getUcomapanybh().hashCode());
        result = prime * result + ((getUcomapanyjc() == null) ? 0 : getUcomapanyjc().hashCode());
        result = prime * result + ((getXh() == null) ? 0 : getXh().hashCode());
        result = prime * result + ((getIsshow() == null) ? 0 : getIsshow().hashCode());
        result = prime * result + ((getCategory() == null) ? 0 : getCategory().hashCode());
        result = prime * result + ((getParentidrz() == null) ? 0 : getParentidrz().hashCode());
        result = prime * result + ((getUcomapanyqc() == null) ? 0 : getUcomapanyqc().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupname=").append(groupname);
        sb.append(", parentid=").append(parentid);
        sb.append(", type=").append(type);
        sb.append(", shortpinyin=").append(shortpinyin);
        sb.append(", dydj=").append(dydj);
        sb.append(", ucomapanybh=").append(ucomapanybh);
        sb.append(", ucomapanyjc=").append(ucomapanyjc);
        sb.append(", xh=").append(xh);
        sb.append(", isshow=").append(isshow);
        sb.append(", category=").append(category);
        sb.append(", parentidrz=").append(parentidrz);
        sb.append(", ucomapanyqc=").append(ucomapanyqc);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}