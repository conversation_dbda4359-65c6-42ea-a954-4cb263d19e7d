package com.soft.gcc.common.t_file.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName T_File
 */
@TableName(value ="T_File")
@Data
public class TFile implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "FileName")
    private String filename;

    /**
     * 
     */
    @TableField(value = "FilePath")
    private String filepath;

    /**
     * 
     */
    @TableField(value = "ProjectID")
    private Integer projectid;

    /**
     * 
     */
    @TableField(value = "FunctionID")
    private Integer functionid;

    /**
     * 
     */
    @TableField(value = "Type")
    private String type;

    /**
     * 
     */
    @TableField(value = "hjID")
    private String hjid;

    /**
     * 
     */
    @TableField(value = "UploadDate")
    private Date uploaddate;

    /**
     * 
     */
    @TableField(value = "IsSecret")
    private Integer issecret;

    /**
     * 
     */
    @TableField(value = "PersonName")
    private String personname;

    /**
     * 
     */
    @TableField(value = "PersonZgh")
    private String personzgh;

    /**
     * 
     */
    @TableField(value = "SubTName")
    private String subtname;

    /**
     * 
     */
    @TableField(value = "SubTID")
    private Integer subtid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TFile other = (TFile) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFilename() == null ? other.getFilename() == null : this.getFilename().equals(other.getFilename()))
            && (this.getFilepath() == null ? other.getFilepath() == null : this.getFilepath().equals(other.getFilepath()))
            && (this.getProjectid() == null ? other.getProjectid() == null : this.getProjectid().equals(other.getProjectid()))
            && (this.getFunctionid() == null ? other.getFunctionid() == null : this.getFunctionid().equals(other.getFunctionid()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getHjid() == null ? other.getHjid() == null : this.getHjid().equals(other.getHjid()))
            && (this.getUploaddate() == null ? other.getUploaddate() == null : this.getUploaddate().equals(other.getUploaddate()))
            && (this.getIssecret() == null ? other.getIssecret() == null : this.getIssecret().equals(other.getIssecret()))
            && (this.getPersonname() == null ? other.getPersonname() == null : this.getPersonname().equals(other.getPersonname()))
            && (this.getPersonzgh() == null ? other.getPersonzgh() == null : this.getPersonzgh().equals(other.getPersonzgh()))
            && (this.getSubtname() == null ? other.getSubtname() == null : this.getSubtname().equals(other.getSubtname()))
            && (this.getSubtid() == null ? other.getSubtid() == null : this.getSubtid().equals(other.getSubtid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFilename() == null) ? 0 : getFilename().hashCode());
        result = prime * result + ((getFilepath() == null) ? 0 : getFilepath().hashCode());
        result = prime * result + ((getProjectid() == null) ? 0 : getProjectid().hashCode());
        result = prime * result + ((getFunctionid() == null) ? 0 : getFunctionid().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getHjid() == null) ? 0 : getHjid().hashCode());
        result = prime * result + ((getUploaddate() == null) ? 0 : getUploaddate().hashCode());
        result = prime * result + ((getIssecret() == null) ? 0 : getIssecret().hashCode());
        result = prime * result + ((getPersonname() == null) ? 0 : getPersonname().hashCode());
        result = prime * result + ((getPersonzgh() == null) ? 0 : getPersonzgh().hashCode());
        result = prime * result + ((getSubtname() == null) ? 0 : getSubtname().hashCode());
        result = prime * result + ((getSubtid() == null) ? 0 : getSubtid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", filename=").append(filename);
        sb.append(", filepath=").append(filepath);
        sb.append(", projectid=").append(projectid);
        sb.append(", functionid=").append(functionid);
        sb.append(", type=").append(type);
        sb.append(", hjid=").append(hjid);
        sb.append(", uploaddate=").append(uploaddate);
        sb.append(", issecret=").append(issecret);
        sb.append(", personname=").append(personname);
        sb.append(", personzgh=").append(personzgh);
        sb.append(", subtname=").append(subtname);
        sb.append(", subtid=").append(subtid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}