package com.soft.gcc.common.lc_workFlow.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.soft.gcc.common.lc_workFlow.entity.LcWorkflow;
import com.soft.gcc.common.lc_workFlow.service.LcWorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@Service
public class lc_workFlow {

    @Autowired
    private LcWorkflowService lcWorkflowService;

    @GetMapping
    public List<LcWorkflow> GetWorkFlowList(Integer id, Integer lc_defineID){
        QueryWrapper<LcWorkflow> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(i->i.eq("ywID",id).eq("lc_defineID",lc_defineID)).orderByAsc("ID");
        List<LcWorkflow> list = lcWorkflowService.list(queryWrapper);
        return list;
    }
}

