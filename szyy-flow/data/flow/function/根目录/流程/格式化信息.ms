{
  "properties" : { },
  "id" : "ef504a48d76f4c91adf3ca887813bce7",
  "script" : null,
  "groupId" : "bcff511fe7c34224a36a5786d888a07c",
  "name" : "格式化信息",
  "createTime" : 1674795032128,
  "updateTime" : 1673243590270,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "trimInfo",
  "description" : null,
  "returnType" : null,
  "mappingPath" : "/flow/trimInfo",
  "parameters" : [ {
    "name" : "str",
    "value" : null,
    "description" : "人员信息",
    "required" : false,
    "dataType" : "String",
    "type" : "java.lang.String",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "suffix",
    "value" : null,
    "description" : "填充字符",
    "required" : false,
    "dataType" : "String",
    "type" : "java.lang.String",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ]
}
================================
if (is_blank(str)){
    return "";
}else{
    if (str.startsWith(suffix)) {
        str = suffix.substring(suffix.length(), str.length());
    }
    if (str.endsWith(suffix)) {
        str = str.substring(0, str.length() - suffix.length());
    }
    return str;
}