{
  "properties" : { },
  "id" : "e6ce8fb132ef4ec69d6aebd4551d6892",
  "script" : null,
  "groupId" : "bcff511fe7c34224a36a5786d888a07c",
  "name" : "信息去重",
  "createTime" : 1674795032127,
  "updateTime" : 1675068535652,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : "errorone",
  "path" : "distInfo",
  "description" : null,
  "returnType" : null,
  "mappingPath" : "/flow/distInfo",
  "parameters" : [ {
    "name" : "info",
    "value" : null,
    "description" : "待去重信息",
    "required" : false,
    "dataType" : "String",
    "type" : "java.lang.String",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ]
}
================================
import "@/flow/trimInfo" as trimInfo;
const rInfo = trimInfo(info, "~").split("~")
return rInfo.distinct().filter(i->!is_blank(i))
