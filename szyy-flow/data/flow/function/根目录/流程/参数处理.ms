{
  "properties" : { },
  "id" : "b584ff9851254b079e1729c5934751f3",
  "script" : null,
  "groupId" : "bcff511fe7c34224a36a5786d888a07c",
  "name" : "参数处理",
  "createTime" : null,
  "updateTime" : 1675841430258,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "errorone",
  "path" : "dealSend",
  "description" : null,
  "returnType" : null,
  "mappingPath" : null,
  "parameters" : [ {
    "name" : "array",
    "value" : null,
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : "java.util.Collection",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "idx",
    "value" : null,
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : "java.util.Collection",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ]
}
================================
import "@/flow/trimInfo" as trimInfo;
const info = array.filter((it,i)->idx.some(d->d==i)).map(it->trimInfo(it,"~").replace("/",""))
return info.distinct().filter(i->!is_blank(i)).join("~")+"~"