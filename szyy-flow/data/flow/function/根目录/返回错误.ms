{
  "properties" : { },
  "id" : "15f92392bb0e4e278a9594443decf108",
  "script" : null,
  "groupId" : "b670c3ae06ff456582306050b91f7f76",
  "name" : "返回错误",
  "createTime" : 1674795032125,
  "updateTime" : 1671704476205,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "error",
  "description" : null,
  "returnType" : "java.lang.Object",
  "mappingPath" : "/error",
  "parameters" : [ {
    "name" : "message",
    "value" : null,
    "description" : "错误信息",
    "required" : false,
    "dataType" : "String",
    "type" : "java.lang.String",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "code",
    "value" : null,
    "description" : "错误代码",
    "required" : false,
    "dataType" : "String",
    "type" : "java.lang.Number",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ]
}
================================
import response
return response.json({
    code: code||500,
    message: message || '系统异常'
})