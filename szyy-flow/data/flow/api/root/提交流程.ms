{
  "properties" : { },
  "id" : "e3618871f00d4c6ea449fd98e4c05e40",
  "script" : null,
  "groupId" : "0a676eccdf4d4d1bb18ba376c0a37d62",
  "name" : "提交流程",
  "createTime" : null,
  "updateTime" : 1704420488359,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : "szyy",
  "path" : "submit",
  "method" : "POST",
  "parameters" : [ {
    "name" : "lcjdid",
    "value" : "590604",
    "description" : "流程阶段ID",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择流程",
    "expression" : null,
    "children" : null
  }, {
    "name" : "ywid",
    "value" : "5234",
    "description" : "业务ID",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择提交记录",
    "expression" : null,
    "children" : null
  }, {
    "name" : "feeds",
    "value" : "同意",
    "description" : "意见",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请填写意见",
    "expression" : null,
    "children" : null
  }, {
    "name" : "sendPersonZgh",
    "value" : "0",
    "description" : "流程下一阶段人员ID",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : "",
    "validateType" : null,
    "error" : "请选择提交人员",
    "expression" : null,
    "children" : null
  }, {
    "name" : "sendPersonGroup",
    "value" : "",
    "description" : "流程下一阶段单位",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : "",
    "validateType" : null,
    "error" : "请选择提交单位",
    "expression" : null,
    "children" : null
  }, {
    "name" : "sendPerson",
    "value" : "",
    "description" : "流程下一阶段人员名称",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : "",
    "validateType" : null,
    "error" : "请选择提交名称",
    "expression" : null,
    "children" : null
  }, {
    "name" : "sendPersonJdid",
    "value" : "0",
    "description" : "流程下一阶段ID",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : "0",
    "validateType" : null,
    "error" : "请选择提交流程ID",
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\n\n}",
  "headers" : [ {
    "name" : "x-access-token",
    "value" : "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6Im0iLCJleHAiOjE3MDQ0MjIzMDIsImlhdCI6MTcwNDQxODcwMn0.zWi8mJqJoqhHiatIsuwbIzieLo2jDzh8V0zl0vv_OaM",
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 500,\n    \"message\": \"处理超时，请联系稍后再试\"\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "500",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "处理超时，请联系稍后再试",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import "@/error" as error;
import "@/flow/distInfo" as distInfo;
import "@/flow/dealSend" as dealSend;
import "@/flow/dropInfo" as dropInfo;

import org.springframework.data.redis.connection.stream.StringRecord;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.StringRedisTemplate as stringRedisTemplate;

import log;
//判断流程信息 完整性  可行性
const transdate = now();
const person = user.user()

let curState = {}
let curStates = []
let workFlows = []
let startDate = transdate;
const lcInfo = db.selectOne("""select d.LcID ,d.lcName, l.lcjdID, l.jdmc, l.nextID, l.[type], l.BackjdID, l.IsBX, (select count(1) from Lc_Condition lc where lc.NextJDID = l.lcjdID) next_size from Lcjd l inner join Lcdefine d on d.LcID = l.lc_defineID where l.lcjdID = #{lcjdid}""")
const lcCur = db.table("Lc_currentState").where().eq("lc_defineid",lcInfo.lcid).eq("ywid",ywid).selectOne();
//处理数据
const tjIds = distInfo(sendPersonJdid)
if (tjIds.some(id=>lcCur!=null && id==lcCur.lcJdid::string) ){
    return error('流程重复提交，请刷新数据!')
}
if (lcCur==null && lcInfo.nextSize!=0){
    log.error("异常-定义不存在=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
    return error('流程信息异常，请刷新数据.')
}
const lcCon = db.table("Lc_Condition").where().eq("jdid",lcjdid).select();
if (lcCon==null || lcCon.size()==0){
    log.error("异常-配置不存在=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
    return error('流程信息异常，请刷新数据')
}
const sendPersonZghs = (sendPersonZgh||'').split("~")
const sendPersonGroups = (sendPersonGroup||'').split("~")
const sendPersons = (sendPerson||'').split("~")
const sendPersonJdids = (sendPersonJdid||'').split("~")
//判断是否最后一个 节点
const sendPersonGroupNames = sendPersonGroups.every(it->is_blank(it))?['完成']: db.table("GroupItem").where().in("id",sendPersonGroups).select().map(it->it.groupname)
const sendInfo = {}
tjIds.each(jd->{
    const idx = []
    sendPersonJdids.each((sjd,index)->{
        if (sjd==jd){
           idx.add(index)
        }
    })
    sendInfo[jd]={
        jdId: jd,
        sendPerson: dealSend(sendPersons,idx),
        sendPersonZgh: dealSend(sendPersonZghs,idx),
        sendPersonGroup: dealSend(sendPersonGroups,idx),
        sendPersonGroupName: dealSend(sendPersonGroupNames,idx)
    }
})
//构建 查询语句
const checkGroup = []
lcCon.map(con->{
    const cSql = is_blank(con.condition)? '1 ':`(select count(1) from (${con.condition} ${ywid}) t)`
    checkGroup.add(`select '${con.nextjdid}' nextJdId, '${con.nextjdmc}' nextJdmc, ${cSql} canGo`)
})
const checkSql = checkGroup.join(' union all ')
const checkRes = db.select(checkSql)
if (checkRes==null){
    log.error("异常-配置不正确-节点=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
    return error('流程信息异常，请刷新数据!')
}
const nextJdInfo = checkRes.filter(it=>it.cango!=0 && (tjIds==null || tjIds.size()== 0 || tjIds.some(jd=>jd==it.nextjdid))) //多个下一阶段
if (nextJdInfo==null || nextJdInfo.size()==0){
    log.error("异常-配置不正确-下一步=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
    return error('流程信息异常，请刷新数据!')
}
if (nextJdInfo.some(r=>r.nextjdid=='0')){
    //最后一个环节
    if (lcCon.size()>0 && lcCur.lcJdid!=0){
        log.info("事务-开始 流程ID:{} 业务ID:{}",lcjdid,ywid)
        const wkEnd = db.transaction(()=>{
            //TODO 判断结束情况
            curState = {...lcCur};
            curState.lcJdid = 0
            curState.lcJdmc ='完成'
            curState.sendperson='归档';
            curState.sendpersonzgh='归档';

            db.table("Lc_currentState").primary("id").save(curState)
            let curWf = db.table("Lc_workFlow").where().eq("lc_defineid",lcInfo.lcid).eq("ywid",ywid).orderBy("id", "desc").selectOne()
            curWf.transdate=transdate
            curWf.feed=feeds
            db.table("Lc_workFlow").primary("id").save(curWf)
            log.info("事务-数据库 流程ID:{} 业务ID:{}",lcjdid,ywid)
            //TODO 流程运行成功，发送订阅消息  "szyy_flow_"+lcInfo.lcid
            const stringRecord = StreamRecords.string({name:"SZYY.FLOW.SUBMIT", ywid: ywid, from: lcjdid, to:sendInfo::stringify}).withStreamKey("szyy_flow");
            stringRedisTemplate.opsForStream().add(stringRecord);
            stringRedisTemplate.convertAndSend("SZYY.FLOW.SUBMIT",{ywid: ywid, from: lcjdid, to:sendInfo}::stringify)
            log.info("事务-通知 流程ID:{} 业务ID:{}",lcjdid,ywid)
            return 10;
        })
        if (wkEnd!=1){
            return error('处理超时，请联系稍后再试');
        }else{
            return wkEnd
        }
    }else{
        return error('流程信息异常，请刷新数据!!')
    }
}
//获取出来的下一阶段
nextJdInfo.each((nJdInfo,index)->{
    const sInfo = sendInfo[nJdInfo.nextjdid]
    let curWf = null
    if (lcCur==null){
        //判断是否第一个阶段
        if (lcInfo.nextSize==0){
            const zgh = distInfo(sendPersonZgh)
            //初始化数据
            curStates.add({
                lcDefineid: lcInfo.lcid,
                lcName: lcInfo.lcname,
                ywid: ywid,
                sendperson: sInfo.sendPerson,
                sendpersonzgh: sInfo.sendPersonZgh,
                allpersonzgh: person.getLoginName()+"~"+sInfo.sendPersonZgh,
                lcJdmc: nJdInfo.nextjdmc,
                lcJdid: nJdInfo.nextjdid,
                lcIsback: 0,
                lcTojdid: null,
                isotheradd: null,
                ismany: sInfo.sendPersonZgh.split("~").size()-1, //是否会签
                number: 1
            })
            curWf = {
                "ywid": ywid,
                "lcDefineid": lcInfo.lcid,
                "lcJdid": lcInfo.lcjdid,
                "lcJdmc": lcInfo.jdmc,
                "groupid": person.groupID,
                "groupname": person.groupName,
                "personzgh": person.loginName,
                "personname": person.realName,
                "transdate": transdate,
                "feed": feeds,
                "number": 1,
                "pno": "",
                "startdate": startDate,
                "isback": null,
                "useback": null
            }
            workFlows.add(curWf)
        }else{
            //异常数据
            log.error("异常-非法数据=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
            return error('流程信息异常，请刷新数据')
        }
    }else{
        curState = {...lcCur};
        curState.lcJdmc=nJdInfo.nextjdmc;
        curState.lcJdid=nJdInfo.nextjdid;
        curState.sendperson=sInfo.sendPerson;
        curState.allpersonzgh=curState.allpersonzgh+sInfo.sendPersonZgh;
        curState.sendpersonzgh=sInfo.sendPersonZgh;

        curState.number = curState.number+1;
        curState.bxtype = index==0?'子线':'主线';
        curState.isotheradd = 0;
        curStates.add(curState);
        curWf = db.table("Lc_workFlow").where().eq("lc_defineid",lcInfo.lcid).eq("ywid",ywid).orderBy("id", "desc").selectOne()
        startDate = curWf.transdate || transdate;
        curWf.transdate=transdate
        curWf.feed=feeds
        workFlows.add(curWf)
    }
    if (nJdInfo.nextjdid!=0){
    //更新 Lc_workFlow
        workFlows.add({
            "ywid": ywid,
            "lcDefineid": lcInfo.lcid,
            "lcJdid": nJdInfo.nextjdid,
            "lcJdmc": nJdInfo.nextjdmc,
            "groupid": dropInfo(sInfo.sendPersonGroup),
            "groupname": dropInfo(sInfo.sendPersonGroupName),
            "personzgh": dropInfo(sInfo.sendPersonZgh),
            "personname": dropInfo(sInfo.sendPerson),
            "number": curWf.number + 1,
            "pno": "",
            "startdate": startDate,
            "isback": null,
            "useback": null
        })
    }
});
log.info("事务-开始 流程ID:{} 业务ID:{}",lcjdid,ywid)
const wkId = db.transaction(()->{
    curStates.forEach(res->{
        db.table("Lc_currentState").primary("id").save(res);
    })
    workFlows.forEach(res->{
        db.table("Lc_workFlow").primary("id").save(res);
    })
    //切换主子线
    const zzx = db.table("Lc_currentState").where().eq("ywid",ywid).eq("lcDefineid",lcInfo.lcid).select()
    const zzxZx = zzx.filter(z->z.bxtype=='主线');
    if (zzxZx.size()==0){
        const xgzx = zzx[0]
        xgzx.bxtype = '主线'
        db.table("Lc_currentState").primary("id").save(xgzx);
    }else if(zzxZx.size()==1){
        log.info("{} 检查主子线", lcInfo.lcid);
    }else{
        const zxIds = zzx.filter((z,idx)->z.bxtype=='主线' && idx>0);
        db.update("""update Lc_currentState set bxtype = '子线' where lc_defineid = #{lcInfo.lcid} and ywid in (#{zxIds})""")
    }
    log.info("事务-数据库 流程ID:{} 业务ID:{}",lcjdid,ywid)
    //流程运行成功，发送订阅消息
    const stringRecord = StreamRecords.string({name:"SZYY.FLOW.SUBMIT",ywid: ywid, from: lcjdid, to:sendInfo::stringify}).withStreamKey("szyy_flow"); //"szyy_flow_"+lcInfo.lcid
    stringRedisTemplate.opsForStream().add(stringRecord);
    stringRedisTemplate.convertAndSend("SZYY.FLOW.SUBMIT",{ywid: ywid, from: lcjdid, to:sendInfo}::stringify)
    log.info("事务-通知 流程ID:{} 业务ID:{}",lcjdid,ywid)
    return 1;
})
if (wkId!=1){
    return error('处理超时，请联系稍后再试.');
}else{
    return wkId
}
