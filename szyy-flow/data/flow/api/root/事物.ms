{
  "properties" : { },
  "id" : "86a9e93072074b4f8e1e0b315fac34f4",
  "script" : null,
  "groupId" : "0a676eccdf4d4d1bb18ba376c0a37d62",
  "name" : "事物",
  "createTime" : null,
  "updateTime" : 1699864538224,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "/testtr",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ {
    "name" : "anonymous",
    "value" : "true",
    "description" : "该接口需要不登录也可访问",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"message\": \"success\",\n    \"result\": \"\",\n    \"success\": true,\n    \"timestamp\": 1699864447464\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "result",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "success",
      "value" : "true",
      "description" : "",
      "required" : false,
      "dataType" : "Boolean",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1699864447464",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import log
import "@/error" as error;

// const wkId = db.transaction(()->{
//     db.table("APP_AQM_USER").primary("id").update({
//         id: 471, "[user]": '科技公司管理员001'
//     })
//     const a = 1/0;
//     return 10;
// })
// if (wkId>0){
//     return wkId
// }else{
//     return error('流程信息异常，请联系管理员');
// }

import org.springframework.data.redis.connection.stream.StringRecord
import org.springframework.data.redis.connection.stream.StreamRecords

import org.springframework.data.redis.core.StringRedisTemplate as stringRedisTemplate;
import org.springframework.data.redis.connection.stream.StreamInfo as StreamInfo; 
const stream = "szyy_flow";
const sOpts = stringRedisTemplate.opsForStream();
const sInfo =sOpts.info(stream);
log.info("{} { size:{}, message:{}, last:{}}", stream, sInfo.groupCount(), sInfo.getRaw().get("length"), sInfo.lastGeneratedId())
const sGroups = sOpts.groups(stream)
let gCur = null;
sGroups.forEach((g)->{
    const cs = sOpts.consumers(stream,g.groupName())
    const csn = []
    cs.forEach(csnt->{
        csn.add(csnt.consumerName())
    })
    log.info("{} -> {} { size:{}, pending:{}, last:{} }", stream, g.groupName(),g.consumerCount(), g.pendingCount(), consumers:{}, csn)
})
return ""