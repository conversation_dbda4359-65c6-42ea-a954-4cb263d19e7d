{
  "properties" : { },
  "id" : "1590d61e9de449bd8a536bf0d9e7382c",
  "script" : null,
  "groupId" : "0a676eccdf4d4d1bb18ba376c0a37d62",
  "name" : "测试",
  "createTime" : null,
  "updateTime" : 1704418658473,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "szyy",
  "path" : "/test",
  "method" : "GET",
  "parameters" : [ {
    "name" : "f01",
    "value" : { },
    "description" : null,
    "required" : false,
    "dataType" : "MultipartFile",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "loginName",
    "value" : "m",
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ {
    "name" : "anonymous",
    "value" : "true",
    "description" : "该接口需要不登录也可访问",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"message\": \"success\",\n    \"result\": null,\n    \"success\": true,\n    \"timestamp\": 1704418645667\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "result",
      "value" : "null",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "success",
      "value" : "true",
      "description" : "",
      "required" : false,
      "dataType" : "Boolean",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1704418645667",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import org.springframework.data.redis.connection.stream.StringRecord
import org.springframework.data.redis.connection.stream.StreamRecords

import org.springframework.data.redis.core.StringRedisTemplate as stringRedisTemplate;
import org.springframework.data.redis.connection.stream.StreamInfo as StreamInfo;
import log;
log.info("stringRedisTemplate? ==> {}",stringRedisTemplate == null)

user.mock("m")

return stringRedisTemplate.opsForValue().get("TOKEN::m")






const r = []
for (i in range(1,2)){
    var stringRecord = StreamRecords.string({name:"SZYY.FLOW.SUBMIT",ywid: i+"", from: "910201", to:[]::stringify}).withStreamKey("szyy_flow");
    let mInfo = stringRedisTemplate.opsForStream().add(stringRecord);
    // stringRedisTemplate.convertAndSend("SZYY.FLOW.SUBMIT",""+i)
    r.add(mInfo)
    // log.info("发送消息 {}", mInfo.value)
}
// return r;

// import rs;

// return rs.check()


const stream = "szyy_flow";
const group = "mygroup"
const sOpts = stringRedisTemplate.opsForStream();
const sInfo =sOpts.info(stream);
log.info("{} stream group数量:{}, message数量:{}", stream, sInfo.groupCount(), sInfo.getRaw().get("length"))
const sGroups = sOpts.groups(stream)
let gCur = null;
sGroups.forEach((g)->{
    const cs = sOpts.consumers(stream,g.groupName())
    const csn = []
    cs.forEach(csnt->{
        csn.add(csnt.consumerName())
        log.info(csnt::stringify)

    })
    log.info("{} -> {} { consumers-size:{}, pending:{}, last:{}, consumers:{} }", stream, g.groupName(),g.consumerCount(), g.pendingCount(), g.lastDeliveredId(), csn)
    if (g.groupName()==group){
        gCur = g
    }
})
if (gCur==null){

}else{
   log.info("{} stream group:{}, 消费数量:{}, 剩余数据{}", stream, gCur.groupName(), gCur.consumerCount(), gCur.pendingCount())
}
return gCur


// import "org.ssssssss.magicapi.utils.ScriptManager" as ScriptManager;

// var getQueryVariable = (query, variable)=>{
//     var vars = (query.split('\\?')[1]||query).split("\\&");
//     for (vi in vars) {
//         var pair = vi.split("=");
//         if(pair[0] == variable){
//             return pair[1];
//         }
//     }
//     return '';
// }


// var to = 999
// var a = '`ss ${to} ${getQueryVariable("https://www.y-dashi.com:4435/app/ecard/code?ID=14398","ID")}`'

//return ScriptManager.executeExpression(a,{to,getQueryVariable})

return user.mock(loginName)

return user.user()
return stringRedisTemplate.opsForValue().get("AUTH::18074226137")

