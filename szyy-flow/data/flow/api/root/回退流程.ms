{
  "properties" : { },
  "id" : "ab62108408fc41e592094c5bf6c1f0cd",
  "script" : null,
  "groupId" : "0a676eccdf4d4d1bb18ba376c0a37d62",
  "name" : "回退流程",
  "createTime" : null,
  "updateTime" : 1699863826651,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "back",
  "method" : "POST",
  "parameters" : [ {
    "name" : "lcjdid",
    "value" : "910207",
    "description" : "流程阶段ID",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择流程",
    "expression" : null,
    "children" : null
  }, {
    "name" : "ywid",
    "value" : "31150",
    "description" : "业务ID",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择提交记录",
    "expression" : null,
    "children" : null
  }, {
    "name" : "feeds",
    "value" : "不同意",
    "description" : "意见",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请填写意见",
    "expression" : null,
    "children" : null
  }, {
    "name" : "init",
    "value" : "",
    "description" : "默认回退到第一步",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : "1",
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "reject",
    "value" : null,
    "description" : "驳回且结束流程",
    "required" : false,
    "dataType" : "Boolean",
    "type" : null,
    "defaultValue" : "false",
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\n\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"message\": \"success\",\n    \"result\": 1,\n    \"success\": true,\n    \"timestamp\": 1675929925765\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "result",
      "value" : "1",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "success",
      "value" : "true",
      "description" : "",
      "required" : false,
      "dataType" : "Boolean",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1675929925765",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import "@/error" as error;
import "@/flow/distInfo" as distInfo;
import "@/flow/dealSend" as dealSend;

import org.springframework.data.redis.connection.stream.StringRecord;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.StringRedisTemplate as stringRedisTemplate;

import log;
//TODO 后续改为 动态表名
//判断流程信息 完整性  可行性
const transdate = now();
const person = user.user()
let curState = {}
const lcInfo = db.selectOne("""select d.LcID ,d.lcName, l.lcjdID, l.jdmc, l.nextID, l.[type], l.BackjdID, l.IsBX from Lcjd l inner join Lcdefine d on d.LcID = l.lc_defineID where l.lcjdID=#{lcjdid} """)

const lcCon = db.table("Lc_Condition").where().eq("nextjdid",lcjdid).select();
if (lcCon==null || lcCon.size()==0){
    log.error("异常的流程数据=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
    return error('流程信息异常，请刷新数据')
}
const lcCur = db.table("Lc_currentState").where().eq("lc_defineid",lcInfo.lcid).eq("ywid",ywid).select()
if (lcCur==null && lcCur.size()==0){
    log.error("异常的流程数据=>流程ID:{} 业务ID:{} 人员ID:{}",lcjdid,ywid,person.getId())
    return error('流程信息异常，请刷新数据.')
}

let curStates = []
let workFlows = []
let mqFlow = []
if (reject){
    init='1'
}
//TODO 默认取 Lcjd 表 BackjdID 值，若 空 取走线的上一个节点 （Lc_workFlow）
if (init=='1'){
    //获取第一个阶段信息
    curWf = db.table("Lc_workFlow").where().eq("lc_defineid",lcInfo.lcid).eq("ywid",ywid).orderBy("id").selectOne()
    if (curWf==null){
        return error('流程信息异常，请刷新数据!')
    }
    if (reject){
        lcCur.forEach(cur->{
            curState = {...cur};
            curState.lcJdid = -1;
            curState.lcJdmc = "已驳回"
            curState.sendperson="已驳回";
            curState.sendpersonzgh="已驳回";
            curStates.add(curState)
        })
        mqFlow.add({
            ywid, personzgh: curWf.personzgh, type: 1, lcJdid: curWf.lcJdid, from: lcjdid
        })
    }else{
        curWf.id = null;
        curWf.transdate=null
        curWf.feed=null
        curWf.startdate = transdate
        curWf.isback = 1
        curWf.useback = 1
        curWf.number = 1
        workFlows.add(curWf)
        mqFlow.add({
            ywid, personzgh: curWf.personzgh, type: 0, lcJdid: curWf.lcJdid, from: lcjdid
        })
        lcCur.forEach(cur->{
            curState = {...cur};
            curState.lcIsback = 1
            curState.lcJdid = curWf.lcJdid
            curState.lcJdmc = curWf.lcJdmc
            curState.sendperson=curWf.personname+"~";
            curState.sendpersonzgh=curWf.personzgh+"~";
            curState.number = 1;
            curStates.add(curState)
        })
    }
}else{
    // const checkGroup = []
    // lcCon.map(con->{
    //     checkGroup.add(`select '${con.jdid}' lcjdID, '${con.jdmc}' lcjdMc,1 canGo`)
    // })
    // const checkSql = checkGroup.join(' union ')
    // const checkRes = db.select(checkSql)
    // if (checkRes==null || checkRes.every(r=>r.cango==0)){
    //     return error('流程信息异常，请刷新数据!')
    // }
    // checkRes.each((nJdInfo,index)->{
    //     curState = {...lcCur};
    //     curState.lcIsback = 1
    //     curState.lcJdid = lcInfo.lcjdid
    //     curState.lcJdmc = lcInfo.lcjdmc
    //     curState.sendperson=sendPerson;
    //     curState.sendpersonzgh=sendPersonZgh;
    //     curState.allpersonzgh=sendPersonZgh;
    //     curState.number = curState.number+1;
    // })
    return error('流程功能未实现，请联系管理员');
}

const wkId = db.transaction(()=>{
    try{
        db.update("""update Lc_workFlow set transdate=#{transdate}, feed=#{feeds}, isback=1, useback=1 where ywid = #{ywid} and lc_defineID = #{lcInfo.lcid} and transdate is null""");
        curStates.forEach(res->{
            db.table("Lc_currentState").primary("id").save(res);
        })
        workFlows.forEach(res->{
            db.table("Lc_workFlow").primary("id").save(res);
        })
        const stringRecord = StreamRecords.string({name:"SZYY.FLOW.BACK",to:mqFlow::stringify}).withStreamKey("szyy_flow_"+lcInfo.lcid);
        stringRedisTemplate.convertAndSend("SZYY.FLOW.BACK",mqFlow::stringify)      
        stringRedisTemplate.opsForStream().add(stringRecord);
        return 1;
    }catch(e) {
        log.error(e.message);
        return error('流程信息异常，请联系管理员');
    }
});
return wkId
