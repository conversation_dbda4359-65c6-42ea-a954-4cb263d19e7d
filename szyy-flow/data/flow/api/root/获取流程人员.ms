{
  "properties" : { },
  "id" : "a2d9cb928f404557bb860cd144846df8",
  "script" : null,
  "groupId" : "0a676eccdf4d4d1bb18ba376c0a37d62",
  "name" : "获取流程人员",
  "createTime" : null,
  "updateTime" : 1704420285383,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : "szyy",
  "path" : "getFlowUsers",
  "method" : "GET",
  "parameters" : [ {
    "name" : "loginName",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "permission",
    "value" : "590604,5234",
    "description" : "",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  }, {
    "name" : "byCom",
    "value" : "",
    "description" : "是否本公司全部人员，默认本公司",
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : "-1",
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ {
    "name" : "x-access-token",
    "value" : "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTmFtZSI6Im0iLCJleHAiOjE3MDQ0MjIzMDIsImlhdCI6MTcwNDQxODcwMn0.zWi8mJqJoqhHiatIsuwbIzieLo2jDzh8V0zl0vv_OaM",
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 500,\n    \"message\": \"流程信息异常，请刷新数据\"\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "500",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "流程信息异常，请刷新数据",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import "@/error" as error;
const person = user.user()
const gId = person.getGroupID()
const tId = person.getTopGroupId()
// const gId = 471
// const tId = 471
const lcjdid = permission.split(",")[0]
const ywid = permission.split(",")[1]
//TODO 获取人员信息 权限信息 TopGroupId ，后续加上自定义过滤器
//TODO
const lcCon = db.table("Lc_Condition").where().eq("jdid",lcjdid).select();
if (lcCon==null || lcCon.size()==0){
    return error('流程信息异常，请刷新数据')
}
const checkGroup = []
lcCon.map(con->{
    const cSql = is_blank(con.condition)? '1 ':`(select count(1) from (${con.condition} #{ywid}) t)`
    //lcCon->lConditionCode 为 强制指定单位ID
    checkGroup.add(`select '${con.lConditionCode}' config,'${con.nextjdid}' nextJdId, '${con.nextjdmc}' nextJdmc, ${cSql} canGo`)
})
const checkSql = checkGroup.join(' union all ')
const checkRes = db.select(checkSql)
if (checkRes==null){
    return error('流程信息异常，请刷新数据!')
}
const nextJdInfo = checkRes.filter(it=>it.cango!=0 && (tjIds==null || tjIds.size()== 0 || tjIds.some(jd==it.nextJdId)))//多个下一阶段
const nextJdId = nextJdInfo.map(it->it.nextjdid)
if (nextJdInfo==null || nextJdInfo.size()==0){
   return [];
}
if (nextJdInfo.size()>1){
   return error('流程配置信息异常!')
}
if (nextJdId.get(0) == 0){
   return '完成'
}
if (nextJdInfo.some(info-> info.config!=null && /^\d+$/.test(info.config::string))){
    //lcCon->lConditionCode 为 强制指定单位ID
    const tail = nextJdInfo.filter(inf-> info.groupId> -1).map(it->`(l.lcjdID=${it.nextjdid} and Person.GroupID=${it.groupId})`).join(" or ")
    const sql = """SELECT Person.id, LoginName login_name, RealName real_name,Person.GroupID group_id, CONCAT(g1.groupname,'/', g.groupname) AS group_name, l.lcjdID jd_id, l.jdmc jd_mc, l.isbx  FROM Person
    INNER JOIN GroupItem g ON Person.GroupID = g.id
    LEFT JOIN  GroupItem g1 ON g.parentid = g1.id
    LEFT JOIN  Lcjd l on  l.lcjdID in (#{nextJdId})
    WHERE Person.Id IN (SELECT PersonId FROM RolePerson
        WHERE (RoleId IN (SELECT roleid FROM RolePermission
    WHERE (PermissionNo in (#{nextJdId}))))) and (tail)
    ?{loginName != ''and loginName !=null, AND LoginName = #{loginName}} order by P_XH"""
    const list = db.select(sql)
    return list;
}else {
    let tail = ''; let isDwBm = true;
    const isByDefault = byCom=='-1'
    if (isByDefault){
        let configTail = {
           '1': `dbo.FUNC_TOP_GROUP(Person.GroupID) = #{tId}`,
           '0': `Person.GroupID = #{gId}`,
           '2': ' dbo.FUNC_TOP_GROUP(Person.GroupID) = '
        }
        //默认情况走配置
        if (nextJdInfo.some(info-> info.config!=null && /^\{.*\}$/.test(info.config::string))){
            //lcCon->lConditionCode 为 JSON 配置 level: [] 允许执行的级别 1,default: 1 默认显示的人
            //TODO Condition 分支
            const tMap = nextJdInfo.get(0)
            const tConfig = tMap.config::json || {default: '1', level: '0'}
            if (tConfig.default == "2"){
                isDwBm = false
                const tSql = tConfig.sql || ''
                if (tSql!=""){
                    tail = ` and (l.lcjdID=${tMap.nextjdid} and ${configTail[tConfig.default]} ${db.selectInt(tSql)})`
                }
            }else{
                tail = ` and l.lcjdID=${tMap.nextjdid} and ${configTail[tConfig.default]}`
            }
        }else{
            byCom='1'
        }
    }
    let sql = """SELECT Person.id, LoginName login_name, RealName real_name,Person.GroupID group_id, CONCAT(g1.groupname,'/', g.groupname) AS group_name, l.lcjdID jd_id, l.jdmc jd_mc, l.isbx  FROM Person
    INNER JOIN GroupItem g ON Person.GroupID = g.id
    LEFT JOIN  GroupItem g1 ON g.parentid = g1.id
    LEFT JOIN  Lcjd l on  l.lcjdID in (#{nextJdId})
    WHERE Person.Id IN (SELECT PersonId FROM RolePerson
        WHERE (RoleId IN (SELECT roleid FROM RolePermission
    WHERE (PermissionNo in (#{nextJdId})))))
    ${tail}
    ?{ byCom == '1', and dbo.FUNC_TOP_GROUP(Person.GroupID) = #{tId}}
    ?{ byCom == '0', and Person.GroupID = #{gId}}
    ?{loginName != ''and loginName !=null, AND LoginName = #{loginName}} order by P_XH"""
    let list = db.select(sql)
    if ((list==null || list.size()==0 ) && isDwBm){
        return db.select("""SELECT Person.id, LoginName login_name, RealName real_name,Person.GroupID group_id, CONCAT(g1.groupname,'/', g.groupname) AS group_name, l.lcjdID jd_id, l.jdmc jd_mc, l.isbx  FROM Person
            INNER JOIN GroupItem g ON Person.GroupID = g.id
            LEFT JOIN  GroupItem g1 ON g.parentid = g1.id
            LEFT JOIN  Lcjd l on  l.lcjdID in (#{nextJdId})
            WHERE Person.Id IN (SELECT PersonId FROM RolePerson
                WHERE (RoleId IN (SELECT roleid FROM RolePermission
            WHERE (PermissionNo in (#{nextJdId})))))
            and dbo.FUNC_TOP_GROUP(Person.GroupID) = #{tId}
            ?{loginName != ''and loginName !=null, AND LoginName = #{loginName}} order by P_XH""")
    }else{
        return list
    }
}
