{
  "properties" : { },
  "id" : "d57e11f0cbf44018aa83eba1f0025cc0",
  "script" : null,
  "groupId" : "dac0a5ee651d4d4b99e9da01d3d86373",
  "name" : "5.删除流程条件详情",
  "createTime" : 1674795032124,
  "updateTime" : 1673492946226,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : null,
  "path" : "/delCon",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import "@/error" as error;
//检查数据是否存在
const count = db.table("Lc_Condition").where().eq("id",id).count()
if (count>0){
    return db.table("Lc_Condition").primary("id").where().eq("id",id).delete()==1?'删除成功!':'删除失败!'
}else{
    return error('数据不存在', 500)
}
