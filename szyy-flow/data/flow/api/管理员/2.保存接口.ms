{
  "properties" : { },
  "id" : "bc84d2f09d9c42e89b95928f178bdfdb",
  "script" : null,
  "groupId" : "57cc7d1259fb479cadae7b91cc059d9b",
  "name" : "2.保存接口",
  "createTime" : null,
  "updateTime" : 1675300663964,
  "lock" : null,
  "createBy" : null,
  "updateBy" : "errorone",
  "path" : "/saveFlow",
  "method" : "POST",
  "parameters" : [ {
    "name" : "lcid",
    "value" : "9102",
    "description" : "流程ID",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择流程",
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\r\n    \"nodes\": [\r\n        {\r\n            \"id\": \"910201\",\r\n            \"type\": \"beautify-node\",\r\n            \"x\": 120,\r\n            \"y\": 230,\r\n            \"properties\": {\r\n                \"ui\": \"node-red\"\r\n            },\r\n            \"text\": {\r\n                \"x\": 120,\r\n                \"y\": 230,\r\n                \"value\": \"用印申请11\"\r\n            }\r\n        },\r\n        {\r\n            \"id\": \"910202\",\r\n            \"type\": \"beautify-node\",\r\n            \"x\": 350,\r\n            \"y\": 230,\r\n            \"properties\": {\r\n                \"ui\": \"node-red\"\r\n            },\r\n            \"text\": {\r\n                \"x\": 350,\r\n                \"y\": 230,\r\n                \"value\": \"主管部门审核\"\r\n            }\r\n        },\r\n        {\r\n            \"id\": \"910203\",\r\n            \"type\": \"beautify-node\",\r\n            \"x\": 810,\r\n            \"y\": 230,\r\n            \"properties\": {\r\n                \"ui\": \"node-red\"\r\n            },\r\n            \"text\": {\r\n                \"x\": 810,\r\n                \"y\": 230,\r\n                \"value\": \"综合管理部意见\"\r\n            }\r\n        },\r\n        {\r\n            \"id\": \"910206\",\r\n            \"type\": \"beautify-node\",\r\n            \"x\": 580,\r\n            \"y\": 230,\r\n            \"properties\": {\r\n                \"ui\": \"node-red\"\r\n            },\r\n            \"text\": {\r\n                \"x\": 580,\r\n                \"y\": 230,\r\n                \"value\": \"总公司审核\"\r\n            }\r\n        }\r\n    ],\r\n    \"edges\": [\r\n        {\r\n            \"id\": 504,\r\n            \"type\": \"beautify-line\",\r\n            \"sourceNodeId\": \"910201\",\r\n            \"targetNodeId\": \"910203\",\r\n            \"startPoint\": {\r\n                \"x\": 170,\r\n                \"y\": 230\r\n            },\r\n            \"endPoint\": {\r\n                \"x\": 870,\r\n                \"y\": 230\r\n            },\r\n            \"properties\": {},\r\n            \"text\": {\r\n                \"x\": 520,\r\n                \"y\": 529,\r\n                \"value\": \"发起人\"\r\n            },\r\n            \"pointsList\": [\r\n                {\r\n                    \"x\": 170,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 170,\r\n                    \"y\": 529\r\n                },\r\n                {\r\n                    \"x\": 870,\r\n                    \"y\": 529\r\n                },\r\n                {\r\n                    \"x\": 870,\r\n                    \"y\": 230\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"id\": 513,\r\n            \"type\": \"beautify-line\",\r\n            \"sourceNodeId\": \"910201\",\r\n            \"targetNodeId\": \"910202\",\r\n            \"startPoint\": {\r\n                \"x\": 170,\r\n                \"y\": 230\r\n            },\r\n            \"endPoint\": {\r\n                \"x\": 170,\r\n                \"y\": 230\r\n            },\r\n            \"properties\": {},\r\n            \"text\": {\r\n                \"x\": 123,\r\n                \"y\": 230,\r\n                \"value\": \"发起人\"\r\n            },\r\n            \"pointsList\": [\r\n                {\r\n                    \"x\": 170,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 290,\r\n                    \"y\": 230\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"id\": 514,\r\n            \"type\": \"beautify-line\",\r\n            \"sourceNodeId\": \"910202\",\r\n            \"targetNodeId\": \"910206\",\r\n            \"startPoint\": {\r\n                \"x\": 410,\r\n                \"y\": 230\r\n            },\r\n            \"endPoint\": {\r\n                \"x\": 630,\r\n                \"y\": 230\r\n            },\r\n            \"properties\": {},\r\n            \"text\": {\r\n                \"x\": 520,\r\n                \"y\": 111,\r\n                \"value\": \"主管部门审核\"\r\n            },\r\n            \"pointsList\": [\r\n                {\r\n                    \"x\": 410,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 410,\r\n                    \"y\": 111\r\n                },\r\n                {\r\n                    \"x\": 630,\r\n                    \"y\": 111\r\n                },\r\n                {\r\n                    \"x\": 630,\r\n                    \"y\": 230\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"id\": 516,\r\n            \"type\": \"beautify-line\",\r\n            \"sourceNodeId\": \"910201\",\r\n            \"targetNodeId\": \"910202\",\r\n            \"startPoint\": {\r\n                \"x\": 170,\r\n                \"y\": 230\r\n            },\r\n            \"endPoint\": {\r\n                \"x\": 170,\r\n                \"y\": 230\r\n            },\r\n            \"properties\": {},\r\n            \"text\": {\r\n                \"x\": 123,\r\n                \"y\": 230,\r\n                \"value\": \"发起人\"\r\n            },\r\n            \"pointsList\": [\r\n                {\r\n                    \"x\": 170,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 290,\r\n                    \"y\": 230\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"id\": 903,\r\n            \"type\": \"beautify-line\",\r\n            \"sourceNodeId\": \"910202\",\r\n            \"targetNodeId\": \"910203\",\r\n            \"startPoint\": {\r\n                \"x\": 410,\r\n                \"y\": 230\r\n            },\r\n            \"endPoint\": {\r\n                \"x\": 870,\r\n                \"y\": 230\r\n            },\r\n            \"properties\": {},\r\n            \"text\": {\r\n                \"x\": 640.5,\r\n                \"y\": 419,\r\n                \"value\": \"主管部门审核\"\r\n            },\r\n            \"pointsList\": [\r\n                {\r\n                    \"x\": 410,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 411,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 411,\r\n                    \"y\": 419\r\n                },\r\n                {\r\n                    \"x\": 870,\r\n                    \"y\": 419\r\n                },\r\n                {\r\n                    \"x\": 870,\r\n                    \"y\": 230\r\n                }\r\n            ]\r\n        },\r\n        {\r\n            \"id\": 1025,\r\n            \"type\": \"beautify-line\",\r\n            \"sourceNodeId\": \"910206\",\r\n            \"targetNodeId\": \"910203\",\r\n            \"startPoint\": {\r\n                \"x\": 630,\r\n                \"y\": 230\r\n            },\r\n            \"endPoint\": {\r\n                \"x\": 630,\r\n                \"y\": 230\r\n            },\r\n            \"properties\": {},\r\n            \"text\": {\r\n                \"x\": 125,\r\n                \"y\": 230,\r\n                \"value\": \"总公司审核\"\r\n            },\r\n            \"pointsList\": [\r\n                {\r\n                    \"x\": 630,\r\n                    \"y\": 230\r\n                },\r\n                {\r\n                    \"x\": 750,\r\n                    \"y\": 230\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"message\": \"success\",\n    \"result\": 0,\n    \"success\": true,\n    \"timestamp\": 1675241897651\n}",
  "description" : null,
  "requestBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "nodes",
      "value" : "",
      "description" : "节点信息",
      "required" : true,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "expression",
      "error" : "流程阶段不能为空",
      "expression" : "value!=null && value.size()>0",
      "children" : [ {
        "name" : "",
        "value" : "",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ {
          "name" : "id",
          "value" : "910201",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "type",
          "value" : "beautify-node",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "x",
          "value" : "120",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "y",
          "value" : "230",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "properties",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "ui",
            "value" : "node-red",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          } ]
        }, {
          "name" : "text",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "x",
            "value" : "120",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "y",
            "value" : "230",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "value",
            "value" : "用印申请11",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          } ]
        } ]
      } ]
    }, {
      "name" : "edges",
      "value" : "",
      "description" : "条件信息",
      "required" : true,
      "dataType" : "Array",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "expression",
      "error" : "流程条件不能为空",
      "expression" : "value!=null && value.size()>0",
      "children" : [ {
        "name" : "",
        "value" : "",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ {
          "name" : "id",
          "value" : "504",
          "description" : "",
          "required" : false,
          "dataType" : "Integer",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "type",
          "value" : "beautify-line",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "sourceNodeId",
          "value" : "910201",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "targetNodeId",
          "value" : "910203",
          "description" : "",
          "required" : false,
          "dataType" : "String",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "startPoint",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "x",
            "value" : "170",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "y",
            "value" : "230",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          } ]
        }, {
          "name" : "endPoint",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "x",
            "value" : "870",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "y",
            "value" : "230",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          } ]
        }, {
          "name" : "properties",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ ]
        }, {
          "name" : "text",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Object",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "x",
            "value" : "520",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "y",
            "value" : "529",
            "description" : "",
            "required" : false,
            "dataType" : "Integer",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          }, {
            "name" : "value",
            "value" : "发起人",
            "description" : "",
            "required" : false,
            "dataType" : "String",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ ]
          } ]
        }, {
          "name" : "pointsList",
          "value" : "",
          "description" : "",
          "required" : false,
          "dataType" : "Array",
          "type" : null,
          "defaultValue" : null,
          "validateType" : "",
          "error" : "",
          "expression" : "",
          "children" : [ {
            "name" : "",
            "value" : "",
            "description" : "",
            "required" : false,
            "dataType" : "Object",
            "type" : null,
            "defaultValue" : null,
            "validateType" : "",
            "error" : "",
            "expression" : "",
            "children" : [ {
              "name" : "x",
              "value" : "170",
              "description" : "",
              "required" : false,
              "dataType" : "Integer",
              "type" : null,
              "defaultValue" : null,
              "validateType" : "",
              "error" : "",
              "expression" : "",
              "children" : [ ]
            }, {
              "name" : "y",
              "value" : "230",
              "description" : "",
              "required" : false,
              "dataType" : "Integer",
              "type" : null,
              "defaultValue" : null,
              "validateType" : "",
              "error" : "",
              "expression" : "",
              "children" : [ ]
            } ]
          } ]
        } ]
      } ]
    } ]
  },
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "result",
      "value" : "0",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "success",
      "value" : "true",
      "description" : "",
      "required" : false,
      "dataType" : "Boolean",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1675241897651",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import "@/error" as error;
const { nodes,edges } = body;
const lc = db.table("Lcdefine").where().eq("lcid",lcid).selectOne()
//阶段信息
const jdList = db.table("Lcjd").where().eq("lcDefineid", lcid).orderBy("type").select();
//阶段条件
const cndList = db.table("Lc_Condition").where().eq("lcDefineid", lcid).orderBy("id").select();
//
if (nodes.size()!=jdList.size()){
    return error('流程阶段信息不一致!')
}
const nLine = edges.filter(it->cndList.some(cnd->cnd.jdid!=it.sourceNodeId && cnd.nextjdid != it.targetNodeId))
if (nLine.size() !=edges.size()){
    return error('流程连接信息不一致!')
}
const drag = db.table("lc_wf_draw").where().eq("pId",lcid).selectOne();
let id = null;
if (drag!=null){
    id = drag.id
}
return db.table("lc_wf_draw").primary("id").save({
    id, name: lc.lcname, content: body::stringify, pId: lc.lcid
})