{
  "properties" : { },
  "id" : "e745378b93314961afc73fdace3ad38a",
  "script" : null,
  "groupId" : "57cc7d1259fb479cadae7b91cc059d9b",
  "name" : "1.获取流程图形节点信息",
  "createTime" : null,
  "updateTime" : 1675241998354,
  "lock" : "0",
  "createBy" : "errorone",
  "updateBy" : "errorone",
  "path" : "/getFlowView",
  "method" : "GET",
  "parameters" : [ {
    "name" : "lcid",
    "value" : "9102",
    "description" : "流程ID",
    "required" : true,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : "请选择流程",
    "expression" : null,
    "children" : null
  } ],
  "options" : [ ],
  "requestBody" : "{\n\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"message\": \"success\",\n    \"result\": {\n        \"id\": 0,\n        \"name\": \"用印申请流程\",\n        \"content\": \"{\\\"nodes\\\":[{\\\"id\\\":\\\"910201\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":120,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":120,\\\"y\\\":230,\\\"value\\\":\\\"用印申请11\\\"}},{\\\"id\\\":\\\"910202\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":350,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":350,\\\"y\\\":230,\\\"value\\\":\\\"主管部门审核\\\"}},{\\\"id\\\":\\\"910203\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":810,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":810,\\\"y\\\":230,\\\"value\\\":\\\"综合管理部意见\\\"}},{\\\"id\\\":\\\"910206\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":580,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":580,\\\"y\\\":230,\\\"value\\\":\\\"总公司审核\\\"}}],\\\"edges\\\":[{\\\"id\\\":504,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910201\\\",\\\"targetNodeId\\\":\\\"910203\\\",\\\"startPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":870,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":520,\\\"y\\\":529,\\\"value\\\":\\\"发起人\\\"},\\\"pointsList\\\":[{\\\"x\\\":170,\\\"y\\\":230},{\\\"x\\\":170,\\\"y\\\":529},{\\\"x\\\":870,\\\"y\\\":529},{\\\"x\\\":870,\\\"y\\\":230}]},{\\\"id\\\":513,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910201\\\",\\\"targetNodeId\\\":\\\"910202\\\",\\\"startPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":123,\\\"y\\\":230,\\\"value\\\":\\\"发起人\\\"},\\\"pointsList\\\":[{\\\"x\\\":170,\\\"y\\\":230},{\\\"x\\\":290,\\\"y\\\":230}]},{\\\"id\\\":514,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910202\\\",\\\"targetNodeId\\\":\\\"910206\\\",\\\"startPoint\\\":{\\\"x\\\":410,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":630,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":520,\\\"y\\\":111,\\\"value\\\":\\\"主管部门审核\\\"},\\\"pointsList\\\":[{\\\"x\\\":410,\\\"y\\\":230},{\\\"x\\\":410,\\\"y\\\":111},{\\\"x\\\":630,\\\"y\\\":111},{\\\"x\\\":630,\\\"y\\\":230}]},{\\\"id\\\":516,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910201\\\",\\\"targetNodeId\\\":\\\"910202\\\",\\\"startPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":123,\\\"y\\\":230,\\\"value\\\":\\\"发起人\\\"},\\\"pointsList\\\":[{\\\"x\\\":170,\\\"y\\\":230},{\\\"x\\\":290,\\\"y\\\":230}]},{\\\"id\\\":903,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910202\\\",\\\"targetNodeId\\\":\\\"910203\\\",\\\"startPoint\\\":{\\\"x\\\":410,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":870,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":640,\\\"y\\\":419,\\\"value\\\":\\\"主管部门审核\\\"},\\\"pointsList\\\":[{\\\"x\\\":410,\\\"y\\\":230},{\\\"x\\\":411,\\\"y\\\":230},{\\\"x\\\":411,\\\"y\\\":419},{\\\"x\\\":870,\\\"y\\\":419},{\\\"x\\\":870,\\\"y\\\":230}]},{\\\"id\\\":1025,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910206\\\",\\\"targetNodeId\\\":\\\"910203\\\",\\\"startPoint\\\":{\\\"x\\\":630,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":630,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":125,\\\"y\\\":230,\\\"value\\\":\\\"总公司审核\\\"},\\\"pointsList\\\":[{\\\"x\\\":630,\\\"y\\\":230},{\\\"x\\\":750,\\\"y\\\":230}]}]}\",\n        \"cTime\": null,\n        \"uTime\": null,\n        \"cUId\": null,\n        \"uUId\": null,\n        \"delFlag\": 0,\n        \"pId\": 9102\n    },\n    \"success\": true,\n    \"timestamp\": 1675241984195\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "result",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ {
        "name" : "id",
        "value" : "0",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "name",
        "value" : "用印申请流程",
        "description" : "",
        "required" : false,
        "dataType" : "String",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "content",
        "value" : "{\\\"nodes\\\":[{\\\"id\\\":\\\"910201\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":120,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":120,\\\"y\\\":230,\\\"value\\\":\\\"用印申请11\\\"}},{\\\"id\\\":\\\"910202\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":350,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":350,\\\"y\\\":230,\\\"value\\\":\\\"主管部门审核\\\"}},{\\\"id\\\":\\\"910203\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":810,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":810,\\\"y\\\":230,\\\"value\\\":\\\"综合管理部意见\\\"}},{\\\"id\\\":\\\"910206\\\",\\\"type\\\":\\\"beautify-node\\\",\\\"x\\\":580,\\\"y\\\":230,\\\"properties\\\":{\\\"ui\\\":\\\"node-red\\\"},\\\"text\\\":{\\\"x\\\":580,\\\"y\\\":230,\\\"value\\\":\\\"总公司审核\\\"}}],\\\"edges\\\":[{\\\"id\\\":504,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910201\\\",\\\"targetNodeId\\\":\\\"910203\\\",\\\"startPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":870,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":520,\\\"y\\\":529,\\\"value\\\":\\\"发起人\\\"},\\\"pointsList\\\":[{\\\"x\\\":170,\\\"y\\\":230},{\\\"x\\\":170,\\\"y\\\":529},{\\\"x\\\":870,\\\"y\\\":529},{\\\"x\\\":870,\\\"y\\\":230}]},{\\\"id\\\":513,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910201\\\",\\\"targetNodeId\\\":\\\"910202\\\",\\\"startPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":123,\\\"y\\\":230,\\\"value\\\":\\\"发起人\\\"},\\\"pointsList\\\":[{\\\"x\\\":170,\\\"y\\\":230},{\\\"x\\\":290,\\\"y\\\":230}]},{\\\"id\\\":514,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910202\\\",\\\"targetNodeId\\\":\\\"910206\\\",\\\"startPoint\\\":{\\\"x\\\":410,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":630,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":520,\\\"y\\\":111,\\\"value\\\":\\\"主管部门审核\\\"},\\\"pointsList\\\":[{\\\"x\\\":410,\\\"y\\\":230},{\\\"x\\\":410,\\\"y\\\":111},{\\\"x\\\":630,\\\"y\\\":111},{\\\"x\\\":630,\\\"y\\\":230}]},{\\\"id\\\":516,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910201\\\",\\\"targetNodeId\\\":\\\"910202\\\",\\\"startPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":170,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":123,\\\"y\\\":230,\\\"value\\\":\\\"发起人\\\"},\\\"pointsList\\\":[{\\\"x\\\":170,\\\"y\\\":230},{\\\"x\\\":290,\\\"y\\\":230}]},{\\\"id\\\":903,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910202\\\",\\\"targetNodeId\\\":\\\"910203\\\",\\\"startPoint\\\":{\\\"x\\\":410,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":870,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":640,\\\"y\\\":419,\\\"value\\\":\\\"主管部门审核\\\"},\\\"pointsList\\\":[{\\\"x\\\":410,\\\"y\\\":230},{\\\"x\\\":411,\\\"y\\\":230},{\\\"x\\\":411,\\\"y\\\":419},{\\\"x\\\":870,\\\"y\\\":419},{\\\"x\\\":870,\\\"y\\\":230}]},{\\\"id\\\":1025,\\\"type\\\":\\\"beautify-line\\\",\\\"sourceNodeId\\\":\\\"910206\\\",\\\"targetNodeId\\\":\\\"910203\\\",\\\"startPoint\\\":{\\\"x\\\":630,\\\"y\\\":230},\\\"endPoint\\\":{\\\"x\\\":630,\\\"y\\\":230},\\\"properties\\\":{},\\\"text\\\":{\\\"x\\\":125,\\\"y\\\":230,\\\"value\\\":\\\"总公司审核\\\"},\\\"pointsList\\\":[{\\\"x\\\":630,\\\"y\\\":230},{\\\"x\\\":750,\\\"y\\\":230}]}]}",
        "description" : "",
        "required" : false,
        "dataType" : "String",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "cTime",
        "value" : "null",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "uTime",
        "value" : "null",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "cUId",
        "value" : "null",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "uUId",
        "value" : "null",
        "description" : "",
        "required" : false,
        "dataType" : "Object",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "delFlag",
        "value" : "0",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      }, {
        "name" : "pId",
        "value" : "9102",
        "description" : "",
        "required" : false,
        "dataType" : "Integer",
        "type" : null,
        "defaultValue" : null,
        "validateType" : "",
        "error" : "",
        "expression" : "",
        "children" : [ ]
      } ]
    }, {
      "name" : "success",
      "value" : "true",
      "description" : "",
      "required" : false,
      "dataType" : "Boolean",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "timestamp",
      "value" : "1675241984195",
      "description" : "",
      "required" : false,
      "dataType" : "Long",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
const drag = db.table("lc_wf_draw").where().eq("pId",lcid).selectOne();
if (drag!=null){
    return drag.content::json;
}
//阶段信息
const jdList = db.table("Lcjd").where().eq("lcDefineid", lcid).orderBy("type").select();
//阶段条件
const cndList = db.table("Lc_Condition").where().eq("lcDefineid", lcid).orderBy("id").select();
//


return {
    nodes: jdList.map(j->{
        return {
            id:j.lcjdid::string,
            text: {value: j.jdmc},
            type: 'beautify-node'
        }
    }),
    edges: select c.id, c.jdid::string sourceNodeId, c.nextjdid::string targetNodeId,"beautify-line" type, c.jdmc text  from cndList c
}