package com.soft.gcc.xtbg.sjks.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class ParseUtil {

    static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static int tryParseInt(String value) {
        return tryParseInt(value, 0);
    }

    public static int tryParseInt(String value, int defaultVal) {
        try {
            if (value != null && !"".equals(value) && !"undefined".equals(value) && !"null".equals(value)) {
                return Integer.parseInt(value);
            } else {
                return defaultVal;
            }
        } catch (NumberFormatException e) {
            return defaultVal;
        }
    }

    public static String tryParseString(String value) {
        return tryParseString(value, "");
    }

    public static String tryParseString(String value, String defaultVal) {
        if (value != null && !"".equals(value) && !"undefined".equals(value) && !"null".equals(value)) {
            return value;
        } else {
            return defaultVal;
        }
    }

    //去掉两头所有的指定字符串
    //例如： this.trim(“**1234***”,'*');
    //      return "1234";
    public static String trim(String str, char c) {
        char[] value = str.toCharArray();
        int len = value.length;
        int st = 0;
        char[] val = value;    /* avoid getfield opcode */

        while ((st < len) && (val[st] == c)) {
            st++;
        }
        while ((st < len) && (val[len - 1] == ' ')) {
            len--;
        }
        return ((st > 0) || (len < value.length)) ? str.substring(st, len) : str;
    }

    public static String trimNew(String selectGridIDs, char c) {
        if (selectGridIDs == null || "".equals(selectGridIDs) || "undefined".equals(selectGridIDs) || "null".equals(selectGridIDs)) {
            return "";
        }

        if (selectGridIDs.charAt(selectGridIDs.length() - 1) == c) {
            selectGridIDs = selectGridIDs.substring(0, selectGridIDs.length() - 1);
        }
        if (selectGridIDs.charAt(0) == c) {
            selectGridIDs = selectGridIDs.substring(1, selectGridIDs.length());
        }

        return selectGridIDs;
    }

    public static String trimNewEnd(String selectGridIDs, char c) {

        if (selectGridIDs.charAt(selectGridIDs.length() - 1) == c) {
            selectGridIDs = selectGridIDs.substring(0, selectGridIDs.length() - 1);
        }

        return selectGridIDs;
    }

    public static StringBuilder ToRequest(boolean success, String text, Object t) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(String.valueOf(success).toLowerCase(Locale.ROOT));
        jsonData.append(",\"text\":\"");
        jsonData.append(text);
        jsonData.append("\",\"data\":");
        if (t != null) {
            jsonData.append(JSON.toJSON(t));
        } else {
            jsonData.append("{}");
        }
        jsonData.append("}");
        return jsonData;
    }

    public static StringBuilder ToFormPanel(boolean success, String text, Object t) {
        StringBuilder jsonData = new StringBuilder();
        jsonData.append("{");
        jsonData.append("\"success\":");
        jsonData.append(String.valueOf(success).toLowerCase(Locale.ROOT));
        jsonData.append(",\"text\":\"");
        jsonData.append(text);
        jsonData.append("\",\"data\":[");
        if (t != null) {
            jsonData.append(JSON.toJSON(t));
        } else {
            jsonData.append("{}");
        }
        jsonData.append("]}");
        return jsonData;
    }


    /**
     * 判断两个日期相差多少秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer toSeconds(Date startTime, Date endTime) {
        long startDateTime = startTime.getTime();
        long endDateTime = endTime.getTime();
        int diffSeconds = (int) ((endDateTime - startDateTime) / 1000);  // 判断两个时间相差多少秒
        return diffSeconds;
    }


    public static boolean isIDNumber(String IDNumber) {
        if (StringUtils.isEmpty(IDNumber)) {
            return false;
        }
        System.out.println(IDNumber.length());
        if (IDNumber.length() != 18 && IDNumber.length() != 15) {
            return false;
        }
        String re = "";
        if (IDNumber.length() == 18) {
            re = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        } else if (IDNumber.length() == 15) {
            re = "^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$";
        }
        Pattern pa = Pattern.compile(re);
        Matcher m = pa.matcher(IDNumber);
        return m.matches();


    }

    static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static String getCurrate() {
        Date date = new Date();
        return dateFormat.format(date);
    }

    public static String getStringDate(Date date) {
        return dateFormat.format(date);
    }

    public static String getLikeString(String s) {
        if (StringUtils.isEmpty(s)) {
            s = "";
        }
        return "%" + s + "%";
    }


//    public static void main(String[] args) {
//       String s = encryptIdCard("330722198201012817");
//    }

}
