package com.soft.gcc.xtbg.sjks.params;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 生成二维码请求参数
 * <AUTHOR>
 * @date 2025/7/31
 */
@Data
public class GenerateQrcodeParams implements Serializable {
    
    /**
     * 生成数量
     */
    @NotNull(message = "生成数量不能为空")
    @Min(value = 1, message = "生成数量不能小于1")
    @Max(value = 1000, message = "生成数量不能大于1000")
    private Integer quantity;
}
