package com.soft.gcc.xtbg.sjks.util;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class InfluxDBUtil {

    public static String ipAddress;

    public static String token;
    public static String bucket;
    public static String org;
    public static String measurement = "vehicle_position";

    private static InfluxDBClient influxDBClient = null;

    @Value("${InfluxDBConfig.ipAddress}")
    public void setIpAddress(String ipAddress) {
        InfluxDBUtil.ipAddress = ipAddress;
    }
    @Value("${InfluxDBConfig.token}")
    public void setToken(String token) {
        InfluxDBUtil.token = token;
    }
    @Value("${InfluxDBConfig.bucket}")
    public void setBucket(String bucket) {
        InfluxDBUtil.bucket = bucket;
    }
    @Value("${InfluxDBConfig.org}")
    public void setOrg(String org) {
        InfluxDBUtil.org = org;
    }
//    @Value("${InfluxDBConfig.measurement}")
//    public void setMeasurement(String measurement) {
//        LzclInfluxDBUtil.measurement = measurement;
//    }

    //连接
    public static InfluxDBClient getInfluxDBClient(){
        if(influxDBClient==null) {
            influxDBClient = InfluxDBClientFactory.create(ipAddress, token.toCharArray());
        }
        return influxDBClient;
    }

    //重连
    public static void reloadInfluxDBClinet() {
        try {
            influxDBClient.close();
        } catch (Exception e1) {
        }
        influxDBClient = null;
        getInfluxDBClient();
    }

}
