package com.soft.gcc.xtbg.sjks.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.base.entity.SjksBaseEntity;
import lombok.Data;

/**
 * 设计-考试-二维码主表
 * <AUTHOR>
 * @TableName sj_ks_qrcode
 */
@TableName(value ="sj_ks_qrcode")
@Data
public class SjKsQrcode extends SjksBaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年份
     */
    @TableField(value = "year")
    private Integer year;

    /**
     * 月份
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 批次
     */
    @TableField(value = "batch")
    private Integer batch;

    /**
     * 生成二维码数量
     */
    @TableField(value = "qr_code_number")
    private Integer qrCodeNumber;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}