package com.soft.framework.controller.CPSoft;

import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.gcc.base.entity.CPS_VER_CTRL;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/VCtrl" )
public class VerCtrlController {
    @RequestMapping("/ModifyVER")
    @ResponseBody
    @RepeatSubmit
    public AjaxResult ModifyVER(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();
            String strsql="select * from cps_ver_ctrl";
            List<CPS_VER_CTRL> list=sqlhelper.GetObjectList(CPS_VER_CTRL.class,strsql);

            if(list.size()==0)
            {
                CPS_VER_CTRL entity=new CPS_VER_CTRL();
                String verc=params.get("VC_VER");
                sqlhelper.ExecuteNoQuery("insert into cps_ver_ctrl(VC_VER) values('"+verc+"')");
            }else
            {
                CPS_VER_CTRL entity=list.get(0);
                String verc=params.get("VC_VER");
                sqlhelper.ExecuteNoQuery("update cps_ver_ctrl set VC_VER='"+verc+"'");
            }

            ajaxResult=AjaxResult.success("保存版本信息成功!");
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetVERList",produces ={"application/json;charset=UTF-8"})
    @ResponseBody
    public AjaxResult GetVERList(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();
            String strsql="select * from cps_ver_ctrl";
            List<CPS_VER_CTRL> list=sqlhelper.GetObjectList(CPS_VER_CTRL.class,strsql);
            ajaxResult=AjaxResult.extgrid(CPS_VER_CTRL.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping("/GetVER")
    @ResponseBody
    public AjaxResult GetVER() {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();
            String strsql="select * from cps_ver_ctrl";
            List<CPS_VER_CTRL> list=sqlhelper.GetObjectList(CPS_VER_CTRL.class,strsql);
            ajaxResult=AjaxResult.success("获取信息成功!",list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
