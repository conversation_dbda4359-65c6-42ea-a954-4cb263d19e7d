package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.FunctionItem;
import com.yyszc.wpbase.entity.vFunctionItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/ResMan")
@Api(tags = "基本框架接口->资源管理接口")
public class ResManController {
    private Boolean GatherParams2Obj(Map<String, String> params, FunctionItem entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("Id"))) {
                entity.setId(params.get("Id"));
            }
            if (!StringUtil.IsNull(params.get("Title"))) {
                entity.setTitle(params.get("Title"));
            }
            if (!StringUtil.IsNull(params.get("Url"))) {
                entity.setUrl(params.get("Url"));
            }
            if (!StringUtil.IsNull(params.get("ParentId"))) {
                entity.setParentId(params.get("ParentId"));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("OrderNumber"))) {
                entity.setOrderNumber(Integer.parseInt(params.get("OrderNumber")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("IsPublic"))) {
                entity.setIsPublic(Integer.parseInt(params.get("IsPublic")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddRes", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddRes", notes = "新增资源接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddRes(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            FunctionItem entity = new FunctionItem();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，获取传输参数失败！");
                return ajaxResult;
            }

            String fid = "";
            fid = WpServiceHelper.AddFunctionItem(entity);
            if (fid == null || fid.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，新增权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(fid);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyRes", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyRes", notes = "修改资源接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyRes(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String ResId = params.get("ResId");

            FunctionItem entity = null;
            entity = WpServiceHelper.GetFunctionItemById(ResId);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取资源信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateFunctionItem(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改资源信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getId().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteRes", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteRes", notes = "删除资源接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteRes(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String ResId = request.getParameter("ResId");

            Boolean uflag = false;

            uflag = WpServiceHelper.DeleteFunctionItemById(ResId);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除资源成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String ResId = request.getParameter("ResId");
            String ResName = request.getParameter("ResName");
            String ResUrl = request.getParameter("ResUrl");

            strsql.append("select * from vFunctionItem where module_id='" + ConfigHelper.getModuleId() + "'");
            if (!StringUtil.IsNullOrEmpty(ResId)) {
                strsql.append(" and Id like '%" + ResId + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(ResName)) {
                strsql.append(" and Title like '%" + ResName + "%'");
            }
            if (!StringUtil.IsNullOrEmpty(ResUrl)) {
                strsql.append(" and Url like '%" + ResUrl + "%'");
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }
            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetResList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetResList", notes = "获取资源列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetResList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<FunctionItem> list = null;
            list = WpServiceHelper.GetFunctionItemList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(FunctionItem.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetResById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetResById", notes = "获取指定具有指定ID的资源接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetResById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String ResId = request.getParameter("ResId");
        if (StringUtil.IsNullOrEmpty(ResId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            FunctionItem entity = null;

            entity = WpServiceHelper.GetFunctionItemById(ResId);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(FunctionItem.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出资源信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("Id", "资源编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("Title", "资源名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("Url", "资源链接", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("ParentId", "上级资源", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("OrderNumber", "显示序号", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("IsPublic", "是否公开", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "操作资源信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetResByMark", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetResByMark", notes = "获取指定资源连接串等信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetResByMark(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strPath = "../../Page/";
        try {
            String mRoot = request.getParameter("mRoot");
            String mMenu = request.getParameter("mMenu");
            if (StringUtil.IsNullOrEmpty(mRoot) || StringUtil.IsNullOrEmpty(mMenu)) {
                ajaxResult = AjaxResult.error("传输参数有误!");
                return ajaxResult;
            }

            vFunctionItem functionItem = null;

            functionItem = WpServiceHelper.GetFunctionItemByMark(mMenu, mRoot);
            if (functionItem == null) {
                ajaxResult = AjaxResult.error("操作失败，获取权限信息失败！");
                return ajaxResult;
            }
            functionItem.setUrl(strPath + functionItem.getUrl());

            ajaxResult = AjaxResult.success("获取信息成功！", functionItem);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
