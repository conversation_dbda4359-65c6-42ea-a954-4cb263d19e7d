package com.soft.framework.controller.BaseOpt;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.wpbase.entity.Lc_currentState;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/CurrentState")
@Api(tags = "基本框架接口->流程状态管理接口")
public class CurrentStateController {
    private Boolean GatherParams2Obj(Map<String, String> params, Lc_currentState entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("Lc_Name"))) {
                entity.setLc_Name(params.get("Lc_Name"));
            }
            if (!StringUtil.IsNull(params.get("sendPerson"))) {
                entity.setSendPerson(params.get("sendPerson"));
            }
            if (!StringUtil.IsNull(params.get("sendPersonZgh"))) {
                entity.setSendPersonZgh(params.get("sendPersonZgh"));
            }
            if (!StringUtil.IsNull(params.get("AllPersonZgh"))) {
                entity.setAllPersonZgh(params.get("AllPersonZgh"));
            }
            if (!StringUtil.IsNull(params.get("lc_jdmc"))) {
                entity.setLc_jdmc(params.get("lc_jdmc"));
            }
            if (!StringUtil.IsNull(params.get("lc_tojdid"))) {
                entity.setLc_tojdid(params.get("lc_tojdid"));
            }
            if (!StringUtil.IsNull(params.get("BXType"))) {
                entity.setBXType(params.get("BXType"));
            }
            if (!StringUtil.IsNull(params.get("PNO"))) {
                entity.setPNO(params.get("PNO"));
            }
            if (!StringUtil.IsNull(params.get("sendGroupIDs"))) {
                entity.setSendGroupIDs(params.get("sendGroupIDs"));
            }

            if (!StringUtil.IsNullOrEmpty(params.get("Lc_defineID"))) {
                entity.setLc_defineID(Integer.parseInt(params.get("Lc_defineID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("ywID"))) {
                entity.setYwID(Integer.parseInt(params.get("ywID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("isMany"))) {
                entity.setIsMany(Integer.parseInt(params.get("isMany")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("lc_jdid"))) {
                entity.setLc_jdid(Integer.parseInt(params.get("lc_jdid")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("lc_isback"))) {
                entity.setLc_isback(Integer.parseInt(params.get("lc_isback")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("isOtherAdd"))) {
                entity.setIsOtherAdd(Integer.parseInt(params.get("isOtherAdd")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("number"))) {
                entity.setNumber(Integer.parseInt(params.get("number")));
            }


            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddCurrentState", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddCurrentState", notes = "新增流程状态信息接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddCurrentState(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            Lc_currentState entity = new Lc_currentState();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Integer rid = -1;
            rid = WpServiceHelper.AddLc_currentState(entity);
            if (rid == null || rid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增流程信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(rid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyCurrentState", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyCurrentState", notes = "修改流程状态信息接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyCurrentState(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            Integer iid = Integer.parseInt(Id);

            Lc_currentState entity = null;
            entity = WpServiceHelper.GetLc_currentStateById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程状态信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateLc_currentState(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改流程状态信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteCurrentState", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteCurrentState", notes = "删除流程状态信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteCurrentState(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String Id = request.getParameter("ID");
            int iid = Integer.parseInt(Id);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteLc_currentStateById(iid);
            if (uflag == null || uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除流程状态信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除流程状态信息成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String LcId = request.getParameter("LcId");
            String YwId = request.getParameter("YwId");

            strsql.append("select * from Lc_currentState where 1=1 ");
            if (!StringUtil.IsNullOrEmpty(LcId)) {
                strsql.append(" and Lc_defineID=" + LcId);
            }
            if (!StringUtil.IsNullOrEmpty(YwId)) {
                strsql.append(" and ywID=" + YwId);
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetCurrentStateList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetCurrentStateList", notes = "获取当前流程状态信息列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetCurrentStateList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<Lc_currentState> list = null;
            list = WpServiceHelper.GetLc_currentStateList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程状态信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(Lc_currentState.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetCurrentStateById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetCurrentStateById", notes = "获取指定具有指定ID的流程状态信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetCurrentStateById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer iid = Integer.parseInt(Id);

            Lc_currentState entity = null;

            entity = WpServiceHelper.GetLc_currentStateById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程状态信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(Lc_currentState.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
