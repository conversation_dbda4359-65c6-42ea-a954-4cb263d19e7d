package com.soft.framework.controller.BaseOpt;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/RolePerm")
@Api(tags = "基本框架接口->角色权限操作接口")
public class RolePermController {
    @RequestMapping(value = "/GetRolePermList_Y", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetRolePermList_Y", notes = "获取当前角色具有的权限的接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetRolePermList_Y(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String Module = request.getParameter("RoleKind");
        String PermMark = request.getParameter("PermMark");
        if (StringUtil.IsNullOrEmpty(RoleId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            strsql = "select a.* from Permission a where 1=1 ";
            if (!StringUtil.IsNullOrEmpty(Module) && !Module.equals("0")) {
                strsql += " and a.PermissionKind='" + Module + "'";
            }
            if (!StringUtil.IsNullOrEmpty(PermMark)) {
                strsql += " and a.Operation like '" + PermMark + "'";
            }
            strsql += " and a.PermissionNo in(select PermissionNo from rolePermission where RoleId='" + RoleId + "')";

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/GetRolePermList_W", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetRolePermList_W", notes = "获取当前角色不具有的权限的接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetRolePermList_W(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);


        String RoleId = request.getParameter("RoleId");
        String Module = request.getParameter("RoleKind");
        String PermMark = request.getParameter("PermMark");
        if (StringUtil.IsNullOrEmpty(RoleId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        String basesql = "";
        String orderstr = "";
        String msgstr = "";
        int rcount = 0;

        basesql = "select a.* from Permission a where 1=1 ";
        if (!StringUtil.IsNullOrEmpty(Module) && !Module.equals("0")) {
            basesql += " and a.PermissionKind='" + Module + "'";
        }
        if (!StringUtil.IsNullOrEmpty(PermMark)) {
            basesql += " and a.Operation like '" + PermMark + "'";
        }
        basesql += " and a.PermissionNo not in(select PermissionNo from rolePermission where RoleId='" + RoleId + "')";
        orderstr += "order by a.PermissionNo asc";

        try {
            SqlHelper sqlhelper = new SqlHelper();

            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult.toString();
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt, rcount);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/AddRolePerm", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddRolePerm", notes = "角色添加具体权限接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult AddRolePerm(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String PermissionNo = request.getParameter("PermissionNo");

        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(PermissionNo)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);

            Boolean uflag = false;
            uflag = WpServiceHelper.AddRolePermission(iroleid, PermissionNo);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("添加角色权限成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteRolePerm", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteRolePerm", notes = "角色删除具体权限接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteRolePerm(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String PermissionNo = request.getParameter("PermissionNo");

        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(PermissionNo)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteRolePermission(iroleid, PermissionNo);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色权限信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("去除角色权限成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
