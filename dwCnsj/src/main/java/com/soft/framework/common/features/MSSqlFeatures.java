package com.soft.framework.common.features;

import com.soft.framework.helper.MetaHelper;

/**
 * Created by Administrator on 2016/1/26.
 */
public class MSSqlFeatures implements IDBFeatures {
    public String FormatDateToStr(int strlen, String colname) {
        String strret = colname;
        if (strlen == 8) {
            strret = "replace(convert(varchar(10)," + colname + ",120),'-','') as " + colname;
        } else if (strlen == 10) {
            strret = "convert(varchar(10)," + colname + ",120) as " + colname;
        } else if (strlen == 16) {
            strret = "convert(varchar(16)," + colname + ",120) as " + colname;
        } else if (strlen == 19) {
            strret = "convert(varchar(19)," + colname + ",120) as " + colname;
        } else if (strlen>20) {
            strret = "convert(varchar(30)," + colname + ",120) as " + colname;
        }
        return strret;
    }

    public String FormatStrToDate(String strval) {
        String strret = null;
        int slen=strval.length();
        if (slen== 8)
        {
            String strvalt = MetaHelper.ShortDateToLong(strval);
            strret = "convert(datetime,'" + strvalt + "',120)";
        }
        else if (slen== 10) {
            strret = "convert(datetime,'" + strval + "',120)";
        } else if (slen== 16) {
            strret = "convert(datetime,'" + strval + "',120)";
        } else if (slen== 19) {
            strret = "convert(datetime,'" + strval + "',120)";
        } else if (slen>20) {
            strret = "convert(datetime,'" + strval + "',120)";
        }
        return strret;
    }

    public String GetColType(String schema,String strtab, String colname) {
        String strSel = "select a.name from systypes a,syscolumns b where b.id=object_id('" + strtab + "') and b.name='" + colname + "' and a.xtype=b.xtype";
        return strSel;
    }

    public String ToPageSql(String strSql,String orderStr, int intPageSize, int intCurrentPage) {
        String sqlstr = strSql.replace(orderStr,"");
        sqlstr = sqlstr.replaceFirst("(?i)select", "select row_number() over(" + orderStr + ") as row_num,");
        String strReturn = "select * from (" + sqlstr + ") a  where row_num>"
                + (intCurrentPage * intPageSize) + " and row_num<=" + ((intCurrentPage + 1) * intPageSize) + " ";
        return strReturn;
    }

    public String ToLimitSql(String strSql,String orderStr, int start, int end) {
        String sqlstr = strSql.replace(orderStr,"");
        sqlstr = sqlstr.replaceFirst("(?i)select", "select row_number() over(" + orderStr + ") as row_num,");
        String strReturn = "select * from (" + sqlstr + ") a  where row_num>"
                + start + " and row_num<=" + end + " ";
        return strReturn;
    }

    public String PackFunc(String funcname) {
        return "dbo." + funcname;
    }

    public String PackProc(String procname, String valstr) {
        return "exec " + procname + " " + valstr + ";";
    }

    public String PackMetaQry(String sqlstr) {
        return sqlstr;
    }

    public String PackTreeOrder(String orderstr) {
        return orderstr;
    }

    public String GetDefaultDT() {
        return "getdate()";
    }
}
