package com.soft.gcc.xtbg.cnsj.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjGhzjLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.vo.DfdwTCnsjghzjVo;

import java.util.List;

public interface DfdwTCnsjGhzjLoadService  extends IService<DfdwTCnsjGhzjLoad> {

    List<DfdwTCnsjGhzjLoad> getGhzjList(DfdwTCnsjGhzjLoad ghsj);
    List<DfdwTCnsjGhzjLoad> computeGhzjLoad(DfdwTCnsjGhzjLoad ghsj);
}
