package com.soft.gcc.xtbg.cnsj.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjProject;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjProjectMapper;
import com.soft.gcc.xtbg.cnsj.pojo.request.ProjectManageExcelReq;
import com.soft.gcc.xtbg.cnsj.pojo.resp.ProjectExcelResp;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjProjectService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class DfdwTCnsjProjectServiceImpl extends ServiceImpl<DfdwTCnsjProjectMapper, DfdwTCnsjProject> implements DfdwTCnsjProjectService {

    private static final Logger log = LoggerFactory.getLogger(DfdwTCnsjProjectServiceImpl.class);

    @Override
    public Result<Object> GetProjectList(DfdwTCnsjProject req) {
        try {
            IPage<DfdwTCnsjProject> list = new Page<>();
            list.setCurrent(req.getPageNum());
            list.setSize(req.getPageSize());
            if (req.getCreateTimeArray() != null && req.getCreateTimeArray().length == 2) {
                //查询分页
                list = this.page(list, new LambdaQueryWrapper<DfdwTCnsjProject>()
                        .eq(StringUtils.isNotEmpty(req.getUserNum()), DfdwTCnsjProject::getUserNum, req.getUserNum())
                        .like(StringUtils.isNotEmpty(req.getUserName()), DfdwTCnsjProject::getUserName, req.getUserName())
                        .like(StringUtils.isNotEmpty(req.getProjectName()), DfdwTCnsjProject::getProjectName, req.getProjectName())
                        .between(true, DfdwTCnsjProject::getCreateTime,
                                req.getCreateTimeArray()[0],req.getCreateTimeArray()[1])
                        .orderByAsc(DfdwTCnsjProject::getId));
            } else {
                //查询分页
                list = this.page(list, new LambdaQueryWrapper<DfdwTCnsjProject>()
                        .eq(StringUtils.isNotEmpty(req.getUserNum()), DfdwTCnsjProject::getUserNum, req.getUserNum())
                        .like(StringUtils.isNotEmpty(req.getUserName()), DfdwTCnsjProject::getUserName, req.getUserName())
                        .like(StringUtils.isNotEmpty(req.getProjectName()), DfdwTCnsjProject::getProjectName, req.getProjectName())
                        .orderByAsc(DfdwTCnsjProject::getId));
            }
            return Result.ok(list);
        }catch (Exception Ex){
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            log.error(s);
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> createProject(DfdwTCnsjProject req) {
        // 创建时间
        req.setCreateTime(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //用户id
        req.setCreator(user.getId()+"");
        req.setRealName(user.getRealName());
        this.saveOrUpdate(req);
        return Result.ok("项目新增成功");
    }

    @Override
    public Result<Object> updateProject(DfdwTCnsjProject req) {
        Integer id = req.getId();
        DfdwTCnsjProject dfdwTCnsjProject = baseMapper.selectById(id);
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        if(Objects.isNull(dfdwTCnsjProject)){
            log.error("在修改项目的时候,记录不存在！请求对象：{}", JSONObject.toJSONString(req));
            return Result.error("记录不存在，不能进行修改");
        }
        req.setUpdater(user.getId()+"");
        req.setUpdateTime(new Date());
        this.saveOrUpdate(req);
        return Result.ok("项目修改成功");
    }

    @Override
    public Result<Object> deleteProject(Integer id) {
        DfdwTCnsjProject dfdwTCnsjProject = baseMapper.selectById(id);
        if(Objects.isNull(dfdwTCnsjProject)){
            log.error("在删除项目的时候,记录不存在！记录ID：{}", id);
            return Result.error("记录不存在，不能进行删除");
        }
        baseMapper.updateToDeleteById(id);
        return Result.ok("项目删除成功");
    }

    @Override
    public void saveExcelRecord(List<ProjectManageExcelReq> list, PersonEntity user) {
        List<DfdwTCnsjProject> projects = new ArrayList<>();
        String userId = user.getId()+"";
        String realName = user.getRealName();
        Date date = new Date();
        list.forEach(i->{
            if(!Objects.isNull(i.getUserNum())){
                DfdwTCnsjProject dfdwTCnsjProject = new DfdwTCnsjProject();
                dfdwTCnsjProject.setProjectName(i.getProjectName());
                dfdwTCnsjProject.setUserName(i.getUserName());
                dfdwTCnsjProject.setUserNum(i.getUserNum().trim());
                dfdwTCnsjProject.setCreator(userId);
                dfdwTCnsjProject.setRealName(realName);
                dfdwTCnsjProject.setCreateTime(date);
                dfdwTCnsjProject.setDeleted("0");
                projects.add(dfdwTCnsjProject);
            }
        });
        batchInsertPowerDissipation(projects);
    }

    @Override
    public List<ProjectExcelResp> getExcelList(DfdwTCnsjProject req) {
        try {
            List<ProjectExcelResp> excelList = baseMapper.getExcelList(req);
            return excelList;
        }catch (Exception Ex){
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            log.error(s);
            return new ArrayList<>();
        }
    }

    public void batchInsertPowerDissipation(List<DfdwTCnsjProject> projects) {
        int batchSize = 100; // 每批次处理的数据量 SqlServer最大处理2100个参数
        for (int i = 0; i < projects.size(); i += batchSize) {
            List<DfdwTCnsjProject> batchList = projects.subList(i, Math.min(i + batchSize, projects.size()));
            baseMapper.batchInsertProject(batchList);
        }
    }
}
