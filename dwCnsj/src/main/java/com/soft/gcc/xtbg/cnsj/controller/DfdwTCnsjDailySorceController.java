package com.soft.gcc.xtbg.cnsj.controller;

import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjDailyScoreService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RequestMapping("/cnsj/dailySorce")
@RestController
public class DfdwTCnsjDailySorceController extends BaseController {
    @Resource
    private DfdwTCnsjDailyScoreService dfdwTCnsjDailySorceService;

    @RequestMapping("/CompleteDailySorce")
    public Result<Object> completeDailySorce(@RequestBody DfdwTCnsjDailyScore dailyScore) {
        PersonEntity person = user();
        try {
            return Result.ok(dfdwTCnsjDailySorceService.completeDailySorce(dailyScore, person));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/GetDailySorce")
    public Result<Object> getDailySorce(@RequestBody DfdwTCnsjDailyScore dailyScore) {
        PersonEntity person = user();
        try {
            return Result.ok(dfdwTCnsjDailySorceService.getDailySorce(dailyScore, person));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }
}
