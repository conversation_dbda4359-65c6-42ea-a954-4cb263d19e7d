package com.soft.gcc.xtbg.cnsj.mapper;

import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyLoad;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_CNSJ_DAILY_LOAD(储能设计-典型日负荷)】的数据库操作Mapper
* @createDate 2024-07-08 10:36:56
* @Entity com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyLoad
*/
public interface DfdwTCnsjDailyLoadMapper extends BaseMapper<DfdwTCnsjDailyLoad> {

    void insertOrUpdateBatch(@Param("list") List<DfdwTCnsjDailyLoad> subList, @Param("name") String name);
}




