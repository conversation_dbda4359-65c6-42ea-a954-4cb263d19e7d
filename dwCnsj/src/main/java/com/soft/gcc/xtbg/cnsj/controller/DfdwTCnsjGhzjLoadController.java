package com.soft.gcc.xtbg.cnsj.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjGhzjLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjGhzjLoadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 *  功耗总结controller
 */
@RequestMapping("/cnsj/ghzjLoad")
@RestController
public class DfdwTCnsjGhzjLoadController {
    private static final Logger log = LoggerFactory.getLogger(DfdwTCnsjGhzjLoadController.class);
    @Resource
    DfdwTCnsjGhzjLoadService dfdwTCnsjGhzjLoadService;


    /**
     * 功耗总结  查询
     * @return
     */
    @PostMapping("/list")
    public Result<Object> getGhzjList(@RequestBody DfdwTCnsjGhzjLoad ghsj) {
        if (StringUtils.checkValNull(ghsj.getUserNum())){
            return Result.error("用户编号不能为空");
        }
        if(ghsj.getYear() == null){
            return Result.error("年份不能为空");
        }
        return Result.ok(dfdwTCnsjGhzjLoadService.getGhzjList(ghsj));
    }


    /**
     * 功耗总结 重新生成
     */
    @PostMapping("/computeGhzj")
    public Result<Object> compute(@RequestBody DfdwTCnsjGhzjLoad ghsj) {
        return Result.ok(dfdwTCnsjGhzjLoadService.computeGhzjLoad(ghsj));
    }

}
