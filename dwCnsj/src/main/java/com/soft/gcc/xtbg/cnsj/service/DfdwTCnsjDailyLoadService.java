package com.soft.gcc.xtbg.cnsj.service;

import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyLoad;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.cnsj.pojo.response.DailyLoadDto;
import com.yyszc.wpbase.ventity.PersonEntity;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_CNSJ_DAILY_LOAD(储能设计-典型日负荷)】的数据库操作Service
* @createDate 2024-07-08 10:36:56
*/
public interface DfdwTCnsjDailyLoadService extends IService<DfdwTCnsjDailyLoad> {

    DailyLoadDto completeDailyLoad(DfdwTCnsjDailyLoad dailyLoad, PersonEntity person);

    DailyLoadDto getDailyLoad(DfdwTCnsjDailyLoad dailyLoad, PersonEntity person);
}
