package com.soft.gcc.xtbg.cnsj.mapper;

import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_CNSJ_DAILY_SCORE(储能设计-每日分数)】的数据库操作Mapper
* @createDate 2024-07-10 09:42:53
* @Entity com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore
*/
public interface DfdwTCnsjDailyScoreMapper extends BaseMapper<DfdwTCnsjDailyScore> {

    void insertOrUpdateBatch(@Param("list") List<DfdwTCnsjDailyScore> subList, @Param("name") String name);

    List<DfdwTCnsjDailyScore> selectScoreYear(@Param("userNum") String userNum,
                                              @Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("decimal") BigDecimal decimal
    );

    int countUseDate(@Param("userNum") String userNum,
                     @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime,
                     @Param("timeList") List<DfdwTDictData> timeList
    );
}




