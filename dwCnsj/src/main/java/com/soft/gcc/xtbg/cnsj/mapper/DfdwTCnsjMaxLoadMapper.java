package com.soft.gcc.xtbg.cnsj.mapper;

import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_CNSJ_MAX_LOAD(储能设计-月最大负荷)】的数据库操作Mapper
* @createDate 2024-07-08 09:28:23
* @Entity com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad
*/
public interface DfdwTCnsjMaxLoadMapper extends BaseMapper<DfdwTCnsjMaxLoad> {

    void saveOrUpdateBatchMaxLoad(@Param("list") List<DfdwTCnsjMaxLoad> list);
}




