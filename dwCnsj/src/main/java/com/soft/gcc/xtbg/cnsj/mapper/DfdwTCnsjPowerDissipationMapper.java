package com.soft.gcc.xtbg.cnsj.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.pojo.request.GhsjReq;
import com.soft.gcc.xtbg.cnsj.vo.DfdwTCnsjghzjVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public interface DfdwTCnsjPowerDissipationMapper extends BaseMapper<DfdwTCnsjPowerDissipation> {

    IPage<DfdwTCnsjPowerDissipation> GetGhsjList(IPage<DfdwTCnsjPowerDissipation> list, @Param("req") GhsjReq req);

    // 批量插入或更新功耗数据
    void batchInsertOrUpdatePowerDissipation(@Param("list") List<GhsjReq> distinctClass);

    BigDecimal selectMaxLoad(@Param("userNum") String userNum, @Param("useDate") LocalDateTime useDate, @Param("localDateTime") LocalDateTime localDateTime);

    BigDecimal selectMinLoad(@Param("userNum") String userNum, @Param("useDate") LocalDateTime useDate, @Param("localDateTime") LocalDateTime localDateTime);

    int countUseDate(@Param("userNum") String userNum, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("timeList") List<DfdwTDictData> timeList);
}
