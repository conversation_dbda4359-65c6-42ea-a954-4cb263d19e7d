package com.soft.gcc.xtbg.cnsj.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjGhzjLoad;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DfdwTCnsjGhzjLoadMapper extends BaseMapper<DfdwTCnsjGhzjLoad> {

    /**
     * 计算功耗总结
     * @param ghzjLoad
     * @return
     */
    List<DfdwTCnsjGhzjLoad> computeGhzjLoad(DfdwTCnsjGhzjLoad ghzjLoad);

    void insertOrUpdateBatch(@Param("list") List<DfdwTCnsjGhzjLoad> list);

}
