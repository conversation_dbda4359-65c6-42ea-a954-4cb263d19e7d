package com.soft.gcc.xtbg.cnsj.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjProject;
import com.soft.gcc.xtbg.cnsj.pojo.resp.ProjectExcelResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DfdwTCnsjProjectMapper extends BaseMapper<DfdwTCnsjProject> {

    void updateToDeleteById(@Param("id") Integer id);

    void batchInsertProject(@Param("list") List<DfdwTCnsjProject> batchList);

    List<ProjectExcelResp> getExcelList(@Param("req") DfdwTCnsjProject req);
}
