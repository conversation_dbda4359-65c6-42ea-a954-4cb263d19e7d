package com.soft.gcc.xtbg.cnsj.pojo.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DailyLoadDto {

    /**
     * 时间
     */
    private LocalDateTime useDate;

    /**
     * 功率
     */
    private BigDecimal power;

    /**
     * 实际负荷
     */
    private List<DailyLoadDto> actualLoadList;

    /**
     * 储能充放电
     */
    private List<DailyLoadDto> dischargeLoadList;

    /**
     * 加权后负荷
     */
    private List<DailyLoadDto> weightLoadList;

    /**
     * 预计需要值
     */
    private List<DailyLoadDto> estimateLoadList;

    public DailyLoadDto() {

    }

    public DailyLoadDto(LocalDateTime useDate, BigDecimal power) {
        this.useDate = useDate;
        this.power = power;
    }

    public DailyLoadDto(List<DailyLoadDto> actualLoadList, List<DailyLoadDto> dischargeLoadList, List<DailyLoadDto> weightLoadList, List<DailyLoadDto> estimateLoadList) {
        this.actualLoadList = actualLoadList;
        this.dischargeLoadList = dischargeLoadList;
        this.weightLoadList = weightLoadList;
        this.estimateLoadList = estimateLoadList;
    }
}
