package com.soft.gcc.xtbg.cnsj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjGhzjLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjGhzjLoadMapper;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjGhzjLoadService;
import com.soft.gcc.xtbg.cnsj.vo.DfdwTCnsjghzjVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class DfdwTCnsjGhzjLoadServiceImpl extends ServiceImpl<DfdwTCnsjGhzjLoadMapper, DfdwTCnsjGhzjLoad>
        implements DfdwTCnsjGhzjLoadService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<DfdwTCnsjGhzjLoad> getGhzjList(DfdwTCnsjGhzjLoad ghsj) {
        //优先查询数据库表里面的数据，如果没有就直接计算
        List<DfdwTCnsjGhzjLoad> resList = this.list(new LambdaQueryWrapper<DfdwTCnsjGhzjLoad>()
                .eq(DfdwTCnsjGhzjLoad::getUserNum, ghsj.getUserNum())
                .eq(DfdwTCnsjGhzjLoad::getYear, ghsj.getYear())
                .orderByAsc(DfdwTCnsjGhzjLoad::getMonth)
        );
        return resList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<DfdwTCnsjGhzjLoad> computeGhzjLoad(DfdwTCnsjGhzjLoad ghsj) {

        //计算
        List<DfdwTCnsjGhzjLoad> list = this.baseMapper.computeGhzjLoad(ghsj);

        if (!list.isEmpty()){
            String currentLoginUserName = SessionHelper.getSessionPerson().getRealName();
            list.forEach(item->{
                item.setCreator(currentLoginUserName);
                item.setUpdater(currentLoginUserName);
            });
            this.baseMapper.insertOrUpdateBatch(list);
        }
        return this.list(new LambdaQueryWrapper<DfdwTCnsjGhzjLoad>()
                .eq(DfdwTCnsjGhzjLoad::getUserNum, ghsj.getUserNum())
                .eq(DfdwTCnsjGhzjLoad::getYear, ghsj.getYear())
                .orderByAsc(DfdwTCnsjGhzjLoad::getMonth)
        );
    }
}
