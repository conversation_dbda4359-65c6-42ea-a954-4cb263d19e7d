package com.soft.gcc.xtbg.cnsj.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

/**
 * 储能设计-每日分数
 *
 * <AUTHOR>
 * @TableName DFDW_T_CNSJ_DAILY_SCORE
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "DFDW_T_CNSJ_DAILY_SCORE")
@Data
public class DfdwTCnsjDailyScore extends DfdwBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编号
     */
    @TableField(value = "userNum")
    private String userNum;

    /**
     * 分数
     */
    @TableField(value = "score")
    private BigDecimal score;

    /**
     * 利用天数
     */
    @TableField(value = "utilizeDays")
    private Integer utilizeDays;

    /**
     * 功率
     */
    @TableField(value = "power")
    private BigDecimal power;

    /**
     * 时间
     */
    @TableField(value = "useDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}