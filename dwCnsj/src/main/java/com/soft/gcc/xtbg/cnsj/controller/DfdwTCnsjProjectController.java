package com.soft.gcc.xtbg.cnsj.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjProject;
import com.soft.gcc.xtbg.cnsj.pojo.request.ProjectManageExcelReq;
import com.soft.gcc.xtbg.cnsj.pojo.resp.ProjectExcelResp;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjProjectService;
import com.soft.gcc.xtbg.cnsj.util.ExcelUtil;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

@RequestMapping("/cnsj/project")
@RestController
public class DfdwTCnsjProjectController extends BaseController {

    @Resource
    private DfdwTCnsjProjectService cnsjProjectService;

    @PostMapping("/GetProjectList")
    @PreAuthorize("@ss.hasPermi('JDWCN01XM01QX01')")
    public Result<Object> GetProjectList(@RequestBody DfdwTCnsjProject req) {
        return cnsjProjectService.GetProjectList(req);
    }

    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermi('JDWCN01XM01QX02')")
    public Result<Object> createProject( @RequestBody DfdwTCnsjProject req) {
        return cnsjProjectService.createProject(req);
    }

    /**
     * 修改字典类型
     * @return
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermi('JDWCN01XM01QX03')")
    public Result<Object> updateProject(@RequestBody DfdwTCnsjProject req) {
        return cnsjProjectService.updateProject(req);
    }

    /**
     * 删除项目
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermi('JDWCN01XM01QX04')")
    public Result<Object> deleteProject(@RequestParam("id") Integer id) {
        return cnsjProjectService.deleteProject(id);
    }

    /**
     * 导出导入的模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        //resources路径下的文件地址
        String filePath = "importTemplate/projectManage.xlsx";
        //用来读取resources下的文件
        ClassPathResource classPathResource = new ClassPathResource(filePath);
        try {

            //获取文件
            File file = classPathResource.getFile();
            // 获取文件名
            String filename = file.getName();
            // 获取文件后缀名
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();

            // 将文件写入输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            InputStream fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();

            // 清空response
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            //Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
            //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            // 告知浏览器文件的大小
            response.addHeader("Content-Length", "" + file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }


    /**
     * 导入
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importProjectExcel")
    @PreAuthorize("@ss.hasPermi('JDWCN01XM01QX05')")
    public Result<Object> importSheetsExcel(MultipartFile file) throws Exception {
        ImportParams importParams = new ImportParams();
        //表格标题行数,默认0
        importParams.setTitleRows(0);
        //是否需要校验上传的Excel
        importParams.setNeedVerify(false);
        ExcelImportResult<ProjectManageExcelReq> result = ExcelImportUtil.importExcelMore(file.getInputStream(), ProjectManageExcelReq.class, importParams);
        List<ProjectManageExcelReq> list = result.getList();
        if(list.size() < 1){
            return Result.error("暂无可导入的数据，请重新上传");
        }
        cnsjProjectService.saveExcelRecord(list, SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser());
        return Result.ok("导入成功");
    }

    /**
     * 导出
     * @param req
     * @param response
     * @throws IOException
     */
    @RequestMapping("/DownLoadProject")
    @PreAuthorize("@ss.hasPermi('JDWCN01XM01QX06')")
    public void DownLoadJsyxx(@RequestBody DfdwTCnsjProject req, HttpServletResponse response) throws IOException {
        List<ProjectExcelResp> excelList = cnsjProjectService.getExcelList(req);
        String fileName = "项目数据" + System.currentTimeMillis() + ".xls";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

        ExcelUtil.exportExcelX(excelList, "项目数据", "项目数据", ProjectExcelResp.class, fileName, response);
    }
}
