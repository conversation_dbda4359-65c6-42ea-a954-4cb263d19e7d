package com.soft.gcc.xtbg.cnsj.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjMaxLoadService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("/cnsj/maxLoad")
@RestController
public class DfdwTCnsjMaxLoadController extends BaseController {

    @Resource
    DfdwTCnsjMaxLoadService dfdwTCnsjMaxLoadService;

    @PostMapping("getMaxLoadData")
    public Result<Object> getMaxLoadData(@RequestBody DfdwTCnsjMaxLoad maxLoad){
        if (StringUtils.checkValNull(maxLoad.getUserNum())){
            return Result.error("用户编号不能为空");
        }


        return Result.ok(dfdwTCnsjMaxLoadService.getMaxLoadData(maxLoad));
    }

    @PostMapping("completeMaxLoad")
    public Result<Object> completeMaxLoad(@RequestBody DfdwTCnsjMaxLoad maxLoad){
        if (StringUtils.checkValNull(maxLoad.getUserNum())){
            return Result.error("用户编号不能为空");
        }

        return Result.ok(dfdwTCnsjMaxLoadService.completeMaxLoad(maxLoad));
    }

}
