package com.soft.gcc.xtbg.cnsj.pojo.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class ProjectManageExcelReq implements Serializable {

    @Excel(name = "用户编号")
    private String userNum;

    @Excel(name = "用户名称")
    private String userName;

    @Excel(name = "项目名称")
    private String projectName;
}
