package com.soft.gcc.xtbg.cnsj.service;

import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yyszc.wpbase.ventity.PersonEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_CNSJ_DAILY_SCORE(储能设计-每日分数)】的数据库操作Service
 * @createDate 2024-07-10 09:42:53
 */
public interface DfdwTCnsjDailyScoreService extends IService<DfdwTCnsjDailyScore> {

    List<DfdwTCnsjDailyScore> completeDailySorce(DfdwTCnsjDailyScore dailyScore, PersonEntity person);

    List<DfdwTCnsjDailyScore> getDailySorce(DfdwTCnsjDailyScore dailyScore, PersonEntity person);
}
