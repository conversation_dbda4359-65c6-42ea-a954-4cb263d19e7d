package com.soft.gcc.xtbg.cnsj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjPowerDissipationMapper;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjMaxLoadService;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjMaxLoadMapper;
import com.soft.gcc.xtbg.cnsj.service.GhsjService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_CNSJ_MAX_LOAD(储能设计-月最大负荷)】的数据库操作Service实现
* @createDate 2024-07-08 09:28:23
*/
@Service
public class DfdwTCnsjMaxLoadServiceImpl extends ServiceImpl<DfdwTCnsjMaxLoadMapper, DfdwTCnsjMaxLoad>
    implements DfdwTCnsjMaxLoadService{

    @Resource
    GhsjService ghsjService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<DfdwTCnsjMaxLoad> getMaxLoadData(DfdwTCnsjMaxLoad maxLoad) {
        List<DfdwTCnsjMaxLoad> resList = this.list(new LambdaQueryWrapper<DfdwTCnsjMaxLoad>()
                .eq(DfdwTCnsjMaxLoad::getUserNum,maxLoad.getUserNum())
                .between(DfdwTCnsjMaxLoad::getYear,maxLoad.getStartYear(),maxLoad.getEndYear())
                .between(DfdwTCnsjMaxLoad::getMonth,maxLoad.getStartMonth(),maxLoad.getEndMonth())
                .orderByAsc(DfdwTCnsjMaxLoad::getYear).orderByAsc(DfdwTCnsjMaxLoad::getMonth)
        );
        //计算两个月份相差多少
//        LocalDate localDate1 = LocalDate.of(maxLoad.getStartYear(),maxLoad.getStartMonth(),1);
//        LocalDate localDate2 = LocalDate.of(maxLoad.getEndYear(),maxLoad.getEndMonth(),1);
//        Period period = Period.between(localDate1,localDate2);
//        int months = period.getYears()*12+period.getMonths();

        return resList;
    }



    /**
     * 重新计算
     * @param maxLoad
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<DfdwTCnsjMaxLoad> completeMaxLoad(DfdwTCnsjMaxLoad maxLoad) {
        //重新计算
        QueryWrapper<DfdwTCnsjPowerDissipation> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("userNum", "MAX(workingPower) as workingPower", "MONTH(useDate) as month", "YEAR(useDate) as year")
                .eq("userNum",maxLoad.getUserNum())
                .between("YEAR(useDate)",maxLoad.getStartYear(),maxLoad.getEndYear())
                .between("MONTH(useDate)",maxLoad.getStartMonth(),maxLoad.getEndMonth())
                .groupBy("userNum", "MONTH(useDate)", "YEAR(useDate)")
                .orderByAsc("YEAR(useDate)").orderByAsc("MONTH(useDate)");

        List<DfdwTCnsjPowerDissipation> resList = ghsjService.list(queryWrapper);

        if(!resList.isEmpty()){
            List<DfdwTCnsjMaxLoad> loads = new ArrayList<>();

            String loginUserName = SessionHelper.getSessionPerson().getRealName();

            resList.forEach(item->{
                loads.add(new DfdwTCnsjMaxLoad(){{
                    setMaxLoad(item.getWorkingPower());
                    setMonth(item.getMonth());
                    setYear(item.getYear());
                    setUserNum(item.getUserNum());
                    setCreator(loginUserName);
                    setUpdater(loginUserName);
                }});
            });

            this.baseMapper.saveOrUpdateBatchMaxLoad(loads);
        }


        return this.list(new LambdaQueryWrapper<DfdwTCnsjMaxLoad>()
                .eq(DfdwTCnsjMaxLoad::getUserNum,maxLoad.getUserNum())
                .between(DfdwTCnsjMaxLoad::getYear,maxLoad.getStartYear(),maxLoad.getEndYear())
                .between(DfdwTCnsjMaxLoad::getMonth,maxLoad.getStartMonth(),maxLoad.getEndMonth())
                .orderByAsc(DfdwTCnsjMaxLoad::getYear).orderByAsc(DfdwTCnsjMaxLoad::getMonth)
        );
    }

}




