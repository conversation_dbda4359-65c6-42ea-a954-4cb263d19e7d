package com.soft.gcc.xtbg.cnsj.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjPowerDissipationMapper;
import com.soft.gcc.xtbg.cnsj.pojo.request.GhsjReq;
import com.soft.gcc.xtbg.cnsj.service.GhsjService;
import com.yyszc.wpbase.ventity.PersonEntity;
import cn.hutool.core.collection.ListUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service
public class GhsjServiceImpl extends ServiceImpl<DfdwTCnsjPowerDissipationMapper, DfdwTCnsjPowerDissipation> implements GhsjService {

    private static final Logger log = LoggerFactory.getLogger(GhsjServiceImpl.class);
    @Override
    public Result<Object> GetGhsjList(DfdwTCnsjPowerDissipation ghsj) {
        try {
            IPage<DfdwTCnsjPowerDissipation> list = new Page<>();
            list.setCurrent(ghsj.getPageNum());
            list.setSize(ghsj.getPageSize());
            if (ghsj.getUseDateArray() != null && ghsj.getUseDateArray().length == 2) {
                //查询分页
                list = this.page(list, new LambdaQueryWrapper<DfdwTCnsjPowerDissipation>()
                        .eq(StringUtils.isNotEmpty(ghsj.getUserNum()), DfdwTCnsjPowerDissipation::getUserNum, ghsj.getUserNum())
                        .like(StringUtils.isNotEmpty(ghsj.getUserName()), DfdwTCnsjPowerDissipation::getUserName, ghsj.getUserName())
                        .between(true, DfdwTCnsjPowerDissipation::getUseDate,
                                ghsj.getUseDateArray()[0],ghsj.getUseDateArray()[1])
                        .orderByAsc(DfdwTCnsjPowerDissipation::getId));
            } else {
                //查询分页
                list = this.page(list, new LambdaQueryWrapper<DfdwTCnsjPowerDissipation>()
                        .eq(StringUtils.isNotEmpty(ghsj.getUserNum()), DfdwTCnsjPowerDissipation::getUserNum, ghsj.getUserNum())
                        .like(StringUtils.isNotEmpty(ghsj.getUserName()), DfdwTCnsjPowerDissipation::getUserName, ghsj.getUserName())
                        .orderByAsc(DfdwTCnsjPowerDissipation::getId));

            }
            return Result.ok(list);
        }catch (Exception Ex){
            Ex.printStackTrace();
            String s = "程序产生异常:" + Ex.getMessage() + "!";
            log.error(s);
            return Result.error(s);
        }
    }

    @Override
    public Result<Object> deleteGhsj(Integer id) {
        DfdwTCnsjPowerDissipation dfdwTCnsjPowerDissipation = baseMapper.selectById(id);
        if(Objects.isNull(dfdwTCnsjPowerDissipation)){
            return Result.error("功耗数据不存在");
        }
        //删除标识
        dfdwTCnsjPowerDissipation.setDeleted("1");
        baseMapper.updateById(dfdwTCnsjPowerDissipation);
        return Result.ok("功耗数据删除成功");
    }

    @Override
    public Result<Object> updateGhsj(DfdwTCnsjPowerDissipation ghsj) {
        DfdwTCnsjPowerDissipation dfdwTCnsjPowerDissipation = baseMapper.selectById(ghsj.getId());
        if(Objects.isNull(dfdwTCnsjPowerDissipation)){
            return Result.error("功耗数据不存在,不能进行修改");
        }
        //修改时间
        ghsj.setUpdateTime(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        ghsj.setUpdater(user.getId()+"");
        baseMapper.updateById(ghsj);
        return Result.ok("功耗数据修改成功");
    }

    @Override
    @Async
    public Result<Object> saveExcelRecord(List<GhsjReq> list, PersonEntity user) {
        // 1、反转lists 保证留下最新的那一条
        Collections.reverse(list);
        //根据相同用户编号和使用时间的记录去重
        List<GhsjReq> distinctClass = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o ->
                o.getUserNum().trim() + ";" + o.getUseDate()))), ArrayList::new));
        List<GhsjReq> ghsjReqList = new ArrayList<>();
        distinctClass.forEach(i->{
            if(!Objects.isNull(i.getUserNum()) && !Objects.isNull(i.getWorkingPower())){
                i.setUserNum(i.getUserNum().trim());
                i.setCreator(user.getId()+"");
                ghsjReqList.add(i);
            }
        });
        long start = System.currentTimeMillis();
        batchInsertPowerDissipation(ghsjReqList);
        // 每批次处理的数据量 SqlServer最大处理2100个参数
//        List<List<GhsjReq>> lists = ListUtil.partition(ghsjReqList, 400);
//        CountDownLatch countDownLatch = new CountDownLatch(lists.size());

//        for (List<GhsjReq> ghsjReqs : lists) {
//            batchInsertPowerDissipation(ghsjReqs, countDownLatch);
//        }
//        try {
//            countDownLatch.await(); //保证之前的所有的线程都执行完成，才会走下面的；
//            // 这样就可以在下面拿到所有线程执行完的集合结果
//        } catch (Exception e) {
//            log.error("阻塞异常："+e.getMessage());
//            e.printStackTrace();
//        }
        long end = System.currentTimeMillis();
        log.info("本次插入数据耗费时间："+(end-start));

        return Result.ok("导入Excel成功");
    }

    //@Async("asyncGhsjServiceExecutor")
//    public void batchInsertPowerDissipation(List<GhsjReq> powerDissipations, CountDownLatch countDownLatch) {
//        try {
//             baseMapper.batchInsertOrUpdatePowerDissipation(powerDissipations);
//        } finally {
//            countDownLatch.countDown();
//        }
//    }


    public void batchInsertPowerDissipation(List<GhsjReq> powerDissipations) {
        int batchSize = 400; // 每批次处理的数据量 SqlServer最大处理2100条
        for (int i = 0; i < powerDissipations.size(); i += batchSize) {
            List<GhsjReq> batchList = powerDissipations.subList(i, Math.min(i + batchSize, powerDissipations.size()));
            baseMapper.batchInsertOrUpdatePowerDissipation(batchList);
        }
    }

}
