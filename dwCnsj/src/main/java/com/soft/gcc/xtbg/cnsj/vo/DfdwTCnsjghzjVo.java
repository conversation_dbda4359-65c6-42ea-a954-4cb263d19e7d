package com.soft.gcc.xtbg.cnsj.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 功耗总结vo
 */
@Data
public class DfdwTCnsjghzjVo {
    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 最高功率
     */
    private BigDecimal maxWorkingPower;
    /**
     * 最低功率
     */
    private BigDecimal minWorkingPower;
    /**
     * 最高功率和最低功率差
     */
    private BigDecimal powerDifference;
    /**
     * 最高功率时间
     */
    private Date maxWorkingPowerTime;
    /**
     * 最低功率时间
     */
    private Date minWorkingPowerTime;
    /**
     * 8-10点最低功率
     */
    private BigDecimal minWorkingPower_8_10;
    /**
     * 10-11点最低功率
     */
    private BigDecimal minWorkingPower_10_11;
    /**
     * 15-17点最低功率
     */
    private BigDecimal minWorkingPower_15_17;



}
