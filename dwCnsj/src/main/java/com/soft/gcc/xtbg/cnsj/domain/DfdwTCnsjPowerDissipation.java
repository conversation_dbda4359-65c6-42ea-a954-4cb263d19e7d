package com.soft.gcc.xtbg.cnsj.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("DFDW_T_CNSJ_POWER_DISSIPATION")
public class DfdwTCnsjPowerDissipation extends DfdwBaseEntity implements Serializable {

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Integer Id;

    @ApiModelProperty(value = "用户编号")
    @JSONField(name="userNum")
    private String userNum;

    @ApiModelProperty(value = "用户名称")
    @JSONField(name="userName")
    private String userName;

    @ApiModelProperty(value = "使用日期")
    @JSONField(name="useDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useDate;

    @ApiModelProperty(value = "使用功率（瞬时有功）")
    @JSONField(name="workingPower")
    private BigDecimal workingPower;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] useDateArray;

    @TableField(exist = false)
    private Integer month;
    @TableField(exist = false)
    private Integer year;

    /**
     * 时间类型(充电时间 charge 放电时间discharge 最大功率充电时间 chargeMax)
     */
    @TableField(exist = false)
    private String timeType;
}
