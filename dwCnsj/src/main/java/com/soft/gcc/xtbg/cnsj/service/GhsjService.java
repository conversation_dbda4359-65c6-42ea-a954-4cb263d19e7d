package com.soft.gcc.xtbg.cnsj.service;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.pojo.request.GhsjReq;
import com.soft.gcc.xtbg.cnsj.vo.DfdwTCnsjghzjVo;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface GhsjService extends IService<DfdwTCnsjPowerDissipation> {

    Result<Object> GetGhsjList(DfdwTCnsjPowerDissipation ghsj);

    Result<Object> deleteGhsj(Integer id);

    Result<Object> updateGhsj(DfdwTCnsjPowerDissipation ghsj);

    Result<Object> saveExcelRecord(List<GhsjReq> ghsjReqList, PersonEntity user);

}
