package com.soft.gcc.xtbg.cnsj.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

/**
 * 储能设计-典型日负荷
 * <AUTHOR>
 * @TableName DFDW_T_CNSJ_DAILY_LOAD
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="DFDW_T_CNSJ_DAILY_LOAD")
@Data
public class DfdwTCnsjDailyLoad extends DfdwBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编号
     */
    @TableField(value = "userNum")
    @NotNull(message = "用户编号不能为空")
    private String userNum;

    /**
     * 类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 时间
     */
    @TableField(value = "useDate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "日期不能为空")
    private LocalDateTime useDate;

    @TableField(exist = false)
    private String useDateString;

    /**
     * 功率
     */
    @TableField(value = "power")
    private BigDecimal power;

    /**
     * 输入功率
     */
    @TableField(value = "inputPower")
    @NotNull(message = "输入功率不能为空")
    private Integer inputPower;

    public DfdwTCnsjDailyLoad() {}

    public DfdwTCnsjDailyLoad(String userNum, String type, LocalDateTime useDate, BigDecimal power, Integer inputPower) {
        this.userNum = userNum;
        this.type = type;
        this.useDate = useDate;
        this.useDateString = useDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        this.power = power;
        this.inputPower = inputPower;
    }
}