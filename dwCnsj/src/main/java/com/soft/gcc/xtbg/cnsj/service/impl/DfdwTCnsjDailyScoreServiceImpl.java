package com.soft.gcc.xtbg.cnsj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictDataMapper;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjPowerDissipationMapper;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjDailyScoreService;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjDailyScoreMapper;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_CNSJ_DAILY_SCORE(储能设计-每日分数)】的数据库操作Service实现
 * @createDate 2024-07-10 09:42:53
 */
@Service
public class DfdwTCnsjDailyScoreServiceImpl extends ServiceImpl<DfdwTCnsjDailyScoreMapper, DfdwTCnsjDailyScore>
        implements DfdwTCnsjDailyScoreService {
    @Resource
    private DfdwTCnsjPowerDissipationMapper dfdwTCnsjPowerDissipationMapper;
    @Resource
    private DfdwTDictDataMapper dfdwTDictDataMapper;

    DateTimeFormatter HHmm = DateTimeFormatter.ofPattern("HH:mm");

    @Override
    public List<DfdwTCnsjDailyScore> completeDailySorce(DfdwTCnsjDailyScore dailyScore, PersonEntity person) {
        if (StringUtils.isNotBlank(vaildDailyScore(dailyScore))) {
            throw new RuntimeException(vaildDailyScore(dailyScore));
        }
        // 获取实际负荷
        List<DfdwTCnsjPowerDissipation> powerDissipationList = dfdwTCnsjPowerDissipationMapper.selectList(new LambdaUpdateWrapper<DfdwTCnsjPowerDissipation>()
                .eq(DfdwTCnsjPowerDissipation::getUserNum, dailyScore.getUserNum())
                .between(DfdwTCnsjPowerDissipation::getUseDate,
                        dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                        dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1)
                )
                .orderByAsc(DfdwTCnsjPowerDissipation::getUseDate)
        );
        if (powerDissipationList.isEmpty()) {
            throw new RuntimeException("未找到用户的功耗数据信息，请在功耗数据中添加！");
        }
        // 系数
        List<DfdwTDictData> coefficientList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-coefficient")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 最大功率充电时间
        List<DfdwTDictData> chargeTimeMaxList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-chargeTimeMax")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 放电时间
        List<DfdwTDictData> dischargeTimeList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-dischargeTime")
                .eq(DfdwTDictData::getStatus, 0)
        );
        List<DfdwTCnsjPowerDissipation> filterList = new ArrayList<>();
        for (DfdwTCnsjPowerDissipation powerDissipation : powerDissipationList) {
            // 最大功率充电时间
            for (DfdwTDictData data : chargeTimeMaxList) {
                String now = powerDissipation.getUseDate().format(HHmm);
                String start = data.getLabel().split("-")[0];
                String end = data.getLabel().split("-")[1];
                if (now.compareTo(start) >= 0 && now.compareTo(end) <= 0) {
                    powerDissipation.setTimeType("chargeMax");
                    filterList.add(powerDissipation);
                }
            }
            // 放电时间
            for (DfdwTDictData data : dischargeTimeList) {
                String now = powerDissipation.getUseDate().format(HHmm);
                String start = data.getLabel().split("-")[0];
                String end = data.getLabel().split("-")[1];
                if (now.compareTo(start) >= 0 && now.compareTo(end) <= 0) {
                    powerDissipation.setTimeType("discharge");
                    filterList.add(powerDissipation);
                }
            }
        }
        Set<DfdwTCnsjPowerDissipation> set = new HashSet<>(filterList);
        filterList = new ArrayList<>(set);

        BigDecimal maxLoad = dfdwTCnsjPowerDissipationMapper.selectMaxLoad(
                dailyScore.getUserNum(),
                dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1)
        );
        BigDecimal minLoad = dfdwTCnsjPowerDissipationMapper.selectMinLoad(
                dailyScore.getUserNum(),
                dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1)
        );

        // 储能测算-结论天数
        int target = 300;
        // 有效点位（%）
        BigDecimal decimal = BigDecimal.valueOf(Integer.parseInt("80"));
        // 储能测算-天数间隔
        int daysInterval = 50;
        // 储能测算-计算误差值
        int errorValue = 0;
        for (DfdwTDictData data : coefficientList) {
            if ("cnsj-coefficient-conclusionDays".equals(data.getValue())) {
                target = Integer.parseInt(data.getLabel());
            } else if ("cnsj-coefficient-effectivePoint".equals(data.getValue())) {
                decimal = BigDecimal.valueOf(Double.parseDouble(data.getLabel()));
            } else if ("cnsj-coefficient-daysInterval".equals(data.getValue())) {
                daysInterval = Integer.parseInt(data.getLabel());
            } else if ("cnsj-coefficient-errorValue".equals(data.getValue())) {
                errorValue = Integer.parseInt(data.getLabel());
            }
        }
        int left = minLoad.intValue();
        int right = maxLoad.intValue();
        List<DfdwTCnsjDailyScore> list = new ArrayList<>();
        // 找出最接近点
        DfdwTCnsjDailyScore closestIndex = findClosest(filterList, target, left, right, decimal, errorValue, person);
        // 根据间隔取整
        int power1 = closestIndex.getPower().intValue() / daysInterval * daysInterval;
        int power2 = closestIndex.getPower().intValue() / daysInterval * daysInterval;
        if (closestIndex.getPower().intValue() % daysInterval != 0) {
            power2 = (closestIndex.getPower().intValue() / daysInterval + 1) * daysInterval;
        }
        if (power1 == power2) {
            DfdwTCnsjDailyScore score1 = completeDailyScoreYear(filterList, power1, decimal, true, person);
            list.add(score1);
        } else {
            DfdwTCnsjDailyScore score1 = completeDailyScoreYear(filterList, power1, decimal, true, person);
            DfdwTCnsjDailyScore score2 = completeDailyScoreYear(filterList, power2, decimal, true, person);
            if (Math.abs(score1.getUtilizeDays() - target) <= Math.abs(score2.getUtilizeDays() - target)) {
                list.add(score1);
            } else {
                list.add(score2);
            }
        }

        // 根据间隔做出数据
        int days = 0;
        while (days < 10) {
            if (list.get(0).getPower().intValue() - daysInterval < left && list.get(list.size() - 1).getPower().intValue() + daysInterval > right) {
                break;
            }
            if (list.get(0).getPower().intValue() - daysInterval >= left) {
                int inputPower = Math.max(list.get(0).getPower().intValue() - daysInterval - errorValue, 0);
                DfdwTCnsjDailyScore score = completeDailyScoreYear(filterList, inputPower, decimal, true, person);
                list.add(0, score);
                days++;
            }
            if (list.get(list.size() - 1).getPower().intValue() + daysInterval <= right) {
                int inputPower = Math.max(list.get(list.size() - 1).getPower().intValue() + daysInterval - errorValue, 0);
                DfdwTCnsjDailyScore score = completeDailyScoreYear(filterList, inputPower, decimal, true, person);
                list.add(score);
                days++;
            }
        }

        // 删除多余数据
        List<BigDecimal> powerList = list.stream().map(DfdwTCnsjDailyScore::getPower).collect(Collectors.toList());
        this.remove(new LambdaQueryWrapper<DfdwTCnsjDailyScore>()
                .eq(DfdwTCnsjDailyScore::getUserNum, dailyScore.getUserNum())
                .between(DfdwTCnsjDailyScore::getUseDate,
                        dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                        dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1)
                )
                .notIn(DfdwTCnsjDailyScore::getPower, powerList)
        );
        return list;
    }

    @Override
    public List<DfdwTCnsjDailyScore> getDailySorce(DfdwTCnsjDailyScore dailyScore, PersonEntity person) {
        if (StringUtils.isNotBlank(vaildDailyScore(dailyScore))) {
            throw new RuntimeException(vaildDailyScore(dailyScore));
        }
        // 最大功率充电时间
        List<DfdwTDictData> chargeTimeMaxList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-chargeTimeMax")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 放电时间
        List<DfdwTDictData> dischargeTimeList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-dischargeTime")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 系数
        List<DfdwTDictData> coefficientList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-coefficient")
                .eq(DfdwTDictData::getStatus, 0)
        );
        List<DfdwTDictData> timeList = new ArrayList<>();
        for (DfdwTDictData data : chargeTimeMaxList) {
            DfdwTDictData time = new DfdwTDictData();
            time.setStartTime(data.getLabel().split("-")[0]);
            time.setEndTime(data.getLabel().split("-")[1]);
            timeList.add(time);
        }
        for (DfdwTDictData data : dischargeTimeList) {
            DfdwTDictData time = new DfdwTDictData();
            time.setStartTime(data.getLabel().split("-")[0]);
            time.setEndTime(data.getLabel().split("-")[1]);
            timeList.add(time);
        }
        BigDecimal decimal = BigDecimal.valueOf(Integer.parseInt("80"));
        for (DfdwTDictData data : coefficientList) {
            if ("cnsj-coefficient-effectivePoint".equals(data.getValue())) {
                decimal = BigDecimal.valueOf(Double.parseDouble(data.getLabel()));
            }
        }
        int countAll = dfdwTCnsjPowerDissipationMapper.countUseDate(
                dailyScore.getUserNum(),
                dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1),
                timeList
        );
        int countScore = baseMapper.countUseDate(
                dailyScore.getUserNum(),
                dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1),
                timeList
        );
        if (countAll == countScore) {
            return baseMapper.selectScoreYear(
                    dailyScore.getUserNum(),
                    dailyScore.getUseDate().with(TemporalAdjusters.firstDayOfMonth()),
                    dailyScore.getUseDate().plusMonths(11).with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1),
                    decimal
            );
        } else {
            return completeDailySorce(dailyScore, person);
        }
    }

    private String vaildDailyScore(DfdwTCnsjDailyScore dailyScore) {
        if (StringUtils.isBlank(dailyScore.getUserNum())) {
            return "用户名称不能为空";
        } else if (dailyScore.getUseDate() == null) {
            return "月份不能为空";
        }
        return "";
    }

    // 查找最接近的值
    public DfdwTCnsjDailyScore findClosest(List<DfdwTCnsjPowerDissipation> arr, Integer target, Integer left, Integer right, BigDecimal decimal, Integer errorValue, PersonEntity person) {
        DfdwTCnsjDailyScore closestIndex = new DfdwTCnsjDailyScore();
        int closestDiff = Integer.MAX_VALUE;

        // 二分法找出最接近点
        while (left <= right) {
            int mid = left + ((right - left) >> 1);
            int inputPower = Math.max(mid - errorValue, 0);
            DfdwTCnsjDailyScore dailyScoreYear = completeDailyScoreYear(arr, inputPower, decimal, false, person);
            if (Objects.equals(dailyScoreYear.getUtilizeDays(), target)) {
                return dailyScoreYear;
            }
            // 更新最接近值和索引
            int diff = Math.abs(dailyScoreYear.getUtilizeDays() - target);
            if (diff < closestDiff) {
                closestDiff = diff;
                closestIndex = dailyScoreYear;
            }

            if (dailyScoreYear.getUtilizeDays() < target) {
                right = mid - 1;
            } else {
                left = mid + 1;
            }
        }
        // 返回最接近值的索引
        return closestIndex;
    }

    public DfdwTCnsjDailyScore completeDailyScoreYear(List<DfdwTCnsjPowerDissipation> list, Integer inputPower, BigDecimal decimal, Boolean isSave, PersonEntity person) {
        DfdwTCnsjDailyScore score = new DfdwTCnsjDailyScore();
        score.setUserNum(list.get(0).getUserNum());
        score.setPower(new BigDecimal(inputPower));
        score.setScore(BigDecimal.ZERO);
        score.setUtilizeDays(0);
        Map<LocalDateTime, List<DfdwTCnsjPowerDissipation>> dayMap = list.stream().collect(Collectors.groupingBy(item -> item.getUseDate().toLocalDate().atStartOfDay()));
        List<DfdwTCnsjDailyScore> saveList = new ArrayList<>();
        for (LocalDateTime date : dayMap.keySet()) {
            List<DfdwTCnsjPowerDissipation> dayList = dayMap.get(date);
            dayList = dayList.stream().sorted(Comparator.comparing(DfdwTCnsjPowerDissipation::getUseDate)).collect(Collectors.toList());
            DfdwTCnsjDailyScore dailyScore = completeDailyScore(dayList, inputPower, decimal);
            score.setScore(score.getScore().add(dailyScore.getScore()));
            score.setUtilizeDays(score.getUtilizeDays() + dailyScore.getUtilizeDays());
            saveList.add(dailyScore);
        }
        if (isSave) {
            batchInsertDailyScore(saveList, person);
        }
        return score;
    }

    private void batchInsertDailyScore(List<DfdwTCnsjDailyScore> saveList, PersonEntity person) {
        int batchSize = 100;
        for (int i = 0; i < saveList.size(); i += batchSize) {
            List<DfdwTCnsjDailyScore> subList = saveList.subList(i, Math.min(i + batchSize, saveList.size()));
            baseMapper.insertOrUpdateBatch(subList, person.getLoginName());
        }
    }

    public DfdwTCnsjDailyScore completeDailyScore(List<DfdwTCnsjPowerDissipation> list, Integer inputPower, BigDecimal decimal) {
        DfdwTCnsjDailyScore score = new DfdwTCnsjDailyScore();
        score.setUserNum(list.get(0).getUserNum());
        score.setUseDate(list.get(0).getUseDate().toLocalDate().atStartOfDay());
        score.setPower(new BigDecimal(inputPower));
        int num = 0;
        for (DfdwTCnsjPowerDissipation powerDissipation : list) {
            if ("chargeMax".equals(powerDissipation.getTimeType())) {
                num++;
            } else if (powerDissipation.getWorkingPower().compareTo(new BigDecimal(inputPower)) >= 0) {
                num++;
            }
        }
        BigDecimal percentage = new BigDecimal(num).divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        score.setScore(percentage);
        if (decimal.compareTo(percentage) <= 0) {
            score.setUtilizeDays(1);
        } else {
            score.setUtilizeDays(0);
        }
        return score;
    }
}




