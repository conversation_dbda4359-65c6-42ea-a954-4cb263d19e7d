package com.soft.gcc.xtbg.cnsj.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictDataMapper;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjMaxLoadMapper;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjPowerDissipationMapper;
import com.soft.gcc.xtbg.cnsj.pojo.response.DailyLoadDto;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjDailyLoadService;
import com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjDailyLoadMapper;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_CNSJ_DAILY_LOAD(储能设计-典型日负荷)】的数据库操作Service实现
 * @createDate 2024-07-08 10:36:56
 */
@Service
public class DfdwTCnsjDailyLoadServiceImpl extends ServiceImpl<DfdwTCnsjDailyLoadMapper, DfdwTCnsjDailyLoad>
        implements DfdwTCnsjDailyLoadService {
    private static final Logger log = LoggerFactory.getLogger(DfdwTCnsjDailyLoadServiceImpl.class);
    @Resource
    private DfdwTCnsjPowerDissipationMapper dfdwTCnsjPowerDissipationMapper;
    @Resource
    private DfdwTCnsjMaxLoadMapper dfdwTCnsjMaxLoadMapper;
    @Resource
    private DfdwTDictDataMapper dfdwTDictDataMapper;

    DateTimeFormatter HHmm = DateTimeFormatter.ofPattern("HH:mm");

    @Override
    public DailyLoadDto completeDailyLoad(DfdwTCnsjDailyLoad dailyLoad, PersonEntity person) {
        if (StringUtils.isNotBlank(vaildDailyLoad(dailyLoad))) {
            throw new RuntimeException(vaildDailyLoad(dailyLoad));
        }
        List<DfdwTCnsjDailyLoad> dailyLoads = new ArrayList<>();
        // 获取实际负荷
        List<DfdwTCnsjPowerDissipation> powerDissipationList = dfdwTCnsjPowerDissipationMapper.selectList(new LambdaUpdateWrapper<DfdwTCnsjPowerDissipation>()
                .eq(DfdwTCnsjPowerDissipation::getUserNum, dailyLoad.getUserNum())
                .between(DfdwTCnsjPowerDissipation::getUseDate, dailyLoad.getUseDate(), dailyLoad.getUseDate().plusDays(1).plusSeconds(-1))
                .orderByAsc(DfdwTCnsjPowerDissipation::getUseDate)
        );
        if (powerDissipationList.isEmpty()) {
            throw new RuntimeException("未找到用户的功耗数据信息，请在功耗数据中添加！");
        }
        List<DailyLoadDto> actualLoadList = powerDissipationList.stream().map(powerDissipation -> new DailyLoadDto(powerDissipation.getUseDate(), powerDissipation.getWorkingPower())).collect(Collectors.toList());

        // 获取预计需要负荷
        List<DfdwTCnsjMaxLoad> maxLoadList = dfdwTCnsjMaxLoadMapper.selectList(new LambdaUpdateWrapper<DfdwTCnsjMaxLoad>()
                .eq(DfdwTCnsjMaxLoad::getUserNum, dailyLoad.getUserNum())
                .eq(DfdwTCnsjMaxLoad::getYear, dailyLoad.getUseDate().getYear())
                .eq(DfdwTCnsjMaxLoad::getMonth, dailyLoad.getUseDate().getMonthValue())
        );
        BigDecimal estimateLoad;
        if (!maxLoadList.isEmpty()) {
            estimateLoad = maxLoadList.get(0).getMaxLoad();
        } else if (!powerDissipationList.isEmpty()) {
            estimateLoad = dfdwTCnsjPowerDissipationMapper.selectMaxLoad(dailyLoad.getUserNum(), dailyLoad.getUseDate().with(TemporalAdjusters.firstDayOfMonth()), dailyLoad.getUseDate().with(TemporalAdjusters.lastDayOfMonth()).plusDays(1).minusSeconds(1));
        } else {
            estimateLoad = BigDecimal.ZERO;
        }
        List<DailyLoadDto> estimateLoadList = new ArrayList<>();
        for (DailyLoadDto dto : actualLoadList) {
            dailyLoads.add(new DfdwTCnsjDailyLoad(dailyLoad.getUserNum(), "actualLoad", dto.getUseDate(), dto.getPower(), dailyLoad.getInputPower()));
            estimateLoadList.add(new DailyLoadDto(dto.getUseDate(), estimateLoad));
            dailyLoads.add(new DfdwTCnsjDailyLoad(dailyLoad.getUserNum(), "estimateLoad", dto.getUseDate(), estimateLoad, dailyLoad.getInputPower()));
        }

        // 获取放电负荷
        // 系数
        List<DfdwTDictData> coefficientList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-coefficient")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 系数功率充电时间
        List<DfdwTDictData> chargeTimeList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-chargingTime")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 最大功率充电时间
        List<DfdwTDictData> chargeTimeMaxList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-chargeTimeMax")
                .eq(DfdwTDictData::getStatus, 0)
        );
        // 放电时间
        List<DfdwTDictData> dischargeTimeList = dfdwTDictDataMapper.selectList(new LambdaUpdateWrapper<DfdwTDictData>()
                .eq(DfdwTDictData::getDictType, "cnsj-dischargeTime")
                .eq(DfdwTDictData::getStatus, 0)
        );
        List<DailyLoadDto> dischargeLoadList = new ArrayList<>();
        List<DailyLoadDto> weightLoadList = new ArrayList<>();
        for (DailyLoadDto dto : actualLoadList) {
            DailyLoadDto dischargeLoad = new DailyLoadDto(dto.getUseDate(), BigDecimal.ZERO);
            // 系数功率充电
            for (DfdwTDictData data : chargeTimeList) {
                String now = dto.getUseDate().format(HHmm);
                String start = data.getLabel().split("-")[0];
                String end = data.getLabel().split("-")[1];
                if (now.compareTo(start) >= 0 && now.compareTo(end) <= 0) {
                    DfdwTDictData dictData = coefficientList.stream().filter(item -> "cnsj-coefficient-charging".equals(item.getValue())).findFirst().get();
                    BigDecimal power = BigDecimal.valueOf(Double.parseDouble(dictData.getLabel())).multiply(BigDecimal.valueOf(dailyLoad.getInputPower()));
                    if (power.compareTo(estimateLoad.subtract(dto.getPower())) <= 0) {
                        dischargeLoad = new DailyLoadDto(dto.getUseDate(), power);
                    } else {
                        dischargeLoad = new DailyLoadDto(dto.getUseDate(), estimateLoad.subtract(dto.getPower()));
                    }
                }
            }
            // 最大功率充电
            for (DfdwTDictData data : chargeTimeMaxList) {
                String now = dto.getUseDate().format(HHmm);
                String start = data.getLabel().split("-")[0];
                String end = data.getLabel().split("-")[1];
                if (now.compareTo(start) >= 0 && now.compareTo(end) <= 0) {
                    dischargeLoad = new DailyLoadDto(dto.getUseDate(), estimateLoad.subtract(dto.getPower()));
                }
            }
            // 放电
            for (DfdwTDictData data : dischargeTimeList) {
                String now = dto.getUseDate().format(HHmm);
                String start = data.getLabel().split("-")[0];
                String end = data.getLabel().split("-")[1];
                if (now.compareTo(start) >= 0 && now.compareTo(end) < 0) {
                    DfdwTDictData dictData = coefficientList.stream().filter(item -> "cnsj-coefficient-discharge".equals(item.getValue())).findFirst().get();
                    BigDecimal power = BigDecimal.valueOf(Double.parseDouble(dictData.getLabel())).multiply(dto.getPower());
                    if (power.compareTo(BigDecimal.valueOf(dailyLoad.getInputPower())) <= 0) {
                        dischargeLoad = new DailyLoadDto(dto.getUseDate(), power.multiply(BigDecimal.valueOf(-1)));
                    } else {
                        dischargeLoad = new DailyLoadDto(dto.getUseDate(), BigDecimal.valueOf(dailyLoad.getInputPower()).multiply(BigDecimal.valueOf(-1)));
                    }
                } else if (now.compareTo(end) == 0) {
                    dischargeLoad = new DailyLoadDto(dto.getUseDate(), BigDecimal.ZERO);
                }
            }
            dischargeLoadList.add(dischargeLoad);
            dailyLoads.add(new DfdwTCnsjDailyLoad(dailyLoad.getUserNum(), "dischargeLoad", dto.getUseDate(), dischargeLoad.getPower(), dailyLoad.getInputPower()));
            // 加权负荷
            weightLoadList.add(new DailyLoadDto(dto.getUseDate(), dischargeLoad.getPower().add(dto.getPower())));
            dailyLoads.add(new DfdwTCnsjDailyLoad(dailyLoad.getUserNum(), "weightLoad", dto.getUseDate(), dischargeLoad.getPower().add(dto.getPower()), dailyLoad.getInputPower()));
        }
        batchInsertDailyLoad(dailyLoads, person);
        return new DailyLoadDto(actualLoadList, dischargeLoadList, weightLoadList, estimateLoadList);
    }

    @Override
    public DailyLoadDto getDailyLoad(DfdwTCnsjDailyLoad dailyLoad, PersonEntity person) {
        if (StringUtils.isNotBlank(vaildDailyLoad(dailyLoad))) {
            throw new RuntimeException(vaildDailyLoad(dailyLoad));
        }
        List<DfdwTCnsjDailyLoad> list = this.list(new LambdaQueryWrapper<DfdwTCnsjDailyLoad>()
                .eq(DfdwTCnsjDailyLoad::getUserNum, dailyLoad.getUserNum())
                .between(DfdwTCnsjDailyLoad::getUseDate, dailyLoad.getUseDate(), dailyLoad.getUseDate().plusDays(1).plusSeconds(-1))
                .eq(DfdwTCnsjDailyLoad::getInputPower, dailyLoad.getInputPower())
        );
        if (!list.isEmpty()) {
            List<DailyLoadDto> actualLoadList = list.stream().filter(item -> "actualLoad".equals(item.getType())).map(item -> new DailyLoadDto(item.getUseDate(), item.getPower())).sorted(Comparator.comparing(DailyLoadDto::getUseDate)).collect(Collectors.toList());
            List<DailyLoadDto> dischargeLoadList = list.stream().filter(item -> "dischargeLoad".equals(item.getType())).map(item -> new DailyLoadDto(item.getUseDate(), item.getPower())).sorted(Comparator.comparing(DailyLoadDto::getUseDate)).collect(Collectors.toList());
            List<DailyLoadDto> weightLoadList = list.stream().filter(item -> "weightLoad".equals(item.getType())).map(item -> new DailyLoadDto(item.getUseDate(), item.getPower())).sorted(Comparator.comparing(DailyLoadDto::getUseDate)).collect(Collectors.toList());
            List<DailyLoadDto> estimateLoadList = list.stream().filter(item -> "estimateLoad".equals(item.getType())).map(item -> new DailyLoadDto(item.getUseDate(), item.getPower())).sorted(Comparator.comparing(DailyLoadDto::getUseDate)).collect(Collectors.toList());
            return new DailyLoadDto(actualLoadList, dischargeLoadList, weightLoadList, estimateLoadList);
        } else {
            return completeDailyLoad(dailyLoad, person);
        }
    }

    private String vaildDailyLoad(DfdwTCnsjDailyLoad dailyLoad) {
        if (StringUtils.isBlank(dailyLoad.getUserNum())) {
            return "用户名称不能为空";
        } else if (dailyLoad.getUseDate() == null) {
            return "日期不能为空";
        } else if (dailyLoad.getInputPower() == null) {
            return "负荷值不能为空";
        } else if (dailyLoad.getInputPower() <= 0) {
            return "负荷值不能小于0";
        }
        return "";
    }

    public void batchInsertDailyLoad(List<DfdwTCnsjDailyLoad> saveList, PersonEntity person) {
        int batchSize = 100;
        for (int i = 0; i < saveList.size(); i += batchSize) {
            List<DfdwTCnsjDailyLoad> subList = saveList.subList(i, Math.min(i + batchSize, saveList.size()));
            baseMapper.insertOrUpdateBatch(subList, person.getLoginName());
        }
    }
}




