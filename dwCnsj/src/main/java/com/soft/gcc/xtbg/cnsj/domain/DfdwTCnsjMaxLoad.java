package com.soft.gcc.xtbg.cnsj.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 储能设计-月最大负荷
 * <AUTHOR>
 * @TableName DFDW_T_CNSJ_MAX_LOAD
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="DFDW_T_CNSJ_MAX_LOAD")
@Data
public class DfdwTCnsjMaxLoad extends DfdwBaseEntity {
    /**
     * 
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编号
     */
    @TableField(value = "userNum")
    private String userNum;

    /**
     * 年
     */
    @TableField(value = "year")
    private Integer year;

    /**
     * 月
     */
    @TableField(value = "month")
    private Integer month;

    /**
     * 最大负荷
     */
    @TableField(value = "max_load")
    private BigDecimal maxLoad;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    @TableField(exist = false)
    private Integer startYear;
    @TableField(exist = false)
    private Integer endYear;
    @TableField(exist = false)
    private Integer startMonth;
    @TableField(exist = false)
    private Integer endMonth;
}