package com.soft.gcc.xtbg.cnsj.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("DFDW_T_CNSJ_PROJECT")
public class DfdwTCnsjProject extends DfdwBaseEntity implements Serializable {

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "Id", type = IdType.AUTO)
    @JSONField(name="Id")
    private Integer Id;

    @ApiModelProperty(value = "项目名称")
    @JSONField(name="projectName")
    private String projectName;

    @ApiModelProperty(value = "用户编号")
    @JSONField(name="userNum")
    private String userNum;

    @ApiModelProperty(value = "用户名称")
    @JSONField(name="userName")
    private String userName;

    @ApiModelProperty(value = "真实姓名")
    @JSONField(name="realName")
    private String realName;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTimeArray;
}
