package com.soft.gcc.xtbg.cnsj.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyLoad;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjProject;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjDailyLoadService;
import com.soft.gcc.xtbg.cnsj.service.DfdwTCnsjProjectService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/cnsj/dailyLoad")
@RestController
public class DfdwTCnsjDailyLoadController extends BaseController {
    @Resource
    private DfdwTCnsjDailyLoadService dfdwTCnsjDailyLoadService;
    @Resource
    private DfdwTCnsjProjectService dfdwTCnsjProjectService;

    @RequestMapping("/GetUserList")
    public Result<Object> getUserList() {
        try {
            List<DfdwTCnsjProject> list = dfdwTCnsjProjectService.list(new LambdaUpdateWrapper<DfdwTCnsjProject>()
                    .orderByAsc(DfdwTCnsjProject::getUserNum)
            );
            return Result.ok(list);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/CompleteDailyLoad")
    public Result<Object> completeDailyLoad(@RequestBody @Valid DfdwTCnsjDailyLoad dailyLoad) {
        PersonEntity person = user();
        try {
            return Result.ok(dfdwTCnsjDailyLoadService.completeDailyLoad(dailyLoad, person));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @RequestMapping("/GetDailyLoad")
    public Result<Object> getDailyLoad(@RequestBody @Valid DfdwTCnsjDailyLoad dailyLoad) {
        PersonEntity person = user();
        try {
            return Result.ok(dfdwTCnsjDailyLoadService.getDailyLoad(dailyLoad, person));
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }
}
