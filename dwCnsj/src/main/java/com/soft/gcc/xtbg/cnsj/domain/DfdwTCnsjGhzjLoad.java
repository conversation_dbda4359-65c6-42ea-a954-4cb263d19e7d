package com.soft.gcc.xtbg.cnsj.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
@TableName("DFDW_T_CNSJ_GHZJ_LOAD")
@Data
public class DfdwTCnsjGhzjLoad extends DfdwBaseEntity {
    /**
     *  主键
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户编号
     */
    @TableField("userNum")
    private String userNum;

    /**
     * 年份
     */
    @TableField
    private Integer year;

    /**
     * 月份
     */
    @TableField
    private Integer month;

    /**
     * 最高功率
     */
    @TableField("max_working_power")
    private BigDecimal maxWorkingPower;

    /**
     * 最高功率时间段
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("max_working_power_time")
    private Date maxWorkingPowerTime;

    /**
     * 11-13点最高功率
     */
    @TableField("max_working_power11_to_13")
    private BigDecimal maxWorkingPower11To13;

    /**
     * 相差功率（最高功率 - 11-13点最高功率）
     */
    @TableField("power_difference")
    private BigDecimal powerDifference;

    /**
     * 8-10点最低功率时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "min_working_power_time8_to_10")
    private Date minWorkingPowerTime8To10;

    /**
     * 8-10点最低功率
     */
    @TableField("min_working_power8_to_10")
    private BigDecimal minWorkingPower8To10;

    /**
     * 10-11点最低功率时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "min_working_power_time10_to_11")
    private Date minWorkingPowerTime10To11;

    /**
     * 10-11点最低功率
     */
    @TableField("min_working_power10to_11")
    private BigDecimal minWorkingPower10To11;

    /**
     * 15-17点最低功率时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "min_working_power_time15_to_17")
    private Date minWorkingPowerTime15To17;

    /**
     * 15-17点最低功率
     */
    @TableField("min_working_power15_to_17")
    private BigDecimal minWorkingPower15To17;

}