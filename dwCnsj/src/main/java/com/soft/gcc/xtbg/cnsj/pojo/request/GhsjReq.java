package com.soft.gcc.xtbg.cnsj.pojo.request;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import com.soft.gcc.xtbg.cnsj.pojo.dto.ExcelVerifyInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
public class GhsjReq extends ExcelVerifyInfoDTO implements Serializable {

    @Excel(name = "用户编号")
    @NotNull(message = "不能为空")
    private String userNum;

    @Excel(name = "用户名称")
    private String userName;

    @Excel(name = "使用日期", exportFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useDate;

    @Excel(name = "使用功率（瞬时有功）")
    @NotNull(message = "不能为空")
    @DecimalMin(value = "0.01", message = "不能低于0.01")
    private BigDecimal workingPower;


    private String deleted;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 更新时间
     */
    private Date updateTime;
}
