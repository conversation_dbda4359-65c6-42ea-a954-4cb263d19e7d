package com.soft.gcc.xtbg.cnsj.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.fastjson.JSONObject;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation;
import com.soft.gcc.xtbg.cnsj.pojo.request.GhsjReq;
import com.soft.gcc.xtbg.cnsj.service.GhsjService;
import com.soft.gcc.xtbg.cnsj.util.ExcelUtil;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.docx4j.wml.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RequestMapping("/cnsj/ghsj")
@RestController
public class GhsjController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(GhsjController.class);

    @Resource
    private GhsjService ghsjService;

    private static final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());


    /**
     * 多Sheet导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importSheetsExcel")
    @PreAuthorize("@ss.hasPermi('JDWCN01GH01QX02')")
    public Result<Object> importSheetsExcel(MultipartFile file) throws Exception {
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();

        long start = System.currentTimeMillis();
        List<GhsjReq> ghsjReqList = processExcelFile(file).get();
        // 将List转换为Set
        Set<GhsjReq> set = new HashSet<>(ghsjReqList);
        // 将Set转换回List
        List<GhsjReq> convertedList = new ArrayList<>(set);

        long end = System.currentTimeMillis();
        log.info("解析表格运行时间：" + (end - start));
        log.info("Excel表格数据大小："+convertedList.size());
        log.info("数据第一行："+convertedList.get(0));
        log.info("数据最后一行："+convertedList.get(convertedList.size()-1));
        ghsjService.saveExcelRecord(convertedList, user);
        // 老的代码
//        for (int i = 0; i < sheetNum; i++) {
//            System.out.println("解析的当前页："+i);
//            // 根据file得到Workbook,主要是要根据这个对象获取,传过来的excel有几个sheet页
//            ImportParams params = new ImportParams();
//            // 第几个sheet表页
//            params.setStartSheetIndex(i);
//            //设置表标题行数
//            params.setTitleRows(0);
//            try {
//                //读取的excel数据集合
//                ExcelImportResult<GhsjReq> result = ExcelImportUtil.importExcelMore(file.getInputStream(), GhsjReq.class, params);
//
////                ExcelUtil.importExcel(file.getInputStream(), )
//                System.out.println("1: " + result.getList().size());
////                ghsjService.saveExcelRecord(result, user);
//            } catch (NoSuchElementException e) {
//                return Result.error("模板不能为空");
//            } catch (Exception e) {
//                e.printStackTrace();
//                return Result.error("excel导入异常：" + e.getMessage());
//            }
//        }
        return Result.ok("导入成功");
    }

    public CompletableFuture<List<GhsjReq>> processExcelFile(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes(); // Read file content into byte array

            try (InputStream inputStream = new ByteArrayInputStream(fileBytes);
                 Workbook workbook = new XSSFWorkbook(inputStream)) {

                List<CompletableFuture<List<GhsjReq>>> futures = IntStream.range(0, workbook.getNumberOfSheets())
                        .mapToObj(i -> CompletableFuture.supplyAsync(() -> {
                            try (InputStream is = new ByteArrayInputStream(fileBytes)) {
                                System.out.println("===========开始解析第" + i + "页");
                                return ExcelUtil.importExcel(is, i, 0, 1, GhsjReq.class);
                            } catch (IOException e) {
                                log.error("Error processing sheet " + i + ": " + e.getMessage());
                                throw new RuntimeException(e);
                            }
                        }, executor))
                        .collect(Collectors.toList());

                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                return allFutures.thenApply(v ->
                        futures.stream()
                                .map(CompletableFuture::join)
                                .collect(ArrayList::new, List::addAll, List::addAll)
                );
            }

        } catch (Exception e) {
            log.error("Error processing Excel file: " + e.getMessage());
            throw new RuntimeException("Error parsing Excel file", e);
        }
    }

    public CompletableFuture<List<GhsjReq>> processExcelFile1(MultipartFile file) {
        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            /**
             * 使用CompletableFuture 并行随机解析多sheet
             */
            List<CompletableFuture<List<GhsjReq>>> futures = IntStream.range(0, workbook.getNumberOfSheets())
                    .parallel()
                    .mapToObj(i -> CompletableFuture.supplyAsync(() -> {
                        try {
                            System.out.println("===========开始解析第" + i + "页");
                            return ExcelUtil.importExcel(file.getInputStream(), i, 0, 1, GhsjReq.class);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    }))
                    .collect(Collectors.toList());

            // Combine all CompletableFuture results into a single list
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            return allFutures.thenApply(v ->
                    futures.stream()
                            .map(CompletableFuture::join)
                            .collect(ArrayList::new, List::addAll, List::addAll)
            );

        } catch (Exception e) {
            e.printStackTrace();
            // Handle exceptions as needed
            throw new RuntimeException("解析Excel表格时报错=================");
        }
    }

//    public CompletableFuture<List<GhsjReq>> processExcelFile(MultipartFile file) {
//        try {
//            Workbook workbook = new XSSFWorkbook(file.getInputStream());
//
//            List<CompletableFuture<List<GhsjReq>>> futures = new ArrayList<>();
//
//            // Iterate over each sheet
//            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
//                int finalI = i;
//                CompletableFuture<List<GhsjReq>> future = CompletableFuture.supplyAsync(
//                        () -> {
//                            try {
//                                return ExcelUtil.importExcel(file.getInputStream(), finalI, 0, 1, GhsjReq.class);
//                            } catch (IOException e) {
//                                log.error("解析Excel第：{}页报错",finalI);
//                                throw new RuntimeException(e);
//                            }
//                        });
//                futures.add(future);
//            }
//
//            // Combine all CompletableFuture results into a single list
//            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
//                    futures.toArray(new CompletableFuture[0])
//            );
//
//            return allFutures.thenApply(v ->
//                    futures.stream()
//                            .map(CompletableFuture::join)
//                            .collect(ArrayList::new, List::addAll, List::addAll)
//            );
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            // Handle exceptions as needed
//            throw new RuntimeException("解析Excel表格时报错=================");
//        }
//    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        //resources路径下的文件地址
        String filePath = "importTemplate/jsyxxImport.xlsx";
        //用来读取resources下的文件
        ClassPathResource classPathResource = new ClassPathResource(filePath);
        try {

            //获取文件
            File file = classPathResource.getFile();
            // 获取文件名
            String filename = file.getName();
            // 获取文件后缀名
            String ext = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();

            // 将文件写入输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            InputStream fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();

            // 清空response
            response.reset();
            // 设置response的Header
            response.setCharacterEncoding("UTF-8");
            //Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
            //attachment表示以附件方式下载   inline表示在线打开   "Content-Disposition: inline; filename=文件名.mp3"
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            // 告知浏览器文件的大小
            response.addHeader("Content-Length", "" + file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 普通导入
     *
     * @param file
     */
    @PostMapping("/import")
    public Result<Object> importExcel(MultipartFile file) throws Exception {

        ImportParams importParams = new ImportParams();
        //表格标题行数,默认0
        importParams.setTitleRows(0);
        //是否需要校验上传的Excel
        importParams.setNeedVerify(false);
        //告诉easypoi我们自定义的验证器
//        importParams.setVerifyHandler(userVerifyHandler);
        ExcelImportResult<GhsjReq> result = ExcelImportUtil.importExcelMore(file.getInputStream(), GhsjReq.class, importParams);
        return ghsjService.saveExcelRecord(result.getList(), SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser());
    }

    public static Workbook getWorkBook(MultipartFile file) throws IOException {
        //这样写excel能兼容03和07
        InputStream is = file.getInputStream();
        Workbook hssfWorkbook = null;
        try {
            hssfWorkbook = new HSSFWorkbook(is);
        } catch (Exception ex) {
            is = file.getInputStream();
            hssfWorkbook = new XSSFWorkbook(is);
        }
        return hssfWorkbook;
    }

    /**
     * 列表
     *
     * @param ghsj
     * @return
     */
    @PostMapping("/GetGhsjList")
    public Result<Object> GetGhsjList(@RequestBody DfdwTCnsjPowerDissipation ghsj) {
        return ghsjService.GetGhsjList(ghsj);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    public Result<Object> deleteGhsj(@RequestParam("id") Integer id) {
        return ghsjService.deleteGhsj(id);
    }

    /**
     * 修改
     *
     * @param ghsj
     * @return
     */
    @PutMapping("/update")
    public Result<Object> updateGhsj(@RequestBody DfdwTCnsjPowerDissipation ghsj) {
        return ghsjService.updateGhsj(ghsj);
    }



}
