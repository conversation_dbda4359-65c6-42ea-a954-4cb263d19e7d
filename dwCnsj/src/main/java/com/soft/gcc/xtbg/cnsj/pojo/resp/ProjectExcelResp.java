package com.soft.gcc.xtbg.cnsj.pojo.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class ProjectExcelResp {

    @Excel(name="项目名称",orderNum="1")
    private String projectName;

    @Excel(name="用户编号",orderNum="2")
    private String userNum;

    @Excel(name="用户名称",orderNum="3")
    private String userName;

    @Excel(name="创建人",orderNum="4")
    private String realName;

    @Excel(name="创建时间",orderNum = "5",width = 20.0,exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
