package com.soft.gcc.xtbg.cnsj.service;

import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_CNSJ_MAX_LOAD(储能设计-月最大负荷)】的数据库操作Service
* @createDate 2024-07-08 09:28:23
*/
public interface DfdwTCnsjMaxLoadService extends IService<DfdwTCnsjMaxLoad> {

    List<DfdwTCnsjMaxLoad> getMaxLoadData(DfdwTCnsjMaxLoad maxLoad);

    List<DfdwTCnsjMaxLoad> completeMaxLoad(DfdwTCnsjMaxLoad maxLoad);

}
