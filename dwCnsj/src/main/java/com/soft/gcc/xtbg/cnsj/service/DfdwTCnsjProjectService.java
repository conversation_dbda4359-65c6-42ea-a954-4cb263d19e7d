package com.soft.gcc.xtbg.cnsj.service;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.base.controller.Result;
import com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjProject;
import com.soft.gcc.xtbg.cnsj.pojo.request.GhsjReq;
import com.soft.gcc.xtbg.cnsj.pojo.request.ProjectManageExcelReq;
import com.soft.gcc.xtbg.cnsj.pojo.resp.ProjectExcelResp;
import com.yyszc.wpbase.ventity.PersonEntity;

import java.util.List;

public interface DfdwTCnsjProjectService extends IService<DfdwTCnsjProject> {

    /**
     * 项目列表
     * @param req
     * @return
     */
    Result<Object> GetProjectList(DfdwTCnsjProject req);

    /**
     * 新建项目
     * @param req
     * @return
     */
    Result<Object> createProject(DfdwTCnsjProject req);

    /**
     * 修改项目
     * @param req
     * @return
     */
    Result<Object> updateProject(DfdwTCnsjProject req);

    /**
     * 删除
     * @param id
     * @return
     */
    Result<Object> deleteProject(Integer id);

    void saveExcelRecord(List<ProjectManageExcelReq> list, PersonEntity user);

    List<ProjectExcelResp> getExcelList(DfdwTCnsjProject req);
}
