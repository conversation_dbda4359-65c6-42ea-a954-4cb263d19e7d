package com.soft.gcc.common.dfdw_dict.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.security.service.TokenService;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictType;
import com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictTypeMapper;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictTypeService;
import com.soft.gcc.xtbg.base.controller.Result;

import com.yyszc.wpbase.ventity.PersonEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class DfdwTDictTypeServiceImpl extends ServiceImpl<DfdwTDictTypeMapper, DfdwTDictType>  implements DfdwTDictTypeService {

    @Resource
    DfdwTDictTypeMapper dfdwTDictTypeMapper;
    @Resource
    DfdwTDictDataService dfdwTDictDataService;

    /**
     * 新增字典
     * @param dfdwTDictType
     * @return
     */
    @Override
    public Result<Object> createDictType(DfdwTDictType dfdwTDictType){
        // 校验正确性
        Result<Object> result = validateDictTypeForCreateOrUpdate(null, dfdwTDictType.getName(), dfdwTDictType.getType());
        if (result.getCode() != 200) {
            return result;
        }
        // 创建时间
        dfdwTDictType.setCreateTime(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //用户id
        dfdwTDictType.setCreator(user.getId()+"");
        dfdwTDictType.setDeleted("0");
        //更新用户
        dfdwTDictType.setUpdater(user.getId()+"");
        //更新时间
        dfdwTDictType.setUpdateTime(new Date());
        //新增
        dfdwTDictTypeMapper.insert(dfdwTDictType);
        return result;
    }

    private Result<Object> validateDictTypeForCreateOrUpdate(Integer id, String name, String type) {
        Result<Object> result;
        // 校验字典类型的名字的唯一性
        result =  validateDictTypeNameUnique(id, name);
        if (result.getCode() != 200) {
            return  result;
        }
        // 校 验字典类型的类型的唯一性
        result =  validateDictTypeUnique(id, type);
        return result;

    }

    private Result<Object> validateDictTypeUnique(Integer id, String type)   {
        //查询字典类型
        List<DfdwTDictType> dictTypes = dfdwTDictTypeMapper.selectList(new LambdaQueryWrapper<DfdwTDictType>()
                .eq(type != null , DfdwTDictType::getType,type)
                .eq( DfdwTDictType::getDeleted,0));

        if (id == null){
            if (CollectionUtils.isNotEmpty(dictTypes)) {
                return Result.error("字典类型已存在");
            }
        } else {
            if (CollectionUtils.isNotEmpty(dictTypes) && id != dictTypes.get(0).getId()) {
                return Result.error("字典类型已存在");
            }
        }
       return Result.ok();
    }

    private Result<Object> validateDictTypeNameUnique(Integer id, String name) {
        //查询字典名称
        List<DfdwTDictType> dictNames = dfdwTDictTypeMapper.selectList(new LambdaQueryWrapper<DfdwTDictType>()
                .eq(name != null , DfdwTDictType::getName,name)
                .eq( DfdwTDictType::getDeleted,0));
        if (id == null){
            if (CollectionUtils.isNotEmpty(dictNames)) {
                return Result.error("字典名称已存在");
            }
        } else {
            if (CollectionUtils.isNotEmpty(dictNames) && id != dictNames.get(0).getId()) {
                return Result.error("字典名称已存在");
            }
        }
        return Result.ok();
    }


    /**
     * 删除字典类型
     * @return
     */
    @Override
    public Result<Object> deleteDictType(Integer id) {
        DfdwTDictType dfdwTDictType = this.dfdwTDictTypeMapper.selectById(id);
        //校验该字典类型是否存在字典数据
        Result<Object> result = validateDictTypeDel(dfdwTDictType);
        if (result.getCode() != 200) {
            return result;
        }
        //删除标识
        dfdwTDictType.setDeleted("1");
        //删除时间
        dfdwTDictType.setDeletedTime(new Date());
        this.dfdwTDictTypeMapper.updateById(dfdwTDictType);
        this.dfdwTDictTypeMapper.deleteById(id);
        return result;
    }

    private Result<Object> validateDictTypeDel(DfdwTDictType dfdwTDictType) {
        List<DfdwTDictData> dataByType = dfdwTDictDataService.getDataByType(dfdwTDictType);
        if (CollectionUtils.isNotEmpty(dataByType)){
            return Result.error("字典类型存在字典数据");
        }
        return Result.ok(dfdwTDictType);
    }

    /**
     * 修改字典类型
     * @return
     */
    @Override
    public Result<Object> updateDictType(DfdwTDictType dfdwTDictType) {
        // 校验正确性
        Result<Object> result = validateDictTypeForCreateOrUpdate(dfdwTDictType.getId(), dfdwTDictType.getName(), dfdwTDictType.getType());
        if (result.getCode() != 200) {
            return result;
        }
        //修改前的数据
        DfdwTDictType dictTypeOriginal = dfdwTDictTypeMapper.selectById(dfdwTDictType.getId());
        DfdwTDictType type = new DfdwTDictType();
        type.setType(dictTypeOriginal.getType());
        //修改前的字典数据
        List<DfdwTDictData> dataByType = dfdwTDictDataService.getDataByType(type);
        //修改前和修改后的type不相同
        if (!dictTypeOriginal.getType().equals(dfdwTDictType.getType())) {
            for (DfdwTDictData data: dataByType) {
                data.setDictType(dfdwTDictType.getType());
                dfdwTDictDataService.updateById(data);
            }
        }
        //修改时间
        dfdwTDictType.setUpdateTime(new Date());
        // 获取当前的用户
        PersonEntity user = SpringUtil.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest()).getUser();
        //修改用户id
        dfdwTDictType.setUpdater(user.getId()+"");
        //修改
        dfdwTDictTypeMapper.updateById(dfdwTDictType);
        return result;
    }

    /**
     * 字典类型分页查询
     * @param dfdwTDictType
     * @return
     */
    @Override
    public IPage<DfdwTDictType> getList(DfdwTDictType dfdwTDictType) {
        IPage<DfdwTDictType> list = new Page<>();
        list.setCurrent(dfdwTDictType.getPageNum());
        list.setSize(dfdwTDictType.getPageSize());
        if (dfdwTDictType.getCreateTimeArray() != null && dfdwTDictType.getCreateTimeArray().length == 2) {
            //查询分页
            list = this.page(list, new LambdaQueryWrapper<DfdwTDictType>()
                    .like(dfdwTDictType.getName() != null,DfdwTDictType::getName,dfdwTDictType.getName())
                    .eq(dfdwTDictType.getType() != null,DfdwTDictType::getType,dfdwTDictType.getType())
                    .eq(dfdwTDictType.getStatus() != null,DfdwTDictType::getStatus,dfdwTDictType.getStatus())
                    .eq(DfdwTDictType::getDeleted,0)
                    .between(true, DfdwTDictType::getCreateTime,
                            dfdwTDictType.getCreateTimeArray()[0],dfdwTDictType.getCreateTimeArray()[1])
                    .orderByAsc(DfdwTDictType::getType));
        } else {
            //查询分页
            list = this.page(list, new LambdaQueryWrapper<DfdwTDictType>()
                    .like(dfdwTDictType.getName() != null,DfdwTDictType::getName,dfdwTDictType.getName())
                    .eq(dfdwTDictType.getType() != null,DfdwTDictType::getType,dfdwTDictType.getType())
                    .eq(dfdwTDictType.getStatus() != null,DfdwTDictType::getStatus,dfdwTDictType.getStatus())
                    .eq(DfdwTDictType::getDeleted,0)
                    .orderByAsc(DfdwTDictType::getType));
        }
        return list;
    }
}




