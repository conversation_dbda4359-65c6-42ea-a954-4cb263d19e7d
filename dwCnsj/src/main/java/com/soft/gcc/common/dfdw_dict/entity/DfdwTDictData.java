package com.soft.gcc.common.dfdw_dict.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.soft.gcc.xtbg.base.entity.DfdwBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 东方多维-字典数据
 * <AUTHOR>
 * @TableName DFDW_DICT_DATA
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="DFDW_DICT_DATA")
@Data
public class DfdwTDictData extends DfdwBaseEntity implements Serializable {
    /**
     * 字典编码
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字典排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 字典标签
     */
    @TableField(value = "label")
    private String label;

    /**
     * 字典键值
     */
    @TableField(value = "value")
    private String value;

    /**
     * 字典类型
     */
    @TableField(value = "dict_Type")
    private String dictType;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;
}