package com.soft.gcc.common.dfdw_dict.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.dfdw_dict.entity.DfdwTDictData;
import com.soft.gcc.common.dfdw_dict.service.DfdwTDictDataService;
import com.soft.gcc.xtbg.base.controller.BaseController;
import com.soft.gcc.xtbg.base.controller.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RequestMapping("/dfdw/dictData")
@RestController
public class DfdwTDictDataController extends BaseController {

    @Resource
    private DfdwTDictDataService dfdwTDictDataService;


    /**
     * 新增字典数据
     * @param dfdwTDictData
     * @return
     */
    @PostMapping("/create")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> createDictData( @RequestBody DfdwTDictData dfdwTDictData) {
        Result<Object> createResule =  dfdwTDictDataService.createDictData(dfdwTDictData);
        return createResule ;
    }

    /**
     * 删除字典数据
     * @return
     */
    @DeleteMapping("/delete")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> deleteDictData(@RequestParam("id") Long id) {
        Result<Object> result = dfdwTDictDataService.deleteDictData(id);
        return result;
    }

    /**
     * 修改字典数据
     * @return
     */
    @PutMapping("/update")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> updateDictData(@RequestBody DfdwTDictData dfdwTDictData) {
        Result<Object> result = dfdwTDictDataService.updateDictData(dfdwTDictData);
        return result;
    }


    /**
     * 字典数据分页
     * @param dfdwTDictData
     * @return
     */
    @PostMapping("/dicDataByType")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> dicDataByType( @RequestBody DfdwTDictData dfdwTDictData) {
        IPage<DfdwTDictData> dictDataIPage =  dfdwTDictDataService.dicDataByType(dfdwTDictData);
        return Result.ok(dictDataIPage);
    }


    /**
     * 字典数据分页
     * @param dfdwTDictData
     * @return
     */
    @PostMapping("/dicDataByTypeAll")
//    @PreAuthorize("@ss.hasRole('gcssb-lbgl')")
    public Result<Object> dicDataByTypeAll( @RequestBody DfdwTDictData dfdwTDictData) {
        List<DfdwTDictData> dictDataList =  dfdwTDictDataService.getDictDataByType(dfdwTDictData.getDictType());
        return Result.ok(dictDataList);
    }
}
