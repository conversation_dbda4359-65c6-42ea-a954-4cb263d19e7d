<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjGhzjLoadMapper">

    <select id="computeGhzjLoad" resultType="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjGhzjLoad"
            parameterType="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjGhzjLoad">
        SELECT
        userNum,
        year,
        Month,
        MAX(CASE WHEN RowNum_Overall = 1 THEN workingPower END) AS maxWorkingPower,
        MAX(CASE WHEN RowNum_Overall = 1 THEN useDate END) AS maxWorkingPowerTime,
        MAX(CASE WHEN DATEPART(HOUR, useDate) BETWEEN 11 AND 13 AND RowNum_11_13 = 1 THEN workingPower END) AS maxWorkingPower11To13,
        MAX(CASE WHEN RowNum_Overall = 1 THEN workingPower END) - ISNULL(MAX(CASE WHEN DATEPART(HOUR, useDate) BETWEEN 11 AND 13 AND RowNum_11_13 = 1 THEN workingPower END), 0) AS powerDifference,
        MIN(CASE WHEN DATEPART(HOUR, useDate) BETWEEN 8 AND 10 AND RowNum_8_10 = 1 THEN workingPower END) AS minWorkingPower8To10,
        MAX(CASE WHEN DATEPART(HOUR, useDate) BETWEEN 8 AND 10 AND RowNum_8_10 = 1 THEN useDate END) AS minWorkingPowerTime8To10,
        MIN(CASE WHEN DATEPART(HOUR, useDate) = 10 AND RowNum_10_11 = 1 THEN workingPower END) AS minWorkingPower10To11,
        MAX(CASE WHEN DATEPART(HOUR, useDate) = 10 AND RowNum_10_11 = 1 THEN useDate END) AS minWorkingPowerTime10To11,
        MIN(CASE WHEN DATEPART(HOUR, useDate) BETWEEN 15 AND 17 AND RowNum_15_17 = 1 THEN workingPower END) AS minWorkingPower15To17,
        MAX(CASE WHEN DATEPART(HOUR, useDate) BETWEEN 15 AND 17 AND RowNum_15_17 = 1 THEN useDate END) AS minWorkingPowerTime15To17
        FROM
        (

        SELECT
        userNum,
        YEAR(useDate) as year,
        MONTH(useDate) AS Month,
        useDate,
        ISNULL(workingPower, 0) as workingPower,
        ROW_NUMBER() OVER (PARTITION BY MONTH(useDate) ORDER BY workingPower DESC) AS RowNum_Overall,
        ROW_NUMBER() OVER (PARTITION BY MONTH(useDate), CASE WHEN DATEPART(HOUR, useDate) BETWEEN 11 AND 13 THEN 1 ELSE NULL END ORDER BY workingPower DESC) AS RowNum_11_13,
        ROW_NUMBER() OVER (PARTITION BY MONTH(useDate), CASE WHEN DATEPART(HOUR, useDate) BETWEEN 8 AND 10 THEN 1 ELSE NULL END ORDER BY workingPower ASC) AS RowNum_8_10,
        ROW_NUMBER() OVER (PARTITION BY MONTH(useDate), CASE WHEN DATEPART(HOUR, useDate) = 10 THEN 1 ELSE NULL END ORDER BY workingPower ASC) AS RowNum_10_11,
        ROW_NUMBER() OVER (PARTITION BY MONTH(useDate), CASE WHEN DATEPART(HOUR, useDate) BETWEEN 15 AND 17 THEN 1 ELSE NULL END ORDER BY workingPower ASC) AS RowNum_15_17
        FROM
            DFDW_T_CNSJ_POWER_DISSIPATION
        WHERE
        deleted = '0'
        <if test="userNum != null and userNum != ''">
            AND userNum = #{userNum}
        </if>
        <if test="year != null">
            AND YEAR(useDate) = #{year}
        </if>
        )aa
        GROUP BY
        userNum,year,Month
    </select>

    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        MERGE INTO DFDW_T_CNSJ_GHZJ_LOAD as target
        USING (
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userNum},
            #{item.year},
            #{item.month},
            #{item.maxWorkingPower},
            #{item.maxWorkingPowerTime},
            #{item.maxWorkingPower11To13},
            #{item.powerDifference},
            #{item.minWorkingPowerTime8To10},
            #{item.minWorkingPower8To10},
            #{item.minWorkingPowerTime10To11},
            #{item.minWorkingPower10To11},
            #{item.minWorkingPowerTime15To17},
            #{item.minWorkingPower15To17},
            #{item.creator},
            #{item.updater}
            )
        </foreach>
        ) AS source (userNum, year,month,max_working_power,max_working_power_time,max_working_power11_to_13,
            power_difference,min_working_power_time8_to_10,min_working_power8_to_10, min_working_power_time10_to_11,
        min_working_power10to_11, min_working_power_time15_to_17,min_working_power15_to_17,creator,updater)
        ON target.userNum = source.userNum and target.year =source.year and target.month = source.month
        WHEN MATCHED THEN
        UPDATE SET
        target.max_working_power = source.max_working_power,
        target.max_working_power_time = source.max_working_power_time,
        target.max_working_power11_to_13 = source.max_working_power11_to_13,
        target.power_difference = source.power_difference,
        target.min_working_power_time8_to_10 = source.min_working_power_time8_to_10,
        target.min_working_power8_to_10 = source.min_working_power8_to_10,
        target.min_working_power10to_11 = source.min_working_power10to_11,
        target.min_working_power_time10_to_11 = source.min_working_power_time10_to_11,
        target.min_working_power15_to_17 = source.min_working_power15_to_17,
        target.min_working_power_time15_to_17 = source.min_working_power_time15_to_17,
        target.updater = source.updater,
        target.updateTime =  getdate( )
        WHEN NOT MATCHED THEN
        INSERT (userNum, year,month,max_working_power,max_working_power_time,max_working_power11_to_13,
        power_difference,min_working_power_time8_to_10,min_working_power8_to_10, min_working_power_time10_to_11,
        min_working_power10to_11, min_working_power_time15_to_17,min_working_power15_to_17,creator,updater)
        VALUES (source.userNum,source.year,source.month,source.max_working_power,source.max_working_power_time,source.max_working_power11_to_13,
                source.power_difference,source.min_working_power_time8_to_10,source.min_working_power8_to_10,source.min_working_power_time10_to_11,source.
        min_working_power10to_11,source.min_working_power_time15_to_17,source.min_working_power15_to_17,source.creator,source.updater
        );



    </insert>




</mapper>
