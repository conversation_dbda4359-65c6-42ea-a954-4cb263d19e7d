<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjDailyLoadMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyLoad">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="userNum" column="userNum" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="useDate" column="useDate" jdbcType="TIMESTAMP"/>
            <result property="power" column="power" jdbcType="DECIMAL"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
            <result property="inputPower" column="inputPower" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,userNum,type,
        time,power,creator,
        createTime,updater,updateTime,
        deleted,inputPower
    </sql>
    <insert id="insertOrUpdateBatch">
        MERGE INTO DFDW_T_CNSJ_DAILY_LOAD AS target USING ( VALUES
        <foreach collection="list" item="item" separator=",">
            ( #{item.userNum}, #{item.type}, CONVERT ( datetime2, #{item.useDate} ), #{item.power}, #{item.inputPower}, #{name}, #{name} )
        </foreach>
        ) AS source ( userNum, type, useDate, power, inputPower, creator, updater ) ON target.userNum = source.userNum
            AND CONVERT ( datetime2, target.useDate ) = source.useDate
            AND target.type = source.type
            AND target.inputPower = source.inputPower
            AND target.deleted = 0
        WHEN MATCHED THEN
            UPDATE
            SET target.power = source.power,
                target.updater = source.updater,
                target.updateTime = getdate( )
        WHEN NOT MATCHED THEN
            INSERT ( userNum, type, useDate, power, inputPower, creator, updater )
            VALUES
                ( source.userNum, source.type, source.useDate, source.power, source.inputPower, source.creator, source.updater );
    </insert>
</mapper>
