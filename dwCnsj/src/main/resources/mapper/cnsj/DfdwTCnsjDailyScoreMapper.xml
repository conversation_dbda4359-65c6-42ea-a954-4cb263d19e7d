<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjDailyScoreMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="userNum" column="userNum" jdbcType="VARCHAR"/>
            <result property="score" column="score" jdbcType="DECIMAL"/>
            <result property="utilizeDays" column="utilizeDays" jdbcType="INTEGER"/>
            <result property="power" column="power" jdbcType="DECIMAL"/>
            <result property="useDate" column="useDate" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,userNum,score,
        utilizeDays,power,useDate,
        creator,createTime,updater,
        updateTime,deleted
    </sql>
    <insert id="insertOrUpdateBatch">
        MERGE INTO DFDW_T_CNSJ_DAILY_SCORE AS target USING ( VALUES
        <foreach collection="list" item="item" separator=",">
            ( #{item.userNum}, #{item.score}, #{item.utilizeDays}, #{item.power}, CONVERT ( datetime2, #{item.useDate} ), #{name}, #{name} )
        </foreach>
        ) AS source ( userNum, score, utilizeDays, power, useDate, creator, updater ) ON target.userNum = source.userNum
            AND CONVERT ( datetime2, target.useDate ) = source.useDate
            AND target.power = source.power
            AND target.deleted = 0
        WHEN MATCHED THEN
        UPDATE
            SET target.score = source.score,
                target.utilizeDays = source.utilizeDays,
                target.power = source.power,
                target.updater = source.updater,
                target.updateTime = getdate( )
        WHEN NOT MATCHED THEN
        INSERT ( userNum, score, utilizeDays, power, useDate, creator, updater )
        VALUES
        ( source.userNum, source.score, source.utilizeDays, source.power, source.useDate, source.creator, source.updater );
    </insert>
    <select id="selectScoreYear" resultType="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjDailyScore">
        SELECT
            userNum, power, SUM(score) as score, SUM(utilizeDays) as utilizeDays
        FROM
            DFDW_T_CNSJ_DAILY_SCORE
        where deleted = '0' and userNum = #{userNum} and useDate >= #{startTime} and useDate &lt;= #{endTime} and score >= #{decimal}
        GROUP BY userNum, power
        order by userNum, power
    </select>
    <select id="countUseDate" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT CONVERT(date, useDate)) as distinctCount FROM DFDW_T_CNSJ_DAILY_SCORE
        where userNum = #{userNum}
          and useDate between #{startTime} and #{endTime}
          and deleted = '0'
        <if test="timeList != null and timeList.size() > 0">
            <foreach collection="timeList" item="item" separator=" ">
                and SUBSTRING(CONVERT(VARCHAR, useDate), 12, 8) between #{item.startTime} and #{item.endTime}
            </foreach>
        </if>
    </select>
</mapper>
