<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjPowerDissipationMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjPowerDissipation">
        <id property="Id" column="Id" jdbcType="INTEGER"/>
        <result property="userNum" column="userNum" jdbcType="VARCHAR"/>
        <result property="userName" column="userName" jdbcType="VARCHAR"/>
        <result property="useDate" column="useDate" jdbcType="TIMESTAMP"/>
        <result property="workingPower" column="workingPower" jdbcType="DECIMAL"/>
    </resultMap>


    <sql id="Base_Column_List">
        Id,userNum,userName,useDate,workingPower
    </sql>

    <select id="GetGhsjList" resultMap="BaseResultMap">
        SELECT Id,userNum,userName,useDate,workingPower
        FROM DFDW_T_CNSJ_POWER_DISSIPATION
        <where>
            <if test="req.userName != null and req.userName != ''">
                AND userName LIKE concat('%',#{req.userName},'%')
            </if>
        </where>
    </select>

    <insert id="batchInsertOrUpdatePowerDissipation" parameterType="java.util.List">
        MERGE INTO DFDW_T_CNSJ_POWER_DISSIPATION AS target
        USING (
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userNum},
            #{item.userName},
            CONVERT(datetime2, #{item.useDate}),  -- 使用 CONVERT 函数将 varbinary 转换为 datetime2
            #{item.workingPower},
            #{item.creator}
            )
        </foreach>
        ) AS source (userNum, userName, useDate, workingPower, creator)
        ON target.userNum = source.userNum AND CONVERT(datetime2, target.useDate) = source.useDate
        WHEN MATCHED THEN
        UPDATE SET
        target.userName = source.userName,
        target.workingPower = source.workingPower,
        target.creator = source.creator
        WHEN NOT MATCHED THEN
        INSERT (userNum, userName, useDate, workingPower, creator)
        VALUES (source.userNum, source.userName, source.useDate, source.workingPower, source.creator);
    </insert>

    <select id="selectMaxLoad" resultType="java.math.BigDecimal">
        SELECT MAX(workingPower) FROM DFDW_T_CNSJ_POWER_DISSIPATION
        where userNum = #{userNum}
          and useDate between #{useDate} and #{localDateTime}
          and deleted = '0'
    </select>
    <select id="selectMinLoad" resultType="java.math.BigDecimal">
        SELECT MIN(workingPower) FROM DFDW_T_CNSJ_POWER_DISSIPATION
        where userNum = #{userNum}
          and useDate between #{useDate} and #{localDateTime}
          and deleted = '0'
    </select>
    <select id="countUseDate" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT CONVERT(date, useDate)) as distinctCount FROM DFDW_T_CNSJ_POWER_DISSIPATION
        where userNum = #{userNum}
          and useDate between #{startTime} and #{endTime}
          and deleted = '0'
        <if test="timeList != null and timeList.size() > 0">
            <foreach collection="timeList" item="item" separator=" ">
                and SUBSTRING(CONVERT(VARCHAR, useDate), 12, 8) between #{item.startTime} and #{item.endTime}
            </foreach>
        </if>
    </select>
</mapper>
