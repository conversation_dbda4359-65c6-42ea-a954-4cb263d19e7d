<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjProjectMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjProject">
        <id property="Id" column="Id" jdbcType="INTEGER"/>
        <result property="projectName" column="projectName" jdbcType="VARCHAR"/>
        <result property="userNum" column="userNum" jdbcType="VARCHAR"/>
        <result property="userName" column="userName" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        Id,projectName,userNum,userName,createBy,creationDate,lastUpdateBy,lastUpdateDate,deleted
    </sql>

    <update id="updateToDeleteById">
        UPDATE DFDW_T_CNSJ_PROJECT SET deleted = '1' WHERE Id = #{id}
    </update>

    <!-- 插入多条记录 -->
    <insert id="batchInsertProject" parameterType="java.util.List">
        INSERT INTO DFDW_T_CNSJ_PROJECT (projectName, userNum, userName, creator, createTime, deleted, realName)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.projectName}, #{item.userNum}, #{item.userName}, #{item.creator}, #{item.createTime}, #{item.deleted}, #{item.realName})
        </foreach>
    </insert>

    <select id="getExcelList" resultType="com.soft.gcc.xtbg.cnsj.pojo.resp.ProjectExcelResp">
        SELECT Id,projectName,userNum,userName,realName,creator,createTime,updater,updateTime,deleted FROM DFDW_T_CNSJ_PROJECT
        <where>
            deleted='0'
            <if test="req.userName != null and req.userName != ''">
                and userName like concat('%', #{req.userName}, '%')
            </if>
            <if test="req.projectName != null and req.projectName != ''">
                and projectName like concat('%', #{req.projectName}, '%')
            </if>
            <if test="req.userNum != null and req.userNum != ''">
                and userNum = #{req.userNum}
            </if>
            <if test="req.createTimeArray != null and req.createTimeArray.length == 2">
                AND createTime BETWEEN #{req.createTimeArray[0]} AND #{req.createTimeArray[1]}
            </if>
        </where>
        ORDER BY Id ASC
    </select>
</mapper>
