<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.cnsj.mapper.DfdwTCnsjMaxLoadMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.cnsj.domain.DfdwTCnsjMaxLoad">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="userNum" column="userNum" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="month" column="month" jdbcType="INTEGER"/>
            <result property="max_load" column="max_load" jdbcType="DECIMAL"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updater" column="updater" jdbcType="VARCHAR"/>
            <result property="update_time" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,userNum,year,
        month,max_load,creator,
        create_time,updater,update_time,
        deleted
    </sql>

    <insert id="saveOrUpdateBatchMaxLoad" parameterType="java.util.List">
        MERGE INTO DFDW_T_CNSJ_MAX_LOAD as target
        USING (
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userNum},
            #{item.year},
            #{item.month},
            #{item.maxLoad},
            #{item.updater},
            #{item.creator}
            )
        </foreach>
        ) AS source (userNum, year, month, max_load,creator,updater)
        ON target.userNum = source.userNum and target.year =source.year and target.month = source.month
        WHEN MATCHED THEN
        UPDATE SET
        target.max_load = source.max_load,
        target.updater = source.updater,
        target.updateTime =  getdate( )
        WHEN NOT MATCHED THEN
        INSERT (userNum, year, month, max_load, creator,updater)
        VALUES (source.userNum, source.year, source.month, source.max_load, source.creator,source.updater);
    </insert>

</mapper>
