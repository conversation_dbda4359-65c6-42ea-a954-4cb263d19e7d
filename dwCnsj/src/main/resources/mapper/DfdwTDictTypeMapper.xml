<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.dfdw_dict.mapper.DfdwTDictTypeMapper">
    <resultMap id="BaseResultMap" type="com.soft.gcc.common.dfdw_dict.entity.DfdwTDictType">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="VARCHAR"/>
        <result property="deletedTime" column="deleted_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,type,
        status,remark,creator,create_time,
        updater,update_time,deleted,deleted_time
    </sql>


</mapper>
