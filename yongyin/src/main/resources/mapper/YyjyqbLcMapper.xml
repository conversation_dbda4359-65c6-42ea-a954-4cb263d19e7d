<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.yygl.mapper.YyjyqbLcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.yygl.entity.YyjyqbLc">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="qblx" column="Qblx" jdbcType="VARCHAR"/>
            <result property="time" column="Time" jdbcType="TIMESTAMP"/>
            <result property="applicant" column="Applicant" jdbcType="VARCHAR"/>
            <result property="yysy" column="Yysy" jdbcType="VARCHAR"/>
            <result property="yzfl" column="Yzfl" jdbcType="VARCHAR"/>
            <result property="sqfzr" column="Sqfzr" jdbcType="VARCHAR"/>
            <result property="fzryj" column="Fzryj" jdbcType="VARCHAR"/>
            <result property="fzrqrsj" column="Fzrqrsj" jdbcType="VARCHAR"/>
            <result property="zhglbyj" column="Zhglbyj" jdbcType="VARCHAR"/>
            <result property="zhglbqrsj" column="Zhglbqrsj" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="groupname" column="groupname" jdbcType="VARCHAR"/>
            <result property="zhglbfzr" column="zhglbfzr" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="bh" column="bh" jdbcType="VARCHAR"/>
            <result property="pripority" column="pripority" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="yyzztype" column="yyzztype" jdbcType="VARCHAR"/>
            <result property="yyzzbh" column="yyzzbh" jdbcType="VARCHAR"/>
            <result property="yysend" column="yySend" jdbcType="VARCHAR"/>
            <result property="yycount" column="yyCount" jdbcType="INTEGER"/>
            <result property="jystartdate" column="jyStartDate" jdbcType="TIMESTAMP"/>
            <result property="jyedndate" column="jyEdnDate" jdbcType="TIMESTAMP"/>
            <result property="sdatequantum" column="sDateQuantum" jdbcType="VARCHAR"/>
            <result property="edatequantum" column="eDateQuantum" jdbcType="VARCHAR"/>
            <result property="yysendgroupid" column="yySendGroupID" jdbcType="INTEGER"/>
            <result property="topgroupid" column="TopGroupID" jdbcType="INTEGER"/>
            <result property="groupid" column="GroupID" jdbcType="INTEGER"/>
            <result property="zgsleader" column="zgsleader" jdbcType="VARCHAR"/>
            <result property="zgsleaderyj" column="zgsleaderYj" jdbcType="VARCHAR"/>
            <result property="zgsleadersj" column="zgsleaderSj" jdbcType="VARCHAR"/>
            <result property="userid" column="UserID" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,Qblx,Time,
        Applicant,Yysy,Yzfl,
        Sqfzr,Fzryj,Fzrqrsj,
        Zhglbyj,Zhglbqrsj,name,
        groupname,zhglbfzr,phone,
        bh,pripority,type,
        yyzztype,yyzzbh,yySend,
        yyCount,jyStartDate,jyEdnDate,
        sDateQuantum,eDateQuantum,yySendGroupID,
        TopGroupID,GroupID,zgsleader,
        zgsleaderYj,zgsleaderSj,UserID
    </sql>
</mapper>
