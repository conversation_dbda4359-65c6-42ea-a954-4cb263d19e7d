<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.lcdefine.mapper.LcdefineMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.lcdefine.entity.Lcdefine">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="lcid" column="LcID" jdbcType="INTEGER"/>
            <result property="lcname" column="lcName" jdbcType="VARCHAR"/>
            <result property="ywb" column="ywb" jdbcType="VARCHAR"/>
            <result property="ywurl" column="ywUrl" jdbcType="VARCHAR"/>
            <result property="xszd" column="xszd" jdbcType="VARCHAR"/>
            <result property="isuse" column="isUse" jdbcType="INTEGER"/>
            <result property="appUrl" column="app_url" jdbcType="VARCHAR"/>
            <result property="appYwb" column="app_ywb" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,LcID,lcName,
        ywb,ywUrl,xszd,
        isUse,app_url,app_ywb
    </sql>
</mapper>
