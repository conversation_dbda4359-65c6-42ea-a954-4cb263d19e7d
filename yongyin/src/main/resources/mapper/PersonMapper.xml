<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.person.mapper.PersonMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.person.entity.Person">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="loginname" column="LoginName" jdbcType="VARCHAR"/>
            <result property="realname" column="RealName" jdbcType="VARCHAR"/>
            <result property="password" column="Password" jdbcType="VARCHAR"/>
            <result property="groupid" column="GroupID" jdbcType="INTEGER"/>
            <result property="roleid" column="RoleId" jdbcType="INTEGER"/>
            <result property="telephone" column="Telephone" jdbcType="VARCHAR"/>
            <result property="msgtype" column="MsgType" jdbcType="VARCHAR"/>
            <result property="oa" column="OA" jdbcType="VARCHAR"/>
            <result property="wzUserid" column="WZ_UserID" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="sphone" column="Sphone" jdbcType="VARCHAR"/>
            <result property="zjCs" column="ZJ_CS" jdbcType="INTEGER"/>
            <result property="phname" column="PhName" jdbcType="INTEGER"/>
            <result property="certificateid" column="CertificateID" jdbcType="VARCHAR"/>
            <result property="officephone" column="OfficePhone" jdbcType="VARCHAR"/>
            <result property="bfloginname" column="BFLoginName" jdbcType="VARCHAR"/>
            <result property="pXh" column="P_XH" jdbcType="INTEGER"/>
            <result property="loginname2" column="LoginName2" jdbcType="VARCHAR"/>
            <result property="oldid" column="oldID" jdbcType="INTEGER"/>
            <result property="state" column="state" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,LoginName,RealName,
        Password,GroupID,RoleId,
        Telephone,MsgType,OA,
        WZ_UserID,type,Sphone,
        ZJ_CS,PhName,CertificateID,
        OfficePhone,BFLoginName,P_XH,
        LoginName2,oldID,state
    </sql>
</mapper>
