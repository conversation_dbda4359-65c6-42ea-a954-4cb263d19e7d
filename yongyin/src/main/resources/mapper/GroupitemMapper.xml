<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.common.groupitem.mapper.GroupitemMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.common.groupitem.entity.Groupitem">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="groupname" column="groupname" jdbcType="VARCHAR"/>
            <result property="parentid" column="parentid" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="shortpinyin" column="shortpinyin" jdbcType="VARCHAR"/>
            <result property="dydj" column="dydj" jdbcType="VARCHAR"/>
            <result property="ucomapanybh" column="uComapanyBH" jdbcType="VARCHAR"/>
            <result property="ucomapanyjc" column="uComapanyJC" jdbcType="VARCHAR"/>
            <result property="xh" column="XH" jdbcType="INTEGER"/>
            <result property="isshow" column="IsShow" jdbcType="INTEGER"/>
            <result property="category" column="Category" jdbcType="INTEGER"/>
            <result property="parentidrz" column="parentidRz" jdbcType="INTEGER"/>
            <result property="ucomapanyqc" column="uComapanyQC" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,groupname,parentid,
        type,shortpinyin,dydj,
        uComapanyBH,uComapanyJC,XH,
        IsShow,Category,parentidRz,
        uComapanyQC
    </sql>
</mapper>
