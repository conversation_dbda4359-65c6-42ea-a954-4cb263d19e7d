html{ margin:0; height:auto; }

/*gridview样式*/
.datalist
{
	clear:both;
	border-collapse:collapse;
	background-color: #ffffff;
	empty-cells: show;
	font-size:9pt;
}
.datalist tr td
{
	color:#2e4b5d;
	font-size:9pt;
}
.datalist tr th
{
	white-space:nowrap;
	background-color:#d1d7dc;
	border:0px;
	color:blue;
	border: 0px;
	border-right: 1px dotted #002D96;
	border-bottom: 0px solid #002D96;
	font-size:9pt;
	min-width:20px;
}
.datalist tbody tr
{
	border:0px;
}
.datalist tbody td
{
	white-space:nowrap;
	border:0px;
	border-right: 1px dotted #c1cdd8;
	padding-right: 4px;
	padding-left: 4px;
	border-bottom: 1px solid #c1cdd8;
	height: 22px;
}
.pagebtn
{
	border-right : 1px solid #002D96 ;
	border-bottom: 1px solid #002D96 ;
	border-top: 1px solid #002D96;
	border-left: 1px solid #002D96;
	padding-right: 0px;
	padding-left: 0px;
	padding-top: 0px;
	padding-bottom:0px;
	font-size: 12px;
	filter: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr=#FFFFFF, EndColorStr=#9DBCEA);
	background: -webkit-gradient(linear, left top, left bottom, from(#FFFFFF), to(#9DBCEA)); /* for webkit browsers */
	background: -moz-linear-gradient(top,  #FFFFFF,  #9DBCEA); /* for firefox 3.6+ */
	cursor: hand;
	color: #000099;
	width:80px;
	height:22px;
	behavior:url(../../htc/editbtn.htc);
}
.listpagebtn
{
	margin-top:0px;
	margin-bottom:0px;
	margin-left:7px;
	padding-left: 5px;
	padding-top: 0px;
	padding-bottom:0px;
	font-size: 9pt;
	cursor: hand;
	vertical-align: text-bottom;
	behavior:url(../../htc/editbtn.htc);
}
/*查询按钮样式*/
.imgbutton
{
	border:solid 0px #7F9DB9;
	font-size: 9pt;
	margin:0px;
	padding:0px;
	cursor: hand;
	background-image:url(../Image/button/btnbg.gif);
	height:23px;
	line-height:23px;
	width:74px;
	text-align:center;
	background-color:Transparent;
}
.imgbutton:hover
{
	filter:progid:DXImageTransform.Microsoft.Shadow(color=#3366ff,direction=120,strength=3);/*ie*/
	-moz-box-shadow: 2px 2px 10px #3366ff;/*firefox*/
	-webkit-box-shadow: 2px 2px 10px #3366ff;/*safari或chrome*/
	box-shadow:2px 2px 10px #3366ff;/*opera或ie9*/
}
.imgbutton1
{
	border:solid 0px #7F9DB9;
	font-size: 9pt;
	margin:0px;
	padding:0px;
	cursor: hand;
	background-image:url(../Image/button/buttonbg.gif);
	height:23px;
	line-height:23px;
	width:64px;
	text-align:center;
	background-color:Transparent;
}
.null_panel
{
	width:98%;
	height:98%;
	padding:8px 8px 8px 8px;
	text-align:center;
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#ECE9D8', endColorStr='#CFCECD', gradientType='0');
	background: -webkit-gradient(linear, left top, left bottom, from(#ECE9D8), to(#CFCECD)); /* for webkit browsers */
	background: -moz-linear-gradient(top,  #ECE9D8,  #CFCECD); /* for firefox 3.6+ */
}
.eval_panel
{
	width:98%;
	height:98%;
	padding:8px 8px 8px 8px;
	text-align:center;
	filter:progid:DXImageTransform.Microsoft.Gradient(startColorStr='#B3CEFB', endColorStr='#72A5F9', gradientType='0');
	background: -webkit-gradient(linear, left top, left bottom, from(#B3CEFB), to(#72A5F9)); /* for webkit browsers */
	background: -moz-linear-gradient(top,  #B3CEFB,  #72A5F9); /* for firefox 3.6+ */
}
.divcontainer
{
	background-color:White;
	scrollbar-face-color:#c2d5fc;
	scrollbar-shadow-color: #ffffff;
	scrollbar-highlight-color: #b7caf5;
	scrollbar-3dlight-color: #ffffff;
	scrollbar-darkshadow-color: #9fb5d2;
	scrollbar-track-color:#f2f2f2;
	scrollbar-arrow-color: #4d6185;
	scrollbar-base-color: #c5c6fc;
	overflow-x:auto;
	overflow-y:hidden;
	height:auto;
	width:100%;
}
.divcontainer1
{
	background-color:White;
	scrollbar-face-color:#c2d5fc;
	scrollbar-shadow-color: #ffffff;
	scrollbar-highlight-color: #b7caf5;
	scrollbar-3dlight-color: #ffffff;
	scrollbar-darkshadow-color: #9fb5d2;
	scrollbar-track-color:#f2f2f2;
	scrollbar-arrow-color: #4d6185;
	scrollbar-base-color: #c5c6fc;
	overflow-x:auto;
	overflow-y:auto;
	height:400px;
	width:100%;
}
.divcontainer2
{
	background-color:White;
	scrollbar-face-color:#c2d5fc;
	scrollbar-shadow-color: #ffffff;
	scrollbar-highlight-color: #b7caf5;
	scrollbar-3dlight-color: #ffffff;
	scrollbar-darkshadow-color: #9fb5d2;
	scrollbar-track-color:#f2f2f2;
	scrollbar-arrow-color: #4d6185;
	scrollbar-base-color: #c5c6fc;
	overflow-x:auto;
	overflow-y:auto;
	height:200px;
	width:100%;
}
.divcontainer3
{
	background-color:White;
	scrollbar-face-color:#c2d5fc;
	scrollbar-shadow-color: #ffffff;
	scrollbar-highlight-color: #b7caf5;
	scrollbar-3dlight-color: #ffffff;
	scrollbar-darkshadow-color: #9fb5d2;
	scrollbar-track-color:#f2f2f2;
	scrollbar-arrow-color: #4d6185;
	scrollbar-base-color: #c5c6fc;
	overflow-x:auto;
	overflow-y:auto;
	height:320px;
	width:100%;
}
.RowGreen
{
	background-color:#00BC00;
	behavior:url(../../htc/listitem.htc);
}
.RowRed
{
	background-color:#FD5C01;
	behavior:url(../../htc/listitem.htc);
}
.RowYellow
{
	background-color:#D7FF00;
	behavior:url(../../htc/listitem.htc);
}
.RowBlue
{
	background-color:#01A7E7;
	behavior:url(../../htc/listitem.htc);
}
.listitem
{
	background-color:white;
	behavior:url(../../htc/listitem.htc);
}
.alterlistitem
{
	background-color:#f2f2f2;
	behavior:url(../../htc/listitem.htc);
}
.normalrow
{
	background-color:#FFFFFF;
	behavior:url(../../htc/listitem.htc);
}
.hlightrow
{
	background-color:#D0D7EF;
	behavior:url(../../htc/listitem.htc);
}
.nborderlinkedt
{
	font-family:宋体;
	font-size: 9pt;
	border:solid 0px #c5c6fc;
	width:16px;
	height:18px;
	color:red;
	cursor:hand;
	background-color:Transparent;
	vertical-align:middle;
	text-align:center;
	behavior:url(../../htc/linkedt.htc);
}
.readonly{BACKGROUND-COLOR: #f2f2f2;}
.notreadonly{BACKGROUND-COLOR: #ffffff;}
.m_txt{height:18px;FONT-FAMILY: 新宋体,宋体, Arial, Sans-serif;FONT-SIZE:9pt; text-align:left;}
.m_list{height:24px;FONT-FAMILY: 新宋体,宋体, Arial, Sans-serif;FONT-SIZE:9pt; text-align:left;}
.readonly_edit
{
	font-family: "宋体";
	font-size: 9pt;
	border-style:none;
	border-bottom-style:solid;
	border-bottom-width:1px;
	border-bottom-color:#000099;
	text-align:left;
	color:#000099;
	margin:0px 0px 0px 0px;
	padding:0px 0px 0px 0px;
	vertical-align:text-bottom;
	background-color:Transparent;
}
.nodiscussed_mark
{
	background-image:url(../Image/common/graym.png);
}
.discussed_mark
{
	/*background-Image:url(../Image/common/greenm.png);*/
}
.discussed_mark_ls
{
	background-image:url(../Image/common/pinkm.png);
}
.menutree-div
{
	width:220px;
	margin:0px;
	padding:0px;
	border:0px solid #8DB2E3;
	overflow:hidden;
	background: rgb(232, 240, 254);
}
