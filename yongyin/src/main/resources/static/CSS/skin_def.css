.def-menudrager-ul
{
    list-style-type:none;
    margin: 0;
    padding: 0;
    font-size:12px;
    font-family:新宋体,宋体;
}
.def-menudrager-ul ul{
    list-style-type:none;
    margin: 0;
    padding: 0;
    font-size:12px;
    font-family:新宋体,宋体;
}
.def-menudrager-ul a 
{
    display: block;
    text-decoration: none;	
}
.def-menudrager-ul li{
    margin-top: 1px;
}
.def-menudrager-ul li a{
    background: rgb(232, 240, 254);
    color: #000000;
    padding:0.6em;
}
.def-menudrager-ul li a:hover{
    background: #B5E2F9;
}    
.def-menudrager-ul li ul li a{ 
    background-color:#DBEAF1;
    border:0px solid red;
    color: #000;
    padding-left: 20px;
}
.def-menudrager-ul li ul li a:hover{
    background: #B5E2F9;
    border-left: 5px #328DBE solid;
    padding-left: 15px;
}
.def-menudrager-ul li ul li ul
{
    padding:0px;
    margin:0px;
    list-style-type:none;
    background-color:Transparent;
    color: #000;
    border: 1px #328DBE solid;
}
.def-menudrager-ul li ul li ul li a{
    background-color:#DBEAF1;
    border:0px; 
    color: #000;
    padding-left: 30px;
    margin:0px;
}
.def-menudrager-ul li ul li ul li a:hover{
    background: #B5E2F9;
    border-left: 5px #328DBE solid;
    padding-left: 25px;
}
.def-menudrager-innertable
{
    background: Transparent;
    border:0px solid red;
    padding:0px;
    margin:0px;
    font-size:12px;
    font-family:新宋体,宋体;
    width:150px;
    text-decoration:none;
}
