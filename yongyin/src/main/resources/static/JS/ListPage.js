// JScript 文件
function refreshClick()
{
    //alert("in refreshClick");
    document.getElementById("form1:btnRefresh").click();
}
function AdjustVOClick()
{
    //alert("in AdjustVOClick");
    document.getElementById("form1:btnAdjustVO").click();
}
function SearchClick()
{
    document.getElementById("form1:btnQry").click();
}

function RefershView()
{
    document.getElementById("form1:btnRefresh").click();
}

function newClick()
{
    //ShowModalWin(CP,ExtendParamUrl(EP), iW, iH);
    //OpenWin(CP,ExtendParamUrl(EP), iW, iH);
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(EP), iW, iH, "测试配置",this.window);
    } else {
        OpenWin(ExtendParamUrl(EP), iW, iH);
    }
}
function exportClick()
{
    document.getElementById("form1:btnExport").click();
}
function Execute_DownRec(id)
{
    document.getElementById("form1:hidIDList").value=id;
    document.getElementById("form1:btnDownload").click();
}
function Execute_DelRec(id)
{
    document.getElementById("form1:hidIDList").value=id;
    document.getElementById("form1:btnDel").click();
}
function Execute_FDelRec(id)
{
    document.getElementById("form1:hidIDList").value=id;
    document.getElementById("form1:btnFDelete").click();
}
function Execute_ReportAfter(id)
{
    document.all.hidIDList.value = id;
    var btn=document.all("form1:btnRAfter");
    if(btn!=null)
    {
        btn.click();
    }
}
function fprintClick()
{
    document.getElementById("form1:btnFPrint").click();
}
function jreportClick(rptid,rpttype)
{
    var url = "/Page/comm/JReportProx.xhtml?rptid=" + rptid;
    if (rpttype == "record") {
        var id = DeleteSelect();
        if (id == "") {
            proxAlert("请先选择要操作的记录！");
            return false;
        }
        else if (id == "0") {
            proxAlert("没有可以操作的记录！");
            return false;
        }
        url = url + "&param=" + id;
        url=CP+ExtendParamUrl(url);
        OpenFullWin(url, "", "no");

        Execute_ReportAfter(id);
    }
    else
    {
        url=CP+ExtendParamUrl(url);
        OpenFullWin(url, "", "no");
    }
}
function tooltipClick(tipres)
{
    var url = "/Page/framework/TooltipProx.xhtml?tipres=" + tipres;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), iW, iH, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), iW, iH);
    }
    //OpenWin(CP,ExtendParamUrl(url), iW, iH);
}
function configClick()
{
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(cfgP), cfgW, cfgH, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(cfgP), cfgW, cfgH);
    }
    //OpenWin(CP,ExtendParamUrl(cfgP), cfgW, cfgH);
}
function modifyClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要操作的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以操作的记录！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行修改！");
        return false;
    }
    var url = ExtendParamUrl(EP)+"show=update&id=" + id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), iW, iH, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), iW, iH);
    }
    //OpenWin(CP,url, iW, iH);
}
function viewClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要查看的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以查看的记录！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行查看！");
        return false;
    }
    ViewFunc(id);
}
function ViewFunc(id)
{
    var url = ExtendParamUrl(EP)+"show=select&id=" + id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), iW, iH, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), iW, iH);
    }
    //OpenWin(CP,url, iW, iH);
}
function deleteClick()
{
    var id = DeleteSelect();
    if(id=="")
    {
        proxAlert("请先选择要删除的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以删除的记录！");
        return false;
    }
    proxConfirm("您确定要删除选中的记录吗？",function(){ Execute_DelRec(id);});
}
function fdelClick()
{
    var id = DeleteSelect();
    if(id=="")
    {
        proxAlert("请先选择要删除的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以删除的记录！");
        return false;
    }
    proxConfirm("您确定要删除选中的记录吗？",function(){ Execute_FDelRec(id);});
}
function downClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要下载的文件！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以下载的文件！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行查看！");
        return false;
    }
    Execute_DownRec(id);
}
//角色函数
function roleresClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要操作的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以操作的记录！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行操作！");
        return false;
    }
    var url=ExtendParamUrl(RSP)+"roleid="+id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), 740, 580, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), 740, 580);
    }
    //OpenWin(CP,url,740, 580);
}
function roleuserClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要操作的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以操作的记录！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行操作！");
        return false;
    }
    var url=ExtendParamUrl(RUP)+"roleid="+id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), 540, 580, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), 540, 580);
    }
   //OpenWin(CP,url, 540, 580);
}

//用户函数
function userresClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要操作的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以操作的记录！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行操作！");
        return false;
    }
    var url=ExtendParamUrl(USP)+"userid="+id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), 740, 580, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), 740, 580);
    }
    //OpenWin(CP,url, 740, 580);
}
function userroleClick()
{
    var id = ModifySelect();
    if(id=="")
    {
        proxAlert("请先选择要操作的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以操作的记录！");
        return false;
    }
    else if(id==-1)
    {
        proxAlert("你选择了太多的记录,请选择某一条记录进行操作！");
        return false;
    }
    var url=ExtendParamUrl(URP)+"userid="+id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), 540, 580, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), 540, 580);
    }
    //OpenWin(CP,url, 540, 580);
}

function gropClick()
{
    var url="/Page/framework/DeptRootEdit.xhtml?";
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), 600, 420, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), 600, 420);
    }
    //OpenWin(CP,strurl, 600, 420);
}

function RefreshNavTree(selid)
{
    window.opener.document.getElementById("dtframe").contentWindow.document.getElementById("form1:hidID").value=selid;
    window.opener.document.getElementById("dtframe").contentWindow.document.getElementById("form1:btnRefNav").click();
}

function RefreshParentNavTree(selid)
{
    window.opener.parent.parent.document.getElementById("dtframe").contentWindow.document.getElementById("form1:hidID").value=selid;
    window.opener.parent.parent.document.getElementById("dtframe").contentWindow.document.getElementById("form1:btnRefNav").click();
}

function modifyMark()
{
    document.getElementById("btnMark").click();
}

function switchClick()
{
    var id = DeleteSelect();
    if(id=="")
    {
        proxAlert("请先选择要操作的记录！");
        return false;
    }
    else if(id=="0")
    {
        proxAlert("没有可以操作的记录！");
        return false;
    }
    var url=ExtendParamUrl(SEP)+"id="+id;
    if (window.top.document.getElementById("mDialog")) {
        window.top.mDialog(CP+ExtendParamUrl(url), sW, sH, "测试配置",this.window);
    } else {
        OpenWin(CP,ExtendParamUrl(url), sW, sH);
    }
    //OpenWin(CP,url, sW, sH);
}
