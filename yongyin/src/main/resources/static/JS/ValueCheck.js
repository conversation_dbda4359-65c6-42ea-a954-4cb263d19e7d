// JScript 文件
//***--------------------------------------------数值验证模块-----------------------------------------
//验证数值(小数点前10位，小数点后6位)
function IsValidNum(vNum)
{
    var reg=/^-?[0-9]{1,10}(\.[0-9]{1,6})?$/;
	var flag = reg.test(vNum);
	return flag;
}

//判断是否为数字
function IsNumber(num)
{
	var reg=/^[0-9]*$/;
	var flag = reg.test(num); 
	return (flag);
}

//判断是否为浮点数
function IsValidFloat(num)
{
	if(IsBlank(num)||isNaN(num+1))
		return false;
	return true;
}

//判断是否为整形数
function IsValidInt(num)
{
	if(!IsValidFloat(num))
		return false;

	var reg=/\./;
	return (num.search(reg)==-1);
}

//判断是否为10位的数字
function IsTenLengthNum(num)
{
	var reg=/^\d{10}$/;
	var flag = reg.test(num); 
	return (flag);
}

//判断是否是非零的正整数："^\+?[1-9][0-9]*$"
function IsNoZeroPlus(num)
{
	var reg=/^\+?[1-9][0-9]*$/;
	var flag = reg.test(num); 
	return (flag);
}

//判断是否非零的负整数："^\-[1-9][0-9]*$"
function IsNoZeroNegative(num)
{
	var reg=/^\-[1-9][0-9]*$/;
	var flag = reg.test(num); 
	return (flag);
}

function IsAccordReg(str,reg)
{
	var flag = reg.test(str); 
	return (flag);
}

//-----------------------------------------------------------------------------------------------
//验证日期 返回 true or false
function IsValidDate(vDate)
{
     var reg = /^[0-9]{4}-(0[1-9]|[1-9]|1[0-2])-(((0[1-9])|(1[0-9])|(2[0-9])|30|31))$/;
     var flag = reg.test(vDate);
     if(flag == true)
     {
        return true;
     }
     else
     {
        return false;
     }
}
//-----------------------------------------------------------------------------------------------
//验证日期 返回 true or false
function IsValidSDate(vDate) {
    var reg = /^[0-9]{4}(0[1-9]|[1-9]|1[0-2])(((0[1-9])|(1[0-9])|(2[0-9])|30|31))$/;
    var flag = reg.test(vDate);
    if (flag == true) {
        return true;
    }
    else {
        return false;
    }
}
//-----------------------------------------------------------------------------------------------
function IsValidHMStr(hmstr)
{
	var reg=/^([0-1][0-9])|(2[0-3])(:)?[0-5][0-9]$/; 
	var flag = reg.test(hmstr); 
	return flag;
}
//-----------------------------------------------------------------------------------------------
function IsValidHMStr1(hmstr)
{
	var reg=/^([0-1][0-9])|(2[0-3]):[0-5][0-9]$/; 
	var flag = reg.test(hmstr); 
	return flag;
}
//-----------------------------------------------------------------------------------------------
function HMStrToIntStr(hmstr)
{
    if(hmstr.length>4)
    {
        var hh=hmstr.substr(0,2);
        var mm=hmstr.substr(3,2);
        return hh+""+mm;
    }else
    {
        var hh=hmstr.substr(0,2);
        var mm=hmstr.substr(2,2);
        return hh+""+mm;
    }
}
//-----------------------------------------------------------------------------------------------
function IsValidInTimeArea(num1,area)
{
   var ss=area.split("-");
   var num2=ss[0];
   var num3=ss[1];
   return IsValidInTime(num1,num2,num3)
}
//-----------------------------------------------------------------------------------------------
function IsValidInTime(num1,num2,num3)
{
   num1=HMStrToIntStr(num1);
   num2=HMStrToIntStr(num2);
   num3=HMStrToIntStr(num3);
   if(num1>num3) return false;
   if(num1<num2) return false;
   return true;
}
//-----------------------------------------------------------------------------------------------
function IsLessTime(num1,num2)
{
   num1=HMStrToIntStr(num1);
   num2=HMStrToIntStr(num2);
   if(num1>num2) return false;
   return true;
}
//***-----------------------------------字符串验证模块---------------------------------------
//检查字符串是否为空串或仅包含空格 
function IsBlank(str)
{
	var reg=/^\s*$/;
	var flag = reg.test(str); 
	return (flag);
}

//***--------------------------------日期验证模块---------------------------------------------
//取得一年中该月的天数
function GetDaysOfMonth(month,year)
{
		var array1=new Array(12);
		array1[0] = 31 // January
		if (((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0)) // February
			array1[1] =29; // Leap year
		else 
			array1[1] =28;
		array1[2]  = 31 ;// March
		array1[3]  = 30 ;// April
		array1[4]  = 31 ;// May
		array1[5]  = 30 ;// June
		array1[6]  = 31 ;// July
		array1[7]  = 31 ;// August
		array1[8]  = 30 ;// September
		array1[9]  = 31 ;// October
		array1[10] = 30 ;// November
		array1[11] = 31 ;// December
		var serial;
		serial=parseInt(month)-1;
		serial=array1[serial];
		return serial;
}

//判断两个日期是否一样，一样返回0，前面的大于后面的返回1，否则返回-1
function CompareDate(date1,date2)
{
	var y1,m1,d1;
	var y2,m2,d2;
	if(date1.length==8&&date1.indexOf("-")==-1) // yyyymmdd
	{
		y1=date1.substr(0,4);
		m1=date1.substr(4,2);
		d1=date1.substr(6,2);
	}else{ // yyyy-mm-dd
		var arr=date1.split("-");
		y1=arr[0];
		m1=arr[1];
		d1=arr[2];
	}
	if(date2.length==8&&date2.indexOf("-")==-1) // yyyymmdd
	{
		y2=date2.substr(0,4);
		m2=date2.substr(4,2);
		d2=date2.substr(6,2);
	}else{ // yyyy-mm-dd
		var arr=date2.split("-");
		y2=arr[0];
		m2=arr[1];
		d2=arr[2];
	}
	if(m1.indexOf("0")==0)
		m1=m1.substr(1,1);
	if(d1.indexOf("0")==0)
		d1=d1.substr(1,1);
	if(m2.indexOf("0")==0)
		m2=m2.substr(1,1);
	if(d2.indexOf("0")==0)
		d2=d2.substr(1,1);
	var ld1=new Date(parseInt(y1),parseInt(m1)-1,parseInt(d1));
	var ld2=new Date(parseInt(y2),parseInt(m2)-1,parseInt(d2));
	if(ld1==ld2) return 0;
	else if(ld1>ld2) return 1;
	else return -1;
}

//将日期转化成用“delimiter”分割的分割格式
function GetDateString(strDate,delimiter)
{
	var str,strYear,strMonth,strDay;
	if(strDate=="NOW")
	{
		dNow=new Date();
		strYear=dNow.getFullYear();
		strMonth=dNow.getMonth()+1;
		strDay=dNow.getDate();
	}else{
		str=strDate.split("-");
		strYear=str[0];
		strMonth=str[1];
		strDay=str[2];
	}
	return (strYear+delimiter+strMonth+delimiter+strDay);
}

//***---------------------------------其它验证模块----------------------------------------------
//验证Email地址是否有效
function IsValidEmail(str)
{
	var reg=/^\w+([\.\-]\w+)*\@\w+([\.\-]\w+)*\.\w+$/; 
	var flag = reg.test(str); 
	return (flag);
}

//***---------------------------------其它验证模块----------------------------------------------
//验证Email地址是否有效
function IsValidIP(str)
{
	var reg=/^(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])$/;
	var flag = reg.test(str); 
	return (flag);
}

//验证验证电话号码是否有效 正确格式为："XXXX-XXXXXXX"，"XXXX-XXXXXXXX"，"XXX-XXXXXXX"，
//"XXX-XXXXXXXX"，"XXXXXXX"，"XXXXXXXX"。
function IsValidPhone(str)
{
	var reg=/^(\(\d{3,4}\)|\d{3,4}-)?\d{7,8}$/;
	var flag = reg.test(str); 
	return (flag);
}

//验证InternetURL地址是否有效
//function IsValidUrl(str)
//{
//	var reg=/^http:\/\/([\w-]+\.)+[\w-]+(/[\w-./?%&=]*)?$/;
//	var flag = reg.test(str); 
//	return (flag);
//}


//验证身份证号（15位或18位数字）是否有效
//function IsValidIDCard(str)
//{
//	var reg=/^\d{15}|\d{}18$/;
//	var flag = reg.test(str); 
//	return (flag);
//}

////将strUrl中的特殊字符编码，编码方法与JScript 5.5
////的encodeUrlComponent函数相同，这是因为旧版本的IE
////不支持JScript5.5。
//function ConvertUrl(strUrl)
//{
//	var reg;
//	reg=/%/g;
//	var result=strUrl.replace(reg,"%2F");
//	reg=/\//g;
//	result=result.replace(reg,"%3F");
//	reg=/\?/g;
//	result=result.replace(reg,"%25");
//	reg=/=/g;
//	result=result.replace(reg,"%3D");
//	reg=/&/g;
//	result=result.replace(reg,"%26");
//	return result;
//}

