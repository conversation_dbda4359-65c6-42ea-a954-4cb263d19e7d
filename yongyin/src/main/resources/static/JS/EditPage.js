// JScript 文件
function ParentWinClose_WithRefParent()
{
    parent.window.close();
    parent.window.opener.refreshClick();
}

function WinClose()
{
    if (window.top.document.getElementById("mDialog"))
    {
        window.top.mDialogClose();
    }else
    {
        WinClose_NoUseDilaog();
    }
}

function WinClose_NoUseDilaog() {
    try
    {
        window.opener = window;
        var win = window.open("", "_self");
        win.close();
        //frame的时候
        top.close();
    } catch (e) {

    }
}

function WinClose_WithRefParent()
{
    //alert('WinClose_WithRefParent');
    if (window.top.document.getElementById("mDialog"))
    {
        if (window.LogicPW != undefined)
        {
            window.LogicPW.refreshClick();
        }
    }else
    {
        window.parent.opener.refreshClick();
    }
    WinClose();
}

function SaveClick()
{
    document.getElementById("form1:btnSave").click();
}

function WinClose_WithRefBase() {
    window.top.location.reload(true);
}

var LogicPW=undefined;
