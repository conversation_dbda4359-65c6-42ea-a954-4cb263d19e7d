<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TextBox with Clear Icon - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TextBox with Clear Icon</h2>
	<p>This example shows how to create an textbox with an icon to clear the input element itself.</p>
	<div style="margin:20px 0 40px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="tt" style="width:100%;" data-options="
					label: 'Email:',
					labelPosition: 'top',
					prompt: 'Input something here!',
					value: '<EMAIL>',
					icons:[{
						iconCls:'icon-search',
						handler: function(e){
							var v = $(e.data.target).textbox('getValue');
							alert('The inputed value is ' + (v ? v : 'empty'));
						}
					}]
					">
		</div>
	</div>
	<script>
		$.extend($.fn.textbox.methods, {
			addClearBtn: function(jq, iconCls){
				return jq.each(function(){
					var t = $(this);
					var opts = t.textbox('options');
					opts.icons = opts.icons || [];
					opts.icons.unshift({
						iconCls: iconCls,
						handler: function(e){
							$(e.data.target).textbox('clear').textbox('textbox').focus();
							$(this).css('visibility','hidden');
						}
					});
					t.textbox();
					if (!t.textbox('getText')){
						t.textbox('getIcon',0).css('visibility','hidden');
					}
					t.textbox('textbox').bind('keyup', function(){
						var icon = t.textbox('getIcon',0);
						if ($(this).val()){
							icon.css('visibility','visible');
						} else {
							icon.css('visibility','hidden');
						}
					});
				});
			}
		});
		
		$(function(){
			$('#tt').textbox().textbox('addClearBtn', 'icon-clear');
		});
	</script>
</body>
</html>