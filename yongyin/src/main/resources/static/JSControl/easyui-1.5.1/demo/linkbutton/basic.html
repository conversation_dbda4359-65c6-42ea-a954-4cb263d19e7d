<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Basic LinkButton - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Basic LinkButton</h2>
	<p>Buttons can be created from &lt;a&gt; or &lt;button&gt; elements.</p>
	<div style="margin:10px 0 40px 0;"></div>
	<p>Basic Buttons</p>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-add'">Add</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-remove'">Remove</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-save'">Save</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cut',disabled:true">Cut</a>
		<a href="#" class="easyui-linkbutton">Text Button</a>
	</div>
	<p>Fixed Width Buttons</p>
	<div style="padding:5px 0;">
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'" style="width:80px">Search</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-print'" style="width:80px">Print</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-reload'" style="width:80px">Reload</a>
		<a href="#" class="easyui-linkbutton" data-options="iconCls:'icon-help'" style="width:80px">Help</a>
	</div>
	
</body>
</html>