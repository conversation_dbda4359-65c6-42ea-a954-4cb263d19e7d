<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>SideMenu Style - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>SideMenu Style</h2>
	<p>Collapse the side menu to display the main icon.</p>
	<div style="margin:20px 0;">
		<a href="javascript:;" class="easyui-linkbutton" onclick="toggle()">Toggle</a>
	</div>
	<div id="sm" class="easyui-sidemenu" data-options="data:data"></div>
	<link rel="stylesheet" type="text/css" href="sidemenu_style.css">
	<script type="text/javascript">
		var data = [{
	        text: 'Forms',
	        iconCls: 'fa fa-wpforms',
	        state: 'open',
	        children: [{
	            text: 'Form Element'
	        },{
	            text: 'Wizard'
	        },{
	            text: 'File Upload'
	        }]
	    },{
	        text: 'Mail',
	        iconCls: 'fa fa-at',
	        selected: true,
	        children: [{
	            text: 'Inbox'
	        },{
	            text: 'Sent'
	        },{
	            text: 'Trash',
	            children: [{
	                text: 'Item1'
	            },{
	                text: 'Item2'
	            }]
	        }]
	    },{
	        text: 'Layout',
	        iconCls: 'fa fa-table',
	        children: [{
	            text: 'Panel'
	        },{
	            text: 'Accordion'
	        },{
	            text: 'Tabs'
	        }]
	    }];

		function toggle(){
			var opts = $('#sm').sidemenu('options');
			$('#sm').sidemenu(opts.collapsed ? 'expand' : 'collapse');
			opts = $('#sm').sidemenu('options');
			$('#sm').sidemenu('resize', {
				width: opts.collapsed ? 60 : 200
			})
		}
	</script>
</body>
</html>