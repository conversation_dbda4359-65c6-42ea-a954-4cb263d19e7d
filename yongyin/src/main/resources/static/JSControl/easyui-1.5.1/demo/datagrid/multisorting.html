<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Multiple Sorting - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Multiple Sorting</h2>
	<p>Set 'multiSort' property to true to enable multiple column sorting.</p>
	<div style="margin:20px 0;"></div>
	
	<table class="easyui-datagrid" title="Multiple Sorting" style="width:700px;height:250px"
			data-options="singleSelect:true,collapsible:true,
				url:'datagrid_data1.json',
				method:'get',
				remoteSort:false,
				multiSort:true
			">
		<thead>
			<tr>
				<th data-options="field:'itemid',width:80,sortable:true">Item ID</th>
				<th data-options="field:'productid',width:100,sortable:true">Product</th>
				<th data-options="field:'listprice',width:80,align:'right',sortable:true,sorter:numSorter">List Price</th>
				<th data-options="field:'unitcost',width:80,align:'right',sortable:true,sorter:numSorter">Unit Cost</th>
				<th data-options="field:'attr1',width:250">Attribute</th>
				<th data-options="field:'status',width:60,align:'center'">Status</th>
			</tr>
		</thead>
	</table>
	<script type="text/javascript">
		function numSorter(a,b){
			a = parseFloat(a);
			b = parseFloat(b);
			return a==b?0:(a>b?1:-1);
		}
	</script>
</body>
</html>