<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Pagination Links - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Pagination Links</h2>
	<p>The example shows how to customize numbered pagination links.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel">
		<div class="easyui-pagination" data-options="
					total:114,
					layout:['list','sep','first','prev','links','next','last','sep','refresh','info']
				"></div>
	</div>
</body>
</html>