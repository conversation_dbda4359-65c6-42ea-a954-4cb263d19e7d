<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>DateBox Events - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>DateBox Events</h2>
	<p>Click the calendar image on the right side.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-datebox" data-options="label:'Select Date:',labelPosition:'top',onSelect:onSelect" style="width:100%;">
		</div>
		<div style="margin:10px 0">
			<span>Selected Date: </span>
			<span id="result"></span>
		</div>
	</div>
	<script>
		function onSelect(date){
			$('#result').text(date)
		}
	</script>
</body>
</html>