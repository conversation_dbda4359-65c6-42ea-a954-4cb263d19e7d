<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Format NumberBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Format NumberBox</h2>
	<p>Number formatting is the ability to control how a number is displayed.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" value="1234567.89" data-options="label:'Number in the United States',labelPosition:'top',precision:2,groupSeparator:',',width:'100%'">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" value="1234567.89" data-options="label:'Number in France',labelPosition:'top',precision:2,groupSeparator:' ',decimalSeparator:',',width:'100%'">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" value="1234567.89" data-options="label:'Currency:USD',labelPosition:'top',precision:2,groupSeparator:',',decimalSeparator:'.',prefix:'$',width:'100%'">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" value="1234567.89" data-options="label:'Currency:EUR',labelPosition:'top',precision:2,groupSeparator:',',decimalSeparator:' ',prefix:'€',width:'100%'">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" value="1234567.89" data-options="label:'Currency:EUR',labelPosition:'top',precision:2,groupSeparator:' ',decimalSeparator:',',suffix:'€',width:'100%'">
		</div>
	</div>
</body>
</html>