<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Basic ComboTree - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
	<script type="text/javascript" src="../../source/jquery.textbox.js"></script>
	<script type="text/javascript" src="../../source/jquery.combo.js"></script>
	<script type="text/javascript" src="../../source/jquery.combotree.js"></script>
	<script type="text/javascript">
		$(function(){
			$('#ct').combotree({
				onLoadSuccess: function(node,data){
					console.log(this)
					// var opts = $(this).combotree('options')
					// console.log(opts)
					$('#ct').combotree('setValue', 113)
				}
			})
		})
	</script>
</head>
<body>
	<h2>Basic ComboTree</h2>
	<p>Click the right arrow button to show the tree panel.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="ct" data-options="url:'tree_data1.json',method:'get',label:'Select Node:',labelPosition:'top'" style="width:100%">
		</div>
	</div>
</body>
</html>