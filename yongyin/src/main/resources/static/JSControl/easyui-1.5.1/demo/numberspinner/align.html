<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Number Spin Alignment - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Number Spin Alignment</h2>
	<p>This example shows how to set different spin alignments on numberspinner.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-numberspinner" value="30" data-options="label:'Right:',labelPosition:'top',spinAlign:'right'" style="width:100%;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberspinner" value="30" data-options="label:'Left:',labelPosition:'top',spinAlign:'left'" style="width:100%;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberspinner" value="30" data-options="label:'Horizontal:',labelPosition:'top',spinAlign:'horizontal'" style="width:100%;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberspinner" value="30" data-options="label:'Vertical:',labelPosition:'top',spinAlign:'vertical'" style="width:100%;max-width:60px;text-align:center">
		</div>
	</div>
</body>
</html>