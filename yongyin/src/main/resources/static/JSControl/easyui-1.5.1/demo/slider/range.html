<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Range Slider - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Range Slider</h2>
	<p>This sample shows how to define a range slider.</p>
	<div style="margin:20px 0 50px 0;"></div>
	<input class="easyui-slider" style="width:300px" data-options="
				showTip: true,
				range: true,
				value: [20,60],
				rule: [0,'|',25,'|',50,'|',75,'|',100]
			">
</body>
</html>