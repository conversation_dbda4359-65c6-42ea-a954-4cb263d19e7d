<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>No collapsible button in Layout - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>No collapsible button in Layout</h2>
	<p>The layout region panel has no collapsible button.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-layout" style="width:700px;height:350px;">
		<div data-options="region:'north'" style="height:50px"></div>
		<div data-options="region:'south',split:true" style="height:50px;"></div>
		<div data-options="region:'east',split:true,title:'East',collapsible:false" style="width:250px;">
			<table id="tt" class="easyui-propertygrid" data-options="
						url: 'propertygrid_data1.json',
						method: 'get',
						showGroup: true,
						fit: true,
						border: false
					">
			</table>
		</div>
		<div data-options="region:'center',title:'Main Title',iconCls:'icon-ok',href:'_content.html'" style="padding:10px">
		</div>
	</div>

</body>
</html>