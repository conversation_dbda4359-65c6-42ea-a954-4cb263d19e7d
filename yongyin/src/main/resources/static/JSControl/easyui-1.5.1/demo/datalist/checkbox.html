<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Checkbox in DataList - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../easyui-1.5.1/themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Checkbox in DataList</h2>
	<p>Each item in the DataList has a checkbox.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-datalist" title="Checkbox in DataList" style="width:400px;height:250px" data-options="
			url: 'datalist_data1.json',
			method: 'get',
			checkbox: true,
			selectOnCheck: false,
			onBeforeSelect: function(){return false;}
			">
	</div>
</body>
</html>