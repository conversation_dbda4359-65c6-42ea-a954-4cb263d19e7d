<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>datetimefield</title>
        <link href="../ExtResources/css/ext-all.css" rel="stylesheet" type="text/css" />

        <script src="../JS/Ext/ext-base.js" type="text/javascript"></script>
        <script src="../JS/Ext/ext-all.js" type="text/javascript"></script>
        <script src="../JS/Ext/ext-lang-zh_CN.js" type="text/javascript"></script>
		<link rel="stylesheet" type="text/css" href="css/Spinner.css"/>
		<script type="text/javascript" src="Spinner.js"></script>
		<script type="text/javascript" src="SpinnerField.js"></script>
        <script type="text/javascript" src="DateTimeField.js"></script>
		<script type="text/javascript">
            Ext.onReady(function(){
				new Ext.form.FormPanel({
					renderTo:'my_form',
					frame:true,
					items:[{
						xtype:'datetimefield',
						fieldLabel:'选取时间'
					},{
						xtype:'datetimefield',
						fieldLabel:'格式化时间',
						format:'Y/m/d H:i'//可以自己配置需要的格式
					}]
				});
            });
        </script>
    </head>
    <body>
		<div id="my_form"></div>
    </body>
</html>
