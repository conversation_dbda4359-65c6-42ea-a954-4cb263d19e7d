<!-- vim: ts=2:sw=2:nu:fdc=2:spell

Ext.ux.form.LovCombo Example Page

<AUTHOR>
@copyright (c) 2008, by Ing. <PERSON><PERSON>
@date      16. April 2008
@version   $Id: lovcombo.html 78 2008-06-06 09:22:10Z jozo $

@license lovcombo.html is licensed under the terms of the Open Source
LGPL 3.0 license. Commercial use is permitted to the extent that the 
code/component(s) do NOT become part of another Open Source or Commercially
licensed development library or toolkit without explicit permission.
 
License details: http://www.gnu.org/licenses/lgpl.html
-->

<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<link rel="stylesheet" type="text/css" href="../resources/css/ext-all.css">
	<link rel="stylesheet" type="text/css" href="./css/Ext.ux.form.LovCombo.css">
	<link rel="stylesheet" type="text/css" href="./css/empty.css" id="theme">
	<link rel="stylesheet" type="text/css" href="./css/webpage.css">
	<link rel="stylesheet" type="text/css" href="./css/lovcombo.css">
	<link rel="shortcut icon" href="../img/extjs.ico">
	<script type="text/javascript" src="../adapter/ext/ext-base.js"></script>
	<script type="text/javascript" src="../ext-all-debug.js"></script>
	<script type="text/javascript" src="./js/WebPage.js"></script>
	<script type="text/javascript" src="./js/Ext.ux.form.LovCombo.js"></script>
	<script type="text/javascript" src="./js/Ext.ux.ThemeCombo.js"></script>
	
	<script type="text/javascript" src="lovcombo.js"></script>
	<title id="page-title">Ext.ux.form.LovCombo Extension by Saki</title>
</head>
<body>

<div id="center-content" class="x-hidden">
	<div id="cts">
		<div id="lovcomboct"></div>
		<br>
		Value (updated on focus):
		<div id="textct"></div>
	</div>

	<div id="change-log">
		<h3>Development Change Log:</h3>
		<ul>
			<li><b>April 17, 2008</b> - hiddenField now gets updated</li>
		</ul>
		<ul>
			<li><b>April 16, 2008</b> - Initial release</li>
		</ul>
	</div>

</div>

<div id="west-content" class="x-hidden">
	<div id="description">
		<h3>Description</h3>
		<p>
		Simple LOV (List Of Values) combo. Individual values are selected in dropdown list and 
		overall value is delimited list of values.
		</p>
		<div id="adsense-float" class="adsense x-hidden">
			<script type="text/javascript"><!--
			google_ad_client = "pub-2768521146228687"
			/* 125x125 - in text */
			google_ad_slot = "2156000855";
			google_ad_width = 125;
			google_ad_height = 125;
			//-->
			</script>
			<script type="text/javascript"
			src="http://pagead2.googlesyndication.com/pagead/show_ads.js">
			</script>
		</div>

		<p>
		Delimeter is configurable.
		</p>

		<p>
		LovCombo setValue method accepts the delimited list of values and sets all individual values accordingly.
		</p>

	</div>

	<br><br><br>
	<a href="http://extjs.com/forum/showthread.php?t=32692">LovCombo Forum Thread</a><br><br>

		<b>View Sources:</b><br>
		<a href="source.php?file=js/Ext.ux.form.LovCombo.js" target="_blank">Ext.ux.form.LovCombo.js</a><br>
		<a href="source.php?file=css/Ext.ux.form.LovCombo.css" target="_blank">Ext.ux.form.LovCombo.css</a><br>
		<a href="source.php?file=lovcombo.html" target="_blank">lovcombo.html</a><br>
		<a href="source.php?file=lovcombo.js" target="_blank">lovcombo.js</a><br>
		<a href="source.php?file=css/lovcombo.css" target="_blank">lovcombo.css</a><br>
		<br><a href="http://famfamfam.com/lab/icons/silk" target="_blank">Silk Icons</a><br>

		<div id="paypal">
		<!-- PayPal Donate Button -->
		<form action="https://www.paypal.com/cgi-bin/webscr" method="post">
		<input type="hidden" name="cmd" value="_s-xclick">
		<input type="image" src="https://www.paypal.com/en_US/i/btn/x-click-butcc-donate.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
		<img alt="" border="0" src="https://www.paypal.com/en_US/i/scr/pixel.gif" width="1" height="1">
		<input type="hidden" name="encrypted" value="-----BEGIN PKCS7-----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-----END PKCS7-----
		">
		</form>
		<!-- PayPal Donate Button End-->
	</div>

	<div id="downloads">
		<p><b>Downloads (stable 1.0):</b></p>
		<a href="http://extjs.eu/phpmv2/phpmyvisites.php?url=http%3A//lovcombo.extjs.eu/lovcombo-1.0.tar.bz2&id=1&pagename=FILE: download/lovcombo/lovcombo-1.0.tar.bz2">lovcombo-1.0.tar.bz2</a><br>
		<a href="http://extjs.eu/phpmv2/phpmyvisites.php?url=http%3A//lovcombo.extjs.eu/lovcombo-1.0.tar.gz&id=1&pagename=FILE: download/lovcombo/lovcombo-1.0.tar.gz">lovcombo-1.0.tar.gz</a><br>
		<a href="http://extjs.eu/phpmv2/phpmyvisites.php?url=http%3A//lovcombo.extjs.eu/lovcombo-1.0.zip&id=1&pagename=FILE: download/lovcombo/lovcombo-1.0.zip">lovcombo-1.0.zip</a><br>
		<br>
		<p><b>Downloads (development):</b></p>
		<a href="http://extjs.eu/phpmv2/phpmyvisites.php?url=http%3A//lovcombo.extjs.eu/lovcombo.tar.bz2&id=1&pagename=FILE: download/lovcombo/lovcombo.tar.bz2">lovcombo.tar.bz2</a><br>
		<a href="http://extjs.eu/phpmv2/phpmyvisites.php?url=http%3A//lovcombo.extjs.eu/lovcombo.tar.gz&id=1&pagename=FILE: download/lovcombo/lovcombo.tar.gz">lovcombo.tar.gz</a><br>
		<a href="http://extjs.eu/phpmv2/phpmyvisites.php?url=http%3A//lovcombo.extjs.eu/lovcombo.zip&id=1&pagename=FILE: download/lovcombo/lovcombo.zip">lovcombo.zip</a><br>
	</div>
	<br>
	<p>
		<b><a href="http://extjs.eu" target="_blank">Other Extensions and Plugins</a></b>
	</p>

	<!-- digg -->
	<div id="digg">
		<script>
		digg_url = 'http://lovcombo.extjs.eu';
		</script>
		<script src="http://digg.com/api/diggthis.js"></script>
	</div>
	<!-- digg -->

	<a href="http://www.stumbleupon.com/submit?url=<?=urlencode('http://'.$_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI']);?>"><img border=0 src="../img/stumble7.gif" alt="StumbleUpon Toolbar"></a>	

</div>

<div id="adrow-content" class="adsense x-hidden">
	<div id="adsense-top">
		<script type="text/javascript"><!--
		google_ad_client = "pub-2768521146228687";
		/* recordform top row wide */
		google_ad_slot = "3909046423";
		google_ad_width = 728;
		google_ad_height = 15;
		//-->
		</script>
		<script type="text/javascript"
		src="http://pagead2.googlesyndication.com/pagead/show_ads.js">
		</script>
	</div>
</div>

<?if("lovcombo.extjs.eu"===$_SERVER["SERVER_NAME"])include("stats.php");?>
</body>
</html>
<!-- eof -->

