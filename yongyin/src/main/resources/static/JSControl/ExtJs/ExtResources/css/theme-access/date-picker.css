/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-date-picker {
    border-color: #737b8c;
    background-color:#21252e;
}

.x-date-middle,.x-date-left,.x-date-right {
	background-image: url(../images/access/shared/hd-sprite.gif);
	color:#fff;
	font:bold 14px "sans serif", tahoma, verdana, helvetica;
}

.x-date-middle .x-btn .x-btn-text {
    color:#fff;
}

.x-date-middle .x-btn-mc em.x-btn-arrow {
    background-image:url(../images/access/toolbar/btn-arrow-light.gif);
}

.x-date-right a {
    background-image: url(../images/access/shared/right-btn.gif);
}

.x-date-left a{
	background-image: url(../images/access/shared/left-btn.gif);
}

.x-date-inner th {
    background-color:#363d4a;
    background-image:url(../images/access/toolbar/bg.gif);
	border-bottom-color:#535b5c;
    font:normal 13px arial, helvetica,tahoma,sans-serif;
	color:#fff;
}

.x-date-inner td {
    border-color:#112;
}

.x-date-inner a {
    font:normal 14px arial, helvetica,tahoma,sans-serif;
    color:#fff;
    padding:2px 7px 1px 3px; /* Structure to account for larger, bolder fonts in Access theme. */
}

.x-date-inner .x-date-active{
	color:#000;
}

.x-date-inner .x-date-selected a{
    background-color:#e5872c;
	background-image:none;
	border-color:#864900;
    padding:1px 6px 1px 2px; /* Structure to account for larger, bolder fonts in Access theme. */
}

.x-date-inner .x-date-today a{
	border-color:#99a;
}

.x-date-inner .x-date-selected span{
    font-weight:bold;
}

.x-date-inner .x-date-prevday a,.x-date-inner .x-date-nextday a {
	color:#aaa;
}

.x-date-bottom {
    border-top-color:#737b8c;
    background-color:#464d5a;
    background-image:url(../images/access/shared/glass-bg.gif);
}

.x-date-inner a:hover, .x-date-inner .x-date-disabled a:hover{
    color:#fff;
    background-color:#7e5530;
}

.x-date-inner .x-date-disabled a {
	background-color:#eee;
	color:#bbb;
}

.x-date-mmenu{
    background-color:#eee !important;
}

.x-date-mmenu .x-menu-item {
	font-size:13px;
	color:#000;
}

.x-date-mp {
	background-color:#21252e;
}

.x-date-mp td {
	font:normal 14px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns button {
	background-color:#083772;
	color:#fff;
	border-color: #3366cc #000055 #000055 #3366cc;
	font:normal 14px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns {
    background-color: #dfecfb;
	background-image: url(../images/access/shared/glass-bg.gif);
}

.x-date-mp-btns td {
	border-top-color: #c5d2df;
}

td.x-date-mp-month a,td.x-date-mp-year a {
	color:#fff;
}

td.x-date-mp-month a:hover,td.x-date-mp-year a:hover {
	color:fff;
	background-color: #7e5530;
}

td.x-date-mp-sel a {
    background-color: #e5872c;
	background-image: none;
	border-color:#864900;
}

.x-date-mp-ybtn a {
    background-image:url(../images/access/panel/tool-sprites.gif);
}

td.x-date-mp-sep {
   border-right-color:#c5d2df;
}
