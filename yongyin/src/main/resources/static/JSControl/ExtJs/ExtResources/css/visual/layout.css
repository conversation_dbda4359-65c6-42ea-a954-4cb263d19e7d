/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-border-layout-ct {
    background-color:#dfe8f6;
}

.x-accordion-hd {
	color:#222;
    font-weight:normal;
    background-image: url(../images/default/panel/light-hd.gif);
}

.x-layout-collapsed{
    background-color:#d2e0f2;
	border-color:#98c0f4;
}

.x-layout-collapsed-over{
    background-color:#d9e8fb;
}

.x-layout-split-west .x-layout-mini {
    background-image:url(../images/default/layout/mini-left.gif);
}
.x-layout-split-east .x-layout-mini {
    background-image:url(../images/default/layout/mini-right.gif);
}
.x-layout-split-north .x-layout-mini {
    background-image:url(../images/default/layout/mini-top.gif);
}
.x-layout-split-south .x-layout-mini {
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-west .x-layout-mini {
    background-image:url(../images/default/layout/mini-right.gif);
}

.x-layout-cmini-east .x-layout-mini {
    background-image:url(../images/default/layout/mini-left.gif);
}

.x-layout-cmini-north .x-layout-mini {
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-south .x-layout-mini {
    background-image:url(../images/default/layout/mini-top.gif);
}