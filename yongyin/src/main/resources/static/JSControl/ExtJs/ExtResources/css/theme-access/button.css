/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-btn {
	font:normal 14px tahoma, verdana, helvetica;
}

.x-btn button {
    font:normal 14px arial,tahoma,verdana,helvetica;
    color:#fffffa;
    padding-left:6px !important;
    padding-right:6px !important;
}

.x-btn-over .x-btn button{
    color:#fff;
}

.x-btn-noicon .x-btn-small .x-btn-text, .x-btn-text-icon .x-btn-icon-small-left .x-btn-text,
.x-btn-icon .x-btn-small .x-btn-text, .x-btn-text-icon .x-btn-icon-small-right .x-btn-text {
    height:18px;
}

.x-btn-icon .x-btn-small .x-btn-text {
    width:18px;
}

.x-btn-text-icon .x-btn-icon-small-left .x-btn-text {
    padding-left:21px !important;
}

.x-btn-text-icon .x-btn-icon-small-right .x-btn-text {
    padding-right:21px !important;
}

.x-btn-text-icon .x-btn-icon-medium-left .x-btn-text {
    padding-left:29px !important;
}

.x-btn-text-icon .x-btn-icon-medium-right .x-btn-text {
    padding-right:29px !important;
}

.x-btn-text-icon .x-btn-icon-large-left .x-btn-text {
    padding-left:37px !important;
}

.x-btn-text-icon .x-btn-icon-large-right .x-btn-text {
    padding-right:37px !important;
}

.x-btn em {
    font-style:normal;
    font-weight:normal;
}

.x-btn-tl, .x-btn-tr, .x-btn-tc, .x-btn-ml, .x-btn-mr, .x-btn-mc, .x-btn-bl, .x-btn-br, .x-btn-bc{
	background-image:url(../images/access/button/btn.gif);
}

.x-btn-click .x-btn-text, .x-btn-menu-active .x-btn-text, .x-btn-pressed .x-btn-text{
    color:#fff;
}

.x-btn-disabled *{
	color:#eee !important;
}

.x-btn-mc em.x-btn-arrow {
    background-image:url(../images/access/button/arrow.gif);
    padding-right:13px;
}

.x-btn-mc em.x-btn-split {
    background-image:url(../images/access/button/s-arrow.gif);
    padding-right:20px;
}

.x-btn-over .x-btn-mc em.x-btn-split, .x-btn-click .x-btn-mc em.x-btn-split, .x-btn-menu-active .x-btn-mc em.x-btn-split, .x-btn-pressed .x-btn-mc em.x-btn-split {
    background-image:url(../images/access/button/s-arrow-o.gif);
}

.x-btn-mc em.x-btn-arrow-bottom {
    background-image:url(../images/access/button/s-arrow-b-noline.gif);
}

.x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/access/button/s-arrow-b.gif);
}

.x-btn-over .x-btn-mc em.x-btn-split-bottom, .x-btn-click .x-btn-mc em.x-btn-split-bottom, .x-btn-menu-active .x-btn-mc em.x-btn-split-bottom, .x-btn-pressed .x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/access/button/s-arrow-bo.gif);
}

.x-btn-group-header {
    color: #d2d2d2;
}

.x-btn-group-tc {
	background-image: url(../images/access/button/group-tb.gif);
}

.x-btn-group-tl {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-tr {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-bc {
	background-image: url(../images/access/button/group-tb.gif);
}

.x-btn-group-bl {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-br {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-ml {
	background-image: url(../images/access/button/group-lr.gif);
}

.x-btn-group-mr {
	background-image: url(../images/access/button/group-lr.gif);
}

.x-btn-group-notitle .x-btn-group-tc {
	background-image: url(../images/access/button/group-tb.gif);
}
