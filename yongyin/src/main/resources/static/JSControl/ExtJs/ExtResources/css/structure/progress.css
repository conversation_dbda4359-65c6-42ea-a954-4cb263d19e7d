/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-progress-wrap {
    border:1px solid;
    overflow:hidden;
}

.x-progress-inner {
    height:18px;
    background:repeat-x;
    position:relative;
}

.x-progress-bar {
    height:18px;
    float:left;
    width:0;
    background: repeat-x left center;
    border-top:1px solid;
    border-bottom:1px solid;
    border-right:1px solid;
}

.x-progress-text {
    padding:1px 5px;
    overflow:hidden;
    position:absolute;
    left:0;
    text-align:center;
}

.x-progress-text-back {
    line-height:16px;
}

.ext-ie .x-progress-text-back {
    line-height:15px;
}

.ext-strict .ext-ie7 .x-progress-text-back{
    width: 100%;
}
