/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-list-header{
	background: repeat-x 0 bottom;
	cursor:default;
    zoom:1;
    height:22px;
}

.x-list-header-inner div {
    display:block;
    float:left;
    overflow:hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
    white-space: nowrap;
}

.x-list-header-inner div em {
    display:block;
    border-left:1px solid;
    padding:4px 4px;
    overflow:hidden;
    -moz-user-select: none;
    -khtml-user-select: none;
    line-height:14px;
}

.x-list-body {
    overflow:auto;
    overflow-x:hidden;
    overflow-y:auto;
    zoom:1;
    float: left;
    width: 100%;
}

.x-list-body dl {
    zoom:1;
}

.x-list-body dt {
    display:block;
    float:left;
    overflow:hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
    white-space: nowrap;
    cursor:pointer;
    zoom:1;
}

.x-list-body dt em {
    display:block;
    padding:3px 4px;
    overflow:hidden;
    -moz-user-select: none;
    -khtml-user-select: none;
}

.x-list-resizer {
    border-left:1px solid;
    border-right:1px solid;
    position:absolute;
    left:0;
    top:0;
}

.x-list-header-inner em.sort-asc {
    background: transparent no-repeat center 0;
    border-style:solid;
    border-width: 0 1px 1px;
    padding-bottom:3px;
}

.x-list-header-inner em.sort-desc {
    background: transparent no-repeat center -23px;
    border-style:solid;
    border-width: 0 1px 1px;
    padding-bottom:3px;
}

