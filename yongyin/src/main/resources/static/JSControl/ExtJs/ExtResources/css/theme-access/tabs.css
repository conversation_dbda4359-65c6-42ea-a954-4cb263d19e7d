/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-tab-panel-header, .x-tab-panel-footer {
	background-color:#e18325;
	border-color:#8db2e3;
    overflow:hidden;
    zoom:1;
}

.x-tab-panel-header, .x-tab-panel-footer {
	border-color:#222;
}

ul.x-tab-strip-top{
    background-color:#343843;
	background-image: url(../images/access/tabs/tab-strip-bg.gif);
	border-bottom-color:#343d4e;
}

ul.x-tab-strip-bottom{
    background-color:#343843;
	background-image: url(../images/access/tabs/tab-strip-btm-bg.gif);
	border-top-color:#343843;
}

.x-tab-panel-header-plain .x-tab-strip-spacer,
.x-tab-panel-footer-plain .x-tab-strip-spacer {
    border-color:#222;
    background-color:#e18325;
}

.x-tab-strip span.x-tab-strip-text {
	font:normal 14px tahoma,arial,helvetica;
	color:#fff;
}

.x-tab-strip-over span.x-tab-strip-text {
	color:#fff;
}

.x-tab-strip-active span.x-tab-strip-text {
	color:#fff;
    font-weight:bold;
}

.x-tab-strip-disabled .x-tabs-text {
	color:#aaaaaa;
}

.x-tab-strip-top .x-tab-right, .x-tab-strip-top .x-tab-left, .x-tab-strip-top .x-tab-strip-inner{
	background-image: url(../images/access/tabs/tabs-sprite.gif);
}

.x-tab-strip-bottom .x-tab-right {
	background-image: url(../images/access/tabs/tab-btm-inactive-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-left {
	background-image: url(../images/access/tabs/tab-btm-inactive-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background-image: url(../images/access/tabs/tab-btm-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background-image: url(../images/access/tabs/tab-btm-left-bg.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
	background-image:url(../images/access/tabs/tab-close.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close:hover{
	background-image:url(../images/access/tabs/tab-close.gif);
}

.x-tab-panel-body {
    border-color:#18181a;
    background-color:#fff;
}

.x-tab-panel-body-top {
    border-top: 0 none;
}

.x-tab-panel-body-bottom {
    border-bottom: 0 none;
}

.x-tab-scroller-left {
    background-image:url(../images/access/tabs/scroll-left.gif);
    border-bottom-color:#8db2e3;
}

.x-tab-scroller-left-over {
    background-position: 0 0;
}

.x-tab-scroller-left-disabled {
    background-position: -18px 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}

.x-tab-scroller-right {
    background-image:url(../images/access/tabs/scroll-right.gif);
    border-bottom-color:#8db2e3;
}

.x-tab-panel-bbar .x-toolbar, .x-tab-panel-tbar .x-toolbar {
    border-color:#99bbe8;
}
