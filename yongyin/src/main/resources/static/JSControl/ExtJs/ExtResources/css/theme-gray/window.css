/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-window-proxy {
    background-color:#fcfcfc;
    border-color:#d0d0d0;
}

.x-window-tl .x-window-header {
    color:#555;
	font:bold 11px tahoma,arial,verdana,sans-serif;
}

.x-window-tc {
	background-image: url(../images/gray/window/top-bottom.png);
}

.x-window-tl {
	background-image: url(../images/gray/window/left-corners.png);
}

.x-window-tr {
	background-image: url(../images/gray/window/right-corners.png);
}

.x-window-bc {
	background-image: url(../images/gray/window/top-bottom.png);
}

.x-window-bl {
	background-image: url(../images/gray/window/left-corners.png);
}

.x-window-br {
	background-image: url(../images/gray/window/right-corners.png);
}

.x-window-mc {
    border-color:#d0d0d0;
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    background-color:#e8e8e8;
}

.x-window-ml {
	background-image: url(../images/gray/window/left-right.png);
}

.x-window-mr {
	background-image: url(../images/gray/window/left-right.png);
}

.x-window-maximized .x-window-tc {
    background-color:#fff;
}

.x-window-bbar .x-toolbar {
    border-top-color:#d0d0d0;
}

.x-panel-ghost .x-window-tl {
    border-bottom-color:#d0d0d0;
}

.x-panel-collapsed .x-window-tl {
    border-bottom-color:#d0d0d0;
}

.x-dlg-mask{
   background-color:#ccc;
}

.x-window-plain .x-window-mc {
    background-color: #E8E8E8;
    border-color: #D0D0D0 #EEEEEE #EEEEEE #D0D0D0;
}

.x-window-plain .x-window-body {
    border-color: #EEEEEE #D0D0D0 #D0D0D0 #EEEEEE;
}

body.x-body-masked .x-window-plain .x-window-mc {
    background-color: #E4E4E4;
}
