/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-toolbar{
    border-style:solid;
    border-width:0 0 1px 0;
    display: block;
	padding:2px;
    background:repeat-x top left;
    position:relative;
    left:0;
    top:0;
    zoom:1;
    overflow:hidden;
}

.x-toolbar-left {
    width: 100%;
}

.x-toolbar .x-item-disabled .x-btn-icon {
    opacity: .35;
    -moz-opacity: .35;
    filter: alpha(opacity=35);
}

.x-toolbar td {
	vertical-align:middle;
}

.x-toolbar td,.x-toolbar span,.x-toolbar input,.x-toolbar div,.x-toolbar select,.x-toolbar label{
	white-space: nowrap;
}

.x-toolbar .x-item-disabled {
	cursor:default;
	opacity:.6;
	-moz-opacity:.6;
	filter:alpha(opacity=60);
}

.x-toolbar .x-item-disabled * {
	cursor:default;
}

.x-toolbar .x-toolbar-cell {
    vertical-align:middle;
}

.x-toolbar .x-btn-tl, .x-toolbar .x-btn-tr, .x-toolbar .x-btn-tc, .x-toolbar .x-btn-ml, .x-toolbar .x-btn-mr,
.x-toolbar .x-btn-mc, .x-toolbar .x-btn-bl, .x-toolbar .x-btn-br, .x-toolbar .x-btn-bc
{
	background-position: 500px 500px;
}

/* These rules are duplicated from button.Css to give priority of x-toolbar rules above */
.x-toolbar .x-btn-over .x-btn-tl{
	background-position: -6px 0;
}

.x-toolbar .x-btn-over .x-btn-tr{
	background-position: -9px 0;
}

.x-toolbar .x-btn-over .x-btn-tc{
	background-position: 0 -9px;
}

.x-toolbar .x-btn-over .x-btn-ml{
	background-position: -6px -24px;
}

.x-toolbar .x-btn-over .x-btn-mr{
	background-position: -9px -24px;
}

.x-toolbar .x-btn-over .x-btn-mc{
	background-position: 0 -2168px;
}

.x-toolbar .x-btn-over .x-btn-bl{
	background-position: -6px -3px;
}

.x-toolbar .x-btn-over .x-btn-br{
	background-position: -9px -3px;
}

.x-toolbar .x-btn-over .x-btn-bc{
	background-position: 0 -18px;
}

.x-toolbar .x-btn-click .x-btn-tl, .x-toolbar .x-btn-menu-active .x-btn-tl, .x-toolbar .x-btn-pressed .x-btn-tl{
	background-position: -12px 0;
}

.x-toolbar .x-btn-click .x-btn-tr, .x-toolbar .x-btn-menu-active .x-btn-tr, .x-toolbar .x-btn-pressed .x-btn-tr{
	background-position: -15px 0;
}

.x-toolbar .x-btn-click .x-btn-tc, .x-toolbar .x-btn-menu-active .x-btn-tc, .x-toolbar .x-btn-pressed .x-btn-tc{
	background-position: 0 -12px;
}

.x-toolbar .x-btn-click .x-btn-ml, .x-toolbar .x-btn-menu-active .x-btn-ml, .x-toolbar .x-btn-pressed .x-btn-ml{
	background-position: -12px -24px;
}

.x-toolbar .x-btn-click .x-btn-mr, .x-toolbar .x-btn-menu-active .x-btn-mr, .x-toolbar .x-btn-pressed .x-btn-mr{
	background-position: -15px -24px;
}

.x-toolbar .x-btn-click .x-btn-mc, .x-toolbar .x-btn-menu-active .x-btn-mc, .x-toolbar .x-btn-pressed .x-btn-mc{
	background-position: 0 -3240px;
}

.x-toolbar .x-btn-click .x-btn-bl, .x-toolbar .x-btn-menu-active .x-btn-bl, .x-toolbar .x-btn-pressed .x-btn-bl{
	background-position: -12px -3px;
}

.x-toolbar .x-btn-click .x-btn-br, .x-toolbar .x-btn-menu-active .x-btn-br, .x-toolbar .x-btn-pressed .x-btn-br{
	background-position: -15px -3px;
}

.x-toolbar .x-btn-click .x-btn-bc, .x-toolbar .x-btn-menu-active .x-btn-bc, .x-toolbar .x-btn-pressed .x-btn-bc{
	background-position: 0 -21px;
}

.x-toolbar div.xtb-text{
    padding:2px 2px 0;
    line-height:16px;
    display:block;
}

.x-toolbar .xtb-sep {
	background-position: center;
	background-repeat: no-repeat;
	display: block;
	font-size: 1px;
	height: 16px;
	width:4px;
	overflow: hidden;
	cursor:default;
	margin: 0 2px 0;
	border:0;
}

.x-toolbar .xtb-spacer {
    width:2px;
}

/* Paging Toolbar */
.x-tbar-page-number{
	width:30px;
	height:14px;
}

.ext-ie .x-tbar-page-number{
    margin-top: 2px;
}

.x-paging-info {
    position:absolute;
    top:5px;
    right: 8px;
}

/* floating */
.x-toolbar-ct {
    width:100%;
}

.x-toolbar-right td {
    text-align: center;
}

.x-panel-tbar, .x-panel-bbar, .x-window-tbar, .x-window-bbar, .x-tab-panel-tbar, .x-tab-panel-bbar, .x-plain-tbar, .x-plain-bbar {
    overflow:hidden;
    zoom:1;
}

.x-toolbar-more .x-btn-small .x-btn-text{
	height: 16px;
	width: 12px;
}

.x-toolbar-more em.x-btn-arrow {
    display:inline;
    background:transparent;
	padding-right:0;
}

.x-toolbar-more .x-btn-mc em.x-btn-arrow {
    background-image: none;
}

div.x-toolbar-no-items {
    color:gray !important;
    padding:5px 10px !important;
}

/* fix ie toolbar form items */
.ext-border-box .x-toolbar-cell .x-form-text {
    margin-bottom:-1px !important;
}

.ext-border-box .x-toolbar-cell .x-form-field-wrap .x-form-text {
    margin:0 !important;
}

.ext-ie .x-toolbar-cell .x-form-field-wrap {
    height:21px;
}

.ext-ie .x-toolbar-cell .x-form-text {
    position:relative;
    top:-1px;
}

.ext-strict .ext-ie8 .x-toolbar-cell .x-form-field-trigger-wrap .x-form-text, .ext-strict .ext-ie .x-toolbar-cell .x-form-text {
    top: 0px;
}

.x-toolbar-right td .x-form-field-trigger-wrap{
    text-align: left;
}

.x-toolbar-cell .x-form-checkbox, .x-toolbar-cell .x-form-radio{
    margin-top: 5px;
}

.x-toolbar-cell .x-form-cb-label{
    vertical-align: bottom;
    top: 1px;
}

.ext-ie .x-toolbar-cell .x-form-checkbox, .ext-ie .x-toolbar-cell .x-form-radio{
    margin-top: 4px;
}

.ext-ie .x-toolbar-cell .x-form-cb-label{
    top: 0;
}
