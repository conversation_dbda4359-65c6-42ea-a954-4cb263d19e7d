/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-toolbar{
	border-color:#18181a;
    background-color:#393d4e;
    background-image:url(../images/access/toolbar/bg.gif);
}

.x-toolbar td,.x-toolbar span,.x-toolbar input,.x-toolbar div,.x-toolbar select,.x-toolbar label{
    font:normal 14px arial,tahoma, helvetica, sans-serif;
}

.x-toolbar .x-item-disabled {
	color:gray;
}

.x-toolbar .x-item-disabled * {
	color:gray;
}

.x-toolbar .x-btn-mc em.x-btn-split {
    background-image:url(../images/access/button/s-arrow-noline.gif);
}

.x-toolbar .x-btn-over .x-btn-mc em.x-btn-split, .x-toolbar .x-btn-click .x-btn-mc em.x-btn-split, 
.x-toolbar .x-btn-menu-active .x-btn-mc em.x-btn-split, .x-toolbar .x-btn-pressed .x-btn-mc em.x-btn-split 
{
    background-image:url(../images/access/button/s-arrow-o.gif);
}

.x-toolbar .x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/access/button/s-arrow-b-noline.gif);
}

.x-toolbar .x-btn-over .x-btn-mc em.x-btn-split-bottom, .x-toolbar .x-btn-click .x-btn-mc em.x-btn-split-bottom, 
.x-toolbar .x-btn-menu-active .x-btn-mc em.x-btn-split-bottom, .x-toolbar .x-btn-pressed .x-btn-mc em.x-btn-split-bottom 
{
    background-image:url(../images/access/button/s-arrow-bo.gif);
}

.x-toolbar .xtb-sep {
	background-image: url(../images/access/grid/grid-blue-split.gif);
}

.x-toolbar .x-btn {
    padding-left:3px;
    padding-right:3px;
}

.x-toolbar .x-btn-mc em.x-btn-arrow {
    padding-right:10px;
}

.x-toolbar .x-btn-text-icon .x-btn-icon-small-left .x-btn-text {
    padding-left:18px !important;
}

.x-toolbar .x-btn-mc em.x-btn-split {
    padding-right:14px;
}

.x-tbar-page-first{
	background-image: url(../images/access/grid/page-first.gif) !important;
}

.x-tbar-loading{
	background-image: url(../images/access/grid/refresh.gif) !important;
}

.x-tbar-page-last{
	background-image: url(../images/access/grid/page-last.gif) !important;
}

.x-tbar-page-next{
	background-image: url(../images/access/grid/page-next.gif) !important;
}

.x-tbar-page-prev{
	background-image: url(../images/access/grid/page-prev.gif) !important;
}

.x-item-disabled .x-tbar-loading{
	background-image: url(../images/access/grid/loading.gif) !important;
}

.x-item-disabled .x-tbar-page-first{
	background-image: url(../images/access/grid/page-first-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-last{
	background-image: url(../images/access/grid/page-last-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-next{
	background-image: url(../images/access/grid/page-next-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-prev{
	background-image: url(../images/access/grid/page-prev-disabled.gif) !important;
}

.x-paging-info {
    color:#444;
}

.x-toolbar-more-icon {
    background-image: url(../images/access/toolbar/more.gif) !important;
}

.x-statusbar .x-status-busy {
    background-image: url(../images/access/grid/loading.gif);
}

.x-statusbar .x-status-text-panel {
    border-color: #99bbe8 #fff #fff #99bbe8;
}
