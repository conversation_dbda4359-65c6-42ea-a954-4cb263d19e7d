/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-tree-node-expanded .x-tree-node-icon{
	background-image:url(../images/access/tree/folder-open.gif);
}

.x-tree-node-leaf .x-tree-node-icon{
	background-image:url(../images/default/tree/leaf.gif);
}

.x-tree-node-collapsed .x-tree-node-icon{
	background-image:url(../images/access/tree/folder.gif);
}

.x-tree-node-loading .x-tree-node-icon{
	background-image:url(../images/default/tree/loading.gif) !important;
}

.x-tree-node .x-tree-node-inline-icon {
    background-image: none;
}

.x-tree-node-loading a span{
	 font-style: italic;
	 color:#444444;
}

.ext-ie .x-tree-node-el input {
    width:14px;
    height:14px;
}

.x-tree-lines .x-tree-elbow{
	background-image:url(../images/access/tree/elbow.gif);
}

.x-tree-lines .x-tree-elbow-plus{
	background-image:url(../images/access/tree/elbow-plus.gif);
}

.x-tree-lines .x-tree-elbow-minus{
	background-image:url(../images/access/tree/elbow-minus.gif);
}

.x-tree-lines .x-tree-elbow-end{
	background-image:url(../images/access/tree/elbow-end.gif);
}

.x-tree-lines .x-tree-elbow-end-plus{
	background-image:url(../images/access/tree/elbow-end-plus.gif);
}

.x-tree-lines .x-tree-elbow-end-minus{
	background-image:url(../images/access/tree/elbow-end-minus.gif);
}

.x-tree-lines .x-tree-elbow-line{
	background-image:url(../images/access/tree/elbow-line.gif);
}

.x-tree-no-lines .x-tree-elbow-plus{
	background-image:url(../images/access/tree/elbow-plus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-minus{
	background-image:url(../images/access/tree/elbow-minus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-end-plus{
	background-image:url(../images/access/tree/elbow-end-plus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-end-minus{
	background-image:url(../images/access/tree/elbow-end-minus-nl.gif);
}

.x-tree-arrows .x-tree-elbow-plus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-minus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-end-plus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-end-minus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-node{
	color:#000;
	font: normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-tree-node a, .x-dd-drag-ghost a{
	color:#fff;
}

.x-tree-node a span, .x-dd-drag-ghost a span{
	color:#fff;
}

.x-tree-node .x-tree-selected a, .x-dd-drag-ghost a{
	color:#fff;
}

.x-tree-node .x-tree-selected a span, .x-dd-drag-ghost a span{
	color:#fff;
}

.x-tree-node .x-tree-node-disabled a span{
	color:gray !important;
}

.x-tree-node div.x-tree-drag-insert-below{
 	 border-bottom-color:#36c;
}

.x-tree-node div.x-tree-drag-insert-above{
	 border-top-color:#36c;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-below a{
 	 border-bottom-color:#36c;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-above a{
	 border-top-color:#36c;
}

.x-tree-node .x-tree-drag-append a span{
	 background-color:#ddd;
	 border-color:gray;
}

.x-tree-node .x-tree-node-over {
	background-color: #7e5530;
}

.x-tree-node .x-tree-selected {
	background-color: #e5872c;
}

.x-tree-drop-ok-append .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-add.gif);
}

.x-tree-drop-ok-above .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-over.gif);
}

.x-tree-drop-ok-below .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-under.gif);
}

.x-tree-drop-ok-between .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-between.gif);
}
