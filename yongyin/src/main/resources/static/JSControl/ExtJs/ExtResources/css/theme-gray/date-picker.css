/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-date-picker {
    border-color:#585858;
    background-color:#fff;
}

.x-date-middle,.x-date-left,.x-date-right {
	background-image: url(../images/gray/shared/hd-sprite.gif);
	color:#fff;
	font:bold 11px "sans serif", tahoma, verdana, helvetica;
}

.x-date-middle .x-btn .x-btn-text {
    color:#fff;
}

.x-date-middle .x-btn-mc em.x-btn-arrow {
    background-image:url(../images/gray/toolbar/btn-arrow-light.gif);
}

.x-date-right a {
    background-image: url(../images/gray/shared/right-btn.gif);
}

.x-date-left a{
	background-image: url(../images/gray/shared/left-btn.gif);
}

.x-date-inner th {
    background-color:#D8D8D8;
    background-image: url(../images/gray/panel/white-top-bottom.gif);
	border-bottom-color:#AFAFAF;
    font:normal 10px arial, helvetica,tahoma,sans-serif;
	color:#595959;
}

.x-date-inner td {
    border-color:#fff;
}

.x-date-inner a {
    font:normal 11px arial, helvetica,tahoma,sans-serif;
    color:#000;
}

.x-date-inner .x-date-active{
	color:#000;
}

.x-date-inner .x-date-selected a{
    background-image: none;
    background-color:#D8D8D8;
	border-color:#DCDCDC;
}

.x-date-inner .x-date-today a{
	border-color:darkred;
}

.x-date-inner .x-date-selected span{
    font-weight:bold;
}

.x-date-inner .x-date-prevday a,.x-date-inner .x-date-nextday a {
	color:#aaa;
}

.x-date-bottom {
    border-top-color:#AFAFAF;
    background-color:#D8D8D8;
    background:#D8D8D8 url(../images/gray/panel/white-top-bottom.gif) 0 -2px;
}

.x-date-inner a:hover, .x-date-inner .x-date-disabled a:hover{
    color:#000;
    background-color:#D8D8D8;
}

.x-date-inner .x-date-disabled a {
	background-color:#eee;
	color:#bbb;
}

.x-date-mmenu{
    background-color:#eee !important;
}

.x-date-mmenu .x-menu-item {
	font-size:10px;
	color:#000;
}

.x-date-mp {
	background-color:#fff;
}

.x-date-mp td {
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns button {
	background-color:#4E565F;
	color:#fff;
	border-color:#C0C0C0 #434343 #434343 #C0C0C0;
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns {
    background-color:#D8D8D8;
    background:#D8D8D8 url(../images/gray/panel/white-top-bottom.gif) 0 -2px;
}

.x-date-mp-btns td {
	border-top-color:#AFAFAF;
}

td.x-date-mp-month a,td.x-date-mp-year a {
	color: #333;
}

td.x-date-mp-month a:hover,td.x-date-mp-year a:hover {
	color:#333;
	background-color:#FDFDFD;
}

td.x-date-mp-sel a {
    background-color:#D8D8D8;
    background:#D8D8D8 url(../images/gray/panel/white-top-bottom.gif) 0 -2px;
	border-color:#DCDCDC;
}

.x-date-mp-ybtn a {
    background-image:url(../images/gray/panel/tool-sprites.gif);
}

td.x-date-mp-sep {
   border-right-color:#D7D7D7;
}