/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-html-editor-wrap {
    border:1px solid;
}

.x-html-editor-tb .x-btn-text {
    background:transparent no-repeat;
}

.x-html-editor-tb .x-edit-bold, .x-menu-item img.x-edit-bold {
    background-position:0 0;
    background-image:url(../images/default/editor/tb-sprite.gif);    
}

.x-html-editor-tb .x-edit-italic, .x-menu-item img.x-edit-italic {
    background-position:-16px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-underline, .x-menu-item img.x-edit-underline {
    background-position:-32px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-forecolor, .x-menu-item img.x-edit-forecolor {
    background-position:-160px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-backcolor, .x-menu-item img.x-edit-backcolor {
    background-position:-176px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-justifyleft, .x-menu-item img.x-edit-justifyleft {
    background-position:-112px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-justifycenter, .x-menu-item img.x-edit-justifycenter {
    background-position:-128px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-justifyright, .x-menu-item img.x-edit-justifyright {
    background-position:-144px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-insertorderedlist, .x-menu-item img.x-edit-insertorderedlist {
    background-position:-80px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-insertunorderedlist, .x-menu-item img.x-edit-insertunorderedlist {
    background-position:-96px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-increasefontsize, .x-menu-item img.x-edit-increasefontsize {
    background-position:-48px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-decreasefontsize, .x-menu-item img.x-edit-decreasefontsize {
    background-position:-64px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-sourceedit, .x-menu-item img.x-edit-sourceedit {
    background-position:-192px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tb .x-edit-createlink, .x-menu-item img.x-edit-createlink {
    background-position:-208px 0;
    background-image:url(../images/default/editor/tb-sprite.gif);
}

.x-html-editor-tip .x-tip-bd .x-tip-bd-inner {
    padding:5px;
    padding-bottom:1px;
}

.x-html-editor-tb .x-toolbar {
    position:static !important;
}