/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
/*
 Creates rounded, raised boxes like on the Ext website - the markup isn't pretty:
  <div class="x-box-blue">
        <div class="x-box-tl"><div class="x-box-tr"><div class="x-box-tc"></div></div></div>
        <div class="x-box-ml"><div class="x-box-mr"><div class="x-box-mc">
            <h3>YOUR TITLE HERE (optional)</h3>
            <div>YOUR CONTENT HERE</div>
        </div></div></div>
        <div class="x-box-bl"><div class="x-box-br"><div class="x-box-bc"></div></div></div>
    </div>
 */

.x-box-tl {
	background: transparent no-repeat 0 0;
    zoom:1;
}

.x-box-tc {
	height: 8px;
	background: transparent repeat-x 0 0;
	overflow: hidden;
}

.x-box-tr {
	background: transparent no-repeat right -8px;
}

.x-box-ml {
	background: transparent repeat-y 0;
	padding-left: 4px;
	overflow: hidden;
    zoom:1;
}

.x-box-mc {
	background: repeat-x 0 -16px;
	padding: 4px 10px;
}

.x-box-mc h3 {
	margin: 0 0 4px 0;
    zoom:1;
}

.x-box-mr {
	background: transparent repeat-y right;
	padding-right: 4px;
	overflow: hidden;
}

.x-box-bl {
	background: transparent no-repeat 0 -16px;
    zoom:1;
}

.x-box-bc {
	background: transparent repeat-x 0 -8px;
	height: 8px;
	overflow: hidden;
}

.x-box-br {
	background: transparent no-repeat right -24px;
}

.x-box-tl, .x-box-bl {
	padding-left: 8px;
	overflow: hidden;
}

.x-box-tr, .x-box-br {
	padding-right: 8px;
	overflow: hidden;
}