/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-tip .x-tip-close{
	background-image: url(../images/access/qtip/close.gif);
}

.x-tip .x-tip-tc, .x-tip .x-tip-tl, .x-tip .x-tip-tr, .x-tip .x-tip-bc, .x-tip .x-tip-bl, .x-tip .x-tip-br, .x-tip .x-tip-ml, .x-tip .x-tip-mr {
	background-image: url(../images/access/qtip/tip-sprite.gif);
}

.x-tip .x-tip-mc {
    font: normal 14px tahoma,arial,helvetica,sans-serif;
}
.x-tip .x-tip-ml {
	background-color: #fff;
}

.x-tip .x-tip-header-text {
    font: bold 14px tahoma,arial,helvetica,sans-serif;
    color:#ffd;
}

.x-tip .x-tip-body {
    font: normal 14px tahoma,arial,helvetica,sans-serif;
    color:#000;
}

.x-form-invalid-tip .x-tip-tc, .x-form-invalid-tip .x-tip-tl, .x-form-invalid-tip .x-tip-tr, .x-form-invalid-tip .x-tip-bc,
.x-form-invalid-tip .x-tip-bl, .x-form-invalid-tip .x-tip-br, .x-form-invalid-tip .x-tip-ml, .x-form-invalid-tip .x-tip-mr
{
	background-image: url(../images/default/form/error-tip-corners.gif);
}

.x-form-invalid-tip .x-tip-body {
    background-image:url(../images/access/form/exclamation.gif);
}

.x-tip-anchor {
    background-image:url(../images/access/qtip/tip-anchor-sprite.gif);
}
