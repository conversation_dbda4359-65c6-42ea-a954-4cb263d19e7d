/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
/* all fields */
.x-form-field{
    margin: 0 0 0 0;
}

.ext-webkit *:focus{
    outline: none !important;
}

/* ---- text fields ---- */
.x-form-text, textarea.x-form-field{
    padding:1px 3px;
    background:repeat-x 0 0;
    border:1px solid;
}

textarea.x-form-field {
    padding:2px 3px;
}

.x-form-text, .ext-ie .x-form-file {
    height:22px;
    line-height:18px;
    vertical-align:middle;
}

.ext-ie6 .x-form-text, .ext-ie7 .x-form-text {
    margin:-1px 0; /* ie bogus margin bug */
    height:22px; /* ie quirks */
    line-height:18px;
}

.ext-ie6 textarea.x-form-field, .ext-ie7 textarea.x-form-field {
    margin:-1px 0; /* ie bogus margin bug */
}

.ext-strict .x-form-text {
    height:18px;
}

.ext-safari.ext-mac textarea.x-form-field {
    margin-bottom:-2px; /* another bogus margin bug, safari/mac only */
}

.ext-strict .ext-ie8 .x-form-text, .ext-strict .ext-ie8 textarea.x-form-field {
    margin-bottom: 1px;
}

.ext-gecko .x-form-text , .ext-ie8 .x-form-text {
    padding-top:2px; /* FF won't center the text vertically */
    padding-bottom:0;
}

.ext-ie6 .x-form-composite .x-form-text.x-box-item, .ext-ie7 .x-form-composite .x-form-text.x-box-item {
    margin: 0 !important; /* clear ie bogus margin bug fix */
}

textarea {
    resize: none;  /* Disable browser resizable textarea */
}

/* select boxes */
.x-form-select-one {
    height:20px;
    line-height:18px;
    vertical-align:middle;
    border: 1px solid;
}

/* multi select boxes */

/* --- TODO --- */

/* 2.0.2 style */
.x-form-check-wrap {
    line-height:18px;
    height: auto;
}

.ext-ie .x-form-check-wrap input {
    width:15px;
    height:15px;
}

.x-form-check-wrap input{
    vertical-align: bottom;
}

.x-editor .x-form-check-wrap {
    padding:3px;
}

.x-editor .x-form-checkbox {
    height:13px;
}

.x-form-check-group-label {
    border-bottom: 1px solid;
    margin-bottom: 5px;
    padding-left: 3px !important;
    float: none !important;
}

/* wrapped fields and triggers */
.x-form-field-wrap .x-form-trigger{
    width:17px;
    height:21px;
    border:0;
    background:transparent no-repeat 0 0;
    cursor:pointer;
    border-bottom: 1px solid;
    position:absolute;
    top:0;
}

.x-form-field-wrap .x-form-date-trigger, .x-form-field-wrap .x-form-clear-trigger, .x-form-field-wrap .x-form-search-trigger{
    cursor:pointer;
}

.x-form-field-wrap .x-form-twin-triggers .x-form-trigger{
    position:static;
    top:auto;
    vertical-align:top;
}

.x-form-field-wrap {
    position:relative;
    left:0;top:0;
    text-align: left;
    zoom:1;
    white-space: nowrap;
}

.ext-strict .ext-ie8 .x-toolbar-cell .x-form-field-trigger-wrap .x-form-trigger {
    right: 0; /* IE8 Strict mode trigger bug */
}

.x-form-field-wrap .x-form-trigger-over{
    background-position:-17px 0;
}

.x-form-field-wrap .x-form-trigger-click{
    background-position:-34px 0;
}

.x-trigger-wrap-focus .x-form-trigger{
    background-position:-51px 0;
}

.x-trigger-wrap-focus .x-form-trigger-over{
    background-position:-68px 0;
}

.x-trigger-wrap-focus .x-form-trigger-click{
    background-position:-85px 0;
}

.x-trigger-wrap-focus .x-form-trigger{
    border-bottom: 1px solid;
}

.x-item-disabled .x-form-trigger-over{
    background-position:0 0 !important;
    border-bottom: 1px solid;
}

.x-item-disabled .x-form-trigger-click{
    background-position:0 0 !important;
    border-bottom: 1px solid;
}

.x-trigger-noedit{
    cursor:pointer;
}

/* field focus style */
.x-form-focus, textarea.x-form-focus{
    border: 1px solid;
}

/* invalid fields */
.x-form-invalid, textarea.x-form-invalid{
    background:repeat-x bottom;
    border: 1px solid;
}

.x-form-inner-invalid, textarea.x-form-inner-invalid{
    background:repeat-x bottom;
}

/* editors */
.x-editor {
    visibility:hidden;
    padding:0;
    margin:0;
}

.x-form-grow-sizer {
    left: -10000px;
    padding: 8px 3px;
    position: absolute;
    visibility:hidden;
    top: -10000px;
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
    zoom:1;
}

.x-form-grow-sizer p {
    margin:0 !important;
    border:0 none !important;
    padding:0 !important;
}

/* Form Items CSS */

.x-form-item {
    display:block;
    margin-bottom:4px;
    zoom:1;
}

.x-form-item label.x-form-item-label {
    display:block;
    float:left;
    width:100px;
    padding:3px;
    padding-left:0;
    clear:left;
    z-index:2;
    position:relative;
}

.x-form-element {
    padding-left:105px;
    position:relative;
}

.x-form-invalid-msg {
    padding:2px;
    padding-left:18px;
    background: transparent no-repeat 0 2px;
    line-height:16px;
    width:200px;
}

.x-form-label-left label.x-form-item-label {
   text-align:left;
}

.x-form-label-right label.x-form-item-label {
   text-align:right;
}

.x-form-label-top .x-form-item label.x-form-item-label {
    width:auto;
    float:none;
    clear:none;
    display:inline;
    margin-bottom:4px;
    position:static;
}

.x-form-label-top .x-form-element {
    padding-left:0;
    padding-top:4px;
}

.x-form-label-top .x-form-item {
    padding-bottom:4px;
}

/* Editor small font for grid, toolbar and tree */
.x-small-editor .x-form-text {
    height:20px;
    line-height:16px;
    vertical-align:middle;
}

.ext-ie6 .x-small-editor .x-form-text, .ext-ie7 .x-small-editor .x-form-text {
    margin-top:-1px !important; /* ie bogus margin bug */
    margin-bottom:-1px !important;
    height:20px !important; /* ie quirks */
    line-height:16px !important;
}

.ext-strict .x-small-editor .x-form-text {
    height:16px !important;
}

.ext-ie6 .x-small-editor .x-form-text, .ext-ie7 .x-small-editor .x-form-text {
    height:20px;
    line-height:16px;
}

.ext-border-box .x-small-editor .x-form-text {
    height:20px;
}

.x-small-editor .x-form-select-one {
    height:20px;
    line-height:16px;
    vertical-align:middle;
}

.x-small-editor .x-form-num-field {
    text-align:right;
}

.x-small-editor .x-form-field-wrap .x-form-trigger{
    height:19px;
}

.ext-webkit .x-small-editor .x-form-text{padding-top:3px;font-size:100%;}

.x-form-clear {
    clear:both;
    height:0;
    overflow:hidden;
    line-height:0;
    font-size:0;
}
.x-form-clear-left {
    clear:left;
    height:0;
    overflow:hidden;
    line-height:0;
    font-size:0;
}

.ext-ie6 .x-form-check-wrap input, .ext-border-box .x-form-check-wrap input{
   margin-top: 3px;
}

.x-form-cb-label {
    position: relative;
    margin-left:4px;
    top: 2px;
}

.ext-ie .x-form-cb-label{
    top: 1px;
}

.ext-ie6 .x-form-cb-label, .ext-border-box .x-form-cb-label{
    top: 3px;
}

.x-form-display-field{
    padding-top: 2px;
}

.ext-gecko .x-form-display-field, .ext-strict .ext-ie7 .x-form-display-field{
    padding-top: 1px;
}

.ext-ie .x-form-display-field{
    padding-top: 3px;
}

.ext-strict .ext-ie8 .x-form-display-field{
    padding-top: 0;
}

.x-form-column {
    float:left;
    padding:0;
    margin:0;
    width:48%;
    overflow:hidden;
    zoom:1;
}

/* buttons */
.x-form .x-form-btns-ct .x-btn{
    float:right;
    clear:none;
}

.x-form .x-form-btns-ct .x-form-btns td {
    border:0;
    padding:0;
}

.x-form .x-form-btns-ct .x-form-btns-right table{
    float:right;
    clear:none;
}

.x-form .x-form-btns-ct .x-form-btns-left table{
    float:left;
    clear:none;
}

.x-form .x-form-btns-ct .x-form-btns-center{
    text-align:center; /*ie*/
}

.x-form .x-form-btns-ct .x-form-btns-center table{
    margin:0 auto; /*everyone else*/
}

.x-form .x-form-btns-ct table td.x-form-btn-td{
    padding:3px;
}

.x-form .x-form-btns-ct .x-btn-focus .x-btn-left{
    background-position:0 -147px;
}

.x-form .x-form-btns-ct .x-btn-focus .x-btn-right{
    background-position:0 -168px;
}

.x-form .x-form-btns-ct .x-btn-focus .x-btn-center{
    background-position:0 -189px;
}

.x-form .x-form-btns-ct .x-btn-click .x-btn-center{
    background-position:0 -126px;
}

.x-form .x-form-btns-ct .x-btn-click  .x-btn-right{
    background-position:0 -84px;
}

.x-form .x-form-btns-ct .x-btn-click .x-btn-left{
    background-position:0 -63px;
}

.x-form-invalid-icon {
    width:16px;
    height:18px;
    visibility:hidden;
    position:absolute;
    left:0;
    top:0;
    display:block;
    background:transparent no-repeat 0 2px;
}

/* fieldsets */
.x-fieldset {
    border:1px solid;
    padding:10px;
    margin-bottom:10px;
    display:block; /* preserve margins in IE */
}

/* make top of checkbox/tools visible in webkit */
.ext-webkit .x-fieldset-header {
    padding-top: 1px;
}

.ext-ie .x-fieldset legend {
    margin-bottom:10px;
}

.ext-ie .x-fieldset {
    padding-top: 0;
    padding-bottom:10px;
}

.x-fieldset legend .x-tool-toggle {
    margin-right:3px;
    margin-left:0;
    float:left !important;
}

.x-fieldset legend input {
    margin-right:3px;
    float:left !important;
    height:13px;
    width:13px;
}

fieldset.x-panel-collapsed {
    padding-bottom:0 !important;
    border-width: 1px 1px 0 1px !important;
    border-left-color: transparent;
    border-right-color: transparent;
}

.ext-ie6 fieldset.x-panel-collapsed{
    padding-bottom:0 !important;
    border-width: 1px 0 0 0 !important;
    margin-left: 1px;
    margin-right: 1px;
}

fieldset.x-panel-collapsed .x-fieldset-bwrap {
    visibility:hidden;
    position:absolute;
    left:-1000px;
    top:-1000px;
}

.ext-ie .x-fieldset-bwrap {
    zoom:1;
}

.x-fieldset-noborder {
    border:0px none transparent;
}

.x-fieldset-noborder legend {
    margin-left:-3px;
}

/* IE legend positioning bug */
.ext-ie .x-fieldset-noborder legend {
    position: relative;
    margin-bottom:23px;
}
.ext-ie .x-fieldset-noborder legend span {
    position: absolute;
    left:16px;
}

.ext-gecko .x-window-body .x-form-item {
    -moz-outline: none;
    outline: none;
    overflow: auto;
}

.ext-gecko .x-form-item {
    -moz-outline: none;
    outline: none;
}

.x-hide-label label.x-form-item-label {
     display:none;
}

.x-hide-label .x-form-element {
     padding-left: 0 !important;
}

.x-form-label-top .x-hide-label label.x-form-item-label{
    display: none;
}

.x-fieldset {
    overflow:hidden;
}

.x-fieldset-bwrap {
    overflow:hidden;
    zoom:1;
}

.x-fieldset-body {
    overflow:hidden;
}
