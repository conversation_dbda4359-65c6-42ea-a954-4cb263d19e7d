/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-tab-panel-header, .x-tab-panel-footer {
	background-color: #eaeaea;
	border-color:#d0d0d0;
    overflow:hidden;
    zoom:1;
}

.x-tab-panel-header, .x-tab-panel-footer {
	border-color:#d0d0d0;
}

ul.x-tab-strip-top{
    background-color:#dbdbdb;
	background-image: url(../images/gray/tabs/tab-strip-bg.gif);
	border-bottom-color:#d0d0d0;
}

ul.x-tab-strip-bottom{
    background-color:#dbdbdb;
	background-image: url(../images/gray/tabs/tab-strip-btm-bg.gif);
	border-top-color:#d0d0d0;
}

.x-tab-panel-header-plain .x-tab-strip-spacer,
.x-tab-panel-footer-plain .x-tab-strip-spacer {
    border-color:#d0d0d0;
    background-color: #eaeaea;
}

.x-tab-strip span.x-tab-strip-text {
	font:normal 11px tahoma,arial,helvetica;
	color:#333;
}

.x-tab-strip-over span.x-tab-strip-text {
	color:#111;
}

.x-tab-strip-active span.x-tab-strip-text {
	color:#333;
    font-weight:bold;
}

.x-tab-strip-disabled .x-tabs-text {
	color:#aaaaaa;
}

.x-tab-strip-top .x-tab-right, .x-tab-strip-top .x-tab-left, .x-tab-strip-top .x-tab-strip-inner{
	background-image: url(../images/gray/tabs/tabs-sprite.gif);
}

.x-tab-strip-bottom .x-tab-right {
	background-image: url(../images/gray/tabs/tab-btm-inactive-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-left {
	background-image: url(../images/gray/tabs/tab-btm-inactive-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-over .x-tab-left {
	background-image: url(../images/gray/tabs/tab-btm-over-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-over .x-tab-right {
	background-image: url(../images/gray/tabs/tab-btm-over-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background-image: url(../images/gray/tabs/tab-btm-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background-image: url(../images/gray/tabs/tab-btm-left-bg.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
	background-image:url(../images/gray/tabs/tab-close.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close:hover{
	background-image:url(../images/gray/tabs/tab-close.gif);
}

.x-tab-panel-body {
    border-color:#d0d0d0;
    background-color:#fff;
}

.x-tab-panel-body-top {
    border-top: 0 none;
}

.x-tab-panel-body-bottom {
    border-bottom: 0 none;
}

.x-tab-scroller-left {
    background-image:url(../images/gray/tabs/scroll-left.gif);
    border-bottom-color:#d0d0d0;
}

.x-tab-scroller-left-over {
    background-position: 0 0;
}

.x-tab-scroller-left-disabled {
    background-position: -18px 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}

.x-tab-scroller-right {
    background-image:url(../images/gray/tabs/scroll-right.gif);
    border-bottom-color:#d0d0d0;
}

.x-tab-panel-bbar .x-toolbar, .x-tab-panel-tbar .x-toolbar {
    border-color:#d0d0d0;
}
