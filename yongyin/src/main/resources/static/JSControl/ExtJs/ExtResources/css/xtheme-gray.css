/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.ext-el-mask {
    background-color: #ccc;
}

.ext-el-mask-msg {
    border-color:#999;
    background-color:#ddd;
    background-image:url(../images/gray/panel/white-top-bottom.gif);
    background-position: 0 -1px;
}
.ext-el-mask-msg div {
    background-color: #eee;
    border-color:#d0d0d0;
    color:#222;
    font:normal 11px tahoma, arial, helvetica, sans-serif;
}

.x-mask-loading div {
    background-color:#fbfbfb;
    background-image:url(../images/default/grid/loading.gif);
}

.x-item-disabled {
    color: gray;
}

.x-item-disabled * {
    color: gray !important;
}

.x-splitbar-proxy {
    background-color: #aaa;
}

.x-color-palette a {
    border-color:#fff;
}

.x-color-palette a:hover, .x-color-palette a.x-color-palette-sel {
    border-color:#CFCFCF;
    background-color: #eaeaea;
}

.x-color-palette em:hover, .x-color-palette span:hover{   
    background-color: #eaeaea;
}

.x-color-palette em {
    border-color:#aca899;
}

.x-ie-shadow {
    background-color:#777;
}

.x-shadow .xsmc {
    background-image: url(../images/default/shadow-c.png);
}

.x-shadow .xsml, .x-shadow .xsmr {
    background-image: url(../images/default/shadow-lr.png);
}

.x-shadow .xstl, .x-shadow .xstc,  .x-shadow .xstr, .x-shadow .xsbl, .x-shadow .xsbc, .x-shadow .xsbr{
    background-image: url(../images/default/shadow.png);
}

.loading-indicator {
    font-size: 11px;
    background-image: url(../images/default/grid/loading.gif);
}

.x-spotlight {
    background-color: #ccc;
}.x-tab-panel-header, .x-tab-panel-footer {
	background-color: #eaeaea;
	border-color:#d0d0d0;
    overflow:hidden;
    zoom:1;
}

.x-tab-panel-header, .x-tab-panel-footer {
	border-color:#d0d0d0;
}

ul.x-tab-strip-top{
    background-color:#dbdbdb;
	background-image: url(../images/gray/tabs/tab-strip-bg.gif);
	border-bottom-color:#d0d0d0;
}

ul.x-tab-strip-bottom{
    background-color:#dbdbdb;
	background-image: url(../images/gray/tabs/tab-strip-btm-bg.gif);
	border-top-color:#d0d0d0;
}

.x-tab-panel-header-plain .x-tab-strip-spacer,
.x-tab-panel-footer-plain .x-tab-strip-spacer {
    border-color:#d0d0d0;
    background-color: #eaeaea;
}

.x-tab-strip span.x-tab-strip-text {
	font:normal 11px tahoma,arial,helvetica;
	color:#333;
}

.x-tab-strip-over span.x-tab-strip-text {
	color:#111;
}

.x-tab-strip-active span.x-tab-strip-text {
	color:#333;
    font-weight:bold;
}

.x-tab-strip-disabled .x-tabs-text {
	color:#aaaaaa;
}

.x-tab-strip-top .x-tab-right, .x-tab-strip-top .x-tab-left, .x-tab-strip-top .x-tab-strip-inner{
	background-image: url(../images/gray/tabs/tabs-sprite.gif);
}

.x-tab-strip-bottom .x-tab-right {
	background-image: url(../images/gray/tabs/tab-btm-inactive-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-left {
	background-image: url(../images/gray/tabs/tab-btm-inactive-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-over .x-tab-left {
	background-image: url(../images/gray/tabs/tab-btm-over-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-over .x-tab-right {
	background-image: url(../images/gray/tabs/tab-btm-over-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background-image: url(../images/gray/tabs/tab-btm-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background-image: url(../images/gray/tabs/tab-btm-left-bg.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
	background-image:url(../images/gray/tabs/tab-close.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close:hover{
	background-image:url(../images/gray/tabs/tab-close.gif);
}

.x-tab-panel-body {
    border-color:#d0d0d0;
    background-color:#fff;
}

.x-tab-panel-body-top {
    border-top: 0 none;
}

.x-tab-panel-body-bottom {
    border-bottom: 0 none;
}

.x-tab-scroller-left {
    background-image:url(../images/gray/tabs/scroll-left.gif);
    border-bottom-color:#d0d0d0;
}

.x-tab-scroller-left-over {
    background-position: 0 0;
}

.x-tab-scroller-left-disabled {
    background-position: -18px 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}

.x-tab-scroller-right {
    background-image:url(../images/gray/tabs/scroll-right.gif);
    border-bottom-color:#d0d0d0;
}

.x-tab-panel-bbar .x-toolbar, .x-tab-panel-tbar .x-toolbar {
    border-color:#d0d0d0;
}
.x-form-field{
    font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-form-text, textarea.x-form-field{
    background-color:#fff;
    background-image:url(../images/default/form/text-bg.gif);
    border-color:#C1C1C1;
}

.x-form-select-one {
    background-color:#fff;
    border-color:#C1C1C1;
}

.x-form-check-group-label {
    border-bottom: 1px solid #d0d0d0;
    color: #333;
}

.x-editor .x-form-check-wrap {
    background-color:#fff;
}

.x-form-field-wrap .x-form-trigger{
    background-image:url(../images/gray/form/trigger.gif);
    border-bottom-color:#b5b8c8;
}

.x-form-field-wrap .x-form-date-trigger{
    background-image: url(../images/gray/form/date-trigger.gif);
}

.x-form-field-wrap .x-form-clear-trigger{
    background-image: url(../images/gray/form/clear-trigger.gif);
}

.x-form-field-wrap .x-form-search-trigger{
    background-image: url(../images/gray/form/search-trigger.gif);
}

.x-trigger-wrap-focus .x-form-trigger{
    border-bottom-color: #777777;
}

.x-item-disabled .x-form-trigger-over{
    border-bottom-color:#b5b8c8;
}

.x-item-disabled .x-form-trigger-click{
    border-bottom-color:#b5b8c8;
}

.x-form-focus, textarea.x-form-focus{
	border-color:#777777;
}

.x-form-invalid, textarea.x-form-invalid{
    background-color:#fff;
	background-image:url(../images/default/grid/invalid_line.gif);
	border-color:#c30;
}

.ext-webkit .x-form-invalid{
	background-color:#fee;
	border-color:#ff7870;
}

.x-form-inner-invalid, textarea.x-form-inner-invalid{
    background-color:#fff;
	background-image:url(../images/default/grid/invalid_line.gif);
}

.x-form-grow-sizer {
	font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-form-item {
    font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-form-invalid-msg {
    color:#c0272b;
    font:normal 11px tahoma, arial, helvetica, sans-serif;
    background-image:url(../images/default/shared/warning.gif);
}

.x-form-empty-field {
    color:gray;
}

.x-small-editor .x-form-field {
    font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.ext-webkit .x-small-editor .x-form-field {
    font:normal 12px arial, tahoma, helvetica, sans-serif;
}

.x-form-invalid-icon {
    background-image:url(../images/default/form/exclamation.gif);
}

.x-fieldset {
    border-color:#CCCCCC;
}

.x-fieldset legend {
    font:bold 11px tahoma, arial, helvetica, sans-serif;
    color:#777777;
}.x-btn{
	font:normal 11px tahoma, verdana, helvetica;
}

.x-btn button{
    font:normal 11px arial,tahoma,verdana,helvetica;
    color:#333;
}

.x-btn em {
    font-style:normal;
    font-weight:normal;
}

.x-btn-tl, .x-btn-tr, .x-btn-tc, .x-btn-ml, .x-btn-mr, .x-btn-mc, .x-btn-bl, .x-btn-br, .x-btn-bc{
	background-image:url(../images/gray/button/btn.gif);
}

.x-btn-click .x-btn-text, .x-btn-menu-active .x-btn-text, .x-btn-pressed .x-btn-text{
    color:#000;
}

.x-btn-disabled *{
	color:gray !important;
}

.x-btn-mc em.x-btn-arrow {
    background-image:url(../images/default/button/arrow.gif);
}

.x-btn-mc em.x-btn-split {
    background-image:url(../images/default/button/s-arrow.gif);
}

.x-btn-over .x-btn-mc em.x-btn-split, .x-btn-click .x-btn-mc em.x-btn-split, .x-btn-menu-active .x-btn-mc em.x-btn-split, .x-btn-pressed .x-btn-mc em.x-btn-split {
    background-image:url(../images/gray/button/s-arrow-o.gif);
}

.x-btn-mc em.x-btn-arrow-bottom {
    background-image:url(../images/default/button/s-arrow-b-noline.gif);
}

.x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/default/button/s-arrow-b.gif);
}

.x-btn-over .x-btn-mc em.x-btn-split-bottom, .x-btn-click .x-btn-mc em.x-btn-split-bottom, .x-btn-menu-active .x-btn-mc em.x-btn-split-bottom, .x-btn-pressed .x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/gray/button/s-arrow-bo.gif);
}

.x-btn-group-header {
    color: #666;
}

.x-btn-group-tc {
	background-image: url(../images/gray/button/group-tb.gif);
}

.x-btn-group-tl {
	background-image: url(../images/gray/button/group-cs.gif);
}

.x-btn-group-tr {
	background-image: url(../images/gray/button/group-cs.gif);
}

.x-btn-group-bc {
	background-image: url(../images/gray/button/group-tb.gif);
}

.x-btn-group-bl {
	background-image: url(../images/gray/button/group-cs.gif);
}

.x-btn-group-br {
	background-image: url(../images/gray/button/group-cs.gif);
}

.x-btn-group-ml {
	background-image: url(../images/gray/button/group-lr.gif);
}
.x-btn-group-mr {
	background-image: url(../images/gray/button/group-lr.gif);
}

.x-btn-group-notitle .x-btn-group-tc {
	background-image: url(../images/gray/button/group-tb.gif);
}
.x-toolbar{
	border-color:#d0d0d0;
    background-color:#f0f0f0;
    background-image:url(../images/gray/toolbar/bg.gif);
}

.x-toolbar td,.x-toolbar span,.x-toolbar input,.x-toolbar div,.x-toolbar select,.x-toolbar label{
    font:normal 11px arial,tahoma, helvetica, sans-serif;
}

.x-toolbar .x-item-disabled {
	color:gray;
}

.x-toolbar .x-item-disabled * {
	color:gray;
}

.x-toolbar .x-btn-mc em.x-btn-split {
    background-image:url(../images/default/button/s-arrow-noline.gif);
}

.x-toolbar .x-btn-over .x-btn-mc em.x-btn-split, .x-toolbar .x-btn-click .x-btn-mc em.x-btn-split, 
.x-toolbar .x-btn-menu-active .x-btn-mc em.x-btn-split, .x-toolbar .x-btn-pressed .x-btn-mc em.x-btn-split 
{
    background-image:url(../images/gray/button/s-arrow-o.gif);
}

.x-toolbar .x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/default/button/s-arrow-b-noline.gif);
}

.x-toolbar .x-btn-over .x-btn-mc em.x-btn-split-bottom, .x-toolbar .x-btn-click .x-btn-mc em.x-btn-split-bottom, 
.x-toolbar .x-btn-menu-active .x-btn-mc em.x-btn-split-bottom, .x-toolbar .x-btn-pressed .x-btn-mc em.x-btn-split-bottom 
{
    background-image:url(../images/gray/button/s-arrow-bo.gif);
}

.x-toolbar .xtb-sep {
	background-image: url(../images/default/grid/grid-split.gif);
}

.x-tbar-page-first{
	background-image: url(../images/gray/grid/page-first.gif) !important;
}

.x-tbar-loading{
	background-image: url(../images/gray/grid/refresh.gif) !important;
}

.x-tbar-page-last{
	background-image: url(../images/gray/grid/page-last.gif) !important;
}

.x-tbar-page-next{
	background-image: url(../images/gray/grid/page-next.gif) !important;
}

.x-tbar-page-prev{
	background-image: url(../images/gray/grid/page-prev.gif) !important;
}

.x-item-disabled .x-tbar-loading{
	background-image: url(../images/default/grid/loading.gif) !important;
}

.x-item-disabled .x-tbar-page-first{
	background-image: url(../images/default/grid/page-first-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-last{
	background-image: url(../images/default/grid/page-last-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-next{
	background-image: url(../images/default/grid/page-next-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-prev{
	background-image: url(../images/default/grid/page-prev-disabled.gif) !important;
}

.x-paging-info {
    color:#444;
}

.x-toolbar-more-icon {
    background-image: url(../images/gray/toolbar/more.gif) !important;
}
.x-resizable-handle {
	background-color:#fff;
}

.x-resizable-over .x-resizable-handle-east, .x-resizable-pinned .x-resizable-handle-east,
.x-resizable-over .x-resizable-handle-west, .x-resizable-pinned .x-resizable-handle-west
{
    background-image:url(../images/gray/sizer/e-handle.gif);
}

.x-resizable-over .x-resizable-handle-south, .x-resizable-pinned .x-resizable-handle-south,
.x-resizable-over .x-resizable-handle-north, .x-resizable-pinned .x-resizable-handle-north
{
    background-image:url(../images/gray/sizer/s-handle.gif);
}

.x-resizable-over .x-resizable-handle-north, .x-resizable-pinned .x-resizable-handle-north{
    background-image:url(../images/gray/sizer/s-handle.gif);
}
.x-resizable-over .x-resizable-handle-southeast, .x-resizable-pinned .x-resizable-handle-southeast{
    background-image:url(../images/gray/sizer/se-handle.gif);
}
.x-resizable-over .x-resizable-handle-northwest, .x-resizable-pinned .x-resizable-handle-northwest{
    background-image:url(../images/gray/sizer/nw-handle.gif);
}
.x-resizable-over .x-resizable-handle-northeast, .x-resizable-pinned .x-resizable-handle-northeast{
    background-image:url(../images/gray/sizer/ne-handle.gif);
}
.x-resizable-over .x-resizable-handle-southwest, .x-resizable-pinned .x-resizable-handle-southwest{
    background-image:url(../images/gray/sizer/sw-handle.gif);
}
.x-resizable-proxy{
    border-color:#565656;
}
.x-resizable-overlay{
    background-color:#fff;
}
.x-grid3 {
    background-color:#fff;
}

.x-grid-panel .x-panel-mc .x-panel-body {
    border-color:#d0d0d0;
}

.x-grid3-hd-row td, .x-grid3-row td, .x-grid3-summary-row td{
	font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.x-grid3-hd-row td {
    border-left-color:#eee;
    border-right-color:#d0d0d0;
}

.x-grid-row-loading {
    background-color: #fff;
    background-image:url(../images/default/shared/loading-balls.gif);
}

.x-grid3-row {
    border-color:#ededed;
    border-top-color:#fff;
}

.x-grid3-row-alt{
	background-color:#fafafa;
}

.x-grid3-row-over {
	border-color:#ddd;
    background-color:#efefef;
    background-image:url(../images/default/grid/row-over.gif);
}

.x-grid3-resize-proxy {
    background-color:#777;
}

.x-grid3-resize-marker {
    background-color:#777;
}

.x-grid3-header{
    background-color:#f9f9f9;
	background-image:url(../images/gray/grid/grid3-hrow2.gif);
}

.x-grid3-header-pop {
    border-left-color:#d0d0d0;
}

.x-grid3-header-pop-inner {
    border-left-color:#eee;
    background-image:url(../images/default/grid/hd-pop.gif);
}

td.x-grid3-hd-over, td.sort-desc, td.sort-asc, td.x-grid3-hd-menu-open {
    border-left-color:#ACACAC;
    border-right-color:#ACACAC;
}

td.x-grid3-hd-over .x-grid3-hd-inner, td.sort-desc .x-grid3-hd-inner, td.sort-asc .x-grid3-hd-inner, td.x-grid3-hd-menu-open .x-grid3-hd-inner {
    background-color:#f9f9f9;
    background-image:url(../images/gray/grid/grid3-hrow-over2.gif);

}

.sort-asc .x-grid3-sort-icon {
	background-image: url(../images/gray/grid/sort_asc.gif);
}

.sort-desc .x-grid3-sort-icon {
	background-image: url(../images/gray/grid/sort_desc.gif);
}

.x-grid3-cell-text, .x-grid3-hd-text {
	color:#000;
}

.x-grid3-split {
	background-image: url(../images/default/grid/grid-split.gif);
}

.x-grid3-hd-text {
	color:#333;
}

.x-dd-drag-proxy .x-grid3-hd-inner{
    background-color:#f9f9f9;
	background-image:url(../images/gray/grid/grid3-hrow-over2.gif);
	border-color:#ACACAC;
}

.col-move-top{
	background-image:url(../images/gray/grid/col-move-top.gif);
}

.col-move-bottom{
	background-image:url(../images/gray/grid/col-move-bottom.gif);
}

.x-grid3-row-selected {
	background-color:#CCCCCC !important;
	background-image: none;
	border-color:#ACACAC;
}

.x-grid3-cell-selected{
	background-color: #CBCBCB !important;
	color:#000;
}

.x-grid3-cell-selected span{
	color:#000 !important;
}

.x-grid3-cell-selected .x-grid3-cell-text{
	color:#000;
}

.x-grid3-locked td.x-grid3-row-marker, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker{
    background-color:#ebeadb !important;
    background-image:url(../images/default/grid/grid-hrow.gif) !important;
    color:#000;
    border-top-color:#fff;
    border-right-color:#6fa0df !important;
}

.x-grid3-locked td.x-grid3-row-marker div, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker div{
    color:#333 !important;
}

.x-grid3-dirty-cell {
    background-image:url(../images/default/grid/dirty.gif);
}

.x-grid3-topbar, .x-grid3-bottombar{
	font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.x-grid3-bottombar .x-toolbar{
	border-top-color:#a9bfd3;
}

.x-props-grid .x-grid3-td-name .x-grid3-cell-inner{
	background-image:url(../images/default/grid/grid3-special-col-bg.gif) !important;
    color:#000 !important;
}

.x-props-grid .x-grid3-body .x-grid3-td-name{
    background-color:#fff !important;
    border-right-color:#eee;
}

.xg-hmenu-sort-asc .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-asc.gif);
}

.xg-hmenu-sort-desc .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-desc.gif);
}

.xg-hmenu-lock .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-lock.gif);
}

.xg-hmenu-unlock .x-menu-item-icon{
	background-image: url(../images/default/grid/hmenu-unlock.gif);
}

.x-grid3-hd-btn {
    background-color:#f9f9f9;
    background-image:url(../images/gray/grid/grid3-hd-btn.gif);
}

.x-grid3-body .x-grid3-td-expander {
    background-image:url(../images/default/grid/grid3-special-col-bg.gif);
}

.x-grid3-row-expander {
    background-image:url(../images/gray/grid/row-expand-sprite.gif);
}

.x-grid3-body .x-grid3-td-checker {
    background-image: url(../images/default/grid/grid3-special-col-bg.gif);
}

.x-grid3-row-checker, .x-grid3-hd-checker {
    background-image:url(../images/default/grid/row-check-sprite.gif);
}

.x-grid3-body .x-grid3-td-numberer {
    background-image:url(../images/default/grid/grid3-special-col-bg.gif);
}

.x-grid3-body .x-grid3-td-numberer .x-grid3-cell-inner {
	color:#444;
}

.x-grid3-body .x-grid3-td-row-icon {
    background-image:url(../images/default/grid/grid3-special-col-bg.gif);
}

.x-grid3-body .x-grid3-row-selected .x-grid3-td-numberer,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-checker,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-expander {
	background-image:url(../images/gray/grid/grid3-special-col-sel-bg.gif);
}

.x-grid3-check-col {
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-grid3-check-col-on {
	background-image:url(../images/default/menu/checked.gif);
}

.x-grid-group, .x-grid-group-body, .x-grid-group-hd {
    zoom:1;
}

.x-grid-group-hd {
    border-bottom-color:#d0d0d0;
}

.x-grid-group-hd div.x-grid-group-title {
    background-image:url(../images/gray/grid/group-collapse.gif);
    color:#5F5F5F;
    font:bold 11px tahoma, arial, helvetica, sans-serif;
}

.x-grid-group-collapsed .x-grid-group-hd div.x-grid-group-title {
    background-image:url(../images/gray/grid/group-expand.gif);
}

.x-group-by-icon {
    background-image:url(../images/default/grid/group-by.gif);
}

.x-cols-icon {
    background-image:url(../images/default/grid/columns.gif);
}

.x-show-groups-icon {
    background-image:url(../images/default/grid/group-by.gif);
}

.x-grid-empty {
    color:gray;
    font:normal 11px tahoma, arial, helvetica, sans-serif;
}

.x-grid-with-col-lines .x-grid3-row td.x-grid3-cell {
    border-right-color:#ededed;
}

.x-grid-with-col-lines .x-grid3-row{
    border-top-color:#ededed;
}

.x-grid-with-col-lines .x-grid3-row-selected {
	border-top-color:#B9B9B9;
}
.x-dd-drag-ghost{
	color:#000;
	font: normal 11px arial, helvetica, sans-serif;
    border-color: #ddd #bbb #bbb #ddd;
	background-color:#fff;
}

.x-dd-drop-nodrop .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-no.gif);
}

.x-dd-drop-ok .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-yes.gif);
}

.x-dd-drop-ok-add .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-add.gif);
}

.x-view-selector {
    background-color:#D6D6D6;
    border-color:#888888;
}.x-tree-node-expanded .x-tree-node-icon{
	background-image:url(../images/default/tree/folder-open.gif);
}

.x-tree-node-leaf .x-tree-node-icon{
	background-image:url(../images/default/tree/leaf.gif);
}

.x-tree-node-collapsed .x-tree-node-icon{
	background-image:url(../images/default/tree/folder.gif);
}

.x-tree-node-loading .x-tree-node-icon{
	background-image:url(../images/default/tree/loading.gif) !important;
}

.x-tree-node .x-tree-node-inline-icon {
    background-image: none;
}

.x-tree-node-loading a span{
	 font-style: italic;
	 color:#444444;
}

.ext-ie .x-tree-node-el input {
    width:15px;
    height:15px;
}

.x-tree-lines .x-tree-elbow{
	background-image:url(../images/default/tree/elbow.gif);
}

.x-tree-lines .x-tree-elbow-plus{
	background-image:url(../images/default/tree/elbow-plus.gif);
}

.x-tree-lines .x-tree-elbow-minus{
	background-image:url(../images/default/tree/elbow-minus.gif);
}

.x-tree-lines .x-tree-elbow-end{
	background-image:url(../images/default/tree/elbow-end.gif);
}

.x-tree-lines .x-tree-elbow-end-plus{
	background-image:url(../images/gray/tree/elbow-end-plus.gif);
}

.x-tree-lines .x-tree-elbow-end-minus{
	background-image:url(../images/gray/tree/elbow-end-minus.gif);
}

.x-tree-lines .x-tree-elbow-line{
	background-image:url(../images/default/tree/elbow-line.gif);
}

.x-tree-no-lines .x-tree-elbow-plus{
	background-image:url(../images/default/tree/elbow-plus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-minus{
	background-image:url(../images/default/tree/elbow-minus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-end-plus{
	background-image:url(../images/gray/tree/elbow-end-plus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-end-minus{
	background-image:url(../images/gray/tree/elbow-end-minus-nl.gif);
}

.x-tree-arrows .x-tree-elbow-plus{
    background-image:url(../images/gray/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-minus{
    background-image:url(../images/gray/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-end-plus{
    background-image:url(../images/gray/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-end-minus{
    background-image:url(../images/gray/tree/arrows.gif);
}

.x-tree-node{
	color:#000;
	font: normal 11px arial, tahoma, helvetica, sans-serif;
}

.x-tree-node a, .x-dd-drag-ghost a{
	color:#000;
}

.x-tree-node a span, .x-dd-drag-ghost a span{
	color:#000;
}

.x-tree-node .x-tree-node-disabled a span{
	color:gray !important;
}

.x-tree-node div.x-tree-drag-insert-below{
 	 border-bottom-color:#36c;
}

.x-tree-node div.x-tree-drag-insert-above{
	 border-top-color:#36c;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-below a{
 	 border-bottom-color:#36c;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-above a{
	 border-top-color:#36c;
}

.x-tree-node .x-tree-drag-append a span{
	 background-color:#ddd;
	 border-color:gray;
}

.x-tree-node .x-tree-node-over {
	background-color: #eee;
}

.x-tree-node .x-tree-selected {
	background-color: #ddd;
}

.x-tree-drop-ok-append .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-add.gif);
}

.x-tree-drop-ok-above .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-over.gif);
}

.x-tree-drop-ok-below .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-under.gif);
}

.x-tree-drop-ok-between .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-between.gif);
}
.x-date-picker {
    border-color:#585858;
    background-color:#fff;
}

.x-date-middle,.x-date-left,.x-date-right {
	background-image: url(../images/gray/shared/hd-sprite.gif);
	color:#fff;
	font:bold 11px "sans serif", tahoma, verdana, helvetica;
}

.x-date-middle .x-btn .x-btn-text {
    color:#fff;
}

.x-date-middle .x-btn-mc em.x-btn-arrow {
    background-image:url(../images/gray/toolbar/btn-arrow-light.gif);
}

.x-date-right a {
    background-image: url(../images/gray/shared/right-btn.gif);
}

.x-date-left a{
	background-image: url(../images/gray/shared/left-btn.gif);
}

.x-date-inner th {
    background-color:#D8D8D8;
    background-image: url(../images/gray/panel/white-top-bottom.gif);
	border-bottom-color:#AFAFAF;
    font:normal 10px arial, helvetica,tahoma,sans-serif;
	color:#595959;
}

.x-date-inner td {
    border-color:#fff;
}

.x-date-inner a {
    font:normal 11px arial, helvetica,tahoma,sans-serif;
    color:#000;
}

.x-date-inner .x-date-active{
	color:#000;
}

.x-date-inner .x-date-selected a{
    background-image: none;
    background-color:#D8D8D8;
	border-color:#DCDCDC;
}

.x-date-inner .x-date-today a{
	border-color:darkred;
}

.x-date-inner .x-date-selected span{
    font-weight:bold;
}

.x-date-inner .x-date-prevday a,.x-date-inner .x-date-nextday a {
	color:#aaa;
}

.x-date-bottom {
    border-top-color:#AFAFAF;
    background-color:#D8D8D8;
    background:#D8D8D8 url(../images/gray/panel/white-top-bottom.gif) 0 -2px;
}

.x-date-inner a:hover, .x-date-inner .x-date-disabled a:hover{
    color:#000;
    background-color:#D8D8D8;
}

.x-date-inner .x-date-disabled a {
	background-color:#eee;
	color:#bbb;
}

.x-date-mmenu{
    background-color:#eee !important;
}

.x-date-mmenu .x-menu-item {
	font-size:10px;
	color:#000;
}

.x-date-mp {
	background-color:#fff;
}

.x-date-mp td {
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns button {
	background-color:#4E565F;
	color:#fff;
	border-color:#C0C0C0 #434343 #434343 #C0C0C0;
	font:normal 11px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns {
    background-color:#D8D8D8;
    background:#D8D8D8 url(../images/gray/panel/white-top-bottom.gif) 0 -2px;
}

.x-date-mp-btns td {
	border-top-color:#AFAFAF;
}

td.x-date-mp-month a,td.x-date-mp-year a {
	color: #333;
}

td.x-date-mp-month a:hover,td.x-date-mp-year a:hover {
	color:#333;
	background-color:#FDFDFD;
}

td.x-date-mp-sel a {
    background-color:#D8D8D8;
    background:#D8D8D8 url(../images/gray/panel/white-top-bottom.gif) 0 -2px;
	border-color:#DCDCDC;
}

.x-date-mp-ybtn a {
    background-image:url(../images/gray/panel/tool-sprites.gif);
}

td.x-date-mp-sep {
   border-right-color:#D7D7D7;
}.x-tip .x-tip-close{
	background-image: url(../images/gray/qtip/close.gif);
}

.x-tip .x-tip-tc, .x-tip .x-tip-tl, .x-tip .x-tip-tr, .x-tip .x-tip-bc, .x-tip .x-tip-bl, .x-tip .x-tip-br, .x-tip .x-tip-ml, .x-tip .x-tip-mr {
	background-image: url(../images/gray/qtip/tip-sprite.gif);
}

.x-tip .x-tip-mc {
    font: normal 11px tahoma,arial,helvetica,sans-serif;
}
.x-tip .x-tip-ml {
	background-color: #fff;
}

.x-tip .x-tip-header-text {
    font: bold 11px tahoma,arial,helvetica,sans-serif;
    color:#444;
}

.x-tip .x-tip-body {
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    color:#444;
}

.x-form-invalid-tip .x-tip-tc, .x-form-invalid-tip .x-tip-tl, .x-form-invalid-tip .x-tip-tr, .x-form-invalid-tip .x-tip-bc,
.x-form-invalid-tip .x-tip-bl, .x-form-invalid-tip .x-tip-br, .x-form-invalid-tip .x-tip-ml, .x-form-invalid-tip .x-tip-mr
{
	background-image: url(../images/default/form/error-tip-corners.gif);
}

.x-form-invalid-tip .x-tip-body {
    background-image:url(../images/default/form/exclamation.gif);
}

.x-tip-anchor {
    background-image:url(../images/gray/qtip/tip-anchor-sprite.gif);
}.x-menu {
    background-color:#f0f0f0;
	background-image:url(../images/default/menu/menu.gif);
}

.x-menu-floating{
    border-color:#7D7D7D;
}

.x-menu-nosep {
	background-image:none;
}

.x-menu-list-item{
	font:normal 11px arial,tahoma,sans-serif;
}

.x-menu-item-arrow{
	background-image:url(../images/gray/menu/menu-parent.gif);
}

.x-menu-sep {
    background-color:#e0e0e0;
	border-bottom-color:#fff;
}

a.x-menu-item {
	color:#222;
}

.x-menu-item-active {
    background-image: url(../images/gray/menu/item-over.gif);
	background-color: #f1f1f1;
    border-color:#ACACAC;
}

.x-menu-item-active a.x-menu-item {
	border-color:#ACACAC;
}

.x-menu-check-item .x-menu-item-icon{
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/default/menu/checked.gif);
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background-image:url(../images/gray/menu/group-checked.gif);
}

.x-menu-group-item .x-menu-item-icon{
    background-image:none;
}

.x-menu-plain {
	background-color:#fff !important;
}

.x-menu .x-date-picker{
    border-color:#AFAFAF;
}

.x-cycle-menu .x-menu-item-checked {
    border-color:#B9B9B9 !important;
    background-color:#F1F1F1;
}

.x-menu-scroller-top {
    background-image:url(../images/default/layout/mini-top.gif);
}

.x-menu-scroller-bottom {
    background-image:url(../images/default/layout/mini-bottom.gif);
}.x-box-tl {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-tc {
	background-image: url(../images/default/box/tb.gif);
}

.x-box-tr {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-ml {
	background-image: url(../images/default/box/l.gif);
}

.x-box-mc {
	background-color: #eee;
    background-image: url(../images/default/box/tb.gif);
	font-family: "Myriad Pro","Myriad Web","Tahoma","Helvetica","Arial",sans-serif;
	color: #393939;
	font-size: 12px;
}

.x-box-mc h3 {
	font-size: 14px;
	font-weight: bold;
}

.x-box-mr {
	background-image: url(../images/default/box/r.gif);
}

.x-box-bl {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-bc {
	background-image: url(../images/default/box/tb.gif);
}

.x-box-br {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-blue .x-box-bl, .x-box-blue .x-box-br, .x-box-blue .x-box-tl, .x-box-blue .x-box-tr {
	background-image: url(../images/default/box/corners-blue.gif);
}

.x-box-blue .x-box-bc, .x-box-blue .x-box-mc, .x-box-blue .x-box-tc {
	background-image: url(../images/default/box/tb-blue.gif);
}

.x-box-blue .x-box-mc {
	background-color: #c3daf9;
}

.x-box-blue .x-box-mc h3 {
	color: #17385b;
}

.x-box-blue .x-box-ml {
	background-image: url(../images/default/box/l-blue.gif);
}

.x-box-blue .x-box-mr {
	background-image: url(../images/default/box/r-blue.gif);
}
.x-combo-list {
    border-color:#ccc;
    background-color:#ddd;
    font:normal 12px tahoma, arial, helvetica, sans-serif;
}

.x-combo-list-inner {
    background-color:#fff;
}

.x-combo-list-hd {
    font:bold 11px tahoma, arial, helvetica, sans-serif;
    color:#333;
    background-image: url(../images/default/layout/panel-title-light-bg.gif);
    border-bottom-color:#BCBCBC;
}

.x-resizable-pinned .x-combo-list-inner {
    border-bottom-color:#BEBEBE;
}

.x-combo-list-item {
    border-color:#fff;
}

.x-combo-list .x-combo-selected{
	border-color:#777 !important;
    background-color:#f0f0f0;
}

.x-combo-list .x-toolbar {
    border-top-color:#BCBCBC;
}

.x-combo-list-small {
    font:normal 11px tahoma, arial, helvetica, sans-serif;
}.x-panel {
    border-color: #d0d0d0;
}

.x-panel-header {
    color:#333;
	font-weight:bold; 
    font-size: 11px;
    font-family: tahoma,arial,verdana,sans-serif;
    border-color:#d0d0d0;
    background-image: url(../images/gray/panel/white-top-bottom.gif);
}

.x-panel-body {
    border-color:#d0d0d0;
    background-color:#fff;
}

.x-panel-bbar .x-toolbar, .x-panel-tbar .x-toolbar {
    border-color:#d0d0d0;
}

.x-panel-tbar-noheader .x-toolbar, .x-panel-mc .x-panel-tbar .x-toolbar {
    border-top-color:#d0d0d0;
}

.x-panel-body-noheader, .x-panel-mc .x-panel-body {
    border-top-color:#d0d0d0;
}

.x-panel-tl .x-panel-header {
    color:#333;
	font:bold 11px tahoma,arial,verdana,sans-serif;
}

.x-panel-tc {
	background-image: url(../images/gray/panel/top-bottom.gif);
}

.x-panel-tl, .x-panel-tr, .x-panel-bl,  .x-panel-br{
	background-image: url(../images/gray/panel/corners-sprite.gif);
    border-bottom-color:#d0d0d0;
}

.x-panel-bc {
	background-image: url(../images/gray/panel/top-bottom.gif);
}

.x-panel-mc {
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    background-color:#f1f1f1;
}

.x-panel-ml {
	background-color: #fff;
    background-image:url(../images/gray/panel/left-right.gif);
}

.x-panel-mr {
	background-image: url(../images/gray/panel/left-right.gif);
}

.x-tool {
    background-image:url(../images/gray/panel/tool-sprites.gif);
}

.x-panel-ghost {
    background-color:#f2f2f2;
}

.x-panel-ghost ul {
    border-color:#d0d0d0;
}

.x-panel-dd-spacer {
    border-color:#d0d0d0;
}

.x-panel-fbar td,.x-panel-fbar span,.x-panel-fbar input,.x-panel-fbar div,.x-panel-fbar select,.x-panel-fbar label{
    font:normal 11px arial,tahoma, helvetica, sans-serif;
}
.x-window-proxy {
    background-color:#fcfcfc;
    border-color:#d0d0d0;
}

.x-window-tl .x-window-header {
    color:#555;
	font:bold 11px tahoma,arial,verdana,sans-serif;
}

.x-window-tc {
	background-image: url(../images/gray/window/top-bottom.png);
}

.x-window-tl {
	background-image: url(../images/gray/window/left-corners.png);
}

.x-window-tr {
	background-image: url(../images/gray/window/right-corners.png);
}

.x-window-bc {
	background-image: url(../images/gray/window/top-bottom.png);
}

.x-window-bl {
	background-image: url(../images/gray/window/left-corners.png);
}

.x-window-br {
	background-image: url(../images/gray/window/right-corners.png);
}

.x-window-mc {
    border-color:#d0d0d0;
    font: normal 11px tahoma,arial,helvetica,sans-serif;
    background-color:#e8e8e8;
}

.x-window-ml {
	background-image: url(../images/gray/window/left-right.png);
}

.x-window-mr {
	background-image: url(../images/gray/window/left-right.png);
}

.x-window-maximized .x-window-tc {
    background-color:#fff;
}

.x-window-bbar .x-toolbar {
    border-top-color:#d0d0d0;
}

.x-panel-ghost .x-window-tl {
    border-bottom-color:#d0d0d0;
}

.x-panel-collapsed .x-window-tl {
    border-bottom-color:#d0d0d0;
}

.x-dlg-mask{
   background-color:#ccc;
}

.x-window-plain .x-window-mc {
    background-color: #E8E8E8;
    border-color: #D0D0D0 #EEEEEE #EEEEEE #D0D0D0;
}

.x-window-plain .x-window-body {
    border-color: #EEEEEE #D0D0D0 #D0D0D0 #EEEEEE;
}

body.x-body-masked .x-window-plain .x-window-mc {
    background-color: #E4E4E4;
}
.x-html-editor-wrap {
    border-color:#BCBCBC;
    background-color:#fff;
}
.x-html-editor-tb .x-btn-text {
    background-image:url(../images/default/editor/tb-sprite.gif);
}
.x-panel-noborder .x-panel-header-noborder {
    border-bottom-color:#d0d0d0;
}

.x-panel-noborder .x-panel-tbar-noborder .x-toolbar {
    border-bottom-color:#d0d0d0;
}

.x-panel-noborder .x-panel-bbar-noborder .x-toolbar {
    border-top-color:#d0d0d0;
}

.x-tab-panel-bbar-noborder .x-toolbar {
    border-top-color:#d0d0d0;
}

.x-tab-panel-tbar-noborder .x-toolbar {
    border-bottom-color:#d0d0d0;
}

.x-border-layout-ct {
    background-color:#f0f0f0;
}
.x-border-layout-ct {
    background-color:#f0f0f0;
}

.x-accordion-hd {
	color:#222;
    font-weight:normal;
    background-image: url(../images/gray/panel/light-hd.gif);
}

.x-layout-collapsed{
    background-color:#dfdfdf;
	border-color:#d0d0d0;
}

.x-layout-collapsed-over{
    background-color:#e7e7e7;
}

.x-layout-split-west .x-layout-mini {
    background-image:url(../images/default/layout/mini-left.gif);
}
.x-layout-split-east .x-layout-mini {
    background-image:url(../images/default/layout/mini-right.gif);
}
.x-layout-split-north .x-layout-mini {
    background-image:url(../images/default/layout/mini-top.gif);
}
.x-layout-split-south .x-layout-mini {
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-west .x-layout-mini {
    background-image:url(../images/default/layout/mini-right.gif);
}

.x-layout-cmini-east .x-layout-mini {
    background-image:url(../images/default/layout/mini-left.gif);
}

.x-layout-cmini-north .x-layout-mini {
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-south .x-layout-mini {
    background-image:url(../images/default/layout/mini-top.gif);
}
.x-progress-wrap {
    border-color:#8E8E8E;
}

.x-progress-inner {
    background-color:#E7E7E7;
    background-image:url(../images/gray/qtip/bg.gif);
}

.x-progress-bar {
    background-color:#BCBCBC;
    background-image:url(../images/gray/progress/progress-bg.gif);
    border-top-color:#E2E2E2;
    border-bottom-color:#A4A4A4;
    border-right-color:#A4A4A4;
}

.x-progress-text {
    font-size:11px;
    font-weight:bold;
    color:#fff;
}

.x-progress-text-back {
    color:#5F5F5F;
}
.x-list-header{
    background-color:#f9f9f9;
	background-image:url(../images/gray/grid/grid3-hrow2.gif);
}

.x-list-header-inner div em {
    border-left-color:#ddd;
    font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.x-list-body dt em {
    font:normal 11px arial, tahoma, helvetica, sans-serif;
}

.x-list-over {
    background-color:#eee;
}

.x-list-selected {
    background-color:#f0f0f0;
}

.x-list-resizer {
    border-left-color:#555;
    border-right-color:#555;
}

.x-list-header-inner em.sort-asc, .x-list-header-inner em.sort-desc {
    background-image:none;
    border-color: #d0d0d0;
}
.x-slider-horz, .x-slider-horz .x-slider-end, .x-slider-horz .x-slider-inner {
    background-image:url(../images/default/slider/slider-bg.png);
}

.x-slider-horz .x-slider-thumb {
    background-image:url(../images/gray/slider/slider-thumb.png);
}

.x-slider-vert, .x-slider-vert .x-slider-end, .x-slider-vert .x-slider-inner {
    background-image:url(../images/default/slider/slider-v-bg.png);
}

.x-slider-vert .x-slider-thumb {
    background-image:url(../images/gray/slider/slider-v-thumb.png);
}
.x-window-dlg .ext-mb-text,
.x-window-dlg .x-window-header-text {
    font-size:12px;
}

.x-window-dlg .ext-mb-textarea {
    font:normal 12px tahoma,arial,helvetica,sans-serif;
}

.x-window-dlg .x-msg-box-wait {
    background-image:url(../images/default/grid/loading.gif);
}

.x-window-dlg .ext-mb-info {
    background-image:url(../images/gray/window/icon-info.gif);
}

.x-window-dlg .ext-mb-warning {
    background-image:url(../images/gray/window/icon-warning.gif);
}

.x-window-dlg .ext-mb-question {
    background-image:url(../images/gray/window/icon-question.gif);
}

.x-window-dlg .ext-mb-error {
    background-image:url(../images/gray/window/icon-error.gif);
}
