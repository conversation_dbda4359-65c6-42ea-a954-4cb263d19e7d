/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-grid3 {
    background-color:#1f2933;
}

.x-grid-panel .x-panel-mc .x-panel-body {
    border-color:#223;
}

.x-grid3-hd-row td, .x-grid3-row td, .x-grid3-summary-row td{
	font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-grid3-hd-row td {
    border-left-color:#556;
    border-right-color:#223;
}

.x-grid-row-loading {
    background-color: #fff;
    background-image:url(../images/default/shared/loading-balls.gif);
}

.x-grid3-row {
    border:0 none;
    border-bottom:1px solid #111;
    border-right:1px solid #1a1a1c;
}

.x-grid3-row-alt{
	background-color:#1b232b;
}

.x-grid3-row-over {
    background-color:#7e5530;
}

.x-grid3-resize-proxy {
    background-color:#777;
}

.x-grid3-resize-marker {
    background-color:#777;
}

.x-grid3-header{
    background-color:#3b3f50;
	background-image:url(../images/access/grid/grid3-hrow.gif);
}

.x-grid3-header-pop {
    border-left-color:#d0d0d0;
}

.x-grid3-header-pop-inner {
    border-left-color:#eee;
    background-image:url(../images/default/grid/hd-pop.gif);
}

td.x-grid3-hd-over, td.sort-desc, td.sort-asc, td.x-grid3-hd-menu-open {
    border-left-color:#889;
    border-right-color:#445;
}

td.x-grid3-hd-over .x-grid3-hd-inner, td.sort-desc .x-grid3-hd-inner, td.sort-asc .x-grid3-hd-inner, td.x-grid3-hd-menu-open .x-grid3-hd-inner {
    background-color:#4e628a;
    background-image:url(../images/access/grid/grid3-hrow-over.gif);
}

.x-grid3-cell-inner, .x-grid3-hd-inner {
    color:#fff;
}

.sort-asc .x-grid3-sort-icon {
	background-image: url(../images/access/grid/sort_asc.gif);
	width:15px;
	height:9px;
	margin-left:5px;
}

.sort-desc .x-grid3-sort-icon {
	background-image: url(../images/access/grid/sort_desc.gif);
	width:15px;
	height:9px;
	margin-left:5px;
}

.x-grid3-cell-text, .x-grid3-hd-text {
	color:#fff;
}

.x-grid3-split {
	background-image: url(../images/default/grid/grid-split.gif);
}

.x-grid3-hd-text {
	color:fff;
}

.x-dd-drag-proxy .x-grid3-hd-inner{
    background-color:#ebf3fd;
	background-image:url(../images/access/grid/grid3-hrow-over.gif);
	border-color:#aaccf6;
}

.col-move-top{
	background-image:url(../images/default/grid/col-move-top.gif);
}

.col-move-bottom{
	background-image:url(../images/default/grid/col-move-bottom.gif);
}

.x-grid3-row-selected {
	background-color: #e5872c !important;
	background-image: none;
	border-style: solid;
}

.x-grid3-row-selected .x-grid3-cell {
    color: #fff;
}

.x-grid3-cell-selected {
	background-color: #ffa340 !important;
	color:#fff;
}

.x-grid3-cell-selected span{
	color:#fff !important;
}

.x-grid3-cell-selected .x-grid3-cell-text{
	color:#fff;
}

.x-grid3-locked td.x-grid3-row-marker, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker{
    background-color:#ebeadb !important;
    background-image:url(../images/default/grid/grid-hrow.gif) !important;
    color:#fff;
    border-top-color:#fff;
    border-right-color:#6fa0df !important;
}

.x-grid3-locked td.x-grid3-row-marker div, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker div{
    color:fff !important;
}

.x-grid3-dirty-cell {
    background-image:url(../images/access/grid/dirty.gif);
}

.x-grid3-topbar, .x-grid3-bottombar{
	font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-grid3-bottombar .x-toolbar{
	border-top-color:#a9bfd3;
}

.x-props-grid .x-grid3-td-name .x-grid3-cell-inner{
	background-image:url(../images/access/grid/grid3-special-col-bg.gif) !important;
    color:#fff !important;
}
.x-props-grid .x-grid3-td-value {
    color:#fff !important;
}

.x-props-grid .x-grid3-body .x-grid3-td-name{
    background-color:#263240 !important;
    border-right-color:#223;
}

.xg-hmenu-sort-asc .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-asc.gif);
}

.xg-hmenu-sort-desc .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-desc.gif);
}

.xg-hmenu-lock .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-lock.gif);
}

.xg-hmenu-unlock .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-unlock.gif);
}

.x-grid3-hd-btn {
    background-color:#c2c9d0;
    background-image:url(../images/access/grid/grid3-hd-btn.gif);
}

.x-grid3-body .x-grid3-td-expander {
    background-image:url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-row-expander {
    background-image:url(../images/access/grid/row-expand-sprite.gif);
}

.x-grid3-body .x-grid3-td-checker {
    background-image: url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-row-checker, .x-grid3-hd-checker {
    background-image:url(../images/default/grid/row-check-sprite.gif);
}

.x-grid3-body .x-grid3-td-numberer {
    background-image:url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-body .x-grid3-td-numberer .x-grid3-cell-inner {
	color:#fff;
}

.x-grid3-body .x-grid3-td-row-icon {
    background-image:url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-body .x-grid3-row-selected .x-grid3-td-numberer,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-checker,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-expander {
	background-image:url(../images/access/grid/grid3-special-col-sel-bg.gif);
}

.x-grid3-check-col {
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-grid3-check-col-on {
	background-image:url(../images/default/menu/checked.gif);
}

.x-grid-group, .x-grid-group-body, .x-grid-group-hd {
    zoom:1;
}

.x-grid-group-hd {
    border-bottom-color:#4e628a;
}

.x-grid-group-hd div.x-grid-group-title {
    background-image:url(../images/access/grid/group-collapse.gif);
    background-position:3px 6px;
    color:#ffd;
    font:bold 14px tahoma, arial, helvetica, sans-serif;
}

.x-grid-group-collapsed .x-grid-group-hd div.x-grid-group-title {
    background-image:url(../images/access/grid/group-expand.gif);
}

.x-group-by-icon {
    background-image:url(../images/default/grid/group-by.gif);
}

.x-cols-icon {
    background-image:url(../images/default/grid/columns.gif);
}

.x-show-groups-icon {
    background-image:url(../images/default/grid/group-by.gif);
}

.x-grid-empty {
    color:gray;
    font:normal 14px tahoma, arial, helvetica, sans-serif;
}

.x-grid-with-col-lines .x-grid3-row td.x-grid3-cell {
    border-right-color:#ededed;
}

.x-grid-with-col-lines .x-grid3-row{
    border-top-color:#ededed;
}

.x-grid-with-col-lines .x-grid3-row-selected {
	border-top-color:#a3bae9;
}
