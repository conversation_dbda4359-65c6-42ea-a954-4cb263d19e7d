/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-dd-drag-proxy{
	position:absolute;
	left:0;
    top:0;
	visibility:hidden;
	z-index:15000;
}

.x-dd-drag-ghost{
	-moz-opacity: 0.85;
    opacity:.85;
    filter: alpha(opacity=85);
    border: 1px solid;
	padding:3px;
	padding-left:20px;
	white-space:nowrap;
}

.x-dd-drag-repair .x-dd-drag-ghost{
	-moz-opacity: 0.4;
    opacity:.4;
    filter: alpha(opacity=40);
	border:0 none;
	padding:0;
	background-color:transparent;
}

.x-dd-drag-repair .x-dd-drop-icon{
	visibility:hidden;
}

.x-dd-drop-icon{
    position:absolute;
	top:3px;
	left:3px;
	display:block;
	width:16px;
	height:16px;
	background-color:transparent;
	background-position: center;
	background-repeat: no-repeat;
	z-index:1;
}

.x-view-selector {
    position:absolute;
    left:0;
    top:0;
    width:0;
    border:1px dotted;
	opacity: .5;
    -moz-opacity: .5;
    filter:alpha(opacity=50);
    zoom:1;
}