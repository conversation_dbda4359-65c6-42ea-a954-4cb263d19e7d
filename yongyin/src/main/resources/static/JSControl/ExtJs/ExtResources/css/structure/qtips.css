/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-tip{
	position: absolute;
	top: 0;
    left:0;
    visibility: hidden;
	z-index: 20002;
    border:0 none;
}

.x-tip .x-tip-close{
	height: 15px;
	float:right;
	width: 15px;
    margin:0 0 2px 2px;
    cursor:pointer;
    display:none;
}

.x-tip .x-tip-tc {
	background: transparent no-repeat 0 -62px;
	padding-top:3px;
    overflow:hidden;
    zoom:1;
}

.x-tip .x-tip-tl {
	background: transparent no-repeat 0 0;
	padding-left:6px;
    overflow:hidden;
    zoom:1;
}

.x-tip .x-tip-tr {
	background: transparent no-repeat right 0;
	padding-right:6px;
    overflow:hidden;
    zoom:1;
}

.x-tip .x-tip-bc {
	background: transparent no-repeat 0 -121px;
	height:3px;
    overflow:hidden;
}

.x-tip .x-tip-bl {
	background: transparent no-repeat 0 -59px;
	padding-left:6px;
    zoom:1;
}

.x-tip .x-tip-br {
	background: transparent no-repeat right -59px;
	padding-right:6px;
    zoom:1;
}

.x-tip .x-tip-mc {
    border:0 none;
}

.x-tip .x-tip-ml {
	background: no-repeat 0 -124px;
	padding-left:6px;
    zoom:1;
}

.x-tip .x-tip-mr {
	background: transparent no-repeat right -124px;
	padding-right:6px;
    zoom:1;
}

.ext-ie .x-tip .x-tip-header,.ext-ie .x-tip .x-tip-tc {
    font-size:0;
    line-height:0;
}

.ext-border-box .x-tip .x-tip-header, .ext-border-box .x-tip .x-tip-tc{
    line-height: 1px;
}

.x-tip .x-tip-header-text {
    padding:0;
    margin:0 0 2px 0;
}

.x-tip .x-tip-body {
    margin:0 !important;
    line-height:14px;
    padding:0;
}

.x-tip .x-tip-body .loading-indicator {
    margin:0;
}

.x-tip-draggable .x-tip-header,.x-tip-draggable .x-tip-header-text {
    cursor:move;
}

.x-form-invalid-tip .x-tip-tc {
	background: repeat-x 0 -12px;
    padding-top:6px;
}

.x-form-invalid-tip .x-tip-bc {
	background: repeat-x 0 -18px;
    height:6px;
}

.x-form-invalid-tip .x-tip-bl {
	background: no-repeat 0 -6px;
}

.x-form-invalid-tip .x-tip-br {
	background: no-repeat right -6px;
}

.x-form-invalid-tip .x-tip-body {
    padding:2px;
}

.x-form-invalid-tip .x-tip-body {
    padding-left:24px;
    background:transparent no-repeat 2px 2px;
}

.x-tip-anchor {
    position: absolute;
    width: 9px;
    height: 10px;
    overflow:hidden;
    background: transparent no-repeat 0 0;
    zoom:1;
}
.x-tip-anchor-bottom {
    background-position: -9px 0;
}
.x-tip-anchor-right {
    background-position: -18px 0;
    width: 10px;
}
.x-tip-anchor-left {
    background-position: -28px 0;
    width: 10px;
}