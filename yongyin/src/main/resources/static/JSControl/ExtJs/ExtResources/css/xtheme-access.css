/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
body {
	background-color:#16181a;
	color:#fcfcfc;
}

.ext-el-mask {
    background-color: #ccc;
}

.ext-el-mask-msg {
    border-color:#223;
    background-color:#3f4757;
    background-image:url(../images/access/box/tb-blue.gif);
}
.ext-el-mask-msg div {
    background-color: #232d38;
    border-color:#556;
    color:#fff;
    font:normal 14px tahoma, arial, helvetica, sans-serif;
}

.x-mask-loading div {
    background-color:#232d38;
    background-image:url(../images/access/grid/loading.gif);
}

.x-item-disabled {
    color: #ddd;
}

.x-item-disabled * {
    color: #ddd !important;
}

.x-splitbar-proxy {
    background-color: #aaa;
}

.x-color-palette a {
    border-color:#fff;
}

.x-color-palette a:hover, .x-color-palette a.x-color-palette-sel {
    border-color:#8bb8f3;
    background-color: #deecfd;
}

.x-color-palette em {
    border-color:#aca899;
}

.x-ie-shadow {
    background-color:#777;
}

.x-shadow .xsmc {
    background-image: url(../images/default/shadow-c.png);
}

.x-shadow .xsml, .x-shadow .xsmr {
    background-image: url(../images/default/shadow-lr.png);
}

.x-shadow .xstl, .x-shadow .xstc,  .x-shadow .xstr, .x-shadow .xsbl, .x-shadow .xsbc, .x-shadow .xsbr{
    background-image: url(../images/default/shadow.png);
}

.loading-indicator {
    font-size: 14px;
    background-image: url(../images/access/grid/loading.gif);
}

.x-spotlight {
    background-color: #ccc;
}.x-tab-panel-header, .x-tab-panel-footer {
	background-color:#e18325;
	border-color:#8db2e3;
    overflow:hidden;
    zoom:1;
}

.x-tab-panel-header, .x-tab-panel-footer {
	border-color:#222;
}

ul.x-tab-strip-top{
    background-color:#343843;
	background-image: url(../images/access/tabs/tab-strip-bg.gif);
	border-bottom-color:#343d4e;
}

ul.x-tab-strip-bottom{
    background-color:#343843;
	background-image: url(../images/access/tabs/tab-strip-btm-bg.gif);
	border-top-color:#343843;
}

.x-tab-panel-header-plain .x-tab-strip-spacer,
.x-tab-panel-footer-plain .x-tab-strip-spacer {
    border-color:#222;
    background-color:#e18325;
}

.x-tab-strip span.x-tab-strip-text {
	font:normal 14px tahoma,arial,helvetica;
	color:#fff;
}

.x-tab-strip-over span.x-tab-strip-text {
	color:#fff;
}

.x-tab-strip-active span.x-tab-strip-text {
	color:#fff;
    font-weight:bold;
}

.x-tab-strip-disabled .x-tabs-text {
	color:#aaaaaa;
}

.x-tab-strip-top .x-tab-right, .x-tab-strip-top .x-tab-left, .x-tab-strip-top .x-tab-strip-inner{
	background-image: url(../images/access/tabs/tabs-sprite.gif);
}

.x-tab-strip-bottom .x-tab-right {
	background-image: url(../images/access/tabs/tab-btm-inactive-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-left {
	background-image: url(../images/access/tabs/tab-btm-inactive-left-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-right {
	background-image: url(../images/access/tabs/tab-btm-right-bg.gif);
}

.x-tab-strip-bottom .x-tab-strip-active .x-tab-left {
	background-image: url(../images/access/tabs/tab-btm-left-bg.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close {
	background-image:url(../images/access/tabs/tab-close.gif);
}

.x-tab-strip .x-tab-strip-closable a.x-tab-strip-close:hover{
	background-image:url(../images/access/tabs/tab-close.gif);
}

.x-tab-panel-body {
    border-color:#18181a;
    background-color:#fff;
}

.x-tab-panel-body-top {
    border-top: 0 none;
}

.x-tab-panel-body-bottom {
    border-bottom: 0 none;
}

.x-tab-scroller-left {
    background-image:url(../images/access/tabs/scroll-left.gif);
    border-bottom-color:#8db2e3;
}

.x-tab-scroller-left-over {
    background-position: 0 0;
}

.x-tab-scroller-left-disabled {
    background-position: -18px 0;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
    cursor:default;
}

.x-tab-scroller-right {
    background-image:url(../images/access/tabs/scroll-right.gif);
    border-bottom-color:#8db2e3;
}

.x-tab-panel-bbar .x-toolbar, .x-tab-panel-tbar .x-toolbar {
    border-color:#99bbe8;
}
.x-form-field {
    font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-form-text, textarea.x-form-field{
    color: #ffffff;
    background-color:#33373d;
    background-image:url(../images/access/form/text-bg.gif);
    border-color:#737b8c;
    border-width:2px;
}

.ext-webkit .x-form-text, .ext-webkit textarea.x-form-field{
    border-width:2px;
}

.x-form-text, .ext-ie .x-form-file {
    height:26px;
}

.ext-strict .x-form-text {
    height:20px;
}

.x-form-select-one {
    background-color:#fff;
    border-color:#b5b8c8;
}

.x-form-check-group-label {
    border-bottom: 1px solid #99bbe8;
    color: #fff;
}

.x-editor .x-form-check-wrap {
    background-color:#fff;
}

.x-form-field-wrap .x-form-trigger{
    background-image:url(../images/access/form/trigger.gif);
    border-bottom-color:#737b8c;
    border-bottom-width:2px;
    height:24px;
    width:20px;
}

.x-form-field-wrap .x-form-trigger.x-form-trigger-over{
    border-bottom-color:#d97e27;
}

.x-form-field-wrap .x-form-trigger.x-form-trigger-click{
    border-bottom-color:#c86e19;
}

.x-small-editor .x-form-field-wrap .x-form-trigger {
    height:24px;
}

.x-form-field-wrap .x-form-trigger-over {
    background-position:-20px 0;
}

.x-form-field-wrap .x-form-trigger-click {
    background-position:-40px 0;
}

.x-trigger-wrap-focus .x-form-trigger {
    background-position:-60px 0;
}

.x-trigger-wrap-focus .x-form-trigger-over {
    background-position:-80px 0;
}

.x-trigger-wrap-focus .x-form-trigger-click {
    background-position:-100px 0;
}

.x-form-field-wrap .x-form-date-trigger{
    background-image: url(../images/access/form/date-trigger.gif);
}

.x-form-field-wrap .x-form-clear-trigger{
    background-image: url(../images/access/form/clear-trigger.gif);
}

.x-form-field-wrap .x-form-search-trigger{
    background-image: url(../images/access/form/search-trigger.gif);
}

.x-trigger-wrap-focus .x-form-trigger{
    border-bottom-color:#737b8c;
}

.x-item-disabled .x-form-trigger-over{
    border-bottom-color:#b5b8c8;
}

.x-item-disabled .x-form-trigger-click{
    border-bottom-color:#b5b8c8;
}

.x-form-focus, textarea.x-form-focus{
	border-color:#ff9c33;
}

.x-form-invalid, textarea.x-form-invalid,
.ext-webkit .x-form-invalid, .ext-webkit textarea.x-form-invalid{
    background-color:#15171a;
	background-image:url(../images/access/grid/invalid_line.gif);
	border-color:#c30;
}

/*
.ext-safari .x-form-invalid{
	background-color:#fee;
	border-color:#ff7870;
}
*/

.x-form-inner-invalid, textarea.x-form-inner-invalid{
    background-color:#fff;
	background-image:url(../images/access/grid/invalid_line.gif);
}

.x-form-grow-sizer {
	font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-form-item {
    font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-form-invalid-msg {
    color:#c0272b;
    font:normal 14px tahoma, arial, helvetica, sans-serif;
    background-image:url(../images/default/shared/warning.gif);
}

.x-form-empty-field {
    color:#dadadd;
}

.x-small-editor .x-form-text {
    height: 26px;
}

.x-small-editor .x-form-field {
    font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.ext-safari .x-small-editor .x-form-field {
    font:normal 15px arial, tahoma, helvetica, sans-serif;
}

.x-form-invalid-icon {
    background-image:url(../images/access/form/exclamation.gif);
    height:25px;
    width:19px;
    background-position:center right;
}

.x-fieldset {
    border-color:#737B8C;
}

.x-fieldset legend {
    font:bold 14px tahoma, arial, helvetica, sans-serif;
    color:#fff;
}
.x-btn {
	font:normal 14px tahoma, verdana, helvetica;
}

.x-btn button {
    font:normal 14px arial,tahoma,verdana,helvetica;
    color:#fffffa;
    padding-left:6px !important;
    padding-right:6px !important;
}

.x-btn-over .x-btn button{
    color:#fff;
}

.x-btn-noicon .x-btn-small .x-btn-text, .x-btn-text-icon .x-btn-icon-small-left .x-btn-text,
.x-btn-icon .x-btn-small .x-btn-text, .x-btn-text-icon .x-btn-icon-small-right .x-btn-text {
    height:18px;
}

.x-btn-icon .x-btn-small .x-btn-text {
    width:18px;
}

.x-btn-text-icon .x-btn-icon-small-left .x-btn-text {
    padding-left:21px !important;
}

.x-btn-text-icon .x-btn-icon-small-right .x-btn-text {
    padding-right:21px !important;
}

.x-btn-text-icon .x-btn-icon-medium-left .x-btn-text {
    padding-left:29px !important;
}

.x-btn-text-icon .x-btn-icon-medium-right .x-btn-text {
    padding-right:29px !important;
}

.x-btn-text-icon .x-btn-icon-large-left .x-btn-text {
    padding-left:37px !important;
}

.x-btn-text-icon .x-btn-icon-large-right .x-btn-text {
    padding-right:37px !important;
}

.x-btn em {
    font-style:normal;
    font-weight:normal;
}

.x-btn-tl, .x-btn-tr, .x-btn-tc, .x-btn-ml, .x-btn-mr, .x-btn-mc, .x-btn-bl, .x-btn-br, .x-btn-bc{
	background-image:url(../images/access/button/btn.gif);
}

.x-btn-click .x-btn-text, .x-btn-menu-active .x-btn-text, .x-btn-pressed .x-btn-text{
    color:#fff;
}

.x-btn-disabled *{
	color:#eee !important;
}

.x-btn-mc em.x-btn-arrow {
    background-image:url(../images/access/button/arrow.gif);
    padding-right:13px;
}

.x-btn-mc em.x-btn-split {
    background-image:url(../images/access/button/s-arrow.gif);
    padding-right:20px;
}

.x-btn-over .x-btn-mc em.x-btn-split, .x-btn-click .x-btn-mc em.x-btn-split, .x-btn-menu-active .x-btn-mc em.x-btn-split, .x-btn-pressed .x-btn-mc em.x-btn-split {
    background-image:url(../images/access/button/s-arrow-o.gif);
}

.x-btn-mc em.x-btn-arrow-bottom {
    background-image:url(../images/access/button/s-arrow-b-noline.gif);
}

.x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/access/button/s-arrow-b.gif);
}

.x-btn-over .x-btn-mc em.x-btn-split-bottom, .x-btn-click .x-btn-mc em.x-btn-split-bottom, .x-btn-menu-active .x-btn-mc em.x-btn-split-bottom, .x-btn-pressed .x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/access/button/s-arrow-bo.gif);
}

.x-btn-group-header {
    color: #d2d2d2;
}

.x-btn-group-tc {
	background-image: url(../images/access/button/group-tb.gif);
}

.x-btn-group-tl {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-tr {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-bc {
	background-image: url(../images/access/button/group-tb.gif);
}

.x-btn-group-bl {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-br {
	background-image: url(../images/access/button/group-cs.gif);
}

.x-btn-group-ml {
	background-image: url(../images/access/button/group-lr.gif);
}

.x-btn-group-mr {
	background-image: url(../images/access/button/group-lr.gif);
}

.x-btn-group-notitle .x-btn-group-tc {
	background-image: url(../images/access/button/group-tb.gif);
}
.x-toolbar{
	border-color:#18181a;
    background-color:#393d4e;
    background-image:url(../images/access/toolbar/bg.gif);
}

.x-toolbar td,.x-toolbar span,.x-toolbar input,.x-toolbar div,.x-toolbar select,.x-toolbar label{
    font:normal 14px arial,tahoma, helvetica, sans-serif;
}

.x-toolbar .x-item-disabled {
	color:gray;
}

.x-toolbar .x-item-disabled * {
	color:gray;
}

.x-toolbar .x-btn-mc em.x-btn-split {
    background-image:url(../images/access/button/s-arrow-noline.gif);
}

.x-toolbar .x-btn-over .x-btn-mc em.x-btn-split, .x-toolbar .x-btn-click .x-btn-mc em.x-btn-split, 
.x-toolbar .x-btn-menu-active .x-btn-mc em.x-btn-split, .x-toolbar .x-btn-pressed .x-btn-mc em.x-btn-split 
{
    background-image:url(../images/access/button/s-arrow-o.gif);
}

.x-toolbar .x-btn-mc em.x-btn-split-bottom {
    background-image:url(../images/access/button/s-arrow-b-noline.gif);
}

.x-toolbar .x-btn-over .x-btn-mc em.x-btn-split-bottom, .x-toolbar .x-btn-click .x-btn-mc em.x-btn-split-bottom, 
.x-toolbar .x-btn-menu-active .x-btn-mc em.x-btn-split-bottom, .x-toolbar .x-btn-pressed .x-btn-mc em.x-btn-split-bottom 
{
    background-image:url(../images/access/button/s-arrow-bo.gif);
}

.x-toolbar .xtb-sep {
	background-image: url(../images/access/grid/grid-blue-split.gif);
}

.x-toolbar .x-btn {
    padding-left:3px;
    padding-right:3px;
}

.x-toolbar .x-btn-mc em.x-btn-arrow {
    padding-right:10px;
}

.x-toolbar .x-btn-text-icon .x-btn-icon-small-left .x-btn-text {
    padding-left:18px !important;
}

.x-toolbar .x-btn-mc em.x-btn-split {
    padding-right:14px;
}

.x-tbar-page-first{
	background-image: url(../images/access/grid/page-first.gif) !important;
}

.x-tbar-loading{
	background-image: url(../images/access/grid/refresh.gif) !important;
}

.x-tbar-page-last{
	background-image: url(../images/access/grid/page-last.gif) !important;
}

.x-tbar-page-next{
	background-image: url(../images/access/grid/page-next.gif) !important;
}

.x-tbar-page-prev{
	background-image: url(../images/access/grid/page-prev.gif) !important;
}

.x-item-disabled .x-tbar-loading{
	background-image: url(../images/access/grid/loading.gif) !important;
}

.x-item-disabled .x-tbar-page-first{
	background-image: url(../images/access/grid/page-first-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-last{
	background-image: url(../images/access/grid/page-last-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-next{
	background-image: url(../images/access/grid/page-next-disabled.gif) !important;
}

.x-item-disabled .x-tbar-page-prev{
	background-image: url(../images/access/grid/page-prev-disabled.gif) !important;
}

.x-paging-info {
    color:#444;
}

.x-toolbar-more-icon {
    background-image: url(../images/access/toolbar/more.gif) !important;
}

.x-statusbar .x-status-busy {
    background-image: url(../images/access/grid/loading.gif);
}

.x-statusbar .x-status-text-panel {
    border-color: #99bbe8 #fff #fff #99bbe8;
}
.x-resizable-handle {
	background-color:#fff;
	color: #000;
}

.x-resizable-over .x-resizable-handle-east, .x-resizable-pinned .x-resizable-handle-east,
.x-resizable-over .x-resizable-handle-west, .x-resizable-pinned .x-resizable-handle-west
{
    background-image:url(../images/access/sizer/e-handle.gif);
}

.x-resizable-over .x-resizable-handle-south, .x-resizable-pinned .x-resizable-handle-south,
.x-resizable-over .x-resizable-handle-north, .x-resizable-pinned .x-resizable-handle-north
{
    background-image:url(../images/access/sizer/s-handle.gif);
}

.x-resizable-over .x-resizable-handle-north, .x-resizable-pinned .x-resizable-handle-north{
    background-image:url(../images/access/sizer/s-handle.gif);
}
.x-resizable-over .x-resizable-handle-southeast, .x-resizable-pinned .x-resizable-handle-southeast{
    background-image:url(../images/access/sizer/se-handle.gif);
}
.x-resizable-over .x-resizable-handle-northwest, .x-resizable-pinned .x-resizable-handle-northwest{
    background-image:url(../images/access/sizer/nw-handle.gif);
}
.x-resizable-over .x-resizable-handle-northeast, .x-resizable-pinned .x-resizable-handle-northeast{
    background-image:url(../images/access/sizer/ne-handle.gif);
}
.x-resizable-over .x-resizable-handle-southwest, .x-resizable-pinned .x-resizable-handle-southwest{
    background-image:url(../images/access/sizer/sw-handle.gif);
}
.x-resizable-proxy{
    border-color:#3b5a82;
}
.x-resizable-overlay{
    background-color:#fff;
}
.x-grid3 {
    background-color:#1f2933;
}

.x-grid-panel .x-panel-mc .x-panel-body {
    border-color:#223;
}

.x-grid3-hd-row td, .x-grid3-row td, .x-grid3-summary-row td{
	font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-grid3-hd-row td {
    border-left-color:#556;
    border-right-color:#223;
}

.x-grid-row-loading {
    background-color: #fff;
    background-image:url(../images/default/shared/loading-balls.gif);
}

.x-grid3-row {
    border:0 none;
    border-bottom:1px solid #111;
    border-right:1px solid #1a1a1c;
}

.x-grid3-row-alt{
	background-color:#1b232b;
}

.x-grid3-row-over {
    background-color:#7e5530;
}

.x-grid3-resize-proxy {
    background-color:#777;
}

.x-grid3-resize-marker {
    background-color:#777;
}

.x-grid3-header{
    background-color:#3b3f50;
	background-image:url(../images/access/grid/grid3-hrow.gif);
}

.x-grid3-header-pop {
    border-left-color:#d0d0d0;
}

.x-grid3-header-pop-inner {
    border-left-color:#eee;
    background-image:url(../images/default/grid/hd-pop.gif);
}

td.x-grid3-hd-over, td.sort-desc, td.sort-asc, td.x-grid3-hd-menu-open {
    border-left-color:#889;
    border-right-color:#445;
}

td.x-grid3-hd-over .x-grid3-hd-inner, td.sort-desc .x-grid3-hd-inner, td.sort-asc .x-grid3-hd-inner, td.x-grid3-hd-menu-open .x-grid3-hd-inner {
    background-color:#4e628a;
    background-image:url(../images/access/grid/grid3-hrow-over.gif);
}

.x-grid3-cell-inner, .x-grid3-hd-inner {
    color:#fff;
}

.sort-asc .x-grid3-sort-icon {
	background-image: url(../images/access/grid/sort_asc.gif);
	width:15px;
	height:9px;
	margin-left:5px;
}

.sort-desc .x-grid3-sort-icon {
	background-image: url(../images/access/grid/sort_desc.gif);
	width:15px;
	height:9px;
	margin-left:5px;
}

.x-grid3-cell-text, .x-grid3-hd-text {
	color:#fff;
}

.x-grid3-split {
	background-image: url(../images/default/grid/grid-split.gif);
}

.x-grid3-hd-text {
	color:fff;
}

.x-dd-drag-proxy .x-grid3-hd-inner{
    background-color:#ebf3fd;
	background-image:url(../images/access/grid/grid3-hrow-over.gif);
	border-color:#aaccf6;
}

.col-move-top{
	background-image:url(../images/default/grid/col-move-top.gif);
}

.col-move-bottom{
	background-image:url(../images/default/grid/col-move-bottom.gif);
}

.x-grid3-row-selected {
	background-color: #e5872c !important;
	background-image: none;
	border-style: solid;
}

.x-grid3-row-selected .x-grid3-cell {
    color: #fff;
}

.x-grid3-cell-selected {
	background-color: #ffa340 !important;
	color:#fff;
}

.x-grid3-cell-selected span{
	color:#fff !important;
}

.x-grid3-cell-selected .x-grid3-cell-text{
	color:#fff;
}

.x-grid3-locked td.x-grid3-row-marker, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker{
    background-color:#ebeadb !important;
    background-image:url(../images/default/grid/grid-hrow.gif) !important;
    color:#fff;
    border-top-color:#fff;
    border-right-color:#6fa0df !important;
}

.x-grid3-locked td.x-grid3-row-marker div, .x-grid3-locked .x-grid3-row-selected td.x-grid3-row-marker div{
    color:fff !important;
}

.x-grid3-dirty-cell {
    background-image:url(../images/access/grid/dirty.gif);
}

.x-grid3-topbar, .x-grid3-bottombar{
	font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-grid3-bottombar .x-toolbar{
	border-top-color:#a9bfd3;
}

.x-props-grid .x-grid3-td-name .x-grid3-cell-inner{
	background-image:url(../images/access/grid/grid3-special-col-bg.gif) !important;
    color:#fff !important;
}
.x-props-grid .x-grid3-td-value {
    color:#fff !important;
}

.x-props-grid .x-grid3-body .x-grid3-td-name{
    background-color:#263240 !important;
    border-right-color:#223;
}

.xg-hmenu-sort-asc .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-asc.gif);
}

.xg-hmenu-sort-desc .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-desc.gif);
}

.xg-hmenu-lock .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-lock.gif);
}

.xg-hmenu-unlock .x-menu-item-icon{
	background-image: url(../images/access/grid/hmenu-unlock.gif);
}

.x-grid3-hd-btn {
    background-color:#c2c9d0;
    background-image:url(../images/access/grid/grid3-hd-btn.gif);
}

.x-grid3-body .x-grid3-td-expander {
    background-image:url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-row-expander {
    background-image:url(../images/access/grid/row-expand-sprite.gif);
}

.x-grid3-body .x-grid3-td-checker {
    background-image: url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-row-checker, .x-grid3-hd-checker {
    background-image:url(../images/default/grid/row-check-sprite.gif);
}

.x-grid3-body .x-grid3-td-numberer {
    background-image:url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-body .x-grid3-td-numberer .x-grid3-cell-inner {
	color:#fff;
}

.x-grid3-body .x-grid3-td-row-icon {
    background-image:url(../images/access/grid/grid3-special-col-bg.gif);
}

.x-grid3-body .x-grid3-row-selected .x-grid3-td-numberer,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-checker,
.x-grid3-body .x-grid3-row-selected .x-grid3-td-expander {
	background-image:url(../images/access/grid/grid3-special-col-sel-bg.gif);
}

.x-grid3-check-col {
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-grid3-check-col-on {
	background-image:url(../images/default/menu/checked.gif);
}

.x-grid-group, .x-grid-group-body, .x-grid-group-hd {
    zoom:1;
}

.x-grid-group-hd {
    border-bottom-color:#4e628a;
}

.x-grid-group-hd div.x-grid-group-title {
    background-image:url(../images/access/grid/group-collapse.gif);
    background-position:3px 6px;
    color:#ffd;
    font:bold 14px tahoma, arial, helvetica, sans-serif;
}

.x-grid-group-collapsed .x-grid-group-hd div.x-grid-group-title {
    background-image:url(../images/access/grid/group-expand.gif);
}

.x-group-by-icon {
    background-image:url(../images/default/grid/group-by.gif);
}

.x-cols-icon {
    background-image:url(../images/default/grid/columns.gif);
}

.x-show-groups-icon {
    background-image:url(../images/default/grid/group-by.gif);
}

.x-grid-empty {
    color:gray;
    font:normal 14px tahoma, arial, helvetica, sans-serif;
}

.x-grid-with-col-lines .x-grid3-row td.x-grid3-cell {
    border-right-color:#ededed;
}

.x-grid-with-col-lines .x-grid3-row{
    border-top-color:#ededed;
}

.x-grid-with-col-lines .x-grid3-row-selected {
	border-top-color:#a3bae9;
}
.x-dd-drag-ghost{
	color:#000;
	font: normal 14px arial, helvetica, sans-serif;
    border-color: #ddd #bbb #bbb #ddd;
	background-color:#fff;
}

.x-dd-drop-nodrop .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-no.gif);
}

.x-dd-drop-ok .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-yes.gif);
}

.x-dd-drop-ok-add .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-add.gif);
}

.x-view-selector {
    background-color:#c3daf9;
    border-color:#3399bb;
}
.x-tree-node-expanded .x-tree-node-icon{
	background-image:url(../images/access/tree/folder-open.gif);
}

.x-tree-node-leaf .x-tree-node-icon{
	background-image:url(../images/default/tree/leaf.gif);
}

.x-tree-node-collapsed .x-tree-node-icon{
	background-image:url(../images/access/tree/folder.gif);
}

.x-tree-node-loading .x-tree-node-icon{
	background-image:url(../images/default/tree/loading.gif) !important;
}

.x-tree-node .x-tree-node-inline-icon {
    background-image: none;
}

.x-tree-node-loading a span{
	 font-style: italic;
	 color:#444444;
}

.ext-ie .x-tree-node-el input {
    width:14px;
    height:14px;
}

.x-tree-lines .x-tree-elbow{
	background-image:url(../images/access/tree/elbow.gif);
}

.x-tree-lines .x-tree-elbow-plus{
	background-image:url(../images/access/tree/elbow-plus.gif);
}

.x-tree-lines .x-tree-elbow-minus{
	background-image:url(../images/access/tree/elbow-minus.gif);
}

.x-tree-lines .x-tree-elbow-end{
	background-image:url(../images/access/tree/elbow-end.gif);
}

.x-tree-lines .x-tree-elbow-end-plus{
	background-image:url(../images/access/tree/elbow-end-plus.gif);
}

.x-tree-lines .x-tree-elbow-end-minus{
	background-image:url(../images/access/tree/elbow-end-minus.gif);
}

.x-tree-lines .x-tree-elbow-line{
	background-image:url(../images/access/tree/elbow-line.gif);
}

.x-tree-no-lines .x-tree-elbow-plus{
	background-image:url(../images/access/tree/elbow-plus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-minus{
	background-image:url(../images/access/tree/elbow-minus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-end-plus{
	background-image:url(../images/access/tree/elbow-end-plus-nl.gif);
}

.x-tree-no-lines .x-tree-elbow-end-minus{
	background-image:url(../images/access/tree/elbow-end-minus-nl.gif);
}

.x-tree-arrows .x-tree-elbow-plus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-minus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-end-plus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-arrows .x-tree-elbow-end-minus{
    background-image:url(../images/access/tree/arrows.gif);
}

.x-tree-node{
	color:#000;
	font: normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-tree-node a, .x-dd-drag-ghost a{
	color:#fff;
}

.x-tree-node a span, .x-dd-drag-ghost a span{
	color:#fff;
}

.x-tree-node .x-tree-selected a, .x-dd-drag-ghost a{
	color:#fff;
}

.x-tree-node .x-tree-selected a span, .x-dd-drag-ghost a span{
	color:#fff;
}

.x-tree-node .x-tree-node-disabled a span{
	color:gray !important;
}

.x-tree-node div.x-tree-drag-insert-below{
 	 border-bottom-color:#36c;
}

.x-tree-node div.x-tree-drag-insert-above{
	 border-top-color:#36c;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-below a{
 	 border-bottom-color:#36c;
}

.x-tree-dd-underline .x-tree-node div.x-tree-drag-insert-above a{
	 border-top-color:#36c;
}

.x-tree-node .x-tree-drag-append a span{
	 background-color:#ddd;
	 border-color:gray;
}

.x-tree-node .x-tree-node-over {
	background-color: #7e5530;
}

.x-tree-node .x-tree-selected {
	background-color: #e5872c;
}

.x-tree-drop-ok-append .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-add.gif);
}

.x-tree-drop-ok-above .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-over.gif);
}

.x-tree-drop-ok-below .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-under.gif);
}

.x-tree-drop-ok-between .x-dd-drop-icon{
  background-image: url(../images/default/tree/drop-between.gif);
}
.x-date-picker {
    border-color: #737b8c;
    background-color:#21252e;
}

.x-date-middle,.x-date-left,.x-date-right {
	background-image: url(../images/access/shared/hd-sprite.gif);
	color:#fff;
	font:bold 14px "sans serif", tahoma, verdana, helvetica;
}

.x-date-middle .x-btn .x-btn-text {
    color:#fff;
}

.x-date-middle .x-btn-mc em.x-btn-arrow {
    background-image:url(../images/access/toolbar/btn-arrow-light.gif);
}

.x-date-right a {
    background-image: url(../images/access/shared/right-btn.gif);
}

.x-date-left a{
	background-image: url(../images/access/shared/left-btn.gif);
}

.x-date-inner th {
    background-color:#363d4a;
    background-image:url(../images/access/toolbar/bg.gif);
	border-bottom-color:#535b5c;
    font:normal 13px arial, helvetica,tahoma,sans-serif;
	color:#fff;
}

.x-date-inner td {
    border-color:#112;
}

.x-date-inner a {
    font:normal 14px arial, helvetica,tahoma,sans-serif;
    color:#fff;
    padding:2px 7px 1px 3px; /* Structure to account for larger, bolder fonts in Access theme. */
}

.x-date-inner .x-date-active{
	color:#000;
}

.x-date-inner .x-date-selected a{
    background-color:#e5872c;
	background-image:none;
	border-color:#864900;
    padding:1px 6px 1px 2px; /* Structure to account for larger, bolder fonts in Access theme. */
}

.x-date-inner .x-date-today a{
	border-color:#99a;
}

.x-date-inner .x-date-selected span{
    font-weight:bold;
}

.x-date-inner .x-date-prevday a,.x-date-inner .x-date-nextday a {
	color:#aaa;
}

.x-date-bottom {
    border-top-color:#737b8c;
    background-color:#464d5a;
    background-image:url(../images/access/shared/glass-bg.gif);
}

.x-date-inner a:hover, .x-date-inner .x-date-disabled a:hover{
    color:#fff;
    background-color:#7e5530;
}

.x-date-inner .x-date-disabled a {
	background-color:#eee;
	color:#bbb;
}

.x-date-mmenu{
    background-color:#eee !important;
}

.x-date-mmenu .x-menu-item {
	font-size:13px;
	color:#000;
}

.x-date-mp {
	background-color:#21252e;
}

.x-date-mp td {
	font:normal 14px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns button {
	background-color:#083772;
	color:#fff;
	border-color: #3366cc #000055 #000055 #3366cc;
	font:normal 14px arial, helvetica,tahoma,sans-serif;
}

.x-date-mp-btns {
    background-color: #dfecfb;
	background-image: url(../images/access/shared/glass-bg.gif);
}

.x-date-mp-btns td {
	border-top-color: #c5d2df;
}

td.x-date-mp-month a,td.x-date-mp-year a {
	color:#fff;
}

td.x-date-mp-month a:hover,td.x-date-mp-year a:hover {
	color:fff;
	background-color: #7e5530;
}

td.x-date-mp-sel a {
    background-color: #e5872c;
	background-image: none;
	border-color:#864900;
}

.x-date-mp-ybtn a {
    background-image:url(../images/access/panel/tool-sprites.gif);
}

td.x-date-mp-sep {
   border-right-color:#c5d2df;
}
.x-tip .x-tip-close{
	background-image: url(../images/access/qtip/close.gif);
}

.x-tip .x-tip-tc, .x-tip .x-tip-tl, .x-tip .x-tip-tr, .x-tip .x-tip-bc, .x-tip .x-tip-bl, .x-tip .x-tip-br, .x-tip .x-tip-ml, .x-tip .x-tip-mr {
	background-image: url(../images/access/qtip/tip-sprite.gif);
}

.x-tip .x-tip-mc {
    font: normal 14px tahoma,arial,helvetica,sans-serif;
}
.x-tip .x-tip-ml {
	background-color: #fff;
}

.x-tip .x-tip-header-text {
    font: bold 14px tahoma,arial,helvetica,sans-serif;
    color:#ffd;
}

.x-tip .x-tip-body {
    font: normal 14px tahoma,arial,helvetica,sans-serif;
    color:#000;
}

.x-form-invalid-tip .x-tip-tc, .x-form-invalid-tip .x-tip-tl, .x-form-invalid-tip .x-tip-tr, .x-form-invalid-tip .x-tip-bc,
.x-form-invalid-tip .x-tip-bl, .x-form-invalid-tip .x-tip-br, .x-form-invalid-tip .x-tip-ml, .x-form-invalid-tip .x-tip-mr
{
	background-image: url(../images/default/form/error-tip-corners.gif);
}

.x-form-invalid-tip .x-tip-body {
    background-image:url(../images/access/form/exclamation.gif);
}

.x-tip-anchor {
    background-image:url(../images/access/qtip/tip-anchor-sprite.gif);
}
.x-menu {
	border-color:#222;
    background-color:#414551;
	background-image:url(../images/access/menu/menu.gif);
}

.x-menu-nosep {
	background-image:none;
}

.x-menu-list-item{
	font:normal 14px tahoma,arial, sans-serif;
}

.x-menu-item-arrow{
	background-image:url(../images/access/menu/menu-parent.gif);
}

.x-menu-sep {
    background-color:#223;
	border-bottom-color:#666;
}

a.x-menu-item {
	color:#fffff6;
}

.x-menu-item-active {
	background-color: #f09134;
	background-image: none;
    border-color:#b36427;
}

.x-menu-item-active a.x-menu-item {
	border-color:#b36427;
}

.x-menu-check-item .x-menu-item-icon{
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/default/menu/checked.gif);
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background-image:url(../images/access/menu/group-checked.gif);
}

.x-menu-group-item .x-menu-item-icon{
    background-image:none;
}

.x-menu-plain {
	background-color:#fff !important;
}

.x-menu .x-date-picker{
    border-color:#a3bad9;
}

.x-cycle-menu .x-menu-item-checked {
    border-color:#a3bae9 !important;
    background-color:#def8f6;
}

.x-menu-scroller-top {
    background-image:url(../images/default/layout/mini-top.gif);
}

.x-menu-scroller-bottom {
    background-image:url(../images/default/layout/mini-bottom.gif);
}
.x-box-tl {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-tc {
	background-image: url(../images/default/box/tb.gif);
}

.x-box-tr {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-ml {
	background-image: url(../images/default/box/l.gif);
}

.x-box-mc {
	background-color: #eee;
    background-image: url(../images/default/box/tb.gif);
	font-family: "Myriad Pro","Myriad Web","Tahoma","Helvetica","Arial",sans-serif;
	color: #393939;
	font-size: 15px;
}

.x-box-mc h3 {
	font-size: 18px;
	font-weight: bold;
}

.x-box-mr {
	background-image: url(../images/default/box/r.gif);
}

.x-box-bl {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-bc {
	background-image: url(../images/default/box/tb.gif);
}

.x-box-br {
	background-image: url(../images/default/box/corners.gif);
}

.x-box-blue .x-box-bl, .x-box-blue .x-box-br, .x-box-blue .x-box-tl, .x-box-blue .x-box-tr {
	background-image: url(../images/default/box/corners-blue.gif);
}

.x-box-blue .x-box-bc, .x-box-blue .x-box-mc, .x-box-blue .x-box-tc {
	background-image: url(../images/default/box/tb-blue.gif);
}

.x-box-blue .x-box-mc {
	background-color: #c3daf9;
}

.x-box-blue .x-box-mc h3 {
	color: #17385b;
}

.x-box-blue .x-box-ml {
	background-image: url(../images/default/box/l-blue.gif);
}

.x-box-blue .x-box-mr {
	background-image: url(../images/default/box/r-blue.gif);
}
.x-combo-list {
    border:2px solid #232732;
    background-color:#555566;
    font:normal 15px tahoma, arial, helvetica, sans-serif;
}

.x-combo-list-inner {
    background-color:#414551;
}

.x-combo-list-hd {
    font:bold 14px tahoma, arial, helvetica, sans-serif;
    color:fff;
    background-image: url(../images/default/layout/panel-title-light-bg.gif);
    border-bottom-color:#98c0f4;
}

.x-resizable-pinned .x-combo-list-inner {
    border-bottom-color:#98c0f4;
}

.x-combo-list-item {
    border-color:#556;
}

.x-combo-list .x-combo-selected {
	border-color:#e5872c !important;
    background-color:#e5872c;
}

.x-combo-list .x-toolbar {
    border-top-color:#98c0f4;
}

.x-combo-list-small {
    font:normal 14px tahoma, arial, helvetica, sans-serif;
}
.x-panel {
    border-color: #18181a;
    font-size: 14px;
}

.x-panel-header {
    color:#fff;
	font-weight:bold; 
    font-size: 14px;
    font-family: tahoma,arial,verdana,sans-serif;
    border-color:#18181a;
    background-image: url(../images/access/panel/white-top-bottom.gif);
}

.x-panel-body {
    color: #fffff6;
    border-color:#18181a;
    background-color:#232d38;
}

.x-tab-panel .x-panel-body {
    color: #fffff6;
    border-color:#18181a;
    background-color:#1f2730;
}

.x-panel-bbar .x-toolbar, .x-panel-tbar .x-toolbar {
    border-color:#223;
}

.x-panel-tbar-noheader .x-toolbar, .x-panel-mc .x-panel-tbar .x-toolbar {
    border-top-color:#223;
}

.x-panel-body-noheader, .x-panel-mc .x-panel-body {
    border-top-color:#223;
}

.x-panel-tl .x-panel-header {
    color:fff;
	font:bold 14px tahoma,arial,verdana,sans-serif;
}

.x-panel-tc {
	background-image: url(../images/access/panel/top-bottom.gif);
}

.x-panel-tl, .x-panel-tr, .x-panel-bl,  .x-panel-br{
	background-image: url(../images/access/panel/corners-sprite.gif);
    border-bottom-color:#222224;
}

.x-panel-bc {
	background-image: url(../images/access/panel/top-bottom.gif);
}

.x-panel-mc {
    font: normal 14px tahoma,arial,helvetica,sans-serif;
    background-color:#3f4757;
}

.x-panel-ml {
    background-image:url(../images/access/panel/left-right.gif);
}

.x-panel-mr {
	background-image: url(../images/access/panel/left-right.gif);
}

.x-tool {
    background-image:url(../images/access/panel/tool-sprites.gif);
}

.x-panel-ghost {
    background-color:#3f4757;
}

.x-panel-ghost ul {
    border-color:#18181a;
}

.x-panel-dd-spacer {
    border-color:#18181a;
}

.x-panel-fbar td,.x-panel-fbar span,.x-panel-fbar input,.x-panel-fbar div,.x-panel-fbar select,.x-panel-fbar label{
    font:normal 14px arial,tahoma, helvetica, sans-serif;
}
.x-window-proxy {
    background-color:#1f2833;
    border-color:#18181a;
}

.x-window-tl .x-window-header {
    color:fff;
	font:bold 14px tahoma,arial,verdana,sans-serif;
}

.x-window-tc {
	background-image: url(../images/access/window/top-bottom.png);
}

.x-window-tl {
	background-image: url(../images/access/window/left-corners.png);
}

.x-window-tr {
	background-image: url(../images/access/window/right-corners.png);
}

.x-window-bc {
	background-image: url(../images/access/window/top-bottom.png);
}

.x-window-bl {
	background-image: url(../images/access/window/left-corners.png);
}

.x-window-br {
	background-image: url(../images/access/window/right-corners.png);
}

.x-window-mc {
    border-color:#18181a;
    font: normal 14px tahoma,arial,helvetica,sans-serif;
    background-color:#1f2833;
}

.x-window-ml {
	background-image: url(../images/access/window/left-right.png);
}

.x-window-mr {
	background-image: url(../images/access/window/left-right.png);
}

.x-window-maximized .x-window-tc {
    background-color:#fff;
}

.x-window-bbar .x-toolbar {
    border-top-color:#323945;
}

.x-panel-ghost .x-window-tl {
    border-bottom-color:#323945;
}

.x-panel-collapsed .x-window-tl {
    border-bottom-color:#323945;
}

.x-dlg-mask{
   background-color:#ccc;
}

.x-window-plain .x-window-mc {
    background-color: #464f61;
    border-color: #636778;
}

.x-window-plain .x-window-body {
    color: #fffff6;
    border-color: #464F61;
}

body.x-body-masked .x-window-plain .x-window-mc {
    background-color: #464f61;
}
.x-html-editor-wrap {
    border-color:#737B8C;
    background-color:#fff;
}
.x-html-editor-wrap iframe {
    background-color: #fff;
}
.x-html-editor-tb .x-btn-text {
    background-image:url(../images/access/editor/tb-sprite.gif);
}.x-panel-noborder .x-panel-header-noborder {
    border-bottom-color:#343d4e;
}

.x-panel-noborder .x-panel-tbar-noborder .x-toolbar {
    border-bottom-color:#343d4e;
}

.x-panel-noborder .x-panel-bbar-noborder .x-toolbar {
    border-top-color:#343d4e;
}

.x-tab-panel-bbar-noborder .x-toolbar {
    border-top-color:#343d4e;
}

.x-tab-panel-tbar-noborder .x-toolbar {
    border-bottom-color:#343d4e;
}
.x-border-layout-ct {
    background-color:#3f4757;
}

.x-accordion-hd {
	color:#fff;
    font-weight:normal;
    background-image: url(../images/access/panel/light-hd.gif);
}

.x-layout-collapsed{
    background-color:#323845;
	border-color:#1a1a1c;
}

.x-layout-collapsed-over{
    background-color:#2d3440;
}

.x-layout-split-west .x-layout-mini {
    background-image:url(../images/default/layout/mini-left.gif);
}

.x-layout-split-east .x-layout-mini {
    background-image:url(../images/default/layout/mini-right.gif);
}

.x-layout-split-north .x-layout-mini {
    background-image:url(../images/default/layout/mini-top.gif);
}

.x-layout-split-south .x-layout-mini {
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-west .x-layout-mini {
    background-image:url(../images/default/layout/mini-right.gif);
}

.x-layout-cmini-east .x-layout-mini {
    background-image:url(../images/default/layout/mini-left.gif);
}

.x-layout-cmini-north .x-layout-mini {
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-south .x-layout-mini {
    background-image:url(../images/default/layout/mini-top.gif);
}
.x-progress-wrap {
    border-color:#18181a;
}

.x-progress-inner {
    background-color:#232d38;
    background-image:none;
}

.x-progress-bar {
    background-color:#f39a00;
    background-image:url(../images/access/progress/progress-bg.gif);
    border-top-color:#a66900;
    border-bottom-color:#a66900;
    border-right-color:#ffb941;
    height: 20px !important; /* structural override for Accessibility Theme */
}

.x-progress-text {
    font-size:14px;
    font-weight:bold;
    color:#fff;
    padding: 0 5px !important; /* structural override for Accessibility Theme */
}

.x-progress-text-back {
    color:#aaa;
    line-height: 19px;
}
.x-list-header{
    background-color:#393d4e;
	background-image:url(../images/access/toolbar/bg.gif);
	background-position:0 top;
}

.x-list-header-inner div em {
    border-left-color:#667;
    font:normal 14px arial, tahoma, helvetica, sans-serif;
    line-height: 14px;
}

.x-list-body dt em {
    font:normal 14px arial, tahoma, helvetica, sans-serif;
}

.x-list-over {
    background-color:#7E5530;
}

.x-list-selected {
    background-color:#E5872C;
}

.x-list-resizer {
    border-left-color:#555;
    border-right-color:#555;
}

.x-list-header-inner em.sort-asc, .x-list-header-inner em.sort-desc {
    background-image:url(../images/access/grid/sort-hd.gif);
    border-color: #3e4e6c;
}
.x-slider-horz, .x-slider-horz .x-slider-end, .x-slider-horz .x-slider-inner {
    background-image:url(../images/access/slider/slider-bg.png);
}

.x-slider-horz .x-slider-thumb {
    background-image:url(../images/access/slider/slider-thumb.png);
}

.x-slider-vert, .x-slider-vert .x-slider-end, .x-slider-vert .x-slider-inner {
    background-image:url(../images/access/slider/slider-v-bg.png);
}

.x-slider-vert .x-slider-thumb {
    background-image:url(../images/access/slider/slider-v-thumb.png);
}
.x-window-dlg .ext-mb-text,
.x-window-dlg .x-window-header-text {
    font-size:15px;
}

.x-window-dlg .ext-mb-textarea {
    font:normal 15px tahoma,arial,helvetica,sans-serif;
}

.x-window-dlg .x-msg-box-wait {
    background-image:url(../images/access/grid/loading.gif);
}

.x-window-dlg .ext-mb-info {
    background-image:url(../images/access/window/icon-info.gif);
}

.x-window-dlg .ext-mb-warning {
    background-image:url(../images/access/window/icon-warning.gif);
}

.x-window-dlg .ext-mb-question {
    background-image:url(../images/access/window/icon-question.gif);
}

.x-window-dlg .ext-mb-error {
    background-image:url(../images/access/window/icon-error.gif);
}
