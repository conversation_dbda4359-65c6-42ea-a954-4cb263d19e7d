/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
.x-menu {
    background-color:#f0f0f0;
	background-image:url(../images/default/menu/menu.gif);
}

.x-menu-floating{
    border-color:#7D7D7D;
}

.x-menu-nosep {
	background-image:none;
}

.x-menu-list-item{
	font:normal 11px arial,tahoma,sans-serif;
}

.x-menu-item-arrow{
	background-image:url(../images/gray/menu/menu-parent.gif);
}

.x-menu-sep {
    background-color:#e0e0e0;
	border-bottom-color:#fff;
}

a.x-menu-item {
	color:#222;
}

.x-menu-item-active {
    background-image: url(../images/gray/menu/item-over.gif);
	background-color: #f1f1f1;
    border-color:#ACACAC;
}

.x-menu-item-active a.x-menu-item {
	border-color:#ACACAC;
}

.x-menu-check-item .x-menu-item-icon{
	background-image:url(../images/default/menu/unchecked.gif);
}

.x-menu-item-checked .x-menu-item-icon{
	background-image:url(../images/default/menu/checked.gif);
}

.x-menu-item-checked .x-menu-group-item .x-menu-item-icon{
    background-image:url(../images/gray/menu/group-checked.gif);
}

.x-menu-group-item .x-menu-item-icon{
    background-image:none;
}

.x-menu-plain {
	background-color:#fff !important;
}

.x-menu .x-date-picker{
    border-color:#AFAFAF;
}

.x-cycle-menu .x-menu-item-checked {
    border-color:#B9B9B9 !important;
    background-color:#F1F1F1;
}

.x-menu-scroller-top {
    background-image:url(../images/default/layout/mini-top.gif);
}

.x-menu-scroller-bottom {
    background-image:url(../images/default/layout/mini-bottom.gif);
}