/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-dd-drag-proxy{
	position:absolute;
	left:0;top:0;
	visibility:hidden;
	z-index:15000;
}
.x-dd-drag-ghost{
	color: black;
	font: normal 11px arial, helvetica, sans-serif;
	-moz-opacity: 0.85;
   opacity:.85;
   filter: alpha(opacity=85);
	border-top:1px solid #dddddd;
	border-left:1px solid #dddddd;
	border-right:1px solid #bbbbbb;
	border-bottom:1px solid #bbbbbb;
	padding:3px;
	padding-left:20px;
	background-color:white;
	white-space:nowrap;
}
.x-dd-drag-repair .x-dd-drag-ghost{
	-moz-opacity: 0.4;
   opacity:.4;
   filter: alpha(opacity=40);
	border:0 none;
	padding:0;
	background-color:transparent;
}
.x-dd-drag-repair .x-dd-drop-icon{
	visibility:hidden;
}
.x-dd-drop-icon{
  position:absolute;
	top:3px;
	left:3px;
	display:block;
	width:16px;
	height:16px;
	background-color:transparent;
	background-position: center;
	background-repeat: no-repeat;
	z-index:1;
}
.x-dd-drop-nodrop .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-no.gif);
}
.x-dd-drop-ok .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-yes.gif);
}
.x-dd-drop-ok-add .x-dd-drop-icon{
  background-image: url(../images/default/dd/drop-add.gif);
}


.x-view-selector {
    position:absolute;
    left:0;
    top:0;
    width:0;
    background:#c3daf9;
    border:1px dotted #3399bb;
	opacity: .5;
    -moz-opacity: .5;
    filter:alpha(opacity=50);
    zoom:1;
}