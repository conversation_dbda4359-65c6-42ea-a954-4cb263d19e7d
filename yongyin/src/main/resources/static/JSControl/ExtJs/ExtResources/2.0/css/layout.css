/*
 * Ext JS Library 2.0.2
 * Copyright(c) 2006-2008, Ext JS, LLC.
 * <EMAIL>
 * 
 * http://extjs.com/license
 */

.x-border-layout-ct {
    background:#dfe8f6;
}
.x-border-panel {
    position:absolute;
    left:0;
    top:0;
}

.x-tool-collapse-south {
    background-position:0 -195px;
}
.x-tool-collapse-south-over {
    background-position:-15px -195px;
}

.x-tool-collapse-north {
    background-position:0 -210px;
}
.x-tool-collapse-north-over {
    background-position:-15px -210px;
}

.x-tool-collapse-west {
    background-position:0 -180px;
}
.x-tool-collapse-west-over {
    background-position:-15px -180px;
}

.x-tool-collapse-east {
    background-position:0 -165px;
}
.x-tool-collapse-east-over {
    background-position:-15px -165px;
}


.x-tool-expand-south {
    background-position:0 -210px;
}
.x-tool-expand-south-over {
    background-position:-15px -210px;
}

.x-tool-expand-north {
    background-position:0 -195px;
}
.x-tool-expand-north-over {
    background-position:-15px -195px;
}

.x-tool-expand-west {
    background-position:0 -165px;
}
.x-tool-expand-west-over {
    background-position:-15px -165px;
}

.x-tool-expand-east {
    background-position:0 -180px;
}
.x-tool-expand-east-over {
    background-position:-15px -180px;
}

.x-tool-expand-north, .x-tool-expand-south {
    float:right;
    margin:3px;
}
.x-tool-expand-east, .x-tool-expand-west {
    float:none;
    margin:3px auto;
}



.x-accordion-hd .x-tool-toggle {
    background-position:0 -255px;
}
.x-accordion-hd .x-tool-toggle-over {
    background-position:-15px -255px;
}
.x-panel-collapsed .x-accordion-hd .x-tool-toggle {
    background-position:0 -240px;
}
.x-panel-collapsed .x-accordion-hd .x-tool-toggle-over {
    background-position:-15px -240px;
}

.x-accordion-hd {
	color:#222;
	padding-top:4px;
	padding-bottom:3px;
	border-top:0 none;
    font-weight:normal;
    background: transparent url(../images/default/panel/light-hd.gif) repeat-x 0 -9px;
}

.x-layout-collapsed{
    position:absolute;
    left:-10000px;
    top:-10000px;
    visibility:hidden;
    background-color:#d2e0f2;
    width:20px;
    height:20px;
    overflow:hidden;
	border:1px solid #98c0f4;
	z-index:20;
}
.ext-border-box .x-layout-collapsed{
    width:22px;
    height:22px;
}
.x-layout-collapsed-over{
    cursor:pointer;
	 background-color:#d9e8fb;
}
.x-layout-collapsed-west .x-layout-collapsed-tools, .x-layout-collapsed-east .x-layout-collapsed-tools{
	position:absolute;
    top:0;
    left:0;
    width:20px;
    height:20px;
}


.x-layout-split{
    position:absolute;
    height:5px;
    width:5px;
    line-height:1px;
    font-size:1px;
    z-index:3;
    background-color:transparent;
}

.x-layout-split-h{
    background-image:url(../images/default/s.gif);
    background-position: left;
}
.x-layout-split-v{
    background-image:url(../images/default/s.gif);
    background-position: top;
}

.x-column-layout-ct {
    overflow:hidden;
    /*padding:3px 3px 3px 3px;*/
    zoom:1;
}

.x-column {
    float:left;
    padding:0;
    margin:0;
    overflow:hidden;
    zoom:1;
    /*margin:3px;*/
}

/* mini mode */

.x-layout-mini {
    position:absolute;
    top:0;
    left:0;
    display:block;
    width:5px;
    height:35px;
    cursor:pointer;
    opacity:.5;
    -moz-opacity:.5;
    filter:alpha(opacity=50);
}
.x-layout-mini-over, .x-layout-collapsed-over .x-layout-mini{
    opacity:1;
    -moz-opacity:1;
    filter:none;
}

.x-layout-split-west .x-layout-mini {
    top:48%;
    background-image:url(../images/default/layout/mini-left.gif);
}
.x-layout-split-east .x-layout-mini {
    top:48%;
    background-image:url(../images/default/layout/mini-right.gif);
}
.x-layout-split-north .x-layout-mini {
    left:48%;
    height:5px;
    width:35px;
    background-image:url(../images/default/layout/mini-top.gif);
}
.x-layout-split-south .x-layout-mini {
    left:48%;
    height:5px;
    width:35px;
    background-image:url(../images/default/layout/mini-bottom.gif);
}


.x-layout-cmini-west .x-layout-mini {
    top:48%;
    background-image:url(../images/default/layout/mini-right.gif);
}

.x-layout-cmini-east .x-layout-mini {
    top:48%;
    background-image:url(../images/default/layout/mini-left.gif);
}

.x-layout-cmini-north .x-layout-mini {
    left:48%;
    height:5px;
    width:35px;
    background-image:url(../images/default/layout/mini-bottom.gif);
}

.x-layout-cmini-south .x-layout-mini {
    left:48%;
    height:5px;
    width:35px;
    background-image:url(../images/default/layout/mini-top.gif);
}

.x-layout-cmini-west, .x-layout-cmini-east {
    border:0 none;
    width:5px !important;
    padding:0;
    background:transparent;
}

.x-layout-cmini-north, .x-layout-cmini-south {
    border:0 none;
    height:5px !important;
    padding:0;
    background:transparent;
}

.x-viewport, .x-viewport body {
    margin: 0;
    padding: 0;
    border: 0 none;
    overflow: hidden;
    height: 100%;
}

.x-abs-layout-item {
    position:absolute;
    left:0;
    top:0;
}

.ext-ie input.x-abs-layout-item, .ext-ie textarea.x-abs-layout-item {
    margin:0;
}