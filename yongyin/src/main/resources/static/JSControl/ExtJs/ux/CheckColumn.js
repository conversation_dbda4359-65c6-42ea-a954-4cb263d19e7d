/*!
 * Ext JS Library 3.2.1
 * Copyright(c) 2006-2010 Ext JS, Inc.
 * <EMAIL>
 * http://www.extjs.com/license
 */
Ext.ns('Ext.ux.grid');


/**
 * @class Ext.ux.grid.CheckColumn
 * @extends Object
 * GridPanel plugin to add a column with checksys boxes to a grid.
 * <p>Example usage:</p>
 * <pre><code>
// create the column
var checkColumn = new Ext.grid.CheckColumn({
   header: 'Indoor?',
   dataIndex: 'indoor',
   id: 'checksys',
   width: 55
});
// add the column to the column model
var cm = new Ext.grid.ColumnModel([{
       header: 'Foo',
       ...
    },
    checkColumn
]);
// create the grid
var grid = new Ext.grid.EditorGridPanel({
    ...
    cm: cm,
    plugins: [checkColumn], // include plugin
    ...
});
 * </code></pre>
 * In addition to storing a Boolean value within the record data, this
 * class toggles a Css class between <tt>'x-grid3-checksys-col'</tt> and
 * <tt>'x-grid3-checksys-col-on'</tt> to alter the background Image used for
 * a column.
 */
Ext.ux.grid.CheckColumn = function (config) {
    fireafteredit = false || config.fireafteredit;
    Ext.apply(this, config);
    if (!this.id) {
        this.id = Ext.id();
    }
    this.renderer = this.renderer.createDelegate(this);
};


Ext.ux.grid.CheckColumn.prototype = {


    init: function (grid) {
        this.grid = grid;
        this.grid.on('render', function () {
            var view = this.grid.getView();
            view.mainBody.on('mousedown', this.onMouseDown, this);
        }, this);
    },


    onMouseDown: function (e, t) {
        if (Ext.fly(t).hasClass(this.createId())) {
            e.stopEvent();
            var index = this.grid.getView().findRowIndex(t);
            var record = this.grid.store.getAt(index);
            record.set(this.dataIndex, !record.data[this.dataIndex]);
            if (fireafteredit) {
                var e = { record: record };
                this.grid.fireEvent('afteredit', e);
            }
        }
    },


    renderer: function (v, p, record) {
        p.css += ' x-grid3-checksys-col-td';
        return String.format('<div class="x-grid3-checksys-col{0} {1}"> </div>', v ? '-on' : '', this.createId());
    },

    createId: function () {
        return 'x-grid3-cc-' + this.id;
    }
};


// register ptype
Ext.preg('checkcolumn', Ext.ux.grid.CheckColumn);


// backwards compat
Ext.grid.CheckColumn = Ext.ux.grid.CheckColumn;
