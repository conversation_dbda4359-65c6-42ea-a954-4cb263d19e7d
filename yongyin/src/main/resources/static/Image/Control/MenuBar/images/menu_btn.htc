<implements type="Behavior">

<attach event="onmouseover" handler="ShowBorder"/>
<attach event="onmouseout" handler="HideBorder"/>

</implements>

<script language="jscript">
var clrOld;
function ShowBorder()
{
	style.borderStyle="solid";
	style.borderWidth='1px';
	clrOld=style.color;
	style.color="blue";
}
function HideBorder()
{
	style.borderStyle="none";
	style.borderWidth='0px';
	style.color=clrOld;
}

</script>
