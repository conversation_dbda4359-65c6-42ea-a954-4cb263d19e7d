import UrlConfig from './config.js'
//默认Url加载配置，允许多个后台API
import Request from './request.js'
const apiRequest = new Request({
	processNoAccess:function(res){},
	processAuthorizationHeader:function(access) {
		let fbtoken = uni.getStorageSync('fbtoken')
		if (_.isEmpty(fbtoken) && !access) {
			uni.showToast3s({
				title: '分包服务异常',
				icon: 'none',
				duration: 1000
			})
			return {}
		} else {
			return {
				'token': fbtoken,
			}
		}
		return;
	},
	// 请求默认StatusCode处理方法
	processHttpStatusCode: function(statusCode) {
		return true;
	},
	// 请求默认StatusCode处理方法
	processResultHandle: function(res) {
		return true
	},
	baseUrl:UrlConfig.FBUrl   //默认，如有需要请自行修改
})
let fbserver = apiRequest.request.bind(apiRequest);
export default fbserver;