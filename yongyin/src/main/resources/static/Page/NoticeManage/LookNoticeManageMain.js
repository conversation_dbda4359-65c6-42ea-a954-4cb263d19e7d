Ext.namespace('NoticeManage.LookNoticeManageMain');
//初始化函数
NoticeManage.LookNoticeManageMain.Initialize = function() {


    //初始数据
    var start = 0;
    var limit = 25;
    var pageSize = limit;

    //文件类别表单定义

    var PublishCombo = new Ext.form.ComboBox({
        id: 'NoticeManage.LookNoticeManageMain.LookNoticeForm.PublishCombo',
        name: 'PublishGroupName',
        fieldLabel: '是否集团显示',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'Content',
        displayField: 'Content',
        hiddenName: 'PublishGroupName',
        anchor: '98%',
        editable: false,
        allowBlank: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: _netsvrurl+'Service/SystemManage/DictionaryValueService.ashx?Method=GetDictionaryValueById&ParentId=61',
            fields: [
         'ID',
         'Content'
        ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    LookNoticeForm = new Ext.FormPanel({
        id: 'NoticeManage.LookNoticeManageMain.LookNoticeForm.ExtFileUploadForm',
        labelWidth: 55,
        width: 400,
        frame: true,
        fileUpload: true,
        bodyStyle: 'padding:5px 5px 0',
        items: [
            {
                id: 'NoticeManage.LookNoticeManageMain.LookNoticeForm.Title',
                xtype: 'textfield',
                vtype: 'customtextfield',
                fieldLabel: '标题',
                name: 'Title',
                anchor: '98%'
            },
           {
               id: 'NoticeManage.LookNoticeManageMain.LookNoticeForm.TContent',
               xtype: 'textarea',
               //               vtype: 'customtextfield',
               fieldLabel: '内容',
               name: 'TContent',
               anchor: '98%'
           } 
  ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
            { name: 'Title', mapping: 'Title', type: 'string' },
            { name: 'TContent', mapping: 'TContent', type: 'string' },
            { name: 'PublishGroupName', mapping: 'PublishGroupName', type: 'string'}]
        })
    });

    LookNoticeFormWindow = new Ext.Window({
        id: 'NoticeManage.LookNoticeManageMain.IdYJFormWindow',
        width: 417,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: true,
        items: LookNoticeForm,
        title: '表单',
        buttons: [{
            text: '保存',
            disabled: true
        },
                  {
                      text: '关闭',
                      handler: function() {
                          LookNoticeFormWindow.hide();
                      }
                  }
          ]
    });



    var toolbar = new Ext.Toolbar({
        items: [ 
     {
         text: '查看',
         PermissionNo: '240201',
         iconCls: 'ButtonFormLook'
         ,
         handler: onLookButtonClick
     } 

    ]
 });

 //========================================================附件界面--=================================
 //文件列表定义
 var fileGrid = new Ext.grid.GridPanel({
 id: 'NoticeManage.LookNoticeManageMain.Lc_HTTableFlowForm.ExtFileGrid',
     store: new Ext.data.Store({
         proxy: new Ext.data.HttpProxy({
             url: _netsvrurl+"Service/HTManage/T_FileService.ashx?Method=GetHTFileByIDList"
         }),
         reader: new Ext.data.JsonReader({
             root: 'Table',
             totalProperty: 'RecordCount',
             id: 'ID',
             fields: ['ID', 'FileName', 'FilePath', 'ProjectID', 'IsSecret']
         })
     }),
     columns: [
                new Ext.grid.RowNumberer(),
                { header: "文件名", width: 7, sortable: true, dataIndex: 'FileName', renderer: setFileDownloadColumn }
            ],

     loadMask: { msg: "加载中..." },
     stripeRows: true, title: '附件信息',
     //title: '备用设备入库记录列表',
     viewConfig: {
         forceFit: true
     },
     //layout: 'fit',
     sm: new Ext.grid.RowSelectionModel({ singleSelect: true })
     //tbar: tool//Ext.getCmp("ExtToolBar")//[{text:'新增'},'-',{text:'修改'},'-',{text:'删除'}]

 });
 function setFileDownloadColumn(val, metadata, record) {

     return '<a href="'+_netsvrurl+'../SystemManage/NewFileDownload.aspx?issrecet=0&flog=1&AttachName=' + record.data.FilePath + '&DownloadName=' + encodeURI(val) + '">' + val + '</a>';


 }
function onLookButtonClick() {
    var grid = Ext.getCmp("NoticeManage.LookNoticeManageMain.IdYJGrid");
    if (grid.getSelectionModel().getSelections()[0] == undefined) {
        Ext.MessageBox.alert('提示', '请选中列表行！');
    }
    else {
        var title = "查看";
        LookNoticeFormWindow.setTitle(title);

        LookNoticeForm.form.reset();
        var id = grid.getSelectionModel().getSelected().data.ID;
        var url = _netsvrurl+'Service/NoticeManage/NoticeManageService.ashx?Method=GetNoticeManageById&ID=' + id;
        LookNoticeFormWindow.show();
        LookNoticeForm.load({
            url: url,
            waitTitle: "请稍候",
            waitMsg: '正在读取数据...',
            success: function(form, action) {

            },
            failure: function(form, action) {
            }
        });
    };
}
var gridStore = new Ext.data.Store({
    proxy: new Ext.data.HttpProxy({
        url: _netsvrurl+"Service/NoticeManage/NoticeManageService.ashx?Method=GetLookNoticeManageList"
    }),
    reader: new Ext.data.JsonReader({
        root: 'Table',
        totalProperty: 'RecordCount',
        fields: ['ID', 'Title', 'TContent', 'MakeDate', 'PublishDate', 'PublishGroupName']
    })
});

var record_start = 0;
var grid = new Ext.grid.GridPanel({
    id: 'NoticeManage.LookNoticeManageMain.IdYJGrid',
    width: 350,
    store: gridStore,
    columns: [new Ext.grid.RowNumberer({ renderer: function(v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
       { header: "标题", width: 120, sortable: false, dataIndex: 'Title' },
         { header: "内容", width: 120, sortable: true, dataIndex: 'TContent' },
         { header: "编辑日期", width: 120, sortable: true, dataIndex: 'MakeDate'
, renderer: function(value, cellmeta, record, rowIdex, columnIndex, store) { return (value == null || value == "") ? "" : value.format('Y-m-d H:i'); }
         },
         { header: "发布日期", width: 120, sortable: true, dataIndex: 'PublishDate'
, renderer: function(value, cellmeta, record, rowIdex, columnIndex, store) { return (value == null || value == "") ? "" : value.format('Y-m-d H:i'); }
         },
         { header: "显示单位", width: 120, sortable: true, dataIndex: 'PublishGroupName' }

     ], loadMask: { msg: "加载中..." },
    stripeRows: true,
    title: '列表',
    viewConfig: {
        forceFit: true
    },
    sm: new Ext.grid.RowSelectionModel({ singleSelect: true }),
    tbar: toolbar,

    listeners:
    {
        "rowclick": function(grid, rowIndex, e) {
            setToolBar();
        },
        "rowdblclick": function(grid, rowIndex, e) {
            //        onLookButtonClick();
        }
    },

    bbar: new Ext.PagingToolbar({
        pageSize: pageSize,
        store: gridStore,
        displayInfo: true,
        displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
        emptyMsg: "没有记录",
        doLoad: function(start) {
            record_start = start;
            var o = {};
            o['start'] = start;
            o['limit'] = this.pageSize;
            this.store.load({ params: o });
        }
    })
});
grid.store.load({ params: {
    start: 0, limit: limit
}
});
function setToolBar() {
    Ext.getCmp("NoticeManage.LookNoticeManageMain.Lc_HTTableFlowForm.ExtFileGrid").store.load({ params: {
        ArticleID: grid.getSelectionModel().getSelections()[0].data.ID, FunctionID: "1303"
    }
    });
}
var tab2 = new Ext.FormPanel({
    region: 'east',
    labelWidth: 55,
    frame: true,
    collapsible: true,
    bodyStyle: 'padding:5px 5px 0',
    width: 500,
    items: [
            {

                region: 'center',
                width: 480,
                height: 800,
                layout: 'fit',
                items: [fileGrid]
            }
            ]
});
//设置页面布局
SetNewTabPanel(new Ext.Panel({
    layout: 'border',
    id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
    title: FrameMainTabContainer.ClickedTreeMenuNode.text,
    refreshfn:function() {
    },
    items: [
     tab2,
      {
          region: 'center',
          width: 400,
          layout: 'fit',
          items: [grid]
      }
      ]
}));
SetToolBarPermission(toolbar);
Ext.QuickTips.init();
};
NoticeManage.LookNoticeManageMain.Initialize();
