function WorkFlowMain(ownerStr) {
    var obj = this;

    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;

    //===================WorkFlow信息界面===========================start
    var WorkFlow_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: ownerStr + '.WorkFlow_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idlc_defineID',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'lc_defineID',
                fieldLabel: "lc_defineID字段",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdywID',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'ywID',
                fieldLabel: "ywID字段",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idlc_jdID',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'lc_jdID',
                fieldLabel: "lc_jdID字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idlc_jdmc',
                xtype: "textfield",
                name: 'lc_jdmc',
                fieldLabel: "lc_jdmc字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdgroupID',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'groupID',
                fieldLabel: "groupID字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdgroupName',
                xtype: "textfield",
                name: 'groupName',
                fieldLabel: "groupName字段",
                allowBlank: true,
                anchor: '98%'
            }, 
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdpersonZgh',
                xtype: "textfield",
                name: 'personZgh',
                fieldLabel: "personZgh字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdpersonName',
                xtype: "textfield",
                name: 'personName',
                fieldLabel: "personName字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idtransdate',
                xtype: "wmtextfield",
                name: 'transdate',
                readOnly: true,
                style: 'background-color: #DDDDDD; background-image: none;',
                fieldLabel: "transdate字段",
                allowBlank: true,
                anchor: '98%',
                listeners:
                 {
                     dblclick: function (thisobj, e) {
                         ShowEditFormForUpdate(thisobj);
                     }
                 }
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idfeed',
                xtype: "textfield",
                name: 'feed',
                fieldLabel: "feed字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idnumber',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'number',
                fieldLabel: "number字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdPNO',
                xtype: "textfield",
                name: 'PNO',
                fieldLabel: "PNO字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idstartdate',
                xtype: "wmtextfield",
                name: 'startdate',
                fieldLabel: "startdate字段",
                readOnly: true,
                style: 'background-color: #DDDDDD; background-image: none;',
                allowBlank: true,
                anchor: '98%',
                listeners:
                 {
                     dblclick: function (thisobj, e) {
                         ShowEditFormForUpdate(thisobj);
                     }
                 }
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.IdLcByRole',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'LcByRole',
                fieldLabel: "LcByRole字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Idisback',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'isback',
                fieldLabel: "isback字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.WorkFlow_Form.Iduseback',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'useback',
                fieldLabel: "useback字段",
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'string' },
                { name: 'lc_defineID', mapping: 'lc_defineID', type: 'string' },
                { name: 'ywID', mapping: 'ywID', type: 'string' },
                { name: 'lc_jdID', mapping: 'lc_jdID', type: 'string' },
                { name: 'lc_jdmc', mapping: 'lc_jdmc', type: 'string' },
                { name: 'groupID', mapping: 'groupID', type: 'string' },
                { name: 'groupName', mapping: 'groupName', type: 'string' },
                { name: 'personZgh', mapping: 'personZgh', type: 'string' },
                { name: 'personName', mapping: 'personName', type: 'string' },
                { name: 'transdate', mapping: 'transdate', type: 'string' },
                { name: 'feed', mapping: 'feed', type: 'string' },
                { name: 'number', mapping: 'number', type: 'string' },
                { name: 'BXType', mapping: 'BXType', type: 'string' },
                { name: 'PNO', mapping: 'PNO', type: 'string' },
                { name: 'startdate', startdate: 'BXType', type: 'string' },
                { name: 'LcByRole', startdate: 'LcByRole', type: 'string' },
                { name: 'isback', startdate: 'isback', type: 'string' },
                { name: 'useback', startdate: 'useback', type: 'string' }
            ]
        })
    });

    var WorkFlow_FormWindow = new Ext.Window({
        id: ownerStr + '.IdWorkFlow_FormWindow',
        width: 760,
        height: 560,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: WorkFlow_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    WorkFlow_FormWindow.hide();
                }
            }
        ]
     });


    function onAddButtonClick() {
        WorkFlow_Form.form.reset();

        WorkFlow_FormWindow.setTitle(ExtractTitleString("WorkFlow信息==新增WorkFlow信息=="));
        WorkFlow_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (WorkFlow_Form.form.isValid()) {
                WorkFlow_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/WorkFlow/AddWorkFlow',
                    method: 'post',
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        WorkFlow_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        WorkFlow_FormWindow.buttons[0].enable();
        WorkFlow_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdWorkFlow_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "WorkFlow信息==修改WorkFlow信息==";
            WorkFlow_FormWindow.setTitle(ExtractTitleString(title));
            WorkFlow_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = WorkFlow_Form.form.reader.jsonData.data[0].ID;
                if (WorkFlow_Form.form.isValid()) {
                    WorkFlow_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/WorkFlow/ModifyWorkFlow',
                        method: 'post',
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            WorkFlow_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            WorkFlow_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/WorkFlow/GetWorkFlowById?ID=' + id;
            WorkFlow_FormWindow.show();
            WorkFlow_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    WorkFlow_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdWorkFlow_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "WorkFlow信息==查看WorkFlow信息==";
            WorkFlow_FormWindow.setTitle(ExtractTitleString(title));
            WorkFlow_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/WorkFlow/GetWorkFlowById?ID=' + id;
            WorkFlow_FormWindow.show();
            WorkFlow_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    WorkFlow_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdWorkFlow_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                {
                    url: "../../Service/BaseOpt/WorkFlow/DeleteWorkFlow",
                    successfn: function (data) {
                        RefreshData();
                    },
                    failurefn: function (data) {
                    },
                    params: { ID: grid.getSelectionModel().getSelected().data.ID }
                });
                }
            });
        }
    }
    //===================WorkFlow信息界面===========================end  

    //===================WorkFlow信息管理form===========================start
    var LCCombo = new Ext.form.ComboBox({
        id: ownerStr + '.toolbar.IdlcName',
        name: 'lcName',
        fieldLabel: '流程名称',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'LcID',
        displayField: 'lcName',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/LCDBMan/GetLCList',
            fields: [
                 'LcID', 'lcName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(ownerStr + '.toolbar.IdlcName').setValue(rec.data.lcName);
                Ext.getCmp(ownerStr + '.toolbar.IdlcID').setValue(rec.data.LcID);
            }
        }
    });


    var toolbar1 = new Ext.Toolbar({
        items:
        [
            {
                text: '新增',
                iconCls: 'ButtonFormAdd',
                handler: onAddButtonClick
            },
            {
                text: '修改',
                iconCls: 'ButtonFormEdit',
                handler: onEditButtonClick
            },
            {
                text: '删除',
                iconCls: 'ButtonFormDelete',
                handler: onDeleteButtonClick
            },
            {
                text: '查看',
                iconCls: 'ButtonFormLook',
                handler: onLookButtonClick
            }
	     ]
    });

    var toolbar2 = new Ext.Toolbar({
        items:
        [
            '流程类型',
            LCCombo,
            {
                id: ownerStr + '.toolbar.IdlcID',
                xtype: 'hidden',
                width: 176
            },
            "业务编号",
            {
                id: ownerStr + '.toolbar.IdYwId',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    obj.LoadData();
                }
            },
            {
                text: '刷新',
                id: ownerStr + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        obj.RefreshData();
                    }
                }
            }
	    ]
     });

    this.RefreshData = function () {

        Ext.getCmp(ownerStr + ".IdWorkFlow_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            LcId: Ext.getCmp(ownerStr + '.toolbar.IdlcID').getValue(),
            YwId: Ext.getCmp(ownerStr + '.toolbar.IdYwId').getValue() 
        }
        });
    }

    this.LoadData=function() {
        start = 0;
        Ext.getCmp(ownerStr + ".IdWorkFlow_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            LcId: Ext.getCmp(ownerStr + '.toolbar.IdlcID').getValue(),
            YwId: Ext.getCmp(ownerStr + '.toolbar.IdYwId').getValue() 
        }
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/WorkFlow/GetWorkFlowList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'lc_defineID', 'ywID', 'lc_jdID', 'lc_jdmc', 'groupID', 'groupName', 'personZgh', 'personName', 'transdate', 'feed', 'number', 'BXType', 'PNO', 'startdate', 'LcByRole', 'isback', 'useback']
        }),
        sortInfo: { field: "ID", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    this.grid = new Ext.grid.EditorGridPanel({
        id: ownerStr + '.IdWorkFlow_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({ width: 35, css: "background-color: #EEEEEE; background-image: none;", renderer: function (v, m, rec, rIdx) { return obj.record_start + 1 + rIdx } }),
            { header: "lc_defineID字段", width: 100, sortable: true, dataIndex: 'lc_defineID' },
            { header: "ywID字段", width: 100, sortable: true, dataIndex: 'ywID' },
            { header: "lc_jdID字段", width: 100, sortable: true, dataIndex: 'lc_jdID' },
            { header: "lc_jdmc字段", width: 160, sortable: true, dataIndex: 'lc_jdmc' },
            { header: "groupID字段", width: 100, sortable: true, dataIndex: 'groupID' },
            { header: "groupName字段", width: 160, sortable: true, dataIndex: 'groupName' },
            { header: "personZgh字段", width: 160, sortable: true, dataIndex: 'personZgh' },
            { header: "personName字段", width: 160, sortable: true, dataIndex: 'personName' },
            { header: "transdate字段", width: 160, sortable: true, dataIndex: 'transdate' },
            { header: "feed字段", width: 160, sortable: true, dataIndex: 'feed' },
            { header: "number字段", width: 100, sortable: true, dataIndex: 'number' },
            { header: "BXType字段", width: 100, sortable: true, dataIndex: 'BXType' },
            { header: "PNO字段", width: 100, sortable: true, dataIndex: 'PNO' },
            { header: "startdate字段", width: 160, sortable: true, dataIndex: 'startdate' },
            { header: "LcByRole字段", width: 100, sortable: true, dataIndex: 'LcByRole' },
            { header: "isback字段", width: 100, sortable: true, dataIndex: 'isback' },
            { header: "useback字段", width: 100, sortable: true, dataIndex: 'useback' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1,toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0 && LoginPerson.RoleNamesString.indexOf("_系统管理") < 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['LcId'] = Ext.getCmp(ownerStr + '.toolbar.IdLcId').getValue();
                o['YwId'] = Ext.getCmp(ownerStr + '.toolbar.IdYwId').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
    }
};
