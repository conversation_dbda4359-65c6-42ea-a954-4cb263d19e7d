function PermRoleList(ownerStr) 
{
    var obj = this;
    var start = 0;
    var limit = 100;
    var pageSize = limit;
    this.PermissionNo = "";

    var cmModule = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/MDLMan/GetModuleList',
            fields: [
                'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var txtPermissionNo = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "权限编号",
        allowBlank: true,
        maxLength: 100,
        readOnly:true,
        anchor: '98%'
    });
    var txtOperationModule = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "所属模块",
        allowBlank: true,
        maxLength: 100,
        readOnly: true,
        anchor: '98%'
    });
    var txtOperation = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "权限名称",
        allowBlank: true,
        maxLength: 100,
        readOnly: true,
        anchor: '98%'
    });
    var txtSubsystem = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "所属系统",
        allowBlank: true,
        maxLength: 100,
        readOnly: true,
        anchor: '98%'
    });

    var cmRoleMark = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "搜索角色",
        allowBlank: true,
        maxLength: 100,
        anchor: '98%'
    });

    var AddPermRole=function(role) {
        Ext.Ajax.request({
            url: '../../Service/BaseOpt/PermRole/AddPermRole',
            params: {
                PermissionNo: obj.PermissionNo,
                RoleId: role 
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success ===true||resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var DeletePermRole = function (role) {
        Ext.Ajax.request({
            url: '../../Service/BaseOpt/PermRole/DeletePermRole',
            params: {
                PermissionNo: obj.PermissionNo,
                RoleId: role
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success === true || resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var yGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/PermRole/GetPermRoleList_Y"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'RoleName','RoleKind']
        })
    });

    var yEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: yGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "角色ID", width: 160, sortable: true, dataIndex: 'Id' },
            { header: "角色名称", width: 160, sortable: true, dataIndex: 'RoleName' },
            { header: "所属模块", width: 160, sortable: true, dataIndex: 'RoleKind' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'Id',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "取消权限";
                }
            }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var roleId = grid.store.getAt(rowIndex).get('Id');
                if (columnIndex == 3) {
                    DeletePermRole(roleId);
                }
            }
        }
    });

    var wGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/PermRole/GetPermRoleList_W"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'RoleName', 'RoleKind']
        })
    });

    var wEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: wGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "角色ID", width: 160, sortable: true, dataIndex: 'Id' },
            { header: "角色名称", width: 160, sortable: true, dataIndex: 'RoleName' },
            { header: "所属模块", width: 160, sortable: true, dataIndex: 'RoleKind' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'Id',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "添加权限";
                }
            }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var roleId = grid.store.getAt(rowIndex).get('Id');
                if (columnIndex == 3) {
                    AddPermRole(roleId);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: wGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st)
            {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o["PermissionNo"] = obj.PermissionNo;
                o['Module'] = cmModule.getValue();
                o['RoleMark'] = cmRoleMark.getValue();
                this.store.load({ params: o, callback: function (r, options, success)
                {
                    if (!success) 
                    {
                        Ext.MessageBox.alert('提示', "加载失败.....请稍候再试！");
                    }
                }});
            }
        })
    }); 

    var EditPanel = new Ext.TabPanel({
        activeTab: 0,
        deferredRender: false,
        autoDestroy: true,
        items:
        [
            {
                title: '已配置角色',
                isFixedTab: true,
                layout: 'fit',
                items: [yEditGrid]
            },
            {
                title: '未配置角色',
                isFixedTab: true,
                layout: 'fit',
                items: [wEditGrid]
            }
        ]
    });

    var cmForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 920,
        layout: 'form',
        autoScroll: false,
        items: [
            txtPermissionNo,
            txtOperationModule,
            txtOperation,
            txtSubsystem,
            cmModule,
            cmRoleMark,
            {
                xtype: 'fieldset',
                autoHeight: true,
                layout: 'fit',
                items: [EditPanel]
            }
        ]
     });

    var cmWindow = new Ext.Window({
        width: 920,
        height: 680,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: cmForm,
        title: '操作权限角色关联配置...',
        buttonAlign: 'center',
        buttons: [
        {
            text: '查询',
            height: 30,
            handler: function () {
                obj.RefreshData();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                cmWindow.close();
            }
        }]
    });

    this.ShowWindow = function (no, Module, name, system) {
        cmForm.form.reset();
        cmWindow.show();
        txtPermissionNo.setValue(no);
        obj.PermissionNo = no;
        txtOperationModule.setValue(Module);
        txtOperation.setValue(name);
        txtSubsystem.setValue(system);

        yGridStore.removeAll();
        wGridStore.removeAll();

        obj.RefreshData();
    }

    this.RefreshData = function () {
        var Module = cmModule.getValue();
        var rmark = cmRoleMark.getValue();

        yGridStore.reload({ params: { PermissionNo: obj.PermissionNo, Module: Module, RoleMark: rmark }, callback: function () {
            if (yEditGrid.store.getCount() > 0) {
                yEditGrid.getSelectionModel().selectAll();
            }
        } 
        });
    wGridStore.reload({ params: { start: 0, limit: limit, PermissionNo: obj.PermissionNo, Module: Module, RoleMark: rmark} });
    }
}

PermRoleList.prototype = {
    constructor: PermRoleList
}