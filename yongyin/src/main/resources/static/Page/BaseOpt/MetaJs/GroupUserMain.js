function GroupUserMain(ownerStr) {
    var obj = this;

    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    this.GroupId = 0;

    //===================用户信息界面===========================start
    var GUser_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        defaults:{
            msgTarget:'side',
        },
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: ownerStr + '.GUser_Form.IdId',
                name: 'Id',
                xtype: "hidden"
            },
            {   //行六
                id: ownerStr + '.GUser_Form.IdLoginName',
                xtype: "textfield",
                name: 'LoginName',
                fieldLabel: "登录名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GUser_Form.IdRealName',
                xtype: "textfield",
                name: 'RealName',
                fieldLabel: "用户姓名",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GUser_Form.IdTelephone',
                xtype: "textfield",
                name: 'Telephone',
                fieldLabel: "电话号码",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GUser_Form.IdMsgType',
                xtype: "textfield",
                name: 'MsgType',
                fieldLabel: "消息类型",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GUser_Form.IdOA',
                xtype: "textfield",
                name: 'OA',
                fieldLabel: "OA编号",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GUser_Form.Idtype',
                xtype: "numberfield",
                name: 'type',
                decimalPrecision: 0,
                fieldLabel: "用户类型",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GUser_Form.IdP_XH',
                xtype: "numberfield",
                name: 'P_XH',
                decimalPrecision: 0,
                fieldLabel: "显示序号",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'Id', mapping: 'Id', type: 'string' },
                { name: 'LoginName', mapping: 'LoginName', type: 'string' },
                { name: 'RealName', mapping: 'RealName', type: 'string' },
                { name: 'GroupID', mapping: 'GroupID', type: 'string' },
                { name: 'GroupName', mapping: 'GroupName', type: 'string' },
                { name: 'GroupDesc', mapping: 'GroupDesc', type: 'string' },
                { name: 'Telephone', mapping: 'Telephone', type: 'string' },
                { name: 'MsgType', mapping: 'MsgType', type: 'string' },
                { name: 'OA', mapping: 'OA', type: 'string' },
                { name: 'type', mapping: 'type', type: 'string' },
                { name: 'P_XH', mapping: 'P_XH', type: 'string' } 
            ]
        })
    });

    var GUser_FormWindow = new Ext.Window({
        id: ownerStr + '.IdGUser_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: GUser_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    GUser_FormWindow.hide();
                }
            }
        ]
    });

    function onRoleButtonClick(record) {

        var id = record.get("Id");
        var login = record.get("LoginName");
        var name = record.get("RealName");
        var group = record.get("GroupName");
        var tgroup = record.get("TopGroupName");

        var oUserRoleList = new UserRoleList(ownerStr);
        oUserRoleList.ShowWindow(id, login, name, tgroup,group);
    }

    function onAddButtonClick() {
        GUser_Form.form.reset();

        GUser_FormWindow.setTitle(ExtractTitleString("用户信息==新增用户信息=="));
        GUser_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (GUser_Form.form.isValid()) {
                GUser_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/UserMan/AddUser',
                    method: 'post',
                    params: {GroupID:obj.GroupId},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        GUser_Form.ownerCt.hide();
                        obj.RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        GUser_FormWindow.buttons[0].enable();
        GUser_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "用户信息==修改用户信息==";
            GUser_FormWindow.setTitle(ExtractTitleString(title));
            GUser_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = GUser_Form.form.reader.jsonData.data[0].Id;
                if (GUser_Form.form.isValid()) {
                    GUser_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/UserMan/ModifyUser',
                        method: 'post',
                        params: { UserId: id,GroupID:obj.GroupId},
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            GUser_Form.ownerCt.hide();
                            obj.RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            GUser_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/UserMan/GetUserById?UserId=' + id;
            GUser_FormWindow.show();
            GUser_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    GUser_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "用户信息==查看用户信息==";
            GUser_FormWindow.setTitle(ExtractTitleString(title));
            GUser_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/UserMan/GetUserById?UserId=' + id;
            GUser_FormWindow.show();
            GUser_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    GUser_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdUser_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/UserMan/DeleteUser",
                        successfn: function (data) {
                            obj.RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { UserId: grid.getSelectionModel().getSelected().data.Id }
                    });
                }
            });
        }
    }
    //===================用户信息界面===========================end  

    //===================用户信息管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
	     ]
    });

    var toolbar2= new Ext.Toolbar({
        items:
        [
            '登录账号',
            {
                id: ownerStr + '.toolbar.IdLoginName',
                xtype: 'textfield',
                width: 176
            },
            '用户名称',
            {
                id: ownerStr + '.toolbar.IdRealName',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function() {
                    obj.LoadData();
                }
            },
            {
                text: '刷新',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        obj.RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
	    ]
     });

    this.RefreshData = function () {
        Ext.getCmp(ownerStr + ".IdUser_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            GroupId: obj.GroupId,
            LoginName: Ext.getCmp(ownerStr + '.toolbar.IdLoginName').getValue(),
            RealName: Ext.getCmp(ownerStr + '.toolbar.IdRealName').getValue()   
        }
        });
    }

    this.LoadData=function() {
        start = 0;
        Ext.getCmp(ownerStr + ".IdUser_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            GroupId:obj.GroupId,
            LoginName: Ext.getCmp(ownerStr + '.toolbar.IdLoginName').getValue(),
            RealName: Ext.getCmp(ownerStr + '.toolbar.IdRealName').getValue()        
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/UserMan/ExportExcel", {
            GroupId: obj.GroupId,
            LoginName: Ext.getCmp(ownerStr + '.toolbar.IdLoginName').getValue(),
            RealName: Ext.getCmp(ownerStr + '.toolbar.IdRealName').getValue()   
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/UserMan/GetUserList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName','MsgType','Telephone', 'OA', 'type', 'P_XH']
        }),
        sortInfo: { field: "Id", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    this.grid = new Ext.grid.GridPanel({
        id: ownerStr + '.IdUser_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "用户权限", width: 160, sortable: true, dataIndex: 'LoginName',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "用户角色";
                }
            },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 300, sortable: true, dataIndex: 'TopGroupName' },
            { header: "所属部门", width: 160, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' },
            { header: "接收消息类型", width: 120, sortable: true, dataIndex: 'MsgType' },
            { header: "电话号码", width: 120, sortable: true, dataIndex: 'Telephone' },
            { header: "OA账户", width: 120, sortable: true, dataIndex: 'OA' },
            { header: "用户type", width: 120, sortable: true, dataIndex: 'type' },
            { header: "显示序号", width: 120, sortable: true, dataIndex: 'P_XH' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: false
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1, toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0){
                    onEditButtonClick();
                }
            },
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var record = grid.store.getAt(rowIndex);
                if (columnIndex == 3) {
                    onRoleButtonClick(record);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['GroupId'] = obj.GroupId;
                o['LoginName'] = Ext.getCmp(ownerStr + '.toolbar.IdLoginName').getValue();
                o['RealName'] = Ext.getCmp(ownerStr + '.toolbar.IdRealName').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0){
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
    }
};
