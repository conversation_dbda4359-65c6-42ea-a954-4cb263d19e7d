function LcJdMain(ownerStr) {
    var obj = this;

    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    this.LcId = 0;

    //===================流程节点界面===========================start
    var LCJD_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: ownerStr + '.LCJD_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdlcjdID',
                xtype: "numberfield",
                name: 'lcjdID',
                decimalPrecision: 0,
                fieldLabel: "节点编号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.Idlc_defineID',
                xtype: "numberfield",
                name: 'lc_defineID',
                decimalPrecision: 0,
                fieldLabel: "流程编号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.Idjdmc',
                xtype: "textfield",
                name: 'jdmc',
                fieldLabel: "节点名称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdnextID',
                xtype: "numberfield",
                name: 'nextID',
                decimalPrecision: 0,
                fieldLabel: "下一节点",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.Idtype',
                xtype: "numberfield",
                name: 'type',
                decimalPrecision: 0,
                fieldLabel: "节点步骤",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.Idshgr',
                xtype: "numberfield",
                name: 'shgr',
                decimalPrecision: 0,
                fieldLabel: "审核类型",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdGroupID',
                xtype: "textfield",
                name: 'GroupID',
                fieldLabel: "所属组号",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdIsBX',
                xtype: "numberfield",
                name: 'IsBX',
                decimalPrecision: 0,
                fieldLabel: "是否主线",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdFormType',
                xtype: "textfield",
                name: 'FormType',
                fieldLabel: "上一步骤",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdCheckFile',
                xtype: "textfield",
                name: 'CheckFile',
                fieldLabel: "检测file说明",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdCheckData',
                xtype: "textfield",
                name: 'CheckData',
                fieldLabel: "检测数据",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdcanBack',
                xtype: "numberfield",
                name: 'canBack',
                decimalPrecision: 0,
                fieldLabel: "能否回退",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdBackjdID',
                xtype: "textfield",
                name: 'BackjdID',
                fieldLabel: "回退节点",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.LCJD_Form.IdLookFileJdID',
                xtype: "textfield",
                name: 'LookFileJdID',
                fieldLabel: "查看节点的FILE",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'string' },
                { name: 'lcjdID', mapping: 'lcjdID', type: 'string' },
                { name: 'lc_defineID', mapping: 'lc_defineID', type: 'string' },
                { name: 'jdmc', mapping: 'jdmc', type: 'string' },
                { name: 'nextID', mapping: 'nextID', type: 'string' },
                { name: 'type', mapping: 'type', type: 'string' },
                { name: 'shgr', mapping: 'shgr', type: 'string' },
                { name: 'GroupID', mapping: 'GroupID', type: 'string' },
                { name: 'IsBX', mapping: 'IsBX', type: 'string' },
                { name: 'FormType', mapping: 'FormType', type: 'string' },
                { name: 'CheckFile', mapping: 'CheckFile', type: 'string' },
                { name: 'CheckData', mapping: 'CheckData', type: 'string' },
                { name: 'canBack', mapping: 'canBack', type: 'string' },
                { name: 'BackjdID', mapping: 'BackjdID', type: 'string' },
                { name: 'LookFileJdID', mapping: 'LookFileJdID', type: 'string' } 
            ]
        })
    });

    var LCJD_FormWindow = new Ext.Window({
        id: ownerStr + '.IdLCJD_FormWindow',
        width: 760,
        height: 540,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: LCJD_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    LCJD_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        LCJD_Form.form.reset();

        LCJD_FormWindow.setTitle(ExtractTitleString("流程节点==新增流程节点=="));
        LCJD_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (LCJD_Form.form.isValid()) {
                LCJD_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/LCJDMan/AddLCJD',
                    method: 'post',
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        LCJD_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        LCJD_FormWindow.buttons[0].enable();
        LCJD_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdLCJD_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "流程节点==修改流程节点==";
            LCJD_FormWindow.setTitle(ExtractTitleString(title));
            LCJD_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = LCJD_Form.form.reader.jsonData.data[0].ID;
                if (LCJD_Form.form.isValid()) {
                    LCJD_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/LCJDMan/ModifyLCJD',
                        method: 'post',
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            LCJD_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            LCJD_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/LCJDMan/GetLCJDById?ID=' + id;
            LCJD_FormWindow.show();
            LCJD_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    LCJD_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdLCJD_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "流程节点==查看流程节点==";
            LCJD_FormWindow.setTitle(ExtractTitleString(title));
            LCJD_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/LCJDMan/GetLCJDById?ID=' + id;
            LCJD_FormWindow.show();
            LCJD_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    LCJD_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdLCJD_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/LCJDMan/DeleteLCJD",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.ID }
                    });
                }
            });
        }
    }
    //===================流程节点界面===========================end  

    //===================流程节点管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function() {
                    obj.LoadData();
                }
            },
            {
                text: '刷新',
                id: ownerStr + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        obj.RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
	    ]
     });

    this.RefreshData=function() {
        Ext.getCmp(ownerStr + ".IdLCJD_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            LcId: obj.LcId
        }
        });
    }

    this.LoadData=function() {
        start = 0;
        Ext.getCmp(ownerStr + ".IdLCJD_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            LcId:obj.LcId
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/LCJDMan/ExportExcel", {
            LcId: obj.LcId
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/LCJDMan/GetLCJDList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'lcjdID', 'lc_defineID', 'jdmc', 'nextID', 'type', 'shgr', 'LCJDID', 'IsBX', 'FormType', 'CheckFile', 'CheckData', 'canBack', 'BackjdID', 'LookFileJdID']
        }),
        sortInfo: { field: "ID", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    this.grid = new Ext.grid.GridPanel({
        id: ownerStr + '.IdLCJD_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "流程节点", width: 200, sortable: true, dataIndex: 'lcjdID' },
            { header: "节点名称", width: 300, sortable: true, dataIndex: 'jdmc' },
            { header: "下一节点", width: 200, sortable: true, dataIndex: 'nextID' },
            { header: "节点步骤", width: 160, sortable: true, dataIndex: 'type' },
            { header: "审核标志", width: 100, sortable: true, dataIndex: 'shgr' },
            { header: "所属机构", width: 100, sortable: true, dataIndex: 'GroupID' },
            { header: "是否主线", width: 100, sortable: true, dataIndex: 'IsBX' },
            { header: "上一步骤", width: 100, sortable: true, dataIndex: 'FormType' },
            { header: "检测FILE", width: 200, sortable: true, dataIndex: 'CheckFile' },
            { header: "检测数据", width: 160, sortable: true, dataIndex: 'CheckData' },
            { header: "能否回退", width: 100, sortable: true, dataIndex: 'canBack' },
            { header: "回退节点", width: 100, sortable: true, dataIndex: 'BackjdID' },
            { header: "可查看文件节点", width: 100, sortable: true, dataIndex: 'LookFileJdID' }
         ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0 && LoginPerson.RoleNamesString.indexOf("_系统管理") < 0)
                {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['LcId'] = obj.LcId;
                o['LCJDName'] = Ext.getCmp(ownerStr + '.toolbar.IdLCJDName').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
    }
};
