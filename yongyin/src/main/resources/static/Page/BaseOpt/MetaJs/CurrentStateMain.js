function CurrentStateMain(ownerStr) {
    var obj = this;

    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;

    //===================CurrentState信息界面===========================start
    var CurrentState_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: ownerStr + '.CurrentState_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdLc_defineID',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'Lc_defineID',
                fieldLabel: "Lc_defineID字段",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdLc_Name',
                xtype: "textfield",
                name: 'Lc_Name',
                fieldLabel: "Lc_Name字段",
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdywID',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'ywID',
                fieldLabel: "ywID字段",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdXH',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'XH',
                fieldLabel: "XH字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdsendPerson',
                xtype: "textfield",
                name: 'sendPerson',
                fieldLabel: "sendPerson字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdsendPersonZgh',
                xtype: "textfield",
                name: 'sendPersonZgh',
                fieldLabel: "sendPersonZgh字段",
                allowBlank: true,
                anchor: '98%'
            }, 
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdAllPersonZgh',
                xtype: "textfield",
                name: 'AllPersonZgh',
                fieldLabel: "AllPersonZgh字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdisMany',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'isMany',
                fieldLabel: "isMany字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.Idlc_jdmc',
                xtype: "textfield",
                name: 'lc_jdmc',
                fieldLabel: "lc_jdmc字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.Idlc_jdid',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'lc_jdid',
                fieldLabel: "lc_jdid字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.Idlc_isback',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'lc_isback',
                fieldLabel: "lc_isback字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.Idlc_tojdid',
                xtype: "textfield",
                name: 'lc_tojdid',
                fieldLabel: "lc_tojdid字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdisOtherAdd',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'isOtherAdd',
                fieldLabel: "isOtherAdd字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.Idnumber',
                xtype: "numberfield",
                decimalPrecision: 0,
                name: 'number',
                fieldLabel: "number字段",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdPNO',
                xtype: "textfield",
                name: 'PNO',
                fieldLabel: "PNO字段",
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.CurrentState_Form.IdsendGroupIDs',
                xtype: "textfield",
                name: 'sendGroupIDs',
                fieldLabel: "sendGroupIDs字段",
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'string' },
                { name: 'Lc_defineID', mapping: 'Lc_defineID', type: 'string' },
                { name: 'Lc_Name', mapping: 'Lc_Name', type: 'string' },
                { name: 'ywID', mapping: 'ywID', type: 'string' },
                { name: 'XH', mapping: 'XH', type: 'string' },
                { name: 'sendPerson', mapping: 'sendPerson', type: 'string' },
                { name: 'sendPersonZgh', mapping: 'sendPersonZgh', type: 'string' },
                { name: 'AllPersonZgh', mapping: 'AllPersonZgh', type: 'string' },
                { name: 'isMany', mapping: 'isMany', type: 'string' },
                { name: 'lc_jdmc', mapping: 'lc_jdmc', type: 'string' },
                { name: 'lc_jdid', mapping: 'lc_jdid', type: 'string' },
                { name: 'lc_isback', mapping: 'lc_isback', type: 'string' },
                { name: 'lc_tojdid', mapping: 'lc_tojdid', type: 'string' },
                { name: 'isOtherAdd', mapping: 'isOtherAdd', type: 'string' },
                { name: 'number', mapping: 'number', type: 'string' },
                { name: 'BXType', mapping: 'BXType', type: 'string' },
                { name: 'PNO', mapping: 'PNO', type: 'string' },
                { name: 'sendGroupIDs', sendGroupIDs: 'BXType', type: 'string' }
            ]
        })
    });


    var CurrentState_FormWindow = new Ext.Window({
        id: ownerStr + '.IdCurrentState_FormWindow',
        width: 760,
        height: 560,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: CurrentState_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    CurrentState_FormWindow.hide();
                }
            }
        ]
     });


    function onAddButtonClick() {
        CurrentState_Form.form.reset();

        CurrentState_FormWindow.setTitle(ExtractTitleString("CurrentState信息==新增CurrentState信息=="));
        CurrentState_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (CurrentState_Form.form.isValid()) {
                CurrentState_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/CurrentState/AddCurrentState',
                    method: 'post',
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        CurrentState_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        CurrentState_FormWindow.buttons[0].enable();
        CurrentState_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdCurrentState_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "CurrentState信息==修改CurrentState信息==";
            CurrentState_FormWindow.setTitle(ExtractTitleString(title));
            CurrentState_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = CurrentState_Form.form.reader.jsonData.data[0].ID;
                if (CurrentState_Form.form.isValid()) {
                    CurrentState_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/CurrentState/ModifyCurrentState',
                        method: 'post',
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            CurrentState_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            CurrentState_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/CurrentState/GetCurrentStateById?ID=' + id;
            CurrentState_FormWindow.show();
            CurrentState_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    CurrentState_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdCurrentState_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "CurrentState信息==查看CurrentState信息==";
            CurrentState_FormWindow.setTitle(ExtractTitleString(title));
            CurrentState_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/CurrentState/GetCurrentStateById?ID=' + id;
            CurrentState_FormWindow.show();
            CurrentState_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    CurrentState_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdCurrentState_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                {
                    url: "../../Service/BaseOpt/CurrentState/DeleteCurrentState",
                    successfn: function (data) {
                        RefreshData();
                    },
                    failurefn: function (data) {
                    },
                    params: { ID: grid.getSelectionModel().getSelected().data.ID }
                });
                }
            });
        }
    }
    //===================CurrentState信息界面===========================end  

    //===================CurrentState信息管理form===========================start
    var LCCombo = new Ext.form.ComboBox({
        id: ownerStr + '.toolbar.IdlcName',
        name: 'lcName',
        fieldLabel: '流程名称',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'LcID',
        displayField: 'lcName',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/BaseOpt/LCDBMan/GetLCList',
            fields: [
                 'LcID', 'lcName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(ownerStr + '.toolbar.IdlcName').setValue(rec.data.lcName);
                Ext.getCmp(ownerStr + '.toolbar.IdlcID').setValue(rec.data.LcID);
            }
        }
    });


    var toolbar1 = new Ext.Toolbar({
        items:
        [
            {
                text: '新增',
                iconCls: 'ButtonFormAdd',
                handler: onAddButtonClick
            },
            {
                text: '修改',
                iconCls: 'ButtonFormEdit',
                handler: onEditButtonClick
            },
            {
                text: '删除',
                iconCls: 'ButtonFormDelete',
                handler: onDeleteButtonClick
            },
            {
                text: '查看',
                iconCls: 'ButtonFormLook',
                handler: onLookButtonClick
            }
	     ]
    });

    var toolbar2 = new Ext.Toolbar({
        items:
        [
            '流程类型',
            LCCombo,
            {
                id: ownerStr + '.toolbar.IdlcID',
                xtype: 'hidden',
                width: 176
            },
            "业务编号",
            {
                id: ownerStr + '.toolbar.IdYwId',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    obj.LoadData();
                }
            },
            {
                text: '刷新',
                id: ownerStr + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        obj.RefreshData();
                    }
                }
            }
	    ]
     });

    this.RefreshData = function () {

        Ext.getCmp(ownerStr + ".IdCurrentState_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            LcId: Ext.getCmp(ownerStr + '.toolbar.IdlcID').getValue(),
            YwId: Ext.getCmp(ownerStr + '.toolbar.IdYwId').getValue() 
        }
        });
    }

    this.LoadData=function() {
        start = 0;
        Ext.getCmp(ownerStr + ".IdCurrentState_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            LcId: Ext.getCmp(ownerStr + '.toolbar.IdlcID').getValue(),
            YwId: Ext.getCmp(ownerStr + '.toolbar.IdYwId').getValue() 
        }
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/CurrentState/GetCurrentStateList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID','Lc_defineID','Lc_Name','ywID','sendPerson','sendPersonZgh','AllPersonZgh','isMany','lc_jdmc','lc_jdid','lc_isback','lc_tojdid','isOtherAdd','number','BXType','PNO','sendGroupIDs']
        }),
        sortInfo: { field: "ID", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    this.grid = new Ext.grid.EditorGridPanel({
        id: ownerStr + '.IdCurrentState_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({ width: 35, css: "background-color: #EEEEEE; background-image: none;", renderer: function (v, m, rec, rIdx) { return obj.record_start + 1 + rIdx } }),
            { header: "Lc_defineID字段", width: 100, sortable: true, dataIndex: 'Lc_defineID' },
            { header: "Lc_Name字段", width: 200, sortable: true, dataIndex: 'Lc_Name' },
            { header: "ywID字段", width: 100, sortable: true, dataIndex: 'ywID' },
            { header: "sendPerson字段", width: 160, sortable: true, dataIndex: 'sendPerson' },
            { header: "sendPersonZgh字段", width: 160, sortable: true, dataIndex: 'sendPersonZgh' },
            { header: "AllPersonZgh字段", width: 160, sortable: true, dataIndex: 'AllPersonZgh' },
            { header: "isMany字段", width: 100, sortable: true, dataIndex: 'isMany' },
            { header: "lc_jdmc字段", width: 100, sortable: true, dataIndex: 'lc_jdmc' },
            { header: "lc_jdid字段", width: 100, sortable: true, dataIndex: 'lc_jdid' },
            { header: "lc_isback字段", width: 100, sortable: true, dataIndex: 'lc_isback' },
            { header: "lc_tojdid字段", width: 100, sortable: true, dataIndex: 'lc_tojdid' },
            { header: "isOtherAdd字段", width: 100, sortable: true, dataIndex: 'isOtherAdd' },
            { header: "number字段", width: 100, sortable: true, dataIndex: 'number' },
            { header: "BXType字段", width: 100, sortable: true, dataIndex: 'BXType' },
            { header: "PNO字段", width: 100, sortable: true, dataIndex: 'PNO' },
            { header: "sendGroupIDs字段", width: 100, sortable: true, dataIndex: 'sendGroupIDs' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: false
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1,toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['LcId'] = Ext.getCmp(ownerStr + '.toolbar.IdLcId').getValue();
                o['YwId'] = Ext.getCmp(ownerStr + '.toolbar.IdYwId').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
    }
};
