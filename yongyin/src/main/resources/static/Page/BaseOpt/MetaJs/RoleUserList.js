function RoleUserList(ownerStr) 
{
    var obj = this;
    var start = 0;
    var limit = 100;
    var pageSize = limit;
    this.RoleId = "";

    var cmGroup = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属公司',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'COMP_ID',
        displayField: 'COMP_NAME',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetGroupList2',
            fields: [
                 'COMP_ID', 'COMP_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var txtRoleId = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "角色编号",
        allowBlank: true,
        maxLength: 100,
        readOnly: true,
        anchor: '98%'
    });

    var txtRoleName = new Ext.form.TextField({
        xtype: "textfield",
        fieldLabel: "角色名称",
        allowBlank: true,
        maxLength: 100,
        readOnly: true,
        anchor: '98%'
    });

    var cmUserMark = new Ext.form.TextField({
        xtype: "textfield",
        name: 'UserMark',
        fieldLabel: "搜索用户",
        allowBlank: true,
        maxLength: 100,
        anchor: '98%'
    });

    var AddRoleUser=function(personId) {
        Ext.Ajax.request({
            url: '../../Service/BaseOpt/RoleUser/AddRoleUser',
            params: {
                UserId: personId,
                RoleId: obj.RoleId 
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success ===true||resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var DeleteRoleUser = function (personId) {
        Ext.Ajax.request({
            url: '../../Service/BaseOpt/RoleUser/DeleteRoleUser',
            params: {
                UserId: personId,
                RoleId: obj.RoleId 
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success === true || resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var yGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/RoleUser/GetRoleUserList_Y"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName']
        })
    });

    var yEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: yGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'LoginName',
                renderer: function (val, metadata, record) {
                    metadata.style += "color: blue;text-align:center;text-decoration:underline;";
                    return "取消权限";
                }
            },
            { header: "所属部门", width: 100, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'TopGroupName' }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var personId = grid.store.getAt(rowIndex).get('Id');
                if (columnIndex == 2) {
                    DeleteRoleUser(personId);
                }
            }
        }
    });

    var wGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/RoleUser/GetRoleUserList_W"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName']
        })
    });

    var wEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: wGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'LoginName',
                renderer: function (val, metadata, record) {
                    metadata.style += "color: blue;text-align:center;text-decoration:underline;";
                    return "添加权限";
                }
            },
            { header: "所属部门", width: 100, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'TopGroupName' }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var personId = grid.store.getAt(rowIndex).get('Id');
                if (columnIndex == 2) {
                    AddRoleUser(personId);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: wGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st)
            {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o["RoleId"] = obj.RoleId;
                o['Group'] = cmGroup.getValue();
                o['UserMark'] = cmUserMark.getValue();
                this.store.load({ params: o, callback: function (r, options, success)
                {
                    if (!success) 
                    {
                        Ext.MessageBox.alert('提示', "加载失败.....请稍候再试！");
                    }
                }});
            }
        })
    }); 

    var EditPanel = new Ext.TabPanel({
        activeTab: 0,
        deferredRender: false,
        autoDestroy: true,
        items:
        [
            {
                title: '已配置用户',
                isFixedTab: true,
                layout: 'fit',
                items: [yEditGrid]
            },
            {
                title: '未配置用户',
                isFixedTab: true,
                layout: 'fit',
                items: [wEditGrid]
            }
        ]
    });

    var cmForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 920,
        layout: 'form',
        autoScroll: false,
        items: [
            txtRoleId,
            txtRoleName,
            cmGroup,
            cmUserMark,
            {
                xtype: 'fieldset',
                autoHeight: true,
                layout: 'fit',
                items: [EditPanel]
            }
        ]
     });

    var cmWindow = new Ext.Window({
        width: 920,
        height: 680,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: cmForm,
        title: '操作权限角色关联配置...',
        buttonAlign: 'center',
        buttons: [
        {
            text: '查询',
            height: 30,
            handler: function () {
                obj.RefreshData();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                cmWindow.close();
            }
        }]
    });

    this.ShowWindow = function (id, name) {
        cmForm.form.reset();
        cmWindow.show();
        txtRoleId.setValue(id);
        obj.RoleId = id;
        txtRoleName.setValue(name);

        yGridStore.removeAll();
        wGridStore.removeAll();

        obj.RefreshData();
    }

    this.RefreshData = function () {
        var Group = cmGroup.getValue();
        var umark = cmUserMark.getValue();

        yGridStore.reload({ params: { RoleId: obj.RoleId, Group: Group, UserMark: umark }, callback: function () {
            if (yEditGrid.store.getCount() > 0) {
                yEditGrid.getSelectionModel().selectAll();
            }
        } 
        });
        wGridStore.reload({ params: { start: 0, limit: limit, RoleId: obj.RoleId, Group: Group, UserMark: umark} });
    }
}

RoleUserList.prototype = {
    constructor: RoleUserList
}