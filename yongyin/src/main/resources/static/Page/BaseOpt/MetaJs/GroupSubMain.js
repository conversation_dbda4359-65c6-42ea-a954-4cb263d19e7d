function GroupSubMain(ownerStr) {
    var obj = this;

    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    this.ParentId = 0;

    //===================单位信息界面===========================start
    var GSub_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: ownerStr + '.GSub_Form.Idid',
                name: 'id',
                xtype: "hidden"
            },
            {   //行六
                id: ownerStr + '.GSub_Form.Idgroupname',
                xtype: "textfield",
                name: 'groupname',
                fieldLabel: "单位名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.IdUComapanyQC',
                xtype: "textfield",
                name: 'UComapanyQC',
                fieldLabel: "单位全称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.Idshortpinyin',
                xtype: "textfield",
                name: 'shortpinyin',
                fieldLabel: "单位简称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.Idtype',
                xtype: "numberfield",
                name: 'type',
                decimalPrecision: 0,
                fieldLabel: "type类型",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.Iddydj',
                xtype: "textfield",
                name: 'dydj',
                fieldLabel: "dydj类型",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.IdIsShow',
                xtype: "numberfield",
                name: 'IsShow',
                decimalPrecision: 0,
                fieldLabel: "是否显示",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.IdXH',
                xtype: "numberfield",
                name: 'XH',
                decimalPrecision: 0,
                fieldLabel: "显示序号",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.GSub_Form.IdCategory',
                xtype: "numberfield",
                name: 'Category',
                decimalPrecision: 0,
                fieldLabel: "Category分类",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'id', mapping: 'id', type: 'string' },
                { name: 'groupname', mapping: 'groupname', type: 'string' },
                { name: 'GroupDesc', mapping: 'GroupDesc', type: 'string' },
                { name: 'parentid', mapping: 'parentid', type: 'string' },
                { name: 'XH', mapping: 'XH', type: 'string' },
                { name: 'UComapanyQC', mapping: 'UComapanyQC', type: 'string' },
                { name: 'dydj', mapping: 'dydj', type: 'string' },
                { name: 'shortpinyin', mapping: 'shortpinyin', type: 'string' },
                { name: 'Category', mapping: 'Category', type: 'string' },
                { name: 'type', mapping: 'type', type: 'string' },
                { name: 'IsShow', mapping: 'IsShow', type: 'string' } 
            ]
        })
    });

    var GSub_FormWindow = new Ext.Window({
        id: ownerStr + '.IdGSub_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: GSub_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    GSub_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        GSub_Form.form.reset();

        GSub_FormWindow.setTitle(ExtractTitleString("单位信息==新增单位信息=="));
        GSub_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (GSub_Form.form.isValid()) {
                GSub_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/GroupMan/AddGroup',
                    method: 'post',
                    params: {parentid:obj.ParentId},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        GSub_Form.ownerCt.hide();
                        obj.RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        GSub_FormWindow.buttons[0].enable();
        GSub_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdGroup_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "单位信息==修改单位信息==";
            GSub_FormWindow.setTitle(ExtractTitleString(title));
            GSub_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = GSub_Form.form.reader.jsonData.data[0].id;
                if (GSub_Form.form.isValid()) {
                    GSub_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/GroupMan/ModifyGroup',
                        method: 'post',
                        params: { GroupId: id,parentid:obj.ParentId},
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            GSub_Form.ownerCt.hide();
                            obj.RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            GSub_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.id;
            var url = '../../Service/BaseOpt/GroupMan/GetGroupById?GroupId=' + id;
            GSub_FormWindow.show();
            GSub_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    GSub_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdGroup_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "单位信息==查看单位信息==";
            GSub_FormWindow.setTitle(ExtractTitleString(title));
            GSub_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.id;
            var url = '../../Service/BaseOpt/GroupMan/GetGroupById?GroupId=' + id;
            GSub_FormWindow.show();
            GSub_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    GSub_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(ownerStr + ".IdGroup_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/GroupMan/DeleteGroup",
                        successfn: function (data) {
                            obj.RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { GroupId: grid.getSelectionModel().getSelected().data.id }
                    });
                }
            });
        }
    }
    //===================单位信息界面===========================end  

    //===================单位信息管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
	     ]
    });

    var toolbar2= new Ext.Toolbar({
        items:
        [
            '机构名称',
            {
                id: ownerStr + '.toolbar.IdGroupname',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function() {
                    obj.LoadData();
                }
            },
            {
                text: '刷新',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        obj.RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
	    ]
     });

    this.RefreshData=function() {
        Ext.getCmp(ownerStr + ".IdGroup_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            ParentId: obj.ParentId,
            Groupname: Ext.getCmp(ownerStr + '.toolbar.IdGroupname').getValue() 
        }
        });
    }

    this.LoadData=function() {
        start = 0;
        Ext.getCmp(ownerStr + ".IdGroup_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            ParentId:obj.ParentId,
            Groupname: Ext.getCmp(ownerStr + '.toolbar.IdGroupname').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/GroupMan/ExportExcel", {
            ParentId: obj.ParentId,
            Groupname: Ext.getCmp(ownerStr + '.toolbar.IdGroupname').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/GroupMan/GetGroupList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['id', 'groupname', 'ParentName', 'parentid', 'type', 'shortpinyin', 'dydj', 'XH', 'IsShow', 'Category', 'UComapanyQC']
        }),
        sortInfo: { field: "Id", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    this.grid = new Ext.grid.GridPanel({
        id: ownerStr + '.IdGroup_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "单位名称", width: 200, sortable: true, dataIndex: 'groupname' },
            { header: "单位全称", width: 300, sortable: true, dataIndex: 'UComapanyQC' },
            { header: "上级单位", width: 200, sortable: true, dataIndex: 'ParentName' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'type' },
            { header: "单位简称", width: 100, sortable: true, dataIndex: 'shortpinyin' },
            { header: "辅助标志", width: 100, sortable: true, dataIndex: 'dydj' },
            { header: "是否显示", width: 100, sortable: true, dataIndex: 'IsShow' },
            { header: "类型标志", width: 100, sortable: true, dataIndex: 'Category' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1, toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['ParentId'] = obj.ParentId;
                o['GroupName'] = Ext.getCmp(ownerStr + '.toolbar.IdGroupname').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
    }
};
