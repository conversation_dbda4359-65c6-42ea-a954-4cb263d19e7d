Ext.namespace('NF.ALOG_ManageMain');

//初始化函数
NF.ALOG_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'NF.ALOG_ManageMain';

    //===================内网认定项管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
              '日志日期',
             {
                 id: funcMark + '.toolbar.IdPDate',
                 xtype: "datefield",
                 name: 'PDate',
                 format: 'Y-m-d',
                 width: 160
             },
              '起始时间',
             {
                 id: funcMark + '.toolbar.IdStartT',
                 xtype: "timefield",
                 name: 'StartT',
                 format: 'H:i:s',
                 width: 160,
             },
              '结束时间',
             {
                 id: funcMark + '.toolbar.IdEndT',
                 xtype: "timefield",
                 name: 'EndT',
                 format: 'H:i:s',
                 width: 160
             },
              '日志描述',
             {
                 id: funcMark + '.toolbar.IdRemark',
                 xtype: "textfield",
                 name: 'Remark',
                 width: 160
             },
              '-',
              '->',
              '-',
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdALOG_Grid").store.reload({ params: {
            PDate: Ext.getCmp(funcMark + '.toolbar.IdPDate').getValue(),
            StartT: Ext.getCmp(funcMark + '.toolbar.IdStartT').getValue(),
            EndT: Ext.getCmp(funcMark + '.toolbar.IdEndT').getValue(),
            Remark: Ext.getCmp(funcMark + '.toolbar.IdRemark').getValue()
        }
        });
    }

    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdALOG_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            PDate: Ext.getCmp(funcMark + '.toolbar.IdPDate').getValue(),
            StartT: Ext.getCmp(funcMark + '.toolbar.IdStartT').getValue(),
            EndT: Ext.getCmp(funcMark + '.toolbar.IdEndT').getValue(),
            Remark: Ext.getCmp(funcMark + '.toolbar.IdRemark').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/ALOGMan/ExportExcel", {
            PDate: Ext.getCmp(funcMark + '.toolbar.IdPDate').getValue(),
            StartT: Ext.getCmp(funcMark + '.toolbar.IdStartT').getValue(),
            EndT: Ext.getCmp(funcMark + '.toolbar.IdEndT').getValue(),
            Remark: Ext.getCmp(funcMark + '.toolbar.IdRemark').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/ALOGMan/GetALOGList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Guid', 'OperatorName', 'OperateDate', 'OperateModule', 'OperateFunction', 'OperateDetail']
        }),
        sortInfo: { field: "OperateDate", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdALOG_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "操作名称", width: 170, sortable: true, dataIndex: 'OperatorName' },
            { header: "操作日期", width: 170, sortable: true, dataIndex: 'OperateDate' },
            { header: "操作模块", width: 170, sortable: true, dataIndex: 'OperateModule' },
            { header: "操作功能", width: 170, sortable: true, dataIndex: 'OperateFunction' },
            { header: "操作说明", width: 170, sortable: true, dataIndex: 'OperateDetail' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['PDate'] = Ext.getCmp(funcMark + '.toolbar.IdPDate').getValue();
                o['StartT'] = Ext.getCmp(funcMark + '.toolbar.IdStartT').getValue();
                o['EndT'] = Ext.getCmp(funcMark + '.toolbar.IdEndT').getValue();
                o['Remark'] = Ext.getCmp(funcMark + '.toolbar.IdRemark').getValue();
                this.store.load({ params: o });
            }
        })
    });

  
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================内网认定项管理form===========================end
};
NF.ALOG_ManageMain.Initialize();
