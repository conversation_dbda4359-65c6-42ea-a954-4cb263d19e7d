Ext.namespace('BaseOpt.LC_ManageMain');

//初始化函数
BaseOpt.LC_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.LC_ManageMain';

    var oLcJdMain = new LcJdMain(funcMark);

    //===================流程管理配置界面===========================start
    var LC_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.LC_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.LC_Form.IdLcID',
                xtype: "numberfield",
                name: 'LcID',
                decimalPrecision: 0,
                fieldLabel: "流程编号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.LC_Form.IdlcName',
                xtype: "textfield",
                name: 'lcName',
                fieldLabel: "流程名称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.LC_Form.Idywb',
                xtype: "textfield",
                name: 'ywb',
                fieldLabel: "关联业务说明",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.LC_Form.IdywUrl',
                xtype: "textfield",
                name: 'ywUrl',
                fieldLabel: "关联业务连接",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.LC_Form.IdisUse',
                xtype: "numberfield",
                name: 'isUse',
                decimalPrecision: 0,
                fieldLabel: "是否在用",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'int' },
                { name: 'LcID', mapping: 'LcID', type: 'string' },
                { name: 'lcName', mapping: 'lcName', type: 'string' },
                { name: 'ywb', mapping: 'ywb', type: 'string' },
                { name: 'ywUrl', mapping: 'ywUrl', type: 'string' },
                { name: 'isUse', mapping: 'isUse', type: 'string' }
            ]
        })
    });

    var LC_FormWindow = new Ext.Window({
        id: funcMark + '.IdLC_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: LC_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    LC_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        LC_Form.form.reset();

        LC_FormWindow.setTitle(ExtractTitleString("流程管理配置==新增流程管理配置=="));
        LC_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (LC_Form.form.isValid()) {
                LC_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/LCMan/AddLC',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        LC_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        LC_FormWindow.buttons[0].enable();
        LC_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdLC_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "流程管理配置==修改流程管理配置==";
            LC_FormWindow.setTitle(ExtractTitleString(title));
            LC_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = LC_Form.form.reader.jsonData.data[0].ID;
                if (LC_Form.form.isValid()) {
                    LC_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/LCMan/ModifyLC',
                        method: 'post',
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            LC_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            LC_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/LCMan/GetLCById?ID=' + id;
            LC_FormWindow.show();
            LC_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    LC_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdLC_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "流程管理配置==查看流程管理配置==";
            LC_FormWindow.setTitle(ExtractTitleString(title));
            LC_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/LCMan/GetLCById?ID=' + id;
            LC_FormWindow.show();
            LC_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    LC_FormWindow.buttons[0].disable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdLC_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/LCMan/DeleteLC",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.ID }
                    });
                }
            });
        }
    }
    //===================流程管理配置界面===========================end  

    //===================流程管理配置管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdLC_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdLC_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/LCMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/LCMan/GetLCList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID','LcID', 'lcName', 'ywb', 'ywUrl','isUse']
        }),
        sortInfo: { field: "LcID", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdLC_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "流程编号", width: 80, sortable: true, dataIndex: 'LcID' },
            { header: "流程名称", width: 120, sortable: true, dataIndex: 'lcName' },
            { header: "关联业务说明", width: 120, sortable: true, dataIndex: 'ywb' },
            { header: "关联业务连接", width: 120, sortable: true, dataIndex: 'ywUrl' },
            { header: "是否在用", width: 120, sortable: true, dataIndex: 'isUse' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            },
            "rowclick":function(grid, rowIndex, e) {
                var record = grid.store.getAt(rowIndex);
                var lcid = record.get('LcID');
                oLcJdMain.LcId = lcid;
                oLcJdMain.LoadData();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    }

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
            {
                split:true,
                region: 'north',
                height: 200,
                layout: 'fit',
                minSize: 200,
                maxSize: 350,
                items: [grid]
            },
            {
                region: 'center',
                layout: 'fit',
                items: [oLcJdMain.grid]
            }
        ]
    }));
    Ext.QuickTips.init();

    //===================流程管理配置管理form===========================end
};
BaseOpt.LC_ManageMain.Initialize();
