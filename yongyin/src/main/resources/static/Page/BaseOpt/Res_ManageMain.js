Ext.namespace('BaseOpt.Res_ManageMain');

//初始化函数
BaseOpt.Res_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.Res_ManageMain';

    //===================操作资源界面===========================start
    var PublicGroup = new Ext.form.RadioGroup({
        id: funcMark + '.Res_Form.IdIsPublicN',
        name: 'IsPublicN',
        fieldLabel: '是否公开',
        anchor: '98%',
        columns: 6,
        style: 'padding-top:3px;height:20px;',
        items:
        [
          {
              boxLabel: '<span style="color:red;">私有</span>',
              name: 'IsPublicN',
              inputValue: '0',
              labelStyle: 'color:red;',
              checked: true
          },
          {
              boxLabel: '<span style="color:blue;">内网公开</span>',
              name: 'IsPublicN',
              labelStyle: 'color:blue;',
              inputValue: '1'
          },
          {
              boxLabel: '<span style="color:green;">外网公开</span>',
              name: 'IsPublicN',
              labelStyle: 'color:green;',
              inputValue: '2'
          }
        ],
        listeners:
        {
            change: function (rdgroup, checked) {
                var chval = PublicGroup.getValue();
                Ext.getCmp(funcMark + '.Res_Form.IdIsPublic').setValue(chval);
            }
        }
    });

    var Res_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {   //行六
                id: funcMark + '.Res_Form.IdId',
                xtype: "textfield",
                name: 'Id',
                fieldLabel: "资源编号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Res_Form.IdTitle',
                xtype: "textfield",
                name: 'Title',
                fieldLabel: "资源名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Res_Form.IdUrl',
                xtype: "textfield",
                name: 'Url',
                fieldLabel: "资源链接",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Res_Form.IdParentId',
                xtype: "textfield",
                name: 'ParentId',
                fieldLabel: "上级资源",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.Res_Form.IdOrderNumber',
                xtype: "numberfield",
                name: 'OrderNumber',
                decimalPrecision: 0,
                fieldLabel: "显示序号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            PublicGroup,
            {
                id: funcMark + '.Res_Form.IdIsPublic',
                name: 'IsPublic',
                xtype: "hidden"
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'Id', mapping: 'Id', type: 'string' },
                { name: 'Title', mapping: 'Title', type: 'string' },
                { name: 'Url', mapping: 'Url', type: 'string' },
                { name: 'ParentId', mapping: 'ParentId', type: 'string' },
                { name: 'OrderNumber', mapping: 'OrderNumber', type: 'string' },
                { name: 'IsPublicN', mapping: 'IsPublicN', type: 'string' },
                { name: 'IsPublic', mapping: 'IsPublic', type: 'string' },
                { name: 'IsPublic', mapping: 'IsPublic', type: 'string' }
            ]
        })
    });

    var Res_FormWindow = new Ext.Window({
        id: funcMark + '.IdRes_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: Res_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    Res_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        Res_Form.form.reset();

        Res_FormWindow.setTitle(ExtractTitleString("操作资源==新增操作资源=="));
        Res_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (Res_Form.form.isValid()) {
                Res_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/ResMan/AddRes',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        Res_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        Res_FormWindow.buttons[0].enable();
        Res_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRes_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "操作资源==修改操作资源==";
            Res_FormWindow.setTitle(ExtractTitleString(title));
            Res_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = Res_Form.form.reader.jsonData.data[0].Id;
                if (Res_Form.form.isValid()) {
                    Res_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/ResMan/ModifyRes',
                        method: 'post',
                        params: { ResId: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            Res_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            Res_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/ResMan/GetResById?ResId=' + id;
            Res_FormWindow.show();
            Res_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    Res_FormWindow.buttons[0].enable();

                    var ispublic = form.reader.jsonData.data[0].IsPublic;
                    PublicGroup.setValue(ispublic);
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRes_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "操作资源==查看操作资源==";
            Res_FormWindow.setTitle(ExtractTitleString(title));
            Res_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/ResMan/GetResById?ResId=' + id;
            Res_FormWindow.show();
            Res_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    Res_FormWindow.buttons[0].disable();
                    var ispublic = form.reader.jsonData.data[0].IsPublic;
                    PublicGroup.setValue(ispublic);
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdRes_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/ResMan/DeleteRes",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ResId: grid.getSelectionModel().getSelected().data.Id }
                    });
                }
            });
        }
    }
    //===================操作资源界面===========================end  

    //===================操作资源管理form===========================start
    var toolbar1 = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             }
	     ]
    });

    var toolbar2 = new Ext.Toolbar({
        items:
        [
            '资源编号',
            {
                id: funcMark + '.toolbar.IdResId',
                xtype: 'textfield',
                width: 176
            },
            '资源链接',
            {
                id: funcMark + '.toolbar.IdResUrl',
                xtype: 'textfield',
                width: 176
            },
            '资源名称',
            {
                id: funcMark + '.toolbar.IdResName',
                xtype: 'textfield',
                width: 176
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: LoadData
            },
            {
                text: '刷新',
                id: funcMark + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormEdit',
                handler: ExportExcel
            }
        ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdRes_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            ResId: Ext.getCmp(funcMark + '.toolbar.IdResId').getValue(),
            ResName: Ext.getCmp(funcMark + '.toolbar.IdResName').getValue(),
            ResUrl: Ext.getCmp(funcMark + '.toolbar.IdResUrl').getValue()
        }
        });
    }

    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdRes_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            ResId: Ext.getCmp(funcMark + '.toolbar.IdResId').getValue(),
            ResName: Ext.getCmp(funcMark + '.toolbar.IdResName').getValue(),
            ResUrl: Ext.getCmp(funcMark + '.toolbar.IdResUrl').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/ResMan/ExportExcel", {
            ResId: Ext.getCmp(funcMark + '.toolbar.IdResId').getValue(),
            ResName: Ext.getCmp(funcMark + '.toolbar.IdResName').getValue(),
            ResUrl: Ext.getCmp(funcMark + '.toolbar.IdResUrl').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/ResMan/GetResList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'Title', 'Url', 'ParentId', 'ParentName', 'OrderNumber', 'IsPublic', 'Parameter']
        }),
        sortInfo: { field: "Id", direction: "ASC" },
        remoteSort:true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdRes_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "资源编号", width: 80, sortable: true, dataIndex: 'Id' },
            { header: "资源名称", width: 120, sortable: true, dataIndex: 'Title' },
            { header: "资源链接", width: 320, sortable: true, dataIndex: 'Url' },
            { header: "所属资源", width: 120, sortable: true, dataIndex: 'ParentId' },
            { header: "上级资源", width: 120, sortable: true, dataIndex: 'ParentName' },
            { header: "显示序号", width: 120, sortable: true, dataIndex: 'OrderNumber' },
            { header: "公开情况", width: 120, sortable: true, dataIndex: 'IsPublic' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar1, toolbar2]
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['ResId'] = Ext.getCmp(funcMark + '.toolbar.IdResId').getValue();
                o['ResName'] = Ext.getCmp(funcMark + '.toolbar.IdResName').getValue();
                o['ResUrl'] = Ext.getCmp(funcMark + '.toolbar.IdResUrl').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar1.items.items[0].disable();
    toolbar1.items.items[1].disable();
    toolbar1.items.items[2].disable();
    toolbar1.items.items[3].enable();
    if (LoginPerson.RoleNamesString.indexOf("平台管理-系统管理") >= 0) {
        toolbar1.items.items[0].enable();
        toolbar1.items.items[1].enable();
        toolbar1.items.items[2].enable();
        toolbar1.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================操作资源管理form===========================end
};
BaseOpt.Res_ManageMain.Initialize();
