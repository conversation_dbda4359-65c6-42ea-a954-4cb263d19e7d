Ext.namespace('BaseOpt.Group_ManageMain');

//初始化函数
BaseOpt.Group_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.Group_ManageMain';

    var oGroupUserMain = new GroupUserMain(funcMark);
    var oGroupSubMain = new GroupSubMain(funcMark);

    //===================机构树面板===========================start
    var groupTree = new Ext.tree.TreePanel({
        title: '单位树',
        region: 'west',
        split: true,
        width: 226,
        minSize: 245,
        maxSize: 400,
        margins: '0 0 0 5',
        autoScroll: true,
        rootVisible: false,
        root: new Ext.tree.AsyncTreeNode({ id: 'root', text: '机构树', expanded: false }),
        loader: new Ext.tree.TreeLoader({
            dataUrl: "../../Service/BaseOpt/GroupMan/GetGroupTreeByParent",
            preloadChildren: true
        }),
        listeners: {
            'click': function (node,obj) {
                groupid = node.id;
                oGroupUserMain.GroupId = groupid;
                oGroupSubMain.ParentId = groupid;

                oGroupUserMain.LoadData();
                oGroupSubMain.LoadData();
            }
        }
    });
    //===================机构树面板===========================end

    var SmartExpandFirst = function () {
        var node = groupTree.getRootNode();
        if (!node.expanded) {
            node.expand(false, false, function (a) {
                var childnodes = node.childNodes;
                if (childnodes != undefined) {
                    var childnode = childnodes[0];
                    if (childnode!=undefined&&!childnode.expanded) {
                        childnode.expand(false, false, function (a) { });
                    }
                }
            });
        } else {
            var childnodes = node.childNodes;
            if (childnodes != undefined) {
                var childnode = childnodes[0];
                if (childnode!=undefined&&!childnode.expanded) {
                    childnode.expand(false, false, function (a) { });
                }
            }
        }
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    }

    var metaPanel = new Ext.TabPanel({
        activeTab: 0,
        border: false,
        deferredRender: false,
        autoDestroy: true,
        split: false,
        items:
        [
            {
                title: '下级机构',
                layout: 'fit',
                isFixedTab: true,
                region: 'center',
                items: [oGroupSubMain.grid]
            },
            {
                title: '机构用户',
                layout: 'fit',
                isFixedTab: true,
                region: 'center',
                items: [oGroupUserMain.grid]
            }
        ]
    });

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            SmartExpandFirst();
        },
        items: [
              groupTree,
              {
                  region: 'center',
                  layout: 'fit',
                  items: [metaPanel]
              }
        ]
    }));
    Ext.QuickTips.init();
    //===================用户信息管理form===========================end
};
BaseOpt.Group_ManageMain.Initialize();
