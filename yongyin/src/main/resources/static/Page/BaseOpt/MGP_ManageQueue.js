DownloadJSByParams('../../Page/CPSoft/common.js', function ()
{
    var FilesArray = [
        '../../Page/CPSoft/ExtGridExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version=' + _cps_js_version,
        '../../Page/CPSoft/SmartCheck.js?version=' + _cps_js_version,
     ];

    Import.LoadJsList(FilesArray, function () {
        DownloadJS('../../Page/BaseOpt/MGP_ManageMain.js?version='+_cps_js_version);
    });

    document.getElementById('../../Page/BaseOpt/MGP_ManageQueue.js').Destroy = function()
    {
        var element = document.getElementById('../../Page/BaseOpt/MGP_ManageMain.js?version='+_cps_js_version); element.parentNode.removeChild(element);
        Ext.getCmp("BaseOpt.MGP_ManageMain.IdMGP_FormWindow").destroy();
    }
    document.getElementById('../../Page/BaseOpt/MGP_ManageQueue.js').DataReload = function () {
        if (Ext.getCmp('BaseOpt.MGP_ManageMain.toolbar.IdREFRESH')!= undefined) {
            Ext.getCmp('BaseOpt.MGP_ManageMain.toolbar.IdREFRESH').fireEvent('click');    
        }
    }
});
