Ext.namespace('BaseOpt.MDL_ManageMain');

//初始化函数
BaseOpt.MDL_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.MDL_ManageMain';

    //===================模块配置界面===========================start
    var MDL_Form = new Ext.FormPanel({
        labelWidth: 120,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.MDL_Form.IdID',
                name: 'ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.MDL_Form.Idmodule_num',
                xtype: "textfield",
                name: 'module_num',
                fieldLabel: "module_num",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Idmodule_name',
                xtype: "textfield",
                name: 'module_name',
                fieldLabel: "module_name",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Idmodule_type',
                xtype: "textfield",
                name: 'module_type',
                fieldLabel: "module_type",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Idis_show',
                xtype: "textfield",
                name: 'is_show',
                fieldLabel: "is_show",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Idurl',
                xtype: "textfield",
                name: 'url',
                fieldLabel: "url",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Idapp_url',
                xtype: "textfield",
                name: 'app_url',
                fieldLabel: "app_url",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Iddefine_f',
                xtype: "textfield",
                name: 'define_f',
                fieldLabel: "define_f",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MDL_Form.Iddefine_s',
                xtype: "textfield",
                name: 'define_s',
                fieldLabel: "define_s",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'ID', mapping: 'ID', type: 'int' },,
                { name: 'module_num', mapping: 'module_num', type: 'string' },
                { name: 'module_name', mapping: 'module_name', type: 'string' },
                { name: 'module_type', mapping: 'module_type', type: 'string' },
                { name: 'is_show', mapping: 'is_show', type: 'string' },
                { name: 'url', mapping: 'url', type: 'string' },
                { name: 'app_url', mapping: 'app_url', type: 'string' },
                { name: 'define_f', mapping: 'define_f', type: 'string' },
                { name: 'define_s', mapping: 'define_s', type: 'string' }
            ]
        })
    });

    var MDL_FormWindow = new Ext.Window({
        id: funcMark + '.IdMDL_FormWindow',
        width: 640,
        height: 420,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: MDL_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                disabled: true
            },
            {
                text: '关闭',
                handler: function () {
                    MDL_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        MDL_Form.form.reset();

        MDL_FormWindow.setTitle(ExtractTitleString("模块配置==新增模块配置=="));
        MDL_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (MDL_Form.form.isValid()) {
                MDL_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/MDLMan/AddMDL',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        MDL_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        MDL_FormWindow.buttons[0].enable();
        MDL_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdMDL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "模块配置==修改模块配置==";
            MDL_FormWindow.setTitle(ExtractTitleString(title));
            MDL_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = MDL_Form.form.reader.jsonData.data[0].ID;
                if (MDL_Form.form.isValid()) {
                    MDL_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/MDLMan/ModifyMDL',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            MDL_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            MDL_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/MDLMan/GetMDLById?ID=' + id;
            MDL_FormWindow.show();
            MDL_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    MDL_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdMDL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "模块配置==查看模块配置==";
            MDL_FormWindow.setTitle(ExtractTitleString(title));
            MDL_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.ID;
            var url = '../../Service/BaseOpt/MDLMan/GetMDLById?ID=' + id;
            MDL_FormWindow.show();
            MDL_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    MDL_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdMDL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/MDLMan/DeleteMDL",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.ID }
                    });
                }
            });
        }
    }
    //===================模块配置界面===========================end

    //===================模块配置管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdMDL_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdMDL_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/MDLMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/MDLMan/GetMDLList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['ID', 'module_num', 'module_name', 'module_type', 'is_show', 'url', 'app_url', 'define_f', 'define_s']
        }),
        sortInfo: { field: "ID", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdMDL_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "ID", width: 120, sortable: true, dataIndex: 'ID' },
            { header: "module_num", width: 170, sortable: true, dataIndex: 'module_num' },
            { header: "module_name", width: 170, sortable: true, dataIndex: 'module_name' },
            { header: "module_type", width: 170, sortable: true, dataIndex: 'module_type' },
            { header: "is_show", width: 170, sortable: true, dataIndex: 'is_show' },
            { header: "url", width: 170, sortable: true, dataIndex: 'url' },
            { header: "app_url", width: 170, sortable: true, dataIndex: 'app_url' },
            { header: "define_f", width: 170, sortable: true, dataIndex: 'define_f' },
            { header: "define_s", width: 170, sortable: true, dataIndex: 'define_s' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================模块配置管理form===========================end
};
BaseOpt.MDL_ManageMain.Initialize();
