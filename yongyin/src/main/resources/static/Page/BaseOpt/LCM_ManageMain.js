Ext.namespace('BaseOpt.LCM_ManageMain');

//初始化函数
BaseOpt.LCM_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.LCM_ManageMain';
    var currlcid = 0;
    var currlcname = "";
    var gProcClose = 0;

    //上传表单定义
    var textLCID = new Ext.form.TextField({
        xtype: "textfield",
        name: 'LCID',
        fieldLabel: "流程编号",
        anchor:'98%',
        allowBlank: true,
        readOnly:true,
        anchor: '98%'
    });

    var textLCNAME = new Ext.form.TextField({
        xtype: "textfield",
        name: 'LCNAME',
        fieldLabel: "流程编号",
        anchor: '98%',
        allowBlank: true,
        readOnly: true,
        anchor: '98%'
    });

    var textArea = new Ext.form.TextArea({
        xtype: "textarea",
        name: 'FileCallBack',
        fieldLabel: "导入反馈",
        vtype:'customtextfield',
        preventScrollbar: false,
        grow: false,
        height: 360,
        allowBlank: true,
        maxLengthText: '最多只允许输入100个字符,一个中文算俩个字符',
        anchor: '98%'
    });

    var progressBar = new Ext.ProgressBar({
        text: '0%',
        xtype: "progress",
        name: 'HandProgBar',
        fieldLabel: "处理进度",
        allowBlank: true,
        anchor: '99%',
        animate: true,
        height: 20
    });

    var MAINT_Form = new Ext.FormPanel({
        labelWidth: 80,
        width: 600,
        height: 480,
        frame: true,
        fileUpload: true,
        bodyStyle: 'padding:10px',
        items:
        [
            textLCID,
            textLCNAME,
            textArea,
            {   //行四
                layout: 'column',
                items:
                [
                    {
                        columnWidth: 1,
                        columnHeith: 1,
                        layout: 'form',
                        items: [
                            {
                                xtype: "label",
                                html: "<div style='width:95%;height:5px;text-align:right; color:red'>&nbsp;</div>",
                                style: 'font-size:10px;',
                                anchor: '100%'
                            }
                        ]
                    }
                ]
            },
            {   //行四
                layout: 'column',
                items:
                [
                    {
                        columnWidth: 0.98,
                        columnHeith: 1,
                        layout: 'form',
                        items: [
                            progressBar
                        ]
                    }
                ]
            }
        ],
        reader: new Ext.data.JsonReader({
            id: 'Id',
            root: 'data',
            fields: []
        })
    });

    var MAINT_FormWindow = new Ext.Window({
        maximized: false,
        width: 720,
        height: 580,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: false,
        items: [MAINT_Form],
        title: '维护流程数据',
        buttonAlign: 'center',
        buttons: [
            {
                text: '确定',
                height: 30,
                handler: function () {
                    if (currlcid > 0) {
                        ExecuteSync();
                    }
                }
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    MAINT_FormWindow.hide();
                    RefreshData();
                }
            }
        ]
     });

    var ExecuteSync = function () {
        textArea.setValue("");
        MAINT_FormWindow.buttons[0].disable();
        MAINT_FormWindow.buttons[1].disable();
        gProcClose = 0;

        Ext.Ajax.request({
            url: '../../Service/BaseOpt/LCMMan/ExecuteSync',
            method: 'post',
            params: { lcid: currlcid }
        });
        DelayFunc.delay(3000);
    }

    var ClearLCDATAClick = function (clcid) {
        Ext.Ajax.request({
            url: '../../Service/BaseOpt/LCMMan/ExecuteClear',
            method: 'post',
            params: { lcid: clcid },
            success: function (res) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);
                if (resp.success == true || resp.success == "true") {
                    Ext.MessageBox.alert("操作提示", "清理空置流程记录成功！", function () {
                        RefreshData();
                    });
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    function SyncLCDATAClick(lcid, lcname) {
        textArea.setValue("");
        currlcid = lcid;
        currlcname = lcname;
        textLCID.setValue(lcid);
        textLCNAME.setValue(lcname);
        MAINT_FormWindow.show();
    }

    var DelayFunc = new Ext.util.DelayedTask(function () {
        GetAutoProcessInfo();
    });

    function GetAutoProcessInfo() {
        if (gProcClose == 1) return;
        var url = '../../Service/BaseOpt/LCMMan/GetProcessInfo';
        Ext.Ajax.request({
            url: url,
            method: 'POST',
            success: function (response, options) {
                var json0 = Ext.util.JSON.decode(response.responseText);
                if (json0.success == true || json0.success == "true") {
                    textArea.setValue(json0.msginfo);

                    if (parseInt(json0.percent) != 100) {
                        if (parseInt(json0.percent) == -1) {
                            Ext.MessageBox.alert('提示', "本次执行同步失败！");
                            MAINT_FormWindow.buttons[0].enable();
                            MAINT_FormWindow.buttons[1].enable();
                        } else {
                            var preint = parseInt(json0.percent) / 100;
                            progressBar.updateProgress(preint, json0.percent.toString() + "%");
                            DelayFunc.delay(3000);
                        }
                    } else {
                        progressBar.updateProgress(1, "100%");
                        Ext.MessageBox.alert('提示', "本次执行同步成功！");
                        MAINT_FormWindow.buttons[0].enable();
                        MAINT_FormWindow.buttons[1].enable();
                        gProcClose = 1;
                    }
                }
            },
            failure: function (form, action) {
                DelayFunc.delay(3000);
            }
        });
    }
    //=======================================================================start  

    //===============================================================start
    var toolbar = new Ext.Toolbar({
        items:
        [
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdLCM_Grid").store.reload({ params: {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        Ext.getCmp(funcMark + ".IdLCM_Grid").store.reload({ params: {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/LCMMan/GetLCMList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['LcID', 'lcName', 'ywb', 'YWB_QUAN', 'QYWB_QUAN', 'NSYNC_QUAN', 'LCO_STATE', 'LCO_MSG']
        }),
        sortInfo: { field: "LcID", direction: "ASC" },
        remoteSort: true
    });

    function setIntColumn(val, metadata, record) {
        if (val == null)return '';
        return val;
    }

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdLCM_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "流程ID", width: 100, sortable: true, dataIndex: 'LcID' },
            { header: "流程名称", width: 170, sortable: true, dataIndex: 'lcName'},
            { header: "配置主表信息", width: 300, sortable: true, dataIndex: 'ywb' },
            { header: "共有流程", width: 100, sortable: true, dataIndex: 'YWB_QUAN' },
            { header: "空置流程(无主记录)", width: 100, sortable: true, dataIndex: 'QYWB_QUAN' },
            { header: "执行清除", width: 100, sortable: false, dataIndex: 'QYWB_QUAN',
                renderer: function (val, metadata, record) {
                    if (val > 0) val = "执行清除";
                    else val = "";
                    metadata.style = "width:100;color: blue;text-align:center;text-decoration:underline;";
                    return val;
                }
            },
            { header: "需同步(可以加速首页查询)", width: 100, sortable: true, dataIndex: 'NSYNC_QUAN' },
            { header: "执行同步", width: 100, sortable: false, dataIndex: 'NSYNC_QUAN',
                renderer: function (val, metadata, record) {
                    if (val > 0) val = "执行同步";
                    else val = "";
                    metadata.style = "width:100;color: blue;text-align:center;text-decoration:underline;";
                    return val;
                }
            },
            { header: "检测标志", width: 100, sortable: true, dataIndex: 'LCO_STATE' },
            { header: "检测说明", width: 200, sortable: true, dataIndex: 'LCO_MSG' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: !_uiwmask,
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var record = grid.store.getAt(rowIndex);
                var qywb = record.get("QYWB_QUAN");
                var nsync = record.get("NSYNC_QUAN");
                var state = record.get("LCO_STATE");
                var lcid = record.get("LCID");
                var lcname = record.get("LCNAME");
                if (state == 1) {
                    if (qywb > 0 && columnIndex == 6) {
                        ClearLCDATAClick(lcid);
                    } else if (nsync > 0 && columnIndex==8) {
                        SyncLCDATAClick(lcid, lcname);
                    }   
                }
            }
        }
    });

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn: function () {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================内网认定项管理form===========================end
};
BaseOpt.LCM_ManageMain.Initialize();
