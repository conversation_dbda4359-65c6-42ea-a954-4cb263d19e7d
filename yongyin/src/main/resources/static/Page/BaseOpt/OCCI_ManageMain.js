Ext.namespace('BaseOpt.OCCI_ManageMain');

//初始化函数
BaseOpt.OCCI_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.OCCI_ManageMain';
    var qDate = "";
    var qSec = "";

    //===================内网认定项管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
              '日志日期',
             {
                 id: funcMark + '.toolbar.IdDate',
                 xtype: "datefield",
                 name: 'Start',
                 format: 'Y-m-d',
                 width: 160
             },
             '耗时(>毫秒)',
             {
                 id: funcMark + '.toolbar.IdSec',
                 xtype: "textfield",
                 name: 'Sec',
                 width: 120
              },
              '-',
              '->',
              '-',
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdOCCI_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            qdate: qDate,
            qsec: qSec
        }
        });
    }

    function LoadData() {
        start = 0;
        qDate = Ext.getCmp(funcMark + '.toolbar.IdDate').getValue();
        qSec= Ext.getCmp(funcMark + '.toolbar.IdSec').getValue();
        Ext.getCmp(funcMark + ".IdOCCI_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            qdate: qDate,
            qsec: qSec
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/OcciMan/ExportExcel", {
            startdt: startDt,
            enddt: endDt,
            qdate: qDate,
            qsec: qSec
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/OcciMan/GetOCCIList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['OCCI_FILE', 'OCCI_FUNC','OCCI_START','OCCI_END','OCCI_SEC']
        })
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdOCCI_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-Image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "调用信息", width: 170, sortable: true, dataIndex: 'OCCI_FILE' },
            { header: "接口函数", width: 350, sortable: true, dataIndex: 'OCCI_FUNC'},
            { header: "开始时间", width: 170, sortable: true, dataIndex: 'OCCI_START' },
            { header: "结束时间", width: 170, sortable: true, dataIndex: 'OCCI_END' },
            { header: "耗时(秒)", width: 170, sortable: true, dataIndex: 'OCCI_SEC' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['qdate'] = qDate;
                o['qsec'] = qSec;
                this.store.load({ params: o });
            }
        })
    });

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================内网认定项管理form===========================end
};
BaseOpt.OCCI_ManageMain.Initialize();
