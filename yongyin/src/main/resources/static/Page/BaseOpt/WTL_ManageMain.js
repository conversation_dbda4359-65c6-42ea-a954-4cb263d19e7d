Ext.namespace('BaseOpt.WTL_ManageMain');

//初始化函数
BaseOpt.WTL_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.WTL_ManageMain';

    //===================白名单配置界面===========================start
    var WTL_Form = new Ext.FormPanel({
        labelWidth: 120,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.WTL_Form.IdId',
                name: 'Id',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.WTL_Form.IdIpAddr',
                xtype: "textfield",
                name: 'IpAddr',
                fieldLabel: "IP地址",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.WTL_Form.IdFuncName',
                xtype: "textfield",
                name: 'FuncName',
                fieldLabel: "模块名称",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'Id', mapping: 'Id', type: 'int' },
                { name: 'IpAddr', mapping: 'IpAddr', type: 'string' },
                { name: 'FuncName', mapping: 'FuncName', type: 'string' }
            ]
        })
    });

    var WTL_FormWindow = new Ext.Window({
        id: funcMark + '.IdWTL_FormWindow',
        width: 540,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: WTL_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                disabled: true
            },
            {
                text: '关闭',
                handler: function () {
                    WTL_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        WTL_Form.form.reset();

        WTL_FormWindow.setTitle(ExtractTitleString("白名单配置==新增白名单配置=="));
        WTL_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (WTL_Form.form.isValid()) {
                WTL_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/WTLMan/AddWTL',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        WTL_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        WTL_FormWindow.buttons[0].enable();
        WTL_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdWTL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "白名单配置==修改白名单配置==";
            WTL_FormWindow.setTitle(ExtractTitleString(title));
            WTL_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = WTL_Form.form.reader.jsonData.data[0].Id;
                if (WTL_Form.form.isValid()) {
                    WTL_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/WTLMan/ModifyWTL',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            WTL_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            WTL_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/WTLMan/GetWTLById?ID=' + id;
            WTL_FormWindow.show();
            WTL_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    WTL_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdWTL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "白名单配置==查看白名单配置==";
            WTL_FormWindow.setTitle(ExtractTitleString(title));
            WTL_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.Id;
            var url = '../../Service/BaseOpt/WTLMan/GetWTLById?ID=' + id;
            WTL_FormWindow.show();
            WTL_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    WTL_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdWTL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/WTLMan/DeleteWTL",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.Id }
                    });
                }
            });
        }
    }
    //===================白名单配置界面===========================end  

    //===================白名单配置管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdWTL_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdWTL_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/WTLMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/WTLMan/GetWTLList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'IpAddr', 'FuncName']
        }),
        sortInfo: { field: "IpAddr", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdWTL_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "IP地址", width: 120, sortable: true, dataIndex: 'IpAddr' },
            { header: "函数名称", width: 170, sortable: true, dataIndex: 'FuncName' },
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================白名单配置管理form===========================end
};
BaseOpt.WTL_ManageMain.Initialize();
