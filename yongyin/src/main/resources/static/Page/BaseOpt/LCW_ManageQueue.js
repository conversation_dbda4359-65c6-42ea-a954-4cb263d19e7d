DownloadJSByParams('../../Page/CPSoft/common.js', function () {
    var CssHead = [
        '../../Page/CPSoft/Spinner.css?version=' + _cps_js_version,
        '../../Page/CPSoft/CustomUI.css?version=' + _cps_js_version
      ];

    var FilesArray = [
        '../../Page/CPSoft/UxSpinner.js?version=' + _cps_js_version,
        '../../Page/CPSoft/UxSpinnerField.js?version=' + _cps_js_version,
        '../../Page/CPSoft/ExtTextField.js?version=' + _cps_js_version,
        '../../Page/CPSoft/ExtGridExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version='+_cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version=' + _cps_js_version,
        '../../Page/CPSoft/UxEditProx.js?version=' + _cps_js_version,
        '../../Page/BaseOpt/MetaJs/CurrentStateMain.js?version=' + _cps_js_version,
        '../../Page/BaseOpt/MetaJs/WorkFlowMain.js?version=' + _cps_js_version
     ];

    Import.LoadJsList(FilesArray, function () {
        DownloadJS('../../Page/BaseOpt/LCW_ManageMain.js?version='+_cps_js_version);
    });

    document.getElementById('../../Page/BaseOpt/LCW_ManageQueue.js').Destroy = function()
    {
        var element = document.getElementById('../../Page/BaseOpt/LCW_ManageMain.js?version=' + _cps_js_version);
        if(element!=undefined){
            element.parentNode.removeChild(element);
        }
        Ext.getCmp("BaseOpt.LCW_ManageMain.IdLCW_FormWindow").destroy();
    }
    document.getElementById('../../Page/BaseOpt/LCW_ManageQueue.js').DataReload = function () {

    }
});
