Ext.namespace('BaseOpt.MGP_ManageMain');

//初始化函数
BaseOpt.MGP_ManageMain.Initialize = function () {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'BaseOpt.MGP_ManageMain';

    //===================模块分组界面===========================start
    var MGP_Form = new Ext.FormPanel({
        labelWidth: 120,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {
                id: funcMark + '.MGP_Form.IdGP_ID',
                name: 'GP_ID',
                xtype: "hidden"
            },
            {   //行六
                id: funcMark + '.MGP_Form.IdGP_NAME',
                xtype: "textfield",
                name: 'GP_NAME',
                fieldLabel: "GP_NAME",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            },
            {   //行六
                id: funcMark + '.MGP_Form.IdGP_MLIST',
                xtype: "textfield",
                name: 'GP_MLIST',
                fieldLabel: "GP_MLIST",
                maxLength: 100,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'GP_ID', mapping: 'GP_ID', type: 'int' },,
                { name: 'GP_NAME', mapping: 'GP_NAME', type: 'string' },
                { name: 'GP_MLIST', mapping: 'GP_MLIST', type: 'string' }
            ]
        })
    });

    var MGP_FormWindow = new Ext.Window({
        id: funcMark + '.IdMGP_FormWindow',
        width: 640,
        height: 420,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: MGP_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                disabled: true
            },
            {
                text: '关闭',
                handler: function () {
                    MGP_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {
        MGP_Form.form.reset();

        MGP_FormWindow.setTitle(ExtractTitleString("模块分组==新增模块分组=="));
        MGP_FormWindow.buttons[0].handler = function () {
            var submitButton = this;
            submitButton.disable();
            if (MGP_Form.form.isValid()) {
                MGP_Form.form.doAction('submit',
                {
                    url: '../../Service/BaseOpt/MGPMan/AddMGP',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function (form, action) {
                        submitButton.enable();
                        MGP_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function (form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        MGP_FormWindow.buttons[0].enable();
        MGP_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdMGP_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "模块分组==修改模块分组==";
            MGP_FormWindow.setTitle(ExtractTitleString(title));
            MGP_FormWindow.buttons[0].handler = function () {
                var submitButton = this;
                submitButton.disable();

                var id = MGP_Form.form.reader.jsonData.data[0].GP_ID;
                if (MGP_Form.form.isValid()) {
                    MGP_Form.form.doAction('submit',
                    {
                        url: '../../Service/BaseOpt/MGPMan/ModifyMGP',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function (form, action) {
                            submitButton.enable();
                            MGP_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function (form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            MGP_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.GP_ID;
            var url = '../../Service/BaseOpt/MGPMan/GetMGPById?ID=' + id;
            MGP_FormWindow.show();
            MGP_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    MGP_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdMGP_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "模块分组==查看模块分组==";
            MGP_FormWindow.setTitle(ExtractTitleString(title));
            MGP_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.GP_ID;
            var url = '../../Service/BaseOpt/MGPMan/GetMGPById?ID=' + id;
            MGP_FormWindow.show();
            MGP_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function (form, action) {
                    MGP_FormWindow.buttons[0].enable();
                },
                failure: function (form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark + ".IdMGP_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function (btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/BaseOpt/MGPMan/DeleteMGP",
                        successfn: function (data) {
                            RefreshData();
                        },
                        failurefn: function (data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.GP_ID }
                    });
                }
            });
        }
    }
    //===================模块分组界面===========================end  

    //===================模块分组管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
              '-',
              '->',
              '-',
              '模糊查询',
              {
                  id: funcMark + '.toolbar.SearchTJ',
                  xtype: 'textfield',
                  width: 200,
                  name: 'SearchTJ',
                  anchor: '98%'
              },
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: LoadData
               },
               {
                   text: '刷新',
                   id: funcMark + '.toolbar.IdREFRESH',
                   iconCls: 'ButtonFormLook',
                   listeners: {
                       click: function () {
                           RefreshData();
                       }
                   }
               },
               {
                   text: '导出XLS',
                   iconCls: 'ButtonFormEdit',
                   handler: ExportExcel
               }
	       ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark + ".IdMGP_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdMGP_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/BaseOpt/MGPMan/ExportExcel", {
            seachtj: Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue()
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/BaseOpt/MGPMan/GetMGPList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['GP_ID', 'GP_NAME', 'GP_MLIST']
        }),
        sortInfo: { field: "GP_ID", direction: "ASC" },
        remoteSort: true
    });
    
    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark + '.IdMGP_Grid',
        width: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({width:35,css:"background-color: #EEEEEE; background-image: none;",renderer: function (v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "GP_ID", width: 120, sortable: true, dataIndex: 'GP_ID' },
            { header: "GP_NAME", width: 170, sortable: true, dataIndex: 'GP_NAME' },
            { header: "GP_MLIST", width: 170, sortable: true, dataIndex: 'GP_MLIST' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,

        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark + '.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });
    LoadData();

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================模块分组管理form===========================end
};
BaseOpt.MGP_ManageMain.Initialize();
