function QueryString(lname){
    var name,
        value,
        i;
    var str = location.href;
    var num = str.indexOf("?")
    str = str.substr(num + 1);
    var arrtmp = str.split("&");
    for(i = 0; i < arrtmp.length; i ++ ){
        num = arrtmp[i].indexOf("=");
        if(num > 0){
            name = arrtmp[i].substring(0, num);
            value = arrtmp[i].substr(num + 1);
            if(name == lname)
                return value;
        }
    }
    return "";
}

function encryptByAES(message, key) {
    var keyHex = CryptoJS.enc.Utf8.parse(key);
    var ivHex = CryptoJS.enc.Utf8.parse(key);
    var encrypted = CryptoJS.AES.encrypt(message, keyHex, {
        iv: ivHex,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
}

function CloseWindow(window) {
    try {
        var browserName = navigator.appName;
        if (browserName == "Netscape")
        {
            window.location.href = "about:blank";
            window.close();
        } else if (browserName == "Microsoft Internet Explorer")
        {
            window.opener = null;
            window.close();
        }
    } catch (e) {

    }
}

function IntervalFunc() {
    UpatePersonInfo(function () {
    }, function () {
        Ext.MessageBox.alert("操作提示","未检测到登录信息，系统将退出！！！",function(){
            CloseWindow(window);
        });
    });
}

function setIntervalProx() {
    if (_stimeout != undefined&&_stimeout!=0) {
        var smvar = setInterval(IntervalFunc,_stimeout);
    }
}

function getUIWMaskOption(succ, fail) {
    AjaxRequestText({
        url: "../../Service/CpSoft/CmmOpt/GetUIWMaskOption",
        successfn: function (value) {
            _uiwmask = true;
            if (succ) succ();
        },
        failurefn: function (value) {
            _uiwmask = false;
            if (fail) fail();
        },
        jexceptfn: function (value) {
            if (fail) fail();
            //Ext.MessageBox.alert("系统异常",'系统异常，返回值异常:'+value);
        }
    });
}

function getSessionTimeOut(succ, fail) {
    AjaxRequestText({
        url: "../../Service/CpSoft/CmmOpt/GetSessionTimeOut",
        successfn: function (value) {
            if (parseInt(value) != 0 && parseInt(value) != undefined) {
                _stimeout = parseInt(value)*1000;
            }
            if (succ) succ();
        },
        failurefn: function (value) {
            if (fail) fail();
        },
        jexceptfn: function (value) {
            if (fail) fail();
            //Ext.MessageBox.alert("系统异常",'系统异常，返回值异常:'+value);
        }
    });
}

function showNotification(){
    setTimeout(function(){
        var notification = new NotificationFx({
            message : '<div class="ns-thumb"><img src="../../Image/user2.jpg"/></div><div class="ns-content"><p><a href="javascript:LXDWindowShow();">您有一条新通知，请及时查收！</a></p></div>',
            layout : 'other',
            ttl : 6000,
            effect : 'thumbslider',
            type : 'notice',
            // notice, warning, error or success
            onClose : function(){
                //bttn.disabled = false;
            }
        });
        notification.show();

    }, 1200);
}

function InitViewLoading()
{
    _sysmark=QueryString("sm");
    _mid=QueryString("mid");

    var storage = window.sessionStorage;
    if(storage!=undefined)
    {
        _token = storage.getItem(_sysmark+"_token");
        _xsystem=storage.getItem(_sysmark+"_xsystem");
        _cps_js_version=storage.getItem(_sysmark+"_cpsversion");
    }

    var lstorage = window.localStorage;
    if(lstorage!=undefined)
    {
        _glayoutheme=lstorage.getItem("layout-theme");
    }

    if(_token!=undefined&&_token!='')
    {
        Ext.Ajax.defaultHeaders={
            Authorization: _token
        };

        UpatePersonInfo(function () {
            //TiggerMAFunction();
            getUIWMaskOption(function () {
                if (_uiwmask) DownloadJSByParams("../../Page/CPSoft/ExtUxExtend.js");
            });
            getSessionTimeOut();

            MainInit();

            if(LoginPerson.EmailNum == 0){
                document.getElementById("MsgNum").innerHTML = "0";
            }
            else if(LoginPerson.EmailNum > 9){
                document.getElementById("MsgNum").innerHTML = "9+";
            }
            else{
                document.getElementById("MsgNum").innerHTML = LoginPerson.EmailNum;
            }

            SafeOpenSchdeule(_sysmark);

            setIntervalProx();

            getTopOption();
            loadMenu();

            //alert(_mid);
            if(window.opener!=null&&window.opener!=undefined)
            {
                var message = {msgtype:'wpsfm',mid:_mid,statue:'加载成功!'};
                window.opener.postMessage(message,'*');
            }

            if(_glayoutheme!=null&&_glayoutheme!=undefined&&_glayoutheme!='#01A7E7')
            {
                $(".headcontainer").css("background-color",_glayoutheme+" !important")
            }

            loadfinish=true;
        }, function () {
            CloseWindow(window);
        });
    }else
    {
        Ext.MessageBox.alert("提示","系统异常，授权信息缺失！",function()
        {
            CloseWindow(window);
        });
    }
}

function loadMenu(gid) {
    $.ajax({
        type: "GET",
        headers: {
            Authorization:_token
        },
        url: "../../Service/Base/Menu/RefreshMenu?showimg=true&tshow=true",
        success: function (result) {
            $("#menu")[0].innerHTML=result;
            initMenu();
        },
        error: function (e) {
        }
    });
}

Ext.onReady(function () {
    var width = $("body").width() - 200;
    var height = $("body").height() - 88;

    InitViewLoading();
});

function getTopOption() {
    Ext.Ajax.request({
        url: "../../Service/Base/Frame/GetTopOption",
        success: function(response, options) {
            var res = GetJSONObject(response.responseText);
            if(res!=undefined)
            {
                if(res.success=="true"||res.success==true)
                {
                    //document.getElementById("spanTitle").innerHTML=jsons.title;
                    document.getElementById("spanwelcom").innerHTML=res.welcome;
                }
            }
        }
    });
}

function GetJSONObject(str){
    if(str){
        return Ext.util.JSON.decode(str);
    }
    else{
        return null;
    }
}

function displayCurLocPageProx(mB)
{
    var mA=showmText;
    displayCurLocPage(mA,mB);
}

function displayCurLocPage(mA, mB){
    var span = Ext.get(Ext.DomQuery.selectNode("span#id-mainpage-curloc"));
    if(span){
        if(mA){
            span.update(mA);
            if(mB){
                span.update(mA + ">" + mB);
            }
        }
        else{
            span.update('');
        }
    }
}

function onPasswordButtonClick()
{
    var oUpdatePassword = new UpdatePassword();
    oUpdatePassword.ShowWindow();
}

function LXDWindowShow(){
    var LXDWindow = new EmailSelectWindow();
    LXDWindow.Show();
}

function initMenu() {
    $('#menu ul').hide();
    $('#menu li ul:first').show();

    $('#menu>li>div').click(
        function ()
        {
            var cidv = $(this).attr("cid");
            var cnamev = $(this).attr("cname");
            if(cnamev!=undefined) {
                showmText=cnamev;
                displayCurLocPageProx("");
            }

            var ulElement = $("ul[cid='" + cidv + "']");
            if ((ulElement!=undefined) && (ulElement.is(':visible')))
            {
                ulElement.slideUp('normal');
                var liElement = $("li[cid='" + cidv + "']");
                if(liElement!=undefined){
                    liElement.css({"border":"0px solid #adc2eb","border-bottom":"1px solid #a0cfff","background-color":"#dbe6f5"});
                }
                var childElement = $(this).find("img[id='" + cidv + "_image']");
                childElement.attr('src', '../../Image/Menus/arrow_down.gif');
                return false;
            }
            if ((ulElement!=undefined) && (!ulElement.is(':visible')))
            {
                ulElement.slideDown('normal');
                var liElement = $("li[cid='" + cidv + "']");
                if(liElement!=undefined){
                    liElement.css({"border":"1px solid #adc2eb","background-color":"#B5E2F9","background-image":"none"});
                }
                var childElement = $(this).find("img[id='" + cidv + "_image']");
                childElement.attr('src', '../../Image/Menus/arrow_up.gif');

                //关闭其他已经打开的兄弟网格
                var pitems=$(this).parent().siblings("li");
                if(pitems!=undefined)
                {
                    $.each(pitems,function(i,item){
                        var cidv = $(this).attr("cid");
                        var ulEl = $("ul[cid='" + cidv + "']");
                        var divEl = $(this).find("div[cid='" + cidv + "']");
                        if (divEl!=undefined && ulEl.is(':visible'))
                        {
                            divEl.click();
                        }
                    });
                }

                return false;
            }
        }
    );

    $('#menu>ul>li>div').click(
        function () {
            var cidv = $(this).attr("cid");
            var ulElement = $("ul[cid='" + cidv + "']")
            if ((ulElement!=undefined) && (ulElement.is(':visible'))) {
                ulElement.slideUp('normal');
                var liElement = $("li[cid='" + cidv + "']");
                if(liElement!=undefined)
                {
                    liElement.css({"border":"0px solid #adc2eb","border-bottom":"1px solid #a0cfff","background-color":"#dbe6f5"});
                }
                var childElement = $(this).find("img[id='" + cidv + "_image']");
                childElement.attr('src', '../../Image/Menus/arrow_down.gif');
                return false;
            }
            if ((ulElement!=undefined) && (!ulElement.is(':visible'))) {
                $('#menu li ul li ul:visible').slideUp('normal');
                ulElement.slideDown('normal');
                var liElement = $("li[cid='" + cidv + "']");
                if(liElement!=undefined)
                {
                    liElement.css({"border":"1px solid #adc2eb","background-color":"#B5E2F9"});
                }
                var childElement = $(this).find("img[id='" + cidv + "_image']");
                childElement.attr('src', '../../Image/Menus/arrow_up.gif');

                //关闭其他已经打开的兄弟网格
                var pitems=$(this).parent().siblings("li");
                if(pitems!=undefined)
                {
                    $.each(pitems,function(i,item){
                        var cidv = $(this).attr("cid");
                        var ulEl = $("ul[cid='" + cidv + "']");
                        var divEl = $(this).find("div[cid='" + cidv + "']");
                        if (divEl!=undefined && ulEl.is(':visible'))
                        {
                            divEl.click();
                        }
                    });
                }

                return false;
            }
        }
    );

    $('#menu>ul>ul>li>div').click(
        function () {
            var cidv = $(this).attr("cid");
            var ulElement = $("ul[cid='" + cidv + "']")
            if ((ulElement!=undefined) && (ulElement.is(':visible'))) {
                ulElement.slideUp('normal');
                var liElement = $("li[cid='" + cidv + "']");
                if(liElement!=undefined){
                    liElement.css({"border":"0px solid #adc2eb","border-bottom":"1px solid #a0cfff","background-color":"#dbe6f5"});
                }
                var childElement = $("img[id='" + cidv + "_image']");
                childElement.attr('src', '../../Image/Menus/arrow_down.gif');
                return false;
            }
            if ((ulElement!=undefined) && (!ulElement.is(':visible'))) {
                $('#menu li ul li ul:visible').slideUp('normal');
                ulElement.slideDown('normal');
                var liElement = $("li[cid='" + cidv + "']");
                if(liElement!=undefined){
                    liElement.css({"border":"1px solid #adc2eb","background-color":"#B5E2F9"});
                }
                var childElement = $(this).find("img[id='" + cidv + "_image']");
                childElement.attr('src', '../../Image/Menus/arrow_up.gif');

                //关闭其他已经打开的兄弟网格
                var pitems=$(this).parent().siblings("li");
                if(pitems!=undefined)
                {
                    $.each(pitems,function(i,item){
                        var cidv = $(this).attr("cid");
                        var ulEl = $("ul[cid='" + cidv + "']");
                        var divEl = $(this).find("div[cid='" + cidv + "']");
                        if (divEl!=undefined && ulEl.is(':visible'))
                        {
                            divEl.click();
                        }
                    });
                }

                return false;
            }
        }
    );

    if ($('#menu>li>div')[0] != undefined)
    {
        $('#menu>li>div')[0].click();
    }
}

function OpenFullWindow(url) {
    var tmpw = window.open('about:blank');
    tmpw.location = url;

    //ShowModalWin(urlstr, "", w, h);
}