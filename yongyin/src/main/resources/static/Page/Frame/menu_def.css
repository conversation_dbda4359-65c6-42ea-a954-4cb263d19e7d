.def-menudrager-ul
{
    list-style-type:none;
    margin: 0;
    padding: 0;
    font-size:12px;
    font-family:新宋体,宋体;
}
.def-menudrager-ul a{
    text-decoration: none;
}
.def-menudrager-ul li{
    margin:0px;
    margin-top:2px;
    padding:0px;
    height:42px;
    line-height:42px;
    background-color: #F0FFF0;
    background-image: -moz-linear-gradient(top, #f4f4f5, #c7d9f7);
    background-image: -webkit-linear-gradient(top, #f4f4f5, #c7d9f7);
    background-image: -o-linear-gradient(top, #f4f4f5, #c7d9f7);
    background-image: linear-gradient(to bottom, #f4f4f5, #c7d9f7);
    border-bottom:1px solid #c7d9f7;
}
.def-menudrager-ul li div{
    color: #000000;
    display: block;
    text-decoration: none;
    padding-left:15px;
    height:42px;
    line-height:42px;
    display: flex;
    align-items: center;
}
.def-menudrager-ul li:hover{
    background-color: #B5E2F9 !important;
    background-image:none !important;
    border-left: 5px #328DBE solid !important;
}
.def-menudrager-ul ul{
    list-style-type:none;
    margin: 0px;
    padding: 0px;
    font-size:12px;
    font-family:新宋体,宋体;
    border:1px solid #adc2eb;
}
.def-menudrager-ul ul li{
    margin:0px;
    height:42px;
    line-height:42px;
    background-color: #ebf3fd;
    background-image: -moz-linear-gradient(left, #ebf3fd, #c7d9f7);
    background-image: -webkit-linear-gradient(left, #ebf3fd, #c7d9f7);
    background-image: -o-linear-gradient(left, #ebf3fd, #c7d9f7);
    background-image: linear-gradient(to right, #ebf3fd, #c7d9f7);
    border-bottom:1px solid #a0cfff;
}
.def-menudrager-ul ul li div{
    border:0px solid red;
    color: #000;
    padding-left: 25px;
    text-decoration: none;
    height:42px;
    line-height:42px;
    display: flex;
    align-items: center;
}
.def-menudrager-ul ul li:hover{
    background-color: #B5E2F9 !important;
    background-image:none !important;
    border-left: 5px #328DBE solid !important;
}
.def-menudrager-ul ul ul
{
    padding:0px;
    margin:0px;
    list-style-type:none;
    background-color:Transparent;
    color: #000;
    border: 1px #adc2eb solid;
}
.def-menudrager-ul ul ul li{
    margin:0px;
    height:42px;
    line-height:42px;
    background-color: #ebf3fd;
    background-image: -moz-linear-gradient(left, #c7d9f7, #ebf3fd);
    background-image: -webkit-linear-gradient(left, #c7d9f7, #ebf3fd);
    background-image: -o-linear-gradient(left, #c7d9f7, #ebf3fd);
    background-image: linear-gradient(to right, #c7d9f7, #ebf3fd);
    border-bottom:1px solid #a0cfff;
}
.def-menudrager-ul ul ul li div{
    border:0px;
    color: #000;
    padding-left: 35px;
    text-decoration: none;
    margin:0px;
    height:42px;
    line-height:42px;
    display: flex;
    align-items: center;
}
.def-menudrager-ul ul ul li:hover{
    background-color: #B5E2F9 !important;
    background-image:none !important;
    border-left: 5px #328DBE solid !important;
}

.def-menudrager-ul ul ul ul
{
    padding:0px;
    margin:0px;
    list-style-type:none;
    background-color:Transparent;
    color: #000;
    border: 1px #adc2eb solid;
}
.def-menudrager-ul ul ul ul li{
    margin:0px;
    height:42px;
    line-height:42px;
    background-color: #ebf3fd;
    background-image: -moz-linear-gradient(left, #ebf3fd, #c7d9f7);
    background-image: -webkit-linear-gradient(left, #ebf3fd, #c7d9f7);
    background-image: -o-linear-gradient(left, #ebf3fd, #c7d9f7);
    background-image: linear-gradient(to right, #ebf3fd, #c7d9f7);
    border-bottom:1px solid #a0cfff;
}
.def-menudrager-ul ul ul ul li div{
    border:0px;
    color: #000;
    padding-left: 45px;
    margin:0px;
    height:42px;
    line-height:42px;
    display: flex;
    align-items: center;
}
.def-menudrager-ul ul ul ul li:hover{
    background-color: #B5E2F9 !important;
    background-image:none !important;
    border-left: 5px #328DBE solid !important;
}
.def-menudrager-outtertable
{
    background: Transparent;
    border:0px solid red;
    padding:0px;
    margin:0px;
    font-size:13px;
    font-weight: bold;
    font-family:新宋体,宋体;
    width:100%;
    color:#1f637b;
    text-decoration:none;
    vertical-align: center;
}
.def-menudrager-innertable
{
    background: Transparent;
    border:0px solid red;
    padding:0px;
    margin:0px;
    font-size:12px;
    font-weight: bold;
    font-family:新宋体,宋体;
    color:#1f637b;
    width:100%;
    text-decoration:none;
    vertical-align: center;
}
.headimage
{
    width:20px;
    height:20px;
    margin-right:6px;
    display: block;
}
.firstTitle
{
    width:160px;
}
.secondTitle
{
    width:150px;
}
.thirdTitle
{
    width:140px;
}
.levelimage
{
    width:8px;
    height:8px;
}