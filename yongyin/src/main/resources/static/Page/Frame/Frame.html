<html xmlns="http://www.w3.org/1999/xhtml" oncontextmenu="window.event.returnValue=true">
<head>
    <title>数字化平台--平台管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <link rel="stylesheet" type="text/css" href="./menu_def.css" />
    <script language="javascript" src="../../JS/ValueCheck.js" type="text/jscript"></script>
    <script language="javascript" src="../../JS/CommJs.js" type="text/jscript"></script>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/ExtResources/css/ext-all.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/lovcombo/css/Ext.ux.form.LovCombo.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/Grid.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/Page.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/Loading.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/file-upload.css"/>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/animated-dataview.css"/>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/LockingGridView.css"/>
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/data-view.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/ext_custom.css" />
    <link rel="stylesheet" type="text/css" href="../../Page/CPSoft/CustomUI.css" />
    <link rel="stylesheet" type="text/css" href="../../JSControl/ExtJs/DateTimeField/css/Spinner.css"/>
    <script type="text/javascript" src="../../JSControl/ExtJs/ext-base.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ext-all.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ext-lang-zh_CN.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/TabCloseMenu.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/FileUploadField.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/ColumnHeaderGroup.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/GroupHeaderGrid.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/LockingGridView.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/ImageButton.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ux/ColorField.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/PagingMemoryProxy.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/ExtDateTime.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/DateTimeField/Spinner.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/DateTimeField/SpinnerField.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/DateTimeField/DateTimeField.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/LockingGridView.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/lovcombo/js/Ext.ux.form.LovCombo.js"></script>
    <script type="text/javascript" src="../../JSControl/ExtJs/lovcombo/lovcombo.js"></script>
    <script type="text/javascript" src="../CPSoft/ExtMain.js"></script>
    <script type="text/javascript" src="../../JSControl/JQuery/jquery-3.6.0.min.js"></script>
    <script type="text/javascript">var $j = $;var _cps_js_version = '1001';</script>
    <script type="text/javascript" src="../../JSControl/JQuery/jquery-migrate-1.4.1.min.js"></script>
    <script type="text/javascript">
        var vrandom = Math.random();
        var jspath = "../../Page/CPSoft/common.js?v=" + vrandom;
        document.write('<script src="' + jspath + '"><\/script>');
    </script>
    <script type="text/javascript">
        jspath = "../../Page/CPSoft/CommonFunc.js?v=" + vrandom;
        document.write('<script src="' + jspath + '"><\/script>');
    </script>
    <script id="../../Page/CPSoft/jqwatermark.js" src="../../Page/CPSoft/jqwatermark.js" type="text/javascript"></script>
    <script id='../../Page/BaseOpt/MetaJs/UptPassword.js' src="../../Page/BaseOpt/MetaJs/UptPassword.js" type="text/javascript"></script>
    <script type="text/javascript" src="../../JS/md5.js"></script>
    <script type="text/javascript" src="../../JS/aes.js"></script>
    <script type="text/javascript" src="../../Page/EmailManage/EmailWindow.js"></script>
    <script type="text/javascript" src="../../JS/modernizr.js"></script>
    <script type="text/javascript" src="../../JS/notificationFx.js"></script>
    <script type="text/javascript" src="MyDesktopSet.js" ></script>
    <script type="text/javascript" src="Frame.js" ></script>
    <style type="text/css">
        html,body{margin:0px 0px 0px 0px;padding:0px;overflow:hidden;}
        .nav-b { width:100%;height:21px; background-image:url(../../Image/a01_18.gif); background-repeat:repeat-x; float:left;display: inline-block;}
        .nav-t { height:40px;margin-top:10px; margin-right:10px; float: right; display: inline-block; border:0px solid red;}

        .west{width:230px; height:100%; margin:0px;padding:0px; float:left;}
        .treemenu{width:230px; height:100%; border:1px solid #a0cfff; background:transparent; overflow-x:hidden; overflow-y:auto;display:inline-block;}
        .headcontainer{
            background-color:#01A7E7;
            width:100%;
            overflow:visible;
            position:relative;
            visibility:visible;
            vertical-align: middle;
            height:61px;
            line-height:61px;
        }
        .logocontainer{
            width:200px;
            height:61px;
            line-height:61px;
            overflow:visible;
            visibility:visible;
            text-align:center;
            vertical-align:middle;
            border: 0px solid red;
            display: inline-block;
        }
        .logoimg{
            width:70px;
            height:50px;
            line-height:50px;
            border:0px solid red;
            display: inline-block;
            background-image: url('../../Image/Logo.png');
            background-size:70px 50px;
            float: left;
        }
        .logomark{
            margin:0px;
            padding:0px;
            font-size:24px;
            height:61px;
            line-height:61px;
            font-family:Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
            font-weight:bold;
            color:white;
            border: 0px solid red;
            background-color: transparent;
            display: inline-block;
            float: right;
        }
    </style>
    <script type="text/javascript">
        var _mid= "";
        var _stimeout=300000;
        var _token="";
        var _sysmark="";
        var _xsystem="false";
        var showMenu;
        var showmID;
        var showmText;
        var _glayoutheme="#4877fb";
        var loadfinish=false;
    </script>
</head>
<body scroll="no">
<div id="norths" style=" height:87px;vertical-align:top; ">
    <div id="head" class="headcontainer">
        <div class="logocontainer">
            <div class="logoimg"></div>
            <div class="logomark">数字化平台</div>
        </div>
        <div class="nav-t">
            <table border="0" height="40px" style="padding:0px; margin:0px;">
                <tr>
                    <td width="48px" ><a><img src="../../Image/load.png" width="24" height="24" title="刷新" onClick="onHomeRefreshClik()" /></a></td>
                    <td width="48px" ><a><img src="../../Image/exit.png" width="24" height="24" title="注销" onClick="LogOutSys()" /></a></td>
                    <td width="48px" ><span style="position:absolute;right:76px; top:15px; height:13px; width:13px; line-height:13px; text-align:center; border-radius: 9px;font-weight:bold; background-color:Red;  color:#fff" id="MsgNum">+</span><a><img src="../../Image/mail.png" width="24" height="24" title="通知管理" onClick="LXDWindowShow()" /></a></td>
                </tr>
            </table>
        </div>
        <div class="nav-b">
            <span style="float:left; font-size:12px; color:#000; height:21px; line-height:21px;">&nbsp;&nbsp;&nbsp;当前位置><span id="id-mainpage-curloc"></span></span>
            <div style="float:right; padding-right:20px; font-size:12px;color:#000; padding:0px; margin:0px; height:21px;">
                <table width="600px" border="0" height="18px" style="padding:0px; margin:0px;">
                    <tr>
                        <td width="470px" align="right" style="font-size:12px" id="welcome"><span id="spanwelcom"></span></td>
                        <td width="10px"></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div id="toolbar" ></div>
</div>
<div id="west" class="west" >
    <div id="westtree" class="treemenu" >
        <ul id="menu" class="def-menudrager-ul">
        </ul>
    </div>
</div>
<div id="center"></div>
</body>
</html>