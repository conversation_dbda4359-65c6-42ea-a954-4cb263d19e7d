
//我的桌面定义
MyDesktopSetWindow = function () {

    if (Ext.getCmp("ExtMyDesktopSetWindow")) {
        return Ext.getCmp("ExtMyDesktopSetWindow");
    }
        
    var tree = new Ext.tree.TreePanel({
        id: 'ExtMyDesktopSetWindowTree',
        rootVisible: false,
        autoScroll: true,
        animate: true,
        //useArrows: true,
        lines: true,
        containerScroll: true,
        width: 340,
        height: 500,
        frame: true,
        autoHeight: false,
        autoWidth: false,
        checkModel: 'cascade',
        dropConfig: { appendOnly: false },
        root: new Ext.tree.AsyncTreeNode({ id: '0', text: '所有功能', url: '', expanded: true })
    });


    MyDesktopSetWindow.superclass.constructor.call(this, {
        id: 'ExtMyDesktopSetWindow',
        width: 350,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: 'fit',
        items: tree,
        title: '我的桌面设置',
        buttons: [{
            id: 'ExtMyDesktopSetWindowSubmit',
            text: '确定',
            disabled: false,
            handler: MyDesktopSetWindowSubmit
        }, {
            text: '关闭',
            handler: function () {
                Ext.getCmp('ExtMyDesktopSetWindow').hide();
            }
        }]
    });
};

//扩展方法
Ext.extend(MyDesktopSetWindow, Ext.Window, {
    //显示Window
    Show: function () {
        var tree = Ext.getCmp("ExtMyDesktopSetWindowTree");

        tree.getRootNode().removeAll(true);

        /*Ext.Ajax.request({
            url: "../../Service/BaseOpt/ResMan/GetMyDesktopTreeMenu",
            method: "post",
            success: function (request) {
                var data = Ext.util.JSON.decode(request.responseText);
                tree.getRootNode().appendChild(data);
                tree.getRootNode().expandChildNodes(true);
                tree.expandAll();
            },
            callback: function (e) {
                //隐藏等待
                if (myMask) myMask.hide();
            }
        });*/

        this.show();
        Ext.getCmp("ExtMyDesktopSetWindowSubmit").enable();

        //显示等待
        var myMask = new Ext.LoadMask(tree.el);
        myMask.show();

    },
    //设置Window提交回调处理事件
    OnCallback: function (data) {
    }
});


//Window提交
function MyDesktopSetWindowSubmit() {
    Ext.getCmp("ExtMyDesktopSetWindowSubmit").disable();

    var tree = Ext.getCmp("ExtMyDesktopSetWindowTree");

    var length = tree.getChecked().length;
    //if (length > 0) {
        var datas = Array();
        for (var i = 0; i < length; i++) {
            var nodeDbId = tree.getChecked()[i].attributes.db_id;
            var nodeText = tree.getChecked()[i].text;
            var parentDbID = tree.getChecked()[i].parentNode.attributes.db_id;
            datas[i] = { DbID: nodeDbId, GroupName: nodeText, ParentDbId: parentDbID };
        }

        Ext.getCmp('ExtMyDesktopSetWindow').OnCallback(datas);
        Ext.getCmp('ExtMyDesktopSetWindow').hide();
    //}
    //else {
        //Ext.MessageBox.alert("提示", "请选择组织单位！");
    //}

}