DownloadJSByParams('../../Page/CPSoft/common.js', function ()
{
    var FilesArray = [
        '../../Page/CPSoft/ExtGridExtend.js?version=' + _cps_js_version,
        '../../Page/CPSoft/UxGroupExtend.js?version=' + _cps_js_version,
        '../../Page/CPSoft/UxGridSummary.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMQRYManage.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMRoleUser.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMUserRole.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMUserRoleEx.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMUserRoleCP.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMSysLog.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMSqlLog.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMEncoder.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMComfort.js?version=' + _cps_js_version,
        '../../Page/CPSoft/SmartCheck.js?version=' + _cps_js_version,
        '../../Page/CPSoft/SmartCheckM.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMHandLog.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMParamQry.js?version=' + _cps_js_version,
        '../../Page/CPSoft/CMOSSTest.js?version=' + _cps_js_version
    ];

    Import.LoadJsList(FilesArray, function () {
        DownloadJS('../../Page/CPSoft/VCTRL_ManageMain.js?version='+_cps_js_version);
    });

    document.getElementById('../../Page/CPSoft/VCTRL_ManageQueue.js').Destroy = function()
    {
        var element = document.getElementById('../../Page/CPSoft/VCTRL_ManageMain.js?version='+_cps_js_version);
        if(element!=undefined){
            element.parentNode.removeChild(element);
        }
        Ext.getCmp("CPS.VCTRL_ManageMain.IdGRPTT_FormWindow").destroy();
    }
    document.getElementById('../../Page/CPSoft/VCTRL_ManageQueue.js').DataReload = function() {

    }
});