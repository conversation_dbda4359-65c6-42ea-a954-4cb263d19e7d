Ext.namespace('CPS.VCTRL_ManageMain');

//初始化函数
CPS.VCTRL_ManageMain.Initialize = function() {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'CPS.VCTRL_ManageMain';


    //===================操作日志窗体====================================end
    function onCMQRYButtonClick(){
        var oCMQRYManage = new CMQRYManage(funcMark);

        var CMQRYManage_FormWindow = new Ext.Window({
            id : funcMark + '.CMQRY_FormWindow',
            width : 950,
            height : 720,
            closeAction : 'close',
            plain : true,
            modal : true,
            resizable : false,
            autoScroll : false,
            items : oCMQRYManage.CMQry_Form,
            title : '通用查询功能',
            buttonAlign : 'center',
            buttons : [
                {
                    text : '导出',
                    height: 30,
                    handler : function(){
                        oCMQRYManage.ExportExcel();
                    }
                },
                {
                    text : '执行',
                    height: 30,
                    handler : function(){
                        oCMQRYManage.ExecuteSubmit();
                    }
                },
                {
                    text : '关闭',
                    height: 30,
                    handler : function(){
                        CMQRYManage_FormWindow.close();
                    }
                }]
        });

        oCMQRYManage.InitView();
        CMQRYManage_FormWindow.show();
    }

    function onCMSysLogClick() {
        var oCMSysLog = new CMSysLog(funcMark);
        oCMSysLog.ShowWindow();
    }

    function onCMSqlLogClick() {
        var oCMSqlLog = new CMSqlLog(funcMark);
        oCMSqlLog.ShowWindow();
    }

    function onCMHandLogClick() {
        var oCMHandLog = new CMHandLog(funcMark);
        oCMHandLog.ShowWindow();
    }

    function onCMParamQryClick() {
        var oCMParamQry = new CMParamQry(funcMark);
        oCMParamQry.ShowWindow();
    }

    function onCMEncoderClick() {
        var oCMEncoder = new CMEncoder(funcMark);
        oCMEncoder.ShowWindow();
    }

    function onCMOSSTestClick() {
        var oCMOSSTest = new CMOSSTest(funcMark);
        oCMOSSTest.ShowWindow();
    }
    //===================简单查询界面===========================start   

    //===================报表类型界面===========================start
    var VER_CTRL_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {   //行六
                id: funcMark + '.VER_CTRL_Form.IdVC_VER',
                xtype: "textfield",
                name: 'VC_VER',
                fieldLabel: "当前版本号",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'VC_VER', mapping: 'VC_VER', type: 'string' }
            ]
        })
    });

    var VER_CTRL_FormWindow = new Ext.Window({
        id: funcMark+'.IdVER_CTRL_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: VER_CTRL_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function() {
                    VER_CTRL_FormWindow.hide();
                }
            }
        ]
    });

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark+".IdVER_CTRL_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "JS版本管理==修改版本号==";
            VER_CTRL_FormWindow.setTitle(ExtractTitleString(title));
            VER_CTRL_FormWindow.buttons[0].handler = function() {
                var submitButton = this;
                submitButton.disable();

                if (VER_CTRL_Form.form.isValid()) {
                    VER_CTRL_Form.form.doAction('submit',
                    {
                        url: '../../Service/CpSoft/VCtrl/ModifyVER',
                        method: 'post',
                        params: { },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function(form, action) {
                            submitButton.enable();
                            VER_CTRL_Form.ownerCt.hide();
                            LoadData();
                        },
                        failure: function(form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            VER_CTRL_Form.form.reset();
            var url = '../../Service/CpSoft/VCtrl/GetVER';
            VER_CTRL_FormWindow.show();
            VER_CTRL_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function(form, action) {
                    VER_CTRL_FormWindow.buttons[0].enable();
                },
                failure: function(form, action) {
                }
            });
        };
    }

    //===================报表类型界面===========================end  

    //===================报表类型管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
            {
                text: '修改',
                iconCls: 'ButtonFormEdit',
                handler: onEditButtonClick
            }, 
            {
                text : '简单查询',
                iconCls : 'ButtonFormLook',
                handler : function() {
                    onCMQRYButtonClick();
                }
            },
            {
                text: '系统日志',
                iconCls: 'ButtonFormLook',
                handler:function() {
                    onCMSysLogClick();
                }
            },
            {
                text: '查询日志',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    onCMSqlLogClick();
                }
            },
            {
                text: '操作日志',
                iconCls: 'ButtonFormLook',
                handler:function() {
                    onCMHandLogClick();
                }
            },
            {
                id: funcMark + '.toolbar.IdParamQry',
                text: '系统参数',
                iconCls: 'ButtonFormLook',
                handler:function() {
                    onCMParamQryClick();
                }
            },
            {
                id: funcMark + '.toolbar.IdEncoder',
                text: '加密测试',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    onCMEncoderClick();
                }
            },
            {
                id: funcMark + '.toolbar.IdOSSTest',
                text: 'OSS测试',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    onCMOSSTestClick();
                }
            },
            '-',
            '->',
            '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        LoadData();
                    }
                }
            },
            {
                text: '刷新',
                id: funcMark + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function () {
                        RefreshData();
                    }
                }
            }
	    ]
    });

    function RefreshData() {
        start = 0;
        Ext.getCmp(funcMark + ".IdVER_CTRL_Grid").store.reload({ params: {
                start: 0,
                limit: limit
            }
        });
    }

    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark+".IdVER_CTRL_Grid").store.reload({ params: {
            start: 0,
            limit: limit
        }
        });
    }

    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/VCtrl/GetVERList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['VC_VER']
        })
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark+'.IdVER_CTRL_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({ renderer: function(v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "当前版本号", width: 120, sortable: true, dataIndex: 'VC_VER' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true 
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: {
            xtype: 'container',
            items: [toolbar]
        },
        listeners:
        {
            "rowdblclick": function(grid, rowIndex, e) {
                onEditButtonClick();
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark+'.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    Ext.getCmp(funcMark + ".toolbar.IdEncoder").hide();
    Ext.getCmp(funcMark + ".toolbar.IdParamQry").hide();
    if (LoginPerson.LoginName== 'qidf') {
        Ext.getCmp(funcMark + ".toolbar.IdEncoder").show();
        Ext.getCmp(funcMark + ".toolbar.IdParamQry").show();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================报表类型管理form===========================end
};
CPS.VCTRL_ManageMain.Initialize();
