function CMComfort(ownerStr)
{
    var obj = this;

    var rootRecord = Ext.data.Record.create([{ name: 'id', type: 'int' }, { name: 'name', type: 'string'}]);
    var rootData = [[0, 'Page'], [1, 'Image']];
    var rootStore = new Ext.data.SimpleStore({
        fields: ['id', 'name'],
        data: rootData
    });

    //查询模式选择框
    var RootCombo = new Ext.form.ComboBox({
        id: ownerStr + '.CMComfort_Form.IdRootPath',
        name: 'RootPath',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'id',
        displayField: 'name',
        allowBlank: false,
        editable: false,
        fieldLabel: '根目录',
        store: rootStore,
        anchor: '98%',
        listeners: {
            'select': function (c, rec, idx) {
                var rootp = rec.data.name;
                FirstPCombo.store.reload({ params: { RootP: rootp} });
            }
        }
    });

    var FirstPCombo = new Ext.form.ComboBox({
        id: ownerStr + '.CMComfort_Form.IdFirstPath',
        name: 'FirstPath',
        fieldLabel: '第一层子目',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'id',
        displayField: 'name',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CMComfort/GetFirstPath',
            fields: [
                 'id', 'name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var rootp = Ext.getCmp(ownerStr + '.CMComfort_Form.IdRootPath').getRawValue();
                var firstp = rec.data.name;
                SecondPCombo.store.reload({ params: { RootP: rootp,FirstP: firstp} });
            }
        }
    });

    var SecondPCombo = new Ext.form.ComboBox({
        id: ownerStr + '.CMComfort_Form.IdSecondPath',
        name: 'SecondPath',
        fieldLabel: '第二层子目',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'id',
        displayField: 'name',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        tpl:'<tpl for="."><div class="x-combo-list-item" style="height:12px;">{name}</div></tpl>',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CMComfort/GetSecondPath',
            fields: [
                 'id', 'name'
            ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var rootp = Ext.getCmp(ownerStr + '.CMComfort_Form.IdRootPath').getRawValue();
                var firstp = Ext.getCmp(ownerStr + '.CMComfort_Form.IdFirstPath').getRawValue();
                var secondp = rec.data.name;
                ThirdPCombo.store.reload({ params: { RootP: rootp, FirstP: firstp, SecondP: secondp} });
            }
        }
    });

    var ThirdPCombo = new Ext.form.ComboBox({
        id: ownerStr + '.CMComfort_Form.IdThirdPath',
        name: 'ThirdPath',
        fieldLabel: '第三层子目',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'id',
        displayField: 'name',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        tpl: '<tpl for="."><div class="x-combo-list-item" style="height:12px;">{name}</div></tpl>',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CMComfort/GetThirdPath',
            fields: [
                 'id', 'name'
            ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    this.CMComfort_Form = new Ext.FormPanel({
        LabelWidth:100,
        frame: true,
        labelAlign:'right',
        bodyStyle: 'padding:5px 5px 0',
        width: 480,
        height:320,
        layout: 'form',
        autoScroll: true,
        fileUpload: true,
        items: [
            RootCombo,
            FirstPCombo,
            SecondPCombo,
            ThirdPCombo,
            {
                id: 'VCTRL_ManageMain_VCTL_CMComfort_Form_IdFileName',
                xtype: 'fileuploadfield',
                emptyText: '请选择一个文件',
                fieldLabel: '导入文件',
                allowBlank: false,
                name: 'FileName',
                buttonText: '',
                anchor: '98%',
                blankText: '文件不能为空',
                editable: false,
                buttonCfg:
                {
                    iconCls: 'upload-icon'
                }
            }
        ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
            ]
        })
    });

    this.CMFileCopyer_Window = new Ext.Window({
        id: ownerStr + '.CMFileCopyer_Window',
        width: 500,
        height: 400,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: obj.CMComfort_Form,
        title: '通用不需要编译文件上传到指定目录下功能',
        buttonAlign: 'center',
        buttons: [
        {
            text: '执行上传',
            height: 30,
            handler: function () {
                obj.ExecuteFileCopy();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                obj.CMFileCopyer_Window.close();
            }
        }]
    });

    this.ExecuteFileCopy = function () {
        if (obj.CMComfort_Form.form.isValid()) {
            var formData = new FormData();
            var ftag="#VCTRL_ManageMain_VCTL_CMComfort_Form_IdFileName-file";
            var file = $j(ftag)[0].files[0];
            formData.append('file', file);
            formData.append('RootP',Ext.getCmp(ownerStr + '.CMComfort_Form.IdRootPath').getRawValue());
            formData.append('FirstP', Ext.getCmp(ownerStr + '.CMComfort_Form.IdFirstPath').getRawValue());
            formData.append('SecondP', Ext.getCmp(ownerStr + '.CMComfort_Form.IdSecondPath').getRawValue());
            formData.append('ThirdP', Ext.getCmp(ownerStr + '.CMComfort_Form.IdThirdPath').getRawValue());

            _ExtUploadFile(
                '../../Service/CpSoft/CMComfort/ExecuteFileCopy',
                formData,
                function(data){
                    var json0 = Ext.util.JSON.decode(data);
                    if(json0.text!=undefined)
                    {
                        Ext.MessageBox.alert("操作提示",json0.text);
                    }
                },function () {
                    Ext.MessageBox.alert("操作提示","上传文件失败！");
                }
            );
        }
     }

     this.ShowWindow = function () {
         obj.CMComfort_Form.form.reset();
         obj.CMFileCopyer_Window.show();
     }
}

CMComfort.prototype = {
    constructor: CMComfort
}