//组织单位选择弹出窗口定义
SmartCheckMN = function (ownerStr, table, condtion, view, csizes, viewt, element, control, trigger, filter, order, width, height, dist, group)
{
    var obj = this;

    this.start = 0;
    this.limit = 10;
    this.pageSize = obj.limit;

    this.strTable = (table != undefined) ? table : "";
    this.strCondtion = (condtion != undefined) ? condtion : "";
    this.strViewColumn = (view != undefined) ? view : "";
    this.strViewSize = (csizes != undefined) ? csizes : "";
    this.strViewTitle = (viewt != undefined) ? viewt : "";
    this.strElement = (element != undefined) ? element : "";
    this.strControls = (control != undefined) ? control : "";
    this.triggerFunc = (trigger != undefined) ? trigger : 0;
    this.strFliter = (filter != undefined) ? filter : "";
    this.strOrder = (order != undefined) ? order : "";
    this.strDist = (dist != undefined) ? dist : "";
    this.strGroup = (group != undefined) ? group : "";
    this.widthSize = (width != undefined) ? width : 720;
    this.heightSize = (height != undefined) ? height : 480;
    this.strQryColumn = (view != undefined) ? view : "";
    this.paramStr = "";

    if (obj.strCondtion != "") obj.strCondtion = obj.strCondtion.replaceAll("=", "opera-equal").replaceAll(">","opera-large").replaceAll("<","opera-less").replaceAll(" and "," opera-andg ").replaceAll(" or "," opera-org ");

    this.CMList = obj.strViewColumn.split(',');
    this.CSList = obj.strViewSize.split(',');
    this.CMTList = obj.strViewTitle.split(',');
    this.CMSList = obj.strViewColumn.split(',');
    this.EMList = obj.strElement.split(',');

    for (var idx = 0; idx < obj.EMList.length; idx++)
    {
        if (obj.CMList.indexOf(obj.EMList[idx]) >= 0) continue;
        else
        {
            obj.strQryColumn = obj.strQryColumn + "," + obj.EMList[idx];
            obj.CMSList.push(obj.EMList[idx]);
        }
    }

    this.ColumnModelArray = new Array();
    obj.ColumnModelArray[0] = new Ext.grid.CheckboxSelectionModel();

    for (var i = 0; i < obj.CMList.length; i++)
    {
        obj.ColumnModelArray[i + 1] = { header: obj.CMTList[i], width: parseInt(obj.CSList[i]), dataIndex: obj.CMList[i] }
    }

    this.LoadData = function ()
    {
        obj.start = 0;
        obj.paramStr = Ext.getCmp(ownerStr + '.toolbar.IdParamStr').getValue();
        Ext.getCmp(ownerStr + ".IdSmarkCheckMN_Grid").store.reload({ params: {
            start: 0,
            limit: obj.limit,
            table: obj.strTable,
            distf: obj.strDist,
            group: obj.strGroup,
            condtion: obj.strCondtion,
            columns: obj.strQryColumn,
            filter: obj.strFliter,
            params: obj.paramStr,
            order: obj.strOrder
        }
        });
    }

    this.toolbar = new Ext.Toolbar({
        items:
        [
              '&nbsp;过滤条件&nbsp;',
              {
                  id: ownerStr + '.toolbar.IdParamStr',
                  xtype: "textfield",
                  name: 'ParamStr',
                  width: 240,
                  listeners:
                    {
                        specialkey: function (field, e)
                        {
                            if (e.getKey() == e.UP || e.getKey() == e.DOWN || e.getKey() == e.ENTER)
                            {
                                e.preventDefault();
                                e.stopEvent();

                                obj.LoadData();
                                return false;
                            }
                        }
                    }
              },
              '-',
              '->',
              '-',
               {
                   text: '查询',
                   iconCls: 'ButtonFormLook',
                   handler: obj.LoadData
               }
	      ]
    });

    //设置数据源参数
    this.resultStore = new Ext.data.Store({
        autoLoad: false,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/SCheckN/ExecuteCheck"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: obj.CMSList
        })
    });

    //设置Grid列表
    this.resultGrid = new Ext.grid.GridPanel({
        id: ownerStr + '.IdSmarkCheckMN_Grid',
        store: obj.resultStore,
        sm: new Ext.grid.CheckboxSelectionModel(),
        columns: obj.ColumnModelArray,
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: false,
        viewConfig: {
            forceFit: true
        },
        listeners:
        {
            "rowdblclick": function (grid, rowIndex, e)
            {
                obj.SmartCheckMNSelect();
            }
        },
        tbar: obj.toolbar
    });

    this.SmartCheckMNSelect = function ()
    {
        var win = obj;
        if (obj.resultGrid.getSelectionModel().getSelections()[0] == undefined)
        {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else
        {
            if (obj.strElement != "")
            {
                var datas = "";
                var elementArr = obj.strElement.split(',');
                var len = obj.resultGrid.getSelectionModel().getSelections().length;
                for (var idx = 0; idx < len; idx++)
                {
                    var datav = "";
                    var data = obj.resultGrid.getSelectionModel().getSelections()[idx];
                    for (var jdx = 0; jdx < elementArr.length; jdx++)
                    {
                        var tmpval = data.get(elementArr[jdx]);
                        if (datav != "") datav = datav + "|cps|" + tmpval;
                        else datav = tmpval;
                    }
                    if (idx < len - 1) datas = datas + datav + ",";
                    else datas = datas + datav;
                }

                win.OnCallback(datas);
                win.close();

                if (obj.triggerFunc != 0) obj.triggerFunc();
            }
        }
    }

    this.InitView = function (params)
    {
        obj.paramStr = params;
        Ext.getCmp(ownerStr + ".toolbar.IdParamStr").setValue(obj.paramStr);
        obj.LoadData();
    }

    SmartCheckMN.superclass.constructor.call(this, {
        width: obj.widthSize,
        height: obj.heightSize,
        closeAction: 'close',
        plain: true,
        modal: true,
        layout: 'border',
        resizable: true,
        maximizable: true,
        items: [
            {
                region: 'center',
                layout: 'fit',
                items: [obj.resultGrid]
            }
        ],
        buttonAlign: 'center',
        buttons: [
            {
                text: '选择',
                height: 30,
                disabled: false,
                handler: function ()
                {
                    obj.SmartCheckMNSelect();
                }
            },
            {
                text: '关闭',
                height: 30,
                handler: function ()
                {
                    this.ownerCt.ownerCt.close();
                    //this.ownerCt.hide();
                }
            }
       ]
    });
};

Ext.extend(SmartCheckMN, Ext.Window, {
    //显示Window
    Show: function (params) {
        this.InitView(params);
        this.show();
    },
    SetPositionId: function (id) {
        this.positionId = id;
    },
    //设置Window提交回调处理事件
    OnCallback: function (data) {
    }
});