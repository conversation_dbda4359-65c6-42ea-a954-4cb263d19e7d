Ext.BLANK_IMAGE_URL = '../../JSControl/ExtJs/ExtResources/images/default/s.gif';
var SchID = 0;
var SchlcID = 0;
var clikcToLc = 0;
var kyurl = "http://127.0.0.1";

AjaxRequest = function(obj)
{
    Ext.Ajax.request({
        url: obj.url,
        params:obj.params,
        success: function(response, options) {
            var responseArray = Ext.decode(response.responseText);
            if (responseArray.success == undefined)
            {
                if (obj.jexceptfn != undefined)
                    obj.jexceptfn(response.responseText);
            }
            else if (responseArray.success == true) {
                if (obj.successfn != undefined)            
                    obj.successfn(responseArray.data);
            }
            else {
                if (responseArray.text != "") {
                    if (obj.failurefn != undefined)                         
                        obj.failurefn();
                }
            }
        }
    });
}

AjaxRequestText = function(obj)
{
    Ext.Ajax.request({
        url: obj.url,
        params:obj.params,
        success: function(response, options) {
            var responseArray = Ext.decode(response.responseText);
            if (responseArray.success == undefined)
            {
                if (obj.jexceptfn != undefined)
                    obj.jexceptfn(responseArray);
            }
            else if (responseArray.success == true) {
                if (obj.successfn != undefined)
                    obj.successfn(responseArray.text);
            }
            else {
                if (responseArray.text != "") {
                    if (obj.failurefn != undefined)
                        obj.failurefn();
                }
            }
        }
    });
}

var LoginPerson;
function UpatePersonInfo(fmSucc,fmFail)
{
    AjaxRequest({
        url: '../../Service/Base/User/GetCurrentPerson',
        successfn: function (val)
        {
            LoginPerson = val;
            if (fmSucc != undefined)
            {
                fmSucc();
            }
        },
        failurefn: function (val)
        {
            if (fmFail != undefined) {
                fmFail();
            }
            Ext.MessageBox.alert("提示", "获取用户信息出错！");
        }
    });
}

// Trim() , Ltrim() , RTrim()
String.prototype.Trim = function() {
    return this.replace(/(^\s*)|(\s*$)/g, "");
}

String.prototype.LTrim = function() {
    return this.replace(/(^\s*)/g, "");
}

String.prototype.RTrim = function() {
    return this.replace(/(\s*$)/g, "");
}

//表单控件验证类型定义customtextfield
Ext.form.VTypes['customtextfieldVal'] = /^[^"'\:\[\]{}]+$/;
Ext.form.VTypes['customtextfieldText'] = '不能输入非法字符，非法字符包括：\u0022 \u0027 \u002C \u003A \u005D \u007B \u007D ，可以用全角符号代替';
Ext.form.VTypes['customtextfield'] = function(v) {
    if(isJsNullObj(v))
    {
        return true;
    }else{
        return Ext.form.VTypes['customtextfieldVal'].test(v);
    }
};

//查询表单控件验证类型定义querytextfield
Ext.form.VTypes['querytextfieldVal'] = /^[^"'\:\[\]{}%]+$/;
Ext.form.VTypes['querytextfieldText'] = '不能输入非法字符，非法字符包括：\u0022 \u0027 \u002C \u003A \u005B \u005D \u007B \u007D %，可以用全角或其他符号代替';
Ext.form.VTypes['querytextfield'] = function(v) {
    if(isJsNullObj(v))
    {
        return true;
    }else{
        return Ext.form.VTypes['querytextfieldVal'].test(v);
    }
};

//给DOM元素添加事件监听
function AddEventListener(element, event, handle) {
    if (element.addEventListener) {
        element.addEventListener(event, handle, false);
    }
    else if (element.attachEvent) { 
        element.attachEvent("on" + event, handle);
    }
}
//给DOM元素删除事件监听
function DeleteEventListener(element, event, handle) {
    if (element.removeEventListener) {
        element.removeEventListener(event, handle, false);
    }
    else if (element.detachEvent) { 
        element.detachEvent("on" + event, handle);
    }
}


//Ext3.0 Ajax 同步方法
//使用方法 var obj = Boat.Synchronize(url);
Ext.Ajax.Synchronize = function(url) {   
    function createXhrObject() {   
        var http;   
        var activeX = ['MSXML2.XMLHTTP.3.0', 'MSXML2.XMLHTTP', 'Microsoft.XMLHTTP'];   
        try {   
            http = new XMLHttpRequest();   
        } catch (e) {   
            for (var i = 0; i < activeX.length; ++i) {   
                try {   
                    http = new ActiveXObject(activeX[i]);   
                    break;   
                } catch (e) { }   
            }   
        } finally {   
           return http;   
        }   
    };   
  
    var conn = createXhrObject();   
    conn.open("POST", url, false);   
    conn.send(null);   
    if (conn.responseText != '') {   
        return Ext.decode(conn.responseText);   
    }else 
    {   
        return null;   
    }
};
  
function DownloadJSByParams(url, funSucc) {

    //获取所有的<script>标记
    var ss = document.getElementsByTagName("script");
    //判断指定的文件是否已经包含，如果已包含则触发onsuccess事件并返回
    for (var i = 0; i < ss.length; i++) {
        if (ss[i].id && ss[i].id.indexOf(url) != -1) {
            if (funSucc) funSucc();
            return;
        }
    }

    var murl = url;
    if (_cps_js_version == "1001") _cps_js_version = Math.random();
    if (murl.indexOf("?") > 0) {
        if (murl.indexOf("version=") < 0) {
            murl = murl + "&version=" + _cps_js_version;
        }
    } else {
        if (murl.indexOf("version=") < 0) {
            murl = murl + "?version=" + _cps_js_version;
        }
    }

    //创建script节点，并将其属性设为外联JavaScript文件
    var s = document.createElement("script");
    s.id = url;
    s.type = "text/javascript";
    s.src = murl;
    //获取head节点，并将<script>插入到其中
    var head = document.getElementsByTagName("head")[0];
    head.appendChild(s);

    s.onload = s.onreadystatechange = function() {
        if (this.readyState && this.readyState == "loading") return;
        downloadtext = downloadtext + "|" + url;
        if (funSucc) funSucc();
    };

    s.onerror = function() {
        head.removeChild(s);
    };
}

function DownloadJSByParams2(url, funSucc) {

    //获取所有的<script>标记
    var ss = document.getElementsByTagName("script");
    //判断指定的文件是否已经包含，如果已包含则触发onsuccess事件并返回
    for (var i = 0; i < ss.length; i++) {
        if (ss[i].src && ss[i].src.indexOf(url) != -1) {
            if (funSucc) funSucc();
            return;
        }
    }

    var murl = url;
    if (_cps_js_version == "1001") _cps_js_version = Math.random();
    if (murl.indexOf("?") > 0) {
        if (murl.indexOf("version=") < 0) {
            murl = murl + "&version=" + _cps_js_version;
        }
    } else {
        if (murl.indexOf("version=") < 0) {
            murl = murl + "?version=" + _cps_js_version;
        }
    }

    //创建script节点，并将其属性设为外联JavaScript文件
    var s = document.createElement("script");
    s.id = url;
    s.type = "text/javascript";
    s.src = murl;
    //获取head节点，并将<script>插入到其中
    var head = document.getElementsByTagName("head")[0];
    head.appendChild(s);

    s.onload = s.onreadystatechange = function () {
        if (this.readyState && this.readyState == "loading") return;
        downloadtext = downloadtext + "|" + url;
        if (funSucc) funSucc();
    };

    s.onerror = function () {
        head.removeChild(s);
    };
}

function GetJSONObject(str) {
    if (str) {
        return Ext.decode(str);
    } else {
        return null;
    }
}

function GetJSONString(obj) {
    if (obj) {
        return Ext.util.JSON.encode(obj);
    } else {
        return null;
    }
}

function SetFormReadOnlyColor(form) {
    function setItemsReadOnlyColor(items) {
        items.each(function(item) {
            var childitems = item.items;
            if (!Ext.isEmpty(childitems, false)) {
                setItemsReadOnlyColor(childitems);
            } else {
                if (item.xtype != "button" && item.xtype != "gridview") {
                    if (item.readOnly) {
                        item.getEl().dom.style.background = ("#E6E6E6");
                    }
                }
            }
        });
    }
    setItemsReadOnlyColor(form.items);
}


function ajaxPage(sId, url) {
    var oXmlHttp = GetHttpRequest();
    oXmlHttp.onreadystatechange = function() {
        if (oXmlHttp.readyState == 4) {
            includeJS(sId, url, oXmlHttp.responseText);
        }
    }
    oXmlHttp.open('GET', url, false); //同步操作  
    oXmlHttp.send(null);
}

function GetHttpRequest() {
    if (window.XMLHttpRequest) // Gecko  
        return new XMLHttpRequest();
    else if (window.ActiveXObject) // IE  
        return new ActiveXObject("MsXml2.XmlHttp");
}

function includeJS(sId, fileUrl, source) {
    if ((source != null) && (!document.getElementById(sId))) {
        var oHead = document.getElementsByTagName('HEAD').item(0);
        var oScript = document.createElement("script");
        oScript.type = "text/javascript";
        oScript.id = sId;
        oScript.text = source;
        oHead.appendChild(oScript);
    }
}  

function OnDownloadJSSuccess() { }
function OnDownloadJSFailure() { }
var downloadtext = "";
function DownloadJS(url) {
    //获取所有的<script>标记
    var ss = document.getElementsByTagName("script");
    //判断指定的文件是否已经包含，如果已包含则触发onsuccess事件并返回
    for (var i = 0; i < ss.length; i++) {
        if (ss[i].src && ss[i].src.indexOf(url) != -1) {
            OnDownloadJSSuccess();
            return;
        }
    }
    //创建script节点，并将其属性设为外联JavaScript文件
    var s = document.createElement("script");
    s.id = url;
    s.type = "text/javascript";
    s.src = url;
    //获取head节点，并将<script>插入到其中
    var head = document.getElementsByTagName("head")[0];
    head.appendChild(s);

    s.onload = s.onreadystatechange = function() {
        if (this.readyState && this.readyState == "loading") return;
        downloadtext = downloadtext + "|" + url;
        OnDownloadJSSuccess();
    };

    s.onerror = function() {
        head.removeChild(s);
        OnDownloadJSFailure();
    };
}

//主功能区面板
var MainTabPanel = function() {
    MainTabPanel.superclass.constructor.call(this, {
        id: 'MainPanel',
        region: 'center',
        deferredRender: false,
        plugins :new Ext.ux.TabCloseMenu(),
        activeTab: 0,
        margins: '0 5 5 5',
        autoDestroy: false,
        title: '',
        items: [
        ],
        listeners:
        {
            'beforeremove': function (tab, comp, eOpts) {
                mvMap.set(comp.id, false);
                return true;
            },
            "tabchange":function(tt,newTab)
            {
                if(newTab!=undefined&&newTab.refreshfn!=undefined&&newTab.refreshfn!=0)
                {
                    newTab.refreshfn();
                }
            }
        }
    });
};

var mvMap = new Map();
var hoMap = new Map();

function XNTreeMenuNode(id, url, text) {
    this.id = id;
    this.url = url;
    this.text = text;
}

var _historyTab=new Array();
Ext.extend(MainTabPanel, Ext.TabPanel, {
    getItem: function (idstr) {
        if (this.items.length > 0) {
            for (var i = 0; i < this.items.length; i++) {
               if(this.items.items[i].getItemId()===idstr) {
                   return this.items.items[i]
                }
           }
           return null;
        } else {
            return null;
        }
    },
    //加载Tab函数定义
    loadTab: function (url, id, title) {
        FrameMainTabContainer = document.getElementById("center");
        FrameMainTabContainer.ContentUrl = url;
        var mainTabId = "MainTab" + id;

        FrameMainTabContainer.ClickedTreeMenuNode = new XNTreeMenuNode(id, url, title);
        if (FrameMainTabContainer.CurrentMainTabId == undefined) {
            FrameMainTabContainer.CurrentMainTabId = "MainTab";
        }

        var isaspx = false;
        if (url.substring(url.lastIndexOf("."), url.length).toLowerCase() == ".html") {
            isaspx = true;
        }
        else if (url.substring(url.lastIndexOf("."), url.length).toLowerCase() == ".Js") {
            isaspx = false;
        }

        var stab = Ext.getCmp(mainTabId);
        if (isaspx) {
            SafeOpenAspx(stab, mainTabId, url, title);
        } else {
            SafeOpenJS(stab, mainTabId, url, title,0);
        }
    },
    //加载Tab函数定义
    loadJsTabWithCallBack: function (url, id, title,succf) {
        FrameMainTabContainer = document.getElementById("center");
        FrameMainTabContainer.ContentUrl = url;
        var mainTabId = "MainTab" + id;

        FrameMainTabContainer.ClickedTreeMenuNode = new XNTreeMenuNode(id, url, title);
        if (FrameMainTabContainer.CurrentMainTabId == undefined) {
            FrameMainTabContainer.CurrentMainTabId = "MainTab";
        }

        var stab = Ext.getCmp(mainTabId);
        SafeOpenJS(stab, mainTabId, url, title,succf);
    }
});

function SafeOpenAspx(otab,id,url,title) {
    if (otab != null) Ext.getCmp("MainPanel").remove(id, true);

    var murl = url;
    if (_cps_js_version == "1001") _cps_js_version = Math.random();
    if (murl.indexOf("?") > 0) {
        if (murl.indexOf("version=") < 0) {
            murl = murl + "&version=" + _cps_js_version;
        }
    } else {
        if (murl.indexOf("version=") < 0) {
            murl = murl + "?version=" + _cps_js_version;
        }
    }

    SetNewTabPanel(new Ext.Panel({
        region: 'center',
        layout: 'fit',
        id: id,
        title: title,
        refreshfn: function () {
            var MyFrame = document.getElementById(id + '.frame');
            if (MyFrame != undefined) {
                MyFrame.contentWindow.location.reload();
            }
        },
        html: '<iframe id="' + id + '.frame" scrolling="auto" frameborder="0" width="100%" height="100%" src="' + murl + '"></iframe>'
    }));
}

function SafeOpenJS(otab, id, url,title,succf) {
    var tabs = Ext.getCmp("MainPanel");
    if (tabs.items.items.length == 0) {
        tabs.setActiveTab(0);
    }

    if (tabs!=undefined&&otab != null) {
        tabs.add(otab);
        tabs.unhideTabStripItem(otab);
        tabs.setActiveTab(id);
        mvMap.set(id,true);

        FrameMainTabContainer.CurrentMainTabId = id;
        if (document.getElementById(FrameMainTabContainer.ContentUrl).DataReload != undefined) {
            document.getElementById(FrameMainTabContainer.ContentUrl).DataReload();
        }
        if(succf!=0)succf();
    }
    else{
        DownloadJSByParams(url,function()
        {
            //if(succf!=0)succf();
        });
    }
}

function SafeOpenJSNWTabs(id,url,title,succf) {
    FrameMainTabContainer = document.getElementById("center");
    FrameMainTabContainer.ContentUrl = url;
    var mainTabId = "MainTab" + id;
    var otab = Ext.getCmp(mainTabId);

    FrameMainTabContainer.ClickedTreeMenuNode = new XNTreeMenuNode(id, url, title);
    if (FrameMainTabContainer.CurrentMainTabId == undefined) {
        FrameMainTabContainer.CurrentMainTabId = "MainTab";
    }

    var tabs = Ext.getCmp("MainPanel");
    if (tabs.items.items.length == 0) {
        tabs.setActiveTab(0);
    }

    if (tabs!=undefined&&otab != null) {
        tabs.add(otab);
        tabs.unhideTabStripItem(otab);
        tabs.setActiveTab(id);
        mvMap.set(id,true);

        FrameMainTabContainer.CurrentMainTabId = id;
        if (document.getElementById(FrameMainTabContainer.ContentUrl).DataReload != undefined) {
            document.getElementById(FrameMainTabContainer.ContentUrl).DataReload();
        }
        if(succf!=0)succf();
    }
    else{
        DownloadJSByParams(url,function()
        {
            //if(succf!=0)succf();
        });
    }
}

var MainExtViewport;
var mainTabPanel;
var MainInit = function() {
    mainTabPanel = new MainTabPanel();  
    
    //设置界面布局
    var viewport = new Ext.Viewport({
        layout: 'border',
        items: [
            new Ext.BoxComponent({
                region : 'north',
                el : 'norths',
                height : 87
            }),
            {
                region: 'center',
                layout:'border',
                items:[
                    new Ext.BoxComponent({
                        region : 'west',
                        el : 'west',
                        width : 230
                    }),
				    mainTabPanel
                ]
            }
		]
    });
    MainExtViewport = viewport;     
};


function SetNewTabPanel(panel) {
    panel.closable = true;
    Ext.getCmp("MainPanel").add(panel);
    Ext.getCmp("MainPanel").setActiveTab(panel.getId());
    FrameMainTabContainer.CurrentMainTabId = panel.getId();
}

function SetNewTabPanelNoClose(panel) {
    panel.closable = false;
    Ext.getCmp("MainPanel").add(panel);
    Ext.getCmp("MainPanel").setActiveTab(panel.getId());
}

function SafeOpenSchdeule(_sysmark) {
    var id='LcSchdeule';
    var url='../../Page/Schdeule/nfIndex.html?sm='+_sysmark+'&v='+_cps_js_version;
    var title='首页';

    SetNewTabPanelNoClose(new Ext.Panel({
        region: 'center',
        layout: 'fit',
        id: id,
        title: title,
        refreshfn: function () {
            var MyFrame = document.getElementById(id + '.frame');
            if (MyFrame != undefined) {
                MyFrame.contentWindow.location.reload();
            }
        },
        html: '<iframe id="' + id + '.frame" scrolling="auto" frameborder="0" width="100%" height="100%" src="' + url + '"></iframe>'
    }));
}

//添加模块进入自动激发功能
var TiggerMAFunction = function (mid) {
    Ext.Ajax.request({
        url: '../../Service/BaseOpt/AsMan/GetStartPList',
        params: { mid: mid},
        success: function (response, options) {
            if (response.responseText == "") return;
            var respjson = Ext.util.JSON.decode(response.responseText);
            if (respjson.success == true) {
                var dlist = respjson.data;
                for (var idx = 0; idx < dlist.length; idx++) {
                    var urlstr = dlist[idx].Url;
                    var idstr = dlist[idx].Id;
                    var titlestr = dlist[idx].Title;
                    if (urlstr != "" && idstr != "" && titlestr!="") {
                        NavFunction(urlstr,idstr,titlestr);
                    }
                }
            }
        },
        failure: function (response, options) {
        }
    });
}

//添加模块进入自动激发功能
var NavFunctionToDo = function (mA, mB, mC, mD) {
    SchID = mC;
    SchlcID = mD;

    var id = "";
    var dobj = $("div[cname='" + b + "']");
    if (dobj != undefined)
    {
        var rid = dobj.attr("cid");
        var uobj = $("ul[cid='" + rid + "']");
        if (uobj != undefined) {
            var lobj = uobj.find("li[cname='" + c + "']");
            if (lobj != undefined) {
                var nobj = lobj.find("a");
                if(nobj!=undefined&&nobj[0]!=undefined)
                {
                    nobj[0].click();
                }
            }
        }
    }

    /*
    Ext.Ajax.request({
        url: '../../Service/BaseOpt/ResMan/GetResByMark',
        params: { mRoot: mA, mMenu: mB},
        success: function (response, options) {
            if (response.responseText == "") return;
            var respjson = Ext.util.JSON.decode(response.responseText);
            if (respjson.success == true) {
                var data = respjson.data;
                if (data != undefined) {
                    var urlstr = data[0].Url;
                    var idstr = data[0].Id;
                    var titlestr = data[0].Title;
                    if (urlstr != "" && idstr != "" && titlestr != "") {
                        NavFunction(urlstr, idstr, titlestr);
                    }
                }
            }
        },
        failure: function (response, options) {
        }
    });*/
}
