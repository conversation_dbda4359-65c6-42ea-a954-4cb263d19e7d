var _netsvrurl='../../../';
var _enccode="db2139561c9fe068";
var _check_root="/checksys";
var _monitor_root="/monitor";

function _getFileSuffix(filename)
{
    return filename.substring(file.name.lastIndexOf('.') + 1);
}
function _ExtDownloadFile(url,plist,ftype,successfn,failfn) {
    var fmime="";
    if(ftype==""||ftype===undefined||ftype.toLowerCase()==".xlsx")
    {
        fmime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    }else if(ftype.toLowerCase()==".xls")
    {
        fmime="application/vnd.ms-excel";
    }else if(ftype.toLowerCase()==".doc"){
        fmime="application/msword";
    }else if(ftype.toLowerCase()==".docx")
    {
        fmime="application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    }else if(ftype.toLowerCase()==".pdf")
    {
        fmime="application/pdf";
    }else if(ftype.toLowerCase()==".rar")
    {
        fmime="application/octet-stream";
    }else if(ftype.toLowerCase()==".zip")
    {
        fmime="application/x-zip-compressed";
    }else{
        fmime="application/octet-stream";
    }


    Ext.Ajax.request({
        url:url,
        method: 'POST',
        success: function (res,options) {
            if (res.responseText == "" || res.responseText == undefined) return;
            var resp = Ext.decode(res.responseText);
            var strNum = resp.text;

            if (resp.data1 != '' && resp.data1 != undefined) {
                var uInt8Array = new Uint8Array(resp.data1);
                var fname =resp.fname;
                var bloburl = window.URL.createObjectURL(new Blob([uInt8Array], {
                    type:fmime
                }));

                if (!Ext.fly('xdownanchor')) {
                    var xdownanchor = document.createElement('a');
                    xdownanchor.download =fname;
                    xdownanchor.href=bloburl;
                    xdownanchor.click();
                    document.body.appendChild(xdownanchor);
                    window.URL.revokeObjectURL(xdownanchor.href);
                    document.body.removeChild(xdownanchor);
                }
            }
            
            if (resp.success != 'true' && resp.success != true){
                Ext.MessageBox.alert("操作提示", resp.text,function () {
                    if(failfn) failfn();
                });
            }else {
                if(successfn) successfn();
            }
        },
        params:plist
    });
}

function _ExtDownloadFile2(url,plist,successfn,failfn) {
    Ext.Ajax.request({
        url:url,
        method: 'POST',
        success: function (res,options) {
            if (res.responseText == "" || res.responseText == undefined) return;
            var resp = Ext.decode(res.responseText);
            var strNum = resp.text;

            if (resp.data1 != '' && resp.data1 != undefined) {
                var uInt8Array = new Uint8Array(resp.data1);
                var fname =resp.fname;

                var fmime="";
                var ftype=resp.ftype;
                if(isJsNullObj(ftype)) ftype=_getFileSuffix(fname);
                if(ftype.toLowerCase()==".xlsx")
                {
                    fmime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                }else if(ftype.toLowerCase()==".xls")
                {
                    fmime="application/vnd.ms-excel";
                }else if(ftype.toLowerCase()==".doc"){
                    fmime="application/msword";
                }else if(ftype.toLowerCase()==".docx")
                {
                    fmime="application/vnd.openxmlformats-officedocument.wordprocessingml.document";
                }else if(ftype.toLowerCase()==".pdf")
                {
                    fmime="application/pdf";
                }else if(ftype.toLowerCase()==".rar")
                {
                    fmime="application/octet-stream";
                }else if(ftype.toLowerCase()==".zip")
                {
                    fmime="application/x-zip-compressed";
                }else{
                    fmime="application/octet-stream";
                }

                var bloburl = window.URL.createObjectURL(new Blob([uInt8Array], {
                    type:fmime
                }));

                if (!Ext.fly('xdownanchor')) {
                    var xdownanchor = document.createElement('a');
                    xdownanchor.download =fname;
                    xdownanchor.href=bloburl;
                    xdownanchor.click();
                    document.body.appendChild(xdownanchor);
                    window.URL.revokeObjectURL(xdownanchor.href);
                    document.body.removeChild(xdownanchor);
                }
            }

            if (resp.success != 'true' && resp.success != true){
                Ext.MessageBox.alert("操作提示", resp.text,function () {
                    if(failfn) failfn();
                });
            }else {
                if(successfn) successfn();
            }
        },
        params:plist
    });
}

function _ExtUploadFile(uploadurl,formData,successfn,failfn) {
    Ext.MessageBox.show({
        title: '请稍等',
        msg: '正在上传...',
        eProgressText: '',
        width: 300,
        eProgress: true,
        closable: false
    });

    var updateProgress = function (eProgress) {
        if (eProgress >= 1) {
            Ext.MessageBox.updateProgress(1, "处理完成");
            return;
        }
        Ext.MessageBox.updateProgress(eProgress, Math.round(eProgress * 100) + "%");
    }

    var storage = window.sessionStorage;
    var jtoken="";
    if(storage!=undefined)
    {
        jtoken = storage.getItem(_sysmark+"_token");
    }
    if(jtoken==""||jtoken==undefined)
    {
        CloseWindowToReLogin();
    }

    $j.ajax({
        timeout: 0,
        url: uploadurl,
        type: 'post',
        dataType: 'text',
        processData: false,
        async: false,
        contentType: false,
        headers: {
            Authorization: jtoken
        },
        data: formData,
        cache: false,
        xhr: function () {
            var xhr = new XMLHttpRequest();
            xhr.upload.addEventListener('eProgress', function (e) {
                pre = 0;
                if (e.load == undefined) {
                    pre = 0;
                } else {
                    pre = parseInt(e.load / e.total);
                }
                updateProgress(pre);
            });
            return xhr;
        },
        success: function (data, textStatus) {
            Ext.MessageBox.hide();
            if (successfn!= 0&&successfn!=undefined) successfn(data);
        },
        error: function (req, textStatus, errorThrown) {
            Ext.MessageBox.hide();
            if (failfn!= 0&&failfn!=undefined) failfn();
        }
    });
}

//设置ToolBar的按钮权限
function SetToolBarPermission(tool)
{
    var count = 0;
    var urlParam = "";
    for (var i = 0; i < tool.items.length; i++)
    {
        var item = tool.items.items[i];
        if (item.text != undefined)
        {
            if (item.PermissionNo == undefined)
            {
                continue;
            }
            else
            {
                count++;
                urlParam = urlParam + "&" + "P" + count + "=" + item.PermissionNo;
            }
        }
    }
    urlParam = "&Count=" + count + urlParam;

    var conn = new Ext.data.Connection();
    conn.request(
        {
            url: _netsvrurl+"Service/SystemManage/PermissionService.aspx?Method=GetOperationPermission" + urlParam,
            params: { temp: 'temp' },
            method:'post',
            scope:this,
            callback:function(options,success,response)
            {
                if (success)
                {
                    //zz：2019年12月23日
                    //bug：tool内的item可能在AJAX请求中发生改变。
                    var json = Ext.util.JSON.decode(response.responseText);
                    var num = 0;
                    for (var i = 0; i < tool.items.length; i++) {
                        var item = tool.items.items[i];
                        if (item.text != undefined && item.PermissionNo != undefined) {
                            //zz：增加的代码：遍历json
                            for (var j = 0; j < json.Data.length; j++) {
                                var jItemData = json.Data[j];
                                if (jItemData.PermissionNo == item.PermissionNo) {
                                    //权限NO一致
                                    if (jItemData.IsValid == false) {
                                        item.hide();
                                        //此处估计是隐藏该隐藏的尾分隔符 |
                                        var nextI = (i + 1) == tool.items.length ? i : i + 1;
                                        if (tool.items.items[nextI].text == undefined) {
                                            tool.items.items[nextI].hide();
                                        }
                                    }
                                    else {
                                        item.show();
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
//                Ext.MessageBox.alert("提示","权限设置出错！");
                }
            }
        });
}


//设置ToolBar的按钮权限
function SetToolBarPermissionForWindow(tool)
{
    var count = 0;
    var urlParam = "";
    for (var i = 0; i < tool.buttons.length; i++)
    {
        var item = tool.buttons[i];
        if (item.text != undefined)
        {

            if (item.PermissionNo == undefined)
            {
                break;
            }
            else
            {
                count++;
                urlParam = urlParam + "&" + "P" + count + "=" + item.PermissionNo;
            }
        }
    }
    urlParam = "&Count=" + count + urlParam;

    var conn = new Ext.data.Connection();
    conn.request(
        {
            url: _netsvrurl+"Service/SystemManage/PermissionService.aspx?Method=GetOperationPermission" + urlParam,
            params: { temp: 'temp' },
            method:'post',
            scope:this,
            callback:function(options,success,response)
            {
                if (success)
                {
                    var json = Ext.util.JSON.decode(response.responseText);
                    var num = 0;
                    for (var i = 0; i < tool.buttons.length; i++)
                    {
                        var item = tool.buttons[i];
                        if (item.text != undefined && item.PermissionNo != undefined)
                        {
                            if (json.Data[num].IsValid == false)
                            {
                                //item.hide();
                                item.hidden = true;
                                nextI = (i+1)==tool.buttons.length?i:i+1;
                                if (tool.buttons[nextI].text == undefined)
                                {
                                    tool.buttons[nextI] = {hidden:true};
                                }
                            }
                            else
                            {
                                item.hidden = false;
                                //item.show();
                            }
                            num++;
                        }
                    }
                }
                else
                {
//                Ext.MessageBox.alert("提示","权限设置出错！");
                }
            }
        });
}


//设置Menu的按钮权限
function SetMenuPermission(tool,selectId)
{
    var count = 0;
    var urlParam = "";
    for (var i = 0; i < tool.items.length; i++)
    {
        var item = tool.items.items[i];
        if (item.text != undefined)
        {
            if (item.PermissionNo == undefined)
            {
                break;
            }
            else
            {
                count++;
                urlParam = urlParam + "&" + "P" + count + "=" + item.PermissionNo;
            }
        }
    }
    urlParam = "&Count=" + count + urlParam;

    var conn = new Ext.data.Connection();
    conn.request({
        url: _netsvrurl+"Service/SystemManage/PermissionService.aspx?Method=GetOperationPermission" + urlParam,
        params:{temp:'temp'},
        method:'post',
        scope:this,
        callback:function(options,success,response)
        {
            if (success)
            {
                var json = Ext.util.JSON.decode(response.responseText);
                var num = 0;
                for (var i = 0; i < tool.items.length; i++)
                {
                    var item = tool.items.items[i];
                    if (item.text != undefined && item.PermissionNo != undefined)
                    {
                        if (json.Data[num].IsValid == false)
                        {
                            item.hide();
                            nextI = (i+1)==tool.items.length?i:i+1;
                            if (tool.items.items[nextI].text == undefined)
                            {
                                tool.items.items[nextI].hide();
                            }
                        }
                        else
                        {
                            if(selectId == '0' && i > 0)
                            {
                                item.hide();
                            }
                            else
                            {
                                item.show();
                            }
                        }
                        num++;
                    }
                }
            }
            else
            {
//                Ext.MessageBox.alert("提示","权限设置出错！");
            }
        }
    });
}

function getCookie(name)
{
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg))
        return decodeURI(arr[2]);
    else
        return null;
}

function getObjectURL(file) {
    var url = null;
    if (window.createObjectURL != undefined) {
        url = window.createObjectURL(file)
    } else if (window.URL != undefined) {
        url = window.URL.createObjectURL(file)
    } else if (window.webkitURL != undefined) {
        url = window.webkitURL.createObjectURL(file)
    }
    return url;
}

function getResultData(result) {
    if (result.data[0] != undefined) {
        return result.data[0];
    }else {
        return result.data;
    }
}

function getResultMeta(result) {
    if (result.data[0] != undefined) {
        return result.meta[0];
    }else {
        return result.meta;
    }
}

function isJsNullObj(tobj) {
    if (tobj==null||tobj==0||tobj==""||tobj==undefined) {
        return true;
    }else {
        return false;
    }
}

var _current_angle = 0;

function _roata_angle(_obj){
    _current_angle = (_current_angle+90)%360;
    _obj.style.transform = 'rotate('+_current_angle+'deg)';
}


function _ShowPreviewWindow(image_url) {
    _current_angle=0;

    var PreviewWindow = new Ext.Window({
        width: 660,
        height: 640,
        closeAction: 'close',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        maximized: false,
        autoScroll: false,
        closable: false,
        items: [
            {
                xtype: 'box',
                width: 600, // 图片宽度
                height: 600, // 图片高度
                autoEl: {
                    tag: 'img',
                    src: image_url,
                    onclick:"_roata_angle(this)"
                }
            }
        ],
        title: '预览窗口...',
        buttonAlign: 'center',
        buttons: [
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    PreviewWindow.close();
                }
            }
        ]
    });

    PreviewWindow.show();
}

function _GetCurrentWeekFirstDate() {
    var nowTemp = new Date();//当前时间
    var oneDayLong = 24*60*60*1000 ;//一天的毫秒数
    var c_time = nowTemp.getTime() ;//当前时间的毫秒时间
    var c_day = nowTemp.getDay()||7;//当前时间的星期几
    var m_time = c_time - (c_day-1)*oneDayLong;//当前周一的毫秒时间
    var ttday = new Date(m_time);//设置周一时间对象
    return ttday;
}

function _GetCurrentWeekLastDate() {
    var nowTemp = new Date();//当前时间
    var oneDayLong = 24*60*60*1000 ;//一天的毫秒数
    var c_time = nowTemp.getTime() ;//当前时间的毫秒时间
    var c_day = nowTemp.getDay()||7;//当前时间的星期几
    var m_time = c_time + (7-c_day)*oneDayLong;//当前周一的毫秒时间
    var ttday = new Date(m_time);//设置周一时间对象
    return ttday;
}

function _GetWeekFirstDate(date) {
    var nowTemp = date;//当前时间
    var oneDayLong = 24*60*60*1000 ;//一天的毫秒数
    var c_time = nowTemp.getTime() ;//当前时间的毫秒时间
    var c_day = nowTemp.getDay()||7;//当前时间的星期几
    var m_time = c_time - (c_day-1)*oneDayLong;//当前周一的毫秒时间
    var ttday = new Date(m_time);//设置周一时间对象
    return ttday;
}

function _GetWeekLastDate(date) {
    var nowTemp = date;//当前时间
    var oneDayLong = 24*60*60*1000 ;//一天的毫秒数
    var c_time = nowTemp.getTime() ;//当前时间的毫秒时间
    var c_day = nowTemp.getDay()||7;//当前时间的星期几
    var m_time = c_time + (7-c_day)*oneDayLong;//当前周一的毫秒时间
    var ttday = new Date(m_time);//设置周一时间对象
    return ttday;
}

function _GetPreWeekFirstDate() {
    var nowTemp = new Date();//当前时间
    var oneDayLong = 24*60*60*1000 ;//一天的毫秒数
    var c_time = nowTemp.getTime() ;//当前时间的毫秒时间
    var c_day = nowTemp.getDay()||7;//当前时间的星期几
    var m_time = c_time - (c_day-1+7)*oneDayLong;//当前周一的毫秒时间
    var ttday = new Date(m_time);//设置周一时间对象
    return ttday;
}

function _GetPreWeekLastDate() {
    var nowTemp = new Date();//当前时间
    var oneDayLong = 24*60*60*1000 ;//一天的毫秒数
    var c_time = nowTemp.getTime() ;//当前时间的毫秒时间
    var c_day = nowTemp.getDay()||7;//当前时间的星期几
    var m_time = c_time - (c_day)*oneDayLong;//当前周一的毫秒时间
    var ttday = new Date(m_time);//设置周一时间对象
    return ttday;
}

function _sleep(numberMillis) {
    var now = new Date();
    var exitTime = now.getTime() + numberMillis;
    while (true) {
        now = new Date();
        if (now.getTime() > exitTime)
            return;
    }
}

function _SetHistoryPageJsObject(id,vv) {
    var ret = hoMap.get(id);
    if (isJsNullObj(ret)) {
        hoMap.set(id,vv);
    }
}

function _GetHistoryPageJsObject(id) {
    var vv = hoMap.get(id);
    if (isJsNullObj(vv)) {
        return null;
    }
    return vv;
}

function CloseWindowToReLogin()
{
    var outw = null;
    if(window.top.opener!=null)
    {
        outw = window.top.opener.top;
    } else {
        outw = window.top;
    }

    if(window.top!=outw)
    {
        window.top.close();
        outw.location.href = "../../../wpframe/Index.html";
    }else if(window.parent!=undefined)
    {
        window.top.close();
    }
}

var _gloab_exit_window=null;
function ShowExitMessage(msg)
{
    if(_gloab_exit_window!=null&&_gloab_exit_window!=undefined)
    {

    }else
    {
        _gloab_exit_window=Ext.MessageBox.alert('提示',msg,function(){
            _gloab_exit_window=null;
            CloseWindowToReLogin();
        });
    }
}

function AjaxRequestComplete(conn, response, options) {
    try
    {
        var robj = Ext.decode(response.responseText);
        if (robj.needrl == "true" || robj.needrl == true) {
            if (robj.text != undefined) {
                ShowExitMessage(robj.text);
            } else {
                ShowExitMessage("您当前不在登录状态，请刷新重新登录后再尝试此操作！");
            }
        }
    } catch (e) {
    }
}

function AjaxBeforeRequest(conn,options) {
    try
    {
        var storage = window.sessionStorage;
        var jtoken="";
        if(storage!=undefined)
        {
            jtoken = storage.getItem(_sysmark+"_token");
        }

        //alert(jtoken);
        if(jtoken==""||jtoken==undefined)
        {
            conn.defaultHeaders={};
        }
    } catch (e) {
    }
}

if (!Ext.Ajax.hasListener("requestcomplete"))
{
    Ext.Ajax.on("requestcomplete", AjaxRequestComplete);
}
if (!Ext.Ajax.hasListener("beforerequest"))
{
    Ext.Ajax.on("beforerequest", AjaxBeforeRequest);
}

function downloadIamge(imgurl,name) {
    var a = document.createElement('a')
    var event = new MouseEvent('click')
    a.download = name||'下载图片名称'
    a.href = imgurl

    a.dispatchEvent(event);
}

function _ExtHideField(field)
{
    field.disable();// for validation
    field.hide();
    field.getEl().up('.x-form-item').setDisplayed(false); // hide label
}

function _ExtShowField(field)
{
    field.enable();
    field.show();
    field.getEl().up('.x-form-item').setDisplayed(true);// show label
}

function _ReplaceSCharToEndStr(imps,spc,spr)
{
    var idx=imps.indexOf(spc);
    var slen=imps.substring(idx,imps.length);
    var ns=imps.replace(slen,spr);
    return ns;
}

if(!Ext.grid.GridView.prototype.templates) {
    Ext.grid.GridView.prototype.templates = {};
}
Ext.grid.GridView.prototype.templates.cell =  new  Ext.Template(
    '<td class="x-grid3-col x-grid3-cell x-grid3-td-{id} x-selectable {css}" style="{style}" tabIndex="0" {cellAttr}>' ,
    '<div class="x-grid3-cell-inner x-grid3-col-{id}" {attr}>{value}</div>' ,
    '</td>'
);

function _ShowTimeMessageBox(title,msg,sec)
{
    var msgbox=Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.INFO
    });
    setTimeout(function(){
        msgbox.hide();
    }, sec*1000);
}
function _ShowMessageBox(title,msg)
{
    Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.INFO
    });
}
function _ShowWaringBox(title,msg)
{
    Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.WARNING
    });
}
function _ShowErrorBox(title,msg)
{
    Ext.MessageBox.show({
        title:title,
        msg: msg,
        buttons: Ext.Msg.OKCANCEL,
        width:300,
        closable:true,
        icon: Ext.MessageBox.ERROR
    });
}