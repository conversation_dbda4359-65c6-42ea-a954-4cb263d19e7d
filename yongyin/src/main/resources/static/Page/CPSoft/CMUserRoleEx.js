function CMUserRoleEx(ownerStr) 
{
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var cmGroup = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '扩展公司',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'COMP_ID',
        displayField: 'COMP_NAME',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetGroupList2',
            fields: [
                 'COMP_ID', 'COMP_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var cmModule0 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '检索模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetModuleList',
            fields: [
                'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var mid = rec.data.ID;
                cmRole0.store.reload({ params: { RoleKind: mid} });
            }
        }
    });

    var cmRole0 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '检索角色',
        typeAhead: true,
        triggerAction: 'all',
        forceSelection: true,
        selectOnFocus: true,
        lazyRender: true,
        lazyInit: false,
        mode: 'local',
        valueField: 'Id',
        displayField: 'RoleName',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        enableKeyEvents: true,
        lastQuery: '',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CmmOpt/GetRoleList',
            fields: [
                 'Id', 'RoleName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'focus': function (e) {
                e.expand();
                this.doQuery(this.allQuery, true);
            },
            'beforequery': function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    var vcount = 0;
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        if (text.indexOf(value) != -1) {
                            vcount = vcount + 1;
                            return true;
                        }
                        return false;
                    });
                    combo.expand();
                    if (vcount > 0) {
                        //Ext.MessageBox.alert("操作提示", "vcount=" + vcount);
                    }
                    return false;
                }
            }
        }
    });

    var cmModule1 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '扩展模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetModuleList',
            fields: [
                'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var mid = rec.data.ID;
                cmRole1.store.reload({ params: { RoleKind: mid} });
            }
        }
    });

    var cmRole1 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '扩展角色',
        typeAhead: true,
        triggerAction: 'all',
        forceSelection: true,
        selectOnFocus: true,
        lazyRender: true,
        lazyInit: false,
        mode: 'local',
        valueField: 'Id',
        displayField: 'RoleName',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        enableKeyEvents: true,
        lastQuery: '',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CmmOpt/GetRoleList',
            fields: [
                 'Id', 'RoleName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'focus': function (e) {
                e.expand();
                this.doQuery(this.allQuery, true);
            },
            'beforequery': function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    var vcount = 0;
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        if (text.indexOf(value) != -1) {
                            vcount = vcount + 1;
                            return true;
                        }
                        return false;
                    });
                    combo.expand();
                    if (vcount > 0) {
                        //Ext.MessageBox.alert("操作提示", "vcount=" + vcount);
                    }
                    return false;
                }
            }
        }
    });

    var ExtendRoleRight = function (person, role) {
        var role0 = cmRole0.getValue();
        var role1 = cmRole1.getValue();
        var comp = cmGroup.getValue();
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmUserRoleEx/ExtendRoleRight',
            params: {
                RoleId0: role0,
                RoleId1: role1,
                CompId: comp
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);
                if (resp.text != undefined) {
                    Ext.MessageBox.alert("操作提示", resp.text);
                }
            }
        });
    }

    var yGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/CmUserRoleEx/GetRoleUserList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName']
        })
    });

    var yEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: yGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "所属部门", width: 100, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'TopGroupName' }
        ]
    });

    var cmForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 920,
        layout: 'form',
        autoScroll: false,
        items: [
            cmGroup,
            cmModule0,
            cmRole0,
            cmModule1,
            cmRole1,
            {
                xtype: 'fieldset',
                autoHeight: true,
                layout: 'fit',
                items: [yEditGrid]
            }
        ]
     });

    var cmWindow = new Ext.Window({
        width: 920,
        height: 620,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: cmForm,
        title: '含有特定权限的用户扩展指定权限',
        buttonAlign: 'center',
        buttons: [
        {
            text: '查询',
            height: 30,
            handler: function () {
                obj.RefreshData();
            }
        },
        {
            text: '扩展',
            height: 30,
            handler: function () {
                ExtendRoleRight();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                cmWindow.hide();
            }
        }]
    });

    this.ShowWindow = function () {
        cmForm.form.reset();
        cmWindow.show();
        yGridStore.removeAll();
    }

    this.RefreshData = function () {
        var comp = cmGroup.getValue();
        var role = cmRole0.getValue();
        yGridStore.reload({ params: { CompId: comp, RoleId: role }});
    }
}

CMUserRoleEx.prototype = {
    constructor: CMUserRoleEx
}