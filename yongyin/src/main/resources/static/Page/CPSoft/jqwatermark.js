 /**
     * 给页面添加文字水印
     * <AUTHOR>
     */
(function(){

    var watermark = function(self){
        this.elem = self;
    }

    watermark.prototype = {
        defaults : {
            texts : ['此处水印文字'],
            textRotate : -30 , //文字旋转 度数
            textColor : '#e5e5e5', //文字颜色
            textFont : '14px 微软雅黑' //字体
        },
        options : {
            canvas : []
        },
        init : function(options){
             $j.extend(this.options, this.defaults, options);
            var $jbody = $j('body'),
                can = this.__createCanvas($jbody),
                settings = this.options,
                txtlen = settings.texts.length;

            settings.deg = settings.textRotate * Math.PI / 180; //js里的正弦余弦用的是弧度

            this.__calcTextSize($jbody);
            settings.canvasHeight = settings.canvasWidth * Math.abs(Math.sin(settings.deg)) + Math.cos(settings.deg) * settings.textHeight;
            //var ctx = this.__setCanvasStyle(can, settings.canvasWidth, settings.canvasHeight);
            var ctx = this.__setCanvasStyle(can, $jbody.innerWidth(), $jbody.innerHeight());

            this.__drawText(ctx);

            //合并canvas
            //ctx.drawImage(can, 0, 0, $jbody.innerWidth(), $jbody.innerHeight());
            //ctx.drawImage(can, 0, 0, settings.canvasWidth, settings.canvasHeight);
            var dataURL = can.toDataURL("Image/png");
            $j(this.elem).css('backgroundImage', "url("+ dataURL +")");
            //this.__destory();
        },
        __createCanvas : function($jcontainer){
            var canvas = document.createElement('canvas');
            $jcontainer.append(canvas);
            this.options.canvas.push(canvas); 
            return canvas;
        },
        __calcTextSize : function($jcontainer){
            var txts = [],
                maxWidth = 0,
                canvasWidth = 0,
                settings = this.options;
            var mark1 = settings.texts[0];
            var mark2 = settings.texts[1];
            var mark3 = settings.texts[2];
            var span = $j('<span style="font:' + settings.textFont + ';visibility: hidden;display: inline-block;"> ' + mark1 + '</span>')
                .appendTo($jcontainer);
            var tWidth1 = span[0].offsetWidth;
            span.remove();

            span = $j('<span style="font:' + settings.textFont + ';visibility: hidden;display: inline-block;"> ' + mark2 + '</span>')
                .appendTo($jcontainer);
            var tWidth2 = span[0].offsetWidth;
            span.remove();

            span = $j('<span style="font:' + settings.textFont + ';visibility: hidden;display: inline-block;"> ' + mark3 + '</span>')
                .appendTo($jcontainer);
            var tWidth3 = span[0].offsetWidth;
            span.remove();

            var span = $j('<span><span style="font:' + settings.textFont + ';visibility: hidden;display: inline-block;"> ' + mark1 + '</span><br/><span style="font:' + settings.textFont + ';visibility: hidden;display: inline-block;"> ' + mark2 + '</span><br/><span style="font:' + settings.textFont + ';visibility: hidden;display: inline-block;"> ' + mark3 + '</span></span>')
                .appendTo($jcontainer);
            var tWidth = span[0].offsetWidth;
            var tHeight = span[0].offsetHeight;
            span.remove();

            maxWidth = Math.max(maxWidth, tWidth1);
            maxWidth = Math.max(maxWidth, tWidth2);
            maxWidth = Math.max(maxWidth, tWidth3);

            var shadow = Math.cos(settings.deg) * tWidth;
            canvasWidth += (tWidth < shadow ? shadow : tWidth) - tHeight * Math.sin(settings.deg);

            settings.markWidth1 = tWidth1;
            settings.markWidth2 = tWidth2;
            settings.markWidth3 = tWidth3;
            settings.maxWidth = maxWidth;
            settings.textHeight = tHeight;
            settings.canvasWidth = canvasWidth;
        },
        __setCanvasStyle : function(canvas, width, height, notextstyle){
            canvas.width = width;
            canvas.height = height;
            canvas.style.display='none';
           
            var ctx = canvas.getContext('2d');
            if(!notextstyle){
                var deg = this.options.deg,
                    absSindeg = Math.abs(Math.sin(deg));
                ctx.rotate(deg);

                //基于视窗的 x、y偏移量
                var offset = this.options.textHeight * absSindeg;
                var nx = - offset * Math.cos(deg),
                    ny = - offset * absSindeg;
                ctx.translate( nx, ny * absSindeg);

                ctx.font = this.options.textFont;
                ctx.fillStyle = this.options.textColor;
                ctx.textAlign = 'left'; 
                ctx.textBaseline = 'Middle';
            }
            return ctx;
        },
        __drawText: function(ctx) {
            var settings = this.options;

            var wnap1 = (settings.maxWidth - settings.markWidth1) / 2;
            var wnap2 = (settings.maxWidth - settings.markWidth2) / 2;
            var wnap3 = (settings.maxWidth - settings.markWidth3) / 2;

            var x = -200;
            var y = 200;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 0;
            var y = 200;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 200;
            var y = 200;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 400;
            var y = 200;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = -200;
            var y = 400;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 0;
            var y = 400;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);
            
            var x = 200;
            var y = 400;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 400;
            var y = 400;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 600;
            var y = 400;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = -200;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 0;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 200;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 400;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 600;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 800;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 1000;
            var y = 600;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = -200;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 0;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 200;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 400;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 600;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 800;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 1000;
            var y = 800;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 200;
            var y = 1000;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 400;
            var y = 1000;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 600;
            var y = 1000;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 800;
            var y = 1000;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 1000;
            var y = 1000;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

            var x = 800;
            var y = 1200;
            ctx.fillText(settings.texts[0], x + wnap1, y);
            ctx.fillText(settings.texts[1], x + wnap2, y + 34);
            ctx.fillText(settings.texts[2], x + wnap3, y + 68);

        },
        __destory : function(){
            $j.each(this.options.canvas, function(i, canvas){
                canvas.remove();
                canvas = null;
            });
        }
    }
   
    $.fn.watermark = function(options){
        new watermark(this).init(options);
    }

})(jQuery);