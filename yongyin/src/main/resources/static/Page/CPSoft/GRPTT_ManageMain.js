Ext.namespace('CPS.GRPTT_ManageMain');

//初始化函数
CPS.GRPTT_ManageMain.Initialize = function() {
    //初始数据
    var start = 0;
    var limit = 50;
    var pageSize = limit;
    var funcMark = 'CPS.GRPTT_ManageMain';

    //===================报表类型界面===========================start
    var GRPTT_Form = new Ext.FormPanel({
        labelWidth: 100,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 530,
        layout: 'form',
        autoScroll: true,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
            {   //行六
                id: funcMark + '.GRPTT_Form.IdGRPTT_ID',
                xtype: "textfield",
                name: 'GRPTT_ID',
                fieldLabel: "类型ID",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: funcMark+'.GRPTT_Form.IdGRPTT_NAME',
                xtype: "textfield",
                name: 'GRPTT_NAME',
                fieldLabel: "类型名称",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },  
            {   //行六
                id: funcMark + '.GRPTT_Form.IdGRPTT_CODE',
                xtype: "textfield",
                name: 'GRPTT_CODE',
                fieldLabel: "类型编码",
                maxLength: 1000,
                allowBlank: false,
                anchor: '98%'
            },                    
            {   //行六
                id: funcMark + '.GRPTT_Form.IdGRPTT_REMARK',
                xtype: "textfield",
                name: 'GRPTT_REMARK',
                fieldLabel: "类型备注",
                maxLength: 1000,
                allowBlank: true,
                anchor: '98%'
            }
        ],

        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [
                { name: 'GRPTT_ID', mapping: 'GRPTT_ID', type: 'int' },
                { name: 'GRPTT_NAME', mapping: 'GRPTT_NAME', type: 'string' },
                { name: 'GRPTT_CODE', mapping: 'GRPTT_CODE', type: 'string' },
                { name: 'GRPTT_REMARK', mapping: 'GRPTT_REMARK', type: 'string' }
            ]
        })
    });

    var GRPTT_FormWindow = new Ext.Window({
        id: funcMark+'.IdGRPTT_FormWindow',
        width: 560,
        height: 400,
        closeAction: 'hide',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: true,
        items: GRPTT_Form,
        title: '表单',
        buttonAlign: 'center',
        buttons: [
            {
                text: '保存',
                height: 30,
                disabled: true
            },
            {
                text: '关闭',
                height: 30,
                handler: function() {
                    GRPTT_FormWindow.hide();
                }
            }
        ]
    });

    function onAddButtonClick() {

        GRPTT_Form.form.reset();

        GRPTT_FormWindow.setTitle(ExtractTitleString("报表类型==新增报表类型=="));
        GRPTT_FormWindow.buttons[0].handler = function() {
            var submitButton = this;
            submitButton.disable();
            if (GRPTT_Form.form.isValid()) {
                GRPTT_Form.form.doAction('submit',
                {
                    url: '../../Service/CpSoft/GrptT/AddGRPTT',
                    method: 'post',
                    params: {},
                    waitTitle: "请稍候",
                    waitMsg: '正在保存数据...',
                    success: function(form, action) {
                        submitButton.enable();
                        GRPTT_Form.ownerCt.hide();
                        RefreshData();
                    },
                    failure: function(form, action) {
                        var tip = action.result;
                        if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                        submitButton.enable();
                    }
                });
            }
            else {
                submitButton.enable();
            }
        };
        GRPTT_FormWindow.buttons[0].enable();
        GRPTT_FormWindow.show();

    }

    function onEditButtonClick() {
        var grid = Ext.getCmp(funcMark+".IdGRPTT_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var title = "报表类型==修改报表类型==";
            GRPTT_FormWindow.setTitle(ExtractTitleString(title));
            GRPTT_FormWindow.buttons[0].handler = function() {
                var submitButton = this;
                submitButton.disable();

                var id = GRPTT_Form.form.reader.jsonData.data[0].GRPTT_ID;
                if (GRPTT_Form.form.isValid()) {
                    GRPTT_Form.form.doAction('submit',
                    {
                        url: '../../Service/CpSoft/GrptT/ModifyGRPTT',
                        method: 'post',
                        params: { ID: id },
                        waitTitle: "请稍候",
                        waitMsg: '正在保存数据...',
                        success: function(form, action) {
                            submitButton.enable();
                            GRPTT_Form.ownerCt.hide();
                            RefreshData();
                        },
                        failure: function(form, action) {
                            var tip = action.result;
                            if (tip != undefined && tip.text != undefined) Ext.Msg.alert('提示', tip.text);
                            submitButton.enable();
                        }
                    });
                }
                else {
                    submitButton.enable();
                }
            };

            GRPTT_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.GRPTT_ID;
            var url = '../../Service/CpSoft/GrptT/GetGRPTTById?ID=' + id;
            GRPTT_FormWindow.show();
            GRPTT_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function(form, action) {
                    GRPTT_FormWindow.buttons[0].enable();
                },
                failure: function(form, action) {
                }
            });
        };
    }

    function onLookButtonClick() {
        var grid = Ext.getCmp(funcMark+".IdGRPTT_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else 
        {
            var title = "报表类型==查看报表类型==";
            GRPTT_FormWindow.setTitle(ExtractTitleString(title));
            GRPTT_Form.form.reset();
            var id = grid.getSelectionModel().getSelected().data.GRPTT_ID;
            var url = '../../Service/CpSoft/GrptT/GetGRPTTById?ID=' + id;
            GRPTT_FormWindow.show();
            GRPTT_Form.load({
                url: url,
                waitTitle: "请稍候",
                waitMsg: '正在加载数据...',
                success: function(form, action) {
                    GRPTT_FormWindow.buttons[0].disable();
                },
                failure: function(form, action) {
                }
            });
        };
    }

    function onDeleteButtonClick() {
        var grid = Ext.getCmp(funcMark+".IdGRPTT_Grid");
        if (grid.getSelectionModel().getSelections()[0] == undefined) {
            Ext.MessageBox.alert('提示', '请选中列表行！');
        }
        else {
            var tip = "确实要删除所选的记录吗？";
            Ext.MessageBox.confirm('提示', tip, function(btn) {
                if (btn == 'yes') {
                    AjaxRequest(
                    {
                        url: "../../Service/CpSoft/GrptT/DeleteGRPTT",
                        successfn: function(data) {
                            RefreshData();
                        },
                        failurefn: function(data) {
                        },
                        params: { ID: grid.getSelectionModel().getSelected().data.GRPTT_ID }
                    });
                }
            });
        }
    }
    //===================报表类型界面===========================end  

    //===================报表类型管理form===========================start
    var toolbar = new Ext.Toolbar({
        items:
        [
             {
                 text: '新增',
                 iconCls: 'ButtonFormAdd',
                 handler: onAddButtonClick
             },
             {
                 text: '修改',
                 iconCls: 'ButtonFormEdit',
                 handler: onEditButtonClick
             },
             {
                 text: '删除',
                 iconCls: 'ButtonFormDelete',
                 handler: onDeleteButtonClick
             },
             {
                 text: '查看',
                 iconCls: 'ButtonFormLook',
                 handler: onLookButtonClick
             },
            '-',
            '->',
            '-',
            '模糊查询',
            {
                id: funcMark+'.toolbar.SearchTJ',
                xtype: 'textfield',
                width: 200,
                name: 'SearchTJ',
                anchor: '98%'
            },
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: LoadData
            },
            {
                text: '刷新',
                id: funcMark + '.toolbar.IdREFRESH',
                iconCls: 'ButtonFormLook',
                listeners: {
                    click: function() {
                        RefreshData();
                    }
                }
            },
            {
                text: '导出XLS',
                iconCls: 'ButtonFormLook',
                handler: function()
                {
                    ExportExcel();
                }
            }
        ]
    });

    function RefreshData() {
        Ext.getCmp(funcMark+".IdGRPTT_Grid").store.reload({ params: {
            start: start,
            limit: limit,
            seachtj: Ext.getCmp(funcMark+'.toolbar.SearchTJ').getValue()
        }
        });
    }
    function LoadData() {
        start = 0;
        Ext.getCmp(funcMark+".IdGRPTT_Grid").store.reload({ params: {
            start: 0,
            limit: limit,
            seachtj: Ext.getCmp(funcMark+'.toolbar.SearchTJ').getValue()
        }
        });
    }

    function ExportExcel() {
        _ExtDownloadFile("../../Service/CpSoft/GrptT/ExportExcel", {
            seachtj: Ext.getCmp(funcMark+'.toolbar.SearchTJ').getValue()
        });
    }
    
    var gridStore = new Ext.data.Store({
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/GrptT/GetGRPTTList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['GRPTT_ID', 'GRPTT_NAME', 'GRPTT_CODE', 'GRPTT_REMARK']
        }),
        sortInfo: { field: "GRPTT_ID", direction: "ASC" },
        remoteSort: true
    });

    var record_start = 0;
    var grid = new Ext.grid.GridPanel({
        id: funcMark+'.IdGRPTT_Grid',
        height: 350,
        store: gridStore,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        autoScroll: true,
        columns: [
            new Ext.grid.RowNumberer({ renderer: function(v, m, rec, rIdx) { return record_start + 1 + rIdx } }),
            { header: "报表类型ID", width: 80, sortable: true, dataIndex: 'GRPTT_ID' },
            { header: "报表类型名称", width: 120, sortable: true, dataIndex: 'GRPTT_NAME' },
            { header: "报表类型编码", width: 120, sortable: true, dataIndex: 'GRPTT_CODE' },
            { header: "报表类型备注", width: 120, sortable: true, dataIndex: 'GRPTT_REMARK' }
        ],
        loadMask: { msg: "数据加载中，请稍待..." },
        stripeRows: true,
        viewConfig: {
            forceFit: true 
        },
        sm: new Ext.grid.RowSelectionModel({ singleSelect: true,menuDisabled:true }),
        tbar: toolbar,
        listeners:
        {
            "rowdblclick": function(grid, rowIndex, e) {
                if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0 && LoginPerson.RoleNamesString.indexOf("平台管理角色") < 0) {
                    onEditButtonClick();
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: gridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st) {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['seachtj'] = Ext.getCmp(funcMark+'.toolbar.SearchTJ').getValue();
                this.store.load({ params: o });
            }
        })
    });

    toolbar.items.items[0].disable();
    toolbar.items.items[1].disable();
    toolbar.items.items[2].disable();
    toolbar.items.items[3].disable();
    if (LoginPerson.RoleNamesString.indexOf("系统管理") >= 0 && LoginPerson.RoleNamesString.indexOf("平台管理角色") < 0) {
        toolbar.items.items[0].enable();
        toolbar.items.items[1].enable();
        toolbar.items.items[2].enable();
        toolbar.items.items[3].enable();
    }

    try 
    {  
        if(typeof(eval('ClearOldGridStore'))=="function")  
        {  
            ClearOldGridStore();
        } 
    }catch(e)  
    {  
    } 

    //设置页面布局
    SetNewTabPanel(new Ext.Panel({
        layout: 'border',
        id: "MainTab" + FrameMainTabContainer.ClickedTreeMenuNode.id,
        title: FrameMainTabContainer.ClickedTreeMenuNode.text,
        refreshfn:function() {
            RefreshData();
        },
        items: [
              {
                  region: 'center',
                  width: 400,
                  layout: 'fit',
                  items: [grid]
              }
        ]
    }));
    Ext.QuickTips.init();

    //===================报表类型管理form===========================end
};
CPS.GRPTT_ManageMain.Initialize();
