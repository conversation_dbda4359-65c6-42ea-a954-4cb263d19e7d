Ext.grid.ColumnModel.override({
    getTotalWidth: function (includeHidden) {
        var off = 0;
        if (Ext.isChrome) {
            off = 2;
        };
        if (!this.totalWidth) {
            this.totalWidth = 0;
            for (var i = 0, len = this.config.length; i < len; i++) {
                if (includeHidden || !this.isHidden(i)) {
                    this.totalWidth += this.getColumnWidth(i) + off;
                };
            };
        };
        return this.totalWidth;
    }
});

Ext.grid.GridView.override({
    getColumnWidth : function(column)
    {
        var columnWidth = this.cm.getColumnWidth(column),borderWidth = this.borderWidth;
        if (Ext.isNumber(columnWidth))
        {
            if (Ext.isBorderBox /**这里去掉了*/)
            {
                return columnWidth + "px";
            } else
            {
                return Math.max(columnWidth - borderWidth, 0) + "px";
            }
        } else
        {
            return columnWidth;
        }
    }
})
