function CMEncoder(ownerStr) 
{
    var obj = this;

    var EnForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 420,
        layout: 'form',
        autoScroll: false,
        items: [
            {   //行六
                id: ownerStr + '.EnForm.IdSrcCode',
                xtype: "textfield",
                name: 'SrcCode',
                fieldLabel: "加密源",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            },
            {   //行六
                id: ownerStr + '.EnForm.IdDestCode',
                xtype: "textfield",
                name: 'DestCod',
                fieldLabel: "密码串",
                maxLength: 100,
                allowBlank: false,
                anchor: '98%'
            }
        ],
        reader: new Ext.data.JsonReader({
            root: 'data',
            fields: [

            ]
        })
     });

    var EnWindow = new Ext.Window({
        width: 420,
        height: 320,
        closeAction: 'close',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: EnForm,
        layout: 'fit',
        title: '密码测试',
        buttonAlign: 'center',
        buttons: [
        {
            text: '加密',
            height: 30,
            handler: function () {
                var submitButton = this;
                submitButton.disable();

                var srcCode = Ext.getCmp(ownerStr + '.EnForm.IdSrcCode').getValue();
                var h5srcCode = hex_md5(srcCode);
                Ext.Ajax.request({
                    url: '../../Service/CpSoft/CmEncoder/ExecuteEncoder',
                    params: {
                        SrcCode: h5srcCode
                    },
                    method: 'POST',
                    success: function (res, opts) {
                        if (res.responseText == "" || res.responseText == undefined) return;
                        var resp = Ext.decode(res.responseText);

                        if (resp.success === true || resp.success === "true") {
                            if (resp.text != undefined) {
                                Ext.getCmp(ownerStr + '.EnForm.IdDestCode').setValue(resp.text);
                            }
                        } else {
                            if (resp.text != undefined) {
                                Ext.MessageBox.alert("操作提示", resp.text);
                            }
                        }
                        submitButton.enable();
                    }
                });
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                EnWindow.close();
            }
        }]
    });

    this.ShowWindow = function () {
        EnForm.form.reset();
        EnWindow.show();
    }
}

CMEncoder.prototype = {
    constructor: CMEncoder
}