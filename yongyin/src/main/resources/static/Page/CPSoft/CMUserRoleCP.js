function CMUserRoleCP(ownerStr) 
{
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var cmGroup0 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属公司',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'COMP_ID',
        displayField: 'COMP_NAME',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetGroupList',
            fields: [
                 'COMP_ID', 'COMP_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var cid = rec.data.COMP_ID;
                cmUser0.store.reload({ params: { CompId: cid} });
            }
        }
    });

    var cmUser0 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '公司用户',
        typeAhead: true,
        triggerAction: 'all',
        forceSelection: true,
        selectOnFocus: true,
        lazyRender: true,
        lazyInit: false,
        mode: 'local',
        valueField: 'Id',
        displayField: 'RealName',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        enableKeyEvents: true,
        lastQuery: '',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CmmOpt/GetUserList',
            fields: [
                 'Id', 'RealName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'focus': function (e) {
                e.expand();
                this.doQuery(this.allQuery, true);
            },
            'beforequery': function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    var vcount = 0;
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        if (text.indexOf(value) != -1) {
                            vcount = vcount + 1;
                            return true;
                        }
                        return false;
                    });
                    combo.expand();
                    if (vcount > 0) {
                        //Ext.MessageBox.alert("操作提示", "vcount=" + vcount);
                    }
                    return false;
                }
            }
        }
    });

    var cmGroup1 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '复刻公司',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'COMP_ID',
        displayField: 'COMP_NAME',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetGroupList',
            fields: [
                 'COMP_ID', 'COMP_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var cid = rec.data.COMP_ID;
                cmUser1.store.reload({ params: { CompId: cid} });
            }
        }
    });

    var cmUser1 = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '复刻用户',
        typeAhead: true,
        triggerAction: 'all',
        forceSelection: true,
        selectOnFocus: true,
        lazyRender: true,
        lazyInit:false,
        mode: 'local',
        valueField: 'Id',
        displayField: 'RealName',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        enableKeyEvents: true,
        lastQuery: '',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CmmOpt/GetUserList',
            fields: [
                 'Id', 'RealName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'focus': function (e) {
                e.expand();
                this.doQuery(this.allQuery, true);
            },
            'beforequery': function (e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    var vcount = 0;
                    combo.store.filterBy(function (record, id) {
                        var text = record.get(combo.displayField);
                        if (text.indexOf(value) != -1) {
                            vcount = vcount + 1;
                            return true;
                        }
                        return false;
                    });
                    combo.expand();
                    if (vcount > 0) {
                        //Ext.MessageBox.alert("操作提示", "vcount=" + vcount);
                    }
                    return false;
                }
            }
        }
    });

    var cmModule = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetModuleList',
            fields: [
                'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var CopyUserRight=function() {
        var person0 = cmUser0.getValue();
        var person1 = cmUser1.getValue();
        var module = cmModule.getValue();
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmUserRoleCp/CopyUserRight',
            params: {
                PersonId0: person0,
                PersonId1: person1,
                ModuleId: module
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.text != undefined) {
                    Ext.MessageBox.alert("操作提示", resp.text);
                }
            }
        });
    }

    var yGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/CmUserRoleCp/GetUserRoleList"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'RoleName','RoleKind']
        })
    });

    var yEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: yGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "角色ID", width: 100, sortable: true, dataIndex: 'Id' },
            { header: "角色名称", width: 100, sortable: true, dataIndex: 'RoleName' },
            { header: "所属模块", width: 100, sortable: true, dataIndex: 'RoleKind' }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var roleId = grid.store.getAt(rowIndex).get('Id');
                var personId = cmUser.getValue();
                if (columnIndex == 3) {
                    DeleteUserRight(personId, roleId);
                }
            }
        }
    });

    var cmForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 920,
        layout: 'form',
        autoScroll: false,
        items: [
            cmGroup0,
            cmUser0,
            cmModule,
            cmGroup1,
            cmUser1,
            {
                xtype: 'fieldset',
                autoHeight: true,
                layout: 'fit',
                items: [yEditGrid]
            }
        ]
     });

    var cmWindow = new Ext.Window({
        width: 920,
        height: 620,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: cmForm,
        title: '特定用户权限复刻至指定用户',
        buttonAlign: 'center',
        buttons: [
        {
            text: '查询',
            height: 30,
            handler: function () {
                obj.RefreshData();
            }
        },
        {
            text: '复刻',
            height: 30,
            handler: function () {
                CopyUserRight();
            }
        },
        {
            text: '关闭',
            height: 30,
            handler: function () {
                cmWindow.hide();
            }
        }]
    });

    this.ShowWindow = function () {
        cmForm.form.reset();
        cmWindow.show();
        yGridStore.removeAll();
    }

    this.RefreshData = function () {
        var module = cmModule.getValue();
        var user = cmUser0.getValue();
        yGridStore.reload({ params: { ModuleId: module, UserId: user }, callback: function () {
            if (yEditGrid.store.getCount() > 0) {
                yEditGrid.getSelectionModel().selectAll();
            }
        } 
        });
    }
}

CMUserRoleCP.prototype = {
    constructor: CMUserRoleCP
}