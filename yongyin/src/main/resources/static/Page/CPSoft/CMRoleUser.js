function CMRoleUser(ownerStr) 
{
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var cmGroup = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属公司',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'COMP_ID',
        displayField: 'COMP_NAME',
        allowBlank: true,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetGroupList2',
            fields: [
                 'COMP_ID', 'COMP_NAME'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        })
    });

    var cmModule = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '所属模块',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'ID',
        displayField: 'module_name',
        allowBlank: false,
        anchor: '98%',
        editable: false,
        store: new Ext.data.JsonStore({
            autoLoad: true,
            url: '../../Service/CpSoft/CmmOpt/GetModuleList',
            fields: [
                'ID', 'module_name'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'select': function (c, rec, idx) {
                var rolekind = rec.data.ID;
                cmRole.store.reload({ params: { RoleKind: rolekind} });
            }
        }
    });

    var cmRole = new Ext.form.ComboBox({
        name: 'Group',
        fieldLabel: '当前角色',
        typeAhead: true,
        triggerAction: 'all',
        forceSelection: true,
        selectOnFocus: true,
        lazyRender: true,
        lazyInit: false,
        mode: 'local',
        valueField: 'Id',
        displayField: 'RoleName',
        allowBlank: false,
        anchor: '98%',
        editable: true,
        enableKeyEvents: true,
        lastQuery: '',
        store: new Ext.data.JsonStore({
            autoLoad: false,
            url: '../../Service/CpSoft/CmmOpt/GetRoleList',
            fields: [
                 'Id', 'RoleName'
                ],
            root: 'Table',
            totalProperty: 'RecordCount'
        }),
        listeners: {
            'focus': function(e) {
                e.expand();
                this.doQuery(this.allQuery, true);
             },
            'beforequery': function (e) {
                var combo = e.combo; 
                if(!e.forceAll)
                {
                    var value = e.query;
                    var vcount = 0;
                    combo.store.filterBy(function (record, id)
                    {
                        var text = record.get(combo.displayField);
                        if (text.indexOf(value) != -1) {
                            vcount = vcount+1;
                            return true;
                        }
                        return false;
                    });
                    combo.expand();
                    if (vcount > 0) {
                        //Ext.MessageBox.alert("操作提示", "vcount=" + vcount);
                    }
                    return false; 
                } 
            }
        }
    });

    var cmUserMark = new Ext.form.TextField({
        xtype: "textfield",
        name: 'UserMark',
        fieldLabel: "搜索用户",
        allowBlank: true,
        maxLength: 100,
        anchor: '98%'
    });

    var AddRoleRight=function(person,role) {
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmRoleUser/AddRoleRight',
            params: {
                UserId: person,
                RoleId: role 
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success ===true||resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var DeleteRoleRight=function(person,role) {
        Ext.Ajax.request({
            url: '../../Service/CpSoft/CmRoleUser/DeleteRoleRight',
            params: {
                UserId: person,
                RoleId: role
            },
            method: 'POST',
            success: function (res, opts) {
                if (res.responseText == "" || res.responseText == undefined) return;
                var resp = Ext.decode(res.responseText);

                if (resp.success === true || resp.success === "true") {
                    obj.RefreshData();
                } else {
                    if (resp.text != undefined) {
                        Ext.MessageBox.alert("操作提示", resp.text);
                    }
                }
            }
        });
    }

    var yGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/CmRoleUser/GetRoleUserList_Y"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName']
        })
    });


    var yEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: yGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'LoginName',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "取消权限";
                }
            },
            { header: "所属部门", width: 100, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'TopGroupName' }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var UserId = grid.store.getAt(rowIndex).get('Id');
                var RoleId = cmRole.getValue();
                if (columnIndex == 2) {
                    DeleteRoleRight(UserId, RoleId);
                }
            }
        }
    });

    var wGridStore = new Ext.data.Store({
        autoload: false,
        pruneModifiedRecords: true,
        proxy: new Ext.data.HttpProxy({
            url: "../../Service/CpSoft/CmRoleUser/GetRoleUserList_W"
        }),
        reader: new Ext.data.JsonReader({
            root: 'Table',
            totalProperty: 'RecordCount',
            fields: ['Id', 'LoginName', 'RealName', 'GroupId', 'GroupName', 'TopGroupId', 'TopGroupName']
        })
    });

    var wEditGrid = new Ext.grid.GridPanel({
        frame: false,
        stripeRows: false,
        autoScroll: true,
        autoHeight: false,
        enableColumnHide: false,
        enableHdMenu: false,
        sortableColumns: false,
        columnLines: true,
        height: 360,
        store: wGridStore,
        loadMask: { msg: "数据加载中，请稍待..." },
        deferRowRender: false,
        viewConfig: {
            forceFit: true
        },
        columns: [
            { header: "登录账户", width: 100, sortable: true, dataIndex: 'LoginName' },
            { header: "用户姓名", width: 100, sortable: true, dataIndex: 'RealName' },
            { header: "权限操作", width: 160, sortable: true, dataIndex: 'LoginName',
                renderer: function (val, metadata, record) {
                    metadata.style+= "color: blue;text-align:center;text-decoration:underline;";
                    return "添加权限";
                }
            },
            { header: "所属部门", width: 100, sortable: true, dataIndex: 'GroupId' },
            { header: "部门名称", width: 300, sortable: true, dataIndex: 'GroupName' },
            { header: "所属公司", width: 160, sortable: true, dataIndex: 'TopGroupId' },
            { header: "公司名称", width: 160, sortable: true, dataIndex: 'TopGroupName' }
        ],
        listeners:
        {
            'cellclick': function (grid, rowIndex, columnIndex, e) {
                var UserId = grid.store.getAt(rowIndex).get('Id');
                var RoleId = cmRole.getValue();
                if (columnIndex == 2) {
                    AddRoleRight(UserId, RoleId);
                }
            }
        },
        bbar: new Ext.PagingToolbar({
            pageSize: pageSize,
            store: wGridStore,
            displayInfo: true,
            displayMsg: '当前页显示第 {0} - {1} 条记录，共 {2} 条',
            emptyMsg: "没有记录",
            doLoad: function (st)
            {
                start = st;
                var o = {};
                o['start'] = start;
                o['limit'] = this.pageSize;
                o['CompId'] = cmGroup.getValue();
                o['RoleId'] = cmRole.getValue();
                o['UserMark'] = cmUserMark.getValue();
                this.store.load({ params: o, callback: function (r, options, success)
                {
                    if (!success) 
                    {
                        Ext.MessageBox.alert('提示', "加载失败.....请稍候再试！");
                    }
                }});
            }
        })
    }); 

    var EditPanel = new Ext.TabPanel({
        activeTab: 0,
        deferredRender: false,
        autoDestroy: true,
        items:
        [
            {
                title: '已配置用户',
                isFixedTab: true,
                layout: 'fit',
                items: [yEditGrid]
            },
            {
                title: '未配置用户',
                isFixedTab: true,
                layout: 'fit',
                items: [wEditGrid]
            }
        ]
    });

    var cmForm = new Ext.FormPanel({
        labelWidth: 80,
        labelAlign: 'left',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 920,
        layout: 'form',
        autoScroll: false,
        items: [
            cmModule,
            cmRole,
            cmGroup,
            cmUserMark,
            {
                xtype: 'fieldset',
                autoHeight: true,
                layout: 'fit',
                items: [EditPanel]
            }
        ]
     });

    var cmWindow = new Ext.Window({
        width: 920,
        height: 620,
        closeAction: 'hide',
        plain: true,
        modal: true,
        resizable: false,
        autoScroll: false,
        items: cmForm,
        title: '通用角色用户管理配置',
        buttonAlign: 'center',
        buttons: [
            {
                text: '查询',
                height: 30,
                handler: function () {
                    obj.RefreshData();
                }
            },
            {
                text: '关闭',
                height: 30,
                handler: function () {
                    cmWindow.hide();
                }
            }
        ]
    });

    this.ShowWindow = function () {
        cmForm.form.reset();
        cmWindow.show();
        yGridStore.removeAll();
        wGridStore.removeAll();
    }

    this.RefreshData = function () {
        var comp = cmGroup.getValue();
        var role = cmRole.getValue();
        var umark = cmUserMark.getValue();
        yGridStore.reload({ params: { CompId: comp, RoleId: role, UserMark: umark }, callback: function () {
            if (yEditGrid.store.getCount() > 0) {
                yEditGrid.getSelectionModel().selectAll();
            }
        } 
        });
        wGridStore.reload({ params: {start: 0,limit: limit,CompId: comp, RoleId: role, UserMark: umark} });
    }
}

CMRoleUser.prototype = {
    constructor: CMRoleUser
}