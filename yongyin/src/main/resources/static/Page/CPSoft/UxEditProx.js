ShowEditFormForUpdate = function (elem, e) {
    var pstr = elem.getValue();
    if (pstr == "") pstr=new Date();

    var value = new Date(pstr);
    var datev = FormatDateStr(value,"yyyy-MM-dd");
    var hourv = FormatDateStr(value,"HH");
    var minv = FormatDateStr(value,"mm");
    var secv = FormatDateStr(value,"ss");
    var msecv = FormatDateStr(value,"l");

    var datef = new Ext.form.DateField({
        fieldLabel: '日期',
        anchor: '98%',
        format: 'Y-m-d',
        value: datev
    });

    var hourSpinner = new Ext.ux.form.SpinnerField({
        fieldLabel: '时',
        anchor: '98%',
        minValue: 0,
        maxValue: 23,
        cls: 'first',
        value: hourv
    });

    var minuteSpinner = new Ext.ux.form.SpinnerField(
    {
        fieldLabel: '分',
        anchor: '98%',
        minValue: 0,
        maxValue: 59,
        value: minv
    });


    var secondSpinner = new Ext.ux.form.SpinnerField(
    {
        fieldLabel: '秒',
        anchor: '98%',
        minValue: 0,
        maxValue: 59,
        value: secv
    });

    var msecondSpinner = new Ext.ux.form.SpinnerField(
    {
        fieldLabel: '毫秒',
        anchor: '98%',
        minValue: 0,
        maxValue: 999,
        value: msecv
    });


    var wmForm = new Ext.FormPanel({
        labelWidth: 40,
        labelAlign: 'right',
        frame: true,
        bodyStyle: 'padding:10px',
        width: 240,
        layout: 'form',
        autoScroll: false,
        //bodyStyle: 'overflow-x:visible;overflow-y:scroll',
        items: [
                datef,
                hourSpinner,
                minuteSpinner,
                secondSpinner,
                msecondSpinner
            ]
    });

    var wmWin = new Ext.Window({
        title: '设置入账时间值 ...',
        width: 360,
        height: 240,
        border: false,
        resiable: true,
        modal: true,
        layout: 'fit',
        autoScroll: true,
        items: [
            wmForm
        ],
        buttonAlign: 'center',
        buttons: [
        {
            text: '当前时间',
            region: "center",
            handler: function () {
                var cdt = new Date();
                datef.setValue(FormatDateStr(cdt,"yyyy-MM-dd"));
                hourSpinner.setValue(FormatDateStr(cdt,"HH"));
                minuteSpinner.setValue(FormatDateStr(cdt,"mm"));
                secondSpinner.setValue(FormatDateStr(cdt,"ss"));
                msecondSpinner.setValue(FormatDateStr(cdt,"l"));
            }
        },
        {
            text: '确定',
            region: "center",
            handler: function () {
                var dv = FormatDateStr(datef.getValue(),"yyyy-MM-dd");
                var hv = String.leftPad(hourSpinner.getValue().toString(), 2, '0');
                var mv = String.leftPad(minuteSpinner.getValue().toString(), 2, '0');
                var sv = String.leftPad(secondSpinner.getValue().toString(), 2, '0');
                var msv = String.leftPad(msecondSpinner.getValue().toString(), 3, '0');
                var dts = dv + " " + hv + ":" + mv + ":" + sv + "." + msv;
                elem.setValue(dts);
                wmWin.close();
            }
        },
        {
            text: '关闭',
            region: "center",
            handler: function () {
                wmWin.close();
            }
        }
        ]
    });
    wmWin.show();
}