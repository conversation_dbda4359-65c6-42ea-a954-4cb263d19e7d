function CMParamQry(ownerStr) {
    var obj = this;
    var start = 0;
    var limit = 20;
    var pageSize = limit;

    var modelRecord = Ext.data.Record.create([{ name: 'modelid', type: 'int' }, { name: 'modelname', type: 'string'}]);
    var modelaData = [[0, 'api接口'], [1, '服务参数']];
    var modelStore = new Ext.data.SimpleStore({
        fields: ['modelid', 'modelname'],
        data: modelaData
    });

    //查询模式选择框
    var ModelCombo = new Ext.form.ComboBox({
        id: ownerStr + '.toolbar.IdQryMode',
        name: 'QRY_MODEL',
        typeAhead: true,
        triggerAction: 'all',
        lazyRender: true,
        mode: 'local',
        valueField: 'modelid',
        displayField: 'modelname',
        allowBlank: false,
        editable: false,
        fieldLabel: '查询模式',
        store: modelStore,
        anchor: '98%',
        listeners: {
            'select': function (c, rec, idx) {
                Ext.getCmp(ownerStr + '.toolbar.IdQryMode').setValue(rec.data.modelid);
            }
        }
    });
    
    var PARAMQ_FormWindow_ttb = new Ext.Toolbar({
        items: [
            '查询模式',
            ModelCombo,
            '-', '->', '-',
            {
                text: '查询',
                iconCls: 'ButtonFormLook',
                handler: function () {
                    var mode = Ext.getCmp(ownerStr + '.toolbar.IdQryMode').getValue();
                    Ext.getCmp(ownerStr + '.toolbar.IdPARAMQ_VIEW').setValue("");

                    Ext.Ajax.request({
                        url: '../../Service/CpSoft/Query/GetParamProx',
                        params:
                        {
                            sMode: mode
                        },
                        waitTitle: "请稍候",
                        waitMsg: '正在获取参数信息...',
                        success: function (response, options) {
                            if (response.responseText == "" || response.responseText == undefined) return;
                            var responseArray = Ext.util.JSON.decode(response.responseText);
                            if (responseArray.success == true&&responseArray.data!=undefined) {
                                Ext.getCmp(ownerStr + '.toolbar.IdPARAMQ_VIEW').setValue(responseArray.data);
                            }
                        }
                    });
                }
            }
        ]
    });

    var PARAMQ_FormWindow_ftb = new Ext.Toolbar({
        height: 40,
        autoHeight: false,
        items: [{
            text: '关闭',
            width: 90,
            height: 30,
            handler: function () {
                PARAMQ_FormWindow.close();
            }
        }]
    });

    var PARAMQ_FormWindow = new Ext.Window({
        id: ownerStr + '.IdPARAMQ_FormWindow',
        height: 720,
        width: 1000,
        closeAction: 'close',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: false,
        items: [{
            id:ownerStr + '.toolbar.IdPARAMQ_VIEW',
            xtype: 'textarea',
            fieldLabel: '操作日志',
            labelWidth: 60,
            preventScrollbars: false,
            autoScroll:true,
            grow: false
        }],
        title: '表单',
        buttonAlign: 'center',
        tbar: PARAMQ_FormWindow_ttb,
        fbar: PARAMQ_FormWindow_ftb
    });

    this.ShowWindow=function() {
        PARAMQ_FormWindow.setTitle(ExtractTitleString("服务运行参数信息..."));
        PARAMQ_FormWindow.show();
    }
}

CMParamQry.prototype = {
    constructor: CMParamQry
}