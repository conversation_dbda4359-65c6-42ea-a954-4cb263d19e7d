function ExecuteReport(rptTitle,rpttId,billId)
{
    Ext.Ajax.request({
        url: "../../Service/CpSoft/GrptS/GenerateReport",
        method: 'POST',
        success: function (res) {
            if (res.responseText == "" || res.responseText == undefined) return;
            var resp = Ext.decode(res.responseText);
            var strNum = resp.text;
            if (resp.success != 'true' && resp.success != true) {
                Ext.MessageBox.alert("操作提示", resp.text);
            }
            if (resp.data1 != '' && resp.data1 != undefined) {
                var uInt8Array = new Uint8Array(resp.data1);
                var rpturl = window.URL.createObjectURL(new Blob([uInt8Array], {
                    type: "application/pdf"
                }));
                ExecuteReportView(rptTitle, rpturl);
            }
        },
        params: {
            rpttid: rpttId,
            billid: billId
        }
    });
}

function ExecuteReportView(rptTitle, rptFile) {
    var GRPTV_FormWindow_tb = new Ext.Toolbar({
        items:
            [
                '-',
                '->',
                '-',
                {
                    text: '打印单据',
                    iconCls: 'ButtonFormLook',
                    handler: function() {
                        var getMyFrame = document.getElementById("grptframe");
                        getMyFrame.focus();
                        getMyFrame.contentWindow.print();
                    }
                },
                {
                    text: '关闭',
                    iconCls: 'ButtonFormDelete',
                    handler: function() {
                        GRPTV_FormWindow.close();
                    }
                }
            ]
    });

    var strurl = rptFile + "#scrollbars=0&toolbar=0";
    var GRPTV_FormWindow = new Ext.Window({
        maximized: true,
        closeAction: 'close',
        plain: true,
        modal: true,
        layout: "fit",
        resizable: false,
        autoScroll: false,
        html: '<iframe id="grptframe" scrolling="auto" frameborder="0" width="100%" height="100%" src="' + strurl + '"></iframe>',
        title: '表单',
        buttonAlign: 'center',
        tbar: GRPTV_FormWindow_tb
    });

    GRPTV_FormWindow.setTitle(ExtractTitleString(rptTitle + "报表预览..."));
    GRPTV_FormWindow.show();
}
