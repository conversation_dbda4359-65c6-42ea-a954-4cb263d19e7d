var _role_str = '1001';
var _uiwmask=true;

var classcodes = [];
window.Import=
{
    LoadJsList:function(_files,succes)
    {      
        var FileArray=[];      
        if(typeof _files==="object")
        {        
            FileArray=_files;      
        }else
        {       
            if(typeof _files==="string")
            {          
                FileArray=_files.split(",");        
            }      
        }      
        
        if(FileArray!=null && FileArray.length>0)
        {        
            var LoadedCount=0;   
            for(var i=0;i< FileArray.length;i++)
            {     
                 var isLoaded=false;       
                 var ss = document.getElementsByTagName("script");
                 for (var j = 0; j < ss.length; j++) {
                    if (ss[j].id && ss[j].id.indexOf(FileArray[i]) != -1) {
                        LoadedCount++; 
                        classcodes.push(FileArray[i]);
                        if(LoadedCount==FileArray.length)
                        {              
                            succes();          
                        }
                        isLoaded=true;
                        break;
                    }
                }
                
                if(isLoaded) continue;
                loadJs(FileArray[i],function()
                {            
                    LoadedCount++;            
                    if(LoadedCount==FileArray.length)
                    {              
                        succes();           
                    }          
                });
            }      
        }      
        
        function loadJs(url, success) 
        {        
            if (!FileIsExt(classcodes,url))
            {          
                var s=document.createElement('script');
                s.id = url;       
                s.type = "text/javascript";
                s.src = url;          
                success = success || function(){};          
                s.onload = s.onreadystatechange = function()
                {            
                    if (!this.readyState || 'loaded' === this.readyState || 'complete' === this.readyState) 
                    {              
                         success();              
                         classcodes.push(url);           
                    }          
                }          
                document.getElementsByTagName('head')[0].appendChild(s);        
            }else
            {          
                success();        
            }
        }

        function loadCss(url, success) {
            if (!FileIsExt(classcodes, url)) {
                var s = document.createElement('link');
                s.id = url;
                s.type = "text/Css";
                s.href = url;
                s.rel = "stylesheet";
                success = success || function () { };
                s.onload = s.onreadystatechange = function () {
                    if (!this.readyState || 'loaded' === this.readyState || 'complete' === this.readyState) {
                        success();
                        classcodes.push(url);
                    }
                }
                document.getElementsByTagName('head')[0].appendChild(s);
            } else {
                success();
            }
        }    
        
        function GetFileType(url)
        {        
            if(url!=null && url.length>0)
            {          
                return url.substr(url.lastIndexOf(".")).toLowerCase();        
            }        
            return "";      
        }   
        
        function FileIsExt(FileArray,_url)
        {                
            if(FileArray!=null && FileArray.length>0)
            {         
                var len =FileArray.length;          
                for (var i = 0; i < len; i++)
                {            
                    if (FileArray[i] ==_url) 
                    {              
                        return true;            
                    }          
                }        
            }        
            return false;      
        }
    }
}

var csscodes = [];
window.ImportCss =
{
    LoadCssList: function (_files, succes) {
        var FileArray = [];
        if (typeof _files === "object") {
            FileArray = _files;
        } else {
            if (typeof _files === "string") {
                FileArray = _files.split(",");
            }
        }

        if (FileArray != null && FileArray.length > 0) {
            var LoadedCount = 0;
            for (var i = 0; i < FileArray.length; i++) {
                var isLoaded = false;
                var ss = document.getElementsByTagName("link");
                for (var j = 0; j < ss.length; j++) {
                    if (ss[j].id && ss[j].id.indexOf(FileArray[i]) != -1) {
                        LoadedCount++;
                        csscodes.push(FileArray[i]);
                        if (LoadedCount == FileArray.length) {
                            succes();
                        }
                        isLoaded = true;
                        break;
                    }
                }

                if (isLoaded) continue;
                loadCss(FileArray[i], function () {
                    LoadedCount++;
                    if (LoadedCount == FileArray.length) {
                        succes();
                    }
                });
            }
        }

        function loadCss(url, success) {
            if (!FileIsExt(csscodes, url)) {
                var s = document.createElement('link');
                s.id = url;
                s.type = "text/Css";
                s.href = url;
                s.rel = "stylesheet";
                success = success || function () { };
                s.onload = s.onreadystatechange = function () {
                    if (!this.readyState || 'loaded' === this.readyState || 'complete' === this.readyState) {
                        success();
                        csscodes.push(url);
                    }
                }
                document.getElementsByTagName('head')[0].appendChild(s);
            } else {
                success();
            }
        }

        function GetFileType(url) {
            if (url != null && url.length > 0) {
                return url.substr(url.lastIndexOf(".")).toLowerCase();
            }
            return "";
        }

        function FileIsExt(FileArray, _url) {
            if (FileArray != null && FileArray.length > 0) {
                var len = FileArray.length;
                for (var i = 0; i < len; i++) {
                    if (FileArray[i] == _url) {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}

function OpenWin(url, iW, iH, scol) {
    var oW = iW ? iW : 800;
    var oH = iH ? iH : 570;
    var wW = window.screen.availWidth;
    var wH = window.screen.availHeight;
    var left = (wW - oW) / 2;
    var top = (wH - oH) / 2;
    var sF = "dependent=yes,resizable=no,toolbar=no,location=no,status=no,directories=no,menubar=no,";
    sF += "scrollbars=" + (scol ? scol : "no") + ",";
    sF += "width=" + oW + ",";
    sF += "height=" + oH + ",";
    sF += "top=" + top + ",";
    sF += "left=" + left;
    window.open(url, "_blank", sF, false);
}

function OpenFullWin(apppath, where, strScrollbars) {
    //*** 弹出窗口的类型
    var h = window.screen.availHeight - 60;
    var w = window.screen.availWidth - 10;
    var ssb = "yes";
    if (arguments.length== 3) {
        ssb = strScrollbars;
    }
    var winType = "toolbar=no,directories=no,status=yes,location=no,resizable=yes,alwaysRaised=yes,dependent=yes,";
    winType += "top=0,left=0,";
    winType += "scrollbars=" + ssb + ",menubar=no,width=" + w + ",";
    winType += "height=" + h;
    var lWhere = "";
    if (arguments.length == 2)
        lWhere = where;
    return window.open(apppath, lWhere, winType);
}

function RefreshIframe(iframe) {
    if (iframe != undefined) {
        var src = iframe.src;
        iframe.src = '';
        iframe.src = src;
    }
}

function ExtractTitleString(pstr) {
    return "<span style='height:40px;font-size:12px;font-weight:bold;padding:10px;border:0px;'>" + pstr + "</span>"
}

function FormatDateStr(pdate, mask)
{
    if(pdate==""||pdate==null||pdate==undefined) return null;
    var d = pdate;

    var zeroize = function (value, length) {
        if (!length) length = 2;
        value = String(value);
        for (var i = 0, zeros = ''; i < (length - value.length); i++) {
            zeros += '0';
        }
        return zeros + value;
    };

    return mask.replace(/"[^"]*"|'[^']*'|\b(?:d{1,4}|m{1,4}|yy(?:yy)?|([hHMstT])\1?|[lLZ])\b/g, function ($0) {
        switch ($0) {
            case 'd': return d.getDate();
            case 'dd': return zeroize(d.getDate());
            case 'ddd': return ['Sun', 'Mon', 'Tue', 'Wed', 'Thr', 'Fri', 'Sat'][d.getDay()];
            case 'dddd': return ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][d.getDay()];
            case 'M': return d.getMonth() + 1;
            case 'MM': return zeroize(d.getMonth() + 1);
            case 'MMM': return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][d.getMonth()];
            case 'MMMM': return ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][d.getMonth()];
            case 'yy': return String(d.getFullYear()).substr(2);
            case 'yyyy': return d.getFullYear();
            case 'h': return d.getHours() % 12 || 12;
            case 'hh': return zeroize(d.getHours() % 12 || 12);
            case 'H': return d.getHours();
            case 'HH': return zeroize(d.getHours());
            case 'm': return d.getMinutes();
            case 'mm': return zeroize(d.getMinutes());
            case 's': return d.getSeconds();
            case 'ss': return zeroize(d.getSeconds());
            case 'l': return zeroize(d.getMilliseconds(), 3);
            case 'L':
                var m = d.getMilliseconds();
                if (m > 99) m = Math.round(m / 10);
                return zeroize(m);
            case 'tt': return d.getHours() < 12 ? 'am' : 'pm';
            case 'TT': return d.getHours() < 12 ? 'AM' : 'PM';
            case 'Z': return d.toUTCString().match(/[A-Z]+$/);
            default: return $0.substr(1, $0.length - 2);
        }
    }); 
 }

function ClearOldGridStore()
{
    if(FrameMainTabContainer.CurrentMainTabId != undefined)
    {
        oPanel = Ext.getCmp(FrameMainTabContainer.CurrentMainTabId);
        if (oPanel != undefined && oPanel.findByType != 0 && oPanel.findByType != undefined)
        {
            oGrid = oPanel.findByType('grid')[0];
            if (oGrid != undefined && oGrid.store != undefined)
            {
                oGrid.store.removeAll();
            }
        }
    }
}

String.prototype.replaceAll = function(s1, s2) {
    return this.replace(new RegExp(s1,"gm"), s2);
}

Ext.Ajax.timeout = 120000;

function _formatFloatDot2(src) {
    var f = parseFloat(src).toFixed(2);
    if (isNaN(f)) { return; }
    f = Math.round(f * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}

function _formatFloatDot3(src) {
    var f = parseFloat(src).toFixed(3);
    if (isNaN(f)) { return; }
    f = Math.round(f * 1000) / 1000;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 3) {
        s += '0';
    }
    return s;
}

function _formatFloatDot4(src) {
    var f = parseFloat(src).toFixed(4);
    if (isNaN(f)) { return; }
    f = Math.round(f * 10000) / 10000;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 4) {
        s += '0';
    }
    return s;
}

function _formatFloatDot6(src) {
    var f = parseFloat(src).toFixed(6);
    if (isNaN(f)) { return; }
    f = Math.round(f * 1000000) / 1000000;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 6) {
        s += '0';
    }
    return s;
}

function GetDockedItem(owner,flag,idx) {
    var obj = undefined;
    if (owner != undefined) {
        obj = owner.getDockedItems('toolbar[dock="'+flag+'"]')[0].items.items[idx];
    }
    return obj;
}

Ext.override(Ext.form.NumberField, {
    setValue: function (v) {
        if (this.allowDecimals) {
            if (this.initFormat != undefined && this.initFormat == true) {
                v = typeof v == 'number' ? v : parseFloat(String(v).replace(this.decimalSeparator, "."));
                v = isNaN(v) ? '' : v.toFixed(this.decimalPrecision).replace(".", this.decimalSeparator);
            }
        }
        return this.setRawValue(v);
    }
});

String.prototype.replaceAll = stringReplaceAll;
function stringReplaceAll(AFindText, ARepText) {
    raRegExp = new RegExp(AFindText, "g");
    return this.replace(raRegExp, ARepText)
}
String.prototype.endWith = function (str) {
    if (str == null || str == "" || this.length == 0 || str.length > this.length)
        return false;
    if (this.substring(this.length - str.length) == str)
        return true;
    else
        return false;
    return true;
}
String.prototype.startWith = function (str) {
    if (str == null || str == "" || this.length == 0 || str.length > this.length)
        return false;
    if (this.substr(0, str.length) == str)
        return true;
    else
        return false;
    return true;
}
String.prototype.gblen = function () {
    var len = 0;
    for (var i = 0; i < this.length; i++) {
        if (this.charCodeAt(i) > 127 || this.charCodeAt(i) == 94) {
            len += 2;
        } else {
            len++;
        }
    }
    return len;
}