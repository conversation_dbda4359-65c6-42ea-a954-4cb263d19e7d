Ext.override(Ext.form.CheckboxGroup, {
    //在inputValue中找到定义的内容后，设置到items里的各个checkbox中　　　   
    setValue: function(value)
    {
        value = value == undefined ? "" : value;
        this.items.each(function(f)
        {
            if (value.indexOf(f.inputValue) != -1)
            {

                f.setValue(true);
            } else
            {

                f.setValue(false);
            }
        });
    },
    //以value1,value2的形式拼接group内的值　　   
    getValue: function()
    {
        var re = "";
        this.items.each(function(f)
        {
            if (f.getValue() == true)
            {
                re += f.inputValue + ",";
            }
        });
        return re.substr(0, re.length - 1);
    }
});

Ext.override(Ext.form.RadioGroup, {
    getValue: function()
    {
        var v;
        if (this.rendered)
        {
            this.items.each(function(item)
            {
                if (!item.getValue())
                    return true;
                v = item.getRawValue();
                return false;
            });
        } else
        {
            for (var k in this.items)
            {
                if (this.items[k].checked)
                {
                    v = this.items[k].inputValue;
                    break;
                }
            }
        }
        return v;
    },
    setValue: function(v)
    {

        if (this.rendered)
            this.items.each(function(item)
            {
                item.setValue(item.getRawValue() == v);
            });
        else
        {
            for (var k in this.items)
            {
                this.items[k].checked = this.items[k].inputValue == v;
            }
        }
    }

});  