package com.soft.gcc.xtbg.dfdwznyy.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.dfdwznyy.base.Result;
import com.soft.gcc.xtbg.dfdwznyy.entity.SealYylc;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_ZNYY_SEAL_YYLC(用印流程关联印章信息)】的数据库操作Service
* @createDate 2023-07-17 11:00:44
*/
public interface ISealYylcService extends IService<SealYylc> {

    Result postYXYG03_01(String orderNum, int ywid, String bizID, String uuid, int yyCount);

    Result postYXYG03_03(String orderNum,int ywid,String bizID,String uuid);

    List<SealYylc> getSealYYLCList(Integer lcId);

    /**
     * 智能用印系统添加用户
     * @param znyyId
     * @param znyyName
     * @return
     */
    Result addZNYYPerson(String znyyId, String znyyName);

    /**
     * 锁定或解锁印章
     */
    Result postYXYG02_08(boolean lock,String bizID,String uuid);

    /**
     * 向设备中录入、删除或清空用户的指纹信息
     * @param uuid  设备标识
     * @param znyyUserId  智能用印userId
     * @param mode	指令类型	0:录入 1:删除 2:清空
     * @return 接口响应
     */
    Result postYXYG02_07(String uuid,String znyyUserId,int mode);

    /**
     * 查询设备已录入的指纹列表  YXYG04_05
     * @param pageNum       查询页码
     * @param pageSize      每页条数
     * @param uuid          设备标识
     * @return   接口响应
     */
    Result postYXYG04_05(int pageNum,int pageSize,String uuid,String userName);

    /**
     *  设置印章设备断开或连接WiFi
     * @param uuid          设备标识
     * @param ssid          网络ssid
     * @param pass          网络秘钥
     * @param connect       连接/断开 true:连接 false:断开
     * @return   接口响应
     */
    Result postYXYG02_05(String uuid,String ssid,String pass,boolean connect);

    /**
     *  开启印章物理锁，给印章设备安装实体章头或更换油墨时使用
     * @param uuid          设备标识
     * @return   接口响应
     */
    Result postYXYG02_04(String uuid);


    /**
     * 是否超时
     * @param nowTime 当前时间
     * @param qrsj    确认时间
     * @param hourStr 多少个小时超时
     * @return true 超时
     * @throws Exception
     */
    boolean isExceedTime(Date nowTime, LocalDateTime qrsj, String hourStr) throws Exception;

    /**
     * 是否超时
     * @param nowTime 当前时间
     * @param qrsj    确认时间
     * @param hourStr 多少个小时超时
     * @return true 超时
     * @throws Exception
     */
    boolean isExceedTimeToStr(Date nowTime, String qrsj, String hourStr) throws Exception;

    /**
     * 用印初始化时间为字符串
     * @param yytime 时间
     * @param ShangwuOrXiawu 上午或者下午
     * @return yyyy-MM-dd HH:mm:ss 时分秒根据上午或者下午来 上午12:00:00 下午 23:59:59
     */
    String yytime2String(Date yytime,String ShangwuOrXiawu) throws Exception;

}
