package com.soft.gcc.xtbg.dfdwznyy.entity.dto;

import com.soft.gcc.xtbg.dfdwznyy.entity.Fingerprint;
import lombok.Data;

/**
 * <AUTHOR>
 * 指纹数据 dto
 */
@Data
public class FingerprintDto extends Fingerprint {

    /**
     * 查询页码	默认第1页
     */
    private Integer current;

    /**
     * 每页条数 默认10
     */
    private Integer size;

    /**
     * 查询参数
     */
    private String CID;

    /**
     * 智能用印userid，直接用真名
     */
    private String znyyUserId;

    /**
     * 设备标识
     */
    private String uuid;

    /**
     * 指令类型	0:录入 1:删除 2:清空
     */
    private Integer mode;
}
