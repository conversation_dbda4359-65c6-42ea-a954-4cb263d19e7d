package com.soft.gcc.xtbg.dfdwznyy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.soft.gcc.xtbg.dfdwznyy.base.Result;
import com.soft.gcc.xtbg.dfdwznyy.entity.DfdwTZnyyYyjl;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.RequestJsonBase;
import com.soft.gcc.xtbg.dfdwznyy.entity.param.ZnyyYyjlParam;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_ZNYY_YYJL(用印记录)】的数据库操作Service
* @createDate 2023-07-17 10:06:32
*/
public interface DfdwTZnyyYyjlService extends IService<DfdwTZnyyYyjl> {

    void saveJson(RequestJsonBase json);

    Result getListYYJL(ZnyyYyjlParam yyjl);

    Result getUsageRecord(ZnyyYyjlParam yyjl);
}
