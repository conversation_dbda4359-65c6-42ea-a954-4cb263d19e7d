<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dfdwznyy.mapper.PersonDFDWMapper">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WZ_UserID, type, Sphone, PhName, Telephone, Id, Password, ZJ_CS, LoginName, GroupID, MsgType, OA, RealName, RoleId
    </sql>

    <select id="getUserRoleName" resultType="java.lang.String">
        SELECT r.RoleName FROM RolePerson rp LEFT JOIN Role r on r.id = rp.RoleId where rp.PersonId = #{personId}
    </select>




</mapper>
