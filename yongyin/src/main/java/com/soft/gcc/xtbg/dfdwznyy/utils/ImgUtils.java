package com.soft.gcc.xtbg.dfdwznyy.utils;

import com.soft.framework.helper.OSSHelper;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.Arrays;

/**
 * <AUTHOR>
 * OSS存储时间过长（超过1秒），接口会出现频繁调用，所以使用异步
 * 示例：
 * ‘@Autowired
 *  ImgUtils imgUtils;
 *  imgUtils.downloadImagAndUploadToOss(url, localPath);’
 */
@Service
public class ImgUtils {

    /**
     * 将图片下载下来后，上传到OSS
     *
     * @param url          url路径
     * @param downloadPath oss地址
     * @return
     * @throws Exception 抛出异常
     * <AUTHOR>
     */
    @Async
    public void downloadImagAndUploadToOss(@NotNull String url, String downloadPath) throws Exception {
        String imgName = Arrays.stream(url.split("/"))
                .reduce((first, second) -> second)
                .orElse(null);
        downloadPicture(url, downloadPath, imgName);

        String ossPath = "Upload/FileManage/znyy/";
        File file = new File(downloadPath + imgName);
        AliyunOSSUtils.uploadFile(ossPath, imgName, file);
    }

    /**
     * 传入要下载的图片的url列表，将url所对应的图片下载到本地
     *
     * @param urlString url路径
     * @param path      本地路径
     * @param imgName   文件名
     * @throws Exception 抛出异常
     */

    private static void downloadPicture(String urlString, String path, String imgName) throws Exception {
        if (StringUtils.isEmpty(urlString)) {
            return;
        }

        URL url = null;
        FileOutputStream fileOutputStream = null;
        InputStream inputStream = null;

        try {
            url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:55.0) Gecko/20100101 Firefox/55.0");
            connection.setConnectTimeout(10 * 1000);
            connection.setReadTimeout(15 * 1000);
            inputStream = connection.getInputStream();
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            }

            byte[] buffer = new byte[1024];
            int length;
            fileOutputStream = new FileOutputStream(path + imgName);
            while ((length = inputStream.read(buffer)) != -1) {

                fileOutputStream.write(buffer, 0, length);

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
            if (fileOutputStream != null) {
                fileOutputStream.flush();
                fileOutputStream.close();
            }
        }
    }

    public static void main(String[] args) throws Exception {
        String localPath = "C:/inetpub/yy/szyy/znyy/yyjl/";
        String url = "https://test.qstamper.com:8036/group1/traffic/2023/7/12/-57058-63-0-0-0-1689142284684.jpg";
    }
}
