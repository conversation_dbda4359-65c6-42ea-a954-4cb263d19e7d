<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dfdwznyy.mapper.DfdwTZnyyYyjlMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dfdwznyy.entity.DfdwTZnyyYyjl">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="tenant" column="tenant" jdbcType="VARCHAR"/>
            <result property="deptId" column="deptId" jdbcType="INTEGER"/>
            <result property="deptName" column="deptName" jdbcType="VARCHAR"/>
            <result property="useTime" column="useTime" jdbcType="DATE"/>
            <result property="userId" column="userId" jdbcType="INTEGER"/>
            <result property="userName" column="userName" jdbcType="VARCHAR"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="deviceName" column="deviceName" jdbcType="VARCHAR"/>
            <result property="applicationId" column="applicationId" jdbcType="VARCHAR"/>
            <result property="useCount" column="useCount" jdbcType="INTEGER"/>
            <result property="error" column="error" jdbcType="INTEGER"/>
            <result property="errorDesc" column="errorDesc" jdbcType="VARCHAR"/>
            <result property="model" column="model" jdbcType="VARCHAR"/>
            <result property="camera" column="camera" jdbcType="BIT"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant,deptId,
        deptName,useTime,userId,
        userName,uuid,deviceName,
        applicationId,useCount,error,
        errorDesc,model,camera,
        location
    </sql>
    <insert id="saveBatchTFile">
        insert into T_File (FunctionID, FilePath, FileName, ProjectID, hjID, Type, UploadDate, SubTName)
        values
        <foreach collection="tFiles" item="item" index="index" separator=",">
            (#{item.FunctionID}, #{item.FilePath},#{item.FileName}, #{item.ProjectID},#{item.hjID},#{item.Type},#{item.UploadDate},#{item.SubTName})
        </foreach>
    </insert>
    <select id="getFileList" resultType="com.soft.gcc.xtbg.dfdwznyy.entity.dto.TFileDto">
        select a.* from (
        select tf.FileName, tf.FilePath, yyjl.useTime, yyjl.location, yyjl.useCount, tf.hjID from T_File tf
        left join DFDW_T_ZNYY_YYJL yyjl on tf.ProjectID = yyjl.id
        where tf.FunctionID = 910206 and tf.Type = 'znyy_yyjl'
        <if test="yyjl.hjID != null and yyjl.hjID != ''">
            and tf.hjID = #{yyjl.hjID}
        </if>
        <if test="yyjl.uuid != null and yyjl.uuid != ''">
            and yyjl.uuid = #{yyjl.uuid}
        </if>
        <if test="yyjl.applicationId != null">
            and yyjl.applicationId = #{yyjl.applicationId}
        </if>
        ) a
        order by a.useCount desc, a.hjID desc
    </select>
</mapper>
