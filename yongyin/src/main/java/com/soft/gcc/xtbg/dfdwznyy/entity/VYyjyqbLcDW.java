package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.soft.gcc.xtbg.yygl.entity.VYyjyqbLc;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 */
@TableName("V_Yyjyqb_LC")
@Data
public class VYyjyqbLcDW implements Serializable {
    /**
     *
     */
    @TableField(value = "id")
    private Integer id;

    /**
     *
     */
    @TableField(exist = false)
    private String wjDate;

    /**
     *
     */
    @TableField(value = "Qblx")
    private String qblx;

    /**
     *
     */
    @TableField(value = "Time")
    private Date time;

    /**
     *
     */
    @TableField(value = "Applicant")
    private String applicant;

    /**
     *
     */
    @TableField(value = "Yysy")
    private String yysy;

    /**
     *
     */
    @TableField(value = "Yzfl")
    private String yzfl;

    /**
     *
     */
    @TableField(value = "Sqfzr")
    private String sqfzr;

    /**
     *
     */
    @TableField(value = "Fzryj")
    private String fzryj;

    /**
     *
     */
    @TableField(value = "Fzrqrsj")
    private String fzrqrsj;

    /**
     *
     */
    @TableField(value = "Zhglbyj")
    private String zhglbyj;

    /**
     *
     */
    @TableField(value = "Zhglbqrsj")
    private String zhglbqrsj;

    /**
     *
     */
    @TableField(value = "name")
    private String name;

    /**
     *
     */
    @TableField(value = "groupname")
    private String groupname;

    /**
     *
     */
    @TableField(value = "zhglbfzr")
    private String zhglbfzr;

    /**
     *
     */
    @TableField(value = "phone")
    private String phone;

    /**
     *
     */
    @TableField(value = "bh")
    private String bh;

    /**
     *
     */
    @TableField(value = "pripority")
    private String pripority;

    /**
     *
     */
    @TableField(value = "type")
    private String type;

    /**
     *
     */
    @TableField(value = "yyzztype")
    private String yyzztype;

    /**
     *
     */
    @TableField(value = "yyzzbh")
    private String yyzzbh;

    /**
     *
     */
    @TableField(value = "yySend")
    private String yysend;

    /**
     *
     */
    @TableField(value = "yyCount")
    private Integer yycount;

    /**
     *
     */
    @TableField(value = "jyStartDate")
    private Date jystartdate;

    /**
     *
     */
    @TableField(value = "jyEdnDate")
    private Date jyedndate;

    /**
     *
     */
    @TableField(value = "sDateQuantum")
    private String sdatequantum;

    /**
     *
     */
    @TableField(value = "eDateQuantum")
    private String edatequantum;

    /**
     *
     */
    @TableField(value = "yySendGroupID")
    private Integer yysendgroupid;

    /**
     *
     */
    @TableField(value = "TopGroupID")
    private Integer topgroupid;

    /**
     *
     */
    @TableField(value = "GroupID")
    private Integer groupid;

    /**
     *
     */
    @TableField(value = "zgsleader")
    private String zgsleader;

    /**
     *
     */
    @TableField(value = "zgsleaderYj")
    private String zgsleaderyj;

    /**
     *
     */
    @TableField(value = "zgsleaderSj")
    private String zgsleadersj;

    /**
     *
     */
    @TableField(value = "UserID")
    private Integer userid;

    /**
     *
     */
    @TableField(value = "Lc_defineID")
    private Integer lcDefineid;

    /**
     *
     */
    @TableField(value = "Lc_Name")
    private String lcName;

    /**
     *
     */
    @TableField(value = "ywID")
    private Integer ywid;

    /**
     *
     */
    @TableField(value = "sendPerson")
    private String sendperson;

    /**
     *
     */
    @TableField(value = "sendPersonZgh")
    private String sendpersonzgh;

    /**
     *
     */
    @TableField(value = "AllPersonZgh")
    private String allpersonzgh;

    /**
     *
     */
    @TableField(value = "isMany")
    private Integer ismany;

    /**
     *
     */
    @TableField(value = "lc_jdmc")
    private String lcJdmc;

    /**
     *
     */
    @TableField(value = "lc_jdid")
    private Integer lcJdid;

    /**
     *
     */
    @TableField(value = "lc_isback")
    private Integer lcIsback;

    /**
     *
     */
    @TableField(value = "lc_tojdid")
    private String lcTojdid;

    /**
     *
     */
    @TableField(value = "number")
    private Integer number;

    /**
     *
     */
    @TableField(value = "BXType")
    private String bxtype;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        VYyjyqbLc other = (VYyjyqbLc) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getQblx() == null ? other.getQblx() == null : this.getQblx().equals(other.getQblx()))
                && (this.getTime() == null ? other.getTime() == null : this.getTime().equals(other.getTime()))
                && (this.getApplicant() == null ? other.getApplicant() == null : this.getApplicant().equals(other.getApplicant()))
                && (this.getYysy() == null ? other.getYysy() == null : this.getYysy().equals(other.getYysy()))
                && (this.getYzfl() == null ? other.getYzfl() == null : this.getYzfl().equals(other.getYzfl()))
                && (this.getSqfzr() == null ? other.getSqfzr() == null : this.getSqfzr().equals(other.getSqfzr()))
                && (this.getFzryj() == null ? other.getFzryj() == null : this.getFzryj().equals(other.getFzryj()))
                && (this.getFzrqrsj() == null ? other.getFzrqrsj() == null : this.getFzrqrsj().equals(other.getFzrqrsj()))
                && (this.getZhglbyj() == null ? other.getZhglbyj() == null : this.getZhglbyj().equals(other.getZhglbyj()))
                && (this.getZhglbqrsj() == null ? other.getZhglbqrsj() == null : this.getZhglbqrsj().equals(other.getZhglbqrsj()))
                && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
                && (this.getGroupname() == null ? other.getGroupname() == null : this.getGroupname().equals(other.getGroupname()))
                && (this.getZhglbfzr() == null ? other.getZhglbfzr() == null : this.getZhglbfzr().equals(other.getZhglbfzr()))
                && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
                && (this.getBh() == null ? other.getBh() == null : this.getBh().equals(other.getBh()))
                && (this.getPripority() == null ? other.getPripority() == null : this.getPripority().equals(other.getPripority()))
                && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
                && (this.getYyzztype() == null ? other.getYyzztype() == null : this.getYyzztype().equals(other.getYyzztype()))
                && (this.getYyzzbh() == null ? other.getYyzzbh() == null : this.getYyzzbh().equals(other.getYyzzbh()))
                && (this.getYysend() == null ? other.getYysend() == null : this.getYysend().equals(other.getYysend()))
                && (this.getYycount() == null ? other.getYycount() == null : this.getYycount().equals(other.getYycount()))
                && (this.getJystartdate() == null ? other.getJystartdate() == null : this.getJystartdate().equals(other.getJystartdate()))
                && (this.getJyedndate() == null ? other.getJyedndate() == null : this.getJyedndate().equals(other.getJyedndate()))
                && (this.getSdatequantum() == null ? other.getSdatequantum() == null : this.getSdatequantum().equals(other.getSdatequantum()))
                && (this.getEdatequantum() == null ? other.getEdatequantum() == null : this.getEdatequantum().equals(other.getEdatequantum()))
                && (this.getYysendgroupid() == null ? other.getYysendgroupid() == null : this.getYysendgroupid().equals(other.getYysendgroupid()))
                && (this.getTopgroupid() == null ? other.getTopgroupid() == null : this.getTopgroupid().equals(other.getTopgroupid()))
                && (this.getGroupid() == null ? other.getGroupid() == null : this.getGroupid().equals(other.getGroupid()))
                && (this.getZgsleader() == null ? other.getZgsleader() == null : this.getZgsleader().equals(other.getZgsleader()))
                && (this.getZgsleaderyj() == null ? other.getZgsleaderyj() == null : this.getZgsleaderyj().equals(other.getZgsleaderyj()))
                && (this.getZgsleadersj() == null ? other.getZgsleadersj() == null : this.getZgsleadersj().equals(other.getZgsleadersj()))
                && (this.getUserid() == null ? other.getUserid() == null : this.getUserid().equals(other.getUserid()))
                && (this.getLcDefineid() == null ? other.getLcDefineid() == null : this.getLcDefineid().equals(other.getLcDefineid()))
                && (this.getLcName() == null ? other.getLcName() == null : this.getLcName().equals(other.getLcName()))
                && (this.getYwid() == null ? other.getYwid() == null : this.getYwid().equals(other.getYwid()))
                && (this.getSendperson() == null ? other.getSendperson() == null : this.getSendperson().equals(other.getSendperson()))
                && (this.getSendpersonzgh() == null ? other.getSendpersonzgh() == null : this.getSendpersonzgh().equals(other.getSendpersonzgh()))
                && (this.getAllpersonzgh() == null ? other.getAllpersonzgh() == null : this.getAllpersonzgh().equals(other.getAllpersonzgh()))
                && (this.getIsmany() == null ? other.getIsmany() == null : this.getIsmany().equals(other.getIsmany()))
                && (this.getLcJdmc() == null ? other.getLcJdmc() == null : this.getLcJdmc().equals(other.getLcJdmc()))
                && (this.getLcJdid() == null ? other.getLcJdid() == null : this.getLcJdid().equals(other.getLcJdid()))
                && (this.getLcIsback() == null ? other.getLcIsback() == null : this.getLcIsback().equals(other.getLcIsback()))
                && (this.getLcTojdid() == null ? other.getLcTojdid() == null : this.getLcTojdid().equals(other.getLcTojdid()))
                && (this.getNumber() == null ? other.getNumber() == null : this.getNumber().equals(other.getNumber()))
                && (this.getBxtype() == null ? other.getBxtype() == null : this.getBxtype().equals(other.getBxtype()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getQblx() == null) ? 0 : getQblx().hashCode());
        result = prime * result + ((getTime() == null) ? 0 : getTime().hashCode());
        result = prime * result + ((getApplicant() == null) ? 0 : getApplicant().hashCode());
        result = prime * result + ((getYysy() == null) ? 0 : getYysy().hashCode());
        result = prime * result + ((getYzfl() == null) ? 0 : getYzfl().hashCode());
        result = prime * result + ((getSqfzr() == null) ? 0 : getSqfzr().hashCode());
        result = prime * result + ((getFzryj() == null) ? 0 : getFzryj().hashCode());
        result = prime * result + ((getFzrqrsj() == null) ? 0 : getFzrqrsj().hashCode());
        result = prime * result + ((getZhglbyj() == null) ? 0 : getZhglbyj().hashCode());
        result = prime * result + ((getZhglbqrsj() == null) ? 0 : getZhglbqrsj().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getGroupname() == null) ? 0 : getGroupname().hashCode());
        result = prime * result + ((getZhglbfzr() == null) ? 0 : getZhglbfzr().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getBh() == null) ? 0 : getBh().hashCode());
        result = prime * result + ((getPripority() == null) ? 0 : getPripority().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getYyzztype() == null) ? 0 : getYyzztype().hashCode());
        result = prime * result + ((getYyzzbh() == null) ? 0 : getYyzzbh().hashCode());
        result = prime * result + ((getYysend() == null) ? 0 : getYysend().hashCode());
        result = prime * result + ((getYycount() == null) ? 0 : getYycount().hashCode());
        result = prime * result + ((getJystartdate() == null) ? 0 : getJystartdate().hashCode());
        result = prime * result + ((getJyedndate() == null) ? 0 : getJyedndate().hashCode());
        result = prime * result + ((getSdatequantum() == null) ? 0 : getSdatequantum().hashCode());
        result = prime * result + ((getEdatequantum() == null) ? 0 : getEdatequantum().hashCode());
        result = prime * result + ((getYysendgroupid() == null) ? 0 : getYysendgroupid().hashCode());
        result = prime * result + ((getTopgroupid() == null) ? 0 : getTopgroupid().hashCode());
        result = prime * result + ((getGroupid() == null) ? 0 : getGroupid().hashCode());
        result = prime * result + ((getZgsleader() == null) ? 0 : getZgsleader().hashCode());
        result = prime * result + ((getZgsleaderyj() == null) ? 0 : getZgsleaderyj().hashCode());
        result = prime * result + ((getZgsleadersj() == null) ? 0 : getZgsleadersj().hashCode());
        result = prime * result + ((getUserid() == null) ? 0 : getUserid().hashCode());
        result = prime * result + ((getLcDefineid() == null) ? 0 : getLcDefineid().hashCode());
        result = prime * result + ((getLcName() == null) ? 0 : getLcName().hashCode());
        result = prime * result + ((getYwid() == null) ? 0 : getYwid().hashCode());
        result = prime * result + ((getSendperson() == null) ? 0 : getSendperson().hashCode());
        result = prime * result + ((getSendpersonzgh() == null) ? 0 : getSendpersonzgh().hashCode());
        result = prime * result + ((getAllpersonzgh() == null) ? 0 : getAllpersonzgh().hashCode());
        result = prime * result + ((getIsmany() == null) ? 0 : getIsmany().hashCode());
        result = prime * result + ((getLcJdmc() == null) ? 0 : getLcJdmc().hashCode());
        result = prime * result + ((getLcJdid() == null) ? 0 : getLcJdid().hashCode());
        result = prime * result + ((getLcIsback() == null) ? 0 : getLcIsback().hashCode());
        result = prime * result + ((getLcTojdid() == null) ? 0 : getLcTojdid().hashCode());
        result = prime * result + ((getNumber() == null) ? 0 : getNumber().hashCode());
        result = prime * result + ((getBxtype() == null) ? 0 : getBxtype().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", qblx=").append(qblx);
        sb.append(", time=").append(time);
        sb.append(", applicant=").append(applicant);
        sb.append(", yysy=").append(yysy);
        sb.append(", yzfl=").append(yzfl);
        sb.append(", sqfzr=").append(sqfzr);
        sb.append(", fzryj=").append(fzryj);
        sb.append(", fzrqrsj=").append(fzrqrsj);
        sb.append(", zhglbyj=").append(zhglbyj);
        sb.append(", zhglbqrsj=").append(zhglbqrsj);
        sb.append(", name=").append(name);
        sb.append(", groupname=").append(groupname);
        sb.append(", zhglbfzr=").append(zhglbfzr);
        sb.append(", phone=").append(phone);
        sb.append(", bh=").append(bh);
        sb.append(", pripority=").append(pripority);
        sb.append(", type=").append(type);
        sb.append(", yyzztype=").append(yyzztype);
        sb.append(", yyzzbh=").append(yyzzbh);
        sb.append(", yysend=").append(yysend);
        sb.append(", yycount=").append(yycount);
        sb.append(", jystartdate=").append(jystartdate);
        sb.append(", jyedndate=").append(jyedndate);
        sb.append(", sdatequantum=").append(sdatequantum);
        sb.append(", edatequantum=").append(edatequantum);
        sb.append(", yysendgroupid=").append(yysendgroupid);
        sb.append(", topgroupid=").append(topgroupid);
        sb.append(", groupid=").append(groupid);
        sb.append(", zgsleader=").append(zgsleader);
        sb.append(", zgsleaderyj=").append(zgsleaderyj);
        sb.append(", zgsleadersj=").append(zgsleadersj);
        sb.append(", userid=").append(userid);
        sb.append(", lcDefineid=").append(lcDefineid);
        sb.append(", lcName=").append(lcName);
        sb.append(", ywid=").append(ywid);
        sb.append(", sendperson=").append(sendperson);
        sb.append(", sendpersonzgh=").append(sendpersonzgh);
        sb.append(", allpersonzgh=").append(allpersonzgh);
        sb.append(", ismany=").append(ismany);
        sb.append(", lcJdmc=").append(lcJdmc);
        sb.append(", lcJdid=").append(lcJdid);
        sb.append(", lcIsback=").append(lcIsback);
        sb.append(", lcTojdid=").append(lcTojdid);
        sb.append(", number=").append(number);
        sb.append(", bxtype=").append(bxtype);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
