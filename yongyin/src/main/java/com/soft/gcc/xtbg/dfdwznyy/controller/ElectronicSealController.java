package com.soft.gcc.xtbg.dfdwznyy.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.helper.SessionHelper;
import com.soft.gcc.common.groupitem.entity.Groupitem;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import com.soft.gcc.xtbg.dfdwznyy.base.BaseController;
import com.soft.gcc.xtbg.dfdwznyy.base.Result;
import com.soft.gcc.xtbg.dfdwznyy.constants.Constant;
import com.soft.gcc.xtbg.dfdwznyy.entity.*;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.FingerprintDto;
import com.soft.gcc.xtbg.dfdwznyy.entity.param.SearchParam;
import com.soft.gcc.xtbg.dfdwznyy.entity.param.ZnyyYyjlParam;
import com.soft.gcc.xtbg.dfdwznyy.service.*;
import com.soft.gcc.xtbg.yygl.entity.VYyjyqbLc;
import com.soft.gcc.xtbg.yygl.service.VYyjyqbLcService;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Controller
@RequestMapping({"/znyy/yylc/device"})
public class ElectronicSealController  extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ElectronicSealController.class);

    @Autowired
    private VYyjyqbLcDWService vYyjyqbLcDWService;

    @Autowired
    private IExtendYylcService extendYylcService;

    @Autowired
    private ISealService sealService;

    @Autowired
    private ISealYylcService sealYylcService;

    @Autowired
    private IDFDWPersonService personService;

    @Autowired
    private IEmpowerLogService empowerLogService;

    @Autowired
    private DfdwTZnyyYyjlService dfdwTZnyyYyjlService;

    @Autowired
    private DictionaryValueService dictionaryValueService;

    @Autowired
    private GroupitemService groupitemService;

    @Autowired
    private VYyjyqbLcService vYyjyqbLcService;

    private PersonEntity person;

    private void initPerson() {
        person = SessionHelper.getSessionPerson();
        if (person == null) {
            throw new RuntimeException("登录异常");
        }
    }

    private <T> Page<T> getPage() {
        initPerson();
        Integer size = ServletUtils.getParameterToInt("size");
        Integer current = ServletUtils.getParameterToInt("current");
        Page<T> page = new Page<>();
        page.setCurrent(current);
        page.setSize(size);
        return page;
    }

    @RequestMapping("/getSealYYLCList")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "获取用印列表，ElectronicSealController->getSealYYLCList")
    public Result getSealYYLCList(@RequestParam(value = "id", required = false) Integer lcId) {

        List<SealYylc> sealYylcs = sealYylcService.getSealYYLCList(lcId);

        //如果没有就进行初始化
        if (sealYylcs == null || sealYylcs.size() == 0) {
            createApply(lcId);
            sealYylcs = sealYylcService.getSealYYLCList(lcId);
        }

        List<String> uuidSet = new ArrayList<>();
        List<Integer> delIds = new ArrayList<>();
        List<SealYylc> removeSealYylc = new ArrayList<>();

        for (SealYylc sealYylc : sealYylcs) {
            if (uuidSet.stream().anyMatch(i -> i.equals(sealYylc.getDevice_uuid()))) {
                delIds.add(sealYylc.getId());
                removeSealYylc.add(sealYylc);
            }else{
                uuidSet.add(sealYylc.getDevice_uuid());
            }
        }

        if (delIds != null && delIds.size() > 0) {
            sealYylcs.removeAll(removeSealYylc);
            sealYylcService.remove(new LambdaQueryWrapper<SealYylc>().in(SealYylc::getId,delIds));
        }

        //获取申请单合集
        List<String> orderNums = sealYylcs.stream().map(m -> m.getOrder_num()).collect(Collectors.toList());

        Map<String,List<DfdwTZnyyYyjl>> yyjlMap = new HashMap<>();

        if(orderNums != null && orderNums.size() != 0){

            //获取用印使用记录
            List<DfdwTZnyyYyjl> znyyJLList = dfdwTZnyyYyjlService.list(new LambdaQueryWrapper<DfdwTZnyyYyjl>().in(DfdwTZnyyYyjl::getApplicationId,orderNums));

            //根据申请单分组
            yyjlMap = znyyJLList.stream().sorted(Comparator.comparing(DfdwTZnyyYyjl::getUseCount).reversed())
                    .collect(Collectors.groupingBy(DfdwTZnyyYyjl::getApplicationId));

        }

        Person person = user();

        List<String> roleName = personService.getUserRoleName(person.getId());

        for (SealYylc sealYylc : sealYylcs) {

            List<DfdwTZnyyYyjl> yyjls = yyjlMap.get(sealYylc.getOrder_num());
            if (yyjls != null && yyjls.size() != 0){
                sealYylc.setLastUseTime(yyjls.get(0).getUseTime());
                sealYylc.setUse_count(yyjls.size());
            }

            //判断是否是保管人,控制按钮显示
            if (roleName.stream().anyMatch(i -> i.contains("印章保管人"))) {
                //控制按钮显示
                sealYylc.setEmpowerView(1);
                sealYylc.setOpenLockView(1);
            } else {
                //控制按钮显示
                if (sealYylc.getEmpower_type() == 1) {

                    sealYylc.setEmpowerView(0);
                    sealYylc.setOpenLockView(1);

                }else{
                    sealYylc.setEmpowerView(0);
                    sealYylc.setOpenLockView(0);
                }
            }
        }

        return Result.ok(sealYylcs);

    }

    @RequestMapping("/getEmpowerLogList")
    @ResponseBody
//    @Log(module = "用印管理",function = "",detail = "获取印章授权记录ElectronicSealController->getEmpowerLogList")
    public Result getEmpowerLogList(@RequestParam(value = "id", required = false) Integer sealYYLCId) {

        SealYylc sealYylcs = sealYylcService.getById(sealYYLCId);

        List<EmpowerLog> empowerLogs = empowerLogService.list(new LambdaQueryWrapper<EmpowerLog>()
                .eq(EmpowerLog::getSealYYLCId,sealYylcs.getId()));

        return Result.ok(empowerLogs);

    }

    public Result createApply(@RequestParam(value = "id", required = false) Integer id) {

        VYyjyqbLcDW vYyjyqbLcDW = vYyjyqbLcDWService.getById(id);

        if (!"用印申请".equals(vYyjyqbLcDW.getType()) && !"公章外借".equals(vYyjyqbLcDW.getType())) {
            return Result.error("只有用印申请、公章外借才能申请智能用印");
        }

        //参数初始化
        Date nowDate = new Date();
        String exceedStr = "24";
        //是否超时
        boolean isExceed = false;

        List<DictionaryValue> dictionaryValues = dictionaryValueService.list(new LambdaQueryWrapper<DictionaryValue>()
                .eq(DictionaryValue::getTitleID,970234).eq(DictionaryValue::getContent,"倒计时小时数"));

        if (dictionaryValues != null && dictionaryValues.size() != 0) {
            exceedStr = dictionaryValues.get(0).getParameter();
        }

        try {
            if ("用印申请".equals(vYyjyqbLcDW.getType())) {

                LocalDateTime qrsjDate = getCreateTime(vYyjyqbLcDW);

                //是否超过时间
                boolean qrsjBool = sealYylcService.isExceedTime(nowDate,qrsjDate,exceedStr);

                if (qrsjBool){
                    isExceed = true;
                }else{
                    isExceed = false;
                }

            } else if ("公章外借".equals(vYyjyqbLcDW.getType())){
                //外借初始化100次
                vYyjyqbLcDW.setYycount(100);
                String jssj = sealYylcService.yytime2String(vYyjyqbLcDW.getJyedndate(),vYyjyqbLcDW.getEdatequantum());
                exceedStr = "0";
                //是否超过时间
                isExceed = sealYylcService.isExceedTimeToStr(nowDate,jssj,exceedStr);
            }

        } catch (Exception e){
            log.error("用印管理,提交申请单，ElectronicSealController->createApply.初始化结束时间失败"+e.getMessage());
            return Result.error("初始化结束时间失败");
        }

        if (isExceed){
            return Result.error("超时无法操作");
        }

        if ("完成".equals(vYyjyqbLcDW.getLcJdmc())){

            //多个章用 、 分隔，单个章就不分隔
            String yzfl = vYyjyqbLcDW.getYzfl();

            String[] yzArr = yzfl.split("、");

            //一个申请可能会有多个 扩展和印章数据
            ExtendYylc extendYylc = new ExtendYylc(id, LocalDateTime.now());
            List<SealYylc> sealYylcs = new ArrayList<>();

            List<Seal> seals = sealService.list(new LambdaQueryWrapper<Seal>().eq(Seal::getTopGroupId,vYyjyqbLcDW.getYysendgroupid()).in(Seal::getSealName,yzArr));

            for (Seal seal : seals) {

                SealYylc sealYylc = new SealYylc(id,seal.getSealName(),seal.getId()
                        ,id+"_"+new Date().getTime(),0,0,UUID.randomUUID().toString()
                        ,seal.getDeviceUuid(),vYyjyqbLcDW.getYycount());

                Result result = sealYylcService.postYXYG03_01(sealYylc.getOrder_num(),sealYylc.getYylc_id(),sealYylc.getBiz_id()
                        ,seal.getDeviceUuid(),vYyjyqbLcDW.getYycount());
//                Result result = Result.ok();
                //没成功标记失败
                if (Constant.SC_OK_200.equals(result.getCode())) {
                    sealYylc.setApply_type(1);
                    sealYylc.setOrder_type(1);
                } else {
                    sealYylc.setApply_type(0);
                    sealYylc.setOrder_type(0);
                }

                sealYylc.setEmpower_type(0);

                sealYylcs.add(sealYylc);

            }

            //接口回调之后先检查有没有存数据
            List<SealYylc> sealYylcsCheck = sealYylcService.getSealYYLCList(id);

            //如果没有就进行初始化
            if (sealYylcsCheck == null || sealYylcsCheck.size() == 0) {
                for (SealYylc sealYylc : sealYylcs){
                    sealYylcService.save(sealYylc);
                }
            }

            extendYylcService.save(extendYylc);

        }

        return Result.ok("操作成功");

    }

    @RequestMapping("/openDevice")
    @ResponseBody
//    @Log(module = "用印管理",function = "",detail = "开启印章，ElectronicSealController->createApply")
    public Result openDevice(@RequestParam(value = "id", required = false) Integer sealYYLCId) {

        Person person = user();

        SealYylc sealYylc = sealYylcService.getById(sealYYLCId);
        Seal seal = sealService.getById(sealYylc.getSeal_id());
        VYyjyqbLcDW vYyjyqbLcDW = vYyjyqbLcDWService.getById(sealYylc.getYylc_id());

        //参数初始化
        Date nowDate = new Date();
        String exceedStr = "24";
        //是否超时
        boolean isExceed = false;

        List<DictionaryValue> dictionaryValues = dictionaryValueService.list(new LambdaQueryWrapper<DictionaryValue>()
                .eq(DictionaryValue::getTitleID,970234).eq(DictionaryValue::getContent,"倒计时小时数"));

        if (dictionaryValues != null && dictionaryValues.size() != 0) {
            exceedStr = dictionaryValues.get(0).getParameter();
        }

        try {
            if ("用印申请".equals(vYyjyqbLcDW.getType())) {
                //是否超过时间
                LocalDateTime qrsjDate = getCreateTime(vYyjyqbLcDW);

                //是否超过时间
                boolean qrsjBool = sealYylcService.isExceedTime(nowDate,qrsjDate,exceedStr);

                if (qrsjBool){
                    isExceed = true;
                }else{
                    isExceed = false;
                }

            } else if ("公章外借".equals(vYyjyqbLcDW.getType())){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                String jssj = sealYylcService.yytime2String(vYyjyqbLcDW.getJyedndate(),vYyjyqbLcDW.getEdatequantum());
                exceedStr = "0";
                //是否超过时间
                isExceed = sealYylcService.isExceedTimeToStr(nowDate,jssj,exceedStr);
            }

        } catch (Exception e){
            log.error("用印管理,提交申请单，ElectronicSealController->createApply.初始化结束时间失败"+e.getMessage());
            return Result.error("初始化结束时间失败");
        }

        if (isExceed){
            return Result.error("超时无法操作");
        }

        if ("完成".equals(vYyjyqbLcDW.getLcJdmc())){

            List<String> roleName = personService.getUserRoleName(person.getId());
            if (!roleName.stream().anyMatch(i -> i.contains("印章保管人")) && sealYylc.getEmpower_type() == 0) {

                return Result.error("未授权无法开锁");

            }

            Result result = sealYylcService.postYXYG03_03(sealYylc.getOrder_num(),sealYylc.getYylc_id(),
                    sealYylc.getBiz_id(),seal.getDeviceUuid());

            if (Constant.SC_OK_200.equals(result.getCode())) {
                sealYylc.setApply_type(1);
                sealYylc.setOrder_type(1);
            } else {
                sealYylc.setApply_type(0);
                sealYylc.setOrder_type(0);
            }

            return result;

        }

        return Result.error("失败,流程未完成");

    }

    /**
     * 删除指纹
     * @param fingerprintDto
     * @return
     */
    @RequestMapping("/delFingerprint")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "获取用印列表，ElectronicSealController->getSealYYLCList")
    public Result delFingerprint(@RequestBody FingerprintDto fingerprintDto) {

        //如果没传人名，则停止操作
        if (StringUtils.isEmpty(fingerprintDto.getZnyyUserId())) {
            return Result.error("姓名输入为空！");
        }

        Result result = sealYylcService.postYXYG02_07(fingerprintDto.getUuid(),fingerprintDto.getZnyyUserId(),1);

        return result;

    }

    /**
     * 添加指纹
     * @param fingerprintDto
     * @return
     */
    @RequestMapping("/addFingerprint")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "获取用印列表，ElectronicSealController->getSealYYLCList")
    public Result addFingerprint(@RequestBody FingerprintDto fingerprintDto) {

        Result znyyUserResult = sealYylcService.addZNYYPerson(fingerprintDto.getUserId(),fingerprintDto.getUserId());

        if (Constant.SC_OK_200.equals(znyyUserResult.getCode())) {

            return sealYylcService.postYXYG02_07(fingerprintDto.getUuid(),fingerprintDto.getUserId(),0);

        }

        return znyyUserResult;

    }

    /**
     * 获取指纹列表
     * @param fingerprintDto
     * @return
     */
    @RequestMapping("/getFingerPage")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "获取用印列表，ElectronicSealController->getSealYYLCList")
    public Result getFingerPage(@RequestBody FingerprintDto fingerprintDto) {

        return sealYylcService.postYXYG04_05(fingerprintDto.getCurrent(),fingerprintDto.getSize()
                ,fingerprintDto.getUuid(),fingerprintDto.getCID());

//        if (!Constant.SC_OK_200.equals(result.getCode())) {
//            return result;
//        }
//
//        String fpJSON = result.getResult().toString();
//
//        List<Fingerprint> fingerprintList = JSON.parseArray(fpJSON, Fingerprint.class);
//
//        result.setResult(fingerprintList);

    }

    /**
     * 设置wifi
     * @param wifi
     * @return 返回信息
     */
    @RequestMapping("/setLinkWifi")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "获取用印列表，ElectronicSealController->getSealYYLCList")
    public Result setLinkWifi(@RequestBody WifiEntity wifi) {

        return sealYylcService.postYXYG02_05(wifi.getUuid(),wifi.getSsid(),wifi.getPassword(),true);

    }

    @RequestMapping("/getSealPage")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "得到用印记录列表，SealRecordController->getList")
    public Result getSealPage(@RequestBody SearchParam param) {

        Page<Seal> page = new Page<>(param.getCurrent(),param.getSize());
        Person person = user();
        sealService.page(
                page
                ,new LambdaQueryWrapper<Seal>()
                        .like(StringUtils.isNotEmpty(param.getCID()),Seal::getSealName,param.getCID())
                        .or()
                        .like(StringUtils.isNotEmpty(param.getCID()),Seal::getDeviceUuid,param.getCID())
                        .eq(Seal::getTopGroupId,person.getTopGroupId())
                        .orderByDesc(Seal::getId)
        );

        Set<Integer> topGroupIds = page.getRecords().stream().map(Seal::getTopGroupId).collect(Collectors.toSet());

        List<Groupitem> groupitemList = groupitemService.list(
                new LambdaQueryWrapper<Groupitem>()
                        .in(Groupitem::getId,topGroupIds)
        );

        //循环赋值
        page.getRecords().stream().forEach(seal -> {
            for (Groupitem g : groupitemList) {
                if (g.getId().equals(seal.getTopGroupId())) {
                    seal.setGroupName(g.getGroupname());
                }
            }
        });

        return Result.ok(page);
    }

    @RequestMapping("/openLock")
    @ResponseBody
    public Result openLock(@RequestBody FingerprintDto fingerprintDto){
        return sealYylcService.postYXYG02_04(fingerprintDto.getUuid());
    }

    @RequestMapping("/openSealEmpower")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "开启印章授权，ElectronicSealController->openSealEmpower")
    public Result openSealEmpower(@RequestParam(value = "id", required = false) Integer sealYYLCId) {

        SealYylc sealYylc = sealYylcService.getById(sealYYLCId);
        Seal seal = sealService.getById(sealYylc.getSeal_id());
        VYyjyqbLcDW vYyjyqbLcDW = vYyjyqbLcDWService.getById(sealYylc.getYylc_id());

        //参数初始化
        Date nowDate = new Date();
        String exceedStr = "24";
        //是否超时
        boolean isExceed = false;

        List<DictionaryValue> dictionaryValues = dictionaryValueService.list(new LambdaQueryWrapper<DictionaryValue>()
                .eq(DictionaryValue::getTitleID,970234).eq(DictionaryValue::getContent,"倒计时小时数"));

        if (dictionaryValues != null && dictionaryValues.size() != 0) {
            exceedStr = dictionaryValues.get(0).getParameter();
        }

        try {
            if ("用印申请".equals(vYyjyqbLcDW.getType())) {
                //是否超过时间
                LocalDateTime qrsjDate = getCreateTime(vYyjyqbLcDW);

                //是否超过时间
                boolean qrsjBool = sealYylcService.isExceedTime(nowDate,qrsjDate,exceedStr);

                if (qrsjBool){
                    isExceed = true;
                }else{
                    isExceed = false;
                }
            } else if ("公章外借".equals(vYyjyqbLcDW.getType())){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                String jssj = sealYylcService.yytime2String(vYyjyqbLcDW.getJyedndate(),vYyjyqbLcDW.getEdatequantum());
                exceedStr = "0";
                //是否超过时间
                isExceed = sealYylcService.isExceedTimeToStr(nowDate,jssj,exceedStr);
            }

        } catch (Exception e){
            log.error("用印管理,提交申请单，ElectronicSealController->createApply.初始化结束时间失败"+e.getMessage());
            return Result.error("初始化结束时间失败");
        }

        if (isExceed){
            return Result.error("超时无法操作");
        }

        if ("完成".equals(vYyjyqbLcDW.getLcJdmc())){

            Person person = user();

            List<String> roleName = personService.getUserRoleName(person.getId());

            if (roleName.stream().anyMatch(i -> i.contains("印章保管人"))) {

                EmpowerLog empowerLog = new EmpowerLog(person.getId(),vYyjyqbLcDW.getUserid(),person.getRealName(),
                        vYyjyqbLcDW.getApplicant(),1, LocalDateTime.now(),sealYylc.getId());
                empowerLogService.save(empowerLog);

                sealYylcService.update(new LambdaUpdateWrapper<SealYylc>()
                        .eq(SealYylc::getId,sealYYLCId)
                        .set(SealYylc::getEmpower_type,1));

            }else{
                return Result.error("失败,非章管员无法操作");
            }

            return Result.ok("授权成功");

        }

        return Result.error("失败,流程未完成");

    }

    @RequestMapping("/closeSealEmpower")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "关闭印章授权，ElectronicSealController->closeSealEmpower")
    public Result closeSealEmpower(@RequestParam(value = "id", required = false) Integer sealYYLCId) {

        SealYylc sealYylc = sealYylcService.getById(sealYYLCId);
        Seal seal = sealService.getById(sealYylc.getSeal_id());
        VYyjyqbLcDW vYyjyqbLcDW = vYyjyqbLcDWService.getById(sealYylc.getYylc_id());

        if ("完成".equals(vYyjyqbLcDW.getLcJdmc())){

            Person person = user();

            List<String> roleName = personService.getUserRoleName(person.getId());

            if (roleName.stream().anyMatch(i -> i.contains("印章保管人"))) {

                EmpowerLog empowerLog = new EmpowerLog(person.getId(),vYyjyqbLcDW.getUserid(),person.getRealName(),
                        vYyjyqbLcDW.getApplicant(),0, LocalDateTime.now(),sealYylc.getId());
                empowerLogService.save(empowerLog);

                sealYylcService.update(new LambdaUpdateWrapper<SealYylc>()
                        .eq(SealYylc::getId,sealYYLCId)
                        .set(SealYylc::getEmpower_type,0));

            }

            return Result.ok("取消成功");

        }

        return Result.error("失败,流程未完成");

    }

    @RequestMapping("/lockSeal")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "设备紧急锁定，ElectronicSealController->lockSeal")
    public Result lockSeal(@RequestParam(value = "id", required = false) Integer sealYYLCId) {
        Person person = user();
        List<String> roleName = personService.getUserRoleName(person.getId());
        if (roleName.stream().noneMatch(i -> i.contains("印章保管人"))) {
            return Result.error("你不是印章保管人，当前按钮无权限");
        }
        SealYylc sealYylc = sealYylcService.getById(sealYYLCId);
        sealService.update(new LambdaUpdateWrapper<Seal>()
                .set(Seal::getLock, 1)
                .eq(Seal::getId, sealYylc.getSeal_id())
        );
        Result result = sealYylcService.postYXYG02_08(true, sealYylc.getBiz_id(),sealYylc.getDevice_uuid());

        return result;
    }

    @RequestMapping("/unlockSeal")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "ElectronicSealController->lockSeal")
    public Result unlockSeal(@RequestParam(value = "id", required = false) Integer sealYYLCId) {
        Person person = user();
        List<String> roleName = personService.getUserRoleName(person.getId());
        if (roleName.stream().noneMatch(i -> i.contains("印章保管人"))) {
            return Result.error("你不是印章保管人，当前按钮无权限");
        }
        SealYylc sealYylc = sealYylcService.getById(sealYYLCId);
        sealService.update(new LambdaUpdateWrapper<Seal>()
                .set(Seal::getLock, 0)
                .eq(Seal::getId, sealYylc.getSeal_id())
        );
        Result result = sealYylcService.postYXYG02_08(false, sealYylc.getBiz_id(),sealYylc.getDevice_uuid());

        return result;
    }

    @RequestMapping("/getYYLCEXTEND")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "ElectronicSealController->getYYLCEXTEND")
    public Result getYYLCEXTEND(@RequestParam(value = "id", required = false) Integer lcId) throws ParseException {
        List<ExtendYylc> yylcs = new ArrayList<>();
        List<VYyjyqbLcDW> vYyjyqbLcDWList = vYyjyqbLcDWService.list(new LambdaQueryWrapper<VYyjyqbLcDW>().eq(VYyjyqbLcDW::getId, lcId));
        if (vYyjyqbLcDWList.size() > 0) {
            ExtendYylc yylc = new ExtendYylc();
            yylc.setYylcId(lcId);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if ("公章外借".equals(vYyjyqbLcDWList.get(0).getType())) {
                if ("上午".equals(vYyjyqbLcDWList.get(0).getSdatequantum())) {
                    Date date = vYyjyqbLcDWList.get(0).getJystartdate();
                    Instant instant = date.toInstant();
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
                    yylc.setCreateDate(dateTime);
                } else {
                    Date date = vYyjyqbLcDWList.get(0).getJystartdate();
                    Instant instant = date.toInstant();
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
                    dateTime = dateTime.plusHours(12);
                    yylc.setCreateDate(dateTime);
                }
                if ("上午".equals(vYyjyqbLcDWList.get(0).getEdatequantum())) {
                    Date date = vYyjyqbLcDWList.get(0).getJyedndate();
                    Instant instant = date.toInstant();
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
                    dateTime = dateTime.plusHours(12);
                    yylc.setEndDate(dateTime);
                } else {
                    Date date = vYyjyqbLcDWList.get(0).getJyedndate();
                    Instant instant = date.toInstant();
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
                    dateTime = dateTime.plusHours(24);
                    dateTime = dateTime.plusSeconds(-1);
                    yylc.setEndDate(dateTime);
                }
            } else if ("用印申请".equals(vYyjyqbLcDWList.get(0).getType())) {
                yylc.setCreateDate(getCreateTime(vYyjyqbLcDWList.get(0)));
                List<DictionaryValue> dateValue = dictionaryValueService.list(new LambdaQueryWrapper<DictionaryValue>()
                        .eq(DictionaryValue::getTitleID, 970234)
                        .eq(DictionaryValue::getContent, "倒计时小时数")
                );
                if (dateValue.size() > 0) {
                    Integer hours = Integer.valueOf(dateValue.get(0).getParameter());
                    LocalDateTime dateTime = yylc.getCreateDate();
                    dateTime = dateTime.plusHours(hours);
                    yylc.setEndDate(dateTime);
                }
            } else {
                yylc.setCreateDate(LocalDateTime.parse("1990-01-01 00:00:00", formatter));
                yylc.setEndDate(LocalDateTime.parse("1990-01-01 00:00:00", formatter));
            }
            yylcs.add(yylc);
        }
        return Result.ok(yylcs);
    }

    /**
     * 查询页
     *
     * @param CID
     * @return
     */
    @GetMapping("/querylist")
    public ResponseEntity<Page<VYyjyqbLc>> GetquerydataList(String CID) {
        Page<VYyjyqbLc> page = getPage();
        QueryWrapper<VYyjyqbLc> queryWrapper = new QueryWrapper<>();

        if (person.getRoleNamesString().contains("财务打印管理")) {
            queryWrapper.eq("TopGroupID", person.getTopGroupId());
        } else {
            queryWrapper.and(i -> i.like("AllPersonZgh", person.getLoginName()));
        }
        if (person.getRoleNamesString().contains("印章保管人")) {
            queryWrapper = new QueryWrapper<VYyjyqbLc>().and(i -> i.eq("TopGroupID", person.getTopGroupId()).or().eq("yySendGroupID", person.getTopGroupId()));
        }

        if (CID != null && !"".equals(CID)) {
            queryWrapper.and(i -> i.like("bh", CID).or().like("type", CID).or().like("yySend", CID));
        }
        queryWrapper.eq("lc_jdmc", "完成");
        queryWrapper.and(i -> i.eq("type", "用印申请").or().eq("type", "公章外借"));
        queryWrapper.orderByDesc("id");

        Page<VYyjyqbLc> yyjyqbLcPage = vYyjyqbLcService.page(page, queryWrapper);
        SimpleDateFormat timeformate = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < yyjyqbLcPage.getRecords().size(); i++) {
            if (yyjyqbLcPage.getRecords().get(i).getType().contains("外借")) {
                String jyStartDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJystartdate());
                String jyEdnDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJyedndate());
                yyjyqbLcPage.getRecords().get(i).setWjDate(jyStartDate + "至" + jyEdnDate);
            }
        }
        return ResponseEntity.ok(yyjyqbLcPage);
    }

    @RequestMapping("/getUsageRecord")
    @ResponseBody
    public Result getUsageRecord(@RequestBody ZnyyYyjlParam yyjl){
        return dfdwTZnyyYyjlService.getUsageRecord(yyjl);
    }

    public LocalDateTime getCreateTime(VYyjyqbLcDW dw) throws ParseException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isNotEmpty(dw.getZgsleadersj())) {
            // 总公司确认时间
            Date date = dateFormat.parse(dw.getZgsleadersj());
            Instant instant = date.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();

            LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
            return dateTime;
        } else if (StringUtils.isNotEmpty(dw.getZhglbqrsj())) {
            // 综合管理部确认时间
            Date date = dateFormat.parse(dw.getZhglbqrsj());
            Instant instant = date.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();

            LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
            return dateTime;
        } else if (StringUtils.isNotEmpty(dw.getFzrqrsj())) {
            // 负责人确认时间
            Date date = dateFormat.parse(dw.getFzrqrsj());
            Instant instant = date.toInstant();
            ZoneId zoneId = ZoneId.systemDefault();

            LocalDateTime dateTime = instant.atZone(zoneId).toLocalDateTime();
            return dateTime;
        } else {
            LocalDateTime dateTime = LocalDateTime.parse("1990-01-01 00:00:00", formatter);
            return dateTime;
        }
    }
}
