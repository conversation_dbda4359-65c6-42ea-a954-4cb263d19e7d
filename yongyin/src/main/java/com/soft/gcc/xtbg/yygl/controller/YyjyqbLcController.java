package com.soft.gcc.xtbg.yygl.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.soft.framework.common.utils.ServletUtils;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.gcc.common.dictionary_value.entity.Dictionaryvalue;
import com.soft.gcc.common.dictionary_value.service.DictionaryvalueService;
import com.soft.gcc.common.groupitem.service.GroupitemService;
import com.soft.gcc.common.lc_workFlow.entity.LcWorkflow;
import com.soft.gcc.common.lc_workFlow.service.LcWorkflowService;
import com.soft.gcc.common.lcdefine.service.LcdefineService;
import com.soft.gcc.common.person.entity.Person;
import com.soft.gcc.common.person.service.PersonService;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.common.utils.SendSMSUtils;
import com.soft.gcc.xtbg.yygl.entity.VYyjyqbLc;
import com.soft.gcc.xtbg.yygl.entity.YyjyqbLc;
import com.soft.gcc.xtbg.yygl.entity.yybb;
import com.soft.gcc.xtbg.yygl.service.VYyjyqbLcService;
import com.soft.gcc.xtbg.yygl.service.YyjyqbLcService;
import com.spire.doc.Document;
import com.spire.doc.FileFormat;
import com.yyszc.wpbase.ventity.PersonEntity;
import fr.opensagres.xdocreport.document.IXDocReport;
import fr.opensagres.xdocreport.document.registry.XDocReportRegistry;
import fr.opensagres.xdocreport.template.IContext;
import fr.opensagres.xdocreport.template.TemplateEngineKind;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

@RestController
@RequestMapping("/service/xtbg/yygl")
public class YyjyqbLcController extends BaseConfig {

    @Autowired
    private YyjyqbLcService yyjyqbLcService;
    @Autowired
    private VYyjyqbLcService vYyjyqbLcService;
    @Autowired
    private TFileService tFileService;
    @Autowired
    private DictionaryvalueService dictionaryvalueService;
    @Autowired
    private GroupitemService groupitemService;
    @Autowired
    private LcWorkflowService lcWorkflowService;
    @Autowired
    private LcdefineService lcdefineService;
    @Autowired
    private PersonService personService;

    private PersonEntity person;
    private static final Integer FUNCTION_ID = 9102;

    private String TEMPLATE_PATH = "/muban/";


    private void initPerson() {
        person = SessionHelper.getSessionPerson();
        if (person == null) {
            throw new RuntimeException("登录异常");
        }
    }

    private <T> Page<T> getPage() {
        initPerson();
        Integer size = ServletUtils.getParameterToInt("size");
        Integer current = ServletUtils.getParameterToInt("current");
        Page<T> page = new Page<>();
        page.setCurrent(current);
        page.setSize(size);
        return page;
    }

    /**
     * test
     */
    @RequestMapping("/test")
    public boolean test() {
        return true;
    }

    /**
     * 提交判断外借期限是否重复
     *
     * @return
     */
    @GetMapping("/getCanSubmitWjDate")
    public String getCanSubmitWjDate(Integer id) {
        YyjyqbLc entity = yyjyqbLcService.getById(id);
        if (entity.getType().contains("外借")) {
            QueryWrapper<LcWorkflow> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(i -> i.eq("ywID", entity.getId()).eq("lc_defineID", FUNCTION_ID));
            List<LcWorkflow> list = lcWorkflowService.list(queryWrapper);
            if (list.size() > 0) {
                return GetResultToNext(entity, "yes");
            } else {
                return GetResultToNext(entity, "");
            }
        }
        return "true";
    }

    /**
     * 获取流程信息
     */
    @GetMapping("/GetWorkFlowList")
    public ResponseEntity<List<LcWorkflow>> GetWorkFlowList(Integer id) {
        QueryWrapper<LcWorkflow> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(i -> i.eq("ywID", id).eq("lc_defineID", FUNCTION_ID)).orderByAsc("ID");
        return ResponseEntity.ok(lcWorkflowService.list(queryWrapper));
    }

    /**
     * 需求描述66、印章类型65
     */
    @GetMapping("/GetType")
    @PreAuthorize("@ss.hasPermi('910106')")
    public ResponseEntity<List<Dictionaryvalue>> GetType(Integer id) {
        LambdaQueryWrapper<Dictionaryvalue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dictionaryvalue::getTitleid, id);
        List<Dictionaryvalue> list = dictionaryvalueService.list(queryWrapper);
        return ResponseEntity.ok(list);
    }

    /**
     * 用印呈送
     */
    @GetMapping("/GetyySend")
    @PreAuthorize("@ss.hasPermi('910106')")
    public ResponseEntity<ArrayList> GetyySend() {
        initPerson();
        ArrayList<Dictionaryvalue> list = new ArrayList<>();
        if (person.getParentZGroupId() == -1) {
            if (person.getTopGroupId() != 373) {
                Dictionaryvalue dictionaryvalue = new Dictionaryvalue();
                dictionaryvalue.setContent("本公司");
                list.add(dictionaryvalue);
            }
        } else {
            Dictionaryvalue dictionaryvalue = new Dictionaryvalue();
            dictionaryvalue.setContent("本公司");
            list.add(dictionaryvalue);
            Dictionaryvalue dictionaryvalue2 = new Dictionaryvalue();
            String parentZGroupName = person.getParentZGroupName();
            if (parentZGroupName != "" && parentZGroupName != null) {
                dictionaryvalue2.setContent(person.getParentZGroupName());
                list.add(dictionaryvalue2);
            }
        }
        Dictionaryvalue dictionaryvalue = new Dictionaryvalue();
        dictionaryvalue.setContent("永耀集团");
        list.add(dictionaryvalue);
        return ResponseEntity.ok(list);
    }

    /**
     * 导出word
     */
    @RequestMapping("/exportWord")
    public void exportWord(HttpServletResponse response, @RequestParam(value = "id") Integer id) throws Exception {
        YyjyqbLc entity = yyjyqbLcService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 获取Word模板
        String fileName = "";
        if ("用印申请".equals(entity.getType())) {
            fileName = "公章外用审批表.docx";
        } else if (entity.getType().contains("法人代表")) {
            fileName = "法人签字审批单.docx";
        } else if ("不动产权证外借".equals(entity.getType()) || "营业执照外借".equals(entity.getType()) || "公章外借".equals(entity.getType())) {
            fileName = "营业执照、房产证借用审批单.docx";
        }
        fileName = URLDecoder.decode(fileName, "UTF-8");
//        InputStream in = new FileInputStream(TEMPLATE_PATH + fileName);
        InputStream in = this.getClass().getResourceAsStream(TEMPLATE_PATH + fileName);
        IXDocReport report = XDocReportRegistry.getRegistry().loadReport(in, TemplateEngineKind.Freemarker);
        IContext context = report.createContext();
        String filenamedisplay = "";
        if ("用印申请".equals(entity.getType())) {
            if (entity.getTopgroupid() == 469 && "本公司".equals(entity.getYysend())) {
                context.put("bz1", "用印申请需由部门负责人审批。");
                context.put("bz2", "公章外借需由部门负责任人及综合管理部负责人审批，相关员工陪同使用。");
            } else {
                context.put("bz1", "用章人（经办人）需正式工或由正式工陪同使用。");
                context.put("bz2", "分公司借用公章需经本单位企业负责人审批同意。");
            }
            context.put("Qblx", entity.getQblx());
            context.put("yysl", entity.getYycount());
            context.put("Yzfl", entity.getYzfl());
        } else if ("法人代表".equals(entity.getType())) {
            context.put("groupname", entity.getGroupname());
            context.put("phone", entity.getPhone());
        } else if ("不动产权证外借".equals(entity.getType()) || "营业执照外借".equals(entity.getType()) || "公章外借".equals(entity.getType())) {
            context.put("yyzztype", entity.getType());
        }
        if (entity.getTopgroupid() == 469 && "本公司".equals(entity.getYysend())) {
            context.put("jlfgs", "监理分公司");
        } else {
            context.put("jlfgs", "");
        }
        context.put("Time", sdf.format(entity.getTime()));
        context.put("Applicant", entity.getApplicant());
        context.put("Fzrqrsj", entity.getFzrqrsj() == null ? "" : sdf.format(new Date(entity.getFzrqrsj())));
        context.put("Fzryj", entity.getFzryj() == null ? "" : entity.getFzryj());
        context.put("Sqfzr", entity.getSqfzr() == null ? "" : entity.getSqfzr());
        context.put("Yysy", entity.getYysy() == null ? "" : entity.getYysy());
        context.put("zhglbfzr", entity.getZhglbfzr() == null ? "" : entity.getZhglbfzr());
        context.put("Zhglbqrsj", entity.getZhglbqrsj() == null ? "" : sdf.format(new Date(entity.getZhglbqrsj())));
        context.put("Zhglbyj", entity.getZhglbyj() == null ? "" : entity.getZhglbyj());

        filenamedisplay = URLEncoder.encode("用印管理流程(" + entity.getBh() + "号).docx", "UTF-8");
        //导出到response输出流中
        response.setContentType("text/html;charset=utf-8");
//        request.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + filenamedisplay);
        response.setContentType("application/x-download;charset=utf-8");
        OutputStream out = response.getOutputStream();
        report.process(context, out);
        out.flush();
        in.close();

    }

    /**
     * 导出pdf
     */
    @RequestMapping("/exportPdf")
    public void exportPdf(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "id") Integer id) throws Exception {
        YyjyqbLc entity = yyjyqbLcService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 获取Word模板
        String fileName = "";
        if ("用印申请".equals(entity.getType())) {
            fileName = "公章外用审批表.docx";
        } else if (entity.getType().contains("法人代表")) {
            fileName = "法人签字审批单.docx";
        } else if ("不动产权证外借".equals(entity.getType()) || "营业执照外借".equals(entity.getType()) || "公章外借".equals(entity.getType())) {
            fileName = "营业执照、房产证借用审批单.docx";
        }
        fileName = URLDecoder.decode(fileName, "UTF-8");
//        InputStream in = new FileInputStream(TEMPLATE_PATH + fileName);
        InputStream in = this.getClass().getResourceAsStream(TEMPLATE_PATH + fileName);
        IXDocReport report = XDocReportRegistry.getRegistry().loadReport(in, TemplateEngineKind.Freemarker);
        IContext context = report.createContext();
        String filenamedisplay = "";
        if ("用印申请".equals(entity.getType())) {
            if (entity.getTopgroupid() == 469 && "本公司".equals(entity.getYysend())) {
                context.put("bz1", "用印申请需由部门负责人审批。");
                context.put("bz2", "公章外借需由部门负责任人及综合管理部负责人审批，相关员工陪同使用。");
            } else {
                context.put("bz1", "用章人（经办人）需正式工或由正式工陪同使用。");
                context.put("bz2", "分公司借用公章需经本单位企业负责人审批同意。");
            }
            context.put("Qblx", entity.getQblx());
            context.put("yysl", entity.getYycount());
            context.put("Yzfl", entity.getYzfl());
        } else if ("法人代表".equals(entity.getType())) {
            context.put("groupname", entity.getGroupname());
            context.put("phone", entity.getPhone());
        } else if ("不动产权证外借".equals(entity.getType()) || "营业执照外借".equals(entity.getType()) || "公章外借".equals(entity.getType())) {
            context.put("yyzztype", entity.getType());
        }
        if (entity.getTopgroupid() == 469 && "本公司".equals(entity.getYysend())) {
            context.put("jlfgs", "监理分公司");
        } else {
            context.put("jlfgs", "");
        }
        context.put("Time", sdf.format(entity.getTime()));
        context.put("Applicant", entity.getApplicant());
        context.put("Fzrqrsj", entity.getFzrqrsj() == null ? "" : sdf.format(new Date(entity.getFzrqrsj())));
        context.put("Fzryj", entity.getFzryj() == null ? "" : entity.getFzryj());
        context.put("Sqfzr", entity.getSqfzr() == null ? "" : entity.getSqfzr());
        context.put("Yysy", entity.getYysy() == null ? "" : entity.getYysy());
        context.put("zhglbfzr", entity.getZhglbfzr() == null ? "" : entity.getZhglbfzr());
        context.put("Zhglbqrsj", entity.getZhglbqrsj() == null ? "" : sdf.format(new Date(entity.getZhglbqrsj())));
        context.put("Zhglbyj", entity.getZhglbyj() == null ? "" : entity.getZhglbyj());

        filenamedisplay = URLEncoder.encode("用印管理流程(" + entity.getBh() + "号).pdf", "UTF-8");
        //导出到response输出流中
        response.setContentType("text/html;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + filenamedisplay);
        response.setContentType("application/x-download;charset=utf-8");
        OutputStream out = response.getOutputStream();
//        report.process(context, out);

        long millis = System.currentTimeMillis();
//        String temp_path = getOssLocalPath() + "temp/";
        String temp_path = request.getServletContext().getRealPath("/") + "temp/";
        String temp_name = "yygl" + millis + ".docx";
        String path = temp_path + temp_name;
        File pathFolder = new File(temp_path);
        if (!pathFolder.exists()) {
            pathFolder.mkdirs();
        }
        File wordOutputFile = new File(path);
        OutputStream outputStream = new FileOutputStream(wordOutputFile);
        report.process(context, outputStream);

        //实例化Document类的对象
        Document doc = new Document(path);
        try {
            doc.saveToStream(out, FileFormat.PDF);
        } catch (Exception ex) {
            return;
        }

        File folder = new File(temp_path);
        File[] files = folder.listFiles();
        for (File file : files) {
            if (file.getName().equals(temp_name)) {
                file.delete();
            }
        }

        out.flush();
        out.close();
        in.close();
    }

    /**
     * 统计页导出
     */
    @RequestMapping("/exportExcel")
    public void exportExcel(String Svalue, String Evalue, HttpServletResponse response) throws Exception {
        initPerson();
        //TODO 用印待优化代码
        SqlHelper sqlhelper = new SqlHelper();

        String sql = "SELECT g.groupname as GroupName,g.XH,yy.TopGroupID as GroupID,";
        sql += " SUM(case when yy.lc_jdid is null then 1 else 0 end) as CGNumb,";
        sql += " SUM(case when yy.lc_jdid>0 then 1 else 0 end) as ZTNumb,";
        sql += " SUM(case when yy.lc_jdid=0 then 1 else 0 end) as GDNumb,";
        sql += " SUM(case when yy.lc_jdid=-1 then 1 else 0 end) as ZFNumb,";
        sql += " COUNT(*) as HJNumb FROM";

        if (Svalue == null && Evalue == null) {
            sql += " V_Yyjyqb_LC yy";
        } else {
            String st = "";
            String et = "";
            if (Svalue == null) {
                st = "2000/1/01 00:00:00";
            } else {
//                st = Convert.ToDateTime(Svalue).ToString("yyyy/MM/dd");
                st = Svalue;
                st += " 00:00:00";
            }
            if (Evalue == "" || Evalue == null) {
//                et = DateTime.Now.ToString("yyyy/MM/dd");
                SimpleDateFormat timeformate = new SimpleDateFormat("yyyy/MM/dd");
                LocalDate localDate = LocalDate.now();
                et = LocalDate.now().toString();
                et += " 00:00:00";
            } else {
//                et = Convert.ToDateTime(Evalue).ToString("yyyy/MM/dd");
                et = Evalue;
                et += " 00:00:00";
            }

            sql += " (select * from V_Yyjyqb_LC vy";
            sql += " where vy.Time>='" + st + "' and vy.Time<='" + et + "') yy ";
        }
        sql += " inner join GroupItem g on yy.TopGroupID=g.id group by yy.TopGroupID,g.groupname,g.XH";
        List<yybb> list = sqlhelper.GetObjectList(yybb.class, sql);

        //在内存操作，写到浏览器
        ExcelWriter writer = ExcelUtil.getWriter(true);
        //自定义标题别名
        writer.addHeaderAlias("GroupName", "使用单位");
        writer.addHeaderAlias("CGNumb", "拟稿数量");
        writer.addHeaderAlias("ZTNumb", "在途数量");
        writer.addHeaderAlias("GDNumb", "归档数量");
        writer.addHeaderAlias("ZFNumb", "作废数量");
        writer.addHeaderAlias("HJNumb", "合计");

        // 用个List<Map>装表格的所有内容
        ArrayList<Map<String, Object>> rows = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("GroupName", list.get(i).getGroupName());
            row.put("CGNumb", list.get(i).getCGNumb());
            row.put("ZTNumb", list.get(i).getZTNumb());
            row.put("GDNumb", list.get(i).getGDNumb());
            row.put("ZFNumb", list.get(i).getZFNumb());
            row.put("HJNumb", list.get(i).getHJNumb());
            rows.add(row);
        }
        // 列宽
        writer.setColumnWidth(0, 30);
        writer.setColumnWidth(1, 20);
        writer.setColumnWidth(2, 20);
        writer.setColumnWidth(3, 20);
        writer.setColumnWidth(4, 20);
        writer.setColumnWidth(5, 20);
        //写出
        writer.write(rows, true);
        //设置content—type
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");
        //设置标题
        String fileName = URLEncoder.encode("用印统计查询", "UTF-8");
        //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream outputStream = response.getOutputStream();
        //将Writer刷新到OutPut
        writer.flush(outputStream, true);
        outputStream.close();
        writer.close();
    }

    /**
     * 获取附件信息
     *
     * @return
     */
    @GetMapping("/FileByIDList")
    @ResponseBody
    public ResponseEntity<List> GetFileByIDList(Integer id) {
        QueryWrapper<TFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(i -> i.eq("ProjectID", id).eq("FunctionID", FUNCTION_ID));
        return ResponseEntity.ok(tFileService.list(queryWrapper));
    }

    /**
     * 附件下载
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @RequestMapping("/downloadFile")
    public void download(Integer id, HttpServletResponse response, HttpServletRequest request) throws Exception {
        TFile tFile = tFileService.getById(id);

        String kname = "Upload/FileManage/" + tFile.getFilepath();
        String filePath = ConfigHelper.getOssLocalPath() + "FileManage/" + tFile.getFilepath();

        String fullfile = "";
//        String localf = kname.replaceAll(ConfigHelper.getOssRemotePath(), ConfigHelper.getOssLocalPath());
        String localf = filePath;
        if (!FileUtil.FileExists(localf)) {
            if (ConfigHelper.isOSSOpen()) {
                fullfile = localf;
                OSSHelper ossHelper = new OSSHelper();
                if (FileUtil.FileExists(fullfile)) {
                    FileUtil.Delete(fullfile);
                }
                ossHelper.DownloadFile(kname, fullfile);
            }
        } else {
            fullfile = localf;
        }
        if (FileUtil.FileExists(fullfile)) {
            byte[] data = ToolHelper.File2Bytes(fullfile);
            response.reset();
//            response.setContentType("application/pdf;charset=UTF-8");
//            response.setHeader("Content-Disposition", "inline");
            response.setContentType("application/octet-stream");
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(tFile.getFilename(), "utf-8"));

            response.addHeader("Content-Length", "" + data.length);

            //用于处理字符流数据 response.getWriter()
            //用于输出字符流数据或者二进制的字节流数据   response.getOutputStream()
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
        }

//        t_fileController fileController = new t_fileController();
//        fileController.download(tFile, response);
    }

    /**
     * 新增附件
     */
    @PostMapping("/AddFile")
    @PreAuthorize("@ss.hasPermi('910110')")
    public AjaxResult AddFile(MultipartFile file, Integer id) {
        AjaxResult ajaxResult = null;
        if (file.isEmpty()) {
            ajaxResult = AjaxResult.error("文件上传失败！请重新上传！");
            return ajaxResult;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssS");

        String filePath = ConfigHelper.getOssLocalPath() + "FileManage/";
        String fileName = file.getOriginalFilename();
        //String fname = FileUtil.ExtractFileNameNoExt(fileName);
        String fname = sdf.format(new Date());
        String fext = FileUtil.ExtractFileExt(fileName);
        FileUploadHelper fileUploadHelper = new FileUploadHelper();
        fileUploadHelper.setFileName(fname);
        fileUploadHelper.setPath(filePath);
        fileUploadHelper.setFileType(fext);
        fileUploadHelper.setPostedFile(file);

        if (fileUploadHelper.Upload() != null) {
            TFile tFile = new TFile();
            tFile.setFilename(fileName);
            tFile.setFilepath(fname + fext);
            tFile.setProjectid(id);
            tFile.setFunctionid(FUNCTION_ID);
            tFile.setUploaddate(new Date());
            tFileService.save(tFile);

            ajaxResult = AjaxResult.success("文件上传成功");
            return ajaxResult;
        } else {
            ajaxResult = AjaxResult.error("文件上传失败！");
            return ajaxResult;
        }
    }

    /**
     * 删除附件
     */
    @RequestMapping("/DeleteFile")
    @PreAuthorize("@ss.hasPermi('910110')")
    public ResponseEntity<Boolean> DeleteFile(Integer id) {
//        TFile tFile = tFileService.getById(id);
//        t_fileController t_fileController = new t_fileController();
//        t_fileController.DeleteFile(tFile.getFilepath());
        return ResponseEntity.ok(tFileService.removeById(id));
    }

    /**
     * 新增
     */
    @RequestMapping("/add")
    @PreAuthorize("@ss.hasPermi('910106')")
    public ResponseEntity<Boolean> add(@RequestBody YyjyqbLc entity) {
        initPerson();
        entity.setTime(new Date());
        entity.setApplicant(person.getRealName());
        String gname = "";
        if (person.getTopGroupName().equals(person.getGroupName())) {
            gname = person.getTopGroupName();
        } else {
            gname = person.getTopGroupName() + '-' + person.getGroupName();
        }
        entity.setGroupname(gname);
        String phone = person.getTelephone() == null ? "" : person.getTelephone();
        String Sphone = person.getSphone() == null ? "" : person.getSphone();
        String resPhone = "";
        if (Sphone.equals(phone)) {
            resPhone = phone;
        } else {
            if ("".equals(phone) || phone == null || "".equals(Sphone)) {
                if ("".equals(phone) || phone == null) {
                    resPhone = Sphone;
                }
                if ("".equals(Sphone)) {
                    resPhone = phone;
                }
            } else {
                resPhone = Sphone + "~~" + phone;
            }
        }
        entity.setPhone(resPhone);

        if (entity.getYysend().contains("本公司")) {
            entity.setYysendgroupid(person.getParentGroupId());
        } else if (entity.getYysend().contains("永耀集团")) {
            entity.setYysendgroupid(373);
        } else {
            entity.setYysendgroupid(person.getParentZGroupId());
        }
        entity.setTopgroupid(person.getTopGroupId());
        entity.setGroupid(person.getGroupId());
        entity.setUserid(person.getId());

        String type = entity.getType();
        if (type.contains("用印申请")) {
            entity.setJystartdate(null);
            entity.setJyedndate(null);
            entity.setSdatequantum(null);
            entity.setEdatequantum(null);
            entity.setQblx("用印");
        } else if (type.contains("外借")) {
            entity.setYycount(0);
            entity.setYzfl("");
            entity.setQblx("借印");
            //判断外借期限是否重复，公司
            String str = GetResultToNext(entity, "");
            if ("true".equals(str)) {
            } else {
                entity = new YyjyqbLc();
                entity.setApplicant(str);
            }

        } else if (type.contains("法人代表签字")) {
            entity.setYycount(0);
            entity.setYzfl("");
            entity.setJystartdate(null);
            entity.setJyedndate(null);
            entity.setSdatequantum(null);
            entity.setEdatequantum(null);
            entity.setQblx("");
        }
//        Groupitem groupitem = groupitemService.getById(person.getTopGroupId());
//        String shortPinyin = groupitem.getShortpinyin();
        String shortPinyin = person.getTopGroupShortPinyin();
        if (shortPinyin == "" || shortPinyin == null) {
            return ResponseEntity.ok(false);
        }

        String gcBH = shortPinyin + "-";
        QueryWrapper<YyjyqbLc> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("bh", gcBH).orderByDesc("id");
        List<YyjyqbLc> list = yyjyqbLcService.list(queryWrapper);

        String year = "";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");
//        String currentDateYear = DateTime.Now.Year.ToString();
        String currentDateYear = dateFormat.format(new Date());
        if (list.size() > 0) {

            String[] str = list.get(0).getBh().split("-");
            year = str[1];
        }

        if (list.size() == 0) {
            gcBH += currentDateYear + "-0001";
        } else if (!currentDateYear.equals(year) && year != "") {
            gcBH += currentDateYear + "-0001";
        } else {
            if (Integer.parseInt(currentDateYear) > Integer.parseInt(year)) {
                gcBH += currentDateYear;
            }
            gcBH += year;
            String[] sqr = list.get(0).getBh().split("-");
            int num = Integer.parseInt(sqr[2]);
            num++;
            if (num < 10) {
                gcBH += "-" + "000" + num;
            } else if (num < 100) {
                gcBH += "-" + "00" + num;
            } else if (num <= 100) {
                gcBH += "-" + "0" + num;
            } else {
                gcBH += "-" + num;
            }
        }
        entity.setBh(gcBH);
        return ResponseEntity.ok(yyjyqbLcService.save(entity));
    }

    /**
     * 修改
     */
    @RequestMapping("/edit")
    @PreAuthorize("@ss.hasPermi('910107')")
    public ResponseEntity<Boolean> edit(@RequestBody YyjyqbLc entity) {
        initPerson();
        if (entity.getYysend().indexOf("本公司") != -1) {
            entity.setYysendgroupid(person.getParentGroupId());
        } else if (entity.getYysend().indexOf("永耀集团") != -1) {
            entity.setYysendgroupid(373);
        } else {
            entity.setYysendgroupid(person.getParentZGroupId());
        }
        entity.setTopgroupid(person.getTopGroupId());
        entity.setGroupid(person.getGroupId());

        String type = entity.getType();
        if (type.indexOf("用印申请") != -1) {
            entity.setJystartdate(null);
            entity.setJyedndate(null);
            entity.setSdatequantum(null);
            entity.setEdatequantum(null);
        } else if (type.indexOf("外借") != -1) {
            entity.setYycount(0);
            entity.setYzfl("");
            //判断外借期限是否重复，公司 yySendGroupID
            String IsBack = "";
            VYyjyqbLc vYyjyqbLc = vYyjyqbLcService.getById(entity.getId());
            if (vYyjyqbLc.getLcJdid() != null && vYyjyqbLc.getLcJdid() == 910201) {
                IsBack = "yes";
            }
            String str = GetResultToNext(entity, IsBack);
            if (str == "true") {
            } else {
                entity = new YyjyqbLc();
                entity.setApplicant(str);
            }
        } else if (type.contains("法人代表签字")) {
            entity.setYycount(0);
            entity.setYzfl("");
            entity.setJystartdate(null);
            entity.setJyedndate(null);
            entity.setSdatequantum(null);
            entity.setEdatequantum(null);
            entity.setQblx("");
        }

        return ResponseEntity.ok(yyjyqbLcService.updateById(entity));
    }

    /**
     * 删除
     */
    @RequestMapping("/deleteById")
    @PreAuthorize("@ss.hasPermi('910108')")
    public ResponseEntity<Boolean> deleteById(Integer id) {
        //TODO 增加删除流程的过程
        return ResponseEntity.ok(yyjyqbLcService.removeById(id));
    }

    /**
     * 查看
     */
    @GetMapping("/queryById")
    public ResponseEntity<YyjyqbLc> GetqueryById(Integer id) {
        return ResponseEntity.ok(this.yyjyqbLcService.getById(id));
    }

    /**
     * 申报页
     *
     * @param CID
     * @return
     */
    @GetMapping("/list")
    public ResponseEntity<Page<VYyjyqbLc>> GetdataList(String CID) {
        Page<VYyjyqbLc> page = getPage();
        QueryWrapper<VYyjyqbLc> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(i -> i.isNull("lc_jdid").or().eq("lc_jdid", "910201"));
        queryWrapper.eq("Applicant", person.getRealName());
        if (CID != null && CID != "") {
            queryWrapper.and(i -> i.like("bh", CID).or().like("type", CID).or().like("yySend", CID));
        }
        queryWrapper.orderByDesc("id");
        Page<VYyjyqbLc> yyjyqbLcPage = vYyjyqbLcService.page(page, queryWrapper);
        SimpleDateFormat timeformate = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < yyjyqbLcPage.getRecords().size(); i++) {
            if (yyjyqbLcPage.getRecords().get(i).getType().contains("外借")) {
                String jyStartDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJystartdate());
                String jyEdnDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJyedndate());
                yyjyqbLcPage.getRecords().get(i).setWjDate(jyStartDate + "至" + jyEdnDate);
            }
        }
        return ResponseEntity.ok(yyjyqbLcPage);
    }

    /**
     * 流程页
     *
     * @param CID
     * @return
     */
    @GetMapping("/flowlist")
    public ResponseEntity<Page<VYyjyqbLc>> GetflowdataList(String CID) {
        Page<VYyjyqbLc> page = getPage();
        QueryWrapper<VYyjyqbLc> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(i -> i.like("sendPersonZgh", person.getLoginName()).
                ne("lc_jdid", "0").ne("lc_jdid", "910201"));
        if (CID != null && CID != "") {
            queryWrapper.and(i -> i.like("bh", CID).or().like("type", CID).or().like("yySend", CID));
        }
        queryWrapper.orderByDesc("id");
        Page<VYyjyqbLc> yyjyqbLcPage = vYyjyqbLcService.page(page, queryWrapper);
        SimpleDateFormat timeformate = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < yyjyqbLcPage.getRecords().size(); i++) {
            if (yyjyqbLcPage.getRecords().get(i).getType().contains("外借")) {
                String jyStartDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJystartdate());
                String jyEdnDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJyedndate());
                yyjyqbLcPage.getRecords().get(i).setWjDate(jyStartDate + "至" + jyEdnDate);
            }
        }
        return ResponseEntity.ok(yyjyqbLcPage);
    }

    /**
     * 查询页
     *
     * @param CID
     * @return
     */
    @GetMapping("/querylist")
    public ResponseEntity<Page<VYyjyqbLc>> GetquerydataList(String CID) {
        Page<VYyjyqbLc> page = getPage();
        QueryWrapper<VYyjyqbLc> queryWrapper = new QueryWrapper<>();

        if (person.getRoleNamesString().contains("财务打印管理")) {
            queryWrapper.eq("TopGroupID", person.getTopGroupId());
        } else {
            queryWrapper.and(i -> i.like("AllPersonZgh", person.getLoginName()));
        }
        if (person.getRoleNamesString().contains("印章保管人")) {
            queryWrapper = new QueryWrapper<VYyjyqbLc>().and(i -> i.eq("TopGroupID", person.getTopGroupId()).or().eq("yySendGroupID", person.getTopGroupId()));
        }

        if (CID != null && CID != "") {
            queryWrapper.and(i -> i.like("bh", CID).or().like("type", CID).or().like("yySend", CID).or().like("Yysy", CID));
        }
        queryWrapper.orderByDesc("id");
        Page<VYyjyqbLc> yyjyqbLcPage = vYyjyqbLcService.page(page, queryWrapper);
        SimpleDateFormat timeformate = new SimpleDateFormat("yyyy/MM/dd");
        for (int i = 0; i < yyjyqbLcPage.getRecords().size(); i++) {
            if (yyjyqbLcPage.getRecords().get(i).getType().contains("外借")) {
                String jyStartDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJystartdate());
                String jyEdnDate = timeformate.format(yyjyqbLcPage.getRecords().get(i).getJyedndate());
                yyjyqbLcPage.getRecords().get(i).setWjDate(jyStartDate + "至" + jyEdnDate);
            }
        }
        return ResponseEntity.ok(yyjyqbLcPage);
    }

    /**
     * 统计页
     *
     * @param Svalue
     * @param Evalue
     * @return
     */
    @GetMapping("/TJlist")
    public AjaxResult GetTJdataList(String Svalue, String Evalue) {
        //TODO 用印待优化代码
        AjaxResult ajax = null;
        SqlHelper sqlhelper = new SqlHelper();

        PersonEntity person = SessionHelper.getSessionPerson();
        if (person == null) {
            ajax = AjaxResult.error("未发现登录信息！");
            return ajax;
        }

        String sql = "SELECT g.groupname as GroupName,g.XH,yy.TopGroupID as GroupID,";
        sql += " SUM(case when yy.lc_jdid is null then 1 else 0 end) as CGNumb,";
        sql += " SUM(case when yy.lc_jdid>0 then 1 else 0 end) as ZTNumb,";
        sql += " SUM(case when yy.lc_jdid=0 then 1 else 0 end) as GDNumb,";
        sql += " SUM(case when yy.lc_jdid=-1 then 1 else 0 end) as ZFNumb,";
        sql += " COUNT(*) as HJNumb FROM";

        if (Svalue == null && Evalue == null) {
            sql += " V_Yyjyqb_LC yy";
        } else {
            String st = "";
            String et = "";
            if (Svalue == null) {
                st = "2000/1/01 00:00:00";
            } else {
//                st = Convert.ToDateTime(Svalue).ToString("yyyy/MM/dd");
                st = Svalue;
                st += " 00:00:00";
            }
            if (Evalue == "" || Evalue == null) {
//                et = DateTime.Now.ToString("yyyy/MM/dd");
                SimpleDateFormat timeformate = new SimpleDateFormat("yyyy/MM/dd");
                LocalDate localDate = LocalDate.now();
                et = LocalDate.now().toString();
                et += " 00:00:00";
            } else {
//                et = Convert.ToDateTime(Evalue).ToString("yyyy/MM/dd");
                et = Evalue;
                et += " 00:00:00";
            }

            sql += " (select * from V_Yyjyqb_LC vy";
            sql += " where vy.Time>='" + st + "' and vy.Time<='" + et + "') yy ";
        }
        sql += " inner join GroupItem g on yy.TopGroupID=g.id group by yy.TopGroupID,g.groupname,g.XH";
        List<yybb> list = sqlhelper.GetObjectList(yybb.class, sql);
        ajax = AjaxResult.success("获取信息成功!");
        ajax.put("list", list);
        return ajax;
    }

    /**
     * 判断外借期限是否重复
     *
     * @param entity
     * @param IsBack
     * @return
     */
    private String GetResultToNext(YyjyqbLc entity, String IsBack) {
        SqlHelper sqlhelper = new SqlHelper();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        String sql = "";
        String sDate = dateFormat.format(entity.getJystartdate());
        if (entity.getSdatequantum() == "上午") {
            sDate += " 08:00:00";
        } else {
            sDate += " 14:00:00";
        }
        String eDate = dateFormat.format(entity.getJyedndate());

        if (entity.getEdatequantum() == "上午") {
            eDate += " 08:00:00";
        } else {
            eDate += " 14:00:00";
        }
        DateTime dateStart = new DateTime(sDate);
        DateTime dateEnd = new DateTime(eDate);

        sql += "select * from ( ";
        sql += " SELECT id  from V_Yyjyqb_LC yl where lc_jdid is not null and type ='" + entity.getType() + "' and yySendGroupID = " + entity.getYysendgroupid();
        sql += " and lc_jdid>0";
        if (IsBack == "yes") {
            sql += " and yl.id!=" + entity.getId();
        }
        sql += " and DATEADD(hh,case sDateQuantum when '上午' then +8 else +14 end,jyStartDate) <='" + dateEnd + "' ) a,";
        sql += " ( SELECT id from V_Yyjyqb_LC yl where lc_jdid is not null and type ='" + entity.getType() + "' and yySendGroupID = " + entity.getYysendgroupid();
        sql += " and lc_jdid>0";
        if (IsBack == "yes") {
            sql += " and yl.id!=" + entity.getId();
        }
        sql += " and DATEADD(hh,case eDateQuantum when '上午' then +8 else +14 end,jyEdnDate) >='" + dateStart + "' ) b";
        sql += " where a.id = b.id";

        List<yybb> list = sqlhelper.GetObjectList(yybb.class, sql);
        if (list.size() > 0) {
            return "您预约的外借期限已被占用，请修改时间！";
        }
        return "true";
    }

    /**
     *
     */
    public void sendPersonSMS(boolean flag, String personzgh, String ywid, String lc_jdid) {
        YyjyqbLc ent = yyjyqbLcService.getById(ywid);
        if (personzgh.isEmpty()) {
            //获取填报人的gh
            LambdaQueryWrapper<LcWorkflow> lw = new LambdaQueryWrapper<>();
            lw.and(i -> i.eq(LcWorkflow::getYwid, ywid).eq(LcWorkflow::getLcJdid, 910201));
            List<LcWorkflow> list = lcWorkflowService.list(lw);
            personzgh = list.get(0).getPersonzgh();
        }
        LambdaQueryWrapper<Person> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Person::getLoginname, personzgh);
        String telephone = personService.getOne(wrapper).getTelephone();

        if (flag) {
            JTGS_SendPersonSMS(telephone, ent, lc_jdid);
        } else {
            backSendSMS(telephone, ent);
        }
    }

    /**
     * 发送短信
     * TopGroupID == 373走这个
     */
    private void JTGS_SendPersonSMS(String telephone, YyjyqbLc ent, String lc_jdid) {
        String strNr = ent.getApplicant() + "向您发起用印申请，请审核。";
        if ("0".equals(lc_jdid)) {
            strNr = "您的用印申请已批准，请及时办理。";
        }
        SendSMSUtils.sendSms(telephone, strNr);
    }

    /**
     *
     */
    private void backSendSMS(String telephone, YyjyqbLc ent) {
        String str = "您呈送至" + ent.getYysend() + "的用印申请已被驳回，请在用印填写界面查看。";
        SendSMSUtils.sendSms(telephone, str);
    }
}
