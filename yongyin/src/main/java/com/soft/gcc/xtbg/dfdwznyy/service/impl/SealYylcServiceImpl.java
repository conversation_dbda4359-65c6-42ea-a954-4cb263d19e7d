package com.soft.gcc.xtbg.dfdwznyy.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dfdwznyy.base.Result;
import com.soft.gcc.xtbg.dfdwznyy.entity.Fingerprint;
import com.soft.gcc.xtbg.dfdwznyy.entity.SealYylc;
import com.soft.gcc.xtbg.dfdwznyy.mapper.SealYylcMapper;
import com.soft.gcc.xtbg.dfdwznyy.service.ISealYylcService;
import com.soft.gcc.xtbg.dfdwznyy.utils.AesUtils;
import com.soft.gcc.xtbg.dfdwznyy.utils.ZNYYHttpUtil;
import com.soft.gcc.xtbg.dfdwznyy.utils.ZNYYParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_ZNYY_SEAL_YYLC(用印流程关联印章信息)】的数据库操作Service实现
* @createDate 2023-07-17 11:00:44
*/
@Service
public class SealYylcServiceImpl extends ServiceImpl<SealYylcMapper, SealYylc>
    implements ISealYylcService {

    private static final Logger log = LoggerFactory.getLogger(SealYylcServiceImpl.class);

    @Override
    public Result postYXYG03_01(String orderNum, int ywid, String bizID, String uuid, int yyCount) {

        String params = "{" +
                "\"id\":\""+orderNum+"\"," +
                "\"title\":\"申请单编号:"+orderNum+"\"," +
                "\"content\":\"申请用印\"," +
                "\"uuid\":\""+uuid+"\"," +
                "\"contractCopiesNum\":1," +
                "\"contractCopiesCount\":"+yyCount+"," +
                "\"typeId\":10001," +
                "\"applicantId\":\"liyb\"," +
                "\"usePeopleId\":\"liyb\"" +
                "}";
        log.info("用印管理,提交申请单，YXYG03_07,参数："+params);
        //加密參數
        Object ZMYYParam;
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,提交申请单，YXYG03_07,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        //構造請求
//        RequestJsonBase req = new RequestJsonBase();
//        req.setAppKey(ZNYYParam.appKey);
//        req.setApiKey("YXYG03_07");
//        req.setBizId(bizID);
//        req.setTimestamp(new Date().getTime());
//        req.setTenant(ZNYYParam.tenant);
//        req.setParams(params);

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG03_07",bizID,params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            return Result.ok("申请电子章成功,信息："+map.get("message"));

        }

        return Result.error("失败:"+map.get("message"));
    }

    @Override
    public Result postYXYG03_03(String orderNum, int ywid, String bizId,String uuid) {

        String params = "{" +
                "\"id\":\""+orderNum+"\"," +
                "\"title\":\"申请单编号:"+orderNum+"\"," +
                "\"content\":\"申请用印\"," +
                "\"uuid\":\""+uuid+"\"," +
                "\"contractCopiesNum\":1," + //如果是推送已经保存的申请单，这两个字段好像没意义
                "\"contractCopiesCount\":1," + //如果是推送已经保存的申请单，这两个字段好像没意义
                "\"typeId\":10001," +
                "\"applicantId\":\"liyb\"," +
                "\"usePeopleId\":\"liyb\"" +
                "}";
        log.info("用印管理,推送用印，YXYG03_03,参数："+params);
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,推送用印，YXYG03_03,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        //構造請求
//        RequestJsonBase req = new RequestJsonBase();
//        req.setAppKey(ZNYYParam.appKey);
//        req.setApiKey("YXYG03_03");
//        req.setBizId(bizId);
//        req.setTimestamp(new Date().getTime());
//        req.setTenant(ZNYYParam.tenant);
//        req.setParams(params);

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG03_03",bizId,params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            return Result.ok(map.get("message"));

        }

        return Result.error("失败："+map.get("message"));
    }

    @Override
    public List<SealYylc> getSealYYLCList(Integer lcId) {
        return baseMapper.getSealYYLCList(lcId);
    }

    @Override
    public Result addZNYYPerson(String znyyId, String znyyName) {

        String params = "[{" +
                "\"id\":\""+znyyId+"\"," +
                "\"name\":\""+znyyName+"\"" +
                "}]";
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            e.printStackTrace();
        }

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG01_08", UUID.randomUUID().toString(),params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            return Result.ok(map.get("message"));

        }

        return Result.error("失败："+map.get("message"));
    }

    @Override
    public Result postYXYG02_08(boolean lock, String bizId, String uuid) {
        String params = "{" +
                "\"uuid\":\""+uuid+"\"," +
                "\"operatorId\":\"liyb\"," +
                "\"lock\":" + lock +
                "}";
        log.info("用印管理,推送用印，YXYG02_08,参数："+params);
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,推送用印，YXYG02_08,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        //構造請求
//        RequestJsonBase req = new RequestJsonBase();
//        req.setAppKey(ZNYYParam.appKey);
//        req.setApiKey("YXYG02_08");
//        req.setBizId(bizId);
//        req.setTimestamp(new Date().getTime());
//        req.setTenant(ZNYYParam.tenant);
//        req.setParams(params);

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG02_08",bizId,params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            return Result.ok(map.get("message"));

        }

        return Result.error("失败："+map.get("message"));
    }

    @Override
    public Result postYXYG02_07(String uuid, String znyyUserId, int mode) {
        String params = "{" +
                "\"uuid\":\""+uuid+"\"," +
                "\"operatorId\":\"liyb\"," +
                "\"userId\":\""+znyyUserId+"\"," +
                "\"mode\":\""+mode+"\"" +
                "}";
        log.info("用印管理,修改指纹，YXYG02_07,参数："+params);
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,修改指纹，YXYG02_07,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG02_07",params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            Result result = Result.ok(map.get("message"));

            return result;

        }

        return Result.error("失败："+map.get("message"));
    }

    @Override
    public Result postYXYG04_05(int pageNum, int pageSize, String uuid, String userName) {

        String params = "{" +
                "\"pageNum\":\""+pageNum+"\"," +
                "\"pageSize\":\""+pageSize+"\"," +
                "\"uuids\":[\""+uuid+"\"]" +
                "}";

        if (StringUtils.isNotEmpty(userName)){
            params = "{" +
                    "\"pageNum\":\""+pageNum+"\"," +
                    "\"pageSize\":\""+pageSize+"\"," +
                    "\"userName\":\""+userName+"\"," +
                    "\"uuids\":[\""+uuid+"\"]" +
                    "}";
        }


        log.info("用印管理,修改指纹，YXYG04_05,参数："+params);
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,修改指纹，YXYG04_05,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG04_05",params);

        JSONObject jsonObject = JSON.parseObject(resultStr);

        //提交請求
        Map<String,String> map = (Map) jsonObject;

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            if (jsonObject.containsKey("data")) {

                JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("list");
                List<Fingerprint> fingerprintList = JSON.parseArray(jsonArray.toJSONString(), Fingerprint.class);
                return Result.ok(fingerprintList);

            } else {
                return Result.ok("暂无数据");
            }
        }

        return Result.error("失败："+map.get("message"));

    }

    @Override
    public Result postYXYG02_05(String uuid, String ssid, String pass, boolean connect) {

        String params = "{" +
                "\"uuid\":\""+uuid+"\"," +
                "\"operatorId\":\"liyb\"," +
                "\"ssid\":\""+ssid+"\"," +
                "\"pass\":\""+pass+"\"," +
                "\"connect\":\""+connect+"\"" +
                "}";
        log.info("用印管理,设置印章设备断开或连接WiFi,YXYG02_05,参数："+params);
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,设置印章设备断开或连接WiFi,YXYG02_05,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG02_05",params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            return Result.ok(map.get("message"));

        }

        return Result.error("失败："+map.get("message"));
    }

    @Override
    public Result postYXYG02_04(String uuid) {
        String params = "{" +
                "\"uuid\":\""+uuid+"\"," +
                "\"operatorId\":\"liyb\"" +
                "}";
        log.info("用印管理,开启物理章锁,YXYG02_04,参数："+params);
        //加密參數
        try{
            params  = AesUtils.encrypt(params, ZNYYParam.appSecret);
        }catch (Exception e){
            log.error("用印管理,开启物理章锁,YXYG02_04,参数加密报错。"+e.getMessage());
            return Result.error("参数加密报错");
        }

        String resultStr = ZNYYHttpUtil.znyyPostJSON("YXYG02_04",params);

        //提交請求
        Map<String,String> map = JSON.parseObject(resultStr,Map.class);

        if ("0".equals(map.get("code")) || ((Integer) 0).equals(map.get("code"))) {

            return Result.ok(map.get("message"));

        }

        return Result.error("失败："+map.get("message"));
    }


    @Override
    public boolean isExceedTime(Date nowDate, LocalDateTime qrsj, String hourStr) {

//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
//
//        Date qrDate = new Date();
//
//        try {
//            qrDate = sdf.parse(qrsj);
//        }catch (Exception e){
//            log.error("用印管理,提交申请单，ElectronicSealController->createApply.初始化："+qrsj+",失败");
//            return true;
//        }

        //还剩多少时间
        Long nowTime = nowDate.getTime();
        Long qrTime = qrsj.toInstant(ZoneOffset.of("+8")).toEpochMilli();

        Long shenYuTime = nowTime - qrTime;

        Long hour = shenYuTime/3600000L;

        if (hour >= Long.valueOf(hourStr)){
            return true;
        }

        return false;
    }

    @Override
    public boolean isExceedTimeToStr(Date nowDate, String qrsj, String hourStr) throws Exception{

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

        Date qrDate = new Date();

        try {
            qrDate = sdf.parse(qrsj);
        }catch (Exception e){
            log.error("用印管理,提交申请单，ElectronicSealController->createApply.初始化："+qrsj+",失败");
            return true;
        }

        //还剩多少时间
        Long nowTime = nowDate.getTime();
        Long qrTime = qrDate.getTime();

        Long shenYuTime = nowTime - qrTime;

        Long hour = shenYuTime/3600000L;

        if (hour >= Long.valueOf(hourStr)){
            return true;
        }

        return false;
    }

    @Override
    public String yytime2String(Date yytime, String ShangwuOrXiawu) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String jssj = sdf.format(yytime);

        if ("上午".equals(ShangwuOrXiawu)) {
            jssj = jssj + " 12:00:00";
        }else if ("下午".equals(ShangwuOrXiawu)) {
            jssj = jssj + " 23:59:59";
        }

        return jssj;

    }
}




