package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* 用印流程关联印章信息
* @TableName DFDW_T_ZNYY_SEAL_YYLC
*/
@TableName(value ="DFDW_T_ZNYY_SEAL_YYLC")
@Data
public class SealYylc implements Serializable {

    public SealYylc() {

    }

    public SealYylc(Integer yylcId,String sealName,int sealId,String orderNum
            ,Integer useCount,Integer orderType,String bizId,String deviceUuid,Integer yyCount) {
        this.yylc_id= yylcId;
        this.seal_name = sealName;
        this.seal_id = sealId;
        this.order_num = orderNum;
        this.use_count = useCount;
        this.order_type = orderType;
        this.biz_id = bizId;
        this.device_uuid = deviceUuid;
        this.yy_count = yyCount;
    }

    /**
    * 主键：
    */
    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;


    /**
    * yyjyqb_lc的id
    */
    @TableField(value = "yylc_id")
    @JSONField(name = "yylc_id")
    private Integer yylc_id;

    /**
    * 章名称
    */
    @TableField(value = "seal_name")
    @JSONField(name = "seal_name")
    private String seal_name;

    /**
    * 章id
    */
    @TableField(value = "seal_id")
    @JSONField(name = "seal_id")
    private Integer seal_id;

    /**
    * 使用次数
    */
    @TableField(value = "use_count")
    @JSONField(name = "use_count")
    private Integer use_count = 0;

    /**
    * 订单号
    */
    @TableField(value = "order_num")
    @JSONField(name = "order_num")
    private String order_num;

    /**
     * 申请单状态 0不可使用（已关闭或者没申请成功） 1可使用
     */
    @TableField(value = "order_type")
    @JSONField(name = "order_type")
    private Integer order_type;

    /**
     * 申请状态 0发送申请失败 1发送申请成功
     */
    @TableField(value = "apply_type")
    @JSONField(name = "apply_type")
    private Integer apply_type;

    /**
     * 智能平台业务id
     */
    @TableField(value = "biz_id")
    @JSONField(name = "biz_id")
    private String biz_id;

    /**
     * 授权状态 0没授权 1已授权
     */
    @TableField(value = "empower_type")
    @JSONField(name = "empower_type")
    private int empower_type = 0;

    /**
     * 章筒uuid
     */
    @TableField(value = "device_uuid")
    @JSONField(name = "device_uuid")
    private String device_uuid;

    /**
     * 章筒uuid
     */
    @TableField(value = "yy_count")
    @JSONField(name = "yy_count")
    private Integer yy_count;

    /**
     * 是否显示授权按钮
     */
    @TableField(exist = false)
    private int empowerView;

    /**
     * 是否显示开锁按钮
     */
    @TableField(exist = false)
    private int openLockView;

    /**
     * 设备锁定（0未锁定 1锁定）
     */
    @TableField(exist = false)
    private int lock;

    /**
     * 最后使用时间
     */
    @TableField(exist = false)
    private LocalDateTime lastUseTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
