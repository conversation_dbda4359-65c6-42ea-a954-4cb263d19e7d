package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@TableName("DictionaryValue")
@Data
public class DictionaryValue implements Serializable {

    private static final long serialVersionUID=1L;

    @TableField("IsUsed")
    private Integer IsUsed;

    @TableField("Content")
    private String Content;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("Parameter")
    private String Parameter;

    @TableField("TitleID")
    private Integer TitleID;

}
