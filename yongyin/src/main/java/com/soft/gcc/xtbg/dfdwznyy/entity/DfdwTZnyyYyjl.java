package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用印记录
 * <AUTHOR>
 * @TableName DFDW_T_ZNYY_YYJL
 */
@TableName(value ="DFDW_T_ZNYY_YYJL")
@Data
public class DfdwTZnyyYyjl implements Serializable {
    /**
     * 用印记录id(印管系统内部的业务标识)
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 租户标识
     */
    @TableField(value = "tenant")
    private String tenant;

    /**
     * 组织id
     */
    @TableField(value = "deptId")
    private String deptId;

    /**
     * 组织名称
     */
    @TableField(value = "deptName")
    private String deptName;

    /**
     * 用印时间
     */
    @TableField(value = "useTime")
    private LocalDateTime useTime;

    /**
     * 用印人id(useId为-2代表密码管章人解锁用印)
     */
    @TableField(value = "userId")
    private String userId;

    /**
     * 用印人
     */
    @TableField(value = "userName")
    private String userName;

    /**
     * 设备标识
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 设备名称
     */
    @TableField(value = "deviceName")
    private String deviceName;

    /**
     * 申请单id
     */
    @TableField(value = "applicationId")
    private String applicationId;

    /**
     * 用印次序(印章设备用印次序依次递增)
     */
    @TableField(value = "useCount")
    private Integer useCount;

    /**
     * -1:异常 0:正常
     */
    @TableField(value = "error")
    private Integer error;

    /**
     * 异常描述
     */
    @TableField(value = "errorDesc")
    private String errorDesc;

    /**
     * 用印模式(如:审批模式、指纹模式、密码模式、静默模式等)
     */
    @TableField(value = "model")
    private String model;

    /**
     * 摄像头开关
     */
    @TableField(value = "camera")
    private Boolean camera;

    /**
     * 用印地址
     */
    @TableField(value = "location")
    private String location;

    /**
     * 用印视频url
     */
    @TableField(exist = false)
    private String videoFileUrl;

    /**
     * 人脸图片url
     */
    @TableField(exist = false)
    private String faceFileUrl;

    /**
     * 用印图片url
     */
    @TableField(exist = false)
    private String useFileUrl;

    /**
     * 审计图片
     */
    @TableField(exist = false)
    private List<String> auditFileUrls;

    /**
     * 超时按压图片
     */
    @TableField(exist = false)
    private List<String> timeoutFileUrls;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
