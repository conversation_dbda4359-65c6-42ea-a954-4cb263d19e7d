package com.soft.gcc.xtbg.dfdwznyy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.soft.gcc.xtbg.dfdwznyy.entity.SealYylc;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_ZNYY_SEAL_YYLC(用印流程关联印章信息)】的数据库操作Mapper
* @createDate 2023-07-17 11:00:44
* @Entity com.yykj.app.dfdwznyy.entity.SealYylc
*/
public interface SealYylcMapper extends BaseMapper<SealYylc> {

    List<SealYylc> getSealYYLCList(@Param("lcId") Integer lcId);
}




