package com.soft.gcc.xtbg.dfdwznyy.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.soft.gcc.xtbg.dfdwznyy.entity.Seal;
import com.soft.gcc.xtbg.dfdwznyy.mapper.SealMapper;
import com.soft.gcc.xtbg.dfdwznyy.service.ISealService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_ZNYY_SEAL(印章管理)】的数据库操作Service实现
* @createDate 2023-07-17 11:01:22
*/
@Service
public class SealServiceImpl extends ServiceImpl<SealMapper, Seal>
    implements ISealService {

}




