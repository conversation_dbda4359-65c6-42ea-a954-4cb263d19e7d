package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* 印章管理
* @TableName DFDW_T_ZNYY_SEAL
*/
@TableName(value ="DFDW_T_ZNYY_SEAL")
@Data
public class Seal implements Serializable {

    /**
    * 主键：
    */
    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;


    /**
    * 印章名称
    */
    @TableField(value = "seal_name")
    @JSONField(name = "seal_name")
    private String sealName;

    /**
    * 章筒uuid
    */
    @TableField(value = "device_uuid")
    @JSONField(name = "device_uuid")
    private String deviceUuid;

    /**
     * 锁定（0 未锁定 1锁定）
     */
    @TableField(value = "lock")
    @JSONField(name = "lock")
    private Integer lock;

    @TableField(value = "top_group_id")
    @JSONField(name = "top_group_id")
    private Integer topGroupId;

    @TableField(exist = false)
    @JSONField(name = "group_name")
    private String groupName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
