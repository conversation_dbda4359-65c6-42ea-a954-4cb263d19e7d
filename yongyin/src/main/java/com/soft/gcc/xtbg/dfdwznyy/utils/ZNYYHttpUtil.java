package com.soft.gcc.xtbg.dfdwznyy.utils;

import com.alibaba.fastjson.JSON;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.RequestJsonBase;

import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ZNYYHttpUtil {

    /**
     * 智能用印通用请求
     * @param apiKey    目标接口，参阅文档
     * @param bizId     接口标识，参阅文档
     * @param params        参数，参阅文档
     * @return          返回用印平台信息
     */
    public static String znyyPostJSON(String apiKey,String bizId,String params){

        //構造請求
        RequestJsonBase req = new RequestJsonBase();
        req.setAppKey(ZNYYParam.appKey);
        req.setApiKey(apiKey);
        req.setBizId(bizId);
        req.setTimestamp(new Date().getTime());
        req.setTenant(ZNYYParam.tenant);
        req.setParams(params);

        String resultStr = HttpClientUtils.doPostJson(ZNYYParam.apiUrl, JSON.toJSONString(req));

        return resultStr;

    }

    /**
     * 智能用印通用请求
     * @param apiKey    目标接口，参阅文档
     * @param params        参数，参阅文档
     * @return          返回用印平台信息
     */
    public static String znyyPostJSON(String apiKey,String params){

        return znyyPostJSON(apiKey,UUID.randomUUID().toString(),params);

    }

}
