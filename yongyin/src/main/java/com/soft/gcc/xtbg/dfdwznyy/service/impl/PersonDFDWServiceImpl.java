package com.soft.gcc.xtbg.dfdwznyy.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.gcc.xtbg.dfdwznyy.entity.Person;
import com.soft.gcc.xtbg.dfdwznyy.mapper.PersonDFDWMapper;
import com.soft.gcc.xtbg.dfdwznyy.service.IDFDWPersonService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 *  服务实现类
 * </p>
 */
@Service("IDFDWPersonService")
@CacheConfig
public class PersonDFDWServiceImpl extends ServiceImpl<PersonDFDWMapper, Person> implements IDFDWPersonService {


    @Override
    public List<String> getUserRoleName(Integer personId) {
        return this.baseMapper.getUserRoleName(personId);
    }

}
