<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dfdwznyy.mapper.SealYylcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dfdwznyy.entity.SealYylc">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="yylcId" column="yylc_id" jdbcType="INTEGER"/>
        <result property="sealName" column="seal_name" jdbcType="VARCHAR"/>
        <result property="sealId" column="seal_id" jdbcType="INTEGER"/>
        <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
        <result property="useCount" column="use_count" jdbcType="INTEGER"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="bizId" column="biz_id" jdbcType="VARCHAR"/>
        <result property="deviceUuid" column="device_uuid" jdbcType="VARCHAR"/>
        <result property="yyCount" column="yy_count" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getSealYYLCList" resultType="com.soft.gcc.xtbg.dfdwznyy.entity.SealYylc">
        select yylc.*, DTZS.lock from DFDW_T_ZNYY_SEAL_YYLC yylc
        left join DFDW_T_ZNYY_SEAL DTZS on yylc.seal_id = DTZS.id
        where yylc.yylc_id = #{lcId}
    </select>
</mapper>
