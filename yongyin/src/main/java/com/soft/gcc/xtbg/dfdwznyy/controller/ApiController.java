package com.soft.gcc.xtbg.dfdwznyy.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.soft.framework.config.BaseConfig;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.RequestJsonBase;
import com.soft.gcc.xtbg.dfdwznyy.service.DfdwTZnyyYyjlService;
import com.soft.gcc.xtbg.dfdwznyy.utils.AesUtils;
import com.soft.gcc.xtbg.dfdwznyy.utils.FileUtil;
import com.soft.gcc.xtbg.dfdwznyy.utils.HttpClientUtils;
import com.soft.gcc.xtbg.dfdwznyy.utils.ZNYYParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.UUID;

@RestController
@Configuration
@RequestMapping(value="/znyy/callback/api" )
@Api(tags ="智能用印测试API->测试接口，API文檔：https://md.qstamper.com/admin/#/pages/doc/docShare/docShare?bookId=63bf59cef5cf3a165a2224db")
public class ApiController extends BaseConfig {
    @Autowired
    DfdwTZnyyYyjlService dfdwTZnyyYyjlService;

    private String apiUrl = ZNYYParam.apiUrl;// "http://60.190.12.134:18585/stamper/apis/business/docking/execute";
    private String appKey = ZNYYParam.appKey;// "szyyYongYin";
    private String appSecret = ZNYYParam.appSecret;// "szyyYongYin123qwe";
    private String tenant = ZNYYParam.tenant;//"szyyYongYin";

//    TEST
//    private String apiUrl = "https://test.qstamper.com:8036/stamper/apis/business/docking/execute";
//    private String appKey = "dfdwtest";
//    private String appSecret = "Q4XGBZ8cQmufRl21";
//    private String tenant = "dfdwtest";

    @RequestMapping(value="/proxy")
//    @ResponseBody
//    @ApiOperation(value ="proxy",notes="proxy接口，如：POST:/app/Service/ZNYY/apitest/proxy?apiKey=YXYG04_01 JSON: {\"pageNum\":1,\"pageSize\":10}")
    public ResponseEntity proxy(@RequestBody String params,
                          @RequestParam(value = "apiKey") String apiKey) throws Exception {
        //加密參數
        params  = AesUtils.encrypt(params,appSecret);
        //構造請求
        RequestJsonBase req = new RequestJsonBase();
        req.setAppKey(appKey);
        req.setApiKey(apiKey);
        req.setBizId(UUID.randomUUID().toString());
        req.setTimestamp(new Date().getTime());
        req.setTenant(tenant);
        req.setParams(params);
        //提交請求
        JSONObject result = JSON.parseObject(HttpClientUtils.doPostJson(apiUrl,JSON.toJSONString(req)));

       return ResponseEntity.ok(result);
    }

    @RequestMapping(value="/callback")
    @ResponseBody
    @ApiOperation(value ="callback",notes="用于印章系統回調")
    public ResponseEntity callback(@RequestBody String jsonBody) throws Exception {

        RequestJsonBase json = JSON.parseObject(jsonBody,RequestJsonBase.class);
        String apiKey = json.getApiKey();
        String params =  AesUtils.decrypt(json.getParams(),appSecret);
        json.setParams(params);
        SaveLog(json);
        switch (apiKey){
            case "YXYG03_06": //用印次数
                break;
            case "YXYG01_01": //同步组织
                break;
            case "YXYG01_03": //同步用户
                break;
            case "YXYG04_02": //用印记录
                dfdwTZnyyYyjlService.saveJson(json);
                break;
            case "YXYG04_09": //人脸图片
                break;
            case "YXYG04_10": //用印视频
                break;
            case "YXYG03_09": //用印异常
                break;
            default:
                break;
        }
        return ResponseEntity.ok("");
    }

    private void SaveLog(RequestJsonBase json){
        String fileName = String.format("httplogs/%s_%s.txt",json.getApiKey() ,json.getBizId());
        FileUtil.makeDirectory(fileName);
        FileUtil.SaveFileAs(json.getParams(),fileName);
    }

}
