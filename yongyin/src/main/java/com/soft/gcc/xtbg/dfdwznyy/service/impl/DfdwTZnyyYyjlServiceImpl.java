package com.soft.gcc.xtbg.dfdwznyy.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.soft.framework.helper.OSSHelper;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.common.t_file.service.TFileService;
import com.soft.gcc.xtbg.dfdwznyy.base.Result;
import com.soft.gcc.xtbg.dfdwznyy.entity.DfdwTZnyyYyjl;
import com.soft.gcc.xtbg.dfdwznyy.entity.Seal;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.RequestJsonBase;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.TFileDto;
import com.soft.gcc.xtbg.dfdwznyy.entity.param.ZnyyYyjlParam;
import com.soft.gcc.xtbg.dfdwznyy.mapper.DfdwTZnyyYyjlMapper;
import com.soft.gcc.xtbg.dfdwznyy.service.DfdwTZnyyYyjlService;
import com.soft.gcc.xtbg.dfdwznyy.service.ISealService;
import com.soft.gcc.xtbg.dfdwznyy.utils.AliyunOSSUtils;
import com.soft.gcc.xtbg.dfdwznyy.utils.ImgUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【DFDW_T_ZNYY_YYJL(用印记录)】的数据库操作Service实现
 * @createDate 2023-07-17 10:06:32
 */
@Service
public class DfdwTZnyyYyjlServiceImpl extends ServiceImpl<DfdwTZnyyYyjlMapper, DfdwTZnyyYyjl>
        implements DfdwTZnyyYyjlService {
    private static final Logger log = LoggerFactory.getLogger(DfdwTZnyyYyjlServiceImpl.class);
    @Value("${jplat.OssLocalPath}")
    String downloadPath;
    @Autowired
    TFileService tFileService;
    @Autowired
    ISealService sealService;
    @Autowired
    ImgUtils imgUtils;

    OSSHelper ossHelper = new OSSHelper();

    @Override
    public void saveJson(@NotNull RequestJsonBase json) {
        String localPath = downloadPath + "/Upload/FileManage/znyy/";
        DfdwTZnyyYyjl yyjl = JSON.parseObject(json.getParams(), DfdwTZnyyYyjl.class);
        try {
            yyjl.setTenant(json.getTenant());
            this.saveOrUpdate(yyjl);
            List<TFile> tFiles = new ArrayList<>();
            // 用印视频url
//            if (StringUtils.isNotEmpty(yyjl.getVideoFileUrl())) {
//                AddFile(localPath, yyjl, tFiles, yyjl.getVideoFileUrl(), "vidoe");
//                DeleteFile(yyjl, "vidoe");
//            }
            // 人脸图片url
            if (StringUtils.isNotEmpty(yyjl.getFaceFileUrl())) {
                AddFile(localPath, yyjl, tFiles, yyjl.getFaceFileUrl(), "face");
                DeleteFile(yyjl, "face");
            }
            // 用印图片url
            if (StringUtils.isNotEmpty(yyjl.getUseFileUrl())) {
                AddFile(localPath, yyjl, tFiles, yyjl.getUseFileUrl(), "user");
                DeleteFile(yyjl, "user");
            }
            // 审计图片url
//            if (yyjl.getAuditFileUrls() != null) {
//                for (String url : yyjl.getAuditFileUrls()) {
//                    AddFile(localPath, yyjl, tFiles, url, "audit");
//                    DeleteFile(yyjl, "audit");
//                }
//            }
            // 超时按压图片url
//            if (yyjl.getTimeoutFileUrls() != null) {
//                for (String url : yyjl.getTimeoutFileUrls()) {
//                    AddFile(localPath, yyjl, tFiles, url, "timeout");
//                    DeleteFile(yyjl, "tiemout");
//                }
//            }
            baseMapper.saveBatchTFile(tFiles);
        } catch (Exception e) {
            log.error(">>>智能用印申请单{}使用记录新增错误：{}", yyjl.getApplicationId(), e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Result getListYYJL(ZnyyYyjlParam yyjl) {
        String localPath = downloadPath + "/Upload/FileManage/znyy/";
        try {
            IPage<TFileDto> list = new Page<>();
            list.setCurrent(yyjl.getPageNum());
            list.setSize(yyjl.getPageSize());
            if (StringUtils.isNotEmpty(yyjl.getUuid())) {
                return Result.ok(baseMapper.getFileList(list, yyjl));
            }
            if (yyjl.getSealId() == null) {
                return Result.error("无对应章，请先确认是否存在！");
            }
            List<Seal> seals = sealService.list(new LambdaQueryWrapper<Seal>().eq(Seal::getId, yyjl.getSealId()));
            if (seals.size() > 0) {
                yyjl.setUuid(seals.get(0).getDeviceUuid());
                list = baseMapper.getFileList(list, yyjl);
//                for (TFileDto file : list.getRecords()) {
//                    // 如果没有则去补上
//                    if (!AliyunOSSUtils.existsByUrl(file.getFilepath())) {
//                        LocalDateTime date = file.getUseTime();
//                        String url = "http://************:18585/group1/traffic/" + date.getYear() + "/" + date.getMonthValue() + "/" + date.getDayOfMonth() + "/" + file.getFilename();
//                        try {
//                            imgUtils.downloadImagAndUploadToOss(url, localPath);
//                        } catch (Exception ex) {
//                            log.error("用印人脸图片读取失败，地址：{}，原因：{}", url, ex.getMessage());
//                        }
//                    }
//                }
                return Result.ok(list);
            } else {
                return Result.error("无对应章，请先确认是否存在！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(e.getMessage());
        }
    }

    @Override
    public Result getUsageRecord(ZnyyYyjlParam yyjl) {
        try {
            IPage<DfdwTZnyyYyjl> list = new Page<>();
            list.setCurrent(yyjl.getPageNum());
            list.setSize(yyjl.getPageSize());
            list = this.page(list, new LambdaQueryWrapper<DfdwTZnyyYyjl>()
                    .and(yyjl.getSearchUsage() != null, t -> t
                            .like(DfdwTZnyyYyjl::getUserName, yyjl.getSearchUsage())
                            .or()
                            .like(DfdwTZnyyYyjl::getApplicationId, yyjl.getSearchUsage())
                    )
                    .eq(yyjl.getModel() != null, DfdwTZnyyYyjl::getModel, yyjl.getModel())
                    .eq(yyjl.getError() != null, DfdwTZnyyYyjl::getError, yyjl.getError())
                    .eq(DfdwTZnyyYyjl::getUuid, yyjl.getUuid())
                    .orderByDesc(DfdwTZnyyYyjl::getUseCount)
            );
            return Result.ok(list);
        } catch (Exception ex) {
            log.error("获取设备使用记录报错：{}", ex.getMessage());
            return Result.error(ex.getMessage());
        }
    }

    private void AddFile(String localPath, @NotNull DfdwTZnyyYyjl yyjl, @NotNull List<TFile> tFiles, String url, String hjID) throws Exception {
        imgUtils.downloadImagAndUploadToOss(url, localPath);
        TFile tFile = new TFile();
        tFile.setFilename(Arrays.stream(url.split("/")).reduce((first, second) -> second).orElse(null));
        tFile.setFilepath("Upload/FileManage/znyy/" + Arrays.stream(url.split("/")).reduce((first, second) -> second).orElse(null));
        tFile.setProjectid(yyjl.getId());
        // 因为原项目有9102，防止业务id冲突，故取最后流程节点910206为functionID
        tFile.setFunctionid(910206);
        tFile.setType("znyy_yyjl");
        tFile.setHjid(hjID);
        tFile.setSubtname(Arrays.stream(tFile.getFilename().split("\\.")).reduce((first, second) -> second).orElse(null));
        tFile.setUploaddate(new Date());
        tFiles.add(tFile);
    }

    private void DeleteFile(@NotNull DfdwTZnyyYyjl yyjl, String hjID) {
        List<TFile> tFiles = tFileService.list(new LambdaQueryWrapper<TFile>().eq(TFile::getProjectid, yyjl.getId()).eq(TFile::getFunctionid, 910206).eq(TFile::getType, "znyy_yyjl").eq(TFile::getHjid, hjID));
        if (tFiles.size() > 0) {
            TFile tFile = tFiles.get(0);
            tFileService.removeById(tFile.getId());
        }
    }
}




