<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dfdwznyy.mapper.SealMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dfdwznyy.entity.Seal">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="sealName" column="seal_name" jdbcType="VARCHAR"/>
            <result property="deviceUuid" column="device_uuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,seal_name,device_uuid
    </sql>
</mapper>
