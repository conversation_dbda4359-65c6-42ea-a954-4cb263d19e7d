package com.soft.gcc.xtbg.dfdwznyy.entity.param;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ZnyyYyjlParam {
    /**
     * 用印人id(useId为-2代表密码管章人解锁用印)
     */
    @TableField(exist = false)
    private String userId;

    /**
     * 设备标识
     */
    @TableField(exist = false)
    private String uuid;

    /**
     * 申请单id
     */
    @TableField(exist = false)
    private String applicationId;

    /**
     * T_File hjID
     */
    @TableField(exist = false)
    private String hjID;

    /**
     * DFDW_T_ZNYY_SEAL id
     */
    @TableField(exist = false)
    private Integer sealId;

    /**
     * 页数
     */
    @TableField(exist = false)
    private Integer pageNum;

    /**
     * 每页数量
     */
    @TableField(exist = false)
    private Integer pageSize;

    /**
     * userName
     */
    @TableField(exist = false)
    private String userName;

    /**
     * model
     */
    @TableField(exist = false)
    private String model;

    /**
     * searchUsage
     */
    @TableField(exist = false)
    private String searchUsage;

    /**
     * error
     */
    @TableField(exist = false)
    private Integer error;
}
