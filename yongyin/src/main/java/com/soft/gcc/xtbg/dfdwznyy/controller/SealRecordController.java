package com.soft.gcc.xtbg.dfdwznyy.controller;

import com.soft.framework.helper.OSSHelper;
import org.apache.commons.lang3.StringUtils;
import com.soft.gcc.xtbg.dfdwznyy.base.Result;
import com.soft.gcc.xtbg.dfdwznyy.constants.Constant;
import com.soft.gcc.xtbg.dfdwznyy.entity.param.ZnyyYyjlParam;
import com.soft.gcc.xtbg.dfdwznyy.service.DfdwTZnyyYyjlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping({"/znyy/yyjl/record"})
public class SealRecordController {
    @Value("${jplat.OssLocalPath}")
    String downloadPath;
    @Autowired
    private DfdwTZnyyYyjlService dfdwTZnyyYyjlService;

    public static final String[] IMAGE_EXTENSION = {"bmp", "gif", "jpg", "jpeg", "png"};

    @RequestMapping("/getList")
    @ResponseBody
//    @Log(module = "用印管理",function = "91",detail = "得到用印记录列表，SealRecordController->getList")
    public Result getList(@RequestBody ZnyyYyjlParam yyjl) {
        return dfdwTZnyyYyjlService.getListYYJL(yyjl);
    }

}
