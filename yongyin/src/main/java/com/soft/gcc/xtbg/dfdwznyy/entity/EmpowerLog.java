package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 授权记录
 * @TableName DFDW_T_ZNYY_EMPOWER_LOG
 */
@TableName(value ="DFDW_T_ZNYY_EMPOWER_LOG")
@Data
public class EmpowerLog implements Serializable {

  public EmpowerLog() {

  }

  public EmpowerLog(Integer empowerId, Integer applicantId, String empowerName, String applicantName
          , Integer powerType, LocalDateTime createTime,Integer sealYYLCId) {
    this.empowerId = empowerId;
    this.applicantId = applicantId;
    this.empowerName = empowerName;
    this.applicantName = applicantName;
    this.powerType = powerType;
    this.createTime = createTime;
    this.sealYYLCId = sealYYLCId;
  }

  /**
   * 主键：
   */
  @TableId(value = "id", type = IdType.AUTO)
  @JSONField(name = "id")
  private Integer id;

  /**
   * 授权人
   */
  @TableField(value = "empower_id")
  @JSONField(name = "empower_id")
  private Integer empowerId;

  /**
   * 申请人
   */
  @TableField(value = "applicant_id")
  @JSONField(name = "applicant_id")
  private Integer applicantId;

  /**
   * 授权人名称
   */
  @TableField(value = "empower_name")
  @JSONField(name = "empower_name")
  private String empowerName;

  /**
   * 申请人名称
   */
  @TableField(value = "applicant_name")
  @JSONField(name = "applicant_name")
  private String applicantName;

  /**
   * 授权动作 0撤回授权 1给申请人授权
   */
  @TableField(value = "power_type")
  @JSONField(name = "power_type")
  private Integer powerType;

  @TableField("create_time")
  @JSONField(name = "create_time")
  private LocalDateTime createTime;

  /**
   * 印章流程id
   */
  @TableField(value = "seal_yylc_id")
  @JSONField(name = "seal_yylc_id")
  private Integer sealYYLCId;


}
