package com.soft.gcc.xtbg.dfdwznyy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.soft.gcc.common.t_file.entity.TFile;
import com.soft.gcc.xtbg.dfdwznyy.entity.DfdwTZnyyYyjl;
import com.soft.gcc.xtbg.dfdwznyy.entity.dto.TFileDto;
import com.soft.gcc.xtbg.dfdwznyy.entity.param.ZnyyYyjlParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【DFDW_T_ZNYY_YYJL(用印记录)】的数据库操作Mapper
* @createDate 2023-07-17 10:06:32
* @Entity com.yykj.app.dfdwznyy.entity.DfdwTZnyyYyjl
*/
public interface DfdwTZnyyYyjlMapper extends BaseMapper<DfdwTZnyyYyjl> {

    void saveBatchTFile( @Param("tFiles") List<TFile> tFiles);

    IPage<TFileDto> getFileList(IPage<TFileDto> list, @Param("yyjl") ZnyyYyjlParam yyjl);
}




