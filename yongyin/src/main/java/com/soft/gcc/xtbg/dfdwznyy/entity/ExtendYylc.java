package com.soft.gcc.xtbg.dfdwznyy.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* 用印流程扩展表
* @TableName DFDW_T_ZNYY_YYLC_EXTEND
*/
@TableName(value ="DFDW_T_ZNYY_YYLC_EXTEND")
@Data
public class ExtendYylc implements Serializable {

    public ExtendYylc() {

    }

    public ExtendYylc(Integer yylcId,LocalDateTime createDate) {

        this.yylcId = yylcId;
        this.createDate = createDate;

    }

    /**
    * 主键：
    */
    @TableId(value = "id", type = IdType.AUTO)
    @JSONField(name = "id")
    private Integer id;


    /**
    * 用印流程id
    */
    @TableField(value = "yylc_id")
    @JSONField(name = "yylc_id")
    private Integer yylcId;

    /**
    * 申请日期
    */
    @TableField(value = "create_date")
    @JSONField(name = "create_date")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
