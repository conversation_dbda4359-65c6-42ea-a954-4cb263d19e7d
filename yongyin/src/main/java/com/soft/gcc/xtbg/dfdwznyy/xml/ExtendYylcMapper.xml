<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.soft.gcc.xtbg.dfdwznyy.mapper.ExtendYylcMapper">

    <resultMap id="BaseResultMap" type="com.soft.gcc.xtbg.dfdwznyy.entity.ExtendYylc">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="yylcId" column="yylc_id" jdbcType="INTEGER"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="contractCopiesCount" column="contract_copies_count" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,yylc_id,order_num,
        create_date,contract_copies_count
    </sql>
</mapper>
