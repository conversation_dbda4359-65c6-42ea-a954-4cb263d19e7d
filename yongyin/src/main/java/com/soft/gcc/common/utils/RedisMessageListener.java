package com.soft.gcc.common.utils;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

@Configuration
public class RedisMessageListener {
    /**
     * redis消息监听器容器
     * 可以添加多个监听不同话题的redis监听器，只需要把消息监听器和相应的消息订阅处理器绑定，该消息监听器
     * 通过反射技术调用消息订阅处理器的相关方法进行一些业务处理
     * @param connectionFactory
     * @return
     */
    @Bean
    RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory,
                                            MessageListenerAdapter listenerAdapterSubmit,
                                            MessageListenerAdapter listenerAdapterBack) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        //订阅通道
        container.addMessageListener(listenerAdapterSubmit, new PatternTopic("SZYY.FLOW.SUBMIT"));
        container.addMessageListener(listenerAdapterBack, new PatternTopic("SZYY.FLOW.BACK"));
        return container;
    }
    /**
     * 消息监听器适配器，绑定消息处理器，利用反射技术调用消息处理器的业务方法
     * @param receiver
     * @return
     */
    @Bean
    MessageListenerAdapter listenerAdapterSubmit(Receiver receiver) {
        return new MessageListenerAdapter(receiver, "receiveMessageSubmit");
    }
    @Bean
    MessageListenerAdapter listenerAdapterBack(Receiver receiver) {
        return new MessageListenerAdapter(receiver, "receiveMessageBack");
    }
}
