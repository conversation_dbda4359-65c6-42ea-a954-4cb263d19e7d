package com.soft.gcc.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.soft.gcc.xtbg.yygl.controller.YyjyqbLcController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;

/**
 * redis消息处理器
 */
@Component
public class Receiver {

    @Autowired
    YyjyqbLcController yyjyqbLcController;

    /**
     * 接收到消息的方法，message就是指从主题获取的消息，主题配置在RedisMessageListener配置类做配置
     *
     * @param message
     */
    public void receiveMessageSubmit(String message) {
        JSONObject jsonObject = JSONObject.parseObject(message);
        String ywid = (String) jsonObject.get("ywid");
        String lc_jdid = (String) jsonObject.get("from");
        JSONObject object = JSONObject.parseObject(jsonObject.get("to").toString());
        Iterator<String> keys = object.keySet().iterator();
        if (keys.hasNext()) {
            String next = keys.next();
            if ("0".equals(next)) lc_jdid = "0";
            String personzgh = (String) object.getJSONObject(next).get("sendPersonZgh");
            personzgh = personzgh.replace("~", "");

            yyjyqbLcController.sendPersonSMS(true, personzgh, ywid, lc_jdid);
        }
    }

    public void receiveMessageBack(String message) {
        JSONArray array = JSONArray.parseArray(message);
        String personzgh = (String) array.getJSONObject(0).get("personzgh");
        personzgh.replace("~", "");
        yyjyqbLcController.sendPersonSMS(false,personzgh,"","");
    }


}