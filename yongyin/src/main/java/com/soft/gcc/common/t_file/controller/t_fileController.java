package com.soft.gcc.common.t_file.controller;


import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.config.OssConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.OSSHelper;
import com.soft.gcc.common.t_file.entity.TFile;
import org.apache.commons.compress.utils.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class t_fileController extends BaseConfig {

    private Boolean useOSS = false;

    /**
     * 文件保存路径
     */
    private String file_path = getOssLocalPath()+ "FileManage/";

    /**
     * 模板位置
     */
    private String moban_path = getOssLocalPath() + "muban/";

    /**
     * 新增附件
     *
     * @param file
     * @return
     */
    public AjaxResult uploadToLocal(@NotNull MultipartFile file) {
        if (OssConfig.getOpenFlag().equals("true")) {
            useOSS = true;
        }

        // 获取文件原本的名字
        String originName = file.getOriginalFilename();
        //doc|xls|ppt|rar|zip|txt|mp3|rm|jpg|gif|bmp|png|pdf|wps|ceb|dwg|
        Set<String> uploadRule = new HashSet<>();
        uploadRule.add(".doc");
        uploadRule.add(".xls");
        uploadRule.add(".ppt");
        uploadRule.add(".rar");
        uploadRule.add(".zip");
        uploadRule.add(".txt");
        uploadRule.add(".mp3");
        uploadRule.add(".rm");
        uploadRule.add(".jpg");
        uploadRule.add(".gif");
        uploadRule.add(".bmp");
        uploadRule.add(".png");
        uploadRule.add(".pdf");
        uploadRule.add(".wps");
        uploadRule.add(".ceb");
        uploadRule.add(".dwg");
        uploadRule.add(".docx");
        uploadRule.add(".xlsx");
        uploadRule.add(".jpeg");
        // 取出文件的后缀
        int count = 0;
        for (int i = 0; i < originName.length(); i++) {
            if (originName.charAt(i) == '.') {
                count = i;
                break;
            }
        }
        //文件类型
        String endName = originName.substring(count);
        String fileType = originName.substring(count + 1);
        if (!uploadRule.contains(endName)) {
            return AjaxResult.error("上传的文件类型错误！");
        }
        //路径
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssS");
        String format = sdf.format(new Date()) + endName;
//        String savePath = System.getProperty("user.dir") + "\\" + "files" +   "\\" + fileType + "\\" + format;
        // 保存文件的文件夹
        File folder = new File(file_path);
        // 判断路径是否存在,不存在则自动创建
        if (!folder.exists()) {
            folder.mkdirs();
        }
        try {
            file.transferTo(new File(folder, format));
//            return new String(format);
            if (useOSS) {
                String fullfilename = folder.getAbsolutePath();
                String ossLocalRoot =   ConfigHelper.getOssLocalPath();
                String ossRemoteRoot = ConfigHelper.getOssRemotePath();
                //文件替换本地OSSLocalRoot映射到OSSRemoteRoot获取键值
                String kname = fullfilename.replace("\\", "/").replace(ossLocalRoot, ossRemoteRoot);
                OSSHelper ossHelper = new OSSHelper();
                ossHelper.UploadFileProx(kname, folder, false);
            }
            return AjaxResult.success(format);

        } catch (IOException e) {
//            return new String(e.getMessage());
            return AjaxResult.error(e.getMessage());

        }

    }

    /**
     * 删除附件
     *
     * @param fileName
     */
    public void DeleteFile(@NotNull String fileName) {
        File folder = new File(file_path);
        File[] files = folder.listFiles();
        for (File file : files) {
            if (file.getName().equals(fileName)) {
                file.delete();
            }
        }
    }

    /**
     * 附件下载
     *
     * @param tFile
     * @param response
     * @throws Exception
     */
    public void download(TFile tFile, HttpServletResponse response) throws Exception {
        if (OssConfig.getOpenFlag().equals("true")) {
            useOSS = true;
        }

        String kname= tFile.getFilepath();
        if (StringUtil.IsNullOrEmpty(kname)) {
            return;
        }

        File file = new File(file_path + tFile.getFilepath());
        if (file.exists()) {
            FileInputStream fileInputStream = new FileInputStream(file);
            int len = fileInputStream.available();
            ServletOutputStream outputStream = response.getOutputStream();
            //文件类型二进制
            response.setContentType("application/octet-stream");
            //如果文件名为中文需要设置编码
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(tFile.getFilename(), "utf-8"));
            //文件大小
            response.setContentLength(len);
            //返回
            IOUtils.copy(fileInputStream, outputStream);
            //释放
            fileInputStream.close();
            outputStream.close();
        }

    }

}
