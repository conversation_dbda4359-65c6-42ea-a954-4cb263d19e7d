package com.soft.framework.common.constant;

public abstract class SysVar {

    /**
     * 允许系统失败次数
     */
    public static Integer MAX_ERROR_COUNT = 5;

    /**
     * 系统失败禁用时间
     */
    public static Integer MAX_ERROR_TIME = 1;
    /**
     * 系统失败提示
     */
    public static String MAX_ERROR_TXT = "登录失败次数过多，请" + MAX_ERROR_TIME + "分钟后再试!";

    /**
     * 系统失败提示
     */
    public static String ERROR_TXT = "用戶认证失败!";


    public static String REGEX_PASSWORD_STRONG = "^(?![0-9]+$)(?![^0-9]+$)(?![a-zA-Z]+$)(?![^a-zA-Z]+$)(?![a-zA-Z0-9]+$)[a-zA-Z0-9\\S]{8,}$";

    // Token过期时间30分钟（用户登录过期时间是此时间的两倍，以token在reids缓存时间为准）
    public static Integer EXPIRE_TIME = 15 * 60;

    public static Integer T_REF = 300;

    public static Integer MSG_CODE_NEXT = 1;

    public static Integer MSG_CODE_LOSS = 2;

    public static Integer MSG_CODE_EXP = 7;

    public static String USER_NAME = "userName";

    public static String LOGIN_NAME = "LOGIN_NAME";

    public static String AUTH = "AUTH";

    public static String TOKEN = "TOKEN";

    public static String EXCEPTION = "EXCEPTION";

    public static String SALT = "c64224a67adae3a8a53388c5db9ac493";
}
