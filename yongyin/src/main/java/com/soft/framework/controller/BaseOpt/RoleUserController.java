package com.soft.framework.controller.BaseOpt;

import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.soft.framework.helper.WpServiceHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.vComp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Hashtable;
import java.util.List;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/RoleUser")
@SuppressWarnings("unchecked")
@Api(tags = "基本框架接口->角色用户接口")
public class RoleUserController {
    @RequestMapping(value = "/GetRoleUserList_Y", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetRoleUserList_Y", notes = "获取具有角色的用户列表的接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetRoleUserList_Y(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String CompId = request.getParameter("Group");
        String UserMark = request.getParameter("UserMark");
        if (StringUtil.IsNullOrEmpty(RoleId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        try {
            SqlHelper sqlhelper = new SqlHelper();

            List<vComp> _clist = null;
            _clist = WpServiceHelper.GetCompList();
            if (_clist == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            Hashtable chash = new Hashtable();
            chash.put(1, "集团");
            for (vComp cobj : _clist) {
                chash.put(cobj.getCOMP_ID(), cobj.getCOMP_NAME());
            }

            strsql = "select * from vperson where Id in(select PersonId from RolePerson where RoleId=" + RoleId + ")";
            if (!StringUtil.IsNullOrEmpty(CompId) && !CompId.equals("0")) {
                strsql += " and TopGroupId=" + CompId;
            }

            if (!StringUtil.IsNullOrEmpty(UserMark)) {
                strsql += " and  (RealName like '%" + UserMark + "%' or LoginName like '%" + UserMark + "%')";
            }
            strsql += " order by GroupId asc";

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/GetRoleUserList_W", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetRoleUserList_W", notes = "获取不具有角色的用户列表的接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String GetRoleUserList_W(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        String RoleId = request.getParameter("RoleId");
        String CompId = request.getParameter("Group");
        String UserMark = request.getParameter("UserMark");
        if (StringUtil.IsNullOrEmpty(RoleId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult.toString();
        }

        String basesql = "";
        String orderstr = "";
        String msgstr = "";
        int rcount = 0;

        try {
            SqlHelper sqlhelper = new SqlHelper();

            List<vComp> _clist = null;

            _clist = WpServiceHelper.GetCompList();
            if (_clist == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            Hashtable chash = new Hashtable();
            chash.put(1, "集团");
            for (vComp cobj : _clist) {
                chash.put(cobj.getCOMP_ID(), cobj.getCOMP_NAME());
            }

            basesql = "select * from vPerson where id not in(select PersonId from RolePerson where RoleId=" + RoleId + ")";
            if (!StringUtil.IsNullOrEmpty(CompId) && !CompId.equals("0")) {
                basesql += " and TopGroupId=" + CompId;
            }
            if (!StringUtil.IsNullOrEmpty(UserMark)) {
                basesql += " and  (RealName like '%" + UserMark + "%' or LoginName like '%" + UserMark + "%')";
            }
            orderstr = " order by GroupId asc";

            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult.toString();
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            DataTable tmpdt = WpServiceHelper.GetDataTable(strsql);
            if (tmpdt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取信息失败！");
                return ajaxResult.toString();
            }

            String jsonstr = AjaxResult.extgrid(tmpdt, rcount);
            return jsonstr;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult.toString();
        }
    }

    @RequestMapping(value = "/AddRoleUser", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddRoleUser", notes = "角色添加用户接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult AddRoleUser(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);
            int iuserid = Integer.parseInt(UserId);

            Boolean uflag = false;
            uflag = WpServiceHelper.AddRolePerson(iroleid, iuserid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("权限设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteRoleUser", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteRoleUser", notes = "角色删除用户接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteRoleUser(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        String strsql = "";

        String RoleId = request.getParameter("RoleId");
        String UserId = request.getParameter("UserId");
        if (StringUtil.IsNullOrEmpty(RoleId) || StringUtil.IsNullOrEmpty(UserId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            int iroleid = Integer.parseInt(RoleId);
            int iuserid = Integer.parseInt(UserId);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteRolePerson(iroleid, iuserid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，操作角色用户信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("权限删除成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
