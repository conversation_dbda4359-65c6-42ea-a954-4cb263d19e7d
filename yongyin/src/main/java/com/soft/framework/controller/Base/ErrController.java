package com.soft.framework.controller.Base;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * web错误 全局处理
 *
 * <AUTHOR>
 */

@Slf4j
@Controller
public class ErrController implements ErrorController {
    private static final String ERROR_PATH="/error";
    private ErrorAttributes errorAttributes;
 
    @Override
    public String getErrorPath() {
        return ERROR_PATH;
    }
    @Autowired
    public ErrController(ErrorAttributes errorAttributes) {
        this.errorAttributes=errorAttributes;
    }
 
    /**
     * web页面错误处理
     */
    @RequestMapping(value=ERROR_PATH)
    public String errorPageHandler(HttpServletRequest request,HttpServletResponse response) {
        ServletWebRequest requestAttributes =  new ServletWebRequest(request);
        Map<String, Object> attr = this.errorAttributes.getErrorAttributes(requestAttributes, false);
        if(requestAttributes!=null)
        {
            log.info("出错啦！进入自定义错误控制器,status="+attr.get("status")+",message="+attr.get("message")+",path="+attr.get("path"));
        } else
        {
            log.info("出错啦！进入自定义错误控制器!");
        }

        return "redirect:/Error.html";
    }
}