package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.ConfigHelper;
import com.soft.framework.helper.DBHelper;
import com.soft.framework.helper.SqlHelper;
import com.soft.framework.helper.ToolHelper;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.gcc.base.entity.NFT_ModuleGroup;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/MGPMan")
@Api(tags = "基本框架接口->流程管理接口")
public class MGPManController {

    private Boolean GatherParams2Obj(Map<String, String> params, NFT_ModuleGroup entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("GP_NAME"))) {
                entity.setGP_NAME(params.get("GP_NAME"));
            }
            if (!StringUtil.IsNull(params.get("GP_MLIST"))) {
                entity.setGP_MLIST(params.get("GP_MLIST"));
            }

            if (!StringUtil.IsNullOrEmpty(params.get("GP_ID"))) {
                entity.setGP_ID(Integer.parseInt(params.get("GP_ID")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddMGP", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddMGP", notes = "新增模块分组配置接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddMGP(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            NFT_ModuleGroup entity = new NFT_ModuleGroup();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            //提交数据库
            String strsql = DBHelper.GetInsertSQL(entity, "NFT_ModuleGroup", Arrays.asList());
            if (StringUtil.IsNullOrEmpty(strsql)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            String tmpstr = sqlhelper.ExecuteInsertWithObtainId(strsql);

            ajaxResult = AjaxResult.success(tmpstr);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyMGP", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyMGP", notes = "修改模块分组配置接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyMGP(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            String strsql = "select * from NFT_ModuleGroup where GP_ID='" + Id + "'";
            NFT_ModuleGroup entity = sqlhelper.GetObject(NFT_ModuleGroup.class, strsql);
            if (entity == null) {
                ajaxResult = AjaxResult.error("此记录在数据库中已经被删除，请刷新主界面，重新加载数据！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            //提交数据库
            strsql = DBHelper.GetUpdateSQL(entity, "NFT_ModuleGroup", Arrays.asList("GP_ID"), Arrays.asList(Id.toString()));
            if (StringUtil.IsNullOrEmpty(strsql)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success(entity.getGP_ID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteMGP", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteMGP", notes = "删除模块分组配置接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteMGP(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();

            String Id = request.getParameter("ID");

            String strsql = "delete from NFT_ModuleGroup where GP_ID='" + Id + "'";
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult = AjaxResult.success("删除默认启动设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String seachtj = request.getParameter("seachtj");

            strsql.append("select * from NFT_ModuleGroup where 1=1 ");

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetMGPList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetMGPList", notes = "获取当前模块分组配置列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetMGPList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<NFT_ModuleGroup> list = sqlhelper.GetObjectList(NFT_ModuleGroup.class, strsql);
            ajaxResult = AjaxResult.extgrid(NFT_ModuleGroup.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetMGPById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetMGPById", notes = "获取指定具有指定ID的模块分组配置接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetMGPById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();
        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            String strsql = "select * from NFT_ModuleGroup where GP_ID='" + Id + "'";
            NFT_ModuleGroup obj = sqlhelper.GetObject(NFT_ModuleGroup.class, strsql);
            ajaxResult = AjaxResult.extform(NFT_ModuleGroup.class, "获取信息成功！", obj);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前模块分组配置信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            DataTable dt = sqlhelper.GetDataTable(basesql.toString() + " " + orderstr.toString());
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("GP_ID", "GP_ID", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("GP_NAME", "GP_NAME", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("GP_MLIST", "GP_MLIST", 20));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "模块分组配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
