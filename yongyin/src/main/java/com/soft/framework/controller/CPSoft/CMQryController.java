package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.yyszc.extend.CmQryResult;
import com.yyszc.extend.DataColumn;
import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataTable;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.wpbase.ventity.PersonEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CmQry" )
@SuppressWarnings("unchecked")
public class CMQryController {
    private static final Logger sysLog = LoggerFactory.getLogger(CMQryController.class);

    @RequestMapping(value="/ExecuteQuery", produces={"text/plain;charset=UTF-8"})
    @ResponseBody
    @RepeatSubmit
    public String ExecuteQuery(HttpServletRequest request)
    {
        SqlHelper sqlhelper=new SqlHelper();

        String jsonstr="";
        try
        {
            String qrystr = request.getParameter("QueryStr");
            String qrymode = request.getParameter("QueryMode");
            if(!qrystr.equals("")) {
                qrystr= AesHelper.aesDecodeCBC(qrystr);
            }

            if(qrymode.equals("0")||qrymode.equals("1"))
            {
                PersonEntity person =SessionHelper.getSessionPerson();
                if (!person.getLoginName().equals("qidf"))
                {
                    AjaxResult ajaxResult =AjaxResult.error("非超级管理用户禁止使用该管理功能!");
                    return JSON.toJSON(ajaxResult).toString();
                }
            }

            if(SecToolHelper.CheckWordHasSQLKQ(qrystr))
            {
                AjaxResult ajaxResult = AjaxResult.error("检测到非法SQL注入代码!");
                return JSON.toJSON(ajaxResult).toString();
            }

            if (qrymode.equals("0"))
            {
                if (!qrystr.equals(""))
                {
                    sqlhelper.ExecuteNoQuery(qrystr);

                    CmQryResult Result = new CmQryResult();
                    Result.resultType = "result";
                    Result.fieldsNames = "";
                    Result.resultData = "处理成功！";
                    Result.columModle = "";
                    jsonstr= JSON.toJSONString(Result);
                    return jsonstr;
                }
            }
            else if (qrymode.equals("1"))
            {
                while (qrystr.lastIndexOf(";") == qrystr.length()-1)
                {
                    qrystr=qrystr.substring(0, qrystr.length() - 1);
                }
                List<String> sqlList = Arrays.asList(qrystr.split(";"));
                if (sqlList.size()> 0)
                {
                    sqlhelper.ExecuteNoQuery(sqlList);

                    CmQryResult Result = new CmQryResult();
                    Result.resultType = "result";
                    Result.fieldsNames = "";
                    Result.resultData = "处理成功！";
                    Result.columModle = "";
                    jsonstr= JSON.toJSONString(Result);
                    return jsonstr;
                }
            }
            else if (qrymode.equals("2"))
            {
                while (qrystr.lastIndexOf(";") == qrystr.length()- 1)
                {
                    qrystr = qrystr.substring(0, qrystr.length() - 1);
                }

                List<String> sqlList = Arrays.asList(qrystr.split(";"));
                if (sqlList.size()>0&&!sqlList.get(0).toString().equals(""))
                {
                    DataTable dt = sqlhelper.GetDataTable(sqlList.get(0).toString());

                    StringBuilder fn = new StringBuilder();
                    StringBuilder cm = new StringBuilder();
                    StringBuilder rdlist = new StringBuilder();
                    for(int i=0;i<dt.getColList().size();i++){
                        DataColumn dr=dt.getColList().get(i);
                        String cname = (dr.getColumnName().equals("")) ? "DefField" : dr.getColumnName();
                        if (i == 0) {
                            fn.append("'" + cname + "'");
                            cm.append("{ header: '" + cname + "',width: 150, sortable: false, dataIndex: '" + cname + "'}");
                        } else {
                            fn.append(",'" + cname + "'");
                            cm.append(",{ header: '" + cname + "',width: 150, sortable: false, dataIndex: '" + cname + "'}");
                        }
                    }

                    int rcount = 0;
                    for(DataRow dr:dt.getRowList()) {
                        rcount++;
                        StringBuilder rd = new StringBuilder();
                        for (int j = 0; j < dr.getColumnList().size(); j++) {
                            String cvalue = dr.getColumn(j).getColumnValue().toString();
                            if (j == 0) {
                                rd.append("'" + cvalue + "'");
                            } else {
                                rd.append(",'" + cvalue + "'");
                            }
                        }
                        if (rdlist.length() == 0) {
                            rdlist.append("[" + rd.toString() + "]");
                        } else {
                            rdlist.append(",[" + rd.toString() + "]");
                        }
                    }

                    CmQryResult Result = new CmQryResult();
                    Result.resultType = "grid";
                    Result.fieldsNames = "[" + fn.toString() + "]";
                    Result.columModle = "[" + cm.toString() + "]";
                    Result.resultData = "[" + rdlist.toString() + "]";
                    jsonstr= JSON.toJSONString(Result);
                    return jsonstr;
                }else
                {
                    CmQryResult Result = new CmQryResult();
                    Result.resultType = "result";
                    Result.fieldsNames = "";
                    Result.resultData = "未发现有效数据！";
                    Result.columModle = "";
                    jsonstr= JSON.toJSONString(Result);
                    return jsonstr;
                }
            }
            CmQryResult Result = new CmQryResult();
            Result.resultType = "result";
            Result.fieldsNames = "";
            Result.resultData = "未发现有效数据！";
            Result.columModle = "";
            jsonstr= JSON.toJSONString(Result);
            return jsonstr;
        }
        catch (Exception Ex)
        {
            CmQryResult Result = new CmQryResult();
            Result.resultType = "result";
            Result.fieldsNames = "";
            Result.resultData = "程序产生异常：" + Ex.getMessage()+ "！";
            Result.columModle = "";
            jsonstr= JSON.toJSONString(Result);
            return jsonstr;
        }
    }

    @RequestMapping(value="/ExportExcel", produces={"text/plain;charset=UTF-8"})
    @ResponseBody
    @RepeatSubmit
    public String ExportExcel(@RequestParam Map<String, String> params, HttpServletResponse response)
    {
        AjaxResult ajaxResult=null;

        SqlHelper sqlhelper=new SqlHelper();

        String qrystr = params.get("QueryStr");
        String qrymode = params.get("QueryMode");
        if(!qrystr.equals("")) {
            qrystr= AesHelper.aesDecodeCBC(qrystr);
        }

        if(SecToolHelper.CheckWordHasSQLKQ(qrystr))
        {
            ajaxResult = AjaxResult.error("检测到非法SQL注入代码!");
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            if (qrymode.equals("2")) {
                while (qrystr.lastIndexOf(";") == qrystr.length() - 1) {
                    qrystr = qrystr.substring(0, qrystr.length() - 1);
                }

                List<String> sqlList = Arrays.asList(qrystr.split(";"));

                List<ToolHelper.ExportColumnMode> cmlist = new ArrayList();

                if (sqlList.size() > 0 && !sqlList.get(0).toString().equals("")) {
                    DataTable dt = sqlhelper.GetDataTable(sqlList.get(0).toString());

                    for (int i = 0; i < dt.getColList().size(); i++) {
                        String cname = dt.getColList().get(i).getColumnName();
                        if (cname == "") {
                            cname = "DefField";
                        }
                        cmlist.add(new ToolHelper.ExportColumnMode(cname, cname, 20));
                    }

                    StringBuilder fname = new StringBuilder();
                    StringBuilder retstr = new StringBuilder();

                    boolean retb = ToolHelper.ExportDS2XlsFile(dt, "查询信息", cmlist, retstr, fname);
                    if(retb)
                    {
                        ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                        cfs.success = true;
                        cfs.text = "生成文件成功！";
                        cfs.fname=fname.toString();
                        cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath()+ ConfigHelper.getfSepChar()+fname.toString());
                        cfs.data2 = null;

                        String jsonstr=JSON.toJSON(cfs).toString();
                        return jsonstr;
                    }else
                    {
                        ajaxResult=AjaxResult.error("导出文件异常!");
                        return JSON.toJSON(ajaxResult).toString();
                    }
                }
            }

            ajaxResult=AjaxResult.error("错误参数!");
            return JSON.toJSON(ajaxResult).toString();
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
