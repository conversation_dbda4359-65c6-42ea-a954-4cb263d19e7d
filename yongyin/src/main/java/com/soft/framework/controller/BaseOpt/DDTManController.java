package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.DictionaryDefine;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value = "/Service/BaseOpt/DDTMan")
@Api(tags = "基本框架接口->字典类型接口")
public class DDTManController {

    private Boolean GatherParams2Obj(Map<String, String> params, DictionaryDefine entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("Title"))) {
                entity.setTitle(params.get("Title"));
            }
            if (!StringUtil.IsNull(params.get("Parameter"))) {
                entity.setParameter(params.get("Parameter"));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddDDT", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddDDT", notes = "新增字典类型定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddDDT(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            DictionaryDefine entity = new DictionaryDefine();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Integer rid = -1;
            rid = WpServiceHelper.AddDictionaryDefine(entity);
            if (rid == null || rid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增流程信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(rid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyDDT", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyDDT", notes = "修改字典类型定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyDDT(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            Integer iid = Integer.parseInt(Id);

            DictionaryDefine entity = null;
            entity = WpServiceHelper.GetDictionaryDefineById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取字典类型定义信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateDictionaryDefine(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改字典类型定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteDDT", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteDDT", notes = "删除字典类型定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteDDT(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String Id = request.getParameter("ID");
            int iid = Integer.parseInt(Id);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteDictionaryDefineById(iid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除字典类型定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除字典类型定义置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String seachtj = request.getParameter("seachtj");

            strsql.append("select * from DictionaryDefine where 1=1 ");

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetDDTList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetDDTList", notes = "获取当前字典类型定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetDDTList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<DictionaryDefine> list = null;
            list = WpServiceHelper.GetDictionaryDefineList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取字典类型定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(DictionaryDefine.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetDDTById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetDDTById", notes = "获取指定具有指定ID的字典类型定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetDDTById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer iid = Integer.parseInt(Id);

            DictionaryDefine entity = null;

            entity = WpServiceHelper.GetDictionaryDefineById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取字典类型定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(DictionaryDefine.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", produces = {"text/plain;charset=UTF-8"}, method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前模块自启动项信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取字典类型定义信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("DDTID", "流程编号", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("DDTName", "流程名称", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("ywb", "关联业务说明", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("ywUrl", "关联业务连接", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("isUse", "是否在用", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "模块配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
