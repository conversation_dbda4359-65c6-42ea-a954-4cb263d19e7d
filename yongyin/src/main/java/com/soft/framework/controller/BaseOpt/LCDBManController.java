package com.soft.framework.controller.BaseOpt;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.yyszc.extend.DataTable;
import com.yyszc.wpbase.entity.Lcdefine;
import com.yyszc.wpbase.entity.NFT_ModuleLCLink;
import com.yyszc.wpbase.entity.NFV_ModuleLCLink;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping("/Service/BaseOpt/LCDBMan")
@Api(tags = "基本框架接口->流程待办定义接口")
public class LCDBManController {
    private Boolean GatherParams2Obj(Map<String, String> params, NFT_ModuleLCLink entity, StringBuilder msgstr) {
        try {
            if (!StringUtil.IsNull(params.get("LK_TMENU"))) {
                entity.setLK_TMENU(params.get("LK_TMENU"));
            }
            if (!StringUtil.IsNull(params.get("LK_TNODE"))) {
                entity.setLK_TNODE(params.get("LK_TNODE"));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("LK_MID"))) {
                entity.setLK_MID(Integer.parseInt(params.get("LK_MID")));
            }
            if (!StringUtil.IsNullOrEmpty(params.get("LK_LCID"))) {
                entity.setLK_LCID(Integer.parseInt(params.get("LK_LCID")));
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("程序产生异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/AddLCDB", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "AddLCDB", notes = "新增流程待办定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddLCDB(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            SqlHelper sqlhelper = new SqlHelper();
            StringBuilder msgstr = new StringBuilder();

            NFT_ModuleLCLink entity = new NFT_ModuleLCLink();

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }

            Integer rid = -1;
            rid = WpServiceHelper.AddNFT_ModuleLCLink(entity);
            if (rid == null || rid == -1) {
                ajaxResult = AjaxResult.error("操作失败，新增流程信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(String.valueOf(rid));
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ModifyLCDB", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "ModifyLCDB", notes = "修改流程待办定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyLCDB(@RequestParam Map<String, String> params) {
        AjaxResult ajaxResult = null;
        try {
            StringBuilder msgstr = new StringBuilder();

            String Id = params.get("ID");
            Integer iid = Integer.parseInt(Id);

            NFT_ModuleLCLink entity = null;
            entity = WpServiceHelper.GetNFT_ModuleLCLinkById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程定义信息失败！");
                return ajaxResult;
            }

            if (!GatherParams2Obj(params, entity, msgstr)) {
                ajaxResult = AjaxResult.error("操作失败，参数传输失败！");
                return ajaxResult;
            }

            Boolean uflag = false;
            uflag = WpServiceHelper.UpdateNFT_ModuleLCLink(entity);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，修改流程定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success(entity.getLK_ID().toString());
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/DeleteLCDB", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "DeleteLCDB", notes = "删除流程待办定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteLCDB(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String Id = request.getParameter("ID");
            int iid = Integer.parseInt(Id);

            Boolean uflag = false;
            uflag = WpServiceHelper.DeleteNFT_ModuleLCLinkById(iid);
            if (uflag != true) {
                ajaxResult = AjaxResult.error("操作失败，删除流程定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.success("删除流程待办设置成功！");
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }


    private boolean GetUIQueryString(HttpServletRequest request, StringBuilder strsql, StringBuilder orderstr, StringBuilder msgstr) {
        try {
            String seachtj = request.getParameter("seachtj");

            strsql.append("select * from NFV_ModuleLCLink where 1=1  ");
            if (!StringUtil.IsNullOrEmpty(seachtj)) {
                strsql.append(" and lcName like '%" + seachtj + "%'");
            }

            if (BaseConfig.getExtVersion().equals("3.2")) {
                String sortf = request.getParameter("sort");
                String sortd = request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf) && !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            } else {
                String jsons = request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons, orderstr);
            }

            return true;
        } catch (Exception Ex) {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value = "/GetLCDBList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetLCDBList", notes = "获取流程待办定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetLCDBList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper = new SqlHelper();

        //参数获取
        String tmpstr = "";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts) ? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits) ? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());

            String rrval = "0";
            rrval = WpServiceHelper.GetRecordCount(rcsql);
            if (rrval == null || rrval.equals("")) {
                ajaxResult = AjaxResult.error("操作失败，获取数据条目信息失败！");
                return ajaxResult;
            }

            if (!rrval.equals("")) {
                rcount = Integer.parseInt(rrval);
            }

            int pageCount = (rcount / limit) + 1;
            int currpage = start / limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<NFV_ModuleLCLink> list = null;
            list = WpServiceHelper.GetNFV_ModuleLCLinkList(strsql);
            if (list == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extgrid(NFV_ModuleLCLink.class, rcount, list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/GetLCDBById", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetLCDBById", notes = "获取指定ID的流程待办定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetLCDBById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        String Id = request.getParameter("ID");
        if (StringUtil.IsNullOrEmpty(Id)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            Integer iid = Integer.parseInt(Id);

            NFV_ModuleLCLink entity = null;

            entity = WpServiceHelper.GetNFV_ModuleLCLinkById(iid);
            if (entity == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程定义信息失败！");
                return ajaxResult;
            }

            ajaxResult = AjaxResult.extform(NFV_ModuleLCLink.class, "获取信息成功！", entity);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value = "/ExportExcel", method = {RequestMethod.GET, RequestMethod.POST}, produces = {"text/plain;charset=UTF-8"})
    @ResponseBody
    @ApiOperation(value = "ExportExcel", notes = "导出当前流程待办定义信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr = new StringBuilder();
        StringBuilder msgstr = new StringBuilder();
        if (!GetUIQueryString(request, basesql, orderstr, msgstr)) {
            ajaxResult = AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            DataTable dt = null;

            dt = WpServiceHelper.GetDataTable(basesql + " " + orderstr);
            if (dt == null) {
                ajaxResult = AjaxResult.error("操作失败，获取流程定义信息失败！");
                return JSON.toJSON(ajaxResult).toString();
            }

            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("LK_ID", "导航项ID", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("LcID", "流程ID", 20));
            cmlist.add(new ToolHelper.ExportColumnMode("lcName", "流程名称", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("LK_TMENU", "菜单目录", 40));
            cmlist.add(new ToolHelper.ExportColumnMode("LK_SNO", "菜单项", 40));

            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "流程待办配置信息", cmlist, retstr, fname);
            if (retb) {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath() + ConfigHelper.getfSepChar() + fname.toString());
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            } else {
                ajaxResult = AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value = "/GetLCList", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value = "GetLCList", notes = "获取流程定义列表信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetLCList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try {
            String strsql = "select * from Lcdefine order by LcId asc";
            List<Lcdefine> list = WpServiceHelper.GetLcdefineList(strsql);

            ajaxResult = AjaxResult.extgrid(Lcdefine.class, list.size(), list);
            return ajaxResult;
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }
}
