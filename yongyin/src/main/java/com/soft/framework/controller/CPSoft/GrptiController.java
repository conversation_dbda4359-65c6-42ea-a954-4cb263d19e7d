package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.BaseConfig;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import com.soft.framework.interceptor.annotation.RepeatSubmit;
import com.soft.gcc.base.entity.CPS_T_GRPT_INFO;
import com.soft.gcc.base.entity.CPS_V_GRPT_INFO;
import com.yyszc.extend.DataTable;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Controller
@Configuration
@RequestMapping(value="/Service/CpSoft/GrptI" )
@Api(tags ="基本框架接口->japer报表信息定义接口")
public class GrptiController {

    @RequestMapping(value="/AddGRPTI",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="AddGRPTI",notes="新增Japer报表信息定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult AddGRPTI(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            CPS_T_GRPT_INFO entity = new CPS_T_GRPT_INFO();

            //数据转换
            String tmpstr="";
            tmpstr=request.getParameter("GRPTT_ID");
            if(!StringUtil.IsNullOrEmpty(tmpstr))
            {
                entity.setGRPTT_ID(Integer.parseInt(tmpstr));
            }
            tmpstr=request.getParameter("GRPT_DEF");
            if(!StringUtil.IsNullOrEmpty(tmpstr))
            {
                entity.setGRPT_DEF(Integer.parseInt(tmpstr));
            }
            entity.setGRPT_SMARK(request.getParameter("GRPT_SMARK"));

            //提交数据库
            String strsql = DBHelper.GetInsertSQL(entity,"CPS_T_GRPT_INFO", Arrays.asList("GRPT_ID"));
            if (StringUtil.IsNullOrEmpty(strsql))
            {
                ajaxResult=AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteInsertWithObtainId(strsql);

            ajaxResult=AjaxResult.success(entity.getGRPT_ID().toString());
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ModifyGRPTI",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ModifyGRPTI",notes="修改Japer报表信息定义接口")
    @ApiImplicitParam(name = "params", value = "@RequestParam Map<String,String>", required = true)
    @RepeatSubmit
    public AjaxResult ModifyGRPTI(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            //数据转换
            String ID=request.getParameter("ID");
            CPS_T_GRPT_INFO entity=sqlhelper.GetObject(CPS_T_GRPT_INFO.class,"select * from CPS_T_GRPT_INFO where GRPT_ID="+ID);
            if(entity==null)
            {
                ajaxResult=AjaxResult.error("此记录在数据库中已经被删除，请刷新主界面，重新加载数据！");
                return ajaxResult;
            }

            String tmpstr="";
            tmpstr=request.getParameter("GRPTT_ID");
            if(!StringUtil.IsNullOrEmpty(tmpstr))
            {
                entity.setGRPTT_ID(Integer.parseInt(tmpstr));
            }
            tmpstr=request.getParameter("GRPT_DEF");
            if(!StringUtil.IsNullOrEmpty(tmpstr))
            {
                entity.setGRPT_DEF(Integer.parseInt(tmpstr));
            }
            entity.setGRPT_SMARK(request.getParameter("GRPT_SMARK"));

            //提交数据库
            String strsql = DBHelper.GetUpdateSQL(entity,"CPS_T_GRPT_INFO", Arrays.asList("GRPT_ID"), Arrays.asList(ID.toString()));
            if (StringUtil.IsNullOrEmpty(strsql))
            {
                ajaxResult=AjaxResult.error("操作失败，生成对象对应插库语句失败！");
                return ajaxResult;
            }
            sqlhelper.ExecuteNoQuery(strsql);

            ajaxResult=AjaxResult.success(entity.getGRPT_ID().toString());
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/DeleteGRPTI",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="DeleteGRPTI",notes="删除Japer报表信息定义接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    @RepeatSubmit
    public AjaxResult DeleteGRPTI(HttpServletRequest request) {
        AjaxResult ajaxResult = null;
        try
        {
            SqlHelper sqlhelper=new SqlHelper();

            //数据转换
            String ID=request.getParameter("ID");

            //提交数据库
            String qnum = sqlhelper.ExecuteScalar("select count(1) from CPS_T_GRPT_ULINK where grpt_id="+ID);
            if (qnum == "0")
            {
                sqlhelper.ExecuteNoQuery("delete from CPS_T_GRPT_INFO where grpt_id="+ID);
                ajaxResult=AjaxResult.success("删除成功！");
                return ajaxResult;
            }
            else
            {
                ajaxResult=AjaxResult.error("删除失败，报表类型被多次引用，不能删除！");
                return ajaxResult;
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    private boolean GetUIQueryString(HttpServletRequest request,StringBuilder strsql,StringBuilder orderstr,StringBuilder msgstr)
    {
        try
        {
            String seachtj=request.getParameter("seachtj");

            strsql.append("select * from CPS_V_GRPT_INFO where 1=1 ");
            if (StringUtil.IsNullOrEmpty(seachtj)) {
                strsql.append(" and GRPT_SMARK like '%" + seachtj + "%'");
            }

            if(BaseConfig.getExtVersion().equals("3.2"))
            {
                String sortf=request.getParameter("sort");
                String sortd=request.getParameter("dir");
                if (!StringUtil.IsNullOrEmpty(sortf)&& !StringUtil.IsNullOrEmpty(sortd)) {
                    orderstr.append(" order by " + sortf + " " + sortd);
                }
            }else
            {
                String jsons=request.getParameter("sort");
                ToolHelper.DeserializeExtJsSortInfo(jsons,orderstr);
            }

            return true;
        }
        catch (Exception Ex)
        {
            msgstr.append("系统异常" + Ex.getMessage() + "!");
            return false;
        }
    }

    @RequestMapping(value="/GetGRPTIList",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetGRPTIList",notes="获取Japer报表信息定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGRPTIList(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        //参数获取
        String tmpstr="";
        String starts = request.getParameter("start");
        String limits = request.getParameter("limit");
        int start = StringUtil.IsNullOrEmpty(starts)? 0 : Integer.parseInt(starts);
        int limit = StringUtil.IsNullOrEmpty(limits)? 20 : Integer.parseInt(limits);

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr  = new StringBuilder();
        StringBuilder msgstr  = new StringBuilder();
        String strsql = "";
        int rcount = 0;

        if (!GetUIQueryString(request,basesql, orderstr, msgstr))
        {
            ajaxResult=AjaxResult.error(msgstr.toString());
            return ajaxResult;
        }

        try
        {
            String rcsql = DBHelper.ToRecordCountSql(basesql.toString());
            tmpstr = sqlhelper.ExecuteScalar(rcsql);
            if (!tmpstr.equals("")) {
                rcount = Integer.parseInt(tmpstr);
            }

            int pageCount = (rcount/limit) + 1;
            int currpage = start/limit;
            strsql = sqlhelper.ToPageSql(basesql.toString(), orderstr.toString(), limit, currpage);

            List<CPS_V_GRPT_INFO> list = sqlhelper.GetObjectList(CPS_V_GRPT_INFO.class,strsql);
            ajaxResult=AjaxResult.extgrid(CPS_V_GRPT_INFO.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetGRPTIList2",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetGRPTIList2",notes="获取指定报表类型Japer报表信息定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGRPTIList2(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        String rpttId=request.getParameter("rpttId");

        String strsql = "select * from CPS_V_GRPT_INFO where GRPT_DEF=0";
        if(!StringUtil.IsNullOrEmpty(rpttId)&&!rpttId.equals("0")) {
            strsql += " and GRPTT_ID="+rpttId;
        }
        strsql+= " order by GRPT_ID";

        try
        {
            List<CPS_T_GRPT_INFO> list = sqlhelper.GetObjectList(CPS_T_GRPT_INFO.class,strsql);
            ajaxResult=AjaxResult.extgrid(CPS_T_GRPT_INFO.class,list.size(),list);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/GetGRPTIById",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="GetGRPTIById",notes="获取指定ID的Japer报表信息定义列表接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult GetGRPTIById(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();
        String ID=request.getParameter("ID");

        try
        {
            CPS_V_GRPT_INFO obj= sqlhelper.GetObject(CPS_V_GRPT_INFO.class,"select * from CPS_V_GRPT_INFO where GRPT_ID="+ID);
            ajaxResult=AjaxResult.extform(CPS_V_GRPT_INFO.class,"获取信息成功！",obj);
            return ajaxResult;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExportExcel", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ExportExcel",notes="导出Japer报表定义信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExportExcel(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        StringBuilder basesql = new StringBuilder();
        StringBuilder orderstr  = new StringBuilder();
        StringBuilder msgstr  = new StringBuilder();
        if (!GetUIQueryString(request,basesql, orderstr, msgstr))
        {
            ajaxResult=AjaxResult.error(msgstr.toString());
            return JSON.toJSON(ajaxResult).toString();
        }

        try
        {
            StringBuilder fname = new StringBuilder();
            StringBuilder retstr = new StringBuilder();
            DataTable dt = sqlhelper.GetDataTable(basesql.toString()+" "+orderstr.toString());
            List<ToolHelper.ExportColumnMode> cmlist = new ArrayList<ToolHelper.ExportColumnMode>();
            cmlist.add(new ToolHelper.ExportColumnMode("GRPTT_ID", "报表ID", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GRPTT_NAME", "报表类型名称", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GRPT_SFILE", "报表模板", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GRPT_SMARK", "报表备注", 30));
            cmlist.add(new ToolHelper.ExportColumnMode("GRPT_DEFN", "是否默认模板", 30));
            boolean retb = ToolHelper.ExportDS2XlsFile(dt, "报表模板信息", cmlist, retstr, fname);
            if (retb)
            {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "生成文件成功！";
                cfs.fname=fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(ConfigHelper.getTempPath()+ ConfigHelper.getfSepChar()+fname.toString());
                cfs.data2 = null;

                String jsonstr= JSON.toJSON(cfs).toString();
                return jsonstr;
            }
            else
            {
                ajaxResult=AjaxResult.error("导出文件异常!");
                return JSON.toJSON(ajaxResult).toString();
            }
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }

    @RequestMapping(value="/ImportGRPTInfo",method ={RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ImportGRPTInfo",notes="导入Japer报表定义信息接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public AjaxResult ImportGRPTInfo(MultipartHttpServletRequest request) {
        AjaxResult ajaxResult = null;

        SqlHelper sqlhelper=new SqlHelper();

        String rptId = request.getParameter("rptId");
        if (StringUtil.IsNullOrEmpty(rptId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        CPS_V_GRPT_INFO entity = sqlhelper.GetObject(CPS_V_GRPT_INFO.class,"select * from CPS_V_GRPT_INFO where GRPT_ID="+rptId);
        if (entity == null){
            ajaxResult = AjaxResult.error("传输参数有误!");
            return ajaxResult;
        }

        try {
            String fname = entity.getGRPTT_CODE() + "_" + entity.getGRPTT_ID().toString();
            String reportpath = ConfigHelper.getReportPath();

            List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
            if(files.size()>0) {
                MultipartFile file = files.get(0);
                if (file.isEmpty()) {
                    ajaxResult = AjaxResult.error("上传模板文件失败");
                    return ajaxResult;
                }
            }else{
                ajaxResult = AjaxResult.error("上传模板文件失败");
                return ajaxResult;
            }

            FileUploadHelper fileUploadHelper=new FileUploadHelper();
            fileUploadHelper.setFileName(fname);
            fileUploadHelper.setPath("report");
            fileUploadHelper.setFileType("jrxml");
            fileUploadHelper.setPostedFile(files.get(0));
            if(fileUploadHelper.UploadTemp()!=null)
            {
                sqlhelper.ExecuteNoQuery("update CPS_T_GRPT_INFO set grpt_sfile='" + fname + ".jrxml' where grpt_id=" + rptId.toString());
                ajaxResult = AjaxResult.success("更新报表模板成功!");
                return ajaxResult;
            }else
            {
                String text = fileUploadHelper.getErrorText();
                ajaxResult = AjaxResult.error(text);
                return ajaxResult;
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/RptFileDownload", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="RptFileDownload",notes="下载Japer报表定义文件接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String RptFileDownload(@RequestParam Map<String, String> params, HttpServletResponse response)
    {
        AjaxResult ajaxResult = null;
        SqlHelper sqlhelper=new SqlHelper();

        String rptId = params.get("rptId");
        if (StringUtil.IsNullOrEmpty(rptId)) {
            ajaxResult = AjaxResult.error("传输参数有误!");
            return JSON.toJSON(ajaxResult).toString();
        }

        try {
            String downloadName = "";
            CPS_V_GRPT_INFO entity = sqlhelper.GetObject(CPS_V_GRPT_INFO.class,"select * from CPS_T_GRPT_INFO where GRPT_ID=" + rptId);
            String sfile = sqlhelper.ExecuteScalar("select grpt_sfile from CPS_T_GRPT_INFO where grpt_id=" + rptId.toString());
            String FileName= ConfigHelper.getReportPath()+sfile;

            ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
            cfs.success = true;
            cfs.text = "导出文件成功！";
            cfs.fname=sfile;
            cfs.data1 = ToolHelper.File2Bytes(FileName);
            cfs.data2 = null;

            String jsonstr=JSON.toJSON(cfs).toString();
            return jsonstr;
        }catch (Exception Ex)
        {
            ajaxResult=AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
