package com.soft.framework.controller.CPSoft;

import com.alibaba.fastjson.JSON;
import com.soft.framework.common.utils.file.FileUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.domain.AjaxResult;
import com.soft.framework.helper.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Configuration
@RequestMapping("/Service/CpSoft/CMOSSTest" )
public class CMOSSTestController {
    ToolHelper cptl = new ToolHelper();
    SqlHelper sqlhelper = new SqlHelper();

    @RequestMapping(value="/ExecuteUploadTest",method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ExecuteUploadTest",notes="上传OSS测试接口")
    public AjaxResult ExecuteUploadTest(HttpServletRequest request)
    {
        AjaxResult ajaxResult = null;

        try
        {
            MultipartFile file=null;
            List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
            if(files.size()>0) {
                file = files.get(0);
                if (file.isEmpty()) {
                    ajaxResult = AjaxResult.error("上传模板文件失败");
                    return ajaxResult;
                }
            }else{
                ajaxResult = AjaxResult.error("上传模板文件失败");
                return ajaxResult;
            }

            String testpath = ConfigHelper.getOssLocalPath()+"Test/";
            String filename=file.getOriginalFilename();
            String fname=FileUtil.ExtractFileNameNoExt(filename);
            String fext= FileUtil.ExtractFileExt(filename);
            FileUploadHelper fileUploadHelper=new FileUploadHelper();
            fileUploadHelper.setFileName(fname);
            fileUploadHelper.setPath(testpath);
            fileUploadHelper.setFileType(fext);
            fileUploadHelper.setPostedFile(files.get(0));
            if(fileUploadHelper.Upload()!=null) {
                ajaxResult = AjaxResult.success("文件上传成功");
                return ajaxResult;
            }else{
                ajaxResult = AjaxResult.error("文件OSS测试失败！");
                return ajaxResult;
            }
        }
        catch (Exception Ex)
        {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return ajaxResult;
        }
    }

    @RequestMapping(value="/ExecuteDownloadTest", produces={"text/plain;charset=UTF-8"},method ={RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @ApiOperation(value ="ExecuteDownloadTest",notes="下载测试接口")
    @ApiImplicitParam(name = "request", value = "HttpServletRequest", required = true)
    public String ExecuteDownloadTest(HttpServletRequest request) {
        AjaxResult ajaxResult = null;

        try {
            String kname = request.getParameter("kname");
            if (StringUtil.IsNullOrEmpty(kname)) {
                ajaxResult = AjaxResult.error("传输参数有误!");
                return ajaxResult.toString();
            }

            String fname=FileUtil.ExtractFileName(kname);
            String ftype=FileUtil.ExtractFileExt(kname);
            String CachePath=ConfigHelper.getProfile()+"cache";
            if(FileUtil.FileExists(CachePath+"/"+fname))
            {
                FileUtil.Delete(CachePath+"/"+fname);
            }

            OSSHelper ossHelper = new OSSHelper();
            ossHelper.DownloadFile(kname,CachePath+"/"+fname);
            if(FileUtil.FileExists(CachePath+"/"+fname))
            {
                ToolHelper.Result_FileBytePack cfs = new ToolHelper.Result_FileBytePack();
                cfs.success = true;
                cfs.text = "下载OSS文件成功！";
                cfs.ftype = ftype.toString();
                cfs.fname = fname.toString();
                cfs.data1 = ToolHelper.File2Bytes(CachePath+"/"+fname);
                cfs.data2 = null;

                String jsonstr = JSON.toJSON(cfs).toString();
                return jsonstr;
            }else
            {
                ajaxResult = AjaxResult.error("下载OSS文件失败!");
                return JSON.toJSON(ajaxResult).toString();
            }
        } catch (Exception Ex) {
            ajaxResult = AjaxResult.error("程序产生异常:" + Ex.getMessage() + "!");
            return JSON.toJSON(ajaxResult).toString();
        }
    }
}
