package com.soft.framework.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "jplat")
public class BaseConfig {
    public static Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        BaseConfig.port = port;
    }

    @Value("${server.port}")
    private static Integer port;

    /**
     * 是否记录日志
     */
    private static int LogFlag;
    /**
     * 是否记录SQL日志
     */
    private static int SqlTg;
    /**
     * 系统mark
     */
    private static String SystemMark;

    public static String getWhiteList() {
        return WhiteList;
    }

    public void setWhiteList(String whiteList) {
        BaseConfig.WhiteList = whiteList;
    }

    /**
     * 系统mark
     */
    private static String WhiteList;

    public static String getRunMode() {
        return RunMode;
    }

    public void setRunMode(String runMode) {
        BaseConfig.RunMode = runMode;
    }

    /**
     * RunMode
     */
    private static String RunMode;
    /**
     * 是否记录OCCI日志
     */
    private static int OcciLog;
    /**
     * OCCI记录时限
     */
    private static int OcciSecond;
    /**
     * 导出水印标志
     */
    private static int EPWMask;
    /**
     * 界面水印
     */
    private static int UIWMask;
    /**
     * 是否加载数据库配置
     */
    private static int LoadDBConf;
    /**
     * 项目名称
     */
    private static String name;
    /**
     * ExtJS版本
     */
    private static String ExtVersion;
    /**
     * 版本
     */
    private static String version;
    /**
     * 版权年份
     */
    private static String copyrightYear;
    /**
     * 实例演示开关
     */
    private static boolean demoEnabled;
    /**
     * 上传路径
     */
    private static String profile;
    /**
     * 短信总开关
     */
    private static int SmsFlag;
    /**
     * 通知短信总开关
     */
    private static int NoticeSmsFlag;
    /**
     * 文件分隔符
     */
    private static String separator;
    /**
     * 上传路径
     */
    private static int SessionTimeOut;
    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;
    /**
     * 获取地址开关
     */
    private static String ReportConnStr;
    /**
     * 上传路径
     */
    private static int CompileReport;
    /**
     * OssLocalPath
     */
    private static String OssLocalPath;
    /**
     * OSSRemoteRoot
     */
    private static String OssRemotePath;
    /**
     * 所属模块标志
     */
    private static String ModuleId;
        /**
     * 限定GroupId标志
     */
    private static String GroupId;


    public static int getSmsFlag() {
        return SmsFlag;
    }

    public void setSmsFlag(int smsFlag) {
        BaseConfig.SmsFlag = smsFlag;
    }

    public static int getNoticeSmsFlag() {
        return NoticeSmsFlag;
    }

    public void setNoticeSmsFlag(int noticeSmsFlag) {
        BaseConfig.NoticeSmsFlag = noticeSmsFlag;
    }

    public static String getSeparator() {
        return separator;
    }

    public void setSeparator(String separator) {
        BaseConfig.separator = separator;
    }

    public static String getOssLocalPath() {
        return (OssLocalPath+"/").replace("//","/");
    }

    public void setOssLocalPath(String ossLocalPath) {
        OssLocalPath = ossLocalPath;
    }

    public static String getOssRemotePath() {
        return OssRemotePath;
    }

    public void setOssRemotePath(String ossRemotePath) {
        OssRemotePath = ossRemotePath;
    }

    public static int getCompileReport() {
        return CompileReport;
    }

    public void setCompileReport(int compileReport) {
        CompileReport = compileReport;
    }

    public static String getModuleId() {
        return ModuleId;
    }

    public void setModuleId(String moduleId) {
        BaseConfig.ModuleId = moduleId;
    }

    public static String getGroupId() {
        return GroupId;
    }

    public void setGroupId(String groupId) {
        BaseConfig.GroupId = groupId;
    }

    public static int getLogFlag() {
        return LogFlag;
    }

    public void setLogFlag(int logFlag) {
        BaseConfig.LogFlag = logFlag;
    }

    public static int getSqlTg() {
        return SqlTg;
    }

    public void setSqlTg(int sqlTg) {
        BaseConfig.SqlTg = sqlTg;
    }

    public static String getSystemMark() {
        return SystemMark;
    }

    public void setSystemMark(String systemMark) {
        BaseConfig.SystemMark = systemMark;
    }

    public static int getOcciLog() {
        return OcciLog;
    }

    public void setOcciLog(int occiLog) {
        BaseConfig.OcciLog = occiLog;
    }

    public static int getOcciSecond() {
        return OcciSecond;
    }

    public void setOcciSecond(int occiSecond) {
        BaseConfig.OcciSecond = occiSecond;
    }

    public static int getEPWMask() {
        return EPWMask;
    }

    public void setEPWMask(int EPWMask) {
        BaseConfig.EPWMask = EPWMask;
    }

    public static int getUIWMask() {
        return UIWMask;
    }

    public void setUIWMask(int UIWMask) {
        BaseConfig.UIWMask = UIWMask;
    }

    public static int getLoadDBConf() {
        return LoadDBConf;
    }

    public void setLoadDBConf(int loadDBConf) {
        BaseConfig.LoadDBConf = loadDBConf;
    }

    public static String getReportConnStr() {
        return ReportConnStr;
    }

    public void setReportConnStr(String reportConnStr) {
        BaseConfig.ReportConnStr = reportConnStr;
    }

    public static String getName() {
        return name;
    }

    public void setName(String name) {
        BaseConfig.name = name;
    }

    public static String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        BaseConfig.version = version;
    }

    public static String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        BaseConfig.copyrightYear = copyrightYear;
    }

    public static boolean isDemoEnabled() {
        return demoEnabled;
    }

    public void setDemoEnabled(boolean demoEnabled) {
        BaseConfig.demoEnabled = demoEnabled;
    }

    public static String getProfile() {
        return (profile+"/").replace("//","/");
    }

    public void setProfile(String profile) {
        BaseConfig.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        BaseConfig.addressEnabled = addressEnabled;
    }

    public static String getExtVersion() {
        return ExtVersion;
    }

    public void setExtVersion(String extVersion) {
        BaseConfig.ExtVersion = extVersion;
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return getProfile() + "download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return getProfile() + "upload/";
    }

    /**
     * 获取上传路径
     */
    public static String getLogPath() {
        String baseUrl = getProfile();
        return baseUrl + "logs/";
    }

    public static String getHotupdatePath() {
        String baseUrl = getProfile();
        return baseUrl + "hotupdate/";
    }

    public static String getTemplatePath() {
        String baseUrl = getProfile();
        return baseUrl + "temp/";
    }

    public static String getReportPath() {
        String baseUrl = getProfile();
        return baseUrl + "report/";
    }

    public static String getMobanPath() {
        String baseUrl = getProfile();
        return baseUrl + "moban/";
    }

    public static String getCachePath() {
        String baseUrl = getProfile();
        return baseUrl + "cache/";
    }

    public static int getSessionTimeOut() {
        return SessionTimeOut;
    }

    public void setSessionTimeOut(int sessionTimeOut) {
        BaseConfig.SessionTimeOut = sessionTimeOut;
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    public static String getAbsoluteFile(String filename) {
        String downloadPath = getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
}
