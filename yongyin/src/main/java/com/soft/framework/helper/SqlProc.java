package com.soft.framework.helper;

import com.yyszc.extend.DataColumn;
import com.yyszc.extend.DataRow;
import com.yyszc.extend.DataSet;
import com.yyszc.extend.DataTable;
import com.soft.framework.common.features.IDBFeatures;
import com.soft.framework.common.features.MSSqlFeatures;
import com.soft.framework.common.utils.spring.SpringUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.config.datasource.DynamicDataSource;
import com.sun.rowset.CachedRowSetImpl;

import javax.sql.RowSet;
import java.lang.reflect.Field;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("unchecked")
public class SqlProc {
    private IDBFeatures dbFeatures;
    private Connection conn;
    private DynamicDataSource ds;

    public SqlProc() {
        dbFeatures = new MSSqlFeatures();
    }

    public Connection getConnection() {
        Connection conn = null;
        try {
            if(ds==null){
                ds=(DynamicDataSource)SpringUtil.getBean("dynamicDataSource");
            }
            conn = ds.getConnection();
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
        return conn;
    }

    public Connection getTranConnection() {
        return this.conn;
    }

    public RowSet GetRowSet(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            CachedRowSetImpl rowset = new CachedRowSetImpl();
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            rowset.populate(rs);
            rs.close();
            statement.close();
            //conn.close();
            return rowset;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public Map<String,Object> GetMapForResult(String strsql) {
        Map<String,Object> map = new HashMap<String,Object>();

        try {
            DataTable tmpdt=GetDataTable(strsql);
            if(tmpdt.getTotalCount()>0)
            {
                DataRow dr=tmpdt.getRow(0);
                for(int i=0;i<dr.getColumnList().size();i++)
                {
                    map.put(dr.getColumn(i).getColumnName(),dr.getColValue(i));
                }
            }

            return map;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public List<Map<String,Object>> GetListMapForResult(String strsql) {
        List<Map<String,Object>> _list=new ArrayList<Map<String,Object>>();
        try {
            DataTable tmpdt=GetDataTable(strsql);
            for(int i=0;i<tmpdt.getTotalCount();i++)
            {
                Map<String,Object> locmap = new HashMap<String,Object>();
                DataRow dr=tmpdt.getRow(i);
                for(int j=0;j<dr.getColumnList().size();j++)
                {
                    locmap.put(dr.getColumn(j).getColumnName(),dr.getColValue(j));
                }
                _list.add(locmap);
            }

            return _list;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public <T> List<T> GetObjectList(Class<T> cl, String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        List<T> list = new ArrayList<T>();
        if (conn == null) {
            return null;
        }
        try {
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            ResultSetMetaData rsMeta = rs.getMetaData();
            ArrayList colnamelist = new ArrayList();
            int columnCount = rsMeta.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMeta.getColumnLabel(i);
                colnamelist.add(columnName);
            }
            while (rs.next()) {
                T row = cl.newInstance();

                for (int i = 0; i <colnamelist.size(); i++) {
                    String columnName = colnamelist.get(i).toString();
                    if(columnName.equals("row_num")) {
                        continue;
                    }

                    try{
                        Field f = row.getClass().getDeclaredField(columnName);
                        if(f!=null)
                        {
                            f.setAccessible(true);
                            Object colvalue = rs.getObject(i + 1);

                            if(colvalue==null)
                            {
                                f.set(row, colvalue);
                                continue;
                            }

                            DBHelper.setValue(f, f.getType().getSimpleName(), colvalue.getClass().getSimpleName(), colvalue, row);
                        }
                    }catch(Exception Ex)
                    {

                    }
                }

                list.add(row);
            }
            statement.close();
            //conn.close();
            return list;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public <T> List<T> GetObjectList(Class<T> cl, String sql, String orderStr, int pageSize, int currentPage) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        String strsql = dbFeatures.ToPageSql(sql, orderStr, pageSize, currentPage);
        return GetObjectList(cl, strsql);
    }

    public <T> T GetObject(Class<T> cl, String sql) {
        List<T> alist = GetObjectList(cl, sql);
        if (alist!=null&&!alist.isEmpty()) {
            return alist.get(0);
        }
        return null;
    }

    public RowSet GetRowSet(String sql, String orderStr, int pageSize, int currentPage) {
        sql = dbFeatures.ToPageSql(sql, orderStr, pageSize, currentPage);
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            CachedRowSetImpl rowset = new CachedRowSetImpl();
            Statement statement = conn.createStatement(ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet rs = statement.executeQuery(sql);
            rowset.setPageSize(pageSize);
            rowset.populate(rs);
            rs.close();
            statement.close();
            //conn.close();
            return rowset;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public DataTable GetDataTable(String sql) {
        //if(ConfigHelper.isRecDBLog()) LogHelper.WriteSqlLog(sql);
        if (conn == null) {
            return null;
        }
        try {
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            DataTable dt=ConvertResultSetToDataTable(rs);
            statement.close();
            //conn.close();
            return dt;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public DataSet GetDataSet(ArrayList<String> sqllist) {
        //if(ConfigHelper.isRecDBLog()) LogHelper.WriteSqlLog(sqllist.toString());
        if (conn == null) {
            return null;
        }
        try {
            DataSet ds=new DataSet();
            for (String sql:sqllist) {
                Statement statement = conn.createStatement();
                ResultSet rs = statement.executeQuery(sql);
                DataTable dt=ConvertResultSetToDataTable(rs);
                statement.close();
                ds.addTable(dt);
            }
            //conn.close();

            return ds;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    private DataTable ConvertResultSetToDataTable(ResultSet rs)  {
        try
        {
            ResultSetMetaData rsmd = rs.getMetaData();
            int columnCount = rsmd.getColumnCount();
            DataTable dt = new DataTable();

            for (int i = 1; i <= columnCount; i++) {
                DataColumn dc = new DataColumn(rsmd.getColumnLabel(i),rsmd.getColumnType(i),null);
                dt.getColList().add(dc);
            }

            while (rs.next()) {
                DataRow dr = new DataRow();
                for (int i = 1; i <= columnCount; i++) {
                    DataColumn dc = new DataColumn(rsmd.getColumnLabel(i),rsmd.getColumnType(i),rs.getObject(i));
                    dr.getColumnList().add(dc);
                }
                dt.addRow(dr);
            }

            return dt;
        } catch (Exception Ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(Ex.getMessage()));
            return null;
        }
    }

    public String ExecuteInsertWithObtainId(String sql){
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                retval = rs.getString(1);
            }
            rs.close();
            statement.close();
            //conn.close();
            return retval;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public String ExecuteScalar(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                retval = rs.getString(1);
            }
            rs.close();
            statement.close();
            //conn.close();
            return retval;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public boolean ExecuteScalarPair(String sql, StringBuilder  s1, StringBuilder  s2) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return false;
        }
        try {
            String retval = "";
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                MetaHelper.StringBuilderEmpty(s1);
                MetaHelper.StringBuilderEmpty(s2);
                s1.append(rs.getString(1));
                s2.append(rs.getString(2));
            }

            rs.close();
            statement.close();
            //conn.close();
            return true;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }


    public boolean ExecuteScalarThree(String sql, StringBuilder  s1, StringBuilder  s2, StringBuilder  s3) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return false;
        }
        try {
            String retval = "";
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                MetaHelper.StringBuilderEmpty(s1);
                MetaHelper.StringBuilderEmpty(s2);
                MetaHelper.StringBuilderEmpty(s3);
                s1.append(rs.getString(1));
                s2.append(rs.getString(2));
                s3.append(rs.getString(3));
            }

            rs.close();
            statement.close();
            //conn.close();
            return true;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }

    public boolean RecordExists(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return false;
        }
        try {
            boolean retval = false;
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            if (rs.next()) {
                String rets = rs.getString(1);
                if (Integer.parseInt(rets) >= 1) {
                    retval = true;
                }
            }
            rs.close();
            statement.close();
            //conn.close();
            return retval;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }

    public String ExecuteScalarList(String sql) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                if (retval.equals("")) {
                    retval += rs.getString(1);
                } else {
                    retval += "," + rs.getString(1);
                }
            }
            rs.close();
            statement.close();
            //conn.close();
            return retval;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public String ExecuteScalarList(String sql,String dels) {
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            String retval = "";
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                if (retval.equals("")) {
                    retval += rs.getString(1);
                } else {
                    retval += dels + rs.getString(1);
                }
            }
            rs.close();
            statement.close();
            //conn.close();
            return retval;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public ArrayList<String> ExecuteScalarArrayList(String sql) {
        ArrayList<String> relist = new ArrayList<String>();
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return null;
        }
        try {
            Statement statement = conn.createStatement();
            ResultSet rs = statement.executeQuery(sql);
            while (rs.next()) {
                relist.add(rs.getString(1));
            }
            rs.close();
            statement.close();
            //conn.close();
            return relist;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public int ExecuteNoQuery(String sql) throws Exception{
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sql);
        }
        if (conn == null) {
            return -1;
        }
        try {
            Statement statement = conn.createStatement();
            int retint = statement.executeUpdate(sql);
            statement.close();
            //conn.close();
            return retint;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            throw ex;
        }
    }

    public boolean ExecuteNoQuery(List<String> sqllist) throws Exception{
        if (ConfigHelper.isRecDBLog()) {
            LogHelper.WriteSqlLog(sqllist.toString());
        }

        if (conn == null) {
            return false;
        }
        try {
            Statement statement = null;
            try {
                if (sqllist.size() > 0) {
                    for (int i = 0; i < sqllist.size(); i++) {
                        statement = conn.createStatement();
                        statement.executeUpdate((String) sqllist.get(i));
                        statement.close();
                    }
                }
                return true;
            } catch (Exception ex) {
                LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
                return false;
            }
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            throw ex;
        }
    }

    public boolean BeginTrans() {
        try {
            conn = getConnection();
            if (conn == null) {
                return false;
            }
            conn.setAutoCommit(false);
            return true;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return false;
        }
    }

    public void CommitTrans() {
        try {
            conn.commit();
            conn.close();
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return;
        }
    }

    public void RollbackTrans() {
        try {
            conn.rollback();
            conn.close();
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return;
        }
    }

    public String GetFormatKeyValue(String schema, String strTable, String strColName, String strKeyValue) {
        try {
            String strValue = StringUtil.ToDbString(strKeyValue);
            String strReturn = "";
            String strColType = GetColType(schema, strTable, strColName);

            if (strColType.equals("")) {
                strReturn = (strValue.equals("")) ? "''" : "'" + strValue + "'";
            } else if (strColType.equals("NUMBER")) {
                strReturn = (strValue.equals("")) ? "null" : strValue;
            } else if (strColType.equals("DATE")) {
                strReturn = (strValue.equals("")) ? "null" : dbFeatures.FormatStrToDate(strValue);
            } else {
                strReturn = (strValue.equals("")) ? "''" : "'" + strValue + "'";
            }
            return strReturn;
        } catch (Exception ex) {
            LogHelper.WriteSysLog(MetaHelper.GetRunCPInfo(ex.getMessage()));
            return null;
        }
    }

    public String FormatDateToStr(int strlen, String colname) {
        return dbFeatures.FormatDateToStr(strlen, colname);
    }

    public String FormatStrToDate(String strval) {
        return dbFeatures.FormatStrToDate(strval);
    }

    public String GetColType(String schema, String strtab, String colname) {
        String strsql = dbFeatures.GetColType(schema, strtab, colname);
        String strColType = null;
        try {
            strColType = ExecuteScalar(strsql);
        } catch (Exception e) {
            return "";
        }
        return strColType;
    }

    public String ToPageSql(String strSql, String orderStr, int pageSize, int currentPage) {
        return dbFeatures.ToPageSql(strSql, orderStr, pageSize, currentPage);
    }

    public String ToLimitSql(String strSql, String orderStr, int start, int end) {
        return dbFeatures.ToLimitSql(strSql, orderStr, start, end);
    }

    public String PackFunc(String funcname) {
        return dbFeatures.PackFunc(funcname);
    }

    public String PackProc(String procname, String valstr) {
        return dbFeatures.PackProc(procname, valstr);
    }

    public String PackMetaQry(String sqlstr) {
        return dbFeatures.PackMetaQry(sqlstr);
    }

    public String PackTreeOrder(String orderstr) {
        return dbFeatures.PackTreeOrder(orderstr);
    }

    public String GetDefaultDT() {
        return dbFeatures.GetDefaultDT();
    }

    //监理程序工程调用接口

    public String exec_delete_project_pro(long pi_project_id) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call red_flag_delete_bill_pro(?,?,?) }");
            proc.setLong(1,pi_project_id);
            proc.registerOutParameter(2, Types.VARCHAR);
            proc.registerOutParameter(3, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(2);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public String exec_red_flag_update_bill_pro(long pi_user_id,long pi_project_id,
           String pi_project_type,long pi_bill_id,String pi_bill_type) {
        try {
            //SqlHelper sqlheler=new SqlHelper();
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call red_flag_update_bill_pro(?,?,?,?,?,?) }");
            proc.setLong(1,pi_user_id);
            proc.setLong(2,pi_project_id);
            proc.setString(3,pi_project_type);
            proc.setLong(4,pi_bill_id);
            proc.setString(5,pi_bill_type);
            proc.registerOutParameter(6, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(6);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public boolean exec_score_create_user_reply_question_score_pro(String pi_bill_type,long pi_bill_id,long pi_item_id,long pi_user_id,
                                                                          long pi_create_by, String pi_flag) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call score_create_user_reply_question_score_pro(?,?,?,?,?,?) }");
            proc.setString(1,pi_bill_type);
            proc.setLong(2,pi_bill_id);
            proc.setLong(3,pi_item_id);
            proc.setLong(4,pi_user_id);
            proc.setLong(5,pi_create_by);
            proc.setString(6,pi_flag);
            proc.execute();

            return true;
        }catch (Exception Ex)
        {
            return false;
        }
    }

    public boolean exec_score_create_user_score_pro(String pi_bill_type,long pi_bill_id,float pi_score,long pi_user_id,
                                                                   long pi_create_by) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call score_create_user_score_pro(?,?,?,?,?) }");
            proc.setString(1,pi_bill_type);
            proc.setLong(2,pi_bill_id);
            proc.setFloat(3,pi_score);
            proc.setLong(4,pi_user_id);
            proc.setLong(5,pi_create_by);
            proc.execute();

            return true;
        }catch (Exception Ex)
        {
            return false;
        }
    }

    public boolean exec_score_delete_user_reply_question_score_pro(String pi_bill_type,long pi_bill_id) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call score_delete_user_reply_question_score_pro(?,?) }");
            proc.setString(1,pi_bill_type);
            proc.setLong(2,pi_bill_id);
            proc.execute();

            return true;
        }catch (Exception Ex)
        {
            return false;
        }
    }



    public String exec_red_flag_delete_bill_pro(long pi_user_id,long pi_project_id,
           String pi_project_type,long pi_bill_id,String pi_bill_type) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call red_flag_delete_bill_pro(?,?,?,?,?,?) }");
            proc.setLong(1,pi_user_id);
            proc.setLong(2,pi_project_id);
            proc.setString(3,pi_project_type);
            proc.setLong(4,pi_bill_id);
            proc.setString(5,pi_bill_type);
            proc.registerOutParameter(6, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(6);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public String exec_red_flag_update_pre_start_work_pro(long pi_user_id,long pi_project_id,
                                                String pi_project_type) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call red_flag_delete_bill_pro(?,?,?,?) }");
            proc.setLong(1,pi_user_id);
            proc.setLong(2,pi_project_id);
            proc.setString(3,pi_project_type);
            proc.registerOutParameter(4, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(6);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public String exec_red_flag_update_pre_start_work_bill_pro(long pi_user_id,long pi_project_id,
                                                          String pi_project_type,String pi_bill_type) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call red_flag_update_pre_start_work_bill_pro(?,?,?,?,?) }");
            proc.setLong(1,pi_user_id);
            proc.setLong(2,pi_project_id);
            proc.setString(3,pi_project_type);
            proc.setString(4,pi_bill_type);
            proc.registerOutParameter(5, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(5);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public String exec_update_notice_status_pro(long pi_notice_id,String pi_bill_type) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call update_notice_status_pro(?,?,?,?) }");
            proc.setLong(1,pi_notice_id);
            proc.setString(2,pi_bill_type);
            proc.registerOutParameter(3, Types.VARCHAR);
            proc.registerOutParameter(4, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(3);
            String po_result_msg = proc.getString(4);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public boolean exec_sys_delete_bill_pro(String pi_bill_type,long pi_bill_id,long pi_list_id,long pi_user_id) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call sys_delete_bill_pro(?,?,?,?) }");
            proc.setString(1,pi_bill_type);
            proc.setLong(2,pi_bill_id);
            proc.setLong(3,pi_list_id);
            proc.setLong(4,pi_user_id);
            proc.execute();
            return true;
        }catch (Exception Ex)
        {
            return false;
        }
    }

    public boolean exec_zi_liao_add_supervisor_job_plan_user_pro(Long pi_create_by,long pi_project_id,long pi_supervisor_job_plan_id,String pi_supervisor_job_plan_date,String pi_plan_user_str) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call zi_liao_add_supervisor_job_plan_user_pro(?,?,?,?,?) }");
            proc.setLong(1,pi_create_by);
            proc.setLong(2,pi_project_id);
            proc.setLong(3,pi_supervisor_job_plan_id);
            proc.setString(4,pi_supervisor_job_plan_date);
            proc.setString(5,pi_plan_user_str);
            proc.execute();
            return true;
        }catch (Exception Ex)
        {
            return false;
        }
    }

    public String exec_update_project_type_pro(String pi_overtime_modify_flag,
       Long pi_user_id,Long pi_project_type_id,Long pi_project_id,String pi_finish_date,String pi_production_date,
       Long pi_version_number,StringBuilder sb) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call update_project_type_pro(?,?,?,?,?,?,?,?,?) }");
            proc.setString(1,pi_overtime_modify_flag);
            proc.setLong(2,pi_user_id);
            proc.setLong(3,pi_project_type_id);
            proc.setLong(4,pi_project_id);
            proc.setString(5,pi_finish_date);
            proc.setString(6,pi_production_date);
            proc.setLong(7,pi_version_number);
            proc.registerOutParameter(8, Types.VARCHAR);
            proc.registerOutParameter(9, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(8);
            String po_result_msg = proc.getString(9);
            sb.append(po_result_msg);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public String exec_return_project_type_status_pro(
          Long pi_user_id,Long pi_project_type_id,String pi_button_code,StringBuilder sb) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call return_project_type_status_pro(?,?,?,?,?) }");
            proc.setLong(1,pi_user_id);
            proc.setLong(2,pi_project_type_id);
            proc.setString(3,pi_button_code);
            proc.registerOutParameter(4, Types.VARCHAR);
            proc.registerOutParameter(5, Types.VARCHAR);
            proc.execute();
            String po_result_flag = proc.getString(4);
            String po_result_msg = proc.getString(5);
            sb.append(po_result_msg);
            return po_result_flag;
        }catch (Exception Ex)
        {
            return null;
        }
    }

    public boolean exec_sys_update_job_work_pro(String pi_opertio,long pi_user_id,long pi_project_id,
                                                String pi_bill_type,long pi_bill_id) {
        try {
            Connection conn =  this.conn;
            CallableStatement proc = null;
            proc = conn.prepareCall("{ call sys_update_job_work_pro(?,?,?,?,?) }");
            proc.setString(1,pi_opertio);
            proc.setLong(2,pi_user_id);
            proc.setLong(3,pi_project_id);
            proc.setString(4,pi_bill_type);
            proc.setLong(5,pi_bill_id);
            proc.execute();
            return true;
        }catch (Exception Ex)
        {
            return false;
        }
    }
}
