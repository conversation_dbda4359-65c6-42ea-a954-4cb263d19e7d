package com.soft.framework.job;

import com.soft.framework.common.constant.Constants;
import com.soft.framework.common.constant.ScheduleConstants;
import com.soft.framework.common.utils.ExceptionUtil;
import com.soft.framework.common.utils.bean.BeanUtils;
import com.soft.framework.common.utils.date.DateUtil;
import com.soft.framework.common.utils.string.StringUtil;
import com.soft.framework.helper.DBHelper;
import com.soft.gcc.base.entity.sysjob;
import com.soft.gcc.base.entity.sysjoblog;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Arrays;
import java.util.Date;

/**
 * 抽象quartz调用
 *
 * <AUTHOR>
 */
public abstract class AbstractQuartzJob implements Job
{
    private static final Logger log = LoggerFactory.getLogger(AbstractQuartzJob.class);

    /**
     * 线程本地变量
     */
    private static ThreadLocal<Date> threadLocal = new ThreadLocal<Date>();

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException
    {
        sysjob sysJob = new sysjob();
        BeanUtils.copyBeanProp(sysJob, context.getMergedJobDataMap().get(ScheduleConstants.TASK_PROPERTIES));
        try
        {
            before(context, sysJob);
            if (sysJob != null)
            {
                doExecute(context, sysJob);
            }
            after(context, sysJob, null);
        }
        catch (Exception e)
        {
            log.error("任务执行异常  - ：", e);
            after(context, sysJob, e);
        }
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    protected void before(JobExecutionContext context, sysjob sysJob)
    {
        threadLocal.set(new Date());
    }

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     */
    protected void after(JobExecutionContext context, sysjob sysJob, Exception e)
    {
        Date startTime = threadLocal.get();
        threadLocal.remove();

        final sysjoblog sysJobLog = new sysjoblog();
        sysJobLog.setJobName(sysJob.getJobName());
        sysJobLog.setJobGroup(sysJob.getJobGroup());
        sysJobLog.setInvokeTarget(sysJob.getInvokeTarget());
        sysJobLog.setStartTime(DateUtil.toLocalDateTime(startTime));
        sysJobLog.setStopTime(DateUtil.toLocalDateTime(new Date()));
        Duration duration = Duration.between(sysJobLog.getStopTime(),sysJobLog.getStartTime());
        long runMs = duration.toMillis();
        sysJobLog.setJobMessage(sysJobLog.getJobName() + " 总共耗时：" + runMs + "毫秒");
        if (e != null)
        {
            sysJobLog.setStatus(Constants.FAIL);
            String errorMsg = StringUtil.substring(ExceptionUtil.getExceptionMessage(e), 0, 2000);
            sysJobLog.setExceptionInfo(errorMsg);
        }
        else
        {
            sysJobLog.setStatus(Constants.SUCCESS);
        }

        String strsql = DBHelper.GetInsertSQL(sysJobLog,"sysjoblog", Arrays.asList("LogId"));
        DBHelper.ExecuteSql(strsql);
    }

    /**
     * 执行方法，由子类重载
     *
     * @param context 工作执行上下文对象
     * @param sysJob 系统计划任务
     * @throws Exception 执行过程中的异常
     */
    protected abstract void doExecute(JobExecutionContext context, sysjob sysJob) throws Exception;
}
