<?xml version="1.0" encoding="UTF-8"?>
<configuration status="DEBUG">
    <properties>
        <property name="LOG_HOME">logs</property>
        <property name="FILE_NAME">rolllog</property>
    </properties>
    <appenders>
        <Console name="Console" target="SYSTEM_OUT">
        </Console>
        <File name="Error" fileName="${LOG_HOME}/error.log">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyy.MM.dd 'at' HH:mm:ss z} %-5level %class{36} %L %M - %msg%xEx%n"/>
        </File>
    </appenders>
    <loggers>
        <root level="all">
            <appender-ref ref="Console"/>
            <appender-ref ref="Error" />
        </root>
    </loggers>
</configuration>